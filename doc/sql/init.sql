-- 空气能安装
CREATE TABLE `bz_order_product_install` (
  `id` bigint(19) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `order_product_id` bigint(19) NOT NULL COMMENT '订单货品id',
  `order_sn` varchar(50) NOT NULL DEFAULT '' COMMENT '订单号',
  `store_id` bigint(20) NOT NULL COMMENT '商家ID',
  `product_id` bigint(20) NOT NULL COMMENT '货品ID',
  `supplier_name` varchar(32) DEFAULT NULL COMMENT '供应商名称',
  `supplier_mobile` varchar(32) DEFAULT NULL COMMENT '供应商联系方式',
  `install_images_url` text DEFAULT NULL COMMENT '安装图片地址',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(32) NOT NULL DEFAULT '-' COMMENT '创建人',
  `update_by` varchar(32) NOT NULL DEFAULT '-' COMMENT '更新人',
  `enabled_flag` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否可用:1-是 0-否',
  PRIMARY KEY (`id`),
  KEY `IDX_order_product_id` (`order_product_id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COMMENT='订单商品安装资料表'