spring:
  redis:
    host: r-2zef4d5b5cdab4e4.redis.rds.aliyuncs.com
    port: 6379
    password: Db123456
    database: 99
    timeout: 100000
    jedis:
      pool:
        max-wait: 2000ms
        min-idle: 2
        max-idle: 8
  rabbitmq:
    host: rabbitmq.tsg.cfpamf.com
    port: 5672
    username: admin
    password: donttelldev
    template:
      receive-timeout: 2000
      reply-timeout: 2000
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  cloud:
    alibaba:
      seata:
        enableAutoDataSourceProxy: false
        tx-service-group: seata_slodon_tx_group
        registry:
          file:
            name: registry.conf #因为seata版本驼峰参数映射有问题导致，seata的zk配置参数设置不上导致异常
    nacos:
      discovery:
        server-addr: nacos.tsg.cfpamf.com:8848
        group: MALL_GROUP
        password: nacos
        username: nacos

cfpamf:
  multiple:
    dataSource:
      enabled: true
  smartid:
    server: smartid.tsg.cfpamf.com
    token: 0f673adf80504e2eaa552f5d791b644c

  ##配置数据源
  jdbc:
    dataSource:
      masterdb:
        jdbcUrl: *********************************************************************************************************************************************************************
        #        username: bbc
        #        password: Zhnx#BBC@T
        username: cd_mall
        password: Cd_Mall1
        hikariPool:
          maximumPoolSize: 10
          driverClassName: com.mysql.cj.jdbc.Driver
  ##配置mybatis plus
  mybatis:
    masterdb:
      basePackage: com.cfpamf.ms.mallorder.mapper
      typeAliasesPackage: com.cfpamf.ms.mallorder.po
      mapperLocations: classpath:mapper/**/*.xml
      configuration:
        default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler
      type-enums-package: com.cfpamf.ms.mallorder.constant.enums
      pageProperties:
        overflow: true
        dialectType: mysql
      metaObjectHandler: com.cfpamf.ms.mallorder.common.handler.MyMetaObjectHandler
  ##配置swagger
  swagger:
    dockets:
      demo:
        groupName: 订单中心
        basePackage: com.cfpamf.ms.mallorder.controller
        author: 毛亮
        title: 订单中心

log4jdbc:
  sqltiming:
    warn:
      threshold: 300
    error:
      threshold: 2000
  dump.sql.select: true

xxl:
  job:
    version: 2.0
  newjob:
    admin:
      addresses: http://xxl-job2.tsg.cfpamf.com/xxl-job-admin
    executor:
      appname: ${spring.application.name}
      ip:
      port: 9911
      logpath: /data/applogs/xxl-job/jobhandler/
      logretentiondays: 5
    accessToken:

#SEATA配置
seata:
  service:
    grouplist: {nacos.tsg.cfpamf.com:8091}

aliyun:
  img:
    url: https://mall-sld-test.oss-cn-beijing.aliyuncs.com/

mall-order-biz:
  url: http://mall-order.tsg.cfpamf.com/
mall-payment:
  url: http://mall-payment.tsg.cfpamf.com
  notify: ${mall-order-biz.url}front/orderPayCallback/notify
  refundNotify: ${mall-order-biz.url}front/orderPayCallback/refundNotify
  loanNotify: ${mall-order-biz.url}front/orderPayCallback/loanNotify
  wxpay-key: caa08fe60b014a14b5503fb2ea60aae1
ms-service-customer:
  url: ms-customer.tsg.cfpamf.com
platform-collection:
  account: **********
  name: 河北电子服务商
ms-service-loan:
  url: http://ms-loan.tsg.cfpamf.com
ding-talk:
  url: https://oapi.dingtalk.com/robot/send?access_token=ab8bdb02cfb902cab5c1d757732c00d6f85a09ea4d1a39ff42c2363ac43bc425