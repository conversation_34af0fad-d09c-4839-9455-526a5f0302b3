server:
  port: 12002
spring:
  profiles:
    active: test
  main:
    allow-bean-definition-overriding: true
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
mybatis-plus:
  # 支持统配符 * 或者 ; 分割
  typeEnumsPackage: com.cfpamf.ms.mallorder.common.enums
#  autoconfigure:
#    exclude: com.alibaba.cloud.sentinel.SentinelWebAutoConfiguration #排除sentinel web自动配置，使用自定义配置
management: # spring-boot-admin监控管理配置
  endpoints:
    web:
      exposure:
        include: '*'

  endpoint:
    health:
      show-details: always
  health:
    elasticsearch:
      enabled: false
#    rabbit:
#      enabled: false

feign:
  client:
    config:
      default:
        # FeignClientConfiguration
        connectTimeout: 10000 # Feign的连接建立超时时间
        readTimeout: 10000 # Feign的请求处理超时时间
        loggerLevel: full #

  httpclient:
    enabled: true
    connection-timeout: 10000
  sentinel:
    enabled: true

#熔断
#hystrix:
#  command:
#    default:
#      execution:
#        isolation:
#          thread:
#            timeoutInMilliseconds: 60000
#  threadpool:
#    default:
#      coreSize: 20

logging:
  level:
    io.seata: info
    com.slodon.bbc: info
    ROOT: INFO
    org.springframework: INFO
    com.cfpamf.ms.mallorder.feign: info
    jdbc:
      sqltiming: WARN #包含 SQL 语句实际的执行时间
      audit: 'OFF' # 	除了 ResultSet 之外的所有JDBC调用信息，篇幅较长
      resultset: 'OFF' #包含 ResultSet 的信息，输出篇幅较长
      connection: 'OFF' #连接信息
      sqlonly: info #仅仅记录 SQL 语句，会将占位符替换为实际的参数
      resultsettable: 'OFF'

knife4j:
  production: false