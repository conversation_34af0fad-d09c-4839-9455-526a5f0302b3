#nacos配置中心
spring:
  application:
    name: mall-order
  cloud:
    nacos:
      config:
        server-addr: http://nacos-nsg.cfpamf.com:8848 #配置中心地址
        group: MALL_GROUP #分组名称
        password: nacos
        username: nacos
        extension-configs:
          - data-id: mall-order.yml #配置id，与配置中心的id对应，如果无此配置则使用 application.yml 中的配置
            refresh: true #是否自动刷新配置
#        namespace: 5f8688da-f294-4d53-a8bb-d3106c64889d #配置中心命名空间
      discovery:
        server-addr: http://nacos-nsg.cfpamf.com:8848 #注册中心地址
        group: MALL_GROUP
        password: nacos
        username: nacos
