package com.cfpamf.ms.mallorder.service;

import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.enums.OrderCreateChannel;
import com.cfpamf.ms.mallorder.common.util.ExpressDeliveryUtil;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.ExpressDeliveryDTO;
import com.cfpamf.ms.mallorder.dto.OrderPerformanceDeliveryDTO;
import com.cfpamf.ms.mallorder.mapper.OrderLogisticMapper;
import com.cfpamf.ms.mallorder.po.OrderLogisticPO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.req.OrderDeliveryReq;
import com.cfpamf.ms.mallorder.service.impl.OrderLogisticServiceImpl;
import com.cfpamf.ms.mallsystem.api.ExpressFeignClient;
import com.cfpamf.ms.mallsystem.request.ExpressExample;
import com.cfpamf.ms.mallsystem.vo.Express;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.*;

@RunWith(SpringJUnit4ClassRunner.class)
public class OrderLogisticServiceTest {

	@Mock
	private ExpressFeignClient expressFeignClient;

	@Mock
	private OrderLogisticMapper orderLogisticMapper;

	@Mock
	private ExpressDeliveryUtil expressDeliveryUtil;

	@InjectMocks
	private OrderLogisticServiceImpl orderLogisticService;

	@Before
	public void setUp() {
		MockitoAnnotations.initMocks(this);
	}

	@Test
	public void saveLogistic_type0() {
		OrderDeliveryReq deliveryReq = new OrderDeliveryReq();
		deliveryReq.setOrderSn("82209854929");
		deliveryReq.setOrderProductIds(Lists.newArrayList());
		deliveryReq.setProductIds(Lists.newArrayList());
		deliveryReq.setDeliverType(0);
		deliveryReq.setExpressId(2);
		deliveryReq.setExpressName("expressName");
		deliveryReq.setExpressCompanyCode("companyCode");
		deliveryReq.setExpressNumber("number");
		deliveryReq.setDeliverName("deliverName");
		deliveryReq.setDeliverMobile("mobile");
		deliveryReq.setDistribution(0);
		deliveryReq.setAllowNoLogistics(false);
		deliveryReq.setChannel(OrderCreateChannel.H5);
		deliveryReq.setExpressNumberValid(false);
		deliveryReq.setManagerDeliver(false);
		deliveryReq.setStationMasterDeliver(false);

		String operator = "admin";
		Express express = new Express();
		express.setExpressId(1);
		express.setExpressName("Express");
		express.setExpressCode("123");

		when(expressFeignClient.getExpressByExpressId(anyInt())).thenReturn(express);
		when(orderLogisticMapper.insert(any())).thenReturn(1);

		OrderPO orderPO = new OrderPO();
		OrderLogisticPO savedLogisticPO = orderLogisticService.saveLogistic(orderPO, deliveryReq, operator, 0);

		verify(expressFeignClient).getExpressByExpressId(anyInt());
		verifyNoMoreInteractions(expressFeignClient);

		Assert.assertNotNull(savedLogisticPO);
		assertEquals("Express", savedLogisticPO.getExpressName());
		assertEquals("123", savedLogisticPO.getExpressCompanyCode());
	}

	@Test
	public void saveLogistic_type0_byCode() {
		OrderDeliveryReq deliveryReq = new OrderDeliveryReq();
		deliveryReq.setOrderSn("82209854929");
		deliveryReq.setOrderProductIds(Lists.newArrayList());
		deliveryReq.setProductIds(Lists.newArrayList());
		deliveryReq.setDeliverType(0);
		deliveryReq.setExpressCompanyCode("companyCode");
		deliveryReq.setExpressNumber("number");
		deliveryReq.setDeliverName("deliverName");
		deliveryReq.setDeliverMobile("mobile");
		deliveryReq.setDistribution(0);
		deliveryReq.setAllowNoLogistics(false);
		deliveryReq.setChannel(OrderCreateChannel.H5);
		deliveryReq.setExpressNumberValid(false);
		deliveryReq.setManagerDeliver(false);
		deliveryReq.setStationMasterDeliver(false);

		String operator = "admin";
		Express express = new Express();
		express.setExpressId(1);
		express.setExpressName("Express");
		express.setExpressCode("123");

		when(expressFeignClient.getExpressList(any())).thenReturn(Collections.singletonList(express));
		when(orderLogisticMapper.insert(any())).thenReturn(1);

		OrderPO orderPO = new OrderPO();
		OrderLogisticPO savedLogisticPO = orderLogisticService.saveLogistic(orderPO, deliveryReq, operator,0);

		verify(expressFeignClient).getExpressList(any());
		verifyNoMoreInteractions(expressFeignClient);

		Assert.assertNotNull(savedLogisticPO);
		assertEquals("Express", savedLogisticPO.getExpressName());
		assertEquals("123", savedLogisticPO.getExpressCompanyCode());
	}

	@Test
	public void saveLogistic_type1() {
		OrderDeliveryReq deliveryReq = new OrderDeliveryReq();
		deliveryReq.setOrderSn("82209854929");
		deliveryReq.setOrderProductIds(Lists.newArrayList());
		deliveryReq.setProductIds(Lists.newArrayList());
		deliveryReq.setDeliverType(1);
		deliveryReq.setExpressId(2);
		deliveryReq.setExpressName("expressName");
		deliveryReq.setExpressCompanyCode("companyCode");
		deliveryReq.setExpressNumber("number");
		deliveryReq.setDeliverName("deliverName");
		deliveryReq.setDeliverMobile("mobile");
		deliveryReq.setDistribution(0);
		deliveryReq.setAllowNoLogistics(false);
		deliveryReq.setChannel(OrderCreateChannel.H5);
		deliveryReq.setExpressNumberValid(false);
		deliveryReq.setManagerDeliver(false);
		deliveryReq.setStationMasterDeliver(false);

		String operator = "admin";

		OrderPO orderPO = new OrderPO();
		OrderLogisticPO savedLogisticPO = orderLogisticService.saveLogistic(orderPO, deliveryReq, operator,0);

		Assert.assertNotNull(savedLogisticPO);
		assertEquals("expressName", savedLogisticPO.getExpressName());
		assertEquals("companyCode", savedLogisticPO.getExpressCompanyCode());
	}

	@Test
	public void testSavePerformanceLogistic() {
		// Setup
		OrderPerformanceDeliveryDTO orderPerformanceDeliveryDTO = new OrderPerformanceDeliveryDTO();
		orderPerformanceDeliveryDTO.setExpressCompanyCode("ABC");
		orderPerformanceDeliveryDTO.setExpressCompanyName("Express Co.");

		Express express = new Express();
		express.setExpressId(1);
		express.setExpressName("Express Name");
		express.setExpressCode("ABC");

		List<Express> expressList = new ArrayList<>();
		expressList.add(express);

		ExpressExample expressExample = new ExpressExample();
		expressExample.setExpressCode(orderPerformanceDeliveryDTO.getExpressCompanyCode());

		when(expressFeignClient.getExpressList(expressExample)).thenReturn(expressList);

		// Execute
		OrderLogisticPO result = orderLogisticService.savePerformanceLogistic(orderPerformanceDeliveryDTO);

		// Verify
		verify(expressFeignClient).getExpressList(expressExample);

		// Assert
		assertEquals(Integer.valueOf(OrderConst.DELIVER_TYPE_0), result.getDeliverType());
		assertEquals(express.getExpressId(), result.getExpressId());
		assertEquals(express.getExpressName(), result.getExpressName());
		assertEquals(express.getExpressCode(), result.getExpressCompanyCode());
	}

	@Test
	public void testSavePerformanceLogistic_expressIsNull() {
		// Setup
		OrderPerformanceDeliveryDTO orderPerformanceDeliveryDTO = new OrderPerformanceDeliveryDTO();
		orderPerformanceDeliveryDTO.setExpressCompanyCode("ABC");
		orderPerformanceDeliveryDTO.setExpressCompanyName("Express Co.");

		Express express = new Express();
		express.setExpressId(1);
		express.setExpressName("Express Name");
		express.setExpressCode("ABC");

		ExpressExample expressExample = new ExpressExample();
		expressExample.setExpressCode(orderPerformanceDeliveryDTO.getExpressCompanyCode());

		when(expressFeignClient.getExpressList(expressExample)).thenReturn(null);

		// Execute
		OrderLogisticPO result = orderLogisticService.savePerformanceLogistic(orderPerformanceDeliveryDTO);

		// Verify
		verify(expressFeignClient).getExpressList(expressExample);

		// Assert
		assertEquals(orderPerformanceDeliveryDTO.getExpressCompanyName(), result.getExpressName());
		assertEquals(orderPerformanceDeliveryDTO.getExpressCompanyCode(), result.getExpressCompanyCode());
	}

	@Test
	public void testGetExpressDelivery_ThrowsException_ReturnsExpressDeliveryDTO() throws Exception {
		// Arrange
		String expressNumber = "*********";
		String requestData = "{'LogisticCode':'" + expressNumber + "'}";
		Exception exception = new Exception("mockedException");
		ExpressDeliveryDTO expected = new ExpressDeliveryDTO();
		expected.setExpressNumber(expressNumber);

		// Mock external dependencies
		when(expressDeliveryUtil.queryKdniaoInterface(requestData, CommonConst.KDNIAO_REQUEST_TYPE_2002))
				.thenThrow(exception);

		// Act
		ExpressDeliveryDTO actual = orderLogisticService.getExpressDelivery(expressNumber);

		// Assert
		assertEquals(expected, actual);
	}
}
