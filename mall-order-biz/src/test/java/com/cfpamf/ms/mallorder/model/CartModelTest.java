package com.cfpamf.ms.mallorder.model;

import com.cfpamf.ms.mallgoods.facade.api.GoodsPromotionFeignClient;
import com.cfpamf.ms.mallgoods.facade.request.GoodsPromotionExample;
import com.cfpamf.ms.mallgoods.facade.vo.Goods;
import com.cfpamf.ms.mallgoods.facade.vo.GoodsPromotion;
import com.cfpamf.ms.mallgoods.facade.vo.Product;
import com.cfpamf.ms.mallgoods.facade.vo.ProductPriceVO;
import com.cfpamf.ms.mallorder.common.constant.CartConst;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.util.CartUtils;
import com.cfpamf.ms.mallorder.common.util.OrderLocalUtils;
import com.cfpamf.ms.mallorder.common.util.PromotionUtils;
import com.cfpamf.ms.mallorder.dto.OrderAddressDTO;
import com.cfpamf.ms.mallorder.dto.OrderCartDTO;
import com.cfpamf.ms.mallorder.mapper.CartMapper;
import com.cfpamf.ms.mallorder.po.CartPO;
import com.cfpamf.ms.mallorder.request.CartExample;
import com.cfpamf.ms.mallorder.service.ICartService;
import com.cfpamf.ms.mallorder.service.impl.OrderCreateHelper;
import com.cfpamf.ms.mallpromotion.api.ExclusiveFeignClient;
import com.cfpamf.ms.mallpromotion.api.PromotionCommonFeignClient;
import com.cfpamf.ms.mallpromotion.constant.PromotionConst;
import com.slodon.bbc.core.response.JsonResult;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

public class CartModelTest {
	@Mock
	private CartMapper cartMapper;
	@Mock
	private CartUtils cartUtils;
	@Mock
	private PromotionUtils promotionUtils;
	@Mock
	private OrderCreateHelper orderCreateHelper;
	@Mock
	private OrderLocalUtils orderLocalUtils;
	@Mock
	private ICartService cartService;
	@Mock
	private PromotionCommonFeignClient promotionCommonFeignClient;
	@Mock
	private GoodsPromotionFeignClient goodsPromotionFeignClient;
	@Mock
	private ExclusiveFeignClient exclusiveFeignClient;

	@InjectMocks
	private CartModel cartModel;

	@Before
	public void setUp() {
		MockitoAnnotations.initMocks(this);
	}

	@Test
	public void testRefreshCart_emptyCart() {
		// Arrange
		OrderCartDTO orderCartDTO = new OrderCartDTO();
		Integer memberId = 1;
		Integer productType = 1;
		OrderAddressDTO addressDTO = new OrderAddressDTO();
		boolean isSubmit = true;

		CartExample cartExample = new CartExample();
		cartExample.setMemberId(memberId);
		cartExample.setProductType(productType);
		cartExample.setOrderBy("create_time DESC");
		cartExample.setIsChecked(CartConst.IS_CHECKED_YES);

		List<CartPO> cartPOList = new ArrayList<>();

		when(cartMapper.listByExample(cartExample)).thenReturn(cartPOList);

		// Act
		List<CartPO> result = cartModel.refreshCart(memberId, productType, addressDTO, isSubmit);

		// Assert
		assertNull(result);
	}

	@Test
	public void testRefreshCart_emptyAreaCode() {
		// Arrange
		OrderCartDTO orderCartDTO = new OrderCartDTO();
		Integer memberId = 1;
		Integer productType = 1;
		OrderAddressDTO addressDTO = new OrderAddressDTO();
		boolean isSubmit = true;

		CartExample cartExample = new CartExample();
		cartExample.setMemberId(memberId);
		cartExample.setProductType(productType);
		cartExample.setOrderBy("create_time DESC");
		cartExample.setIsChecked(CartConst.IS_CHECKED_YES);

		List<CartPO> cartPOList = new ArrayList<>();
		CartPO cartPO = new CartPO();
		cartPO.setCartId(1);
		cartPO.setProductId(20001L);
		cartPO.setStoreId(1L);
		cartPOList.add(cartPO);

		when(cartMapper.listByExample(cartExample)).thenReturn(cartPOList);
		when(orderCreateHelper.getStoreAreaCodeByAddress(anyString(), any(OrderAddressDTO.class), anyInt())).thenReturn("");

		Map<Long, ProductPriceVO> productPriceVOMap = new HashMap<>();
		ProductPriceVO productPriceVO = new ProductPriceVO();
		Product product = new Product();
		product.setProductPrice(BigDecimal.valueOf(10));
		productPriceVO.setProduct(product);
		Goods goods = new Goods();
		goods.setGoodsName("goodsName");
		productPriceVO.setGoods(goods);
		productPriceVOMap.put(20001L, productPriceVO);

		when(orderLocalUtils.getProductBatch(cartPOList)).thenReturn(productPriceVOMap);

		CartPO updateCart = new CartPO();
		updateCart.setCartId(1);
		updateCart.setAreaCode("");
		updateCart.setGoodsName("goodsName");
		updateCart.setProductPrice(BigDecimal.valueOf(10));
		updateCart.setProductState(CartConst.STATTE_INVALID);
		updateCart.setIsChecked(CartConst.IS_CHECKED_NO);

		when(cartService.updateById(updateCart)).thenReturn(true);

		CartExample cartExample1 = new CartExample();
		cartExample1.setOrderBy("create_time DESC");
		cartExample.setCartIdList(cartPOList.stream().map(CartPO::getCartId).collect(Collectors.toList()));
		when(cartMapper.listByExample(cartExample1)).thenReturn(cartPOList);

		// Act
		List<CartPO> result = cartModel.refreshCart(memberId, productType, addressDTO, isSubmit);

	}

	@Test
	public void testRefreshCart_cartInvalid() {
		// Arrange
		OrderCartDTO orderCartDTO = new OrderCartDTO();
		Integer memberId = 1;
		Integer productType = 1;
		OrderAddressDTO addressDTO = new OrderAddressDTO();
		boolean isSubmit = true;

		CartExample cartExample = new CartExample();
		cartExample.setMemberId(memberId);
		cartExample.setProductType(productType);
		cartExample.setOrderBy("create_time DESC");
		cartExample.setIsChecked(CartConst.IS_CHECKED_YES);

		List<CartPO> cartPOList = new ArrayList<>();
		CartPO cartPO = new CartPO();
		cartPO.setCartId(1);
		cartPO.setProductId(20001L);
		cartPO.setStoreId(1L);
		cartPOList.add(cartPO);

		when(cartMapper.listByExample(cartExample)).thenReturn(cartPOList);
		when(orderCreateHelper.getStoreAreaCodeByAddress(anyString(), any(OrderAddressDTO.class), anyInt())).thenReturn("areaCode");

		Map<Long, ProductPriceVO> productPriceVOMap = new HashMap<>();
		ProductPriceVO productPriceVO = new ProductPriceVO();
		Product product = new Product();
		product.setProductPrice(BigDecimal.ONE);
		productPriceVO.setProduct(product);
		Goods goods = new Goods();
		goods.setGoodsName("goodsName");
		productPriceVO.setGoods(goods);
		productPriceVOMap.put(20001L, productPriceVO);

		when(orderLocalUtils.getProductBatch(cartPOList)).thenReturn(productPriceVOMap);

		CartPO updateCart = new CartPO();
		updateCart.setCartId(1);
		updateCart.setAreaCode("areaCode");
		updateCart.setGoodsName("goodsName");
		updateCart.setProductPrice(BigDecimal.valueOf(10));
		updateCart.setProductState(CartConst.STATTE_NORMAL);

		when(cartService.updateById(updateCart)).thenReturn(true);

		when(cartUtils.isCartInvalid(cartPO, productPriceVO)).thenReturn(true);

		// Act
		List<CartPO> result = cartModel.refreshCart(memberId, productType, addressDTO, isSubmit);

	}

	@Test
	public void testRefreshCart_stockLack() {
		// Arrange
		OrderCartDTO orderCartDTO = new OrderCartDTO();
		Integer memberId = 1;
		Integer productType = 1;
		OrderAddressDTO addressDTO = new OrderAddressDTO();
		boolean isSubmit = true;

		CartExample cartExample = new CartExample();
		cartExample.setMemberId(memberId);
		cartExample.setProductType(productType);
		cartExample.setOrderBy("create_time DESC");
		cartExample.setIsChecked(CartConst.IS_CHECKED_YES);

		List<CartPO> cartPOList = new ArrayList<>();
		CartPO cartPO = new CartPO();
		cartPO.setCartId(1);
		cartPO.setProductId(20001L);
		cartPO.setStoreId(1L);
		cartPO.setBuyNum(2);
		cartPOList.add(cartPO);

		when(cartMapper.listByExample(cartExample)).thenReturn(cartPOList);
		when(orderCreateHelper.getStoreAreaCodeByAddress(anyString(), any(OrderAddressDTO.class), anyInt())).thenReturn("areaCode");

		Map<Long, ProductPriceVO> productPriceVOMap = new HashMap<>();
		ProductPriceVO productPriceVO = new ProductPriceVO();
		Product product = new Product();
		product.setProductPrice(BigDecimal.ONE);
		productPriceVO.setEffectiveStock(1);
		productPriceVO.setProduct(product);
		Goods goods = new Goods();
		goods.setGoodsName("goodsName");
		productPriceVO.setGoods(goods);
		productPriceVOMap.put(20001L, productPriceVO);

		when(orderLocalUtils.getProductBatch(cartPOList)).thenReturn(productPriceVOMap);

		CartPO updateCart = new CartPO();
		updateCart.setCartId(1);
		updateCart.setAreaCode("areaCode");
		updateCart.setGoodsName("goodsName");
		updateCart.setProductPrice(BigDecimal.valueOf(10));
		updateCart.setProductState(CartConst.STATTE_NORMAL);

		when(cartService.updateById(updateCart)).thenReturn(true);

		when(cartUtils.isCartInvalid(cartPO, productPriceVO)).thenReturn(false);

		// Act
		List<CartPO> result = cartModel.refreshCart(memberId, productType, addressDTO, isSubmit);

	}

	@Test
	public void testRefreshCart_normal() {
		// Arrange
		OrderCartDTO orderCartDTO = new OrderCartDTO();
		Integer memberId = 1;
		Integer productType = 1;
		OrderAddressDTO addressDTO = new OrderAddressDTO();
		boolean isSubmit = true;

		CartExample cartExample = new CartExample();
		cartExample.setMemberId(memberId);
		cartExample.setProductType(productType);
		cartExample.setOrderBy("create_time DESC");
		cartExample.setIsChecked(CartConst.IS_CHECKED_YES);

		List<CartPO> cartPOList = new ArrayList<>();
		CartPO cartPO = new CartPO();
		cartPO.setCartId(1);
		cartPO.setProductId(20001L);
		cartPO.setStoreId(1L);
		cartPO.setBuyNum(2);
		cartPO.setProductState(CartConst.STATTE_LACK);
		cartPO.setIsChecked(1);
		cartPOList.add(cartPO);

		when(cartMapper.listByExample(cartExample)).thenReturn(cartPOList);
		when(orderCreateHelper.getStoreAreaCodeByAddress(anyString(), any(OrderAddressDTO.class), anyInt())).thenReturn("areaCode");

		Map<Long, ProductPriceVO> productPriceVOMap = new HashMap<>();
		ProductPriceVO productPriceVO = new ProductPriceVO();
		Product product = new Product();
		product.setProductPrice(BigDecimal.ONE);
		productPriceVO.setEffectiveStock(3);
		productPriceVO.setProduct(product);
		Goods goods = new Goods();
		goods.setGoodsName("goodsName");
		productPriceVO.setGoods(goods);
		productPriceVOMap.put(20001L, productPriceVO);

		when(orderLocalUtils.getProductBatch(cartPOList)).thenReturn(productPriceVOMap);

		CartPO updateCart = new CartPO();
		updateCart.setCartId(1);
		updateCart.setAreaCode("areaCode");
		updateCart.setGoodsName("goodsName");
		updateCart.setProductPrice(BigDecimal.valueOf(10));
		updateCart.setProductState(CartConst.STATTE_NORMAL);

		when(cartService.updateById(updateCart)).thenReturn(true);

		when(cartUtils.isCartInvalid(cartPO, productPriceVO)).thenReturn(false);

		// Act
		List<CartPO> result = cartModel.refreshCart(memberId, productType, addressDTO, isSubmit);

	}

	@Test
	public void testRefreshAloneBuy_WhenCartInvalid() {

		ProductPriceVO productPriceVO = new ProductPriceVO();
		Product product = new Product();
		product.setProductPrice(BigDecimal.ONE);
		productPriceVO.setEffectiveStock(3);
		productPriceVO.setProduct(product);
		Goods goods = new Goods();
		goods.setGoodsName("goodsName");
		productPriceVO.setGoods(goods);

		// 模拟 cartUtils.isCartInvalid 返回 true
		when(cartUtils.isCartInvalid(any(), eq(productPriceVO))).thenReturn(true);

		when(promotionUtils.getPromotionList(any())).thenReturn(Lists.emptyList());

		CartPO refreshedCartPO = cartModel.refreshAloneBuy(1, 1, "areaCode", productPriceVO);

		// 验证商品状态和是否选中
		assertEquals(CartConst.STATTE_INVALID, (int) refreshedCartPO.getProductState());
		assertEquals(CartConst.IS_CHECKED_NO, (int) refreshedCartPO.getIsChecked());
	}

	@Test
	public void testRefreshAloneBuy_WhenStockNotAvailable() {

		ProductPriceVO productPriceVO = new ProductPriceVO();
		Product product = new Product();
		product.setProductPrice(BigDecimal.ONE);
		productPriceVO.setEffectiveStock(3);
		productPriceVO.setProduct(product);
		Goods goods = new Goods();
		goods.setGoodsName("goodsName");
		productPriceVO.setGoods(goods);

		// 模拟 cartUtils.isCartInvalid 返回 true
		when(cartUtils.isCartInvalid(any(), eq(productPriceVO))).thenReturn(false);

		when(promotionUtils.getPromotionList(any())).thenReturn(Lists.emptyList());

		CartPO refreshedCartPO = cartModel.refreshAloneBuy(1, 4, "areaCode", productPriceVO);

		// 验证商品状态和是否选中
		assertEquals(CartConst.STATTE_LACK, (int) refreshedCartPO.getProductState());
		assertEquals(CartConst.IS_CHECKED_NO, (int) refreshedCartPO.getIsChecked());
	}

	@Test
	public void testRefreshAloneBuy_WhenStockAvailable() {

		ProductPriceVO productPriceVO = new ProductPriceVO();
		Product product = new Product();
		product.setProductPrice(BigDecimal.ONE);
		productPriceVO.setEffectiveStock(3);
		productPriceVO.setProduct(product);
		Goods goods = new Goods();
		goods.setGoodsName("goodsName");
		productPriceVO.setGoods(goods);

		// 模拟 cartUtils.isCartInvalid 返回 true
		when(cartUtils.isCartInvalid(any(), eq(productPriceVO))).thenReturn(false);

		when(promotionUtils.getPromotionList(any())).thenReturn(Lists.emptyList());

		CartPO refreshedCartPO = cartModel.refreshAloneBuy(1, 2, "areaCode", productPriceVO);

		// 验证商品状态和是否选中
		assertEquals(CartConst.STATTE_NORMAL, (int) refreshedCartPO.getProductState());
		assertEquals(CartConst.IS_CHECKED_YES, (int) refreshedCartPO.getIsChecked());
	}

	@Test
	public void testGetBestPromotion_WithPromotions() {
		// Prepare test data
		OrderCartDTO orderCartDTO = new OrderCartDTO();
		Product product = new Product();
		product.setProductId(1L);
		CartPO cartPO = new CartPO();
		List<GoodsPromotion> promotionList = new ArrayList<>();
		GoodsPromotion promotion1 = new GoodsPromotion();
		promotion1.setPromotionType(1);
		promotion1.setPromotionId(101);
		promotion1.setDescription("Promotion 1");
		GoodsPromotion promotion2 = new GoodsPromotion();
		promotion2.setPromotionType(2);
		promotion2.setPromotionId(102);
		promotion2.setDescription("Promotion 2");
		promotionList.add(promotion1);
		promotionList.add(promotion2);

		// Mock necessary behavior
		when(promotionUtils.getPromotionList(product)).thenReturn(promotionList);

		// Test the method
		//cartModel.getBestPromotion(orderCartDTO, product, cartPO);

		// Verify the results
		Assert.assertEquals(1, orderCartDTO.getGoodsPromotionMap().size());
		Assert.assertEquals(promotionList, orderCartDTO.getGoodsPromotionMap().get(product.getProductId()));
		Assert.assertEquals(1, (int) cartPO.getPromotionType());
		Assert.assertEquals(101, (int) cartPO.getPromotionId());
		Assert.assertEquals("Promotion 1", cartPO.getPromotionDescription());
	}

	@Test
	public void testGetBestPromotion_NoPromotion() {
		// Prepare test data
		OrderCartDTO orderCartDTO = new OrderCartDTO();
		Product product = new Product();
		CartPO cartPO = new CartPO();

		// Mock necessary behavior
		when(promotionUtils.getPromotionList(product)).thenReturn(new ArrayList<>());

		// Test the method
		//cartModel.getBestPromotion(orderCartDTO, product, cartPO);

		// Verify the results
		Assert.assertEquals(1, orderCartDTO.getGoodsPromotionMap().size());
		Assert.assertTrue(orderCartDTO.getGoodsPromotionMap().get(product.getProductId()).isEmpty());
		Assert.assertEquals(0, (int) cartPO.getPromotionType());
		Assert.assertEquals(0, (int) cartPO.getPromotionId());
		Assert.assertEquals("", cartPO.getPromotionDescription());
	}

	@Test
	public void testGetBestPromotion() {
		// 创建一个商品对象
		Product product = new Product();
		// 创建一个购物车对象
		CartPO cartPO = new CartPO();

		// 创建一个活动对象并设置优惠信息
		GoodsPromotion goodsPromotion1 = Mockito.mock(GoodsPromotion.class);
		when(goodsPromotion1.getPromotionType()).thenReturn(1);
		when(goodsPromotion1.getPromotionId()).thenReturn(101);
		when(goodsPromotion1.getDescription()).thenReturn("优惠活动1");

		List<GoodsPromotion> promotionList = new ArrayList<>();
		promotionList.add(goodsPromotion1);

		// 设置mock对象的行为
		when(promotionUtils.getPromotionList(product)).thenReturn(promotionList);

		// 调用被测试的方法
		cartModel.getBestPromotion(product, cartPO);

		// 验证方法是否调用
		verify(promotionUtils).getPromotionList(product);

		// 验证设置的优惠信息是否正确
		assert cartPO.getPromotionType() == 1;
		assert cartPO.getPromotionId() == 101;
		assert cartPO.getPromotionDescription().equals("优惠活动1");
	}

	@Test
	public void testGetSinglePromotion_WithValidPromotion_104() {
		// 构建测试数据
		Long productId = 123L;
		Integer memberId = 456;

		GoodsPromotion goodsPromotion = new GoodsPromotion();
		goodsPromotion.setPromotionType(PromotionConst.PROMOTION_TYPE_104);
		goodsPromotion.setProductId(productId);

		// 设置Mock行为
		when(goodsPromotionFeignClient.getGoodsPromotionList(any(GoodsPromotionExample.class)))
				.thenReturn(Collections.singletonList(goodsPromotion));
		when(exclusiveFeignClient.judgeMemberMatchPromotion(eq(memberId), any()))
				.thenReturn(new JsonResult<>(CommonConst.SLD_SUCCESS, ""));
		when(promotionCommonFeignClient.isPromotionAvailable(anyInt(), anyString()))
				.thenReturn(true);

		// 调用被测试方法
		GoodsPromotion result = cartModel.getSinglePromotion(productId, memberId);

		// 验证结果
		Assert.assertEquals(goodsPromotion, result);
		verify(goodsPromotionFeignClient).getGoodsPromotionList(any(GoodsPromotionExample.class));
		verify(promotionCommonFeignClient).isPromotionAvailable(anyInt(), anyString());
	}

	@Test
	public void testGetSinglePromotion_WithValidPromotion_106() {
		// 构建测试数据
		Long productId = 123L;
		Integer memberId = 456;

		GoodsPromotion goodsPromotion = new GoodsPromotion();
		goodsPromotion.setPromotionType(PromotionConst.PROMOTION_TYPE_106);
		goodsPromotion.setProductId(productId);
		goodsPromotion.setPromotionId(111);

		// 设置Mock行为
		when(goodsPromotionFeignClient.getGoodsPromotionList(any(GoodsPromotionExample.class)))
				.thenReturn(Collections.singletonList(goodsPromotion));
		JsonResult<Boolean> jsonResult = new JsonResult<>(CommonConst.SLD_SUCCESS, "");
		jsonResult.setData(true);
		when(exclusiveFeignClient.judgeMemberMatchPromotion(eq(memberId), any()))
				.thenReturn(jsonResult);
		when(promotionCommonFeignClient.isPromotionAvailable(anyInt(), anyString()))
				.thenReturn(true);

		// 调用被测试方法
		GoodsPromotion result = cartModel.getSinglePromotion(productId, memberId);

		// 验证结果
		Assert.assertEquals(goodsPromotion, result);
		verify(goodsPromotionFeignClient).getGoodsPromotionList(any(GoodsPromotionExample.class));
		verify(promotionCommonFeignClient).isPromotionAvailable(anyInt(), anyString());
	}

	@Test
	public void testGetSinglePromotion_WithInvalidPromotion() {
		// 构建测试数据
		Long productId = 123L;
		Integer memberId = 456;

		// 设置Mock行为
		when(goodsPromotionFeignClient.getGoodsPromotionList(any(GoodsPromotionExample.class)))
				.thenReturn(Collections.emptyList());

		// 调用被测试方法
		GoodsPromotion result = cartModel.getSinglePromotion(productId, memberId);

		// 验证结果
		Assert.assertNull(result);
	}


}
