package com.cfpamf.ms.mallorder.model;

import com.cfpamf.ms.mallgoods.facade.api.GoodsCategoryFeignClient;
import com.cfpamf.ms.mallgoods.facade.api.GoodsFeignClient;
import com.cfpamf.ms.mallgoods.facade.request.GoodsCategoryExample;
import com.cfpamf.ms.mallgoods.facade.request.GoodsExample;
import com.cfpamf.ms.mallgoods.facade.vo.Goods;
import com.cfpamf.ms.mallgoods.facade.vo.GoodsCategory;
import com.cfpamf.ms.mallmember.api.MemberFeignClient;
import com.cfpamf.ms.mallmember.dto.MemberDayDTO;
import com.cfpamf.ms.mallorder.dto.OrderSaleInfoDTO;
import com.cfpamf.ms.mallorder.dto.SaleTotalDayDTO;
import com.cfpamf.ms.mallorder.mapper.OrderMapper;
import com.cfpamf.ms.mallorder.mapper.OrderProductMapper;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderProductPO;
import com.cfpamf.ms.mallorder.request.OrderExample;
import com.cfpamf.ms.mallorder.request.OrderProductExample;
import com.cfpamf.ms.mallorder.vo.AdminIndexVO;
import com.cfpamf.ms.mallorder.vo.SalesVolumeVO;
import com.cfpamf.ms.mallshop.api.StoreFeignClient;
import com.cfpamf.ms.mallshop.request.StoreExample;
import com.cfpamf.ms.mallshop.resp.Store;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.task.TaskRejectedException;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderReportModelTest {

    @Mock
    private ThreadPoolTaskExecutor mockThreadPoolTaskExecutor;
    @Mock
    private OrderMapper mockOrderMapper;
    @Mock
    private OrderProductMapper mockOrderProductMapper;
    @Mock
    private GoodsCategoryFeignClient mockGoodsCategoryFeignClient;
    @Mock
    private GoodsFeignClient mockGoodsFeignClient;
    @Mock
    private StoreFeignClient mockStoreFeignClient;
    @Mock
    private MemberFeignClient mockMemberFeignClient;

    @InjectMocks
    private OrderReportModel orderReportModelUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        orderReportModelUnderTest.threadPoolTaskExecutor = mockThreadPoolTaskExecutor;
    }

    @Test
    void testOrderSaleInfo() {
        // Setup
        final OrderExample example = new OrderExample();
        example.setPayTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setPayTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setOrderState(0);
        example.setOrderStateIn("20,30,40");
        example.setOrderStateList(Arrays.asList(0));

        final OrderSaleInfoDTO expectedResult = new OrderSaleInfoDTO();
        expectedResult.setStoreName("storeName");
        expectedResult.setGoodsName("goodsName");
        expectedResult.setTotalNum(0);
        expectedResult.setTotalAmount(new BigDecimal("0.00"));

        // Configure OrderMapper.orderSaleInfo(...).
        final OrderSaleInfoDTO orderSaleInfoDTO = new OrderSaleInfoDTO();
        orderSaleInfoDTO.setStoreName("storeName");
        orderSaleInfoDTO.setGoodsName("goodsName");
        orderSaleInfoDTO.setTotalNum(0);
        orderSaleInfoDTO.setTotalAmount(new BigDecimal("0.00"));
        final OrderExample example1 = new OrderExample();
        example1.setPayTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setPayTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setOrderState(0);
        example1.setOrderStateIn("20,30,40");
        example1.setOrderStateList(Arrays.asList(0));
        when(mockOrderMapper.orderSaleInfo(example1)).thenReturn(orderSaleInfoDTO);

        // Run the test
        final OrderSaleInfoDTO result = orderReportModelUnderTest.orderSaleInfo(example);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetSaleTotalDayDto() {
        // Setup
        final OrderExample example = new OrderExample();
        example.setPayTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setPayTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setOrderState(0);
        example.setOrderStateIn("20,30,40");
        example.setOrderStateList(Arrays.asList(0));

        final SaleTotalDayDTO saleTotalDayDTO = new SaleTotalDayDTO();
        saleTotalDayDTO.setDay("day");
        saleTotalDayDTO.setAmount(new BigDecimal("0.00"));
        final List<SaleTotalDayDTO> expectedResult = Arrays.asList(saleTotalDayDTO);

        // Configure OrderMapper.getSaleTotalDayDto(...).
        final SaleTotalDayDTO saleTotalDayDTO1 = new SaleTotalDayDTO();
        saleTotalDayDTO1.setDay("day");
        saleTotalDayDTO1.setAmount(new BigDecimal("0.00"));
        final List<SaleTotalDayDTO> saleTotalDayDTOS = Arrays.asList(saleTotalDayDTO1);
        final OrderExample example1 = new OrderExample();
        example1.setPayTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setPayTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setOrderState(0);
        example1.setOrderStateIn("20,30,40");
        example1.setOrderStateList(Arrays.asList(0));
        when(mockOrderMapper.getSaleTotalDayDto(example1)).thenReturn(saleTotalDayDTOS);

        // Run the test
        final List<SaleTotalDayDTO> result = orderReportModelUnderTest.getSaleTotalDayDto(example);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetSaleTotalDayDto_OrderMapperReturnsNoItems() {
        // Setup
        final OrderExample example = new OrderExample();
        example.setPayTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setPayTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setOrderState(0);
        example.setOrderStateIn("20,30,40");
        example.setOrderStateList(Arrays.asList(0));

        // Configure OrderMapper.getSaleTotalDayDto(...).
        final OrderExample example1 = new OrderExample();
        example1.setPayTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setPayTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setOrderState(0);
        example1.setOrderStateIn("20,30,40");
        example1.setOrderStateList(Arrays.asList(0));
        when(mockOrderMapper.getSaleTotalDayDto(example1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<SaleTotalDayDTO> result = orderReportModelUnderTest.getSaleTotalDayDto(example);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetDailySale() {
        // Setup
        // Configure OrderMapper.listByExample(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("orderSn");
        orderPO.setStoreId(0L);
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setRefundAmount(new BigDecimal("0.00"));
        final List<OrderPO> orderPOList = Arrays.asList(orderPO);
        final OrderExample example = new OrderExample();
        example.setPayTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setPayTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setOrderState(0);
        example.setOrderStateIn("20,30,40");
        example.setOrderStateList(Arrays.asList(0));
        when(mockOrderMapper.listByExample(example)).thenReturn(orderPOList);

        // Run the test
        final BigDecimal result = orderReportModelUnderTest.getDailySale("payState");

        // Verify the results
        assertThat(result).isEqualTo(new BigDecimal("0.00"));
    }

    @Test
    void testGetDailySale_OrderMapperReturnsNoItems() {
        // Setup
        // Configure OrderMapper.listByExample(...).
        final OrderExample example = new OrderExample();
        example.setPayTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setPayTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setOrderState(0);
        example.setOrderStateIn("20,30,40");
        example.setOrderStateList(Arrays.asList(0));
        when(mockOrderMapper.listByExample(example)).thenReturn(Collections.emptyList());

        // Run the test
        final BigDecimal result = orderReportModelUnderTest.getDailySale("payState");

        // Verify the results
        assertThat(result).isEqualTo(new BigDecimal("0.00"));
    }

    @Test
    void testGetSaleCateStatistics() {
        // Setup
        final SalesVolumeVO salesVolumeVO = new SalesVolumeVO();
        salesVolumeVO.setName("其他");
        salesVolumeVO.setMoneyAmount(new BigDecimal("0.00"));
        salesVolumeVO.setPer("per");
        final List<SalesVolumeVO> expectedResult = Arrays.asList(salesVolumeVO);

        // Configure OrderProductMapper.salesGroupByCategoryId(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setGoodsCategoryId(0);
        orderProductPO.setGoodsId(0L);
        orderProductPO.setProductNum(0);
        orderProductPO.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO.setReturnNumber(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO);
        final OrderExample orderExample = new OrderExample();
        orderExample.setPayTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderExample.setPayTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderExample.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderExample.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderExample.setOrderState(0);
        orderExample.setOrderStateIn("20,30,40");
        orderExample.setOrderStateList(Arrays.asList(0));
        when(mockOrderProductMapper.salesGroupByCategoryId(orderExample)).thenReturn(orderProductPOS);

        // Configure GoodsCategoryFeignClient.getGoodsCategoryList(...).
        final GoodsCategory goodsCategory = new GoodsCategory();
        goodsCategory.setCategoryId(0);
        goodsCategory.setCategoryName("categoryName");
        goodsCategory.setCategoryAlias("categoryAlias");
        goodsCategory.setPid(0);
        goodsCategory.setDescription("description");
        final List<GoodsCategory> goodsCategories = Arrays.asList(goodsCategory);
        final GoodsCategoryExample goodsCategoryExample = new GoodsCategoryExample();
        goodsCategoryExample.setCategoryIdNotEquals(0);
        goodsCategoryExample.setCategoryIdIn("categoryIdIn");
        goodsCategoryExample.setCategoryId(0);
        goodsCategoryExample.setCategoryName("categoryName");
        goodsCategoryExample.setCategoryNameLike("categoryNameLike");
        when(mockGoodsCategoryFeignClient.getGoodsCategoryList(goodsCategoryExample)).thenReturn(goodsCategories);

        // Run the test
        final List<SalesVolumeVO> result = orderReportModelUnderTest.getSaleCateStatistics("model");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetSaleCateStatistics_OrderProductMapperReturnsNoItems() {
        // Setup
        // Configure OrderProductMapper.salesGroupByCategoryId(...).
        final OrderExample orderExample = new OrderExample();
        orderExample.setPayTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderExample.setPayTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderExample.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderExample.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderExample.setOrderState(0);
        orderExample.setOrderStateIn("20,30,40");
        orderExample.setOrderStateList(Arrays.asList(0));
        when(mockOrderProductMapper.salesGroupByCategoryId(orderExample)).thenReturn(Collections.emptyList());

        // Run the test
        final List<SalesVolumeVO> result = orderReportModelUnderTest.getSaleCateStatistics("model");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetSaleCateStatistics_GoodsCategoryFeignClientReturnsNoItems() {
        // Setup
        // Configure OrderProductMapper.salesGroupByCategoryId(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setGoodsCategoryId(0);
        orderProductPO.setGoodsId(0L);
        orderProductPO.setProductNum(0);
        orderProductPO.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO.setReturnNumber(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO);
        final OrderExample orderExample = new OrderExample();
        orderExample.setPayTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderExample.setPayTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderExample.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderExample.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderExample.setOrderState(0);
        orderExample.setOrderStateIn("20,30,40");
        orderExample.setOrderStateList(Arrays.asList(0));
        when(mockOrderProductMapper.salesGroupByCategoryId(orderExample)).thenReturn(orderProductPOS);

        // Configure GoodsCategoryFeignClient.getGoodsCategoryList(...).
        final GoodsCategoryExample goodsCategoryExample = new GoodsCategoryExample();
        goodsCategoryExample.setCategoryIdNotEquals(0);
        goodsCategoryExample.setCategoryIdIn("categoryIdIn");
        goodsCategoryExample.setCategoryId(0);
        goodsCategoryExample.setCategoryName("categoryName");
        goodsCategoryExample.setCategoryNameLike("categoryNameLike");
        when(mockGoodsCategoryFeignClient.getGoodsCategoryList(goodsCategoryExample))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<SalesVolumeVO> result = orderReportModelUnderTest.getSaleCateStatistics("model");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGoodsSaleRankV2() {
        // Setup
        final OrderExample example = new OrderExample();
        example.setPayTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setPayTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setOrderState(0);
        example.setOrderStateIn("20,30,40");
        example.setOrderStateList(Arrays.asList(0));

        final AdminIndexVO.GoodsSaleRankVO goodsSaleRankVO = new AdminIndexVO.GoodsSaleRankVO();
        goodsSaleRankVO.setGoodsName("goodsName");
        goodsSaleRankVO.setNumber(0);
        final List<AdminIndexVO.GoodsSaleRankVO> expectedResult = Arrays.asList(goodsSaleRankVO);

        // Configure OrderMapper.goodsSaleRank(...).
        final OrderSaleInfoDTO orderSaleInfoDTO = new OrderSaleInfoDTO();
        orderSaleInfoDTO.setStoreName("storeName");
        orderSaleInfoDTO.setGoodsName("goodsName");
        orderSaleInfoDTO.setTotalNum(0);
        orderSaleInfoDTO.setTotalAmount(new BigDecimal("0.00"));
        final List<OrderSaleInfoDTO> orderSaleInfoDTOS = Arrays.asList(orderSaleInfoDTO);
        final OrderExample example1 = new OrderExample();
        example1.setPayTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setPayTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setOrderState(0);
        example1.setOrderStateIn("20,30,40");
        example1.setOrderStateList(Arrays.asList(0));
        when(mockOrderMapper.goodsSaleRank(example1)).thenReturn(orderSaleInfoDTOS);

        // Run the test
        final List<AdminIndexVO.GoodsSaleRankVO> result = orderReportModelUnderTest.goodsSaleRankV2(example);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGoodsSaleRankV2_OrderMapperReturnsNoItems() {
        // Setup
        final OrderExample example = new OrderExample();
        example.setPayTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setPayTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setOrderState(0);
        example.setOrderStateIn("20,30,40");
        example.setOrderStateList(Arrays.asList(0));

        // Configure OrderMapper.goodsSaleRank(...).
        final OrderExample example1 = new OrderExample();
        example1.setPayTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setPayTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setOrderState(0);
        example1.setOrderStateIn("20,30,40");
        example1.setOrderStateList(Arrays.asList(0));
        when(mockOrderMapper.goodsSaleRank(example1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<AdminIndexVO.GoodsSaleRankVO> result = orderReportModelUnderTest.goodsSaleRankV2(example);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGoodsSaleRank() {
        // Setup
        final OrderExample example = new OrderExample();
        example.setPayTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setPayTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setOrderState(0);
        example.setOrderStateIn("20,30,40");
        example.setOrderStateList(Arrays.asList(0));

        final AdminIndexVO.GoodsSaleRankVO goodsSaleRankVO = new AdminIndexVO.GoodsSaleRankVO();
        goodsSaleRankVO.setGoodsName("goodsName");
        goodsSaleRankVO.setNumber(0);
        final List<AdminIndexVO.GoodsSaleRankVO> expectedResult = Arrays.asList(goodsSaleRankVO);

        // Configure OrderMapper.listByExample(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("orderSn");
        orderPO.setStoreId(0L);
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setRefundAmount(new BigDecimal("0.00"));
        final List<OrderPO> orderPOList = Arrays.asList(orderPO);
        final OrderExample example1 = new OrderExample();
        example1.setPayTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setPayTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setOrderState(0);
        example1.setOrderStateIn("20,30,40");
        example1.setOrderStateList(Arrays.asList(0));
        when(mockOrderMapper.listByExample(example1)).thenReturn(orderPOList);

        // Configure OrderProductMapper.listByExample(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setGoodsCategoryId(0);
        orderProductPO.setGoodsId(0L);
        orderProductPO.setProductNum(0);
        orderProductPO.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO.setReturnNumber(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO);
        final OrderProductExample example2 = new OrderProductExample();
        example2.setCost(new BigDecimal("0.00"));
        example2.setTaxRate(new BigDecimal("0.00"));
        example2.setOrderProductIdNotEquals(0L);
        example2.setOrderProductIdIn("orderProductIdIn");
        example2.setOrderSn("orderSn");
        when(mockOrderProductMapper.listByExample(example2)).thenReturn(orderProductPOS);

        // Configure GoodsFeignClient.getGoodsList(...).
        final Goods goods1 = new Goods();
        goods1.setId(0L);
        goods1.setGoodsId(0L);
        goods1.setGoodsName("goodsName");
        goods1.setGoodsBrief("goodsBrief");
        goods1.setKeyword("keyword");
        final List<Goods> goods = Arrays.asList(goods1);
        final GoodsExample goodsExample = new GoodsExample();
        goodsExample.setGoodsIdNotEquals(0L);
        goodsExample.setGoodsIdIn("goodsIdIn");
        goodsExample.setGoodsIdNotIn("goodsIdNotIn");
        goodsExample.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodsExample.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockGoodsFeignClient.getGoodsList(goodsExample)).thenReturn(goods);

        // Run the test
        final List<AdminIndexVO.GoodsSaleRankVO> result = orderReportModelUnderTest.goodsSaleRank(example);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGoodsSaleRank_OrderMapperReturnsNull() {
        // Setup
        final OrderExample example = new OrderExample();
        example.setPayTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setPayTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setOrderState(0);
        example.setOrderStateIn("20,30,40");
        example.setOrderStateList(Arrays.asList(0));

        final AdminIndexVO.GoodsSaleRankVO goodsSaleRankVO = new AdminIndexVO.GoodsSaleRankVO();
        goodsSaleRankVO.setGoodsName("goodsName");
        goodsSaleRankVO.setNumber(0);
        final List<AdminIndexVO.GoodsSaleRankVO> expectedResult = Arrays.asList(goodsSaleRankVO);

        // Configure OrderMapper.listByExample(...).
        final OrderExample example1 = new OrderExample();
        example1.setPayTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setPayTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setOrderState(0);
        example1.setOrderStateIn("20,30,40");
        example1.setOrderStateList(Arrays.asList(0));
        when(mockOrderMapper.listByExample(example1)).thenReturn(null);

        // Run the test
        final List<AdminIndexVO.GoodsSaleRankVO> result = orderReportModelUnderTest.goodsSaleRank(example);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGoodsSaleRank_OrderMapperReturnsNoItems() {
        // Setup
        final OrderExample example = new OrderExample();
        example.setPayTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setPayTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setOrderState(0);
        example.setOrderStateIn("20,30,40");
        example.setOrderStateList(Arrays.asList(0));

        final AdminIndexVO.GoodsSaleRankVO goodsSaleRankVO = new AdminIndexVO.GoodsSaleRankVO();
        goodsSaleRankVO.setGoodsName("goodsName");
        goodsSaleRankVO.setNumber(0);
        final List<AdminIndexVO.GoodsSaleRankVO> expectedResult = Arrays.asList(goodsSaleRankVO);

        // Configure OrderMapper.listByExample(...).
        final OrderExample example1 = new OrderExample();
        example1.setPayTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setPayTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setOrderState(0);
        example1.setOrderStateIn("20,30,40");
        example1.setOrderStateList(Arrays.asList(0));
        when(mockOrderMapper.listByExample(example1)).thenReturn(Collections.emptyList());

        // Configure GoodsFeignClient.getGoodsList(...).
        final Goods goods1 = new Goods();
        goods1.setId(0L);
        goods1.setGoodsId(0L);
        goods1.setGoodsName("goodsName");
        goods1.setGoodsBrief("goodsBrief");
        goods1.setKeyword("keyword");
        final List<Goods> goods = Arrays.asList(goods1);
        final GoodsExample goodsExample = new GoodsExample();
        goodsExample.setGoodsIdNotEquals(0L);
        goodsExample.setGoodsIdIn("goodsIdIn");
        goodsExample.setGoodsIdNotIn("goodsIdNotIn");
        goodsExample.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodsExample.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockGoodsFeignClient.getGoodsList(goodsExample)).thenReturn(goods);

        // Run the test
        final List<AdminIndexVO.GoodsSaleRankVO> result = orderReportModelUnderTest.goodsSaleRank(example);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGoodsSaleRank_OrderProductMapperReturnsNoItems() {
        // Setup
        final OrderExample example = new OrderExample();
        example.setPayTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setPayTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setOrderState(0);
        example.setOrderStateIn("20,30,40");
        example.setOrderStateList(Arrays.asList(0));

        final AdminIndexVO.GoodsSaleRankVO goodsSaleRankVO = new AdminIndexVO.GoodsSaleRankVO();
        goodsSaleRankVO.setGoodsName("goodsName");
        goodsSaleRankVO.setNumber(0);
        final List<AdminIndexVO.GoodsSaleRankVO> expectedResult = Arrays.asList(goodsSaleRankVO);

        // Configure OrderMapper.listByExample(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("orderSn");
        orderPO.setStoreId(0L);
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setRefundAmount(new BigDecimal("0.00"));
        final List<OrderPO> orderPOList = Arrays.asList(orderPO);
        final OrderExample example1 = new OrderExample();
        example1.setPayTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setPayTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setOrderState(0);
        example1.setOrderStateIn("20,30,40");
        example1.setOrderStateList(Arrays.asList(0));
        when(mockOrderMapper.listByExample(example1)).thenReturn(orderPOList);

        // Configure OrderProductMapper.listByExample(...).
        final OrderProductExample example2 = new OrderProductExample();
        example2.setCost(new BigDecimal("0.00"));
        example2.setTaxRate(new BigDecimal("0.00"));
        example2.setOrderProductIdNotEquals(0L);
        example2.setOrderProductIdIn("orderProductIdIn");
        example2.setOrderSn("orderSn");
        when(mockOrderProductMapper.listByExample(example2)).thenReturn(Collections.emptyList());

        // Configure GoodsFeignClient.getGoodsList(...).
        final Goods goods1 = new Goods();
        goods1.setId(0L);
        goods1.setGoodsId(0L);
        goods1.setGoodsName("goodsName");
        goods1.setGoodsBrief("goodsBrief");
        goods1.setKeyword("keyword");
        final List<Goods> goods = Arrays.asList(goods1);
        final GoodsExample goodsExample = new GoodsExample();
        goodsExample.setGoodsIdNotEquals(0L);
        goodsExample.setGoodsIdIn("goodsIdIn");
        goodsExample.setGoodsIdNotIn("goodsIdNotIn");
        goodsExample.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodsExample.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockGoodsFeignClient.getGoodsList(goodsExample)).thenReturn(goods);

        // Run the test
        final List<AdminIndexVO.GoodsSaleRankVO> result = orderReportModelUnderTest.goodsSaleRank(example);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGoodsSaleRank_GoodsFeignClientReturnsNoItems() {
        // Setup
        final OrderExample example = new OrderExample();
        example.setPayTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setPayTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setOrderState(0);
        example.setOrderStateIn("20,30,40");
        example.setOrderStateList(Arrays.asList(0));

        // Configure OrderMapper.listByExample(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("orderSn");
        orderPO.setStoreId(0L);
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setRefundAmount(new BigDecimal("0.00"));
        final List<OrderPO> orderPOList = Arrays.asList(orderPO);
        final OrderExample example1 = new OrderExample();
        example1.setPayTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setPayTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setOrderState(0);
        example1.setOrderStateIn("20,30,40");
        example1.setOrderStateList(Arrays.asList(0));
        when(mockOrderMapper.listByExample(example1)).thenReturn(orderPOList);

        // Configure OrderProductMapper.listByExample(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setGoodsCategoryId(0);
        orderProductPO.setGoodsId(0L);
        orderProductPO.setProductNum(0);
        orderProductPO.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO.setReturnNumber(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO);
        final OrderProductExample example2 = new OrderProductExample();
        example2.setCost(new BigDecimal("0.00"));
        example2.setTaxRate(new BigDecimal("0.00"));
        example2.setOrderProductIdNotEquals(0L);
        example2.setOrderProductIdIn("orderProductIdIn");
        example2.setOrderSn("orderSn");
        when(mockOrderProductMapper.listByExample(example2)).thenReturn(orderProductPOS);

        // Configure GoodsFeignClient.getGoodsList(...).
        final GoodsExample goodsExample = new GoodsExample();
        goodsExample.setGoodsIdNotEquals(0L);
        goodsExample.setGoodsIdIn("goodsIdIn");
        goodsExample.setGoodsIdNotIn("goodsIdNotIn");
        goodsExample.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        goodsExample.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockGoodsFeignClient.getGoodsList(goodsExample)).thenReturn(Collections.emptyList());

        // Run the test
        final List<AdminIndexVO.GoodsSaleRankVO> result = orderReportModelUnderTest.goodsSaleRank(example);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testStoreSaleRankV2() {
        // Setup
        final OrderExample example = new OrderExample();
        example.setPayTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setPayTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setOrderState(0);
        example.setOrderStateIn("20,30,40");
        example.setOrderStateList(Arrays.asList(0));

        final AdminIndexVO.StoreSaleRankVO storeSaleRankVO = new AdminIndexVO.StoreSaleRankVO();
        storeSaleRankVO.setStoreName("storeName");
        storeSaleRankVO.setAmount(new BigDecimal("0.00"));
        final List<AdminIndexVO.StoreSaleRankVO> expectedResult = Arrays.asList(storeSaleRankVO);

        // Configure OrderMapper.storeSaleRank(...).
        final OrderSaleInfoDTO orderSaleInfoDTO = new OrderSaleInfoDTO();
        orderSaleInfoDTO.setStoreName("storeName");
        orderSaleInfoDTO.setGoodsName("goodsName");
        orderSaleInfoDTO.setTotalNum(0);
        orderSaleInfoDTO.setTotalAmount(new BigDecimal("0.00"));
        final List<OrderSaleInfoDTO> orderSaleInfoDTOS = Arrays.asList(orderSaleInfoDTO);
        final OrderExample example1 = new OrderExample();
        example1.setPayTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setPayTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setOrderState(0);
        example1.setOrderStateIn("20,30,40");
        example1.setOrderStateList(Arrays.asList(0));
        when(mockOrderMapper.storeSaleRank(example1)).thenReturn(orderSaleInfoDTOS);

        // Run the test
        final List<AdminIndexVO.StoreSaleRankVO> result = orderReportModelUnderTest.storeSaleRankV2(example);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testStoreSaleRankV2_OrderMapperReturnsNoItems() {
        // Setup
        final OrderExample example = new OrderExample();
        example.setPayTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setPayTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setOrderState(0);
        example.setOrderStateIn("20,30,40");
        example.setOrderStateList(Arrays.asList(0));

        // Configure OrderMapper.storeSaleRank(...).
        final OrderExample example1 = new OrderExample();
        example1.setPayTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setPayTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setOrderState(0);
        example1.setOrderStateIn("20,30,40");
        example1.setOrderStateList(Arrays.asList(0));
        when(mockOrderMapper.storeSaleRank(example1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<AdminIndexVO.StoreSaleRankVO> result = orderReportModelUnderTest.storeSaleRankV2(example);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testStoreSaleRank() {
        // Setup
        final OrderExample example = new OrderExample();
        example.setPayTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setPayTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setOrderState(0);
        example.setOrderStateIn("20,30,40");
        example.setOrderStateList(Arrays.asList(0));

        final AdminIndexVO.StoreSaleRankVO storeSaleRankVO = new AdminIndexVO.StoreSaleRankVO();
        storeSaleRankVO.setStoreName("storeName");
        storeSaleRankVO.setAmount(new BigDecimal("0.00"));
        final List<AdminIndexVO.StoreSaleRankVO> expectedResult = Arrays.asList(storeSaleRankVO);

        // Configure OrderMapper.listByExample(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("orderSn");
        orderPO.setStoreId(0L);
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setRefundAmount(new BigDecimal("0.00"));
        final List<OrderPO> orderPOList = Arrays.asList(orderPO);
        final OrderExample example1 = new OrderExample();
        example1.setPayTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setPayTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setOrderState(0);
        example1.setOrderStateIn("20,30,40");
        example1.setOrderStateList(Arrays.asList(0));
        when(mockOrderMapper.listByExample(example1)).thenReturn(orderPOList);

        // Configure StoreFeignClient.getStoreList(...).
        final Store store = new Store();
        store.setStoreId(0L);
        store.setStoreName("storeName");
        store.setStoreLogo("storeLogo");
        store.setStoreGradeId(0);
        store.setStoreGradeName("storeGradeName");
        final List<Store> stores = Arrays.asList(store);
        final StoreExample storeExample = new StoreExample();
        storeExample.setStoreIdNotEquals(0L);
        storeExample.setStoreIdIn("storeIdIn");
        storeExample.setStoreIdNotIn("storeIdNotIn");
        storeExample.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        storeExample.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockStoreFeignClient.getStoreList(storeExample)).thenReturn(stores);

        // Run the test
        final List<AdminIndexVO.StoreSaleRankVO> result = orderReportModelUnderTest.storeSaleRank(example);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testStoreSaleRank_OrderMapperReturnsNull() {
        // Setup
        final OrderExample example = new OrderExample();
        example.setPayTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setPayTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setOrderState(0);
        example.setOrderStateIn("20,30,40");
        example.setOrderStateList(Arrays.asList(0));

        final AdminIndexVO.StoreSaleRankVO storeSaleRankVO = new AdminIndexVO.StoreSaleRankVO();
        storeSaleRankVO.setStoreName("storeName");
        storeSaleRankVO.setAmount(new BigDecimal("0.00"));
        final List<AdminIndexVO.StoreSaleRankVO> expectedResult = Arrays.asList(storeSaleRankVO);

        // Configure OrderMapper.listByExample(...).
        final OrderExample example1 = new OrderExample();
        example1.setPayTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setPayTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setOrderState(0);
        example1.setOrderStateIn("20,30,40");
        example1.setOrderStateList(Arrays.asList(0));
        when(mockOrderMapper.listByExample(example1)).thenReturn(null);

        // Run the test
        final List<AdminIndexVO.StoreSaleRankVO> result = orderReportModelUnderTest.storeSaleRank(example);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testStoreSaleRank_OrderMapperReturnsNoItems() {
        // Setup
        final OrderExample example = new OrderExample();
        example.setPayTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setPayTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setOrderState(0);
        example.setOrderStateIn("20,30,40");
        example.setOrderStateList(Arrays.asList(0));

        final AdminIndexVO.StoreSaleRankVO storeSaleRankVO = new AdminIndexVO.StoreSaleRankVO();
        storeSaleRankVO.setStoreName("storeName");
        storeSaleRankVO.setAmount(new BigDecimal("0.00"));
        final List<AdminIndexVO.StoreSaleRankVO> expectedResult = Arrays.asList(storeSaleRankVO);

        // Configure OrderMapper.listByExample(...).
        final OrderExample example1 = new OrderExample();
        example1.setPayTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setPayTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setOrderState(0);
        example1.setOrderStateIn("20,30,40");
        example1.setOrderStateList(Arrays.asList(0));
        when(mockOrderMapper.listByExample(example1)).thenReturn(Collections.emptyList());

        // Configure StoreFeignClient.getStoreList(...).
        final Store store = new Store();
        store.setStoreId(0L);
        store.setStoreName("storeName");
        store.setStoreLogo("storeLogo");
        store.setStoreGradeId(0);
        store.setStoreGradeName("storeGradeName");
        final List<Store> stores = Arrays.asList(store);
        final StoreExample storeExample = new StoreExample();
        storeExample.setStoreIdNotEquals(0L);
        storeExample.setStoreIdIn("storeIdIn");
        storeExample.setStoreIdNotIn("storeIdNotIn");
        storeExample.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        storeExample.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockStoreFeignClient.getStoreList(storeExample)).thenReturn(stores);

        // Run the test
        final List<AdminIndexVO.StoreSaleRankVO> result = orderReportModelUnderTest.storeSaleRank(example);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testStoreSaleRank_StoreFeignClientReturnsNoItems() {
        // Setup
        final OrderExample example = new OrderExample();
        example.setPayTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setPayTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setOrderState(0);
        example.setOrderStateIn("20,30,40");
        example.setOrderStateList(Arrays.asList(0));

        // Configure OrderMapper.listByExample(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("orderSn");
        orderPO.setStoreId(0L);
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setRefundAmount(new BigDecimal("0.00"));
        final List<OrderPO> orderPOList = Arrays.asList(orderPO);
        final OrderExample example1 = new OrderExample();
        example1.setPayTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setPayTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setOrderState(0);
        example1.setOrderStateIn("20,30,40");
        example1.setOrderStateList(Arrays.asList(0));
        when(mockOrderMapper.listByExample(example1)).thenReturn(orderPOList);

        // Configure StoreFeignClient.getStoreList(...).
        final StoreExample storeExample = new StoreExample();
        storeExample.setStoreIdNotEquals(0L);
        storeExample.setStoreIdIn("storeIdIn");
        storeExample.setStoreIdNotIn("storeIdNotIn");
        storeExample.setCreateTimeAfter(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        storeExample.setCreateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockStoreFeignClient.getStoreList(storeExample)).thenReturn(Collections.emptyList());

        // Run the test
        final List<AdminIndexVO.StoreSaleRankVO> result = orderReportModelUnderTest.storeSaleRank(example);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testWrapBoardRemoteData() throws Exception {
        // Setup
        final AdminIndexVO vo = new AdminIndexVO();
        vo.setNewMemberNum(0);
        vo.setMemberTotal(0);
        vo.setNewStoreNum(0);
        vo.setStoreTotal(0);
        vo.setNewGoodsNum(0);
        vo.setGoodsTotal(0);
        final AdminIndexVO.MemberReportVO memberReportVO = new AdminIndexVO.MemberReportVO();
        memberReportVO.setDay("day");
        memberReportVO.setNumber(0);
        vo.setMemberWeeklyReport(Arrays.asList(memberReportVO));

        doAnswer(invocation -> {
            final Callable<?> callable = (Callable<?>) invocation.getArguments()[0];
            return CompletableFuture.completedFuture(callable.call());
        }).when(mockThreadPoolTaskExecutor).submit((Callable<?>) any(Callable.class));

        // Run the test
        orderReportModelUnderTest.wrapBoardRemoteData(vo, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
    }

    @Test
    void testWrapBoardRemoteData_ThreadPoolTaskExecutorThrowsTaskRejectedException() {
        // Setup
        final AdminIndexVO vo = new AdminIndexVO();
        vo.setNewMemberNum(0);
        vo.setMemberTotal(0);
        vo.setNewStoreNum(0);
        vo.setStoreTotal(0);
        vo.setNewGoodsNum(0);
        vo.setGoodsTotal(0);
        final AdminIndexVO.MemberReportVO memberReportVO = new AdminIndexVO.MemberReportVO();
        memberReportVO.setDay("day");
        memberReportVO.setNumber(0);
        vo.setMemberWeeklyReport(Arrays.asList(memberReportVO));

        when(mockThreadPoolTaskExecutor.submit((Callable<?>) any(Callable.class)))
                .thenThrow(TaskRejectedException.class);

        // Run the test
        assertThatThrownBy(() -> orderReportModelUnderTest.wrapBoardRemoteData(vo,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).isInstanceOf(TaskRejectedException.class);
    }

    @Test
    void testGetMemberWeeklyReport() {
        // Setup
        final MemberDayDTO memberDayDTO = new MemberDayDTO();
        memberDayDTO.setDay("day");
        memberDayDTO.setNumber(0);
        final List<MemberDayDTO> result1 = Arrays.asList(memberDayDTO);
        final AdminIndexVO.MemberReportVO memberReportVO = new AdminIndexVO.MemberReportVO();
        memberReportVO.setDay("day");
        memberReportVO.setNumber(0);
        final List<AdminIndexVO.MemberReportVO> expectedResult = Arrays.asList(memberReportVO);

        // Run the test
        final List<AdminIndexVO.MemberReportVO> result = orderReportModelUnderTest.getMemberWeeklyReport(result1);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
