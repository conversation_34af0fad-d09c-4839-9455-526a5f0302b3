package com.cfpamf.ms.mallorder.model;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.cfpamf.ms.mallmember.api.MemberFeignClient;
import com.cfpamf.ms.mallorder.common.calculate.RefundCalculator;
import com.cfpamf.ms.mallorder.common.enums.OrderProductDeliveryEnum;
import com.cfpamf.ms.mallorder.common.enums.OrderStatusEnum;
import com.cfpamf.ms.mallorder.common.enums.RefundType;
import com.cfpamf.ms.mallorder.constant.ExchangeOrderConst;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.OrderAfterDTO;
import com.cfpamf.ms.mallorder.enums.PayWayEnum;
import com.cfpamf.ms.mallorder.integration.pay.PayIntegration;
import com.cfpamf.ms.mallorder.mapper.*;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.request.OrderExample;
import com.cfpamf.ms.mallorder.request.OrderExtendExample;
import com.cfpamf.ms.mallorder.service.IOrderAfterService;
import com.cfpamf.ms.mallorder.service.IOrderReturnService;
import com.cfpamf.ms.mallorder.service.impl.OrderProductServiceImpl;
import com.cfpamf.ms.mallorder.v2.service.OrderRefundRecordService;
import com.cfpamf.ms.mallorder.validation.OrderReturnValidation;
import com.cfpamf.ms.mallorder.vo.AfsApplyInfoVO;
import com.cfpamf.ms.mallorder.vo.AfsProductVO;
import com.cfpamf.ms.mallorder.vo.OrderProductListVO;
import com.slodon.bbc.core.constant.OrdersAfsConst;
import com.slodon.bbc.core.exception.MallException;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

//使用Mockito框架编写JUnit测试用例
@RunWith(MockitoJUnitRunner.class)
public class OrderRefundTest {

	//模拟依赖注入
	@InjectMocks
	OrderAfterServiceModel afterServiceModel;
	@Mock
	OrderAfterServiceModel afterServiceModelMock;
	//模拟被依赖
	@Mock(lenient = true)
	IOrderAfterService orderAfterService;
	@Mock
	OrderProductMapper orderProductMapper;
	@Mock
	OrderExtendMapper orderExtendMapper;
	@Mock
	IOrderReturnService orderReturnService;
	@Mock(lenient = true)
	OrderMapper orderMapper;
	@Mock(lenient = true)
	OrderProductServiceImpl orderProductService;
	@Mock
	MemberFeignClient memberFeignClient;
	@Mock
	OrderAfterSaleLogMapper orderAfterSaleLogMapper;
	@Mock
	private OrderAfterMapper orderAfterMapper;
	@Mock
	private OrderReturnValidation orderReturnValidation;
	@Mock
	private RefundCalculator refundCalculator;
	@Mock(lenient = true)
	private OrderRefundRecordService orderRefundRecordService;
	@Mock
	private PayIntegration payIntegration;
	@Mock
	private StringRedisTemplate stringRedisTemplate;
	@Mock
	private OrderReturnMapper orderReturnMapper;

	@Before
	public void setUp() {
		MockitoAnnotations.initMocks(this);
		TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), OrderReturnPO.class);
		TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), OrderRefundRecordPO.class);

	}

	//测试样例1：正常情况下测试退款金额计算方法的正确性
	@Test
	public void dealAfsProductTest() {
		//构造测试数据
		OrderPO orderPO = getOrderPO();
		OrderAfterDTO orderAfterDTO = getOrderAfterDTO();

//		List<OrderAfterDTO.AfterProduct> productList = new ArrayList<>();
//		productList.add(new OrderAfterDTO.AfterProduct());
//		orderAfterDTO.setProductList(productList);
//		orderAfterDTO.setOrderSn("123");

		List<OrderExtendPO> orderExtendPOList = new ArrayList<>();
		orderExtendPOList.add(new OrderExtendPO());
		when(orderExtendMapper.listByExample(any(OrderExtendExample.class))).thenReturn(orderExtendPOList);

		OrderProductPO orderProductPO = getOrderProductPOS().get(0);
		//orderProductPO.setMoneyAmount(BigDecimal.valueOf(100));

		when(orderProductMapper.getByPrimaryKey(anyLong())).thenReturn(orderProductPO);

		when(orderMapper.listByExample(any())).thenReturn(Collections.singletonList(orderPO));

		Map<String, BigDecimal> map = new HashMap<>();
		map.put("returnMoneyAmount", BigDecimal.ZERO);
		map.put("serviceFee", BigDecimal.ZERO);
		map.put("thirdpartnarFee", BigDecimal.ZERO);
		map.put("xzCardAmount", BigDecimal.ZERO);
		map.put("orderCommission", BigDecimal.ZERO);
		map.put("businessCommission", BigDecimal.ZERO);
		map.put("platformVoucherAmount", BigDecimal.ZERO);
		map.put("platformActivityAmount", BigDecimal.ZERO);
		map.put("storeActivityAmount", BigDecimal.ZERO);
		map.put("storeVoucherAmount", BigDecimal.ZERO);
		map.put("returnIntegralAmount", BigDecimal.ZERO);

		when(orderReturnService.getSumAmountByProductId(anyLong())).thenReturn(map);

		//执行被测试方法
		List<AfsProductVO> result = afterServiceModel.dealAfsProduct(orderPO, orderAfterDTO);

		BigDecimal actualReturnMoney = result.get(0).getReturnMoneyAmount();
		BigDecimal expectedReturnMoney = BigDecimal.valueOf(110.08);
		assertEquals(expectedReturnMoney, actualReturnMoney);
	}

	//测试样例2：测试在最后一次退款时，钆差计算方法的正确性
	@Test
	public void dealAfsProductLastProductTest() {
		//构造测试数据
		OrderPO orderPO = getOrderPO();
		OrderAfterDTO orderAfterDTO = getOrderAfterDTO();

//		List<OrderAfterDTO.AfterProduct> productList = new ArrayList<>();
//		productList.add(new OrderAfterDTO.AfterProduct());//在本样例中，退货数量参数将覆盖原有的2，使得第一个测试样例计算退款金额的方法返回120而非100。
//		orderAfterDTO.setProductList(productList);
//		orderAfterDTO.setOrderSn("123");

		List<OrderExtendPO> orderExtendPOList = new ArrayList<>();
		orderExtendPOList.add(new OrderExtendPO());
		when(orderExtendMapper.listByExample(any(OrderExtendExample.class))).thenReturn(orderExtendPOList);

		OrderProductPO orderProductPO = getOrderProductPOS().get(0);
		//orderProductPO.setMoneyAmount(BigDecimal.valueOf(40));

		when(orderProductMapper.getByPrimaryKey(anyLong())).thenReturn(orderProductPO);

		when(orderMapper.listByExample(any())).thenReturn(Collections.singletonList(orderPO));

		Map<String, BigDecimal> map = new HashMap<>();
		map.put("returnMoneyAmount", BigDecimal.valueOf(80));
		map.put("serviceFee", BigDecimal.ZERO);
		map.put("thirdpartnarFee", BigDecimal.ZERO);
		map.put("xzCardAmount", BigDecimal.ZERO);
		map.put("orderCommission", BigDecimal.ZERO);
		map.put("businessCommission", BigDecimal.ZERO);
		map.put("platformVoucherAmount", BigDecimal.ZERO);
		map.put("platformActivityAmount", BigDecimal.ZERO);
		map.put("storeActivityAmount", BigDecimal.ZERO);
		map.put("storeVoucherAmount", BigDecimal.ZERO);
		map.put("returnIntegralAmount", BigDecimal.ZERO);

		when(orderReturnService.getSumAmountByProductId(anyLong())).thenReturn(map);

		//执行被测试方法
		List<AfsProductVO> result = afterServiceModel.dealAfsProduct(orderPO, orderAfterDTO);

		BigDecimal actualReturnMoney = result.get(0).getReturnMoneyAmount();
		BigDecimal expectedReturnMoney = BigDecimal.valueOf(30.08);
		assertEquals(expectedReturnMoney, actualReturnMoney);
	}


	private List<OrderProductPO> getOrderProductPOS() {
		String orderProductJson = "[{\"activityDiscountAmount\":100,\"barCode\":\"\",\"batchNo\":\"CGTB2023031500000003568\"," +
				"\"businessCommission\":0,\"channelSkuId\":\"************\"," +
				"\"channelSkuUnit\":\"1\",\"channelSource\":3,\"commissionAmount\":0,\"commissionRate\":0," +
				"\"goodsAmountTotal\":220,\"goodsCategoryId\":1000001717,\"goodsCategoryPath\":" +
				"\"农机农具->农膜/大棚/农具->ceshi111\",\"goodsId\":100010590006," +
				"\"goodsIsDistribute\":0,\"goodsName\":\"活动商品\",\"goodsParameter\":\"\"," +
				"\"integral\":0,\"integralCashAmount\":0,\"isComment\":0,\"isDistribution\":0," +
				"\"isGift\":0,\"isVirtualGoods\":1,\"landingPrice\":10,\"logisticId\":0," +
				"\"memberId\":308640,\"moneyAmount\":110.08,\"orderCommission\":0,\"orderProductId\":46937," +
				"\"orderSn\":\"****************\",\"performanceChannel\":1,\"performanceMode\":0," +
				"\"performanceService\":\"0\",\"platformActivityAmount\":0,\"platformVoucherAmount\":100," +
				"\"productCode\":\"\",\"productEffectivePrice\":10,\"productId\":************," +
				"\"productNum\":22,\"productShowPrice\":10,\"productSpecJson\":\"颜色: 粉红色\"," +
				"\"productVersion\":\"1000004689158\",\"replacementNumber\":0,\"returnNumber\":10," +
				"\"ruleTag\":\"\",\"serviceFee\":0,\"skuMaterialCode\":\"WMS202292717572\"," +
				"\"skuMaterialName\":\"2022927自动化货品17572\",\"specValues\":\"粉红色\",\"spellTeamId\":0," +
				"\"spuOutId\":\"\",\"status\":2,\"storeActivityAmount\":0," +
				"\"storeCompanyName\":\"中和农信（北京）供应链管理有限公司\",\"storeId\":6710002,\"storeName\":\"农服模式店铺\"," +
				"\"storeVoucherAmount\":0,\"supplierCode\":\"150039\"," +
				"\"supplierName\":\"8888\",\"taxPrice\":10,\"taxRate\":1,\"thirdpartnarFee\":2.21,\"updateBy\":\"-\"," +
				"\"updateTime\":*************,\"usrNo\":\"\",\"weight\":5,\"xzCardAmount\":9.92}]";
		List<OrderProductPO> orderProductPOS = JSONArray.parseArray(orderProductJson, OrderProductPO.class);
		orderProductPOS.get(0).setDeliveryState(OrderProductDeliveryEnum.WAIT_DELIVERY);
		return orderProductPOS;
	}

	private OrderPO getOrderPO() {
		String orderJson = "{\"activityDiscountAmount\":100,\"areaCode\":\"6710002-**********\",\"balanceAmount\":0," +
				"\"bankPayTrxNo\":\"2023042422001475860503361597\",\"businessCommission\":0,\"channel\":\"H5\"," +
				"\"channelServiceFee\":0.67,\"composePayName\":\"支付宝+乡助卡支付\"," +
				"\"createBy\":\"308640-***********\",\"createTime\":*************," +
				"\"customerConfirmStatus\":0,\"customerConfirmStatusDesc\":\"无需确认\"," +
				"\"exchangeFlag\":0,\"expressFee\":0.92,\"expressFeeTotal\":1," +
				"\"financeRuleCode\":\"\",\"goodsAmount\":220,\"integral\":0," +
				"\"integralCashAmount\":0,\"isDelivery\":1,\"isSettlement\":0," +
				"\"loanPayState\":0,\"lockState\":0,\"marginOrderAmount\":0,\"memberId\":308640," +
				"\"memberName\":\"***********\",\"newOrder\":true,\"orderAmount\":111," +
				"\"orderAmountTotal\":221,\"orderCommission\":0,\"orderId\":44025," +
				"\"orderPattern\":1,\"orderPlaceUserRoleCode\":1,\"orderPlaceUserRoleDesc\":" +
				"\"本人\",\"orderSn\":\"****************\",\"orderState\":20," +
				"\"orderType\":1,\"parentSn\":\"****************\",\"payAmount\":111," +
				"\"paySn\":\"1123042432540849\",\"payTime\":1682326156000," +
				"\"payUpdateTime\":1682326156000,\"paymentCode\":\"ALIPAY\"," +
				"\"paymentName\":\"支付宝\",\"performanceModes\":\"[0]\",\"planDiscountAmount\":0," +
				"\"platformActivityAmount\":0,\"platformVoucherAmount\":100," +
				"\"recommendStoreId\":1140004,\"recommendStoreName\":\"中和农服\",\"refundAmount\":50.03," +
				"\"serviceFee\":0,\"serviceFeeRate\":0.01,\"settleMode\":\"standard\"," +
				"\"settlementPrice\":218.71,\"storeActivityAmount\":0,\"storeCompanyName\":\"中和农信（北京）供应链管理有限公司\"," +
				"\"storeId\":6710002,\"storeIsSelf\":1,\"storeName\":\"农服模式店铺\"," +
				"\"storeVoucherAmount\":0,\"thirdpartnarFee\":2.21,\"thirdpartnarFeeRate\":0.01," +
				"\"userMobile\":\"***********\",\"userNo\":\"U2023032100006165864\"," +
				"\"xzCardAmount\":10,\"xzCardExpressFeeAmount\":0.08}";
		OrderPO orderPO = JSONObject.parseObject(orderJson, OrderPO.class);
		return orderPO;
	}

	private OrderAfterDTO getOrderAfterDTO() {

		OrderPO orderPO = getOrderPO();
		List<OrderProductPO> orderProductPOS = getOrderProductPOS();

		// 构造退款单信息
		OrderAfterDTO orderAfterDTO = new OrderAfterDTO();
		orderAfterDTO.setOrderSn(orderPO.getOrderSn());
		orderAfterDTO.setAfsType(OrdersAfsConst.AFS_TYPE_REFUND);
		orderAfterDTO.setGoodsState(OrdersAfsConst.GOODS_STATE_NO);
		orderAfterDTO.setApplyReasonContent("cancelReason");
		orderAfterDTO.setAfsDescription("cancelRemark");
		orderAfterDTO.setFinalReturnAmount(orderPO.getPayAmount());
		orderAfterDTO.setReturnBy(1);
		orderAfterDTO.setContactName("optUserName");

		List<OrderAfterDTO.AfterProduct> afterProductList = orderProductPOS.stream().map((orderProduct -> {
			OrderAfterDTO.AfterProduct afterProduct = new OrderAfterDTO.AfterProduct();
			afterProduct.setOrderProductId(orderProduct.getOrderProductId());
			afterProduct.setAfsNum(orderProduct.getProductNum() - orderProduct.getReturnNumber());
			return afterProduct;
		})).collect(Collectors.toCollection(LinkedList::new));

		orderAfterDTO.setProductList(afterProductList);

		return orderAfterDTO;
	}

	@Test(expected = MallException.class)
	public void updateRefundFailReasonByAfsSn_withEmptyAfsSn_shouldThrowException() {
		// 准备测试数据
		String afsSn = "";
		String failReason = "测试原因";

		// 执行测试方法
		afterServiceModel.updateRefundFailReasonByAfsSn(afsSn, failReason);
	}

	@Test
	public void updateRefundFailReasonByAfsSn_withValidData_shouldReturnCount() {
		// 准备测试数据
		String afsSn = "test_afs_sn";
		String failReason = "测试原因";
		OrderAfterPO orderAfterPO = new OrderAfterPO();
		orderAfterPO.setAfsSn(afsSn);
		orderAfterPO.setRefundFailReason(failReason);
		when(orderAfterMapper.updateByAfsSn(orderAfterPO)).thenReturn(1);

		// 执行测试方法
		int count = afterServiceModel.updateRefundFailReasonByAfsSn(afsSn, failReason);

		// 验证测试结果
		assertEquals(1, count);
		verify(orderAfterMapper).updateByAfsSn(orderAfterPO);
	}

	@Test(expected = MallException.class)
	public void updateRefundFailReasonByAfsSn_withInvalidData_shouldThrowException() {
		// 准备测试数据
		String afsSn = "test_afs_sn";
		String failReason = "测试原因";
		OrderAfterPO orderAfterPO = new OrderAfterPO();
		orderAfterPO.setAfsSn(afsSn);
		orderAfterPO.setRefundFailReason(failReason);
		when(orderAfterMapper.updateByAfsSn(orderAfterPO)).thenReturn(0);

		// 执行测试方法
		afterServiceModel.updateRefundFailReasonByAfsSn(afsSn, failReason);
	}

	// 测试getAfterSaleApplyInfo方法
	@Test
	public void testGetAfterSaleApplyInfo() {
		// 模拟数据
		Integer memberId = 123;
		String orderSn = "ABC123";
		Long orderProductId = 456L;

		OrderPO orderPO = new OrderPO();
		orderPO.setOrderId(1);
		orderPO.setOrderSn(orderSn);
		orderPO.setMemberId(memberId);
		orderPO.setStoreId(1L);
		orderPO.setOrderState(OrderStatusEnum.WAIT_DELIVER.getValue());
		orderPO.setOrderAmountTotal(new BigDecimal("111"));
		orderPO.setOrderAmount(new BigDecimal(106));
		orderPO.setPayAmount(new BigDecimal("0"));
		orderPO.setMarginOrderAmount(new BigDecimal("0"));
		orderPO.setGoodsAmount(new BigDecimal("111"));
		orderPO.setExpressFeeTotal(new BigDecimal("0"));
		orderPO.setExpressFee(new BigDecimal("0"));
		orderPO.setActivityDiscountAmount(new BigDecimal("5"));
		orderPO.setStoreActivityAmount(new BigDecimal("0"));
		orderPO.setPlatformActivityAmount(new BigDecimal("0"));
		orderPO.setStoreVoucherAmount(new BigDecimal("0"));
		orderPO.setPlatformVoucherAmount(new BigDecimal("5"));
		orderPO.setXzCardAmount(new BigDecimal("0"));
		orderPO.setXzCardExpressFeeAmount(new BigDecimal("0"));
		orderPO.setBalanceAmount(new BigDecimal("0"));
		orderPO.setIntegral(0);
		orderPO.setIntegralCashAmount(new BigDecimal("0"));
		orderPO.setServiceFee(new BigDecimal("0"));
		orderPO.setServiceFeeRate(new BigDecimal("0.03"));
		orderPO.setThirdpartnarFee(new BigDecimal("2.22"));
		orderPO.setThirdpartnarFeeRate(new BigDecimal("0.03"));
		orderPO.setOrderCommission(new BigDecimal("0"));
		orderPO.setBusinessCommission(new BigDecimal("0"));


		OrderProductPO orderProductPO = new OrderProductPO();
		orderProductPO.setOrderProductId(orderProductId);
		orderProductPO.setProductId(orderProductId);
		orderProductPO.setOrderSn(orderPO.getOrderSn());
		orderProductPO.setMemberId(memberId);
		orderProductPO.setGoodsName("test product");
		orderProductPO.setProductShowPrice(new BigDecimal("20"));
		orderProductPO.setProductNum(5);
		orderProductPO.setProductShowPrice(new BigDecimal("111.00"));
		orderProductPO.setLandingPrice(new BigDecimal("1.25"));
		orderProductPO.setGoodsAmountTotal(new BigDecimal("111"));
		orderProductPO.setMoneyAmount(new BigDecimal(106));
		orderProductPO.setActivityDiscountAmount(new BigDecimal("5"));
		orderProductPO.setStoreActivityAmount(new BigDecimal("0"));
		orderProductPO.setPlatformActivityAmount(new BigDecimal("0"));
		orderProductPO.setStoreVoucherAmount(new BigDecimal("0"));
		orderProductPO.setPlatformVoucherAmount(new BigDecimal("5"));
		orderProductPO.setXzCardAmount(new BigDecimal("0"));
		orderProductPO.setIntegral(0);
		orderProductPO.setIntegralCashAmount(new BigDecimal("0"));
		orderProductPO.setCommissionRate(new BigDecimal("0"));
		orderProductPO.setCommissionAmount(new BigDecimal("0"));
		orderProductPO.setServiceFee(new BigDecimal("0"));
		orderProductPO.setThirdpartnarFee(new BigDecimal("2.22"));
		orderProductPO.setOrderCommission(new BigDecimal("0"));
		orderProductPO.setBusinessCommission(new BigDecimal("0"));
		orderProductPO.setReturnNumber(0);
		orderProductPO.setReplacementNumber(0);

		OrderExtendPO orderExtendPODb = new OrderExtendPO();
		orderExtendPODb.setExtendId(1);
		orderExtendPODb.setOrderSn(orderSn);
		orderExtendPODb.setVoucherPrice(new BigDecimal("50"));
		orderExtendPODb.setOrderPointsCount(0);

		// 模拟方法调用
		OrderExample orderExample = new OrderExample();
		orderExample.setMemberId(memberId);
		orderExample.setOrderSn(orderSn);
		when(orderMapper.listByExample(orderExample)).thenReturn(Arrays.asList(orderPO));
		when(orderProductMapper.getByPrimaryKey(orderProductId)).thenReturn(orderProductPO);
		OrderExtendExample orderExtendExample = new OrderExtendExample();
		orderExtendExample.setOrderSn(orderSn);
		when(orderExtendMapper.listByExample(orderExtendExample)).thenReturn(Arrays.asList(orderExtendPODb));
		when(orderReturnValidation.isInnerRefundStore(orderPO.getStoreId())).thenReturn(false);
		when(orderProductMapper.listByExample(any())).thenReturn(Collections.singletonList(orderProductPO));

		// 调用被测试方法
		AfsApplyInfoVO afsApplyInfoVO = afterServiceModel.getAfterSaleApplyInfo(memberId, orderSn, orderProductId, false);

		// 验证结果
		assertNotNull(afsApplyInfoVO);
		OrderProductListVO orderProductListVO = afsApplyInfoVO.getOrderProduct();
		assertNotNull(orderProductListVO);
		assertEquals(orderProductPO.getOrderProductId(), orderProductListVO.getOrderProductId());
		assertEquals(orderProductPO.getGoodsName(), orderProductListVO.getGoodsName());
		assertEquals(orderProductPO.getProductShowPrice(), orderProductListVO.getProductShowPrice());
	}

	@Test
	public void getRefundType_should_return_zero_yuan_refund_when_order_is_exchange() {
		OrderPO orderPODb = new OrderPO();
		orderPODb.setExchangeFlag(ExchangeOrderConst.ORDER_EXCHANGE_FLAG_2);
		RefundType result = afterServiceModel.getRefundType(orderPODb);
		Assert.assertEquals(RefundType.ZERO_YUAN_REFUND, result);
	}

	@Test
	public void getRefundType_should_return_alipay_refund_when_payment_code_is_alipay() {
		String orderSn = "20210101002";
		OrderExample example = new OrderExample();
		example.setOrderSn(orderSn);
		List<OrderPO> orderPOList = new ArrayList<>();
		OrderPO orderPODb = new OrderPO();
		orderPODb.setPaymentCode(PayWayEnum.ALI_PAY.getValue());
		orderPODb.setExchangeFlag(ExchangeOrderConst.ORDER_EXCHANGE_FLAG_1);
		orderPOList.add(orderPODb);
		when(orderMapper.listByExample(any(OrderExample.class))).thenReturn(orderPOList);

		RefundType result = afterServiceModel.getRefundType(orderSn);

		Assert.assertEquals(RefundType.APLIPAY_REFUND, result);
	}

	@Test
	public void getRefundType_should_return_wxpay_refund_when_payment_code_is_wxpay() {
		String orderSn = "20210101003";
		OrderExample example = new OrderExample();
		example.setOrderSn(orderSn);
		List<OrderPO> orderPOList = new ArrayList<>();
		OrderPO orderPODb = new OrderPO();
		orderPODb.setExchangeFlag(ExchangeOrderConst.ORDER_EXCHANGE_FLAG_1);
		orderPODb.setPaymentCode(PayWayEnum.WX_PAY.getValue());
		orderPOList.add(orderPODb);
		when(orderMapper.listByExample(any(OrderExample.class))).thenReturn(orderPOList);

		RefundType result = afterServiceModel.getRefundType(orderSn);

		Assert.assertEquals(RefundType.WXPAY_REFUND, result);
	}

	@Test
	public void testRecalculateRefundAmountWithCombinationRefund() {
		OrderPO order = new OrderPO();
		OrderReturnPO returnPO = new OrderReturnPO();
		returnPO.setAfsSn("testAfsSn");
		returnPO.setActualReturnMoneyAmount(BigDecimal.valueOf(100));
		returnPO.setRefundType(RefundType.COMBINATION_REFUND.getValue());

		// 设置模拟行为
		when(refundCalculator.refundPunishCalculate(any(OrderReturnPO.class), any(OrderPO.class)))
				.thenReturn(BigDecimal.valueOf(10));
		OrderRefundRecordPO t = new OrderRefundRecordPO();
		t.setActualAmount(BigDecimal.ZERO);
		when(orderRefundRecordService.getOne(any())).thenReturn(t);
		Mockito.when(orderRefundRecordService.update(any())).thenReturn(true);

		// 调用被测试方法
		afterServiceModel.recalculateRefundAmount(order, returnPO);

	}

	@Test
	public void testRecalculateRefundAmountWithNoCombinationRefund() {
		OrderPO order = new OrderPO();
		OrderReturnPO returnPO = new OrderReturnPO();
		returnPO.setAfsSn("testAfsSn");
		returnPO.setActualReturnMoneyAmount(BigDecimal.valueOf(100));
		returnPO.setRefundType(null);

		// 设置模拟行为
		when(refundCalculator.refundPunishCalculate(any(OrderReturnPO.class), any(OrderPO.class)))
				.thenReturn(BigDecimal.valueOf(10));
		Mockito.when(orderRefundRecordService.update(any())).thenReturn(true);

		// 调用被测试方法
		afterServiceModel.recalculateRefundAmount(order, returnPO);

	}

	@Test
	public void testCheckRundByEnjoy() {
		OrderPO orderPO = getOrderPO();
		OrderAfterDTO orderAfterDTO = getOrderAfterDTO();

		// 构造订单货品列表
		List<OrderProductPO> orderProductPOList = new ArrayList<>();
		OrderProductPO orderProductPO1 = new OrderProductPO();
		orderProductPO1.setOrderSn("123");
		orderProductPO1.setEnabledFlag(OrderConst.ENABLED_FLAG_Y);
		orderProductPOList.add(orderProductPO1);
		OrderProductPO orderProductPO2 = new OrderProductPO();
		orderProductPO2.setOrderSn("123");
		orderProductPO2.setEnabledFlag(OrderConst.ENABLED_FLAG_Y);
		orderProductPOList.add(orderProductPO2);
		when(orderProductService.list(any())).thenReturn(orderProductPOList);

		// 构造售后申请列表
		List<OrderAfterDTO.AfterProduct> afterProductList = new ArrayList<>();
		OrderAfterDTO.AfterProduct afterProduct1 = new OrderAfterDTO.AfterProduct();
		afterProduct1.setOrderProductId(1L);
		afterProduct1.setAfsNum(1);
		afterProductList.add(afterProduct1);
		OrderAfterDTO.AfterProduct afterProduct2 = new OrderAfterDTO.AfterProduct();
		afterProduct2.setOrderProductId(2L);
		afterProduct2.setAfsNum(1);
		afterProductList.add(afterProduct2);

		List<OrderAfterPO> afterPOS = new ArrayList<>(orderAfterDTO.getProductList().size());
		for (OrderAfterDTO.AfterProduct afterProduct : orderAfterDTO.getProductList()) {
			OrderAfterPO orderAfterPO = new OrderAfterPO();
			orderAfterPO.setOrderProductId(afterProduct.getOrderProductId());
			orderAfterPO.setAfsNum(afterProduct.getAfsNum());
			afterPOS.add(orderAfterPO);
		}

		// 设置部分退款判断结果为true
		when(orderAfterService.isAllReturnApplied(
				afterPOS, orderProductPOList)).thenReturn(false);

		// 调用被测方法
		afterServiceModel.checkRundByEnjoy(orderAfterDTO, orderPO);

	}

	@Test
	public void testInsertAfterService() {
		String afsSn = "123";
		OrderPO orderPO = getOrderPO();
		List<OrderProductPO> orderProductPOS = getOrderProductPOS();
		AfsProductVO afsProductVO = new AfsProductVO(orderProductPOS.get(0));
		OrderAfterDTO orderAfterDTO = new OrderAfterDTO();

		afterServiceModel.insertAfterService(afsSn, afsProductVO, orderAfterDTO, orderPO);

		// 验证调用orderAfterMapper对象的insert方法
		verify(orderAfterMapper).insert(any(OrderAfterPO.class));
	}

	@Test
	public void testInsertReturnOrder() throws Exception {
		// 模拟输入数据
		String afsSn = "AFS123456";
		Integer afsType = 1;
		OrderPO orderPO = getOrderPO();
		List<OrderProductPO> orderProductPOS = getOrderProductPOS();
		AfsProductVO afsProductVO = new AfsProductVO(orderProductPOS.get(0));
		afsProductVO.setReturnMoneyAmount(BigDecimal.ZERO);
		afsProductVO.setActualReturnMoneyAmount(BigDecimal.ZERO);
		afsProductVO.setReturnExpressAmount(BigDecimal.ZERO);
		afsProductVO.setServiceFee(BigDecimal.ZERO);
		afsProductVO.setReturnBy(2);

		ValueOperations valueOperations = mock(ValueOperations.class);
		when(stringRedisTemplate.opsForValue()).thenReturn(valueOperations);
		when(stringRedisTemplate.opsForValue().get("refund_setting_switch")).thenReturn("1");
		when(orderReturnMapper.insert(any(OrderReturnPO.class))).thenReturn(1);
		when(orderReturnService.isLastReturn(Mockito.anyString())).thenReturn(true);

		orderPO.setPaymentCode(PayWayEnum.ALI_PAY.getValue());
		orderPO.setExchangeFlag(ExchangeOrderConst.ORDER_EXCHANGE_FLAG_1);
		when(orderMapper.listByExample(any(OrderExample.class))).thenReturn(Collections.singletonList(orderPO));

		OrderReturnPO orderReturnPO = new OrderReturnPO();
		orderReturnPO.setChannelServiceFee(BigDecimal.ZERO);
		when(orderReturnMapper.selectList(any())).thenReturn(Collections.singletonList(orderReturnPO));

		when(orderReturnService.update(any())).thenReturn(true);
		when(orderAfterSaleLogMapper.insert(any())).thenReturn(1);

		// 调用测试方法
		//OrderReturnPO actual = afterServiceModel.insertReturnOrder(afsSn, afsType, afsProductVO, orderPO);

		// 验证输出结果
		//assertNotNull(actual.getState());
	}


}