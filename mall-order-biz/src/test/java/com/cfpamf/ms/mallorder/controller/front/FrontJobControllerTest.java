//package com.cfpamf.ms.mallorder.controller.front;
//
//import com.cfpamf.ms.mallorder.StubIntergration;
//import org.junit.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//
//public class FrontJobControllerTest extends StubIntergration {
//
//     @Autowired
//     FrontJobController frontJobController;
//
//     @Test
//     public void cleanPromotion() {
//     frontJobController.cleanPromotion();
//     }
//}
/*
package com.cfpamf.ms.mallorder.controller.front;


import com.cfpamf.ms.mallorder.StubIntergration;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class FrontJobControllerTest  extends StubIntergration {

    @Autowired
    FrontJobController frontJobController;

    @Test
    public void cleanPromotion() {
        frontJobController.cleanPromotion();
    }
}*/
