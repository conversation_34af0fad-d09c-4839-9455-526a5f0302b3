//package com.cfpamf.ms.mallorder.model;
//
//import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
//import com.cfpamf.mallpayment.facade.api.MallPaymentFacade;
//import com.cfpamf.ms.mall.liquidate.api.BizActionFacade;
//import com.cfpamf.ms.mallmember.api.MemberFeignClient;
//import com.cfpamf.ms.mallorder.common.enums.LoanStatusEnum;
//import com.cfpamf.ms.mallorder.common.enums.OrderReturnStatus;
//import com.cfpamf.ms.mallorder.common.mq.msg.TransferRefundMsg;
//import com.cfpamf.ms.mallorder.controller.fegin.facade.CustomerServiceFeign;
//import com.cfpamf.ms.mallorder.enums.PayWayEnum;
//import com.cfpamf.ms.mallorder.integration.pay.PayIntegration;
//import com.cfpamf.ms.mallorder.mapper.*;
//import com.cfpamf.ms.mallorder.po.OrderAfterPO;
//import com.cfpamf.ms.mallorder.po.OrderPO;
//import com.cfpamf.ms.mallorder.po.OrderReturnPO;
//import com.cfpamf.ms.mallorder.service.*;
//import com.cfpamf.ms.mallshop.api.StoreFeignClient;
//import com.cfpamf.ms.mallsystem.api.ExpressFeignClient;
//import com.cfpamf.smartid.client.utils.sharding.ShardingId;
//import com.github.jsonzou.jmockdata.JMockData;
//import com.slodon.bbc.core.util.AssertUtil;
//import org.apache.ibatis.builder.MapperBuilderAssistant;
//import org.apache.ibatis.session.Configuration;
//import org.junit.Assert;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.powermock.modules.junit4.PowerMockRunner;
//import org.springframework.amqp.rabbit.core.RabbitTemplate;
//import org.springframework.data.redis.core.StringRedisTemplate;
//
//import java.math.BigDecimal;
//import java.util.*;
//
//import static org.mockito.Mockito.*;
//
//
//@RunWith(PowerMockRunner.class)
//public class OrderAfterServiceModelTest {
//    @Mock
//    OrderAfterMapper orderAfterMapper;
//    @Mock
//    OrderReturnMapper orderReturnMapper;
//    @Mock
//    OrderReplacementMapper orderReplacementMapper;
//    @Mock
//    OrderMapper orderMapper;
//    @Mock
//    OrderProductMapper orderProductMapper;
//    @Mock
//    OrderExtendMapper orderExtendMapper;
//    @Mock
//    OrderAfterSaleLogMapper orderAfterSaleLogMapper;
//    @Mock
//    OrderReturnTrackMapper orderReturnTrackMapper;
//    @Mock
//    StringRedisTemplate stringRedisTemplate;
//    @Mock
//    ExpressFeignClient expressFeignClient;
//    @Mock
//    RabbitTemplate rabbitTemplate;
//    @Mock
//    PayIntegration payIntegration;
//    @Mock
//    OrderModel orderModel;
//    @Mock
//    StoreFeignClient storeFeignClient;
//    @Mock
//    MemberFeignClient memberFeignClient;
//    @Mock
//    CustomerServiceFeign customerServiceFeign;
//    @Mock
//    MallPaymentFacade mallPaymentFacade;
//    @Mock
//    OrderReturnModel orderReturnModel;
//    @Mock
//    IOrderService orderService;
//    @Mock
//    IOrderReturnService orderReturnService;
//    @Mock
//    IOrderPlacingService orderPlacingService;
//    @Mock
//    IOrderProductService orderProductService;
//    @Mock
//    IOrderAfterService orderAfterService;
//    @Mock
//    BizActionFacade bizActionFacade;
//    @InjectMocks
//    OrderAfterServiceModel orderAfterServiceModel;
//
//
//    @Test
//    public void testLoanRefundResult() throws Exception {
//        TransferRefundMsg refundMsg = JMockData.mock(TransferRefundMsg.class);
//        refundMsg.getList().get(0).setRefundStatus(OrderReturnStatus.REFUND_SUCCESS.getValue());
//        refundMsg.getList().get(0).setPaymentId("123456");
//        refundMsg.getList().get(0).setAfsSn("123456789");
//        refundMsg.getList().get(0).setRefundAmount(new BigDecimal("123"));
//
//        OrderReturnPO orderReturnPO = new OrderReturnPO();
//        orderReturnPO.setActualReturnMoneyAmount(new BigDecimal("123"));
//        orderReturnPO.setReturnExpressAmount(BigDecimal.ZERO);
//        orderReturnPO.setState(300);
//
//        OrderPO orderPO = new OrderPO();
//        orderPO.setPaymentCode(PayWayEnum.ENJOY_PAY.getValue());
//        orderPO.setLoanPayState(LoanStatusEnum.LENDING_SUCCESS.getValue());
//
//
//        when(orderModel.getOrderByOrderSn(any())).thenReturn(new OrderPO());
//        when(orderMapper.listByExample(any())).thenReturn(Collections.singletonList(orderPO));
//        when(orderReturnModel.getOrderAfterServiceByAfsSn(any())).thenReturn(new OrderAfterPO());
//        when(orderReturnService.getByAfsSn(any())).thenReturn(orderReturnPO);
//        when(orderReturnService.update(any())).thenReturn(true);
//
//        doNothing().when(payIntegration).loanRefundByPayment(any(), any());
//
//        //初始化实体
//        Configuration configuration2 = new Configuration();
//        MapperBuilderAssistant mapperBuilderAssistant2 = new MapperBuilderAssistant(configuration2, "111");
//        TableInfoHelper.initTableInfo(mapperBuilderAssistant2, OrderReturnPO.class);
//        boolean result = orderAfterServiceModel.loanRefundResult(refundMsg);
//        Assert.assertTrue(result);
//    }
//}
//
//