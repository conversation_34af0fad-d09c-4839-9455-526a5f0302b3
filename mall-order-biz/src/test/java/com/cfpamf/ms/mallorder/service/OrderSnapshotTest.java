package com.cfpamf.ms.mallorder.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.mallgoods.facade.vo.StockCutVO;
import com.cfpamf.ms.mallorder.common.enums.BizTypeEnum;
import com.cfpamf.ms.mallorder.mapper.OrderSnapshotMapper;
import com.cfpamf.ms.mallorder.po.OrderSnapshotPO;
import com.cfpamf.ms.mallorder.req.OrderRenewalPriceReq;
import com.cfpamf.ms.mallorder.service.impl.OrderServiceImpl;
import com.cfpamf.ms.mallorder.service.impl.OrderSnapshotServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(SpringJUnit4ClassRunner.class)
public class OrderSnapshotTest {

	@InjectMocks
	private OrderSnapshotServiceImpl orderSnapshotService;
	@Mock
	private OrderSnapshotMapper orderSnapshotMapper;

	@InjectMocks
	private OrderServiceImpl orderService;

	@Before
	public void setUp() {
		MockitoAnnotations.initMocks(this);
	}

	@Test
	public void orderSnapshotTest() {
		String str = "[{\"batchNo\":\"CGTB2022122700000001151\",\"financeCutCount\":1.4E+2," +
				"\"productId\":200000904508,\"sellCutCount\":1.4E+2},{\"batchNo\":\"CGTB2022122800000001153\"," +
				"\"financeCutCount\":4E+1,\"productId\":200000904508,\"sellCutCount\":4E+1}]";
		List<StockCutVO> stockCutVOS = new ArrayList<>(2);
		StockCutVO stockCutVO = new StockCutVO();
		stockCutVOS.add(stockCutVO);
		stockCutVO.setProductId(200000904508L);
		stockCutVO.setBatchNo("CGTB2022122700000001151");
		stockCutVO.setSellCutCount(new BigDecimal("40"));
		stockCutVO.setFinanceCutCount(new BigDecimal("140"));
		StockCutVO stockCutVO2 = new StockCutVO();
		stockCutVOS.add(stockCutVO2);
		stockCutVO2.setProductId(200000904508L);
		stockCutVO2.setBatchNo("CGTB20221228000000011531");
		stockCutVO2.setSellCutCount(new BigDecimal("40"));
		stockCutVO2.setFinanceCutCount(new BigDecimal("140"));

		String orderSn = "8222122866714857";

		when(orderSnapshotMapper.insert(any())).thenReturn(1);

		orderSnapshotService.saveOrderProductStockBatch(orderSn, stockCutVOS.get(0).getProductId().toString(),
				BizTypeEnum.REDUCE_STOCK, stockCutVOS, "system");

	}


	@Test
	public void refreshOrderSnapshotTest() {
		String str = "[\n" +
				"  {\n" +
				"    \"snapshot_id\": 1852,\n" +
				"    \"biz_sn\": \"8223040929201531\",\n" +
				"    \"sub_biz_sn\": \"200010720008\",\n" +
				"    \"biz_type\": \"REDUCE_STOCK_BATCH_LIST\",\n" +
				"    \"biz_type_desc\": \"下单扣减商品批次号库存\",\n" +
				"    \"remark\": \"\",\n" +
//				"    \"snapshot\": \"{\"batchNo\":\"CGTB2023040400000003856\",\"financeCutCount\":2,\"productId\":200010720008,\"sellCutCount\":2}\",\n" +
//				"    \"order_snapshot\": \"{\"batchNo\": \"CGTB2023040400000003856\", \"productId\": 200010720008, \"sellCutCount\": 2, \"financeCutCount\": 2}\",\n" +
				"    \"create_time\": \"2023-04-09 12:18:23\",\n" +
				"    \"update_time\": \"2023-04-09 12:18:23\",\n" +
				"    \"create_by\": \"system\",\n" +
				"    \"update_by\": \"system\",\n" +
				"    \"enabled_flag\": 1\n" +
				"  },\n" +
				"  {\n" +
				"    \"snapshot_id\": 1856,\n" +
				"    \"biz_sn\": \"8223040929201531\",\n" +
				"    \"sub_biz_sn\": \"200010720007\",\n" +
				"    \"biz_type\": \"INCREASE_STOCK_BATCH_LIST\",\n" +
				"    \"biz_type_desc\": \"改价回退原批次号库存\",\n" +
				"    \"remark\": \"\",\n" +
//				"    \"snapshot\": \"{\"batchNo\":\"CGTB2023040400000003857\",\"financeCutCount\":2,\"productId\":200010720007,\"sellCutCount\":2}\",\n" +
//				"    \"order_snapshot\": \"{\"batchNo\": \"CGTB2023040400000003857\", \"productId\": 200010720007, \"sellCutCount\": 2, \"financeCutCount\": 2}\",\n" +
				"    \"create_time\": \"2023-04-09 13:34:28\",\n" +
				"    \"update_time\": \"2023-04-09 13:34:28\",\n" +
				"    \"create_by\": \"system\",\n" +
				"    \"update_by\": \"system\",\n" +
				"    \"enabled_flag\": 1\n" +
				"  }]";
		List<OrderSnapshotPO> orderSnapshotPOS = JSONArray.parseArray(str, OrderSnapshotPO.class);
		StockCutVO stockCutVO = new StockCutVO();
		stockCutVO.setProductId(200000904508L);
		stockCutVO.setBatchNo("CGTB2023040400000003856");
		stockCutVO.setSellCutCount(new BigDecimal("2"));
		stockCutVO.setFinanceCutCount(new BigDecimal("2"));
		List<StockCutVO> stockCutVOS = new ArrayList<>(1);
		stockCutVOS.add(stockCutVO);
		orderSnapshotPOS.get(0).setSnapshot("[]");
		StockCutVO stockCutVO2 = new StockCutVO();
		stockCutVO2.setProductId(200000904508L);
		stockCutVO2.setBatchNo("CGTB2023040400000003857");
		stockCutVO2.setSellCutCount(new BigDecimal("2"));
		stockCutVO2.setFinanceCutCount(new BigDecimal("2"));
		List<StockCutVO> stockCutVOS2 = new ArrayList<>(1);
		stockCutVOS2.add(stockCutVO2);
		orderSnapshotPOS.get(1).setSnapshot("[]");

		when(orderSnapshotMapper.list(any())).thenReturn(orderSnapshotPOS);
		when(orderSnapshotMapper.insertBatch(any())).thenReturn(1);
		when(orderSnapshotMapper.deleteBatchIds(any())).thenReturn(1);

		orderSnapshotService.refreshOrderSnapshot();
	}

	@Test
	public void renewalPriceTest() {
		String orderSn = "8223020125980717";

		String req = "{\"orderProductReqs\":[{\"renewalProductShowPrice\":110,\"renewalLandingPrice\":1," +
				"\"renewalBatchNo\":\"CGTB2023010400000002727\",\"orderProductId\":42990}]," +
				"\"orderSn\":\"8223020125980717\",\"renewalExpressFee\":0,\"remark\":\"改价测试\"}";
		OrderRenewalPriceReq orderRenewalPriceReq = JSONObject.parseObject(req, OrderRenewalPriceReq.class);

		//orderService.renewalPrice(orderRenewalPriceReq, 1L, "xwj");

	}

	@Test
	public void test1() {
		StringBuilder performanceService = new StringBuilder();
		performanceService.append(",").append(1);
		performanceService.append(",").append(0);
		performanceService.append(",").append(3);

		String join = performanceService.substring(1);

		Set<String> performances = new HashSet<>();
		List<String> strings = Arrays.asList(join.split(","));
		performances.addAll(strings);
		performances.addAll(strings);
		String s = performances.toString();
	}

}