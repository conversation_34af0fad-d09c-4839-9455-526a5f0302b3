package com.cfpamf.ms.mallorder.service;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.cfpamf.ms.bizconfig.facade.vo.UserVo;
import com.cfpamf.ms.customer.facade.vo.CustBaseInfoVo;
import com.cfpamf.ms.customer.facade.vo.CustDetailVo;
import com.cfpamf.ms.mallorder.common.enums.OrderOfflineSettlementEnum;
import com.cfpamf.ms.mallorder.dto.OrderOfflineManualSettlementDTO;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.integration.cust.BizConfigIntegration;
import com.cfpamf.ms.mallorder.integration.cust.CustomerIntegration;
import com.cfpamf.ms.mallorder.mapper.OrderOfflineMapper;
import com.cfpamf.ms.mallorder.model.OrderLogModel;
import com.cfpamf.ms.mallorder.po.OrderOfflinePO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderPayPO;
import com.cfpamf.ms.mallorder.service.impl.*;
import com.cfpamf.ms.mallorder.vo.CustInfoVo;
import com.cfpamf.ms.mallshop.resp.Vendor;
import com.google.common.collect.Lists;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(SpringJUnit4ClassRunner.class)
public class OrderOfflineServiceTest {
	@InjectMocks
	private OrderOfflineServiceImpl orderOfflineService;

	@Mock
	private OrderOfflineMapper orderOfflineMapper;

	@Mock
	private BizConfigIntegration bizConfigIntegration;

	@Mock
	private CustomerIntegration customerIntegration;

	@Mock
	private OrderServiceImpl orderService;

	@Mock
	private OrderPayServiceImpl orderPayService;

	@Mock
	private OrderExtendServiceImpl orderExtendService;

	@Mock
	private OrderLogModel orderLogModel;

	@Mock
	private OrderCreateHelper orderCreateHelper;

	@Before
	public void setUp() {
		MockitoAnnotations.initMocks(this);
		TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), OrderOfflinePO.class);
	}

	@Test
	public void testQueryOrderOfflineList_WithValidPaySn() {
		// Arrange
		String paySn = "123456";
		LambdaQueryWrapper<OrderOfflinePO> expectedQuery = new LambdaQueryWrapper<>();
		expectedQuery.eq(OrderOfflinePO::getPaySn, paySn);

		List<OrderOfflinePO> expectedList = new ArrayList<>();
		expectedList.add(new OrderOfflinePO());

		when(orderOfflineMapper.selectList(anyObject())).thenReturn(expectedList);

		// Act
		List<OrderOfflinePO> result = orderOfflineService.queryOrderOfflineList(paySn);

		// Assert
		assertNotNull(result);
		assertEquals(expectedList, result);
	}

	@Test
	public void testQueryOrderOfflineList_WithNullPaySn() {
		// Arrange
		String paySn = null;

		// Act
		List<OrderOfflinePO> result = orderOfflineService.queryOrderOfflineList(paySn);

		// Assert
		assertNull(result);
		verifyNoInteractions(orderOfflineMapper);
	}

	@Test
	public void testQueryCustInfo_withValidUserCode_shouldReturnCustInfoVo() {
		// Arrange
		String userCode = "ABC123";
		UserVo userVo = new UserVo();
		userVo.setUserCode(userCode);
		userVo.setUserName("John Doe");
		userVo.setUserIdNo("1234567890");

		when(bizConfigIntegration.getDefaultUserByUserCode(userCode)).thenReturn(userVo);

		CustBaseInfoVo custBaseInfoVo = new CustBaseInfoVo();
		custBaseInfoVo.setIdNo("1234567890");
		custBaseInfoVo.setMobile("1234567890");
		CustDetailVo custDetailVo = new CustDetailVo();
		custDetailVo.setLoanBranch("XYZ");
		custDetailVo.setLoanManager("MGR001");
		custBaseInfoVo.setCustDetail(custDetailVo);

		when(customerIntegration.baseInfoByIdNo(userVo.getUserIdNo())).thenReturn(custBaseInfoVo);

		UserVo managerInfo = new UserVo();
		managerInfo.setUserCode("MGR001");
		managerInfo.setBranchName("Branch 1");
		managerInfo.setUserName("Manager 1");

		when(customerIntegration.getUserInfoByUserCode(
				custBaseInfoVo.getCustDetail().getLoanManager(),
				custBaseInfoVo.getCustDetail().getLoanBranch()
		)).thenReturn(managerInfo);

		// Act
		CustInfoVo result = orderOfflineService.queryCustInfo(userCode);

		// Assert
		assertNotNull(result);
		assertEquals(userCode, result.getUserCode());
		assertEquals(userVo.getUserName(), result.getUserName());
		assertEquals(custBaseInfoVo.getIdNo(), result.getIdNo());
		assertEquals(custBaseInfoVo.getMobile(), result.getMobile());
		assertEquals(custDetailVo.getLoanBranch(), result.getBranchCode());
		assertEquals(custDetailVo.getLoanManager(), result.getManager());
		assertEquals(managerInfo.getBranchName(), result.getBranchName());
		assertEquals(managerInfo.getUserName(), result.getManagerName());

		verify(bizConfigIntegration).getDefaultUserByUserCode(userCode);
		verify(customerIntegration).baseInfoByIdNo(userVo.getUserIdNo());
		verify(customerIntegration).getUserInfoByUserCode(
				custBaseInfoVo.getCustDetail().getLoanManager(),
				custBaseInfoVo.getCustDetail().getLoanBranch()
		);
	}

	@Test
	public void testQueryCustInfo_withInvalidUserCode_shouldReturnNull() {
		// Arrange
		String userCode = "XYZ789";

		when(bizConfigIntegration.getDefaultUserByUserCode(userCode)).thenReturn(null);

		// Act
		CustInfoVo result = orderOfflineService.queryCustInfo(userCode);

		// Assert
		assertNull(result);

		verify(bizConfigIntegration).getDefaultUserByUserCode(userCode);
		verifyZeroInteractions(customerIntegration);
	}

	@Test
	public void testQueryCustInfo_withNullCustBaseInfoVo_shouldReturnNull() {
		// Arrange
		String userCode = "ABC123";
		UserVo userVo = new UserVo();
		userVo.setUserCode(userCode);
		userVo.setUserName("John Doe");
		userVo.setUserIdNo("1234567890");

		when(bizConfigIntegration.getDefaultUserByUserCode(userCode)).thenReturn(userVo);
		when(customerIntegration.baseInfoByIdNo(userVo.getUserIdNo())).thenReturn(null);

		// Act
		CustInfoVo result = orderOfflineService.queryCustInfo(userCode);

		// Assert
		assertNull(result);

		verify(bizConfigIntegration).getDefaultUserByUserCode(userCode);
		verify(customerIntegration).baseInfoByIdNo(userVo.getUserIdNo());
	}

	@Test
	public void testManualSettlement() {
		// 模拟输入参数
		Vendor vendor = new Vendor();
		vendor.setVendorId(10223L);
		vendor.setVendorName("vendorName");

		OrderOfflineManualSettlementDTO orderOfflineManualSettlement = new OrderOfflineManualSettlementDTO();
		orderOfflineManualSettlement.setPaySn("11104954059");
		orderOfflineManualSettlement.setOrderOfflineSettlement(OrderOfflineSettlementEnum.NO_PAYMENT);
		orderOfflineManualSettlement.setOrderType(OrderTypeEnum.ORDER_TYPE_6);
		orderOfflineManualSettlement.setRemark("remark");
		orderOfflineManualSettlement.setOrderOfflineList(Lists.newArrayList());

		// 模拟返回数据
		List<OrderPO> data = new ArrayList<>();
		OrderPO orderPO = new OrderPO();
		orderPO.setOrderId(110);
		orderPO.setOrderSn("memberName");
		orderPO.setUserNo("userNo");
		orderPO.setPaySn("paySn");
		orderPO.setSellerId("sellerId");
		orderPO.setBankPayTrxNo("bankPayTrxNo");
		orderPO.setMemberName("memberName");
		orderPO.setMemberId(32330);
		orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
		orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
		orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
		orderPO.setOrderState(30);
		orderPO.setLoanPayState(70);
		orderPO.setPaymentName("paymentName");
		orderPO.setPaymentCode("paymentCode");
		orderPO.setOrderAmount(new BigDecimal("0.00"));
		orderPO.setGoodsAmount(new BigDecimal("0.00"));
		orderPO.setExpressFee(new BigDecimal("0.00"));
		orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
		orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
		orderPO.setXzCardAmount(new BigDecimal("0.00"));
		orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
		orderPO.setComposePayName("composeWay");
		orderPO.setBalanceAmount(new BigDecimal("0.00"));
		orderPO.setPayAmount(new BigDecimal("0.00"));
		orderPO.setAreaCode("areaCode");
		orderPO.setOrderType(5);
		orderPO.setServiceFee(new BigDecimal("0.00"));
		orderPO.setServiceFeeRate(new BigDecimal("0.00"));
		orderPO.setSettleMode("settleMode");
		orderPO.setFinanceRuleCode("financeRuleCode");
		orderPO.setIsDelivery(0);
		orderPO.setChannel("channel");
		orderPO.setChannelServiceFee(new BigDecimal("0.00"));
		orderPO.setNewOrder(false);
		orderPO.setCustomerConfirmStatus(0);
		orderPO.setOrderPlaceUserRoleCode(0);
		orderPO.setExchangeFlag(0);
		data.add(orderPO);

		// 设置Mock方法的行为
		when(orderService.listByPaySn(anyString())).thenReturn(data);
		when(orderPayService.getByPaySn(anyString())).thenReturn(new OrderPayPO());
		when(orderService.update(any(), any())).thenReturn(true);
		when(orderExtendService.update(any(), any())).thenReturn(true);
		doNothing().when(orderLogModel).insertOrderLog(any(), any(), anyString(), anyString(), anyInt(), anyInt(), anyInt(), anyString(), any(), anyString());


		// 调用被测试方法
		orderOfflineService.manualSettlement(vendor, orderOfflineManualSettlement);

		// 验证相应的行为是否发生
		verify(orderExtendService).updateOrderRemark(any(), any());
		verify(orderService).update(any(), any());
	}

}
