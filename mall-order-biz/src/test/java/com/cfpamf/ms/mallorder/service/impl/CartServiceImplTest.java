package com.cfpamf.ms.mallorder.service.impl;

import com.cfpamf.framework.autoconfigure.redis.lock.DistributeLock;
import com.cfpamf.ms.mallgoods.facade.api.ProductFeignClient;
import com.cfpamf.ms.mallgoods.facade.vo.*;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.common.util.CartUtils;
import com.cfpamf.ms.mallorder.common.util.OrderLocalUtils;
import com.cfpamf.ms.mallorder.common.util.OrderSubmitAttributesUtils;
import com.cfpamf.ms.mallorder.common.util.PromotionUtils;
import com.cfpamf.ms.mallorder.dto.OrderAddressDTO;
import com.cfpamf.ms.mallorder.dto.OrderSkuInfoDTO;
import com.cfpamf.ms.mallorder.model.CartModel;
import com.cfpamf.ms.mallorder.po.CartPO;
import com.cfpamf.ms.mallorder.vo.CartListVO;
import com.cfpamf.ms.mallshop.api.StoreRegionFeignClient;
import com.cfpamf.ms.mallshop.vo.FrontStoreRegionVo;
import com.cfpamf.ms.mallshop.vo.StoreRegionVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class CartServiceImplTest {

    @Mock
    private OrderLocalUtils mockOrderLocalUtils;
    @Mock
    private OrderSubmitAttributesUtils mockOrderSubmitAttributesUtils;
    @Mock
    private PromotionUtils mockPromotionUtils;
    @Mock
    private CartModel mockCartModel;
    @Mock
    private CartUtils mockCartUtils;
    @Mock
    private OrderCreateHelper mockOrderCreateHelper;
    @Mock
    private OrderProductHelper mockOrderProductHelper;
    @Mock
    private ProductFeignClient mockProductFeignClient;
    @Mock
    private DistributeLock mockDistributeLock;
    @Mock
    private StoreRegionFeignClient mockStoreRegionFeignClient;

    @InjectMocks
    private CartServiceImpl cartServiceImplUnderTest;

    @Test
    public void testAddCart2() {
        // Setup
        final Member member = new Member();
        member.setMemberId(0);
        member.setUserNo("userNo");
        member.setCustNo("custNo");
        member.setCappCustNo("cappCustNo");
        member.setMemberName("memberName");

        // Configure OrderSubmitAttributesUtils.getProductPriceByProductId(...).
        final ProductPriceVO productPriceVO = new ProductPriceVO();
        final Product product = new Product();
        product.setProductId(0L);
        product.setActivityPrice(new BigDecimal("0.00"));
        product.setProductStock(0);
        product.setState(0);
        product.setBatchNo("batchNo");
        productPriceVO.setProduct(product);
        final ProductExtend productExtend = new ProductExtend();
        productExtend.setIsSelfLift(0);
        productPriceVO.setProductExtend(productExtend);
        final Goods goods = new Goods();
        goods.setState(0);
        goods.setIsVirtualGoods(0);
        goods.setFundsBorrowable(0);
        productPriceVO.setGoods(goods);
        productPriceVO.setEffectiveStock(0);
//        when(mockOrderSubmitAttributesUtils.getProductPriceByProductId(0L, "areaCode", "financeRuleCode"))
//                .thenReturn(productPriceVO);

        // Run the test
//        final Boolean result = cartServiceImplUnderTest.addCart(member, 0L, "areaCode", 0, "financeRuleCode");

        // Verify the results
//        assertFalse(result);
//        verify(mockDistributeLock).lockAndProcess(eq("lockName"), eq(0L), eq(30L), eq(TimeUnit.SECONDS),
//                any(Supplier.class));

        // Confirm CartModel.addToCart(...).
        final ProductPriceVO productPriceVO1 = new ProductPriceVO();
        final Product product1 = new Product();
        product1.setProductId(0L);
        product1.setActivityPrice(new BigDecimal("0.00"));
        product1.setProductStock(0);
        product1.setState(0);
        product1.setBatchNo("batchNo");
        productPriceVO1.setProduct(product1);
        final ProductExtend productExtend1 = new ProductExtend();
        productExtend1.setIsSelfLift(0);
        productPriceVO1.setProductExtend(productExtend1);
        final Goods goods1 = new Goods();
        goods1.setState(0);
        goods1.setIsVirtualGoods(0);
        goods1.setFundsBorrowable(0);
        productPriceVO1.setGoods(goods1);
        productPriceVO1.setEffectiveStock(0);
//        verify(mockCartModel).addToCart(0, 0, "areaCode", productPriceVO1);

        // Confirm CartModel.mutuallyExclusiveCartAction(...).
        final ProductPriceVO productPriceVO2 = new ProductPriceVO();
        final Product product2 = new Product();
        product2.setProductId(0L);
        product2.setActivityPrice(new BigDecimal("0.00"));
        product2.setProductStock(0);
        product2.setState(0);
        product2.setBatchNo("batchNo");
        productPriceVO2.setProduct(product2);
        final ProductExtend productExtend2 = new ProductExtend();
        productExtend2.setIsSelfLift(0);
        productPriceVO2.setProductExtend(productExtend2);
        final Goods goods2 = new Goods();
        goods2.setState(0);
        goods2.setIsVirtualGoods(0);
        goods2.setFundsBorrowable(0);
        productPriceVO2.setGoods(goods2);
        productPriceVO2.setEffectiveStock(0);
//        verify(mockCartModel).mutuallyExclusiveCartAction(0, productPriceVO2);
    }

    @Test
    public void testGetCartList2() {
        // Setup
        final CartPO cartPO = new CartPO();
        cartPO.setCartId(0);
        cartPO.setMemberId(0);
        cartPO.setStoreId(0L);
        cartPO.setGoodsName("goodsName");
        cartPO.setProductId(0L);
        cartPO.setBuyNum(0);
        cartPO.setProductPrice(new BigDecimal("0.00"));
        cartPO.setLandingPrice(new BigDecimal("0.00"));
        cartPO.setTaxPrice(new BigDecimal("0.00"));
        cartPO.setPromotionId(0);
        cartPO.setPromotionType(0);
        cartPO.setPromotionDescription("description");
        cartPO.setOffPrice(new BigDecimal("0.00"));
        cartPO.setProductState(0);
        cartPO.setProductType(0);
        cartPO.setAreaCode("areaCode");
        cartPO.setFinanceRuleCode("financeRuleCode");
        cartPO.setEnabledFlag(0);
        final CartPO cartPO1 = new CartPO();
        cartPO1.setCartId(0);
        cartPO1.setMemberId(0);
        cartPO1.setStoreId(0L);
        cartPO1.setGoodsName("goodsName");
        cartPO1.setProductId(0L);
        cartPO1.setBuyNum(0);
        cartPO1.setProductPrice(new BigDecimal("0.00"));
        cartPO1.setLandingPrice(new BigDecimal("0.00"));
        cartPO1.setTaxPrice(new BigDecimal("0.00"));
        cartPO1.setPromotionId(0);
        cartPO1.setPromotionType(0);
        cartPO1.setPromotionDescription("description");
        cartPO1.setOffPrice(new BigDecimal("0.00"));
        cartPO1.setProductState(0);
        cartPO1.setProductType(0);
        cartPO1.setAreaCode("areaCode");
        cartPO1.setFinanceRuleCode("financeRuleCode");
        cartPO1.setEnabledFlag(0);
//        final CartListVO expectedResult = new CartListVO(new OrderConfirmDTO(Arrays.asList(cartPO)),
//                Arrays.asList(cartPO1));

        // Configure CartModel.refreshCart(...).
        final CartPO cartPO2 = new CartPO();
        cartPO2.setCartId(0);
        cartPO2.setMemberId(0);
        cartPO2.setStoreId(0L);
        cartPO2.setGoodsName("goodsName");
        cartPO2.setProductId(0L);
        cartPO2.setBuyNum(0);
        cartPO2.setProductPrice(new BigDecimal("0.00"));
        cartPO2.setLandingPrice(new BigDecimal("0.00"));
        cartPO2.setTaxPrice(new BigDecimal("0.00"));
        cartPO2.setPromotionId(0);
        cartPO2.setPromotionType(0);
        cartPO2.setPromotionDescription("description");
        cartPO2.setOffPrice(new BigDecimal("0.00"));
        cartPO2.setProductState(0);
        cartPO2.setProductType(0);
        cartPO2.setAreaCode("areaCode");
        cartPO2.setFinanceRuleCode("financeRuleCode");
        cartPO2.setEnabledFlag(0);
        final List<CartPO> cartPOS = Arrays.asList(cartPO2);
        final OrderAddressDTO addressDTO = new OrderAddressDTO();
        addressDTO.setReceiverName("receiverName");
        addressDTO.setReceiverMobile("receiverMobile");
        addressDTO.setProvince("province");
        addressDTO.setCity("city");
        addressDTO.setCityCode("cityCode");
//        when(mockCartModel.refreshCart(0, 0, addressDTO, false)).thenReturn(cartPOS);

        // Configure OrderSubmitAttributesUtils.getProductPriceByProductId(...).
        final ProductPriceVO productPriceVO = new ProductPriceVO();
        final Product product = new Product();
        product.setProductId(0L);
        product.setActivityPrice(new BigDecimal("0.00"));
        product.setProductStock(0);
        product.setState(0);
        product.setBatchNo("batchNo");
        productPriceVO.setProduct(product);
        final ProductExtend productExtend = new ProductExtend();
        productExtend.setIsSelfLift(0);
        productPriceVO.setProductExtend(productExtend);
        final Goods goods = new Goods();
        goods.setState(0);
        goods.setIsVirtualGoods(0);
        goods.setFundsBorrowable(0);
        productPriceVO.setGoods(goods);
        productPriceVO.setEffectiveStock(0);
//        when(mockOrderSubmitAttributesUtils.getProductPriceByProductId(0L, "areaCode", "financeRuleCode"))
//                .thenReturn(productPriceVO);

        // Configure OrderSubmitAttributesUtils.getPromotionList(...).
        final GoodsPromotion goodsPromotion = new GoodsPromotion();
        goodsPromotion.setGoodsPromotionId(0);
        goodsPromotion.setPromotionId(0);
        goodsPromotion.setStoreId(0L);
        goodsPromotion.setPromotionType(0);
        goodsPromotion.setDescription("description");
        final List<GoodsPromotion> goodsPromotions = Arrays.asList(goodsPromotion);
        final Product product1 = new Product();
        product1.setProductId(0L);
        product1.setActivityPrice(new BigDecimal("0.00"));
        product1.setProductStock(0);
        product1.setState(0);
        product1.setBatchNo("batchNo");
//        when(mockOrderSubmitAttributesUtils.getPromotionList(product1)).thenReturn(goodsPromotions);

        // Run the test
        final CartListVO result = cartServiceImplUnderTest.getCartList(0, 0);

        // Verify the results
//        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetCartList_CartModelReturnsNoItems2() {
        // Setup
        final CartPO cartPO = new CartPO();
        cartPO.setCartId(0);
        cartPO.setMemberId(0);
        cartPO.setStoreId(0L);
        cartPO.setGoodsName("goodsName");
        cartPO.setProductId(0L);
        cartPO.setBuyNum(0);
        cartPO.setProductPrice(new BigDecimal("0.00"));
        cartPO.setLandingPrice(new BigDecimal("0.00"));
        cartPO.setTaxPrice(new BigDecimal("0.00"));
        cartPO.setPromotionId(0);
        cartPO.setPromotionType(0);
        cartPO.setPromotionDescription("description");
        cartPO.setOffPrice(new BigDecimal("0.00"));
        cartPO.setProductState(0);
        cartPO.setProductType(0);
        cartPO.setAreaCode("areaCode");
        cartPO.setFinanceRuleCode("financeRuleCode");
        cartPO.setEnabledFlag(0);
        final CartPO cartPO1 = new CartPO();
        cartPO1.setCartId(0);
        cartPO1.setMemberId(0);
        cartPO1.setStoreId(0L);
        cartPO1.setGoodsName("goodsName");
        cartPO1.setProductId(0L);
        cartPO1.setBuyNum(0);
        cartPO1.setProductPrice(new BigDecimal("0.00"));
        cartPO1.setLandingPrice(new BigDecimal("0.00"));
        cartPO1.setTaxPrice(new BigDecimal("0.00"));
        cartPO1.setPromotionId(0);
        cartPO1.setPromotionType(0);
        cartPO1.setPromotionDescription("description");
        cartPO1.setOffPrice(new BigDecimal("0.00"));
        cartPO1.setProductState(0);
        cartPO1.setProductType(0);
        cartPO1.setAreaCode("areaCode");
        cartPO1.setFinanceRuleCode("financeRuleCode");
        cartPO1.setEnabledFlag(0);
//        final CartListVO expectedResult = new CartListVO(new OrderConfirmDTO(Arrays.asList(cartPO)),
//                Arrays.asList(cartPO1));

        // Configure CartModel.refreshCart(...).
        final OrderAddressDTO addressDTO = new OrderAddressDTO();
        addressDTO.setReceiverName("receiverName");
        addressDTO.setReceiverMobile("receiverMobile");
        addressDTO.setProvince("province");
        addressDTO.setCity("city");
        addressDTO.setCityCode("cityCode");
//        when(mockCartModel.refreshCart(0, 0, addressDTO, false)).thenReturn(Collections.emptyList());

        // Run the test
        final CartListVO result = cartServiceImplUnderTest.getCartList(0, 0);

        // Verify the results
//        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetCartList_OrderSubmitAttributesUtilsGetPromotionListReturnsNoItems23() {
        // Setup
        final CartPO cartPO = new CartPO();
        cartPO.setCartId(0);
        cartPO.setMemberId(0);
        cartPO.setStoreId(0L);
        cartPO.setGoodsName("goodsName");
        cartPO.setProductId(0L);
        cartPO.setBuyNum(0);
        cartPO.setProductPrice(new BigDecimal("0.00"));
        cartPO.setLandingPrice(new BigDecimal("0.00"));
        cartPO.setTaxPrice(new BigDecimal("0.00"));
        cartPO.setPromotionId(0);
        cartPO.setPromotionType(0);
        cartPO.setPromotionDescription("description");
        cartPO.setOffPrice(new BigDecimal("0.00"));
        cartPO.setProductState(0);
        cartPO.setProductType(0);
        cartPO.setAreaCode("areaCode");
        cartPO.setFinanceRuleCode("financeRuleCode");
        cartPO.setEnabledFlag(0);
        final CartPO cartPO1 = new CartPO();
        cartPO1.setCartId(0);
        cartPO1.setMemberId(0);
        cartPO1.setStoreId(0L);
        cartPO1.setGoodsName("goodsName");
        cartPO1.setProductId(0L);
        cartPO1.setBuyNum(0);
        cartPO1.setProductPrice(new BigDecimal("0.00"));
        cartPO1.setLandingPrice(new BigDecimal("0.00"));
        cartPO1.setTaxPrice(new BigDecimal("0.00"));
        cartPO1.setPromotionId(0);
        cartPO1.setPromotionType(0);
        cartPO1.setPromotionDescription("description");
        cartPO1.setOffPrice(new BigDecimal("0.00"));
        cartPO1.setProductState(0);
        cartPO1.setProductType(0);
        cartPO1.setAreaCode("areaCode");
        cartPO1.setFinanceRuleCode("financeRuleCode");
        cartPO1.setEnabledFlag(0);
//        final CartListVO expectedResult = new CartListVO(new OrderConfirmDTO(Arrays.asList(cartPO)),
//                Arrays.asList(cartPO1));

        // Configure CartModel.refreshCart(...).
        final CartPO cartPO2 = new CartPO();
        cartPO2.setCartId(0);
        cartPO2.setMemberId(0);
        cartPO2.setStoreId(0L);
        cartPO2.setGoodsName("goodsName");
        cartPO2.setProductId(0L);
        cartPO2.setBuyNum(0);
        cartPO2.setProductPrice(new BigDecimal("0.00"));
        cartPO2.setLandingPrice(new BigDecimal("0.00"));
        cartPO2.setTaxPrice(new BigDecimal("0.00"));
        cartPO2.setPromotionId(0);
        cartPO2.setPromotionType(0);
        cartPO2.setPromotionDescription("description");
        cartPO2.setOffPrice(new BigDecimal("0.00"));
        cartPO2.setProductState(0);
        cartPO2.setProductType(0);
        cartPO2.setAreaCode("areaCode");
        cartPO2.setFinanceRuleCode("financeRuleCode");
        cartPO2.setEnabledFlag(0);
        final List<CartPO> cartPOS = Arrays.asList(cartPO2);
        final OrderAddressDTO addressDTO = new OrderAddressDTO();
        addressDTO.setReceiverName("receiverName");
        addressDTO.setReceiverMobile("receiverMobile");
        addressDTO.setProvince("province");
        addressDTO.setCity("city");
        addressDTO.setCityCode("cityCode");
//        when(mockCartModel.refreshCart(0, 0, addressDTO, false)).thenReturn(cartPOS);

        // Configure OrderSubmitAttributesUtils.getProductPriceByProductId(...).
        final ProductPriceVO productPriceVO = new ProductPriceVO();
        final Product product = new Product();
        product.setProductId(0L);
        product.setActivityPrice(new BigDecimal("0.00"));
        product.setProductStock(0);
        product.setState(0);
        product.setBatchNo("batchNo");
        productPriceVO.setProduct(product);
        final ProductExtend productExtend = new ProductExtend();
        productExtend.setIsSelfLift(0);
        productPriceVO.setProductExtend(productExtend);
        final Goods goods = new Goods();
        goods.setState(0);
        goods.setIsVirtualGoods(0);
        goods.setFundsBorrowable(0);
        productPriceVO.setGoods(goods);
        productPriceVO.setEffectiveStock(0);
//        when(mockOrderSubmitAttributesUtils.getProductPriceByProductId(0L, "areaCode", "financeRuleCode"))
//                .thenReturn(productPriceVO);

        // Configure OrderSubmitAttributesUtils.getPromotionList(...).
        final Product product1 = new Product();
        product1.setProductId(0L);
        product1.setActivityPrice(new BigDecimal("0.00"));
        product1.setProductStock(0);
        product1.setState(0);
        product1.setBatchNo("batchNo");
//        when(mockOrderSubmitAttributesUtils.getPromotionList(product1)).thenReturn(Collections.emptyList());

        // Run the test
        final CartListVO result = cartServiceImplUnderTest.getCartList(0, 0);

        // Verify the results
//        assertEquals(expectedResult, result);
    }

    @Test
    public void testCountInCar4t() {
//        assertEquals(Integer.valueOf(0), cartServiceImplUnderTest.countInCart(0, 0));
    }

    @Test
    public void testChangeNum4() {
        // Setup
        final Member member = new Member();
        member.setMemberId(0);
        member.setUserNo("userNo");
        member.setCustNo("custNo");
        member.setCappCustNo("cappCustNo");
        member.setMemberName("memberName");

        // Configure ProductFeignClient.getProductByProductId(...).
        final Product product = new Product();
        product.setProductId(0L);
        product.setActivityPrice(new BigDecimal("0.00"));
        product.setProductStock(0);
        product.setState(0);
        product.setBatchNo("batchNo");
//        when(mockProductFeignClient.getProductByProductId(0L)).thenReturn(product);

        // Run the test
//        final Boolean result = cartServiceImplUnderTest.changeNum(member, 0, 0);

        // Verify the results
//        assertFalse(result);
    }

    @Test
    public void testBuildCartList5() {
        // Setup
        final OrderSkuInfoDTO orderSkuInfoDTO = new OrderSkuInfoDTO();
        orderSkuInfoDTO.setProductId(0L);
        orderSkuInfoDTO.setNumber(0);
        orderSkuInfoDTO.setProductPrice(new BigDecimal("0.00"));
        orderSkuInfoDTO.setTaxPrice(new BigDecimal("0.00"));
        orderSkuInfoDTO.setLandingPrice(new BigDecimal("0.00"));
        orderSkuInfoDTO.setProductDiscountAmount(new BigDecimal("0.00"));
        orderSkuInfoDTO.setStoreId(0L);
        orderSkuInfoDTO.setAreaCode("areaCode");
        orderSkuInfoDTO.setFinanceRuleCode("financeRuleCode");
        final List<OrderSkuInfoDTO> skuInfoList = Arrays.asList(orderSkuInfoDTO);
        final OrderAddressDTO addressDTO = new OrderAddressDTO();
        addressDTO.setReceiverName("receiverName");
        addressDTO.setReceiverMobile("receiverMobile");
        addressDTO.setProvince("province");
        addressDTO.setCity("city");
        addressDTO.setCityCode("cityCode");

        final CartPO cartPO = new CartPO();
        cartPO.setCartId(0);
        cartPO.setMemberId(0);
        cartPO.setStoreId(0L);
        cartPO.setGoodsName("goodsName");
        cartPO.setProductId(0L);
        cartPO.setBuyNum(0);
        cartPO.setProductPrice(new BigDecimal("0.00"));
        cartPO.setLandingPrice(new BigDecimal("0.00"));
        cartPO.setTaxPrice(new BigDecimal("0.00"));
        cartPO.setPromotionId(0);
        cartPO.setPromotionType(0);
        cartPO.setPromotionDescription("description");
        cartPO.setOffPrice(new BigDecimal("0.00"));
        cartPO.setProductState(0);
        cartPO.setProductType(0);
        cartPO.setAreaCode("areaCode");
        cartPO.setFinanceRuleCode("financeRuleCode");
        cartPO.setEnabledFlag(0);
        final List<CartPO> expectedResult = Arrays.asList(cartPO);
//        when(mockOrderSubmitAttributesUtils.getProductBatch(Arrays.asList(0L), "areaCode",
//                "financeRuleCode")).thenReturn(new HashMap<>());

        // Configure CartModel.assembleCartPO(...).
        final CartPO cartPO1 = new CartPO();
        cartPO1.setCartId(0);
        cartPO1.setMemberId(0);
        cartPO1.setStoreId(0L);
        cartPO1.setGoodsName("goodsName");
        cartPO1.setProductId(0L);
        cartPO1.setBuyNum(0);
        cartPO1.setProductPrice(new BigDecimal("0.00"));
        cartPO1.setLandingPrice(new BigDecimal("0.00"));
        cartPO1.setTaxPrice(new BigDecimal("0.00"));
        cartPO1.setPromotionId(0);
        cartPO1.setPromotionType(0);
        cartPO1.setPromotionDescription("description");
        cartPO1.setOffPrice(new BigDecimal("0.00"));
        cartPO1.setProductState(0);
        cartPO1.setProductType(0);
        cartPO1.setAreaCode("areaCode");
        cartPO1.setFinanceRuleCode("financeRuleCode");
        cartPO1.setEnabledFlag(0);
        final ProductPriceVO productPriceVO = new ProductPriceVO();
        final Product product = new Product();
        product.setProductId(0L);
        product.setActivityPrice(new BigDecimal("0.00"));
        product.setProductStock(0);
        product.setState(0);
        product.setBatchNo("batchNo");
        productPriceVO.setProduct(product);
        final ProductExtend productExtend = new ProductExtend();
        productExtend.setIsSelfLift(0);
        productPriceVO.setProductExtend(productExtend);
        final Goods goods = new Goods();
        goods.setState(0);
        goods.setIsVirtualGoods(0);
        goods.setFundsBorrowable(0);
        productPriceVO.setGoods(goods);
        productPriceVO.setEffectiveStock(0);
//        when(mockCartModel.assembleCartPO(0, 0, "areaCode", productPriceVO, false)).thenReturn(cartPO1);

        // Configure OrderSubmitAttributesUtils.getMemberByMemberId(...).
        final Member member = new Member();
        member.setMemberId(0);
        member.setUserNo("userNo");
        member.setCustNo("custNo");
        member.setCappCustNo("cappCustNo");
        member.setMemberName("memberName");
//        when(mockOrderSubmitAttributesUtils.getMemberByMemberId(0)).thenReturn(member);

        // Configure OrderSubmitAttributesUtils.getStoreAreaCode(...).
        final StoreRegionVO storeRegionVO = new StoreRegionVO();
        storeRegionVO.setId(0);
        storeRegionVO.setName("name");
        storeRegionVO.setCode("areaCode");
        storeRegionVO.setSortNum(0);
        storeRegionVO.setBizBranchCode("bizBranchCode");
        final FrontStoreRegionVo frontStoreRegionVo = new FrontStoreRegionVo(0, Arrays.asList(storeRegionVO));
        final OrderAddressDTO addressDTO1 = new OrderAddressDTO();
        addressDTO1.setReceiverName("receiverName");
        addressDTO1.setReceiverMobile("receiverMobile");
        addressDTO1.setProvince("province");
        addressDTO1.setCity("city");
        addressDTO1.setCityCode("cityCode");
        final Member member1 = new Member();
        member1.setMemberId(0);
        member1.setUserNo("userNo");
        member1.setCustNo("custNo");
        member1.setCappCustNo("cappCustNo");
        member1.setMemberName("memberName");
//        when(mockOrderSubmitAttributesUtils.getStoreAreaCode("storeId", addressDTO1, member1))
//                .thenReturn(frontStoreRegionVo);

        // Configure CartModel.getSinglePromotion(...).
        final GoodsPromotion goodsPromotion = new GoodsPromotion();
        goodsPromotion.setGoodsPromotionId(0);
        goodsPromotion.setPromotionId(0);
        goodsPromotion.setStoreId(0L);
        goodsPromotion.setPromotionType(0);
        goodsPromotion.setDescription("description");
//        when(mockCartModel.getSinglePromotion(0L, 0)).thenReturn(goodsPromotion);

        // Configure CartUtils.isCartInvalid(...).
        final CartPO cartPO2 = new CartPO();
        cartPO2.setCartId(0);
        cartPO2.setMemberId(0);
        cartPO2.setStoreId(0L);
        cartPO2.setGoodsName("goodsName");
        cartPO2.setProductId(0L);
        cartPO2.setBuyNum(0);
        cartPO2.setProductPrice(new BigDecimal("0.00"));
        cartPO2.setLandingPrice(new BigDecimal("0.00"));
        cartPO2.setTaxPrice(new BigDecimal("0.00"));
        cartPO2.setPromotionId(0);
        cartPO2.setPromotionType(0);
        cartPO2.setPromotionDescription("description");
        cartPO2.setOffPrice(new BigDecimal("0.00"));
        cartPO2.setProductState(0);
        cartPO2.setProductType(0);
        cartPO2.setAreaCode("areaCode");
        cartPO2.setFinanceRuleCode("financeRuleCode");
        cartPO2.setEnabledFlag(0);
        final ProductPriceVO productPriceByProductId = new ProductPriceVO();
        final Product product1 = new Product();
        product1.setProductId(0L);
        product1.setActivityPrice(new BigDecimal("0.00"));
        product1.setProductStock(0);
        product1.setState(0);
        product1.setBatchNo("batchNo");
        productPriceByProductId.setProduct(product1);
        final ProductExtend productExtend1 = new ProductExtend();
        productExtend1.setIsSelfLift(0);
        productPriceByProductId.setProductExtend(productExtend1);
        final Goods goods1 = new Goods();
        goods1.setState(0);
        goods1.setIsVirtualGoods(0);
        goods1.setFundsBorrowable(0);
        productPriceByProductId.setGoods(goods1);
        productPriceByProductId.setEffectiveStock(0);
//        when(mockCartUtils.isCartInvalid(cartPO2, productPriceByProductId)).thenReturn(false);

        // Run the test
//        final List<CartPO> result = cartServiceImplUnderTest.buildCartList(skuInfoList, OrderTypeEnum.NORMAL, 0,
//                "areaCode", "financeRuleCode", "channel", addressDTO);

        // Verify the results
//        assertEquals(expectedResult, result);
//        verify(mockOrderProductHelper).getSpellGroupPrice(0, 0L, 0L);
//        verify(mockOrderProductHelper).getLadderGroupPrice(0, 0L);
//        verify(mockOrderProductHelper).getPreSellPrice(0, 0L);
    }

    @Test
    public void testBuildCartListV2() {
        // Setup
        final OrderSkuInfoDTO orderSkuInfoDTO = new OrderSkuInfoDTO();
        orderSkuInfoDTO.setProductId(0L);
        orderSkuInfoDTO.setNumber(0);
        orderSkuInfoDTO.setProductPrice(new BigDecimal("0.00"));
        orderSkuInfoDTO.setTaxPrice(new BigDecimal("0.00"));
        orderSkuInfoDTO.setLandingPrice(new BigDecimal("0.00"));
        orderSkuInfoDTO.setProductDiscountAmount(new BigDecimal("0.00"));
        orderSkuInfoDTO.setStoreId(0L);
        orderSkuInfoDTO.setAreaCode("areaCode");
        orderSkuInfoDTO.setFinanceRuleCode("financeRuleCode");
        final List<OrderSkuInfoDTO> skuInfoList = Arrays.asList(orderSkuInfoDTO);
        final OrderAddressDTO addressDTO = new OrderAddressDTO();
        addressDTO.setReceiverName("receiverName");
        addressDTO.setReceiverMobile("receiverMobile");
        addressDTO.setProvince("province");
        addressDTO.setCity("city");
        addressDTO.setCityCode("cityCode");

        final CartPO cartPO = new CartPO();
        cartPO.setCartId(0);
        cartPO.setMemberId(0);
        cartPO.setStoreId(0L);
        cartPO.setGoodsName("goodsName");
        cartPO.setProductId(0L);
        cartPO.setBuyNum(0);
        cartPO.setProductPrice(new BigDecimal("0.00"));
        cartPO.setLandingPrice(new BigDecimal("0.00"));
        cartPO.setTaxPrice(new BigDecimal("0.00"));
        cartPO.setPromotionId(0);
        cartPO.setPromotionType(0);
        cartPO.setPromotionDescription("description");
        cartPO.setOffPrice(new BigDecimal("0.00"));
        cartPO.setProductState(0);
        cartPO.setProductType(0);
        cartPO.setAreaCode("areaCode");
        cartPO.setFinanceRuleCode("financeRuleCode");
        cartPO.setEnabledFlag(0);
        final List<CartPO> expectedResult = Arrays.asList(cartPO);

        // Configure OrderCreateHelper.getStoreAreaCodeByAddress(...).
        final OrderAddressDTO addressDTO1 = new OrderAddressDTO();
        addressDTO1.setReceiverName("receiverName");
        addressDTO1.setReceiverMobile("receiverMobile");
        addressDTO1.setProvince("province");
        addressDTO1.setCity("city");
        addressDTO1.setCityCode("cityCode");
//        when(mockOrderCreateHelper.getStoreAreaCodeByAddress("storeId", addressDTO1, 0)).thenReturn("areaCode");

        // Configure OrderSubmitAttributesUtils.getProductBatch(...).
        final CartPO cartPO1 = new CartPO();
        cartPO1.setCartId(0);
        cartPO1.setMemberId(0);
        cartPO1.setStoreId(0L);
        cartPO1.setGoodsName("goodsName");
        cartPO1.setProductId(0L);
        cartPO1.setBuyNum(0);
        cartPO1.setProductPrice(new BigDecimal("0.00"));
        cartPO1.setLandingPrice(new BigDecimal("0.00"));
        cartPO1.setTaxPrice(new BigDecimal("0.00"));
        cartPO1.setPromotionId(0);
        cartPO1.setPromotionType(0);
        cartPO1.setPromotionDescription("description");
        cartPO1.setOffPrice(new BigDecimal("0.00"));
        cartPO1.setProductState(0);
        cartPO1.setProductType(0);
        cartPO1.setAreaCode("areaCode");
        cartPO1.setFinanceRuleCode("financeRuleCode");
        cartPO1.setEnabledFlag(0);
        final List<CartPO> cartPOList = Arrays.asList(cartPO1);
//        when(mockOrderSubmitAttributesUtils.getProductBatch(cartPOList)).thenReturn(new HashMap<>());

        // Configure CartModel.assembleCartPO(...).
        final CartPO cartPO2 = new CartPO();
        cartPO2.setCartId(0);
        cartPO2.setMemberId(0);
        cartPO2.setStoreId(0L);
        cartPO2.setGoodsName("goodsName");
        cartPO2.setProductId(0L);
        cartPO2.setBuyNum(0);
        cartPO2.setProductPrice(new BigDecimal("0.00"));
        cartPO2.setLandingPrice(new BigDecimal("0.00"));
        cartPO2.setTaxPrice(new BigDecimal("0.00"));
        cartPO2.setPromotionId(0);
        cartPO2.setPromotionType(0);
        cartPO2.setPromotionDescription("description");
        cartPO2.setOffPrice(new BigDecimal("0.00"));
        cartPO2.setProductState(0);
        cartPO2.setProductType(0);
        cartPO2.setAreaCode("areaCode");
        cartPO2.setFinanceRuleCode("financeRuleCode");
        cartPO2.setEnabledFlag(0);
        final ProductPriceVO productPriceVO = new ProductPriceVO();
        final Product product = new Product();
        product.setProductId(0L);
        product.setActivityPrice(new BigDecimal("0.00"));
        product.setProductStock(0);
        product.setState(0);
        product.setBatchNo("batchNo");
        productPriceVO.setProduct(product);
        final ProductExtend productExtend = new ProductExtend();
        productExtend.setIsSelfLift(0);
        productPriceVO.setProductExtend(productExtend);
        final Goods goods = new Goods();
        goods.setState(0);
        goods.setIsVirtualGoods(0);
        goods.setFundsBorrowable(0);
        productPriceVO.setGoods(goods);
        productPriceVO.setEffectiveStock(0);
//        when(mockCartModel.assembleCartPO(0, 0, "areaCode", productPriceVO, true)).thenReturn(cartPO2);

        // Configure CartUtils.isCartInvalid(...).
        final CartPO cartPO3 = new CartPO();
        cartPO3.setCartId(0);
        cartPO3.setMemberId(0);
        cartPO3.setStoreId(0L);
        cartPO3.setGoodsName("goodsName");
        cartPO3.setProductId(0L);
        cartPO3.setBuyNum(0);
        cartPO3.setProductPrice(new BigDecimal("0.00"));
        cartPO3.setLandingPrice(new BigDecimal("0.00"));
        cartPO3.setTaxPrice(new BigDecimal("0.00"));
        cartPO3.setPromotionId(0);
        cartPO3.setPromotionType(0);
        cartPO3.setPromotionDescription("description");
        cartPO3.setOffPrice(new BigDecimal("0.00"));
        cartPO3.setProductState(0);
        cartPO3.setProductType(0);
        cartPO3.setAreaCode("areaCode");
        cartPO3.setFinanceRuleCode("financeRuleCode");
        cartPO3.setEnabledFlag(0);
        final ProductPriceVO productPriceByProductId = new ProductPriceVO();
        final Product product1 = new Product();
        product1.setProductId(0L);
        product1.setActivityPrice(new BigDecimal("0.00"));
        product1.setProductStock(0);
        product1.setState(0);
        product1.setBatchNo("batchNo");
        productPriceByProductId.setProduct(product1);
        final ProductExtend productExtend1 = new ProductExtend();
        productExtend1.setIsSelfLift(0);
        productPriceByProductId.setProductExtend(productExtend1);
        final Goods goods1 = new Goods();
        goods1.setState(0);
        goods1.setIsVirtualGoods(0);
        goods1.setFundsBorrowable(0);
        productPriceByProductId.setGoods(goods1);
        productPriceByProductId.setEffectiveStock(0);
//        when(mockCartUtils.isCartInvalid(cartPO3, productPriceByProductId)).thenReturn(false);

        // Run the test
//        final List<CartPO> result = cartServiceImplUnderTest.buildCartListV2(skuInfoList, 0, OrderTypeEnum.NORMAL,
//                "channel", addressDTO, false);

        // Verify the results
//        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetCartByProduct() {
        // Setup
        final CartPO expectedResult = new CartPO();
        expectedResult.setCartId(0);
        expectedResult.setMemberId(0);
        expectedResult.setStoreId(0L);
        expectedResult.setGoodsName("goodsName");
        expectedResult.setProductId(0L);
        expectedResult.setBuyNum(0);
        expectedResult.setProductPrice(new BigDecimal("0.00"));
        expectedResult.setLandingPrice(new BigDecimal("0.00"));
        expectedResult.setTaxPrice(new BigDecimal("0.00"));
        expectedResult.setPromotionId(0);
        expectedResult.setPromotionType(0);
        expectedResult.setPromotionDescription("description");
        expectedResult.setOffPrice(new BigDecimal("0.00"));
        expectedResult.setProductState(0);
        expectedResult.setProductType(0);
        expectedResult.setAreaCode("areaCode");
        expectedResult.setFinanceRuleCode("financeRuleCode");
        expectedResult.setEnabledFlag(0);

        // Run the test
//        final CartPO result = cartServiceImplUnderTest.getCartByProduct(0L, 0);

        // Verify the results
//        assertEquals(expectedResult, result);
    }

    @Test
    public void testUpdateCartsGoodsName() {
//        assertFalse(cartServiceImplUnderTest.updateCartsGoodsName("cartIds", "goodsName"));
    }

    @Test
    public void testAddCart5() {
        // Setup
        final Member member = new Member();
        member.setMemberId(0);
        member.setUserNo("userNo");
        member.setCustNo("custNo");
        member.setCappCustNo("cappCustNo");
        member.setMemberName("memberName");

        // Configure OrderSubmitAttributesUtils.getProductPriceByProductId(...).
        final ProductPriceVO productPriceVO = new ProductPriceVO();
        final Product product = new Product();
        product.setProductId(0L);
        product.setActivityPrice(new BigDecimal("0.00"));
        product.setProductStock(0);
        product.setState(0);
        product.setBatchNo("batchNo");
        productPriceVO.setProduct(product);
        final ProductExtend productExtend = new ProductExtend();
        productExtend.setIsSelfLift(0);
        productPriceVO.setProductExtend(productExtend);
        final Goods goods = new Goods();
        goods.setState(0);
        goods.setIsVirtualGoods(0);
        goods.setFundsBorrowable(0);
        productPriceVO.setGoods(goods);
        productPriceVO.setEffectiveStock(0);
//        when(mockOrderSubmitAttributesUtils.getProductPriceByProductId(0L, "areaCode", "financeRuleCode"))
//                .thenReturn(productPriceVO);

        // Run the test
//        final Boolean result = cartServiceImplUnderTest.addCart(member, 0L, "areaCode", 0, "financeRuleCode");

        // Verify the results
//        assertFalse(result);
//        verify(mockDistributeLock).lockAndProcess(eq("lockName"), eq(0L), eq(30L), eq(TimeUnit.SECONDS),
//                any(Supplier.class));

        // Confirm CartModel.addToCart(...).
        final ProductPriceVO productPriceVO1 = new ProductPriceVO();
        final Product product1 = new Product();
        product1.setProductId(0L);
        product1.setActivityPrice(new BigDecimal("0.00"));
        product1.setProductStock(0);
        product1.setState(0);
        product1.setBatchNo("batchNo");
        productPriceVO1.setProduct(product1);
        final ProductExtend productExtend1 = new ProductExtend();
        productExtend1.setIsSelfLift(0);
        productPriceVO1.setProductExtend(productExtend1);
        final Goods goods1 = new Goods();
        goods1.setState(0);
        goods1.setIsVirtualGoods(0);
        goods1.setFundsBorrowable(0);
        productPriceVO1.setGoods(goods1);
        productPriceVO1.setEffectiveStock(0);
//        verify(mockCartModel).addToCart(0, 0, "areaCode", productPriceVO1);

        // Confirm CartModel.mutuallyExclusiveCartAction(...).
        final ProductPriceVO productPriceVO2 = new ProductPriceVO();
        final Product product2 = new Product();
        product2.setProductId(0L);
        product2.setActivityPrice(new BigDecimal("0.00"));
        product2.setProductStock(0);
        product2.setState(0);
        product2.setBatchNo("batchNo");
        productPriceVO2.setProduct(product2);
        final ProductExtend productExtend2 = new ProductExtend();
        productExtend2.setIsSelfLift(0);
        productPriceVO2.setProductExtend(productExtend2);
        final Goods goods2 = new Goods();
        goods2.setState(0);
        goods2.setIsVirtualGoods(0);
        goods2.setFundsBorrowable(0);
        productPriceVO2.setGoods(goods2);
        productPriceVO2.setEffectiveStock(0);
//        verify(mockCartModel).mutuallyExclusiveCartAction(0, productPriceVO2);
    }

    @Test
    public void testGetCartLis6t() {
        // Setup
        final CartPO cartPO = new CartPO();
        cartPO.setCartId(0);
        cartPO.setMemberId(0);
        cartPO.setStoreId(0L);
        cartPO.setGoodsName("goodsName");
        cartPO.setProductId(0L);
        cartPO.setBuyNum(0);
        cartPO.setProductPrice(new BigDecimal("0.00"));
        cartPO.setLandingPrice(new BigDecimal("0.00"));
        cartPO.setTaxPrice(new BigDecimal("0.00"));
        cartPO.setPromotionId(0);
        cartPO.setPromotionType(0);
        cartPO.setPromotionDescription("description");
        cartPO.setOffPrice(new BigDecimal("0.00"));
        cartPO.setProductState(0);
        cartPO.setProductType(0);
        cartPO.setAreaCode("areaCode");
        cartPO.setFinanceRuleCode("financeRuleCode");
        cartPO.setEnabledFlag(0);
        final CartPO cartPO1 = new CartPO();
        cartPO1.setCartId(0);
        cartPO1.setMemberId(0);
        cartPO1.setStoreId(0L);
        cartPO1.setGoodsName("goodsName");
        cartPO1.setProductId(0L);
        cartPO1.setBuyNum(0);
        cartPO1.setProductPrice(new BigDecimal("0.00"));
        cartPO1.setLandingPrice(new BigDecimal("0.00"));
        cartPO1.setTaxPrice(new BigDecimal("0.00"));
        cartPO1.setPromotionId(0);
        cartPO1.setPromotionType(0);
        cartPO1.setPromotionDescription("description");
        cartPO1.setOffPrice(new BigDecimal("0.00"));
        cartPO1.setProductState(0);
        cartPO1.setProductType(0);
        cartPO1.setAreaCode("areaCode");
        cartPO1.setFinanceRuleCode("financeRuleCode");
        cartPO1.setEnabledFlag(0);
//        final CartListVO expectedResult = new CartListVO(new OrderConfirmDTO(Arrays.asList(cartPO)),
//                Arrays.asList(cartPO1));

        // Configure CartModel.refreshCart(...).
        final CartPO cartPO2 = new CartPO();
        cartPO2.setCartId(0);
        cartPO2.setMemberId(0);
        cartPO2.setStoreId(0L);
        cartPO2.setGoodsName("goodsName");
        cartPO2.setProductId(0L);
        cartPO2.setBuyNum(0);
        cartPO2.setProductPrice(new BigDecimal("0.00"));
        cartPO2.setLandingPrice(new BigDecimal("0.00"));
        cartPO2.setTaxPrice(new BigDecimal("0.00"));
        cartPO2.setPromotionId(0);
        cartPO2.setPromotionType(0);
        cartPO2.setPromotionDescription("description");
        cartPO2.setOffPrice(new BigDecimal("0.00"));
        cartPO2.setProductState(0);
        cartPO2.setProductType(0);
        cartPO2.setAreaCode("areaCode");
        cartPO2.setFinanceRuleCode("financeRuleCode");
        cartPO2.setEnabledFlag(0);
        final List<CartPO> cartPOS = Arrays.asList(cartPO2);
        final OrderAddressDTO addressDTO = new OrderAddressDTO();
        addressDTO.setReceiverName("receiverName");
        addressDTO.setReceiverMobile("receiverMobile");
        addressDTO.setProvince("province");
        addressDTO.setCity("city");
        addressDTO.setCityCode("cityCode");
//        when(mockCartModel.refreshCart(0, 0, addressDTO, false)).thenReturn(cartPOS);

        // Configure OrderSubmitAttributesUtils.getProductPriceByProductId(...).
        final ProductPriceVO productPriceVO = new ProductPriceVO();
        final Product product = new Product();
        product.setProductId(0L);
        product.setActivityPrice(new BigDecimal("0.00"));
        product.setProductStock(0);
        product.setState(0);
        product.setBatchNo("batchNo");
        productPriceVO.setProduct(product);
        final ProductExtend productExtend = new ProductExtend();
        productExtend.setIsSelfLift(0);
        productPriceVO.setProductExtend(productExtend);
        final Goods goods = new Goods();
        goods.setState(0);
        goods.setIsVirtualGoods(0);
        goods.setFundsBorrowable(0);
        productPriceVO.setGoods(goods);
        productPriceVO.setEffectiveStock(0);
//        when(mockOrderSubmitAttributesUtils.getProductPriceByProductId(0L, "areaCode", "financeRuleCode"))
//                .thenReturn(productPriceVO);

        // Configure OrderSubmitAttributesUtils.getPromotionList(...).
        final GoodsPromotion goodsPromotion = new GoodsPromotion();
        goodsPromotion.setGoodsPromotionId(0);
        goodsPromotion.setPromotionId(0);
        goodsPromotion.setStoreId(0L);
        goodsPromotion.setPromotionType(0);
        goodsPromotion.setDescription("description");
        final List<GoodsPromotion> goodsPromotions = Arrays.asList(goodsPromotion);
        final Product product1 = new Product();
        product1.setProductId(0L);
        product1.setActivityPrice(new BigDecimal("0.00"));
        product1.setProductStock(0);
        product1.setState(0);
        product1.setBatchNo("batchNo");
//        when(mockOrderSubmitAttributesUtils.getPromotionList(product1)).thenReturn(goodsPromotions);

        // Run the test
        final CartListVO result = cartServiceImplUnderTest.getCartList(0, 0);

        // Verify the results
//        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetCartList_Ca2rtModelReturnsNoItems() {
        // Setup
        final CartPO cartPO = new CartPO();
        cartPO.setCartId(0);
        cartPO.setMemberId(0);
        cartPO.setStoreId(0L);
        cartPO.setGoodsName("goodsName");
        cartPO.setProductId(0L);
        cartPO.setBuyNum(0);
        cartPO.setProductPrice(new BigDecimal("0.00"));
        cartPO.setLandingPrice(new BigDecimal("0.00"));
        cartPO.setTaxPrice(new BigDecimal("0.00"));
        cartPO.setPromotionId(0);
        cartPO.setPromotionType(0);
        cartPO.setPromotionDescription("description");
        cartPO.setOffPrice(new BigDecimal("0.00"));
        cartPO.setProductState(0);
        cartPO.setProductType(0);
        cartPO.setAreaCode("areaCode");
        cartPO.setFinanceRuleCode("financeRuleCode");
        cartPO.setEnabledFlag(0);
        final CartPO cartPO1 = new CartPO();
        cartPO1.setCartId(0);
        cartPO1.setMemberId(0);
        cartPO1.setStoreId(0L);
        cartPO1.setGoodsName("goodsName");
        cartPO1.setProductId(0L);
        cartPO1.setBuyNum(0);
        cartPO1.setProductPrice(new BigDecimal("0.00"));
        cartPO1.setLandingPrice(new BigDecimal("0.00"));
        cartPO1.setTaxPrice(new BigDecimal("0.00"));
        cartPO1.setPromotionId(0);
        cartPO1.setPromotionType(0);
        cartPO1.setPromotionDescription("description");
        cartPO1.setOffPrice(new BigDecimal("0.00"));
        cartPO1.setProductState(0);
        cartPO1.setProductType(0);
        cartPO1.setAreaCode("areaCode");
        cartPO1.setFinanceRuleCode("financeRuleCode");
        cartPO1.setEnabledFlag(0);
//        final CartListVO expectedResult = new CartListVO(new OrderConfirmDTO(Arrays.asList(cartPO)),
//                Arrays.asList(cartPO1));

        // Configure CartModel.refreshCart(...).
        final OrderAddressDTO addressDTO = new OrderAddressDTO();
        addressDTO.setReceiverName("receiverName");
        addressDTO.setReceiverMobile("receiverMobile");
        addressDTO.setProvince("province");
        addressDTO.setCity("city");
        addressDTO.setCityCode("cityCode");
//        when(mockCartModel.refreshCart(0, 0, addressDTO, false)).thenReturn(Collections.emptyList());

        // Run the test
        final CartListVO result = cartServiceImplUnderTest.getCartList(0, 0);

        // Verify the results
//        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetCartList_OrderSubmitAttributesUtilsGetPr2omotionListReturnsNoItems() {
        // Setup
        final CartPO cartPO = new CartPO();
        cartPO.setCartId(0);
        cartPO.setMemberId(0);
        cartPO.setStoreId(0L);
        cartPO.setGoodsName("goodsName");
        cartPO.setProductId(0L);
        cartPO.setBuyNum(0);
        cartPO.setProductPrice(new BigDecimal("0.00"));
        cartPO.setLandingPrice(new BigDecimal("0.00"));
        cartPO.setTaxPrice(new BigDecimal("0.00"));
        cartPO.setPromotionId(0);
        cartPO.setPromotionType(0);
        cartPO.setPromotionDescription("description");
        cartPO.setOffPrice(new BigDecimal("0.00"));
        cartPO.setProductState(0);
        cartPO.setProductType(0);
        cartPO.setAreaCode("areaCode");
        cartPO.setFinanceRuleCode("financeRuleCode");
        cartPO.setEnabledFlag(0);
        final CartPO cartPO1 = new CartPO();
        cartPO1.setCartId(0);
        cartPO1.setMemberId(0);
        cartPO1.setStoreId(0L);
        cartPO1.setGoodsName("goodsName");
        cartPO1.setProductId(0L);
        cartPO1.setBuyNum(0);
        cartPO1.setProductPrice(new BigDecimal("0.00"));
        cartPO1.setLandingPrice(new BigDecimal("0.00"));
        cartPO1.setTaxPrice(new BigDecimal("0.00"));
        cartPO1.setPromotionId(0);
        cartPO1.setPromotionType(0);
        cartPO1.setPromotionDescription("description");
        cartPO1.setOffPrice(new BigDecimal("0.00"));
        cartPO1.setProductState(0);
        cartPO1.setProductType(0);
        cartPO1.setAreaCode("areaCode");
        cartPO1.setFinanceRuleCode("financeRuleCode");
        cartPO1.setEnabledFlag(0);
//        final CartListVO expectedResult = new CartListVO(new OrderConfirmDTO(Arrays.asList(cartPO)),
//                Arrays.asList(cartPO1));

        // Configure CartModel.refreshCart(...).
        final CartPO cartPO2 = new CartPO();
        cartPO2.setCartId(0);
        cartPO2.setMemberId(0);
        cartPO2.setStoreId(0L);
        cartPO2.setGoodsName("goodsName");
        cartPO2.setProductId(0L);
        cartPO2.setBuyNum(0);
        cartPO2.setProductPrice(new BigDecimal("0.00"));
        cartPO2.setLandingPrice(new BigDecimal("0.00"));
        cartPO2.setTaxPrice(new BigDecimal("0.00"));
        cartPO2.setPromotionId(0);
        cartPO2.setPromotionType(0);
        cartPO2.setPromotionDescription("description");
        cartPO2.setOffPrice(new BigDecimal("0.00"));
        cartPO2.setProductState(0);
        cartPO2.setProductType(0);
        cartPO2.setAreaCode("areaCode");
        cartPO2.setFinanceRuleCode("financeRuleCode");
        cartPO2.setEnabledFlag(0);
        final List<CartPO> cartPOS = Arrays.asList(cartPO2);
        final OrderAddressDTO addressDTO = new OrderAddressDTO();
        addressDTO.setReceiverName("receiverName");
        addressDTO.setReceiverMobile("receiverMobile");
        addressDTO.setProvince("province");
        addressDTO.setCity("city");
        addressDTO.setCityCode("cityCode");
//        when(mockCartModel.refreshCart(0, 0, addressDTO, false)).thenReturn(cartPOS);

        // Configure OrderSubmitAttributesUtils.getProductPriceByProductId(...).
        final ProductPriceVO productPriceVO = new ProductPriceVO();
        final Product product = new Product();
        product.setProductId(0L);
        product.setActivityPrice(new BigDecimal("0.00"));
        product.setProductStock(0);
        product.setState(0);
        product.setBatchNo("batchNo");
        productPriceVO.setProduct(product);
        final ProductExtend productExtend = new ProductExtend();
        productExtend.setIsSelfLift(0);
        productPriceVO.setProductExtend(productExtend);
        final Goods goods = new Goods();
        goods.setState(0);
        goods.setIsVirtualGoods(0);
        goods.setFundsBorrowable(0);
        productPriceVO.setGoods(goods);
        productPriceVO.setEffectiveStock(0);
//        when(mockOrderSubmitAttributesUtils.getProductPriceByProductId(0L, "areaCode", "financeRuleCode"))
//                .thenReturn(productPriceVO);

        // Configure OrderSubmitAttributesUtils.getPromotionList(...).
        final Product product1 = new Product();
        product1.setProductId(0L);
        product1.setActivityPrice(new BigDecimal("0.00"));
        product1.setProductStock(0);
        product1.setState(0);
        product1.setBatchNo("batchNo");
//        when(mockOrderSubmitAttributesUtils.getPromotionList(product1)).thenReturn(Collections.emptyList());

        // Run the test
        final CartListVO result = cartServiceImplUnderTest.getCartList(0, 0);

        // Verify the results
//        assertEquals(expectedResult, result);
    }

    @Test
    public void testCountInCa3rt() {
//        assertEquals(Integer.valueOf(0), cartServiceImplUnderTest.countInCart(0, 0));
    }

    @Test
    public void testChan4geNum() {
        // Setup
        final Member member = new Member();
        member.setMemberId(0);
        member.setUserNo("userNo");
        member.setCustNo("custNo");
        member.setCappCustNo("cappCustNo");
        member.setMemberName("memberName");

        // Configure ProductFeignClient.getProductByProductId(...).
        final Product product = new Product();
        product.setProductId(0L);
        product.setActivityPrice(new BigDecimal("0.00"));
        product.setProductStock(0);
        product.setState(0);
        product.setBatchNo("batchNo");
//        when(mockProductFeignClient.getProductByProductId(0L)).thenReturn(product);

        // Run the test
//        final Boolean result = cartServiceImplUnderTest.changeNum(member, 0, 0);

        // Verify the results
//        assertFalse(result);
    }

    @Test
    public void testBuildCar4tList() {
        // Setup
        final OrderSkuInfoDTO orderSkuInfoDTO = new OrderSkuInfoDTO();
        orderSkuInfoDTO.setProductId(0L);
        orderSkuInfoDTO.setNumber(0);
        orderSkuInfoDTO.setProductPrice(new BigDecimal("0.00"));
        orderSkuInfoDTO.setTaxPrice(new BigDecimal("0.00"));
        orderSkuInfoDTO.setLandingPrice(new BigDecimal("0.00"));
        orderSkuInfoDTO.setProductDiscountAmount(new BigDecimal("0.00"));
        orderSkuInfoDTO.setStoreId(0L);
        orderSkuInfoDTO.setAreaCode("areaCode");
        orderSkuInfoDTO.setFinanceRuleCode("financeRuleCode");
        final List<OrderSkuInfoDTO> skuInfoList = Arrays.asList(orderSkuInfoDTO);
        final OrderAddressDTO addressDTO = new OrderAddressDTO();
        addressDTO.setReceiverName("receiverName");
        addressDTO.setReceiverMobile("receiverMobile");
        addressDTO.setProvince("province");
        addressDTO.setCity("city");
        addressDTO.setCityCode("cityCode");

        final CartPO cartPO = new CartPO();
        cartPO.setCartId(0);
        cartPO.setMemberId(0);
        cartPO.setStoreId(0L);
        cartPO.setGoodsName("goodsName");
        cartPO.setProductId(0L);
        cartPO.setBuyNum(0);
        cartPO.setProductPrice(new BigDecimal("0.00"));
        cartPO.setLandingPrice(new BigDecimal("0.00"));
        cartPO.setTaxPrice(new BigDecimal("0.00"));
        cartPO.setPromotionId(0);
        cartPO.setPromotionType(0);
        cartPO.setPromotionDescription("description");
        cartPO.setOffPrice(new BigDecimal("0.00"));
        cartPO.setProductState(0);
        cartPO.setProductType(0);
        cartPO.setAreaCode("areaCode");
        cartPO.setFinanceRuleCode("financeRuleCode");
        cartPO.setEnabledFlag(0);
        final List<CartPO> expectedResult = Arrays.asList(cartPO);
//        when(mockOrderSubmitAttributesUtils.getProductBatch(Arrays.asList(0L), "areaCode",
//                "financeRuleCode")).thenReturn(new HashMap<>());

        // Configure CartModel.assembleCartPO(...).
        final CartPO cartPO1 = new CartPO();
        cartPO1.setCartId(0);
        cartPO1.setMemberId(0);
        cartPO1.setStoreId(0L);
        cartPO1.setGoodsName("goodsName");
        cartPO1.setProductId(0L);
        cartPO1.setBuyNum(0);
        cartPO1.setProductPrice(new BigDecimal("0.00"));
        cartPO1.setLandingPrice(new BigDecimal("0.00"));
        cartPO1.setTaxPrice(new BigDecimal("0.00"));
        cartPO1.setPromotionId(0);
        cartPO1.setPromotionType(0);
        cartPO1.setPromotionDescription("description");
        cartPO1.setOffPrice(new BigDecimal("0.00"));
        cartPO1.setProductState(0);
        cartPO1.setProductType(0);
        cartPO1.setAreaCode("areaCode");
        cartPO1.setFinanceRuleCode("financeRuleCode");
        cartPO1.setEnabledFlag(0);
        final ProductPriceVO productPriceVO = new ProductPriceVO();
        final Product product = new Product();
        product.setProductId(0L);
        product.setActivityPrice(new BigDecimal("0.00"));
        product.setProductStock(0);
        product.setState(0);
        product.setBatchNo("batchNo");
        productPriceVO.setProduct(product);
        final ProductExtend productExtend = new ProductExtend();
        productExtend.setIsSelfLift(0);
        productPriceVO.setProductExtend(productExtend);
        final Goods goods = new Goods();
        goods.setState(0);
        goods.setIsVirtualGoods(0);
        goods.setFundsBorrowable(0);
        productPriceVO.setGoods(goods);
        productPriceVO.setEffectiveStock(0);
//        when(mockCartModel.assembleCartPO(0, 0, "areaCode", productPriceVO, false)).thenReturn(cartPO1);

        // Configure OrderSubmitAttributesUtils.getMemberByMemberId(...).
        final Member member = new Member();
        member.setMemberId(0);
        member.setUserNo("userNo");
        member.setCustNo("custNo");
        member.setCappCustNo("cappCustNo");
        member.setMemberName("memberName");
//        when(mockOrderSubmitAttributesUtils.getMemberByMemberId(0)).thenReturn(member);

        // Configure OrderSubmitAttributesUtils.getStoreAreaCode(...).
        final StoreRegionVO storeRegionVO = new StoreRegionVO();
        storeRegionVO.setId(0);
        storeRegionVO.setName("name");
        storeRegionVO.setCode("areaCode");
        storeRegionVO.setSortNum(0);
        storeRegionVO.setBizBranchCode("bizBranchCode");
        final FrontStoreRegionVo frontStoreRegionVo = new FrontStoreRegionVo(0, Arrays.asList(storeRegionVO));
        final OrderAddressDTO addressDTO1 = new OrderAddressDTO();
        addressDTO1.setReceiverName("receiverName");
        addressDTO1.setReceiverMobile("receiverMobile");
        addressDTO1.setProvince("province");
        addressDTO1.setCity("city");
        addressDTO1.setCityCode("cityCode");
        final Member member1 = new Member();
        member1.setMemberId(0);
        member1.setUserNo("userNo");
        member1.setCustNo("custNo");
        member1.setCappCustNo("cappCustNo");
        member1.setMemberName("memberName");
//        when(mockOrderSubmitAttributesUtils.getStoreAreaCode("storeId", addressDTO1, member1))
//                .thenReturn(frontStoreRegionVo);

        // Configure CartModel.getSinglePromotion(...).
        final GoodsPromotion goodsPromotion = new GoodsPromotion();
        goodsPromotion.setGoodsPromotionId(0);
        goodsPromotion.setPromotionId(0);
        goodsPromotion.setStoreId(0L);
        goodsPromotion.setPromotionType(0);
        goodsPromotion.setDescription("description");
//        when(mockCartModel.getSinglePromotion(0L, 0)).thenReturn(goodsPromotion);

        // Configure CartUtils.isCartInvalid(...).
        final CartPO cartPO2 = new CartPO();
        cartPO2.setCartId(0);
        cartPO2.setMemberId(0);
        cartPO2.setStoreId(0L);
        cartPO2.setGoodsName("goodsName");
        cartPO2.setProductId(0L);
        cartPO2.setBuyNum(0);
        cartPO2.setProductPrice(new BigDecimal("0.00"));
        cartPO2.setLandingPrice(new BigDecimal("0.00"));
        cartPO2.setTaxPrice(new BigDecimal("0.00"));
        cartPO2.setPromotionId(0);
        cartPO2.setPromotionType(0);
        cartPO2.setPromotionDescription("description");
        cartPO2.setOffPrice(new BigDecimal("0.00"));
        cartPO2.setProductState(0);
        cartPO2.setProductType(0);
        cartPO2.setAreaCode("areaCode");
        cartPO2.setFinanceRuleCode("financeRuleCode");
        cartPO2.setEnabledFlag(0);
        final ProductPriceVO productPriceByProductId = new ProductPriceVO();
        final Product product1 = new Product();
        product1.setProductId(0L);
        product1.setActivityPrice(new BigDecimal("0.00"));
        product1.setProductStock(0);
        product1.setState(0);
        product1.setBatchNo("batchNo");
        productPriceByProductId.setProduct(product1);
        final ProductExtend productExtend1 = new ProductExtend();
        productExtend1.setIsSelfLift(0);
        productPriceByProductId.setProductExtend(productExtend1);
        final Goods goods1 = new Goods();
        goods1.setState(0);
        goods1.setIsVirtualGoods(0);
        goods1.setFundsBorrowable(0);
        productPriceByProductId.setGoods(goods1);
        productPriceByProductId.setEffectiveStock(0);
//        when(mockCartUtils.isCartInvalid(cartPO2, productPriceByProductId)).thenReturn(false);

        // Run the test
//        final List<CartPO> result = cartServiceImplUnderTest.buildCartList(skuInfoList, OrderTypeEnum.NORMAL, 0,
//                "areaCode", "financeRuleCode", "channel", addressDTO);

        // Verify the results
//        assertEquals(expectedResult, result);
//        verify(mockOrderProductHelper).getSpellGroupPrice(0, 0L, 0L);
//        verify(mockOrderProductHelper).getLadderGroupPrice(0, 0L);
//        verify(mockOrderProductHelper).getPreSellPrice(0, 0L);
    }

    @Test
    public void testAddCa6rt() {
        // Setup
        final Member member = new Member();
        member.setMemberId(0);
        member.setUserNo("userNo");
        member.setCustNo("custNo");
        member.setCappCustNo("cappCustNo");
        member.setMemberName("memberName");

        // Configure OrderSubmitAttributesUtils.getProductPriceByProductId(...).
        final ProductPriceVO productPriceVO = new ProductPriceVO();
        final Product product = new Product();
        product.setProductId(0L);
        product.setActivityPrice(new BigDecimal("0.00"));
        product.setProductStock(0);
        product.setState(0);
        product.setBatchNo("batchNo");
        productPriceVO.setProduct(product);
        final ProductExtend productExtend = new ProductExtend();
        productExtend.setIsSelfLift(0);
        productPriceVO.setProductExtend(productExtend);
        final Goods goods = new Goods();
        goods.setState(0);
        goods.setIsVirtualGoods(0);
        goods.setFundsBorrowable(0);
        productPriceVO.setGoods(goods);
        productPriceVO.setEffectiveStock(0);
//        when(mockOrderSubmitAttributesUtils.getProductPriceByProductId(0L, "areaCode", "financeRuleCode"))
//                .thenReturn(productPriceVO);

        // Run the test
//        final Boolean result = cartServiceImplUnderTest.addCart(member, 0L, "areaCode", 0, "financeRuleCode");

        // Verify the results
//        assertFalse(result);
//        verify(mockDistributeLock).lockAndProcess(eq("lockName"), eq(0L), eq(30L), eq(TimeUnit.SECONDS),
//                any(Supplier.class));

        // Confirm CartModel.addToCart(...).
        final ProductPriceVO productPriceVO1 = new ProductPriceVO();
        final Product product1 = new Product();
        product1.setProductId(0L);
        product1.setActivityPrice(new BigDecimal("0.00"));
        product1.setProductStock(0);
        product1.setState(0);
        product1.setBatchNo("batchNo");
        productPriceVO1.setProduct(product1);
        final ProductExtend productExtend1 = new ProductExtend();
        productExtend1.setIsSelfLift(0);
        productPriceVO1.setProductExtend(productExtend1);
        final Goods goods1 = new Goods();
        goods1.setState(0);
        goods1.setIsVirtualGoods(0);
        goods1.setFundsBorrowable(0);
        productPriceVO1.setGoods(goods1);
        productPriceVO1.setEffectiveStock(0);
//        verify(mockCartModel).addToCart(0, 0, "areaCode", productPriceVO1);

        // Confirm CartModel.mutuallyExclusiveCartAction(...).
        final ProductPriceVO productPriceVO2 = new ProductPriceVO();
        final Product product2 = new Product();
        product2.setProductId(0L);
        product2.setActivityPrice(new BigDecimal("0.00"));
        product2.setProductStock(0);
        product2.setState(0);
        product2.setBatchNo("batchNo");
        productPriceVO2.setProduct(product2);
        final ProductExtend productExtend2 = new ProductExtend();
        productExtend2.setIsSelfLift(0);
        productPriceVO2.setProductExtend(productExtend2);
        final Goods goods2 = new Goods();
        goods2.setState(0);
        goods2.setIsVirtualGoods(0);
        goods2.setFundsBorrowable(0);
        productPriceVO2.setGoods(goods2);
        productPriceVO2.setEffectiveStock(0);
//        verify(mockCartModel).mutuallyExclusiveCartAction(0, productPriceVO2);
    }

    @Test
    public void testGetC45artList() {
        // Setup
        final CartPO cartPO = new CartPO();
        cartPO.setCartId(0);
        cartPO.setMemberId(0);
        cartPO.setStoreId(0L);
        cartPO.setGoodsName("goodsName");
        cartPO.setProductId(0L);
        cartPO.setBuyNum(0);
        cartPO.setProductPrice(new BigDecimal("0.00"));
        cartPO.setLandingPrice(new BigDecimal("0.00"));
        cartPO.setTaxPrice(new BigDecimal("0.00"));
        cartPO.setPromotionId(0);
        cartPO.setPromotionType(0);
        cartPO.setPromotionDescription("description");
        cartPO.setOffPrice(new BigDecimal("0.00"));
        cartPO.setProductState(0);
        cartPO.setProductType(0);
        cartPO.setAreaCode("areaCode");
        cartPO.setFinanceRuleCode("financeRuleCode");
        cartPO.setEnabledFlag(0);
        final CartPO cartPO1 = new CartPO();
        cartPO1.setCartId(0);
        cartPO1.setMemberId(0);
        cartPO1.setStoreId(0L);
        cartPO1.setGoodsName("goodsName");
        cartPO1.setProductId(0L);
        cartPO1.setBuyNum(0);
        cartPO1.setProductPrice(new BigDecimal("0.00"));
        cartPO1.setLandingPrice(new BigDecimal("0.00"));
        cartPO1.setTaxPrice(new BigDecimal("0.00"));
        cartPO1.setPromotionId(0);
        cartPO1.setPromotionType(0);
        cartPO1.setPromotionDescription("description");
        cartPO1.setOffPrice(new BigDecimal("0.00"));
        cartPO1.setProductState(0);
        cartPO1.setProductType(0);
        cartPO1.setAreaCode("areaCode");
        cartPO1.setFinanceRuleCode("financeRuleCode");
        cartPO1.setEnabledFlag(0);
//        final CartListVO expectedResult = new CartListVO(new OrderConfirmDTO(Arrays.asList(cartPO)),
//                Arrays.asList(cartPO1));

        // Configure CartModel.refreshCart(...).
        final CartPO cartPO2 = new CartPO();
        cartPO2.setCartId(0);
        cartPO2.setMemberId(0);
        cartPO2.setStoreId(0L);
        cartPO2.setGoodsName("goodsName");
        cartPO2.setProductId(0L);
        cartPO2.setBuyNum(0);
        cartPO2.setProductPrice(new BigDecimal("0.00"));
        cartPO2.setLandingPrice(new BigDecimal("0.00"));
        cartPO2.setTaxPrice(new BigDecimal("0.00"));
        cartPO2.setPromotionId(0);
        cartPO2.setPromotionType(0);
        cartPO2.setPromotionDescription("description");
        cartPO2.setOffPrice(new BigDecimal("0.00"));
        cartPO2.setProductState(0);
        cartPO2.setProductType(0);
        cartPO2.setAreaCode("areaCode");
        cartPO2.setFinanceRuleCode("financeRuleCode");
        cartPO2.setEnabledFlag(0);
        final List<CartPO> cartPOS = Arrays.asList(cartPO2);
        final OrderAddressDTO addressDTO = new OrderAddressDTO();
        addressDTO.setReceiverName("receiverName");
        addressDTO.setReceiverMobile("receiverMobile");
        addressDTO.setProvince("province");
        addressDTO.setCity("city");
        addressDTO.setCityCode("cityCode");
//        when(mockCartModel.refreshCart(0, 0, addressDTO, false)).thenReturn(cartPOS);

        // Configure OrderSubmitAttributesUtils.getProductPriceByProductId(...).
        final ProductPriceVO productPriceVO = new ProductPriceVO();
        final Product product = new Product();
        product.setProductId(0L);
        product.setActivityPrice(new BigDecimal("0.00"));
        product.setProductStock(0);
        product.setState(0);
        product.setBatchNo("batchNo");
        productPriceVO.setProduct(product);
        final ProductExtend productExtend = new ProductExtend();
        productExtend.setIsSelfLift(0);
        productPriceVO.setProductExtend(productExtend);
        final Goods goods = new Goods();
        goods.setState(0);
        goods.setIsVirtualGoods(0);
        goods.setFundsBorrowable(0);
        productPriceVO.setGoods(goods);
        productPriceVO.setEffectiveStock(0);
//        when(mockOrderSubmitAttributesUtils.getProductPriceByProductId(0L, "areaCode", "financeRuleCode"))
//                .thenReturn(productPriceVO);

        // Configure OrderSubmitAttributesUtils.getPromotionList(...).
        final GoodsPromotion goodsPromotion = new GoodsPromotion();
        goodsPromotion.setGoodsPromotionId(0);
        goodsPromotion.setPromotionId(0);
        goodsPromotion.setStoreId(0L);
        goodsPromotion.setPromotionType(0);
        goodsPromotion.setDescription("description");
        final List<GoodsPromotion> goodsPromotions = Arrays.asList(goodsPromotion);
        final Product product1 = new Product();
        product1.setProductId(0L);
        product1.setActivityPrice(new BigDecimal("0.00"));
        product1.setProductStock(0);
        product1.setState(0);
        product1.setBatchNo("batchNo");
//        when(mockOrderSubmitAttributesUtils.getPromotionList(product1)).thenReturn(goodsPromotions);

        // Run the test
        final CartListVO result = cartServiceImplUnderTest.getCartList(0, 0);

        // Verify the results
//        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetCartList_CartModel3ReturnsNoItems() {
        // Setup
        final CartPO cartPO = new CartPO();
        cartPO.setCartId(0);
        cartPO.setMemberId(0);
        cartPO.setStoreId(0L);
        cartPO.setGoodsName("goodsName");
        cartPO.setProductId(0L);
        cartPO.setBuyNum(0);
        cartPO.setProductPrice(new BigDecimal("0.00"));
        cartPO.setLandingPrice(new BigDecimal("0.00"));
        cartPO.setTaxPrice(new BigDecimal("0.00"));
        cartPO.setPromotionId(0);
        cartPO.setPromotionType(0);
        cartPO.setPromotionDescription("description");
        cartPO.setOffPrice(new BigDecimal("0.00"));
        cartPO.setProductState(0);
        cartPO.setProductType(0);
        cartPO.setAreaCode("areaCode");
        cartPO.setFinanceRuleCode("financeRuleCode");
        cartPO.setEnabledFlag(0);
        final CartPO cartPO1 = new CartPO();
        cartPO1.setCartId(0);
        cartPO1.setMemberId(0);
        cartPO1.setStoreId(0L);
        cartPO1.setGoodsName("goodsName");
        cartPO1.setProductId(0L);
        cartPO1.setBuyNum(0);
        cartPO1.setProductPrice(new BigDecimal("0.00"));
        cartPO1.setLandingPrice(new BigDecimal("0.00"));
        cartPO1.setTaxPrice(new BigDecimal("0.00"));
        cartPO1.setPromotionId(0);
        cartPO1.setPromotionType(0);
        cartPO1.setPromotionDescription("description");
        cartPO1.setOffPrice(new BigDecimal("0.00"));
        cartPO1.setProductState(0);
        cartPO1.setProductType(0);
        cartPO1.setAreaCode("areaCode");
        cartPO1.setFinanceRuleCode("financeRuleCode");
        cartPO1.setEnabledFlag(0);
//        final CartListVO expectedResult = new CartListVO(new OrderConfirmDTO(Arrays.asList(cartPO)),
//                Arrays.asList(cartPO1));

        // Configure CartModel.refreshCart(...).
        final OrderAddressDTO addressDTO = new OrderAddressDTO();
        addressDTO.setReceiverName("receiverName");
        addressDTO.setReceiverMobile("receiverMobile");
        addressDTO.setProvince("province");
        addressDTO.setCity("city");
        addressDTO.setCityCode("cityCode");
//        when(mockCartModel.refreshCart(0, 0, addressDTO, false)).thenReturn(Collections.emptyList());

        // Run the test
        final CartListVO result = cartServiceImplUnderTest.getCartList(0, 0);

        // Verify the results
//        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetCartList_Orde445rSubmitAttributesUtilsGetPromotionListReturnsNoItems() {
        // Setup
        final CartPO cartPO = new CartPO();
        cartPO.setCartId(0);
        cartPO.setMemberId(0);
        cartPO.setStoreId(0L);
        cartPO.setGoodsName("goodsName");
        cartPO.setProductId(0L);
        cartPO.setBuyNum(0);
        cartPO.setProductPrice(new BigDecimal("0.00"));
        cartPO.setLandingPrice(new BigDecimal("0.00"));
        cartPO.setTaxPrice(new BigDecimal("0.00"));
        cartPO.setPromotionId(0);
        cartPO.setPromotionType(0);
        cartPO.setPromotionDescription("description");
        cartPO.setOffPrice(new BigDecimal("0.00"));
        cartPO.setProductState(0);
        cartPO.setProductType(0);
        cartPO.setAreaCode("areaCode");
        cartPO.setFinanceRuleCode("financeRuleCode");
        cartPO.setEnabledFlag(0);
        final CartPO cartPO1 = new CartPO();
        cartPO1.setCartId(0);
        cartPO1.setMemberId(0);
        cartPO1.setStoreId(0L);
        cartPO1.setGoodsName("goodsName");
        cartPO1.setProductId(0L);
        cartPO1.setBuyNum(0);
        cartPO1.setProductPrice(new BigDecimal("0.00"));
        cartPO1.setLandingPrice(new BigDecimal("0.00"));
        cartPO1.setTaxPrice(new BigDecimal("0.00"));
        cartPO1.setPromotionId(0);
        cartPO1.setPromotionType(0);
        cartPO1.setPromotionDescription("description");
        cartPO1.setOffPrice(new BigDecimal("0.00"));
        cartPO1.setProductState(0);
        cartPO1.setProductType(0);
        cartPO1.setAreaCode("areaCode");
        cartPO1.setFinanceRuleCode("financeRuleCode");
        cartPO1.setEnabledFlag(0);
//        final CartListVO expectedResult = new CartListVO(new OrderConfirmDTO(Arrays.asList(cartPO)),
//                Arrays.asList(cartPO1));

        // Configure CartModel.refreshCart(...).
        final CartPO cartPO2 = new CartPO();
        cartPO2.setCartId(0);
        cartPO2.setMemberId(0);
        cartPO2.setStoreId(0L);
        cartPO2.setGoodsName("goodsName");
        cartPO2.setProductId(0L);
        cartPO2.setBuyNum(0);
        cartPO2.setProductPrice(new BigDecimal("0.00"));
        cartPO2.setLandingPrice(new BigDecimal("0.00"));
        cartPO2.setTaxPrice(new BigDecimal("0.00"));
        cartPO2.setPromotionId(0);
        cartPO2.setPromotionType(0);
        cartPO2.setPromotionDescription("description");
        cartPO2.setOffPrice(new BigDecimal("0.00"));
        cartPO2.setProductState(0);
        cartPO2.setProductType(0);
        cartPO2.setAreaCode("areaCode");
        cartPO2.setFinanceRuleCode("financeRuleCode");
        cartPO2.setEnabledFlag(0);
        final List<CartPO> cartPOS = Arrays.asList(cartPO2);
        final OrderAddressDTO addressDTO = new OrderAddressDTO();
        addressDTO.setReceiverName("receiverName");
        addressDTO.setReceiverMobile("receiverMobile");
        addressDTO.setProvince("province");
        addressDTO.setCity("city");
        addressDTO.setCityCode("cityCode");
//        when(mockCartModel.refreshCart(0, 0, addressDTO, false)).thenReturn(cartPOS);

        // Configure OrderSubmitAttributesUtils.getProductPriceByProductId(...).
        final ProductPriceVO productPriceVO = new ProductPriceVO();
        final Product product = new Product();
        product.setProductId(0L);
        product.setActivityPrice(new BigDecimal("0.00"));
        product.setProductStock(0);
        product.setState(0);
        product.setBatchNo("batchNo");
        productPriceVO.setProduct(product);
        final ProductExtend productExtend = new ProductExtend();
        productExtend.setIsSelfLift(0);
        productPriceVO.setProductExtend(productExtend);
        final Goods goods = new Goods();
        goods.setState(0);
        goods.setIsVirtualGoods(0);
        goods.setFundsBorrowable(0);
        productPriceVO.setGoods(goods);
        productPriceVO.setEffectiveStock(0);
//        when(mockOrderSubmitAttributesUtils.getProductPriceByProductId(0L, "areaCode", "financeRuleCode"))
//                .thenReturn(productPriceVO);

        // Configure OrderSubmitAttributesUtils.getPromotionList(...).
        final Product product1 = new Product();
        product1.setProductId(0L);
        product1.setActivityPrice(new BigDecimal("0.00"));
        product1.setProductStock(0);
        product1.setState(0);
        product1.setBatchNo("batchNo");
//        when(mockOrderSubmitAttributesUtils.getPromotionList(product1)).thenReturn(Collections.emptyList());

        // Run the test
        final CartListVO result = cartServiceImplUnderTest.getCartList(0, 0);

        // Verify the results
//        assertEquals(expectedResult, result);
    }

    @Test
    public void testCountInC54art() {
//        assertEquals(Integer.valueOf(0), cartServiceImplUnderTest.countInCart(0, 0));
    }

    @Test
    public void testChangeN4um() {
        // Setup
        final Member member = new Member();
        member.setMemberId(0);
        member.setUserNo("userNo");
        member.setCustNo("custNo");
        member.setCappCustNo("cappCustNo");
        member.setMemberName("memberName");

        // Configure ProductFeignClient.getProductByProductId(...).
        final Product product = new Product();
        product.setProductId(0L);
        product.setActivityPrice(new BigDecimal("0.00"));
        product.setProductStock(0);
        product.setState(0);
        product.setBatchNo("batchNo");
//        when(mockProductFeignClient.getProductByProductId(0L)).thenReturn(product);

        // Run the test
//        final Boolean result = cartServiceImplUnderTest.changeNum(member, 0, 0);

        // Verify the results
//        assertFalse(result);
    }

    @Test
    public void testBuildCartList() {
        // Setup
        final OrderSkuInfoDTO orderSkuInfoDTO = new OrderSkuInfoDTO();
        orderSkuInfoDTO.setProductId(0L);
        orderSkuInfoDTO.setNumber(0);
        orderSkuInfoDTO.setProductPrice(new BigDecimal("0.00"));
        orderSkuInfoDTO.setTaxPrice(new BigDecimal("0.00"));
        orderSkuInfoDTO.setLandingPrice(new BigDecimal("0.00"));
        orderSkuInfoDTO.setProductDiscountAmount(new BigDecimal("0.00"));
        orderSkuInfoDTO.setStoreId(0L);
        orderSkuInfoDTO.setAreaCode("areaCode");
        orderSkuInfoDTO.setFinanceRuleCode("financeRuleCode");
        final List<OrderSkuInfoDTO> skuInfoList = Arrays.asList(orderSkuInfoDTO);
        final OrderAddressDTO addressDTO = new OrderAddressDTO();
        addressDTO.setReceiverName("receiverName");
        addressDTO.setReceiverMobile("receiverMobile");
        addressDTO.setProvince("province");
        addressDTO.setCity("city");
        addressDTO.setCityCode("cityCode");

        final CartPO cartPO = new CartPO();
        cartPO.setCartId(0);
        cartPO.setMemberId(0);
        cartPO.setStoreId(0L);
        cartPO.setGoodsName("goodsName");
        cartPO.setProductId(0L);
        cartPO.setBuyNum(0);
        cartPO.setProductPrice(new BigDecimal("0.00"));
        cartPO.setLandingPrice(new BigDecimal("0.00"));
        cartPO.setTaxPrice(new BigDecimal("0.00"));
        cartPO.setPromotionId(0);
        cartPO.setPromotionType(0);
        cartPO.setPromotionDescription("description");
        cartPO.setOffPrice(new BigDecimal("0.00"));
        cartPO.setProductState(0);
        cartPO.setProductType(0);
        cartPO.setAreaCode("areaCode");
        cartPO.setFinanceRuleCode("financeRuleCode");
        cartPO.setEnabledFlag(0);
        final List<CartPO> expectedResult = Arrays.asList(cartPO);
//        when(mockOrderSubmitAttributesUtils.getProductBatch(Arrays.asList(0L), "areaCode",
//                "financeRuleCode")).thenReturn(new HashMap<>());

        // Configure CartModel.assembleCartPO(...).
        final CartPO cartPO1 = new CartPO();
        cartPO1.setCartId(0);
        cartPO1.setMemberId(0);
        cartPO1.setStoreId(0L);
        cartPO1.setGoodsName("goodsName");
        cartPO1.setProductId(0L);
        cartPO1.setBuyNum(0);
        cartPO1.setProductPrice(new BigDecimal("0.00"));
        cartPO1.setLandingPrice(new BigDecimal("0.00"));
        cartPO1.setTaxPrice(new BigDecimal("0.00"));
        cartPO1.setPromotionId(0);
        cartPO1.setPromotionType(0);
        cartPO1.setPromotionDescription("description");
        cartPO1.setOffPrice(new BigDecimal("0.00"));
        cartPO1.setProductState(0);
        cartPO1.setProductType(0);
        cartPO1.setAreaCode("areaCode");
        cartPO1.setFinanceRuleCode("financeRuleCode");
        cartPO1.setEnabledFlag(0);
        final ProductPriceVO productPriceVO = new ProductPriceVO();
        final Product product = new Product();
        product.setProductId(0L);
        product.setActivityPrice(new BigDecimal("0.00"));
        product.setProductStock(0);
        product.setState(0);
        product.setBatchNo("batchNo");
        productPriceVO.setProduct(product);
        final ProductExtend productExtend = new ProductExtend();
        productExtend.setIsSelfLift(0);
        productPriceVO.setProductExtend(productExtend);
        final Goods goods = new Goods();
        goods.setState(0);
        goods.setIsVirtualGoods(0);
        goods.setFundsBorrowable(0);
        productPriceVO.setGoods(goods);
        productPriceVO.setEffectiveStock(0);
//        when(mockCartModel.assembleCartPO(0, 0, "areaCode", productPriceVO, false)).thenReturn(cartPO1);

        // Configure OrderSubmitAttributesUtils.getMemberByMemberId(...).
        final Member member = new Member();
        member.setMemberId(0);
        member.setUserNo("userNo");
        member.setCustNo("custNo");
        member.setCappCustNo("cappCustNo");
        member.setMemberName("memberName");
//        when(mockOrderSubmitAttributesUtils.getMemberByMemberId(0)).thenReturn(member);

        // Configure OrderSubmitAttributesUtils.getStoreAreaCode(...).
        final StoreRegionVO storeRegionVO = new StoreRegionVO();
        storeRegionVO.setId(0);
        storeRegionVO.setName("name");
        storeRegionVO.setCode("areaCode");
        storeRegionVO.setSortNum(0);
        storeRegionVO.setBizBranchCode("bizBranchCode");
        final FrontStoreRegionVo frontStoreRegionVo = new FrontStoreRegionVo(0, Arrays.asList(storeRegionVO));
        final OrderAddressDTO addressDTO1 = new OrderAddressDTO();
        addressDTO1.setReceiverName("receiverName");
        addressDTO1.setReceiverMobile("receiverMobile");
        addressDTO1.setProvince("province");
        addressDTO1.setCity("city");
        addressDTO1.setCityCode("cityCode");
        final Member member1 = new Member();
        member1.setMemberId(0);
        member1.setUserNo("userNo");
        member1.setCustNo("custNo");
        member1.setCappCustNo("cappCustNo");
        member1.setMemberName("memberName");
//        when(mockOrderSubmitAttributesUtils.getStoreAreaCode("storeId", addressDTO1, member1))
//                .thenReturn(frontStoreRegionVo);

        // Configure CartModel.getSinglePromotion(...).
        final GoodsPromotion goodsPromotion = new GoodsPromotion();
        goodsPromotion.setGoodsPromotionId(0);
        goodsPromotion.setPromotionId(0);
        goodsPromotion.setStoreId(0L);
        goodsPromotion.setPromotionType(0);
        goodsPromotion.setDescription("description");
//        when(mockCartModel.getSinglePromotion(0L, 0)).thenReturn(goodsPromotion);

        // Configure CartUtils.isCartInvalid(...).
        final CartPO cartPO2 = new CartPO();
        cartPO2.setCartId(0);
        cartPO2.setMemberId(0);
        cartPO2.setStoreId(0L);
        cartPO2.setGoodsName("goodsName");
        cartPO2.setProductId(0L);
        cartPO2.setBuyNum(0);
        cartPO2.setProductPrice(new BigDecimal("0.00"));
        cartPO2.setLandingPrice(new BigDecimal("0.00"));
        cartPO2.setTaxPrice(new BigDecimal("0.00"));
        cartPO2.setPromotionId(0);
        cartPO2.setPromotionType(0);
        cartPO2.setPromotionDescription("description");
        cartPO2.setOffPrice(new BigDecimal("0.00"));
        cartPO2.setProductState(0);
        cartPO2.setProductType(0);
        cartPO2.setAreaCode("areaCode");
        cartPO2.setFinanceRuleCode("financeRuleCode");
        cartPO2.setEnabledFlag(0);
        final ProductPriceVO productPriceByProductId = new ProductPriceVO();
        final Product product1 = new Product();
        product1.setProductId(0L);
        product1.setActivityPrice(new BigDecimal("0.00"));
        product1.setProductStock(0);
        product1.setState(0);
        product1.setBatchNo("batchNo");
        productPriceByProductId.setProduct(product1);
        final ProductExtend productExtend1 = new ProductExtend();
        productExtend1.setIsSelfLift(0);
        productPriceByProductId.setProductExtend(productExtend1);
        final Goods goods1 = new Goods();
        goods1.setState(0);
        goods1.setIsVirtualGoods(0);
        goods1.setFundsBorrowable(0);
        productPriceByProductId.setGoods(goods1);
        productPriceByProductId.setEffectiveStock(0);
//        when(mockCartUtils.isCartInvalid(cartPO2, productPriceByProductId)).thenReturn(false);

        // Run the test
//        final List<CartPO> result = cartServiceImplUnderTest.buildCartList(skuInfoList, OrderTypeEnum.NORMAL, 0,
//                "areaCode", "financeRuleCode", "channel", addressDTO);

        // Verify the results
//        assertEquals(expectedResult, result);
//        verify(mockOrderProductHelper).getSpellGroupPrice(0, 0L, 0L);
//        verify(mockOrderProductHelper).getLadderGroupPrice(0, 0L);
//        verify(mockOrderProductHelper).getPreSellPrice(0, 0L);
    }
    @Test
    public void testAddCart() {
        // Setup
        final Member member = new Member();
        member.setMemberId(0);
        member.setUserNo("userNo");
        member.setCustNo("custNo");
        member.setCappCustNo("cappCustNo");
        member.setMemberName("memberName");

        // Configure OrderSubmitAttributesUtils.getProductPriceByProductId(...).
        final ProductPriceVO productPriceVO = new ProductPriceVO();
        final Product product = new Product();
        product.setProductId(0L);
        product.setActivityPrice(new BigDecimal("0.00"));
        product.setProductStock(0);
        product.setState(0);
        product.setBatchNo("batchNo");
        productPriceVO.setProduct(product);
        final ProductExtend productExtend = new ProductExtend();
        productExtend.setIsSelfLift(0);
        productPriceVO.setProductExtend(productExtend);
        final Goods goods = new Goods();
        goods.setState(0);
        goods.setIsVirtualGoods(0);
        goods.setFundsBorrowable(0);
        productPriceVO.setGoods(goods);
        productPriceVO.setEffectiveStock(0);
//        when(mockOrderSubmitAttributesUtils.getProductPriceByProductId(0L, "areaCode", "financeRuleCode"))
//                .thenReturn(productPriceVO);

        // Run the test
//        final Boolean result = cartServiceImplUnderTest.addCart(member, 0L, "areaCode", 0, "financeRuleCode");

        // Verify the results
//        assertFalse(result);
//        verify(mockDistributeLock).lockAndProcess(eq("lockName"), eq(0L), eq(30L), eq(TimeUnit.SECONDS),
//                any(Supplier.class));

        // Confirm CartModel.addToCart(...).
        final ProductPriceVO productPriceVO1 = new ProductPriceVO();
        final Product product1 = new Product();
        product1.setProductId(0L);
        product1.setActivityPrice(new BigDecimal("0.00"));
        product1.setProductStock(0);
        product1.setState(0);
        product1.setBatchNo("batchNo");
        productPriceVO1.setProduct(product1);
        final ProductExtend productExtend1 = new ProductExtend();
        productExtend1.setIsSelfLift(0);
        productPriceVO1.setProductExtend(productExtend1);
        final Goods goods1 = new Goods();
        goods1.setState(0);
        goods1.setIsVirtualGoods(0);
        goods1.setFundsBorrowable(0);
        productPriceVO1.setGoods(goods1);
        productPriceVO1.setEffectiveStock(0);
//        verify(mockCartModel).addToCart(0, 0, "areaCode", productPriceVO1);

        // Confirm CartModel.mutuallyExclusiveCartAction(...).
        final ProductPriceVO productPriceVO2 = new ProductPriceVO();
        final Product product2 = new Product();
        product2.setProductId(0L);
        product2.setActivityPrice(new BigDecimal("0.00"));
        product2.setProductStock(0);
        product2.setState(0);
        product2.setBatchNo("batchNo");
        productPriceVO2.setProduct(product2);
        final ProductExtend productExtend2 = new ProductExtend();
        productExtend2.setIsSelfLift(0);
        productPriceVO2.setProductExtend(productExtend2);
        final Goods goods2 = new Goods();
        goods2.setState(0);
        goods2.setIsVirtualGoods(0);
        goods2.setFundsBorrowable(0);
        productPriceVO2.setGoods(goods2);
        productPriceVO2.setEffectiveStock(0);
//        verify(mockCartModel).mutuallyExclusiveCartAction(0, productPriceVO2);
    }

    @Test
    public void testGetCartList() {
        // Setup
        final CartPO cartPO = new CartPO();
        cartPO.setCartId(0);
        cartPO.setMemberId(0);
        cartPO.setStoreId(0L);
        cartPO.setGoodsName("goodsName");
        cartPO.setProductId(0L);
        cartPO.setBuyNum(0);
        cartPO.setProductPrice(new BigDecimal("0.00"));
        cartPO.setLandingPrice(new BigDecimal("0.00"));
        cartPO.setTaxPrice(new BigDecimal("0.00"));
        cartPO.setPromotionId(0);
        cartPO.setPromotionType(0);
        cartPO.setPromotionDescription("description");
        cartPO.setOffPrice(new BigDecimal("0.00"));
        cartPO.setProductState(0);
        cartPO.setProductType(0);
        cartPO.setAreaCode("areaCode");
        cartPO.setFinanceRuleCode("financeRuleCode");
        cartPO.setEnabledFlag(0);
        final CartPO cartPO1 = new CartPO();
        cartPO1.setCartId(0);
        cartPO1.setMemberId(0);
        cartPO1.setStoreId(0L);
        cartPO1.setGoodsName("goodsName");
        cartPO1.setProductId(0L);
        cartPO1.setBuyNum(0);
        cartPO1.setProductPrice(new BigDecimal("0.00"));
        cartPO1.setLandingPrice(new BigDecimal("0.00"));
        cartPO1.setTaxPrice(new BigDecimal("0.00"));
        cartPO1.setPromotionId(0);
        cartPO1.setPromotionType(0);
        cartPO1.setPromotionDescription("description");
        cartPO1.setOffPrice(new BigDecimal("0.00"));
        cartPO1.setProductState(0);
        cartPO1.setProductType(0);
        cartPO1.setAreaCode("areaCode");
        cartPO1.setFinanceRuleCode("financeRuleCode");
        cartPO1.setEnabledFlag(0);
//        final CartListVO expectedResult = new CartListVO(new OrderConfirmDTO(Arrays.asList(cartPO)),
//                Arrays.asList(cartPO1));

        // Configure CartModel.refreshCart(...).
        final CartPO cartPO2 = new CartPO();
        cartPO2.setCartId(0);
        cartPO2.setMemberId(0);
        cartPO2.setStoreId(0L);
        cartPO2.setGoodsName("goodsName");
        cartPO2.setProductId(0L);
        cartPO2.setBuyNum(0);
        cartPO2.setProductPrice(new BigDecimal("0.00"));
        cartPO2.setLandingPrice(new BigDecimal("0.00"));
        cartPO2.setTaxPrice(new BigDecimal("0.00"));
        cartPO2.setPromotionId(0);
        cartPO2.setPromotionType(0);
        cartPO2.setPromotionDescription("description");
        cartPO2.setOffPrice(new BigDecimal("0.00"));
        cartPO2.setProductState(0);
        cartPO2.setProductType(0);
        cartPO2.setAreaCode("areaCode");
        cartPO2.setFinanceRuleCode("financeRuleCode");
        cartPO2.setEnabledFlag(0);
        final List<CartPO> cartPOS = Arrays.asList(cartPO2);
        final OrderAddressDTO addressDTO = new OrderAddressDTO();
        addressDTO.setReceiverName("receiverName");
        addressDTO.setReceiverMobile("receiverMobile");
        addressDTO.setProvince("province");
        addressDTO.setCity("city");
        addressDTO.setCityCode("cityCode");
//        when(mockCartModel.refreshCart(0, 0, addressDTO, false)).thenReturn(cartPOS);

        // Configure OrderSubmitAttributesUtils.getProductPriceByProductId(...).
        final ProductPriceVO productPriceVO = new ProductPriceVO();
        final Product product = new Product();
        product.setProductId(0L);
        product.setActivityPrice(new BigDecimal("0.00"));
        product.setProductStock(0);
        product.setState(0);
        product.setBatchNo("batchNo");
        productPriceVO.setProduct(product);
        final ProductExtend productExtend = new ProductExtend();
        productExtend.setIsSelfLift(0);
        productPriceVO.setProductExtend(productExtend);
        final Goods goods = new Goods();
        goods.setState(0);
        goods.setIsVirtualGoods(0);
        goods.setFundsBorrowable(0);
        productPriceVO.setGoods(goods);
        productPriceVO.setEffectiveStock(0);
//        when(mockOrderSubmitAttributesUtils.getProductPriceByProductId(0L, "areaCode", "financeRuleCode"))
//                .thenReturn(productPriceVO);

        // Configure OrderSubmitAttributesUtils.getPromotionList(...).
        final GoodsPromotion goodsPromotion = new GoodsPromotion();
        goodsPromotion.setGoodsPromotionId(0);
        goodsPromotion.setPromotionId(0);
        goodsPromotion.setStoreId(0L);
        goodsPromotion.setPromotionType(0);
        goodsPromotion.setDescription("description");
        final List<GoodsPromotion> goodsPromotions = Arrays.asList(goodsPromotion);
        final Product product1 = new Product();
        product1.setProductId(0L);
        product1.setActivityPrice(new BigDecimal("0.00"));
        product1.setProductStock(0);
        product1.setState(0);
        product1.setBatchNo("batchNo");
//        when(mockOrderSubmitAttributesUtils.getPromotionList(product1)).thenReturn(goodsPromotions);

        // Run the test
        final CartListVO result = cartServiceImplUnderTest.getCartList(0, 0);

        // Verify the results
//        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetCartList_CartModelReturnsNoItems() {
        // Setup
        final CartPO cartPO = new CartPO();
        cartPO.setCartId(0);
        cartPO.setMemberId(0);
        cartPO.setStoreId(0L);
        cartPO.setGoodsName("goodsName");
        cartPO.setProductId(0L);
        cartPO.setBuyNum(0);
        cartPO.setProductPrice(new BigDecimal("0.00"));
        cartPO.setLandingPrice(new BigDecimal("0.00"));
        cartPO.setTaxPrice(new BigDecimal("0.00"));
        cartPO.setPromotionId(0);
        cartPO.setPromotionType(0);
        cartPO.setPromotionDescription("description");
        cartPO.setOffPrice(new BigDecimal("0.00"));
        cartPO.setProductState(0);
        cartPO.setProductType(0);
        cartPO.setAreaCode("areaCode");
        cartPO.setFinanceRuleCode("financeRuleCode");
        cartPO.setEnabledFlag(0);
        final CartPO cartPO1 = new CartPO();
        cartPO1.setCartId(0);
        cartPO1.setMemberId(0);
        cartPO1.setStoreId(0L);
        cartPO1.setGoodsName("goodsName");
        cartPO1.setProductId(0L);
        cartPO1.setBuyNum(0);
        cartPO1.setProductPrice(new BigDecimal("0.00"));
        cartPO1.setLandingPrice(new BigDecimal("0.00"));
        cartPO1.setTaxPrice(new BigDecimal("0.00"));
        cartPO1.setPromotionId(0);
        cartPO1.setPromotionType(0);
        cartPO1.setPromotionDescription("description");
        cartPO1.setOffPrice(new BigDecimal("0.00"));
        cartPO1.setProductState(0);
        cartPO1.setProductType(0);
        cartPO1.setAreaCode("areaCode");
        cartPO1.setFinanceRuleCode("financeRuleCode");
        cartPO1.setEnabledFlag(0);
//        final CartListVO expectedResult = new CartListVO(new OrderConfirmDTO(Arrays.asList(cartPO)),
//                Arrays.asList(cartPO1));

        // Configure CartModel.refreshCart(...).
        final OrderAddressDTO addressDTO = new OrderAddressDTO();
        addressDTO.setReceiverName("receiverName");
        addressDTO.setReceiverMobile("receiverMobile");
        addressDTO.setProvince("province");
        addressDTO.setCity("city");
        addressDTO.setCityCode("cityCode");
//        when(mockCartModel.refreshCart(0, 0, addressDTO, false)).thenReturn(Collections.emptyList());

        // Run the test
        final CartListVO result = cartServiceImplUnderTest.getCartList(0, 0);

        // Verify the results
//        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetCartList_OrderSubmitAttributesUtilsGetPromotionListReturnsNoItems() {
        // Setup
        final CartPO cartPO = new CartPO();
        cartPO.setCartId(0);
        cartPO.setMemberId(0);
        cartPO.setStoreId(0L);
        cartPO.setGoodsName("goodsName");
        cartPO.setProductId(0L);
        cartPO.setBuyNum(0);
        cartPO.setProductPrice(new BigDecimal("0.00"));
        cartPO.setLandingPrice(new BigDecimal("0.00"));
        cartPO.setTaxPrice(new BigDecimal("0.00"));
        cartPO.setPromotionId(0);
        cartPO.setPromotionType(0);
        cartPO.setPromotionDescription("description");
        cartPO.setOffPrice(new BigDecimal("0.00"));
        cartPO.setProductState(0);
        cartPO.setProductType(0);
        cartPO.setAreaCode("areaCode");
        cartPO.setFinanceRuleCode("financeRuleCode");
        cartPO.setEnabledFlag(0);
        final CartPO cartPO1 = new CartPO();
        cartPO1.setCartId(0);
        cartPO1.setMemberId(0);
        cartPO1.setStoreId(0L);
        cartPO1.setGoodsName("goodsName");
        cartPO1.setProductId(0L);
        cartPO1.setBuyNum(0);
        cartPO1.setProductPrice(new BigDecimal("0.00"));
        cartPO1.setLandingPrice(new BigDecimal("0.00"));
        cartPO1.setTaxPrice(new BigDecimal("0.00"));
        cartPO1.setPromotionId(0);
        cartPO1.setPromotionType(0);
        cartPO1.setPromotionDescription("description");
        cartPO1.setOffPrice(new BigDecimal("0.00"));
        cartPO1.setProductState(0);
        cartPO1.setProductType(0);
        cartPO1.setAreaCode("areaCode");
        cartPO1.setFinanceRuleCode("financeRuleCode");
        cartPO1.setEnabledFlag(0);
//        final CartListVO expectedResult = new CartListVO(new OrderConfirmDTO(Arrays.asList(cartPO)),
//                Arrays.asList(cartPO1));

        // Configure CartModel.refreshCart(...).
        final CartPO cartPO2 = new CartPO();
        cartPO2.setCartId(0);
        cartPO2.setMemberId(0);
        cartPO2.setStoreId(0L);
        cartPO2.setGoodsName("goodsName");
        cartPO2.setProductId(0L);
        cartPO2.setBuyNum(0);
        cartPO2.setProductPrice(new BigDecimal("0.00"));
        cartPO2.setLandingPrice(new BigDecimal("0.00"));
        cartPO2.setTaxPrice(new BigDecimal("0.00"));
        cartPO2.setPromotionId(0);
        cartPO2.setPromotionType(0);
        cartPO2.setPromotionDescription("description");
        cartPO2.setOffPrice(new BigDecimal("0.00"));
        cartPO2.setProductState(0);
        cartPO2.setProductType(0);
        cartPO2.setAreaCode("areaCode");
        cartPO2.setFinanceRuleCode("financeRuleCode");
        cartPO2.setEnabledFlag(0);
        final List<CartPO> cartPOS = Arrays.asList(cartPO2);
        final OrderAddressDTO addressDTO = new OrderAddressDTO();
        addressDTO.setReceiverName("receiverName");
        addressDTO.setReceiverMobile("receiverMobile");
        addressDTO.setProvince("province");
        addressDTO.setCity("city");
        addressDTO.setCityCode("cityCode");
//        when(mockCartModel.refreshCart(0, 0, addressDTO, false)).thenReturn(cartPOS);

        // Configure OrderSubmitAttributesUtils.getProductPriceByProductId(...).
        final ProductPriceVO productPriceVO = new ProductPriceVO();
        final Product product = new Product();
        product.setProductId(0L);
        product.setActivityPrice(new BigDecimal("0.00"));
        product.setProductStock(0);
        product.setState(0);
        product.setBatchNo("batchNo");
        productPriceVO.setProduct(product);
        final ProductExtend productExtend = new ProductExtend();
        productExtend.setIsSelfLift(0);
        productPriceVO.setProductExtend(productExtend);
        final Goods goods = new Goods();
        goods.setState(0);
        goods.setIsVirtualGoods(0);
        goods.setFundsBorrowable(0);
        productPriceVO.setGoods(goods);
        productPriceVO.setEffectiveStock(0);
//        when(mockOrderSubmitAttributesUtils.getProductPriceByProductId(0L, "areaCode", "financeRuleCode"))
//                .thenReturn(productPriceVO);

        // Configure OrderSubmitAttributesUtils.getPromotionList(...).
        final Product product1 = new Product();
        product1.setProductId(0L);
        product1.setActivityPrice(new BigDecimal("0.00"));
        product1.setProductStock(0);
        product1.setState(0);
        product1.setBatchNo("batchNo");
//        when(mockOrderSubmitAttributesUtils.getPromotionList(product1)).thenReturn(Collections.emptyList());

        // Run the test
        final CartListVO result = cartServiceImplUnderTest.getCartList(0, 0);

        // Verify the results
//        assertEquals(expectedResult, result);
    }

    @Test
    public void testCountInCart() {
//        assertEquals(Integer.valueOf(0), cartServiceImplUnderTest.countInCart(0, 0));
    }

    @Test
    public void testChangeNum() {
        // Setup
        final Member member = new Member();
        member.setMemberId(0);
        member.setUserNo("userNo");
        member.setCustNo("custNo");
        member.setCappCustNo("cappCustNo");
        member.setMemberName("memberName");

        // Configure ProductFeignClient.getProductByProductId(...).
        final Product product = new Product();
        product.setProductId(0L);
        product.setActivityPrice(new BigDecimal("0.00"));
        product.setProductStock(0);
        product.setState(0);
        product.setBatchNo("batchNo");
//        when(mockProductFeignClient.getProductByProductId(0L)).thenReturn(product);

        // Run the test
//        final Boolean result = cartServiceImplUnderTest.changeNum(member, 0, 0);

        // Verify the results
//        assertFalse(result);
    }

    @Test
    public void testBuildCartL453ist() {
        // Setup
        final OrderSkuInfoDTO orderSkuInfoDTO = new OrderSkuInfoDTO();
        orderSkuInfoDTO.setProductId(0L);
        orderSkuInfoDTO.setNumber(0);
        orderSkuInfoDTO.setProductPrice(new BigDecimal("0.00"));
        orderSkuInfoDTO.setTaxPrice(new BigDecimal("0.00"));
        orderSkuInfoDTO.setLandingPrice(new BigDecimal("0.00"));
        orderSkuInfoDTO.setProductDiscountAmount(new BigDecimal("0.00"));
        orderSkuInfoDTO.setStoreId(0L);
        orderSkuInfoDTO.setAreaCode("areaCode");
        orderSkuInfoDTO.setFinanceRuleCode("financeRuleCode");
        final List<OrderSkuInfoDTO> skuInfoList = Arrays.asList(orderSkuInfoDTO);
        final OrderAddressDTO addressDTO = new OrderAddressDTO();
        addressDTO.setReceiverName("receiverName");
        addressDTO.setReceiverMobile("receiverMobile");
        addressDTO.setProvince("province");
        addressDTO.setCity("city");
        addressDTO.setCityCode("cityCode");

        final CartPO cartPO = new CartPO();
        cartPO.setCartId(0);
        cartPO.setMemberId(0);
        cartPO.setStoreId(0L);
        cartPO.setGoodsName("goodsName");
        cartPO.setProductId(0L);
        cartPO.setBuyNum(0);
        cartPO.setProductPrice(new BigDecimal("0.00"));
        cartPO.setLandingPrice(new BigDecimal("0.00"));
        cartPO.setTaxPrice(new BigDecimal("0.00"));
        cartPO.setPromotionId(0);
        cartPO.setPromotionType(0);
        cartPO.setPromotionDescription("description");
        cartPO.setOffPrice(new BigDecimal("0.00"));
        cartPO.setProductState(0);
        cartPO.setProductType(0);
        cartPO.setAreaCode("areaCode");
        cartPO.setFinanceRuleCode("financeRuleCode");
        cartPO.setEnabledFlag(0);
        final List<CartPO> expectedResult = Arrays.asList(cartPO);
//        when(mockOrderSubmitAttributesUtils.getProductBatch(Arrays.asList(0L), "areaCode",
//                "financeRuleCode")).thenReturn(new HashMap<>());

        // Configure CartModel.assembleCartPO(...).
        final CartPO cartPO1 = new CartPO();
        cartPO1.setCartId(0);
        cartPO1.setMemberId(0);
        cartPO1.setStoreId(0L);
        cartPO1.setGoodsName("goodsName");
        cartPO1.setProductId(0L);
        cartPO1.setBuyNum(0);
        cartPO1.setProductPrice(new BigDecimal("0.00"));
        cartPO1.setLandingPrice(new BigDecimal("0.00"));
        cartPO1.setTaxPrice(new BigDecimal("0.00"));
        cartPO1.setPromotionId(0);
        cartPO1.setPromotionType(0);
        cartPO1.setPromotionDescription("description");
        cartPO1.setOffPrice(new BigDecimal("0.00"));
        cartPO1.setProductState(0);
        cartPO1.setProductType(0);
        cartPO1.setAreaCode("areaCode");
        cartPO1.setFinanceRuleCode("financeRuleCode");
        cartPO1.setEnabledFlag(0);
        final ProductPriceVO productPriceVO = new ProductPriceVO();
        final Product product = new Product();
        product.setProductId(0L);
        product.setActivityPrice(new BigDecimal("0.00"));
        product.setProductStock(0);
        product.setState(0);
        product.setBatchNo("batchNo");
        productPriceVO.setProduct(product);
        final ProductExtend productExtend = new ProductExtend();
        productExtend.setIsSelfLift(0);
        productPriceVO.setProductExtend(productExtend);
        final Goods goods = new Goods();
        goods.setState(0);
        goods.setIsVirtualGoods(0);
        goods.setFundsBorrowable(0);
        productPriceVO.setGoods(goods);
        productPriceVO.setEffectiveStock(0);
//        when(mockCartModel.assembleCartPO(0, 0, "areaCode", productPriceVO, false)).thenReturn(cartPO1);

        // Configure OrderSubmitAttributesUtils.getMemberByMemberId(...).
        final Member member = new Member();
        member.setMemberId(0);
        member.setUserNo("userNo");
        member.setCustNo("custNo");
        member.setCappCustNo("cappCustNo");
        member.setMemberName("memberName");
//        when(mockOrderSubmitAttributesUtils.getMemberByMemberId(0)).thenReturn(member);

        // Configure OrderSubmitAttributesUtils.getStoreAreaCode(...).
        final StoreRegionVO storeRegionVO = new StoreRegionVO();
        storeRegionVO.setId(0);
        storeRegionVO.setName("name");
        storeRegionVO.setCode("areaCode");
        storeRegionVO.setSortNum(0);
        storeRegionVO.setBizBranchCode("bizBranchCode");
        final FrontStoreRegionVo frontStoreRegionVo = new FrontStoreRegionVo(0, Arrays.asList(storeRegionVO));
        final OrderAddressDTO addressDTO1 = new OrderAddressDTO();
        addressDTO1.setReceiverName("receiverName");
        addressDTO1.setReceiverMobile("receiverMobile");
        addressDTO1.setProvince("province");
        addressDTO1.setCity("city");
        addressDTO1.setCityCode("cityCode");
        final Member member1 = new Member();
        member1.setMemberId(0);
        member1.setUserNo("userNo");
        member1.setCustNo("custNo");
        member1.setCappCustNo("cappCustNo");
        member1.setMemberName("memberName");
//        when(mockOrderSubmitAttributesUtils.getStoreAreaCode("storeId", addressDTO1, member1))
//                .thenReturn(frontStoreRegionVo);

        // Configure CartModel.getSinglePromotion(...).
        final GoodsPromotion goodsPromotion = new GoodsPromotion();
        goodsPromotion.setGoodsPromotionId(0);
        goodsPromotion.setPromotionId(0);
        goodsPromotion.setStoreId(0L);
        goodsPromotion.setPromotionType(0);
        goodsPromotion.setDescription("description");
//        when(mockCartModel.getSinglePromotion(0L, 0)).thenReturn(goodsPromotion);

        // Configure CartUtils.isCartInvalid(...).
        final CartPO cartPO2 = new CartPO();
        cartPO2.setCartId(0);
        cartPO2.setMemberId(0);
        cartPO2.setStoreId(0L);
        cartPO2.setGoodsName("goodsName");
        cartPO2.setProductId(0L);
        cartPO2.setBuyNum(0);
        cartPO2.setProductPrice(new BigDecimal("0.00"));
        cartPO2.setLandingPrice(new BigDecimal("0.00"));
        cartPO2.setTaxPrice(new BigDecimal("0.00"));
        cartPO2.setPromotionId(0);
        cartPO2.setPromotionType(0);
        cartPO2.setPromotionDescription("description");
        cartPO2.setOffPrice(new BigDecimal("0.00"));
        cartPO2.setProductState(0);
        cartPO2.setProductType(0);
        cartPO2.setAreaCode("areaCode");
        cartPO2.setFinanceRuleCode("financeRuleCode");
        cartPO2.setEnabledFlag(0);
        final ProductPriceVO productPriceByProductId = new ProductPriceVO();
        final Product product1 = new Product();
        product1.setProductId(0L);
        product1.setActivityPrice(new BigDecimal("0.00"));
        product1.setProductStock(0);
        product1.setState(0);
        product1.setBatchNo("batchNo");
        productPriceByProductId.setProduct(product1);
        final ProductExtend productExtend1 = new ProductExtend();
        productExtend1.setIsSelfLift(0);
        productPriceByProductId.setProductExtend(productExtend1);
        final Goods goods1 = new Goods();
        goods1.setState(0);
        goods1.setIsVirtualGoods(0);
        goods1.setFundsBorrowable(0);
        productPriceByProductId.setGoods(goods1);
        productPriceByProductId.setEffectiveStock(0);
//        when(mockCartUtils.isCartInvalid(cartPO2, productPriceByProductId)).thenReturn(false);

        // Run the test
//        final List<CartPO> result = cartServiceImplUnderTest.buildCartList(skuInfoList, OrderTypeEnum.NORMAL, 0,
//                "areaCode", "financeRuleCode", "channel", addressDTO);

        // Verify the results
//        assertEquals(expectedResult, result);
//        verify(mockOrderProductHelper).getSpellGroupPrice(0, 0L, 0L);
//        verify(mockOrderProductHelper).getLadderGroupPrice(0, 0L);
//        verify(mockOrderProductHelper).getPreSellPrice(0, 0L);
    }
}
