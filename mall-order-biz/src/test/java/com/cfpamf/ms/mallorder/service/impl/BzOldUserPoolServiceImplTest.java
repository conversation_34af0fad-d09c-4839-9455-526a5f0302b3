package com.cfpamf.ms.mallorder.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.cfpamf.ms.mallorder.mapper.BzOldUserPoolMapper;
import com.cfpamf.ms.mallorder.mapper.OrderMapper;
import com.cfpamf.ms.mallorder.service.IOrderService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BzOldUserPoolServiceImplTest {

	@Mock(lenient = true)
	BzOldUserPoolMapper bzOldUserPoolMapper;
	@Mock(lenient = true)
	private IOrderService mockIOrderService;
	@InjectMocks
	private BzOldUserPoolServiceImpl bzOldUserPoolServiceImplUnderTest;

	@Before
	public void setUp() {
		MockitoAnnotations.initMocks(this);
	}

	@Test
	public void testIsFirstLoanOrderByUserNo() {
		when(mockIOrderService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));
		Mockito.when(bzOldUserPoolMapper.selectOne(any())).thenReturn(null);

		Assert.assertTrue(bzOldUserPoolServiceImplUnderTest.isFirstLoanOrderByUserNo("userNo"));
	}

	@Test
	public void testRestoreFirstLoanOrder() {
		// Setup
		OrderMapper orderMapper = mock(OrderMapper.class);
		when(mockIOrderService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(orderMapper));
		Mockito.when(orderMapper.selectOne(any())).thenReturn(null);

		// Run the test
		final Boolean result = bzOldUserPoolServiceImplUnderTest.restoreFirstLoanOrder("orderSn");

	}

	@Test
	public void testInsertFirstLoanOrder() {
		when(mockIOrderService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));
		Mockito.when(bzOldUserPoolMapper.selectOne(any())).thenReturn(null);

		bzOldUserPoolServiceImplUnderTest.insertFirstLoanOrder("userNo", "orderSn");
	}
}
