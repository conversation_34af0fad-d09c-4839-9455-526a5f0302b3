package com.cfpamf.ms.mallorder.common.util;
import com.cfpamf.ms.mallgoods.facade.vo.ProductFinanceRuleLabel;
import com.google.common.collect.Lists;
import com.cfpamf.ms.mallgoods.facade.vo.ProductExtend;
import com.cfpamf.ms.mallgoods.facade.vo.Goods;
import com.cfpamf.ms.mallgoods.facade.vo.Product;
import com.cfpamf.ms.mallgoods.facade.enums.ProductReasonTypeEnum;
import com.cfpamf.ms.mallgoods.facade.vo.ProductPriceBranchRange;

import com.cfpamf.ms.mallgoods.facade.api.ProductFeignClient;
import com.cfpamf.ms.mallgoods.facade.vo.ProductPriceVO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.jupiter.api.Assertions.*;

/**
 * @ClassName: OrderLocalUtilsTest
 * @Description:
 * @Author: maoliang
 * @Date: 2023/5/18
 * @version: 1.0
 */
@RunWith(MockitoJUnitRunner.class)
public class OrderLocalUtilsTest {

    @InjectMocks
    private OrderLocalUtils  orderLocalUtils;

    @Mock
    private ProductFeignClient productFeignClient;


    // Test case 1: getProductPrice() method should return non-null ProductPriceVO object
    @Test
    public void getProductPrice_shouldReturnProductPriceVO() {
        // Arrange mock data
        Long productId = 1L;
        String areaCode = "ABC";
        String financeRuleCode = "123";
        ProductPriceVO expected = new ProductPriceVO();
        Product product = new Product();
        product.setGoodsIsDistribute(1);
        expected.setProduct(product);
        expected.setProductExtend(new ProductExtend());
        expected.setProductFinanceRuleLabel(new ProductFinanceRuleLabel());
        expected.setProductPriceBranchRange(new ProductPriceBranchRange());
        expected.setGoods(new Goods());
        expected.setJudgeFlag(false);
        expected.setCompanyName("");
        expected.setProductReasonTypeEnum(ProductReasonTypeEnum.PRODUCT_FOREIGN_NO);
        expected.setCouponList(Lists.newArrayList());
        expected.setEffectiveStock(0);
        expected.setProductSpecJson("");
        expected.setMfrCoopFlag(0);

        Mockito.when(productFeignClient.getProductPriceByProductId(productId, areaCode, financeRuleCode))
                .thenReturn(expected);

        // Act
        ProductPriceVO actual = orderLocalUtils.getProductPrice(productId, areaCode, financeRuleCode);

        // Assert
        Assert.assertNotNull(actual);
        Assert.assertEquals(expected, actual);
    }

    // Test case 2: getProductPrice() method should call getProductPriceByProductId() method with correct parameters
    @Test
    public void getProductPrice_shouldCallCorrectFeignClientMethod() {
        // Arrange mock data
        Long productId = 1L;
        String areaCode = "ABC";
        String financeRuleCode = "123";
        ProductPriceVO expected = new ProductPriceVO();
        Product product = new Product();
        product.setGoodsIsDistribute(1);
        expected.setProduct(product);        expected.setProductExtend(new ProductExtend());
        expected.setProductFinanceRuleLabel(new ProductFinanceRuleLabel());
        expected.setProductPriceBranchRange(new ProductPriceBranchRange());
        expected.setGoods(new Goods());
        expected.setJudgeFlag(false);
        expected.setCompanyName("");
        expected.setProductReasonTypeEnum(ProductReasonTypeEnum.PRODUCT_FOREIGN_NO);
        expected.setCouponList(Lists.newArrayList());
        expected.setEffectiveStock(0);
        expected.setProductSpecJson("");
        expected.setMfrCoopFlag(0);

        Mockito.when(productFeignClient.getProductPriceByProductId(productId, areaCode, financeRuleCode)).thenReturn(expected);

        // Act
        orderLocalUtils.getProductPrice(productId, areaCode, financeRuleCode);

        // Assert
        Mockito.verify(productFeignClient).getProductPriceByProductId(productId, areaCode, financeRuleCode);
    }

    @Test
    public void testSubMobileLastFourWithValidInput() {
        String mobile = "1234567890";
        String lastFour = orderLocalUtils.subMobileLastFour(mobile);
        assertEquals("7890", lastFour);
    }

    @Test
    public void testSubMobileLastFourWithEmptyStringInput() {
        String mobile = "";
        String lastFour = orderLocalUtils.subMobileLastFour(mobile);
        assertEquals("", lastFour);
    }

    @Test
    public void testSubMobileLastFourWithShortStringInput() {
        String mobile = "1234";
        String lastFour = orderLocalUtils.subMobileLastFour(mobile);
        assertEquals("", lastFour);
    }

    @Test
    public void testSubMobileLastFourWithNullInput() {
        String lastFour = orderLocalUtils.subMobileLastFour(null);
        assertEquals("", lastFour);
    }

}