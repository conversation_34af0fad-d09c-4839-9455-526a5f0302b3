package com.cfpamf.ms.mallorder.service.impl;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.conditions.query.ChainQuery;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.common.ms.vo.PageVO;
import com.cfpamf.ms.mallgoods.facade.api.ProductFeignClient;
import com.cfpamf.ms.mallorder.common.enums.PayMethodEnum;
import com.cfpamf.ms.mallorder.common.util.OrderLocalUtils;
import com.cfpamf.ms.mallorder.dto.*;
import com.cfpamf.ms.mallorder.integration.cust.CustomerIntegration;
import com.cfpamf.ms.mallorder.integration.facade.BmsIntegration;
import com.cfpamf.ms.mallorder.integration.facade.LoanProductFacade;
import com.cfpamf.ms.mallorder.integration.facade.MallPaymentFacade;
import com.cfpamf.ms.mallorder.integration.loan.LoanPayIntegration;
import com.cfpamf.ms.mallorder.mapper.OrderPresellMapper;
import com.cfpamf.ms.mallorder.mapper.PayMethodMapper;
import com.cfpamf.ms.mallorder.model.BankTransferModel;
import com.cfpamf.ms.mallorder.model.OrderPayModel;
import com.cfpamf.ms.mallorder.po.BzOrderPayBlacklistPO;
import com.cfpamf.ms.mallorder.po.BzOrderPayCategoryPO;
import com.cfpamf.ms.mallorder.po.BzOrderPayWhitelistPO;
import com.cfpamf.ms.mallorder.po.PayMethodPO;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.vo.BzOrderPayBlacklistVO;
import com.cfpamf.ms.mallorder.vo.PayMethodPageVO;
import com.cfpamf.ms.mallshop.api.StoreFeignClient;
import com.cfpamf.ms.mallshop.dto.PayMethodStoreQueryDTO;
import com.cfpamf.ms.mallshop.resp.PaymethodStoreVO;
import com.cfpamf.ms.mallshop.vo.NewStoreVo;
import com.google.common.collect.Lists;
import com.slodon.bbc.core.response.JsonResult;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.modules.junit4.PowerMockRunner;
import org.slf4j.Logger;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/5/30 09:52
 * @Version 1.0
 */
@RunWith(PowerMockRunner.class)
public class PayMethodServiceImplTest {
    @Mock
    IService iService;
    @Mock
    Map<String, PayMethodEnum> CUST_PAY_CODE_MAP;
    @Mock
    PayMethodMapper payMethodMapper;
    @Mock
    IBzPayMethodTrackService payMethodTrackService;
    @Mock
    StoreFeignClient storeFeignClient;
    @Mock
    LoanProductFacade loanProductFacade;
    @Mock
    IBzOrderPayBlacklistService iBzOrderPayBlacklistService;
    @Mock
    IBzOrderPayWhitelistService iBzOrderPayWhitelistService;
    @Mock
    IBzOrderPayCategoryService orderPayCategoryService;
    @Mock
    IPayMethodService iPayMethodService;
    @Mock
    CustomerIntegration customerIntegration;
    @Mock
    OrderPayModel orderPayModel;
    @Mock
    IOrderService iOrderService;
    @Mock
    IOrderProductService iOrderProductService;
    @Mock
    ProductFeignClient productFeignClient;
    @Mock
    IOrderExtendService orderExtendService;
    @Mock
    LoanPayIntegration loanPayIntegration;
    @Mock
    BankTransferModel bankTransferModel;
    @Mock
    IOrderProductService orderProductService;
    @Mock
    OrderPresellMapper orderPresellMapper;
    @Mock
    OrderLocalUtils orderLocalUtils;
    @Mock
    MallPaymentFacade mallPaymentFacade;
    @Mock
    BmsIntegration bmsIntegration;
    @Mock
    Logger log;
    @Mock
    BaseMapper baseMapper;
    //Field entityClass of type Class - was not mocked since Mockito doesn't mock a Final class when 'mock-maker-inline' option is not set
    //Field mapperClass of type Class - was not mocked since Mockito doesn't mock a Final class when 'mock-maker-inline' option is not set
    @InjectMocks
    PayMethodServiceImpl payMethodServiceImpl;
    @Mock
    ChainQuery chainQuery;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), BzOrderPayCategoryPO.class);
    }

    @Test
    public void testConvertPayModelToPayMethod() throws Exception {
        PayMethodEnum result = PayMethodServiceImpl.convertPayModelToPayMethod("enjoypay");
        Assert.assertEquals(PayMethodEnum.ENJOY_PAY, result);
    }

    @Test
    public void testInsertPayMethodMerchant() throws Exception {
        PaymethodStoreVO paymethodStoreVO = new PaymethodStoreVO();
        paymethodStoreVO.setStoreId("1232");
        paymethodStoreVO.setStoreName("测试一下");
        paymethodStoreVO.setIsOwnStore(0);


        JsonResult<Page<PaymethodStoreVO>> pageJsonResult = new JsonResult<>();
        pageJsonResult.setData(null);

        PayMethodStoreQueryDTO payMethodStoreQueryDTO = new PayMethodStoreQueryDTO();
        payMethodStoreQueryDTO.setMerchantIds(Lists.newArrayList());
        payMethodStoreQueryDTO.setStoreName("");
        payMethodStoreQueryDTO.setIsOwnStore(0);
        payMethodStoreQueryDTO.setCurrent(0);
        payMethodStoreQueryDTO.setPageSize(0);
        // Mockito.when(storeFeignClient.pagePayMethodStoreList(any())).thenReturn(pageJsonResult);
        String errorMsg = "1";
        try {
            JsonResult result = payMethodServiceImpl.insertPayMethodMerchant(null);
        } catch (Exception e) {
            errorMsg = "失败";
        }
        Assert.assertNotNull(errorMsg);

    }

    @Test
    public void testPagePayMethodList() throws Exception {
        PayMethodPO payMethodPO = new PayMethodPO();
        payMethodPO.setId(0L);
        payMethodPO.setPayMethodCode("");
        payMethodPO.setPayMethodName("");
        payMethodPO.setPayMethodDesc("");
        payMethodPO.setPayMethodStatus(0);
        payMethodPO.setMinAmount(new BigDecimal("0"));
        payMethodPO.setSupportOrderType("");
        payMethodPO.setSupportMerchant("");
        payMethodPO.setSupportOrderChannel("");
        payMethodPO.setIsLoanPay(0);
        payMethodPO.setLoanCode("");
        payMethodPO.setLoanProduct("");
        payMethodPO.setLoanProductName("");
        payMethodPO.setSort(0);
        payMethodPO.setAllowUserFlag(0);
        payMethodPO.setCreateTime(new Date());
        payMethodPO.setUpdateTime(new Date());
        payMethodPO.setCreateBy("");
        payMethodPO.setUpdateBy("");
        payMethodPO.setEnabledFlag(false);
        payMethodPO.setPayIcon("");


        when(payMethodMapper.pagePayMethodList(any(), any())).thenReturn(Arrays.asList(payMethodPO));

        JsonResult<Page<PayMethodPageVO>> result = payMethodServiceImpl.pagePayMethodList(new PayMethodReq());
        Assert.assertNotNull(result);
    }

    @Test
    public void testSavePayMethod() throws Exception {
        PayMethodDTO payMethodDTO = new PayMethodDTO();
        payMethodDTO.setId(1L);
        payMethodDTO.setPayMethodStatus(0);
        payMethodDTO.setMinAmount(new BigDecimal("0"));
        payMethodDTO.setSupportOrderType("");
        payMethodDTO.setSupportOrderChannel("");
        payMethodDTO.setIsLoanPay(0);
        payMethodDTO.setLoanCode("");
        payMethodDTO.setLoanProduct("");
        payMethodDTO.setLoanProductName("");
        payMethodDTO.setSort(0);
        payMethodDTO.setPayIconPath("");
        payMethodDTO.setOperateUserName("");

        Mockito.when(baseMapper.insert(any())).thenReturn(1);
        Mockito.when(baseMapper.updateById(any())).thenReturn(1);
        Mockito.when(payMethodTrackService.save(any())).thenReturn(true);
        Mockito.when(iPayMethodService.updateById(any())).thenReturn(true);

        JsonResult result = payMethodServiceImpl.savePayMethod(payMethodDTO);
        Assert.assertEquals("保存成功", result.getMsg());

    }

    @Test
    public void testInsertMerchant() throws Exception {
        PayMethodPO payMethodPO = new PayMethodPO();
        payMethodPO.setId(0L);
        payMethodPO.setPayMethodCode("");
        payMethodPO.setPayMethodName("");
        payMethodPO.setPayMethodDesc("");
        payMethodPO.setPayMethodStatus(0);
        payMethodPO.setMinAmount(new BigDecimal("0"));
        payMethodPO.setSupportOrderType("");
        payMethodPO.setSupportMerchant("");
        payMethodPO.setSupportOrderChannel("");
        payMethodPO.setIsLoanPay(0);
        payMethodPO.setLoanCode("");
        payMethodPO.setLoanProduct("");
        payMethodPO.setLoanProductName("");
        payMethodPO.setSort(0);
        payMethodPO.setAllowUserFlag(0);
        payMethodPO.setCreateTime(new Date());
        payMethodPO.setUpdateTime(new Date());
        payMethodPO.setCreateBy("");
        payMethodPO.setUpdateBy("");
        payMethodPO.setEnabledFlag(false);
        payMethodPO.setPayIcon("");



        PayMethodMerchantDTO payMethodMerchantDTO = new PayMethodMerchantDTO();
        payMethodMerchantDTO.setPayMethodId(0L);
        payMethodMerchantDTO.setOperateUserName("");
        payMethodMerchantDTO.setMerchantIds(Arrays.asList("21","234"));
        Mockito.when(baseMapper.selectOne(any())).thenReturn(payMethodPO);

        JsonResult result = payMethodServiceImpl.insertMerchant(payMethodMerchantDTO);
        Assert.assertEquals("新增成功", result.getMsg());
    }


    @Test
    public void testDeleteMerchant() throws Exception {

        PayMethodPO payMethodPO = new PayMethodPO();
        payMethodPO.setId(0L);
        payMethodPO.setPayMethodCode("");
        payMethodPO.setPayMethodName("");
        payMethodPO.setPayMethodDesc("");
        payMethodPO.setPayMethodStatus(0);
        payMethodPO.setMinAmount(new BigDecimal("0"));
        payMethodPO.setSupportOrderType("");
        payMethodPO.setSupportMerchant("");
        payMethodPO.setSupportOrderChannel("");
        payMethodPO.setIsLoanPay(0);
        payMethodPO.setLoanCode("");
        payMethodPO.setLoanProduct("");
        payMethodPO.setLoanProductName("");
        payMethodPO.setSort(0);
        payMethodPO.setAllowUserFlag(0);
        payMethodPO.setCreateTime(new Date());
        payMethodPO.setUpdateTime(new Date());
        payMethodPO.setCreateBy("");
        payMethodPO.setUpdateBy("");
        payMethodPO.setEnabledFlag(false);
        payMethodPO.setPayIcon("");

        PayMethodMerchantDTO payMethodMerchantDTO = new PayMethodMerchantDTO();
        payMethodMerchantDTO.setPayMethodId(0L);
        payMethodMerchantDTO.setMerchantIds(Lists.newArrayList("1232", "312"));
        payMethodMerchantDTO.setOperateUserName("");
        Mockito.when(baseMapper.selectOne(any())).thenReturn(payMethodPO);

        JsonResult result = payMethodServiceImpl.deleteMerchant(payMethodMerchantDTO);
        Assert.assertEquals("删除成功", result.getMsg());
    }

    @Test
    public void testDeleteGoods() throws Exception {
        BzOrderPayBlacklistPO bzOrderPayBlacklistPO = new BzOrderPayBlacklistPO();
        bzOrderPayBlacklistPO.setPayId(0L);
        bzOrderPayBlacklistPO.setProductId(0L);
        bzOrderPayBlacklistPO.setGoodsName("");
        bzOrderPayBlacklistPO.setSpecValueIds("");
        bzOrderPayBlacklistPO.setSpecValues("");
        bzOrderPayBlacklistPO.setStoreId(0L);
        bzOrderPayBlacklistPO.setStoreName("");
        bzOrderPayBlacklistPO.setCreateBy("");
        bzOrderPayBlacklistPO.setUpdateBy("");
        bzOrderPayBlacklistPO.setCategoryId1(0);
        bzOrderPayBlacklistPO.setCategoryId2(0);
        bzOrderPayBlacklistPO.setCategoryId3(0);
        bzOrderPayBlacklistPO.setCategoryName1("");
        bzOrderPayBlacklistPO.setCategoryName2("");
        bzOrderPayBlacklistPO.setCategoryName3("");
        bzOrderPayBlacklistPO.setGoodsId(0L);
        bzOrderPayBlacklistPO.setCategoryPath("");
        bzOrderPayBlacklistPO.setCreateTime(new Date());
        bzOrderPayBlacklistPO.setUpdateTime(new Date());
        bzOrderPayBlacklistPO.setEnabledFlag(0);
        bzOrderPayBlacklistPO.setId(0L);


        List<BzOrderPayBlacklistPO> bzOrderPayBlacklistPOS = new ArrayList<>();
        bzOrderPayBlacklistPOS.add(bzOrderPayBlacklistPO);
        PayMethodGoodsDeleteDTO payMethodGoodsDeleteDTO = new PayMethodGoodsDeleteDTO();
        payMethodGoodsDeleteDTO.setPayMethodId(0L);
        payMethodGoodsDeleteDTO.setIds(Lists.newArrayList(12L, 4332L));
        payMethodGoodsDeleteDTO.setOperateUserName("");
        Mockito.when(iBzOrderPayBlacklistService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper(baseMapper));
        Mockito.when(baseMapper.selectList(any())).thenReturn(bzOrderPayBlacklistPOS);
        Mockito.when(baseMapper.insert(any())).thenReturn(1);

        JsonResult jsonResult = payMethodServiceImpl.deleteGoods(payMethodGoodsDeleteDTO);
        Assert.assertEquals("删除成功", jsonResult.getMsg());
    }

    @Test
    public void testInsertCategory() throws Exception {
        PayMethodCategoryDTO payMethodCategoryDTO = new PayMethodCategoryDTO();
        payMethodCategoryDTO.setPayMethodId(0L);
        payMethodCategoryDTO.setCategoryListDTOS(Lists.newArrayList("12312_11_12"));
        payMethodCategoryDTO.setOperateUserName("");

        PayMethodPO payMethodPO = new PayMethodPO();
        payMethodPO.setId(0L);
        payMethodPO.setPayMethodCode("");
        payMethodPO.setPayMethodName("");
        payMethodPO.setPayMethodDesc("");
        payMethodPO.setPayMethodStatus(0);
        payMethodPO.setMinAmount(new BigDecimal("0"));
        payMethodPO.setSupportOrderType("");
        payMethodPO.setSupportMerchant("");
        payMethodPO.setSupportOrderChannel("");
        payMethodPO.setIsLoanPay(0);
        payMethodPO.setLoanCode("");
        payMethodPO.setLoanProduct("");
        payMethodPO.setLoanProductName("");
        payMethodPO.setSort(0);
        payMethodPO.setAllowUserFlag(0);
        payMethodPO.setCreateTime(new Date());
        payMethodPO.setUpdateTime(new Date());
        payMethodPO.setCreateBy("");
        payMethodPO.setUpdateBy("");
        payMethodPO.setEnabledFlag(false);
        payMethodPO.setPayIcon("");

        BzOrderPayCategoryPO bzOrderPayCategoryPO = new BzOrderPayCategoryPO();
        bzOrderPayCategoryPO.setId(0L);
        bzOrderPayCategoryPO.setPayId(0L);
        bzOrderPayCategoryPO.setCategoryId(0);
        bzOrderPayCategoryPO.setCategoryGrade(0);
        bzOrderPayCategoryPO.setCategoryName("");
        bzOrderPayCategoryPO.setCreateTime(new Date());
        bzOrderPayCategoryPO.setUpdateTime(new Date());
        bzOrderPayCategoryPO.setCreateBy("");
        bzOrderPayCategoryPO.setUpdateBy("");
        bzOrderPayCategoryPO.setEnabledFlag(0);

        List<BzOrderPayCategoryPO> list = new ArrayList<>();
        list.add(bzOrderPayCategoryPO);

        Mockito.when(orderPayCategoryService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper(baseMapper));
        Mockito.when(baseMapper.selectList(any())).thenReturn(list);
        Mockito.when(baseMapper.insert(any())).thenReturn(1);
        Mockito.when(baseMapper.selectOne(any())).thenReturn(payMethodPO);

        JsonResult jsonResult = payMethodServiceImpl.insertCategory(payMethodCategoryDTO);
        Assert.assertEquals("保存成功", jsonResult.getMsg());

    }
    @Test
    public void testQueryCategory() throws Exception {
        PayMethodPO payMethodPO = new PayMethodPO();
        payMethodPO.setId(0L);
        payMethodPO.setPayMethodCode("");
        payMethodPO.setPayMethodName("");
        payMethodPO.setPayMethodDesc("");
        payMethodPO.setPayMethodStatus(0);
        payMethodPO.setMinAmount(new BigDecimal("0"));
        payMethodPO.setSupportOrderType("");
        payMethodPO.setSupportMerchant("");
        payMethodPO.setSupportOrderChannel("");
        payMethodPO.setIsLoanPay(0);
        payMethodPO.setLoanCode("");
        payMethodPO.setLoanProduct("");
        payMethodPO.setLoanProductName("");
        payMethodPO.setSort(0);
        payMethodPO.setAllowUserFlag(0);
        payMethodPO.setCreateTime(new Date());
        payMethodPO.setUpdateTime(new Date());
        payMethodPO.setCreateBy("");
        payMethodPO.setUpdateBy("");
        payMethodPO.setEnabledFlag(false);
        payMethodPO.setPayIcon("");

        BzOrderPayCategoryPO bzOrderPayCategoryPO = new BzOrderPayCategoryPO();
        bzOrderPayCategoryPO.setId(0L);
        bzOrderPayCategoryPO.setPayId(0L);
        bzOrderPayCategoryPO.setCategoryId(0);
        bzOrderPayCategoryPO.setCategoryGrade(0);
        bzOrderPayCategoryPO.setCategoryName("");
        bzOrderPayCategoryPO.setCreateTime(new Date());
        bzOrderPayCategoryPO.setUpdateTime(new Date());
        bzOrderPayCategoryPO.setCreateBy("");
        bzOrderPayCategoryPO.setUpdateBy("");
        bzOrderPayCategoryPO.setEnabledFlag(0);


        List<BzOrderPayCategoryPO> bzOrderPayCategoryPOS = new ArrayList<>();
        bzOrderPayCategoryPOS.add(bzOrderPayCategoryPO);

        Mockito.when(baseMapper.selectOne(any())).thenReturn(payMethodPO);
        Mockito.when(orderPayCategoryService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper(baseMapper));
        Mockito.when(baseMapper.selectList(any())).thenReturn(bzOrderPayCategoryPOS);

        JsonResult<List<String>> result = payMethodServiceImpl.queryCategory("payMethodId", null);
        Assert.assertNotNull(result);
    }

    @Test
    public void testPageGoodsList() throws Exception {
        PayMethodGoodsReq payMethodGoodsReq = new PayMethodGoodsReq();
        payMethodGoodsReq.setPayMethodId(0L);
        payMethodGoodsReq.setName("");
        payMethodGoodsReq.setStoreId("");
        payMethodGoodsReq.setStoreName("");
        payMethodGoodsReq.setGoodsId(0L);
        payMethodGoodsReq.setSpecValues("");
        payMethodGoodsReq.setProductId(0L);
        payMethodGoodsReq.setCategoryId1(0);
        payMethodGoodsReq.setCategoryId2(0);
        payMethodGoodsReq.setCategoryId3(0);
        payMethodGoodsReq.setCurrent(0);
        payMethodGoodsReq.setPageSize(0);


        Page<BzOrderPayBlacklistPO> objectPage = new Page<>();
        objectPage.setRecords(Arrays.asList(new BzOrderPayBlacklistPO()));
        objectPage.setTotal(1L);
        objectPage.setPages(1L);
        Mockito.when(iBzOrderPayBlacklistService.page(any(), any())).thenReturn(objectPage);

        JsonResult<PageVO<BzOrderPayBlacklistVO>> result = payMethodServiceImpl.pageGoodsList(payMethodGoodsReq);
        Assert.assertNotNull(result);
    }

    @Test
    public void testGoodsList() throws Exception {
        PayMethodGoodsReq payMethodGoodsReq = new PayMethodGoodsReq();
        payMethodGoodsReq.setPayMethodId(0L);
        payMethodGoodsReq.setName("");
        payMethodGoodsReq.setStoreId("");
        payMethodGoodsReq.setStoreName("");
        payMethodGoodsReq.setGoodsId(0L);
        payMethodGoodsReq.setSpecValues("");
        payMethodGoodsReq.setProductId(0L);
        payMethodGoodsReq.setCategoryId1(0);
        payMethodGoodsReq.setCategoryId2(0);
        payMethodGoodsReq.setCategoryId3(0);
        payMethodGoodsReq.setCurrent(0);
        payMethodGoodsReq.setPageSize(0);

        BzOrderPayBlacklistPO bzOrderPayBlacklistPO = new BzOrderPayBlacklistPO();
        bzOrderPayBlacklistPO.setPayId(0L);
        bzOrderPayBlacklistPO.setProductId(0L);
        bzOrderPayBlacklistPO.setGoodsName("");
        bzOrderPayBlacklistPO.setSpecValueIds("");
        bzOrderPayBlacklistPO.setSpecValues("");
        bzOrderPayBlacklistPO.setStoreId(0L);
        bzOrderPayBlacklistPO.setStoreName("");
        bzOrderPayBlacklistPO.setCreateBy("");
        bzOrderPayBlacklistPO.setUpdateBy("");
        bzOrderPayBlacklistPO.setCategoryId1(0);
        bzOrderPayBlacklistPO.setCategoryId2(0);
        bzOrderPayBlacklistPO.setCategoryId3(0);
        bzOrderPayBlacklistPO.setCategoryName1("");
        bzOrderPayBlacklistPO.setCategoryName2("");
        bzOrderPayBlacklistPO.setCategoryName3("");
        bzOrderPayBlacklistPO.setGoodsId(0L);
        bzOrderPayBlacklistPO.setCategoryPath("");
        bzOrderPayBlacklistPO.setCreateTime(new Date());
        bzOrderPayBlacklistPO.setUpdateTime(new Date());
        bzOrderPayBlacklistPO.setEnabledFlag(0);
        bzOrderPayBlacklistPO.setId(0L);

        List<BzOrderPayBlacklistPO> list = new ArrayList<>();
        list.add(bzOrderPayBlacklistPO);

        Mockito.when(iBzOrderPayBlacklistService.list()).thenReturn(list);
        Mockito.when(baseMapper.selectList(any())).thenReturn(list);

        List<? extends Object> result = payMethodServiceImpl.goodsList(payMethodGoodsReq);
        Assert.assertNull(result);
    }

    @Test
    public void testInitStore() throws Exception {
        BzOrderPayWhitelistPO bzOrderPayWhitelistPO = new BzOrderPayWhitelistPO();
        bzOrderPayWhitelistPO.setPayId(0L);
        bzOrderPayWhitelistPO.setStoreId("");
        bzOrderPayWhitelistPO.setStoreName("");
        bzOrderPayWhitelistPO.setRegisterPhone("");
        bzOrderPayWhitelistPO.setIdentityName("");
        bzOrderPayWhitelistPO.setRecommendStoreId("");
        bzOrderPayWhitelistPO.setRecommendStoreName("");
        bzOrderPayWhitelistPO.setIsOwnStore(0);
        bzOrderPayWhitelistPO.setCreateBy("");
        bzOrderPayWhitelistPO.setUpdateBy("");
        bzOrderPayWhitelistPO.setCreateTime(new Date());
        bzOrderPayWhitelistPO.setUpdateTime(new Date());
        bzOrderPayWhitelistPO.setEnabledFlag(0);
        bzOrderPayWhitelistPO.setId(0L);

        List<BzOrderPayWhitelistPO> objects = new ArrayList<>();
        objects.add(bzOrderPayWhitelistPO);
        Mockito.when(iBzOrderPayWhitelistService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper(baseMapper));
        Mockito.when(baseMapper.selectList(any())).thenReturn(objects);

        com.slodon.bbc.core.response.PageVO<NewStoreVo> storeVoPageVO = new com.slodon.bbc.core.response.PageVO<>();
        NewStoreVo newStoreVo = new NewStoreVo();
        newStoreVo.setStoreId(1L);
        storeVoPageVO.setList(Collections.singletonList(newStoreVo));
        when(storeFeignClient.listAllStore(any())).thenReturn(storeVoPageVO);

        Boolean result = payMethodServiceImpl.initStore();
    }

    @Test
    public void testInsertCategoryTmp() throws Exception {
        PayMethodCategoryListDTO payMethodCategoryListDTO = new PayMethodCategoryListDTO();
        payMethodCategoryListDTO.setCategoryId(0);
        payMethodCategoryListDTO.setCategoryGrade(0);
        payMethodCategoryListDTO.setCategoryName("");

        JsonResult<Boolean> result = payMethodServiceImpl.insertCategoryTmp(payMethodCategoryListDTO);
        Assert.assertNotNull(result);
    }
/*
    @Test
    public void testSyncMerchant() throws Exception {
        when(mallPaymentFacade.sync(any())).thenReturn(null);

        JsonResult<Boolean> result = payMethodServiceImpl.syncMerchant("storeId", "beginDate", "endDate");
        Assert.assertEquals(null, result);
    }

    @Test
    public void testSyncMerchantIntraday() throws Exception {
        when(mallPaymentFacade.sync(any())).thenReturn(null);

        boolean result = payMethodServiceImpl.syncMerchantIntraday();
        Assert.assertEquals(true, result);
    }

    @Test
    public void testInitGoods() throws Exception {
        Boolean result = payMethodServiceImpl.initGoods();
        Assert.assertEquals(Boolean.TRUE, result);
    }

    @Test
    public void testPageMerchantV2List() throws Exception {
        JsonResult<PageVO<BzOrderPayWhitelistVO>> result = payMethodServiceImpl.pageMerchantV2List(new PayMethodMerchantReq());
        Assert.assertEquals(null, result);
    }

    @Test
    public void testInsertMerchantV2() throws Exception {
        JsonResult result = payMethodServiceImpl.insertMerchantV2(new PayMethodMerchantV2DTO());
        Assert.assertEquals(null, result);
    }

    @Test
    public void testDeleteMerchantV2() throws Exception {
        JsonResult result = payMethodServiceImpl.deleteMerchantV2(new PayMethodGoodsDeleteDTO());
        Assert.assertEquals(null, result);
    }

    @Test
    public void testMerchantClean() throws Exception {
        JsonResult result = payMethodServiceImpl.merchantClean();
        Assert.assertEquals(null, result);
    }

    @Test
    public void testListPayMethodTrack() throws Exception {
        JsonResult<List<BzPayMethodTrackVO>> result = payMethodServiceImpl.listPayMethodTrack("payMethodId");
        Assert.assertEquals(null, result);
    }

    @Test
    public void testListPayMethodTrackV2() throws Exception {
        JsonResult<PageVO<BzPayMethodTrackVO>> result = payMethodServiceImpl.listPayMethodTrackV2("payMethodId", Integer.valueOf(0), Integer.valueOf(0));
        Assert.assertEquals(null, result);
    }

    @Test
    public void testGetProductListByQuery() throws Exception {
        when(loanProductFacade.getProductListByQuery(any())).thenReturn(null);

        JsonResult<List<ProductVo>> result = payMethodServiceImpl.getProductListByQuery(null);
        Assert.assertEquals(null, result);
    }

    @Test
    public void testQueryProductEssential() throws Exception {
        when(loanProductFacade.queryProductEssential(any())).thenReturn(null);

        JsonResult<PageBean<RepaymentRelationVo>> result = payMethodServiceImpl.queryProductEssential(null);
        Assert.assertEquals(null, result);
    }

    @Test
    public void testGetPayMethodVOS() throws Exception {
        when(customerIntegration.queryLimitList(anyString())).thenReturn(Arrays.<CustCreditLimitVo>asList(null));
        when(orderPayModel.getOrderPayByPaySn(anyString())).thenReturn(new OrderPayPO());
        when(orderExtendService.getOrderExtendByOrderSn(anyString())).thenReturn(new OrderExtendPO());
        when(loanPayIntegration.isPreconditionCheckAllow(any())).thenReturn(true);
        when(bankTransferModel.queryLastByMemberId(anyInt())).thenReturn(new BzBankTransferPO());
        when(bankTransferModel.queryLastByPaymentUserNo(anyString())).thenReturn(new PayerLargePayacctVO());
        when(orderPresellMapper.getPreSellOrderDetailByOrderSn(anyString())).thenReturn(Arrays.<OrderPresellPO>asList(new OrderPresellPO()));

        List<PayMethodVO> result = payMethodServiceImpl.getPayMethodVOS("paySn", OrderCreateChannel.H5, null, "mobiletype", "utdid", "imei", null);
        Assert.assertEquals(Arrays.<PayMethodVO>asList(new PayMethodVO()), result);
    }

    @Test
    public void testGetPayMethodVOS3() throws Exception {
        when(customerIntegration.userBaseInfo(anyString())).thenReturn(null);
        when(orderPayModel.getOrderPayByPaySn(anyString())).thenReturn(new OrderPayPO());
        when(orderExtendService.getOrderExtendByOrderSn(anyString())).thenReturn(new OrderExtendPO());
        when(loanPayIntegration.getAvailablePayMode(any())).thenReturn(null);
        when(bankTransferModel.queryLastByMemberId(anyInt())).thenReturn(new BzBankTransferPO());
        when(bankTransferModel.queryLastByPaymentUserNo(anyString())).thenReturn(new PayerLargePayacctVO());
        when(orderPresellMapper.getPreSellOrderDetailByOrderSn(anyString())).thenReturn(Arrays.<OrderPresellPO>asList(new OrderPresellPO()));
        when(orderLocalUtils.getLongProductMap(any())).thenReturn(new HashMap<Long, Product>() {{
            put(Long.valueOf(1), null);
        }});
        when(bmsIntegration.listDictionaryItemsByTypeCode(anyString(), anyInt())).thenReturn(Arrays.<DictionaryItemVO>asList(null));

        List<PayMethodVO3> result = payMethodServiceImpl.getPayMethodVOS3("paySn", OrderCreateChannel.H5, null, "mobiletype", "utdid", "imei", null, "wifi", "returnUrl", "chan_nel", "ctversion");
        Assert.assertEquals(Arrays.<PayMethodVO3>asList(new PayMethodVO3()), result);
    }

    @Test
    public void testInsertPayMethodStore() throws Exception {
        JsonResult<String> result = payMethodServiceImpl.insertPayMethodStore(new PayMethodStoreDTO());
        Assert.assertEquals(null, result);
    }

    @Test
    public void testDeletePayMethodStore() throws Exception {
        JsonResult<String> result = payMethodServiceImpl.deletePayMethodStore(new PayMethodStoreDTO());
        Assert.assertEquals(null, result);
    }

    @Test
    public void testListPayMethodByCode() throws Exception {
        List<PayMethodVOV2> result = payMethodServiceImpl.listPayMethodByCode(Arrays.<String>asList("String"));
        Assert.assertEquals(Arrays.<PayMethodVOV2>asList(new PayMethodVOV2()), result);
    }

    @Test
    public void testFullIntersection() throws Exception {
        boolean result = payMethodServiceImpl.fullIntersection(Arrays.<String>asList("String"), Arrays.<String>asList("String"));
        Assert.assertEquals(true, result);
    }

    @Test
    public void testFullIntersection2() throws Exception {
        boolean result = payMethodServiceImpl.fullIntersection2(Arrays.<Long>asList(Long.valueOf(1)), Arrays.<Long>asList(Long.valueOf(1)));
        Assert.assertEquals(true, result);
    }

    @Test
    public void testFullIntersection3() throws Exception {
        boolean result = payMethodServiceImpl.fullIntersection3(Arrays.<Integer>asList(Integer.valueOf(0)), Arrays.<Integer>asList(Integer.valueOf(0)));
        Assert.assertEquals(true, result);
    }*/
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme