package com.cfpamf.ms.mallorder.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.beust.jcommander.internal.Lists;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.common.constant.OrdersAfsConst;
import com.cfpamf.ms.mallorder.dto.OrderAfterDTO;
import com.cfpamf.ms.mallorder.model.OrderProductModel;
import com.cfpamf.ms.mallorder.model.OrderReturnModel;
import com.cfpamf.ms.mallorder.po.OrderProductPO;
import com.cfpamf.ms.mallorder.po.OrderReturnPO;
import com.cfpamf.ms.mallorder.req.admin.AdminAfterSaleListRequest;
import com.cfpamf.ms.mallorder.request.OrderReturnExample;
import com.cfpamf.ms.mallorder.service.impl.OrderAfterServiceImpl;
import com.cfpamf.ms.mallorder.vo.AfsOrderProductVO;
import com.cfpamf.ms.mallorder.vo.DistributionOrderInfoVO;
import com.cfpamf.ms.mallorder.vo.OrderReturnVOV2;
import com.cfpamf.ms.mallshop.api.StoreIsolateWhiteListFeignClient;
import com.cfpamf.ms.mallshop.enums.WhiteListEnum;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.response.PageVO;
import com.slodon.bbc.core.response.PagerInfo;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

@RunWith(SpringJUnit4ClassRunner.class)
public class IOrderAfterServiceTest {

    private static final PagerInfo PAGER = new PagerInfo();
    @InjectMocks
    private OrderAfterServiceImpl orderAfterService;
    @Mock
    private IOrderProductService orderProductService;
    @Mock
    private OrderReturnModel orderReturnModel;
    @Mock
    private OrderProductModel orderProductModel;

    @Mock
    private StoreIsolateWhiteListFeignClient storeIsolateWhiteListFeignClient;

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void isAllReturnTrue() {
        List<OrderAfterDTO.AfterProduct> afsDTOs = afsDTOs();

        List<OrderProductPO> orderProductPOS = orderProductPOS();

        Mockito.when(orderProductService.list(Mockito.any())).thenReturn(orderProductPOS);

        assertTrue(orderAfterService.isAllReturnApplied(afsDTOs, ""));
    }

    @Test
    public void isAllReturnFalse() {

        List<OrderAfterDTO.AfterProduct> afsDTOs = afsDTOs();

        List<OrderProductPO> orderProductPOS = orderProductPOS();
        orderProductPOS.get(0).setProductNum(orderProductPOS.get(0).getProductNum() + 1);

        Mockito.when(orderProductService.list(Mockito.any())).thenReturn(orderProductPOS);

        assertFalse(orderAfterService.isAllReturnApplied(afsDTOs, ""));
    }

    @Test(expected = BusinessException.class)
    public void isAllReturnException() {

        List<OrderAfterDTO.AfterProduct> afsDTOs = afsDTOs();

        List<OrderProductPO> orderProductPOS = orderProductPOS();
        orderProductPOS.get(0).setProductNum(orderProductPOS.get(0).getProductNum() - 1);

        Mockito.when(orderProductService.list(Mockito.any())).thenReturn(orderProductPOS);
        orderAfterService.isAllReturnApplied(afsDTOs, "");
    }

    public List<OrderAfterDTO.AfterProduct> afsDTOs() {
        List<OrderAfterDTO.AfterProduct> afsDTOs = new ArrayList<>();

        OrderAfterDTO.AfterProduct afterProduct = new OrderAfterDTO.AfterProduct();
        afterProduct.setOrderProductId(1L);
        afterProduct.setAfsNum(2);
        afsDTOs.add(afterProduct);

        OrderAfterDTO.AfterProduct afterProduct2 = new OrderAfterDTO.AfterProduct();
        afterProduct2.setOrderProductId(2L);
        afterProduct2.setAfsNum(10);
        afsDTOs.add(afterProduct2);
        return afsDTOs;
    }

    public List<OrderProductPO> orderProductPOS() {
        List<OrderProductPO> orderProductPOS = new ArrayList<>();
        OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(1L);
        orderProductPO1.setProductNum(10);
        orderProductPO1.setReturnNumber(8);
        orderProductPOS.add(orderProductPO1);

        OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(2L);
        orderProductPO2.setProductNum(10);
        orderProductPO2.setReturnNumber(0);
        orderProductPOS.add(orderProductPO2);
        return orderProductPOS;
    }

    @Test
    public void testIsAllReturnApplied() {
        List<OrderAfterDTO.AfterProduct> afsDTOs = new ArrayList<>();
        OrderAfterDTO.AfterProduct afterProduct = new OrderAfterDTO.AfterProduct();
        afterProduct.setOrderProductId(1L);
        afterProduct.setReturnAmount(BigDecimal.ONE);
        afterProduct.setAfsNum(1);
        afsDTOs.add(afterProduct);
        String orderSn = "orderSn";

        OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(1L);
        orderProductPO.setProductNum(1);
        orderProductPO.setReturnNumber(0);
        when(orderProductService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.singletonList(orderProductPO));

        boolean result = orderAfterService.isAllReturnApplied(afsDTOs, orderSn);

        assertTrue(result);
    }

    @Test
    public void testAdminAfterSaleListWithAudit() {
        AdminAfterSaleListRequest request = new AdminAfterSaleListRequest();
        request.setType("audit");
        request.setState(OrdersAfsConst.RETURN_STATE_203);
        OrderReturnExample example = new OrderReturnExample();
        when(orderReturnModel.getOrderReturnListWithJoin(any(), any()))
                .thenReturn(Lists.newArrayList(new OrderReturnVOV2()));
        PageVO<OrderReturnVOV2> result = orderAfterService.adminAfterSaleList(PAGER, request);
        assertTrue(CollectionUtils.isNotEmpty(result.getList()));

        request.setState(OrdersAfsConst.RETURN_STATE_300);
        example.setState(OrdersAfsConst.RETURN_STATE_300);
        result = orderAfterService.adminAfterSaleList(PAGER, request);
        assertTrue(CollectionUtils.isNotEmpty(result.getList()));

        request.setState(null);
        example.setStateIn(OrdersAfsConst.RETURN_STATE_200 + "," + OrdersAfsConst.RETURN_STATE_203 + "," + OrdersAfsConst.RETURN_STATE_300);
        result = orderAfterService.adminAfterSaleList(PAGER, request);
        assertTrue(CollectionUtils.isNotEmpty(result.getList()));
    }

    @Test
    public void testAdminAfterSaleListWithStoreAudit() throws Exception {
        AdminAfterSaleListRequest request = new AdminAfterSaleListRequest();
        request.setType("storeAudit");
        request.setState(OrdersAfsConst.RETURN_STATE_203);
        OrderReturnExample example = new OrderReturnExample();
        when(storeIsolateWhiteListFeignClient.whiteListStoreIds(Mockito.eq(WhiteListEnum.INNER_REFUND)))
                .thenReturn(Lists.newArrayList(1L, 2L));
        when(orderReturnModel.getOrderReturnListWithJoin(any(), any()))
                .thenReturn(Lists.newArrayList(new OrderReturnVOV2()));
        PageVO<OrderReturnVOV2> result = orderAfterService.adminAfterSaleList(PAGER, request);
        assertTrue(CollectionUtils.isNotEmpty(result.getList()));

    }

    @Test
    public void testAdminAfterSaleListWithoutType() {
        AdminAfterSaleListRequest request = new AdminAfterSaleListRequest();
        OrderReturnExample example = new OrderReturnExample();
        when(orderReturnModel.getOrderReturnListWithJoin(Mockito.eq(example), Mockito.eq(PAGER)))
                .thenReturn(Lists.newArrayList(new OrderReturnVOV2()));

        request.setState(OrdersAfsConst.RETURN_STATE_100);
        example.setStateIn(OrdersAfsConst.RETURN_STATE_100 + "," + OrdersAfsConst.RETURN_STATE_101);
        PageVO<OrderReturnVOV2> result = orderAfterService.adminAfterSaleList(PAGER, request);
        assertTrue(CollectionUtils.isNotEmpty(result.getList()));

        request.setState(OrdersAfsConst.RETURN_STATE_203);
        example.setStateIn(OrdersAfsConst.RETURN_STATE_200 + "," + OrdersAfsConst.RETURN_STATE_203);
        result = orderAfterService.adminAfterSaleList(PAGER, request);
        assertTrue(CollectionUtils.isNotEmpty(result.getList()));

        request.setState(null);
        example.setState(null);
        result = orderAfterService.adminAfterSaleList(PAGER, request);
        assertTrue(CollectionUtils.isNotEmpty(result.getList()));
    }

    @Test(expected = BusinessException.class)
    public void testGetRefundableOrderProductWithNoOrderProduct() {
        // 设置模拟对象的返回值为空列表，模拟“订单不存在或没有可退商品”的情况。
        when(orderProductModel.selectRefundableOrderProduct(anyString()))
                .thenReturn(Collections.emptyList());

        orderAfterService.getRefundableOrderProduct("123", 456L, null);
    }

    @Test(expected = BusinessException.class)
    public void testGetRefundableOrderProductWithUnauthorizedMember() {
        // 设置模拟对象的返回值为包含一个订单商品的列表，模拟会员未被授权操作该订单的情况。
        List<OrderProductPO> orderProductPOList = Collections.singletonList(new OrderProductPO());
        orderProductPOList.get(0).setMemberId(1);
        Member unauthorizedMember = new Member();
        unauthorizedMember.setMemberId(2);
        when(orderProductModel.selectRefundableOrderProduct(anyString()))
                .thenReturn(orderProductPOList);

        orderAfterService.getRefundableOrderProduct("123", null, unauthorizedMember);
    }

    @Test
    public void testGetRefundableOrderProductWithAuthorizedMember() {
        // 设置模拟对象的返回值为包含一个订单商品的列表，模拟会员被授权操作该订单的情况。
        List<OrderProductPO> orderProductPOList = Collections.singletonList(new OrderProductPO());
        orderProductPOList.get(0).setMemberId(1);
        orderProductPOList.get(0).setOrderProductId(1L);
        orderProductPOList.get(0).setProductNum(1);
        orderProductPOList.get(0).setReturnNumber(0);
        Member authorizedMember = new Member();
        authorizedMember.setMemberId(1);
        when(orderProductModel.selectRefundableOrderProduct(anyString()))
                .thenReturn(orderProductPOList);

        List<AfsOrderProductVO> refundableOrderProducts = orderAfterService.getRefundableOrderProduct("123", 1L, authorizedMember);

        // 确认返回值不为空，且包含一个 AfsOrderProductVO 对象，且该 AfsOrderProductVO 对象的 orderProductId 与设置时的一致。
        Assert.assertNotNull(refundableOrderProducts);
        assertEquals(1, refundableOrderProducts.size());
        assertEquals(orderProductPOList.get(0).getOrderProductId(), refundableOrderProducts.get(0).getOrderProductId());
    }

    @Test
    public void testGetRefundableOrderProductWithNullOrderProductId() {
        // 设置模拟对象的返回值为包含两个订单商品的列表，模拟没有指定订单商品的情况。
        List<OrderProductPO> orderProductPOList = Arrays.asList(
                new OrderProductPO() {{ setOrderProductId(1L); }},
                new OrderProductPO() {{ setOrderProductId(2L); }}
        );
        orderProductPOList.get(0).setMemberId(1);
        orderProductPOList.get(0).setProductNum(1);
        orderProductPOList.get(0).setReturnNumber(0);
        orderProductPOList.get(1).setMemberId(1);
        orderProductPOList.get(1).setProductNum(1);
        orderProductPOList.get(1).setReturnNumber(0);
        when(orderProductModel.selectRefundableOrderProduct(anyString()))
                .thenReturn(orderProductPOList);

        List<AfsOrderProductVO> refundableOrderProducts = orderAfterService.getRefundableOrderProduct("123", null, null);

        // 确认返回值不为空，且包含两个 AfsOrderProductVO 对象，且第一个对象的 orderProductId 与设置时的一致。
        Assert.assertNotNull(refundableOrderProducts);
        assertEquals(2, refundableOrderProducts.size());
        assertEquals(orderProductPOList.get(0).getOrderProductId(), refundableOrderProducts.get(0).getOrderProductId());
    }

    @Test
    public void testBalanceReminder_whenOrderCommissionIsNull_thenDoNotDistribute() {
        String afsSn = "123456";
        OrderReturnPO orderReturnPO = new OrderReturnPO();
        when(orderReturnModel.getOrderReturnByAfsSn(afsSn)).thenReturn(orderReturnPO);

        DistributionOrderInfoVO result = orderAfterService.balanceReminder(afsSn);

        assertFalse(result.isDistributionOrder());
        assertEquals(BigDecimal.ZERO, result.getDistributionAmount());
    }

    @Test
    public void testBalanceReminder_whenOrderCommissionIsZero_thenDoNotDistribute() {
        String afsSn = "123456";
        OrderReturnPO orderReturnPO = new OrderReturnPO();
        orderReturnPO.setOrderCommission(BigDecimal.ZERO);
        when(orderReturnModel.getOrderReturnByAfsSn(afsSn)).thenReturn(orderReturnPO);

        DistributionOrderInfoVO result = orderAfterService.balanceReminder(afsSn);

        assertFalse(result.isDistributionOrder());
        assertEquals(BigDecimal.ZERO, result.getDistributionAmount());
    }

    @Test
    public void testBalanceReminder_whenOrderCommissionGreaterThanZero_thenDistribute() {
        String afsSn = "123456";
        BigDecimal commission = BigDecimal.valueOf(100);
        OrderReturnPO orderReturnPO = new OrderReturnPO();
        orderReturnPO.setOrderCommission(commission);
        when(orderReturnModel.getOrderReturnByAfsSn(afsSn)).thenReturn(orderReturnPO);

        DistributionOrderInfoVO result = orderAfterService.balanceReminder(afsSn);

        assertTrue(result.isDistributionOrder());
        assertEquals(commission, result.getDistributionAmount());
    }

}