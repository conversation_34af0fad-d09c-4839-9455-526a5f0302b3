package com.cfpamf.ms.mallorder.service.impl;

import com.cfpamf.ms.mallorder.dto.CategoryLoanDTO;
import com.cfpamf.ms.mallorder.dto.CategoryLoanDetailDTO;
import com.cfpamf.ms.mallorder.mapper.BzOrderPayCategoryMapper;
import com.cfpamf.ms.mallorder.po.BzOrderPayCategoryPO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;

@RunWith(MockitoJUnitRunner.class)
public class BzOrderPayCategoryServiceImplTest {

	@InjectMocks
	private BzOrderPayCategoryServiceImpl categoryService;
	@Mock
	private BzOrderPayCategoryMapper bzOrderPayCategoryMapper;

	@Before
	public void setUp() {
		MockitoAnnotations.initMocks(this);
	}

	@Test
	public void testIsAllowLoan() {
		List<CategoryLoanDetailDTO> detailDTOS = new ArrayList<>();
		CategoryLoanDetailDTO detail1 = new CategoryLoanDetailDTO();
		detail1.setCategoryId1(1);
		detail1.setCategoryId2(2);
		detail1.setCategoryId3(3);
		detailDTOS.add(detail1);
		CategoryLoanDTO loanDTO = new CategoryLoanDTO();
		loanDTO.setCategoryLoanDetailDTOS(detailDTOS);

		List<BzOrderPayCategoryPO> categoryDb = new ArrayList<>();
		BzOrderPayCategoryPO category1 = new BzOrderPayCategoryPO();
		category1.setCategoryId(2);
		categoryDb.add(category1);
		BzOrderPayCategoryPO category2 = new BzOrderPayCategoryPO();
		category2.setCategoryId(3);
		categoryDb.add(category2);
		Mockito.when(bzOrderPayCategoryMapper.selectList(any())).thenReturn(categoryDb);

		Assert.assertTrue(categoryService.isAllowLoan(loanDTO));
	}

}
