package com.cfpamf.ms.mallorder.model;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.cfpamf.common.ms.result.CommonError;
import com.cfpamf.common.ms.result.ErrorContext;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.mallpayment.facade.vo.PaymentNotifyVO;
import com.cfpamf.ms.mall.account.api.StmAccountFacade;
import com.cfpamf.ms.mallmember.api.MemberBalanceLogFeignClient;
import com.cfpamf.ms.mallmember.api.MemberFeignClient;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.common.config.ChannelFeeRateConfig;
import com.cfpamf.ms.mallorder.common.enums.OrderCreateChannel;
import com.cfpamf.ms.mallorder.common.enums.PayMethodEnum;
import com.cfpamf.ms.mallorder.common.enums.TaskQueueBizTypeEnum;
import com.cfpamf.ms.mallorder.controller.fegin.facade.AccountCardFacade;
import com.cfpamf.ms.mallorder.controller.fegin.facade.CustomerServiceFeign;
import com.cfpamf.ms.mallorder.dto.*;
import com.cfpamf.ms.mallorder.enums.OrderEventEnum;
import com.cfpamf.ms.mallorder.integration.omsbase.OmsBaseIntegration;
import com.cfpamf.ms.mallorder.integration.settlement.BillOperatinIntegration;
import com.cfpamf.ms.mallorder.mapper.OrderMapper;
import com.cfpamf.ms.mallorder.mapper.OrderPayMapper;
import com.cfpamf.ms.mallorder.mapper.OrderProductMapper;
import com.cfpamf.ms.mallorder.mapper.OrderPromotionSendCouponMapper;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.req.PayWayChangeRequest;
import com.cfpamf.ms.mallorder.request.OrderExample;
import com.cfpamf.ms.mallorder.request.OrderPayExample;
import com.cfpamf.ms.mallorder.request.OrderPromotionSendCouponExample;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.service.impl.OrderCreateHelper;
import com.cfpamf.ms.mallorder.v2.domain.vo.PayInfoExtraInfoVO;
import com.cfpamf.ms.mallorder.v2.service.OrderPresellService;
import com.cfpamf.ms.mallorder.v2.service.PayService;
import com.cfpamf.ms.mallorder.v2.strategy.context.OrderPayProcessStrategyContext;
import com.cfpamf.ms.mallorder.vo.OrderPayInfoVO;
import com.cfpamf.ms.mallpromotion.api.CouponFeignClient;
import com.cfpamf.ms.mallpromotion.api.CouponMemberFeignClient;
import com.cfpamf.ms.mallpromotion.api.CouponUseLogFeignClient;
import com.cfpamf.ms.mallpromotion.api.PromotionCommonFeignClient;
import com.cfpamf.ms.mallpromotion.request.Coupon;
import com.cfpamf.ms.mallpromotion.request.CouponMember;
import com.cfpamf.ms.mallpromotion.request.CouponUseLog;
import com.cfpamf.ms.mallpromotion.vo.CouponVO;
import com.cfpamf.ms.mallshop.api.StoreBindCategoryFeignClient;
import com.cfpamf.ms.mallshop.api.StoreFeignClient;
import com.cfpamf.ms.mallshop.request.StoreBindCategoryExample;
import com.cfpamf.ms.mallshop.resp.StoreBindCategory;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.PagerInfo;
import com.slodon.bbc.starter.mq.entity.MessageSendProperty;
import com.slodon.bbc.starter.mq.entity.MessageSendVO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.amqp.AmqpException;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

import java.math.BigDecimal;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class OrderPayModel2Test {

    @Mock
    private ChannelFeeRateConfig mockChannelFeeRateConfig;
    @Mock
    private OrderPayMapper mockOrderPayMapper;
    @Mock
    private OrderMapper mockOrderMapper;
    @Mock
    private OrderPromotionSendCouponMapper mockOrderPromotionSendCouponMapper;
    @Mock
    private OrderProductMapper mockOrderProductMapper;
    @Mock
    private OrderLogModel mockOrderLogModel;
    @Mock
    private OrderProductModel mockOrderProductModel;
    @Mock
    private MemberFeignClient mockMemberFeignClient;
    @Mock
    private MemberBalanceLogFeignClient mockMemberBalanceLogFeignClient;
    @Mock
    private PromotionCommonFeignClient mockPromotionCommonFeignClient;
    @Mock
    private CouponFeignClient mockCouponFeignClient;
    @Mock
    private CouponMemberFeignClient mockCouponMemberFeignClient;
    @Mock
    private CouponUseLogFeignClient mockCouponUseLogFeignClient;
    @Mock
    private RabbitTemplate mockRabbitTemplate;
    @Mock
    private OrderModel mockOrderModel;
    @Mock
    private IOrderService mockOrderService;
    @Mock
    private OrderCreateHelper mockOrderCreateHelper;
    @Mock
    private IOrderProductService mockOrderProductService;
    @Mock
    private IOrderPayService mockOrderPayService;
    @Mock
    private IOrderReturnService mockOrderReturnService;
    @Mock
    private IBzBankTransferService mockBzBankTransferService;
    @Mock
    private IOrderExtendFinanceService mockFinanceService;
    @Mock
    private ITaskQueueService mockTaskQueueService;
    @Mock
    private StoreFeignClient mockStoreFeignClient;
    @Mock
    private AccountCardFacade mockAccountCardFacade;
    @Mock
    private CustomerServiceFeign mockCustomerServiceFeign;
    @Mock
    private ILoanResultService mockLoanResultService;
    @Mock
    private BillOperatinIntegration mockBillOperatinIntegration;
    @Mock
    private StmAccountFacade mockStmAccountFacade;
    @Mock
    private OrderPayProcessStrategyContext mockOrderPayProcessStrategyContext;
    @Mock
    private PayService mockPayService;
    @Mock
    private BankTransferModel mockBankTransferModel;
    @Mock
    private OrderOfflineService mockOrderOfflineService;
    @Mock
    private IOrderAmountStateRecordService mockOrderAmountRecordService;
    @Mock
    private OrderPresellService mockOrderPresellService;
    @Mock
    private OmsBaseIntegration mockOmsBaseIntegration;
    @Mock
    private IOrderAmountStateRecordService mockIOrderAmountStateRecordService;
    @Mock
    private IBzOldUserPoolService mockIBzOldUserPoolService;
    @Mock
    private StoreBindCategoryFeignClient mockStoreBindCategoryFeignClient;

    @InjectMocks
    private OrderPayModel orderPayModelUnderTest;
  /*  @Test
    void testOrderPaySuccess_IOrderServiceListReturnsNoItems() throws Exception {
        // Setup
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);

        when(mockOrderPayProcessStrategyContext.getStrategy(0)).thenReturn(null);
        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategory storeBindCategory = new StoreBindCategory();
        storeBindCategory.setBindId(0);
        storeBindCategory.setStoreId(0L);
        storeBindCategory.setCreateVendorId(0L);
        storeBindCategory.setGoodsCategoryId1(0);
        storeBindCategory.setGoodsCategoryId2(0);
        final List<StoreBindCategory> storeBindCategories = Arrays.asList(storeBindCategory);
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                .thenReturn(storeBindCategories);

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        when(mockAccountCardFacade.defaultCard(0L)).thenReturn(new JsonResult<>(0, "errMsg"));
        when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure OrderPayMapper.updateByPrimaryKeySelective(...).
        final OrderPayPO record = new OrderPayPO();
        record.setPayId(0);
        record.setPaySn("paySn");
        record.setOrderSn("pOrderSn");
        record.setPayAmount(new BigDecimal("0.00"));
        record.setMemberId(0);
        record.setApiPayState("0");
        record.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        record.setTradeSn("memberName");
        record.setPaymentName("paymentName");
        record.setPaymentCode("paymentCode");
        record.setLoanSuccess(0);
        record.setEnjoyPayVipFlag(0);
        record.setPayWayExtraInfo(new JSONObject(0, false));
        record.setOutBizSource("channel");
        record.setOutBizId("paySn");
        when(mockOrderPayMapper.updateByPrimaryKeySelective(record)).thenReturn(0);

        when(mockPromotionCommonFeignClient.orderPaySuccess("memberName", 0, "paySn", "memberName", "paymentName",
                "paymentCode")).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderProductMapper.selectOne(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setStoreName("memberName");
        orderProductPO1.setGoodsName("memberName");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setSpellTeamId(0);
        orderProductPO1.setEnabledFlag(0);
        when(mockOrderProductMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderProductPO1);

        // Configure OrderProductMapper.selectList(...).
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setStoreName("memberName");
        orderProductPO2.setGoodsName("memberName");
        orderProductPO2.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO2.setProductNum(0);
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setSpellTeamId(0);
        orderProductPO2.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO2);
        when(mockOrderProductMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);
        final List<OrderPromotionSendCouponPO> orderPromotionSendCouponPOS = Arrays.asList(orderPromotionSendCouponPO);
        final OrderPromotionSendCouponExample example = new OrderPromotionSendCouponExample();
        example.setSendCouponIdNotEquals(0);
        example.setSendCouponIdIn("sendCouponIdIn");
        example.setSendCouponId(0);
        example.setOrderSn("memberName");
        example.setOrderSnLike("orderSnLike");
        when(mockOrderPromotionSendCouponMapper.listByExample(example)).thenReturn(orderPromotionSendCouponPOS);

        // Configure CouponFeignClient.getCouponByCouponId(...).
        final CouponVO couponVO = new CouponVO();
        couponVO.setCouponId(0);
        couponVO.setCouponType(0);
        couponVO.setReceivedNum(0);
        couponVO.setRandomMax(new BigDecimal("0.00"));
        couponVO.setRandomMin(new BigDecimal("0.00"));
        couponVO.setPublishNum(0);
        couponVO.setEffectiveTimeType(0);
        couponVO.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setCycle(0);
        couponVO.setState(0);
        couponVO.setStoreId(0L);
        couponVO.setUseType(0);
        when(mockCouponFeignClient.getCouponByCouponId(0)).thenReturn(couponVO);

        // Configure IOrderService.getById(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        when(mockOrderService.getById(0)).thenReturn(orderPO1);

        // Configure IOrderAmountStateRecordService.initOrderAmountState(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult = new Result<>(false, errorContext, null);
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("memberName");
        orderPO2.setUserNo("userNo");
        orderPO2.setPaySn("paySn");
        orderPO2.setSellerId("sellerId");
        orderPO2.setBankPayTrxNo("bankPayTrxNo");
        orderPO2.setStoreId(0L);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setComposePayName("composeWay");
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setServiceFee(new BigDecimal("0.00"));
        orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO2.setSettleMode("settleMode");
        orderPO2.setFinanceRuleCode("financeRuleCode");
        orderPO2.setIsDelivery(0);
        orderPO2.setChannel("channel");
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setCustomerConfirmStatus(0);
        orderPO2.setOrderPlaceUserRoleCode(0);
        orderPO2.setExchangeFlag(0);
        when(mockOrderAmountRecordService.initOrderAmountState(orderPO2)).thenReturn(voidResult);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO3 = new OrderProductPO();
        orderProductPO3.setOrderProductId(0L);
        orderProductPO3.setOrderSn("orderSn");
        orderProductPO3.setStoreName("memberName");
        orderProductPO3.setGoodsName("memberName");
        orderProductPO3.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO3.setProductNum(0);
        orderProductPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO3.setServiceFee(new BigDecimal("0.00"));
        orderProductPO3.setSpellTeamId(0);
        orderProductPO3.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS1 = Arrays.asList(orderProductPO3);
        when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(orderProductPOS1);

        // Run the test
        orderPayModelUnderTest.orderPaySuccess(orderPO, "memberName", "paymentCode", "paymentName",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "payNo");

        // Verify the results
        verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
                new BigDecimal("0.00"));
        verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 20, 0, "订单支付完成",
                OrderCreateChannel.H5);
        verify(mockOrderProductModel).orderPaySuccessAddSales("memberName");
        verify(mockOrderService).setOrdersDeliverable(Arrays.asList("value"));

        // Confirm CouponMemberFeignClient.saveCouponMember(...).
        final CouponMember couponMember = new CouponMember();
        couponMember.setCouponId(0);
        couponMember.setCouponCode("couponCode");
        couponMember.setStoreId(0L);
        couponMember.setMemberId(0);
        couponMember.setMemberName("memberName");
        couponMember.setReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseState(0);
        couponMember.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseType(0);
        couponMember.setRandomAmount(new BigDecimal("0.00"));
        verify(mockCouponMemberFeignClient).saveCouponMember(couponMember);

        // Confirm CouponUseLogFeignClient.saveCouponUseLog(...).
        final CouponUseLog couponUseLog = new CouponUseLog();
        couponUseLog.setCouponCode("couponCode");
        couponUseLog.setMemberId(0);
        couponUseLog.setMemberName("memberName");
        couponUseLog.setStoreId(0L);
        couponUseLog.setLogType(0);
        couponUseLog.setLogTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponUseLog.setLogContent("logContent");
        verify(mockCouponUseLogFeignClient).saveCouponUseLog(couponUseLog);

        // Confirm CouponFeignClient.updateOrderCoupon(...).
        final Coupon coupon = new Coupon();
        coupon.setCouponId(0);
        coupon.setCouponName("couponName");
        coupon.setCouponContent("couponContent");
        coupon.setDescription("description");
        coupon.setReceivedNum(0);
        verify(mockCouponFeignClient).updateOrderCoupon(coupon);

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("memberName");
        orderPO3.setUserNo("userNo");
        orderPO3.setPaySn("paySn");
        orderPO3.setSellerId("sellerId");
        orderPO3.setBankPayTrxNo("bankPayTrxNo");
        orderPO3.setStoreId(0L);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setComposePayName("composeWay");
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setServiceFee(new BigDecimal("0.00"));
        orderPO3.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO3.setSettleMode("settleMode");
        orderPO3.setFinanceRuleCode("financeRuleCode");
        orderPO3.setIsDelivery(0);
        orderPO3.setChannel("channel");
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setCustomerConfirmStatus(0);
        orderPO3.setOrderPlaceUserRoleCode(0);
        orderPO3.setExchangeFlag(0);
        verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO3, OrderEventEnum.CREATE,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg",
                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));
    }
*/
  /*  @Test
    void testOrderPaySuccess_OrderProductMapperSelectListReturnsNoItems() throws Exception {
        // Setup
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);

        when(mockOrderPayProcessStrategyContext.getStrategy(0)).thenReturn(null);
        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategory storeBindCategory = new StoreBindCategory();
        storeBindCategory.setBindId(0);
        storeBindCategory.setStoreId(0L);
        storeBindCategory.setCreateVendorId(0L);
        storeBindCategory.setGoodsCategoryId1(0);
        storeBindCategory.setGoodsCategoryId2(0);
        final List<StoreBindCategory> storeBindCategories = Arrays.asList(storeBindCategory);
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                .thenReturn(storeBindCategories);

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        when(mockAccountCardFacade.defaultCard(0L)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure IOrderService.list(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO1);
        when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(orderPOList);

        when(mockPromotionCommonFeignClient.orderPaySuccess("memberName", 0, "paySn", "memberName", "paymentName",
                "paymentCode")).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderProductMapper.selectOne(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setStoreName("memberName");
        orderProductPO1.setGoodsName("memberName");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setSpellTeamId(0);
        orderProductPO1.setEnabledFlag(0);
        when(mockOrderProductMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderProductPO1);

        when(mockOrderProductMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);
        final List<OrderPromotionSendCouponPO> orderPromotionSendCouponPOS = Arrays.asList(orderPromotionSendCouponPO);
        final OrderPromotionSendCouponExample example = new OrderPromotionSendCouponExample();
        example.setSendCouponIdNotEquals(0);
        example.setSendCouponIdIn("sendCouponIdIn");
        example.setSendCouponId(0);
        example.setOrderSn("memberName");
        example.setOrderSnLike("orderSnLike");
        when(mockOrderPromotionSendCouponMapper.listByExample(example)).thenReturn(orderPromotionSendCouponPOS);

        // Configure CouponFeignClient.getCouponByCouponId(...).
        final CouponVO couponVO = new CouponVO();
        couponVO.setCouponId(0);
        couponVO.setCouponType(0);
        couponVO.setReceivedNum(0);
        couponVO.setRandomMax(new BigDecimal("0.00"));
        couponVO.setRandomMin(new BigDecimal("0.00"));
        couponVO.setPublishNum(0);
        couponVO.setEffectiveTimeType(0);
        couponVO.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setCycle(0);
        couponVO.setState(0);
        couponVO.setStoreId(0L);
        couponVO.setUseType(0);
        when(mockCouponFeignClient.getCouponByCouponId(0)).thenReturn(couponVO);

        // Configure IOrderService.getById(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("memberName");
        orderPO2.setUserNo("userNo");
        orderPO2.setPaySn("paySn");
        orderPO2.setSellerId("sellerId");
        orderPO2.setBankPayTrxNo("bankPayTrxNo");
        orderPO2.setStoreId(0L);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setComposePayName("composeWay");
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setServiceFee(new BigDecimal("0.00"));
        orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO2.setSettleMode("settleMode");
        orderPO2.setFinanceRuleCode("financeRuleCode");
        orderPO2.setIsDelivery(0);
        orderPO2.setChannel("channel");
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setCustomerConfirmStatus(0);
        orderPO2.setOrderPlaceUserRoleCode(0);
        orderPO2.setExchangeFlag(0);
        when(mockOrderService.getById(0)).thenReturn(orderPO2);

        // Configure IOrderAmountStateRecordService.initOrderAmountState(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult = new Result<>(false, errorContext, null);
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("memberName");
        orderPO3.setUserNo("userNo");
        orderPO3.setPaySn("paySn");
        orderPO3.setSellerId("sellerId");
        orderPO3.setBankPayTrxNo("bankPayTrxNo");
        orderPO3.setStoreId(0L);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setComposePayName("composeWay");
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setServiceFee(new BigDecimal("0.00"));
        orderPO3.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO3.setSettleMode("settleMode");
        orderPO3.setFinanceRuleCode("financeRuleCode");
        orderPO3.setIsDelivery(0);
        orderPO3.setChannel("channel");
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setCustomerConfirmStatus(0);
        orderPO3.setOrderPlaceUserRoleCode(0);
        orderPO3.setExchangeFlag(0);
        when(mockOrderAmountRecordService.initOrderAmountState(orderPO3)).thenReturn(voidResult);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setStoreName("memberName");
        orderProductPO2.setGoodsName("memberName");
        orderProductPO2.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO2.setProductNum(0);
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setSpellTeamId(0);
        orderProductPO2.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO2);
        when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(orderProductPOS);

        // Run the test
        orderPayModelUnderTest.orderPaySuccess(orderPO, "memberName", "paymentCode", "paymentName",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "payNo");

        // Verify the results
        verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
                new BigDecimal("0.00"));
        verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 20, 0, "订单支付完成",
                OrderCreateChannel.H5);
        verify(mockOrderProductModel).orderPaySuccessAddSales("memberName");
        verify(mockOrderService).setOrdersDeliverable(Arrays.asList("value"));

        // Confirm CouponMemberFeignClient.saveCouponMember(...).
        final CouponMember couponMember = new CouponMember();
        couponMember.setCouponId(0);
        couponMember.setCouponCode("couponCode");
        couponMember.setStoreId(0L);
        couponMember.setMemberId(0);
        couponMember.setMemberName("memberName");
        couponMember.setReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseState(0);
        couponMember.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseType(0);
        couponMember.setRandomAmount(new BigDecimal("0.00"));
        verify(mockCouponMemberFeignClient).saveCouponMember(couponMember);

        // Confirm CouponUseLogFeignClient.saveCouponUseLog(...).
        final CouponUseLog couponUseLog = new CouponUseLog();
        couponUseLog.setCouponCode("couponCode");
        couponUseLog.setMemberId(0);
        couponUseLog.setMemberName("memberName");
        couponUseLog.setStoreId(0L);
        couponUseLog.setLogType(0);
        couponUseLog.setLogTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponUseLog.setLogContent("logContent");
        verify(mockCouponUseLogFeignClient).saveCouponUseLog(couponUseLog);

        // Confirm CouponFeignClient.updateOrderCoupon(...).
        final Coupon coupon = new Coupon();
        coupon.setCouponId(0);
        coupon.setCouponName("couponName");
        coupon.setCouponContent("couponContent");
        coupon.setDescription("description");
        coupon.setReceivedNum(0);
        verify(mockCouponFeignClient).updateOrderCoupon(coupon);

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO4 = new OrderPO();
        orderPO4.setOrderId(0);
        orderPO4.setOrderSn("memberName");
        orderPO4.setUserNo("userNo");
        orderPO4.setPaySn("paySn");
        orderPO4.setSellerId("sellerId");
        orderPO4.setBankPayTrxNo("bankPayTrxNo");
        orderPO4.setStoreId(0L);
        orderPO4.setRecommendStoreId(0L);
        orderPO4.setMemberName("memberName");
        orderPO4.setMemberId(0);
        orderPO4.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setOrderState(0);
        orderPO4.setLoanPayState(0);
        orderPO4.setPaymentName("paymentName");
        orderPO4.setPaymentCode("paymentCode");
        orderPO4.setOrderAmount(new BigDecimal("0.00"));
        orderPO4.setGoodsAmount(new BigDecimal("0.00"));
        orderPO4.setExpressFee(new BigDecimal("0.00"));
        orderPO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO4.setComposePayName("composeWay");
        orderPO4.setBalanceAmount(new BigDecimal("0.00"));
        orderPO4.setPayAmount(new BigDecimal("0.00"));
        orderPO4.setAreaCode("areaCode");
        orderPO4.setOrderType(0);
        orderPO4.setServiceFee(new BigDecimal("0.00"));
        orderPO4.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO4.setSettleMode("settleMode");
        orderPO4.setFinanceRuleCode("financeRuleCode");
        orderPO4.setIsDelivery(0);
        orderPO4.setChannel("channel");
        orderPO4.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO4.setNewOrder(false);
        orderPO4.setCustomerConfirmStatus(0);
        orderPO4.setOrderPlaceUserRoleCode(0);
        orderPO4.setExchangeFlag(0);
        verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO4, OrderEventEnum.CREATE,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg",
                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));
    }
*/
   /* @Test
    void testOrderPaySuccess_OrderPromotionSendCouponMapperReturnsNoItems() throws Exception {
        // Setup
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);

        when(mockOrderPayProcessStrategyContext.getStrategy(0)).thenReturn(null);
        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategory storeBindCategory = new StoreBindCategory();
        storeBindCategory.setBindId(0);
        storeBindCategory.setStoreId(0L);
        storeBindCategory.setCreateVendorId(0L);
        storeBindCategory.setGoodsCategoryId1(0);
        storeBindCategory.setGoodsCategoryId2(0);
        final List<StoreBindCategory> storeBindCategories = Arrays.asList(storeBindCategory);
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                .thenReturn(storeBindCategories);

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        when(mockAccountCardFacade.defaultCard(0L)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure IOrderService.list(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO1);
        when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(orderPOList);

        when(mockPromotionCommonFeignClient.orderPaySuccess("memberName", 0, "paySn", "memberName", "paymentName",
                "paymentCode")).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderProductMapper.selectOne(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setStoreName("memberName");
        orderProductPO1.setGoodsName("memberName");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setSpellTeamId(0);
        orderProductPO1.setEnabledFlag(0);
        when(mockOrderProductMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderProductPO1);

        // Configure OrderProductMapper.selectList(...).
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setStoreName("memberName");
        orderProductPO2.setGoodsName("memberName");
        orderProductPO2.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO2.setProductNum(0);
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setSpellTeamId(0);
        orderProductPO2.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO2);
        when(mockOrderProductMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponExample example = new OrderPromotionSendCouponExample();
        example.setSendCouponIdNotEquals(0);
        example.setSendCouponIdIn("sendCouponIdIn");
        example.setSendCouponId(0);
        example.setOrderSn("memberName");
        example.setOrderSnLike("orderSnLike");
        when(mockOrderPromotionSendCouponMapper.listByExample(example)).thenReturn(Collections.emptyList());

        // Configure IOrderService.getById(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("memberName");
        orderPO2.setUserNo("userNo");
        orderPO2.setPaySn("paySn");
        orderPO2.setSellerId("sellerId");
        orderPO2.setBankPayTrxNo("bankPayTrxNo");
        orderPO2.setStoreId(0L);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setComposePayName("composeWay");
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setServiceFee(new BigDecimal("0.00"));
        orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO2.setSettleMode("settleMode");
        orderPO2.setFinanceRuleCode("financeRuleCode");
        orderPO2.setIsDelivery(0);
        orderPO2.setChannel("channel");
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setCustomerConfirmStatus(0);
        orderPO2.setOrderPlaceUserRoleCode(0);
        orderPO2.setExchangeFlag(0);
        when(mockOrderService.getById(0)).thenReturn(orderPO2);

        // Configure IOrderAmountStateRecordService.initOrderAmountState(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult = new Result<>(false, errorContext, null);
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("memberName");
        orderPO3.setUserNo("userNo");
        orderPO3.setPaySn("paySn");
        orderPO3.setSellerId("sellerId");
        orderPO3.setBankPayTrxNo("bankPayTrxNo");
        orderPO3.setStoreId(0L);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setComposePayName("composeWay");
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setServiceFee(new BigDecimal("0.00"));
        orderPO3.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO3.setSettleMode("settleMode");
        orderPO3.setFinanceRuleCode("financeRuleCode");
        orderPO3.setIsDelivery(0);
        orderPO3.setChannel("channel");
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setCustomerConfirmStatus(0);
        orderPO3.setOrderPlaceUserRoleCode(0);
        orderPO3.setExchangeFlag(0);
        when(mockOrderAmountRecordService.initOrderAmountState(orderPO3)).thenReturn(voidResult);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO3 = new OrderProductPO();
        orderProductPO3.setOrderProductId(0L);
        orderProductPO3.setOrderSn("orderSn");
        orderProductPO3.setStoreName("memberName");
        orderProductPO3.setGoodsName("memberName");
        orderProductPO3.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO3.setProductNum(0);
        orderProductPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO3.setServiceFee(new BigDecimal("0.00"));
        orderProductPO3.setSpellTeamId(0);
        orderProductPO3.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS1 = Arrays.asList(orderProductPO3);
        when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(orderProductPOS1);

        // Run the test
        orderPayModelUnderTest.orderPaySuccess(orderPO, "memberName", "paymentCode", "paymentName",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "payNo");

        // Verify the results
        verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
                new BigDecimal("0.00"));
        verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 20, 0, "订单支付完成",
                OrderCreateChannel.H5);
        verify(mockOrderProductModel).orderPaySuccessAddSales("memberName");
        verify(mockOrderService).setOrdersDeliverable(Arrays.asList("value"));

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO4 = new OrderPO();
        orderPO4.setOrderId(0);
        orderPO4.setOrderSn("memberName");
        orderPO4.setUserNo("userNo");
        orderPO4.setPaySn("paySn");
        orderPO4.setSellerId("sellerId");
        orderPO4.setBankPayTrxNo("bankPayTrxNo");
        orderPO4.setStoreId(0L);
        orderPO4.setRecommendStoreId(0L);
        orderPO4.setMemberName("memberName");
        orderPO4.setMemberId(0);
        orderPO4.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setOrderState(0);
        orderPO4.setLoanPayState(0);
        orderPO4.setPaymentName("paymentName");
        orderPO4.setPaymentCode("paymentCode");
        orderPO4.setOrderAmount(new BigDecimal("0.00"));
        orderPO4.setGoodsAmount(new BigDecimal("0.00"));
        orderPO4.setExpressFee(new BigDecimal("0.00"));
        orderPO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO4.setComposePayName("composeWay");
        orderPO4.setBalanceAmount(new BigDecimal("0.00"));
        orderPO4.setPayAmount(new BigDecimal("0.00"));
        orderPO4.setAreaCode("areaCode");
        orderPO4.setOrderType(0);
        orderPO4.setServiceFee(new BigDecimal("0.00"));
        orderPO4.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO4.setSettleMode("settleMode");
        orderPO4.setFinanceRuleCode("financeRuleCode");
        orderPO4.setIsDelivery(0);
        orderPO4.setChannel("channel");
        orderPO4.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO4.setNewOrder(false);
        orderPO4.setCustomerConfirmStatus(0);
        orderPO4.setOrderPlaceUserRoleCode(0);
        orderPO4.setExchangeFlag(0);
        verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO4, OrderEventEnum.CREATE,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg",
                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));
    }
*/
   /* @Test
    void testOrderPaySuccess_OrderProductModelGetOrderProductListByOrderSnReturnsNoItems() throws Exception {
        // Setup
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);

        when(mockOrderPayProcessStrategyContext.getStrategy(0)).thenReturn(null);
        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategory storeBindCategory = new StoreBindCategory();
        storeBindCategory.setBindId(0);
        storeBindCategory.setStoreId(0L);
        storeBindCategory.setCreateVendorId(0L);
        storeBindCategory.setGoodsCategoryId1(0);
        storeBindCategory.setGoodsCategoryId2(0);
        final List<StoreBindCategory> storeBindCategories = Arrays.asList(storeBindCategory);
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                .thenReturn(storeBindCategories);

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        when(mockAccountCardFacade.defaultCard(0L)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure IOrderService.list(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO1);
        when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(orderPOList);

        when(mockPromotionCommonFeignClient.orderPaySuccess("memberName", 0, "paySn", "memberName", "paymentName",
                "paymentCode")).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderProductMapper.selectOne(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setStoreName("memberName");
        orderProductPO1.setGoodsName("memberName");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setSpellTeamId(0);
        orderProductPO1.setEnabledFlag(0);
        when(mockOrderProductMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderProductPO1);

        // Configure OrderProductMapper.selectList(...).
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setStoreName("memberName");
        orderProductPO2.setGoodsName("memberName");
        orderProductPO2.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO2.setProductNum(0);
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setSpellTeamId(0);
        orderProductPO2.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO2);
        when(mockOrderProductMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);
        final List<OrderPromotionSendCouponPO> orderPromotionSendCouponPOS = Arrays.asList(orderPromotionSendCouponPO);
        final OrderPromotionSendCouponExample example = new OrderPromotionSendCouponExample();
        example.setSendCouponIdNotEquals(0);
        example.setSendCouponIdIn("sendCouponIdIn");
        example.setSendCouponId(0);
        example.setOrderSn("memberName");
        example.setOrderSnLike("orderSnLike");
        when(mockOrderPromotionSendCouponMapper.listByExample(example)).thenReturn(orderPromotionSendCouponPOS);

        // Configure CouponFeignClient.getCouponByCouponId(...).
        final CouponVO couponVO = new CouponVO();
        couponVO.setCouponId(0);
        couponVO.setCouponType(0);
        couponVO.setReceivedNum(0);
        couponVO.setRandomMax(new BigDecimal("0.00"));
        couponVO.setRandomMin(new BigDecimal("0.00"));
        couponVO.setPublishNum(0);
        couponVO.setEffectiveTimeType(0);
        couponVO.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setCycle(0);
        couponVO.setState(0);
        couponVO.setStoreId(0L);
        couponVO.setUseType(0);
        when(mockCouponFeignClient.getCouponByCouponId(0)).thenReturn(couponVO);

        // Configure IOrderService.getById(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("memberName");
        orderPO2.setUserNo("userNo");
        orderPO2.setPaySn("paySn");
        orderPO2.setSellerId("sellerId");
        orderPO2.setBankPayTrxNo("bankPayTrxNo");
        orderPO2.setStoreId(0L);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setComposePayName("composeWay");
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setServiceFee(new BigDecimal("0.00"));
        orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO2.setSettleMode("settleMode");
        orderPO2.setFinanceRuleCode("financeRuleCode");
        orderPO2.setIsDelivery(0);
        orderPO2.setChannel("channel");
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setCustomerConfirmStatus(0);
        orderPO2.setOrderPlaceUserRoleCode(0);
        orderPO2.setExchangeFlag(0);
        when(mockOrderService.getById(0)).thenReturn(orderPO2);

        // Configure IOrderAmountStateRecordService.initOrderAmountState(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult = new Result<>(false, errorContext, null);
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("memberName");
        orderPO3.setUserNo("userNo");
        orderPO3.setPaySn("paySn");
        orderPO3.setSellerId("sellerId");
        orderPO3.setBankPayTrxNo("bankPayTrxNo");
        orderPO3.setStoreId(0L);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setComposePayName("composeWay");
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setServiceFee(new BigDecimal("0.00"));
        orderPO3.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO3.setSettleMode("settleMode");
        orderPO3.setFinanceRuleCode("financeRuleCode");
        orderPO3.setIsDelivery(0);
        orderPO3.setChannel("channel");
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setCustomerConfirmStatus(0);
        orderPO3.setOrderPlaceUserRoleCode(0);
        orderPO3.setExchangeFlag(0);
        when(mockOrderAmountRecordService.initOrderAmountState(orderPO3)).thenReturn(voidResult);

        when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(Collections.emptyList());

        // Run the test
        orderPayModelUnderTest.orderPaySuccess(orderPO, "memberName", "paymentCode", "paymentName",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "payNo");

        // Verify the results
        verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
                new BigDecimal("0.00"));
        verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 20, 0, "订单支付完成",
                OrderCreateChannel.H5);
        verify(mockOrderProductModel).orderPaySuccessAddSales("memberName");
        verify(mockOrderService).setOrdersDeliverable(Arrays.asList("value"));

        // Confirm CouponMemberFeignClient.saveCouponMember(...).
        final CouponMember couponMember = new CouponMember();
        couponMember.setCouponId(0);
        couponMember.setCouponCode("couponCode");
        couponMember.setStoreId(0L);
        couponMember.setMemberId(0);
        couponMember.setMemberName("memberName");
        couponMember.setReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseState(0);
        couponMember.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseType(0);
        couponMember.setRandomAmount(new BigDecimal("0.00"));
        verify(mockCouponMemberFeignClient).saveCouponMember(couponMember);

        // Confirm CouponUseLogFeignClient.saveCouponUseLog(...).
        final CouponUseLog couponUseLog = new CouponUseLog();
        couponUseLog.setCouponCode("couponCode");
        couponUseLog.setMemberId(0);
        couponUseLog.setMemberName("memberName");
        couponUseLog.setStoreId(0L);
        couponUseLog.setLogType(0);
        couponUseLog.setLogTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponUseLog.setLogContent("logContent");
        verify(mockCouponUseLogFeignClient).saveCouponUseLog(couponUseLog);

        // Confirm CouponFeignClient.updateOrderCoupon(...).
        final Coupon coupon = new Coupon();
        coupon.setCouponId(0);
        coupon.setCouponName("couponName");
        coupon.setCouponContent("couponContent");
        coupon.setDescription("description");
        coupon.setReceivedNum(0);
        verify(mockCouponFeignClient).updateOrderCoupon(coupon);

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO4 = new OrderPO();
        orderPO4.setOrderId(0);
        orderPO4.setOrderSn("memberName");
        orderPO4.setUserNo("userNo");
        orderPO4.setPaySn("paySn");
        orderPO4.setSellerId("sellerId");
        orderPO4.setBankPayTrxNo("bankPayTrxNo");
        orderPO4.setStoreId(0L);
        orderPO4.setRecommendStoreId(0L);
        orderPO4.setMemberName("memberName");
        orderPO4.setMemberId(0);
        orderPO4.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setOrderState(0);
        orderPO4.setLoanPayState(0);
        orderPO4.setPaymentName("paymentName");
        orderPO4.setPaymentCode("paymentCode");
        orderPO4.setOrderAmount(new BigDecimal("0.00"));
        orderPO4.setGoodsAmount(new BigDecimal("0.00"));
        orderPO4.setExpressFee(new BigDecimal("0.00"));
        orderPO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO4.setComposePayName("composeWay");
        orderPO4.setBalanceAmount(new BigDecimal("0.00"));
        orderPO4.setPayAmount(new BigDecimal("0.00"));
        orderPO4.setAreaCode("areaCode");
        orderPO4.setOrderType(0);
        orderPO4.setServiceFee(new BigDecimal("0.00"));
        orderPO4.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO4.setSettleMode("settleMode");
        orderPO4.setFinanceRuleCode("financeRuleCode");
        orderPO4.setIsDelivery(0);
        orderPO4.setChannel("channel");
        orderPO4.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO4.setNewOrder(false);
        orderPO4.setCustomerConfirmStatus(0);
        orderPO4.setOrderPlaceUserRoleCode(0);
        orderPO4.setExchangeFlag(0);
        verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO4, OrderEventEnum.CREATE,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg",
                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));
    }
*/
   /* @Test
    void testOrderPaySuccess_RabbitTemplateThrowsAmqpException() throws Exception {
        // Setup
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);

        when(mockOrderPayProcessStrategyContext.getStrategy(0)).thenReturn(null);
        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategory storeBindCategory = new StoreBindCategory();
        storeBindCategory.setBindId(0);
        storeBindCategory.setStoreId(0L);
        storeBindCategory.setCreateVendorId(0L);
        storeBindCategory.setGoodsCategoryId1(0);
        storeBindCategory.setGoodsCategoryId2(0);
        final List<StoreBindCategory> storeBindCategories = Arrays.asList(storeBindCategory);
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                .thenReturn(storeBindCategories);

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        when(mockAccountCardFacade.defaultCard(0L)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure IOrderService.list(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO1);
        when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(orderPOList);

        when(mockPromotionCommonFeignClient.orderPaySuccess("memberName", 0, "paySn", "memberName", "paymentName",
                "paymentCode")).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderProductMapper.selectOne(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setStoreName("memberName");
        orderProductPO1.setGoodsName("memberName");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setSpellTeamId(0);
        orderProductPO1.setEnabledFlag(0);
        when(mockOrderProductMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderProductPO1);

        // Configure OrderProductMapper.selectList(...).
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setStoreName("memberName");
        orderProductPO2.setGoodsName("memberName");
        orderProductPO2.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO2.setProductNum(0);
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setSpellTeamId(0);
        orderProductPO2.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO2);
        when(mockOrderProductMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);
        final List<OrderPromotionSendCouponPO> orderPromotionSendCouponPOS = Arrays.asList(orderPromotionSendCouponPO);
        final OrderPromotionSendCouponExample example = new OrderPromotionSendCouponExample();
        example.setSendCouponIdNotEquals(0);
        example.setSendCouponIdIn("sendCouponIdIn");
        example.setSendCouponId(0);
        example.setOrderSn("memberName");
        example.setOrderSnLike("orderSnLike");
        when(mockOrderPromotionSendCouponMapper.listByExample(example)).thenReturn(orderPromotionSendCouponPOS);

        // Configure CouponFeignClient.getCouponByCouponId(...).
        final CouponVO couponVO = new CouponVO();
        couponVO.setCouponId(0);
        couponVO.setCouponType(0);
        couponVO.setReceivedNum(0);
        couponVO.setRandomMax(new BigDecimal("0.00"));
        couponVO.setRandomMin(new BigDecimal("0.00"));
        couponVO.setPublishNum(0);
        couponVO.setEffectiveTimeType(0);
        couponVO.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setCycle(0);
        couponVO.setState(0);
        couponVO.setStoreId(0L);
        couponVO.setUseType(0);
        when(mockCouponFeignClient.getCouponByCouponId(0)).thenReturn(couponVO);

        // Configure IOrderService.getById(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("memberName");
        orderPO2.setUserNo("userNo");
        orderPO2.setPaySn("paySn");
        orderPO2.setSellerId("sellerId");
        orderPO2.setBankPayTrxNo("bankPayTrxNo");
        orderPO2.setStoreId(0L);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setComposePayName("composeWay");
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setServiceFee(new BigDecimal("0.00"));
        orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO2.setSettleMode("settleMode");
        orderPO2.setFinanceRuleCode("financeRuleCode");
        orderPO2.setIsDelivery(0);
        orderPO2.setChannel("channel");
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setCustomerConfirmStatus(0);
        orderPO2.setOrderPlaceUserRoleCode(0);
        orderPO2.setExchangeFlag(0);
        when(mockOrderService.getById(0)).thenReturn(orderPO2);

        // Configure IOrderAmountStateRecordService.initOrderAmountState(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult = new Result<>(false, errorContext, null);
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("memberName");
        orderPO3.setUserNo("userNo");
        orderPO3.setPaySn("paySn");
        orderPO3.setSellerId("sellerId");
        orderPO3.setBankPayTrxNo("bankPayTrxNo");
        orderPO3.setStoreId(0L);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setComposePayName("composeWay");
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setServiceFee(new BigDecimal("0.00"));
        orderPO3.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO3.setSettleMode("settleMode");
        orderPO3.setFinanceRuleCode("financeRuleCode");
        orderPO3.setIsDelivery(0);
        orderPO3.setChannel("channel");
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setCustomerConfirmStatus(0);
        orderPO3.setOrderPlaceUserRoleCode(0);
        orderPO3.setExchangeFlag(0);
        when(mockOrderAmountRecordService.initOrderAmountState(orderPO3)).thenReturn(voidResult);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO3 = new OrderProductPO();
        orderProductPO3.setOrderProductId(0L);
        orderProductPO3.setOrderSn("orderSn");
        orderProductPO3.setStoreName("memberName");
        orderProductPO3.setGoodsName("memberName");
        orderProductPO3.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO3.setProductNum(0);
        orderProductPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO3.setServiceFee(new BigDecimal("0.00"));
        orderProductPO3.setSpellTeamId(0);
        orderProductPO3.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS1 = Arrays.asList(orderProductPO3);
        when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(orderProductPOS1);

        doThrow(AmqpException.class).when(mockRabbitTemplate).convertAndSend("newmall_exchange",
                "newmall_queue_member_msg",
                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));

        // Run the test
        orderPayModelUnderTest.orderPaySuccess(orderPO, "memberName", "paymentCode", "paymentName",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "payNo");

        // Verify the results
        verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
                new BigDecimal("0.00"));
        verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 20, 0, "订单支付完成",
                OrderCreateChannel.H5);
        verify(mockOrderProductModel).orderPaySuccessAddSales("memberName");
        verify(mockOrderService).setOrdersDeliverable(Arrays.asList("value"));

        // Confirm CouponMemberFeignClient.saveCouponMember(...).
        final CouponMember couponMember = new CouponMember();
        couponMember.setCouponId(0);
        couponMember.setCouponCode("couponCode");
        couponMember.setStoreId(0L);
        couponMember.setMemberId(0);
        couponMember.setMemberName("memberName");
        couponMember.setReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseState(0);
        couponMember.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseType(0);
        couponMember.setRandomAmount(new BigDecimal("0.00"));
        verify(mockCouponMemberFeignClient).saveCouponMember(couponMember);

        // Confirm CouponUseLogFeignClient.saveCouponUseLog(...).
        final CouponUseLog couponUseLog = new CouponUseLog();
        couponUseLog.setCouponCode("couponCode");
        couponUseLog.setMemberId(0);
        couponUseLog.setMemberName("memberName");
        couponUseLog.setStoreId(0L);
        couponUseLog.setLogType(0);
        couponUseLog.setLogTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponUseLog.setLogContent("logContent");
        verify(mockCouponUseLogFeignClient).saveCouponUseLog(couponUseLog);

        // Confirm CouponFeignClient.updateOrderCoupon(...).
        final Coupon coupon = new Coupon();
        coupon.setCouponId(0);
        coupon.setCouponName("couponName");
        coupon.setCouponContent("couponContent");
        coupon.setDescription("description");
        coupon.setReceivedNum(0);
        verify(mockCouponFeignClient).updateOrderCoupon(coupon);

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO4 = new OrderPO();
        orderPO4.setOrderId(0);
        orderPO4.setOrderSn("memberName");
        orderPO4.setUserNo("userNo");
        orderPO4.setPaySn("paySn");
        orderPO4.setSellerId("sellerId");
        orderPO4.setBankPayTrxNo("bankPayTrxNo");
        orderPO4.setStoreId(0L);
        orderPO4.setRecommendStoreId(0L);
        orderPO4.setMemberName("memberName");
        orderPO4.setMemberId(0);
        orderPO4.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setOrderState(0);
        orderPO4.setLoanPayState(0);
        orderPO4.setPaymentName("paymentName");
        orderPO4.setPaymentCode("paymentCode");
        orderPO4.setOrderAmount(new BigDecimal("0.00"));
        orderPO4.setGoodsAmount(new BigDecimal("0.00"));
        orderPO4.setExpressFee(new BigDecimal("0.00"));
        orderPO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO4.setComposePayName("composeWay");
        orderPO4.setBalanceAmount(new BigDecimal("0.00"));
        orderPO4.setPayAmount(new BigDecimal("0.00"));
        orderPO4.setAreaCode("areaCode");
        orderPO4.setOrderType(0);
        orderPO4.setServiceFee(new BigDecimal("0.00"));
        orderPO4.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO4.setSettleMode("settleMode");
        orderPO4.setFinanceRuleCode("financeRuleCode");
        orderPO4.setIsDelivery(0);
        orderPO4.setChannel("channel");
        orderPO4.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO4.setNewOrder(false);
        orderPO4.setCustomerConfirmStatus(0);
        orderPO4.setOrderPlaceUserRoleCode(0);
        orderPO4.setExchangeFlag(0);
        verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO4, OrderEventEnum.CREATE,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
    }*/

  /*  @Test
    void testOrderInfoProcess() throws Exception {
        // Setup
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);

        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategory storeBindCategory = new StoreBindCategory();
        storeBindCategory.setBindId(0);
        storeBindCategory.setStoreId(0L);
        storeBindCategory.setCreateVendorId(0L);
        storeBindCategory.setGoodsCategoryId1(0);
        storeBindCategory.setGoodsCategoryId2(0);
        final List<StoreBindCategory> storeBindCategories = Arrays.asList(storeBindCategory);
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                .thenReturn(storeBindCategories);

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        when(mockAccountCardFacade.defaultCard(0L)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure BillOperatinIntegration.detailByBankAccount(...).
        final AccountCard accountCard = new AccountCard();
        accountCard.setCardId(0L);
        accountCard.setStoreId("storeId");
        accountCard.setStoreName("storeName");
        accountCard.setOrgType("orgType");
        accountCard.setBankAccountNumber("bankAccountNumber");
        when(mockBillOperatinIntegration.detailByBankAccount("UIN_PLF_STORE_ID101",
                AccountCardTypeEnum.UNI_JS_PLF_SUP)).thenReturn(accountCard);

        // Configure IBzBankTransferService.list(...).
        final BzBankTransferPO bzBankTransferPO = new BzBankTransferPO();
        bzBankTransferPO.setEnabledFlag(0);
        bzBankTransferPO.setPaySn("paySn");
        bzBankTransferPO.setOutOrderNo("outOrderNo");
        bzBankTransferPO.setPayAmount(new BigDecimal("0.00"));
        bzBankTransferPO.setReceiptAccount("receiptAccount");
        final List<BzBankTransferPO> bzBankTransferPOS = Arrays.asList(bzBankTransferPO);
        when(mockBzBankTransferService.list(any(LambdaQueryWrapper.class))).thenReturn(bzBankTransferPOS);

        // Configure StoreFeignClient.getStoreContractReciptInfo(...).
        final StoreContractReceiptInfoVO storeContractReceiptInfoVO = new StoreContractReceiptInfoVO();
        storeContractReceiptInfoVO.setWxSellerId("wxSellerId");
        storeContractReceiptInfoVO.setAliSellerId("aliSellerId");
        final Store store = new Store();
        store.setAcctId("acctId");
        storeContractReceiptInfoVO.setStore(store);
        storeContractReceiptInfoVO.setRecommentWxSellerId("recommentWxSellerId");
        storeContractReceiptInfoVO.setRecommentAliSellerId("recommentAliSellerId");
        when(mockStoreFeignClient.getStoreContractReciptInfo(0L)).thenReturn(storeContractReceiptInfoVO);

        // Configure CustomerServiceFeign.info(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final BankcardVo bankcardVo = new BankcardVo();
        bankcardVo.setCardId("cardId");
        bankcardVo.setCardNo("cardNo");
        bankcardVo.setAcctName("acctName");
        bankcardVo.setBankCode("bankCode");
        bankcardVo.setBankName("bankName");
        final Result<BankcardVo> bankcardVoResult = new Result<>(false, errorContext, bankcardVo);
        final QueryBankcardInfoReq var1 = new QueryBankcardInfoReq();
        var1.setCardId("cardId");
        when(mockCustomerServiceFeign.info(var1)).thenReturn(bankcardVoResult);

        // Configure StmAccountFacade.detailV2(...).
        final AccountQuery accountQuery = new AccountQuery();
        accountQuery.setCurrent(0);
        accountQuery.setPageSize(0);
        accountQuery.setAccountId("accountId");
        accountQuery.setStoreId("UIN_PLF_STORE_ID101");
        accountQuery.setAccountType("accountType");
        when(mockStmAccountFacade.detailV2(accountQuery)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderOfflineService.queryOrderOfflineList(...).
        final OrderOfflinePO orderOfflinePO = new OrderOfflinePO();
        orderOfflinePO.setEnabledFlag(0);
        orderOfflinePO.setPaySn("paySn");
        orderOfflinePO.setReceiptAccount("receiptAccount");
        orderOfflinePO.setReceiptAmount(new BigDecimal("0.00"));
        orderOfflinePO.setReceiptTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<OrderOfflinePO> orderOfflinePOS = Arrays.asList(orderOfflinePO);
        when(mockOrderOfflineService.queryOrderOfflineList("paySn")).thenReturn(orderOfflinePOS);

        // Run the test
        final boolean result = orderPayModelUnderTest.orderInfoProcess(orderPO, "paymentCode", "paymentName",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "composeWay");

        // Verify the results
        assertThat(result).isFalse();
        verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
                new BigDecimal("0.00"));
    }
*/
  /*  @Test
    void testOrderInfoProcess_StoreBindCategoryFeignClientReturnsNoItems() throws Exception {
        // Setup
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);

        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                .thenReturn(Collections.emptyList());

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        when(mockAccountCardFacade.defaultCard(0L)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure BillOperatinIntegration.detailByBankAccount(...).
        final AccountCard accountCard = new AccountCard();
        accountCard.setCardId(0L);
        accountCard.setStoreId("storeId");
        accountCard.setStoreName("storeName");
        accountCard.setOrgType("orgType");
        accountCard.setBankAccountNumber("bankAccountNumber");
        when(mockBillOperatinIntegration.detailByBankAccount("UIN_PLF_STORE_ID101",
                AccountCardTypeEnum.UNI_JS_PLF_SUP)).thenReturn(accountCard);

        // Configure IBzBankTransferService.list(...).
        final BzBankTransferPO bzBankTransferPO = new BzBankTransferPO();
        bzBankTransferPO.setEnabledFlag(0);
        bzBankTransferPO.setPaySn("paySn");
        bzBankTransferPO.setOutOrderNo("outOrderNo");
        bzBankTransferPO.setPayAmount(new BigDecimal("0.00"));
        bzBankTransferPO.setReceiptAccount("receiptAccount");
        final List<BzBankTransferPO> bzBankTransferPOS = Arrays.asList(bzBankTransferPO);
        when(mockBzBankTransferService.list(any(LambdaQueryWrapper.class))).thenReturn(bzBankTransferPOS);

        // Configure StoreFeignClient.getStoreContractReciptInfo(...).
        final StoreContractReceiptInfoVO storeContractReceiptInfoVO = new StoreContractReceiptInfoVO();
        storeContractReceiptInfoVO.setWxSellerId("wxSellerId");
        storeContractReceiptInfoVO.setAliSellerId("aliSellerId");
        final Store store = new Store();
        store.setAcctId("acctId");
        storeContractReceiptInfoVO.setStore(store);
        storeContractReceiptInfoVO.setRecommentWxSellerId("recommentWxSellerId");
        storeContractReceiptInfoVO.setRecommentAliSellerId("recommentAliSellerId");
        when(mockStoreFeignClient.getStoreContractReciptInfo(0L)).thenReturn(storeContractReceiptInfoVO);

        // Configure CustomerServiceFeign.info(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final BankcardVo bankcardVo = new BankcardVo();
        bankcardVo.setCardId("cardId");
        bankcardVo.setCardNo("cardNo");
        bankcardVo.setAcctName("acctName");
        bankcardVo.setBankCode("bankCode");
        bankcardVo.setBankName("bankName");
        final Result<BankcardVo> bankcardVoResult = new Result<>(false, errorContext, bankcardVo);
        final QueryBankcardInfoReq var1 = new QueryBankcardInfoReq();
        var1.setCardId("cardId");
        when(mockCustomerServiceFeign.info(var1)).thenReturn(bankcardVoResult);

        // Configure StmAccountFacade.detailV2(...).
        final AccountQuery accountQuery = new AccountQuery();
        accountQuery.setCurrent(0);
        accountQuery.setPageSize(0);
        accountQuery.setAccountId("accountId");
        accountQuery.setStoreId("UIN_PLF_STORE_ID101");
        accountQuery.setAccountType("accountType");
        when(mockStmAccountFacade.detailV2(accountQuery)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderOfflineService.queryOrderOfflineList(...).
        final OrderOfflinePO orderOfflinePO = new OrderOfflinePO();
        orderOfflinePO.setEnabledFlag(0);
        orderOfflinePO.setPaySn("paySn");
        orderOfflinePO.setReceiptAccount("receiptAccount");
        orderOfflinePO.setReceiptAmount(new BigDecimal("0.00"));
        orderOfflinePO.setReceiptTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<OrderOfflinePO> orderOfflinePOS = Arrays.asList(orderOfflinePO);
        when(mockOrderOfflineService.queryOrderOfflineList("paySn")).thenReturn(orderOfflinePOS);

        // Run the test
        final boolean result = orderPayModelUnderTest.orderInfoProcess(orderPO, "paymentCode", "paymentName",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "composeWay");

        // Verify the results
        assertThat(result).isFalse();
        verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
                new BigDecimal("0.00"));
    }
*/
   /* @Test
    void testOrderInfoProcess_IBzBankTransferServiceReturnsNoItems() throws Exception {
        // Setup
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);

        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategory storeBindCategory = new StoreBindCategory();
        storeBindCategory.setBindId(0);
        storeBindCategory.setStoreId(0L);
        storeBindCategory.setCreateVendorId(0L);
        storeBindCategory.setGoodsCategoryId1(0);
        storeBindCategory.setGoodsCategoryId2(0);
        final List<StoreBindCategory> storeBindCategories = Arrays.asList(storeBindCategory);
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                .thenReturn(storeBindCategories);

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        when(mockBzBankTransferService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final boolean result = orderPayModelUnderTest.orderInfoProcess(orderPO, "paymentCode", "paymentName",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "composeWay");

        // Verify the results
        assertThat(result).isFalse();
        verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
                new BigDecimal("0.00"));
    }
*/
   /* @Test
    void testOrderInfoProcess_OrderOfflineServiceReturnsNoItems() throws Exception {
        // Setup
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);

        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategory storeBindCategory = new StoreBindCategory();
        storeBindCategory.setBindId(0);
        storeBindCategory.setStoreId(0L);
        storeBindCategory.setCreateVendorId(0L);
        storeBindCategory.setGoodsCategoryId1(0);
        storeBindCategory.setGoodsCategoryId2(0);
        final List<StoreBindCategory> storeBindCategories = Arrays.asList(storeBindCategory);
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                .thenReturn(storeBindCategories);

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        when(mockOrderOfflineService.queryOrderOfflineList("paySn")).thenReturn(Collections.emptyList());

        // Run the test
        final boolean result = orderPayModelUnderTest.orderInfoProcess(orderPO, "paymentCode", "paymentName",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "composeWay");

        // Verify the results
        assertThat(result).isFalse();
        verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
                new BigDecimal("0.00"));
    }
*/
   /* @Test
    void testUpdatePlatformAmount() throws Exception {
        // Setup
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);

        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        // Run the test
        orderPayModelUnderTest.updatePlatformAmount(orderPO, new BigDecimal("0.00"));

        // Verify the results
    }
*/
   /* @Test
    void testQueryRuleServiceFeeFlag() throws Exception {
        // Setup
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategory storeBindCategory = new StoreBindCategory();
        storeBindCategory.setBindId(0);
        storeBindCategory.setStoreId(0L);
        storeBindCategory.setCreateVendorId(0L);
        storeBindCategory.setGoodsCategoryId1(0);
        storeBindCategory.setGoodsCategoryId2(0);
        final List<StoreBindCategory> storeBindCategories = Arrays.asList(storeBindCategory);
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                .thenReturn(storeBindCategories);

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        // Run the test
        final Boolean result = orderPayModelUnderTest.queryRuleServiceFeeFlag(orderPO, "paymentCode");

        // Verify the results
        assertThat(result).isFalse();
    }
*/
   /* @Test
    void testQueryRuleServiceFeeFlag_StoreBindCategoryFeignClientReturnsNoItems() throws Exception {
        // Setup
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                .thenReturn(Collections.emptyList());

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        // Run the test
        final Boolean result = orderPayModelUnderTest.queryRuleServiceFeeFlag(orderPO, "paymentCode");

        // Verify the results
        assertThat(result).isFalse();
    }
*/
   /* @Test
    void testQueryRuleServiceFeeFlag_OmsBaseIntegrationReturnsTrue() throws Exception {
        // Setup
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategory storeBindCategory = new StoreBindCategory();
        storeBindCategory.setBindId(0);
        storeBindCategory.setStoreId(0L);
        storeBindCategory.setCreateVendorId(0L);
        storeBindCategory.setGoodsCategoryId1(0);
        storeBindCategory.setGoodsCategoryId2(0);
        final List<StoreBindCategory> storeBindCategories = Arrays.asList(storeBindCategory);
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                .thenReturn(storeBindCategories);

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(true);

        // Run the test
        final Boolean result = orderPayModelUnderTest.queryRuleServiceFeeFlag(orderPO, "paymentCode");

        // Verify the results
        assertThat(result).isTrue();
    }
*/
  /*  @Test
    void testCalculateChannelServiceFee() throws Exception {
        // Setup
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);

        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Run the test
        final BigDecimal result = orderPayModelUnderTest.calculateChannelServiceFee(orderPO, "paymentCode");

        // Verify the results
        assertThat(result).isEqualTo(new BigDecimal("0.00"));
    }
*/
    /*@Test
    void testParseSellerId() throws Exception {
        // Setup
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);

        when(mockAccountCardFacade.defaultCard(0L)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure BillOperatinIntegration.detailByBankAccount(...).
        final AccountCard accountCard = new AccountCard();
        accountCard.setCardId(0L);
        accountCard.setStoreId("storeId");
        accountCard.setStoreName("storeName");
        accountCard.setOrgType("orgType");
        accountCard.setBankAccountNumber("bankAccountNumber");
        when(mockBillOperatinIntegration.detailByBankAccount("UIN_PLF_STORE_ID101",
                AccountCardTypeEnum.UNI_JS_PLF_SUP)).thenReturn(accountCard);

        // Configure IBzBankTransferService.list(...).
        final BzBankTransferPO bzBankTransferPO = new BzBankTransferPO();
        bzBankTransferPO.setEnabledFlag(0);
        bzBankTransferPO.setPaySn("paySn");
        bzBankTransferPO.setOutOrderNo("outOrderNo");
        bzBankTransferPO.setPayAmount(new BigDecimal("0.00"));
        bzBankTransferPO.setReceiptAccount("receiptAccount");
        final List<BzBankTransferPO> bzBankTransferPOS = Arrays.asList(bzBankTransferPO);
        when(mockBzBankTransferService.list(any(LambdaQueryWrapper.class))).thenReturn(bzBankTransferPOS);

        // Configure StoreFeignClient.getStoreContractReciptInfo(...).
        final StoreContractReceiptInfoVO storeContractReceiptInfoVO = new StoreContractReceiptInfoVO();
        storeContractReceiptInfoVO.setWxSellerId("wxSellerId");
        storeContractReceiptInfoVO.setAliSellerId("aliSellerId");
        final Store store = new Store();
        store.setAcctId("acctId");
        storeContractReceiptInfoVO.setStore(store);
        storeContractReceiptInfoVO.setRecommentWxSellerId("recommentWxSellerId");
        storeContractReceiptInfoVO.setRecommentAliSellerId("recommentAliSellerId");
        when(mockStoreFeignClient.getStoreContractReciptInfo(0L)).thenReturn(storeContractReceiptInfoVO);

        // Configure CustomerServiceFeign.info(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final BankcardVo bankcardVo = new BankcardVo();
        bankcardVo.setCardId("cardId");
        bankcardVo.setCardNo("cardNo");
        bankcardVo.setAcctName("acctName");
        bankcardVo.setBankCode("bankCode");
        bankcardVo.setBankName("bankName");
        final Result<BankcardVo> bankcardVoResult = new Result<>(false, errorContext, bankcardVo);
        final QueryBankcardInfoReq var1 = new QueryBankcardInfoReq();
        var1.setCardId("cardId");
        when(mockCustomerServiceFeign.info(var1)).thenReturn(bankcardVoResult);

        // Configure StmAccountFacade.detailV2(...).
        final AccountQuery accountQuery = new AccountQuery();
        accountQuery.setCurrent(0);
        accountQuery.setPageSize(0);
        accountQuery.setAccountId("accountId");
        accountQuery.setStoreId("UIN_PLF_STORE_ID101");
        accountQuery.setAccountType("accountType");
        when(mockStmAccountFacade.detailV2(accountQuery)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderOfflineService.queryOrderOfflineList(...).
        final OrderOfflinePO orderOfflinePO = new OrderOfflinePO();
        orderOfflinePO.setEnabledFlag(0);
        orderOfflinePO.setPaySn("paySn");
        orderOfflinePO.setReceiptAccount("receiptAccount");
        orderOfflinePO.setReceiptAmount(new BigDecimal("0.00"));
        orderOfflinePO.setReceiptTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<OrderOfflinePO> orderOfflinePOS = Arrays.asList(orderOfflinePO);
        when(mockOrderOfflineService.queryOrderOfflineList("paySn")).thenReturn(orderOfflinePOS);

        // Run the test
        final String result = orderPayModelUnderTest.parseSellerId(orderPO, "paymentCode");

        // Verify the results
        assertThat(result).isEqualTo("");
    }*/

    /* @Test
     void testParseSellerId_IBzBankTransferServiceReturnsNoItems() throws Exception {
         // Setup
         final OrderPO orderPO = new OrderPO();
         orderPO.setOrderId(0);
         orderPO.setOrderSn("memberName");
         orderPO.setUserNo("userNo");
         orderPO.setPaySn("paySn");
         orderPO.setSellerId("sellerId");
         orderPO.setBankPayTrxNo("bankPayTrxNo");
         orderPO.setStoreId(0L);
         orderPO.setRecommendStoreId(0L);
         orderPO.setMemberName("memberName");
         orderPO.setMemberId(0);
         orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
         orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
         orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
         orderPO.setOrderState(0);
         orderPO.setLoanPayState(0);
         orderPO.setPaymentName("paymentName");
         orderPO.setPaymentCode("paymentCode");
         orderPO.setOrderAmount(new BigDecimal("0.00"));
         orderPO.setGoodsAmount(new BigDecimal("0.00"));
         orderPO.setExpressFee(new BigDecimal("0.00"));
         orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
         orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
         orderPO.setXzCardAmount(new BigDecimal("0.00"));
         orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
         orderPO.setComposePayName("composeWay");
         orderPO.setBalanceAmount(new BigDecimal("0.00"));
         orderPO.setPayAmount(new BigDecimal("0.00"));
         orderPO.setAreaCode("areaCode");
         orderPO.setOrderType(0);
         orderPO.setServiceFee(new BigDecimal("0.00"));
         orderPO.setServiceFeeRate(new BigDecimal("0.00"));
         orderPO.setSettleMode("settleMode");
         orderPO.setFinanceRuleCode("financeRuleCode");
         orderPO.setIsDelivery(0);
         orderPO.setChannel("channel");
         orderPO.setChannelServiceFee(new BigDecimal("0.00"));
         orderPO.setNewOrder(false);
         orderPO.setCustomerConfirmStatus(0);
         orderPO.setOrderPlaceUserRoleCode(0);
         orderPO.setExchangeFlag(0);

         when(mockBzBankTransferService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

         // Run the test
         final String result = orderPayModelUnderTest.parseSellerId(orderPO, "paymentCode");

         // Verify the results
         // assertThat(result).isEqualTo("");
     }
 */
  /*  @Test
    void testParseSellerId_OrderOfflineServiceReturnsNoItems() throws Exception {
        // Setup
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);

        when(mockOrderOfflineService.queryOrderOfflineList("paySn")).thenReturn(Collections.emptyList());

        // Run the test
        final String result = orderPayModelUnderTest.parseSellerId(orderPO, "paymentCode");

        // Verify the results
        //      assertThat(result).isNull();
    }
*/
  /*  @Test
    void testOrderPayFail() throws Exception {
        // Setup
        final OrderPO order = new OrderPO();
        order.setOrderId(0);
        order.setOrderSn("memberName");
        order.setUserNo("userNo");
        order.setPaySn("paySn");
        order.setSellerId("sellerId");
        order.setBankPayTrxNo("bankPayTrxNo");
        order.setStoreId(0L);
        order.setRecommendStoreId(0L);
        order.setMemberName("memberName");
        order.setMemberId(0);
        order.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        order.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        order.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        order.setOrderState(0);
        order.setLoanPayState(0);
        order.setPaymentName("paymentName");
        order.setPaymentCode("paymentCode");
        order.setOrderAmount(new BigDecimal("0.00"));
        order.setGoodsAmount(new BigDecimal("0.00"));
        order.setExpressFee(new BigDecimal("0.00"));
        order.setStoreVoucherAmount(new BigDecimal("0.00"));
        order.setStoreActivityAmount(new BigDecimal("0.00"));
        order.setXzCardAmount(new BigDecimal("0.00"));
        order.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        order.setComposePayName("composeWay");
        order.setBalanceAmount(new BigDecimal("0.00"));
        order.setPayAmount(new BigDecimal("0.00"));
        order.setAreaCode("areaCode");
        order.setOrderType(0);
        order.setServiceFee(new BigDecimal("0.00"));
        order.setServiceFeeRate(new BigDecimal("0.00"));
        order.setSettleMode("settleMode");
        order.setFinanceRuleCode("financeRuleCode");
        order.setIsDelivery(0);
        order.setChannel("channel");
        order.setChannelServiceFee(new BigDecimal("0.00"));
        order.setNewOrder(false);
        order.setCustomerConfirmStatus(0);
        order.setOrderPlaceUserRoleCode(0);
        order.setExchangeFlag(0);

        // Configure OrderMapper.updateByExampleSelective(...).
        final OrderPO record = new OrderPO();
        record.setOrderId(0);
        record.setOrderSn("memberName");
        record.setUserNo("userNo");
        record.setPaySn("paySn");
        record.setSellerId("sellerId");
        record.setBankPayTrxNo("bankPayTrxNo");
        record.setStoreId(0L);
        record.setRecommendStoreId(0L);
        record.setMemberName("memberName");
        record.setMemberId(0);
        record.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        record.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        record.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        record.setOrderState(0);
        record.setLoanPayState(0);
        record.setPaymentName("paymentName");
        record.setPaymentCode("paymentCode");
        record.setOrderAmount(new BigDecimal("0.00"));
        record.setGoodsAmount(new BigDecimal("0.00"));
        record.setExpressFee(new BigDecimal("0.00"));
        record.setStoreVoucherAmount(new BigDecimal("0.00"));
        record.setStoreActivityAmount(new BigDecimal("0.00"));
        record.setXzCardAmount(new BigDecimal("0.00"));
        record.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        record.setComposePayName("composeWay");
        record.setBalanceAmount(new BigDecimal("0.00"));
        record.setPayAmount(new BigDecimal("0.00"));
        record.setAreaCode("areaCode");
        record.setOrderType(0);
        record.setServiceFee(new BigDecimal("0.00"));
        record.setServiceFeeRate(new BigDecimal("0.00"));
        record.setSettleMode("settleMode");
        record.setFinanceRuleCode("financeRuleCode");
        record.setIsDelivery(0);
        record.setChannel("channel");
        record.setChannelServiceFee(new BigDecimal("0.00"));
        record.setNewOrder(false);
        record.setCustomerConfirmStatus(0);
        record.setOrderPlaceUserRoleCode(0);
        record.setExchangeFlag(0);
        final OrderExample example = new OrderExample();
        example.setOrderIdNotEquals(0);
        example.setUserNo("userNo");
        example.setUserMobile("userMobile");
        example.setOrderSn("memberName");
        example.setPaySn("paySn");
        when(mockOrderMapper.updateByExampleSelective(record, example)).thenReturn(0);

        // Configure OrderPayMapper.updateByPrimaryKeySelective(...).
        final OrderPayPO record1 = new OrderPayPO();
        record1.setPayId(0);
        record1.setPaySn("paySn");
        record1.setOrderSn("pOrderSn");
        record1.setPayAmount(new BigDecimal("0.00"));
        record1.setMemberId(0);
        record1.setApiPayState("0");
        record1.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        record1.setTradeSn("memberName");
        record1.setPaymentName("paymentName");
        record1.setPaymentCode("paymentCode");
        record1.setLoanSuccess(0);
        record1.setEnjoyPayVipFlag(0);
        record1.setPayWayExtraInfo(new JSONObject(0, false));
        record1.setOutBizSource("channel");
        record1.setOutBizId("paySn");
        when(mockOrderPayMapper.updateByPrimaryKeySelective(record1)).thenReturn(0);

        // Run the test
        orderPayModelUnderTest.orderPayFail(order);

        // Verify the results
        verify(mockOrderProductService).updateProductReturnNumAfterCancel("memberName");
        verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 0, 0, "订单付款失败，发生退汇",
                OrderCreateChannel.WEB);

        // Confirm OrderModel.orderCancelAddGoodsStock(...).
        final OrderPO orderDb = new OrderPO();
        orderDb.setOrderId(0);
        orderDb.setOrderSn("memberName");
        orderDb.setUserNo("userNo");
        orderDb.setPaySn("paySn");
        orderDb.setSellerId("sellerId");
        orderDb.setBankPayTrxNo("bankPayTrxNo");
        orderDb.setStoreId(0L);
        orderDb.setRecommendStoreId(0L);
        orderDb.setMemberName("memberName");
        orderDb.setMemberId(0);
        orderDb.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderDb.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderDb.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderDb.setOrderState(0);
        orderDb.setLoanPayState(0);
        orderDb.setPaymentName("paymentName");
        orderDb.setPaymentCode("paymentCode");
        orderDb.setOrderAmount(new BigDecimal("0.00"));
        orderDb.setGoodsAmount(new BigDecimal("0.00"));
        orderDb.setExpressFee(new BigDecimal("0.00"));
        orderDb.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderDb.setStoreActivityAmount(new BigDecimal("0.00"));
        orderDb.setXzCardAmount(new BigDecimal("0.00"));
        orderDb.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderDb.setComposePayName("composeWay");
        orderDb.setBalanceAmount(new BigDecimal("0.00"));
        orderDb.setPayAmount(new BigDecimal("0.00"));
        orderDb.setAreaCode("areaCode");
        orderDb.setOrderType(0);
        orderDb.setServiceFee(new BigDecimal("0.00"));
        orderDb.setServiceFeeRate(new BigDecimal("0.00"));
        orderDb.setSettleMode("settleMode");
        orderDb.setFinanceRuleCode("financeRuleCode");
        orderDb.setIsDelivery(0);
        orderDb.setChannel("channel");
        orderDb.setChannelServiceFee(new BigDecimal("0.00"));
        orderDb.setNewOrder(false);
        orderDb.setCustomerConfirmStatus(0);
        orderDb.setOrderPlaceUserRoleCode(0);
        orderDb.setExchangeFlag(0);
        verify(mockOrderModel).orderCancelAddGoodsStock("memberName", 0, "areaCode", "financeRuleCode", orderDb);

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);
        verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO, OrderEventEnum.CANCEL,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
    }
*/
    @Test
    void testSendMsgPaySuccess() throws Exception {
        // Setup
        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO);
        when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(orderProductPOS);

        // Run the test
        orderPayModelUnderTest.sendMsgPaySuccess(0, "memberName", 0L, "memberName", new BigDecimal("0.00"));

        // Verify the results
//        verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg",
//                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
//                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
//                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));
//
    }

    /*  @Test
      void testSendMsgPaySuccess_OrderProductModelReturnsNoItems() throws Exception {
          // Setup
          when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(Collections.emptyList());

          // Run the test
          orderPayModelUnderTest.sendMsgPaySuccess(0, "memberName", 0L, "memberName", new BigDecimal("0.00"));

          // Verify the results
          verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg",
                  new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
                          Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
                          "balance_change_reminder", "{\"type\":\"balance_change\"}"));
      }
  */
    @Test
    void testSendMsgPaySuccess_RabbitTemplateThrowsAmqpException() throws Exception {
        // Setup
        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO);
        when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(orderProductPOS);

        doThrow(AmqpException.class).when(mockRabbitTemplate).convertAndSend("newmall_exchange",
                "newmall_queue_member_msg",
                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));

        // Run the test
        orderPayModelUnderTest.sendMsgPaySuccess(0, "memberName", 0L, "memberName", new BigDecimal("0.00"));

        // Verify the results
    }

    @Test
    void testSendMsgAccountChange() throws Exception {
        // Setup
        final Member member = new Member();
        member.setMemberId(0);
        member.setMemberName("adminName");
        member.setLastPaymentCode("BALANCE");
        member.setBalanceAvailable(new BigDecimal("0.00"));
        member.setBalanceFrozen(new BigDecimal("0.00"));
        member.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Run the test
        orderPayModelUnderTest.sendMsgAccountChange(member, new BigDecimal("0.00"));

        // Verify the results
//        verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg",
//                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
//                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
//                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));
    }

    @Test
    void testSendMsgAccountChange_RabbitTemplateThrowsAmqpException() throws Exception {
        // Setup
        final Member member = new Member();
        member.setMemberId(0);
        member.setMemberName("adminName");
        member.setLastPaymentCode("BALANCE");
        member.setBalanceAvailable(new BigDecimal("0.00"));
        member.setBalanceFrozen(new BigDecimal("0.00"));
        member.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        doThrow(AmqpException.class).when(mockRabbitTemplate).convertAndSend("newmall_exchange",
                "newmall_queue_member_msg",
                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));

        // Run the test
        orderPayModelUnderTest.sendMsgAccountChange(member, new BigDecimal("0.00"));

        // Verify the results
    }

   /* @Test
    void testDoLoanOperation() throws Exception {
        // Setup
        final LoanLendingResult result1 = new LoanLendingResult();
        result1.setTradeNo(0L);
        result1.setLoanNo("loanNo");
        result1.setResult("loanResult");
        result1.setFailureReason("failureReason");
        result1.setPaySuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure OrderPresellService.queryBalanceInfoByPayNo(...).
        final OrderPresellPO orderPresellPO = new OrderPresellPO();
        orderPresellPO.setEnabledFlag(0);
        orderPresellPO.setOrderSn("orderSn");
        orderPresellPO.setPaySn("paySn");
        orderPresellPO.setPayNo("payNo");
        orderPresellPO.setBankPayTrxNo("bankPayTrxNo");
        when(mockOrderPresellService.queryBalanceInfoByPayNo("payNo")).thenReturn(orderPresellPO);

        // Configure OrderModel.getOrderList(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO);
        final OrderExample example = new OrderExample();
        example.setOrderIdNotEquals(0);
        example.setUserNo("userNo");
        example.setUserMobile("userMobile");
        example.setOrderSn("memberName");
        example.setPaySn("paySn");
        when(mockOrderModel.getOrderList(eq(example), any(PagerInfo.class))).thenReturn(orderPOList);

        // Configure IOrderPayService.getOne(...).
        final OrderPayPO orderPayPO = new OrderPayPO();
        orderPayPO.setPayId(0);
        orderPayPO.setPaySn("paySn");
        orderPayPO.setOrderSn("pOrderSn");
        orderPayPO.setPayAmount(new BigDecimal("0.00"));
        orderPayPO.setMemberId(0);
        orderPayPO.setApiPayState("0");
        orderPayPO.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPayPO.setTradeSn("memberName");
        orderPayPO.setPaymentName("paymentName");
        orderPayPO.setPaymentCode("paymentCode");
        orderPayPO.setLoanSuccess(0);
        orderPayPO.setEnjoyPayVipFlag(0);
        orderPayPO.setPayWayExtraInfo(new JSONObject(0, false));
        orderPayPO.setOutBizSource("channel");
        orderPayPO.setOutBizId("paySn");
        when(mockOrderPayService.getOne(any(LambdaQueryWrapper.class))).thenReturn(orderPayPO);

        // Run the test
        orderPayModelUnderTest.doLoanOperation(result1);

        // Verify the results
        // Confirm OrderModel.updateOrder(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        verify(mockOrderModel).updateOrder(orderPO1);
        verify(mockOrderLogModel).insertOrderLog(0, 0L, "system", "memberName", 0, 60, 0, "放款成功",
                OrderCreateChannel.WEB);

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("memberName");
        orderPO2.setUserNo("userNo");
        orderPO2.setPaySn("paySn");
        orderPO2.setSellerId("sellerId");
        orderPO2.setBankPayTrxNo("bankPayTrxNo");
        orderPO2.setStoreId(0L);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setComposePayName("composeWay");
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setServiceFee(new BigDecimal("0.00"));
        orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO2.setSettleMode("settleMode");
        orderPO2.setFinanceRuleCode("financeRuleCode");
        orderPO2.setIsDelivery(0);
        orderPO2.setChannel("channel");
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setCustomerConfirmStatus(0);
        orderPO2.setOrderPlaceUserRoleCode(0);
        orderPO2.setExchangeFlag(0);
        verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO2, OrderEventEnum.LENDING_SUCCESS,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Confirm IOrderPayService.updateById(...).
        final OrderPayPO entity = new OrderPayPO();
        entity.setPayId(0);
        entity.setPaySn("paySn");
        entity.setOrderSn("pOrderSn");
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setMemberId(0);
        entity.setApiPayState("0");
        entity.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setTradeSn("memberName");
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setLoanSuccess(0);
        entity.setEnjoyPayVipFlag(0);
        entity.setPayWayExtraInfo(new JSONObject(0, false));
        entity.setOutBizSource("channel");
        entity.setOutBizId("paySn");
        verify(mockOrderPayService).updateById(entity);
        verify(mockTaskQueueService).saveTaskQueue(0L, TaskQueueBizTypeEnum.MAKE_UP_BANK_TRX_NO,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "system");

        // Confirm ILoanResultService.saveOrUpdate(...).
        final LoanResultPO entity1 = new LoanResultPO();
        entity1.setPayNo("payNo");
        entity1.setLoanResult("loanResult");
        entity1.setFailureReason("failureReason");
        entity1.setFailureType(0);
        entity1.setCreateBy("CNBJ0000");
        entity1.setUpdateBy("CNBJ0000");
        entity1.setEnabledFlag(0);
        verify(mockLoanResultService).saveOrUpdate(eq(entity1), any(LambdaUpdateWrapper.class));
    }
*/
  /*  @Test
    void testDoLoanOperation_OrderModelGetOrderListReturnsNoItems() throws Exception {
        // Setup
        final LoanLendingResult result1 = new LoanLendingResult();
        result1.setTradeNo(0L);
        result1.setLoanNo("loanNo");
        result1.setResult("loanResult");
        result1.setFailureReason("failureReason");
        result1.setPaySuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure OrderPresellService.queryBalanceInfoByPayNo(...).
        final OrderPresellPO orderPresellPO = new OrderPresellPO();
        orderPresellPO.setEnabledFlag(0);
        orderPresellPO.setOrderSn("orderSn");
        orderPresellPO.setPaySn("paySn");
        orderPresellPO.setPayNo("payNo");
        orderPresellPO.setBankPayTrxNo("bankPayTrxNo");
        when(mockOrderPresellService.queryBalanceInfoByPayNo("payNo")).thenReturn(orderPresellPO);

        // Configure OrderModel.getOrderList(...).
        final OrderExample example = new OrderExample();
        example.setOrderIdNotEquals(0);
        example.setUserNo("userNo");
        example.setUserMobile("userMobile");
        example.setOrderSn("memberName");
        example.setPaySn("paySn");
        when(mockOrderModel.getOrderList(eq(example), any(PagerInfo.class))).thenReturn(Collections.emptyList());

        // Configure IOrderPayService.getOne(...).
        final OrderPayPO orderPayPO = new OrderPayPO();
        orderPayPO.setPayId(0);
        orderPayPO.setPaySn("paySn");
        orderPayPO.setOrderSn("pOrderSn");
        orderPayPO.setPayAmount(new BigDecimal("0.00"));
        orderPayPO.setMemberId(0);
        orderPayPO.setApiPayState("0");
        orderPayPO.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPayPO.setTradeSn("memberName");
        orderPayPO.setPaymentName("paymentName");
        orderPayPO.setPaymentCode("paymentCode");
        orderPayPO.setLoanSuccess(0);
        orderPayPO.setEnjoyPayVipFlag(0);
        orderPayPO.setPayWayExtraInfo(new JSONObject(0, false));
        orderPayPO.setOutBizSource("channel");
        orderPayPO.setOutBizId("paySn");
        when(mockOrderPayService.getOne(any(LambdaQueryWrapper.class))).thenReturn(orderPayPO);

        // Run the test
        orderPayModelUnderTest.doLoanOperation(result1);

        // Verify the results
        // Confirm OrderModel.updateOrder(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);
        verify(mockOrderModel).updateOrder(orderPO);
        verify(mockOrderLogModel).insertOrderLog(0, 0L, "system", "memberName", 0, 60, 0, "放款成功",
                OrderCreateChannel.WEB);

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO1, OrderEventEnum.LENDING_SUCCESS,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Confirm IOrderPayService.updateById(...).
        final OrderPayPO entity = new OrderPayPO();
        entity.setPayId(0);
        entity.setPaySn("paySn");
        entity.setOrderSn("pOrderSn");
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setMemberId(0);
        entity.setApiPayState("0");
        entity.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setTradeSn("memberName");
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setLoanSuccess(0);
        entity.setEnjoyPayVipFlag(0);
        entity.setPayWayExtraInfo(new JSONObject(0, false));
        entity.setOutBizSource("channel");
        entity.setOutBizId("paySn");
        verify(mockOrderPayService).updateById(entity);
        verify(mockTaskQueueService).saveTaskQueue(0L, TaskQueueBizTypeEnum.MAKE_UP_BANK_TRX_NO,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "system");

        // Confirm ILoanResultService.saveOrUpdate(...).
        final LoanResultPO entity1 = new LoanResultPO();
        entity1.setPayNo("payNo");
        entity1.setLoanResult("loanResult");
        entity1.setFailureReason("failureReason");
        entity1.setFailureType(0);
        entity1.setCreateBy("CNBJ0000");
        entity1.setUpdateBy("CNBJ0000");
        entity1.setEnabledFlag(0);
        verify(mockLoanResultService).saveOrUpdate(eq(entity1), any(LambdaUpdateWrapper.class));
    }
*/
   /* @Test
    void testWxAlipayCallBack() throws Exception {
        // Setup
        final PaymentNotifyVO req = new PaymentNotifyVO();
        req.setOrderOn("paySn");
        req.setPayCode("memberName");
        req.setPayTradeNo("memberName");
        req.setRelPayAmt(new BigDecimal("0.00"));
        req.setPayStatus(0);
        req.setPayWay("payWay");
        req.setBankPayTrxNos(new HashMap<>());
        req.setNewOrder(false);

        final PayInfoExtraInfoVO payInfoExtraInfoVO = new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn");

        // Configure PayService.buildPayNotifyOrderOn(...).
        final PaymentNotifyVO req1 = new PaymentNotifyVO();
        req1.setOrderOn("paySn");
        req1.setPayCode("memberName");
        req1.setPayTradeNo("memberName");
        req1.setRelPayAmt(new BigDecimal("0.00"));
        req1.setPayStatus(0);
        req1.setPayWay("payWay");
        req1.setBankPayTrxNos(new HashMap<>());
        req1.setNewOrder(false);
        when(mockPayService.buildPayNotifyOrderOn(req1,
                new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"), false)).thenReturn("paySn");

        // Configure OrderPayMapper.getByPrimaryKey(...).
        final OrderPayPO orderPayPO = new OrderPayPO();
        orderPayPO.setPayId(0);
        orderPayPO.setPaySn("paySn");
        orderPayPO.setOrderSn("pOrderSn");
        orderPayPO.setPayAmount(new BigDecimal("0.00"));
        orderPayPO.setMemberId(0);
        orderPayPO.setApiPayState("0");
        orderPayPO.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPayPO.setTradeSn("memberName");
        orderPayPO.setPaymentName("paymentName");
        orderPayPO.setPaymentCode("paymentCode");
        orderPayPO.setLoanSuccess(0);
        orderPayPO.setEnjoyPayVipFlag(0);
        orderPayPO.setPayWayExtraInfo(new JSONObject(0, false));
        orderPayPO.setOutBizSource("channel");
        orderPayPO.setOutBizId("paySn");
        when(mockOrderPayMapper.getByPrimaryKey("paySn")).thenReturn(orderPayPO);

        // Configure OrderModel.getOrderList(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO);
        final OrderExample example = new OrderExample();
        example.setOrderIdNotEquals(0);
        example.setUserNo("userNo");
        example.setUserMobile("userMobile");
        example.setOrderSn("memberName");
        example.setPaySn("paySn");
        when(mockOrderModel.getOrderList(eq(example), any(PagerInfo.class))).thenReturn(orderPOList);

        when(mockOrderService.lambdaUpdate()).thenReturn(new LambdaUpdateChainWrapper<>(null));
        when(mockPayService.payAmount("payNo")).thenReturn(new BigDecimal("0.00"));
        when(mockOrderPayProcessStrategyContext.getStrategy(0)).thenReturn(null);
        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategory storeBindCategory = new StoreBindCategory();
        storeBindCategory.setBindId(0);
        storeBindCategory.setStoreId(0L);
        storeBindCategory.setCreateVendorId(0L);
        storeBindCategory.setGoodsCategoryId1(0);
        storeBindCategory.setGoodsCategoryId2(0);
        final List<StoreBindCategory> storeBindCategories = Arrays.asList(storeBindCategory);
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                .thenReturn(storeBindCategories);

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        when(mockAccountCardFacade.defaultCard(0L)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure BillOperatinIntegration.detailByBankAccount(...).
        final AccountCard accountCard = new AccountCard();
        accountCard.setCardId(0L);
        accountCard.setStoreId("storeId");
        accountCard.setStoreName("storeName");
        accountCard.setOrgType("orgType");
        accountCard.setBankAccountNumber("bankAccountNumber");
        when(mockBillOperatinIntegration.detailByBankAccount("UIN_PLF_STORE_ID101",
                AccountCardTypeEnum.UNI_JS_PLF_SUP)).thenReturn(accountCard);

        // Configure IBzBankTransferService.list(...).
        final BzBankTransferPO bzBankTransferPO = new BzBankTransferPO();
        bzBankTransferPO.setEnabledFlag(0);
        bzBankTransferPO.setPaySn("paySn");
        bzBankTransferPO.setOutOrderNo("outOrderNo");
        bzBankTransferPO.setPayAmount(new BigDecimal("0.00"));
        bzBankTransferPO.setReceiptAccount("receiptAccount");
        final List<BzBankTransferPO> bzBankTransferPOS = Arrays.asList(bzBankTransferPO);
        when(mockBzBankTransferService.list(any(LambdaQueryWrapper.class))).thenReturn(bzBankTransferPOS);

        // Configure StoreFeignClient.getStoreContractReciptInfo(...).
        final StoreContractReceiptInfoVO storeContractReceiptInfoVO = new StoreContractReceiptInfoVO();
        storeContractReceiptInfoVO.setWxSellerId("wxSellerId");
        storeContractReceiptInfoVO.setAliSellerId("aliSellerId");
        final Store store = new Store();
        store.setAcctId("acctId");
        storeContractReceiptInfoVO.setStore(store);
        storeContractReceiptInfoVO.setRecommentWxSellerId("recommentWxSellerId");
        storeContractReceiptInfoVO.setRecommentAliSellerId("recommentAliSellerId");
        when(mockStoreFeignClient.getStoreContractReciptInfo(0L)).thenReturn(storeContractReceiptInfoVO);

        // Configure CustomerServiceFeign.info(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final BankcardVo bankcardVo = new BankcardVo();
        bankcardVo.setCardId("cardId");
        bankcardVo.setCardNo("cardNo");
        bankcardVo.setAcctName("acctName");
        bankcardVo.setBankCode("bankCode");
        bankcardVo.setBankName("bankName");
        final Result<BankcardVo> bankcardVoResult = new Result<>(false, errorContext, bankcardVo);
        final QueryBankcardInfoReq var1 = new QueryBankcardInfoReq();
        var1.setCardId("cardId");
        when(mockCustomerServiceFeign.info(var1)).thenReturn(bankcardVoResult);

        // Configure StmAccountFacade.detailV2(...).
        final AccountQuery accountQuery = new AccountQuery();
        accountQuery.setCurrent(0);
        accountQuery.setPageSize(0);
        accountQuery.setAccountId("accountId");
        accountQuery.setStoreId("UIN_PLF_STORE_ID101");
        accountQuery.setAccountType("accountType");
        when(mockStmAccountFacade.detailV2(accountQuery)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderOfflineService.queryOrderOfflineList(...).
        final OrderOfflinePO orderOfflinePO = new OrderOfflinePO();
        orderOfflinePO.setEnabledFlag(0);
        orderOfflinePO.setPaySn("paySn");
        orderOfflinePO.setReceiptAccount("receiptAccount");
        orderOfflinePO.setReceiptAmount(new BigDecimal("0.00"));
        orderOfflinePO.setReceiptTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<OrderOfflinePO> orderOfflinePOS = Arrays.asList(orderOfflinePO);
        when(mockOrderOfflineService.queryOrderOfflineList("paySn")).thenReturn(orderOfflinePOS);

        // Configure IOrderService.list(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        final List<OrderPO> orderPOList1 = Arrays.asList(orderPO1);
        when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(orderPOList1);

        // Configure OrderPayMapper.updateByPrimaryKeySelective(...).
        final OrderPayPO record = new OrderPayPO();
        record.setPayId(0);
        record.setPaySn("paySn");
        record.setOrderSn("pOrderSn");
        record.setPayAmount(new BigDecimal("0.00"));
        record.setMemberId(0);
        record.setApiPayState("0");
        record.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        record.setTradeSn("memberName");
        record.setPaymentName("paymentName");
        record.setPaymentCode("paymentCode");
        record.setLoanSuccess(0);
        record.setEnjoyPayVipFlag(0);
        record.setPayWayExtraInfo(new JSONObject(0, false));
        record.setOutBizSource("channel");
        record.setOutBizId("paySn");
        when(mockOrderPayMapper.updateByPrimaryKeySelective(record)).thenReturn(0);

        when(mockPromotionCommonFeignClient.orderPaySuccess("memberName", 0, "paySn", "memberName", "paymentName",
                "paymentCode")).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderProductMapper.selectOne(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setStoreName("memberName");
        orderProductPO1.setGoodsName("memberName");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setSpellTeamId(0);
        orderProductPO1.setEnabledFlag(0);
        when(mockOrderProductMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderProductPO1);

        // Configure OrderProductMapper.selectList(...).
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setStoreName("memberName");
        orderProductPO2.setGoodsName("memberName");
        orderProductPO2.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO2.setProductNum(0);
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setSpellTeamId(0);
        orderProductPO2.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO2);
        when(mockOrderProductMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);
        final List<OrderPromotionSendCouponPO> orderPromotionSendCouponPOS = Arrays.asList(orderPromotionSendCouponPO);
        final OrderPromotionSendCouponExample example1 = new OrderPromotionSendCouponExample();
        example1.setSendCouponIdNotEquals(0);
        example1.setSendCouponIdIn("sendCouponIdIn");
        example1.setSendCouponId(0);
        example1.setOrderSn("memberName");
        example1.setOrderSnLike("orderSnLike");
        when(mockOrderPromotionSendCouponMapper.listByExample(example1)).thenReturn(orderPromotionSendCouponPOS);

        // Configure CouponFeignClient.getCouponByCouponId(...).
        final CouponVO couponVO = new CouponVO();
        couponVO.setCouponId(0);
        couponVO.setCouponType(0);
        couponVO.setReceivedNum(0);
        couponVO.setRandomMax(new BigDecimal("0.00"));
        couponVO.setRandomMin(new BigDecimal("0.00"));
        couponVO.setPublishNum(0);
        couponVO.setEffectiveTimeType(0);
        couponVO.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setCycle(0);
        couponVO.setState(0);
        couponVO.setStoreId(0L);
        couponVO.setUseType(0);
        when(mockCouponFeignClient.getCouponByCouponId(0)).thenReturn(couponVO);

        // Configure IOrderService.getById(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("memberName");
        orderPO2.setUserNo("userNo");
        orderPO2.setPaySn("paySn");
        orderPO2.setSellerId("sellerId");
        orderPO2.setBankPayTrxNo("bankPayTrxNo");
        orderPO2.setStoreId(0L);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setComposePayName("composeWay");
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setServiceFee(new BigDecimal("0.00"));
        orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO2.setSettleMode("settleMode");
        orderPO2.setFinanceRuleCode("financeRuleCode");
        orderPO2.setIsDelivery(0);
        orderPO2.setChannel("channel");
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setCustomerConfirmStatus(0);
        orderPO2.setOrderPlaceUserRoleCode(0);
        orderPO2.setExchangeFlag(0);
        when(mockOrderService.getById(0)).thenReturn(orderPO2);

        // Configure IOrderAmountStateRecordService.initOrderAmountState(...).
        final ErrorContext errorContext1 = new ErrorContext();
        errorContext1.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext1.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult = new Result<>(false, errorContext1, null);
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("memberName");
        orderPO3.setUserNo("userNo");
        orderPO3.setPaySn("paySn");
        orderPO3.setSellerId("sellerId");
        orderPO3.setBankPayTrxNo("bankPayTrxNo");
        orderPO3.setStoreId(0L);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setComposePayName("composeWay");
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setServiceFee(new BigDecimal("0.00"));
        orderPO3.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO3.setSettleMode("settleMode");
        orderPO3.setFinanceRuleCode("financeRuleCode");
        orderPO3.setIsDelivery(0);
        orderPO3.setChannel("channel");
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setCustomerConfirmStatus(0);
        orderPO3.setOrderPlaceUserRoleCode(0);
        orderPO3.setExchangeFlag(0);
        when(mockOrderAmountRecordService.initOrderAmountState(orderPO3)).thenReturn(voidResult);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO3 = new OrderProductPO();
        orderProductPO3.setOrderProductId(0L);
        orderProductPO3.setOrderSn("orderSn");
        orderProductPO3.setStoreName("memberName");
        orderProductPO3.setGoodsName("memberName");
        orderProductPO3.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO3.setProductNum(0);
        orderProductPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO3.setServiceFee(new BigDecimal("0.00"));
        orderProductPO3.setSpellTeamId(0);
        orderProductPO3.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS1 = Arrays.asList(orderProductPO3);
        when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(orderProductPOS1);

        // Run the test
        orderPayModelUnderTest.wxAlipayCallBack(req, payInfoExtraInfoVO);

        // Verify the results
        // Confirm PayService.paySuccessProcess(...).
        final PaymentNotifyVO req2 = new PaymentNotifyVO();
        req2.setOrderOn("paySn");
        req2.setPayCode("memberName");
        req2.setPayTradeNo("memberName");
        req2.setRelPayAmt(new BigDecimal("0.00"));
        req2.setPayStatus(0);
        req2.setPayWay("payWay");
        req2.setBankPayTrxNos(new HashMap<>());
        req2.setNewOrder(false);
        verify(mockPayService).paySuccessProcess(0, req2,
                new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"), new HashMap<>());

        // Confirm BankTransferModel.transferSuccess(...).
        final PaymentNotifyVO req3 = new PaymentNotifyVO();
        req3.setOrderOn("paySn");
        req3.setPayCode("memberName");
        req3.setPayTradeNo("memberName");
        req3.setRelPayAmt(new BigDecimal("0.00"));
        req3.setPayStatus(0);
        req3.setPayWay("payWay");
        req3.setBankPayTrxNos(new HashMap<>());
        req3.setNewOrder(false);
        verify(mockBankTransferModel).transferSuccess(req3);
        verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
                new BigDecimal("0.00"));
        verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 20, 0, "订单支付完成",
                OrderCreateChannel.H5);
        verify(mockOrderProductModel).orderPaySuccessAddSales("memberName");
        verify(mockOrderService).setOrdersDeliverable(Arrays.asList("value"));

        // Confirm CouponMemberFeignClient.saveCouponMember(...).
        final CouponMember couponMember = new CouponMember();
        couponMember.setCouponId(0);
        couponMember.setCouponCode("couponCode");
        couponMember.setStoreId(0L);
        couponMember.setMemberId(0);
        couponMember.setMemberName("memberName");
        couponMember.setReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseState(0);
        couponMember.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseType(0);
        couponMember.setRandomAmount(new BigDecimal("0.00"));
        verify(mockCouponMemberFeignClient).saveCouponMember(couponMember);

        // Confirm CouponUseLogFeignClient.saveCouponUseLog(...).
        final CouponUseLog couponUseLog = new CouponUseLog();
        couponUseLog.setCouponCode("couponCode");
        couponUseLog.setMemberId(0);
        couponUseLog.setMemberName("memberName");
        couponUseLog.setStoreId(0L);
        couponUseLog.setLogType(0);
        couponUseLog.setLogTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponUseLog.setLogContent("logContent");
        verify(mockCouponUseLogFeignClient).saveCouponUseLog(couponUseLog);

        // Confirm CouponFeignClient.updateOrderCoupon(...).
        final Coupon coupon = new Coupon();
        coupon.setCouponId(0);
        coupon.setCouponName("couponName");
        coupon.setCouponContent("couponContent");
        coupon.setDescription("description");
        coupon.setReceivedNum(0);
        verify(mockCouponFeignClient).updateOrderCoupon(coupon);

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO4 = new OrderPO();
        orderPO4.setOrderId(0);
        orderPO4.setOrderSn("memberName");
        orderPO4.setUserNo("userNo");
        orderPO4.setPaySn("paySn");
        orderPO4.setSellerId("sellerId");
        orderPO4.setBankPayTrxNo("bankPayTrxNo");
        orderPO4.setStoreId(0L);
        orderPO4.setRecommendStoreId(0L);
        orderPO4.setMemberName("memberName");
        orderPO4.setMemberId(0);
        orderPO4.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setOrderState(0);
        orderPO4.setLoanPayState(0);
        orderPO4.setPaymentName("paymentName");
        orderPO4.setPaymentCode("paymentCode");
        orderPO4.setOrderAmount(new BigDecimal("0.00"));
        orderPO4.setGoodsAmount(new BigDecimal("0.00"));
        orderPO4.setExpressFee(new BigDecimal("0.00"));
        orderPO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO4.setComposePayName("composeWay");
        orderPO4.setBalanceAmount(new BigDecimal("0.00"));
        orderPO4.setPayAmount(new BigDecimal("0.00"));
        orderPO4.setAreaCode("areaCode");
        orderPO4.setOrderType(0);
        orderPO4.setServiceFee(new BigDecimal("0.00"));
        orderPO4.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO4.setSettleMode("settleMode");
        orderPO4.setFinanceRuleCode("financeRuleCode");
        orderPO4.setIsDelivery(0);
        orderPO4.setChannel("channel");
        orderPO4.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO4.setNewOrder(false);
        orderPO4.setCustomerConfirmStatus(0);
        orderPO4.setOrderPlaceUserRoleCode(0);
        orderPO4.setExchangeFlag(0);
        verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO4, OrderEventEnum.CREATE,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg",
                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));

        // Confirm BankTransferModel.transferExpire(...).
        final PaymentNotifyVO req4 = new PaymentNotifyVO();
        req4.setOrderOn("paySn");
        req4.setPayCode("memberName");
        req4.setPayTradeNo("memberName");
        req4.setRelPayAmt(new BigDecimal("0.00"));
        req4.setPayStatus(0);
        req4.setPayWay("payWay");
        req4.setBankPayTrxNos(new HashMap<>());
        req4.setNewOrder(false);
        verify(mockBankTransferModel).transferExpire(req4);
        verify(mockOrderService).update(any(LambdaUpdateWrapper.class));
    }*/

   /* @Test
    void testWxAlipayCallBack_OrderModelReturnsNoItems() throws Exception {
        // Setup
        final PaymentNotifyVO req = new PaymentNotifyVO();
        req.setOrderOn("paySn");
        req.setPayCode("memberName");
        req.setPayTradeNo("memberName");
        req.setRelPayAmt(new BigDecimal("0.00"));
        req.setPayStatus(0);
        req.setPayWay("payWay");
        req.setBankPayTrxNos(new HashMap<>());
        req.setNewOrder(false);

        final PayInfoExtraInfoVO payInfoExtraInfoVO = new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn");

        // Configure PayService.buildPayNotifyOrderOn(...).
        final PaymentNotifyVO req1 = new PaymentNotifyVO();
        req1.setOrderOn("paySn");
        req1.setPayCode("memberName");
        req1.setPayTradeNo("memberName");
        req1.setRelPayAmt(new BigDecimal("0.00"));
        req1.setPayStatus(0);
        req1.setPayWay("payWay");
        req1.setBankPayTrxNos(new HashMap<>());
        req1.setNewOrder(false);
        when(mockPayService.buildPayNotifyOrderOn(req1,
                new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"), false)).thenReturn("paySn");

        // Configure OrderPayMapper.getByPrimaryKey(...).
        final OrderPayPO orderPayPO = new OrderPayPO();
        orderPayPO.setPayId(0);
        orderPayPO.setPaySn("paySn");
        orderPayPO.setOrderSn("pOrderSn");
        orderPayPO.setPayAmount(new BigDecimal("0.00"));
        orderPayPO.setMemberId(0);
        orderPayPO.setApiPayState("0");
        orderPayPO.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPayPO.setTradeSn("memberName");
        orderPayPO.setPaymentName("paymentName");
        orderPayPO.setPaymentCode("paymentCode");
        orderPayPO.setLoanSuccess(0);
        orderPayPO.setEnjoyPayVipFlag(0);
        orderPayPO.setPayWayExtraInfo(new JSONObject(0, false));
        orderPayPO.setOutBizSource("channel");
        orderPayPO.setOutBizId("paySn");
        when(mockOrderPayMapper.getByPrimaryKey("paySn")).thenReturn(orderPayPO);

        // Configure OrderModel.getOrderList(...).
        final OrderExample example = new OrderExample();
        example.setOrderIdNotEquals(0);
        example.setUserNo("userNo");
        example.setUserMobile("userMobile");
        example.setOrderSn("memberName");
        example.setPaySn("paySn");
        when(mockOrderModel.getOrderList(eq(example), any(PagerInfo.class))).thenReturn(Collections.emptyList());

        when(mockOrderService.lambdaUpdate()).thenReturn(new LambdaUpdateChainWrapper<>(null));
        when(mockPayService.payAmount("payNo")).thenReturn(new BigDecimal("0.00"));
        when(mockOrderPayProcessStrategyContext.getStrategy(0)).thenReturn(null);
        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategory storeBindCategory = new StoreBindCategory();
        storeBindCategory.setBindId(0);
        storeBindCategory.setStoreId(0L);
        storeBindCategory.setCreateVendorId(0L);
        storeBindCategory.setGoodsCategoryId1(0);
        storeBindCategory.setGoodsCategoryId2(0);
        final List<StoreBindCategory> storeBindCategories = Arrays.asList(storeBindCategory);
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                .thenReturn(storeBindCategories);

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        when(mockAccountCardFacade.defaultCard(0L)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure BillOperatinIntegration.detailByBankAccount(...).
        final AccountCard accountCard = new AccountCard();
        accountCard.setCardId(0L);
        accountCard.setStoreId("storeId");
        accountCard.setStoreName("storeName");
        accountCard.setOrgType("orgType");
        accountCard.setBankAccountNumber("bankAccountNumber");
        when(mockBillOperatinIntegration.detailByBankAccount("UIN_PLF_STORE_ID101",
                AccountCardTypeEnum.UNI_JS_PLF_SUP)).thenReturn(accountCard);

        // Configure IBzBankTransferService.list(...).
        final BzBankTransferPO bzBankTransferPO = new BzBankTransferPO();
        bzBankTransferPO.setEnabledFlag(0);
        bzBankTransferPO.setPaySn("paySn");
        bzBankTransferPO.setOutOrderNo("outOrderNo");
        bzBankTransferPO.setPayAmount(new BigDecimal("0.00"));
        bzBankTransferPO.setReceiptAccount("receiptAccount");
        final List<BzBankTransferPO> bzBankTransferPOS = Arrays.asList(bzBankTransferPO);
        when(mockBzBankTransferService.list(any(LambdaQueryWrapper.class))).thenReturn(bzBankTransferPOS);

        // Configure StoreFeignClient.getStoreContractReciptInfo(...).
        final StoreContractReceiptInfoVO storeContractReceiptInfoVO = new StoreContractReceiptInfoVO();
        storeContractReceiptInfoVO.setWxSellerId("wxSellerId");
        storeContractReceiptInfoVO.setAliSellerId("aliSellerId");
        final Store store = new Store();
        store.setAcctId("acctId");
        storeContractReceiptInfoVO.setStore(store);
        storeContractReceiptInfoVO.setRecommentWxSellerId("recommentWxSellerId");
        storeContractReceiptInfoVO.setRecommentAliSellerId("recommentAliSellerId");
        when(mockStoreFeignClient.getStoreContractReciptInfo(0L)).thenReturn(storeContractReceiptInfoVO);

        // Configure CustomerServiceFeign.info(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final BankcardVo bankcardVo = new BankcardVo();
        bankcardVo.setCardId("cardId");
        bankcardVo.setCardNo("cardNo");
        bankcardVo.setAcctName("acctName");
        bankcardVo.setBankCode("bankCode");
        bankcardVo.setBankName("bankName");
        final Result<BankcardVo> bankcardVoResult = new Result<>(false, errorContext, bankcardVo);
        final QueryBankcardInfoReq var1 = new QueryBankcardInfoReq();
        var1.setCardId("cardId");
        when(mockCustomerServiceFeign.info(var1)).thenReturn(bankcardVoResult);

        // Configure StmAccountFacade.detailV2(...).
        final AccountQuery accountQuery = new AccountQuery();
        accountQuery.setCurrent(0);
        accountQuery.setPageSize(0);
        accountQuery.setAccountId("accountId");
        accountQuery.setStoreId("UIN_PLF_STORE_ID101");
        accountQuery.setAccountType("accountType");
        when(mockStmAccountFacade.detailV2(accountQuery)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderOfflineService.queryOrderOfflineList(...).
        final OrderOfflinePO orderOfflinePO = new OrderOfflinePO();
        orderOfflinePO.setEnabledFlag(0);
        orderOfflinePO.setPaySn("paySn");
        orderOfflinePO.setReceiptAccount("receiptAccount");
        orderOfflinePO.setReceiptAmount(new BigDecimal("0.00"));
        orderOfflinePO.setReceiptTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<OrderOfflinePO> orderOfflinePOS = Arrays.asList(orderOfflinePO);
        when(mockOrderOfflineService.queryOrderOfflineList("paySn")).thenReturn(orderOfflinePOS);

        // Configure IOrderService.list(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO);
        when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(orderPOList);

        // Configure OrderPayMapper.updateByPrimaryKeySelective(...).
        final OrderPayPO record = new OrderPayPO();
        record.setPayId(0);
        record.setPaySn("paySn");
        record.setOrderSn("pOrderSn");
        record.setPayAmount(new BigDecimal("0.00"));
        record.setMemberId(0);
        record.setApiPayState("0");
        record.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        record.setTradeSn("memberName");
        record.setPaymentName("paymentName");
        record.setPaymentCode("paymentCode");
        record.setLoanSuccess(0);
        record.setEnjoyPayVipFlag(0);
        record.setPayWayExtraInfo(new JSONObject(0, false));
        record.setOutBizSource("channel");
        record.setOutBizId("paySn");
        when(mockOrderPayMapper.updateByPrimaryKeySelective(record)).thenReturn(0);

        when(mockPromotionCommonFeignClient.orderPaySuccess("memberName", 0, "paySn", "memberName", "paymentName",
                "paymentCode")).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderProductMapper.selectOne(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setStoreName("memberName");
        orderProductPO1.setGoodsName("memberName");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setSpellTeamId(0);
        orderProductPO1.setEnabledFlag(0);
        when(mockOrderProductMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderProductPO1);

        // Configure OrderProductMapper.selectList(...).
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setStoreName("memberName");
        orderProductPO2.setGoodsName("memberName");
        orderProductPO2.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO2.setProductNum(0);
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setSpellTeamId(0);
        orderProductPO2.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO2);
        when(mockOrderProductMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);
        final List<OrderPromotionSendCouponPO> orderPromotionSendCouponPOS = Arrays.asList(orderPromotionSendCouponPO);
        final OrderPromotionSendCouponExample example1 = new OrderPromotionSendCouponExample();
        example1.setSendCouponIdNotEquals(0);
        example1.setSendCouponIdIn("sendCouponIdIn");
        example1.setSendCouponId(0);
        example1.setOrderSn("memberName");
        example1.setOrderSnLike("orderSnLike");
        when(mockOrderPromotionSendCouponMapper.listByExample(example1)).thenReturn(orderPromotionSendCouponPOS);

        // Configure CouponFeignClient.getCouponByCouponId(...).
        final CouponVO couponVO = new CouponVO();
        couponVO.setCouponId(0);
        couponVO.setCouponType(0);
        couponVO.setReceivedNum(0);
        couponVO.setRandomMax(new BigDecimal("0.00"));
        couponVO.setRandomMin(new BigDecimal("0.00"));
        couponVO.setPublishNum(0);
        couponVO.setEffectiveTimeType(0);
        couponVO.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setCycle(0);
        couponVO.setState(0);
        couponVO.setStoreId(0L);
        couponVO.setUseType(0);
        when(mockCouponFeignClient.getCouponByCouponId(0)).thenReturn(couponVO);

        // Configure IOrderService.getById(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        when(mockOrderService.getById(0)).thenReturn(orderPO1);

        // Configure IOrderAmountStateRecordService.initOrderAmountState(...).
        final ErrorContext errorContext1 = new ErrorContext();
        errorContext1.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext1.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult = new Result<>(false, errorContext1, null);
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("memberName");
        orderPO2.setUserNo("userNo");
        orderPO2.setPaySn("paySn");
        orderPO2.setSellerId("sellerId");
        orderPO2.setBankPayTrxNo("bankPayTrxNo");
        orderPO2.setStoreId(0L);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setComposePayName("composeWay");
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setServiceFee(new BigDecimal("0.00"));
        orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO2.setSettleMode("settleMode");
        orderPO2.setFinanceRuleCode("financeRuleCode");
        orderPO2.setIsDelivery(0);
        orderPO2.setChannel("channel");
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setCustomerConfirmStatus(0);
        orderPO2.setOrderPlaceUserRoleCode(0);
        orderPO2.setExchangeFlag(0);
        when(mockOrderAmountRecordService.initOrderAmountState(orderPO2)).thenReturn(voidResult);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO3 = new OrderProductPO();
        orderProductPO3.setOrderProductId(0L);
        orderProductPO3.setOrderSn("orderSn");
        orderProductPO3.setStoreName("memberName");
        orderProductPO3.setGoodsName("memberName");
        orderProductPO3.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO3.setProductNum(0);
        orderProductPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO3.setServiceFee(new BigDecimal("0.00"));
        orderProductPO3.setSpellTeamId(0);
        orderProductPO3.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS1 = Arrays.asList(orderProductPO3);
        when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(orderProductPOS1);

        // Run the test
        orderPayModelUnderTest.wxAlipayCallBack(req, payInfoExtraInfoVO);

        // Verify the results
        // Confirm PayService.paySuccessProcess(...).
        final PaymentNotifyVO req2 = new PaymentNotifyVO();
        req2.setOrderOn("paySn");
        req2.setPayCode("memberName");
        req2.setPayTradeNo("memberName");
        req2.setRelPayAmt(new BigDecimal("0.00"));
        req2.setPayStatus(0);
        req2.setPayWay("payWay");
        req2.setBankPayTrxNos(new HashMap<>());
        req2.setNewOrder(false);
        verify(mockPayService).paySuccessProcess(0, req2,
                new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"), new HashMap<>());

        // Confirm BankTransferModel.transferSuccess(...).
        final PaymentNotifyVO req3 = new PaymentNotifyVO();
        req3.setOrderOn("paySn");
        req3.setPayCode("memberName");
        req3.setPayTradeNo("memberName");
        req3.setRelPayAmt(new BigDecimal("0.00"));
        req3.setPayStatus(0);
        req3.setPayWay("payWay");
        req3.setBankPayTrxNos(new HashMap<>());
        req3.setNewOrder(false);
        verify(mockBankTransferModel).transferSuccess(req3);
        verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
                new BigDecimal("0.00"));
        verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 20, 0, "订单支付完成",
                OrderCreateChannel.H5);
        verify(mockOrderProductModel).orderPaySuccessAddSales("memberName");
        verify(mockOrderService).setOrdersDeliverable(Arrays.asList("value"));

        // Confirm CouponMemberFeignClient.saveCouponMember(...).
        final CouponMember couponMember = new CouponMember();
        couponMember.setCouponId(0);
        couponMember.setCouponCode("couponCode");
        couponMember.setStoreId(0L);
        couponMember.setMemberId(0);
        couponMember.setMemberName("memberName");
        couponMember.setReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseState(0);
        couponMember.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseType(0);
        couponMember.setRandomAmount(new BigDecimal("0.00"));
        verify(mockCouponMemberFeignClient).saveCouponMember(couponMember);

        // Confirm CouponUseLogFeignClient.saveCouponUseLog(...).
        final CouponUseLog couponUseLog = new CouponUseLog();
        couponUseLog.setCouponCode("couponCode");
        couponUseLog.setMemberId(0);
        couponUseLog.setMemberName("memberName");
        couponUseLog.setStoreId(0L);
        couponUseLog.setLogType(0);
        couponUseLog.setLogTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponUseLog.setLogContent("logContent");
        verify(mockCouponUseLogFeignClient).saveCouponUseLog(couponUseLog);

        // Confirm CouponFeignClient.updateOrderCoupon(...).
        final Coupon coupon = new Coupon();
        coupon.setCouponId(0);
        coupon.setCouponName("couponName");
        coupon.setCouponContent("couponContent");
        coupon.setDescription("description");
        coupon.setReceivedNum(0);
        verify(mockCouponFeignClient).updateOrderCoupon(coupon);

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("memberName");
        orderPO3.setUserNo("userNo");
        orderPO3.setPaySn("paySn");
        orderPO3.setSellerId("sellerId");
        orderPO3.setBankPayTrxNo("bankPayTrxNo");
        orderPO3.setStoreId(0L);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setComposePayName("composeWay");
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setServiceFee(new BigDecimal("0.00"));
        orderPO3.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO3.setSettleMode("settleMode");
        orderPO3.setFinanceRuleCode("financeRuleCode");
        orderPO3.setIsDelivery(0);
        orderPO3.setChannel("channel");
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setCustomerConfirmStatus(0);
        orderPO3.setOrderPlaceUserRoleCode(0);
        orderPO3.setExchangeFlag(0);
        verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO3, OrderEventEnum.CREATE,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg",
                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));

        // Confirm BankTransferModel.transferExpire(...).
        final PaymentNotifyVO req4 = new PaymentNotifyVO();
        req4.setOrderOn("paySn");
        req4.setPayCode("memberName");
        req4.setPayTradeNo("memberName");
        req4.setRelPayAmt(new BigDecimal("0.00"));
        req4.setPayStatus(0);
        req4.setPayWay("payWay");
        req4.setBankPayTrxNos(new HashMap<>());
        req4.setNewOrder(false);
        verify(mockBankTransferModel).transferExpire(req4);
        verify(mockOrderService).update(any(LambdaUpdateWrapper.class));
    }
*/
  /*  @Test
    void testWxAlipayCallBack_StoreBindCategoryFeignClientReturnsNoItems() throws Exception {
        // Setup
        final PaymentNotifyVO req = new PaymentNotifyVO();
        req.setOrderOn("paySn");
        req.setPayCode("memberName");
        req.setPayTradeNo("memberName");
        req.setRelPayAmt(new BigDecimal("0.00"));
        req.setPayStatus(0);
        req.setPayWay("payWay");
        req.setBankPayTrxNos(new HashMap<>());
        req.setNewOrder(false);

        final PayInfoExtraInfoVO payInfoExtraInfoVO = new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn");

        // Configure PayService.buildPayNotifyOrderOn(...).
        final PaymentNotifyVO req1 = new PaymentNotifyVO();
        req1.setOrderOn("paySn");
        req1.setPayCode("memberName");
        req1.setPayTradeNo("memberName");
        req1.setRelPayAmt(new BigDecimal("0.00"));
        req1.setPayStatus(0);
        req1.setPayWay("payWay");
        req1.setBankPayTrxNos(new HashMap<>());
        req1.setNewOrder(false);
        when(mockPayService.buildPayNotifyOrderOn(req1,
                new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"), false)).thenReturn("paySn");

        // Configure OrderPayMapper.getByPrimaryKey(...).
        final OrderPayPO orderPayPO = new OrderPayPO();
        orderPayPO.setPayId(0);
        orderPayPO.setPaySn("paySn");
        orderPayPO.setOrderSn("pOrderSn");
        orderPayPO.setPayAmount(new BigDecimal("0.00"));
        orderPayPO.setMemberId(0);
        orderPayPO.setApiPayState("0");
        orderPayPO.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPayPO.setTradeSn("memberName");
        orderPayPO.setPaymentName("paymentName");
        orderPayPO.setPaymentCode("paymentCode");
        orderPayPO.setLoanSuccess(0);
        orderPayPO.setEnjoyPayVipFlag(0);
        orderPayPO.setPayWayExtraInfo(new JSONObject(0, false));
        orderPayPO.setOutBizSource("channel");
        orderPayPO.setOutBizId("paySn");
        when(mockOrderPayMapper.getByPrimaryKey("paySn")).thenReturn(orderPayPO);

        // Configure OrderModel.getOrderList(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO);
        final OrderExample example = new OrderExample();
        example.setOrderIdNotEquals(0);
        example.setUserNo("userNo");
        example.setUserMobile("userMobile");
        example.setOrderSn("memberName");
        example.setPaySn("paySn");
        when(mockOrderModel.getOrderList(eq(example), any(PagerInfo.class))).thenReturn(orderPOList);

        when(mockOrderService.lambdaUpdate()).thenReturn(new LambdaUpdateChainWrapper<>(null));
        when(mockPayService.payAmount("payNo")).thenReturn(new BigDecimal("0.00"));
        when(mockOrderPayProcessStrategyContext.getStrategy(0)).thenReturn(null);
        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                .thenReturn(Collections.emptyList());

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        when(mockAccountCardFacade.defaultCard(0L)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure BillOperatinIntegration.detailByBankAccount(...).
        final AccountCard accountCard = new AccountCard();
        accountCard.setCardId(0L);
        accountCard.setStoreId("storeId");
        accountCard.setStoreName("storeName");
        accountCard.setOrgType("orgType");
        accountCard.setBankAccountNumber("bankAccountNumber");
        when(mockBillOperatinIntegration.detailByBankAccount("UIN_PLF_STORE_ID101",
                AccountCardTypeEnum.UNI_JS_PLF_SUP)).thenReturn(accountCard);

        // Configure IBzBankTransferService.list(...).
        final BzBankTransferPO bzBankTransferPO = new BzBankTransferPO();
        bzBankTransferPO.setEnabledFlag(0);
        bzBankTransferPO.setPaySn("paySn");
        bzBankTransferPO.setOutOrderNo("outOrderNo");
        bzBankTransferPO.setPayAmount(new BigDecimal("0.00"));
        bzBankTransferPO.setReceiptAccount("receiptAccount");
        final List<BzBankTransferPO> bzBankTransferPOS = Arrays.asList(bzBankTransferPO);
        when(mockBzBankTransferService.list(any(LambdaQueryWrapper.class))).thenReturn(bzBankTransferPOS);

        // Configure StoreFeignClient.getStoreContractReciptInfo(...).
        final StoreContractReceiptInfoVO storeContractReceiptInfoVO = new StoreContractReceiptInfoVO();
        storeContractReceiptInfoVO.setWxSellerId("wxSellerId");
        storeContractReceiptInfoVO.setAliSellerId("aliSellerId");
        final Store store = new Store();
        store.setAcctId("acctId");
        storeContractReceiptInfoVO.setStore(store);
        storeContractReceiptInfoVO.setRecommentWxSellerId("recommentWxSellerId");
        storeContractReceiptInfoVO.setRecommentAliSellerId("recommentAliSellerId");
        when(mockStoreFeignClient.getStoreContractReciptInfo(0L)).thenReturn(storeContractReceiptInfoVO);

        // Configure CustomerServiceFeign.info(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final BankcardVo bankcardVo = new BankcardVo();
        bankcardVo.setCardId("cardId");
        bankcardVo.setCardNo("cardNo");
        bankcardVo.setAcctName("acctName");
        bankcardVo.setBankCode("bankCode");
        bankcardVo.setBankName("bankName");
        final Result<BankcardVo> bankcardVoResult = new Result<>(false, errorContext, bankcardVo);
        final QueryBankcardInfoReq var1 = new QueryBankcardInfoReq();
        var1.setCardId("cardId");
        when(mockCustomerServiceFeign.info(var1)).thenReturn(bankcardVoResult);

        // Configure StmAccountFacade.detailV2(...).
        final AccountQuery accountQuery = new AccountQuery();
        accountQuery.setCurrent(0);
        accountQuery.setPageSize(0);
        accountQuery.setAccountId("accountId");
        accountQuery.setStoreId("UIN_PLF_STORE_ID101");
        accountQuery.setAccountType("accountType");
        when(mockStmAccountFacade.detailV2(accountQuery)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderOfflineService.queryOrderOfflineList(...).
        final OrderOfflinePO orderOfflinePO = new OrderOfflinePO();
        orderOfflinePO.setEnabledFlag(0);
        orderOfflinePO.setPaySn("paySn");
        orderOfflinePO.setReceiptAccount("receiptAccount");
        orderOfflinePO.setReceiptAmount(new BigDecimal("0.00"));
        orderOfflinePO.setReceiptTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<OrderOfflinePO> orderOfflinePOS = Arrays.asList(orderOfflinePO);
        when(mockOrderOfflineService.queryOrderOfflineList("paySn")).thenReturn(orderOfflinePOS);

        // Configure IOrderService.list(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        final List<OrderPO> orderPOList1 = Arrays.asList(orderPO1);
        when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(orderPOList1);

        when(mockPromotionCommonFeignClient.orderPaySuccess("memberName", 0, "paySn", "memberName", "paymentName",
                "paymentCode")).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderProductMapper.selectOne(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setStoreName("memberName");
        orderProductPO1.setGoodsName("memberName");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setSpellTeamId(0);
        orderProductPO1.setEnabledFlag(0);
        when(mockOrderProductMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderProductPO1);

        // Configure OrderProductMapper.selectList(...).
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setStoreName("memberName");
        orderProductPO2.setGoodsName("memberName");
        orderProductPO2.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO2.setProductNum(0);
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setSpellTeamId(0);
        orderProductPO2.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO2);
        when(mockOrderProductMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);
        final List<OrderPromotionSendCouponPO> orderPromotionSendCouponPOS = Arrays.asList(orderPromotionSendCouponPO);
        final OrderPromotionSendCouponExample example1 = new OrderPromotionSendCouponExample();
        example1.setSendCouponIdNotEquals(0);
        example1.setSendCouponIdIn("sendCouponIdIn");
        example1.setSendCouponId(0);
        example1.setOrderSn("memberName");
        example1.setOrderSnLike("orderSnLike");
        when(mockOrderPromotionSendCouponMapper.listByExample(example1)).thenReturn(orderPromotionSendCouponPOS);

        // Configure CouponFeignClient.getCouponByCouponId(...).
        final CouponVO couponVO = new CouponVO();
        couponVO.setCouponId(0);
        couponVO.setCouponType(0);
        couponVO.setReceivedNum(0);
        couponVO.setRandomMax(new BigDecimal("0.00"));
        couponVO.setRandomMin(new BigDecimal("0.00"));
        couponVO.setPublishNum(0);
        couponVO.setEffectiveTimeType(0);
        couponVO.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setCycle(0);
        couponVO.setState(0);
        couponVO.setStoreId(0L);
        couponVO.setUseType(0);
        when(mockCouponFeignClient.getCouponByCouponId(0)).thenReturn(couponVO);

        // Configure IOrderService.getById(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("memberName");
        orderPO2.setUserNo("userNo");
        orderPO2.setPaySn("paySn");
        orderPO2.setSellerId("sellerId");
        orderPO2.setBankPayTrxNo("bankPayTrxNo");
        orderPO2.setStoreId(0L);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setComposePayName("composeWay");
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setServiceFee(new BigDecimal("0.00"));
        orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO2.setSettleMode("settleMode");
        orderPO2.setFinanceRuleCode("financeRuleCode");
        orderPO2.setIsDelivery(0);
        orderPO2.setChannel("channel");
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setCustomerConfirmStatus(0);
        orderPO2.setOrderPlaceUserRoleCode(0);
        orderPO2.setExchangeFlag(0);
        when(mockOrderService.getById(0)).thenReturn(orderPO2);

        // Configure IOrderAmountStateRecordService.initOrderAmountState(...).
        final ErrorContext errorContext1 = new ErrorContext();
        errorContext1.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext1.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult = new Result<>(false, errorContext1, null);
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("memberName");
        orderPO3.setUserNo("userNo");
        orderPO3.setPaySn("paySn");
        orderPO3.setSellerId("sellerId");
        orderPO3.setBankPayTrxNo("bankPayTrxNo");
        orderPO3.setStoreId(0L);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setComposePayName("composeWay");
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setServiceFee(new BigDecimal("0.00"));
        orderPO3.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO3.setSettleMode("settleMode");
        orderPO3.setFinanceRuleCode("financeRuleCode");
        orderPO3.setIsDelivery(0);
        orderPO3.setChannel("channel");
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setCustomerConfirmStatus(0);
        orderPO3.setOrderPlaceUserRoleCode(0);
        orderPO3.setExchangeFlag(0);
        when(mockOrderAmountRecordService.initOrderAmountState(orderPO3)).thenReturn(voidResult);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO3 = new OrderProductPO();
        orderProductPO3.setOrderProductId(0L);
        orderProductPO3.setOrderSn("orderSn");
        orderProductPO3.setStoreName("memberName");
        orderProductPO3.setGoodsName("memberName");
        orderProductPO3.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO3.setProductNum(0);
        orderProductPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO3.setServiceFee(new BigDecimal("0.00"));
        orderProductPO3.setSpellTeamId(0);
        orderProductPO3.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS1 = Arrays.asList(orderProductPO3);
        when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(orderProductPOS1);

        // Run the test
        orderPayModelUnderTest.wxAlipayCallBack(req, payInfoExtraInfoVO);

        // Verify the results
        // Confirm PayService.paySuccessProcess(...).
        final PaymentNotifyVO req2 = new PaymentNotifyVO();
        req2.setOrderOn("paySn");
        req2.setPayCode("memberName");
        req2.setPayTradeNo("memberName");
        req2.setRelPayAmt(new BigDecimal("0.00"));
        req2.setPayStatus(0);
        req2.setPayWay("payWay");
        req2.setBankPayTrxNos(new HashMap<>());
        req2.setNewOrder(false);
        verify(mockPayService).paySuccessProcess(0, req2,
                new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"), new HashMap<>());

        // Confirm BankTransferModel.transferSuccess(...).
        final PaymentNotifyVO req3 = new PaymentNotifyVO();
        req3.setOrderOn("paySn");
        req3.setPayCode("memberName");
        req3.setPayTradeNo("memberName");
        req3.setRelPayAmt(new BigDecimal("0.00"));
        req3.setPayStatus(0);
        req3.setPayWay("payWay");
        req3.setBankPayTrxNos(new HashMap<>());
        req3.setNewOrder(false);
        verify(mockBankTransferModel).transferSuccess(req3);
        verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
                new BigDecimal("0.00"));
        verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 20, 0, "订单支付完成",
                OrderCreateChannel.H5);
        verify(mockOrderProductModel).orderPaySuccessAddSales("memberName");
        verify(mockOrderService).setOrdersDeliverable(Arrays.asList("value"));

        // Confirm CouponMemberFeignClient.saveCouponMember(...).
        final CouponMember couponMember = new CouponMember();
        couponMember.setCouponId(0);
        couponMember.setCouponCode("couponCode");
        couponMember.setStoreId(0L);
        couponMember.setMemberId(0);
        couponMember.setMemberName("memberName");
        couponMember.setReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseState(0);
        couponMember.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseType(0);
        couponMember.setRandomAmount(new BigDecimal("0.00"));
        verify(mockCouponMemberFeignClient).saveCouponMember(couponMember);

        // Confirm CouponUseLogFeignClient.saveCouponUseLog(...).
        final CouponUseLog couponUseLog = new CouponUseLog();
        couponUseLog.setCouponCode("couponCode");
        couponUseLog.setMemberId(0);
        couponUseLog.setMemberName("memberName");
        couponUseLog.setStoreId(0L);
        couponUseLog.setLogType(0);
        couponUseLog.setLogTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponUseLog.setLogContent("logContent");
        verify(mockCouponUseLogFeignClient).saveCouponUseLog(couponUseLog);

        // Confirm CouponFeignClient.updateOrderCoupon(...).
        final Coupon coupon = new Coupon();
        coupon.setCouponId(0);
        coupon.setCouponName("couponName");
        coupon.setCouponContent("couponContent");
        coupon.setDescription("description");
        coupon.setReceivedNum(0);
        verify(mockCouponFeignClient).updateOrderCoupon(coupon);

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO4 = new OrderPO();
        orderPO4.setOrderId(0);
        orderPO4.setOrderSn("memberName");
        orderPO4.setUserNo("userNo");
        orderPO4.setPaySn("paySn");
        orderPO4.setSellerId("sellerId");
        orderPO4.setBankPayTrxNo("bankPayTrxNo");
        orderPO4.setStoreId(0L);
        orderPO4.setRecommendStoreId(0L);
        orderPO4.setMemberName("memberName");
        orderPO4.setMemberId(0);
        orderPO4.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setOrderState(0);
        orderPO4.setLoanPayState(0);
        orderPO4.setPaymentName("paymentName");
        orderPO4.setPaymentCode("paymentCode");
        orderPO4.setOrderAmount(new BigDecimal("0.00"));
        orderPO4.setGoodsAmount(new BigDecimal("0.00"));
        orderPO4.setExpressFee(new BigDecimal("0.00"));
        orderPO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO4.setComposePayName("composeWay");
        orderPO4.setBalanceAmount(new BigDecimal("0.00"));
        orderPO4.setPayAmount(new BigDecimal("0.00"));
        orderPO4.setAreaCode("areaCode");
        orderPO4.setOrderType(0);
        orderPO4.setServiceFee(new BigDecimal("0.00"));
        orderPO4.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO4.setSettleMode("settleMode");
        orderPO4.setFinanceRuleCode("financeRuleCode");
        orderPO4.setIsDelivery(0);
        orderPO4.setChannel("channel");
        orderPO4.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO4.setNewOrder(false);
        orderPO4.setCustomerConfirmStatus(0);
        orderPO4.setOrderPlaceUserRoleCode(0);
        orderPO4.setExchangeFlag(0);
        verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO4, OrderEventEnum.CREATE,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg",
                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));
    }
*/
  /*  @Test
    void testWxAlipayCallBack_IBzBankTransferServiceReturnsNoItems() throws Exception {
        // Setup
        final PaymentNotifyVO req = new PaymentNotifyVO();
        req.setOrderOn("paySn");
        req.setPayCode("memberName");
        req.setPayTradeNo("memberName");
        req.setRelPayAmt(new BigDecimal("0.00"));
        req.setPayStatus(0);
        req.setPayWay("payWay");
        req.setBankPayTrxNos(new HashMap<>());
        req.setNewOrder(false);

        final PayInfoExtraInfoVO payInfoExtraInfoVO = new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn");

        // Configure PayService.buildPayNotifyOrderOn(...).
        final PaymentNotifyVO req1 = new PaymentNotifyVO();
        req1.setOrderOn("paySn");
        req1.setPayCode("memberName");
        req1.setPayTradeNo("memberName");
        req1.setRelPayAmt(new BigDecimal("0.00"));
        req1.setPayStatus(0);
        req1.setPayWay("payWay");
        req1.setBankPayTrxNos(new HashMap<>());
        req1.setNewOrder(false);
        when(mockPayService.buildPayNotifyOrderOn(req1,
                new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"), false)).thenReturn("paySn");

        // Configure OrderPayMapper.getByPrimaryKey(...).
        final OrderPayPO orderPayPO = new OrderPayPO();
        orderPayPO.setPayId(0);
        orderPayPO.setPaySn("paySn");
        orderPayPO.setOrderSn("pOrderSn");
        orderPayPO.setPayAmount(new BigDecimal("0.00"));
        orderPayPO.setMemberId(0);
        orderPayPO.setApiPayState("0");
        orderPayPO.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPayPO.setTradeSn("memberName");
        orderPayPO.setPaymentName("paymentName");
        orderPayPO.setPaymentCode("paymentCode");
        orderPayPO.setLoanSuccess(0);
        orderPayPO.setEnjoyPayVipFlag(0);
        orderPayPO.setPayWayExtraInfo(new JSONObject(0, false));
        orderPayPO.setOutBizSource("channel");
        orderPayPO.setOutBizId("paySn");
        when(mockOrderPayMapper.getByPrimaryKey("paySn")).thenReturn(orderPayPO);

        // Configure OrderModel.getOrderList(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO);
        final OrderExample example = new OrderExample();
        example.setOrderIdNotEquals(0);
        example.setUserNo("userNo");
        example.setUserMobile("userMobile");
        example.setOrderSn("memberName");
        example.setPaySn("paySn");
        when(mockOrderModel.getOrderList(eq(example), any(PagerInfo.class))).thenReturn(orderPOList);

        when(mockOrderService.lambdaUpdate()).thenReturn(new LambdaUpdateChainWrapper<>(null));
        when(mockPayService.payAmount("payNo")).thenReturn(new BigDecimal("0.00"));
        when(mockOrderPayProcessStrategyContext.getStrategy(0)).thenReturn(null);
        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategory storeBindCategory = new StoreBindCategory();
        storeBindCategory.setBindId(0);
        storeBindCategory.setStoreId(0L);
        storeBindCategory.setCreateVendorId(0L);
        storeBindCategory.setGoodsCategoryId1(0);
        storeBindCategory.setGoodsCategoryId2(0);
        final List<StoreBindCategory> storeBindCategories = Arrays.asList(storeBindCategory);
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                .thenReturn(storeBindCategories);

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        when(mockBzBankTransferService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure IOrderService.list(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        final List<OrderPO> orderPOList1 = Arrays.asList(orderPO1);
        when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(orderPOList1);

        when(mockPromotionCommonFeignClient.orderPaySuccess("memberName", 0, "paySn", "memberName", "paymentName",
                "paymentCode")).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderProductMapper.selectOne(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setStoreName("memberName");
        orderProductPO1.setGoodsName("memberName");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setSpellTeamId(0);
        orderProductPO1.setEnabledFlag(0);
        when(mockOrderProductMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderProductPO1);

        // Configure OrderProductMapper.selectList(...).
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setStoreName("memberName");
        orderProductPO2.setGoodsName("memberName");
        orderProductPO2.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO2.setProductNum(0);
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setSpellTeamId(0);
        orderProductPO2.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO2);
        when(mockOrderProductMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);
        final List<OrderPromotionSendCouponPO> orderPromotionSendCouponPOS = Arrays.asList(orderPromotionSendCouponPO);
        final OrderPromotionSendCouponExample example1 = new OrderPromotionSendCouponExample();
        example1.setSendCouponIdNotEquals(0);
        example1.setSendCouponIdIn("sendCouponIdIn");
        example1.setSendCouponId(0);
        example1.setOrderSn("memberName");
        example1.setOrderSnLike("orderSnLike");
        when(mockOrderPromotionSendCouponMapper.listByExample(example1)).thenReturn(orderPromotionSendCouponPOS);

        // Configure CouponFeignClient.getCouponByCouponId(...).
        final CouponVO couponVO = new CouponVO();
        couponVO.setCouponId(0);
        couponVO.setCouponType(0);
        couponVO.setReceivedNum(0);
        couponVO.setRandomMax(new BigDecimal("0.00"));
        couponVO.setRandomMin(new BigDecimal("0.00"));
        couponVO.setPublishNum(0);
        couponVO.setEffectiveTimeType(0);
        couponVO.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setCycle(0);
        couponVO.setState(0);
        couponVO.setStoreId(0L);
        couponVO.setUseType(0);
        when(mockCouponFeignClient.getCouponByCouponId(0)).thenReturn(couponVO);

        // Configure IOrderService.getById(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("memberName");
        orderPO2.setUserNo("userNo");
        orderPO2.setPaySn("paySn");
        orderPO2.setSellerId("sellerId");
        orderPO2.setBankPayTrxNo("bankPayTrxNo");
        orderPO2.setStoreId(0L);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setComposePayName("composeWay");
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setServiceFee(new BigDecimal("0.00"));
        orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO2.setSettleMode("settleMode");
        orderPO2.setFinanceRuleCode("financeRuleCode");
        orderPO2.setIsDelivery(0);
        orderPO2.setChannel("channel");
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setCustomerConfirmStatus(0);
        orderPO2.setOrderPlaceUserRoleCode(0);
        orderPO2.setExchangeFlag(0);
        when(mockOrderService.getById(0)).thenReturn(orderPO2);

        // Configure IOrderAmountStateRecordService.initOrderAmountState(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult = new Result<>(false, errorContext, null);
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("memberName");
        orderPO3.setUserNo("userNo");
        orderPO3.setPaySn("paySn");
        orderPO3.setSellerId("sellerId");
        orderPO3.setBankPayTrxNo("bankPayTrxNo");
        orderPO3.setStoreId(0L);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setComposePayName("composeWay");
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setServiceFee(new BigDecimal("0.00"));
        orderPO3.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO3.setSettleMode("settleMode");
        orderPO3.setFinanceRuleCode("financeRuleCode");
        orderPO3.setIsDelivery(0);
        orderPO3.setChannel("channel");
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setCustomerConfirmStatus(0);
        orderPO3.setOrderPlaceUserRoleCode(0);
        orderPO3.setExchangeFlag(0);
        when(mockOrderAmountRecordService.initOrderAmountState(orderPO3)).thenReturn(voidResult);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO3 = new OrderProductPO();
        orderProductPO3.setOrderProductId(0L);
        orderProductPO3.setOrderSn("orderSn");
        orderProductPO3.setStoreName("memberName");
        orderProductPO3.setGoodsName("memberName");
        orderProductPO3.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO3.setProductNum(0);
        orderProductPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO3.setServiceFee(new BigDecimal("0.00"));
        orderProductPO3.setSpellTeamId(0);
        orderProductPO3.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS1 = Arrays.asList(orderProductPO3);
        when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(orderProductPOS1);

        // Run the test
        orderPayModelUnderTest.wxAlipayCallBack(req, payInfoExtraInfoVO);

        // Verify the results
        // Confirm PayService.paySuccessProcess(...).
        final PaymentNotifyVO req2 = new PaymentNotifyVO();
        req2.setOrderOn("paySn");
        req2.setPayCode("memberName");
        req2.setPayTradeNo("memberName");
        req2.setRelPayAmt(new BigDecimal("0.00"));
        req2.setPayStatus(0);
        req2.setPayWay("payWay");
        req2.setBankPayTrxNos(new HashMap<>());
        req2.setNewOrder(false);
        verify(mockPayService).paySuccessProcess(0, req2,
                new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"), new HashMap<>());

        // Confirm BankTransferModel.transferSuccess(...).
        final PaymentNotifyVO req3 = new PaymentNotifyVO();
        req3.setOrderOn("paySn");
        req3.setPayCode("memberName");
        req3.setPayTradeNo("memberName");
        req3.setRelPayAmt(new BigDecimal("0.00"));
        req3.setPayStatus(0);
        req3.setPayWay("payWay");
        req3.setBankPayTrxNos(new HashMap<>());
        req3.setNewOrder(false);
        verify(mockBankTransferModel).transferSuccess(req3);
        verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
                new BigDecimal("0.00"));
        verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 20, 0, "订单支付完成",
                OrderCreateChannel.H5);
        verify(mockOrderProductModel).orderPaySuccessAddSales("memberName");
        verify(mockOrderService).setOrdersDeliverable(Arrays.asList("value"));

        // Confirm CouponMemberFeignClient.saveCouponMember(...).
        final CouponMember couponMember = new CouponMember();
        couponMember.setCouponId(0);
        couponMember.setCouponCode("couponCode");
        couponMember.setStoreId(0L);
        couponMember.setMemberId(0);
        couponMember.setMemberName("memberName");
        couponMember.setReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseState(0);
        couponMember.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseType(0);
        couponMember.setRandomAmount(new BigDecimal("0.00"));
        verify(mockCouponMemberFeignClient).saveCouponMember(couponMember);

        // Confirm CouponUseLogFeignClient.saveCouponUseLog(...).
        final CouponUseLog couponUseLog = new CouponUseLog();
        couponUseLog.setCouponCode("couponCode");
        couponUseLog.setMemberId(0);
        couponUseLog.setMemberName("memberName");
        couponUseLog.setStoreId(0L);
        couponUseLog.setLogType(0);
        couponUseLog.setLogTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponUseLog.setLogContent("logContent");
        verify(mockCouponUseLogFeignClient).saveCouponUseLog(couponUseLog);

        // Confirm CouponFeignClient.updateOrderCoupon(...).
        final Coupon coupon = new Coupon();
        coupon.setCouponId(0);
        coupon.setCouponName("couponName");
        coupon.setCouponContent("couponContent");
        coupon.setDescription("description");
        coupon.setReceivedNum(0);
        verify(mockCouponFeignClient).updateOrderCoupon(coupon);

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO4 = new OrderPO();
        orderPO4.setOrderId(0);
        orderPO4.setOrderSn("memberName");
        orderPO4.setUserNo("userNo");
        orderPO4.setPaySn("paySn");
        orderPO4.setSellerId("sellerId");
        orderPO4.setBankPayTrxNo("bankPayTrxNo");
        orderPO4.setStoreId(0L);
        orderPO4.setRecommendStoreId(0L);
        orderPO4.setMemberName("memberName");
        orderPO4.setMemberId(0);
        orderPO4.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setOrderState(0);
        orderPO4.setLoanPayState(0);
        orderPO4.setPaymentName("paymentName");
        orderPO4.setPaymentCode("paymentCode");
        orderPO4.setOrderAmount(new BigDecimal("0.00"));
        orderPO4.setGoodsAmount(new BigDecimal("0.00"));
        orderPO4.setExpressFee(new BigDecimal("0.00"));
        orderPO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO4.setComposePayName("composeWay");
        orderPO4.setBalanceAmount(new BigDecimal("0.00"));
        orderPO4.setPayAmount(new BigDecimal("0.00"));
        orderPO4.setAreaCode("areaCode");
        orderPO4.setOrderType(0);
        orderPO4.setServiceFee(new BigDecimal("0.00"));
        orderPO4.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO4.setSettleMode("settleMode");
        orderPO4.setFinanceRuleCode("financeRuleCode");
        orderPO4.setIsDelivery(0);
        orderPO4.setChannel("channel");
        orderPO4.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO4.setNewOrder(false);
        orderPO4.setCustomerConfirmStatus(0);
        orderPO4.setOrderPlaceUserRoleCode(0);
        orderPO4.setExchangeFlag(0);
        verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO4, OrderEventEnum.CREATE,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg",
                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));
    }
*/
   /* @Test
    void testWxAlipayCallBack_OrderOfflineServiceReturnsNoItems() throws Exception {
        // Setup
        final PaymentNotifyVO req = new PaymentNotifyVO();
        req.setOrderOn("paySn");
        req.setPayCode("memberName");
        req.setPayTradeNo("memberName");
        req.setRelPayAmt(new BigDecimal("0.00"));
        req.setPayStatus(0);
        req.setPayWay("payWay");
        req.setBankPayTrxNos(new HashMap<>());
        req.setNewOrder(false);

        final PayInfoExtraInfoVO payInfoExtraInfoVO = new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn");

        // Configure PayService.buildPayNotifyOrderOn(...).
        final PaymentNotifyVO req1 = new PaymentNotifyVO();
        req1.setOrderOn("paySn");
        req1.setPayCode("memberName");
        req1.setPayTradeNo("memberName");
        req1.setRelPayAmt(new BigDecimal("0.00"));
        req1.setPayStatus(0);
        req1.setPayWay("payWay");
        req1.setBankPayTrxNos(new HashMap<>());
        req1.setNewOrder(false);
        when(mockPayService.buildPayNotifyOrderOn(req1,
                new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"), false)).thenReturn("paySn");

        // Configure OrderPayMapper.getByPrimaryKey(...).
        final OrderPayPO orderPayPO = new OrderPayPO();
        orderPayPO.setPayId(0);
        orderPayPO.setPaySn("paySn");
        orderPayPO.setOrderSn("pOrderSn");
        orderPayPO.setPayAmount(new BigDecimal("0.00"));
        orderPayPO.setMemberId(0);
        orderPayPO.setApiPayState("0");
        orderPayPO.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPayPO.setTradeSn("memberName");
        orderPayPO.setPaymentName("paymentName");
        orderPayPO.setPaymentCode("paymentCode");
        orderPayPO.setLoanSuccess(0);
        orderPayPO.setEnjoyPayVipFlag(0);
        orderPayPO.setPayWayExtraInfo(new JSONObject(0, false));
        orderPayPO.setOutBizSource("channel");
        orderPayPO.setOutBizId("paySn");
        when(mockOrderPayMapper.getByPrimaryKey("paySn")).thenReturn(orderPayPO);

        // Configure OrderModel.getOrderList(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO);
        final OrderExample example = new OrderExample();
        example.setOrderIdNotEquals(0);
        example.setUserNo("userNo");
        example.setUserMobile("userMobile");
        example.setOrderSn("memberName");
        example.setPaySn("paySn");
        when(mockOrderModel.getOrderList(eq(example), any(PagerInfo.class))).thenReturn(orderPOList);

        when(mockOrderService.lambdaUpdate()).thenReturn(new LambdaUpdateChainWrapper<>(null));
        when(mockPayService.payAmount("payNo")).thenReturn(new BigDecimal("0.00"));
        when(mockOrderPayProcessStrategyContext.getStrategy(0)).thenReturn(null);
        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategory storeBindCategory = new StoreBindCategory();
        storeBindCategory.setBindId(0);
        storeBindCategory.setStoreId(0L);
        storeBindCategory.setCreateVendorId(0L);
        storeBindCategory.setGoodsCategoryId1(0);
        storeBindCategory.setGoodsCategoryId2(0);
        final List<StoreBindCategory> storeBindCategories = Arrays.asList(storeBindCategory);
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                .thenReturn(storeBindCategories);

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        when(mockOrderOfflineService.queryOrderOfflineList("paySn")).thenReturn(Collections.emptyList());

        // Configure IOrderService.list(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        final List<OrderPO> orderPOList1 = Arrays.asList(orderPO1);
        when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(orderPOList1);

        when(mockPromotionCommonFeignClient.orderPaySuccess("memberName", 0, "paySn", "memberName", "paymentName",
                "paymentCode")).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderProductMapper.selectOne(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setStoreName("memberName");
        orderProductPO1.setGoodsName("memberName");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setSpellTeamId(0);
        orderProductPO1.setEnabledFlag(0);
        when(mockOrderProductMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderProductPO1);

        // Configure OrderProductMapper.selectList(...).
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setStoreName("memberName");
        orderProductPO2.setGoodsName("memberName");
        orderProductPO2.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO2.setProductNum(0);
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setSpellTeamId(0);
        orderProductPO2.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO2);
        when(mockOrderProductMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);
        final List<OrderPromotionSendCouponPO> orderPromotionSendCouponPOS = Arrays.asList(orderPromotionSendCouponPO);
        final OrderPromotionSendCouponExample example1 = new OrderPromotionSendCouponExample();
        example1.setSendCouponIdNotEquals(0);
        example1.setSendCouponIdIn("sendCouponIdIn");
        example1.setSendCouponId(0);
        example1.setOrderSn("memberName");
        example1.setOrderSnLike("orderSnLike");
        when(mockOrderPromotionSendCouponMapper.listByExample(example1)).thenReturn(orderPromotionSendCouponPOS);

        // Configure CouponFeignClient.getCouponByCouponId(...).
        final CouponVO couponVO = new CouponVO();
        couponVO.setCouponId(0);
        couponVO.setCouponType(0);
        couponVO.setReceivedNum(0);
        couponVO.setRandomMax(new BigDecimal("0.00"));
        couponVO.setRandomMin(new BigDecimal("0.00"));
        couponVO.setPublishNum(0);
        couponVO.setEffectiveTimeType(0);
        couponVO.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setCycle(0);
        couponVO.setState(0);
        couponVO.setStoreId(0L);
        couponVO.setUseType(0);
        when(mockCouponFeignClient.getCouponByCouponId(0)).thenReturn(couponVO);

        // Configure IOrderService.getById(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("memberName");
        orderPO2.setUserNo("userNo");
        orderPO2.setPaySn("paySn");
        orderPO2.setSellerId("sellerId");
        orderPO2.setBankPayTrxNo("bankPayTrxNo");
        orderPO2.setStoreId(0L);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setComposePayName("composeWay");
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setServiceFee(new BigDecimal("0.00"));
        orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO2.setSettleMode("settleMode");
        orderPO2.setFinanceRuleCode("financeRuleCode");
        orderPO2.setIsDelivery(0);
        orderPO2.setChannel("channel");
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setCustomerConfirmStatus(0);
        orderPO2.setOrderPlaceUserRoleCode(0);
        orderPO2.setExchangeFlag(0);
        when(mockOrderService.getById(0)).thenReturn(orderPO2);

        // Configure IOrderAmountStateRecordService.initOrderAmountState(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult = new Result<>(false, errorContext, null);
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("memberName");
        orderPO3.setUserNo("userNo");
        orderPO3.setPaySn("paySn");
        orderPO3.setSellerId("sellerId");
        orderPO3.setBankPayTrxNo("bankPayTrxNo");
        orderPO3.setStoreId(0L);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setComposePayName("composeWay");
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setServiceFee(new BigDecimal("0.00"));
        orderPO3.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO3.setSettleMode("settleMode");
        orderPO3.setFinanceRuleCode("financeRuleCode");
        orderPO3.setIsDelivery(0);
        orderPO3.setChannel("channel");
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setCustomerConfirmStatus(0);
        orderPO3.setOrderPlaceUserRoleCode(0);
        orderPO3.setExchangeFlag(0);
        when(mockOrderAmountRecordService.initOrderAmountState(orderPO3)).thenReturn(voidResult);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO3 = new OrderProductPO();
        orderProductPO3.setOrderProductId(0L);
        orderProductPO3.setOrderSn("orderSn");
        orderProductPO3.setStoreName("memberName");
        orderProductPO3.setGoodsName("memberName");
        orderProductPO3.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO3.setProductNum(0);
        orderProductPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO3.setServiceFee(new BigDecimal("0.00"));
        orderProductPO3.setSpellTeamId(0);
        orderProductPO3.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS1 = Arrays.asList(orderProductPO3);
        when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(orderProductPOS1);

        // Run the test
        orderPayModelUnderTest.wxAlipayCallBack(req, payInfoExtraInfoVO);

        // Verify the results
        // Confirm PayService.paySuccessProcess(...).
        final PaymentNotifyVO req2 = new PaymentNotifyVO();
        req2.setOrderOn("paySn");
        req2.setPayCode("memberName");
        req2.setPayTradeNo("memberName");
        req2.setRelPayAmt(new BigDecimal("0.00"));
        req2.setPayStatus(0);
        req2.setPayWay("payWay");
        req2.setBankPayTrxNos(new HashMap<>());
        req2.setNewOrder(false);
        verify(mockPayService).paySuccessProcess(0, req2,
                new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"), new HashMap<>());

        // Confirm BankTransferModel.transferSuccess(...).
        final PaymentNotifyVO req3 = new PaymentNotifyVO();
        req3.setOrderOn("paySn");
        req3.setPayCode("memberName");
        req3.setPayTradeNo("memberName");
        req3.setRelPayAmt(new BigDecimal("0.00"));
        req3.setPayStatus(0);
        req3.setPayWay("payWay");
        req3.setBankPayTrxNos(new HashMap<>());
        req3.setNewOrder(false);
        verify(mockBankTransferModel).transferSuccess(req3);
        verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
                new BigDecimal("0.00"));
        verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 20, 0, "订单支付完成",
                OrderCreateChannel.H5);
        verify(mockOrderProductModel).orderPaySuccessAddSales("memberName");
        verify(mockOrderService).setOrdersDeliverable(Arrays.asList("value"));

        // Confirm CouponMemberFeignClient.saveCouponMember(...).
        final CouponMember couponMember = new CouponMember();
        couponMember.setCouponId(0);
        couponMember.setCouponCode("couponCode");
        couponMember.setStoreId(0L);
        couponMember.setMemberId(0);
        couponMember.setMemberName("memberName");
        couponMember.setReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseState(0);
        couponMember.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseType(0);
        couponMember.setRandomAmount(new BigDecimal("0.00"));
        verify(mockCouponMemberFeignClient).saveCouponMember(couponMember);

        // Confirm CouponUseLogFeignClient.saveCouponUseLog(...).
        final CouponUseLog couponUseLog = new CouponUseLog();
        couponUseLog.setCouponCode("couponCode");
        couponUseLog.setMemberId(0);
        couponUseLog.setMemberName("memberName");
        couponUseLog.setStoreId(0L);
        couponUseLog.setLogType(0);
        couponUseLog.setLogTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponUseLog.setLogContent("logContent");
        verify(mockCouponUseLogFeignClient).saveCouponUseLog(couponUseLog);

        // Confirm CouponFeignClient.updateOrderCoupon(...).
        final Coupon coupon = new Coupon();
        coupon.setCouponId(0);
        coupon.setCouponName("couponName");
        coupon.setCouponContent("couponContent");
        coupon.setDescription("description");
        coupon.setReceivedNum(0);
        verify(mockCouponFeignClient).updateOrderCoupon(coupon);

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO4 = new OrderPO();
        orderPO4.setOrderId(0);
        orderPO4.setOrderSn("memberName");
        orderPO4.setUserNo("userNo");
        orderPO4.setPaySn("paySn");
        orderPO4.setSellerId("sellerId");
        orderPO4.setBankPayTrxNo("bankPayTrxNo");
        orderPO4.setStoreId(0L);
        orderPO4.setRecommendStoreId(0L);
        orderPO4.setMemberName("memberName");
        orderPO4.setMemberId(0);
        orderPO4.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setOrderState(0);
        orderPO4.setLoanPayState(0);
        orderPO4.setPaymentName("paymentName");
        orderPO4.setPaymentCode("paymentCode");
        orderPO4.setOrderAmount(new BigDecimal("0.00"));
        orderPO4.setGoodsAmount(new BigDecimal("0.00"));
        orderPO4.setExpressFee(new BigDecimal("0.00"));
        orderPO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO4.setComposePayName("composeWay");
        orderPO4.setBalanceAmount(new BigDecimal("0.00"));
        orderPO4.setPayAmount(new BigDecimal("0.00"));
        orderPO4.setAreaCode("areaCode");
        orderPO4.setOrderType(0);
        orderPO4.setServiceFee(new BigDecimal("0.00"));
        orderPO4.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO4.setSettleMode("settleMode");
        orderPO4.setFinanceRuleCode("financeRuleCode");
        orderPO4.setIsDelivery(0);
        orderPO4.setChannel("channel");
        orderPO4.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO4.setNewOrder(false);
        orderPO4.setCustomerConfirmStatus(0);
        orderPO4.setOrderPlaceUserRoleCode(0);
        orderPO4.setExchangeFlag(0);
        verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO4, OrderEventEnum.CREATE,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg",
                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));
    }
*/
  /*  @Test
    void testWxAlipayCallBack_IOrderServiceListReturnsNoItems() throws Exception {
        // Setup
        final PaymentNotifyVO req = new PaymentNotifyVO();
        req.setOrderOn("paySn");
        req.setPayCode("memberName");
        req.setPayTradeNo("memberName");
        req.setRelPayAmt(new BigDecimal("0.00"));
        req.setPayStatus(0);
        req.setPayWay("payWay");
        req.setBankPayTrxNos(new HashMap<>());
        req.setNewOrder(false);

        final PayInfoExtraInfoVO payInfoExtraInfoVO = new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn");

        // Configure PayService.buildPayNotifyOrderOn(...).
        final PaymentNotifyVO req1 = new PaymentNotifyVO();
        req1.setOrderOn("paySn");
        req1.setPayCode("memberName");
        req1.setPayTradeNo("memberName");
        req1.setRelPayAmt(new BigDecimal("0.00"));
        req1.setPayStatus(0);
        req1.setPayWay("payWay");
        req1.setBankPayTrxNos(new HashMap<>());
        req1.setNewOrder(false);
        when(mockPayService.buildPayNotifyOrderOn(req1,
                new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"), false)).thenReturn("paySn");

        // Configure OrderPayMapper.getByPrimaryKey(...).
        final OrderPayPO orderPayPO = new OrderPayPO();
        orderPayPO.setPayId(0);
        orderPayPO.setPaySn("paySn");
        orderPayPO.setOrderSn("pOrderSn");
        orderPayPO.setPayAmount(new BigDecimal("0.00"));
        orderPayPO.setMemberId(0);
        orderPayPO.setApiPayState("0");
        orderPayPO.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPayPO.setTradeSn("memberName");
        orderPayPO.setPaymentName("paymentName");
        orderPayPO.setPaymentCode("paymentCode");
        orderPayPO.setLoanSuccess(0);
        orderPayPO.setEnjoyPayVipFlag(0);
        orderPayPO.setPayWayExtraInfo(new JSONObject(0, false));
        orderPayPO.setOutBizSource("channel");
        orderPayPO.setOutBizId("paySn");
        when(mockOrderPayMapper.getByPrimaryKey("paySn")).thenReturn(orderPayPO);

        // Configure OrderModel.getOrderList(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO);
        final OrderExample example = new OrderExample();
        example.setOrderIdNotEquals(0);
        example.setUserNo("userNo");
        example.setUserMobile("userMobile");
        example.setOrderSn("memberName");
        example.setPaySn("paySn");
        when(mockOrderModel.getOrderList(eq(example), any(PagerInfo.class))).thenReturn(orderPOList);

        when(mockOrderService.lambdaUpdate()).thenReturn(new LambdaUpdateChainWrapper<>(null));
        when(mockPayService.payAmount("payNo")).thenReturn(new BigDecimal("0.00"));
        when(mockOrderPayProcessStrategyContext.getStrategy(0)).thenReturn(null);
        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategory storeBindCategory = new StoreBindCategory();
        storeBindCategory.setBindId(0);
        storeBindCategory.setStoreId(0L);
        storeBindCategory.setCreateVendorId(0L);
        storeBindCategory.setGoodsCategoryId1(0);
        storeBindCategory.setGoodsCategoryId2(0);
        final List<StoreBindCategory> storeBindCategories = Arrays.asList(storeBindCategory);
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                .thenReturn(storeBindCategories);

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        when(mockAccountCardFacade.defaultCard(0L)).thenReturn(new JsonResult<>(0, "errMsg"));
        when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure OrderPayMapper.updateByPrimaryKeySelective(...).
        final OrderPayPO record = new OrderPayPO();
        record.setPayId(0);
        record.setPaySn("paySn");
        record.setOrderSn("pOrderSn");
        record.setPayAmount(new BigDecimal("0.00"));
        record.setMemberId(0);
        record.setApiPayState("0");
        record.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        record.setTradeSn("memberName");
        record.setPaymentName("paymentName");
        record.setPaymentCode("paymentCode");
        record.setLoanSuccess(0);
        record.setEnjoyPayVipFlag(0);
        record.setPayWayExtraInfo(new JSONObject(0, false));
        record.setOutBizSource("channel");
        record.setOutBizId("paySn");
        when(mockOrderPayMapper.updateByPrimaryKeySelective(record)).thenReturn(0);

        when(mockPromotionCommonFeignClient.orderPaySuccess("memberName", 0, "paySn", "memberName", "paymentName",
                "paymentCode")).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderProductMapper.selectOne(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setStoreName("memberName");
        orderProductPO1.setGoodsName("memberName");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setSpellTeamId(0);
        orderProductPO1.setEnabledFlag(0);
        when(mockOrderProductMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderProductPO1);

        // Configure OrderProductMapper.selectList(...).
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setStoreName("memberName");
        orderProductPO2.setGoodsName("memberName");
        orderProductPO2.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO2.setProductNum(0);
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setSpellTeamId(0);
        orderProductPO2.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO2);
        when(mockOrderProductMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);
        final List<OrderPromotionSendCouponPO> orderPromotionSendCouponPOS = Arrays.asList(orderPromotionSendCouponPO);
        final OrderPromotionSendCouponExample example1 = new OrderPromotionSendCouponExample();
        example1.setSendCouponIdNotEquals(0);
        example1.setSendCouponIdIn("sendCouponIdIn");
        example1.setSendCouponId(0);
        example1.setOrderSn("memberName");
        example1.setOrderSnLike("orderSnLike");
        when(mockOrderPromotionSendCouponMapper.listByExample(example1)).thenReturn(orderPromotionSendCouponPOS);

        // Configure CouponFeignClient.getCouponByCouponId(...).
        final CouponVO couponVO = new CouponVO();
        couponVO.setCouponId(0);
        couponVO.setCouponType(0);
        couponVO.setReceivedNum(0);
        couponVO.setRandomMax(new BigDecimal("0.00"));
        couponVO.setRandomMin(new BigDecimal("0.00"));
        couponVO.setPublishNum(0);
        couponVO.setEffectiveTimeType(0);
        couponVO.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setCycle(0);
        couponVO.setState(0);
        couponVO.setStoreId(0L);
        couponVO.setUseType(0);
        when(mockCouponFeignClient.getCouponByCouponId(0)).thenReturn(couponVO);

        // Configure IOrderService.getById(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        when(mockOrderService.getById(0)).thenReturn(orderPO1);

        // Configure IOrderAmountStateRecordService.initOrderAmountState(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult = new Result<>(false, errorContext, null);
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("memberName");
        orderPO2.setUserNo("userNo");
        orderPO2.setPaySn("paySn");
        orderPO2.setSellerId("sellerId");
        orderPO2.setBankPayTrxNo("bankPayTrxNo");
        orderPO2.setStoreId(0L);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setComposePayName("composeWay");
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setServiceFee(new BigDecimal("0.00"));
        orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO2.setSettleMode("settleMode");
        orderPO2.setFinanceRuleCode("financeRuleCode");
        orderPO2.setIsDelivery(0);
        orderPO2.setChannel("channel");
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setCustomerConfirmStatus(0);
        orderPO2.setOrderPlaceUserRoleCode(0);
        orderPO2.setExchangeFlag(0);
        when(mockOrderAmountRecordService.initOrderAmountState(orderPO2)).thenReturn(voidResult);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO3 = new OrderProductPO();
        orderProductPO3.setOrderProductId(0L);
        orderProductPO3.setOrderSn("orderSn");
        orderProductPO3.setStoreName("memberName");
        orderProductPO3.setGoodsName("memberName");
        orderProductPO3.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO3.setProductNum(0);
        orderProductPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO3.setServiceFee(new BigDecimal("0.00"));
        orderProductPO3.setSpellTeamId(0);
        orderProductPO3.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS1 = Arrays.asList(orderProductPO3);
        when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(orderProductPOS1);

        // Run the test
        orderPayModelUnderTest.wxAlipayCallBack(req, payInfoExtraInfoVO);

        // Verify the results
        // Confirm PayService.paySuccessProcess(...).
        final PaymentNotifyVO req2 = new PaymentNotifyVO();
        req2.setOrderOn("paySn");
        req2.setPayCode("memberName");
        req2.setPayTradeNo("memberName");
        req2.setRelPayAmt(new BigDecimal("0.00"));
        req2.setPayStatus(0);
        req2.setPayWay("payWay");
        req2.setBankPayTrxNos(new HashMap<>());
        req2.setNewOrder(false);
        verify(mockPayService).paySuccessProcess(0, req2,
                new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"), new HashMap<>());

        // Confirm BankTransferModel.transferSuccess(...).
        final PaymentNotifyVO req3 = new PaymentNotifyVO();
        req3.setOrderOn("paySn");
        req3.setPayCode("memberName");
        req3.setPayTradeNo("memberName");
        req3.setRelPayAmt(new BigDecimal("0.00"));
        req3.setPayStatus(0);
        req3.setPayWay("payWay");
        req3.setBankPayTrxNos(new HashMap<>());
        req3.setNewOrder(false);
        verify(mockBankTransferModel).transferSuccess(req3);
        verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
                new BigDecimal("0.00"));
        verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 20, 0, "订单支付完成",
                OrderCreateChannel.H5);
        verify(mockOrderProductModel).orderPaySuccessAddSales("memberName");
        verify(mockOrderService).setOrdersDeliverable(Arrays.asList("value"));

        // Confirm CouponMemberFeignClient.saveCouponMember(...).
        final CouponMember couponMember = new CouponMember();
        couponMember.setCouponId(0);
        couponMember.setCouponCode("couponCode");
        couponMember.setStoreId(0L);
        couponMember.setMemberId(0);
        couponMember.setMemberName("memberName");
        couponMember.setReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseState(0);
        couponMember.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseType(0);
        couponMember.setRandomAmount(new BigDecimal("0.00"));
        verify(mockCouponMemberFeignClient).saveCouponMember(couponMember);

        // Confirm CouponUseLogFeignClient.saveCouponUseLog(...).
        final CouponUseLog couponUseLog = new CouponUseLog();
        couponUseLog.setCouponCode("couponCode");
        couponUseLog.setMemberId(0);
        couponUseLog.setMemberName("memberName");
        couponUseLog.setStoreId(0L);
        couponUseLog.setLogType(0);
        couponUseLog.setLogTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponUseLog.setLogContent("logContent");
        verify(mockCouponUseLogFeignClient).saveCouponUseLog(couponUseLog);

        // Confirm CouponFeignClient.updateOrderCoupon(...).
        final Coupon coupon = new Coupon();
        coupon.setCouponId(0);
        coupon.setCouponName("couponName");
        coupon.setCouponContent("couponContent");
        coupon.setDescription("description");
        coupon.setReceivedNum(0);
        verify(mockCouponFeignClient).updateOrderCoupon(coupon);

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("memberName");
        orderPO3.setUserNo("userNo");
        orderPO3.setPaySn("paySn");
        orderPO3.setSellerId("sellerId");
        orderPO3.setBankPayTrxNo("bankPayTrxNo");
        orderPO3.setStoreId(0L);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setComposePayName("composeWay");
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setServiceFee(new BigDecimal("0.00"));
        orderPO3.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO3.setSettleMode("settleMode");
        orderPO3.setFinanceRuleCode("financeRuleCode");
        orderPO3.setIsDelivery(0);
        orderPO3.setChannel("channel");
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setCustomerConfirmStatus(0);
        orderPO3.setOrderPlaceUserRoleCode(0);
        orderPO3.setExchangeFlag(0);
        verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO3, OrderEventEnum.CREATE,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg",
                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));
    }
*/
  /*  @Test
    void testWxAlipayCallBack_OrderProductMapperSelectListReturnsNoItems() throws Exception {
        // Setup
        final PaymentNotifyVO req = new PaymentNotifyVO();
        req.setOrderOn("paySn");
        req.setPayCode("memberName");
        req.setPayTradeNo("memberName");
        req.setRelPayAmt(new BigDecimal("0.00"));
        req.setPayStatus(0);
        req.setPayWay("payWay");
        req.setBankPayTrxNos(new HashMap<>());
        req.setNewOrder(false);

        final PayInfoExtraInfoVO payInfoExtraInfoVO = new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn");

        // Configure PayService.buildPayNotifyOrderOn(...).
        final PaymentNotifyVO req1 = new PaymentNotifyVO();
        req1.setOrderOn("paySn");
        req1.setPayCode("memberName");
        req1.setPayTradeNo("memberName");
        req1.setRelPayAmt(new BigDecimal("0.00"));
        req1.setPayStatus(0);
        req1.setPayWay("payWay");
        req1.setBankPayTrxNos(new HashMap<>());
        req1.setNewOrder(false);
        when(mockPayService.buildPayNotifyOrderOn(req1,
                new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"), false)).thenReturn("paySn");

        // Configure OrderPayMapper.getByPrimaryKey(...).
        final OrderPayPO orderPayPO = new OrderPayPO();
        orderPayPO.setPayId(0);
        orderPayPO.setPaySn("paySn");
        orderPayPO.setOrderSn("pOrderSn");
        orderPayPO.setPayAmount(new BigDecimal("0.00"));
        orderPayPO.setMemberId(0);
        orderPayPO.setApiPayState("0");
        orderPayPO.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPayPO.setTradeSn("memberName");
        orderPayPO.setPaymentName("paymentName");
        orderPayPO.setPaymentCode("paymentCode");
        orderPayPO.setLoanSuccess(0);
        orderPayPO.setEnjoyPayVipFlag(0);
        orderPayPO.setPayWayExtraInfo(new JSONObject(0, false));
        orderPayPO.setOutBizSource("channel");
        orderPayPO.setOutBizId("paySn");
        when(mockOrderPayMapper.getByPrimaryKey("paySn")).thenReturn(orderPayPO);

        // Configure OrderModel.getOrderList(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO);
        final OrderExample example = new OrderExample();
        example.setOrderIdNotEquals(0);
        example.setUserNo("userNo");
        example.setUserMobile("userMobile");
        example.setOrderSn("memberName");
        example.setPaySn("paySn");
        when(mockOrderModel.getOrderList(eq(example), any(PagerInfo.class))).thenReturn(orderPOList);

        when(mockOrderService.lambdaUpdate()).thenReturn(new LambdaUpdateChainWrapper<>(null));
        when(mockPayService.payAmount("payNo")).thenReturn(new BigDecimal("0.00"));
        when(mockOrderPayProcessStrategyContext.getStrategy(0)).thenReturn(null);
        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategory storeBindCategory = new StoreBindCategory();
        storeBindCategory.setBindId(0);
        storeBindCategory.setStoreId(0L);
        storeBindCategory.setCreateVendorId(0L);
        storeBindCategory.setGoodsCategoryId1(0);
        storeBindCategory.setGoodsCategoryId2(0);
        final List<StoreBindCategory> storeBindCategories = Arrays.asList(storeBindCategory);
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                .thenReturn(storeBindCategories);

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        when(mockAccountCardFacade.defaultCard(0L)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure IOrderService.list(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        final List<OrderPO> orderPOList1 = Arrays.asList(orderPO1);
        when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(orderPOList1);

        when(mockPromotionCommonFeignClient.orderPaySuccess("memberName", 0, "paySn", "memberName", "paymentName",
                "paymentCode")).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderProductMapper.selectOne(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setStoreName("memberName");
        orderProductPO1.setGoodsName("memberName");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setSpellTeamId(0);
        orderProductPO1.setEnabledFlag(0);
        when(mockOrderProductMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderProductPO1);

        when(mockOrderProductMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);
        final List<OrderPromotionSendCouponPO> orderPromotionSendCouponPOS = Arrays.asList(orderPromotionSendCouponPO);
        final OrderPromotionSendCouponExample example1 = new OrderPromotionSendCouponExample();
        example1.setSendCouponIdNotEquals(0);
        example1.setSendCouponIdIn("sendCouponIdIn");
        example1.setSendCouponId(0);
        example1.setOrderSn("memberName");
        example1.setOrderSnLike("orderSnLike");
        when(mockOrderPromotionSendCouponMapper.listByExample(example1)).thenReturn(orderPromotionSendCouponPOS);

        // Configure CouponFeignClient.getCouponByCouponId(...).
        final CouponVO couponVO = new CouponVO();
        couponVO.setCouponId(0);
        couponVO.setCouponType(0);
        couponVO.setReceivedNum(0);
        couponVO.setRandomMax(new BigDecimal("0.00"));
        couponVO.setRandomMin(new BigDecimal("0.00"));
        couponVO.setPublishNum(0);
        couponVO.setEffectiveTimeType(0);
        couponVO.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setCycle(0);
        couponVO.setState(0);
        couponVO.setStoreId(0L);
        couponVO.setUseType(0);
        when(mockCouponFeignClient.getCouponByCouponId(0)).thenReturn(couponVO);

        // Configure IOrderService.getById(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("memberName");
        orderPO2.setUserNo("userNo");
        orderPO2.setPaySn("paySn");
        orderPO2.setSellerId("sellerId");
        orderPO2.setBankPayTrxNo("bankPayTrxNo");
        orderPO2.setStoreId(0L);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setComposePayName("composeWay");
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setServiceFee(new BigDecimal("0.00"));
        orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO2.setSettleMode("settleMode");
        orderPO2.setFinanceRuleCode("financeRuleCode");
        orderPO2.setIsDelivery(0);
        orderPO2.setChannel("channel");
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setCustomerConfirmStatus(0);
        orderPO2.setOrderPlaceUserRoleCode(0);
        orderPO2.setExchangeFlag(0);
        when(mockOrderService.getById(0)).thenReturn(orderPO2);

        // Configure IOrderAmountStateRecordService.initOrderAmountState(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult = new Result<>(false, errorContext, null);
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("memberName");
        orderPO3.setUserNo("userNo");
        orderPO3.setPaySn("paySn");
        orderPO3.setSellerId("sellerId");
        orderPO3.setBankPayTrxNo("bankPayTrxNo");
        orderPO3.setStoreId(0L);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setComposePayName("composeWay");
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setServiceFee(new BigDecimal("0.00"));
        orderPO3.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO3.setSettleMode("settleMode");
        orderPO3.setFinanceRuleCode("financeRuleCode");
        orderPO3.setIsDelivery(0);
        orderPO3.setChannel("channel");
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setCustomerConfirmStatus(0);
        orderPO3.setOrderPlaceUserRoleCode(0);
        orderPO3.setExchangeFlag(0);
        when(mockOrderAmountRecordService.initOrderAmountState(orderPO3)).thenReturn(voidResult);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setStoreName("memberName");
        orderProductPO2.setGoodsName("memberName");
        orderProductPO2.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO2.setProductNum(0);
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setSpellTeamId(0);
        orderProductPO2.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO2);
        when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(orderProductPOS);

        // Run the test
        orderPayModelUnderTest.wxAlipayCallBack(req, payInfoExtraInfoVO);

        // Verify the results
        // Confirm PayService.paySuccessProcess(...).
        final PaymentNotifyVO req2 = new PaymentNotifyVO();
        req2.setOrderOn("paySn");
        req2.setPayCode("memberName");
        req2.setPayTradeNo("memberName");
        req2.setRelPayAmt(new BigDecimal("0.00"));
        req2.setPayStatus(0);
        req2.setPayWay("payWay");
        req2.setBankPayTrxNos(new HashMap<>());
        req2.setNewOrder(false);
        verify(mockPayService).paySuccessProcess(0, req2,
                new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"), new HashMap<>());

        // Confirm BankTransferModel.transferSuccess(...).
        final PaymentNotifyVO req3 = new PaymentNotifyVO();
        req3.setOrderOn("paySn");
        req3.setPayCode("memberName");
        req3.setPayTradeNo("memberName");
        req3.setRelPayAmt(new BigDecimal("0.00"));
        req3.setPayStatus(0);
        req3.setPayWay("payWay");
        req3.setBankPayTrxNos(new HashMap<>());
        req3.setNewOrder(false);
        verify(mockBankTransferModel).transferSuccess(req3);
        verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
                new BigDecimal("0.00"));
        verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 20, 0, "订单支付完成",
                OrderCreateChannel.H5);
        verify(mockOrderProductModel).orderPaySuccessAddSales("memberName");
        verify(mockOrderService).setOrdersDeliverable(Arrays.asList("value"));

        // Confirm CouponMemberFeignClient.saveCouponMember(...).
        final CouponMember couponMember = new CouponMember();
        couponMember.setCouponId(0);
        couponMember.setCouponCode("couponCode");
        couponMember.setStoreId(0L);
        couponMember.setMemberId(0);
        couponMember.setMemberName("memberName");
        couponMember.setReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseState(0);
        couponMember.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseType(0);
        couponMember.setRandomAmount(new BigDecimal("0.00"));
        verify(mockCouponMemberFeignClient).saveCouponMember(couponMember);

        // Confirm CouponUseLogFeignClient.saveCouponUseLog(...).
        final CouponUseLog couponUseLog = new CouponUseLog();
        couponUseLog.setCouponCode("couponCode");
        couponUseLog.setMemberId(0);
        couponUseLog.setMemberName("memberName");
        couponUseLog.setStoreId(0L);
        couponUseLog.setLogType(0);
        couponUseLog.setLogTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponUseLog.setLogContent("logContent");
        verify(mockCouponUseLogFeignClient).saveCouponUseLog(couponUseLog);

        // Confirm CouponFeignClient.updateOrderCoupon(...).
        final Coupon coupon = new Coupon();
        coupon.setCouponId(0);
        coupon.setCouponName("couponName");
        coupon.setCouponContent("couponContent");
        coupon.setDescription("description");
        coupon.setReceivedNum(0);
        verify(mockCouponFeignClient).updateOrderCoupon(coupon);

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO4 = new OrderPO();
        orderPO4.setOrderId(0);
        orderPO4.setOrderSn("memberName");
        orderPO4.setUserNo("userNo");
        orderPO4.setPaySn("paySn");
        orderPO4.setSellerId("sellerId");
        orderPO4.setBankPayTrxNo("bankPayTrxNo");
        orderPO4.setStoreId(0L);
        orderPO4.setRecommendStoreId(0L);
        orderPO4.setMemberName("memberName");
        orderPO4.setMemberId(0);
        orderPO4.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setOrderState(0);
        orderPO4.setLoanPayState(0);
        orderPO4.setPaymentName("paymentName");
        orderPO4.setPaymentCode("paymentCode");
        orderPO4.setOrderAmount(new BigDecimal("0.00"));
        orderPO4.setGoodsAmount(new BigDecimal("0.00"));
        orderPO4.setExpressFee(new BigDecimal("0.00"));
        orderPO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO4.setComposePayName("composeWay");
        orderPO4.setBalanceAmount(new BigDecimal("0.00"));
        orderPO4.setPayAmount(new BigDecimal("0.00"));
        orderPO4.setAreaCode("areaCode");
        orderPO4.setOrderType(0);
        orderPO4.setServiceFee(new BigDecimal("0.00"));
        orderPO4.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO4.setSettleMode("settleMode");
        orderPO4.setFinanceRuleCode("financeRuleCode");
        orderPO4.setIsDelivery(0);
        orderPO4.setChannel("channel");
        orderPO4.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO4.setNewOrder(false);
        orderPO4.setCustomerConfirmStatus(0);
        orderPO4.setOrderPlaceUserRoleCode(0);
        orderPO4.setExchangeFlag(0);
        verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO4, OrderEventEnum.CREATE,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg",
                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));
    }*/

}
