package com.cfpamf.ms.mallorder.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cfpamf.ms.mallorder.mapper.OrderProductMapper;
import com.cfpamf.ms.mallorder.mapper.OrderReturnMapper;
import com.cfpamf.ms.mallorder.po.OrderProductPO;
import com.cfpamf.ms.mallorder.po.OrderReturnPO;
import com.cfpamf.ms.mallorder.service.IOrderProductService;
import com.cfpamf.ms.mallstock.facade.api.StockBalanceFeignClient;
import com.cfpamf.ms.mallstock.facade.dto.ProcurementCostPriceQueryDTO;
import com.slodon.bbc.core.response.JsonResult;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HistoryDataDealServiceImplTest {

    @Mock
    private OrderReturnMapper mockOrderReturnMapper;
    @Mock
    private StockBalanceFeignClient mockStockBalanceFeignClient;
    @Mock
    private OrderProductMapper mockOrderProductMapper;
    @Mock
    private IOrderProductService mockProductService;

    @InjectMocks
    private HistoryDataDealServiceImpl historyDataDealServiceImplUnderTest;

    @Test
    public void testReturnServiceFeeFix() {
        // Setup
        when(mockOrderReturnMapper.update(any(OrderReturnPO.class), any(LambdaUpdateWrapper.class))).thenReturn(0);

        // Run the test
        final int result = historyDataDealServiceImplUnderTest.returnServiceFeeFix("afsSn", new BigDecimal("0.00"));

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    public void testOrderProductCostFix() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList))
                .thenReturn(new JsonResult<>(0, "errMsg"));

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
        verify(mockProductService).update(any(LambdaUpdateWrapper.class));
    }

    @Test
    public void testOrderProductCostFix_a02() {
        // Setup
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(Collections.emptyList());

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList))
                .thenReturn(new JsonResult<>(0, "errMsg"));

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
        verify(mockProductService).update(any(LambdaUpdateWrapper.class));
    }

    @Test
    public void testOrderProductCostFix_a03() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a04() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a05() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a06() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a07() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a08() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a09() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a10() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a11() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a12() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a13() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a14() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a15() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a16() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a17() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a18() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a19() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a20() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a21() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a22() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a23() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a24() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a25() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a26() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a27() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a28() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a29() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a30() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a31() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a32() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a33() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a34() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a35() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a36() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a37() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a38() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a39() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a40() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a41() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a42() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a43() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a44() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a45() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a46() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a47() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a48() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a49() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a50() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a51() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a52() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a53() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a54() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a55() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a56() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a57() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a58() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a59() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a60() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a61() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a62() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a63() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a64() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a65() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a66() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a67() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a68() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a69() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a70() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a71() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a72() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a73() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a74() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a75() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a76() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a77() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a78() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a79() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a80() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a81() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a82() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a83() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a84() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a85() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a86() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a87() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a88() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a89() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a90() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a91() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a92() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a93() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a94() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a95() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a96() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a97() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a98() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a99() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a100() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a101() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a102() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a103() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a104() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a105() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a106() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a107() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a108() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a109() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a110() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a111() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a112() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a113() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a114() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a115() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a116() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a117() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a118() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a119() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a120() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a121() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a122() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a123() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a124() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a125() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a126() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }

    @Test
    public void testOrderProductCostFix_a127() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }


}
