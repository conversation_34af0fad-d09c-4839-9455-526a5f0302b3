package com.cfpamf.ms.mallorder.service;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.mallgoods.facade.api.GoodsBusDistriFeignClient;
import com.cfpamf.ms.mallgoods.facade.request.GoodsLandingPricePramaDTO;
import com.cfpamf.ms.mallgoods.facade.vo.GoodsLandingPriceResponse;
import com.cfpamf.ms.mallorder.dto.OrderAmountDP;
import com.cfpamf.ms.mallorder.integration.cashier.CashierIntegration;
import com.cfpamf.ms.mallorder.integration.pay.PayIntegration;
import com.cfpamf.ms.mallorder.mapper.OrderLogMapper;
import com.cfpamf.ms.mallorder.mapper.OrderMapper;
import com.cfpamf.ms.mallorder.model.OrderLogModel;
import com.cfpamf.ms.mallorder.po.OrderExtendPO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderPayPO;
import com.cfpamf.ms.mallorder.po.OrderProductPO;
import com.cfpamf.ms.mallorder.req.OrderRenewalPriceReq;
import com.cfpamf.ms.mallorder.service.impl.*;
import com.cfpamf.ms.mallorder.v2.manager.GoodsStockService;
import com.slodon.bbc.core.response.JsonResult;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@RunWith(SpringJUnit4ClassRunner.class)
public class OrderReneawlPriceTest {

	@InjectMocks
	private OrderServiceImpl mockOrderService;
	@Mock
	private OrderServiceImpl orderService;
	@Mock
	private OrderLogModel orderLogModel;
	@Mock
	private GoodsBusDistriFeignClient goodsBusDistriFeignClient;
	@Mock
	private OrderProductServiceImpl orderProductService;
	@Mock
	private OrderExtendServiceImpl orderExtendService;
	@Mock
	private OrderMapper baseMapper;
	@Mock
	private PayIntegration payIntegration;
	@Mock
	private OrderPayServiceImpl orderPayService;
	@Mock
	private OrderPriceRecordServiceImpl orderPriceRecordService;
	@Mock
	private GoodsStockService goodsStockService;
	@Mock
	private CashierIntegration cashierIntegration;
	@Mock
	private OrderLogMapper orderLogMapper;

	@Before
	public void setUp() {
		MockitoAnnotations.initMocks(this);
	}

	@Test
	public void renewalPriceTest() {
		String orderSn = "8223020125980717";

		String req = "{\"orderProductReqs\":[{\"renewalProductShowPrice\":110,\"renewalLandingPrice\":1," +
				"\"renewalBatchNo\":\"CGTB2023010400000002727\",\"orderProductId\":42990}]," +
				"\"orderSn\":\"8223020125980717\",\"renewalExpressFee\":0,\"remark\":\"改价测试\"}";
		OrderRenewalPriceReq orderRenewalPriceReq = JSONObject.parseObject(req, OrderRenewalPriceReq.class);

		//orderService.renewalPrice(orderRenewalPriceReq, 1L, "xwj");

	}
	@Test
	public void testRenewalPriceCalculation() {
		// Create a mock renewal price request
		OrderRenewalPriceReq renewalPriceReq = new OrderRenewalPriceReq();
		renewalPriceReq.setOrderSn("8223020125980717");
		renewalPriceReq.setRenewalExpressFee(new BigDecimal("5"));
		renewalPriceReq.setRemark("改价测试");
		List<OrderRenewalPriceReq.OrderProductRenewalPriceReq> orderProductReqs = new ArrayList<>();
		OrderRenewalPriceReq.OrderProductRenewalPriceReq orderProductReq = new OrderRenewalPriceReq.OrderProductRenewalPriceReq();
		orderProductReq.setOrderProductId(42990L);
		orderProductReq.setRenewalProductShowPrice(new BigDecimal("110.00"));
		orderProductReq.setRenewalBatchNo("CGTB2023010400000002727");
		orderProductReqs.add(orderProductReq);
		renewalPriceReq.setOrderProductReqs(orderProductReqs);

		// Mock external API call to get goods landing price
		GoodsLandingPriceResponse response = new GoodsLandingPriceResponse();
		response.setConvertLandingPrice(new BigDecimal("7.00"));
		JsonResult<GoodsLandingPriceResponse> priceResponseJsonResult = new JsonResult<>();
		priceResponseJsonResult.setData(response);
		when(goodsBusDistriFeignClient.getGoodsDistributionLandingPrice(any(GoodsLandingPricePramaDTO.class)))
				.thenReturn(priceResponseJsonResult);

		when(payIntegration.queryPayStatus(any())).thenReturn(false);

		// Mock order and product data
		OrderPO orderPO = new OrderPO();
		orderPO.setOrderSn("8223020125980717");
		orderPO.setStoreId(2140002L);
		orderPO.setExpressFee(new BigDecimal("3.00"));
		orderPO.setExchangeFlag(0);
		orderPO.setOrderState(10);
		orderPO.setPaymentCode("ONLINE");
		orderPO.setPaySn("1123020128370417");
		orderPO.setChannel("H5");
		orderPO.setOrderType(1);
		orderPO.setPerformanceModes("[0]");
		orderPO.setOrderAmountTotal(new BigDecimal("111"));
		orderPO.setOrderAmount(new BigDecimal(106));
		orderPO.setPayAmount(new BigDecimal("0"));
		orderPO.setMarginOrderAmount(new BigDecimal("0"));
		orderPO.setGoodsAmount(new BigDecimal("111"));
		orderPO.setExpressFeeTotal(new BigDecimal("0"));
		orderPO.setExpressFee(new BigDecimal("0"));
		orderPO.setActivityDiscountAmount(new BigDecimal("5"));
		orderPO.setStoreActivityAmount(new BigDecimal("0"));
		orderPO.setPlatformActivityAmount(new BigDecimal("0"));
		orderPO.setStoreVoucherAmount(new BigDecimal("0"));
		orderPO.setPlatformVoucherAmount(new BigDecimal("5"));
		orderPO.setXzCardAmount(new BigDecimal("0"));
		orderPO.setXzCardExpressFeeAmount(new BigDecimal("0"));
		orderPO.setBalanceAmount(new BigDecimal("0"));
		orderPO.setIntegral(0);
		orderPO.setIntegralCashAmount(new BigDecimal("0"));
		orderPO.setServiceFee(new BigDecimal("0"));
		orderPO.setServiceFeeRate(new BigDecimal("0.03"));
		orderPO.setThirdpartnarFee(new BigDecimal("2.22"));
		orderPO.setThirdpartnarFeeRate(new BigDecimal("0.03"));
		orderPO.setOrderCommission(new BigDecimal("0"));
		orderPO.setBusinessCommission(new BigDecimal("0"));

		OrderProductPO orderProductPO = new OrderProductPO();
		orderProductPO.setOrderSn("8223020125980717");
		orderProductPO.setStoreId(2140002L);
		orderProductPO.setStoreName("连锁18244");
		orderProductPO.setMemberId(241255);
		orderProductPO.setOrderProductId(42990L);
		orderProductPO.setBatchNo("CGTB2022121300000002478");
		orderProductPO.setProductId(200009490008L);
		orderProductPO.setChannelSkuId("************");
		orderProductPO.setGoodsName("Product X");
		orderProductPO.setProductShowPrice(new BigDecimal("111.00"));
		orderProductPO.setLandingPrice(new BigDecimal("1.25"));
		orderProductPO.setProductNum(1);
		orderProductPO.setGoodsAmountTotal(new BigDecimal("111"));
		orderProductPO.setMoneyAmount(new BigDecimal(106));
		orderProductPO.setActivityDiscountAmount(new BigDecimal("5"));
		orderProductPO.setStoreActivityAmount(new BigDecimal("0"));
		orderProductPO.setPlatformActivityAmount(new BigDecimal("0"));
		orderProductPO.setStoreVoucherAmount(new BigDecimal("0"));
		orderProductPO.setPlatformVoucherAmount(new BigDecimal("5"));
		orderProductPO.setXzCardAmount(new BigDecimal("0"));
		orderProductPO.setIntegral(0);
		orderProductPO.setIntegralCashAmount(new BigDecimal("0"));
		orderProductPO.setCommissionRate(new BigDecimal("0"));
		orderProductPO.setCommissionAmount(new BigDecimal("0"));
		orderProductPO.setServiceFee(new BigDecimal("0"));
		orderProductPO.setThirdpartnarFee(new BigDecimal("2.22"));
		orderProductPO.setOrderCommission(new BigDecimal("0"));
		orderProductPO.setBusinessCommission(new BigDecimal("0"));
		orderProductPO.setReturnNumber(0);

		OrderPayPO orderPayPO = new OrderPayPO();
		orderPayPO.setPayId(20216);
		orderPayPO.setPaySn("1123020128370417");
		orderPayPO.setOrderSn("8223020125980717");
		orderPayPO.setPayAmount(new BigDecimal("106"));
		orderPayPO.setMemberId(241255);
		orderPayPO.setApiPayState("0");
		orderPayPO.setPaymentName("在线支付");
		orderPayPO.setPaymentCode("ONLINE");
		orderPayPO.setLoanSuccess(0);
		orderPayPO.setEnjoyPayVipFlag(0);

		//OrderServiceImpl orderService = Mockito.mock(OrderServiceImpl.class);
		Mockito.when(baseMapper.selectOne(any())).thenReturn(orderPO);
		Mockito.when(orderProductService.listByOrderSn(Mockito.anyString())).thenReturn(Collections.singletonList(orderProductPO));
		Mockito.when(orderExtendService.getOrderExtendByOrderSn(Mockito.anyString())).thenReturn(new OrderExtendPO());
		Mockito.when(orderPayService.getByPaySn(any())).thenReturn(orderPayPO);

		// Call the method to calculate renewal price
		Boolean result = mockOrderService.renewalPrice(renewalPriceReq, 1L,"Test User");

		// mock 金额更新
		when(baseMapper.update(any(),any())).thenReturn(1);
		when(orderPayService.update(any(),any())).thenReturn(true);
		when(orderProductService.updateBatchById(any())).thenReturn(true);
		when(orderPriceRecordService.save(any())).thenReturn(true);
		when(orderLogMapper.insert(any())).thenReturn(1);

		// mock 库存、收银台
		doNothing().when(goodsStockService).goodsStock(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any()
				, any(), any(), any(), any(), any(), any(),any());
		when(cashierIntegration.pushToCashier(any(),any())).thenReturn(true);

		// Call the method to calculate renewal price
		Boolean result1 = mockOrderService.renewalPrice(renewalPriceReq, orderPO, new OrderExtendPO(),
				Collections.singletonMap(42990L, orderProductPO),
				new OrderAmountDP(orderPO, Collections.singletonList(orderProductPO)),
				"Test User");

		// Check that the resulting renewal prices are as expected
		assertEquals(Boolean.TRUE, result);
		assertEquals(Boolean.TRUE, result1);
	}

}