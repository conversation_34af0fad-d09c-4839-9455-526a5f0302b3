package com.cfpamf.ms.mallorder.controller.admin;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cfpamf.ms.mallorder.dto.PayMethodDTO;
import com.cfpamf.ms.mallorder.dto.PayMethodReq;
import com.cfpamf.ms.mallorder.service.IPayMethodService;
import com.cfpamf.ms.mallorder.vo.PayMethodPageVO;
import com.slodon.bbc.core.response.JsonResult;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;

import static org.mockito.Mockito.when;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/5/16 15:34
 * @Version 1.0
 */
@RunWith(SpringJUnit4ClassRunner.class)
public class AdminPayMethodControllerTest {
    @Mock
    IPayMethodService iPayMethodService;
    @Mock
    Logger log;
    @InjectMocks
    AdminPayMethodController adminPayMethodController;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testPagePayMethodList() throws Exception {
        PayMethodReq payMethodReq = new PayMethodReq();
        payMethodReq.setPayMethodName("");
        payMethodReq.setCurrent(0);
        payMethodReq.setPageSize(0);

        PayMethodPageVO payMethodPageVO = new PayMethodPageVO();
        payMethodPageVO.setId(0L);
        payMethodPageVO.setPayMethodCode("");
        payMethodPageVO.setPayMethodName("");
        payMethodPageVO.setPayMethodDesc("");
        payMethodPageVO.setPayMethodStatus(0);
        payMethodPageVO.setMinAmount(new BigDecimal("0"));
        payMethodPageVO.setSupportOrderType("");
        payMethodPageVO.setSupportMerchant("");
        payMethodPageVO.setSupportOrderChannel("");
        payMethodPageVO.setIsLoanPay(0);
        payMethodPageVO.setLoanCode("");
        payMethodPageVO.setLoanProduct("");
        payMethodPageVO.setLoanProductName("");
        payMethodPageVO.setSort(0);
        payMethodPageVO.setAllowUserFlag(0);
        payMethodPageVO.setCreateTime(LocalDateTime.now());
        payMethodPageVO.setUpdateTime(LocalDateTime.now());
        payMethodPageVO.setCreateBy("");
        payMethodPageVO.setUpdateBy("");
        payMethodPageVO.setEnabledFlag(false);
        payMethodPageVO.setPayIconUrl("");
        PayMethodPageVO payMethodPageVO2 = new PayMethodPageVO();
        payMethodPageVO2.setId(0L);
        payMethodPageVO2.setPayMethodCode("");
        payMethodPageVO2.setPayMethodName("");
        payMethodPageVO2.setPayMethodDesc("");
        payMethodPageVO2.setPayMethodStatus(0);
        payMethodPageVO2.setMinAmount(new BigDecimal("0"));
        payMethodPageVO2.setSupportOrderType("");
        payMethodPageVO2.setSupportMerchant("");
        payMethodPageVO2.setSupportOrderChannel("");
        payMethodPageVO2.setIsLoanPay(0);
        payMethodPageVO2.setLoanCode("");
        payMethodPageVO2.setLoanProduct("");
        payMethodPageVO2.setLoanProductName("");
        payMethodPageVO2.setSort(0);
        payMethodPageVO2.setAllowUserFlag(0);
        payMethodPageVO2.setCreateTime(LocalDateTime.now());
        payMethodPageVO2.setUpdateTime(LocalDateTime.now());
        payMethodPageVO2.setCreateBy("");
        payMethodPageVO2.setUpdateBy("");
        payMethodPageVO2.setEnabledFlag(false);
        payMethodPageVO2.setPayIconUrl("");


        Page<PayMethodPageVO> page = new Page<>();
        page.setRecords(Arrays.asList(payMethodPageVO, payMethodPageVO2));

        JsonResult<Page<PayMethodPageVO>> objectJsonResult = new JsonResult<>();
        objectJsonResult.setData(page);
        Assert.assertNotNull(page.getRecords());
    }

    @Test
    public void testSavePayMethod() throws Exception {
        PayMethodDTO payMethodDTO = new PayMethodDTO();
        payMethodDTO.setId(0L);
        payMethodDTO.setPayMethodStatus(0);
        payMethodDTO.setMinAmount(new BigDecimal("0"));
        payMethodDTO.setSupportOrderType("");
        payMethodDTO.setSupportOrderChannel("");
        payMethodDTO.setIsLoanPay(0);
        payMethodDTO.setLoanCode("");
        payMethodDTO.setLoanProduct("");
        payMethodDTO.setLoanProductName("");
        payMethodDTO.setSort(0);
        payMethodDTO.setPayIconPath("");
        payMethodDTO.setOperateUserName("");

        PayMethodDTO payMethodDTO1 = new PayMethodDTO();
        payMethodDTO1.setId(0L);
        payMethodDTO1.setPayMethodStatus(0);
        payMethodDTO1.setMinAmount(new BigDecimal("0"));
        payMethodDTO1.setSupportOrderType("");
        payMethodDTO1.setSupportOrderChannel("");
        payMethodDTO1.setIsLoanPay(0);
        payMethodDTO1.setLoanCode("");
        payMethodDTO1.setLoanProduct("");
        payMethodDTO1.setLoanProductName("");
        payMethodDTO1.setSort(0);
        payMethodDTO1.setPayIconPath("");
        payMethodDTO1.setOperateUserName("");

        when(iPayMethodService.savePayMethod(payMethodDTO1)).thenReturn(null);

        JsonResult result = adminPayMethodController.savePayMethod(payMethodDTO);
        Assert.assertEquals(null, result);
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme