//package com.cfpamf.ms.mallorder.controller.front;
//
//import com.cfpamf.mallpayment.facade.vo.PaymentRefundNotifyVO;
//import com.cfpamf.ms.mallorder.StubIntergration;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//
//import static org.junit.jupiter.api.Assertions.*;
//
//class FrontOrderPayPOCallbackControllerTest extends StubIntergration {
//
//    @Autowired
//    private  FrontOrderPayCallbackController frontOrderPayCallbackController;
//
//    @Test
//    void refundNotify() {
//
//        PaymentRefundNotifyVO notifyVO = new PaymentRefundNotifyVO();
//        notifyVO.setOrderOn("8221072001310336");
//        notifyVO.setPayTradeNo(null);
//        notifyVO.setRefundOn("8221072001310336");
//        notifyVO.setRefundStatus(1);
//
//
//        frontOrderPayCallbackController.refundNotify(notifyVO);
//    }
//}