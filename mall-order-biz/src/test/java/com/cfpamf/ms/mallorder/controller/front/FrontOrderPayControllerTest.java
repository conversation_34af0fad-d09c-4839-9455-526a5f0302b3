////package com.cfpamf.ms.mallorder.controller.front;
////
////import com.cfpamf.ms.mallorder.integration.loan.LoanPayIntegration;
////import com.cfpamf.ms.mallorder.model.OrderPayModel;
////import com.cfpamf.ms.mallorder.service.IOrderExtendService;
////import com.cfpamf.ms.mallorder.service.IOrderService;
////import com.cfpamf.ms.mallorder.service.IPayMethodService;
////import com.cfpamf.ms.mallorder.service.impl.PayMethodServiceImpl;
////import org.junit.Assert;
////import org.junit.Before;
////import org.junit.Test;
////import org.junit.runner.RunWith;
////import org.mockito.InjectMocks;
////import org.mockito.Mock;
////import org.mockito.MockitoAnnotations;
////import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
////
////import java.util.ArrayList;
////import java.util.Arrays;
////import java.util.List;
////
////@RunWith(SpringJUnit4ClassRunner.class)
////public class FrontOrderPayControllerTest {
////
////    @InjectMocks
////    FrontOrderPayController orderPayController;
////    @Mock
////    private IPayMethodService payMethodService;
////    @Mock
////    private OrderPayModel orderPayModel;
////    @Mock
////    private IOrderService orderService;
////    @Mock
////    private PayMethodServiceImpl  methodService;
////    @Mock
////    private IOrderExtendService orderExtendService;
////    @Mock
////    private LoanPayIntegration loanPayIntegration;
////
////    @Before
////    public void init() {
////        MockitoAnnotations.initMocks(this);
////    }
////
////    @Test
////    public void fullIntersection() {
////
////        List<String> storeIds = new ArrayList<>();
////        storeIds.add("210004");
////
////        List<String> storeIdsStrSet = Arrays.asList("210003,250003,260002,250004,270002,270502,270402,270303,270203,271402,271109,271110,271502,271107,210004".split(","));
////
////        Assert.assertTrue(methodService.fullIntersection(storeIds, storeIdsStrSet));
////    }
////
////    @Test
////    public void listRepaymentList() {
////        MockHttpServletRequest request = new MockHttpServletRequest();
////        Member member = JMockData.mock(Member.class);
////        request.addHeader("User-Header", JSON.toJSONString(member));
////
////        PayMethodPO enjoyPayMethod = new PayMethodPO();//JMockData.mock(PayMethodPO.class);
////        enjoyPayMethod.setPayMethodCode(PayMethodEnum.ENJOY_PAY.getValue());
////        Mockito.when(payMethodService.getOne(Mockito.any())).thenReturn(enjoyPayMethod);
////
////
////        OrderPayPO orderPayPO = JMockData.mock(OrderPayPO.class);
////        Mockito.when(orderPayModel.getOrderPayByPaySn(Mockito.anyString())).thenReturn(orderPayPO);
////
////        List<OrderPO> orderPOS = JMockData.mock(new TypeReference<List<OrderPO>>() {});
////
////        Mockito.when(orderService.list(Mockito.any())).thenReturn(orderPOS);
////
////        Mockito.when(orderExtendService.getOrderExtendByOrderSn(Mockito.anyString()))
////                .thenReturn(JMockData.mock(OrderExtendPO.class));
////
////        List<RepaymentVo> vos = JMockData.mock(new TypeReference<List<RepaymentVo>>() {});
////
////        Mockito.when(loanPayIntegration.listLoanRepayment(Mockito.any()))
////                .thenReturn(vos);
////
////        JsonResult result = orderPayController.listRepaymentList(
////                request,
////                "ENJOY_PAY",
////                "2323",
////                3,
////                "234343",
////                "2323",
////                "32434");
////
////        Assert.assertTrue(result.getState() == 200);
////
////    }
////
////    @Test
////    public void payMethodV2() {
////    }
////}
//package com.cfpamf.ms.mallorder.controller.front;
//
//import com.cfpamf.ms.mallorder.integration.loan.LoanPayIntegration;
//import com.cfpamf.ms.mallorder.model.OrderPayModel;
//import com.cfpamf.ms.mallorder.service.IOrderExtendService;
//import com.cfpamf.ms.mallorder.service.IOrderService;
//import com.cfpamf.ms.mallorder.service.IPayMethodService;
//import com.cfpamf.ms.mallorder.service.impl.PayMethodServiceImpl;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.MockitoAnnotations;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//
//@RunWith(SpringJUnit4ClassRunner.class)
//public class FrontOrderPayControllerTest {
//
//    @InjectMocks
//    FrontOrderPayController orderPayController;
//    @Mock
//    private IPayMethodService payMethodService;
//    @Mock
//    private OrderPayModel orderPayModel;
//    @Mock
//    private IOrderService orderService;
//    @Mock
//    private PayMethodServiceImpl  methodService;
//    @Mock
//    private IOrderExtendService orderExtendService;
//    @Mock
//    private LoanPayIntegration loanPayIntegration;
//
//    @Before
//    public void init() {
//        MockitoAnnotations.initMocks(this);
//    }
//
///*    @Test
//    public void fullIntersection() {
//
//        List<String> storeIds = new ArrayList<>();
//        storeIds.add("210004");
//
//        List<String> storeIdsStrSet = Arrays.asList("210003,250003,260002,250004,270002,270502,270402,270303,270203,271402,271109,271110,271502,271107,210004".split(","));
//
//        Assert.assertTrue(methodService.fullIntersection(storeIds, storeIdsStrSet));
//    }*/
//
//    @Test
//    public void listRepaymentList() {
//        //MockHttpServletRequest request = new MockHttpServletRequest();
//        //Member member = JMockData.mock(Member.class);
//        //request.addHeader("User-Header", JSON.toJSONString(member));
//        //
//        //PayMethodPO enjoyPayMethod = new PayMethodPO();//JMockData.mock(PayMethodPO.class);
//        //enjoyPayMethod.setPayMethodCode(PayMethodEnum.ENJOY_PAY.getValue());
//        //Mockito.when(payMethodService.getOne(Mockito.any())).thenReturn(enjoyPayMethod);
//        //
//        //
//        //OrderPayPO orderPayPO = JMockData.mock(OrderPayPO.class);
//        //Mockito.when(orderPayModel.getOrderPayByPaySn(Mockito.anyString())).thenReturn(orderPayPO);
//        //
//        //List<OrderPO> orderPOS = JMockData.mock(new TypeReference<List<OrderPO>>() {});
//        //
//        //Mockito.when(orderService.list(Mockito.any())).thenReturn(orderPOS);
//        //
//        //Mockito.when(orderExtendService.getOrderExtendByOrderSn(Mockito.anyString()))
//        //        .thenReturn(JMockData.mock(OrderExtendPO.class));
//        //
//        //List<RepaymentVo> vos = JMockData.mock(new TypeReference<List<RepaymentVo>>() {});
//        //
//        //Mockito.when(loanPayIntegration.listLoanRepayment(Mockito.any()))
//        //        .thenReturn(vos);
//        //
//        //JsonResult result = orderPayController.listRepaymentList(
//        //        request,
//        //        "ENJOY_PAY",
//        //        "2323",
//        //        3,
//        //        "234343",
//        //        "2323",
//        //        "32434");
//        //
//        //Assert.assertTrue(result.getState() == 200);
//
//    }
//
//    @Test
//    public void payMethodV2() {
//    }
//}
