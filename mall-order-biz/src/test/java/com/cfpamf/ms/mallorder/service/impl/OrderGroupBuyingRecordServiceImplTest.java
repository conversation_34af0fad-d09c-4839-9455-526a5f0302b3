package com.cfpamf.ms.mallorder.service.impl;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cfpamf.ms.mallorder.common.enums.OrderProductDeliveryEnum;
import com.cfpamf.ms.mallorder.dto.*;
import com.cfpamf.ms.mallorder.mapper.OrderGroupBuyingBindMapper;
import com.cfpamf.ms.mallorder.mapper.OrderGroupBuyingRecordMapper;
import com.cfpamf.ms.mallorder.po.OrderExtendPO;
import com.cfpamf.ms.mallorder.po.OrderGroupBuyingBindPO;
import com.cfpamf.ms.mallorder.po.OrderGroupBuyingRecordPO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.req.query.GroupOrderQuery;
import com.cfpamf.ms.mallorder.req.query.GroupOrderRecordQuery;
import com.cfpamf.ms.mallorder.req.query.OrderGroupBuyingBindQuery;
import com.cfpamf.ms.mallorder.service.IOrderExtendService;
import com.cfpamf.ms.mallorder.vo.*;
import com.cfpamf.ms.mallshop.api.StoreLabelBindGoodsFeignClient;
import com.cfpamf.ms.mallshop.request.StoreLabelBindGoodsExample;
import com.cfpamf.ms.mallshop.resp.StoreLabelBindGoods;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.response.PageVO;
import com.slodon.bbc.core.response.PagerInfo;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@RunWith(SpringJUnit4ClassRunner.class)
public class OrderGroupBuyingRecordServiceImplTest {

	@Mock
	private OrderGroupBuyingRecordMapper mockGroupBuyingRecordMapper;

	@Mock
	private OrderGroupBuyingBindMapper mockGroupBuyingBindMapper;

	@Mock
	private IOrderExtendService mockOrderExtendService;

	@Mock
	private StoreLabelBindGoodsFeignClient mockStoreLabelBindGoodsFeignClient;

	@InjectMocks
	private OrderGroupBuyingRecordServiceImpl orderGroupBuyingRecordServiceImplUnderTest;

	@Before
	public void setUp() {
		MockitoAnnotations.initMocks(this);
		TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), OrderGroupBuyingRecordPO.class);
		TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), OrderGroupBuyingBindPO.class);
	}

	@Test
	public void testListOrderGroupBuyingRecord() {
		// Setup
		final GroupOrderRecordQuery query = new GroupOrderRecordQuery();
		query.setCurrent(1);
		query.setPageSize(10);
		query.setGroupBuyingCode("groupBuyingCode");
		query.setGiftOrderSn("giftOrderSn");
		query.setStoreId(0L);

		final GroupOrderRecordVO groupOrderRecordVO = new GroupOrderRecordVO();
		groupOrderRecordVO.setGroupBuyingId(0L);
		groupOrderRecordVO.setGroupBuyingCode("groupBuyingCode");
		groupOrderRecordVO.setGiftOrderSn("giftOrderSn");
		groupOrderRecordVO.setStoreId(0L);
		groupOrderRecordVO.setStoreName("storeName");
		final PageVO<GroupOrderRecordVO> expectedResult = new PageVO<>(Collections.singletonList(groupOrderRecordVO), new PagerInfo(1, 10));
		final Page<GroupOrderRecordVO> pageVO = new Page<>(1, 10);
		pageVO.setRecords(Collections.singletonList(groupOrderRecordVO));

		// Configure OrderGroupBuyingRecordMapper.ListOrderGroupBuyingRecord(...).
		final GroupOrderRecordQuery query1 = new GroupOrderRecordQuery();
		query1.setCurrent(1);
		query1.setPageSize(10);
		query1.setGroupBuyingCode("groupBuyingCode");
		query1.setGiftOrderSn("giftOrderSn");
		query1.setStoreId(0L);
		when(mockGroupBuyingRecordMapper.ListOrderGroupBuyingRecord(any(), eq(query1))).thenReturn(pageVO);

		// Run the test
		final PageVO<GroupOrderRecordVO> result = orderGroupBuyingRecordServiceImplUnderTest.ListOrderGroupBuyingRecord(query);

		// Verify the results
		assertThat(result.getList().get(0).getGroupBuyingCode()).isEqualTo(expectedResult.getList().get(0).getGroupBuyingCode());
	}

	@Test
	public void testListOrderGroupBuyingRecord_OrderGroupBuyingRecordMapperReturnsNull() {
		// Setup
		final GroupOrderRecordQuery query = new GroupOrderRecordQuery();
		query.setCurrent(1);
		query.setPageSize(10);
		query.setGroupBuyingCode("groupBuyingCode");
		query.setGiftOrderSn("giftOrderSn");
		query.setStoreId(0L);

		final GroupOrderRecordVO groupOrderRecordVO = new GroupOrderRecordVO();
		groupOrderRecordVO.setGroupBuyingId(0L);
		groupOrderRecordVO.setGroupBuyingCode("groupBuyingCode");
		groupOrderRecordVO.setGiftOrderSn("giftOrderSn");
		groupOrderRecordVO.setStoreId(0L);
		groupOrderRecordVO.setStoreName("storeName");
		final PageVO<GroupOrderRecordVO> expectedResult =
				new PageVO<>(null, new PagerInfo(1, 10));

		// Configure OrderGroupBuyingRecordMapper.ListOrderGroupBuyingRecord(...).
		final GroupOrderRecordQuery query1 = new GroupOrderRecordQuery();
		query1.setCurrent(1);
		query1.setPageSize(10);
		query1.setGroupBuyingCode("groupBuyingCode");
		query1.setGiftOrderSn("giftOrderSn");
		query1.setStoreId(0L);
		when(mockGroupBuyingRecordMapper.ListOrderGroupBuyingRecord(any(Page.class), eq(query1))).thenReturn(null);

		// Run the test
		final PageVO<GroupOrderRecordVO> result = orderGroupBuyingRecordServiceImplUnderTest.ListOrderGroupBuyingRecord(query);

		// Verify the results
		assertThat(result.getList()).isEqualTo(null);
	}

	@Test
	public void testOrderGroupBuyingDetail() {
		// Setup
		final GroupOrderRecordDetailVO expectedResult = new GroupOrderRecordDetailVO();
		final GroupOrderVO groupOrderVO = new GroupOrderVO();
		groupOrderVO.setOrderSn("orderSn");
		final GroupOrderProductVO groupOrderProductVO = new GroupOrderProductVO();
		groupOrderProductVO.setOrderSn("orderSn");
		groupOrderProductVO.setGoodsName("goodsName");
		groupOrderProductVO.setValidNum(0);
		groupOrderProductVO.setDeliveryState(OrderProductDeliveryEnum.WAIT_DELIVERY);
		groupOrderProductVO.setGroupBuyingTag(0);
		groupOrderProductVO.setIsGift(0);
		groupOrderVO.setOrderProductVOs(Arrays.asList(groupOrderProductVO));
		expectedResult.setGroupOrderInfoList(Arrays.asList(groupOrderVO));
		final GroupOrderVO giftOrderInfo = new GroupOrderVO();
		giftOrderInfo.setOrderSn("orderSn");
		final GroupOrderProductVO groupOrderProductVO1 = new GroupOrderProductVO();
		groupOrderProductVO1.setOrderSn("orderSn");
		groupOrderProductVO1.setGoodsName("goodsName");
		groupOrderProductVO1.setValidNum(0);
		groupOrderProductVO1.setDeliveryState(OrderProductDeliveryEnum.WAIT_DELIVERY);
		groupOrderProductVO1.setGroupBuyingTag(0);
		groupOrderProductVO1.setIsGift(0);
		giftOrderInfo.setOrderProductVOs(Arrays.asList(groupOrderProductVO1));
		expectedResult.setGiftOrderInfo(giftOrderInfo);
		final OrderAddressDTO giftReceiveInfo = new OrderAddressDTO();
		expectedResult.setGiftReceiveInfo(giftReceiveInfo);

		// Configure OrderGroupBuyingRecordMapper.selectOne(...).
		final OrderGroupBuyingRecordPO orderGroupBuyingRecordPO = new OrderGroupBuyingRecordPO();
		orderGroupBuyingRecordPO.setGroupBuyingCode("groupBuyingCode");
		orderGroupBuyingRecordPO.setGiftOrderSn("giftOrderSn");
		orderGroupBuyingRecordPO.setGroupOrderNum(0);
		orderGroupBuyingRecordPO.setGroupGoodsNum(0);
		orderGroupBuyingRecordPO.setGiftGoodsNum(0);
		orderGroupBuyingRecordPO.setStoreId(0L);
		orderGroupBuyingRecordPO.setStoreName("storeName");
		orderGroupBuyingRecordPO.setStoreCategoryId(0);
		orderGroupBuyingRecordPO.setStoreCategoryName("innerLabelName");
		orderGroupBuyingRecordPO.setBranchCode("branch");
		orderGroupBuyingRecordPO.setBranchName("branchName");
		orderGroupBuyingRecordPO.setAreaCode("areaCode");
		orderGroupBuyingRecordPO.setAreaName("areaName");
		orderGroupBuyingRecordPO.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
		orderGroupBuyingRecordPO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
		orderGroupBuyingRecordPO.setCreateBy("createBy");
		orderGroupBuyingRecordPO.setUpdateBy("createBy");
		orderGroupBuyingRecordPO.setEnabledFlag(0);
		when(mockGroupBuyingRecordMapper.selectOne(any())).thenReturn(orderGroupBuyingRecordPO);

		// Configure OrderGroupBuyingBindMapper.listBindGroupOrder(...).
		final GroupOrderVO groupOrderVO1 = new GroupOrderVO();
		groupOrderVO1.setOrderSn("orderSn");
		final GroupOrderProductVO groupOrderProductVO2 = new GroupOrderProductVO();
		groupOrderProductVO2.setOrderSn("orderSn");
		groupOrderProductVO2.setGoodsName("goodsName");
		groupOrderProductVO2.setValidNum(0);
		groupOrderProductVO2.setDeliveryState(OrderProductDeliveryEnum.WAIT_DELIVERY);
		groupOrderProductVO2.setGroupBuyingTag(0);
		groupOrderProductVO2.setIsGift(0);
		groupOrderVO1.setOrderProductVOs(Arrays.asList(groupOrderProductVO2));
		final List<GroupOrderVO> groupOrderVOS = Collections.singletonList(groupOrderVO1);
		final OrderGroupBuyingBindQuery query = new OrderGroupBuyingBindQuery();
		query.setGroupBuyingCode("groupBuyingCode");
		query.setGiftOrderSn("giftOrderSn");
		query.setGroupOrderSn("groupOrderSn");
		query.setOrderProductId(0L);
		query.setOrderSn("orderSn");
		when(mockGroupBuyingBindMapper.listBindGroupOrder(any())).thenReturn(groupOrderVOS);

		// Configure OrderGroupBuyingRecordMapper.listGroupOrder(...).
		final GroupOrderVO groupOrderVO2 = new GroupOrderVO();
		groupOrderVO2.setOrderSn("orderSn");
		final GroupOrderProductVO groupOrderProductVO3 = new GroupOrderProductVO();
		groupOrderProductVO3.setOrderSn("orderSn");
		groupOrderProductVO3.setGoodsName("goodsName");
		groupOrderProductVO3.setValidNum(0);
		groupOrderProductVO3.setDeliveryState(OrderProductDeliveryEnum.WAIT_DELIVERY);
		groupOrderProductVO3.setGroupBuyingTag(0);
		groupOrderProductVO3.setIsGift(0);
		groupOrderVO2.setOrderProductVOs(Arrays.asList(groupOrderProductVO3));
		final List<GroupOrderVO> groupOrderVOS1 = Collections.singletonList(groupOrderVO2);
		final GroupOrderQuery query1 = new GroupOrderQuery();
		query1.setCurrent(0);
		query1.setPageSize(0);
		query1.setStoreId(0L);
		query1.setStoreCategoryId("storeCategoryId");
		query1.setBranchCode("branchCode");
		query1.setOrderSn("giftOrderSn");
		query1.setGroupOrderProductIdList(Arrays.asList(0L));
		when(mockGroupBuyingRecordMapper.listGroupOrder(any())).thenReturn(groupOrderVOS1);

		// Configure IOrderExtendService.getOrderExtendByOrderSn(...).
		final OrderExtendPO orderExtendPO = new OrderExtendPO();
		orderExtendPO.setExtendId(0);
		orderExtendPO.setBranch("branch");
		orderExtendPO.setBranchName("branchName");
		orderExtendPO.setAreaCode("areaCode");
		orderExtendPO.setAreaName("areaName");
		when(mockOrderExtendService.getOrderExtendByOrderSn("giftOrderSn")).thenReturn(orderExtendPO);

		// Configure IOrderExtendService.buildReceiveInfo(...).
		final OrderAddressDTO orderAddressDTO = new OrderAddressDTO();
		orderAddressDTO.setReceiverName("receiverName");
		orderAddressDTO.setReceiverMobile("receiverMobile");
		orderAddressDTO.setProvince("province");
		orderAddressDTO.setCity("city");
		orderAddressDTO.setCityCode("cityCode");
		final OrderExtendPO orderExtendPO1 = new OrderExtendPO();
		orderExtendPO1.setExtendId(0);
		orderExtendPO1.setBranch("branch");
		orderExtendPO1.setBranchName("branchName");
		orderExtendPO1.setAreaCode("areaCode");
		orderExtendPO1.setAreaName("areaName");
		when(mockOrderExtendService.buildReceiveInfo(orderExtendPO)).thenReturn(orderAddressDTO);

		// Run the test
		final GroupOrderRecordDetailVO result = orderGroupBuyingRecordServiceImplUnderTest.orderGroupBuyingDetail("groupBuyingCode");

		// Verify the results
		Assert.assertNotNull(result);
	}

	@Test
	public void testOrderGroupBuyingDetail_OrderGroupBuyingBindMapperReturnsNoItems() {
		// Setup
		final GroupOrderRecordDetailVO expectedResult = new GroupOrderRecordDetailVO();
		final GroupOrderVO groupOrderVO = new GroupOrderVO();
		groupOrderVO.setOrderSn("orderSn");
		final GroupOrderProductVO groupOrderProductVO = new GroupOrderProductVO();
		groupOrderProductVO.setOrderSn("orderSn");
		groupOrderProductVO.setGoodsName("goodsName");
		groupOrderProductVO.setValidNum(0);
		groupOrderProductVO.setDeliveryState(OrderProductDeliveryEnum.WAIT_DELIVERY);
		groupOrderProductVO.setGroupBuyingTag(0);
		groupOrderProductVO.setIsGift(0);
		groupOrderVO.setOrderProductVOs(Arrays.asList(groupOrderProductVO));
		expectedResult.setGroupOrderInfoList(Arrays.asList(groupOrderVO));
		final GroupOrderVO giftOrderInfo = new GroupOrderVO();
		giftOrderInfo.setOrderSn("orderSn");
		final GroupOrderProductVO groupOrderProductVO1 = new GroupOrderProductVO();
		groupOrderProductVO1.setOrderSn("orderSn");
		groupOrderProductVO1.setGoodsName("goodsName");
		groupOrderProductVO1.setValidNum(0);
		groupOrderProductVO1.setDeliveryState(OrderProductDeliveryEnum.WAIT_DELIVERY);
		groupOrderProductVO1.setGroupBuyingTag(0);
		groupOrderProductVO1.setIsGift(0);
		giftOrderInfo.setOrderProductVOs(Arrays.asList(groupOrderProductVO1));
		expectedResult.setGiftOrderInfo(giftOrderInfo);
		final OrderAddressDTO giftReceiveInfo = new OrderAddressDTO();
		expectedResult.setGiftReceiveInfo(giftReceiveInfo);

		// Configure OrderGroupBuyingRecordMapper.selectOne(...).
		final OrderGroupBuyingRecordPO orderGroupBuyingRecordPO = new OrderGroupBuyingRecordPO();
		orderGroupBuyingRecordPO.setGroupBuyingCode("groupBuyingCode");
		orderGroupBuyingRecordPO.setGiftOrderSn("giftOrderSn");
		orderGroupBuyingRecordPO.setGroupOrderNum(0);
		orderGroupBuyingRecordPO.setGroupGoodsNum(0);
		orderGroupBuyingRecordPO.setGiftGoodsNum(0);
		orderGroupBuyingRecordPO.setStoreId(0L);
		orderGroupBuyingRecordPO.setStoreName("storeName");
		orderGroupBuyingRecordPO.setStoreCategoryId(0);
		orderGroupBuyingRecordPO.setStoreCategoryName("innerLabelName");
		orderGroupBuyingRecordPO.setBranchCode("branch");
		orderGroupBuyingRecordPO.setBranchName("branchName");
		orderGroupBuyingRecordPO.setAreaCode("areaCode");
		orderGroupBuyingRecordPO.setAreaName("areaName");
		orderGroupBuyingRecordPO.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
		orderGroupBuyingRecordPO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
		orderGroupBuyingRecordPO.setCreateBy("createBy");
		orderGroupBuyingRecordPO.setUpdateBy("createBy");
		orderGroupBuyingRecordPO.setEnabledFlag(0);
		when(mockGroupBuyingRecordMapper.selectOne(any())).thenReturn(orderGroupBuyingRecordPO);

		// Configure OrderGroupBuyingBindMapper.listBindGroupOrder(...).
		final OrderGroupBuyingBindQuery query = new OrderGroupBuyingBindQuery();
		query.setGroupBuyingCode("groupBuyingCode");
		query.setGiftOrderSn("giftOrderSn");
		query.setGroupOrderSn("groupOrderSn");
		query.setOrderProductId(0L);
		query.setOrderSn("orderSn");
		when(mockGroupBuyingBindMapper.listBindGroupOrder(any())).thenReturn(Collections.emptyList());

		// Configure OrderGroupBuyingRecordMapper.listGroupOrder(...).
		final GroupOrderVO groupOrderVO1 = new GroupOrderVO();
		groupOrderVO1.setOrderSn("orderSn");
		final GroupOrderProductVO groupOrderProductVO2 = new GroupOrderProductVO();
		groupOrderProductVO2.setOrderSn("orderSn");
		groupOrderProductVO2.setGoodsName("goodsName");
		groupOrderProductVO2.setValidNum(0);
		groupOrderProductVO2.setDeliveryState(OrderProductDeliveryEnum.WAIT_DELIVERY);
		groupOrderProductVO2.setGroupBuyingTag(0);
		groupOrderProductVO2.setIsGift(0);
		groupOrderVO1.setOrderProductVOs(Arrays.asList(groupOrderProductVO2));
		final List<GroupOrderVO> groupOrderVOS = Arrays.asList(groupOrderVO1);
		final GroupOrderQuery query1 = new GroupOrderQuery();
		query1.setCurrent(0);
		query1.setPageSize(0);
		query1.setStoreId(0L);
		query1.setStoreCategoryId("storeCategoryId");
		query1.setBranchCode("branchCode");
		query1.setOrderSn("giftOrderSn");
		query1.setGroupOrderProductIdList(Arrays.asList(0L));
		when(mockGroupBuyingRecordMapper.listGroupOrder(any())).thenReturn(groupOrderVOS);

		// Configure IOrderExtendService.getOrderExtendByOrderSn(...).
		final OrderExtendPO orderExtendPO = new OrderExtendPO();
		orderExtendPO.setExtendId(0);
		orderExtendPO.setBranch("branch");
		orderExtendPO.setBranchName("branchName");
		orderExtendPO.setAreaCode("areaCode");
		orderExtendPO.setAreaName("areaName");
		when(mockOrderExtendService.getOrderExtendByOrderSn("giftOrderSn")).thenReturn(orderExtendPO);

		// Configure IOrderExtendService.buildReceiveInfo(...).
		final OrderAddressDTO orderAddressDTO = new OrderAddressDTO();
		orderAddressDTO.setReceiverName("receiverName");
		orderAddressDTO.setReceiverMobile("receiverMobile");
		orderAddressDTO.setProvince("province");
		orderAddressDTO.setCity("city");
		orderAddressDTO.setCityCode("cityCode");
		final OrderExtendPO orderExtendPO1 = new OrderExtendPO();
		orderExtendPO1.setExtendId(0);
		orderExtendPO1.setBranch("branch");
		orderExtendPO1.setBranchName("branchName");
		orderExtendPO1.setAreaCode("areaCode");
		orderExtendPO1.setAreaName("areaName");
		when(mockOrderExtendService.buildReceiveInfo(orderExtendPO)).thenReturn(orderAddressDTO);

		// Run the test
		final GroupOrderRecordDetailVO result = orderGroupBuyingRecordServiceImplUnderTest.orderGroupBuyingDetail("groupBuyingCode");

		// Verify the results
		Assert.assertTrue(CollectionUtils.isEmpty(result.getGroupOrderInfoList()));
	}

	@Test
	public void testOrderGroupBuyingDetail_OrderGroupBuyingRecordMapperListGroupOrderReturnsNoItems() {
		// Setup
		final GroupOrderRecordDetailVO expectedResult = new GroupOrderRecordDetailVO();
		final GroupOrderVO groupOrderVO = new GroupOrderVO();
		groupOrderVO.setOrderSn("orderSn");
		final GroupOrderProductVO groupOrderProductVO = new GroupOrderProductVO();
		groupOrderProductVO.setOrderSn("orderSn");
		groupOrderProductVO.setGoodsName("goodsName");
		groupOrderProductVO.setValidNum(0);
		groupOrderProductVO.setDeliveryState(OrderProductDeliveryEnum.WAIT_DELIVERY);
		groupOrderProductVO.setGroupBuyingTag(0);
		groupOrderProductVO.setIsGift(0);
		groupOrderVO.setOrderProductVOs(Arrays.asList(groupOrderProductVO));
		expectedResult.setGroupOrderInfoList(Arrays.asList(groupOrderVO));
		final GroupOrderVO giftOrderInfo = new GroupOrderVO();
		giftOrderInfo.setOrderSn("orderSn");
		final GroupOrderProductVO groupOrderProductVO1 = new GroupOrderProductVO();
		groupOrderProductVO1.setOrderSn("orderSn");
		groupOrderProductVO1.setGoodsName("goodsName");
		groupOrderProductVO1.setValidNum(0);
		groupOrderProductVO1.setDeliveryState(OrderProductDeliveryEnum.WAIT_DELIVERY);
		groupOrderProductVO1.setGroupBuyingTag(0);
		groupOrderProductVO1.setIsGift(0);
		giftOrderInfo.setOrderProductVOs(Arrays.asList(groupOrderProductVO1));
		expectedResult.setGiftOrderInfo(giftOrderInfo);
		final OrderAddressDTO giftReceiveInfo = new OrderAddressDTO();
		expectedResult.setGiftReceiveInfo(giftReceiveInfo);

		// Configure OrderGroupBuyingRecordMapper.selectOne(...).
		final OrderGroupBuyingRecordPO orderGroupBuyingRecordPO = new OrderGroupBuyingRecordPO();
		orderGroupBuyingRecordPO.setGroupBuyingCode("groupBuyingCode");
		orderGroupBuyingRecordPO.setGiftOrderSn("giftOrderSn");
		orderGroupBuyingRecordPO.setGroupOrderNum(0);
		orderGroupBuyingRecordPO.setGroupGoodsNum(0);
		orderGroupBuyingRecordPO.setGiftGoodsNum(0);
		orderGroupBuyingRecordPO.setStoreId(0L);
		orderGroupBuyingRecordPO.setStoreName("storeName");
		orderGroupBuyingRecordPO.setStoreCategoryId(0);
		orderGroupBuyingRecordPO.setStoreCategoryName("innerLabelName");
		orderGroupBuyingRecordPO.setBranchCode("branch");
		orderGroupBuyingRecordPO.setBranchName("branchName");
		orderGroupBuyingRecordPO.setAreaCode("areaCode");
		orderGroupBuyingRecordPO.setAreaName("areaName");
		orderGroupBuyingRecordPO.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
		orderGroupBuyingRecordPO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
		orderGroupBuyingRecordPO.setCreateBy("createBy");
		orderGroupBuyingRecordPO.setUpdateBy("createBy");
		orderGroupBuyingRecordPO.setEnabledFlag(0);
		when(mockGroupBuyingRecordMapper.selectOne(any())).thenReturn(orderGroupBuyingRecordPO);

		// Configure OrderGroupBuyingBindMapper.listBindGroupOrder(...).
		final GroupOrderVO groupOrderVO1 = new GroupOrderVO();
		groupOrderVO1.setOrderSn("orderSn");
		final GroupOrderProductVO groupOrderProductVO2 = new GroupOrderProductVO();
		groupOrderProductVO2.setOrderSn("orderSn");
		groupOrderProductVO2.setGoodsName("goodsName");
		groupOrderProductVO2.setValidNum(0);
		groupOrderProductVO2.setDeliveryState(OrderProductDeliveryEnum.WAIT_DELIVERY);
		groupOrderProductVO2.setGroupBuyingTag(0);
		groupOrderProductVO2.setIsGift(0);
		groupOrderVO1.setOrderProductVOs(Arrays.asList(groupOrderProductVO2));
		final List<GroupOrderVO> groupOrderVOS = Arrays.asList(groupOrderVO1);
		final OrderGroupBuyingBindQuery query = new OrderGroupBuyingBindQuery();
		query.setGroupBuyingCode("groupBuyingCode");
		query.setGiftOrderSn("giftOrderSn");
		query.setGroupOrderSn("groupOrderSn");
		query.setOrderProductId(0L);
		query.setOrderSn("orderSn");
		when(mockGroupBuyingBindMapper.listBindGroupOrder(any())).thenReturn(groupOrderVOS);

		// Configure OrderGroupBuyingRecordMapper.listGroupOrder(...).
		final GroupOrderQuery query1 = new GroupOrderQuery();
		query1.setCurrent(0);
		query1.setPageSize(0);
		query1.setStoreId(0L);
		query1.setStoreCategoryId("storeCategoryId");
		query1.setBranchCode("branchCode");
		query1.setOrderSn("giftOrderSn");
		query1.setGroupOrderProductIdList(Arrays.asList(0L));
		when(mockGroupBuyingRecordMapper.listGroupOrder(any())).thenReturn(Collections.emptyList());

		// Configure IOrderExtendService.getOrderExtendByOrderSn(...).
		final OrderExtendPO orderExtendPO = new OrderExtendPO();
		orderExtendPO.setExtendId(0);
		orderExtendPO.setBranch("branch");
		orderExtendPO.setBranchName("branchName");
		orderExtendPO.setAreaCode("areaCode");
		orderExtendPO.setAreaName("areaName");
		when(mockOrderExtendService.getOrderExtendByOrderSn("giftOrderSn")).thenReturn(orderExtendPO);

		// Configure IOrderExtendService.buildReceiveInfo(...).
		final OrderAddressDTO orderAddressDTO = new OrderAddressDTO();
		orderAddressDTO.setReceiverName("receiverName");
		orderAddressDTO.setReceiverMobile("receiverMobile");
		orderAddressDTO.setProvince("province");
		orderAddressDTO.setCity("city");
		orderAddressDTO.setCityCode("cityCode");
		final OrderExtendPO orderExtendPO1 = new OrderExtendPO();
		orderExtendPO1.setExtendId(0);
		orderExtendPO1.setBranch("branch");
		orderExtendPO1.setBranchName("branchName");
		orderExtendPO1.setAreaCode("areaCode");
		orderExtendPO1.setAreaName("areaName");
		when(mockOrderExtendService.buildReceiveInfo(orderExtendPO)).thenReturn(orderAddressDTO);

		// Run the test
		final GroupOrderRecordDetailVO result = orderGroupBuyingRecordServiceImplUnderTest.orderGroupBuyingDetail("groupBuyingCode");

		// Verify the results
		Assert.assertNull(result.getGiftOrderInfo());
	}

	@Test
	public void testExport() {
		// Setup
		final MockHttpServletRequest request = new MockHttpServletRequest();
		final MockHttpServletResponse response = new MockHttpServletResponse();
		final GroupOrderRecordQuery query = new GroupOrderRecordQuery();
		query.setCurrent(0);
		query.setPageSize(0);
		query.setGroupBuyingCode("groupBuyingCode");
		query.setGiftOrderSn("giftOrderSn");
		query.setStoreId(0L);

		// Configure OrderGroupBuyingRecordMapper.exportRecord(...).
		final GroupOrderExportDTO groupOrderExportDTO = new GroupOrderExportDTO();
		final OrderDeliveryPackageDTO orderDeliveryPackageDTO = new OrderDeliveryPackageDTO();
		orderDeliveryPackageDTO.setDeliverType(0);
		orderDeliveryPackageDTO.setExpressName("expressName");
		orderDeliveryPackageDTO.setExpressCompanyCode("expressCompanyCode");
		orderDeliveryPackageDTO.setExpressNumber("expressNumber");
		orderDeliveryPackageDTO.setDeliverName("deliverName");
		orderDeliveryPackageDTO.setDeliverMobile("deliverMobile");
		orderDeliveryPackageDTO.setDeliverWarehouseName("deliverWarehouseName");
		orderDeliveryPackageDTO.setCreateTime("createTime");
		groupOrderExportDTO.setOrderDeliveryPackageDTOList(Arrays.asList(orderDeliveryPackageDTO));
		groupOrderExportDTO.setDeliverType("deliverType");
		groupOrderExportDTO.setProductDeliverTime("productDeliverTime");
		groupOrderExportDTO.setExpressCode("expressCode");
		groupOrderExportDTO.setExpressName("expressName");
		groupOrderExportDTO.setExpressNumber("expressNumber");
		groupOrderExportDTO.setDeliverName("deliverName");
		groupOrderExportDTO.setDeliverMobile("deliverMobile");
		groupOrderExportDTO.setDeliverWarehouseName("deliverWarehouseName");
		final List<GroupOrderExportDTO> groupOrderExportDTOS = Arrays.asList(groupOrderExportDTO);
		final GroupOrderRecordQuery query1 = new GroupOrderRecordQuery();
		query1.setCurrent(0);
		query1.setPageSize(0);
		query1.setGroupBuyingCode("groupBuyingCode");
		query1.setGiftOrderSn("giftOrderSn");
		query1.setStoreId(0L);
		when(mockGroupBuyingRecordMapper.exportRecord(query1)).thenReturn(groupOrderExportDTOS);

		// Run the test
		orderGroupBuyingRecordServiceImplUnderTest.export(request, response, query);

		// Verify the results
	}

	@Test
	public void testExport_OrderGroupBuyingRecordMapperReturnsNoItems() {
		// Setup
		final MockHttpServletRequest request = new MockHttpServletRequest();
		final MockHttpServletResponse response = new MockHttpServletResponse();
		final GroupOrderRecordQuery query = new GroupOrderRecordQuery();
		query.setCurrent(0);
		query.setPageSize(0);
		query.setGroupBuyingCode("groupBuyingCode");
		query.setGiftOrderSn("giftOrderSn");
		query.setStoreId(0L);

		// Configure OrderGroupBuyingRecordMapper.exportRecord(...).
		final GroupOrderRecordQuery query1 = new GroupOrderRecordQuery();
		query1.setCurrent(0);
		query1.setPageSize(0);
		query1.setGroupBuyingCode("groupBuyingCode");
		query1.setGiftOrderSn("giftOrderSn");
		query1.setStoreId(0L);
		when(mockGroupBuyingRecordMapper.exportRecord(query1)).thenReturn(Collections.emptyList());

		// Run the test
		orderGroupBuyingRecordServiceImplUnderTest.export(request, response, query);

		// Verify the results
	}

	@Test
	public void testGetRecordByGroupBuyingCode() {
		// Setup
		final OrderGroupBuyingRecordPO expectedResult = new OrderGroupBuyingRecordPO();
		expectedResult.setGroupBuyingCode("groupBuyingCode");
		expectedResult.setGiftOrderSn("giftOrderSn");
		expectedResult.setGroupOrderNum(0);
		expectedResult.setGroupGoodsNum(0);
		expectedResult.setGiftGoodsNum(0);
		expectedResult.setStoreId(0L);
		expectedResult.setStoreName("storeName");
		expectedResult.setStoreCategoryId(0);
		expectedResult.setStoreCategoryName("innerLabelName");
		expectedResult.setBranchCode("branch");
		expectedResult.setBranchName("branchName");
		expectedResult.setAreaCode("areaCode");
		expectedResult.setAreaName("areaName");
		expectedResult.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
		expectedResult.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
		expectedResult.setCreateBy("createBy");
		expectedResult.setUpdateBy("createBy");
		expectedResult.setEnabledFlag(0);

		// Configure OrderGroupBuyingRecordMapper.selectOne(...).
		final OrderGroupBuyingRecordPO orderGroupBuyingRecordPO = new OrderGroupBuyingRecordPO();
		orderGroupBuyingRecordPO.setGroupBuyingCode("groupBuyingCode");
		orderGroupBuyingRecordPO.setGiftOrderSn("giftOrderSn");
		orderGroupBuyingRecordPO.setGroupOrderNum(0);
		orderGroupBuyingRecordPO.setGroupGoodsNum(0);
		orderGroupBuyingRecordPO.setGiftGoodsNum(0);
		orderGroupBuyingRecordPO.setStoreId(0L);
		orderGroupBuyingRecordPO.setStoreName("storeName");
		orderGroupBuyingRecordPO.setStoreCategoryId(0);
		orderGroupBuyingRecordPO.setStoreCategoryName("innerLabelName");
		orderGroupBuyingRecordPO.setBranchCode("branch");
		orderGroupBuyingRecordPO.setBranchName("branchName");
		orderGroupBuyingRecordPO.setAreaCode("areaCode");
		orderGroupBuyingRecordPO.setAreaName("areaName");
		orderGroupBuyingRecordPO.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
		orderGroupBuyingRecordPO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
		orderGroupBuyingRecordPO.setCreateBy("createBy");
		orderGroupBuyingRecordPO.setUpdateBy("createBy");
		orderGroupBuyingRecordPO.setEnabledFlag(0);
		when(mockGroupBuyingRecordMapper.selectOne(any())).thenReturn(orderGroupBuyingRecordPO);

		// Run the test
		final OrderGroupBuyingRecordPO result = orderGroupBuyingRecordServiceImplUnderTest.getRecordByGroupBuyingCode("groupBuyingCode");

		// Verify the results
		Assert.assertNotNull(result);
	}

	@Test
	public void testGroupOrderList() {
		// Setup
		final GroupOrderQuery query = new GroupOrderQuery();
		query.setCurrent(1);
		query.setPageSize(10);
		query.setStoreId(0L);
		query.setStoreCategoryId("storeCategoryId");
		query.setBranchCode("branchCode");
		query.setOrderSn("giftOrderSn");
		query.setGroupOrderProductIdList(Arrays.asList(0L));

		final GroupOrderVO groupOrderVO = new GroupOrderVO();
		groupOrderVO.setOrderSn("orderSn");
		final GroupOrderProductVO groupOrderProductVO = new GroupOrderProductVO();
		groupOrderProductVO.setOrderSn("orderSn");
		groupOrderProductVO.setGoodsName("goodsName");
		groupOrderProductVO.setValidNum(0);
		groupOrderProductVO.setDeliveryState(OrderProductDeliveryEnum.WAIT_DELIVERY);
		groupOrderProductVO.setGroupBuyingTag(0);
		groupOrderProductVO.setIsGift(0);
		groupOrderVO.setOrderProductVOs(Arrays.asList(groupOrderProductVO));
		final PageVO<GroupOrderVO> expectedResult = new PageVO<>(Collections.singletonList(groupOrderVO), new PagerInfo(1, 10));
		final Page<GroupOrderVO> pageVO = new Page<>(1, 10);
		pageVO.setRecords(Collections.singletonList(groupOrderVO));

		// Configure OrderGroupBuyingRecordMapper.listWaitingGroupOrderPage(...).
		final GroupOrderQuery query1 = new GroupOrderQuery();
		query1.setCurrent(1);
		query1.setPageSize(10);
		query1.setStoreId(0L);
		query1.setStoreCategoryId("storeCategoryId");
		query1.setBranchCode("branchCode");
		query1.setOrderSn("giftOrderSn");
		query1.setGroupOrderProductIdList(Arrays.asList(0L));
		when(mockGroupBuyingRecordMapper.listWaitingGroupOrderPage(any(Page.class), any())).thenReturn(pageVO);

		// Configure OrderGroupBuyingRecordMapper.listWaitingGroupOrderProduct(...).
		final GroupOrderProductVO groupOrderProductVO1 = new GroupOrderProductVO();
		groupOrderProductVO1.setOrderSn("orderSn");
		groupOrderProductVO1.setGoodsName("goodsName");
		groupOrderProductVO1.setValidNum(0);
		groupOrderProductVO1.setDeliveryState(OrderProductDeliveryEnum.WAIT_DELIVERY);
		groupOrderProductVO1.setGroupBuyingTag(0);
		groupOrderProductVO1.setIsGift(0);
		final List<GroupOrderProductVO> groupOrderProductVOS = Arrays.asList(groupOrderProductVO1);
		when(mockGroupBuyingRecordMapper.listWaitingGroupOrderProduct("orderSn", "storeCategoryId")).thenReturn(groupOrderProductVOS);

		// Run the test
		final PageVO<GroupOrderVO> result = orderGroupBuyingRecordServiceImplUnderTest.groupOrderList(query);

		// Verify the results
		Assert.assertNotNull(result.getList());
	}

	@Test
	public void testGroupOrderList_ReturnsNull() {
		// Setup
		final GroupOrderQuery query = new GroupOrderQuery();
		query.setCurrent(1);
		query.setPageSize(10);
		query.setStoreId(0L);
		query.setStoreCategoryId("storeCategoryId");
		query.setBranchCode("branchCode");
		query.setOrderSn("giftOrderSn");
		query.setGroupOrderProductIdList(Arrays.asList(0L));

		final GroupOrderVO groupOrderVO = new GroupOrderVO();
		groupOrderVO.setOrderSn("orderSn");
		GroupOrderProductVO groupOrderProductVO = new GroupOrderProductVO();
		groupOrderProductVO.setOrderSn("orderSn");
		groupOrderProductVO.setGoodsName("goodsName");
		groupOrderProductVO.setValidNum(0);
		groupOrderProductVO.setDeliveryState(OrderProductDeliveryEnum.WAIT_DELIVERY);
		groupOrderProductVO.setGroupBuyingTag(0);
		groupOrderProductVO.setIsGift(0);
		groupOrderVO.setOrderProductVOs(Arrays.asList(groupOrderProductVO));
		final PageVO<GroupOrderVO> expectedResult = new PageVO<>(Arrays.asList(groupOrderVO), new PagerInfo(1, 10));
		final Page<GroupOrderVO> pageVO = new Page<>(1, 10);
		pageVO.setRecords(Collections.singletonList(groupOrderVO));

		// Configure OrderGroupBuyingRecordMapper.listWaitingGroupOrderPage(...).
		final GroupOrderQuery query1 = new GroupOrderQuery();
		query1.setCurrent(0);
		query1.setPageSize(0);
		query1.setStoreId(0L);
		query1.setStoreCategoryId("storeCategoryId");
		query1.setBranchCode("branchCode");
		query1.setOrderSn("giftOrderSn");
		query1.setGroupOrderProductIdList(Arrays.asList(0L));
		when(mockGroupBuyingRecordMapper.listWaitingGroupOrderPage(any(Page.class), eq(query1))).thenReturn(null);

		// Run the test
		final PageVO<GroupOrderVO> result = orderGroupBuyingRecordServiceImplUnderTest.groupOrderList(query);

		// Verify the results
		Assert.assertNull(result.getList());
	}

	@Test
	public void testGroupOrderList_OrderGroupBuyingRecordMapperListWaitingGroupOrderProductReturnsNoItems() {
		// Setup
		final GroupOrderQuery query = new GroupOrderQuery();
		query.setCurrent(1);
		query.setPageSize(10);
		query.setStoreId(0L);
		query.setStoreCategoryId("storeCategoryId");
		query.setBranchCode("branchCode");
		query.setOrderSn("giftOrderSn");
		query.setGroupOrderProductIdList(Arrays.asList(0L));

		final GroupOrderVO groupOrderVO = new GroupOrderVO();
		groupOrderVO.setOrderSn("orderSn");
		final GroupOrderProductVO groupOrderProductVO = new GroupOrderProductVO();
		groupOrderProductVO.setOrderSn("orderSn");
		groupOrderProductVO.setGoodsName("goodsName");
		groupOrderProductVO.setValidNum(0);
		groupOrderProductVO.setDeliveryState(OrderProductDeliveryEnum.WAIT_DELIVERY);
		groupOrderProductVO.setGroupBuyingTag(0);
		groupOrderProductVO.setIsGift(0);
		groupOrderVO.setOrderProductVOs(Arrays.asList(groupOrderProductVO));
		final PageVO<GroupOrderVO> expectedResult = new PageVO<>(Arrays.asList(groupOrderVO), new PagerInfo(1, 10));
		final Page<GroupOrderVO> pageVO = new Page<>(1, 10);
		pageVO.setRecords(Collections.singletonList(groupOrderVO));

		// Configure OrderGroupBuyingRecordMapper.listWaitingGroupOrderPage(...).
		final GroupOrderQuery query1 = new GroupOrderQuery();
		query1.setCurrent(10);
		query1.setPageSize(10);
		query1.setStoreId(0L);
		query1.setStoreCategoryId("storeCategoryId");
		query1.setBranchCode("branchCode");
		query1.setOrderSn("giftOrderSn");
		query1.setGroupOrderProductIdList(Arrays.asList(0L));
		when(mockGroupBuyingRecordMapper.listWaitingGroupOrderPage(any(), any())).thenReturn(pageVO);

		when(mockGroupBuyingRecordMapper.listWaitingGroupOrderProduct("orderSn", "storeCategoryId")).thenReturn(Collections.emptyList());

		// Run the test
		final PageVO<GroupOrderVO> result = orderGroupBuyingRecordServiceImplUnderTest.groupOrderList(query);

		// Verify the results
		Assert.assertTrue(CollectionUtils.isEmpty(result.getList().get(0).getOrderProductVOs()));
	}

	@Test
	public void testGroupOrderStatistic() {
		// Setup
		final GroupOrderQuery query = new GroupOrderQuery();
		query.setCurrent(1);
		query.setPageSize(10);
		query.setStoreId(0L);
		query.setStoreCategoryId("11");
		query.setBranchCode("branchCode");
		query.setOrderSn("giftOrderSn");
		query.setGroupOrderProductIdList(Arrays.asList(0L));

		final GroupOrderStatisticVO groupOrderStatisticVO = new GroupOrderStatisticVO();
		groupOrderStatisticVO.setStoreId(0L);
		groupOrderStatisticVO.setStoreName("storeName");
		groupOrderStatisticVO.setStoreCategoryId(0);
		groupOrderStatisticVO.setStoreCategoryName("innerLabelName");
		final GroupOrderStatisticVO.BranchStatisticVO branchStatisticVO = new GroupOrderStatisticVO.BranchStatisticVO();
		branchStatisticVO.setBranchName("branchName");
		branchStatisticVO.setBranchCode("branchCode");
		branchStatisticVO.setTotalNum(0);
		branchStatisticVO.setReturnNumber(0);
		branchStatisticVO.setValidNum(0);
		groupOrderStatisticVO.setBranchStatisticVOs(Arrays.asList(branchStatisticVO));
		final List<GroupOrderStatisticVO> expectedResult = Arrays.asList(groupOrderStatisticVO);

		// Configure OrderGroupBuyingRecordMapper.statisticGroupOrderNumber(...).
		final GroupOrderStatisticDTO groupOrderStatisticDTO = new GroupOrderStatisticDTO();
		groupOrderStatisticDTO.setStoreId(0L);
		groupOrderStatisticDTO.setStoreName("storeName");
		groupOrderStatisticDTO.setStoreCategoryId("11");
		groupOrderStatisticDTO.setBranchName("branchName");
		groupOrderStatisticDTO.setBranchCode("branchCode");
		groupOrderStatisticDTO.setTotalNum(0);
		groupOrderStatisticDTO.setReturnNumber(0);
		groupOrderStatisticDTO.setValidNum(0);
		final List<GroupOrderStatisticDTO> groupOrderStatisticDTOS = Arrays.asList(groupOrderStatisticDTO);
		final GroupOrderQuery query1 = new GroupOrderQuery();
		query1.setCurrent(0);
		query1.setPageSize(0);
		query1.setStoreId(0L);
		query1.setStoreCategoryId("11");
		query1.setBranchCode("branchCode");
		query1.setOrderSn("giftOrderSn");
		query1.setGroupOrderProductIdList(Arrays.asList(0L));
		when(mockGroupBuyingRecordMapper.statisticGroupOrderNumber(any())).thenReturn(groupOrderStatisticDTOS);

		// Configure StoreLabelBindGoodsFeignClient.getStoreLabelBindGoodsList(...).
		final StoreLabelBindGoods storeLabelBindGoods1 = new StoreLabelBindGoods();
		storeLabelBindGoods1.setBindId(0);
		storeLabelBindGoods1.setInnerLabelId(0);
		storeLabelBindGoods1.setInnerLabelName("innerLabelName");
		storeLabelBindGoods1.setGoodsId(0L);
		storeLabelBindGoods1.setStoreId(0L);
		final List<StoreLabelBindGoods> storeLabelBindGoods = Arrays.asList(storeLabelBindGoods1);
		final StoreLabelBindGoodsExample storeLabelBindGoodsExample = new StoreLabelBindGoodsExample();
		storeLabelBindGoodsExample.setBindIdNotEquals(0);
		storeLabelBindGoodsExample.setBindIdIn("bindIdIn");
		storeLabelBindGoodsExample.setBindId(0);
		storeLabelBindGoodsExample.setInnerLabelId(0);
		storeLabelBindGoodsExample.setGoodsId(0L);
		when(mockStoreLabelBindGoodsFeignClient.getStoreLabelBindGoodsList(any())).thenReturn(storeLabelBindGoods);

		// Run the test
		final List<GroupOrderStatisticVO> result = orderGroupBuyingRecordServiceImplUnderTest.groupOrderStatistic(query);

		// Verify the results
		Assert.assertNotNull(result);
	}

	@Test
	public void testGroupOrderStatistic_OrderGroupBuyingRecordMapperReturnsNoItems() {
		// Setup
		final GroupOrderQuery query = new GroupOrderQuery();
		query.setCurrent(1);
		query.setPageSize(10);
		query.setStoreId(0L);
		query.setStoreCategoryId("11");
		query.setBranchCode("branchCode");
		query.setOrderSn("giftOrderSn");
		query.setGroupOrderProductIdList(Arrays.asList(0L));

		// Configure OrderGroupBuyingRecordMapper.statisticGroupOrderNumber(...).
		final GroupOrderQuery query1 = new GroupOrderQuery();
		query1.setCurrent(0);
		query1.setPageSize(0);
		query1.setStoreId(0L);
		query1.setStoreCategoryId("11");
		query1.setBranchCode("branchCode");
		query1.setOrderSn("giftOrderSn");
		query1.setGroupOrderProductIdList(Arrays.asList(0L));
		when(mockGroupBuyingRecordMapper.statisticGroupOrderNumber(any())).thenReturn(Collections.emptyList());

		// Run the test
		final List<GroupOrderStatisticVO> result = orderGroupBuyingRecordServiceImplUnderTest.groupOrderStatistic(query);

		// Verify the results
		assertThat(result).isEqualTo(Collections.emptyList());
	}

	@Test
	public void testGroupOrderStatistic_StoreLabelBindGoodsFeignClientReturnsNoItems() {
		// Setup
		final GroupOrderQuery query = new GroupOrderQuery();
		query.setCurrent(10);
		query.setPageSize(10);
		query.setStoreId(0L);
		query.setStoreCategoryId("11");
		query.setBranchCode("branchCode");
		query.setOrderSn("giftOrderSn");
		query.setGroupOrderProductIdList(Arrays.asList(0L));

		// Configure OrderGroupBuyingRecordMapper.statisticGroupOrderNumber(...).
		final GroupOrderStatisticDTO groupOrderStatisticDTO = new GroupOrderStatisticDTO();
		groupOrderStatisticDTO.setStoreId(0L);
		groupOrderStatisticDTO.setStoreName("storeName");
		groupOrderStatisticDTO.setStoreCategoryId("11");
		groupOrderStatisticDTO.setBranchName("branchName");
		groupOrderStatisticDTO.setBranchCode("branchCode");
		groupOrderStatisticDTO.setTotalNum(0);
		groupOrderStatisticDTO.setReturnNumber(0);
		groupOrderStatisticDTO.setValidNum(0);
		final List<GroupOrderStatisticDTO> groupOrderStatisticDTOS = Arrays.asList(groupOrderStatisticDTO);
		final GroupOrderQuery query1 = new GroupOrderQuery();
		query1.setCurrent(10);
		query1.setPageSize(10);
		query1.setStoreId(0L);
		query1.setStoreCategoryId("storeCategoryId");
		query1.setBranchCode("branchCode");
		query1.setOrderSn("giftOrderSn");
		query1.setGroupOrderProductIdList(Arrays.asList(0L));
		when(mockGroupBuyingRecordMapper.statisticGroupOrderNumber(any())).thenReturn(groupOrderStatisticDTOS);

		// Configure StoreLabelBindGoodsFeignClient.getStoreLabelBindGoodsList(...).
		final StoreLabelBindGoodsExample storeLabelBindGoodsExample = new StoreLabelBindGoodsExample();
		storeLabelBindGoodsExample.setBindIdNotEquals(0);
		storeLabelBindGoodsExample.setBindIdIn("bindIdIn");
		storeLabelBindGoodsExample.setBindId(0);
		storeLabelBindGoodsExample.setInnerLabelId(0);
		storeLabelBindGoodsExample.setGoodsId(0L);
		when(mockStoreLabelBindGoodsFeignClient.getStoreLabelBindGoodsList(any())).thenReturn(Collections.emptyList());

		// Run the test
		final List<GroupOrderStatisticVO> result = orderGroupBuyingRecordServiceImplUnderTest.groupOrderStatistic(query);

		// Verify the results
		Assert.assertTrue(StringUtils.isBlank(result.get(0).getStoreCategoryName()));
	}

	@Test
	public void testOrderReceiveInfo() {
		// Setup
		final OrderAddressDTO expectedResult = new OrderAddressDTO();
		expectedResult.setReceiverName("receiverName");
		expectedResult.setReceiverMobile("receiverMobile");
		expectedResult.setProvince("province");
		expectedResult.setCity("city");
		expectedResult.setCityCode("cityCode");

		// Configure IOrderExtendService.getOrderExtendByOrderSn(...).
		final OrderExtendPO orderExtendPO = new OrderExtendPO();
		orderExtendPO.setExtendId(0);
		orderExtendPO.setBranch("branch");
		orderExtendPO.setBranchName("branchName");
		orderExtendPO.setAreaCode("areaCode");
		orderExtendPO.setAreaName("areaName");
		when(mockOrderExtendService.getOrderExtendByOrderSn("orderSn")).thenReturn(orderExtendPO);

		// Configure IOrderExtendService.buildReceiveInfo(...).
		final OrderAddressDTO orderAddressDTO = new OrderAddressDTO();
		orderAddressDTO.setReceiverName("receiverName");
		orderAddressDTO.setReceiverMobile("receiverMobile");
		orderAddressDTO.setProvince("province");
		orderAddressDTO.setCity("city");
		orderAddressDTO.setCityCode("cityCode");
		final OrderExtendPO orderExtendPO1 = new OrderExtendPO();
		orderExtendPO1.setExtendId(0);
		orderExtendPO1.setBranch("branch");
		orderExtendPO1.setBranchName("branchName");
		orderExtendPO1.setAreaCode("areaCode");
		orderExtendPO1.setAreaName("areaName");
		when(mockOrderExtendService.buildReceiveInfo(orderExtendPO1)).thenReturn(orderAddressDTO);

		// Run the test
		final OrderAddressDTO result = orderGroupBuyingRecordServiceImplUnderTest.orderReceiveInfo("orderSn");

		// Verify the results
		Assert.assertNotNull(result);
	}

	@Test
	public void testCheckGroupOrder() {
		// Setup
		final GroupOrderProductSubmitDTO dto = new GroupOrderProductSubmitDTO();
		dto.setStoreId(0L);
		dto.setStoreCategoryId(0);
		dto.setBranchCode("branchCode");
		dto.setGroupOrderProductIdList(Arrays.asList(0L));
		dto.setStoreId(0L);
		dto.setStoreCategoryId(11);
		final GroupOrderProductVO groupOrderProductVO = new GroupOrderProductVO();
		groupOrderProductVO.setOrderSn("orderSn");
		groupOrderProductVO.setGoodsName("goodsName");
		groupOrderProductVO.setValidNum(0);
		groupOrderProductVO.setDeliveryState(OrderProductDeliveryEnum.WAIT_DELIVERY);
		groupOrderProductVO.setGroupBuyingTag(0);
		groupOrderProductVO.setIsGift(0);
		dto.setGroupOrderProductVOList(Arrays.asList(groupOrderProductVO));

		// Configure OrderGroupBuyingRecordMapper.listGroupOrderProduct(...).
		final GroupOrderProductVO groupOrderProductVO1 = new GroupOrderProductVO();
		groupOrderProductVO1.setOrderSn("orderSn");
		groupOrderProductVO1.setGoodsName("goodsName");
		groupOrderProductVO1.setValidNum(0);
		groupOrderProductVO1.setDeliveryState(OrderProductDeliveryEnum.WAIT_DELIVERY);
		groupOrderProductVO1.setGroupBuyingTag(0);
		groupOrderProductVO1.setIsGift(0);
		groupOrderProductVO1.setStoreCategoryId("11");
		groupOrderProductVO1.setBranchCode("branchCode");
		groupOrderProductVO1.setStoreId(0L);
		groupOrderProductVO1.setGroupBuyingTag(1);
		final List<GroupOrderProductVO> groupOrderProductVOS = Arrays.asList(groupOrderProductVO1);
		final GroupOrderQuery query = new GroupOrderQuery();
		query.setCurrent(0);
		query.setPageSize(0);
		query.setStoreId(0L);
		query.setStoreCategoryId("storeCategoryId");
		query.setBranchCode("branchCode");
		query.setOrderSn("giftOrderSn");
		query.setGroupOrderProductIdList(Arrays.asList(0L));
		when(mockGroupBuyingRecordMapper.listGroupOrderProduct(any())).thenReturn(groupOrderProductVOS);

		// Run the test
		final Boolean result = orderGroupBuyingRecordServiceImplUnderTest.checkGroupOrder(dto);

		// Verify the results
		assertThat(result).isTrue();
	}

	@Test(expected = BusinessException.class)
	public void testCheckGroupOrder_OrderGroupBuyingRecordMapperReturnsNoItems() {
		// Setup
		final GroupOrderProductSubmitDTO dto = new GroupOrderProductSubmitDTO();
		dto.setStoreId(0L);
		dto.setBranchCode("branchCode");
		dto.setGroupOrderProductIdList(Arrays.asList(0L));
		dto.setStoreId(0L);
		dto.setStoreCategoryId(11);
		final GroupOrderProductVO groupOrderProductVO = new GroupOrderProductVO();
		groupOrderProductVO.setOrderSn("orderSn");
		groupOrderProductVO.setGoodsName("goodsName");
		groupOrderProductVO.setValidNum(0);
		groupOrderProductVO.setDeliveryState(OrderProductDeliveryEnum.WAIT_DELIVERY);
		groupOrderProductVO.setGroupBuyingTag(0);
		groupOrderProductVO.setIsGift(0);
		dto.setGroupOrderProductVOList(Arrays.asList(groupOrderProductVO));

		// Configure OrderGroupBuyingRecordMapper.listGroupOrderProduct(...).
		final GroupOrderProductVO groupOrderProductVO1 = new GroupOrderProductVO();
		groupOrderProductVO1.setOrderSn("orderSn");
		groupOrderProductVO1.setGoodsName("goodsName");
		groupOrderProductVO1.setValidNum(0);
		groupOrderProductVO1.setDeliveryState(OrderProductDeliveryEnum.WAIT_DELIVERY);
		groupOrderProductVO1.setGroupBuyingTag(0);
		groupOrderProductVO1.setIsGift(0);
		groupOrderProductVO1.setStoreCategoryId("11");
		groupOrderProductVO1.setBranchCode("branchCode");
		groupOrderProductVO1.setStoreId(0L);
		groupOrderProductVO1.setGroupBuyingTag(1);
		when(mockGroupBuyingRecordMapper.listGroupOrderProduct(any())).thenReturn(Collections.emptyList());

		// Run the test
		orderGroupBuyingRecordServiceImplUnderTest.checkGroupOrder(dto);

	}

	@Test
	public void testBuildOrderGroupBuyingRecord() {
		// Setup
		final GroupOrderProductSubmitDTO dto = new GroupOrderProductSubmitDTO();
		dto.setStoreId(0L);
		dto.setStoreCategoryId(0);
		dto.setBranchCode("branchCode");
		final OrderSkuInfoDTO orderSkuInfoDTO = new OrderSkuInfoDTO();
		orderSkuInfoDTO.setNumber(0);
		dto.setSkuInfoList(Arrays.asList(orderSkuInfoDTO));
		dto.setGroupOrderProductIdList(Arrays.asList(0L));
		final GroupOrderProductVO groupOrderProductVO = new GroupOrderProductVO();
		groupOrderProductVO.setOrderSn("orderSn");
		groupOrderProductVO.setGoodsName("goodsName");
		groupOrderProductVO.setValidNum(0);
		groupOrderProductVO.setDeliveryState(OrderProductDeliveryEnum.WAIT_DELIVERY);
		groupOrderProductVO.setGroupBuyingTag(0);
		groupOrderProductVO.setIsGift(0);
		dto.setGroupOrderProductVOList(Arrays.asList(groupOrderProductVO));

		final OrderExtendPO orderExtendPO = new OrderExtendPO();
		orderExtendPO.setExtendId(0);
		orderExtendPO.setBranch("branch");
		orderExtendPO.setBranchName("branchName");
		orderExtendPO.setAreaCode("areaCode");
		orderExtendPO.setAreaName("areaName");

		final OrderPO giftOrder = new OrderPO();
		giftOrder.setOrderId(0);
		giftOrder.setOrderSn("giftOrderSn");
		giftOrder.setStoreId(0L);
		giftOrder.setStoreName("storeName");
		giftOrder.setCreateBy("createBy");

		final OrderGroupBuyingRecordPO expectedResult = new OrderGroupBuyingRecordPO();
		expectedResult.setGroupBuyingCode("groupBuyingCode");
		expectedResult.setGiftOrderSn("giftOrderSn");
		expectedResult.setGroupOrderNum(0);
		expectedResult.setGroupGoodsNum(0);
		expectedResult.setGiftGoodsNum(0);
		expectedResult.setStoreId(0L);
		expectedResult.setStoreName("storeName");
		expectedResult.setStoreCategoryId(0);
		expectedResult.setStoreCategoryName("innerLabelName");
		expectedResult.setBranchCode("branch");
		expectedResult.setBranchName("branchName");
		expectedResult.setAreaCode("areaCode");
		expectedResult.setAreaName("areaName");
		expectedResult.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
		expectedResult.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
		expectedResult.setCreateBy("createBy");
		expectedResult.setUpdateBy("createBy");
		expectedResult.setEnabledFlag(0);

		// Configure StoreLabelBindGoodsFeignClient.getStoreLabelBindGoodsList(...).
		final StoreLabelBindGoods storeLabelBindGoods1 = new StoreLabelBindGoods();
		storeLabelBindGoods1.setBindId(0);
		storeLabelBindGoods1.setInnerLabelId(0);
		storeLabelBindGoods1.setInnerLabelName("innerLabelName");
		storeLabelBindGoods1.setGoodsId(0L);
		storeLabelBindGoods1.setStoreId(0L);
		final List<StoreLabelBindGoods> storeLabelBindGoods = Arrays.asList(storeLabelBindGoods1);
		final StoreLabelBindGoodsExample storeLabelBindGoodsExample = new StoreLabelBindGoodsExample();
		storeLabelBindGoodsExample.setBindIdNotEquals(0);
		storeLabelBindGoodsExample.setBindIdIn("bindIdIn");
		storeLabelBindGoodsExample.setBindId(0);
		storeLabelBindGoodsExample.setInnerLabelId(0);
		storeLabelBindGoodsExample.setGoodsId(0L);
		when(mockStoreLabelBindGoodsFeignClient.getStoreLabelBindGoodsList(any())).thenReturn(storeLabelBindGoods);

		// Run the test
		final OrderGroupBuyingRecordPO result = orderGroupBuyingRecordServiceImplUnderTest.buildOrderGroupBuyingRecord(dto, orderExtendPO,
				giftOrder);

		// Verify the results
		Assert.assertNotNull(result);
	}

	@Test(expected = BusinessException.class)
	public void testBuildOrderGroupBuyingRecord_StoreLabelBindGoodsFeignClientReturnsNoItems() {
		// Setup
		final GroupOrderProductSubmitDTO dto = new GroupOrderProductSubmitDTO();
		dto.setStoreId(0L);
		dto.setStoreCategoryId(0);
		dto.setBranchCode("branchCode");
		final OrderSkuInfoDTO orderSkuInfoDTO = new OrderSkuInfoDTO();
		orderSkuInfoDTO.setNumber(0);
		dto.setSkuInfoList(Arrays.asList(orderSkuInfoDTO));
		dto.setGroupOrderProductIdList(Arrays.asList(0L));
		final GroupOrderProductVO groupOrderProductVO = new GroupOrderProductVO();
		groupOrderProductVO.setOrderSn("orderSn");
		groupOrderProductVO.setGoodsName("goodsName");
		groupOrderProductVO.setValidNum(0);
		groupOrderProductVO.setDeliveryState(OrderProductDeliveryEnum.WAIT_DELIVERY);
		groupOrderProductVO.setGroupBuyingTag(0);
		groupOrderProductVO.setIsGift(0);
		dto.setGroupOrderProductVOList(Arrays.asList(groupOrderProductVO));

		final OrderExtendPO orderExtendPO = new OrderExtendPO();
		orderExtendPO.setExtendId(0);
		orderExtendPO.setBranch("branch");
		orderExtendPO.setBranchName("branchName");
		orderExtendPO.setAreaCode("areaCode");
		orderExtendPO.setAreaName("areaName");

		final OrderPO giftOrder = new OrderPO();
		giftOrder.setOrderId(0);
		giftOrder.setOrderSn("giftOrderSn");
		giftOrder.setStoreId(0L);
		giftOrder.setStoreName("storeName");
		giftOrder.setCreateBy("createBy");

		final OrderGroupBuyingRecordPO expectedResult = new OrderGroupBuyingRecordPO();
		expectedResult.setGroupBuyingCode("groupBuyingCode");
		expectedResult.setGiftOrderSn("giftOrderSn");
		expectedResult.setGroupOrderNum(0);
		expectedResult.setGroupGoodsNum(0);
		expectedResult.setGiftGoodsNum(0);
		expectedResult.setStoreId(0L);
		expectedResult.setStoreName("storeName");
		expectedResult.setStoreCategoryId(0);
		expectedResult.setStoreCategoryName("innerLabelName");
		expectedResult.setBranchCode("branch");
		expectedResult.setBranchName("branchName");
		expectedResult.setAreaCode("areaCode");
		expectedResult.setAreaName("areaName");
		expectedResult.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
		expectedResult.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
		expectedResult.setCreateBy("createBy");
		expectedResult.setUpdateBy("createBy");
		expectedResult.setEnabledFlag(0);

		// Configure StoreLabelBindGoodsFeignClient.getStoreLabelBindGoodsList(...).
		final StoreLabelBindGoodsExample storeLabelBindGoodsExample = new StoreLabelBindGoodsExample();
		storeLabelBindGoodsExample.setBindIdNotEquals(0);
		storeLabelBindGoodsExample.setBindIdIn("bindIdIn");
		storeLabelBindGoodsExample.setBindId(0);
		storeLabelBindGoodsExample.setInnerLabelId(0);
		storeLabelBindGoodsExample.setGoodsId(0L);
		when(mockStoreLabelBindGoodsFeignClient.getStoreLabelBindGoodsList(any())).thenReturn(Collections.emptyList());

		// Run the test
		final OrderGroupBuyingRecordPO result = orderGroupBuyingRecordServiceImplUnderTest.buildOrderGroupBuyingRecord(dto, orderExtendPO,
				giftOrder);

		// Verify the results
		Assert.assertTrue(StringUtils.isBlank(result.getStoreCategoryName()));
	}
}
