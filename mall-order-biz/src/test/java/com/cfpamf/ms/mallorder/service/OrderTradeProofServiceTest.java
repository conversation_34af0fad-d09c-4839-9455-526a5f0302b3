package com.cfpamf.ms.mallorder.service;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.cfpamf.ms.mall.filecenter.constant.IScenesMaterialProofConstant.ProofTypeEnum;
import com.cfpamf.ms.mall.filecenter.constant.IScenesMaterialProofConstant.ProviderTypeEnum;
import com.cfpamf.ms.mall.filecenter.domain.dto.FileMaterialDTO;
import com.cfpamf.ms.mall.filecenter.domain.dto.FileScenesMaterialProofDTO;
import com.cfpamf.ms.mall.filecenter.domain.vo.FileScenesProofMaterialVO;
import com.cfpamf.ms.mall.filecenter.domain.vo.FileScenesProofVO;
import com.cfpamf.ms.mallgoods.facade.api.ProductFeignClient;
import com.cfpamf.ms.mallgoods.facade.vo.Product;
import com.cfpamf.ms.mallorder.common.enums.OrderMaterialTypeEnum;
import com.cfpamf.ms.mallorder.common.enums.OrderPatternEnum;
import com.cfpamf.ms.mallorder.common.enums.PayMethodEnum;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.OrderTradeProofQueryDTO;
import com.cfpamf.ms.mallorder.integration.filecenter.FileCenterIntegration;
import com.cfpamf.ms.mallorder.integration.filecenter.ProofSceneEnum;
import com.cfpamf.ms.mallorder.integration.system.TradeDocumentIntegration;
import com.cfpamf.ms.mallorder.mapper.OrderTradeProofMapper;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderProductPO;
import com.cfpamf.ms.mallorder.service.impl.OrderTradeProofServiceImpl;
import com.cfpamf.ms.mallorder.vo.OrderTradeProofVO;
import com.cfpamf.ms.mallsystem.vo.DocumentVo;
import com.cfpamf.ms.mallsystem.vo.TradeDocumentVo;
import com.slodon.bbc.core.exception.BusinessException;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;


@RunWith(SpringJUnit4ClassRunner.class)
public class OrderTradeProofServiceTest {

	@Mock
	private IOrderService orderService;

	@Mock
	private IOrderProductService orderProductService;

	@Mock
	private ProductFeignClient productFeignClient;

	@Mock
	private TradeDocumentIntegration tradeDocumentIntegration;

	@Mock
	private FileCenterIntegration fileCenterIntegration;

	@Mock
	private OrderTradeProofMapper orderTradeProofMapper;

	@InjectMocks
	private OrderTradeProofServiceImpl orderTradeProofService;

	@Before
	public void setUp() {
		MockitoAnnotations.initMocks(this);
		TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), OrderPO.class);
		TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), OrderProductPO.class);
	}

	@Test
	public void testQueryScenesMaterial_emptyProofNo_returnsEmptyList() {
		// Arrange
		String proofNo = "proofNo";
		String subProofNo = "subProofNo";
		String sceneNo = "sceneNo";
		Boolean showAgreement = true;

		// Act

		List<FileScenesProofVO> fileScenesProofVOS = new ArrayList<>();
		when(fileCenterIntegration.queryScenesMaterialProofV2(proofNo, subProofNo, sceneNo, null, null, null))
				.thenReturn(fileScenesProofVOS);

		List<FileScenesProofVO> result = orderTradeProofService.queryScenesMaterial(proofNo, subProofNo, sceneNo, null,showAgreement);

		assertTrue(result.isEmpty());
	}

	@Test
	public void testQueryScenesMaterial_showAgreementFalse_removesAgreements() {
		// Arrange
		String proofNo = "proofNo";
		String subProofNo = "subProofNo";
		String sceneNo = "sceneNo";
		Boolean showAgreement = false;

		FileScenesProofMaterialVO agreementMaterial1 = new FileScenesProofMaterialVO();
		agreementMaterial1.setMaterialType(OrderMaterialTypeEnum.AGREEMENT.getCode());

		FileScenesProofMaterialVO otherMaterial = new FileScenesProofMaterialVO();
		otherMaterial.setMaterialType("OTHER");

		List<FileScenesProofMaterialVO> materialList1 = new ArrayList<>();
		materialList1.add(agreementMaterial1);
		materialList1.add(otherMaterial);

		FileScenesProofVO fileScenesProof1 = new FileScenesProofVO();
		fileScenesProof1.setMaterialVOList(materialList1);

		List<FileScenesProofVO> fileScenesProofVOS = new ArrayList<>();
		fileScenesProofVOS.add(fileScenesProof1);

		when(fileCenterIntegration.queryScenesMaterialProofV2(proofNo, subProofNo, sceneNo, null, null, null))
				.thenReturn(fileScenesProofVOS);

		// Act
		List<FileScenesProofVO> result = orderTradeProofService.queryScenesMaterial(proofNo, subProofNo, sceneNo, null,showAgreement);

		// Assert
		assertFalse(result.isEmpty());
		verify(fileCenterIntegration).queryScenesMaterialProofV2(proofNo, subProofNo, sceneNo, null, null, null);
	}

	@Test
	public void listSceneMaterial_emptyOrderTradeProofVOS_shouldReturnNull() {
		// Setup
		List<OrderTradeProofVO> emptyList = Collections.emptyList();
		when(orderTradeProofMapper.queryOrderProofList(anyString(), anyList(), any(), any(),any())).thenReturn(emptyList);

		// Test
		List<OrderTradeProofVO> result = orderTradeProofService.listSceneMaterial("order123", 1L, null,ProofSceneEnum.SUBMIT);

		// Verify
		assertNull(result);
		verify(fileCenterIntegration, never()).queryScenesMaterialProofV2(anyString(), any(), any(), any(), any(), null);
	}

	@Test
	public void listSceneMaterial_emptyFileScenesProofVOS_shouldReturnOrderTradeProofVOS() {
		// Setup
		List<OrderTradeProofVO> orderTradeProofVOS = createOrderTradeProofVOS();
		when(orderTradeProofMapper.queryOrderProofList(anyString(), anyList(), any(), any(),any()))
				.thenReturn(orderTradeProofVOS);
		when(fileCenterIntegration.queryScenesMaterialProofV2(anyString(), any(), any(), any(), any(), null)).thenReturn(Collections.emptyList());

		// Test
		List<OrderTradeProofVO> result = orderTradeProofService.listSceneMaterial("order123", null, null,ProofSceneEnum.SUBMIT);

		// Verify
		assertNotNull(result);
		verify(fileCenterIntegration).queryScenesMaterialProofV2(anyString(), any(), any(), any(), any(), null);
	}

	@Test
	public void listSceneMaterial_shouldReturnModifiedOrderTradeProofVOS() {
		// Setup
		List<OrderTradeProofVO> orderTradeProofVOS = createOrderTradeProofVOS();
		List<FileScenesProofVO> fileScenesProofVOS = createFileScenesProofVOS();

		when(orderTradeProofMapper.queryOrderProofList(anyString(), anyList(), any(), any(),any()))
				.thenReturn(orderTradeProofVOS);
		when(fileCenterIntegration.queryScenesMaterialProofV2(anyString(), any(), any(), any(), any(), null))
				.thenReturn(fileScenesProofVOS);

		// Test
		List<OrderTradeProofVO> result = orderTradeProofService.listSceneMaterial("order123", 1L, null,ProofSceneEnum.SUBMIT);

		// Verify
		assertEquals(2, result.size());
		assertEquals("content1", result.get(0).getMaterialContentList().get(0));
		assertEquals("content2", result.get(1).getMaterialContentList().get(0));
		verify(fileCenterIntegration).queryScenesMaterialProofV2(anyString(), any(), anyString(), any(), any(), null);
	}

	private List<OrderTradeProofVO> createOrderTradeProofVOS() {
		OrderTradeProofVO proofVO1 = new OrderTradeProofVO();
		proofVO1.setOrderSn("orderSn");
		proofVO1.setMaterialNo("material1");
		proofVO1.setMaterialName("materialName1");
		proofVO1.setMaterialType("1,2");
		proofVO1.setMaterialRemark("111");
		proofVO1.setRequisite(0);
		proofVO1.setUniqueCheck(0);
		proofVO1.setMaxUploads(0);
		proofVO1.setSceneNo("submit");
		proofVO1.setIsUpload(false);
		OrderTradeProofVO proofVO2 = new OrderTradeProofVO();
		proofVO1.setOrderSn("orderSn");
		proofVO1.setMaterialNo("material2");
		proofVO1.setMaterialName("materialName2");
		proofVO1.setMaterialType("1,2");
		proofVO1.setMaterialRemark("111");
		proofVO1.setRequisite(0);
		proofVO1.setUniqueCheck(0);
		proofVO1.setMaxUploads(0);
		proofVO1.setSceneNo("submit");
		proofVO1.setIsUpload(false);

		return Arrays.asList(proofVO1, proofVO2);
	}

	private List<FileScenesProofVO> createFileScenesProofVOS() {
		FileScenesProofMaterialVO materialVO1 = new FileScenesProofMaterialVO();
		materialVO1.setMaterialNo("material1");
		materialVO1.setMaterialContentList(Arrays.asList("content1","content2"));
		FileScenesProofMaterialVO materialVO2 = new FileScenesProofMaterialVO();
		materialVO2.setMaterialNo("material2");
		materialVO2.setMaterialContentList(Arrays.asList("content2","content3"));

		FileScenesProofVO fileScenesProofVO = new FileScenesProofVO();
		fileScenesProofVO.setMaterialVOList(Arrays.asList(materialVO1, materialVO2));

		return Collections.singletonList(fileScenesProofVO);
	}

	@Test
	public void testMatchSceneMaterials_order_existMaterail() {
		// Prepare test data
		OrderTradeProofQueryDTO queryDTO = new OrderTradeProofQueryDTO();
		queryDTO.setOrderSn("orderSn");
		queryDTO.setSceneNo("submit");

		List<OrderTradeProofVO> orderTradeProofVOS = createOrderTradeProofVOS();
		List<FileScenesProofVO> fileScenesProofVOS = createFileScenesProofVOS();

		when(orderTradeProofMapper.queryOrderProofList(anyString(), anyList(), any(), any(),any()))
				.thenReturn(orderTradeProofVOS);
		when(fileCenterIntegration.queryScenesMaterialProofV2(anyString(), any(), any(), any(), any(), null))
				.thenReturn(fileScenesProofVOS);

		// Perform the test
		List<OrderTradeProofVO> result = orderTradeProofService.matchSceneMaterials(queryDTO);

		assertNotNull(result);
	}

	@Test
	public void testMatchSceneMaterials_order_notExistMaterail() {
		// Prepare test data
		OrderTradeProofQueryDTO queryDTO = new OrderTradeProofQueryDTO();
		queryDTO.setOrderSn("orderSn");
		queryDTO.setSceneNo("submit");

		List<OrderTradeProofVO> emptyList = Collections.emptyList();
		when(orderTradeProofMapper.queryOrderProofList(anyString(), anyList(), any(), any(),any())).thenReturn(emptyList);

		OrderPO orderPO = createOrderPO();
		when(orderService.getOne(any())).thenReturn(orderPO);

		// orderProductService.list()
		List<OrderProductPO> orderProductPOS = createOrderProductPOS();
		when(orderProductService.list(any())).thenReturn(orderProductPOS);

		List<Product> productList = createProducts();
		when(productFeignClient.getProductListByProductIds(anyList())).thenReturn(productList);

		// tradeDocumentIntegration.matchOrderSceneMaterials()
		TradeDocumentVo tradeDocumentVo = createTradeDocumentVo();
		when(tradeDocumentIntegration.matchOrderSceneMaterials(any())).thenReturn(tradeDocumentVo);

		// Perform the test
		List<OrderTradeProofVO> result = orderTradeProofService.matchSceneMaterials(queryDTO);

		assertNotNull(result);
	}

	private List<Product> createProducts() {
		List<Product> productList = new ArrayList<>();
		Product product = new Product();
		product.setProductId(120002L);
		product.setCategoryId1(111);
		product.setCategoryId2(222);
		product.setCategoryId3(333);
		productList.add(product);
		Product product1 = new Product();
		product1.setProductId(120003L);
		product1.setCategoryId1(111);
		product1.setCategoryId2(222);
		product1.setCategoryId3(333);
		productList.add(product1);
		return productList;
	}

	private TradeDocumentVo createTradeDocumentVo() {
		TradeDocumentVo tradeDocumentVo = new TradeDocumentVo();
		tradeDocumentVo.setOrderNode("submit");
		tradeDocumentVo.setBappDelivery("1");
		tradeDocumentVo.setBappReceive("1");
		List<DocumentVo> documentList = new ArrayList<>();
		DocumentVo documentVo = new DocumentVo();
		documentVo.setCode("materialNo");
		documentVo.setName("materialName");
		documentVo.setTypes("1,2");
		documentVo.setRequired("1");
		documentVo.setUniqueCheck("1");
		documentList.add(documentVo);
		tradeDocumentVo.setDocumentList(documentList);
		return tradeDocumentVo;
	}

	private List<OrderProductPO> createOrderProductPOS() {
		List<OrderProductPO> orderProductPOS = new ArrayList<>();
		OrderProductPO orderProductPO1 = new OrderProductPO();
		orderProductPO1.setOrderProductId(1L);
		orderProductPO1.setProductId(120002L);
		orderProductPOS.add(orderProductPO1);
		OrderProductPO orderProductPO2 = new OrderProductPO();
		orderProductPO2.setOrderProductId(2L);
		orderProductPO2.setProductId(120003L);
		orderProductPOS.add(orderProductPO2);
		return orderProductPOS;
	}

	private OrderPO createOrderPO() {
		OrderPO orderPO = new OrderPO();
		orderPO.setStoreId(20992L);
		orderPO.setOrderPattern(OrderPatternEnum.SELF_LIFT.getValue());
		orderPO.setOrderAmount(BigDecimal.ONE);
		orderPO.setPerformanceModes("[4]");
		orderPO.setPaymentCode(PayMethodEnum.ALIPAY.getValue());
		return orderPO;
	}

	@Test
	public void testMatchBappPrivilege_DeliveryScene_ValidOrder() {
		// Arrange
		String orderSn = "ORDER123";
		ProofSceneEnum sceneEnum = ProofSceneEnum.DELIVERY;
		OrderPO orderPO = createOrderPO();
		when(orderService.getOne(any())).thenReturn(orderPO);

		// orderProductService.list()
		List<OrderProductPO> orderProductPOS = createOrderProductPOS();
		when(orderProductService.list(any())).thenReturn(orderProductPOS);

		List<Product> productList = createProducts();
		when(productFeignClient.getProductListByProductIds(anyList())).thenReturn(productList);

		// tradeDocumentIntegration.matchOrderSceneMaterials()
		TradeDocumentVo tradeDocumentVo = createTradeDocumentVo();
		when(tradeDocumentIntegration.matchOrderSceneMaterials(any())).thenReturn(tradeDocumentVo);

		// Act
		boolean result = orderTradeProofService.matchBappPrivilege(orderSn, sceneEnum);

		// Assert
		assertTrue(result);
	}

	@Test
	public void testMatchBappPrivilege_DeliveryScene_InvalidOrder() {
		// Arrange
		String orderSn = "ORDER456";
		ProofSceneEnum sceneEnum = ProofSceneEnum.DELIVERY;
		OrderPO orderPO = createOrderPO();
		when(orderService.getOne(any())).thenReturn(orderPO);

		// orderProductService.list()
		List<OrderProductPO> orderProductPOS = createOrderProductPOS();
		when(orderProductService.list(any())).thenReturn(orderProductPOS);

		List<Product> productList = createProducts();
		when(productFeignClient.getProductListByProductIds(anyList())).thenReturn(productList);

		// tradeDocumentIntegration.matchOrderSceneMaterials()
		TradeDocumentVo tradeDocumentVo = createTradeDocumentVo();
		tradeDocumentVo.setBappDelivery("0");
		when(tradeDocumentIntegration.matchOrderSceneMaterials(any())).thenReturn(tradeDocumentVo);

		// Act
		boolean result = orderTradeProofService.matchBappPrivilege(orderSn, sceneEnum);

		// Assert
		assertFalse(result);
	}

	@Test
	public void testMatchBappPrivilege_ReceiveScene_ValidOrder() {
		// Arrange
		String orderSn = "ORDER123";
		ProofSceneEnum sceneEnum = ProofSceneEnum.RECEIVE;

		OrderPO orderPO = createOrderPO();
		when(orderService.getOne(any())).thenReturn(orderPO);

		// orderProductService.list()
		List<OrderProductPO> orderProductPOS = createOrderProductPOS();
		when(orderProductService.list(any())).thenReturn(orderProductPOS);

		List<Product> productList = createProducts();
		when(productFeignClient.getProductListByProductIds(anyList())).thenReturn(productList);

		// tradeDocumentIntegration.matchOrderSceneMaterials()
		TradeDocumentVo tradeDocumentVo = createTradeDocumentVo();
		when(tradeDocumentIntegration.matchOrderSceneMaterials(any())).thenReturn(tradeDocumentVo);

		// Act
		boolean result = orderTradeProofService.matchBappPrivilege(orderSn, sceneEnum);

		// Assert
		assertTrue(result);
	}

	@Test
	public void testMatchBappPrivilege_ReceiveScene_InvalidOrder() {
		// Arrange
		String orderSn = "ORDER456";
		ProofSceneEnum sceneEnum = ProofSceneEnum.RECEIVE;

		OrderPO orderPO = createOrderPO();
		when(orderService.getOne(any())).thenReturn(orderPO);

		// orderProductService.list()
		List<OrderProductPO> orderProductPOS = createOrderProductPOS();
		when(orderProductService.list(any())).thenReturn(orderProductPOS);

		List<Product> productList = createProducts();
		when(productFeignClient.getProductListByProductIds(anyList())).thenReturn(productList);

		// tradeDocumentIntegration.matchOrderSceneMaterials()
		TradeDocumentVo tradeDocumentVo = createTradeDocumentVo();
		tradeDocumentVo.setBappDelivery("0");
		tradeDocumentVo.setBappReceive("0");
		when(tradeDocumentIntegration.matchOrderSceneMaterials(any())).thenReturn(tradeDocumentVo);

		// Act
		boolean result = orderTradeProofService.matchBappPrivilege(orderSn, sceneEnum);

		// Assert
		assertFalse(result);
	}

	@Test
	public void testCheckOrderProofUpload_withEmptyList_returnsTrue() {
		List<OrderTradeProofVO> emptyList = new ArrayList<>();
		when(orderTradeProofMapper.queryOrderProofList(anyString(), anyList(), any(), isNull(),any()))
				.thenReturn(emptyList);

		Boolean result = orderTradeProofService.checkOrderProofUpload(
				"order123", new ArrayList<>(), ProofSceneEnum.RECEIVE);
		assertTrue(result);
	}

	@Test
	public void testCheckOrderProofUpload_withAllUpload_returnsTrue() {
		List<OrderTradeProofVO> orderTradeProofVOS = new ArrayList<>();
		OrderTradeProofVO proof1 = new OrderTradeProofVO();
		proof1.setRequisite(NumberUtils.INTEGER_ONE);
		proof1.setIsUpload(true);
		OrderTradeProofVO proof2 = new OrderTradeProofVO();
		proof2.setRequisite(NumberUtils.INTEGER_ONE);
		proof2.setIsUpload(true);
		orderTradeProofVOS.add(proof1);
		orderTradeProofVOS.add(proof2);

		when(orderTradeProofMapper.queryOrderProofList(anyString(), anyList(), any(), isNull(),any()))
				.thenReturn(orderTradeProofVOS);

		Boolean result = orderTradeProofService.checkOrderProofUpload(
				"order123", new ArrayList<>(), ProofSceneEnum.RECEIVE);
		assertTrue(result);
	}

	@Test
	public void testCheckOrderProofUpload_withUnuploadedProofs_returnsFalse() {
		List<OrderTradeProofVO> orderTradeProofVOS = new ArrayList<>();
		OrderTradeProofVO proof1 = new OrderTradeProofVO();
		proof1.setRequisite(NumberUtils.INTEGER_ONE);
		proof1.setIsUpload(false);
		OrderTradeProofVO proof2 = new OrderTradeProofVO();
		proof2.setRequisite(NumberUtils.INTEGER_ONE);
		proof2.setIsUpload(true);
		orderTradeProofVOS.add(proof1);
		orderTradeProofVOS.add(proof2);

		when(orderTradeProofMapper.queryOrderProofList(anyString(), anyList(), any(), isNull(),any()))
				.thenReturn(orderTradeProofVOS);

		Boolean result = orderTradeProofService.checkOrderProofUpload(
				"order123", new ArrayList<>(), ProofSceneEnum.RECEIVE);
		assertFalse(result);
	}

	@Test
	public void testSaveScenesMaterial_NoMaterial_ReturnsFalse() {
		// Setup
		FileScenesMaterialProofDTO paramDTO = new FileScenesMaterialProofDTO();

		// Run method
		Boolean result = orderTradeProofService.saveScenesMaterial(OrderConst.LOG_ROLE_MEMBER,"1","TEST",paramDTO);

		// Verify
		assertFalse(result);
	}

	@Test(expected = BusinessException.class)
	public void testSaveScenesMaterial_MatchingOrderNodeNull_ThrowsException() {
		// Setup
		FileScenesMaterialProofDTO paramDTO = new FileScenesMaterialProofDTO();
		paramDTO.setSceneNo("ddd");

		// Run method
		orderTradeProofService.saveScenesMaterial(OrderConst.LOG_ROLE_MEMBER,"1","TEST",paramDTO);
	}

	@Test
	public void testSaveScenesMaterial_MatchingOrderNodeNotNull_NoMatchingSceneMaterials_ReturnsFalse() {
		// Setup
		FileScenesMaterialProofDTO paramDTO = createFileScenesMaterialProofDTO();

		List<OrderTradeProofVO> orderTradeProofVOS = createOrderTradeProofVOS();
		List<FileScenesProofVO> fileScenesProofVOS = createFileScenesProofVOS();

		when(orderTradeProofMapper.queryOrderProofList(eq(paramDTO.getProofNo()), eq(null), eq(paramDTO.getSceneNo()), eq(null),eq(null)))
				.thenReturn(orderTradeProofVOS);
		when(fileCenterIntegration.queryScenesMaterialProofV2(anyString(), any(), any(), any(), any(), null))
				.thenReturn(fileScenesProofVOS);
		when(fileCenterIntegration.saveScenesMaterialProofV2(eq(paramDTO)))
				.thenReturn(any());

		// Run method
		Boolean result = orderTradeProofService.saveScenesMaterial(OrderConst.LOG_ROLE_MEMBER,"1","TEST",paramDTO);

		// Verify
		assertFalse(result);
	}

	private FileScenesMaterialProofDTO createFileScenesMaterialProofDTO() {
		FileScenesMaterialProofDTO paramDTO = new FileScenesMaterialProofDTO();
		List<FileMaterialDTO> materialDTOList = new ArrayList<>();
		FileMaterialDTO fileMaterialDTO = new FileMaterialDTO();
		materialDTOList.add(fileMaterialDTO);
		fileMaterialDTO.setMaterialNo("materialNo");
		fileMaterialDTO.setMaterialName("materialName");
		fileMaterialDTO.setMaterialType("1,2");
		fileMaterialDTO.setMaterialContentList(Arrays.asList("http://///","http://////"));
		fileMaterialDTO.setProofRemark("ddd");
		fileMaterialDTO.setProviderId("id");
		fileMaterialDTO.setProviderName("xwj");
		fileMaterialDTO.setProviderType(ProviderTypeEnum.PLAT_ADMIN);
		paramDTO.setMaterialDTOList(materialDTOList);
		paramDTO.setProofNo("orderSn");
		paramDTO.setProofType(ProofTypeEnum.EMPTY_TYPE);
		paramDTO.setSubProofNo("");
		paramDTO.setSubProofType(ProofTypeEnum.EMPTY_TYPE);
		paramDTO.setSceneNo("submit");
		return paramDTO;
	}

}

