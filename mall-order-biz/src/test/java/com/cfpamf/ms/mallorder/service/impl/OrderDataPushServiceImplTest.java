package com.cfpamf.ms.mallorder.service.impl;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.cfpamf.ms.mallgoods.facade.api.ProductFeignClient;
import com.cfpamf.ms.mallgoods.facade.vo.Product;
import com.cfpamf.ms.mallorder.common.mq.RabbitMQUtils;
import com.cfpamf.ms.mallorder.common.mq.msg.GoodNewsMessage;
import com.cfpamf.ms.mallorder.integration.erp.ERPIntegration;
import com.cfpamf.ms.mallorder.integration.facade.dto.UnitConvertVo;
import com.cfpamf.ms.mallorder.integration.system.TradeDocumentIntegration;
import com.cfpamf.ms.mallorder.mapper.OrderProductMapper;
import com.cfpamf.ms.mallorder.po.OrderExtendPO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderProductPO;
import com.cfpamf.ms.mallorder.service.ICommonMqEventService;
import com.cfpamf.ms.mallorder.service.IOrderExtendService;
import com.cfpamf.ms.mallorder.service.IOrderProductService;
import com.cfpamf.ms.mallorder.service.IOrderService;
import com.cfpamf.ms.mallsystem.request.OrderTradeMatchDTO;
import com.cfpamf.ms.mallsystem.vo.OrderDataPushHitObject;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(SpringJUnit4ClassRunner.class)
public class OrderDataPushServiceImplTest {

	@Mock
	private IOrderService mockOrderService;
	@Mock
	private IOrderExtendService mockOrderExtendService;
	@Mock
	private IOrderProductService mockOrderProductService;
	@Mock
	private ProductFeignClient mockProductFeignClient;
	@Mock
	private TradeDocumentIntegration mockTradeDocumentIntegration;
	@Mock
	private ERPIntegration mockErpIntegration;
	@Mock
	private ICommonMqEventService mockCommonMqEventService;
	@Mock
	private RabbitMQUtils mockRabbitMQUtils;

	@Mock
	private OrderProductMapper mockOrderProductMapper;

	@InjectMocks
	private OrderDataPushServiceImpl orderDataPushServiceImplUnderTest;

	@Before
	public void setUp() {
		MockitoAnnotations.initMocks(this);
		TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), OrderPO.class);
		TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), OrderExtendPO.class);
		TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), OrderProductPO.class);
	}

	@Test
	public void testPaySuccessDataPush() {
		// Setup
		// Configure IOrderService.getOne(...).
		final OrderPO orderPO = new OrderPO();
		orderPO.setOrderSn("orderSn");
		orderPO.setStoreId(0L);
		orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
		orderPO.setPaymentCode("paymentCode");
		orderPO.setOrderAmount(new BigDecimal("0.00"));
		orderPO.setOrderPattern(0);
		orderPO.setPerformanceModes("performanceModes");
		when(mockOrderService.getOne(any(LambdaQueryWrapper.class))).thenReturn(orderPO);

		// Configure IOrderExtendService.getOne(...).
		final OrderExtendPO orderExtendPO = new OrderExtendPO();
		orderExtendPO.setExtendId(0);
		orderExtendPO.setOrderSn("orderSn");
		orderExtendPO.setManager("manager");
		orderExtendPO.setManagerName("managerName");
		orderExtendPO.setBranch("branch");
		orderExtendPO.setBranchName("branchName");
		orderExtendPO.setAreaCode("areaCode");
		orderExtendPO.setAreaName("areaName");
		when(mockOrderExtendService.getOne(any(LambdaQueryWrapper.class))).thenReturn(orderExtendPO);

		// Configure IOrderProductService.list(...).
		final OrderProductPO orderProductPO = new OrderProductPO();
		orderProductPO.setOrderProductId(0L);
		orderProductPO.setOrderSn("orderSn");
		orderProductPO.setGoodsCategoryPath("goods->Category->Path");
		orderProductPO.setProductId(0L);
		orderProductPO.setProductNum(0);
		orderProductPO.setChannelSkuUnit("channelSkuUnit");
		orderProductPO.setChannelSkuId("channelSkuId");
		orderProductPO.setWeight(BigDecimal.valueOf(10L));
		final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
		when(mockOrderProductService.list(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOList);

		// Configure ProductFeignClient.getProductListByProductIds(...).
		final Product product = new Product();
		product.setId(0L);
		product.setProductId(0L);
		product.setCategoryId1(0);
		product.setCategoryId2(0);
		product.setCategoryId3(0);
		final List<Product> products = Arrays.asList(product);
		when(mockProductFeignClient.getProductListByProductIds(Arrays.asList(0L))).thenReturn(products);

		// Configure TradeDocumentIntegration.orderDataPushMatch(...).
		final OrderDataPushHitObject dataPushHitObject = new OrderDataPushHitObject();
		dataPushHitObject.setDesc("desc");
		dataPushHitObject.setCode("mall_pay_order");
		final List<OrderDataPushHitObject> hitObjects = Arrays.asList(dataPushHitObject);
		final OrderTradeMatchDTO orderTradeMatchDTO = new OrderTradeMatchDTO();
		orderTradeMatchDTO.setStoreId("storeId");
		orderTradeMatchDTO.setOrderPattern(0);
		orderTradeMatchDTO.setPerformanceModes("performanceModes");
		orderTradeMatchDTO.setOrderAmount(new BigDecimal("0.00"));
		orderTradeMatchDTO.setGoodsCategoryId1("categoryId1");
		orderTradeMatchDTO.setGoodsCategoryId2("categoryId2");
		orderTradeMatchDTO.setGoodsCategoryId3("categoryId3");
		orderTradeMatchDTO.setPaymentCode("paymentCode");
		when(mockTradeDocumentIntegration.orderDataPushMatch(any())).thenReturn(hitObjects);

		// Configure ERPIntegration.unitConvert(...).
		final UnitConvertVo unitConvertVo = new UnitConvertVo();
		unitConvertVo.setTagId("tagId");
		unitConvertVo.setBatchNo("batchNo");
		unitConvertVo.setSkuId("skuId");
		unitConvertVo.setPackageNum(new BigDecimal("10.00"));
		unitConvertVo.setUnitName("unitName");
		final List<UnitConvertVo> unitConvertVos = Arrays.asList(unitConvertVo);
		when(mockErpIntegration.unitConvert(Arrays.asList(new HashMap<>()))).thenReturn(unitConvertVos);

		// Configure ICommonMqEventService.saveEvent(...).
		final GoodNewsMessage body = new GoodNewsMessage();
		body.setReportType("code");
		body.setBranch("branch");
		body.setMarketingCode("2023MALL_PAY_ORDER");
		body.setReportLevel(0);
		body.setPushParam("pushParam");
		body.setStatisticalObject("manager");
		body.setStatisticalTime("orderSn");
		body.setParamAddStatisticalCount(false);
		body.setMessageId("messageId");
		when(mockCommonMqEventService.saveEvent(body, "agric.performance.goodNews.exchange")).thenReturn(0L);

		// Run the test
		orderDataPushServiceImplUnderTest.paySuccessDataPush("orderSn");

	}

	@Test
	public void testPaySuccessDataPush_IOrderProductServiceReturnsNoItems() {
		// Setup
		// Configure IOrderService.getOne(...).
		final OrderPO orderPO = new OrderPO();
		orderPO.setOrderSn("orderSn");
		orderPO.setStoreId(0L);
		orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
		orderPO.setPaymentCode("paymentCode");
		orderPO.setOrderAmount(new BigDecimal("0.00"));
		orderPO.setOrderPattern(0);
		orderPO.setPerformanceModes("performanceModes");
		when(mockOrderService.getOne(any(LambdaQueryWrapper.class))).thenReturn(orderPO);

		// Configure IOrderExtendService.getOne(...).
		final OrderExtendPO orderExtendPO = new OrderExtendPO();
		orderExtendPO.setExtendId(0);
		orderExtendPO.setOrderSn("orderSn");
		orderExtendPO.setManager("manager");
		orderExtendPO.setManagerName("managerName");
		orderExtendPO.setBranch("branch");
		orderExtendPO.setBranchName("branchName");
		orderExtendPO.setAreaCode("areaCode");
		orderExtendPO.setAreaName("areaName");
		when(mockOrderExtendService.getOne(any(LambdaQueryWrapper.class))).thenReturn(orderExtendPO);

		when(mockOrderProductService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

		// Configure ProductFeignClient.getProductListByProductIds(...).
		final Product product = new Product();
		product.setId(0L);
		product.setProductId(0L);
		product.setCategoryId1(0);
		product.setCategoryId2(0);
		product.setCategoryId3(0);
		final List<Product> products = Arrays.asList(product);
		when(mockProductFeignClient.getProductListByProductIds(Arrays.asList(0L))).thenReturn(products);

		// Run the test
		orderDataPushServiceImplUnderTest.paySuccessDataPush("orderSn");

		// Verify the results
	}

	@Test
	public void testPaySuccessDataPush_ProductFeignClientReturnsNoItems() {
		// Setup
		// Configure IOrderService.getOne(...).
		final OrderPO orderPO = new OrderPO();
		orderPO.setOrderSn("orderSn");
		orderPO.setStoreId(0L);
		orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
		orderPO.setPaymentCode("paymentCode");
		orderPO.setOrderAmount(new BigDecimal("0.00"));
		orderPO.setOrderPattern(0);
		orderPO.setPerformanceModes("performanceModes");
		when(mockOrderService.getOne(any(LambdaQueryWrapper.class))).thenReturn(orderPO);

		// Configure IOrderExtendService.getOne(...).
		final OrderExtendPO orderExtendPO = new OrderExtendPO();
		orderExtendPO.setExtendId(0);
		orderExtendPO.setOrderSn("orderSn");
		orderExtendPO.setManager("manager");
		orderExtendPO.setManagerName("managerName");
		orderExtendPO.setBranch("branch");
		orderExtendPO.setBranchName("branchName");
		orderExtendPO.setAreaCode("areaCode");
		orderExtendPO.setAreaName("areaName");
		when(mockOrderExtendService.getOne(any())).thenReturn(orderExtendPO);

		// Configure IOrderProductService.list(...).
		final OrderProductPO orderProductPO = new OrderProductPO();
		orderProductPO.setOrderProductId(0L);
		orderProductPO.setOrderSn("orderSn");
		orderProductPO.setGoodsCategoryPath("goods->Category->Path");
		orderProductPO.setProductId(0L);
		orderProductPO.setProductNum(0);
		orderProductPO.setChannelSkuUnit("channelSkuUnit");
		orderProductPO.setChannelSkuId("channelSkuId");
		final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
		when(mockOrderProductService.list(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOList);

		// Configure ProductFeignClient.getProductListByProductIds(...).
		final Product product = new Product();
		product.setId(0L);
		product.setProductId(0L);
		product.setCategoryId1(0);
		product.setCategoryId2(0);
		product.setCategoryId3(0);
		final List<Product> products = Arrays.asList(product);
		when(mockProductFeignClient.getProductListByProductIds(Arrays.asList(0L))).thenReturn(products);

		// Configure TradeDocumentIntegration.orderDataPushMatch(...).
		final OrderDataPushHitObject dataPushHitObject = new OrderDataPushHitObject();
		dataPushHitObject.setDesc("desc");
		dataPushHitObject.setCode("mall_air_energy_pay_order");
		final List<OrderDataPushHitObject> hitObjects = Arrays.asList(dataPushHitObject);
		final OrderTradeMatchDTO orderTradeMatchDTO = new OrderTradeMatchDTO();
		orderTradeMatchDTO.setStoreId("storeId");
		orderTradeMatchDTO.setOrderPattern(0);
		orderTradeMatchDTO.setPerformanceModes("performanceModes");
		orderTradeMatchDTO.setOrderAmount(new BigDecimal("0.00"));
		orderTradeMatchDTO.setGoodsCategoryId1("categoryId1");
		orderTradeMatchDTO.setGoodsCategoryId2("categoryId2");
		orderTradeMatchDTO.setGoodsCategoryId3("categoryId3");
		orderTradeMatchDTO.setPaymentCode("paymentCode");
		when(mockTradeDocumentIntegration.orderDataPushMatch(any())).thenReturn(hitObjects);

		// Configure ERPIntegration.unitConvert(...).
		final UnitConvertVo unitConvertVo = new UnitConvertVo();
		unitConvertVo.setTagId("tagId");
		unitConvertVo.setBatchNo("batchNo");
		unitConvertVo.setSkuId("skuId");
		unitConvertVo.setPackageNum(new BigDecimal("0.00"));
		unitConvertVo.setUnitName("unitName");
		final List<UnitConvertVo> unitConvertVos = Arrays.asList(unitConvertVo);
		when(mockErpIntegration.unitConvert(Arrays.asList(new HashMap<>()))).thenReturn(unitConvertVos);

		// Configure ICommonMqEventService.saveEvent(...).
		final GoodNewsMessage body = new GoodNewsMessage();
		body.setReportType("code");
		body.setBranch("branch");
		body.setMarketingCode("2023MALL_PAY_ORDER");
		body.setReportLevel(0);
		body.setPushParam("pushParam");
		body.setStatisticalObject("manager");
		body.setStatisticalTime("orderSn");
		body.setParamAddStatisticalCount(false);
		body.setMessageId("messageId");
		when(mockCommonMqEventService.saveEvent(body, "agric.performance.goodNews.exchange")).thenReturn(0L);

		// Run the test
		orderDataPushServiceImplUnderTest.paySuccessDataPush("orderSn");

	}

	@Test
	public void testPaySuccessDataPush_TradeDocumentIntegrationReturnsNoItems() {
		// Setup
		// Configure IOrderService.getOne(...).
		final OrderPO orderPO = new OrderPO();
		orderPO.setOrderSn("orderSn");
		orderPO.setStoreId(0L);
		orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
		orderPO.setPaymentCode("paymentCode");
		orderPO.setOrderAmount(new BigDecimal("0.00"));
		orderPO.setOrderPattern(0);
		orderPO.setPerformanceModes("performanceModes");
		when(mockOrderService.getOne(any(LambdaQueryWrapper.class))).thenReturn(orderPO);

		// Configure IOrderExtendService.getOne(...).
		final OrderExtendPO orderExtendPO = new OrderExtendPO();
		orderExtendPO.setExtendId(0);
		orderExtendPO.setOrderSn("orderSn");
		orderExtendPO.setManager("manager");
		orderExtendPO.setManagerName("managerName");
		orderExtendPO.setBranch("branch");
		orderExtendPO.setBranchName("branchName");
		orderExtendPO.setAreaCode("areaCode");
		orderExtendPO.setAreaName("areaName");
		when(mockOrderExtendService.getOne(any())).thenReturn(orderExtendPO);

		// Configure IOrderProductService.list(...).
		final OrderProductPO orderProductPO = new OrderProductPO();
		orderProductPO.setOrderProductId(0L);
		orderProductPO.setOrderSn("orderSn");
		orderProductPO.setGoodsCategoryPath("goods->Category->Path");
		orderProductPO.setProductId(0L);
		orderProductPO.setProductNum(0);
		orderProductPO.setChannelSkuUnit("channelSkuUnit");
		orderProductPO.setChannelSkuId("channelSkuId");
		final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
		when(mockOrderProductService.list(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOList);

		// Configure ProductFeignClient.getProductListByProductIds(...).
		final Product product = new Product();
		product.setId(0L);
		product.setProductId(0L);
		product.setCategoryId1(0);
		product.setCategoryId2(0);
		product.setCategoryId3(0);
		final List<Product> products = Collections.singletonList(product);
		when(mockProductFeignClient.getProductListByProductIds(Collections.singletonList(0L))).thenReturn(products);

		// Configure TradeDocumentIntegration.orderDataPushMatch(...).
		final OrderTradeMatchDTO orderTradeMatchDTO = new OrderTradeMatchDTO();
		orderTradeMatchDTO.setStoreId("storeId");
		orderTradeMatchDTO.setOrderPattern(0);
		orderTradeMatchDTO.setPerformanceModes("performanceModes");
		orderTradeMatchDTO.setOrderAmount(new BigDecimal("0.00"));
		orderTradeMatchDTO.setGoodsCategoryId1("categoryId1");
		orderTradeMatchDTO.setGoodsCategoryId2("categoryId2");
		orderTradeMatchDTO.setGoodsCategoryId3("categoryId3");
		orderTradeMatchDTO.setPaymentCode("paymentCode");
		when(mockTradeDocumentIntegration.orderDataPushMatch(any())).thenReturn(null);

		// Configure ERPIntegration.unitConvert(...).
		final UnitConvertVo unitConvertVo = new UnitConvertVo();
		unitConvertVo.setTagId("tagId");
		unitConvertVo.setBatchNo("batchNo");
		unitConvertVo.setSkuId("skuId");
		unitConvertVo.setPackageNum(new BigDecimal("0.00"));
		unitConvertVo.setUnitName("unitName");
		final List<UnitConvertVo> unitConvertVos = Arrays.asList(unitConvertVo);
		when(mockErpIntegration.unitConvert(Arrays.asList(new HashMap<>()))).thenReturn(unitConvertVos);

		// Configure ICommonMqEventService.saveEvent(...).
		final GoodNewsMessage body = new GoodNewsMessage();
		body.setReportType("mall_tian_jie_energy_pay_order");
		body.setBranch("branch");
		body.setMarketingCode("2023MALL_PAY_ORDER");
		body.setReportLevel(0);
		body.setPushParam("pushParam");
		body.setStatisticalObject("manager");
		body.setStatisticalTime("orderSn");
		body.setParamAddStatisticalCount(false);
		body.setMessageId("messageId");
		when(mockCommonMqEventService.saveEvent(body, "agric.performance.goodNews.exchange")).thenReturn(0L);

		// Run the test
		orderDataPushServiceImplUnderTest.paySuccessDataPush("orderSn");

	}

	@Test
	public void testPaySuccessDataPush_ERPIntegrationReturnsNoItems() {
		// Setup
		// Configure IOrderService.getOne(...).
		final OrderPO orderPO = new OrderPO();
		orderPO.setOrderSn("orderSn");
		orderPO.setStoreId(0L);
		orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
		orderPO.setPaymentCode("paymentCode");
		orderPO.setOrderAmount(new BigDecimal("0.00"));
		orderPO.setOrderPattern(0);
		orderPO.setPerformanceModes("performanceModes");
		when(mockOrderService.getOne(any(LambdaQueryWrapper.class))).thenReturn(orderPO);

		// Configure IOrderExtendService.getOne(...).
		final OrderExtendPO orderExtendPO = new OrderExtendPO();
		orderExtendPO.setExtendId(0);
		orderExtendPO.setOrderSn("orderSn");
		orderExtendPO.setManager("manager");
		orderExtendPO.setManagerName("managerName");
		orderExtendPO.setBranch("branch");
		orderExtendPO.setBranchName("branchName");
		orderExtendPO.setAreaCode("areaCode");
		orderExtendPO.setAreaName("areaName");
		when(mockOrderExtendService.getOne(any(LambdaQueryWrapper.class))).thenReturn(orderExtendPO);

		// Configure IOrderProductService.list(...).
		final OrderProductPO orderProductPO = new OrderProductPO();
		orderProductPO.setOrderProductId(0L);
		orderProductPO.setOrderSn("orderSn");
		orderProductPO.setGoodsCategoryPath("goods->Category->Path");
		orderProductPO.setProductId(0L);
		orderProductPO.setProductNum(0);
		orderProductPO.setChannelSkuUnit("channelSkuUnit");
		orderProductPO.setChannelSkuId("channelSkuId");
		final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
		when(mockOrderProductService.list(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOList);

		// Configure ProductFeignClient.getProductListByProductIds(...).
		final Product product = new Product();
		product.setId(0L);
		product.setProductId(0L);
		product.setCategoryId1(0);
		product.setCategoryId2(0);
		product.setCategoryId3(0);
		final List<Product> products = Arrays.asList(product);
		when(mockProductFeignClient.getProductListByProductIds(Arrays.asList(0L))).thenReturn(products);

		// Configure TradeDocumentIntegration.orderDataPushMatch(...).
		final OrderDataPushHitObject dataPushHitObject = new OrderDataPushHitObject();
		dataPushHitObject.setDesc("desc");
		dataPushHitObject.setCode("scenario_finance_pay_order");
		final List<OrderDataPushHitObject> hitObjects = Arrays.asList(dataPushHitObject);
		final OrderTradeMatchDTO orderTradeMatchDTO = new OrderTradeMatchDTO();
		orderTradeMatchDTO.setStoreId("storeId");
		orderTradeMatchDTO.setOrderPattern(0);
		orderTradeMatchDTO.setPerformanceModes("performanceModes");
		orderTradeMatchDTO.setOrderAmount(new BigDecimal("0.00"));
		orderTradeMatchDTO.setGoodsCategoryId1("categoryId1");
		orderTradeMatchDTO.setGoodsCategoryId2("categoryId2");
		orderTradeMatchDTO.setGoodsCategoryId3("categoryId3");
		orderTradeMatchDTO.setPaymentCode("paymentCode");
		when(mockTradeDocumentIntegration.orderDataPushMatch(any())).thenReturn(hitObjects);

		when(mockErpIntegration.unitConvert(Arrays.asList(new HashMap<>()))).thenReturn(Collections.emptyList());

		// Configure ICommonMqEventService.saveEvent(...).
		final GoodNewsMessage body = new GoodNewsMessage();
		body.setReportType("code");
		body.setBranch("branch");
		body.setMarketingCode("2023MALL_PAY_ORDER");
		body.setReportLevel(0);
		body.setPushParam("pushParam");
		body.setStatisticalObject("manager");
		body.setStatisticalTime("orderSn");
		body.setParamAddStatisticalCount(false);
		body.setMessageId("messageId");
		when(mockCommonMqEventService.saveEvent(body, "agric.performance.goodNews.exchange")).thenReturn(0L);

		// Run the test
		orderDataPushServiceImplUnderTest.paySuccessDataPush("orderSn");

	}
}
