package com.cfpamf.ms.mallorder.model;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.cfpamf.common.ms.result.CommonError;
import com.cfpamf.common.ms.result.ErrorContext;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.mallpayment.facade.vo.PaymentNotifyVO;
import com.cfpamf.ms.mall.account.api.StmAccountFacade;
import com.cfpamf.ms.mallmember.api.MemberBalanceLogFeignClient;
import com.cfpamf.ms.mallmember.api.MemberFeignClient;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.common.config.ChannelFeeRateConfig;
import com.cfpamf.ms.mallorder.common.enums.OrderCreateChannel;
import com.cfpamf.ms.mallorder.common.enums.PayMethodEnum;
import com.cfpamf.ms.mallorder.common.enums.TaskQueueBizTypeEnum;
import com.cfpamf.ms.mallorder.controller.fegin.facade.AccountCardFacade;
import com.cfpamf.ms.mallorder.controller.fegin.facade.CustomerServiceFeign;
import com.cfpamf.ms.mallorder.dto.*;
import com.cfpamf.ms.mallorder.enums.OrderEventEnum;
import com.cfpamf.ms.mallorder.integration.omsbase.OmsBaseIntegration;
import com.cfpamf.ms.mallorder.integration.settlement.BillOperatinIntegration;
import com.cfpamf.ms.mallorder.mapper.OrderMapper;
import com.cfpamf.ms.mallorder.mapper.OrderPayMapper;
import com.cfpamf.ms.mallorder.mapper.OrderProductMapper;
import com.cfpamf.ms.mallorder.mapper.OrderPromotionSendCouponMapper;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.req.PayWayChangeRequest;
import com.cfpamf.ms.mallorder.request.OrderExample;
import com.cfpamf.ms.mallorder.request.OrderPayExample;
import com.cfpamf.ms.mallorder.request.OrderPromotionSendCouponExample;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.service.impl.OrderCreateHelper;
import com.cfpamf.ms.mallorder.v2.domain.vo.PayInfoExtraInfoVO;
import com.cfpamf.ms.mallorder.v2.service.OrderPresellService;
import com.cfpamf.ms.mallorder.v2.service.PayService;
import com.cfpamf.ms.mallorder.v2.strategy.context.OrderPayProcessStrategyContext;
import com.cfpamf.ms.mallorder.vo.OrderPayInfoVO;
import com.cfpamf.ms.mallpromotion.api.CouponFeignClient;
import com.cfpamf.ms.mallpromotion.api.CouponMemberFeignClient;
import com.cfpamf.ms.mallpromotion.api.CouponUseLogFeignClient;
import com.cfpamf.ms.mallpromotion.api.PromotionCommonFeignClient;
import com.cfpamf.ms.mallpromotion.request.Coupon;
import com.cfpamf.ms.mallpromotion.request.CouponMember;
import com.cfpamf.ms.mallpromotion.request.CouponUseLog;
import com.cfpamf.ms.mallpromotion.vo.CouponVO;
import com.cfpamf.ms.mallshop.api.StoreBindCategoryFeignClient;
import com.cfpamf.ms.mallshop.api.StoreFeignClient;
import com.cfpamf.ms.mallshop.request.StoreBindCategoryExample;
import com.cfpamf.ms.mallshop.resp.StoreBindCategory;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.PagerInfo;
import com.slodon.bbc.starter.mq.entity.MessageSendProperty;
import com.slodon.bbc.starter.mq.entity.MessageSendVO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.amqp.AmqpException;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

import java.math.BigDecimal;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class OrderPayModel3Test {

    @Mock
    private ChannelFeeRateConfig mockChannelFeeRateConfig;
    @Mock
    private OrderPayMapper mockOrderPayMapper;
    @Mock
    private OrderMapper mockOrderMapper;
    @Mock
    private OrderPromotionSendCouponMapper mockOrderPromotionSendCouponMapper;
    @Mock
    private OrderProductMapper mockOrderProductMapper;
    @Mock
    private OrderLogModel mockOrderLogModel;
    @Mock
    private OrderProductModel mockOrderProductModel;
    @Mock
    private MemberFeignClient mockMemberFeignClient;
    @Mock
    private MemberBalanceLogFeignClient mockMemberBalanceLogFeignClient;
    @Mock
    private PromotionCommonFeignClient mockPromotionCommonFeignClient;
    @Mock
    private CouponFeignClient mockCouponFeignClient;
    @Mock
    private CouponMemberFeignClient mockCouponMemberFeignClient;
    @Mock
    private CouponUseLogFeignClient mockCouponUseLogFeignClient;
    @Mock
    private RabbitTemplate mockRabbitTemplate;
    @Mock
    private OrderModel mockOrderModel;
    @Mock
    private IOrderService mockOrderService;
    @Mock
    private OrderCreateHelper mockOrderCreateHelper;
    @Mock
    private IOrderProductService mockOrderProductService;
    @Mock
    private IOrderPayService mockOrderPayService;
    @Mock
    private IOrderReturnService mockOrderReturnService;
    @Mock
    private IBzBankTransferService mockBzBankTransferService;
    @Mock
    private IOrderExtendFinanceService mockFinanceService;
    @Mock
    private ITaskQueueService mockTaskQueueService;
    @Mock
    private StoreFeignClient mockStoreFeignClient;
    @Mock
    private AccountCardFacade mockAccountCardFacade;
    @Mock
    private CustomerServiceFeign mockCustomerServiceFeign;
    @Mock
    private ILoanResultService mockLoanResultService;
    @Mock
    private BillOperatinIntegration mockBillOperatinIntegration;
    @Mock
    private StmAccountFacade mockStmAccountFacade;
    @Mock
    private OrderPayProcessStrategyContext mockOrderPayProcessStrategyContext;
    @Mock
    private PayService mockPayService;
    @Mock
    private BankTransferModel mockBankTransferModel;
    @Mock
    private OrderOfflineService mockOrderOfflineService;
    @Mock
    private IOrderAmountStateRecordService mockOrderAmountRecordService;
    @Mock
    private OrderPresellService mockOrderPresellService;
    @Mock
    private OmsBaseIntegration mockOmsBaseIntegration;
    @Mock
    private IOrderAmountStateRecordService mockIOrderAmountStateRecordService;
    @Mock
    private IBzOldUserPoolService mockIBzOldUserPoolService;
    @Mock
    private StoreBindCategoryFeignClient mockStoreBindCategoryFeignClient;

    @InjectMocks
    private OrderPayModel orderPayModelUnderTest;
 /*  @Test
    void testWxAlipayCallBack_OrderPromotionSendCouponMapperReturnsNoItems() throws Exception {
        // Setup
        final PaymentNotifyVO req = new PaymentNotifyVO();
        req.setOrderOn("paySn");
        req.setPayCode("memberName");
        req.setPayTradeNo("memberName");
        req.setRelPayAmt(new BigDecimal("0.00"));
        req.setPayStatus(0);
        req.setPayWay("payWay");
        req.setBankPayTrxNos(new HashMap<>());
        req.setNewOrder(false);

        final PayInfoExtraInfoVO payInfoExtraInfoVO = new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn");

        // Configure PayService.buildPayNotifyOrderOn(...).
        final PaymentNotifyVO req1 = new PaymentNotifyVO();
        req1.setOrderOn("paySn");
        req1.setPayCode("memberName");
        req1.setPayTradeNo("memberName");
        req1.setRelPayAmt(new BigDecimal("0.00"));
        req1.setPayStatus(0);
        req1.setPayWay("payWay");
        req1.setBankPayTrxNos(new HashMap<>());
        req1.setNewOrder(false);
        when(mockPayService.buildPayNotifyOrderOn(req1,
                new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"), false)).thenReturn("paySn");

        // Configure OrderPayMapper.getByPrimaryKey(...).
        final OrderPayPO orderPayPO = new OrderPayPO();
        orderPayPO.setPayId(0);
        orderPayPO.setPaySn("paySn");
        orderPayPO.setOrderSn("pOrderSn");
        orderPayPO.setPayAmount(new BigDecimal("0.00"));
        orderPayPO.setMemberId(0);
        orderPayPO.setApiPayState("0");
        orderPayPO.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPayPO.setTradeSn("memberName");
        orderPayPO.setPaymentName("paymentName");
        orderPayPO.setPaymentCode("paymentCode");
        orderPayPO.setLoanSuccess(0);
        orderPayPO.setEnjoyPayVipFlag(0);
        orderPayPO.setPayWayExtraInfo(new JSONObject(0, false));
        orderPayPO.setOutBizSource("channel");
        orderPayPO.setOutBizId("paySn");
        when(mockOrderPayMapper.getByPrimaryKey("paySn")).thenReturn(orderPayPO);

        // Configure OrderModel.getOrderList(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO);
        final OrderExample example = new OrderExample();
        example.setOrderIdNotEquals(0);
        example.setUserNo("userNo");
        example.setUserMobile("userMobile");
        example.setOrderSn("memberName");
        example.setPaySn("paySn");
        when(mockOrderModel.getOrderList(eq(example), any(PagerInfo.class))).thenReturn(orderPOList);

        when(mockOrderService.lambdaUpdate()).thenReturn(new LambdaUpdateChainWrapper<>(null));
        when(mockPayService.payAmount("payNo")).thenReturn(new BigDecimal("0.00"));
        when(mockOrderPayProcessStrategyContext.getStrategy(0)).thenReturn(null);
        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategory storeBindCategory = new StoreBindCategory();
        storeBindCategory.setBindId(0);
        storeBindCategory.setStoreId(0L);
        storeBindCategory.setCreateVendorId(0L);
        storeBindCategory.setGoodsCategoryId1(0);
        storeBindCategory.setGoodsCategoryId2(0);
        final List<StoreBindCategory> storeBindCategories = Arrays.asList(storeBindCategory);
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                .thenReturn(storeBindCategories);

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        when(mockAccountCardFacade.defaultCard(0L)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure IOrderService.list(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        final List<OrderPO> orderPOList1 = Arrays.asList(orderPO1);
        when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(orderPOList1);

        when(mockPromotionCommonFeignClient.orderPaySuccess("memberName", 0, "paySn", "memberName", "paymentName",
                "paymentCode")).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderProductMapper.selectOne(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setStoreName("memberName");
        orderProductPO1.setGoodsName("memberName");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setSpellTeamId(0);
        orderProductPO1.setEnabledFlag(0);
        when(mockOrderProductMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderProductPO1);

        // Configure OrderProductMapper.selectList(...).
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setStoreName("memberName");
        orderProductPO2.setGoodsName("memberName");
        orderProductPO2.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO2.setProductNum(0);
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setSpellTeamId(0);
        orderProductPO2.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO2);
        when(mockOrderProductMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponExample example1 = new OrderPromotionSendCouponExample();
        example1.setSendCouponIdNotEquals(0);
        example1.setSendCouponIdIn("sendCouponIdIn");
        example1.setSendCouponId(0);
        example1.setOrderSn("memberName");
        example1.setOrderSnLike("orderSnLike");
        when(mockOrderPromotionSendCouponMapper.listByExample(example1)).thenReturn(Collections.emptyList());

        // Configure IOrderService.getById(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("memberName");
        orderPO2.setUserNo("userNo");
        orderPO2.setPaySn("paySn");
        orderPO2.setSellerId("sellerId");
        orderPO2.setBankPayTrxNo("bankPayTrxNo");
        orderPO2.setStoreId(0L);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setComposePayName("composeWay");
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setServiceFee(new BigDecimal("0.00"));
        orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO2.setSettleMode("settleMode");
        orderPO2.setFinanceRuleCode("financeRuleCode");
        orderPO2.setIsDelivery(0);
        orderPO2.setChannel("channel");
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setCustomerConfirmStatus(0);
        orderPO2.setOrderPlaceUserRoleCode(0);
        orderPO2.setExchangeFlag(0);
        when(mockOrderService.getById(0)).thenReturn(orderPO2);

        // Configure IOrderAmountStateRecordService.initOrderAmountState(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult = new Result<>(false, errorContext, null);
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("memberName");
        orderPO3.setUserNo("userNo");
        orderPO3.setPaySn("paySn");
        orderPO3.setSellerId("sellerId");
        orderPO3.setBankPayTrxNo("bankPayTrxNo");
        orderPO3.setStoreId(0L);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setComposePayName("composeWay");
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setServiceFee(new BigDecimal("0.00"));
        orderPO3.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO3.setSettleMode("settleMode");
        orderPO3.setFinanceRuleCode("financeRuleCode");
        orderPO3.setIsDelivery(0);
        orderPO3.setChannel("channel");
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setCustomerConfirmStatus(0);
        orderPO3.setOrderPlaceUserRoleCode(0);
        orderPO3.setExchangeFlag(0);
        when(mockOrderAmountRecordService.initOrderAmountState(orderPO3)).thenReturn(voidResult);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO3 = new OrderProductPO();
        orderProductPO3.setOrderProductId(0L);
        orderProductPO3.setOrderSn("orderSn");
        orderProductPO3.setStoreName("memberName");
        orderProductPO3.setGoodsName("memberName");
        orderProductPO3.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO3.setProductNum(0);
        orderProductPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO3.setServiceFee(new BigDecimal("0.00"));
        orderProductPO3.setSpellTeamId(0);
        orderProductPO3.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS1 = Arrays.asList(orderProductPO3);
        when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(orderProductPOS1);

        // Run the test
        orderPayModelUnderTest.wxAlipayCallBack(req, payInfoExtraInfoVO);

        // Verify the results
        // Confirm PayService.paySuccessProcess(...).
        final PaymentNotifyVO req2 = new PaymentNotifyVO();
        req2.setOrderOn("paySn");
        req2.setPayCode("memberName");
        req2.setPayTradeNo("memberName");
        req2.setRelPayAmt(new BigDecimal("0.00"));
        req2.setPayStatus(0);
        req2.setPayWay("payWay");
        req2.setBankPayTrxNos(new HashMap<>());
        req2.setNewOrder(false);
        verify(mockPayService).paySuccessProcess(0, req2,
                new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"), new HashMap<>());

        // Confirm BankTransferModel.transferSuccess(...).
        final PaymentNotifyVO req3 = new PaymentNotifyVO();
        req3.setOrderOn("paySn");
        req3.setPayCode("memberName");
        req3.setPayTradeNo("memberName");
        req3.setRelPayAmt(new BigDecimal("0.00"));
        req3.setPayStatus(0);
        req3.setPayWay("payWay");
        req3.setBankPayTrxNos(new HashMap<>());
        req3.setNewOrder(false);
        verify(mockBankTransferModel).transferSuccess(req3);
        verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
                new BigDecimal("0.00"));
        verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 20, 0, "订单支付完成",
                OrderCreateChannel.H5);
        verify(mockOrderProductModel).orderPaySuccessAddSales("memberName");
        verify(mockOrderService).setOrdersDeliverable(Arrays.asList("value"));

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO4 = new OrderPO();
        orderPO4.setOrderId(0);
        orderPO4.setOrderSn("memberName");
        orderPO4.setUserNo("userNo");
        orderPO4.setPaySn("paySn");
        orderPO4.setSellerId("sellerId");
        orderPO4.setBankPayTrxNo("bankPayTrxNo");
        orderPO4.setStoreId(0L);
        orderPO4.setRecommendStoreId(0L);
        orderPO4.setMemberName("memberName");
        orderPO4.setMemberId(0);
        orderPO4.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setOrderState(0);
        orderPO4.setLoanPayState(0);
        orderPO4.setPaymentName("paymentName");
        orderPO4.setPaymentCode("paymentCode");
        orderPO4.setOrderAmount(new BigDecimal("0.00"));
        orderPO4.setGoodsAmount(new BigDecimal("0.00"));
        orderPO4.setExpressFee(new BigDecimal("0.00"));
        orderPO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO4.setComposePayName("composeWay");
        orderPO4.setBalanceAmount(new BigDecimal("0.00"));
        orderPO4.setPayAmount(new BigDecimal("0.00"));
        orderPO4.setAreaCode("areaCode");
        orderPO4.setOrderType(0);
        orderPO4.setServiceFee(new BigDecimal("0.00"));
        orderPO4.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO4.setSettleMode("settleMode");
        orderPO4.setFinanceRuleCode("financeRuleCode");
        orderPO4.setIsDelivery(0);
        orderPO4.setChannel("channel");
        orderPO4.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO4.setNewOrder(false);
        orderPO4.setCustomerConfirmStatus(0);
        orderPO4.setOrderPlaceUserRoleCode(0);
        orderPO4.setExchangeFlag(0);
        verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO4, OrderEventEnum.CREATE,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg",
                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));
    }
*/
  /*  @Test
    void testWxAlipayCallBack_OrderProductModelGetOrderProductListByOrderSnReturnsNoItems() throws Exception {
        // Setup
        final PaymentNotifyVO req = new PaymentNotifyVO();
        req.setOrderOn("paySn");
        req.setPayCode("memberName");
        req.setPayTradeNo("memberName");
        req.setRelPayAmt(new BigDecimal("0.00"));
        req.setPayStatus(0);
        req.setPayWay("payWay");
        req.setBankPayTrxNos(new HashMap<>());
        req.setNewOrder(false);

        final PayInfoExtraInfoVO payInfoExtraInfoVO = new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn");

        // Configure PayService.buildPayNotifyOrderOn(...).
        final PaymentNotifyVO req1 = new PaymentNotifyVO();
        req1.setOrderOn("paySn");
        req1.setPayCode("memberName");
        req1.setPayTradeNo("memberName");
        req1.setRelPayAmt(new BigDecimal("0.00"));
        req1.setPayStatus(0);
        req1.setPayWay("payWay");
        req1.setBankPayTrxNos(new HashMap<>());
        req1.setNewOrder(false);
        when(mockPayService.buildPayNotifyOrderOn(req1,
                new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"), false)).thenReturn("paySn");

        // Configure OrderPayMapper.getByPrimaryKey(...).
        final OrderPayPO orderPayPO = new OrderPayPO();
        orderPayPO.setPayId(0);
        orderPayPO.setPaySn("paySn");
        orderPayPO.setOrderSn("pOrderSn");
        orderPayPO.setPayAmount(new BigDecimal("0.00"));
        orderPayPO.setMemberId(0);
        orderPayPO.setApiPayState("0");
        orderPayPO.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPayPO.setTradeSn("memberName");
        orderPayPO.setPaymentName("paymentName");
        orderPayPO.setPaymentCode("paymentCode");
        orderPayPO.setLoanSuccess(0);
        orderPayPO.setEnjoyPayVipFlag(0);
        orderPayPO.setPayWayExtraInfo(new JSONObject(0, false));
        orderPayPO.setOutBizSource("channel");
        orderPayPO.setOutBizId("paySn");
        when(mockOrderPayMapper.getByPrimaryKey("paySn")).thenReturn(orderPayPO);

        // Configure OrderModel.getOrderList(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO);
        final OrderExample example = new OrderExample();
        example.setOrderIdNotEquals(0);
        example.setUserNo("userNo");
        example.setUserMobile("userMobile");
        example.setOrderSn("memberName");
        example.setPaySn("paySn");
        when(mockOrderModel.getOrderList(eq(example), any(PagerInfo.class))).thenReturn(orderPOList);

        when(mockOrderService.lambdaUpdate()).thenReturn(new LambdaUpdateChainWrapper<>(null));
        when(mockPayService.payAmount("payNo")).thenReturn(new BigDecimal("0.00"));
        when(mockOrderPayProcessStrategyContext.getStrategy(0)).thenReturn(null);
        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategory storeBindCategory = new StoreBindCategory();
        storeBindCategory.setBindId(0);
        storeBindCategory.setStoreId(0L);
        storeBindCategory.setCreateVendorId(0L);
        storeBindCategory.setGoodsCategoryId1(0);
        storeBindCategory.setGoodsCategoryId2(0);
        final List<StoreBindCategory> storeBindCategories = Arrays.asList(storeBindCategory);
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                .thenReturn(storeBindCategories);

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        when(mockAccountCardFacade.defaultCard(0L)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure IOrderService.list(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        final List<OrderPO> orderPOList1 = Arrays.asList(orderPO1);
        when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(orderPOList1);

        when(mockPromotionCommonFeignClient.orderPaySuccess("memberName", 0, "paySn", "memberName", "paymentName",
                "paymentCode")).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderProductMapper.selectOne(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setStoreName("memberName");
        orderProductPO1.setGoodsName("memberName");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setSpellTeamId(0);
        orderProductPO1.setEnabledFlag(0);
        when(mockOrderProductMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderProductPO1);

        // Configure OrderProductMapper.selectList(...).
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setStoreName("memberName");
        orderProductPO2.setGoodsName("memberName");
        orderProductPO2.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO2.setProductNum(0);
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setSpellTeamId(0);
        orderProductPO2.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO2);
        when(mockOrderProductMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);
        final List<OrderPromotionSendCouponPO> orderPromotionSendCouponPOS = Arrays.asList(orderPromotionSendCouponPO);
        final OrderPromotionSendCouponExample example1 = new OrderPromotionSendCouponExample();
        example1.setSendCouponIdNotEquals(0);
        example1.setSendCouponIdIn("sendCouponIdIn");
        example1.setSendCouponId(0);
        example1.setOrderSn("memberName");
        example1.setOrderSnLike("orderSnLike");
        when(mockOrderPromotionSendCouponMapper.listByExample(example1)).thenReturn(orderPromotionSendCouponPOS);

        // Configure CouponFeignClient.getCouponByCouponId(...).
        final CouponVO couponVO = new CouponVO();
        couponVO.setCouponId(0);
        couponVO.setCouponType(0);
        couponVO.setReceivedNum(0);
        couponVO.setRandomMax(new BigDecimal("0.00"));
        couponVO.setRandomMin(new BigDecimal("0.00"));
        couponVO.setPublishNum(0);
        couponVO.setEffectiveTimeType(0);
        couponVO.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setCycle(0);
        couponVO.setState(0);
        couponVO.setStoreId(0L);
        couponVO.setUseType(0);
        when(mockCouponFeignClient.getCouponByCouponId(0)).thenReturn(couponVO);

        // Configure IOrderService.getById(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("memberName");
        orderPO2.setUserNo("userNo");
        orderPO2.setPaySn("paySn");
        orderPO2.setSellerId("sellerId");
        orderPO2.setBankPayTrxNo("bankPayTrxNo");
        orderPO2.setStoreId(0L);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setComposePayName("composeWay");
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setServiceFee(new BigDecimal("0.00"));
        orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO2.setSettleMode("settleMode");
        orderPO2.setFinanceRuleCode("financeRuleCode");
        orderPO2.setIsDelivery(0);
        orderPO2.setChannel("channel");
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setCustomerConfirmStatus(0);
        orderPO2.setOrderPlaceUserRoleCode(0);
        orderPO2.setExchangeFlag(0);
        when(mockOrderService.getById(0)).thenReturn(orderPO2);

        // Configure IOrderAmountStateRecordService.initOrderAmountState(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult = new Result<>(false, errorContext, null);
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("memberName");
        orderPO3.setUserNo("userNo");
        orderPO3.setPaySn("paySn");
        orderPO3.setSellerId("sellerId");
        orderPO3.setBankPayTrxNo("bankPayTrxNo");
        orderPO3.setStoreId(0L);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setComposePayName("composeWay");
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setServiceFee(new BigDecimal("0.00"));
        orderPO3.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO3.setSettleMode("settleMode");
        orderPO3.setFinanceRuleCode("financeRuleCode");
        orderPO3.setIsDelivery(0);
        orderPO3.setChannel("channel");
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setCustomerConfirmStatus(0);
        orderPO3.setOrderPlaceUserRoleCode(0);
        orderPO3.setExchangeFlag(0);
        when(mockOrderAmountRecordService.initOrderAmountState(orderPO3)).thenReturn(voidResult);

        when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(Collections.emptyList());

        // Run the test
        orderPayModelUnderTest.wxAlipayCallBack(req, payInfoExtraInfoVO);

        // Verify the results
        // Confirm PayService.paySuccessProcess(...).
        final PaymentNotifyVO req2 = new PaymentNotifyVO();
        req2.setOrderOn("paySn");
        req2.setPayCode("memberName");
        req2.setPayTradeNo("memberName");
        req2.setRelPayAmt(new BigDecimal("0.00"));
        req2.setPayStatus(0);
        req2.setPayWay("payWay");
        req2.setBankPayTrxNos(new HashMap<>());
        req2.setNewOrder(false);
        verify(mockPayService).paySuccessProcess(0, req2,
                new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"), new HashMap<>());

        // Confirm BankTransferModel.transferSuccess(...).
        final PaymentNotifyVO req3 = new PaymentNotifyVO();
        req3.setOrderOn("paySn");
        req3.setPayCode("memberName");
        req3.setPayTradeNo("memberName");
        req3.setRelPayAmt(new BigDecimal("0.00"));
        req3.setPayStatus(0);
        req3.setPayWay("payWay");
        req3.setBankPayTrxNos(new HashMap<>());
        req3.setNewOrder(false);
        verify(mockBankTransferModel).transferSuccess(req3);
        verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
                new BigDecimal("0.00"));
        verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 20, 0, "订单支付完成",
                OrderCreateChannel.H5);
        verify(mockOrderProductModel).orderPaySuccessAddSales("memberName");
        verify(mockOrderService).setOrdersDeliverable(Arrays.asList("value"));

        // Confirm CouponMemberFeignClient.saveCouponMember(...).
        final CouponMember couponMember = new CouponMember();
        couponMember.setCouponId(0);
        couponMember.setCouponCode("couponCode");
        couponMember.setStoreId(0L);
        couponMember.setMemberId(0);
        couponMember.setMemberName("memberName");
        couponMember.setReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseState(0);
        couponMember.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseType(0);
        couponMember.setRandomAmount(new BigDecimal("0.00"));
        verify(mockCouponMemberFeignClient).saveCouponMember(couponMember);

        // Confirm CouponUseLogFeignClient.saveCouponUseLog(...).
        final CouponUseLog couponUseLog = new CouponUseLog();
        couponUseLog.setCouponCode("couponCode");
        couponUseLog.setMemberId(0);
        couponUseLog.setMemberName("memberName");
        couponUseLog.setStoreId(0L);
        couponUseLog.setLogType(0);
        couponUseLog.setLogTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponUseLog.setLogContent("logContent");
        verify(mockCouponUseLogFeignClient).saveCouponUseLog(couponUseLog);

        // Confirm CouponFeignClient.updateOrderCoupon(...).
        final Coupon coupon = new Coupon();
        coupon.setCouponId(0);
        coupon.setCouponName("couponName");
        coupon.setCouponContent("couponContent");
        coupon.setDescription("description");
        coupon.setReceivedNum(0);
        verify(mockCouponFeignClient).updateOrderCoupon(coupon);

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO4 = new OrderPO();
        orderPO4.setOrderId(0);
        orderPO4.setOrderSn("memberName");
        orderPO4.setUserNo("userNo");
        orderPO4.setPaySn("paySn");
        orderPO4.setSellerId("sellerId");
        orderPO4.setBankPayTrxNo("bankPayTrxNo");
        orderPO4.setStoreId(0L);
        orderPO4.setRecommendStoreId(0L);
        orderPO4.setMemberName("memberName");
        orderPO4.setMemberId(0);
        orderPO4.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setOrderState(0);
        orderPO4.setLoanPayState(0);
        orderPO4.setPaymentName("paymentName");
        orderPO4.setPaymentCode("paymentCode");
        orderPO4.setOrderAmount(new BigDecimal("0.00"));
        orderPO4.setGoodsAmount(new BigDecimal("0.00"));
        orderPO4.setExpressFee(new BigDecimal("0.00"));
        orderPO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO4.setComposePayName("composeWay");
        orderPO4.setBalanceAmount(new BigDecimal("0.00"));
        orderPO4.setPayAmount(new BigDecimal("0.00"));
        orderPO4.setAreaCode("areaCode");
        orderPO4.setOrderType(0);
        orderPO4.setServiceFee(new BigDecimal("0.00"));
        orderPO4.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO4.setSettleMode("settleMode");
        orderPO4.setFinanceRuleCode("financeRuleCode");
        orderPO4.setIsDelivery(0);
        orderPO4.setChannel("channel");
        orderPO4.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO4.setNewOrder(false);
        orderPO4.setCustomerConfirmStatus(0);
        orderPO4.setOrderPlaceUserRoleCode(0);
        orderPO4.setExchangeFlag(0);
        verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO4, OrderEventEnum.CREATE,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg",
                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));
    }
*/
   /* @Test
    void testWxAlipayCallBack_RabbitTemplateThrowsAmqpException() throws Exception {
        // Setup
        final PaymentNotifyVO req = new PaymentNotifyVO();
        req.setOrderOn("paySn");
        req.setPayCode("memberName");
        req.setPayTradeNo("memberName");
        req.setRelPayAmt(new BigDecimal("0.00"));
        req.setPayStatus(0);
        req.setPayWay("payWay");
        req.setBankPayTrxNos(new HashMap<>());
        req.setNewOrder(false);

        final PayInfoExtraInfoVO payInfoExtraInfoVO = new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn");

        // Configure PayService.buildPayNotifyOrderOn(...).
        final PaymentNotifyVO req1 = new PaymentNotifyVO();
        req1.setOrderOn("paySn");
        req1.setPayCode("memberName");
        req1.setPayTradeNo("memberName");
        req1.setRelPayAmt(new BigDecimal("0.00"));
        req1.setPayStatus(0);
        req1.setPayWay("payWay");
        req1.setBankPayTrxNos(new HashMap<>());
        req1.setNewOrder(false);
        when(mockPayService.buildPayNotifyOrderOn(req1,
                new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"), false)).thenReturn("paySn");

        // Configure OrderPayMapper.getByPrimaryKey(...).
        final OrderPayPO orderPayPO = new OrderPayPO();
        orderPayPO.setPayId(0);
        orderPayPO.setPaySn("paySn");
        orderPayPO.setOrderSn("pOrderSn");
        orderPayPO.setPayAmount(new BigDecimal("0.00"));
        orderPayPO.setMemberId(0);
        orderPayPO.setApiPayState("0");
        orderPayPO.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPayPO.setTradeSn("memberName");
        orderPayPO.setPaymentName("paymentName");
        orderPayPO.setPaymentCode("paymentCode");
        orderPayPO.setLoanSuccess(0);
        orderPayPO.setEnjoyPayVipFlag(0);
        orderPayPO.setPayWayExtraInfo(new JSONObject(0, false));
        orderPayPO.setOutBizSource("channel");
        orderPayPO.setOutBizId("paySn");
        when(mockOrderPayMapper.getByPrimaryKey("paySn")).thenReturn(orderPayPO);

        // Configure OrderModel.getOrderList(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO);
        final OrderExample example = new OrderExample();
        example.setOrderIdNotEquals(0);
        example.setUserNo("userNo");
        example.setUserMobile("userMobile");
        example.setOrderSn("memberName");
        example.setPaySn("paySn");
        when(mockOrderModel.getOrderList(eq(example), any(PagerInfo.class))).thenReturn(orderPOList);

        when(mockOrderService.lambdaUpdate()).thenReturn(new LambdaUpdateChainWrapper<>(null));
        when(mockPayService.payAmount("payNo")).thenReturn(new BigDecimal("0.00"));
        when(mockOrderPayProcessStrategyContext.getStrategy(0)).thenReturn(null);
        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategory storeBindCategory = new StoreBindCategory();
        storeBindCategory.setBindId(0);
        storeBindCategory.setStoreId(0L);
        storeBindCategory.setCreateVendorId(0L);
        storeBindCategory.setGoodsCategoryId1(0);
        storeBindCategory.setGoodsCategoryId2(0);
        final List<StoreBindCategory> storeBindCategories = Arrays.asList(storeBindCategory);
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                .thenReturn(storeBindCategories);

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        when(mockAccountCardFacade.defaultCard(0L)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure IOrderService.list(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        final List<OrderPO> orderPOList1 = Arrays.asList(orderPO1);
        when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(orderPOList1);

        when(mockPromotionCommonFeignClient.orderPaySuccess("memberName", 0, "paySn", "memberName", "paymentName",
                "paymentCode")).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderProductMapper.selectOne(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setStoreName("memberName");
        orderProductPO1.setGoodsName("memberName");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setSpellTeamId(0);
        orderProductPO1.setEnabledFlag(0);
        when(mockOrderProductMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderProductPO1);

        // Configure OrderProductMapper.selectList(...).
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setStoreName("memberName");
        orderProductPO2.setGoodsName("memberName");
        orderProductPO2.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO2.setProductNum(0);
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setSpellTeamId(0);
        orderProductPO2.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO2);
        when(mockOrderProductMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);
        final List<OrderPromotionSendCouponPO> orderPromotionSendCouponPOS = Arrays.asList(orderPromotionSendCouponPO);
        final OrderPromotionSendCouponExample example1 = new OrderPromotionSendCouponExample();
        example1.setSendCouponIdNotEquals(0);
        example1.setSendCouponIdIn("sendCouponIdIn");
        example1.setSendCouponId(0);
        example1.setOrderSn("memberName");
        example1.setOrderSnLike("orderSnLike");
        when(mockOrderPromotionSendCouponMapper.listByExample(example1)).thenReturn(orderPromotionSendCouponPOS);

        // Configure CouponFeignClient.getCouponByCouponId(...).
        final CouponVO couponVO = new CouponVO();
        couponVO.setCouponId(0);
        couponVO.setCouponType(0);
        couponVO.setReceivedNum(0);
        couponVO.setRandomMax(new BigDecimal("0.00"));
        couponVO.setRandomMin(new BigDecimal("0.00"));
        couponVO.setPublishNum(0);
        couponVO.setEffectiveTimeType(0);
        couponVO.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setCycle(0);
        couponVO.setState(0);
        couponVO.setStoreId(0L);
        couponVO.setUseType(0);
        when(mockCouponFeignClient.getCouponByCouponId(0)).thenReturn(couponVO);

        // Configure IOrderService.getById(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("memberName");
        orderPO2.setUserNo("userNo");
        orderPO2.setPaySn("paySn");
        orderPO2.setSellerId("sellerId");
        orderPO2.setBankPayTrxNo("bankPayTrxNo");
        orderPO2.setStoreId(0L);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setComposePayName("composeWay");
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setServiceFee(new BigDecimal("0.00"));
        orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO2.setSettleMode("settleMode");
        orderPO2.setFinanceRuleCode("financeRuleCode");
        orderPO2.setIsDelivery(0);
        orderPO2.setChannel("channel");
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setCustomerConfirmStatus(0);
        orderPO2.setOrderPlaceUserRoleCode(0);
        orderPO2.setExchangeFlag(0);
        when(mockOrderService.getById(0)).thenReturn(orderPO2);

        // Configure IOrderAmountStateRecordService.initOrderAmountState(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult = new Result<>(false, errorContext, null);
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("memberName");
        orderPO3.setUserNo("userNo");
        orderPO3.setPaySn("paySn");
        orderPO3.setSellerId("sellerId");
        orderPO3.setBankPayTrxNo("bankPayTrxNo");
        orderPO3.setStoreId(0L);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setComposePayName("composeWay");
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setServiceFee(new BigDecimal("0.00"));
        orderPO3.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO3.setSettleMode("settleMode");
        orderPO3.setFinanceRuleCode("financeRuleCode");
        orderPO3.setIsDelivery(0);
        orderPO3.setChannel("channel");
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setCustomerConfirmStatus(0);
        orderPO3.setOrderPlaceUserRoleCode(0);
        orderPO3.setExchangeFlag(0);
        when(mockOrderAmountRecordService.initOrderAmountState(orderPO3)).thenReturn(voidResult);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO3 = new OrderProductPO();
        orderProductPO3.setOrderProductId(0L);
        orderProductPO3.setOrderSn("orderSn");
        orderProductPO3.setStoreName("memberName");
        orderProductPO3.setGoodsName("memberName");
        orderProductPO3.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO3.setProductNum(0);
        orderProductPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO3.setServiceFee(new BigDecimal("0.00"));
        orderProductPO3.setSpellTeamId(0);
        orderProductPO3.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS1 = Arrays.asList(orderProductPO3);
        when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(orderProductPOS1);

        doThrow(AmqpException.class).when(mockRabbitTemplate).convertAndSend("newmall_exchange",
                "newmall_queue_member_msg",
                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));

        // Run the test
        orderPayModelUnderTest.wxAlipayCallBack(req, payInfoExtraInfoVO);

        // Verify the results
        // Confirm PayService.paySuccessProcess(...).
        final PaymentNotifyVO req2 = new PaymentNotifyVO();
        req2.setOrderOn("paySn");
        req2.setPayCode("memberName");
        req2.setPayTradeNo("memberName");
        req2.setRelPayAmt(new BigDecimal("0.00"));
        req2.setPayStatus(0);
        req2.setPayWay("payWay");
        req2.setBankPayTrxNos(new HashMap<>());
        req2.setNewOrder(false);
        verify(mockPayService).paySuccessProcess(0, req2,
                new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"), new HashMap<>());

        // Confirm BankTransferModel.transferSuccess(...).
        final PaymentNotifyVO req3 = new PaymentNotifyVO();
        req3.setOrderOn("paySn");
        req3.setPayCode("memberName");
        req3.setPayTradeNo("memberName");
        req3.setRelPayAmt(new BigDecimal("0.00"));
        req3.setPayStatus(0);
        req3.setPayWay("payWay");
        req3.setBankPayTrxNos(new HashMap<>());
        req3.setNewOrder(false);
        verify(mockBankTransferModel).transferSuccess(req3);
        verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
                new BigDecimal("0.00"));
        verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 20, 0, "订单支付完成",
                OrderCreateChannel.H5);
        verify(mockOrderProductModel).orderPaySuccessAddSales("memberName");
        verify(mockOrderService).setOrdersDeliverable(Arrays.asList("value"));

        // Confirm CouponMemberFeignClient.saveCouponMember(...).
        final CouponMember couponMember = new CouponMember();
        couponMember.setCouponId(0);
        couponMember.setCouponCode("couponCode");
        couponMember.setStoreId(0L);
        couponMember.setMemberId(0);
        couponMember.setMemberName("memberName");
        couponMember.setReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseState(0);
        couponMember.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseType(0);
        couponMember.setRandomAmount(new BigDecimal("0.00"));
        verify(mockCouponMemberFeignClient).saveCouponMember(couponMember);

        // Confirm CouponUseLogFeignClient.saveCouponUseLog(...).
        final CouponUseLog couponUseLog = new CouponUseLog();
        couponUseLog.setCouponCode("couponCode");
        couponUseLog.setMemberId(0);
        couponUseLog.setMemberName("memberName");
        couponUseLog.setStoreId(0L);
        couponUseLog.setLogType(0);
        couponUseLog.setLogTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponUseLog.setLogContent("logContent");
        verify(mockCouponUseLogFeignClient).saveCouponUseLog(couponUseLog);

        // Confirm CouponFeignClient.updateOrderCoupon(...).
        final Coupon coupon = new Coupon();
        coupon.setCouponId(0);
        coupon.setCouponName("couponName");
        coupon.setCouponContent("couponContent");
        coupon.setDescription("description");
        coupon.setReceivedNum(0);
        verify(mockCouponFeignClient).updateOrderCoupon(coupon);

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO4 = new OrderPO();
        orderPO4.setOrderId(0);
        orderPO4.setOrderSn("memberName");
        orderPO4.setUserNo("userNo");
        orderPO4.setPaySn("paySn");
        orderPO4.setSellerId("sellerId");
        orderPO4.setBankPayTrxNo("bankPayTrxNo");
        orderPO4.setStoreId(0L);
        orderPO4.setRecommendStoreId(0L);
        orderPO4.setMemberName("memberName");
        orderPO4.setMemberId(0);
        orderPO4.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setOrderState(0);
        orderPO4.setLoanPayState(0);
        orderPO4.setPaymentName("paymentName");
        orderPO4.setPaymentCode("paymentCode");
        orderPO4.setOrderAmount(new BigDecimal("0.00"));
        orderPO4.setGoodsAmount(new BigDecimal("0.00"));
        orderPO4.setExpressFee(new BigDecimal("0.00"));
        orderPO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO4.setComposePayName("composeWay");
        orderPO4.setBalanceAmount(new BigDecimal("0.00"));
        orderPO4.setPayAmount(new BigDecimal("0.00"));
        orderPO4.setAreaCode("areaCode");
        orderPO4.setOrderType(0);
        orderPO4.setServiceFee(new BigDecimal("0.00"));
        orderPO4.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO4.setSettleMode("settleMode");
        orderPO4.setFinanceRuleCode("financeRuleCode");
        orderPO4.setIsDelivery(0);
        orderPO4.setChannel("channel");
        orderPO4.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO4.setNewOrder(false);
        orderPO4.setCustomerConfirmStatus(0);
        orderPO4.setOrderPlaceUserRoleCode(0);
        orderPO4.setExchangeFlag(0);
        verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO4, OrderEventEnum.CREATE,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
    }*/

  /*  @Test
    void testUpdateNewOrderByPaySn() throws Exception {
        // Setup
        when(mockOrderService.lambdaUpdate()).thenReturn(new LambdaUpdateChainWrapper<>(null));

        // Run the test
        final boolean result = orderPayModelUnderTest.updateNewOrderByPaySn("paySn", false);

        // Verify the results
        assertThat(result).isFalse();
    }
*/
  /*  @Test
    void testEnjoyPayCallBack() throws Exception {
        // Setup
        final PaymentNotifyVO req = new PaymentNotifyVO();
        req.setOrderOn("paySn");
        req.setPayCode("memberName");
        req.setPayTradeNo("memberName");
        req.setRelPayAmt(new BigDecimal("0.00"));
        req.setPayStatus(0);
        req.setPayWay("payWay");
        req.setBankPayTrxNos(new HashMap<>());
        req.setNewOrder(false);

        final PayInfoExtraInfoVO payInfoExtraInfoVO = new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn");

        // Configure PayService.buildPayNotifyOrderOn(...).
        final PaymentNotifyVO req1 = new PaymentNotifyVO();
        req1.setOrderOn("paySn");
        req1.setPayCode("memberName");
        req1.setPayTradeNo("memberName");
        req1.setRelPayAmt(new BigDecimal("0.00"));
        req1.setPayStatus(0);
        req1.setPayWay("payWay");
        req1.setBankPayTrxNos(new HashMap<>());
        req1.setNewOrder(false);
        when(mockPayService.buildPayNotifyOrderOn(req1,
                new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"), true)).thenReturn("memberName");

        // Configure OrderModel.getOrderByOrderSn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);
        when(mockOrderModel.getOrderByOrderSn("memberName")).thenReturn(orderPO);

        // Configure OrderPayMapper.getByPrimaryKey(...).
        final OrderPayPO orderPayPO = new OrderPayPO();
        orderPayPO.setPayId(0);
        orderPayPO.setPaySn("paySn");
        orderPayPO.setOrderSn("pOrderSn");
        orderPayPO.setPayAmount(new BigDecimal("0.00"));
        orderPayPO.setMemberId(0);
        orderPayPO.setApiPayState("0");
        orderPayPO.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPayPO.setTradeSn("memberName");
        orderPayPO.setPaymentName("paymentName");
        orderPayPO.setPaymentCode("paymentCode");
        orderPayPO.setLoanSuccess(0);
        orderPayPO.setEnjoyPayVipFlag(0);
        orderPayPO.setPayWayExtraInfo(new JSONObject(0, false));
        orderPayPO.setOutBizSource("channel");
        orderPayPO.setOutBizId("paySn");
        when(mockOrderPayMapper.getByPrimaryKey("paySn")).thenReturn(orderPayPO);

        when(mockOrderService.lambdaUpdate()).thenReturn(new LambdaUpdateChainWrapper<>(null));
        when(mockPayService.payAmount("payNo")).thenReturn(new BigDecimal("0.00"));

        // Configure OrderModel.getOrderList(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO1);
        final OrderExample example = new OrderExample();
        example.setOrderIdNotEquals(0);
        example.setUserNo("userNo");
        example.setUserMobile("userMobile");
        example.setOrderSn("memberName");
        example.setPaySn("paySn");
        when(mockOrderModel.getOrderList(eq(example), any(PagerInfo.class))).thenReturn(orderPOList);

        when(mockOrderPayProcessStrategyContext.getStrategy(0)).thenReturn(null);
        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategory storeBindCategory = new StoreBindCategory();
        storeBindCategory.setBindId(0);
        storeBindCategory.setStoreId(0L);
        storeBindCategory.setCreateVendorId(0L);
        storeBindCategory.setGoodsCategoryId1(0);
        storeBindCategory.setGoodsCategoryId2(0);
        final List<StoreBindCategory> storeBindCategories = Arrays.asList(storeBindCategory);
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                .thenReturn(storeBindCategories);

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        when(mockAccountCardFacade.defaultCard(0L)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure BillOperatinIntegration.detailByBankAccount(...).
        final AccountCard accountCard = new AccountCard();
        accountCard.setCardId(0L);
        accountCard.setStoreId("storeId");
        accountCard.setStoreName("storeName");
        accountCard.setOrgType("orgType");
        accountCard.setBankAccountNumber("bankAccountNumber");
        when(mockBillOperatinIntegration.detailByBankAccount("UIN_PLF_STORE_ID101",
                AccountCardTypeEnum.UNI_JS_PLF_SUP)).thenReturn(accountCard);

        // Configure IBzBankTransferService.list(...).
        final BzBankTransferPO bzBankTransferPO = new BzBankTransferPO();
        bzBankTransferPO.setEnabledFlag(0);
        bzBankTransferPO.setPaySn("paySn");
        bzBankTransferPO.setOutOrderNo("outOrderNo");
        bzBankTransferPO.setPayAmount(new BigDecimal("0.00"));
        bzBankTransferPO.setReceiptAccount("receiptAccount");
        final List<BzBankTransferPO> bzBankTransferPOS = Arrays.asList(bzBankTransferPO);
        when(mockBzBankTransferService.list(any(LambdaQueryWrapper.class))).thenReturn(bzBankTransferPOS);

        // Configure StoreFeignClient.getStoreContractReciptInfo(...).
        final StoreContractReceiptInfoVO storeContractReceiptInfoVO = new StoreContractReceiptInfoVO();
        storeContractReceiptInfoVO.setWxSellerId("wxSellerId");
        storeContractReceiptInfoVO.setAliSellerId("aliSellerId");
        final Store store = new Store();
        store.setAcctId("acctId");
        storeContractReceiptInfoVO.setStore(store);
        storeContractReceiptInfoVO.setRecommentWxSellerId("recommentWxSellerId");
        storeContractReceiptInfoVO.setRecommentAliSellerId("recommentAliSellerId");
        when(mockStoreFeignClient.getStoreContractReciptInfo(0L)).thenReturn(storeContractReceiptInfoVO);

        // Configure CustomerServiceFeign.info(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final BankcardVo bankcardVo = new BankcardVo();
        bankcardVo.setCardId("cardId");
        bankcardVo.setCardNo("cardNo");
        bankcardVo.setAcctName("acctName");
        bankcardVo.setBankCode("bankCode");
        bankcardVo.setBankName("bankName");
        final Result<BankcardVo> bankcardVoResult = new Result<>(false, errorContext, bankcardVo);
        final QueryBankcardInfoReq var1 = new QueryBankcardInfoReq();
        var1.setCardId("cardId");
        when(mockCustomerServiceFeign.info(var1)).thenReturn(bankcardVoResult);

        // Configure StmAccountFacade.detailV2(...).
        final AccountQuery accountQuery = new AccountQuery();
        accountQuery.setCurrent(0);
        accountQuery.setPageSize(0);
        accountQuery.setAccountId("accountId");
        accountQuery.setStoreId("UIN_PLF_STORE_ID101");
        accountQuery.setAccountType("accountType");
        when(mockStmAccountFacade.detailV2(accountQuery)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderOfflineService.queryOrderOfflineList(...).
        final OrderOfflinePO orderOfflinePO = new OrderOfflinePO();
        orderOfflinePO.setEnabledFlag(0);
        orderOfflinePO.setPaySn("paySn");
        orderOfflinePO.setReceiptAccount("receiptAccount");
        orderOfflinePO.setReceiptAmount(new BigDecimal("0.00"));
        orderOfflinePO.setReceiptTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<OrderOfflinePO> orderOfflinePOS = Arrays.asList(orderOfflinePO);
        when(mockOrderOfflineService.queryOrderOfflineList("paySn")).thenReturn(orderOfflinePOS);

        // Configure IOrderService.list(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("memberName");
        orderPO2.setUserNo("userNo");
        orderPO2.setPaySn("paySn");
        orderPO2.setSellerId("sellerId");
        orderPO2.setBankPayTrxNo("bankPayTrxNo");
        orderPO2.setStoreId(0L);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setComposePayName("composeWay");
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setServiceFee(new BigDecimal("0.00"));
        orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO2.setSettleMode("settleMode");
        orderPO2.setFinanceRuleCode("financeRuleCode");
        orderPO2.setIsDelivery(0);
        orderPO2.setChannel("channel");
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setCustomerConfirmStatus(0);
        orderPO2.setOrderPlaceUserRoleCode(0);
        orderPO2.setExchangeFlag(0);
        final List<OrderPO> orderPOList1 = Arrays.asList(orderPO2);
        when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(orderPOList1);

        // Configure OrderPayMapper.updateByPrimaryKeySelective(...).
        final OrderPayPO record = new OrderPayPO();
        record.setPayId(0);
        record.setPaySn("paySn");
        record.setOrderSn("pOrderSn");
        record.setPayAmount(new BigDecimal("0.00"));
        record.setMemberId(0);
        record.setApiPayState("0");
        record.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        record.setTradeSn("memberName");
        record.setPaymentName("paymentName");
        record.setPaymentCode("paymentCode");
        record.setLoanSuccess(0);
        record.setEnjoyPayVipFlag(0);
        record.setPayWayExtraInfo(new JSONObject(0, false));
        record.setOutBizSource("channel");
        record.setOutBizId("paySn");
        when(mockOrderPayMapper.updateByPrimaryKeySelective(record)).thenReturn(0);

        when(mockPromotionCommonFeignClient.orderPaySuccess("memberName", 0, "paySn", "memberName", "paymentName",
                "paymentCode")).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderProductMapper.selectOne(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setStoreName("memberName");
        orderProductPO1.setGoodsName("memberName");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setSpellTeamId(0);
        orderProductPO1.setEnabledFlag(0);
        when(mockOrderProductMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderProductPO1);

        // Configure OrderProductMapper.selectList(...).
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setStoreName("memberName");
        orderProductPO2.setGoodsName("memberName");
        orderProductPO2.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO2.setProductNum(0);
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setSpellTeamId(0);
        orderProductPO2.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO2);
        when(mockOrderProductMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);
        final List<OrderPromotionSendCouponPO> orderPromotionSendCouponPOS = Arrays.asList(orderPromotionSendCouponPO);
        final OrderPromotionSendCouponExample example1 = new OrderPromotionSendCouponExample();
        example1.setSendCouponIdNotEquals(0);
        example1.setSendCouponIdIn("sendCouponIdIn");
        example1.setSendCouponId(0);
        example1.setOrderSn("memberName");
        example1.setOrderSnLike("orderSnLike");
        when(mockOrderPromotionSendCouponMapper.listByExample(example1)).thenReturn(orderPromotionSendCouponPOS);

        // Configure CouponFeignClient.getCouponByCouponId(...).
        final CouponVO couponVO = new CouponVO();
        couponVO.setCouponId(0);
        couponVO.setCouponType(0);
        couponVO.setReceivedNum(0);
        couponVO.setRandomMax(new BigDecimal("0.00"));
        couponVO.setRandomMin(new BigDecimal("0.00"));
        couponVO.setPublishNum(0);
        couponVO.setEffectiveTimeType(0);
        couponVO.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setCycle(0);
        couponVO.setState(0);
        couponVO.setStoreId(0L);
        couponVO.setUseType(0);
        when(mockCouponFeignClient.getCouponByCouponId(0)).thenReturn(couponVO);

        // Configure IOrderService.getById(...).
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("memberName");
        orderPO3.setUserNo("userNo");
        orderPO3.setPaySn("paySn");
        orderPO3.setSellerId("sellerId");
        orderPO3.setBankPayTrxNo("bankPayTrxNo");
        orderPO3.setStoreId(0L);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setComposePayName("composeWay");
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setServiceFee(new BigDecimal("0.00"));
        orderPO3.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO3.setSettleMode("settleMode");
        orderPO3.setFinanceRuleCode("financeRuleCode");
        orderPO3.setIsDelivery(0);
        orderPO3.setChannel("channel");
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setCustomerConfirmStatus(0);
        orderPO3.setOrderPlaceUserRoleCode(0);
        orderPO3.setExchangeFlag(0);
        when(mockOrderService.getById(0)).thenReturn(orderPO3);

        // Configure IOrderAmountStateRecordService.initOrderAmountState(...).
        final ErrorContext errorContext1 = new ErrorContext();
        errorContext1.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext1.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult = new Result<>(false, errorContext1, null);
        final OrderPO orderPO4 = new OrderPO();
        orderPO4.setOrderId(0);
        orderPO4.setOrderSn("memberName");
        orderPO4.setUserNo("userNo");
        orderPO4.setPaySn("paySn");
        orderPO4.setSellerId("sellerId");
        orderPO4.setBankPayTrxNo("bankPayTrxNo");
        orderPO4.setStoreId(0L);
        orderPO4.setRecommendStoreId(0L);
        orderPO4.setMemberName("memberName");
        orderPO4.setMemberId(0);
        orderPO4.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setOrderState(0);
        orderPO4.setLoanPayState(0);
        orderPO4.setPaymentName("paymentName");
        orderPO4.setPaymentCode("paymentCode");
        orderPO4.setOrderAmount(new BigDecimal("0.00"));
        orderPO4.setGoodsAmount(new BigDecimal("0.00"));
        orderPO4.setExpressFee(new BigDecimal("0.00"));
        orderPO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO4.setComposePayName("composeWay");
        orderPO4.setBalanceAmount(new BigDecimal("0.00"));
        orderPO4.setPayAmount(new BigDecimal("0.00"));
        orderPO4.setAreaCode("areaCode");
        orderPO4.setOrderType(0);
        orderPO4.setServiceFee(new BigDecimal("0.00"));
        orderPO4.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO4.setSettleMode("settleMode");
        orderPO4.setFinanceRuleCode("financeRuleCode");
        orderPO4.setIsDelivery(0);
        orderPO4.setChannel("channel");
        orderPO4.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO4.setNewOrder(false);
        orderPO4.setCustomerConfirmStatus(0);
        orderPO4.setOrderPlaceUserRoleCode(0);
        orderPO4.setExchangeFlag(0);
        when(mockOrderAmountRecordService.initOrderAmountState(orderPO4)).thenReturn(voidResult);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO3 = new OrderProductPO();
        orderProductPO3.setOrderProductId(0L);
        orderProductPO3.setOrderSn("orderSn");
        orderProductPO3.setStoreName("memberName");
        orderProductPO3.setGoodsName("memberName");
        orderProductPO3.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO3.setProductNum(0);
        orderProductPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO3.setServiceFee(new BigDecimal("0.00"));
        orderProductPO3.setSpellTeamId(0);
        orderProductPO3.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS1 = Arrays.asList(orderProductPO3);
        when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(orderProductPOS1);

        when(mockIBzOldUserPoolService.insertFirstLoanOrder("userNo", "memberName")).thenReturn(false);

        // Configure IOrderExtendFinanceService.getByOrderSn(...).
        final OrderExtendFinancePO orderExtendFinancePO = new OrderExtendFinancePO();
        orderExtendFinancePO.setFinanceId(0L);
        orderExtendFinancePO.setOrderSn("orderSn");
        orderExtendFinancePO.setInterestWay(0);
        orderExtendFinancePO.setPlanInterestStartDays(0);
        orderExtendFinancePO.setPlanLoanDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockFinanceService.getByOrderSn("memberName")).thenReturn(orderExtendFinancePO);

        // Configure OrderModel.doLoanOperate(...).
        final ErrorContext errorContext2 = new ErrorContext();
        errorContext2.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext2.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult1 = new Result<>(false, errorContext2, null);
        when(mockOrderModel.doLoanOperate("memberName", 0L, "payNo")).thenReturn(voidResult1);

        // Run the test
        orderPayModelUnderTest.enjoyPayCallBack(req, payInfoExtraInfoVO);

        // Verify the results
        // Confirm PayService.paySuccessProcess(...).
        final PaymentNotifyVO req2 = new PaymentNotifyVO();
        req2.setOrderOn("paySn");
        req2.setPayCode("memberName");
        req2.setPayTradeNo("memberName");
        req2.setRelPayAmt(new BigDecimal("0.00"));
        req2.setPayStatus(0);
        req2.setPayWay("payWay");
        req2.setBankPayTrxNos(new HashMap<>());
        req2.setNewOrder(false);
        verify(mockPayService).paySuccessProcess(0, req2,
                new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"), new HashMap<>());
        verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
                new BigDecimal("0.00"));
        verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 20, 0, "订单支付完成",
                OrderCreateChannel.H5);
        verify(mockOrderProductModel).orderPaySuccessAddSales("memberName");
        verify(mockOrderService).setOrdersDeliverable(Arrays.asList("value"));

        // Confirm CouponMemberFeignClient.saveCouponMember(...).
        final CouponMember couponMember = new CouponMember();
        couponMember.setCouponId(0);
        couponMember.setCouponCode("couponCode");
        couponMember.setStoreId(0L);
        couponMember.setMemberId(0);
        couponMember.setMemberName("memberName");
        couponMember.setReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseState(0);
        couponMember.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseType(0);
        couponMember.setRandomAmount(new BigDecimal("0.00"));
        verify(mockCouponMemberFeignClient).saveCouponMember(couponMember);

        // Confirm CouponUseLogFeignClient.saveCouponUseLog(...).
        final CouponUseLog couponUseLog = new CouponUseLog();
        couponUseLog.setCouponCode("couponCode");
        couponUseLog.setMemberId(0);
        couponUseLog.setMemberName("memberName");
        couponUseLog.setStoreId(0L);
        couponUseLog.setLogType(0);
        couponUseLog.setLogTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponUseLog.setLogContent("logContent");
        verify(mockCouponUseLogFeignClient).saveCouponUseLog(couponUseLog);

        // Confirm CouponFeignClient.updateOrderCoupon(...).
        final Coupon coupon = new Coupon();
        coupon.setCouponId(0);
        coupon.setCouponName("couponName");
        coupon.setCouponContent("couponContent");
        coupon.setDescription("description");
        coupon.setReceivedNum(0);
        verify(mockCouponFeignClient).updateOrderCoupon(coupon);

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO5 = new OrderPO();
        orderPO5.setOrderId(0);
        orderPO5.setOrderSn("memberName");
        orderPO5.setUserNo("userNo");
        orderPO5.setPaySn("paySn");
        orderPO5.setSellerId("sellerId");
        orderPO5.setBankPayTrxNo("bankPayTrxNo");
        orderPO5.setStoreId(0L);
        orderPO5.setRecommendStoreId(0L);
        orderPO5.setMemberName("memberName");
        orderPO5.setMemberId(0);
        orderPO5.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO5.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO5.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO5.setOrderState(0);
        orderPO5.setLoanPayState(0);
        orderPO5.setPaymentName("paymentName");
        orderPO5.setPaymentCode("paymentCode");
        orderPO5.setOrderAmount(new BigDecimal("0.00"));
        orderPO5.setGoodsAmount(new BigDecimal("0.00"));
        orderPO5.setExpressFee(new BigDecimal("0.00"));
        orderPO5.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO5.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO5.setXzCardAmount(new BigDecimal("0.00"));
        orderPO5.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO5.setComposePayName("composeWay");
        orderPO5.setBalanceAmount(new BigDecimal("0.00"));
        orderPO5.setPayAmount(new BigDecimal("0.00"));
        orderPO5.setAreaCode("areaCode");
        orderPO5.setOrderType(0);
        orderPO5.setServiceFee(new BigDecimal("0.00"));
        orderPO5.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO5.setSettleMode("settleMode");
        orderPO5.setFinanceRuleCode("financeRuleCode");
        orderPO5.setIsDelivery(0);
        orderPO5.setChannel("channel");
        orderPO5.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO5.setNewOrder(false);
        orderPO5.setCustomerConfirmStatus(0);
        orderPO5.setOrderPlaceUserRoleCode(0);
        orderPO5.setExchangeFlag(0);
        verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO5, OrderEventEnum.CREATE,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg",
                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));
        verify(mockOrderService).dealZeroEnjoyPayOrder("paySn", "payNo");
        verify(mockTaskQueueService).saveTaskQueue(0L, TaskQueueBizTypeEnum.AUTO_LOAN,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "CNBJ0000");

        // Confirm OrderModel.loanPayFail(...).
        final OrderPO orderPODb = new OrderPO();
        orderPODb.setOrderId(0);
        orderPODb.setOrderSn("memberName");
        orderPODb.setUserNo("userNo");
        orderPODb.setPaySn("paySn");
        orderPODb.setSellerId("sellerId");
        orderPODb.setBankPayTrxNo("bankPayTrxNo");
        orderPODb.setStoreId(0L);
        orderPODb.setRecommendStoreId(0L);
        orderPODb.setMemberName("memberName");
        orderPODb.setMemberId(0);
        orderPODb.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPODb.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPODb.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPODb.setOrderState(0);
        orderPODb.setLoanPayState(0);
        orderPODb.setPaymentName("paymentName");
        orderPODb.setPaymentCode("paymentCode");
        orderPODb.setOrderAmount(new BigDecimal("0.00"));
        orderPODb.setGoodsAmount(new BigDecimal("0.00"));
        orderPODb.setExpressFee(new BigDecimal("0.00"));
        orderPODb.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPODb.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPODb.setXzCardAmount(new BigDecimal("0.00"));
        orderPODb.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPODb.setComposePayName("composeWay");
        orderPODb.setBalanceAmount(new BigDecimal("0.00"));
        orderPODb.setPayAmount(new BigDecimal("0.00"));
        orderPODb.setAreaCode("areaCode");
        orderPODb.setOrderType(0);
        orderPODb.setServiceFee(new BigDecimal("0.00"));
        orderPODb.setServiceFeeRate(new BigDecimal("0.00"));
        orderPODb.setSettleMode("settleMode");
        orderPODb.setFinanceRuleCode("financeRuleCode");
        orderPODb.setIsDelivery(0);
        orderPODb.setChannel("channel");
        orderPODb.setChannelServiceFee(new BigDecimal("0.00"));
        orderPODb.setNewOrder(false);
        orderPODb.setCustomerConfirmStatus(0);
        orderPODb.setOrderPlaceUserRoleCode(0);
        orderPODb.setExchangeFlag(0);
        verify(mockOrderModel).loanPayFail(orderPODb, "failureReason");

        // Confirm OrderModel.loanPaySuccess(...).
        final OrderPO orderPO6 = new OrderPO();
        orderPO6.setOrderId(0);
        orderPO6.setOrderSn("memberName");
        orderPO6.setUserNo("userNo");
        orderPO6.setPaySn("paySn");
        orderPO6.setSellerId("sellerId");
        orderPO6.setBankPayTrxNo("bankPayTrxNo");
        orderPO6.setStoreId(0L);
        orderPO6.setRecommendStoreId(0L);
        orderPO6.setMemberName("memberName");
        orderPO6.setMemberId(0);
        orderPO6.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO6.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO6.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO6.setOrderState(0);
        orderPO6.setLoanPayState(0);
        orderPO6.setPaymentName("paymentName");
        orderPO6.setPaymentCode("paymentCode");
        orderPO6.setOrderAmount(new BigDecimal("0.00"));
        orderPO6.setGoodsAmount(new BigDecimal("0.00"));
        orderPO6.setExpressFee(new BigDecimal("0.00"));
        orderPO6.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO6.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO6.setXzCardAmount(new BigDecimal("0.00"));
        orderPO6.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO6.setComposePayName("composeWay");
        orderPO6.setBalanceAmount(new BigDecimal("0.00"));
        orderPO6.setPayAmount(new BigDecimal("0.00"));
        orderPO6.setAreaCode("areaCode");
        orderPO6.setOrderType(0);
        orderPO6.setServiceFee(new BigDecimal("0.00"));
        orderPO6.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO6.setSettleMode("settleMode");
        orderPO6.setFinanceRuleCode("financeRuleCode");
        orderPO6.setIsDelivery(0);
        orderPO6.setChannel("channel");
        orderPO6.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO6.setNewOrder(false);
        orderPO6.setCustomerConfirmStatus(0);
        orderPO6.setOrderPlaceUserRoleCode(0);
        orderPO6.setExchangeFlag(0);
        verify(mockOrderModel).loanPaySuccess(orderPO6, 3, 0L, "memberName");

        // Confirm IOrderReturnService.orderCancelWithoutRefund(...).
        final OrderPO orderDb = new OrderPO();
        orderDb.setOrderId(0);
        orderDb.setOrderSn("memberName");
        orderDb.setUserNo("userNo");
        orderDb.setPaySn("paySn");
        orderDb.setSellerId("sellerId");
        orderDb.setBankPayTrxNo("bankPayTrxNo");
        orderDb.setStoreId(0L);
        orderDb.setRecommendStoreId(0L);
        orderDb.setMemberName("memberName");
        orderDb.setMemberId(0);
        orderDb.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderDb.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderDb.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderDb.setOrderState(0);
        orderDb.setLoanPayState(0);
        orderDb.setPaymentName("paymentName");
        orderDb.setPaymentCode("paymentCode");
        orderDb.setOrderAmount(new BigDecimal("0.00"));
        orderDb.setGoodsAmount(new BigDecimal("0.00"));
        orderDb.setExpressFee(new BigDecimal("0.00"));
        orderDb.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderDb.setStoreActivityAmount(new BigDecimal("0.00"));
        orderDb.setXzCardAmount(new BigDecimal("0.00"));
        orderDb.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderDb.setComposePayName("composeWay");
        orderDb.setBalanceAmount(new BigDecimal("0.00"));
        orderDb.setPayAmount(new BigDecimal("0.00"));
        orderDb.setAreaCode("areaCode");
        orderDb.setOrderType(0);
        orderDb.setServiceFee(new BigDecimal("0.00"));
        orderDb.setServiceFeeRate(new BigDecimal("0.00"));
        orderDb.setSettleMode("settleMode");
        orderDb.setFinanceRuleCode("financeRuleCode");
        orderDb.setIsDelivery(0);
        orderDb.setChannel("channel");
        orderDb.setChannelServiceFee(new BigDecimal("0.00"));
        orderDb.setNewOrder(false);
        orderDb.setCustomerConfirmStatus(0);
        orderDb.setOrderPlaceUserRoleCode(0);
        orderDb.setExchangeFlag(0);
        verify(mockOrderReturnService).orderCancelWithoutRefund(orderDb, "用呗订单支付失败", "用呗订单支付失败-系统取消", 0L, "system");
        verify(mockOrderService).dealZeroEnjoyPayOrderFail("paySn");
    }
*/
   /* @Test
    void testEnjoyPayCallBack_OrderModelGetOrderListReturnsNoItems() throws Exception {
        // Setup
        final PaymentNotifyVO req = new PaymentNotifyVO();
        req.setOrderOn("paySn");
        req.setPayCode("memberName");
        req.setPayTradeNo("memberName");
        req.setRelPayAmt(new BigDecimal("0.00"));
        req.setPayStatus(0);
        req.setPayWay("payWay");
        req.setBankPayTrxNos(new HashMap<>());
        req.setNewOrder(false);

        final PayInfoExtraInfoVO payInfoExtraInfoVO = new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn");

        // Configure PayService.buildPayNotifyOrderOn(...).
        final PaymentNotifyVO req1 = new PaymentNotifyVO();
        req1.setOrderOn("paySn");
        req1.setPayCode("memberName");
        req1.setPayTradeNo("memberName");
        req1.setRelPayAmt(new BigDecimal("0.00"));
        req1.setPayStatus(0);
        req1.setPayWay("payWay");
        req1.setBankPayTrxNos(new HashMap<>());
        req1.setNewOrder(false);
        when(mockPayService.buildPayNotifyOrderOn(req1,
                new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"), true)).thenReturn("memberName");

        // Configure OrderModel.getOrderByOrderSn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);
        when(mockOrderModel.getOrderByOrderSn("memberName")).thenReturn(orderPO);

        // Configure OrderPayMapper.getByPrimaryKey(...).
        final OrderPayPO orderPayPO = new OrderPayPO();
        orderPayPO.setPayId(0);
        orderPayPO.setPaySn("paySn");
        orderPayPO.setOrderSn("pOrderSn");
        orderPayPO.setPayAmount(new BigDecimal("0.00"));
        orderPayPO.setMemberId(0);
        orderPayPO.setApiPayState("0");
        orderPayPO.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPayPO.setTradeSn("memberName");
        orderPayPO.setPaymentName("paymentName");
        orderPayPO.setPaymentCode("paymentCode");
        orderPayPO.setLoanSuccess(0);
        orderPayPO.setEnjoyPayVipFlag(0);
        orderPayPO.setPayWayExtraInfo(new JSONObject(0, false));
        orderPayPO.setOutBizSource("channel");
        orderPayPO.setOutBizId("paySn");
        when(mockOrderPayMapper.getByPrimaryKey("paySn")).thenReturn(orderPayPO);

        when(mockOrderService.lambdaUpdate()).thenReturn(new LambdaUpdateChainWrapper<>(null));
        when(mockPayService.payAmount("payNo")).thenReturn(new BigDecimal("0.00"));

        // Configure OrderModel.getOrderList(...).
        final OrderExample example = new OrderExample();
        example.setOrderIdNotEquals(0);
        example.setUserNo("userNo");
        example.setUserMobile("userMobile");
        example.setOrderSn("memberName");
        example.setPaySn("paySn");
        when(mockOrderModel.getOrderList(eq(example), any(PagerInfo.class))).thenReturn(Collections.emptyList());

        when(mockOrderPayProcessStrategyContext.getStrategy(0)).thenReturn(null);
        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategory storeBindCategory = new StoreBindCategory();
        storeBindCategory.setBindId(0);
        storeBindCategory.setStoreId(0L);
        storeBindCategory.setCreateVendorId(0L);
        storeBindCategory.setGoodsCategoryId1(0);
        storeBindCategory.setGoodsCategoryId2(0);
        final List<StoreBindCategory> storeBindCategories = Arrays.asList(storeBindCategory);
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                .thenReturn(storeBindCategories);

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        when(mockAccountCardFacade.defaultCard(0L)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure BillOperatinIntegration.detailByBankAccount(...).
        final AccountCard accountCard = new AccountCard();
        accountCard.setCardId(0L);
        accountCard.setStoreId("storeId");
        accountCard.setStoreName("storeName");
        accountCard.setOrgType("orgType");
        accountCard.setBankAccountNumber("bankAccountNumber");
        when(mockBillOperatinIntegration.detailByBankAccount("UIN_PLF_STORE_ID101",
                AccountCardTypeEnum.UNI_JS_PLF_SUP)).thenReturn(accountCard);

        // Configure IBzBankTransferService.list(...).
        final BzBankTransferPO bzBankTransferPO = new BzBankTransferPO();
        bzBankTransferPO.setEnabledFlag(0);
        bzBankTransferPO.setPaySn("paySn");
        bzBankTransferPO.setOutOrderNo("outOrderNo");
        bzBankTransferPO.setPayAmount(new BigDecimal("0.00"));
        bzBankTransferPO.setReceiptAccount("receiptAccount");
        final List<BzBankTransferPO> bzBankTransferPOS = Arrays.asList(bzBankTransferPO);
        when(mockBzBankTransferService.list(any(LambdaQueryWrapper.class))).thenReturn(bzBankTransferPOS);

        // Configure StoreFeignClient.getStoreContractReciptInfo(...).
        final StoreContractReceiptInfoVO storeContractReceiptInfoVO = new StoreContractReceiptInfoVO();
        storeContractReceiptInfoVO.setWxSellerId("wxSellerId");
        storeContractReceiptInfoVO.setAliSellerId("aliSellerId");
        final Store store = new Store();
        store.setAcctId("acctId");
        storeContractReceiptInfoVO.setStore(store);
        storeContractReceiptInfoVO.setRecommentWxSellerId("recommentWxSellerId");
        storeContractReceiptInfoVO.setRecommentAliSellerId("recommentAliSellerId");
        when(mockStoreFeignClient.getStoreContractReciptInfo(0L)).thenReturn(storeContractReceiptInfoVO);

        // Configure CustomerServiceFeign.info(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final BankcardVo bankcardVo = new BankcardVo();
        bankcardVo.setCardId("cardId");
        bankcardVo.setCardNo("cardNo");
        bankcardVo.setAcctName("acctName");
        bankcardVo.setBankCode("bankCode");
        bankcardVo.setBankName("bankName");
        final Result<BankcardVo> bankcardVoResult = new Result<>(false, errorContext, bankcardVo);
        final QueryBankcardInfoReq var1 = new QueryBankcardInfoReq();
        var1.setCardId("cardId");
        when(mockCustomerServiceFeign.info(var1)).thenReturn(bankcardVoResult);

        // Configure StmAccountFacade.detailV2(...).
        final AccountQuery accountQuery = new AccountQuery();
        accountQuery.setCurrent(0);
        accountQuery.setPageSize(0);
        accountQuery.setAccountId("accountId");
        accountQuery.setStoreId("UIN_PLF_STORE_ID101");
        accountQuery.setAccountType("accountType");
        when(mockStmAccountFacade.detailV2(accountQuery)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderOfflineService.queryOrderOfflineList(...).
        final OrderOfflinePO orderOfflinePO = new OrderOfflinePO();
        orderOfflinePO.setEnabledFlag(0);
        orderOfflinePO.setPaySn("paySn");
        orderOfflinePO.setReceiptAccount("receiptAccount");
        orderOfflinePO.setReceiptAmount(new BigDecimal("0.00"));
        orderOfflinePO.setReceiptTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<OrderOfflinePO> orderOfflinePOS = Arrays.asList(orderOfflinePO);
        when(mockOrderOfflineService.queryOrderOfflineList("paySn")).thenReturn(orderOfflinePOS);

        // Configure IOrderService.list(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO1);
        when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(orderPOList);

        when(mockPromotionCommonFeignClient.orderPaySuccess("memberName", 0, "paySn", "memberName", "paymentName",
                "paymentCode")).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderProductMapper.selectOne(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setStoreName("memberName");
        orderProductPO1.setGoodsName("memberName");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setSpellTeamId(0);
        orderProductPO1.setEnabledFlag(0);
        when(mockOrderProductMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderProductPO1);

        // Configure OrderProductMapper.selectList(...).
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setStoreName("memberName");
        orderProductPO2.setGoodsName("memberName");
        orderProductPO2.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO2.setProductNum(0);
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setSpellTeamId(0);
        orderProductPO2.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO2);
        when(mockOrderProductMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);
        final List<OrderPromotionSendCouponPO> orderPromotionSendCouponPOS = Arrays.asList(orderPromotionSendCouponPO);
        final OrderPromotionSendCouponExample example1 = new OrderPromotionSendCouponExample();
        example1.setSendCouponIdNotEquals(0);
        example1.setSendCouponIdIn("sendCouponIdIn");
        example1.setSendCouponId(0);
        example1.setOrderSn("memberName");
        example1.setOrderSnLike("orderSnLike");
        when(mockOrderPromotionSendCouponMapper.listByExample(example1)).thenReturn(orderPromotionSendCouponPOS);

        // Configure CouponFeignClient.getCouponByCouponId(...).
        final CouponVO couponVO = new CouponVO();
        couponVO.setCouponId(0);
        couponVO.setCouponType(0);
        couponVO.setReceivedNum(0);
        couponVO.setRandomMax(new BigDecimal("0.00"));
        couponVO.setRandomMin(new BigDecimal("0.00"));
        couponVO.setPublishNum(0);
        couponVO.setEffectiveTimeType(0);
        couponVO.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setCycle(0);
        couponVO.setState(0);
        couponVO.setStoreId(0L);
        couponVO.setUseType(0);
        when(mockCouponFeignClient.getCouponByCouponId(0)).thenReturn(couponVO);

        // Configure IOrderService.getById(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("memberName");
        orderPO2.setUserNo("userNo");
        orderPO2.setPaySn("paySn");
        orderPO2.setSellerId("sellerId");
        orderPO2.setBankPayTrxNo("bankPayTrxNo");
        orderPO2.setStoreId(0L);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setComposePayName("composeWay");
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setServiceFee(new BigDecimal("0.00"));
        orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO2.setSettleMode("settleMode");
        orderPO2.setFinanceRuleCode("financeRuleCode");
        orderPO2.setIsDelivery(0);
        orderPO2.setChannel("channel");
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setCustomerConfirmStatus(0);
        orderPO2.setOrderPlaceUserRoleCode(0);
        orderPO2.setExchangeFlag(0);
        when(mockOrderService.getById(0)).thenReturn(orderPO2);

        // Configure IOrderAmountStateRecordService.initOrderAmountState(...).
        final ErrorContext errorContext1 = new ErrorContext();
        errorContext1.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext1.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult = new Result<>(false, errorContext1, null);
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("memberName");
        orderPO3.setUserNo("userNo");
        orderPO3.setPaySn("paySn");
        orderPO3.setSellerId("sellerId");
        orderPO3.setBankPayTrxNo("bankPayTrxNo");
        orderPO3.setStoreId(0L);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setComposePayName("composeWay");
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setServiceFee(new BigDecimal("0.00"));
        orderPO3.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO3.setSettleMode("settleMode");
        orderPO3.setFinanceRuleCode("financeRuleCode");
        orderPO3.setIsDelivery(0);
        orderPO3.setChannel("channel");
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setCustomerConfirmStatus(0);
        orderPO3.setOrderPlaceUserRoleCode(0);
        orderPO3.setExchangeFlag(0);
        when(mockOrderAmountRecordService.initOrderAmountState(orderPO3)).thenReturn(voidResult);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO3 = new OrderProductPO();
        orderProductPO3.setOrderProductId(0L);
        orderProductPO3.setOrderSn("orderSn");
        orderProductPO3.setStoreName("memberName");
        orderProductPO3.setGoodsName("memberName");
        orderProductPO3.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO3.setProductNum(0);
        orderProductPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO3.setServiceFee(new BigDecimal("0.00"));
        orderProductPO3.setSpellTeamId(0);
        orderProductPO3.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS1 = Arrays.asList(orderProductPO3);
        when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(orderProductPOS1);

        when(mockIBzOldUserPoolService.insertFirstLoanOrder("userNo", "memberName")).thenReturn(false);

        // Configure IOrderExtendFinanceService.getByOrderSn(...).
        final OrderExtendFinancePO orderExtendFinancePO = new OrderExtendFinancePO();
        orderExtendFinancePO.setFinanceId(0L);
        orderExtendFinancePO.setOrderSn("orderSn");
        orderExtendFinancePO.setInterestWay(0);
        orderExtendFinancePO.setPlanInterestStartDays(0);
        orderExtendFinancePO.setPlanLoanDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockFinanceService.getByOrderSn("memberName")).thenReturn(orderExtendFinancePO);

        // Configure OrderModel.doLoanOperate(...).
        final ErrorContext errorContext2 = new ErrorContext();
        errorContext2.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext2.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult1 = new Result<>(false, errorContext2, null);
        when(mockOrderModel.doLoanOperate("memberName", 0L, "payNo")).thenReturn(voidResult1);

        // Run the test
        orderPayModelUnderTest.enjoyPayCallBack(req, payInfoExtraInfoVO);

        // Verify the results
        // Confirm PayService.paySuccessProcess(...).
        final PaymentNotifyVO req2 = new PaymentNotifyVO();
        req2.setOrderOn("paySn");
        req2.setPayCode("memberName");
        req2.setPayTradeNo("memberName");
        req2.setRelPayAmt(new BigDecimal("0.00"));
        req2.setPayStatus(0);
        req2.setPayWay("payWay");
        req2.setBankPayTrxNos(new HashMap<>());
        req2.setNewOrder(false);
        verify(mockPayService).paySuccessProcess(0, req2,
                new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"), new HashMap<>());
        verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
                new BigDecimal("0.00"));
        verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 20, 0, "订单支付完成",
                OrderCreateChannel.H5);
        verify(mockOrderProductModel).orderPaySuccessAddSales("memberName");
        verify(mockOrderService).setOrdersDeliverable(Arrays.asList("value"));

        // Confirm CouponMemberFeignClient.saveCouponMember(...).
        final CouponMember couponMember = new CouponMember();
        couponMember.setCouponId(0);
        couponMember.setCouponCode("couponCode");
        couponMember.setStoreId(0L);
        couponMember.setMemberId(0);
        couponMember.setMemberName("memberName");
        couponMember.setReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseState(0);
        couponMember.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseType(0);
        couponMember.setRandomAmount(new BigDecimal("0.00"));
        verify(mockCouponMemberFeignClient).saveCouponMember(couponMember);

        // Confirm CouponUseLogFeignClient.saveCouponUseLog(...).
        final CouponUseLog couponUseLog = new CouponUseLog();
        couponUseLog.setCouponCode("couponCode");
        couponUseLog.setMemberId(0);
        couponUseLog.setMemberName("memberName");
        couponUseLog.setStoreId(0L);
        couponUseLog.setLogType(0);
        couponUseLog.setLogTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponUseLog.setLogContent("logContent");
        verify(mockCouponUseLogFeignClient).saveCouponUseLog(couponUseLog);

        // Confirm CouponFeignClient.updateOrderCoupon(...).
        final Coupon coupon = new Coupon();
        coupon.setCouponId(0);
        coupon.setCouponName("couponName");
        coupon.setCouponContent("couponContent");
        coupon.setDescription("description");
        coupon.setReceivedNum(0);
        verify(mockCouponFeignClient).updateOrderCoupon(coupon);

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO4 = new OrderPO();
        orderPO4.setOrderId(0);
        orderPO4.setOrderSn("memberName");
        orderPO4.setUserNo("userNo");
        orderPO4.setPaySn("paySn");
        orderPO4.setSellerId("sellerId");
        orderPO4.setBankPayTrxNo("bankPayTrxNo");
        orderPO4.setStoreId(0L);
        orderPO4.setRecommendStoreId(0L);
        orderPO4.setMemberName("memberName");
        orderPO4.setMemberId(0);
        orderPO4.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setOrderState(0);
        orderPO4.setLoanPayState(0);
        orderPO4.setPaymentName("paymentName");
        orderPO4.setPaymentCode("paymentCode");
        orderPO4.setOrderAmount(new BigDecimal("0.00"));
        orderPO4.setGoodsAmount(new BigDecimal("0.00"));
        orderPO4.setExpressFee(new BigDecimal("0.00"));
        orderPO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO4.setComposePayName("composeWay");
        orderPO4.setBalanceAmount(new BigDecimal("0.00"));
        orderPO4.setPayAmount(new BigDecimal("0.00"));
        orderPO4.setAreaCode("areaCode");
        orderPO4.setOrderType(0);
        orderPO4.setServiceFee(new BigDecimal("0.00"));
        orderPO4.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO4.setSettleMode("settleMode");
        orderPO4.setFinanceRuleCode("financeRuleCode");
        orderPO4.setIsDelivery(0);
        orderPO4.setChannel("channel");
        orderPO4.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO4.setNewOrder(false);
        orderPO4.setCustomerConfirmStatus(0);
        orderPO4.setOrderPlaceUserRoleCode(0);
        orderPO4.setExchangeFlag(0);
        verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO4, OrderEventEnum.CREATE,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg",
                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));
        verify(mockOrderService).dealZeroEnjoyPayOrder("paySn", "payNo");
        verify(mockTaskQueueService).saveTaskQueue(0L, TaskQueueBizTypeEnum.AUTO_LOAN,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "CNBJ0000");

        // Confirm OrderModel.loanPayFail(...).
        final OrderPO orderPODb = new OrderPO();
        orderPODb.setOrderId(0);
        orderPODb.setOrderSn("memberName");
        orderPODb.setUserNo("userNo");
        orderPODb.setPaySn("paySn");
        orderPODb.setSellerId("sellerId");
        orderPODb.setBankPayTrxNo("bankPayTrxNo");
        orderPODb.setStoreId(0L);
        orderPODb.setRecommendStoreId(0L);
        orderPODb.setMemberName("memberName");
        orderPODb.setMemberId(0);
        orderPODb.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPODb.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPODb.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPODb.setOrderState(0);
        orderPODb.setLoanPayState(0);
        orderPODb.setPaymentName("paymentName");
        orderPODb.setPaymentCode("paymentCode");
        orderPODb.setOrderAmount(new BigDecimal("0.00"));
        orderPODb.setGoodsAmount(new BigDecimal("0.00"));
        orderPODb.setExpressFee(new BigDecimal("0.00"));
        orderPODb.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPODb.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPODb.setXzCardAmount(new BigDecimal("0.00"));
        orderPODb.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPODb.setComposePayName("composeWay");
        orderPODb.setBalanceAmount(new BigDecimal("0.00"));
        orderPODb.setPayAmount(new BigDecimal("0.00"));
        orderPODb.setAreaCode("areaCode");
        orderPODb.setOrderType(0);
        orderPODb.setServiceFee(new BigDecimal("0.00"));
        orderPODb.setServiceFeeRate(new BigDecimal("0.00"));
        orderPODb.setSettleMode("settleMode");
        orderPODb.setFinanceRuleCode("financeRuleCode");
        orderPODb.setIsDelivery(0);
        orderPODb.setChannel("channel");
        orderPODb.setChannelServiceFee(new BigDecimal("0.00"));
        orderPODb.setNewOrder(false);
        orderPODb.setCustomerConfirmStatus(0);
        orderPODb.setOrderPlaceUserRoleCode(0);
        orderPODb.setExchangeFlag(0);
        verify(mockOrderModel).loanPayFail(orderPODb, "failureReason");

        // Confirm OrderModel.loanPaySuccess(...).
        final OrderPO orderPO5 = new OrderPO();
        orderPO5.setOrderId(0);
        orderPO5.setOrderSn("memberName");
        orderPO5.setUserNo("userNo");
        orderPO5.setPaySn("paySn");
        orderPO5.setSellerId("sellerId");
        orderPO5.setBankPayTrxNo("bankPayTrxNo");
        orderPO5.setStoreId(0L);
        orderPO5.setRecommendStoreId(0L);
        orderPO5.setMemberName("memberName");
        orderPO5.setMemberId(0);
        orderPO5.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO5.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO5.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO5.setOrderState(0);
        orderPO5.setLoanPayState(0);
        orderPO5.setPaymentName("paymentName");
        orderPO5.setPaymentCode("paymentCode");
        orderPO5.setOrderAmount(new BigDecimal("0.00"));
        orderPO5.setGoodsAmount(new BigDecimal("0.00"));
        orderPO5.setExpressFee(new BigDecimal("0.00"));
        orderPO5.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO5.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO5.setXzCardAmount(new BigDecimal("0.00"));
        orderPO5.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO5.setComposePayName("composeWay");
        orderPO5.setBalanceAmount(new BigDecimal("0.00"));
        orderPO5.setPayAmount(new BigDecimal("0.00"));
        orderPO5.setAreaCode("areaCode");
        orderPO5.setOrderType(0);
        orderPO5.setServiceFee(new BigDecimal("0.00"));
        orderPO5.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO5.setSettleMode("settleMode");
        orderPO5.setFinanceRuleCode("financeRuleCode");
        orderPO5.setIsDelivery(0);
        orderPO5.setChannel("channel");
        orderPO5.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO5.setNewOrder(false);
        orderPO5.setCustomerConfirmStatus(0);
        orderPO5.setOrderPlaceUserRoleCode(0);
        orderPO5.setExchangeFlag(0);
        verify(mockOrderModel).loanPaySuccess(orderPO5, 3, 0L, "memberName");
    }
*/
   /* @Test
    void testEnjoyPayCallBack_StoreBindCategoryFeignClientReturnsNoItems() throws Exception {
        // Setup
        final PaymentNotifyVO req = new PaymentNotifyVO();
        req.setOrderOn("paySn");
        req.setPayCode("memberName");
        req.setPayTradeNo("memberName");
        req.setRelPayAmt(new BigDecimal("0.00"));
        req.setPayStatus(0);
        req.setPayWay("payWay");
        req.setBankPayTrxNos(new HashMap<>());
        req.setNewOrder(false);

        final PayInfoExtraInfoVO payInfoExtraInfoVO = new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn");

        // Configure PayService.buildPayNotifyOrderOn(...).
        final PaymentNotifyVO req1 = new PaymentNotifyVO();
        req1.setOrderOn("paySn");
        req1.setPayCode("memberName");
        req1.setPayTradeNo("memberName");
        req1.setRelPayAmt(new BigDecimal("0.00"));
        req1.setPayStatus(0);
        req1.setPayWay("payWay");
        req1.setBankPayTrxNos(new HashMap<>());
        req1.setNewOrder(false);
        when(mockPayService.buildPayNotifyOrderOn(req1,
                new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"), true)).thenReturn("memberName");

        // Configure OrderModel.getOrderByOrderSn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);
        when(mockOrderModel.getOrderByOrderSn("memberName")).thenReturn(orderPO);

        // Configure OrderPayMapper.getByPrimaryKey(...).
        final OrderPayPO orderPayPO = new OrderPayPO();
        orderPayPO.setPayId(0);
        orderPayPO.setPaySn("paySn");
        orderPayPO.setOrderSn("pOrderSn");
        orderPayPO.setPayAmount(new BigDecimal("0.00"));
        orderPayPO.setMemberId(0);
        orderPayPO.setApiPayState("0");
        orderPayPO.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPayPO.setTradeSn("memberName");
        orderPayPO.setPaymentName("paymentName");
        orderPayPO.setPaymentCode("paymentCode");
        orderPayPO.setLoanSuccess(0);
        orderPayPO.setEnjoyPayVipFlag(0);
        orderPayPO.setPayWayExtraInfo(new JSONObject(0, false));
        orderPayPO.setOutBizSource("channel");
        orderPayPO.setOutBizId("paySn");
        when(mockOrderPayMapper.getByPrimaryKey("paySn")).thenReturn(orderPayPO);

        when(mockOrderService.lambdaUpdate()).thenReturn(new LambdaUpdateChainWrapper<>(null));
        when(mockPayService.payAmount("payNo")).thenReturn(new BigDecimal("0.00"));

        // Configure OrderModel.getOrderList(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO1);
        final OrderExample example = new OrderExample();
        example.setOrderIdNotEquals(0);
        example.setUserNo("userNo");
        example.setUserMobile("userMobile");
        example.setOrderSn("memberName");
        example.setPaySn("paySn");
        when(mockOrderModel.getOrderList(eq(example), any(PagerInfo.class))).thenReturn(orderPOList);

        when(mockOrderPayProcessStrategyContext.getStrategy(0)).thenReturn(null);
        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                .thenReturn(Collections.emptyList());

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        when(mockAccountCardFacade.defaultCard(0L)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure BillOperatinIntegration.detailByBankAccount(...).
        final AccountCard accountCard = new AccountCard();
        accountCard.setCardId(0L);
        accountCard.setStoreId("storeId");
        accountCard.setStoreName("storeName");
        accountCard.setOrgType("orgType");
        accountCard.setBankAccountNumber("bankAccountNumber");
        when(mockBillOperatinIntegration.detailByBankAccount("UIN_PLF_STORE_ID101",
                AccountCardTypeEnum.UNI_JS_PLF_SUP)).thenReturn(accountCard);

        // Configure IBzBankTransferService.list(...).
        final BzBankTransferPO bzBankTransferPO = new BzBankTransferPO();
        bzBankTransferPO.setEnabledFlag(0);
        bzBankTransferPO.setPaySn("paySn");
        bzBankTransferPO.setOutOrderNo("outOrderNo");
        bzBankTransferPO.setPayAmount(new BigDecimal("0.00"));
        bzBankTransferPO.setReceiptAccount("receiptAccount");
        final List<BzBankTransferPO> bzBankTransferPOS = Arrays.asList(bzBankTransferPO);
        when(mockBzBankTransferService.list(any(LambdaQueryWrapper.class))).thenReturn(bzBankTransferPOS);

        // Configure StoreFeignClient.getStoreContractReciptInfo(...).
        final StoreContractReceiptInfoVO storeContractReceiptInfoVO = new StoreContractReceiptInfoVO();
        storeContractReceiptInfoVO.setWxSellerId("wxSellerId");
        storeContractReceiptInfoVO.setAliSellerId("aliSellerId");
        final Store store = new Store();
        store.setAcctId("acctId");
        storeContractReceiptInfoVO.setStore(store);
        storeContractReceiptInfoVO.setRecommentWxSellerId("recommentWxSellerId");
        storeContractReceiptInfoVO.setRecommentAliSellerId("recommentAliSellerId");
        when(mockStoreFeignClient.getStoreContractReciptInfo(0L)).thenReturn(storeContractReceiptInfoVO);

        // Configure CustomerServiceFeign.info(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final BankcardVo bankcardVo = new BankcardVo();
        bankcardVo.setCardId("cardId");
        bankcardVo.setCardNo("cardNo");
        bankcardVo.setAcctName("acctName");
        bankcardVo.setBankCode("bankCode");
        bankcardVo.setBankName("bankName");
        final Result<BankcardVo> bankcardVoResult = new Result<>(false, errorContext, bankcardVo);
        final QueryBankcardInfoReq var1 = new QueryBankcardInfoReq();
        var1.setCardId("cardId");
        when(mockCustomerServiceFeign.info(var1)).thenReturn(bankcardVoResult);

        // Configure StmAccountFacade.detailV2(...).
        final AccountQuery accountQuery = new AccountQuery();
        accountQuery.setCurrent(0);
        accountQuery.setPageSize(0);
        accountQuery.setAccountId("accountId");
        accountQuery.setStoreId("UIN_PLF_STORE_ID101");
        accountQuery.setAccountType("accountType");
        when(mockStmAccountFacade.detailV2(accountQuery)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderOfflineService.queryOrderOfflineList(...).
        final OrderOfflinePO orderOfflinePO = new OrderOfflinePO();
        orderOfflinePO.setEnabledFlag(0);
        orderOfflinePO.setPaySn("paySn");
        orderOfflinePO.setReceiptAccount("receiptAccount");
        orderOfflinePO.setReceiptAmount(new BigDecimal("0.00"));
        orderOfflinePO.setReceiptTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<OrderOfflinePO> orderOfflinePOS = Arrays.asList(orderOfflinePO);
        when(mockOrderOfflineService.queryOrderOfflineList("paySn")).thenReturn(orderOfflinePOS);

        // Configure IOrderService.list(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("memberName");
        orderPO2.setUserNo("userNo");
        orderPO2.setPaySn("paySn");
        orderPO2.setSellerId("sellerId");
        orderPO2.setBankPayTrxNo("bankPayTrxNo");
        orderPO2.setStoreId(0L);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setComposePayName("composeWay");
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setServiceFee(new BigDecimal("0.00"));
        orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO2.setSettleMode("settleMode");
        orderPO2.setFinanceRuleCode("financeRuleCode");
        orderPO2.setIsDelivery(0);
        orderPO2.setChannel("channel");
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setCustomerConfirmStatus(0);
        orderPO2.setOrderPlaceUserRoleCode(0);
        orderPO2.setExchangeFlag(0);
        final List<OrderPO> orderPOList1 = Arrays.asList(orderPO2);
        when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(orderPOList1);

        when(mockPromotionCommonFeignClient.orderPaySuccess("memberName", 0, "paySn", "memberName", "paymentName",
                "paymentCode")).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderProductMapper.selectOne(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setStoreName("memberName");
        orderProductPO1.setGoodsName("memberName");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setSpellTeamId(0);
        orderProductPO1.setEnabledFlag(0);
        when(mockOrderProductMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderProductPO1);

        // Configure OrderProductMapper.selectList(...).
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setStoreName("memberName");
        orderProductPO2.setGoodsName("memberName");
        orderProductPO2.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO2.setProductNum(0);
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setSpellTeamId(0);
        orderProductPO2.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO2);
        when(mockOrderProductMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);
        final List<OrderPromotionSendCouponPO> orderPromotionSendCouponPOS = Arrays.asList(orderPromotionSendCouponPO);
        final OrderPromotionSendCouponExample example1 = new OrderPromotionSendCouponExample();
        example1.setSendCouponIdNotEquals(0);
        example1.setSendCouponIdIn("sendCouponIdIn");
        example1.setSendCouponId(0);
        example1.setOrderSn("memberName");
        example1.setOrderSnLike("orderSnLike");
        when(mockOrderPromotionSendCouponMapper.listByExample(example1)).thenReturn(orderPromotionSendCouponPOS);

        // Configure CouponFeignClient.getCouponByCouponId(...).
        final CouponVO couponVO = new CouponVO();
        couponVO.setCouponId(0);
        couponVO.setCouponType(0);
        couponVO.setReceivedNum(0);
        couponVO.setRandomMax(new BigDecimal("0.00"));
        couponVO.setRandomMin(new BigDecimal("0.00"));
        couponVO.setPublishNum(0);
        couponVO.setEffectiveTimeType(0);
        couponVO.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setCycle(0);
        couponVO.setState(0);
        couponVO.setStoreId(0L);
        couponVO.setUseType(0);
        when(mockCouponFeignClient.getCouponByCouponId(0)).thenReturn(couponVO);

        // Configure IOrderService.getById(...).
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("memberName");
        orderPO3.setUserNo("userNo");
        orderPO3.setPaySn("paySn");
        orderPO3.setSellerId("sellerId");
        orderPO3.setBankPayTrxNo("bankPayTrxNo");
        orderPO3.setStoreId(0L);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setComposePayName("composeWay");
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setServiceFee(new BigDecimal("0.00"));
        orderPO3.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO3.setSettleMode("settleMode");
        orderPO3.setFinanceRuleCode("financeRuleCode");
        orderPO3.setIsDelivery(0);
        orderPO3.setChannel("channel");
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setCustomerConfirmStatus(0);
        orderPO3.setOrderPlaceUserRoleCode(0);
        orderPO3.setExchangeFlag(0);
        when(mockOrderService.getById(0)).thenReturn(orderPO3);

        // Configure IOrderAmountStateRecordService.initOrderAmountState(...).
        final ErrorContext errorContext1 = new ErrorContext();
        errorContext1.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext1.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult = new Result<>(false, errorContext1, null);
        final OrderPO orderPO4 = new OrderPO();
        orderPO4.setOrderId(0);
        orderPO4.setOrderSn("memberName");
        orderPO4.setUserNo("userNo");
        orderPO4.setPaySn("paySn");
        orderPO4.setSellerId("sellerId");
        orderPO4.setBankPayTrxNo("bankPayTrxNo");
        orderPO4.setStoreId(0L);
        orderPO4.setRecommendStoreId(0L);
        orderPO4.setMemberName("memberName");
        orderPO4.setMemberId(0);
        orderPO4.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setOrderState(0);
        orderPO4.setLoanPayState(0);
        orderPO4.setPaymentName("paymentName");
        orderPO4.setPaymentCode("paymentCode");
        orderPO4.setOrderAmount(new BigDecimal("0.00"));
        orderPO4.setGoodsAmount(new BigDecimal("0.00"));
        orderPO4.setExpressFee(new BigDecimal("0.00"));
        orderPO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO4.setComposePayName("composeWay");
        orderPO4.setBalanceAmount(new BigDecimal("0.00"));
        orderPO4.setPayAmount(new BigDecimal("0.00"));
        orderPO4.setAreaCode("areaCode");
        orderPO4.setOrderType(0);
        orderPO4.setServiceFee(new BigDecimal("0.00"));
        orderPO4.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO4.setSettleMode("settleMode");
        orderPO4.setFinanceRuleCode("financeRuleCode");
        orderPO4.setIsDelivery(0);
        orderPO4.setChannel("channel");
        orderPO4.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO4.setNewOrder(false);
        orderPO4.setCustomerConfirmStatus(0);
        orderPO4.setOrderPlaceUserRoleCode(0);
        orderPO4.setExchangeFlag(0);
        when(mockOrderAmountRecordService.initOrderAmountState(orderPO4)).thenReturn(voidResult);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO3 = new OrderProductPO();
        orderProductPO3.setOrderProductId(0L);
        orderProductPO3.setOrderSn("orderSn");
        orderProductPO3.setStoreName("memberName");
        orderProductPO3.setGoodsName("memberName");
        orderProductPO3.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO3.setProductNum(0);
        orderProductPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO3.setServiceFee(new BigDecimal("0.00"));
        orderProductPO3.setSpellTeamId(0);
        orderProductPO3.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS1 = Arrays.asList(orderProductPO3);
        when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(orderProductPOS1);

        when(mockIBzOldUserPoolService.insertFirstLoanOrder("userNo", "memberName")).thenReturn(false);

        // Configure IOrderExtendFinanceService.getByOrderSn(...).
        final OrderExtendFinancePO orderExtendFinancePO = new OrderExtendFinancePO();
        orderExtendFinancePO.setFinanceId(0L);
        orderExtendFinancePO.setOrderSn("orderSn");
        orderExtendFinancePO.setInterestWay(0);
        orderExtendFinancePO.setPlanInterestStartDays(0);
        orderExtendFinancePO.setPlanLoanDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockFinanceService.getByOrderSn("memberName")).thenReturn(orderExtendFinancePO);

        // Configure OrderModel.doLoanOperate(...).
        final ErrorContext errorContext2 = new ErrorContext();
        errorContext2.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext2.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult1 = new Result<>(false, errorContext2, null);
        when(mockOrderModel.doLoanOperate("memberName", 0L, "payNo")).thenReturn(voidResult1);

        // Run the test
        orderPayModelUnderTest.enjoyPayCallBack(req, payInfoExtraInfoVO);

        // Verify the results
        // Confirm PayService.paySuccessProcess(...).
        final PaymentNotifyVO req2 = new PaymentNotifyVO();
        req2.setOrderOn("paySn");
        req2.setPayCode("memberName");
        req2.setPayTradeNo("memberName");
        req2.setRelPayAmt(new BigDecimal("0.00"));
        req2.setPayStatus(0);
        req2.setPayWay("payWay");
        req2.setBankPayTrxNos(new HashMap<>());
        req2.setNewOrder(false);
        verify(mockPayService).paySuccessProcess(0, req2,
                new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"), new HashMap<>());
        verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
                new BigDecimal("0.00"));
        verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 20, 0, "订单支付完成",
                OrderCreateChannel.H5);
        verify(mockOrderProductModel).orderPaySuccessAddSales("memberName");
        verify(mockOrderService).setOrdersDeliverable(Arrays.asList("value"));

        // Confirm CouponMemberFeignClient.saveCouponMember(...).
        final CouponMember couponMember = new CouponMember();
        couponMember.setCouponId(0);
        couponMember.setCouponCode("couponCode");
        couponMember.setStoreId(0L);
        couponMember.setMemberId(0);
        couponMember.setMemberName("memberName");
        couponMember.setReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseState(0);
        couponMember.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseType(0);
        couponMember.setRandomAmount(new BigDecimal("0.00"));
        verify(mockCouponMemberFeignClient).saveCouponMember(couponMember);

        // Confirm CouponUseLogFeignClient.saveCouponUseLog(...).
        final CouponUseLog couponUseLog = new CouponUseLog();
        couponUseLog.setCouponCode("couponCode");
        couponUseLog.setMemberId(0);
        couponUseLog.setMemberName("memberName");
        couponUseLog.setStoreId(0L);
        couponUseLog.setLogType(0);
        couponUseLog.setLogTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponUseLog.setLogContent("logContent");
        verify(mockCouponUseLogFeignClient).saveCouponUseLog(couponUseLog);

        // Confirm CouponFeignClient.updateOrderCoupon(...).
        final Coupon coupon = new Coupon();
        coupon.setCouponId(0);
        coupon.setCouponName("couponName");
        coupon.setCouponContent("couponContent");
        coupon.setDescription("description");
        coupon.setReceivedNum(0);
        verify(mockCouponFeignClient).updateOrderCoupon(coupon);

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO5 = new OrderPO();
        orderPO5.setOrderId(0);
        orderPO5.setOrderSn("memberName");
        orderPO5.setUserNo("userNo");
        orderPO5.setPaySn("paySn");
        orderPO5.setSellerId("sellerId");
        orderPO5.setBankPayTrxNo("bankPayTrxNo");
        orderPO5.setStoreId(0L);
        orderPO5.setRecommendStoreId(0L);
        orderPO5.setMemberName("memberName");
        orderPO5.setMemberId(0);
        orderPO5.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO5.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO5.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO5.setOrderState(0);
        orderPO5.setLoanPayState(0);
        orderPO5.setPaymentName("paymentName");
        orderPO5.setPaymentCode("paymentCode");
        orderPO5.setOrderAmount(new BigDecimal("0.00"));
        orderPO5.setGoodsAmount(new BigDecimal("0.00"));
        orderPO5.setExpressFee(new BigDecimal("0.00"));
        orderPO5.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO5.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO5.setXzCardAmount(new BigDecimal("0.00"));
        orderPO5.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO5.setComposePayName("composeWay");
        orderPO5.setBalanceAmount(new BigDecimal("0.00"));
        orderPO5.setPayAmount(new BigDecimal("0.00"));
        orderPO5.setAreaCode("areaCode");
        orderPO5.setOrderType(0);
        orderPO5.setServiceFee(new BigDecimal("0.00"));
        orderPO5.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO5.setSettleMode("settleMode");
        orderPO5.setFinanceRuleCode("financeRuleCode");
        orderPO5.setIsDelivery(0);
        orderPO5.setChannel("channel");
        orderPO5.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO5.setNewOrder(false);
        orderPO5.setCustomerConfirmStatus(0);
        orderPO5.setOrderPlaceUserRoleCode(0);
        orderPO5.setExchangeFlag(0);
        verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO5, OrderEventEnum.CREATE,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg",
                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));
        verify(mockOrderService).dealZeroEnjoyPayOrder("paySn", "payNo");
        verify(mockTaskQueueService).saveTaskQueue(0L, TaskQueueBizTypeEnum.AUTO_LOAN,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "CNBJ0000");

        // Confirm OrderModel.loanPayFail(...).
        final OrderPO orderPODb = new OrderPO();
        orderPODb.setOrderId(0);
        orderPODb.setOrderSn("memberName");
        orderPODb.setUserNo("userNo");
        orderPODb.setPaySn("paySn");
        orderPODb.setSellerId("sellerId");
        orderPODb.setBankPayTrxNo("bankPayTrxNo");
        orderPODb.setStoreId(0L);
        orderPODb.setRecommendStoreId(0L);
        orderPODb.setMemberName("memberName");
        orderPODb.setMemberId(0);
        orderPODb.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPODb.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPODb.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPODb.setOrderState(0);
        orderPODb.setLoanPayState(0);
        orderPODb.setPaymentName("paymentName");
        orderPODb.setPaymentCode("paymentCode");
        orderPODb.setOrderAmount(new BigDecimal("0.00"));
        orderPODb.setGoodsAmount(new BigDecimal("0.00"));
        orderPODb.setExpressFee(new BigDecimal("0.00"));
        orderPODb.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPODb.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPODb.setXzCardAmount(new BigDecimal("0.00"));
        orderPODb.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPODb.setComposePayName("composeWay");
        orderPODb.setBalanceAmount(new BigDecimal("0.00"));
        orderPODb.setPayAmount(new BigDecimal("0.00"));
        orderPODb.setAreaCode("areaCode");
        orderPODb.setOrderType(0);
        orderPODb.setServiceFee(new BigDecimal("0.00"));
        orderPODb.setServiceFeeRate(new BigDecimal("0.00"));
        orderPODb.setSettleMode("settleMode");
        orderPODb.setFinanceRuleCode("financeRuleCode");
        orderPODb.setIsDelivery(0);
        orderPODb.setChannel("channel");
        orderPODb.setChannelServiceFee(new BigDecimal("0.00"));
        orderPODb.setNewOrder(false);
        orderPODb.setCustomerConfirmStatus(0);
        orderPODb.setOrderPlaceUserRoleCode(0);
        orderPODb.setExchangeFlag(0);
        verify(mockOrderModel).loanPayFail(orderPODb, "failureReason");

        // Confirm OrderModel.loanPaySuccess(...).
        final OrderPO orderPO6 = new OrderPO();
        orderPO6.setOrderId(0);
        orderPO6.setOrderSn("memberName");
        orderPO6.setUserNo("userNo");
        orderPO6.setPaySn("paySn");
        orderPO6.setSellerId("sellerId");
        orderPO6.setBankPayTrxNo("bankPayTrxNo");
        orderPO6.setStoreId(0L);
        orderPO6.setRecommendStoreId(0L);
        orderPO6.setMemberName("memberName");
        orderPO6.setMemberId(0);
        orderPO6.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO6.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO6.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO6.setOrderState(0);
        orderPO6.setLoanPayState(0);
        orderPO6.setPaymentName("paymentName");
        orderPO6.setPaymentCode("paymentCode");
        orderPO6.setOrderAmount(new BigDecimal("0.00"));
        orderPO6.setGoodsAmount(new BigDecimal("0.00"));
        orderPO6.setExpressFee(new BigDecimal("0.00"));
        orderPO6.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO6.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO6.setXzCardAmount(new BigDecimal("0.00"));
        orderPO6.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO6.setComposePayName("composeWay");
        orderPO6.setBalanceAmount(new BigDecimal("0.00"));
        orderPO6.setPayAmount(new BigDecimal("0.00"));
        orderPO6.setAreaCode("areaCode");
        orderPO6.setOrderType(0);
        orderPO6.setServiceFee(new BigDecimal("0.00"));
        orderPO6.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO6.setSettleMode("settleMode");
        orderPO6.setFinanceRuleCode("financeRuleCode");
        orderPO6.setIsDelivery(0);
        orderPO6.setChannel("channel");
        orderPO6.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO6.setNewOrder(false);
        orderPO6.setCustomerConfirmStatus(0);
        orderPO6.setOrderPlaceUserRoleCode(0);
        orderPO6.setExchangeFlag(0);
        verify(mockOrderModel).loanPaySuccess(orderPO6, 3, 0L, "memberName");
    }
*/
   /* @Test
    void testEnjoyPayCallBack_IBzBankTransferServiceReturnsNoItems() throws Exception {
        // Setup
        final PaymentNotifyVO req = new PaymentNotifyVO();
        req.setOrderOn("paySn");
        req.setPayCode("memberName");
        req.setPayTradeNo("memberName");
        req.setRelPayAmt(new BigDecimal("0.00"));
        req.setPayStatus(0);
        req.setPayWay("payWay");
        req.setBankPayTrxNos(new HashMap<>());
        req.setNewOrder(false);

        final PayInfoExtraInfoVO payInfoExtraInfoVO = new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn");

        // Configure PayService.buildPayNotifyOrderOn(...).
        final PaymentNotifyVO req1 = new PaymentNotifyVO();
        req1.setOrderOn("paySn");
        req1.setPayCode("memberName");
        req1.setPayTradeNo("memberName");
        req1.setRelPayAmt(new BigDecimal("0.00"));
        req1.setPayStatus(0);
        req1.setPayWay("payWay");
        req1.setBankPayTrxNos(new HashMap<>());
        req1.setNewOrder(false);
        when(mockPayService.buildPayNotifyOrderOn(req1,
                new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"), true)).thenReturn("memberName");

        // Configure OrderModel.getOrderByOrderSn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);
        when(mockOrderModel.getOrderByOrderSn("memberName")).thenReturn(orderPO);

        // Configure OrderPayMapper.getByPrimaryKey(...).
        final OrderPayPO orderPayPO = new OrderPayPO();
        orderPayPO.setPayId(0);
        orderPayPO.setPaySn("paySn");
        orderPayPO.setOrderSn("pOrderSn");
        orderPayPO.setPayAmount(new BigDecimal("0.00"));
        orderPayPO.setMemberId(0);
        orderPayPO.setApiPayState("0");
        orderPayPO.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPayPO.setTradeSn("memberName");
        orderPayPO.setPaymentName("paymentName");
        orderPayPO.setPaymentCode("paymentCode");
        orderPayPO.setLoanSuccess(0);
        orderPayPO.setEnjoyPayVipFlag(0);
        orderPayPO.setPayWayExtraInfo(new JSONObject(0, false));
        orderPayPO.setOutBizSource("channel");
        orderPayPO.setOutBizId("paySn");
        when(mockOrderPayMapper.getByPrimaryKey("paySn")).thenReturn(orderPayPO);

        when(mockOrderService.lambdaUpdate()).thenReturn(new LambdaUpdateChainWrapper<>(null));
        when(mockPayService.payAmount("payNo")).thenReturn(new BigDecimal("0.00"));

        // Configure OrderModel.getOrderList(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO1);
        final OrderExample example = new OrderExample();
        example.setOrderIdNotEquals(0);
        example.setUserNo("userNo");
        example.setUserMobile("userMobile");
        example.setOrderSn("memberName");
        example.setPaySn("paySn");
        when(mockOrderModel.getOrderList(eq(example), any(PagerInfo.class))).thenReturn(orderPOList);

        when(mockOrderPayProcessStrategyContext.getStrategy(0)).thenReturn(null);
        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategory storeBindCategory = new StoreBindCategory();
        storeBindCategory.setBindId(0);
        storeBindCategory.setStoreId(0L);
        storeBindCategory.setCreateVendorId(0L);
        storeBindCategory.setGoodsCategoryId1(0);
        storeBindCategory.setGoodsCategoryId2(0);
        final List<StoreBindCategory> storeBindCategories = Arrays.asList(storeBindCategory);
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                .thenReturn(storeBindCategories);

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        when(mockBzBankTransferService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure IOrderService.list(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("memberName");
        orderPO2.setUserNo("userNo");
        orderPO2.setPaySn("paySn");
        orderPO2.setSellerId("sellerId");
        orderPO2.setBankPayTrxNo("bankPayTrxNo");
        orderPO2.setStoreId(0L);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setComposePayName("composeWay");
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setServiceFee(new BigDecimal("0.00"));
        orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO2.setSettleMode("settleMode");
        orderPO2.setFinanceRuleCode("financeRuleCode");
        orderPO2.setIsDelivery(0);
        orderPO2.setChannel("channel");
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setCustomerConfirmStatus(0);
        orderPO2.setOrderPlaceUserRoleCode(0);
        orderPO2.setExchangeFlag(0);
        final List<OrderPO> orderPOList1 = Arrays.asList(orderPO2);
        when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(orderPOList1);

        when(mockPromotionCommonFeignClient.orderPaySuccess("memberName", 0, "paySn", "memberName", "paymentName",
                "paymentCode")).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderProductMapper.selectOne(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setStoreName("memberName");
        orderProductPO1.setGoodsName("memberName");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setSpellTeamId(0);
        orderProductPO1.setEnabledFlag(0);
        when(mockOrderProductMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderProductPO1);

        // Configure OrderProductMapper.selectList(...).
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setStoreName("memberName");
        orderProductPO2.setGoodsName("memberName");
        orderProductPO2.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO2.setProductNum(0);
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setSpellTeamId(0);
        orderProductPO2.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO2);
        when(mockOrderProductMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);
        final List<OrderPromotionSendCouponPO> orderPromotionSendCouponPOS = Arrays.asList(orderPromotionSendCouponPO);
        final OrderPromotionSendCouponExample example1 = new OrderPromotionSendCouponExample();
        example1.setSendCouponIdNotEquals(0);
        example1.setSendCouponIdIn("sendCouponIdIn");
        example1.setSendCouponId(0);
        example1.setOrderSn("memberName");
        example1.setOrderSnLike("orderSnLike");
        when(mockOrderPromotionSendCouponMapper.listByExample(example1)).thenReturn(orderPromotionSendCouponPOS);

        // Configure CouponFeignClient.getCouponByCouponId(...).
        final CouponVO couponVO = new CouponVO();
        couponVO.setCouponId(0);
        couponVO.setCouponType(0);
        couponVO.setReceivedNum(0);
        couponVO.setRandomMax(new BigDecimal("0.00"));
        couponVO.setRandomMin(new BigDecimal("0.00"));
        couponVO.setPublishNum(0);
        couponVO.setEffectiveTimeType(0);
        couponVO.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setCycle(0);
        couponVO.setState(0);
        couponVO.setStoreId(0L);
        couponVO.setUseType(0);
        when(mockCouponFeignClient.getCouponByCouponId(0)).thenReturn(couponVO);

        // Configure IOrderService.getById(...).
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("memberName");
        orderPO3.setUserNo("userNo");
        orderPO3.setPaySn("paySn");
        orderPO3.setSellerId("sellerId");
        orderPO3.setBankPayTrxNo("bankPayTrxNo");
        orderPO3.setStoreId(0L);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setComposePayName("composeWay");
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setServiceFee(new BigDecimal("0.00"));
        orderPO3.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO3.setSettleMode("settleMode");
        orderPO3.setFinanceRuleCode("financeRuleCode");
        orderPO3.setIsDelivery(0);
        orderPO3.setChannel("channel");
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setCustomerConfirmStatus(0);
        orderPO3.setOrderPlaceUserRoleCode(0);
        orderPO3.setExchangeFlag(0);
        when(mockOrderService.getById(0)).thenReturn(orderPO3);

        // Configure IOrderAmountStateRecordService.initOrderAmountState(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult = new Result<>(false, errorContext, null);
        final OrderPO orderPO4 = new OrderPO();
        orderPO4.setOrderId(0);
        orderPO4.setOrderSn("memberName");
        orderPO4.setUserNo("userNo");
        orderPO4.setPaySn("paySn");
        orderPO4.setSellerId("sellerId");
        orderPO4.setBankPayTrxNo("bankPayTrxNo");
        orderPO4.setStoreId(0L);
        orderPO4.setRecommendStoreId(0L);
        orderPO4.setMemberName("memberName");
        orderPO4.setMemberId(0);
        orderPO4.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setOrderState(0);
        orderPO4.setLoanPayState(0);
        orderPO4.setPaymentName("paymentName");
        orderPO4.setPaymentCode("paymentCode");
        orderPO4.setOrderAmount(new BigDecimal("0.00"));
        orderPO4.setGoodsAmount(new BigDecimal("0.00"));
        orderPO4.setExpressFee(new BigDecimal("0.00"));
        orderPO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO4.setComposePayName("composeWay");
        orderPO4.setBalanceAmount(new BigDecimal("0.00"));
        orderPO4.setPayAmount(new BigDecimal("0.00"));
        orderPO4.setAreaCode("areaCode");
        orderPO4.setOrderType(0);
        orderPO4.setServiceFee(new BigDecimal("0.00"));
        orderPO4.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO4.setSettleMode("settleMode");
        orderPO4.setFinanceRuleCode("financeRuleCode");
        orderPO4.setIsDelivery(0);
        orderPO4.setChannel("channel");
        orderPO4.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO4.setNewOrder(false);
        orderPO4.setCustomerConfirmStatus(0);
        orderPO4.setOrderPlaceUserRoleCode(0);
        orderPO4.setExchangeFlag(0);
        when(mockOrderAmountRecordService.initOrderAmountState(orderPO4)).thenReturn(voidResult);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO3 = new OrderProductPO();
        orderProductPO3.setOrderProductId(0L);
        orderProductPO3.setOrderSn("orderSn");
        orderProductPO3.setStoreName("memberName");
        orderProductPO3.setGoodsName("memberName");
        orderProductPO3.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO3.setProductNum(0);
        orderProductPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO3.setServiceFee(new BigDecimal("0.00"));
        orderProductPO3.setSpellTeamId(0);
        orderProductPO3.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS1 = Arrays.asList(orderProductPO3);
        when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(orderProductPOS1);

        when(mockIBzOldUserPoolService.insertFirstLoanOrder("userNo", "memberName")).thenReturn(false);

        // Configure IOrderExtendFinanceService.getByOrderSn(...).
        final OrderExtendFinancePO orderExtendFinancePO = new OrderExtendFinancePO();
        orderExtendFinancePO.setFinanceId(0L);
        orderExtendFinancePO.setOrderSn("orderSn");
        orderExtendFinancePO.setInterestWay(0);
        orderExtendFinancePO.setPlanInterestStartDays(0);
        orderExtendFinancePO.setPlanLoanDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockFinanceService.getByOrderSn("memberName")).thenReturn(orderExtendFinancePO);

        // Configure OrderModel.doLoanOperate(...).
        final ErrorContext errorContext1 = new ErrorContext();
        errorContext1.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext1.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult1 = new Result<>(false, errorContext1, null);
        when(mockOrderModel.doLoanOperate("memberName", 0L, "payNo")).thenReturn(voidResult1);

        // Run the test
        orderPayModelUnderTest.enjoyPayCallBack(req, payInfoExtraInfoVO);

        // Verify the results
        // Confirm PayService.paySuccessProcess(...).
        final PaymentNotifyVO req2 = new PaymentNotifyVO();
        req2.setOrderOn("paySn");
        req2.setPayCode("memberName");
        req2.setPayTradeNo("memberName");
        req2.setRelPayAmt(new BigDecimal("0.00"));
        req2.setPayStatus(0);
        req2.setPayWay("payWay");
        req2.setBankPayTrxNos(new HashMap<>());
        req2.setNewOrder(false);
        verify(mockPayService).paySuccessProcess(0, req2,
                new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"), new HashMap<>());
        verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
                new BigDecimal("0.00"));
        verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 20, 0, "订单支付完成",
                OrderCreateChannel.H5);
        verify(mockOrderProductModel).orderPaySuccessAddSales("memberName");
        verify(mockOrderService).setOrdersDeliverable(Arrays.asList("value"));

        // Confirm CouponMemberFeignClient.saveCouponMember(...).
        final CouponMember couponMember = new CouponMember();
        couponMember.setCouponId(0);
        couponMember.setCouponCode("couponCode");
        couponMember.setStoreId(0L);
        couponMember.setMemberId(0);
        couponMember.setMemberName("memberName");
        couponMember.setReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseState(0);
        couponMember.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseType(0);
        couponMember.setRandomAmount(new BigDecimal("0.00"));
        verify(mockCouponMemberFeignClient).saveCouponMember(couponMember);

        // Confirm CouponUseLogFeignClient.saveCouponUseLog(...).
        final CouponUseLog couponUseLog = new CouponUseLog();
        couponUseLog.setCouponCode("couponCode");
        couponUseLog.setMemberId(0);
        couponUseLog.setMemberName("memberName");
        couponUseLog.setStoreId(0L);
        couponUseLog.setLogType(0);
        couponUseLog.setLogTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponUseLog.setLogContent("logContent");
        verify(mockCouponUseLogFeignClient).saveCouponUseLog(couponUseLog);

        // Confirm CouponFeignClient.updateOrderCoupon(...).
        final Coupon coupon = new Coupon();
        coupon.setCouponId(0);
        coupon.setCouponName("couponName");
        coupon.setCouponContent("couponContent");
        coupon.setDescription("description");
        coupon.setReceivedNum(0);
        verify(mockCouponFeignClient).updateOrderCoupon(coupon);

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO5 = new OrderPO();
        orderPO5.setOrderId(0);
        orderPO5.setOrderSn("memberName");
        orderPO5.setUserNo("userNo");
        orderPO5.setPaySn("paySn");
        orderPO5.setSellerId("sellerId");
        orderPO5.setBankPayTrxNo("bankPayTrxNo");
        orderPO5.setStoreId(0L);
        orderPO5.setRecommendStoreId(0L);
        orderPO5.setMemberName("memberName");
        orderPO5.setMemberId(0);
        orderPO5.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO5.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO5.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO5.setOrderState(0);
        orderPO5.setLoanPayState(0);
        orderPO5.setPaymentName("paymentName");
        orderPO5.setPaymentCode("paymentCode");
        orderPO5.setOrderAmount(new BigDecimal("0.00"));
        orderPO5.setGoodsAmount(new BigDecimal("0.00"));
        orderPO5.setExpressFee(new BigDecimal("0.00"));
        orderPO5.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO5.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO5.setXzCardAmount(new BigDecimal("0.00"));
        orderPO5.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO5.setComposePayName("composeWay");
        orderPO5.setBalanceAmount(new BigDecimal("0.00"));
        orderPO5.setPayAmount(new BigDecimal("0.00"));
        orderPO5.setAreaCode("areaCode");
        orderPO5.setOrderType(0);
        orderPO5.setServiceFee(new BigDecimal("0.00"));
        orderPO5.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO5.setSettleMode("settleMode");
        orderPO5.setFinanceRuleCode("financeRuleCode");
        orderPO5.setIsDelivery(0);
        orderPO5.setChannel("channel");
        orderPO5.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO5.setNewOrder(false);
        orderPO5.setCustomerConfirmStatus(0);
        orderPO5.setOrderPlaceUserRoleCode(0);
        orderPO5.setExchangeFlag(0);
        verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO5, OrderEventEnum.CREATE,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg",
                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));
        verify(mockOrderService).dealZeroEnjoyPayOrder("paySn", "payNo");
        verify(mockTaskQueueService).saveTaskQueue(0L, TaskQueueBizTypeEnum.AUTO_LOAN,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "CNBJ0000");

        // Confirm OrderModel.loanPayFail(...).
        final OrderPO orderPODb = new OrderPO();
        orderPODb.setOrderId(0);
        orderPODb.setOrderSn("memberName");
        orderPODb.setUserNo("userNo");
        orderPODb.setPaySn("paySn");
        orderPODb.setSellerId("sellerId");
        orderPODb.setBankPayTrxNo("bankPayTrxNo");
        orderPODb.setStoreId(0L);
        orderPODb.setRecommendStoreId(0L);
        orderPODb.setMemberName("memberName");
        orderPODb.setMemberId(0);
        orderPODb.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPODb.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPODb.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPODb.setOrderState(0);
        orderPODb.setLoanPayState(0);
        orderPODb.setPaymentName("paymentName");
        orderPODb.setPaymentCode("paymentCode");
        orderPODb.setOrderAmount(new BigDecimal("0.00"));
        orderPODb.setGoodsAmount(new BigDecimal("0.00"));
        orderPODb.setExpressFee(new BigDecimal("0.00"));
        orderPODb.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPODb.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPODb.setXzCardAmount(new BigDecimal("0.00"));
        orderPODb.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPODb.setComposePayName("composeWay");
        orderPODb.setBalanceAmount(new BigDecimal("0.00"));
        orderPODb.setPayAmount(new BigDecimal("0.00"));
        orderPODb.setAreaCode("areaCode");
        orderPODb.setOrderType(0);
        orderPODb.setServiceFee(new BigDecimal("0.00"));
        orderPODb.setServiceFeeRate(new BigDecimal("0.00"));
        orderPODb.setSettleMode("settleMode");
        orderPODb.setFinanceRuleCode("financeRuleCode");
        orderPODb.setIsDelivery(0);
        orderPODb.setChannel("channel");
        orderPODb.setChannelServiceFee(new BigDecimal("0.00"));
        orderPODb.setNewOrder(false);
        orderPODb.setCustomerConfirmStatus(0);
        orderPODb.setOrderPlaceUserRoleCode(0);
        orderPODb.setExchangeFlag(0);
        verify(mockOrderModel).loanPayFail(orderPODb, "failureReason");

        // Confirm OrderModel.loanPaySuccess(...).
        final OrderPO orderPO6 = new OrderPO();
        orderPO6.setOrderId(0);
        orderPO6.setOrderSn("memberName");
        orderPO6.setUserNo("userNo");
        orderPO6.setPaySn("paySn");
        orderPO6.setSellerId("sellerId");
        orderPO6.setBankPayTrxNo("bankPayTrxNo");
        orderPO6.setStoreId(0L);
        orderPO6.setRecommendStoreId(0L);
        orderPO6.setMemberName("memberName");
        orderPO6.setMemberId(0);
        orderPO6.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO6.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO6.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO6.setOrderState(0);
        orderPO6.setLoanPayState(0);
        orderPO6.setPaymentName("paymentName");
        orderPO6.setPaymentCode("paymentCode");
        orderPO6.setOrderAmount(new BigDecimal("0.00"));
        orderPO6.setGoodsAmount(new BigDecimal("0.00"));
        orderPO6.setExpressFee(new BigDecimal("0.00"));
        orderPO6.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO6.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO6.setXzCardAmount(new BigDecimal("0.00"));
        orderPO6.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO6.setComposePayName("composeWay");
        orderPO6.setBalanceAmount(new BigDecimal("0.00"));
        orderPO6.setPayAmount(new BigDecimal("0.00"));
        orderPO6.setAreaCode("areaCode");
        orderPO6.setOrderType(0);
        orderPO6.setServiceFee(new BigDecimal("0.00"));
        orderPO6.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO6.setSettleMode("settleMode");
        orderPO6.setFinanceRuleCode("financeRuleCode");
        orderPO6.setIsDelivery(0);
        orderPO6.setChannel("channel");
        orderPO6.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO6.setNewOrder(false);
        orderPO6.setCustomerConfirmStatus(0);
        orderPO6.setOrderPlaceUserRoleCode(0);
        orderPO6.setExchangeFlag(0);
        verify(mockOrderModel).loanPaySuccess(orderPO6, 3, 0L, "memberName");
    }
*/
   /* @Test
    void testEnjoyPayCallBack_OrderOfflineServiceReturnsNoItems() throws Exception {
        // Setup
        final PaymentNotifyVO req = new PaymentNotifyVO();
        req.setOrderOn("paySn");
        req.setPayCode("memberName");
        req.setPayTradeNo("memberName");
        req.setRelPayAmt(new BigDecimal("0.00"));
        req.setPayStatus(0);
        req.setPayWay("payWay");
        req.setBankPayTrxNos(new HashMap<>());
        req.setNewOrder(false);

        final PayInfoExtraInfoVO payInfoExtraInfoVO = new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn");

        // Configure PayService.buildPayNotifyOrderOn(...).
        final PaymentNotifyVO req1 = new PaymentNotifyVO();
        req1.setOrderOn("paySn");
        req1.setPayCode("memberName");
        req1.setPayTradeNo("memberName");
        req1.setRelPayAmt(new BigDecimal("0.00"));
        req1.setPayStatus(0);
        req1.setPayWay("payWay");
        req1.setBankPayTrxNos(new HashMap<>());
        req1.setNewOrder(false);
        when(mockPayService.buildPayNotifyOrderOn(req1,
                new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"), true)).thenReturn("memberName");

        // Configure OrderModel.getOrderByOrderSn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);
        when(mockOrderModel.getOrderByOrderSn("memberName")).thenReturn(orderPO);

        // Configure OrderPayMapper.getByPrimaryKey(...).
        final OrderPayPO orderPayPO = new OrderPayPO();
        orderPayPO.setPayId(0);
        orderPayPO.setPaySn("paySn");
        orderPayPO.setOrderSn("pOrderSn");
        orderPayPO.setPayAmount(new BigDecimal("0.00"));
        orderPayPO.setMemberId(0);
        orderPayPO.setApiPayState("0");
        orderPayPO.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPayPO.setTradeSn("memberName");
        orderPayPO.setPaymentName("paymentName");
        orderPayPO.setPaymentCode("paymentCode");
        orderPayPO.setLoanSuccess(0);
        orderPayPO.setEnjoyPayVipFlag(0);
        orderPayPO.setPayWayExtraInfo(new JSONObject(0, false));
        orderPayPO.setOutBizSource("channel");
        orderPayPO.setOutBizId("paySn");
        when(mockOrderPayMapper.getByPrimaryKey("paySn")).thenReturn(orderPayPO);

        when(mockOrderService.lambdaUpdate()).thenReturn(new LambdaUpdateChainWrapper<>(null));
        when(mockPayService.payAmount("payNo")).thenReturn(new BigDecimal("0.00"));

        // Configure OrderModel.getOrderList(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO1);
        final OrderExample example = new OrderExample();
        example.setOrderIdNotEquals(0);
        example.setUserNo("userNo");
        example.setUserMobile("userMobile");
        example.setOrderSn("memberName");
        example.setPaySn("paySn");
        when(mockOrderModel.getOrderList(eq(example), any(PagerInfo.class))).thenReturn(orderPOList);

        when(mockOrderPayProcessStrategyContext.getStrategy(0)).thenReturn(null);
        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategory storeBindCategory = new StoreBindCategory();
        storeBindCategory.setBindId(0);
        storeBindCategory.setStoreId(0L);
        storeBindCategory.setCreateVendorId(0L);
        storeBindCategory.setGoodsCategoryId1(0);
        storeBindCategory.setGoodsCategoryId2(0);
        final List<StoreBindCategory> storeBindCategories = Arrays.asList(storeBindCategory);
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                .thenReturn(storeBindCategories);

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        when(mockOrderOfflineService.queryOrderOfflineList("paySn")).thenReturn(Collections.emptyList());

        // Configure IOrderService.list(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("memberName");
        orderPO2.setUserNo("userNo");
        orderPO2.setPaySn("paySn");
        orderPO2.setSellerId("sellerId");
        orderPO2.setBankPayTrxNo("bankPayTrxNo");
        orderPO2.setStoreId(0L);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setComposePayName("composeWay");
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setServiceFee(new BigDecimal("0.00"));
        orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO2.setSettleMode("settleMode");
        orderPO2.setFinanceRuleCode("financeRuleCode");
        orderPO2.setIsDelivery(0);
        orderPO2.setChannel("channel");
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setCustomerConfirmStatus(0);
        orderPO2.setOrderPlaceUserRoleCode(0);
        orderPO2.setExchangeFlag(0);
        final List<OrderPO> orderPOList1 = Arrays.asList(orderPO2);
        when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(orderPOList1);

        when(mockPromotionCommonFeignClient.orderPaySuccess("memberName", 0, "paySn", "memberName", "paymentName",
                "paymentCode")).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderProductMapper.selectOne(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setStoreName("memberName");
        orderProductPO1.setGoodsName("memberName");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setSpellTeamId(0);
        orderProductPO1.setEnabledFlag(0);
        when(mockOrderProductMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderProductPO1);

        // Configure OrderProductMapper.selectList(...).
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setStoreName("memberName");
        orderProductPO2.setGoodsName("memberName");
        orderProductPO2.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO2.setProductNum(0);
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setSpellTeamId(0);
        orderProductPO2.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO2);
        when(mockOrderProductMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);
        final List<OrderPromotionSendCouponPO> orderPromotionSendCouponPOS = Arrays.asList(orderPromotionSendCouponPO);
        final OrderPromotionSendCouponExample example1 = new OrderPromotionSendCouponExample();
        example1.setSendCouponIdNotEquals(0);
        example1.setSendCouponIdIn("sendCouponIdIn");
        example1.setSendCouponId(0);
        example1.setOrderSn("memberName");
        example1.setOrderSnLike("orderSnLike");
        when(mockOrderPromotionSendCouponMapper.listByExample(example1)).thenReturn(orderPromotionSendCouponPOS);

        // Configure CouponFeignClient.getCouponByCouponId(...).
        final CouponVO couponVO = new CouponVO();
        couponVO.setCouponId(0);
        couponVO.setCouponType(0);
        couponVO.setReceivedNum(0);
        couponVO.setRandomMax(new BigDecimal("0.00"));
        couponVO.setRandomMin(new BigDecimal("0.00"));
        couponVO.setPublishNum(0);
        couponVO.setEffectiveTimeType(0);
        couponVO.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setCycle(0);
        couponVO.setState(0);
        couponVO.setStoreId(0L);
        couponVO.setUseType(0);
        when(mockCouponFeignClient.getCouponByCouponId(0)).thenReturn(couponVO);

        // Configure IOrderService.getById(...).
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("memberName");
        orderPO3.setUserNo("userNo");
        orderPO3.setPaySn("paySn");
        orderPO3.setSellerId("sellerId");
        orderPO3.setBankPayTrxNo("bankPayTrxNo");
        orderPO3.setStoreId(0L);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setComposePayName("composeWay");
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setServiceFee(new BigDecimal("0.00"));
        orderPO3.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO3.setSettleMode("settleMode");
        orderPO3.setFinanceRuleCode("financeRuleCode");
        orderPO3.setIsDelivery(0);
        orderPO3.setChannel("channel");
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setCustomerConfirmStatus(0);
        orderPO3.setOrderPlaceUserRoleCode(0);
        orderPO3.setExchangeFlag(0);
        when(mockOrderService.getById(0)).thenReturn(orderPO3);

        // Configure IOrderAmountStateRecordService.initOrderAmountState(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult = new Result<>(false, errorContext, null);
        final OrderPO orderPO4 = new OrderPO();
        orderPO4.setOrderId(0);
        orderPO4.setOrderSn("memberName");
        orderPO4.setUserNo("userNo");
        orderPO4.setPaySn("paySn");
        orderPO4.setSellerId("sellerId");
        orderPO4.setBankPayTrxNo("bankPayTrxNo");
        orderPO4.setStoreId(0L);
        orderPO4.setRecommendStoreId(0L);
        orderPO4.setMemberName("memberName");
        orderPO4.setMemberId(0);
        orderPO4.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setOrderState(0);
        orderPO4.setLoanPayState(0);
        orderPO4.setPaymentName("paymentName");
        orderPO4.setPaymentCode("paymentCode");
        orderPO4.setOrderAmount(new BigDecimal("0.00"));
        orderPO4.setGoodsAmount(new BigDecimal("0.00"));
        orderPO4.setExpressFee(new BigDecimal("0.00"));
        orderPO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO4.setComposePayName("composeWay");
        orderPO4.setBalanceAmount(new BigDecimal("0.00"));
        orderPO4.setPayAmount(new BigDecimal("0.00"));
        orderPO4.setAreaCode("areaCode");
        orderPO4.setOrderType(0);
        orderPO4.setServiceFee(new BigDecimal("0.00"));
        orderPO4.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO4.setSettleMode("settleMode");
        orderPO4.setFinanceRuleCode("financeRuleCode");
        orderPO4.setIsDelivery(0);
        orderPO4.setChannel("channel");
        orderPO4.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO4.setNewOrder(false);
        orderPO4.setCustomerConfirmStatus(0);
        orderPO4.setOrderPlaceUserRoleCode(0);
        orderPO4.setExchangeFlag(0);
        when(mockOrderAmountRecordService.initOrderAmountState(orderPO4)).thenReturn(voidResult);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO3 = new OrderProductPO();
        orderProductPO3.setOrderProductId(0L);
        orderProductPO3.setOrderSn("orderSn");
        orderProductPO3.setStoreName("memberName");
        orderProductPO3.setGoodsName("memberName");
        orderProductPO3.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO3.setProductNum(0);
        orderProductPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO3.setServiceFee(new BigDecimal("0.00"));
        orderProductPO3.setSpellTeamId(0);
        orderProductPO3.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS1 = Arrays.asList(orderProductPO3);
        when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(orderProductPOS1);

        when(mockIBzOldUserPoolService.insertFirstLoanOrder("userNo", "memberName")).thenReturn(false);

        // Configure IOrderExtendFinanceService.getByOrderSn(...).
        final OrderExtendFinancePO orderExtendFinancePO = new OrderExtendFinancePO();
        orderExtendFinancePO.setFinanceId(0L);
        orderExtendFinancePO.setOrderSn("orderSn");
        orderExtendFinancePO.setInterestWay(0);
        orderExtendFinancePO.setPlanInterestStartDays(0);
        orderExtendFinancePO.setPlanLoanDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockFinanceService.getByOrderSn("memberName")).thenReturn(orderExtendFinancePO);

        // Configure OrderModel.doLoanOperate(...).
        final ErrorContext errorContext1 = new ErrorContext();
        errorContext1.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext1.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult1 = new Result<>(false, errorContext1, null);
        when(mockOrderModel.doLoanOperate("memberName", 0L, "payNo")).thenReturn(voidResult1);

        // Run the test
        orderPayModelUnderTest.enjoyPayCallBack(req, payInfoExtraInfoVO);

        // Verify the results
        // Confirm PayService.paySuccessProcess(...).
        final PaymentNotifyVO req2 = new PaymentNotifyVO();
        req2.setOrderOn("paySn");
        req2.setPayCode("memberName");
        req2.setPayTradeNo("memberName");
        req2.setRelPayAmt(new BigDecimal("0.00"));
        req2.setPayStatus(0);
        req2.setPayWay("payWay");
        req2.setBankPayTrxNos(new HashMap<>());
        req2.setNewOrder(false);
        verify(mockPayService).paySuccessProcess(0, req2,
                new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"), new HashMap<>());
        verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
                new BigDecimal("0.00"));
        verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 20, 0, "订单支付完成",
                OrderCreateChannel.H5);
        verify(mockOrderProductModel).orderPaySuccessAddSales("memberName");
        verify(mockOrderService).setOrdersDeliverable(Arrays.asList("value"));

        // Confirm CouponMemberFeignClient.saveCouponMember(...).
        final CouponMember couponMember = new CouponMember();
        couponMember.setCouponId(0);
        couponMember.setCouponCode("couponCode");
        couponMember.setStoreId(0L);
        couponMember.setMemberId(0);
        couponMember.setMemberName("memberName");
        couponMember.setReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseState(0);
        couponMember.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseType(0);
        couponMember.setRandomAmount(new BigDecimal("0.00"));
        verify(mockCouponMemberFeignClient).saveCouponMember(couponMember);

        // Confirm CouponUseLogFeignClient.saveCouponUseLog(...).
        final CouponUseLog couponUseLog = new CouponUseLog();
        couponUseLog.setCouponCode("couponCode");
        couponUseLog.setMemberId(0);
        couponUseLog.setMemberName("memberName");
        couponUseLog.setStoreId(0L);
        couponUseLog.setLogType(0);
        couponUseLog.setLogTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponUseLog.setLogContent("logContent");
        verify(mockCouponUseLogFeignClient).saveCouponUseLog(couponUseLog);

        // Confirm CouponFeignClient.updateOrderCoupon(...).
        final Coupon coupon = new Coupon();
        coupon.setCouponId(0);
        coupon.setCouponName("couponName");
        coupon.setCouponContent("couponContent");
        coupon.setDescription("description");
        coupon.setReceivedNum(0);
        verify(mockCouponFeignClient).updateOrderCoupon(coupon);

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO5 = new OrderPO();
        orderPO5.setOrderId(0);
        orderPO5.setOrderSn("memberName");
        orderPO5.setUserNo("userNo");
        orderPO5.setPaySn("paySn");
        orderPO5.setSellerId("sellerId");
        orderPO5.setBankPayTrxNo("bankPayTrxNo");
        orderPO5.setStoreId(0L);
        orderPO5.setRecommendStoreId(0L);
        orderPO5.setMemberName("memberName");
        orderPO5.setMemberId(0);
        orderPO5.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO5.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO5.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO5.setOrderState(0);
        orderPO5.setLoanPayState(0);
        orderPO5.setPaymentName("paymentName");
        orderPO5.setPaymentCode("paymentCode");
        orderPO5.setOrderAmount(new BigDecimal("0.00"));
        orderPO5.setGoodsAmount(new BigDecimal("0.00"));
        orderPO5.setExpressFee(new BigDecimal("0.00"));
        orderPO5.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO5.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO5.setXzCardAmount(new BigDecimal("0.00"));
        orderPO5.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO5.setComposePayName("composeWay");
        orderPO5.setBalanceAmount(new BigDecimal("0.00"));
        orderPO5.setPayAmount(new BigDecimal("0.00"));
        orderPO5.setAreaCode("areaCode");
        orderPO5.setOrderType(0);
        orderPO5.setServiceFee(new BigDecimal("0.00"));
        orderPO5.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO5.setSettleMode("settleMode");
        orderPO5.setFinanceRuleCode("financeRuleCode");
        orderPO5.setIsDelivery(0);
        orderPO5.setChannel("channel");
        orderPO5.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO5.setNewOrder(false);
        orderPO5.setCustomerConfirmStatus(0);
        orderPO5.setOrderPlaceUserRoleCode(0);
        orderPO5.setExchangeFlag(0);
        verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO5, OrderEventEnum.CREATE,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg",
                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));
        verify(mockOrderService).dealZeroEnjoyPayOrder("paySn", "payNo");
        verify(mockTaskQueueService).saveTaskQueue(0L, TaskQueueBizTypeEnum.AUTO_LOAN,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "CNBJ0000");

        // Confirm OrderModel.loanPayFail(...).
        final OrderPO orderPODb = new OrderPO();
        orderPODb.setOrderId(0);
        orderPODb.setOrderSn("memberName");
        orderPODb.setUserNo("userNo");
        orderPODb.setPaySn("paySn");
        orderPODb.setSellerId("sellerId");
        orderPODb.setBankPayTrxNo("bankPayTrxNo");
        orderPODb.setStoreId(0L);
        orderPODb.setRecommendStoreId(0L);
        orderPODb.setMemberName("memberName");
        orderPODb.setMemberId(0);
        orderPODb.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPODb.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPODb.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPODb.setOrderState(0);
        orderPODb.setLoanPayState(0);
        orderPODb.setPaymentName("paymentName");
        orderPODb.setPaymentCode("paymentCode");
        orderPODb.setOrderAmount(new BigDecimal("0.00"));
        orderPODb.setGoodsAmount(new BigDecimal("0.00"));
        orderPODb.setExpressFee(new BigDecimal("0.00"));
        orderPODb.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPODb.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPODb.setXzCardAmount(new BigDecimal("0.00"));
        orderPODb.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPODb.setComposePayName("composeWay");
        orderPODb.setBalanceAmount(new BigDecimal("0.00"));
        orderPODb.setPayAmount(new BigDecimal("0.00"));
        orderPODb.setAreaCode("areaCode");
        orderPODb.setOrderType(0);
        orderPODb.setServiceFee(new BigDecimal("0.00"));
        orderPODb.setServiceFeeRate(new BigDecimal("0.00"));
        orderPODb.setSettleMode("settleMode");
        orderPODb.setFinanceRuleCode("financeRuleCode");
        orderPODb.setIsDelivery(0);
        orderPODb.setChannel("channel");
        orderPODb.setChannelServiceFee(new BigDecimal("0.00"));
        orderPODb.setNewOrder(false);
        orderPODb.setCustomerConfirmStatus(0);
        orderPODb.setOrderPlaceUserRoleCode(0);
        orderPODb.setExchangeFlag(0);
        verify(mockOrderModel).loanPayFail(orderPODb, "failureReason");

        // Confirm OrderModel.loanPaySuccess(...).
        final OrderPO orderPO6 = new OrderPO();
        orderPO6.setOrderId(0);
        orderPO6.setOrderSn("memberName");
        orderPO6.setUserNo("userNo");
        orderPO6.setPaySn("paySn");
        orderPO6.setSellerId("sellerId");
        orderPO6.setBankPayTrxNo("bankPayTrxNo");
        orderPO6.setStoreId(0L);
        orderPO6.setRecommendStoreId(0L);
        orderPO6.setMemberName("memberName");
        orderPO6.setMemberId(0);
        orderPO6.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO6.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO6.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO6.setOrderState(0);
        orderPO6.setLoanPayState(0);
        orderPO6.setPaymentName("paymentName");
        orderPO6.setPaymentCode("paymentCode");
        orderPO6.setOrderAmount(new BigDecimal("0.00"));
        orderPO6.setGoodsAmount(new BigDecimal("0.00"));
        orderPO6.setExpressFee(new BigDecimal("0.00"));
        orderPO6.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO6.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO6.setXzCardAmount(new BigDecimal("0.00"));
        orderPO6.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO6.setComposePayName("composeWay");
        orderPO6.setBalanceAmount(new BigDecimal("0.00"));
        orderPO6.setPayAmount(new BigDecimal("0.00"));
        orderPO6.setAreaCode("areaCode");
        orderPO6.setOrderType(0);
        orderPO6.setServiceFee(new BigDecimal("0.00"));
        orderPO6.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO6.setSettleMode("settleMode");
        orderPO6.setFinanceRuleCode("financeRuleCode");
        orderPO6.setIsDelivery(0);
        orderPO6.setChannel("channel");
        orderPO6.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO6.setNewOrder(false);
        orderPO6.setCustomerConfirmStatus(0);
        orderPO6.setOrderPlaceUserRoleCode(0);
        orderPO6.setExchangeFlag(0);
        verify(mockOrderModel).loanPaySuccess(orderPO6, 3, 0L, "memberName");
    }
*/
/*    @Test
    void testEnjoyPayCallBack_IOrderServiceListReturnsNoItems() throws Exception {
        // Setup
        final PaymentNotifyVO req = new PaymentNotifyVO();
        req.setOrderOn("paySn");
        req.setPayCode("memberName");
        req.setPayTradeNo("memberName");
        req.setRelPayAmt(new BigDecimal("0.00"));
        req.setPayStatus(0);
        req.setPayWay("payWay");
        req.setBankPayTrxNos(new HashMap<>());
        req.setNewOrder(false);

        final PayInfoExtraInfoVO payInfoExtraInfoVO = new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn");

        // Configure PayService.buildPayNotifyOrderOn(...).
        final PaymentNotifyVO req1 = new PaymentNotifyVO();
        req1.setOrderOn("paySn");
        req1.setPayCode("memberName");
        req1.setPayTradeNo("memberName");
        req1.setRelPayAmt(new BigDecimal("0.00"));
        req1.setPayStatus(0);
        req1.setPayWay("payWay");
        req1.setBankPayTrxNos(new HashMap<>());
        req1.setNewOrder(false);
        when(mockPayService.buildPayNotifyOrderOn(req1,
                new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"), true)).thenReturn("memberName");

        // Configure OrderModel.getOrderByOrderSn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);
        when(mockOrderModel.getOrderByOrderSn("memberName")).thenReturn(orderPO);

        // Configure OrderPayMapper.getByPrimaryKey(...).
        final OrderPayPO orderPayPO = new OrderPayPO();
        orderPayPO.setPayId(0);
        orderPayPO.setPaySn("paySn");
        orderPayPO.setOrderSn("pOrderSn");
        orderPayPO.setPayAmount(new BigDecimal("0.00"));
        orderPayPO.setMemberId(0);
        orderPayPO.setApiPayState("0");
        orderPayPO.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPayPO.setTradeSn("memberName");
        orderPayPO.setPaymentName("paymentName");
        orderPayPO.setPaymentCode("paymentCode");
        orderPayPO.setLoanSuccess(0);
        orderPayPO.setEnjoyPayVipFlag(0);
        orderPayPO.setPayWayExtraInfo(new JSONObject(0, false));
        orderPayPO.setOutBizSource("channel");
        orderPayPO.setOutBizId("paySn");
        when(mockOrderPayMapper.getByPrimaryKey("paySn")).thenReturn(orderPayPO);

        when(mockOrderService.lambdaUpdate()).thenReturn(new LambdaUpdateChainWrapper<>(null));
        when(mockPayService.payAmount("payNo")).thenReturn(new BigDecimal("0.00"));

        // Configure OrderModel.getOrderList(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO1);
        final OrderExample example = new OrderExample();
        example.setOrderIdNotEquals(0);
        example.setUserNo("userNo");
        example.setUserMobile("userMobile");
        example.setOrderSn("memberName");
        example.setPaySn("paySn");
        when(mockOrderModel.getOrderList(eq(example), any(PagerInfo.class))).thenReturn(orderPOList);

        when(mockOrderPayProcessStrategyContext.getStrategy(0)).thenReturn(null);
        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategory storeBindCategory = new StoreBindCategory();
        storeBindCategory.setBindId(0);
        storeBindCategory.setStoreId(0L);
        storeBindCategory.setCreateVendorId(0L);
        storeBindCategory.setGoodsCategoryId1(0);
        storeBindCategory.setGoodsCategoryId2(0);
        final List<StoreBindCategory> storeBindCategories = Arrays.asList(storeBindCategory);
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                .thenReturn(storeBindCategories);

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        when(mockAccountCardFacade.defaultCard(0L)).thenReturn(new JsonResult<>(0, "errMsg"));
        when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure OrderPayMapper.updateByPrimaryKeySelective(...).
        final OrderPayPO record = new OrderPayPO();
        record.setPayId(0);
        record.setPaySn("paySn");
        record.setOrderSn("pOrderSn");
        record.setPayAmount(new BigDecimal("0.00"));
        record.setMemberId(0);
        record.setApiPayState("0");
        record.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        record.setTradeSn("memberName");
        record.setPaymentName("paymentName");
        record.setPaymentCode("paymentCode");
        record.setLoanSuccess(0);
        record.setEnjoyPayVipFlag(0);
        record.setPayWayExtraInfo(new JSONObject(0, false));
        record.setOutBizSource("channel");
        record.setOutBizId("paySn");
        when(mockOrderPayMapper.updateByPrimaryKeySelective(record)).thenReturn(0);

        when(mockPromotionCommonFeignClient.orderPaySuccess("memberName", 0, "paySn", "memberName", "paymentName",
                "paymentCode")).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderProductMapper.selectOne(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setStoreName("memberName");
        orderProductPO1.setGoodsName("memberName");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setSpellTeamId(0);
        orderProductPO1.setEnabledFlag(0);
        when(mockOrderProductMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderProductPO1);

        // Configure OrderProductMapper.selectList(...).
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setStoreName("memberName");
        orderProductPO2.setGoodsName("memberName");
        orderProductPO2.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO2.setProductNum(0);
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setSpellTeamId(0);
        orderProductPO2.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO2);
        when(mockOrderProductMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);
        final List<OrderPromotionSendCouponPO> orderPromotionSendCouponPOS = Arrays.asList(orderPromotionSendCouponPO);
        final OrderPromotionSendCouponExample example1 = new OrderPromotionSendCouponExample();
        example1.setSendCouponIdNotEquals(0);
        example1.setSendCouponIdIn("sendCouponIdIn");
        example1.setSendCouponId(0);
        example1.setOrderSn("memberName");
        example1.setOrderSnLike("orderSnLike");
        when(mockOrderPromotionSendCouponMapper.listByExample(example1)).thenReturn(orderPromotionSendCouponPOS);

        // Configure CouponFeignClient.getCouponByCouponId(...).
        final CouponVO couponVO = new CouponVO();
        couponVO.setCouponId(0);
        couponVO.setCouponType(0);
        couponVO.setReceivedNum(0);
        couponVO.setRandomMax(new BigDecimal("0.00"));
        couponVO.setRandomMin(new BigDecimal("0.00"));
        couponVO.setPublishNum(0);
        couponVO.setEffectiveTimeType(0);
        couponVO.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setCycle(0);
        couponVO.setState(0);
        couponVO.setStoreId(0L);
        couponVO.setUseType(0);
        when(mockCouponFeignClient.getCouponByCouponId(0)).thenReturn(couponVO);

        // Configure IOrderService.getById(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("memberName");
        orderPO2.setUserNo("userNo");
        orderPO2.setPaySn("paySn");
        orderPO2.setSellerId("sellerId");
        orderPO2.setBankPayTrxNo("bankPayTrxNo");
        orderPO2.setStoreId(0L);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setComposePayName("composeWay");
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setServiceFee(new BigDecimal("0.00"));
        orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO2.setSettleMode("settleMode");
        orderPO2.setFinanceRuleCode("financeRuleCode");
        orderPO2.setIsDelivery(0);
        orderPO2.setChannel("channel");
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setCustomerConfirmStatus(0);
        orderPO2.setOrderPlaceUserRoleCode(0);
        orderPO2.setExchangeFlag(0);
        when(mockOrderService.getById(0)).thenReturn(orderPO2);

        // Configure IOrderAmountStateRecordService.initOrderAmountState(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult = new Result<>(false, errorContext, null);
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("memberName");
        orderPO3.setUserNo("userNo");
        orderPO3.setPaySn("paySn");
        orderPO3.setSellerId("sellerId");
        orderPO3.setBankPayTrxNo("bankPayTrxNo");
        orderPO3.setStoreId(0L);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setComposePayName("composeWay");
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setServiceFee(new BigDecimal("0.00"));
        orderPO3.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO3.setSettleMode("settleMode");
        orderPO3.setFinanceRuleCode("financeRuleCode");
        orderPO3.setIsDelivery(0);
        orderPO3.setChannel("channel");
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setCustomerConfirmStatus(0);
        orderPO3.setOrderPlaceUserRoleCode(0);
        orderPO3.setExchangeFlag(0);
        when(mockOrderAmountRecordService.initOrderAmountState(orderPO3)).thenReturn(voidResult);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO3 = new OrderProductPO();
        orderProductPO3.setOrderProductId(0L);
        orderProductPO3.setOrderSn("orderSn");
        orderProductPO3.setStoreName("memberName");
        orderProductPO3.setGoodsName("memberName");
        orderProductPO3.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO3.setProductNum(0);
        orderProductPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO3.setServiceFee(new BigDecimal("0.00"));
        orderProductPO3.setSpellTeamId(0);
        orderProductPO3.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS1 = Arrays.asList(orderProductPO3);
        when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(orderProductPOS1);

        when(mockIBzOldUserPoolService.insertFirstLoanOrder("userNo", "memberName")).thenReturn(false);

        // Configure IOrderExtendFinanceService.getByOrderSn(...).
        final OrderExtendFinancePO orderExtendFinancePO = new OrderExtendFinancePO();
        orderExtendFinancePO.setFinanceId(0L);
        orderExtendFinancePO.setOrderSn("orderSn");
        orderExtendFinancePO.setInterestWay(0);
        orderExtendFinancePO.setPlanInterestStartDays(0);
        orderExtendFinancePO.setPlanLoanDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockFinanceService.getByOrderSn("memberName")).thenReturn(orderExtendFinancePO);

        // Configure OrderModel.doLoanOperate(...).
        final ErrorContext errorContext1 = new ErrorContext();
        errorContext1.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext1.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult1 = new Result<>(false, errorContext1, null);
        when(mockOrderModel.doLoanOperate("memberName", 0L, "payNo")).thenReturn(voidResult1);

        // Run the test
        orderPayModelUnderTest.enjoyPayCallBack(req, payInfoExtraInfoVO);

        // Verify the results
        // Confirm PayService.paySuccessProcess(...).
        final PaymentNotifyVO req2 = new PaymentNotifyVO();
        req2.setOrderOn("paySn");
        req2.setPayCode("memberName");
        req2.setPayTradeNo("memberName");
        req2.setRelPayAmt(new BigDecimal("0.00"));
        req2.setPayStatus(0);
        req2.setPayWay("payWay");
        req2.setBankPayTrxNos(new HashMap<>());
        req2.setNewOrder(false);
        verify(mockPayService).paySuccessProcess(0, req2,
                new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"), new HashMap<>());
        verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
                new BigDecimal("0.00"));
        verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 20, 0, "订单支付完成",
                OrderCreateChannel.H5);
        verify(mockOrderProductModel).orderPaySuccessAddSales("memberName");
        verify(mockOrderService).setOrdersDeliverable(Arrays.asList("value"));

        // Confirm CouponMemberFeignClient.saveCouponMember(...).
        final CouponMember couponMember = new CouponMember();
        couponMember.setCouponId(0);
        couponMember.setCouponCode("couponCode");
        couponMember.setStoreId(0L);
        couponMember.setMemberId(0);
        couponMember.setMemberName("memberName");
        couponMember.setReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseState(0);
        couponMember.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseType(0);
        couponMember.setRandomAmount(new BigDecimal("0.00"));
        verify(mockCouponMemberFeignClient).saveCouponMember(couponMember);

        // Confirm CouponUseLogFeignClient.saveCouponUseLog(...).
        final CouponUseLog couponUseLog = new CouponUseLog();
        couponUseLog.setCouponCode("couponCode");
        couponUseLog.setMemberId(0);
        couponUseLog.setMemberName("memberName");
        couponUseLog.setStoreId(0L);
        couponUseLog.setLogType(0);
        couponUseLog.setLogTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponUseLog.setLogContent("logContent");
        verify(mockCouponUseLogFeignClient).saveCouponUseLog(couponUseLog);

        // Confirm CouponFeignClient.updateOrderCoupon(...).
        final Coupon coupon = new Coupon();
        coupon.setCouponId(0);
        coupon.setCouponName("couponName");
        coupon.setCouponContent("couponContent");
        coupon.setDescription("description");
        coupon.setReceivedNum(0);
        verify(mockCouponFeignClient).updateOrderCoupon(coupon);

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO4 = new OrderPO();
        orderPO4.setOrderId(0);
        orderPO4.setOrderSn("memberName");
        orderPO4.setUserNo("userNo");
        orderPO4.setPaySn("paySn");
        orderPO4.setSellerId("sellerId");
        orderPO4.setBankPayTrxNo("bankPayTrxNo");
        orderPO4.setStoreId(0L);
        orderPO4.setRecommendStoreId(0L);
        orderPO4.setMemberName("memberName");
        orderPO4.setMemberId(0);
        orderPO4.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setOrderState(0);
        orderPO4.setLoanPayState(0);
        orderPO4.setPaymentName("paymentName");
        orderPO4.setPaymentCode("paymentCode");
        orderPO4.setOrderAmount(new BigDecimal("0.00"));
        orderPO4.setGoodsAmount(new BigDecimal("0.00"));
        orderPO4.setExpressFee(new BigDecimal("0.00"));
        orderPO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO4.setComposePayName("composeWay");
        orderPO4.setBalanceAmount(new BigDecimal("0.00"));
        orderPO4.setPayAmount(new BigDecimal("0.00"));
        orderPO4.setAreaCode("areaCode");
        orderPO4.setOrderType(0);
        orderPO4.setServiceFee(new BigDecimal("0.00"));
        orderPO4.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO4.setSettleMode("settleMode");
        orderPO4.setFinanceRuleCode("financeRuleCode");
        orderPO4.setIsDelivery(0);
        orderPO4.setChannel("channel");
        orderPO4.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO4.setNewOrder(false);
        orderPO4.setCustomerConfirmStatus(0);
        orderPO4.setOrderPlaceUserRoleCode(0);
        orderPO4.setExchangeFlag(0);
        verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO4, OrderEventEnum.CREATE,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg",
                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));
        verify(mockOrderService).dealZeroEnjoyPayOrder("paySn", "payNo");
        verify(mockTaskQueueService).saveTaskQueue(0L, TaskQueueBizTypeEnum.AUTO_LOAN,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "CNBJ0000");

        // Confirm OrderModel.loanPayFail(...).
        final OrderPO orderPODb = new OrderPO();
        orderPODb.setOrderId(0);
        orderPODb.setOrderSn("memberName");
        orderPODb.setUserNo("userNo");
        orderPODb.setPaySn("paySn");
        orderPODb.setSellerId("sellerId");
        orderPODb.setBankPayTrxNo("bankPayTrxNo");
        orderPODb.setStoreId(0L);
        orderPODb.setRecommendStoreId(0L);
        orderPODb.setMemberName("memberName");
        orderPODb.setMemberId(0);
        orderPODb.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPODb.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPODb.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPODb.setOrderState(0);
        orderPODb.setLoanPayState(0);
        orderPODb.setPaymentName("paymentName");
        orderPODb.setPaymentCode("paymentCode");
        orderPODb.setOrderAmount(new BigDecimal("0.00"));
        orderPODb.setGoodsAmount(new BigDecimal("0.00"));
        orderPODb.setExpressFee(new BigDecimal("0.00"));
        orderPODb.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPODb.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPODb.setXzCardAmount(new BigDecimal("0.00"));
        orderPODb.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPODb.setComposePayName("composeWay");
        orderPODb.setBalanceAmount(new BigDecimal("0.00"));
        orderPODb.setPayAmount(new BigDecimal("0.00"));
        orderPODb.setAreaCode("areaCode");
        orderPODb.setOrderType(0);
        orderPODb.setServiceFee(new BigDecimal("0.00"));
        orderPODb.setServiceFeeRate(new BigDecimal("0.00"));
        orderPODb.setSettleMode("settleMode");
        orderPODb.setFinanceRuleCode("financeRuleCode");
        orderPODb.setIsDelivery(0);
        orderPODb.setChannel("channel");
        orderPODb.setChannelServiceFee(new BigDecimal("0.00"));
        orderPODb.setNewOrder(false);
        orderPODb.setCustomerConfirmStatus(0);
        orderPODb.setOrderPlaceUserRoleCode(0);
        orderPODb.setExchangeFlag(0);
        verify(mockOrderModel).loanPayFail(orderPODb, "failureReason");

        // Confirm OrderModel.loanPaySuccess(...).
        final OrderPO orderPO5 = new OrderPO();
        orderPO5.setOrderId(0);
        orderPO5.setOrderSn("memberName");
        orderPO5.setUserNo("userNo");
        orderPO5.setPaySn("paySn");
        orderPO5.setSellerId("sellerId");
        orderPO5.setBankPayTrxNo("bankPayTrxNo");
        orderPO5.setStoreId(0L);
        orderPO5.setRecommendStoreId(0L);
        orderPO5.setMemberName("memberName");
        orderPO5.setMemberId(0);
        orderPO5.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO5.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO5.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO5.setOrderState(0);
        orderPO5.setLoanPayState(0);
        orderPO5.setPaymentName("paymentName");
        orderPO5.setPaymentCode("paymentCode");
        orderPO5.setOrderAmount(new BigDecimal("0.00"));
        orderPO5.setGoodsAmount(new BigDecimal("0.00"));
        orderPO5.setExpressFee(new BigDecimal("0.00"));
        orderPO5.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO5.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO5.setXzCardAmount(new BigDecimal("0.00"));
        orderPO5.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO5.setComposePayName("composeWay");
        orderPO5.setBalanceAmount(new BigDecimal("0.00"));
        orderPO5.setPayAmount(new BigDecimal("0.00"));
        orderPO5.setAreaCode("areaCode");
        orderPO5.setOrderType(0);
        orderPO5.setServiceFee(new BigDecimal("0.00"));
        orderPO5.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO5.setSettleMode("settleMode");
        orderPO5.setFinanceRuleCode("financeRuleCode");
        orderPO5.setIsDelivery(0);
        orderPO5.setChannel("channel");
        orderPO5.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO5.setNewOrder(false);
        orderPO5.setCustomerConfirmStatus(0);
        orderPO5.setOrderPlaceUserRoleCode(0);
        orderPO5.setExchangeFlag(0);
        verify(mockOrderModel).loanPaySuccess(orderPO5, 3, 0L, "memberName");
    }*/

    /*  @Test
      void testEnjoyPayCallBack_OrderProductMapperSelectListReturnsNoItems() throws Exception {
          // Setup
          final PaymentNotifyVO req = new PaymentNotifyVO();
          req.setOrderOn("paySn");
          req.setPayCode("memberName");
          req.setPayTradeNo("memberName");
          req.setRelPayAmt(new BigDecimal("0.00"));
          req.setPayStatus(0);
          req.setPayWay("payWay");
          req.setBankPayTrxNos(new HashMap<>());
          req.setNewOrder(false);

          final PayInfoExtraInfoVO payInfoExtraInfoVO = new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn");

          // Configure PayService.buildPayNotifyOrderOn(...).
          final PaymentNotifyVO req1 = new PaymentNotifyVO();
          req1.setOrderOn("paySn");
          req1.setPayCode("memberName");
          req1.setPayTradeNo("memberName");
          req1.setRelPayAmt(new BigDecimal("0.00"));
          req1.setPayStatus(0);
          req1.setPayWay("payWay");
          req1.setBankPayTrxNos(new HashMap<>());
          req1.setNewOrder(false);
          when(mockPayService.buildPayNotifyOrderOn(req1,
                  new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"), true)).thenReturn("memberName");

          // Configure OrderModel.getOrderByOrderSn(...).
          final OrderPO orderPO = new OrderPO();
          orderPO.setOrderId(0);
          orderPO.setOrderSn("memberName");
          orderPO.setUserNo("userNo");
          orderPO.setPaySn("paySn");
          orderPO.setSellerId("sellerId");
          orderPO.setBankPayTrxNo("bankPayTrxNo");
          orderPO.setStoreId(0L);
          orderPO.setRecommendStoreId(0L);
          orderPO.setMemberName("memberName");
          orderPO.setMemberId(0);
          orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
          orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
          orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
          orderPO.setOrderState(0);
          orderPO.setLoanPayState(0);
          orderPO.setPaymentName("paymentName");
          orderPO.setPaymentCode("paymentCode");
          orderPO.setOrderAmount(new BigDecimal("0.00"));
          orderPO.setGoodsAmount(new BigDecimal("0.00"));
          orderPO.setExpressFee(new BigDecimal("0.00"));
          orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
          orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
          orderPO.setXzCardAmount(new BigDecimal("0.00"));
          orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
          orderPO.setComposePayName("composeWay");
          orderPO.setBalanceAmount(new BigDecimal("0.00"));
          orderPO.setPayAmount(new BigDecimal("0.00"));
          orderPO.setAreaCode("areaCode");
          orderPO.setOrderType(0);
          orderPO.setServiceFee(new BigDecimal("0.00"));
          orderPO.setServiceFeeRate(new BigDecimal("0.00"));
          orderPO.setSettleMode("settleMode");
          orderPO.setFinanceRuleCode("financeRuleCode");
          orderPO.setIsDelivery(0);
          orderPO.setChannel("channel");
          orderPO.setChannelServiceFee(new BigDecimal("0.00"));
          orderPO.setNewOrder(false);
          orderPO.setCustomerConfirmStatus(0);
          orderPO.setOrderPlaceUserRoleCode(0);
          orderPO.setExchangeFlag(0);
          when(mockOrderModel.getOrderByOrderSn("memberName")).thenReturn(orderPO);

          // Configure OrderPayMapper.getByPrimaryKey(...).
          final OrderPayPO orderPayPO = new OrderPayPO();
          orderPayPO.setPayId(0);
          orderPayPO.setPaySn("paySn");
          orderPayPO.setOrderSn("pOrderSn");
          orderPayPO.setPayAmount(new BigDecimal("0.00"));
          orderPayPO.setMemberId(0);
          orderPayPO.setApiPayState("0");
          orderPayPO.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
          orderPayPO.setTradeSn("memberName");
          orderPayPO.setPaymentName("paymentName");
          orderPayPO.setPaymentCode("paymentCode");
          orderPayPO.setLoanSuccess(0);
          orderPayPO.setEnjoyPayVipFlag(0);
          orderPayPO.setPayWayExtraInfo(new JSONObject(0, false));
          orderPayPO.setOutBizSource("channel");
          orderPayPO.setOutBizId("paySn");
          when(mockOrderPayMapper.getByPrimaryKey("paySn")).thenReturn(orderPayPO);

          when(mockOrderService.lambdaUpdate()).thenReturn(new LambdaUpdateChainWrapper<>(null));
          when(mockPayService.payAmount("payNo")).thenReturn(new BigDecimal("0.00"));

          // Configure OrderModel.getOrderList(...).
          final OrderPO orderPO1 = new OrderPO();
          orderPO1.setOrderId(0);
          orderPO1.setOrderSn("memberName");
          orderPO1.setUserNo("userNo");
          orderPO1.setPaySn("paySn");
          orderPO1.setSellerId("sellerId");
          orderPO1.setBankPayTrxNo("bankPayTrxNo");
          orderPO1.setStoreId(0L);
          orderPO1.setRecommendStoreId(0L);
          orderPO1.setMemberName("memberName");
          orderPO1.setMemberId(0);
          orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
          orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
          orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
          orderPO1.setOrderState(0);
          orderPO1.setLoanPayState(0);
          orderPO1.setPaymentName("paymentName");
          orderPO1.setPaymentCode("paymentCode");
          orderPO1.setOrderAmount(new BigDecimal("0.00"));
          orderPO1.setGoodsAmount(new BigDecimal("0.00"));
          orderPO1.setExpressFee(new BigDecimal("0.00"));
          orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
          orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
          orderPO1.setXzCardAmount(new BigDecimal("0.00"));
          orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
          orderPO1.setComposePayName("composeWay");
          orderPO1.setBalanceAmount(new BigDecimal("0.00"));
          orderPO1.setPayAmount(new BigDecimal("0.00"));
          orderPO1.setAreaCode("areaCode");
          orderPO1.setOrderType(0);
          orderPO1.setServiceFee(new BigDecimal("0.00"));
          orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
          orderPO1.setSettleMode("settleMode");
          orderPO1.setFinanceRuleCode("financeRuleCode");
          orderPO1.setIsDelivery(0);
          orderPO1.setChannel("channel");
          orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
          orderPO1.setNewOrder(false);
          orderPO1.setCustomerConfirmStatus(0);
          orderPO1.setOrderPlaceUserRoleCode(0);
          orderPO1.setExchangeFlag(0);
          final List<OrderPO> orderPOList = Arrays.asList(orderPO1);
          final OrderExample example = new OrderExample();
          example.setOrderIdNotEquals(0);
          example.setUserNo("userNo");
          example.setUserMobile("userMobile");
          example.setOrderSn("memberName");
          example.setPaySn("paySn");
          when(mockOrderModel.getOrderList(eq(example), any(PagerInfo.class))).thenReturn(orderPOList);

          when(mockOrderPayProcessStrategyContext.getStrategy(0)).thenReturn(null);
          when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

          // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
          final StoreBindCategory storeBindCategory = new StoreBindCategory();
          storeBindCategory.setBindId(0);
          storeBindCategory.setStoreId(0L);
          storeBindCategory.setCreateVendorId(0L);
          storeBindCategory.setGoodsCategoryId1(0);
          storeBindCategory.setGoodsCategoryId2(0);
          final List<StoreBindCategory> storeBindCategories = Arrays.asList(storeBindCategory);
          final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
          storeBindCategoryExample.setBindIdNotEquals(0);
          storeBindCategoryExample.setBindIdIn("bindIdIn");
          storeBindCategoryExample.setBindId(0);
          storeBindCategoryExample.setStoreId(0L);
          storeBindCategoryExample.setStoreIdNotEquals(0);
          when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                  .thenReturn(storeBindCategories);

          // Configure OmsBaseIntegration.query(...).
          final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
          ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
          ruleServiceFeeQueryDTO.setPayWay("paymentCode");
          ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
          ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
          when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

          when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

          // Configure IOrderService.updateById(...).
          final OrderPO entity = new OrderPO();
          entity.setOrderId(0);
          entity.setOrderSn("memberName");
          entity.setUserNo("userNo");
          entity.setPaySn("paySn");
          entity.setSellerId("sellerId");
          entity.setBankPayTrxNo("bankPayTrxNo");
          entity.setStoreId(0L);
          entity.setRecommendStoreId(0L);
          entity.setMemberName("memberName");
          entity.setMemberId(0);
          entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
          entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
          entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
          entity.setOrderState(0);
          entity.setLoanPayState(0);
          entity.setPaymentName("paymentName");
          entity.setPaymentCode("paymentCode");
          entity.setOrderAmount(new BigDecimal("0.00"));
          entity.setGoodsAmount(new BigDecimal("0.00"));
          entity.setExpressFee(new BigDecimal("0.00"));
          entity.setStoreVoucherAmount(new BigDecimal("0.00"));
          entity.setStoreActivityAmount(new BigDecimal("0.00"));
          entity.setXzCardAmount(new BigDecimal("0.00"));
          entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
          entity.setComposePayName("composeWay");
          entity.setBalanceAmount(new BigDecimal("0.00"));
          entity.setPayAmount(new BigDecimal("0.00"));
          entity.setAreaCode("areaCode");
          entity.setOrderType(0);
          entity.setServiceFee(new BigDecimal("0.00"));
          entity.setServiceFeeRate(new BigDecimal("0.00"));
          entity.setSettleMode("settleMode");
          entity.setFinanceRuleCode("financeRuleCode");
          entity.setIsDelivery(0);
          entity.setChannel("channel");
          entity.setChannelServiceFee(new BigDecimal("0.00"));
          entity.setNewOrder(false);
          entity.setCustomerConfirmStatus(0);
          entity.setOrderPlaceUserRoleCode(0);
          entity.setExchangeFlag(0);
          when(mockOrderService.updateById(entity)).thenReturn(false);

          // Configure IOrderProductService.updateBatchById(...).
          final OrderProductPO orderProductPO = new OrderProductPO();
          orderProductPO.setOrderProductId(0L);
          orderProductPO.setOrderSn("orderSn");
          orderProductPO.setStoreName("memberName");
          orderProductPO.setGoodsName("memberName");
          orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
          orderProductPO.setProductNum(0);
          orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
          orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
          orderProductPO.setServiceFee(new BigDecimal("0.00"));
          orderProductPO.setSpellTeamId(0);
          orderProductPO.setEnabledFlag(0);
          final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
          when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

          when(mockAccountCardFacade.defaultCard(0L)).thenReturn(new JsonResult<>(0, "errMsg"));

          // Configure IOrderService.list(...).
          final OrderPO orderPO2 = new OrderPO();
          orderPO2.setOrderId(0);
          orderPO2.setOrderSn("memberName");
          orderPO2.setUserNo("userNo");
          orderPO2.setPaySn("paySn");
          orderPO2.setSellerId("sellerId");
          orderPO2.setBankPayTrxNo("bankPayTrxNo");
          orderPO2.setStoreId(0L);
          orderPO2.setRecommendStoreId(0L);
          orderPO2.setMemberName("memberName");
          orderPO2.setMemberId(0);
          orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
          orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
          orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
          orderPO2.setOrderState(0);
          orderPO2.setLoanPayState(0);
          orderPO2.setPaymentName("paymentName");
          orderPO2.setPaymentCode("paymentCode");
          orderPO2.setOrderAmount(new BigDecimal("0.00"));
          orderPO2.setGoodsAmount(new BigDecimal("0.00"));
          orderPO2.setExpressFee(new BigDecimal("0.00"));
          orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
          orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
          orderPO2.setXzCardAmount(new BigDecimal("0.00"));
          orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
          orderPO2.setComposePayName("composeWay");
          orderPO2.setBalanceAmount(new BigDecimal("0.00"));
          orderPO2.setPayAmount(new BigDecimal("0.00"));
          orderPO2.setAreaCode("areaCode");
          orderPO2.setOrderType(0);
          orderPO2.setServiceFee(new BigDecimal("0.00"));
          orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
          orderPO2.setSettleMode("settleMode");
          orderPO2.setFinanceRuleCode("financeRuleCode");
          orderPO2.setIsDelivery(0);
          orderPO2.setChannel("channel");
          orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
          orderPO2.setNewOrder(false);
          orderPO2.setCustomerConfirmStatus(0);
          orderPO2.setOrderPlaceUserRoleCode(0);
          orderPO2.setExchangeFlag(0);
          final List<OrderPO> orderPOList1 = Arrays.asList(orderPO2);
          when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(orderPOList1);

          when(mockPromotionCommonFeignClient.orderPaySuccess("memberName", 0, "paySn", "memberName", "paymentName",
                  "paymentCode")).thenReturn(new JsonResult<>(0, "errMsg"));

          // Configure OrderProductMapper.selectOne(...).
          final OrderProductPO orderProductPO1 = new OrderProductPO();
          orderProductPO1.setOrderProductId(0L);
          orderProductPO1.setOrderSn("orderSn");
          orderProductPO1.setStoreName("memberName");
          orderProductPO1.setGoodsName("memberName");
          orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
          orderProductPO1.setProductNum(0);
          orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
          orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
          orderProductPO1.setServiceFee(new BigDecimal("0.00"));
          orderProductPO1.setSpellTeamId(0);
          orderProductPO1.setEnabledFlag(0);
          when(mockOrderProductMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderProductPO1);

          when(mockOrderProductMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

          // Configure OrderPromotionSendCouponMapper.listByExample(...).
          final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
          orderPromotionSendCouponPO.setSendCouponId(0);
          orderPromotionSendCouponPO.setOrderSn("orderSn");
          orderPromotionSendCouponPO.setPromotionGrade(0);
          orderPromotionSendCouponPO.setCouponId(0);
          orderPromotionSendCouponPO.setNumber(0);
          final List<OrderPromotionSendCouponPO> orderPromotionSendCouponPOS = Arrays.asList(orderPromotionSendCouponPO);
          final OrderPromotionSendCouponExample example1 = new OrderPromotionSendCouponExample();
          example1.setSendCouponIdNotEquals(0);
          example1.setSendCouponIdIn("sendCouponIdIn");
          example1.setSendCouponId(0);
          example1.setOrderSn("memberName");
          example1.setOrderSnLike("orderSnLike");
          when(mockOrderPromotionSendCouponMapper.listByExample(example1)).thenReturn(orderPromotionSendCouponPOS);

          // Configure CouponFeignClient.getCouponByCouponId(...).
          final CouponVO couponVO = new CouponVO();
          couponVO.setCouponId(0);
          couponVO.setCouponType(0);
          couponVO.setReceivedNum(0);
          couponVO.setRandomMax(new BigDecimal("0.00"));
          couponVO.setRandomMin(new BigDecimal("0.00"));
          couponVO.setPublishNum(0);
          couponVO.setEffectiveTimeType(0);
          couponVO.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
          couponVO.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
          couponVO.setCycle(0);
          couponVO.setState(0);
          couponVO.setStoreId(0L);
          couponVO.setUseType(0);
          when(mockCouponFeignClient.getCouponByCouponId(0)).thenReturn(couponVO);

          // Configure IOrderService.getById(...).
          final OrderPO orderPO3 = new OrderPO();
          orderPO3.setOrderId(0);
          orderPO3.setOrderSn("memberName");
          orderPO3.setUserNo("userNo");
          orderPO3.setPaySn("paySn");
          orderPO3.setSellerId("sellerId");
          orderPO3.setBankPayTrxNo("bankPayTrxNo");
          orderPO3.setStoreId(0L);
          orderPO3.setRecommendStoreId(0L);
          orderPO3.setMemberName("memberName");
          orderPO3.setMemberId(0);
          orderPO3.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
          orderPO3.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
          orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
          orderPO3.setOrderState(0);
          orderPO3.setLoanPayState(0);
          orderPO3.setPaymentName("paymentName");
          orderPO3.setPaymentCode("paymentCode");
          orderPO3.setOrderAmount(new BigDecimal("0.00"));
          orderPO3.setGoodsAmount(new BigDecimal("0.00"));
          orderPO3.setExpressFee(new BigDecimal("0.00"));
          orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
          orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
          orderPO3.setXzCardAmount(new BigDecimal("0.00"));
          orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
          orderPO3.setComposePayName("composeWay");
          orderPO3.setBalanceAmount(new BigDecimal("0.00"));
          orderPO3.setPayAmount(new BigDecimal("0.00"));
          orderPO3.setAreaCode("areaCode");
          orderPO3.setOrderType(0);
          orderPO3.setServiceFee(new BigDecimal("0.00"));
          orderPO3.setServiceFeeRate(new BigDecimal("0.00"));
          orderPO3.setSettleMode("settleMode");
          orderPO3.setFinanceRuleCode("financeRuleCode");
          orderPO3.setIsDelivery(0);
          orderPO3.setChannel("channel");
          orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
          orderPO3.setNewOrder(false);
          orderPO3.setCustomerConfirmStatus(0);
          orderPO3.setOrderPlaceUserRoleCode(0);
          orderPO3.setExchangeFlag(0);
          when(mockOrderService.getById(0)).thenReturn(orderPO3);

          // Configure IOrderAmountStateRecordService.initOrderAmountState(...).
          final ErrorContext errorContext = new ErrorContext();
          errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
          errorContext.setThirdPartyError("thirdPartyError");
          final Result<Void> voidResult = new Result<>(false, errorContext, null);
          final OrderPO orderPO4 = new OrderPO();
          orderPO4.setOrderId(0);
          orderPO4.setOrderSn("memberName");
          orderPO4.setUserNo("userNo");
          orderPO4.setPaySn("paySn");
          orderPO4.setSellerId("sellerId");
          orderPO4.setBankPayTrxNo("bankPayTrxNo");
          orderPO4.setStoreId(0L);
          orderPO4.setRecommendStoreId(0L);
          orderPO4.setMemberName("memberName");
          orderPO4.setMemberId(0);
          orderPO4.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
          orderPO4.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
          orderPO4.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
          orderPO4.setOrderState(0);
          orderPO4.setLoanPayState(0);
          orderPO4.setPaymentName("paymentName");
          orderPO4.setPaymentCode("paymentCode");
          orderPO4.setOrderAmount(new BigDecimal("0.00"));
          orderPO4.setGoodsAmount(new BigDecimal("0.00"));
          orderPO4.setExpressFee(new BigDecimal("0.00"));
          orderPO4.setStoreVoucherAmount(new BigDecimal("0.00"));
          orderPO4.setStoreActivityAmount(new BigDecimal("0.00"));
          orderPO4.setXzCardAmount(new BigDecimal("0.00"));
          orderPO4.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
          orderPO4.setComposePayName("composeWay");
          orderPO4.setBalanceAmount(new BigDecimal("0.00"));
          orderPO4.setPayAmount(new BigDecimal("0.00"));
          orderPO4.setAreaCode("areaCode");
          orderPO4.setOrderType(0);
          orderPO4.setServiceFee(new BigDecimal("0.00"));
          orderPO4.setServiceFeeRate(new BigDecimal("0.00"));
          orderPO4.setSettleMode("settleMode");
          orderPO4.setFinanceRuleCode("financeRuleCode");
          orderPO4.setIsDelivery(0);
          orderPO4.setChannel("channel");
          orderPO4.setChannelServiceFee(new BigDecimal("0.00"));
          orderPO4.setNewOrder(false);
          orderPO4.setCustomerConfirmStatus(0);
          orderPO4.setOrderPlaceUserRoleCode(0);
          orderPO4.setExchangeFlag(0);
          when(mockOrderAmountRecordService.initOrderAmountState(orderPO4)).thenReturn(voidResult);

          // Configure OrderProductModel.getOrderProductListByOrderSn(...).
          final OrderProductPO orderProductPO2 = new OrderProductPO();
          orderProductPO2.setOrderProductId(0L);
          orderProductPO2.setOrderSn("orderSn");
          orderProductPO2.setStoreName("memberName");
          orderProductPO2.setGoodsName("memberName");
          orderProductPO2.setProductShowPrice(new BigDecimal("0.00"));
          orderProductPO2.setProductNum(0);
          orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
          orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
          orderProductPO2.setServiceFee(new BigDecimal("0.00"));
          orderProductPO2.setSpellTeamId(0);
          orderProductPO2.setEnabledFlag(0);
          final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO2);
          when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(orderProductPOS);

          when(mockIBzOldUserPoolService.insertFirstLoanOrder("userNo", "memberName")).thenReturn(false);

          // Configure IOrderExtendFinanceService.getByOrderSn(...).
          final OrderExtendFinancePO orderExtendFinancePO = new OrderExtendFinancePO();
          orderExtendFinancePO.setFinanceId(0L);
          orderExtendFinancePO.setOrderSn("orderSn");
          orderExtendFinancePO.setInterestWay(0);
          orderExtendFinancePO.setPlanInterestStartDays(0);
          orderExtendFinancePO.setPlanLoanDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
          when(mockFinanceService.getByOrderSn("memberName")).thenReturn(orderExtendFinancePO);

          // Configure OrderModel.doLoanOperate(...).
          final ErrorContext errorContext1 = new ErrorContext();
          errorContext1.setErrorStack(
                  Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
          errorContext1.setThirdPartyError("thirdPartyError");
          final Result<Void> voidResult1 = new Result<>(false, errorContext1, null);
          when(mockOrderModel.doLoanOperate("memberName", 0L, "payNo")).thenReturn(voidResult1);

          // Run the test
          orderPayModelUnderTest.enjoyPayCallBack(req, payInfoExtraInfoVO);

          // Verify the results
          // Confirm PayService.paySuccessProcess(...).
          final PaymentNotifyVO req2 = new PaymentNotifyVO();
          req2.setOrderOn("paySn");
          req2.setPayCode("memberName");
          req2.setPayTradeNo("memberName");
          req2.setRelPayAmt(new BigDecimal("0.00"));
          req2.setPayStatus(0);
          req2.setPayWay("payWay");
          req2.setBankPayTrxNos(new HashMap<>());
          req2.setNewOrder(false);
          verify(mockPayService).paySuccessProcess(0, req2,
                  new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"), new HashMap<>());
          verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
                  new BigDecimal("0.00"));
          verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 20, 0, "订单支付完成",
                  OrderCreateChannel.H5);
          verify(mockOrderProductModel).orderPaySuccessAddSales("memberName");
          verify(mockOrderService).setOrdersDeliverable(Arrays.asList("value"));

          // Confirm CouponMemberFeignClient.saveCouponMember(...).
          final CouponMember couponMember = new CouponMember();
          couponMember.setCouponId(0);
          couponMember.setCouponCode("couponCode");
          couponMember.setStoreId(0L);
          couponMember.setMemberId(0);
          couponMember.setMemberName("memberName");
          couponMember.setReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
          couponMember.setUseState(0);
          couponMember.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
          couponMember.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
          couponMember.setUseType(0);
          couponMember.setRandomAmount(new BigDecimal("0.00"));
          verify(mockCouponMemberFeignClient).saveCouponMember(couponMember);

          // Confirm CouponUseLogFeignClient.saveCouponUseLog(...).
          final CouponUseLog couponUseLog = new CouponUseLog();
          couponUseLog.setCouponCode("couponCode");
          couponUseLog.setMemberId(0);
          couponUseLog.setMemberName("memberName");
          couponUseLog.setStoreId(0L);
          couponUseLog.setLogType(0);
          couponUseLog.setLogTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
          couponUseLog.setLogContent("logContent");
          verify(mockCouponUseLogFeignClient).saveCouponUseLog(couponUseLog);

          // Confirm CouponFeignClient.updateOrderCoupon(...).
          final Coupon coupon = new Coupon();
          coupon.setCouponId(0);
          coupon.setCouponName("couponName");
          coupon.setCouponContent("couponContent");
          coupon.setDescription("description");
          coupon.setReceivedNum(0);
          verify(mockCouponFeignClient).updateOrderCoupon(coupon);

          // Confirm OrderCreateHelper.addOrderChangeEvent(...).
          final OrderPO orderPO5 = new OrderPO();
          orderPO5.setOrderId(0);
          orderPO5.setOrderSn("memberName");
          orderPO5.setUserNo("userNo");
          orderPO5.setPaySn("paySn");
          orderPO5.setSellerId("sellerId");
          orderPO5.setBankPayTrxNo("bankPayTrxNo");
          orderPO5.setStoreId(0L);
          orderPO5.setRecommendStoreId(0L);
          orderPO5.setMemberName("memberName");
          orderPO5.setMemberId(0);
          orderPO5.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
          orderPO5.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
          orderPO5.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
          orderPO5.setOrderState(0);
          orderPO5.setLoanPayState(0);
          orderPO5.setPaymentName("paymentName");
          orderPO5.setPaymentCode("paymentCode");
          orderPO5.setOrderAmount(new BigDecimal("0.00"));
          orderPO5.setGoodsAmount(new BigDecimal("0.00"));
          orderPO5.setExpressFee(new BigDecimal("0.00"));
          orderPO5.setStoreVoucherAmount(new BigDecimal("0.00"));
          orderPO5.setStoreActivityAmount(new BigDecimal("0.00"));
          orderPO5.setXzCardAmount(new BigDecimal("0.00"));
          orderPO5.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
          orderPO5.setComposePayName("composeWay");
          orderPO5.setBalanceAmount(new BigDecimal("0.00"));
          orderPO5.setPayAmount(new BigDecimal("0.00"));
          orderPO5.setAreaCode("areaCode");
          orderPO5.setOrderType(0);
          orderPO5.setServiceFee(new BigDecimal("0.00"));
          orderPO5.setServiceFeeRate(new BigDecimal("0.00"));
          orderPO5.setSettleMode("settleMode");
          orderPO5.setFinanceRuleCode("financeRuleCode");
          orderPO5.setIsDelivery(0);
          orderPO5.setChannel("channel");
          orderPO5.setChannelServiceFee(new BigDecimal("0.00"));
          orderPO5.setNewOrder(false);
          orderPO5.setCustomerConfirmStatus(0);
          orderPO5.setOrderPlaceUserRoleCode(0);
          orderPO5.setExchangeFlag(0);
          verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO5, OrderEventEnum.CREATE,
                  new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
          verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg",
                  new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
                          Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
                          "balance_change_reminder", "{\"type\":\"balance_change\"}"));
          verify(mockOrderService).dealZeroEnjoyPayOrder("paySn", "payNo");
          verify(mockTaskQueueService).saveTaskQueue(0L, TaskQueueBizTypeEnum.AUTO_LOAN,
                  new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "CNBJ0000");

          // Confirm OrderModel.loanPayFail(...).
          final OrderPO orderPODb = new OrderPO();
          orderPODb.setOrderId(0);
          orderPODb.setOrderSn("memberName");
          orderPODb.setUserNo("userNo");
          orderPODb.setPaySn("paySn");
          orderPODb.setSellerId("sellerId");
          orderPODb.setBankPayTrxNo("bankPayTrxNo");
          orderPODb.setStoreId(0L);
          orderPODb.setRecommendStoreId(0L);
          orderPODb.setMemberName("memberName");
          orderPODb.setMemberId(0);
          orderPODb.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
          orderPODb.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
          orderPODb.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
          orderPODb.setOrderState(0);
          orderPODb.setLoanPayState(0);
          orderPODb.setPaymentName("paymentName");
          orderPODb.setPaymentCode("paymentCode");
          orderPODb.setOrderAmount(new BigDecimal("0.00"));
          orderPODb.setGoodsAmount(new BigDecimal("0.00"));
          orderPODb.setExpressFee(new BigDecimal("0.00"));
          orderPODb.setStoreVoucherAmount(new BigDecimal("0.00"));
          orderPODb.setStoreActivityAmount(new BigDecimal("0.00"));
          orderPODb.setXzCardAmount(new BigDecimal("0.00"));
          orderPODb.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
          orderPODb.setComposePayName("composeWay");
          orderPODb.setBalanceAmount(new BigDecimal("0.00"));
          orderPODb.setPayAmount(new BigDecimal("0.00"));
          orderPODb.setAreaCode("areaCode");
          orderPODb.setOrderType(0);
          orderPODb.setServiceFee(new BigDecimal("0.00"));
          orderPODb.setServiceFeeRate(new BigDecimal("0.00"));
          orderPODb.setSettleMode("settleMode");
          orderPODb.setFinanceRuleCode("financeRuleCode");
          orderPODb.setIsDelivery(0);
          orderPODb.setChannel("channel");
          orderPODb.setChannelServiceFee(new BigDecimal("0.00"));
          orderPODb.setNewOrder(false);
          orderPODb.setCustomerConfirmStatus(0);
          orderPODb.setOrderPlaceUserRoleCode(0);
          orderPODb.setExchangeFlag(0);
          verify(mockOrderModel).loanPayFail(orderPODb, "failureReason");

          // Confirm OrderModel.loanPaySuccess(...).
          final OrderPO orderPO6 = new OrderPO();
          orderPO6.setOrderId(0);
          orderPO6.setOrderSn("memberName");
          orderPO6.setUserNo("userNo");
          orderPO6.setPaySn("paySn");
          orderPO6.setSellerId("sellerId");
          orderPO6.setBankPayTrxNo("bankPayTrxNo");
          orderPO6.setStoreId(0L);
          orderPO6.setRecommendStoreId(0L);
          orderPO6.setMemberName("memberName");
          orderPO6.setMemberId(0);
          orderPO6.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
          orderPO6.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
          orderPO6.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
          orderPO6.setOrderState(0);
          orderPO6.setLoanPayState(0);
          orderPO6.setPaymentName("paymentName");
          orderPO6.setPaymentCode("paymentCode");
          orderPO6.setOrderAmount(new BigDecimal("0.00"));
          orderPO6.setGoodsAmount(new BigDecimal("0.00"));
          orderPO6.setExpressFee(new BigDecimal("0.00"));
          orderPO6.setStoreVoucherAmount(new BigDecimal("0.00"));
          orderPO6.setStoreActivityAmount(new BigDecimal("0.00"));
          orderPO6.setXzCardAmount(new BigDecimal("0.00"));
          orderPO6.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
          orderPO6.setComposePayName("composeWay");
          orderPO6.setBalanceAmount(new BigDecimal("0.00"));
          orderPO6.setPayAmount(new BigDecimal("0.00"));
          orderPO6.setAreaCode("areaCode");
          orderPO6.setOrderType(0);
          orderPO6.setServiceFee(new BigDecimal("0.00"));
          orderPO6.setServiceFeeRate(new BigDecimal("0.00"));
          orderPO6.setSettleMode("settleMode");
          orderPO6.setFinanceRuleCode("financeRuleCode");
          orderPO6.setIsDelivery(0);
          orderPO6.setChannel("channel");
          orderPO6.setChannelServiceFee(new BigDecimal("0.00"));
          orderPO6.setNewOrder(false);
          orderPO6.setCustomerConfirmStatus(0);
          orderPO6.setOrderPlaceUserRoleCode(0);
          orderPO6.setExchangeFlag(0);
          verify(mockOrderModel).loanPaySuccess(orderPO6, 3, 0L, "memberName");
      }
  */
   /* @Test
    void testEnjoyPayCallBack_OrderPromotionSendCouponMapperReturnsNoItems() throws Exception {
        // Setup
        final PaymentNotifyVO req = new PaymentNotifyVO();
        req.setOrderOn("paySn");
        req.setPayCode("memberName");
        req.setPayTradeNo("memberName");
        req.setRelPayAmt(new BigDecimal("0.00"));
        req.setPayStatus(0);
        req.setPayWay("payWay");
        req.setBankPayTrxNos(new HashMap<>());
        req.setNewOrder(false);

        final PayInfoExtraInfoVO payInfoExtraInfoVO = new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn");

        // Configure PayService.buildPayNotifyOrderOn(...).
        final PaymentNotifyVO req1 = new PaymentNotifyVO();
        req1.setOrderOn("paySn");
        req1.setPayCode("memberName");
        req1.setPayTradeNo("memberName");
        req1.setRelPayAmt(new BigDecimal("0.00"));
        req1.setPayStatus(0);
        req1.setPayWay("payWay");
        req1.setBankPayTrxNos(new HashMap<>());
        req1.setNewOrder(false);
        when(mockPayService.buildPayNotifyOrderOn(req1,
                new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"), true)).thenReturn("memberName");

        // Configure OrderModel.getOrderByOrderSn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);
        when(mockOrderModel.getOrderByOrderSn("memberName")).thenReturn(orderPO);

        // Configure OrderPayMapper.getByPrimaryKey(...).
        final OrderPayPO orderPayPO = new OrderPayPO();
        orderPayPO.setPayId(0);
        orderPayPO.setPaySn("paySn");
        orderPayPO.setOrderSn("pOrderSn");
        orderPayPO.setPayAmount(new BigDecimal("0.00"));
        orderPayPO.setMemberId(0);
        orderPayPO.setApiPayState("0");
        orderPayPO.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPayPO.setTradeSn("memberName");
        orderPayPO.setPaymentName("paymentName");
        orderPayPO.setPaymentCode("paymentCode");
        orderPayPO.setLoanSuccess(0);
        orderPayPO.setEnjoyPayVipFlag(0);
        orderPayPO.setPayWayExtraInfo(new JSONObject(0, false));
        orderPayPO.setOutBizSource("channel");
        orderPayPO.setOutBizId("paySn");
        when(mockOrderPayMapper.getByPrimaryKey("paySn")).thenReturn(orderPayPO);

        when(mockOrderService.lambdaUpdate()).thenReturn(new LambdaUpdateChainWrapper<>(null));
        when(mockPayService.payAmount("payNo")).thenReturn(new BigDecimal("0.00"));

        // Configure OrderModel.getOrderList(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO1);
        final OrderExample example = new OrderExample();
        example.setOrderIdNotEquals(0);
        example.setUserNo("userNo");
        example.setUserMobile("userMobile");
        example.setOrderSn("memberName");
        example.setPaySn("paySn");
        when(mockOrderModel.getOrderList(eq(example), any(PagerInfo.class))).thenReturn(orderPOList);

        when(mockOrderPayProcessStrategyContext.getStrategy(0)).thenReturn(null);
        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategory storeBindCategory = new StoreBindCategory();
        storeBindCategory.setBindId(0);
        storeBindCategory.setStoreId(0L);
        storeBindCategory.setCreateVendorId(0L);
        storeBindCategory.setGoodsCategoryId1(0);
        storeBindCategory.setGoodsCategoryId2(0);
        final List<StoreBindCategory> storeBindCategories = Arrays.asList(storeBindCategory);
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                .thenReturn(storeBindCategories);

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        when(mockAccountCardFacade.defaultCard(0L)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure IOrderService.list(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("memberName");
        orderPO2.setUserNo("userNo");
        orderPO2.setPaySn("paySn");
        orderPO2.setSellerId("sellerId");
        orderPO2.setBankPayTrxNo("bankPayTrxNo");
        orderPO2.setStoreId(0L);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setComposePayName("composeWay");
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setServiceFee(new BigDecimal("0.00"));
        orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO2.setSettleMode("settleMode");
        orderPO2.setFinanceRuleCode("financeRuleCode");
        orderPO2.setIsDelivery(0);
        orderPO2.setChannel("channel");
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setCustomerConfirmStatus(0);
        orderPO2.setOrderPlaceUserRoleCode(0);
        orderPO2.setExchangeFlag(0);
        final List<OrderPO> orderPOList1 = Arrays.asList(orderPO2);
        when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(orderPOList1);

        when(mockPromotionCommonFeignClient.orderPaySuccess("memberName", 0, "paySn", "memberName", "paymentName",
                "paymentCode")).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderProductMapper.selectOne(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setStoreName("memberName");
        orderProductPO1.setGoodsName("memberName");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setSpellTeamId(0);
        orderProductPO1.setEnabledFlag(0);
        when(mockOrderProductMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderProductPO1);

        // Configure OrderProductMapper.selectList(...).
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setStoreName("memberName");
        orderProductPO2.setGoodsName("memberName");
        orderProductPO2.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO2.setProductNum(0);
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setSpellTeamId(0);
        orderProductPO2.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO2);
        when(mockOrderProductMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponExample example1 = new OrderPromotionSendCouponExample();
        example1.setSendCouponIdNotEquals(0);
        example1.setSendCouponIdIn("sendCouponIdIn");
        example1.setSendCouponId(0);
        example1.setOrderSn("memberName");
        example1.setOrderSnLike("orderSnLike");
        when(mockOrderPromotionSendCouponMapper.listByExample(example1)).thenReturn(Collections.emptyList());

        // Configure IOrderService.getById(...).
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("memberName");
        orderPO3.setUserNo("userNo");
        orderPO3.setPaySn("paySn");
        orderPO3.setSellerId("sellerId");
        orderPO3.setBankPayTrxNo("bankPayTrxNo");
        orderPO3.setStoreId(0L);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setComposePayName("composeWay");
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setServiceFee(new BigDecimal("0.00"));
        orderPO3.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO3.setSettleMode("settleMode");
        orderPO3.setFinanceRuleCode("financeRuleCode");
        orderPO3.setIsDelivery(0);
        orderPO3.setChannel("channel");
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setCustomerConfirmStatus(0);
        orderPO3.setOrderPlaceUserRoleCode(0);
        orderPO3.setExchangeFlag(0);
        when(mockOrderService.getById(0)).thenReturn(orderPO3);

        // Configure IOrderAmountStateRecordService.initOrderAmountState(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult = new Result<>(false, errorContext, null);
        final OrderPO orderPO4 = new OrderPO();
        orderPO4.setOrderId(0);
        orderPO4.setOrderSn("memberName");
        orderPO4.setUserNo("userNo");
        orderPO4.setPaySn("paySn");
        orderPO4.setSellerId("sellerId");
        orderPO4.setBankPayTrxNo("bankPayTrxNo");
        orderPO4.setStoreId(0L);
        orderPO4.setRecommendStoreId(0L);
        orderPO4.setMemberName("memberName");
        orderPO4.setMemberId(0);
        orderPO4.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setOrderState(0);
        orderPO4.setLoanPayState(0);
        orderPO4.setPaymentName("paymentName");
        orderPO4.setPaymentCode("paymentCode");
        orderPO4.setOrderAmount(new BigDecimal("0.00"));
        orderPO4.setGoodsAmount(new BigDecimal("0.00"));
        orderPO4.setExpressFee(new BigDecimal("0.00"));
        orderPO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO4.setComposePayName("composeWay");
        orderPO4.setBalanceAmount(new BigDecimal("0.00"));
        orderPO4.setPayAmount(new BigDecimal("0.00"));
        orderPO4.setAreaCode("areaCode");
        orderPO4.setOrderType(0);
        orderPO4.setServiceFee(new BigDecimal("0.00"));
        orderPO4.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO4.setSettleMode("settleMode");
        orderPO4.setFinanceRuleCode("financeRuleCode");
        orderPO4.setIsDelivery(0);
        orderPO4.setChannel("channel");
        orderPO4.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO4.setNewOrder(false);
        orderPO4.setCustomerConfirmStatus(0);
        orderPO4.setOrderPlaceUserRoleCode(0);
        orderPO4.setExchangeFlag(0);
        when(mockOrderAmountRecordService.initOrderAmountState(orderPO4)).thenReturn(voidResult);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO3 = new OrderProductPO();
        orderProductPO3.setOrderProductId(0L);
        orderProductPO3.setOrderSn("orderSn");
        orderProductPO3.setStoreName("memberName");
        orderProductPO3.setGoodsName("memberName");
        orderProductPO3.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO3.setProductNum(0);
        orderProductPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO3.setServiceFee(new BigDecimal("0.00"));
        orderProductPO3.setSpellTeamId(0);
        orderProductPO3.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS1 = Arrays.asList(orderProductPO3);
        when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(orderProductPOS1);

        when(mockIBzOldUserPoolService.insertFirstLoanOrder("userNo", "memberName")).thenReturn(false);

        // Configure IOrderExtendFinanceService.getByOrderSn(...).
        final OrderExtendFinancePO orderExtendFinancePO = new OrderExtendFinancePO();
        orderExtendFinancePO.setFinanceId(0L);
        orderExtendFinancePO.setOrderSn("orderSn");
        orderExtendFinancePO.setInterestWay(0);
        orderExtendFinancePO.setPlanInterestStartDays(0);
        orderExtendFinancePO.setPlanLoanDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockFinanceService.getByOrderSn("memberName")).thenReturn(orderExtendFinancePO);

        // Configure OrderModel.doLoanOperate(...).
        final ErrorContext errorContext1 = new ErrorContext();
        errorContext1.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext1.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult1 = new Result<>(false, errorContext1, null);
        when(mockOrderModel.doLoanOperate("memberName", 0L, "payNo")).thenReturn(voidResult1);

        // Run the test
        orderPayModelUnderTest.enjoyPayCallBack(req, payInfoExtraInfoVO);

        // Verify the results
        // Confirm PayService.paySuccessProcess(...).
        final PaymentNotifyVO req2 = new PaymentNotifyVO();
        req2.setOrderOn("paySn");
        req2.setPayCode("memberName");
        req2.setPayTradeNo("memberName");
        req2.setRelPayAmt(new BigDecimal("0.00"));
        req2.setPayStatus(0);
        req2.setPayWay("payWay");
        req2.setBankPayTrxNos(new HashMap<>());
        req2.setNewOrder(false);
        verify(mockPayService).paySuccessProcess(0, req2,
                new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"), new HashMap<>());
        verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
                new BigDecimal("0.00"));
        verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 20, 0, "订单支付完成",
                OrderCreateChannel.H5);
        verify(mockOrderProductModel).orderPaySuccessAddSales("memberName");
        verify(mockOrderService).setOrdersDeliverable(Arrays.asList("value"));

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO5 = new OrderPO();
        orderPO5.setOrderId(0);
        orderPO5.setOrderSn("memberName");
        orderPO5.setUserNo("userNo");
        orderPO5.setPaySn("paySn");
        orderPO5.setSellerId("sellerId");
        orderPO5.setBankPayTrxNo("bankPayTrxNo");
        orderPO5.setStoreId(0L);
        orderPO5.setRecommendStoreId(0L);
        orderPO5.setMemberName("memberName");
        orderPO5.setMemberId(0);
        orderPO5.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO5.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO5.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO5.setOrderState(0);
        orderPO5.setLoanPayState(0);
        orderPO5.setPaymentName("paymentName");
        orderPO5.setPaymentCode("paymentCode");
        orderPO5.setOrderAmount(new BigDecimal("0.00"));
        orderPO5.setGoodsAmount(new BigDecimal("0.00"));
        orderPO5.setExpressFee(new BigDecimal("0.00"));
        orderPO5.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO5.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO5.setXzCardAmount(new BigDecimal("0.00"));
        orderPO5.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO5.setComposePayName("composeWay");
        orderPO5.setBalanceAmount(new BigDecimal("0.00"));
        orderPO5.setPayAmount(new BigDecimal("0.00"));
        orderPO5.setAreaCode("areaCode");
        orderPO5.setOrderType(0);
        orderPO5.setServiceFee(new BigDecimal("0.00"));
        orderPO5.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO5.setSettleMode("settleMode");
        orderPO5.setFinanceRuleCode("financeRuleCode");
        orderPO5.setIsDelivery(0);
        orderPO5.setChannel("channel");
        orderPO5.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO5.setNewOrder(false);
        orderPO5.setCustomerConfirmStatus(0);
        orderPO5.setOrderPlaceUserRoleCode(0);
        orderPO5.setExchangeFlag(0);
        verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO5, OrderEventEnum.CREATE,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg",
                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));
        verify(mockOrderService).dealZeroEnjoyPayOrder("paySn", "payNo");
        verify(mockTaskQueueService).saveTaskQueue(0L, TaskQueueBizTypeEnum.AUTO_LOAN,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "CNBJ0000");

        // Confirm OrderModel.loanPayFail(...).
        final OrderPO orderPODb = new OrderPO();
        orderPODb.setOrderId(0);
        orderPODb.setOrderSn("memberName");
        orderPODb.setUserNo("userNo");
        orderPODb.setPaySn("paySn");
        orderPODb.setSellerId("sellerId");
        orderPODb.setBankPayTrxNo("bankPayTrxNo");
        orderPODb.setStoreId(0L);
        orderPODb.setRecommendStoreId(0L);
        orderPODb.setMemberName("memberName");
        orderPODb.setMemberId(0);
        orderPODb.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPODb.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPODb.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPODb.setOrderState(0);
        orderPODb.setLoanPayState(0);
        orderPODb.setPaymentName("paymentName");
        orderPODb.setPaymentCode("paymentCode");
        orderPODb.setOrderAmount(new BigDecimal("0.00"));
        orderPODb.setGoodsAmount(new BigDecimal("0.00"));
        orderPODb.setExpressFee(new BigDecimal("0.00"));
        orderPODb.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPODb.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPODb.setXzCardAmount(new BigDecimal("0.00"));
        orderPODb.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPODb.setComposePayName("composeWay");
        orderPODb.setBalanceAmount(new BigDecimal("0.00"));
        orderPODb.setPayAmount(new BigDecimal("0.00"));
        orderPODb.setAreaCode("areaCode");
        orderPODb.setOrderType(0);
        orderPODb.setServiceFee(new BigDecimal("0.00"));
        orderPODb.setServiceFeeRate(new BigDecimal("0.00"));
        orderPODb.setSettleMode("settleMode");
        orderPODb.setFinanceRuleCode("financeRuleCode");
        orderPODb.setIsDelivery(0);
        orderPODb.setChannel("channel");
        orderPODb.setChannelServiceFee(new BigDecimal("0.00"));
        orderPODb.setNewOrder(false);
        orderPODb.setCustomerConfirmStatus(0);
        orderPODb.setOrderPlaceUserRoleCode(0);
        orderPODb.setExchangeFlag(0);
        verify(mockOrderModel).loanPayFail(orderPODb, "failureReason");

        // Confirm OrderModel.loanPaySuccess(...).
        final OrderPO orderPO6 = new OrderPO();
        orderPO6.setOrderId(0);
        orderPO6.setOrderSn("memberName");
        orderPO6.setUserNo("userNo");
        orderPO6.setPaySn("paySn");
        orderPO6.setSellerId("sellerId");
        orderPO6.setBankPayTrxNo("bankPayTrxNo");
        orderPO6.setStoreId(0L);
        orderPO6.setRecommendStoreId(0L);
        orderPO6.setMemberName("memberName");
        orderPO6.setMemberId(0);
        orderPO6.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO6.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO6.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO6.setOrderState(0);
        orderPO6.setLoanPayState(0);
        orderPO6.setPaymentName("paymentName");
        orderPO6.setPaymentCode("paymentCode");
        orderPO6.setOrderAmount(new BigDecimal("0.00"));
        orderPO6.setGoodsAmount(new BigDecimal("0.00"));
        orderPO6.setExpressFee(new BigDecimal("0.00"));
        orderPO6.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO6.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO6.setXzCardAmount(new BigDecimal("0.00"));
        orderPO6.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO6.setComposePayName("composeWay");
        orderPO6.setBalanceAmount(new BigDecimal("0.00"));
        orderPO6.setPayAmount(new BigDecimal("0.00"));
        orderPO6.setAreaCode("areaCode");
        orderPO6.setOrderType(0);
        orderPO6.setServiceFee(new BigDecimal("0.00"));
        orderPO6.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO6.setSettleMode("settleMode");
        orderPO6.setFinanceRuleCode("financeRuleCode");
        orderPO6.setIsDelivery(0);
        orderPO6.setChannel("channel");
        orderPO6.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO6.setNewOrder(false);
        orderPO6.setCustomerConfirmStatus(0);
        orderPO6.setOrderPlaceUserRoleCode(0);
        orderPO6.setExchangeFlag(0);
        verify(mockOrderModel).loanPaySuccess(orderPO6, 3, 0L, "memberName");
    }
*/
   /* @Test
    void testEnjoyPayCallBack_OrderProductModelGetOrderProductListByOrderSnReturnsNoItems() throws Exception {
        // Setup
        final PaymentNotifyVO req = new PaymentNotifyVO();
        req.setOrderOn("paySn");
        req.setPayCode("memberName");
        req.setPayTradeNo("memberName");
        req.setRelPayAmt(new BigDecimal("0.00"));
        req.setPayStatus(0);
        req.setPayWay("payWay");
        req.setBankPayTrxNos(new HashMap<>());
        req.setNewOrder(false);

        final PayInfoExtraInfoVO payInfoExtraInfoVO = new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn");

        // Configure PayService.buildPayNotifyOrderOn(...).
        final PaymentNotifyVO req1 = new PaymentNotifyVO();
        req1.setOrderOn("paySn");
        req1.setPayCode("memberName");
        req1.setPayTradeNo("memberName");
        req1.setRelPayAmt(new BigDecimal("0.00"));
        req1.setPayStatus(0);
        req1.setPayWay("payWay");
        req1.setBankPayTrxNos(new HashMap<>());
        req1.setNewOrder(false);
        when(mockPayService.buildPayNotifyOrderOn(req1,
                new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"), true)).thenReturn("memberName");

        // Configure OrderModel.getOrderByOrderSn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);
        when(mockOrderModel.getOrderByOrderSn("memberName")).thenReturn(orderPO);

        // Configure OrderPayMapper.getByPrimaryKey(...).
        final OrderPayPO orderPayPO = new OrderPayPO();
        orderPayPO.setPayId(0);
        orderPayPO.setPaySn("paySn");
        orderPayPO.setOrderSn("pOrderSn");
        orderPayPO.setPayAmount(new BigDecimal("0.00"));
        orderPayPO.setMemberId(0);
        orderPayPO.setApiPayState("0");
        orderPayPO.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPayPO.setTradeSn("memberName");
        orderPayPO.setPaymentName("paymentName");
        orderPayPO.setPaymentCode("paymentCode");
        orderPayPO.setLoanSuccess(0);
        orderPayPO.setEnjoyPayVipFlag(0);
        orderPayPO.setPayWayExtraInfo(new JSONObject(0, false));
        orderPayPO.setOutBizSource("channel");
        orderPayPO.setOutBizId("paySn");
        when(mockOrderPayMapper.getByPrimaryKey("paySn")).thenReturn(orderPayPO);

        when(mockOrderService.lambdaUpdate()).thenReturn(new LambdaUpdateChainWrapper<>(null));
        when(mockPayService.payAmount("payNo")).thenReturn(new BigDecimal("0.00"));

        // Configure OrderModel.getOrderList(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO1);
        final OrderExample example = new OrderExample();
        example.setOrderIdNotEquals(0);
        example.setUserNo("userNo");
        example.setUserMobile("userMobile");
        example.setOrderSn("memberName");
        example.setPaySn("paySn");
        when(mockOrderModel.getOrderList(eq(example), any(PagerInfo.class))).thenReturn(orderPOList);

        when(mockOrderPayProcessStrategyContext.getStrategy(0)).thenReturn(null);
        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategory storeBindCategory = new StoreBindCategory();
        storeBindCategory.setBindId(0);
        storeBindCategory.setStoreId(0L);
        storeBindCategory.setCreateVendorId(0L);
        storeBindCategory.setGoodsCategoryId1(0);
        storeBindCategory.setGoodsCategoryId2(0);
        final List<StoreBindCategory> storeBindCategories = Arrays.asList(storeBindCategory);
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                .thenReturn(storeBindCategories);

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        when(mockAccountCardFacade.defaultCard(0L)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure IOrderService.list(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("memberName");
        orderPO2.setUserNo("userNo");
        orderPO2.setPaySn("paySn");
        orderPO2.setSellerId("sellerId");
        orderPO2.setBankPayTrxNo("bankPayTrxNo");
        orderPO2.setStoreId(0L);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setComposePayName("composeWay");
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setServiceFee(new BigDecimal("0.00"));
        orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO2.setSettleMode("settleMode");
        orderPO2.setFinanceRuleCode("financeRuleCode");
        orderPO2.setIsDelivery(0);
        orderPO2.setChannel("channel");
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setCustomerConfirmStatus(0);
        orderPO2.setOrderPlaceUserRoleCode(0);
        orderPO2.setExchangeFlag(0);
        final List<OrderPO> orderPOList1 = Arrays.asList(orderPO2);
        when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(orderPOList1);

        when(mockPromotionCommonFeignClient.orderPaySuccess("memberName", 0, "paySn", "memberName", "paymentName",
                "paymentCode")).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderProductMapper.selectOne(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setStoreName("memberName");
        orderProductPO1.setGoodsName("memberName");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setSpellTeamId(0);
        orderProductPO1.setEnabledFlag(0);
        when(mockOrderProductMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderProductPO1);

        // Configure OrderProductMapper.selectList(...).
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setStoreName("memberName");
        orderProductPO2.setGoodsName("memberName");
        orderProductPO2.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO2.setProductNum(0);
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setSpellTeamId(0);
        orderProductPO2.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO2);
        when(mockOrderProductMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);
        final List<OrderPromotionSendCouponPO> orderPromotionSendCouponPOS = Arrays.asList(orderPromotionSendCouponPO);
        final OrderPromotionSendCouponExample example1 = new OrderPromotionSendCouponExample();
        example1.setSendCouponIdNotEquals(0);
        example1.setSendCouponIdIn("sendCouponIdIn");
        example1.setSendCouponId(0);
        example1.setOrderSn("memberName");
        example1.setOrderSnLike("orderSnLike");
        when(mockOrderPromotionSendCouponMapper.listByExample(example1)).thenReturn(orderPromotionSendCouponPOS);

        // Configure CouponFeignClient.getCouponByCouponId(...).
        final CouponVO couponVO = new CouponVO();
        couponVO.setCouponId(0);
        couponVO.setCouponType(0);
        couponVO.setReceivedNum(0);
        couponVO.setRandomMax(new BigDecimal("0.00"));
        couponVO.setRandomMin(new BigDecimal("0.00"));
        couponVO.setPublishNum(0);
        couponVO.setEffectiveTimeType(0);
        couponVO.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setCycle(0);
        couponVO.setState(0);
        couponVO.setStoreId(0L);
        couponVO.setUseType(0);
        when(mockCouponFeignClient.getCouponByCouponId(0)).thenReturn(couponVO);

        // Configure IOrderService.getById(...).
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("memberName");
        orderPO3.setUserNo("userNo");
        orderPO3.setPaySn("paySn");
        orderPO3.setSellerId("sellerId");
        orderPO3.setBankPayTrxNo("bankPayTrxNo");
        orderPO3.setStoreId(0L);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setComposePayName("composeWay");
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setServiceFee(new BigDecimal("0.00"));
        orderPO3.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO3.setSettleMode("settleMode");
        orderPO3.setFinanceRuleCode("financeRuleCode");
        orderPO3.setIsDelivery(0);
        orderPO3.setChannel("channel");
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setCustomerConfirmStatus(0);
        orderPO3.setOrderPlaceUserRoleCode(0);
        orderPO3.setExchangeFlag(0);
        when(mockOrderService.getById(0)).thenReturn(orderPO3);

        // Configure IOrderAmountStateRecordService.initOrderAmountState(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult = new Result<>(false, errorContext, null);
        final OrderPO orderPO4 = new OrderPO();
        orderPO4.setOrderId(0);
        orderPO4.setOrderSn("memberName");
        orderPO4.setUserNo("userNo");
        orderPO4.setPaySn("paySn");
        orderPO4.setSellerId("sellerId");
        orderPO4.setBankPayTrxNo("bankPayTrxNo");
        orderPO4.setStoreId(0L);
        orderPO4.setRecommendStoreId(0L);
        orderPO4.setMemberName("memberName");
        orderPO4.setMemberId(0);
        orderPO4.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setOrderState(0);
        orderPO4.setLoanPayState(0);
        orderPO4.setPaymentName("paymentName");
        orderPO4.setPaymentCode("paymentCode");
        orderPO4.setOrderAmount(new BigDecimal("0.00"));
        orderPO4.setGoodsAmount(new BigDecimal("0.00"));
        orderPO4.setExpressFee(new BigDecimal("0.00"));
        orderPO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO4.setComposePayName("composeWay");
        orderPO4.setBalanceAmount(new BigDecimal("0.00"));
        orderPO4.setPayAmount(new BigDecimal("0.00"));
        orderPO4.setAreaCode("areaCode");
        orderPO4.setOrderType(0);
        orderPO4.setServiceFee(new BigDecimal("0.00"));
        orderPO4.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO4.setSettleMode("settleMode");
        orderPO4.setFinanceRuleCode("financeRuleCode");
        orderPO4.setIsDelivery(0);
        orderPO4.setChannel("channel");
        orderPO4.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO4.setNewOrder(false);
        orderPO4.setCustomerConfirmStatus(0);
        orderPO4.setOrderPlaceUserRoleCode(0);
        orderPO4.setExchangeFlag(0);
        when(mockOrderAmountRecordService.initOrderAmountState(orderPO4)).thenReturn(voidResult);

        when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(Collections.emptyList());
        when(mockIBzOldUserPoolService.insertFirstLoanOrder("userNo", "memberName")).thenReturn(false);

        // Configure IOrderExtendFinanceService.getByOrderSn(...).
        final OrderExtendFinancePO orderExtendFinancePO = new OrderExtendFinancePO();
        orderExtendFinancePO.setFinanceId(0L);
        orderExtendFinancePO.setOrderSn("orderSn");
        orderExtendFinancePO.setInterestWay(0);
        orderExtendFinancePO.setPlanInterestStartDays(0);
        orderExtendFinancePO.setPlanLoanDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockFinanceService.getByOrderSn("memberName")).thenReturn(orderExtendFinancePO);

        // Configure OrderModel.doLoanOperate(...).
        final ErrorContext errorContext1 = new ErrorContext();
        errorContext1.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext1.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult1 = new Result<>(false, errorContext1, null);
        when(mockOrderModel.doLoanOperate("memberName", 0L, "payNo")).thenReturn(voidResult1);

        // Run the test
        orderPayModelUnderTest.enjoyPayCallBack(req, payInfoExtraInfoVO);

        // Verify the results
        // Confirm PayService.paySuccessProcess(...).
        final PaymentNotifyVO req2 = new PaymentNotifyVO();
        req2.setOrderOn("paySn");
        req2.setPayCode("memberName");
        req2.setPayTradeNo("memberName");
        req2.setRelPayAmt(new BigDecimal("0.00"));
        req2.setPayStatus(0);
        req2.setPayWay("payWay");
        req2.setBankPayTrxNos(new HashMap<>());
        req2.setNewOrder(false);
        verify(mockPayService).paySuccessProcess(0, req2,
                new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"), new HashMap<>());
        verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
                new BigDecimal("0.00"));
        verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 20, 0, "订单支付完成",
                OrderCreateChannel.H5);
        verify(mockOrderProductModel).orderPaySuccessAddSales("memberName");
        verify(mockOrderService).setOrdersDeliverable(Arrays.asList("value"));

        // Confirm CouponMemberFeignClient.saveCouponMember(...).
        final CouponMember couponMember = new CouponMember();
        couponMember.setCouponId(0);
        couponMember.setCouponCode("couponCode");
        couponMember.setStoreId(0L);
        couponMember.setMemberId(0);
        couponMember.setMemberName("memberName");
        couponMember.setReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseState(0);
        couponMember.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseType(0);
        couponMember.setRandomAmount(new BigDecimal("0.00"));
        verify(mockCouponMemberFeignClient).saveCouponMember(couponMember);

        // Confirm CouponUseLogFeignClient.saveCouponUseLog(...).
        final CouponUseLog couponUseLog = new CouponUseLog();
        couponUseLog.setCouponCode("couponCode");
        couponUseLog.setMemberId(0);
        couponUseLog.setMemberName("memberName");
        couponUseLog.setStoreId(0L);
        couponUseLog.setLogType(0);
        couponUseLog.setLogTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponUseLog.setLogContent("logContent");
        verify(mockCouponUseLogFeignClient).saveCouponUseLog(couponUseLog);

        // Confirm CouponFeignClient.updateOrderCoupon(...).
        final Coupon coupon = new Coupon();
        coupon.setCouponId(0);
        coupon.setCouponName("couponName");
        coupon.setCouponContent("couponContent");
        coupon.setDescription("description");
        coupon.setReceivedNum(0);
        verify(mockCouponFeignClient).updateOrderCoupon(coupon);

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO5 = new OrderPO();
        orderPO5.setOrderId(0);
        orderPO5.setOrderSn("memberName");
        orderPO5.setUserNo("userNo");
        orderPO5.setPaySn("paySn");
        orderPO5.setSellerId("sellerId");
        orderPO5.setBankPayTrxNo("bankPayTrxNo");
        orderPO5.setStoreId(0L);
        orderPO5.setRecommendStoreId(0L);
        orderPO5.setMemberName("memberName");
        orderPO5.setMemberId(0);
        orderPO5.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO5.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO5.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO5.setOrderState(0);
        orderPO5.setLoanPayState(0);
        orderPO5.setPaymentName("paymentName");
        orderPO5.setPaymentCode("paymentCode");
        orderPO5.setOrderAmount(new BigDecimal("0.00"));
        orderPO5.setGoodsAmount(new BigDecimal("0.00"));
        orderPO5.setExpressFee(new BigDecimal("0.00"));
        orderPO5.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO5.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO5.setXzCardAmount(new BigDecimal("0.00"));
        orderPO5.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO5.setComposePayName("composeWay");
        orderPO5.setBalanceAmount(new BigDecimal("0.00"));
        orderPO5.setPayAmount(new BigDecimal("0.00"));
        orderPO5.setAreaCode("areaCode");
        orderPO5.setOrderType(0);
        orderPO5.setServiceFee(new BigDecimal("0.00"));
        orderPO5.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO5.setSettleMode("settleMode");
        orderPO5.setFinanceRuleCode("financeRuleCode");
        orderPO5.setIsDelivery(0);
        orderPO5.setChannel("channel");
        orderPO5.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO5.setNewOrder(false);
        orderPO5.setCustomerConfirmStatus(0);
        orderPO5.setOrderPlaceUserRoleCode(0);
        orderPO5.setExchangeFlag(0);
        verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO5, OrderEventEnum.CREATE,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg",
                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));
        verify(mockOrderService).dealZeroEnjoyPayOrder("paySn", "payNo");
        verify(mockTaskQueueService).saveTaskQueue(0L, TaskQueueBizTypeEnum.AUTO_LOAN,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "CNBJ0000");

        // Confirm OrderModel.loanPayFail(...).
        final OrderPO orderPODb = new OrderPO();
        orderPODb.setOrderId(0);
        orderPODb.setOrderSn("memberName");
        orderPODb.setUserNo("userNo");
        orderPODb.setPaySn("paySn");
        orderPODb.setSellerId("sellerId");
        orderPODb.setBankPayTrxNo("bankPayTrxNo");
        orderPODb.setStoreId(0L);
        orderPODb.setRecommendStoreId(0L);
        orderPODb.setMemberName("memberName");
        orderPODb.setMemberId(0);
        orderPODb.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPODb.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPODb.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPODb.setOrderState(0);
        orderPODb.setLoanPayState(0);
        orderPODb.setPaymentName("paymentName");
        orderPODb.setPaymentCode("paymentCode");
        orderPODb.setOrderAmount(new BigDecimal("0.00"));
        orderPODb.setGoodsAmount(new BigDecimal("0.00"));
        orderPODb.setExpressFee(new BigDecimal("0.00"));
        orderPODb.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPODb.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPODb.setXzCardAmount(new BigDecimal("0.00"));
        orderPODb.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPODb.setComposePayName("composeWay");
        orderPODb.setBalanceAmount(new BigDecimal("0.00"));
        orderPODb.setPayAmount(new BigDecimal("0.00"));
        orderPODb.setAreaCode("areaCode");
        orderPODb.setOrderType(0);
        orderPODb.setServiceFee(new BigDecimal("0.00"));
        orderPODb.setServiceFeeRate(new BigDecimal("0.00"));
        orderPODb.setSettleMode("settleMode");
        orderPODb.setFinanceRuleCode("financeRuleCode");
        orderPODb.setIsDelivery(0);
        orderPODb.setChannel("channel");
        orderPODb.setChannelServiceFee(new BigDecimal("0.00"));
        orderPODb.setNewOrder(false);
        orderPODb.setCustomerConfirmStatus(0);
        orderPODb.setOrderPlaceUserRoleCode(0);
        orderPODb.setExchangeFlag(0);
        verify(mockOrderModel).loanPayFail(orderPODb, "failureReason");

        // Confirm OrderModel.loanPaySuccess(...).
        final OrderPO orderPO6 = new OrderPO();
        orderPO6.setOrderId(0);
        orderPO6.setOrderSn("memberName");
        orderPO6.setUserNo("userNo");
        orderPO6.setPaySn("paySn");
        orderPO6.setSellerId("sellerId");
        orderPO6.setBankPayTrxNo("bankPayTrxNo");
        orderPO6.setStoreId(0L);
        orderPO6.setRecommendStoreId(0L);
        orderPO6.setMemberName("memberName");
        orderPO6.setMemberId(0);
        orderPO6.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO6.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO6.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO6.setOrderState(0);
        orderPO6.setLoanPayState(0);
        orderPO6.setPaymentName("paymentName");
        orderPO6.setPaymentCode("paymentCode");
        orderPO6.setOrderAmount(new BigDecimal("0.00"));
        orderPO6.setGoodsAmount(new BigDecimal("0.00"));
        orderPO6.setExpressFee(new BigDecimal("0.00"));
        orderPO6.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO6.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO6.setXzCardAmount(new BigDecimal("0.00"));
        orderPO6.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO6.setComposePayName("composeWay");
        orderPO6.setBalanceAmount(new BigDecimal("0.00"));
        orderPO6.setPayAmount(new BigDecimal("0.00"));
        orderPO6.setAreaCode("areaCode");
        orderPO6.setOrderType(0);
        orderPO6.setServiceFee(new BigDecimal("0.00"));
        orderPO6.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO6.setSettleMode("settleMode");
        orderPO6.setFinanceRuleCode("financeRuleCode");
        orderPO6.setIsDelivery(0);
        orderPO6.setChannel("channel");
        orderPO6.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO6.setNewOrder(false);
        orderPO6.setCustomerConfirmStatus(0);
        orderPO6.setOrderPlaceUserRoleCode(0);
        orderPO6.setExchangeFlag(0);
        verify(mockOrderModel).loanPaySuccess(orderPO6, 3, 0L, "memberName");
    }
*/
    /*@Test
     */void testEnjoyPayCallBack_RabbitTemplateThrowsAmqpException() throws Exception {
        // Setup
        final PaymentNotifyVO req = new PaymentNotifyVO();
        req.setOrderOn("paySn");
        req.setPayCode("memberName");
        req.setPayTradeNo("memberName");
        req.setRelPayAmt(new BigDecimal("0.00"));
        req.setPayStatus(0);
        req.setPayWay("payWay");
        req.setBankPayTrxNos(new HashMap<>());
        req.setNewOrder(false);

        final PayInfoExtraInfoVO payInfoExtraInfoVO = new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn");

        // Configure PayService.buildPayNotifyOrderOn(...).
        final PaymentNotifyVO req1 = new PaymentNotifyVO();
        req1.setOrderOn("paySn");
        req1.setPayCode("memberName");
        req1.setPayTradeNo("memberName");
        req1.setRelPayAmt(new BigDecimal("0.00"));
        req1.setPayStatus(0);
        req1.setPayWay("payWay");
        req1.setBankPayTrxNos(new HashMap<>());
        req1.setNewOrder(false);
        when(mockPayService.buildPayNotifyOrderOn(req1,
                new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"), true)).thenReturn("memberName");

        // Configure OrderModel.getOrderByOrderSn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);
        when(mockOrderModel.getOrderByOrderSn("memberName")).thenReturn(orderPO);

        // Configure OrderPayMapper.getByPrimaryKey(...).
        final OrderPayPO orderPayPO = new OrderPayPO();
        orderPayPO.setPayId(0);
        orderPayPO.setPaySn("paySn");
        orderPayPO.setOrderSn("pOrderSn");
        orderPayPO.setPayAmount(new BigDecimal("0.00"));
        orderPayPO.setMemberId(0);
        orderPayPO.setApiPayState("0");
        orderPayPO.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPayPO.setTradeSn("memberName");
        orderPayPO.setPaymentName("paymentName");
        orderPayPO.setPaymentCode("paymentCode");
        orderPayPO.setLoanSuccess(0);
        orderPayPO.setEnjoyPayVipFlag(0);
        orderPayPO.setPayWayExtraInfo(new JSONObject(0, false));
        orderPayPO.setOutBizSource("channel");
        orderPayPO.setOutBizId("paySn");
        when(mockOrderPayMapper.getByPrimaryKey("paySn")).thenReturn(orderPayPO);

        when(mockOrderService.lambdaUpdate()).thenReturn(new LambdaUpdateChainWrapper<>(null));
        when(mockPayService.payAmount("payNo")).thenReturn(new BigDecimal("0.00"));

        // Configure OrderModel.getOrderList(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO1);
        final OrderExample example = new OrderExample();
        example.setOrderIdNotEquals(0);
        example.setUserNo("userNo");
        example.setUserMobile("userMobile");
        example.setOrderSn("memberName");
        example.setPaySn("paySn");
        when(mockOrderModel.getOrderList(eq(example), any(PagerInfo.class))).thenReturn(orderPOList);

        when(mockOrderPayProcessStrategyContext.getStrategy(0)).thenReturn(null);
        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategory storeBindCategory = new StoreBindCategory();
        storeBindCategory.setBindId(0);
        storeBindCategory.setStoreId(0L);
        storeBindCategory.setCreateVendorId(0L);
        storeBindCategory.setGoodsCategoryId1(0);
        storeBindCategory.setGoodsCategoryId2(0);
        final List<StoreBindCategory> storeBindCategories = Arrays.asList(storeBindCategory);
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                .thenReturn(storeBindCategories);

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        when(mockAccountCardFacade.defaultCard(0L)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure IOrderService.list(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("memberName");
        orderPO2.setUserNo("userNo");
        orderPO2.setPaySn("paySn");
        orderPO2.setSellerId("sellerId");
        orderPO2.setBankPayTrxNo("bankPayTrxNo");
        orderPO2.setStoreId(0L);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setComposePayName("composeWay");
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setServiceFee(new BigDecimal("0.00"));
        orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO2.setSettleMode("settleMode");
        orderPO2.setFinanceRuleCode("financeRuleCode");
        orderPO2.setIsDelivery(0);
        orderPO2.setChannel("channel");
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setCustomerConfirmStatus(0);
        orderPO2.setOrderPlaceUserRoleCode(0);
        orderPO2.setExchangeFlag(0);
        final List<OrderPO> orderPOList1 = Arrays.asList(orderPO2);
        when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(orderPOList1);

        when(mockPromotionCommonFeignClient.orderPaySuccess("memberName", 0, "paySn", "memberName", "paymentName",
                "paymentCode")).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderProductMapper.selectOne(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setStoreName("memberName");
        orderProductPO1.setGoodsName("memberName");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setSpellTeamId(0);
        orderProductPO1.setEnabledFlag(0);
        when(mockOrderProductMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderProductPO1);

        // Configure OrderProductMapper.selectList(...).
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setStoreName("memberName");
        orderProductPO2.setGoodsName("memberName");
        orderProductPO2.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO2.setProductNum(0);
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setSpellTeamId(0);
        orderProductPO2.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO2);
        when(mockOrderProductMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);
        final List<OrderPromotionSendCouponPO> orderPromotionSendCouponPOS = Arrays.asList(orderPromotionSendCouponPO);
        final OrderPromotionSendCouponExample example1 = new OrderPromotionSendCouponExample();
        example1.setSendCouponIdNotEquals(0);
        example1.setSendCouponIdIn("sendCouponIdIn");
        example1.setSendCouponId(0);
        example1.setOrderSn("memberName");
        example1.setOrderSnLike("orderSnLike");
        when(mockOrderPromotionSendCouponMapper.listByExample(example1)).thenReturn(orderPromotionSendCouponPOS);

        // Configure CouponFeignClient.getCouponByCouponId(...).
        final CouponVO couponVO = new CouponVO();
        couponVO.setCouponId(0);
        couponVO.setCouponType(0);
        couponVO.setReceivedNum(0);
        couponVO.setRandomMax(new BigDecimal("0.00"));
        couponVO.setRandomMin(new BigDecimal("0.00"));
        couponVO.setPublishNum(0);
        couponVO.setEffectiveTimeType(0);
        couponVO.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setCycle(0);
        couponVO.setState(0);
        couponVO.setStoreId(0L);
        couponVO.setUseType(0);
        when(mockCouponFeignClient.getCouponByCouponId(0)).thenReturn(couponVO);

        // Configure IOrderService.getById(...).
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("memberName");
        orderPO3.setUserNo("userNo");
        orderPO3.setPaySn("paySn");
        orderPO3.setSellerId("sellerId");
        orderPO3.setBankPayTrxNo("bankPayTrxNo");
        orderPO3.setStoreId(0L);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setComposePayName("composeWay");
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setServiceFee(new BigDecimal("0.00"));
        orderPO3.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO3.setSettleMode("settleMode");
        orderPO3.setFinanceRuleCode("financeRuleCode");
        orderPO3.setIsDelivery(0);
        orderPO3.setChannel("channel");
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setCustomerConfirmStatus(0);
        orderPO3.setOrderPlaceUserRoleCode(0);
        orderPO3.setExchangeFlag(0);
        when(mockOrderService.getById(0)).thenReturn(orderPO3);

        // Configure IOrderAmountStateRecordService.initOrderAmountState(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult = new Result<>(false, errorContext, null);
        final OrderPO orderPO4 = new OrderPO();
        orderPO4.setOrderId(0);
        orderPO4.setOrderSn("memberName");
        orderPO4.setUserNo("userNo");
        orderPO4.setPaySn("paySn");
        orderPO4.setSellerId("sellerId");
        orderPO4.setBankPayTrxNo("bankPayTrxNo");
        orderPO4.setStoreId(0L);
        orderPO4.setRecommendStoreId(0L);
        orderPO4.setMemberName("memberName");
        orderPO4.setMemberId(0);
        orderPO4.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setOrderState(0);
        orderPO4.setLoanPayState(0);
        orderPO4.setPaymentName("paymentName");
        orderPO4.setPaymentCode("paymentCode");
        orderPO4.setOrderAmount(new BigDecimal("0.00"));
        orderPO4.setGoodsAmount(new BigDecimal("0.00"));
        orderPO4.setExpressFee(new BigDecimal("0.00"));
        orderPO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO4.setComposePayName("composeWay");
        orderPO4.setBalanceAmount(new BigDecimal("0.00"));
        orderPO4.setPayAmount(new BigDecimal("0.00"));
        orderPO4.setAreaCode("areaCode");
        orderPO4.setOrderType(0);
        orderPO4.setServiceFee(new BigDecimal("0.00"));
        orderPO4.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO4.setSettleMode("settleMode");
        orderPO4.setFinanceRuleCode("financeRuleCode");
        orderPO4.setIsDelivery(0);
        orderPO4.setChannel("channel");
        orderPO4.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO4.setNewOrder(false);
        orderPO4.setCustomerConfirmStatus(0);
        orderPO4.setOrderPlaceUserRoleCode(0);
        orderPO4.setExchangeFlag(0);
        when(mockOrderAmountRecordService.initOrderAmountState(orderPO4)).thenReturn(voidResult);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO3 = new OrderProductPO();
        orderProductPO3.setOrderProductId(0L);
        orderProductPO3.setOrderSn("orderSn");
        orderProductPO3.setStoreName("memberName");
        orderProductPO3.setGoodsName("memberName");
        orderProductPO3.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO3.setProductNum(0);
        orderProductPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO3.setServiceFee(new BigDecimal("0.00"));
        orderProductPO3.setSpellTeamId(0);
        orderProductPO3.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS1 = Arrays.asList(orderProductPO3);
        when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(orderProductPOS1);

        doThrow(AmqpException.class).when(mockRabbitTemplate).convertAndSend("newmall_exchange",
                "newmall_queue_member_msg",
                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));
        when(mockIBzOldUserPoolService.insertFirstLoanOrder("userNo", "memberName")).thenReturn(false);

        // Configure IOrderExtendFinanceService.getByOrderSn(...).
        final OrderExtendFinancePO orderExtendFinancePO = new OrderExtendFinancePO();
        orderExtendFinancePO.setFinanceId(0L);
        orderExtendFinancePO.setOrderSn("orderSn");
        orderExtendFinancePO.setInterestWay(0);
        orderExtendFinancePO.setPlanInterestStartDays(0);
        orderExtendFinancePO.setPlanLoanDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockFinanceService.getByOrderSn("memberName")).thenReturn(orderExtendFinancePO);

        // Configure OrderModel.doLoanOperate(...).
        final ErrorContext errorContext1 = new ErrorContext();
        errorContext1.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext1.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult1 = new Result<>(false, errorContext1, null);
        when(mockOrderModel.doLoanOperate("memberName", 0L, "payNo")).thenReturn(voidResult1);

        // Run the test
        orderPayModelUnderTest.enjoyPayCallBack(req, payInfoExtraInfoVO);

        // Verify the results
        // Confirm PayService.paySuccessProcess(...).
        final PaymentNotifyVO req2 = new PaymentNotifyVO();
        req2.setOrderOn("paySn");
        req2.setPayCode("memberName");
        req2.setPayTradeNo("memberName");
        req2.setRelPayAmt(new BigDecimal("0.00"));
        req2.setPayStatus(0);
        req2.setPayWay("payWay");
        req2.setBankPayTrxNos(new HashMap<>());
        req2.setNewOrder(false);
        verify(mockPayService).paySuccessProcess(0, req2,
                new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"), new HashMap<>());
        verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
                new BigDecimal("0.00"));
        verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 20, 0, "订单支付完成",
                OrderCreateChannel.H5);
        verify(mockOrderProductModel).orderPaySuccessAddSales("memberName");
        verify(mockOrderService).setOrdersDeliverable(Arrays.asList("value"));

        // Confirm CouponMemberFeignClient.saveCouponMember(...).
        final CouponMember couponMember = new CouponMember();
        couponMember.setCouponId(0);
        couponMember.setCouponCode("couponCode");
        couponMember.setStoreId(0L);
        couponMember.setMemberId(0);
        couponMember.setMemberName("memberName");
        couponMember.setReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseState(0);
        couponMember.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseType(0);
        couponMember.setRandomAmount(new BigDecimal("0.00"));
        verify(mockCouponMemberFeignClient).saveCouponMember(couponMember);

        // Confirm CouponUseLogFeignClient.saveCouponUseLog(...).
        final CouponUseLog couponUseLog = new CouponUseLog();
        couponUseLog.setCouponCode("couponCode");
        couponUseLog.setMemberId(0);
        couponUseLog.setMemberName("memberName");
        couponUseLog.setStoreId(0L);
        couponUseLog.setLogType(0);
        couponUseLog.setLogTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponUseLog.setLogContent("logContent");
        verify(mockCouponUseLogFeignClient).saveCouponUseLog(couponUseLog);

        // Confirm CouponFeignClient.updateOrderCoupon(...).
        final Coupon coupon = new Coupon();
        coupon.setCouponId(0);
        coupon.setCouponName("couponName");
        coupon.setCouponContent("couponContent");
        coupon.setDescription("description");
        coupon.setReceivedNum(0);
        verify(mockCouponFeignClient).updateOrderCoupon(coupon);

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO5 = new OrderPO();
        orderPO5.setOrderId(0);
        orderPO5.setOrderSn("memberName");
        orderPO5.setUserNo("userNo");
        orderPO5.setPaySn("paySn");
        orderPO5.setSellerId("sellerId");
        orderPO5.setBankPayTrxNo("bankPayTrxNo");
        orderPO5.setStoreId(0L);
        orderPO5.setRecommendStoreId(0L);
        orderPO5.setMemberName("memberName");
        orderPO5.setMemberId(0);
        orderPO5.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO5.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO5.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO5.setOrderState(0);
        orderPO5.setLoanPayState(0);
        orderPO5.setPaymentName("paymentName");
        orderPO5.setPaymentCode("paymentCode");
        orderPO5.setOrderAmount(new BigDecimal("0.00"));
        orderPO5.setGoodsAmount(new BigDecimal("0.00"));
        orderPO5.setExpressFee(new BigDecimal("0.00"));
        orderPO5.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO5.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO5.setXzCardAmount(new BigDecimal("0.00"));
        orderPO5.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO5.setComposePayName("composeWay");
        orderPO5.setBalanceAmount(new BigDecimal("0.00"));
        orderPO5.setPayAmount(new BigDecimal("0.00"));
        orderPO5.setAreaCode("areaCode");
        orderPO5.setOrderType(0);
        orderPO5.setServiceFee(new BigDecimal("0.00"));
        orderPO5.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO5.setSettleMode("settleMode");
        orderPO5.setFinanceRuleCode("financeRuleCode");
        orderPO5.setIsDelivery(0);
        orderPO5.setChannel("channel");
        orderPO5.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO5.setNewOrder(false);
        orderPO5.setCustomerConfirmStatus(0);
        orderPO5.setOrderPlaceUserRoleCode(0);
        orderPO5.setExchangeFlag(0);
        verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO5, OrderEventEnum.CREATE,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockOrderService).dealZeroEnjoyPayOrder("paySn", "payNo");
        verify(mockTaskQueueService).saveTaskQueue(0L, TaskQueueBizTypeEnum.AUTO_LOAN,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "CNBJ0000");

        // Confirm OrderModel.loanPayFail(...).
        final OrderPO orderPODb = new OrderPO();
        orderPODb.setOrderId(0);
        orderPODb.setOrderSn("memberName");
        orderPODb.setUserNo("userNo");
        orderPODb.setPaySn("paySn");
        orderPODb.setSellerId("sellerId");
        orderPODb.setBankPayTrxNo("bankPayTrxNo");
        orderPODb.setStoreId(0L);
        orderPODb.setRecommendStoreId(0L);
        orderPODb.setMemberName("memberName");
        orderPODb.setMemberId(0);
        orderPODb.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPODb.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPODb.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPODb.setOrderState(0);
        orderPODb.setLoanPayState(0);
        orderPODb.setPaymentName("paymentName");
        orderPODb.setPaymentCode("paymentCode");
        orderPODb.setOrderAmount(new BigDecimal("0.00"));
        orderPODb.setGoodsAmount(new BigDecimal("0.00"));
        orderPODb.setExpressFee(new BigDecimal("0.00"));
        orderPODb.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPODb.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPODb.setXzCardAmount(new BigDecimal("0.00"));
        orderPODb.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPODb.setComposePayName("composeWay");
        orderPODb.setBalanceAmount(new BigDecimal("0.00"));
        orderPODb.setPayAmount(new BigDecimal("0.00"));
        orderPODb.setAreaCode("areaCode");
        orderPODb.setOrderType(0);
        orderPODb.setServiceFee(new BigDecimal("0.00"));
        orderPODb.setServiceFeeRate(new BigDecimal("0.00"));
        orderPODb.setSettleMode("settleMode");
        orderPODb.setFinanceRuleCode("financeRuleCode");
        orderPODb.setIsDelivery(0);
        orderPODb.setChannel("channel");
        orderPODb.setChannelServiceFee(new BigDecimal("0.00"));
        orderPODb.setNewOrder(false);
        orderPODb.setCustomerConfirmStatus(0);
        orderPODb.setOrderPlaceUserRoleCode(0);
        orderPODb.setExchangeFlag(0);
        verify(mockOrderModel).loanPayFail(orderPODb, "failureReason");

        // Confirm OrderModel.loanPaySuccess(...).
        final OrderPO orderPO6 = new OrderPO();
        orderPO6.setOrderId(0);
        orderPO6.setOrderSn("memberName");
        orderPO6.setUserNo("userNo");
        orderPO6.setPaySn("paySn");
        orderPO6.setSellerId("sellerId");
        orderPO6.setBankPayTrxNo("bankPayTrxNo");
        orderPO6.setStoreId(0L);
        orderPO6.setRecommendStoreId(0L);
        orderPO6.setMemberName("memberName");
        orderPO6.setMemberId(0);
        orderPO6.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO6.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO6.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO6.setOrderState(0);
        orderPO6.setLoanPayState(0);
        orderPO6.setPaymentName("paymentName");
        orderPO6.setPaymentCode("paymentCode");
        orderPO6.setOrderAmount(new BigDecimal("0.00"));
        orderPO6.setGoodsAmount(new BigDecimal("0.00"));
        orderPO6.setExpressFee(new BigDecimal("0.00"));
        orderPO6.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO6.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO6.setXzCardAmount(new BigDecimal("0.00"));
        orderPO6.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO6.setComposePayName("composeWay");
        orderPO6.setBalanceAmount(new BigDecimal("0.00"));
        orderPO6.setPayAmount(new BigDecimal("0.00"));
        orderPO6.setAreaCode("areaCode");
        orderPO6.setOrderType(0);
        orderPO6.setServiceFee(new BigDecimal("0.00"));
        orderPO6.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO6.setSettleMode("settleMode");
        orderPO6.setFinanceRuleCode("financeRuleCode");
        orderPO6.setIsDelivery(0);
        orderPO6.setChannel("channel");
        orderPO6.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO6.setNewOrder(false);
        orderPO6.setCustomerConfirmStatus(0);
        orderPO6.setOrderPlaceUserRoleCode(0);
        orderPO6.setExchangeFlag(0);
        verify(mockOrderModel).loanPaySuccess(orderPO6, 3, 0L, "memberName");
    }

    @Test
    void testMain() throws Exception {
        // Setup
        // Run the test
        OrderPayModel.main(new String[]{"args"});

        // Verify the results
    }

/*    @Test
    void testPushDingTalk() throws Exception {
        // Setup
        // Run the test
        orderPayModelUnderTest.pushDingTalk("msg", "atPhone");

        // Verify the results
    }*/
}
