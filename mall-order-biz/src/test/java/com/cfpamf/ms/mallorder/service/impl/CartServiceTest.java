package com.cfpamf.ms.mallorder.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.cfpamf.framework.autoconfigure.ZhnxServiceException;
import com.cfpamf.framework.autoconfigure.redis.lock.DistributeLock;
import com.cfpamf.ms.mallgoods.facade.api.GoodsPromotionFeignClient;
import com.cfpamf.ms.mallgoods.facade.api.ProductFeignClient;
import com.cfpamf.ms.mallgoods.facade.vo.Goods;
import com.cfpamf.ms.mallgoods.facade.vo.Product;
import com.cfpamf.ms.mallgoods.facade.vo.ProductPriceVO;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.common.constant.CartConst;
import com.cfpamf.ms.mallorder.common.util.*;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.OrderAddressDTO;
import com.cfpamf.ms.mallorder.dto.OrderSkuInfoDTO;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.mapper.CartMapper;
import com.cfpamf.ms.mallorder.model.CartModel;
import com.cfpamf.ms.mallorder.po.CartPO;
import com.cfpamf.ms.mallorder.request.CartExample;
import com.cfpamf.ms.mallorder.vo.CartListVO;
import com.cfpamf.ms.mallpromotion.api.PromotionCommonFeignClient;
import com.slodon.bbc.core.constant.GoodsConst;
import com.slodon.bbc.core.exception.BusinessException;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;
@RunWith(SpringJUnit4ClassRunner.class)
public class CartServiceTest {

	@InjectMocks
	private CartServiceImpl cartService;

	@Mock
	private ProductFeignClient productFeignClient;
	@Mock
	private GoodsPromotionFeignClient goodsPromotionFeignClient;
	@Mock
	private PromotionCommonFeignClient promotionCommonFeignClient;

	@Mock
	private OrderLocalUtils orderLocalUtils;
	@Mock
	private OrderSubmitAttributesUtils orderSubmitAttributesUtils;
	@Mock
	private CartMapper cartMapper;
	@Mock
	private CartModel cartModel;
	@Mock
	private DistributeLock distributeLock;
	@Mock
	private PromotionUtils promotionUtils;
	@Mock
	private CartUtils cartUtils;

	@Before
	public void setUp() {
		MockitoAnnotations.initMocks(this);
		TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), CartPO.class);

	}

	@Test
	public void addCartTest() {

		/**
		 * 加购
		 */
		Member member = new Member();
		member.setMemberId(1);
		Long productId = ************L;
		String areaCode = "4220003-DEFAULT";

		// mock 商品信息
		String productStr = "{\"product\":{\"createTime\":\"2022-06-13 15:26:27\",\"createAdminId\":999999," +
				"\"createVendorId\":4530004,\"updateTime\":\"2022-07-25 18:29:16\",\"updateAdminId\":999999," +
				"\"updateVendorId\":4530004,\"enabledFlag\":1,\"id\":11115154,\"productId\":************," +
				"\"goodsId\":100005280009,\"goodsName\":\"自动化专用商品，勿动\",\"specValues\":\"一分钱\"," +
				"\"specValueIds\":\"1839\",\"brandId\":null,\"brandName\":null,\"storeId\":4220003," +
				"\"storeName\":\"XQC第三方店铺53819a\",\"categoryId1\":1000001262,\"categoryId2\":1000001263," +
				"\"categoryId3\":1000001266,\"categoryPath\":\"家具建材->基础建材->其他建材\",\"productPrice\":0.01," +
				"\"marketPrice\":null,\"activityPrice\":null,\"cost\":0.00,\"taxRate\":17.00,\"productStock\":99999999," +
				"\"tryProductStock\":0,\"productStockWarning\":-1,\"productStockWarningState\":1,\"actualSales\":0," +
				"\"productCode\":null,\"barCode\":null,\"weight\":0.000,\"length\":0.000,\"width\":0.000," +
				"\"height\":0.000,\"mainImage\":\"2022/06/13/faab7ecc-873d-4e8b-a95e-082da6b31c29男装衣服01（一期）.jpeg.a.jpeg\"," +
				"\"state\":1,\"isDefault\":0,\"isEnable\":null,\"skuOutId\":\"\",\"goodsIsDistribute\":0," +
				"\"distributeParent\":null,\"costPrice\":null,\"agricDistribute\":null,\"commissionRate\":null," +
				"\"minimumPurchaseAmount\":null,\"copyProductIdParent\":null,\"productVersion\":\"1000000240511\"," +
				"\"skuMaterialCode\":null,\"skuUnit\":null,\"agricSkuId\":null,\"skuUnitName\":null,\"batchNo\":null," +
				"\"tobSupplyPrice\":null,\"tocSupplyPrice\":null},\"productExtend\":{\"createTime\":\"2022-06-13 15:26:34\"," +
				"\"createAdminId\":999999,\"createVendorId\":4530004,\"updateTime\":\"2022-06-13 15:27:02\"," +
				"\"updateAdminId\":999999,\"updateVendorId\":4530004,\"enabledFlag\":1,\"id\":2149,\"productId\":************," +
				"\"goodsId\":100005280009,\"isAreaPrice\":1,\"isInstalment\":1,\"isDistribution\":0,\"distributionNew\":0," +
				"\"isSelfLift\":0,\"couponBatch\":null,\"couponLabel\":null,\"financeRuleId\":null}," +
				"\"productFinanceRuleLabel\":null,\"productPriceBranchRange\":{\"createTime\":\"2022-06-13 15:27:02\"," +
				"\"createAdminId\":999999,\"createVendorId\":4530004,\"updateTime\":\"2022-06-13 15:27:02\"," +
				"\"updateAdminId\":999999,\"updateVendorId\":4530004,\"enabledFlag\":1,\"id\":51579," +
				"\"productId\":************,\"goodsId\":100005280009,\"financeRuleId\":null,\"areaName\":\"default\"," +
				"\"areaCode\":\"4220003-DEFAULT\",\"branchName\":null,\"branchCode\":null,\"landingPrice\":null," +
				"\"taxPrice\":0.01,\"commissionRate\":null,\"branchStock\":null,\"branchSumStock\":0," +
				"\"tryBranchCutStock\":0,\"freightCost\":0.00,\"preDiscountCost\":0.00},\"goods\":{" +
				"\"createTime\":\"2022-06-13 15:26:27\",\"createAdminId\":999999,\"createVendorId\":4530004," +
				"\"updateTime\":\"2022-12-12 20:21:57\",\"updateAdminId\":999999,\"updateVendorId\":4530004," +
				"\"enabledFlag\":1,\"id\":11783,\"goodsId\":100005280009,\"goodsName\":\"自动化专用商品，勿动\"," +
				"\"goodsBrief\":\"\",\"keyword\":null,\"brandId\":null,\"brandName\":null,\"categoryId1\":1000001262," +
				"\"categoryId2\":1000001263,\"categoryId3\":1000001266,\"categoryPath\":\"家具建材->基础建材->其他建材\"," +
				"\"marketPrice\":null,\"goodsPrice\":10.00,\"promotionPrice\":null,\"promotionType\":null," +
				"\"goodsStock\":99999999,\"virtualSales\":0,\"actualSales\":52,\"isSpec\":1,\"mainSpecId\":0," +
				"\"isSelf\":2,\"state\":3,\"isRecommend\":0,\"onlineTime\":\"2022-07-25 18:29:16\",\"storeId\":4220003," +
				"\"storeName\":\"XQC第三方店铺53819a\",\"storeIsRecommend\":1,\"storeState\":1,\"commentNumber\":0," +
				"\"isVirtualGoods\":1,\"supplierCode\":null,\"supplierName\":null,\"mainImage\":" +
				"\"2022/06/13/faab7ecc-873d-4e8b-a95e-082da6b31c29男装衣服01（一期）.jpeg.a.jpeg\",\"goodsVideo\":null," +
				"\"barCode\":null,\"serviceLabelIds\":null,\"isOffline\":0,\"isDelete\":0,\"isLock\":0," +
				"\"defaultProductId\":200005190018,\"isVatInvoice\":0,\"spuOutId\":\"\",\"isForeign\":1," +
				"\"distributeParent\":null,\"goodsIsDistribute\":0,\"channel\":null,\"goodsType\":1," +
				"\"isCountrysideCard\":0,\"channelSource\":1,\"sellType\":0,\"copyGoodsIdParent\":null," +
				"\"includedService\":null},\"judgeFlag\":true,\"productReasonTypeEnum\":null,\"companyName\":" +
				"\"中和农服（北京）农业科技有限公司\",\"couponList\":null,\"effectiveStock\":99999999}";
		ProductPriceVO productPriceByProductId = JSONObject.parseObject(productStr, ProductPriceVO.class);
		Mockito.when(orderSubmitAttributesUtils.getProductPriceByProductId(productId, areaCode, null)).thenReturn(productPriceByProductId);

		Mockito.when(cartMapper.selectOne(Mockito.any())).thenReturn(null);

		List<CartPO> listResult = new ArrayList<>();

		Mockito.when(cartMapper.listByExample(Mockito.any(CartExample.class)))
				.thenReturn(listResult);

		cartModel.mutuallyExclusiveCartAction(Mockito.anyInt(), Mockito.any(ProductPriceVO.class));

		Boolean addCart = cartService.addCart(member, productId, areaCode, 1, null);
		BizAssertUtil.isTrue(!addCart, "加购失败");

		// BizAssertUtil.notNull(cartService.countInCart(member.getMemberId()), "获取数量失败");

	}

	@Test
	public void testAddCart() throws ZhnxServiceException, BusinessException {
		// Arrange
		Member member = new Member();
		Long productId = 123L;
		String areaCode = "ABC";
		Integer number = 1;
		String financeRuleCode = "XYZ";

		// Stub getProductPrice
		ProductPriceVO productPriceVO = new ProductPriceVO();
		productPriceVO.setEffectiveStock(10);
		Product product = new Product();
		product.setState(GoodsConst.PRODUCT_STATE_1);
		productPriceVO.setProduct(product);
		Goods goods = new Goods();
		goods.setState(GoodsConst.GOODS_STATE_UPPER);
		goods.setFundsBorrowable(0);
		productPriceVO.setGoods(goods);
		when(orderSubmitAttributesUtils.getProductPriceByProductId(productId, areaCode, financeRuleCode)).thenReturn(productPriceVO);

		// Stub getCartByProduct
		CartPO cartPO = new CartPO();
		cartPO.setBuyNum(5);
		when(cartService.getCartByProduct(productId, member.getMemberId())).thenReturn(cartPO);

		// Stub addToCart
		doNothing().when(cartModel).addToCart(number, member.getMemberId(), areaCode, productPriceVO);

		// Stub mutuallyExclusiveCartAction
		doNothing().when(cartModel).mutuallyExclusiveCartAction(member.getMemberId(), productPriceVO);

		// Act
		Boolean result = cartService.addCart(member, productId, areaCode, number, financeRuleCode);

		// Assert
		assertTrue(result);
		Mockito.verify(orderSubmitAttributesUtils, Mockito.times(1)).getProductPriceByProductId(productId, areaCode, financeRuleCode);
	}

	@Test
	public void testGetCartList_EmptyCartPOList_ReturnsEmptyCartListVO() {
		// Arrange
		Integer memberId = 1;
		when(cartModel.refreshCart(eq(memberId), null, isNull(), eq(false))).thenReturn(Collections.emptyList());

		// Act
		CartListVO result = cartService.getCartList(memberId, null);

		// Assert
		assertEquals(result.getStoreCartGroupList().size(), 0);
		assertEquals(result.getInvalidList().size(), 0);
	}

	@Test
	public void testGetCartList_ValidCartPOList_ReturnsCartListVOWithValidAndInvalidLists() {
		// Arrange
		Integer memberId = 1;
		List<CartPO> cartPOList = new ArrayList<>();
		CartPO validCart = new CartPO();
		validCart.setIsChecked(0);
		validCart.setProductState(CartConst.STATTE_NORMAL);
		cartPOList.add(validCart);
		CartPO invalidCart = new CartPO();
		invalidCart.setIsChecked(0);
		invalidCart.setProductState(CartConst.STATTE_INVALID);
		cartPOList.add(invalidCart);
		when(cartModel.refreshCart(eq(memberId), null, isNull(), eq(false))).thenReturn(cartPOList);

		// Act
		CartListVO result = cartService.getCartList(memberId, null);

		// Assert
		assertEquals(result.getStoreCartGroupList().size(), 1);
		assertEquals(result.getInvalidList().size(), 1);
	}

	@Test
	public void testGetCartList_MultiplePromotions_ReturnsCartListVOWithPromotions() {
		// Arrange
		Integer memberId = 1;
		List<CartPO> cartPOList = new ArrayList<>();
		CartPO validCart = new CartPO();
		validCart.setIsChecked(0);
		validCart.setProductState(CartConst.STATTE_NORMAL);
		cartPOList.add(validCart);
		when(cartModel.refreshCart(eq(memberId), null, isNull(), eq(false))).thenReturn(cartPOList);

		// Mock product price and promotion data
		ProductPriceVO productPriceVO = new ProductPriceVO();
		Product product = new Product();
		product.setProductStock(10);
		productPriceVO.setProduct(product);
		when(orderSubmitAttributesUtils.getProductPriceByProductId(any(), anyString(), anyString())).thenReturn(productPriceVO);
		when(orderSubmitAttributesUtils.getPromotionList(any(Product.class))).thenReturn(Collections.emptyList());

		// Act
		CartListVO result = cartService.getCartList(memberId, null);

	}

	@Test
	public void testCountInCart() {
		Integer memberId = 1;
		LambdaQueryWrapper<CartPO> cartPOLambdaQueryWrapper = mock(LambdaQueryWrapper.class);
		when(cartMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(5);

		Integer result = cartService.countInCart(memberId, null);
		assertEquals(new Integer(5), result);
	}

	@Test
	public void testChangeNum() {
		// Arrange
		Integer cartId = 123;
		Integer number = 5;
		Member member = new Member();
		member.setMemberId(1);
		CartPO cartPO = new CartPO();
		cartPO.setCartId(cartId);
		cartPO.setMemberId(1);
		cartPO.setEnabledFlag(OrderConst.ENABLED_FLAG_Y);

		Product product = new Product();
		product.setProductStock(10);
		when(cartMapper.selectOne(any())).thenReturn(cartPO);
		when(productFeignClient.getProductByProductId(any())).thenReturn(product);
		when(cartMapper.updateById(any())).thenReturn(1);

		// Act
		Boolean result = cartService.changeNum(member, cartId, number);

		verify(productFeignClient).getProductByProductId(cartPO.getProductId());

		Assert.assertTrue(result);
	}

	@Test
	public void testBuildCartList() {
		// Mock input parameters
		List<OrderSkuInfoDTO> skuInfoList = new ArrayList<>();
		OrderSkuInfoDTO skuInfo1 = new OrderSkuInfoDTO();
		skuInfo1.setProductId(1L);
		skuInfo1.setProductPrice(BigDecimal.ONE);
		skuInfo1.setNumber(1);
		skuInfo1.setProductDiscountAmount(BigDecimal.ZERO);
		skuInfoList.add(skuInfo1);

		OrderSkuInfoDTO skuInfo2 = new OrderSkuInfoDTO();
		skuInfo2.setProductId(2L);
		skuInfo2.setProductPrice(BigDecimal.ONE);
		skuInfo2.setNumber(1);
		skuInfoList.add(skuInfo2);

		OrderTypeEnum orderType = OrderTypeEnum.NORMAL;
		Integer memberId = 123;
		String areaCode = "A001";
		String financeRuleCode = "F001";
		String channel = "OMS";
		OrderAddressDTO addressDTO = mock(OrderAddressDTO.class);

		// Mock getProductBatch method of orderLocalUtils
		Map<Long, ProductPriceVO> productMap = new HashMap<>();
		ProductPriceVO product1 = mock(ProductPriceVO.class);
		when(product1.getProduct()).thenReturn(mock(Product.class));
		when(product1.getProduct().getBatchNo()).thenReturn("");
		when(product1.getEffectiveStock()).thenReturn(10);
		productMap.put(1L, product1);

		ProductPriceVO product2 = mock(ProductPriceVO.class);
		when(product2.getProduct()).thenReturn(mock(Product.class));
		when(product2.getProduct().getBatchNo()).thenReturn("");
		when(product2.getEffectiveStock()).thenReturn(5);
		productMap.put(2L, product2);

		when(orderSubmitAttributesUtils.getProductBatch(anyList(), anyString(), anyString())).thenReturn(productMap);
		when(cartUtils.isCartInvalid(any(),any())).thenReturn(false);

		// Mock assembleCartPO method of cartModel
		CartPO cartPO1 = mock(CartPO.class);
		when(cartModel.assembleCartPO(anyInt(), anyInt(), anyString(), any(ProductPriceVO.class), anyBoolean()))
				.thenReturn(cartPO1);

		CartPO cartPO2 = mock(CartPO.class);
		when(cartModel.assembleCartPO(anyInt(), anyInt(), anyString(), any(ProductPriceVO.class), anyBoolean()))
				.thenReturn(cartPO2);

		// Test the method
		List<CartPO> result = cartService.buildCartList(skuInfoList, orderType, memberId, areaCode, financeRuleCode,
				channel, addressDTO);

		// Verify the interactions and assertions
		verify(orderSubmitAttributesUtils).getProductBatch(anyList(), anyString(), anyString());

		verify(cartModel, times(2)).assembleCartPO(anyInt(), anyInt(), anyString(), any(ProductPriceVO.class),
				anyBoolean());

//		verify(skuInfo1).getProductId();
//		verify(skuInfo2).getProductId();
//
//		verify(cartPO1).setProductPrice(nullable(BigDecimal.class));
//		verify(cartPO1).setOffPrice(nullable(BigDecimal.class));
//		verify(cartPO1).setLandingPrice(nullable(BigDecimal.class));
//		verify(cartPO1).setAreaCode(anyString());
//		verify(cartPO1).setProductState(anyInt());
//
//		verify(cartPO2).setProductPrice(nullable(BigDecimal.class));
//		verify(cartPO2).setOffPrice(nullable(BigDecimal.class));
//		verify(cartPO2).setLandingPrice(nullable(BigDecimal.class));
//		verify(cartPO2).setAreaCode(anyString());
//		verify(cartPO2).setProductState(anyInt());

		assertEquals(2, result.size());
	}

	@Test
	public void testGetCartByProduct() {
		Long productId = 1L;
		Integer memberId = 2;
		LambdaQueryWrapper<CartPO> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(CartPO::getMemberId, memberId);
		queryWrapper.eq(CartPO::getProductId, productId);
		queryWrapper.eq(CartPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);

		when(cartMapper.selectOne(eq(queryWrapper))).thenReturn(any());

		CartPO result = cartService.getCartByProduct(productId, memberId);

		verify(cartMapper, times(1)).selectOne(any());

	}

	@Test
	public void testUpdateCartsGoodsName() {
		// Arrange
		String cartIds = "1,2,3";
		String goodsName = "Test Goods";

		//when(cartMapper.update(null,any())).thenReturn(any());

		// Act
		//Boolean result = cartService.updateCartsGoodsName(cartIds, goodsName);

		// Assert
		//Assert.assertTrue(result);
		//verify(cartService, times(1)).update(any());
	}

}
