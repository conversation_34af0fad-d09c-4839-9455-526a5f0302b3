package com.cfpamf.ms.mallorder.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cfpamf.ms.mallgoods.facade.api.GoodsExtendFeignClient;
import com.cfpamf.ms.mallgoods.facade.api.GoodsSharecodeBindUserFeignClient;
import com.cfpamf.ms.mallgoods.facade.api.ProductFeignClient;
import com.cfpamf.ms.mallgoods.facade.request.GoodsExtendExample;
import com.cfpamf.ms.mallgoods.facade.vo.GoodsExtend;
import com.cfpamf.ms.mallgoods.facade.vo.Product;
import com.cfpamf.ms.mallorder.common.enums.OrderStatusEnum;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.StoreOrderReq;
import com.cfpamf.ms.mallorder.integration.cust.CustomerIntegration;
import com.cfpamf.ms.mallorder.mapper.OrderMapper;
import com.cfpamf.ms.mallorder.model.OrderExtendModel;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.model.OrderProductModel;
import com.cfpamf.ms.mallorder.po.BzOrderProductInstallPO;
import com.cfpamf.ms.mallorder.po.OrderExtendPO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderProductPO;
import com.cfpamf.ms.mallorder.service.impl.BzOrderSearchHistoryServiceImpl;
import com.cfpamf.ms.mallorder.service.impl.OrderInfoServiceImpl;
import com.cfpamf.ms.mallorder.service.impl.OrderProductServiceImpl;
import com.cfpamf.ms.mallorder.vo.*;
import com.gexin.fastjson.JSON;
import com.slodon.bbc.core.response.JsonResult;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@RunWith(SpringJUnit4ClassRunner.class)
public class OrderDetailTest {

	@InjectMocks
	private OrderInfoServiceImpl orderService;

	@Mock
	private OrderMapper orderMapper;

	@Mock
	private BzOrderSearchHistoryServiceImpl orderSearchHistoryService;

	@Mock
	private StringRedisTemplate stringRedisTemplate;

	@Mock
	private OrderModel orderModel;

	@Mock
	private OrderExtendModel orderExtendModel;

	@Mock
	private GoodsSharecodeBindUserFeignClient goodsSharecodeBindUserFeignClient;

	@Mock
	private CustomerIntegration customerIntegration;

	@Mock
	private OrderProductModel orderProductModel;

	@Mock
	private ProductFeignClient productFeignClient;

	@Mock
	private GoodsExtendFeignClient goodsExtendFeignClient;

	@Mock
	private OrderProductServiceImpl orderProductService;

	@Before
	public void setUp() {
		MockitoAnnotations.initMocks(this);
	}

	@Test
	public void testPageStoreOrderList() {
		StoreOrderReq queryDTO = new StoreOrderReq();
		queryDTO.setGoodsName("testGoodsName");
		queryDTO.setOrderState(OrderStatusEnum.WAIT_DELIVER.getValue());
		queryDTO.setUserMobile("testMobile");
		queryDTO.setCurrent(1);
		queryDTO.setPageSize(10);
		queryDTO.setStoreId("1");

		List<OrderPO> orderPOList = new ArrayList<>();

		when(orderMapper.pageStoreOrderList(any(), eq(queryDTO))).thenReturn(orderPOList);

		JsonResult<Page<OrderListVO>> result = orderService.pageStoreOrderList(queryDTO);

		Assert.assertNotNull(result);
	}

	@Test
	public void testDeleteSearchHistory() {
		String storeId = "testStoreId";

		when(orderSearchHistoryService.remove(any(LambdaQueryWrapper.class))).thenReturn(true);

		JsonResult result = orderService.deleteSearchHistory(storeId);
		Assert.assertNotNull(result);
		verify(orderSearchHistoryService).remove(any(LambdaQueryWrapper.class));
		verifyNoMoreInteractions(orderSearchHistoryService);
	}

	@Test
	public void testDealRemainTime() {
		// 模拟Redis返回null值
		ValueOperations valueOperations = mock(ValueOperations.class);
		when(stringRedisTemplate.opsForValue()).thenReturn(valueOperations);
		when(stringRedisTemplate.opsForValue().get("test_key")).thenReturn(null);

		// 设置默认值为10分钟
		Integer defaultValue = 10;

		Calendar calendar = Calendar.getInstance();
		Date createTime = calendar.getTime();

		// 第一次调用，应该返回10 * 60秒。
		Assert.assertTrue(10 * 60 >= orderService.dealRemainTime("test_key", createTime, defaultValue));

		// 第二次调用，应该返回0秒。
		Assert.assertEquals(0, orderService.dealRemainTime("test_key", createTime, -defaultValue));

		// 模拟Redis返回"5"字符串
		when(stringRedisTemplate.opsForValue().get("test_key")).thenReturn("5");

		// 第三次调用，应该返回5 * 60秒
		Assert.assertTrue(5 * 60 >= orderService.dealRemainTime("test_key", createTime, defaultValue));

		// 获取当前时间limitHour小时之前的时间
		calendar.add(Calendar.MINUTE, -5);
		Date cancelTime = calendar.getTime();

		long time1 = createTime.getTime();
		long time2 = cancelTime.getTime();
		long remainTime = (time1 - time2) / 1000;

		// 第四次调用，应该返回remainTime秒
		Assert.assertTrue(remainTime >= orderService.dealRemainTime("test_key", createTime, defaultValue));

	}

	@Test
	public void testOrderDetailInfoFordbc_noShareCode() {
		// mock数据
		String orderSn = "123";
		OrderPO orderPO = new OrderPO();
		orderPO.setOrderSn(orderSn);
		orderPO.setPaymentCode("alipay");
		orderPO.setOrderState(1);
		orderPO.setOrderType(2);
		orderPO.setPayTime(new Date());
		orderPO.setChannel("channel");
		orderPO.setUserNo("userNo");
		orderPO.setMemberId(1);
		orderPO.setStoreId(2L);
		orderPO.setStoreName("storeName");
		orderPO.setStoreBranchName("storeBranchName");
		orderPO.setStoreBranch("storeBranch");
		orderPO.setFinanceRuleCode("financeRuleCode");
		orderPO.setStoreActivityAmount(BigDecimal.valueOf(100));
		orderPO.setStoreVoucherAmount(BigDecimal.valueOf(50));
		orderPO.setAreaCode("areaCode");
		orderPO.setOrderPattern(1);
		orderPO.setExchangeFlag(0);

		OrderExtendPO orderExtendPO = new OrderExtendPO();
		orderExtendPO.setCustomerId("C0001");
		orderExtendPO.setManager("ZHNX001");
		orderExtendPO.setBranch("TEST");
		orderExtendPO.setBranchName("branchName");

		List<OrderProductPO> orderProducts = new ArrayList<>();
		OrderProductPO orderProductPO = new OrderProductPO();
		orderProductPO.setOrderProductId(1L);
		orderProductPO.setProductId(2L);
		orderProductPO.setGoodsId(3L);
		orderProductPO.setGoodsName("goodsName");
		orderProductPO.setProductNum(4);
		orderProductPO.setCommissionRate(BigDecimal.valueOf(0.1));
		orderProductPO.setIsDistribution(OrderConst.DISTRIBUTION_TYPE_1);
		orderProductPO.setReturnNumber(0);
		orderProductPO.setMoneyAmount(BigDecimal.valueOf(100));
		orderProductPO.setTaxPrice(BigDecimal.valueOf(20));
		orderProductPO.setLandingPrice(BigDecimal.valueOf(20));
		orderProductPO.setProductShowPrice(BigDecimal.valueOf(30));
		orderProductPO.setStoreVoucherAmount(BigDecimal.valueOf(10));
		orderProductPO.setStoreActivityAmount(BigDecimal.valueOf(5));
		orderProductPO.setChannelSkuId("83001");
		orderProductPO.setGoodsIsDistribute(1);
		orderProductPO.setAgricDistribute(1);
		orderProducts.add(orderProductPO);

		// mock方法调用
		when(orderModel.getOrderByOrderSnLambda(orderSn)).thenReturn(orderPO);
		when(orderExtendModel.getOrderExtendByOrderSnLambda(orderSn)).thenReturn(orderExtendPO);
		when(orderProductModel.getOrderProductListByOrderSn(orderSn)).thenReturn(orderProducts);

		// 调用待测试方法
		OrderInfoForDbcVO orderInfoVO = orderService.orderDetailInfoFordbc(orderSn);

		// 验证结果是否正确
		Assert.assertEquals(orderSn, orderInfoVO.getOrderSn());
		Assert.assertEquals("alipay", orderInfoVO.getPaymentCode());
		Assert.assertEquals(orderPO.getPayTime(), orderInfoVO.getPayTime());
		Assert.assertEquals("channel", orderInfoVO.getChannel());
		Assert.assertEquals("userNo", orderInfoVO.getUserNo());
		Assert.assertEquals(Long.valueOf(2), orderInfoVO.getStoreId());
		Assert.assertEquals("storeName", orderInfoVO.getStoreName());
	}

	@Test
	public void testOrderBriefInfo() {

		// 模拟getOrderByOrderSnLambda返回一个订单信息
		String orderSn = "TEST_ORDER_SN";
		OrderPO orderPO = new OrderPO();
		orderPO.setOrderSn("TEST_ORDER_SN");
		orderPO.setPaySn("TEST_PAY_SN");
		orderPO.setOrderState(1);
		orderPO.setOrderType(2);
		orderPO.setMemberId(3);
		when(orderModel.getOrderByOrderSnLambda(orderSn)).thenReturn(orderPO);

		// 模拟getOrderProductListByOrderSn返回一个订单商品列表
		List<OrderProductPO> orderProducts = new ArrayList<>();
		OrderProductPO orderProductPO = new OrderProductPO();
		orderProductPO.setGoodsId(4L);
		orderProductPO.setProductId(5L);
		orderProductPO.setProductNum(6);
		orderProducts.add(orderProductPO);
		when(orderProductModel.getOrderProductListByOrderSn(orderSn)).thenReturn(orderProducts);

		// 预期返回的订单摘要信息
		OrderBriefInfoVO expectedOrderBriefInfoVO = new OrderBriefInfoVO();
		expectedOrderBriefInfoVO.setOrderSn("TEST_ORDER_SN");
		expectedOrderBriefInfoVO.setPaySn("TEST_PAY_SN");
		expectedOrderBriefInfoVO.setOrderState(1);
		expectedOrderBriefInfoVO.setOrderType(2);
		expectedOrderBriefInfoVO.setMemberId(3);
		List<OrderProductBriefInfoVO> orderProductBriefInfoVOs = new ArrayList<>();
		OrderProductBriefInfoVO orderProductBriefInfoVO = new OrderProductBriefInfoVO();
		orderProductBriefInfoVO.setGoodsId(4L);
		orderProductBriefInfoVO.setProductId(5L);
		orderProductBriefInfoVO.setProductNum(6);
		orderProductBriefInfoVOs.add(orderProductBriefInfoVO);
		expectedOrderBriefInfoVO.setOrderProductInfos(orderProductBriefInfoVOs);

		// 实际返回的订单摘要信息
		OrderBriefInfoVO actualOrderBriefInfoVO = orderService.orderBriefInfo(orderSn);

		// 断言实际结果与预期结果一致
		Assert.assertEquals(expectedOrderBriefInfoVO, actualOrderBriefInfoVO);

	}

	@Test
	public void testUpdateData() {
		String tableName = "orders";
		String columnName = "status";
		String columnValue = "PAID";
		String idColumn = "id";
		String idValue = "123";

		List<HashMap<String, String>> dataList = new ArrayList<>();
		HashMap<String, String> data = new HashMap<>();
		data.put(idColumn, idValue);
		dataList.add(data);

		when(orderMapper.selectUpdateData(tableName, columnName, columnValue, idColumn, idValue)).thenReturn(dataList);
		when(orderMapper.updateData(tableName, columnName, columnValue, idColumn, idValue)).thenReturn(1);

		int result = orderService.updateData(tableName, columnName, columnValue, idColumn, idValue);

		Assertions.assertEquals(1, result);

		verify(orderMapper, times(1)).selectUpdateData(tableName, columnName, columnValue, idColumn, idValue);
		verify(orderMapper, times(1)).updateData(tableName, columnName, columnValue, idColumn, idValue);
	}

	@Test
	public void testUpdateProductInfo() {
		// 构建测试用例数据
		List<Long> productIds = Arrays.asList(1L, 2L, 3L);

		Product product_1 = new Product();
		product_1.setId(1L);
		product_1.setGoodsId(101L);
		product_1.setWeight(BigDecimal.ONE);

		Product product_2 = new Product();
		product_2.setId(2L);
		product_2.setGoodsId(102L);
		product_2.setWeight(BigDecimal.valueOf(2));

		Product product_3 = new Product();
		product_3.setId(3L);
		product_3.setGoodsId(103L);
		product_3.setWeight(BigDecimal.valueOf(3));

		GoodsExtend goodsExtend_1 = new GoodsExtend();
		goodsExtend_1.setGoodsId(101L);
		goodsExtend_1.setGoodsParameter("parameter_1");

		GoodsExtend goodsExtend_2 = new GoodsExtend();
		goodsExtend_2.setGoodsId(102L);
		goodsExtend_2.setGoodsParameter("parameter_2");

		GoodsExtend goodsExtend_3 = new GoodsExtend();
		goodsExtend_3.setGoodsId(103L);
		goodsExtend_3.setGoodsParameter("parameter_3");

		when(productFeignClient.getProductByProductId(1L)).thenReturn(null);
		when(productFeignClient.getProductByProductId(2L)).thenReturn(null);
		when(productFeignClient.getProductByProductId(3L)).thenReturn(null);

		when(goodsExtendFeignClient.getGoodsExtendList(any())).thenAnswer(invocationOnMock -> {
			GoodsExtendExample example = (GoodsExtendExample) invocationOnMock.getArguments()[0];
			Long goodsId = example.getGoodsId();
			if (goodsId.equals(101L)) {
				return Arrays.asList(goodsExtend_1);
			} else if (goodsId.equals(102L)) {
				return Arrays.asList(goodsExtend_2);
			} else if (goodsId.equals(103L)) {
				return Arrays.asList(goodsExtend_3);
			} else {
				return Arrays.asList();
			}
		});

		// 执行测试方法
		Boolean result = orderService.updateProductInfo(productIds);

		// 验证返回结果是否正确
		Assert.assertTrue(result);

	}

	@Test
	public void getOrderCountInfo_whenStoreIdIsBlank() {
		// Arrange
		String storeId = "";
		OrderStatusCountVO orderStatusCountVO = new OrderStatusCountVO();
		when(orderMapper.getOrderCountInfo(storeId)).thenReturn(orderStatusCountVO);

		// Act
		JsonResult<OrderStatusCountVO> result = orderService.getOrderCountInfo(storeId);

		// Assert
		Assert.assertNull(result.getData());
	}

	@Test
	public void getOrderCountInfo_whenStoreIdIsNotBlank() {
		// Arrange
		String storeId = "1";
		OrderStatusCountVO orderStatusCountVO = new OrderStatusCountVO();
		when(orderMapper.getOrderCountInfo(storeId)).thenReturn(orderStatusCountVO);

		// Act
		JsonResult<OrderStatusCountVO> result = orderService.getOrderCountInfo(storeId);

		// Assert
		Assert.assertEquals(orderStatusCountVO, result.getData());
	}
}

