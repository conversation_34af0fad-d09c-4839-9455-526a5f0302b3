package com.cfpamf.ms.mallorder.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cfpamf.ms.mallorder.mapper.OrderProductExtendMapper;
import com.cfpamf.ms.mallorder.po.OrderProductExtendPO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OrderProductExtendServiceImplTest {

    @Mock
    private OrderProductExtendMapper mockOrderProductExtendMapper;

    @InjectMocks
    private OrderProductExtendServiceImpl orderProductExtendServiceImplUnderTest;

    @Test
    public void testGetOrderProductExtendPOList4() {
        // Setup
        final OrderProductExtendPO orderProductExtendPO = new OrderProductExtendPO();
        orderProductExtendPO.setExtendId(0);
        orderProductExtendPO.setOrderProductId(0L);
        orderProductExtendPO.setOrderSn("orderSn");
        orderProductExtendPO.setPromotionGrade(0);
        orderProductExtendPO.setPromotionType(0);
        final List<OrderProductExtendPO> expectedResult = Arrays.asList(orderProductExtendPO);

        // Configure OrderProductExtendMapper.selectList(...).
        final OrderProductExtendPO orderProductExtendPO1 = new OrderProductExtendPO();
        orderProductExtendPO1.setExtendId(0);
        orderProductExtendPO1.setOrderProductId(0L);
        orderProductExtendPO1.setOrderSn("orderSn");
        orderProductExtendPO1.setPromotionGrade(0);
        orderProductExtendPO1.setPromotionType(0);
        final List<OrderProductExtendPO> orderProductExtendPOList = Arrays.asList(orderProductExtendPO1);
        when(mockOrderProductExtendMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(orderProductExtendPOList);

        // Run the test
        final List<OrderProductExtendPO> result = orderProductExtendServiceImplUnderTest.getOrderProductExtendPOList(
                Arrays.asList(0L));

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetOrderProductExtendPOList1_OrderProductExtendMapperReturnsNoItems() {
        // Setup
        when(mockOrderProductExtendMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<OrderProductExtendPO> result = orderProductExtendServiceImplUnderTest.getOrderProductExtendPOList(
                Arrays.asList(0L));

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testGetOrderProductExtendPOList2() {
        // Setup
        final OrderProductExtendPO orderProductExtendPO = new OrderProductExtendPO();
        orderProductExtendPO.setExtendId(0);
        orderProductExtendPO.setOrderProductId(0L);
        orderProductExtendPO.setOrderSn("orderSn");
        orderProductExtendPO.setPromotionGrade(0);
        orderProductExtendPO.setPromotionType(0);
        final List<OrderProductExtendPO> expectedResult = Arrays.asList(orderProductExtendPO);

        // Configure OrderProductExtendMapper.selectList(...).
        final OrderProductExtendPO orderProductExtendPO1 = new OrderProductExtendPO();
        orderProductExtendPO1.setExtendId(0);
        orderProductExtendPO1.setOrderProductId(0L);
        orderProductExtendPO1.setOrderSn("orderSn");
        orderProductExtendPO1.setPromotionGrade(0);
        orderProductExtendPO1.setPromotionType(0);
        final List<OrderProductExtendPO> orderProductExtendPOList = Arrays.asList(orderProductExtendPO1);
        when(mockOrderProductExtendMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(orderProductExtendPOList);

        // Run the test
        final List<OrderProductExtendPO> result = orderProductExtendServiceImplUnderTest.getOrderProductExtendPOList(
                "orderSn");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetOrderProductExtendPOList2_OrderProductExtendMapperReturnsNoItems() {
        // Setup
        when(mockOrderProductExtendMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<OrderProductExtendPO> result = orderProductExtendServiceImplUnderTest.getOrderProductExtendPOList(
                "orderSn");

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }


    @Test
    public void testGetOrderProductExtendPOList1() {
        // Setup
        final OrderProductExtendPO orderProductExtendPO = new OrderProductExtendPO();
        orderProductExtendPO.setExtendId(0);
        orderProductExtendPO.setOrderProductId(0L);
        orderProductExtendPO.setOrderSn("orderSn");
        orderProductExtendPO.setPromotionGrade(0);
        orderProductExtendPO.setPromotionType(0);
        final List<OrderProductExtendPO> expectedResult = Arrays.asList(orderProductExtendPO);

        // Configure OrderProductExtendMapper.selectList(...).
        final OrderProductExtendPO orderProductExtendPO1 = new OrderProductExtendPO();
        orderProductExtendPO1.setExtendId(0);
        orderProductExtendPO1.setOrderProductId(0L);
        orderProductExtendPO1.setOrderSn("orderSn");
        orderProductExtendPO1.setPromotionGrade(0);
        orderProductExtendPO1.setPromotionType(0);
        final List<OrderProductExtendPO> orderProductExtendPOList = Arrays.asList(orderProductExtendPO1);
        when(mockOrderProductExtendMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(orderProductExtendPOList);

        // Run the test
        final List<OrderProductExtendPO> result = orderProductExtendServiceImplUnderTest.getOrderProductExtendPOList(
                Arrays.asList(0L));

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetOrderProductExtendPOList1_OrderProductExtendMapperReturnsNoItems2() {
        // Setup
        when(mockOrderProductExtendMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<OrderProductExtendPO> result = orderProductExtendServiceImplUnderTest.getOrderProductExtendPOList(
                Arrays.asList(0L));

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testGetOrderProductExtendPOList3() {
        // Setup
        final OrderProductExtendPO orderProductExtendPO = new OrderProductExtendPO();
        orderProductExtendPO.setExtendId(0);
        orderProductExtendPO.setOrderProductId(0L);
        orderProductExtendPO.setOrderSn("orderSn");
        orderProductExtendPO.setPromotionGrade(0);
        orderProductExtendPO.setPromotionType(0);
        final List<OrderProductExtendPO> expectedResult = Arrays.asList(orderProductExtendPO);

        // Configure OrderProductExtendMapper.selectList(...).
        final OrderProductExtendPO orderProductExtendPO1 = new OrderProductExtendPO();
        orderProductExtendPO1.setExtendId(0);
        orderProductExtendPO1.setOrderProductId(0L);
        orderProductExtendPO1.setOrderSn("orderSn");
        orderProductExtendPO1.setPromotionGrade(0);
        orderProductExtendPO1.setPromotionType(0);
        final List<OrderProductExtendPO> orderProductExtendPOList = Arrays.asList(orderProductExtendPO1);
        when(mockOrderProductExtendMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(orderProductExtendPOList);

        // Run the test
        final List<OrderProductExtendPO> result = orderProductExtendServiceImplUnderTest.getOrderProductExtendPOList(
                "orderSn");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetOrderProductExtendPOList2_OrderProductExtendMapperReturnsNoItems2() {
        // Setup
        when(mockOrderProductExtendMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<OrderProductExtendPO> result = orderProductExtendServiceImplUnderTest.getOrderProductExtendPOList(
                "orderSn");

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }
}
