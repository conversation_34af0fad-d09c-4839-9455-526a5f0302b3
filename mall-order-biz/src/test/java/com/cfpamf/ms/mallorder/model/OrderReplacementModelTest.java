package com.cfpamf.ms.mallorder.model;

import com.cfpamf.ms.mallorder.mapper.OrderAfterSaleLogMapper;
import com.cfpamf.ms.mallorder.mapper.OrderReplacementMapper;
import com.cfpamf.ms.mallorder.po.OrderAfterSaleLogPO;
import com.cfpamf.ms.mallorder.po.OrderReplacementPO;
import com.cfpamf.ms.mallorder.request.OrderReplacementExample;
import com.slodon.bbc.core.response.PagerInfo;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderReplacementModelTest {

    @Mock
    private OrderReplacementMapper mockOrderReplacementMapper;
    @Mock
    private OrderAfterSaleLogMapper mockOrderAfterSaleLogMapper;
    @Mock
    private StringRedisTemplate mockStringRedisTemplate;

    @InjectMocks
    private OrderReplacementModel orderReplacementModelUnderTest;

    @Test
    void testSaveOrderReplacement() {
        // Setup
        final OrderReplacementPO orderReplacementPO = new OrderReplacementPO();
        orderReplacementPO.setReplacementId(0);
        orderReplacementPO.setAfsSn("afsSn");
        orderReplacementPO.setStoreId(0L);
        orderReplacementPO.setCompleteTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderReplacementPO.setState(0);

        // Configure OrderReplacementMapper.insert(...).
        final OrderReplacementPO entity = new OrderReplacementPO();
        entity.setReplacementId(0);
        entity.setAfsSn("afsSn");
        entity.setStoreId(0L);
        entity.setCompleteTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setState(0);
        when(mockOrderReplacementMapper.insert(entity)).thenReturn(0);

        // Run the test
        final Integer result = orderReplacementModelUnderTest.saveOrderReplacement(orderReplacementPO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testDeleteOrderReplacement() {
        // Setup
        when(mockOrderReplacementMapper.deleteByPrimaryKey(0)).thenReturn(0);

        // Run the test
        final Integer result = orderReplacementModelUnderTest.deleteOrderReplacement(0);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdateOrderReplacement() {
        // Setup
        final OrderReplacementPO orderReplacementPO = new OrderReplacementPO();
        orderReplacementPO.setReplacementId(0);
        orderReplacementPO.setAfsSn("afsSn");
        orderReplacementPO.setStoreId(0L);
        orderReplacementPO.setCompleteTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderReplacementPO.setState(0);

        // Configure OrderReplacementMapper.updateByPrimaryKeySelective(...).
        final OrderReplacementPO record = new OrderReplacementPO();
        record.setReplacementId(0);
        record.setAfsSn("afsSn");
        record.setStoreId(0L);
        record.setCompleteTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        record.setState(0);
        when(mockOrderReplacementMapper.updateByPrimaryKeySelective(record)).thenReturn(0);

        // Run the test
        final Integer result = orderReplacementModelUnderTest.updateOrderReplacement(orderReplacementPO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testGetOrderReplacementByReplacementId() {
        // Setup
        final OrderReplacementPO expectedResult = new OrderReplacementPO();
        expectedResult.setReplacementId(0);
        expectedResult.setAfsSn("afsSn");
        expectedResult.setStoreId(0L);
        expectedResult.setCompleteTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expectedResult.setState(0);

        // Configure OrderReplacementMapper.getByPrimaryKey(...).
        final OrderReplacementPO orderReplacementPO = new OrderReplacementPO();
        orderReplacementPO.setReplacementId(0);
        orderReplacementPO.setAfsSn("afsSn");
        orderReplacementPO.setStoreId(0L);
        orderReplacementPO.setCompleteTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderReplacementPO.setState(0);
        when(mockOrderReplacementMapper.getByPrimaryKey(0)).thenReturn(orderReplacementPO);

        // Run the test
        final OrderReplacementPO result = orderReplacementModelUnderTest.getOrderReplacementByReplacementId(0);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetOrderReplacementList() {
        // Setup
        final OrderReplacementExample example = new OrderReplacementExample();
        example.setReplacementIdNotEquals(0);
        example.setReplacementIdIn("replacementIdIn");
        example.setReplacementId(0);
        example.setStoreDeliveryTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setState(0);

        final PagerInfo pager = new PagerInfo(0, 0);
        final OrderReplacementPO orderReplacementPO = new OrderReplacementPO();
        orderReplacementPO.setReplacementId(0);
        orderReplacementPO.setAfsSn("afsSn");
        orderReplacementPO.setStoreId(0L);
        orderReplacementPO.setCompleteTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderReplacementPO.setState(0);
        final List<OrderReplacementPO> expectedResult = Arrays.asList(orderReplacementPO);

        // Configure OrderReplacementMapper.countByExample(...).
        final OrderReplacementExample example1 = new OrderReplacementExample();
        example1.setReplacementIdNotEquals(0);
        example1.setReplacementIdIn("replacementIdIn");
        example1.setReplacementId(0);
        example1.setStoreDeliveryTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setState(0);
        when(mockOrderReplacementMapper.countByExample(example1)).thenReturn(0);

        // Configure OrderReplacementMapper.listPageByExample(...).
        final OrderReplacementPO orderReplacementPO1 = new OrderReplacementPO();
        orderReplacementPO1.setReplacementId(0);
        orderReplacementPO1.setAfsSn("afsSn");
        orderReplacementPO1.setStoreId(0L);
        orderReplacementPO1.setCompleteTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderReplacementPO1.setState(0);
        final List<OrderReplacementPO> orderReplacementPOS = Arrays.asList(orderReplacementPO1);
        final OrderReplacementExample example2 = new OrderReplacementExample();
        example2.setReplacementIdNotEquals(0);
        example2.setReplacementIdIn("replacementIdIn");
        example2.setReplacementId(0);
        example2.setStoreDeliveryTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example2.setState(0);
        when(mockOrderReplacementMapper.listPageByExample(example2, 0, 0)).thenReturn(orderReplacementPOS);

        // Configure OrderReplacementMapper.listByExample(...).
        final OrderReplacementPO orderReplacementPO2 = new OrderReplacementPO();
        orderReplacementPO2.setReplacementId(0);
        orderReplacementPO2.setAfsSn("afsSn");
        orderReplacementPO2.setStoreId(0L);
        orderReplacementPO2.setCompleteTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderReplacementPO2.setState(0);
        final List<OrderReplacementPO> orderReplacementPOS1 = Arrays.asList(orderReplacementPO2);
        final OrderReplacementExample example3 = new OrderReplacementExample();
        example3.setReplacementIdNotEquals(0);
        example3.setReplacementIdIn("replacementIdIn");
        example3.setReplacementId(0);
        example3.setStoreDeliveryTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example3.setState(0);
        when(mockOrderReplacementMapper.listByExample(example3)).thenReturn(orderReplacementPOS1);

        // Run the test
        final List<OrderReplacementPO> result = orderReplacementModelUnderTest.getOrderReplacementList(example, pager);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetOrderReplacementList_OrderReplacementMapperListPageByExampleReturnsNoItems() {
        // Setup
        final OrderReplacementExample example = new OrderReplacementExample();
        example.setReplacementIdNotEquals(0);
        example.setReplacementIdIn("replacementIdIn");
        example.setReplacementId(0);
        example.setStoreDeliveryTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setState(0);

        final PagerInfo pager = new PagerInfo(0, 0);

        // Configure OrderReplacementMapper.countByExample(...).
        final OrderReplacementExample example1 = new OrderReplacementExample();
        example1.setReplacementIdNotEquals(0);
        example1.setReplacementIdIn("replacementIdIn");
        example1.setReplacementId(0);
        example1.setStoreDeliveryTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setState(0);
        when(mockOrderReplacementMapper.countByExample(example1)).thenReturn(0);

        // Configure OrderReplacementMapper.listPageByExample(...).
        final OrderReplacementExample example2 = new OrderReplacementExample();
        example2.setReplacementIdNotEquals(0);
        example2.setReplacementIdIn("replacementIdIn");
        example2.setReplacementId(0);
        example2.setStoreDeliveryTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example2.setState(0);
        when(mockOrderReplacementMapper.listPageByExample(example2, 0, 0)).thenReturn(Collections.emptyList());

        // Run the test
        final List<OrderReplacementPO> result = orderReplacementModelUnderTest.getOrderReplacementList(example, pager);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetOrderReplacementList_OrderReplacementMapperListByExampleReturnsNoItems() {
        // Setup
        final OrderReplacementExample example = new OrderReplacementExample();
        example.setReplacementIdNotEquals(0);
        example.setReplacementIdIn("replacementIdIn");
        example.setReplacementId(0);
        example.setStoreDeliveryTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setState(0);

        final PagerInfo pager = new PagerInfo(0, 0);

        // Configure OrderReplacementMapper.listByExample(...).
        final OrderReplacementExample example1 = new OrderReplacementExample();
        example1.setReplacementIdNotEquals(0);
        example1.setReplacementIdIn("replacementIdIn");
        example1.setReplacementId(0);
        example1.setStoreDeliveryTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example1.setState(0);
        when(mockOrderReplacementMapper.listByExample(example1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<OrderReplacementPO> result = orderReplacementModelUnderTest.getOrderReplacementList(example, pager);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testJobSystemFinishReplacementOrder() {
        // Setup
        when(mockStringRedisTemplate.opsForValue()).thenReturn(null);

        // Configure OrderReplacementMapper.listByExample(...).
        final OrderReplacementPO orderReplacementPO = new OrderReplacementPO();
        orderReplacementPO.setReplacementId(0);
        orderReplacementPO.setAfsSn("afsSn");
        orderReplacementPO.setStoreId(0L);
        orderReplacementPO.setCompleteTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderReplacementPO.setState(0);
        final List<OrderReplacementPO> orderReplacementPOS = Arrays.asList(orderReplacementPO);
        final OrderReplacementExample example = new OrderReplacementExample();
        example.setReplacementIdNotEquals(0);
        example.setReplacementIdIn("replacementIdIn");
        example.setReplacementId(0);
        example.setStoreDeliveryTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setState(0);
        when(mockOrderReplacementMapper.listByExample(example)).thenReturn(orderReplacementPOS);

        // Configure OrderReplacementMapper.updateByPrimaryKeySelective(...).
        final OrderReplacementPO record = new OrderReplacementPO();
        record.setReplacementId(0);
        record.setAfsSn("afsSn");
        record.setStoreId(0L);
        record.setCompleteTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        record.setState(0);
        when(mockOrderReplacementMapper.updateByPrimaryKeySelective(record)).thenReturn(0);

        // Configure OrderAfterSaleLogMapper.insert(...).
        final OrderAfterSaleLogPO entity = new OrderAfterSaleLogPO();
        entity.setLogRole(0);
        entity.setLogUserId(0L);
        entity.setLogUserName("系统");
        entity.setAfsSn("afsSn");
        entity.setAfsType(0);
        entity.setState("301");
        entity.setContent("系统自动收货，换货完成");
        entity.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockOrderAfterSaleLogMapper.insert(entity)).thenReturn(0);

        // Run the test
        final boolean result = orderReplacementModelUnderTest.jobSystemFinishReplacementOrder();

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    void testJobSystemFinishReplacementOrder_OrderReplacementMapperListByExampleReturnsNoItems() {
        // Setup
        when(mockStringRedisTemplate.opsForValue()).thenReturn(null);

        // Configure OrderReplacementMapper.listByExample(...).
        final OrderReplacementExample example = new OrderReplacementExample();
        example.setReplacementIdNotEquals(0);
        example.setReplacementIdIn("replacementIdIn");
        example.setReplacementId(0);
        example.setStoreDeliveryTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        example.setState(0);
        when(mockOrderReplacementMapper.listByExample(example)).thenReturn(Collections.emptyList());

        // Run the test
        final boolean result = orderReplacementModelUnderTest.jobSystemFinishReplacementOrder();

        // Verify the results
        assertThat(result).isTrue();
    }
}
