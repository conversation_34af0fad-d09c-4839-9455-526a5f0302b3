package com.cfpamf.ms.mallorder.controller.seller;

import com.cfpamf.ms.mallorder.model.OrderAfterServiceModel;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.po.OrderAfterPO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.service.IOrderExtendService;
import com.cfpamf.ms.mallorder.service.IOrderService;
import com.slodon.bbc.core.express.TracesResult;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;

@ExtendWith(SpringExtension.class)
@WebMvcTest(SellerLogisticsController.class)
class SellerLogisticsControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private IOrderExtendService mockOrderExtendService;
    @MockBean
    private OrderModel mockOrderModel;
    @MockBean
    private OrderAfterServiceModel mockOrderAfterServiceModel;
    @MockBean
    private StringRedisTemplate mockStringRedisTemplate;
    @MockBean
    private IOrderService mockOrderService;

    @Test
    void testGetOrderTrace() throws Exception {
        // Setup
        // Configure OrderModel.getOrderByOrderSn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("orderSn");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setOrderType(0);
        when(mockOrderModel.getOrderByOrderSn("orderSn")).thenReturn(orderPO);

        // Configure IOrderService.getOrderTrace(...).
        final TracesResult tracesResult = new TracesResult();
        tracesResult.setGoodsImage("goodsImage");
        tracesResult.setExpressName("expressName");
        tracesResult.setExpressNumber("expressNumber");
        tracesResult.setType("type");
        tracesResult.setState("state");
        final List<TracesResult> tracesResults = Arrays.asList(tracesResult);
        when(mockOrderService.getOrderTrace("orderSn", 0L)).thenReturn(tracesResults);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("seller/logistics/order/getTrace")
                        .param("orderSn", "orderSn")
                        .param("orderProductId", "0")
                        .param("distribution", "0")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testGetOrderTrace_IOrderServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure OrderModel.getOrderByOrderSn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("orderSn");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setOrderType(0);
        when(mockOrderModel.getOrderByOrderSn("orderSn")).thenReturn(orderPO);

        when(mockOrderService.getOrderTrace("orderSn", 0L)).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("seller/logistics/order/getTrace")
                        .param("orderSn", "orderSn")
                        .param("orderProductId", "0")
                        .param("distribution", "0")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testGetTrace() throws Exception {
        // Setup
        // Configure OrderAfterServiceModel.getAfterServiceByAfsSn(...).
        final OrderAfterPO orderAfterPO = new OrderAfterPO();
        orderAfterPO.setStoreId(0L);
        orderAfterPO.setOrderSn("orderSn");
        orderAfterPO.setBuyerExpressName("buyerExpressName");
        orderAfterPO.setBuyerExpressNumber("buyerExpressNumber");
        orderAfterPO.setBuyerExpressCode("buyerExpressCode");
        orderAfterPO.setContactPhone("contactPhone");
        when(mockOrderAfterServiceModel.getAfterServiceByAfsSn("afsSn")).thenReturn(orderAfterPO);

        when(mockStringRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("seller/logistics/afs/getTrace")
                        .param("afsSn", "afsSn")
                        .param("distribution", "0")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }
}
