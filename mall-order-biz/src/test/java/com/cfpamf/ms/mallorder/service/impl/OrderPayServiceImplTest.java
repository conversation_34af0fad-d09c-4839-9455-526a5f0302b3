package com.cfpamf.ms.mallorder.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.cfpamf.mallpayment.facade.vo.PaymentLoanInfoVO;
import com.cfpamf.ms.loan.facade.request.external.mall.*;
import com.cfpamf.ms.loan.facade.vo.commoms.ContractVo;
import com.cfpamf.ms.loan.facade.vo.external.mall.MallContractCodesVo;
import com.cfpamf.ms.loan.facade.vo.external.mall.MallContractContentVO;
import com.cfpamf.ms.mall.account.enums.AccountCardTypeEnum;
import com.cfpamf.ms.mall.account.vo.AccountCard;
import com.cfpamf.ms.mallorder.common.util.OrderLocalUtils;
import com.cfpamf.ms.mallorder.dto.OrderAutoPayDTO;
import com.cfpamf.ms.mallorder.enums.PayWayEnum;
import com.cfpamf.ms.mallorder.integration.loan.LoanPayIntegration;
import com.cfpamf.ms.mallorder.integration.pay.PayIntegration;
import com.cfpamf.ms.mallorder.integration.settlement.BillOperatinIntegration;
import com.cfpamf.ms.mallorder.mapper.OrderMapper;
import com.cfpamf.ms.mallorder.mapper.OrderPayMapper;
import com.cfpamf.ms.mallorder.mapper.OrderPresellMapper;
import com.cfpamf.ms.mallorder.model.OrderProductModel;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.req.OrderPayRequest;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.v2.service.OrderPayRecordService;
import com.cfpamf.ms.mallorder.v2.service.OrderPresellService;
import com.cfpamf.ms.mallorder.vo.*;
import com.cfpamf.ms.mallshop.api.StoreFeignClient;
import com.cfpamf.ms.mallshop.resp.Store;
import com.cfpamf.ms.mallshop.resp.StoreCertificate;
import com.cfpamf.smartid.client.utils.sharding.SeqEnum;
import com.cfpamf.smartid.client.utils.sharding.ShardingId;
import com.slodon.bbc.core.exception.BusinessException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OrderPayServiceImplTest {

    @Mock
    private OrderPayMapper mockOrderPayMapper;
    @Mock
    private StoreFeignClient mockStoreFeignClient;
    @Mock
    private OrderProductModel mockOrderProductModel;
    @Mock
    private OrderMapper mockOrderMapper;
    @Mock
    private IOrderProductService mockIOrderProductService;
    @Mock
    private IPayMethodService mockIPayMethodService;
    @Mock
    private ShardingId mockShardingId;
    @Mock
    private BillOperatinIntegration mockBillOperatinIntegration;
    @Mock
    private IOrderProductService mockOrderProductService;
    @Mock
    private PayIntegration mockPayIntegration;
    @Mock
    private IOrderService mockOrderService;
    @Mock
    private OrderPayRecordService mockOrderPayRecordService;
    @Mock
    private OrderPresellService mockOrderPresellService;
    @Mock
    private OrderPresellMapper mockOrderPresellMapper;
    @Mock
    private OrderLocalUtils mockOrderLocalUtils;
    @Mock
    private IOrderExtendService mockIOrderExtendService;
    @Mock
    private IOrderService mockIOrderService;
    @Mock
    private IOrderExtendFinanceService mockIOrderExtendFinanceService;
    @Mock
    private LoanPayIntegration mockLoanPayIntegration;

    @InjectMocks
    private OrderPayServiceImpl orderPayServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        orderPayServiceImplUnderTest.orderPayMapper = mockOrderPayMapper;
    }

    @Test
    public void testListOrderAutoPay() {
        // Setup
        final OrderAutoPayDTO orderAutoPayDTO = new OrderAutoPayDTO();
        orderAutoPayDTO.setOrderSn("orderSn");
        orderAutoPayDTO.setPaySn("paySn");
        orderAutoPayDTO.setParentSn("parentSn");
        orderAutoPayDTO.setStoreId("storeId");
        orderAutoPayDTO.setOrderAmount(new BigDecimal("0.00"));
        final List<OrderAutoPayDTO> expectedResult = Arrays.asList(orderAutoPayDTO);

        // Configure OrderPayMapper.listOrderAutoPay(...).
        final OrderAutoPayDTO orderAutoPayDTO1 = new OrderAutoPayDTO();
        orderAutoPayDTO1.setOrderSn("orderSn");
        orderAutoPayDTO1.setPaySn("paySn");
        orderAutoPayDTO1.setParentSn("parentSn");
        orderAutoPayDTO1.setStoreId("storeId");
        orderAutoPayDTO1.setOrderAmount(new BigDecimal("0.00"));
        final List<OrderAutoPayDTO> orderAutoPayDTOS = Arrays.asList(orderAutoPayDTO1);
        when(mockOrderPayMapper.listOrderAutoPay()).thenReturn(orderAutoPayDTOS);

        // Run the test
        final List<OrderAutoPayDTO> result = orderPayServiceImplUnderTest.listOrderAutoPay();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testListOrderAutoPay_OrderPayMapperReturnsNoItems() {
        // Setup
        when(mockOrderPayMapper.listOrderAutoPay()).thenReturn(Collections.emptyList());

        // Run the test
        final List<OrderAutoPayDTO> result = orderPayServiceImplUnderTest.listOrderAutoPay();

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testGetOrderPno() {
        // Setup
        when(mockShardingId.next(SeqEnum.PNO, "memberId")).thenReturn(0L);

        // Run the test
        final String result = orderPayServiceImplUnderTest.getOrderPno("memberId");

        // Verify the results
//        assertEquals("result", result);
    }

    @Test
    public void testGetOrderPayBriefInfoByPno() {
        // Setup
        final OrderPayBriefInfoVO expectedResult = new OrderPayBriefInfoVO();
        expectedResult.setPaySn("pno");
        final OrderBriefInfoVO orderBriefInfoVO = new OrderBriefInfoVO();
        orderBriefInfoVO.setOrderSn("orderBatchId");
        orderBriefInfoVO.setOrderType(0);
        orderBriefInfoVO.setOrderState(0);
        orderBriefInfoVO.setMemberId(0);
        final OrderProductBriefInfoVO orderProductBriefInfoVO = new OrderProductBriefInfoVO();
        orderProductBriefInfoVO.setGoodsId(0L);
        orderProductBriefInfoVO.setProductId(0L);
        orderProductBriefInfoVO.setProductNum(0);
        orderBriefInfoVO.setOrderProductInfos(Arrays.asList(orderProductBriefInfoVO));
        expectedResult.setOrderBriefInfos(Arrays.asList(orderBriefInfoVO));

        // Configure OrderMapper.selectList(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderSn("orderBatchId");
        orderPO.setPaySn("paySn");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setStoreIsSelf(0);
        orderPO.setRecommendStoreId(0L);
        orderPO.setStoreName("merchantName");
        orderPO.setMemberId(0);
        orderPO.setOrderState(0);
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setOrderType(0);
        orderPO.setEnabledFlag(0);
        orderPO.setChannel("channel");
        orderPO.setNewOrder(false);
        final List<OrderPO> orderPOS = Arrays.asList(orderPO);
        when(mockOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderPOS);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setGoodsCategoryId(0);
        orderProductPO.setGoodsCategoryPath("goodsCategoryPath");
        orderProductPO.setGoodsId(0L);
        orderProductPO.setGoodsName("commodityName");
        orderProductPO.setProductId(0L);
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO);
        when(mockOrderProductModel.getOrderProductListByOrderSn("orderBatchId")).thenReturn(orderProductPOS);

        // Run the test
        final OrderPayBriefInfoVO result = orderPayServiceImplUnderTest.getOrderPayBriefInfoByPno("pno");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetOrderPayBriefInfoByPno_OrderMapperReturnsNoItems() {
        // Setup
        final OrderPayBriefInfoVO expectedResult = new OrderPayBriefInfoVO();
        expectedResult.setPaySn("pno");
        final OrderBriefInfoVO orderBriefInfoVO = new OrderBriefInfoVO();
        orderBriefInfoVO.setOrderSn("orderBatchId");
        orderBriefInfoVO.setOrderType(0);
        orderBriefInfoVO.setOrderState(0);
        orderBriefInfoVO.setMemberId(0);
        final OrderProductBriefInfoVO orderProductBriefInfoVO = new OrderProductBriefInfoVO();
        orderProductBriefInfoVO.setGoodsId(0L);
        orderProductBriefInfoVO.setProductId(0L);
        orderProductBriefInfoVO.setProductNum(0);
        orderBriefInfoVO.setOrderProductInfos(Arrays.asList(orderProductBriefInfoVO));
        expectedResult.setOrderBriefInfos(Arrays.asList(orderBriefInfoVO));

//        when(mockOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
//        final OrderPayBriefInfoVO result = orderPayServiceImplUnderTest.getOrderPayBriefInfoByPno("pno");

        // Verify the results
//        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetOrderPayBriefInfoByPno_OrderProductModelReturnsNoItems() {
        // Setup
        final OrderPayBriefInfoVO expectedResult = new OrderPayBriefInfoVO();
        expectedResult.setPaySn("pno");
        final OrderBriefInfoVO orderBriefInfoVO = new OrderBriefInfoVO();
        orderBriefInfoVO.setOrderSn("orderBatchId");
        orderBriefInfoVO.setOrderType(0);
        orderBriefInfoVO.setOrderState(0);
        orderBriefInfoVO.setMemberId(0);
        final OrderProductBriefInfoVO orderProductBriefInfoVO = new OrderProductBriefInfoVO();
        orderProductBriefInfoVO.setGoodsId(0L);
        orderProductBriefInfoVO.setProductId(0L);
        orderProductBriefInfoVO.setProductNum(0);
        orderBriefInfoVO.setOrderProductInfos(Arrays.asList(orderProductBriefInfoVO));
        expectedResult.setOrderBriefInfos(Arrays.asList(orderBriefInfoVO));

        // Configure OrderMapper.selectList(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderSn("orderBatchId");
        orderPO.setPaySn("paySn");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setStoreIsSelf(0);
        orderPO.setRecommendStoreId(0L);
        orderPO.setStoreName("merchantName");
        orderPO.setMemberId(0);
        orderPO.setOrderState(0);
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setOrderType(0);
        orderPO.setEnabledFlag(0);
        orderPO.setChannel("channel");
        orderPO.setNewOrder(false);
        final List<OrderPO> orderPOS = Arrays.asList(orderPO);
//        when(mockOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderPOS);

//        when(mockOrderProductModel.getOrderProductListByOrderSn("orderBatchId")).thenReturn(Collections.emptyList());

        // Run the test
//        final OrderPayBriefInfoVO result = orderPayServiceImplUnderTest.getOrderPayBriefInfoByPno("pno");

        // Verify the results
//        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetByPaySn() {
        // Setup
        final OrderPayPO expectedResult = new OrderPayPO();
        expectedResult.setPayId(0);
        expectedResult.setPaySn("paySn");
        expectedResult.setOrderSn("orderSn");
        expectedResult.setPayAmount(new BigDecimal("0.00"));
        expectedResult.setEnabledFlag(0);

        // Run the test
//        final OrderPayPO result = orderPayServiceImplUnderTest.getByPaySn("paySn");

        // Verify the results
//        assertEquals(expectedResult, result);
    }

    @Test
    public void testContractPreview() {
        // Setup
        final OrderPayRequest payRequest = new OrderPayRequest();
        payRequest.setOno(Arrays.asList("value"));
        payRequest.setPayWay(PayWayEnum.ENJOY_PAY);
        payRequest.setLoanPurpose("loanPurpose");
        payRequest.setRepaymentDay("dueDay");
        payRequest.setRepaymentMode("repaymentMode");
        payRequest.setLoanPeriod(0);
        payRequest.setTempleId("templeId");
        payRequest.setPayMode("payMode");

        final MallContractContentVO expectedResult = new MallContractContentVO();
        expectedResult.setContractName("contractName");
        expectedResult.setTempleId("templeId");
        expectedResult.setContent("content");
        expectedResult.setContractType("contractType");
        expectedResult.setPdfViewUrl("pdfViewUrl");

        // Configure IOrderService.getOne(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderSn("orderBatchId");
        orderPO.setPaySn("paySn");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setStoreIsSelf(0);
        orderPO.setRecommendStoreId(0L);
        orderPO.setStoreName("merchantName");
        orderPO.setMemberId(0);
        orderPO.setOrderState(0);
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setOrderType(0);
        orderPO.setEnabledFlag(0);
        orderPO.setChannel("channel");
        orderPO.setNewOrder(false);
//        when(mockIOrderService.getOne(any(LambdaQueryWrapper.class))).thenReturn(orderPO);

        // Configure IOrderExtendFinanceService.getOne(...).
        final OrderExtendFinancePO orderExtendFinancePO = new OrderExtendFinancePO();
        orderExtendFinancePO.setFinanceId(0L);
        orderExtendFinancePO.setOrderSn("orderSn");
        orderExtendFinancePO.setDeliverMethod(0);
        orderExtendFinancePO.setAutoDeliverTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderExtendFinancePO.setCouponBatch("couponBatch");
//        when(mockIOrderExtendFinanceService.getOne(any(LambdaQueryWrapper.class))).thenReturn(orderExtendFinancePO);

//        when(mockIOrderExtendService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure OrderPresellService.queryBalanceInfoByOrderSn(...).
        final OrderPresellPO orderPresellPO = new OrderPresellPO();
        orderPresellPO.setPayNo("orderBatchId");
        orderPresellPO.setBankPayTrxNo("bankPayTrxNo");
        orderPresellPO.setPayAmount(new BigDecimal("0.00"));
        orderPresellPO.setDeadTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderPresellPO.setType(0);
        orderPresellPO.setPayStatus(0);
//        when(mockOrderPresellService.queryBalanceInfoByOrderSn("orderBatchId")).thenReturn(orderPresellPO);

        // Configure OrderProductModel.getMerchantBaseInfo(...).
        final MerchantBaseVo merchantBaseVo = new MerchantBaseVo();
        merchantBaseVo.setId(0L);
        merchantBaseVo.setCompanySealCode("companySealCode");
        merchantBaseVo.setMerchantName("merchantName");
        merchantBaseVo.setMerchantMobile("merchantMobile");
        merchantBaseVo.setCorporateName("corporateName");
        merchantBaseVo.setProvince("province");
        merchantBaseVo.setCity("city");
        merchantBaseVo.setCounty("county");
        merchantBaseVo.setTown("town");
        merchantBaseVo.setAddress("address");
        merchantBaseVo.setJoinDate("joinDate");
        merchantBaseVo.setEndDate("endDate");
        merchantBaseVo.setMerchantDesc("merchantDesc");
        merchantBaseVo.setMerchantType(0);
        merchantBaseVo.setMerchantCode("merchantCode");
        merchantBaseVo.setMerchantNo("merchantNo");
        merchantBaseVo.setCustId("custId");
        merchantBaseVo.setVirtualAcctNo("virtualAcctNo");
        merchantBaseVo.setSellerId("sellerId");
        merchantBaseVo.setDrawCardId("drawCardId");
        merchantBaseVo.setDrawCardNo("bankAccountNumber");
        merchantBaseVo.setDrawBankCode("bankCode");
        merchantBaseVo.setDrawBankName("bankBranch");
        merchantBaseVo.setDrawBankAccount("bankAccountName");
//        when(mockOrderProductModel.getMerchantBaseInfo(0L)).thenReturn(merchantBaseVo);

        // Configure BillOperatinIntegration.detailByBankAccount(...).
        final AccountCard accountCard = new AccountCard();
        accountCard.setLoanCardId("drawCardId");
        accountCard.setBankAccountName("bankAccountName");
        accountCard.setBankAccountNumber("bankAccountNumber");
        accountCard.setBankBranch("bankBranch");
        accountCard.setBankCode("bankCode");
//        when(mockBillOperatinIntegration.detailByBankAccount("UIN_PLF_STORE_ID101",
//                AccountCardTypeEnum.UNI_JS_PLF_SUP)).thenReturn(accountCard);

//        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure OrderLocalUtils.getLongProductMap(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setGoodsCategoryId(0);
        orderProductPO.setGoodsCategoryPath("goodsCategoryPath");
        orderProductPO.setGoodsId(0L);
        orderProductPO.setGoodsName("commodityName");
        orderProductPO.setProductId(0L);
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO);
//        when(mockOrderLocalUtils.getLongProductMap(orderProductPOS)).thenReturn(new HashMap<>());

        // Configure LoanPayIntegration.contractPreviewV3(...).
        final MallContractContentVO mallContractContentVO = new MallContractContentVO();
        mallContractContentVO.setContractName("contractName");
        mallContractContentVO.setTempleId("templeId");
        mallContractContentVO.setContent("content");
        mallContractContentVO.setContractType("contractType");
        mallContractContentVO.setPdfViewUrl("pdfViewUrl");
        final ContractPreviewRequest request = new ContractPreviewRequest();
        request.setOrderBatchId("orderBatchId");
        request.setTempleId("templeId");
        request.setLoanCustId("customerId");
        request.setAmount(new BigDecimal("0.00"));
        request.setApplyPeriod(0);
        request.setDueDay("dueDay");
        request.setRepaymentMode("repaymentMode");
        request.setLoanPurposeDesc("loanPurpose");
        request.setPayMode("payMode");
        request.setMerchantId("merchantId");
        request.setOrderSource("mallOrder");
        request.setRecommendStoreId(0L);
        request.setPromotionBatchNo("couponBatch");
        final MerchantInfoVO merchantInfo = new MerchantInfoVO();
        merchantInfo.setId(0L);
        merchantInfo.setCompanySealCode("companySealCode");
        merchantInfo.setMerchantName("merchantName");
        merchantInfo.setMerchantMobile("merchantMobile");
        merchantInfo.setCorporateName("corporateName");
        merchantInfo.setProvince("province");
        merchantInfo.setCity("city");
        merchantInfo.setCounty("county");
        merchantInfo.setTown("town");
        merchantInfo.setTownFlag(0);
        merchantInfo.setAddress("address");
        merchantInfo.setJoinDate("joinDate");
        merchantInfo.setEndDate("endDate");
        merchantInfo.setMerchantDesc("merchantDesc");
        merchantInfo.setMerchantType(0);
        merchantInfo.setMerchantCode("merchantCode");
        merchantInfo.setMerchantNo("merchantNo");
        merchantInfo.setCustId("custId");
        merchantInfo.setVirtualAcctNo("virtualAcctNo");
        merchantInfo.setSellerId("sellerId");
        merchantInfo.setDrawCardId("drawCardId");
        merchantInfo.setDrawCardNo("bankAccountNumber");
        merchantInfo.setDrawBankCode("bankCode");
        merchantInfo.setDrawBankName("bankBranch");
        merchantInfo.setDrawBankAccount("bankAccountName");
        merchantInfo.setOpeningBank("bankBranch");
        request.setMerchantInfo(merchantInfo);
        final MallCommodityBriefInfo mallCommodityBriefInfo = new MallCommodityBriefInfo();
        mallCommodityBriefInfo.setMerchantId("merchantId");
        mallCommodityBriefInfo.setMerchantName("merchantName");
        mallCommodityBriefInfo.setMerchantType("merchantType");
        mallCommodityBriefInfo.setRecommendStoreId(0L);
        mallCommodityBriefInfo.setCommodityId("commodityId");
        mallCommodityBriefInfo.setCommodityName("commodityName");
        mallCommodityBriefInfo.setFirstItemCategoryName("firstItemCategoryName");
        mallCommodityBriefInfo.setSecondItemCategoryName("secondItemCategoryName");
        mallCommodityBriefInfo.setThreeItemCategoryName("threeItemCategoryName");
        mallCommodityBriefInfo.setFirstItemCategoryId("categoryId1");
        mallCommodityBriefInfo.setSecondItemCategoryId("categoryId2");
        mallCommodityBriefInfo.setThreeItemCategoryId("categoryId3");
        request.setCommodityBriefInfos(Arrays.asList(mallCommodityBriefInfo));
//        when(mockLoanPayIntegration.contractPreviewV3(request)).thenReturn(mallContractContentVO);

        // Run the test
//        final MallContractContentVO result = orderPayServiceImplUnderTest.contractPreview(payRequest);

        // Verify the results
//        assertEquals(expectedResult, result);
    }

//    @Test(expected = BusinessException.class)
//    public void testContractPreview_IOrderServiceReturnsNull() {
//        // Setup
//        final OrderPayRequest payRequest = new OrderPayRequest();
//        payRequest.setOno(Arrays.asList("value"));
//        payRequest.setPayWay(PayWayEnum.ENJOY_PAY);
//        payRequest.setLoanPurpose("loanPurpose");
//        payRequest.setRepaymentDay("dueDay");
//        payRequest.setRepaymentMode("repaymentMode");
//        payRequest.setLoanPeriod(0);
//        payRequest.setTempleId("templeId");
//        payRequest.setPayMode("payMode");
//
////        when(mockIOrderService.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);
//
//        // Run the test
////        orderPayServiceImplUnderTest.contractPreview(payRequest);
//    }

    @Test
    public void testListLoanContractCode() {
        // Setup
        final OrderPayRequest payRequest = new OrderPayRequest();
        payRequest.setOno(Arrays.asList("value"));
        payRequest.setPayWay(PayWayEnum.ENJOY_PAY);
        payRequest.setLoanPurpose("loanPurpose");
        payRequest.setRepaymentDay("dueDay");
        payRequest.setRepaymentMode("repaymentMode");
        payRequest.setLoanPeriod(0);
        payRequest.setTempleId("templeId");
        payRequest.setPayMode("payMode");

        final MallContractCodesVo expectedResult = new MallContractCodesVo();
        expectedResult.setFinanceCode("financeCode");
        expectedResult.setFinanceName("financeName");
        final ContractVo contractVo = new ContractVo();
        contractVo.setContractName("contractName");
        contractVo.setTempleId("templeId");
        expectedResult.setContractVos(Arrays.asList(contractVo));

        // Configure IOrderService.getOne(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderSn("orderBatchId");
        orderPO.setPaySn("paySn");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setStoreIsSelf(0);
        orderPO.setRecommendStoreId(0L);
        orderPO.setStoreName("merchantName");
        orderPO.setMemberId(0);
        orderPO.setOrderState(0);
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setOrderType(0);
        orderPO.setEnabledFlag(0);
        orderPO.setChannel("channel");
        orderPO.setNewOrder(false);
//        when(mockIOrderService.getOne(any(LambdaQueryWrapper.class))).thenReturn(orderPO);

//        when(mockIOrderExtendService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IPayMethodService.getOne(...).
        final PayMethodPO payMethodPO = new PayMethodPO();
        payMethodPO.setId(0L);
        payMethodPO.setPayMethodCode("payMethodCode");
        payMethodPO.setPayMethodName("payMethodName");
        payMethodPO.setPayMethodDesc("payMethodDesc");
        payMethodPO.setLoanProduct("loanProduct");
//        when(mockIPayMethodService.getOne(any(LambdaQueryWrapper.class))).thenReturn(payMethodPO);

        // Configure OrderProductModel.getMerchantBaseInfo(...).
        final MerchantBaseVo merchantBaseVo = new MerchantBaseVo();
        merchantBaseVo.setId(0L);
        merchantBaseVo.setCompanySealCode("companySealCode");
        merchantBaseVo.setMerchantName("merchantName");
        merchantBaseVo.setMerchantMobile("merchantMobile");
        merchantBaseVo.setCorporateName("corporateName");
        merchantBaseVo.setProvince("province");
        merchantBaseVo.setCity("city");
        merchantBaseVo.setCounty("county");
        merchantBaseVo.setTown("town");
        merchantBaseVo.setAddress("address");
        merchantBaseVo.setJoinDate("joinDate");
        merchantBaseVo.setEndDate("endDate");
        merchantBaseVo.setMerchantDesc("merchantDesc");
        merchantBaseVo.setMerchantType(0);
        merchantBaseVo.setMerchantCode("merchantCode");
        merchantBaseVo.setMerchantNo("merchantNo");
        merchantBaseVo.setCustId("custId");
        merchantBaseVo.setVirtualAcctNo("virtualAcctNo");
        merchantBaseVo.setSellerId("sellerId");
        merchantBaseVo.setDrawCardId("drawCardId");
        merchantBaseVo.setDrawCardNo("bankAccountNumber");
        merchantBaseVo.setDrawBankCode("bankCode");
        merchantBaseVo.setDrawBankName("bankBranch");
        merchantBaseVo.setDrawBankAccount("bankAccountName");
//        when(mockOrderProductModel.getMerchantBaseInfo(0L)).thenReturn(merchantBaseVo);

        // Configure StoreFeignClient.getStoreByStoreId(...).
        final Store store = new Store();
        store.setStoreId(0L);
        store.setStoreName("storeName");
        store.setStoreLogo("storeLogo");
        final StoreCertificate storeCertificate = new StoreCertificate();
        storeCertificate.setCertificateRegistrationNum("certificateRegistrationNum");
        store.setStoreCertificate(storeCertificate);
//        when(mockStoreFeignClient.getStoreByStoreId(0L)).thenReturn(store);

//        when(mockIOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure OrderLocalUtils.getLongProductMap(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setGoodsCategoryId(0);
        orderProductPO.setGoodsCategoryPath("goodsCategoryPath");
        orderProductPO.setGoodsId(0L);
        orderProductPO.setGoodsName("commodityName");
        orderProductPO.setProductId(0L);
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO);
//        when(mockOrderLocalUtils.getLongProductMap(orderProductPOS)).thenReturn(new HashMap<>());

        // Configure LoanPayIntegration.listLoanContractCode(...).
        final MallContractCodesVo mallContractCodesVo = new MallContractCodesVo();
        mallContractCodesVo.setFinanceCode("financeCode");
        mallContractCodesVo.setFinanceName("financeName");
        final ContractVo contractVo1 = new ContractVo();
        contractVo1.setContractName("contractName");
        contractVo1.setTempleId("templeId");
        mallContractCodesVo.setContractVos(Arrays.asList(contractVo1));
        final GetMallContractCodesRequest request = new GetMallContractCodesRequest();
        request.setOrderBatchId("orderBatchId");
        request.setLoanCustId("customerId");
        final MallApplyInfo applyInfo = new MallApplyInfo();
        applyInfo.setPayMode("payMode");
        applyInfo.setLoanProductId("loanProduct");
        applyInfo.setAmount(new BigDecimal("0.00"));
        applyInfo.setApplyPeriod(0);
        applyInfo.setDueDay("dueDay");
        applyInfo.setRepaymentMode("repaymentMode");
        applyInfo.setLoanPurposeDesc("loanPurpose");
        applyInfo.setDrawCardId("drawCardId");
        applyInfo.setDrawCardNo("bankAccountNumber");
        applyInfo.setDrawBankCode("bankCode");
        applyInfo.setDrawBankName("bankBranch");
        applyInfo.setDrawBankAccount("bankAccountName");
        applyInfo.setOpeningBank("bankBranch");
        applyInfo.setOrgCode("certificateRegistrationNum");
        request.setApplyInfo(applyInfo);
        final MallOrderInfo orderInfo = new MallOrderInfo();
        orderInfo.setOrderBatchId("orderBatchId");
        orderInfo.setTradeNo("tradeNo");
        orderInfo.setOrderSource("mallOrder");
        orderInfo.setMallChannel("channel");
        orderInfo.setMerchantId("merchantId");
        orderInfo.setMerchantName("merchantName");
        orderInfo.setMerchantType("merchantType");
        orderInfo.setRecommendStoreId(0L);
        final MallCommodityInfo mallCommodityInfo = new MallCommodityInfo();
        mallCommodityInfo.setCommodityId("commodityId");
        mallCommodityInfo.setCommoditySku("commoditySku");
        mallCommodityInfo.setCommodityName("commodityName");
        mallCommodityInfo.setCommodityNumber(0);
        mallCommodityInfo.setUnitPrice(new BigDecimal("0.00"));
        mallCommodityInfo.setItemCategory("itemCategory");
        mallCommodityInfo.setItemCategoryTree("goodsCategoryPath");
        mallCommodityInfo.setFirstItemCategoryName("firstItemCategoryName");
        mallCommodityInfo.setSecondItemCategoryName("secondItemCategoryName");
        mallCommodityInfo.setThreeItemCategoryName("threeItemCategoryName");
        mallCommodityInfo.setFirstItemCategoryId("categoryId1");
        mallCommodityInfo.setSecondItemCategoryId("categoryId2");
        mallCommodityInfo.setThreeItemCategoryId("categoryId3");
        orderInfo.setCommodityInfos(Arrays.asList(mallCommodityInfo));
        request.setOrderInfo(orderInfo);
//        when(mockLoanPayIntegration.listLoanContractCode(request)).thenReturn(mallContractCodesVo);

        // Run the test
//        final MallContractCodesVo result = orderPayServiceImplUnderTest.listLoanContractCode(payRequest);

        // Verify the results
//        assertEquals(expectedResult, result);
    }

//    @Test(expected = BusinessException.class)
//    public void testListLoanContractCode_IOrderServiceReturnsNull() {
//        // Setup
//        final OrderPayRequest payRequest = new OrderPayRequest();
//        payRequest.setOno(Arrays.asList("value"));
//        payRequest.setPayWay(PayWayEnum.ENJOY_PAY);
//        payRequest.setLoanPurpose("loanPurpose");
//        payRequest.setRepaymentDay("dueDay");
//        payRequest.setRepaymentMode("repaymentMode");
//        payRequest.setLoanPeriod(0);
//        payRequest.setTempleId("templeId");
//        payRequest.setPayMode("payMode");
//
////        when(mockIOrderService.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);
//
//        // Run the test
////        orderPayServiceImplUnderTest.listLoanContractCode(payRequest);
//    }

    @Test
    public void testDealPreSellResult() {
        // Setup
        final OrderPayInfoVO orderPayInfoVO = new OrderPayInfoVO();
        orderPayInfoVO.setNeedPay(new BigDecimal("0.00"));
        orderPayInfoVO.setPaySn("paySn");
        orderPayInfoVO.setPayNo("orderBatchId");
        orderPayInfoVO.setPayTimeLimit(0L);
        orderPayInfoVO.setOrderType(0);

        // Configure OrderPresellMapper.getPreSellOrderDetailByOrderSn(...).
        final OrderPresellPO orderPresellPO = new OrderPresellPO();
        orderPresellPO.setPayNo("orderBatchId");
        orderPresellPO.setBankPayTrxNo("bankPayTrxNo");
        orderPresellPO.setPayAmount(new BigDecimal("0.00"));
        orderPresellPO.setDeadTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderPresellPO.setType(0);
        orderPresellPO.setPayStatus(0);
        final List<OrderPresellPO> orderPresellPOS = Arrays.asList(orderPresellPO);
        when(mockOrderPresellMapper.getPreSellOrderDetailByOrderSn("orderSn")).thenReturn(orderPresellPOS);

        // Run the test
        orderPayServiceImplUnderTest.dealPreSellResult("orderSn", 0, orderPayInfoVO);

        // Verify the results
    }

    @Test
    public void testDealPreSellResult_OrderPresellMapperReturnsNoItems() {
        // Setup
        final OrderPayInfoVO orderPayInfoVO = new OrderPayInfoVO();
        orderPayInfoVO.setNeedPay(new BigDecimal("0.00"));
        orderPayInfoVO.setPaySn("paySn");
        orderPayInfoVO.setPayNo("orderBatchId");
        orderPayInfoVO.setPayTimeLimit(0L);
        orderPayInfoVO.setOrderType(0);

//        when(mockOrderPresellMapper.getPreSellOrderDetailByOrderSn("orderSn")).thenReturn(Collections.emptyList());

        // Run the test
//        orderPayServiceImplUnderTest.dealPreSellResult("orderSn", 0, orderPayInfoVO);

        // Verify the results
    }

    @Test
    public void testOrderBankTrxNoMakeUp() {
        // Setup
        // Configure PayIntegration.queryLoanInfo(...).
        final PaymentLoanInfoVO paymentLoanInfoVO = new PaymentLoanInfoVO();
        paymentLoanInfoVO.setPayCode("payCode");
        paymentLoanInfoVO.setPayTradeNo("payTradeNo");
        paymentLoanInfoVO.setOrderOn("orderOn");
        paymentLoanInfoVO.setTradeId("tradeId");
        paymentLoanInfoVO.setTransactionNo("transactionNo");
//        when(mockPayIntegration.queryLoanInfo("orderSn")).thenReturn(paymentLoanInfoVO);

        // Configure IOrderService.getOne(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderSn("orderBatchId");
        orderPO.setPaySn("paySn");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setStoreIsSelf(0);
        orderPO.setRecommendStoreId(0L);
        orderPO.setStoreName("merchantName");
        orderPO.setMemberId(0);
        orderPO.setOrderState(0);
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setOrderType(0);
        orderPO.setEnabledFlag(0);
        orderPO.setChannel("channel");
        orderPO.setNewOrder(false);
//        when(mockIOrderService.getOne(any(LambdaQueryWrapper.class))).thenReturn(orderPO);

//        when(mockOrderService.lambdaUpdate()).thenReturn(new LambdaUpdateChainWrapper<>(null));
//        when(mockOrderPresellService.lambdaUpdate()).thenReturn(new LambdaUpdateChainWrapper<>(null));

        // Run the test
//        final Boolean result = orderPayServiceImplUnderTest.orderBankTrxNoMakeUp(0L);

        // Verify the results
//        assertFalse(result);
    }
}
