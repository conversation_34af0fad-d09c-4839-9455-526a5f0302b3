package com.cfpamf.ms.mallorder.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cfpamf.ms.mallorder.common.enums.PkUserTypeEnum;
import com.cfpamf.ms.mallorder.pgMapper.WineScrmStaticMapper;
import com.cfpamf.ms.mallorder.po.pgrpt.WineScrmStaticPO;
import com.cfpamf.ms.mallorder.req.pgrpt.IndicatorsRequest;
import com.cfpamf.ms.mallorder.req.pgrpt.PkMatchOpponentsRequest;
import com.cfpamf.ms.mallorder.req.pgrpt.WineScrmStaticQuery;
import com.cfpamf.ms.mallorder.vo.pgrpt.IndicatorsResponse;
import com.cfpamf.ms.mallorder.vo.pgrpt.PkMatchOpponentsResponse;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(SpringJUnit4ClassRunner.class)
public class WineScrmStaticServiceImplTest {

	@Mock
	private WineScrmStaticMapper mockWineScrmStaticMapper;

	@InjectMocks
	private WineScrmStaticServiceImpl wineScrmStaticServiceImplUnderTest;

	@Before
	public void setUp() {
		MockitoAnnotations.initMocks(this);
	}

	@Test
	public void testMatchOpponents_easyWithValidSale() {
		// Setup
		final PkMatchOpponentsRequest request = PkMatchOpponentsRequest.builder()
				.type("1")
				.userCode("userCode")
				.indicatorsType("70")
				.indicatorsMaxValue(new BigDecimal("1.1"))
				.indicatorsMinValue(new BigDecimal("0.9"))
				.matchName("matchName")
				.pageNum(0)
				.pageSize(0)
				.build();

		// Configure WineScrmStaticMapper.selectByEmpId(...).
		final WineScrmStaticPO wineScrmStaticPO = new WineScrmStaticPO();
		wineScrmStaticPO.setRptDate("rptDate");
		wineScrmStaticPO.setEmpId("areaCode");
		wineScrmStaticPO.setEmpName("empName");
		wineScrmStaticPO.setAreaCode("areaCode");
		wineScrmStaticPO.setAreaName("areaName");
		wineScrmStaticPO.setBchCode("areaCode");
		wineScrmStaticPO.setBchName("bchName");
		wineScrmStaticPO.setJoinMonths(0);
		wineScrmStaticPO.setJobCode("code");
		wineScrmStaticPO.setSmWineValidSaleAmt(new BigDecimal("0.00"));
		wineScrmStaticPO.setLmWineValidSaleAmt(new BigDecimal("100.00"));
		wineScrmStaticPO.setSmWineAvgValidSaleAmt(new BigDecimal("0.00"));
		wineScrmStaticPO.setLmWineAvgValidSaleAmt(new BigDecimal("0.00"));
		wineScrmStaticPO.setSmSyWineValidSalesAmtTargetFinishRadio(new BigDecimal("0.00"));
		wineScrmStaticPO.setLmSyWineValidSalesAmtTargetFinishRadio(new BigDecimal("0.00"));
		when(mockWineScrmStaticMapper.selectByEmpId("userCode", "2310")).thenReturn(wineScrmStaticPO);

		// Configure WineScrmStaticMapper.pageList(...).
		final WineScrmStaticPO inviteePO = new WineScrmStaticPO();
		inviteePO.setRptDate("rptDate");
		inviteePO.setEmpId("empId1");
		inviteePO.setEmpName("empName");
		inviteePO.setAreaCode("areaCode");
		inviteePO.setAreaName("areaName");
		inviteePO.setBchCode("areaCode");
		inviteePO.setBchName("bchName");
		inviteePO.setJoinMonths(0);
		inviteePO.setJobCode("code");
		inviteePO.setSmWineValidSaleAmt(new BigDecimal("0.00"));
		inviteePO.setLmWineValidSaleAmt(new BigDecimal("100.00"));
		inviteePO.setSmWineAvgValidSaleAmt(new BigDecimal("0.00"));
		inviteePO.setLmWineAvgValidSaleAmt(new BigDecimal("0.00"));
		inviteePO.setSmSyWineValidSalesAmtTargetFinishRadio(new BigDecimal("0.00"));
		inviteePO.setLmSyWineValidSalesAmtTargetFinishRadio(new BigDecimal("0.00"));
		IPage<WineScrmStaticPO> inviteeStaticPageList = new Page<>(1,10,100);
		inviteeStaticPageList.setRecords(Collections.singletonList(inviteePO));
		when(mockWineScrmStaticMapper.pageList(any(Page.class), any())).thenReturn(inviteeStaticPageList);

		// Run the test
		final Page<PkMatchOpponentsResponse> result = wineScrmStaticServiceImplUnderTest.matchOpponents(request);

		// Verify the results
		assertEquals(1, result.getRecords().size());
	}

	@Test
	public void testMatchOpponents_middleWithAvgValidSale() {
		// Setup
		final PkMatchOpponentsRequest request = PkMatchOpponentsRequest.builder()
				.type("2")
				.userCode("userCode")
				.indicatorsType("71")
				.indicatorsMaxValue(new BigDecimal("1.5"))
				.indicatorsMinValue(new BigDecimal("1.1"))
				.matchName("matchName")
				.pageNum(0)
				.pageSize(0)
				.build();

		// Configure WineScrmStaticMapper.selectByEmpId(...).
		final WineScrmStaticPO wineScrmStaticPO = new WineScrmStaticPO();
		wineScrmStaticPO.setRptDate("rptDate");
		wineScrmStaticPO.setEmpId("areaCode");
		wineScrmStaticPO.setEmpName("empName");
		wineScrmStaticPO.setAreaCode("areaCode");
		wineScrmStaticPO.setAreaName("areaName");
		wineScrmStaticPO.setBchCode("areaCode");
		wineScrmStaticPO.setBchName("bchName");
		wineScrmStaticPO.setJoinMonths(0);
		wineScrmStaticPO.setJobCode("code");
		wineScrmStaticPO.setSmWineValidSaleAmt(new BigDecimal("0.00"));
		wineScrmStaticPO.setLmWineValidSaleAmt(new BigDecimal("0.00"));
		wineScrmStaticPO.setSmWineAvgValidSaleAmt(new BigDecimal("0.00"));
		wineScrmStaticPO.setLmWineAvgValidSaleAmt(new BigDecimal("100.00"));
		wineScrmStaticPO.setSmSyWineValidSalesAmtTargetFinishRadio(new BigDecimal("0.00"));
		wineScrmStaticPO.setLmSyWineValidSalesAmtTargetFinishRadio(new BigDecimal("0.00"));
		when(mockWineScrmStaticMapper.selectByEmpId("userCode", "1083")).thenReturn(wineScrmStaticPO);

		// Configure WineScrmStaticMapper.pageList(...).
		final WineScrmStaticPO inviteePO = new WineScrmStaticPO();
		inviteePO.setRptDate("rptDate");
		inviteePO.setEmpId("empId1");
		inviteePO.setEmpName("empName");
		inviteePO.setAreaCode("areaCode");
		inviteePO.setAreaName("areaName");
		inviteePO.setBchCode("areaCode");
		inviteePO.setBchName("bchName");
		inviteePO.setJoinMonths(0);
		inviteePO.setJobCode("code");
		inviteePO.setSmWineValidSaleAmt(new BigDecimal("0.00"));
		inviteePO.setLmWineValidSaleAmt(new BigDecimal("0.00"));
		inviteePO.setSmWineAvgValidSaleAmt(new BigDecimal("0.00"));
		inviteePO.setLmWineAvgValidSaleAmt(new BigDecimal("150.00"));
		inviteePO.setSmSyWineValidSalesAmtTargetFinishRadio(new BigDecimal("0.00"));
		inviteePO.setLmSyWineValidSalesAmtTargetFinishRadio(new BigDecimal("0.00"));
		IPage<WineScrmStaticPO> inviteeStaticPageList = new Page<>(1,10,100);
		inviteeStaticPageList.setRecords(Collections.singletonList(inviteePO));
		when(mockWineScrmStaticMapper.pageList(any(Page.class), any())).thenReturn(inviteeStaticPageList);

		// Run the test
		final Page<PkMatchOpponentsResponse> result = wineScrmStaticServiceImplUnderTest.matchOpponents(request);

		// Verify the results
		assertEquals(new BigDecimal("150.00"), result.getRecords().get(0).getRecipientIndicatorsVO().getValue());
	}

	@Test
	public void testMatchOpponents_hardWithTargetFinish() {
		// Setup
		final PkMatchOpponentsRequest request = PkMatchOpponentsRequest.builder()
				.type("3")
				.userCode("userCode")
				.indicatorsType("72")
				.indicatorsMaxValue(new BigDecimal("99999"))
				.indicatorsMinValue(new BigDecimal("1.5"))
				.matchName("matchName")
				.pageNum(0)
				.pageSize(0)
				.build();

		// Configure WineScrmStaticMapper.selectByEmpId(...).
		final WineScrmStaticPO wineScrmStaticPO = new WineScrmStaticPO();
		wineScrmStaticPO.setRptDate("rptDate");
		wineScrmStaticPO.setEmpId("areaCode");
		wineScrmStaticPO.setEmpName("empName");
		wineScrmStaticPO.setAreaCode("areaCode");
		wineScrmStaticPO.setAreaName("areaName");
		wineScrmStaticPO.setBchCode("areaCode");
		wineScrmStaticPO.setBchName("bchName");
		wineScrmStaticPO.setJoinMonths(0);
		wineScrmStaticPO.setJobCode("code");
		wineScrmStaticPO.setSmWineValidSaleAmt(new BigDecimal("0.00"));
		wineScrmStaticPO.setLmWineValidSaleAmt(new BigDecimal("0.00"));
		wineScrmStaticPO.setSmWineAvgValidSaleAmt(new BigDecimal("0.00"));
		wineScrmStaticPO.setLmWineAvgValidSaleAmt(new BigDecimal("0.00"));
		wineScrmStaticPO.setSmSyWineValidSalesAmtTargetFinishRadio(new BigDecimal("0.00"));
		wineScrmStaticPO.setLmSyWineValidSalesAmtTargetFinishRadio(new BigDecimal("0.80"));
		when(mockWineScrmStaticMapper.selectByEmpId("userCode", "1081")).thenReturn(wineScrmStaticPO);

		// Configure WineScrmStaticMapper.pageList(...).
		final WineScrmStaticPO inviteePO = new WineScrmStaticPO();
		inviteePO.setRptDate("rptDate");
		inviteePO.setEmpId("empId1");
		inviteePO.setEmpName("empName");
		inviteePO.setAreaCode("areaCode");
		inviteePO.setAreaName("areaName");
		inviteePO.setBchCode("areaCode");
		inviteePO.setBchName("bchName");
		inviteePO.setJoinMonths(0);
		inviteePO.setJobCode("code");
		inviteePO.setSmWineValidSaleAmt(new BigDecimal("0.00"));
		inviteePO.setLmWineValidSaleAmt(new BigDecimal("0.00"));
		inviteePO.setSmWineAvgValidSaleAmt(new BigDecimal("0.00"));
		inviteePO.setLmWineAvgValidSaleAmt(new BigDecimal("0.00"));
		inviteePO.setSmSyWineValidSalesAmtTargetFinishRadio(new BigDecimal("0.00"));
		inviteePO.setLmSyWineValidSalesAmtTargetFinishRadio(new BigDecimal("1.05"));
		IPage<WineScrmStaticPO> inviteeStaticPageList = new Page<>(1,10,100);
		inviteeStaticPageList.setRecords(Collections.singletonList(inviteePO));
		when(mockWineScrmStaticMapper.pageList(any(Page.class), any())).thenReturn(inviteeStaticPageList);

		// Run the test
		final Page<PkMatchOpponentsResponse> result = wineScrmStaticServiceImplUnderTest.matchOpponents(request);

		// Verify the results
		assertEquals(new BigDecimal("80.00"), result.getRecords().get(0).getOriginatorIndicatorsVO().getValue());
	}

	@Test
	public void testQueryPkHistoryIndicators() {
		// Setup
		final IndicatorsRequest request = new IndicatorsRequest();
		request.setType("1");
		request.setCodeList(Arrays.asList("area1","area2"));
		request.setIndicatorTypeList(Arrays.asList("70","71","72","COMPANY_YEAR"));
		request.setStartDate("startDate");
		request.setEndDate("endDate");

		when(mockWineScrmStaticMapper.getLatestStatisticsTime()).thenReturn("2023-11-06 12:00:00");

		// Configure WineScrmStaticMapper.list(...).
		final WineScrmStaticPO wineScrmStaticPO1 = new WineScrmStaticPO();
		wineScrmStaticPO1.setRptDate("rptDate");
		wineScrmStaticPO1.setEmpId("areaCode");
		wineScrmStaticPO1.setEmpName("empName");
		wineScrmStaticPO1.setAreaCode("area1");
		wineScrmStaticPO1.setAreaName("areaName");
		wineScrmStaticPO1.setBchCode("areaCode");
		wineScrmStaticPO1.setBchName("bchName");
		wineScrmStaticPO1.setJoinMonths(0);
		wineScrmStaticPO1.setJobCode("code");
		wineScrmStaticPO1.setSmWineValidSaleAmt(new BigDecimal("10.00"));
		wineScrmStaticPO1.setLmWineValidSaleAmt(new BigDecimal("20.00"));
		wineScrmStaticPO1.setSmWineAvgValidSaleAmt(new BigDecimal("20.00"));
		wineScrmStaticPO1.setLmWineAvgValidSaleAmt(new BigDecimal("30.00"));
		wineScrmStaticPO1.setSmSyWineValidSalesAmtTargetFinishRadio(new BigDecimal("0.40"));
		wineScrmStaticPO1.setLmSyWineValidSalesAmtTargetFinishRadio(new BigDecimal("0.50"));
		final WineScrmStaticPO wineScrmStaticPO2 = new WineScrmStaticPO();
		wineScrmStaticPO2.setRptDate("rptDate");
		wineScrmStaticPO2.setEmpId("areaCode");
		wineScrmStaticPO2.setEmpName("empName");
		wineScrmStaticPO2.setAreaCode("area2");
		wineScrmStaticPO2.setAreaName("areaName");
		wineScrmStaticPO2.setBchCode("areaCode");
		wineScrmStaticPO2.setBchName("bchName");
		wineScrmStaticPO2.setJoinMonths(0);
		wineScrmStaticPO2.setJobCode("code");
		wineScrmStaticPO2.setSmWineValidSaleAmt(new BigDecimal("10.00"));
		wineScrmStaticPO2.setLmWineValidSaleAmt(new BigDecimal("20.00"));
		wineScrmStaticPO2.setSmWineAvgValidSaleAmt(new BigDecimal("20.00"));
		wineScrmStaticPO2.setLmWineAvgValidSaleAmt(new BigDecimal("30.00"));
		wineScrmStaticPO2.setSmSyWineValidSalesAmtTargetFinishRadio(new BigDecimal("0.40"));
		wineScrmStaticPO2.setLmSyWineValidSalesAmtTargetFinishRadio(new BigDecimal("0.50"));
		final List<WineScrmStaticPO> wineScrmStaticPOS = Arrays.asList(wineScrmStaticPO2,wineScrmStaticPO1);

		when(mockWineScrmStaticMapper.list(any())).thenReturn(wineScrmStaticPOS);

		// Run the test
		final List<IndicatorsResponse> result = wineScrmStaticServiceImplUnderTest.queryPkHistoryIndicators(request);

		// Verify the results
		assertEquals(2, result.size());
		assertEquals(4,result.get(0).getIndicatorsVOList().size());
	}

	@Test
	public void testQueryPkHistoryIndicators_branch() {
		// Setup
		final IndicatorsRequest request = new IndicatorsRequest();
		request.setType("2");
		request.setCodeList(Arrays.asList("branch1","branch2"));
		request.setIndicatorTypeList(Arrays.asList("70","71","72","COMPANY_YEAR"));
		request.setStartDate("startDate");
		request.setEndDate("endDate");

		when(mockWineScrmStaticMapper.getLatestStatisticsTime()).thenReturn("2023-11-06 12:00:00");

		// Configure WineScrmStaticMapper.list(...).
		final WineScrmStaticPO wineScrmStaticPO1 = new WineScrmStaticPO();
		wineScrmStaticPO1.setRptDate("rptDate");
		wineScrmStaticPO1.setEmpId("areaCode");
		wineScrmStaticPO1.setEmpName("empName");
		wineScrmStaticPO1.setAreaCode("area1");
		wineScrmStaticPO1.setAreaName("areaName");
		wineScrmStaticPO1.setBchCode("branch1");
		wineScrmStaticPO1.setBchName("bchName");
		wineScrmStaticPO1.setJoinMonths(0);
		wineScrmStaticPO1.setJobCode("code");
		wineScrmStaticPO1.setSmWineValidSaleAmt(new BigDecimal("10.00"));
		wineScrmStaticPO1.setLmWineValidSaleAmt(new BigDecimal("20.00"));
		wineScrmStaticPO1.setSmWineAvgValidSaleAmt(new BigDecimal("20.00"));
		wineScrmStaticPO1.setLmWineAvgValidSaleAmt(new BigDecimal("30.00"));
		wineScrmStaticPO1.setSmSyWineValidSalesAmtTargetFinishRadio(new BigDecimal("0.40"));
		wineScrmStaticPO1.setLmSyWineValidSalesAmtTargetFinishRadio(new BigDecimal("0.50"));
		final WineScrmStaticPO wineScrmStaticPO2 = new WineScrmStaticPO();
		wineScrmStaticPO2.setRptDate("rptDate");
		wineScrmStaticPO2.setEmpId("areaCode");
		wineScrmStaticPO2.setEmpName("empName");
		wineScrmStaticPO2.setAreaCode("area2");
		wineScrmStaticPO2.setAreaName("areaName");
		wineScrmStaticPO2.setBchCode("branch2");
		wineScrmStaticPO2.setBchName("bchName");
		wineScrmStaticPO2.setJoinMonths(0);
		wineScrmStaticPO2.setJobCode("code");
		wineScrmStaticPO2.setSmWineValidSaleAmt(new BigDecimal("10.00"));
		wineScrmStaticPO2.setLmWineValidSaleAmt(new BigDecimal("20.00"));
		wineScrmStaticPO2.setSmWineAvgValidSaleAmt(new BigDecimal("20.00"));
		wineScrmStaticPO2.setLmWineAvgValidSaleAmt(new BigDecimal("30.00"));
		wineScrmStaticPO2.setSmSyWineValidSalesAmtTargetFinishRadio(new BigDecimal("0.40"));
		wineScrmStaticPO2.setLmSyWineValidSalesAmtTargetFinishRadio(new BigDecimal("0.50"));
		final List<WineScrmStaticPO> wineScrmStaticPOS = Arrays.asList(wineScrmStaticPO2,wineScrmStaticPO1);

		when(mockWineScrmStaticMapper.list(any())).thenReturn(wineScrmStaticPOS);

		// Run the test
		final List<IndicatorsResponse> result = wineScrmStaticServiceImplUnderTest.queryPkHistoryIndicators(request);

		// Verify the results
		assertEquals(PkUserTypeEnum.BRANCH.getCode(), result.get(0).getType());
	}

	@Test
	public void testQueryPkHistoryIndicators_WineScrmStaticMapperListReturnsNoItems() {
		// Setup
		final IndicatorsRequest request = new IndicatorsRequest();
		request.setType("1");
		request.setCodeList(Arrays.asList("area1","area2"));
		request.setIndicatorTypeList(Arrays.asList("70","71","72","COMPANY_YEAR"));
		request.setStartDate("startDate");
		request.setEndDate("endDate");

		when(mockWineScrmStaticMapper.getLatestStatisticsTime()).thenReturn("rptDate");

		// Configure WineScrmStaticMapper.list(...).
		final WineScrmStaticQuery query = new WineScrmStaticQuery();
		query.setRptDate("rptDate");
		query.setEmpIdIn(Arrays.asList("value"));
		query.setEmpName("matchName");
		query.setAreaCodeIn(Arrays.asList("value"));
		query.setAreaName("matchName");
		query.setBchCodeIn(Arrays.asList("value"));
		query.setBchName("matchName");
		query.setJobCode("code");
		query.setLmWineValidSaleAmtMax(new BigDecimal("0.00"));
		query.setLmWineValidSaleAmtMin(new BigDecimal("0.00"));
		query.setLmWineAvgValidSaleAmtMax(new BigDecimal("0.00"));
		query.setLmWineAvgValidSaleAmtMin(new BigDecimal("0.00"));
		query.setLmSyWineValidSalesAmtTargetFinishRadioMax(new BigDecimal("0.00"));
		query.setLmSyWineValidSalesAmtTargetFinishRadioMin(new BigDecimal("0.00"));
		when(mockWineScrmStaticMapper.list(any())).thenReturn(Collections.emptyList());

		// Run the test
		final List<IndicatorsResponse> result = wineScrmStaticServiceImplUnderTest.queryPkHistoryIndicators(request);

		// Verify the results
		assertThat(result).isEqualTo(null);
	}

	@Test
	public void testQueryPkRealTimeIndicators() {
		// Setup
		final IndicatorsRequest request = new IndicatorsRequest();
		request.setType("1");
		request.setCodeList(Arrays.asList("branch1","branch2"));
		request.setIndicatorTypeList(Arrays.asList("70","71","72","COMPANY_YEAR"));
		request.setStartDate("startDate");
		request.setEndDate("endDate");

		when(mockWineScrmStaticMapper.getLatestStatisticsTime()).thenReturn("2023-11-06 12:00:00");

		// Configure WineScrmStaticMapper.list(...).
		final WineScrmStaticPO wineScrmStaticPO1 = new WineScrmStaticPO();
		wineScrmStaticPO1.setRptDate("rptDate");
		wineScrmStaticPO1.setEmpId("areaCode");
		wineScrmStaticPO1.setEmpName("empName");
		wineScrmStaticPO1.setAreaCode("area1");
		wineScrmStaticPO1.setAreaName("areaName");
		wineScrmStaticPO1.setBchCode("branch1");
		wineScrmStaticPO1.setBchName("bchName");
		wineScrmStaticPO1.setJoinMonths(0);
		wineScrmStaticPO1.setJobCode("code");
		wineScrmStaticPO1.setSmWineValidSaleAmt(new BigDecimal("10.00"));
		wineScrmStaticPO1.setSmWineAvgValidSaleAmt(new BigDecimal("20.00"));
		wineScrmStaticPO1.setSmSyWineValidSalesAmtTargetFinishRadio(new BigDecimal("0.40"));
		final WineScrmStaticPO wineScrmStaticPO2 = new WineScrmStaticPO();
		wineScrmStaticPO2.setRptDate("rptDate");
		wineScrmStaticPO2.setEmpId("areaCode");
		wineScrmStaticPO2.setEmpName("empName");
		wineScrmStaticPO2.setAreaCode("area2");
		wineScrmStaticPO2.setAreaName("areaName");
		wineScrmStaticPO2.setBchCode("branch2");
		wineScrmStaticPO2.setBchName("bchName");
		wineScrmStaticPO2.setJoinMonths(0);
		wineScrmStaticPO2.setJobCode("code");
		wineScrmStaticPO2.setSmWineValidSaleAmt(new BigDecimal("10.00"));
		wineScrmStaticPO2.setLmWineValidSaleAmt(new BigDecimal("20.00"));
		wineScrmStaticPO2.setSmWineAvgValidSaleAmt(new BigDecimal("20.00"));
		wineScrmStaticPO2.setLmWineAvgValidSaleAmt(new BigDecimal("30.00"));
		wineScrmStaticPO2.setSmSyWineValidSalesAmtTargetFinishRadio(new BigDecimal("0.40"));
		wineScrmStaticPO2.setLmSyWineValidSalesAmtTargetFinishRadio(new BigDecimal("0.50"));
		final List<WineScrmStaticPO> wineScrmStaticPOS = Arrays.asList(wineScrmStaticPO2,wineScrmStaticPO1);

		when(mockWineScrmStaticMapper.list(any())).thenReturn(wineScrmStaticPOS);

		// Run the test
		final List<IndicatorsResponse> result = wineScrmStaticServiceImplUnderTest.queryPkRealTimeIndicators(request);

		// Verify the results
		assertEquals(PkUserTypeEnum.AREA.getCode(), result.get(0).getType());
	}

	@Test
	public void testQueryPkRealTimeIndicators_WineScrmStaticMapperListReturnsNoItems() {
		// Setup
		final IndicatorsRequest request = new IndicatorsRequest();
		request.setType("2");
		request.setCodeList(Arrays.asList("area1","area2"));
		request.setIndicatorTypeList(Arrays.asList("70","71","72","COMPANY_YEAR"));
		request.setStartDate("startDate");
		request.setEndDate("endDate");

		when(mockWineScrmStaticMapper.getLatestStatisticsTime()).thenReturn("rptDate");

		// Configure WineScrmStaticMapper.list(...).
		final WineScrmStaticQuery query = new WineScrmStaticQuery();
		query.setRptDate("rptDate");
		query.setEmpIdIn(Arrays.asList("value"));
		query.setEmpName("matchName");
		query.setAreaCodeIn(Arrays.asList("value"));
		query.setAreaName("matchName");
		query.setBchCodeIn(Arrays.asList("value"));
		query.setBchName("matchName");
		query.setJobCode("code");
		query.setLmWineValidSaleAmtMax(new BigDecimal("0.00"));
		query.setLmWineValidSaleAmtMin(new BigDecimal("0.00"));
		query.setLmWineAvgValidSaleAmtMax(new BigDecimal("0.00"));
		query.setLmWineAvgValidSaleAmtMin(new BigDecimal("0.00"));
		query.setLmSyWineValidSalesAmtTargetFinishRadioMax(new BigDecimal("0.00"));
		query.setLmSyWineValidSalesAmtTargetFinishRadioMin(new BigDecimal("0.00"));
		when(mockWineScrmStaticMapper.list(any())).thenReturn(Collections.emptyList());

		// Run the test
		final List<IndicatorsResponse> result = wineScrmStaticServiceImplUnderTest.queryPkRealTimeIndicators(request);

		// Verify the results
		assertThat(result).isEqualTo(null);
	}

	@Test
	public void testPkSettlement() {
		// Setup
		final IndicatorsRequest request = new IndicatorsRequest();
		request.setType("1");
		request.setCodeList(Arrays.asList("area1","area2"));
		request.setIndicatorTypeList(Arrays.asList("70","71","72","COMPANY_YEAR"));
		request.setStartDate("2023-11-01 12:00:00");
		request.setEndDate("2023-11-30 12:00:00");

		when(mockWineScrmStaticMapper.getLatestStatisticsTime()).thenReturn("2023-11-06 12:00:00");

		// Configure WineScrmStaticMapper.list(...).
		final WineScrmStaticPO wineScrmStaticPO1 = new WineScrmStaticPO();
		wineScrmStaticPO1.setRptDate("rptDate");
		wineScrmStaticPO1.setEmpId("areaCode");
		wineScrmStaticPO1.setEmpName("empName");
		wineScrmStaticPO1.setAreaCode("area1");
		wineScrmStaticPO1.setAreaName("areaName");
		wineScrmStaticPO1.setBchCode("branch1");
		wineScrmStaticPO1.setBchName("bchName");
		wineScrmStaticPO1.setJoinMonths(0);
		wineScrmStaticPO1.setJobCode("code");
		wineScrmStaticPO1.setSmWineValidSaleAmt(new BigDecimal("10.00"));
		wineScrmStaticPO1.setSmWineAvgValidSaleAmt(new BigDecimal("20.00"));
		wineScrmStaticPO1.setSmSyWineValidSalesAmtTargetFinishRadio(new BigDecimal("0.40"));
		final WineScrmStaticPO wineScrmStaticPO2 = new WineScrmStaticPO();
		wineScrmStaticPO2.setRptDate("rptDate");
		wineScrmStaticPO2.setEmpId("areaCode");
		wineScrmStaticPO2.setEmpName("empName");
		wineScrmStaticPO2.setAreaCode("area2");
		wineScrmStaticPO2.setAreaName("areaName");
		wineScrmStaticPO2.setBchCode("branch2");
		wineScrmStaticPO2.setBchName("bchName");
		wineScrmStaticPO2.setJoinMonths(0);
		wineScrmStaticPO2.setJobCode("code");
		wineScrmStaticPO2.setSmWineValidSaleAmt(new BigDecimal("10.00"));
		wineScrmStaticPO2.setLmWineValidSaleAmt(new BigDecimal("20.00"));
		wineScrmStaticPO2.setSmWineAvgValidSaleAmt(new BigDecimal("20.00"));
		wineScrmStaticPO2.setLmWineAvgValidSaleAmt(new BigDecimal("30.00"));
		wineScrmStaticPO2.setSmSyWineValidSalesAmtTargetFinishRadio(new BigDecimal("0.40"));
		wineScrmStaticPO2.setLmSyWineValidSalesAmtTargetFinishRadio(new BigDecimal("0.50"));
		final List<WineScrmStaticPO> wineScrmStaticPOS = Arrays.asList(wineScrmStaticPO2,wineScrmStaticPO1);

		when(mockWineScrmStaticMapper.list(any())).thenReturn(wineScrmStaticPOS);

		// Run the test
		final List<IndicatorsResponse> result = wineScrmStaticServiceImplUnderTest.pkSettlement(request);

	}

	@Test
	public void testPkSettlement_WineScrmStaticMapperListReturnsNoItems() {
		// Setup
		final IndicatorsRequest request = new IndicatorsRequest();
		request.setType("1");
		request.setCodeList(Arrays.asList("area1","area2"));
		request.setIndicatorTypeList(Arrays.asList("70","71","72","COMPANY_YEAR"));
		request.setStartDate("2023-11-01 12:00:00");
		request.setEndDate("2023-11-30 12:00:00");

		when(mockWineScrmStaticMapper.getLatestStatisticsTime()).thenReturn("2023-11-06 12:00:00");

		// Configure WineScrmStaticMapper.list(...).
		final WineScrmStaticQuery query = new WineScrmStaticQuery();
		query.setRptDate("rptDate");
		query.setEmpIdIn(Arrays.asList("value"));
		query.setEmpName("matchName");
		query.setAreaCodeIn(Arrays.asList("value"));
		query.setAreaName("matchName");
		query.setBchCodeIn(Arrays.asList("value"));
		query.setBchName("matchName");
		query.setJobCode("code");
		query.setLmWineValidSaleAmtMax(new BigDecimal("0.00"));
		query.setLmWineValidSaleAmtMin(new BigDecimal("0.00"));
		query.setLmWineAvgValidSaleAmtMax(new BigDecimal("0.00"));
		query.setLmWineAvgValidSaleAmtMin(new BigDecimal("0.00"));
		query.setLmSyWineValidSalesAmtTargetFinishRadioMax(new BigDecimal("0.00"));
		query.setLmSyWineValidSalesAmtTargetFinishRadioMin(new BigDecimal("0.00"));
		when(mockWineScrmStaticMapper.list(any())).thenReturn(Collections.emptyList());

		// Run the test
		final List<IndicatorsResponse> result = wineScrmStaticServiceImplUnderTest.pkSettlement(request);

		// Verify the results
		assertThat(result).isEqualTo(null);
	}
}
