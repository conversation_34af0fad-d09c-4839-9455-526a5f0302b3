package com.cfpamf.ms.mallorder.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.mallgoods.facade.api.GoodsBusDistriFeignClient;
import com.cfpamf.ms.mallgoods.facade.request.GoodsLandingPricePramaDTO;
import com.cfpamf.ms.mallgoods.facade.vo.GoodsLandingPriceResponse;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.dto.*;
import com.cfpamf.ms.mallorder.enums.OrderPlaceUserRole;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.integration.cashier.CashierIntegration;
import com.cfpamf.ms.mallorder.integration.pay.PayIntegration;
import com.cfpamf.ms.mallorder.mapper.OrderLogMapper;
import com.cfpamf.ms.mallorder.mapper.OrderMapper;
import com.cfpamf.ms.mallorder.model.OrderLogModel;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.req.OrderRenewalPriceReq;
import com.cfpamf.ms.mallorder.v2.manager.GoodsStockService;
import com.cfpamf.ms.mallorder.vo.OrderSubmitVO;
import com.slodon.bbc.core.response.JsonResult;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;
@RunWith(SpringJUnit4ClassRunner.class)
public class OrderSubmitServiceTest {


    @InjectMocks
    private OrderServiceImpl mockOrderService;
    @Mock
    private OrderServiceImpl orderService;
    @Mock
    private OrderLogModel orderLogModel;
    @Mock
    private GoodsBusDistriFeignClient goodsBusDistriFeignClient;
    @Mock
    private OrderProductServiceImpl orderProductService;
    @Mock
    private OrderExtendServiceImpl orderExtendService;
    @Mock
    private OrderMapper baseMapper;
    @Mock
    private PayIntegration payIntegration;
    @Mock
    private OrderPayServiceImpl orderPayService;
    @Mock
    private OrderPriceRecordServiceImpl orderPriceRecordService;
    @Mock
    private GoodsStockService goodsStockService;
    @Mock
    private CashierIntegration cashierIntegration;
    @Mock
    private OrderLogMapper orderLogMapper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void renewalPriceTest() {
        String orderSn = "8223020125980717";

        String req = "{\"orderProductReqs\":[{\"renewalProductShowPrice\":110,\"renewalLandingPrice\":1," +
                "\"renewalBatchNo\":\"CGTB2023010400000002727\",\"orderProductId\":42990}]," +
                "\"orderSn\":\"8223020125980717\",\"renewalExpressFee\":0,\"remark\":\"改价测试\"}";
        OrderRenewalPriceReq orderRenewalPriceReq = JSONObject.parseObject(req, OrderRenewalPriceReq.class);

        //orderService.renewalPrice(orderRenewalPriceReq, 1L, "xwj");

    }
    @Test
    public void testRenewalPriceCalculation() {
        // Create a mock renewal price request
        OrderRenewalPriceReq renewalPriceReq = new OrderRenewalPriceReq();
        renewalPriceReq.setOrderSn("8223020125980717");
        renewalPriceReq.setRenewalExpressFee(new BigDecimal("5"));
        renewalPriceReq.setRemark("改价测试");
        List<OrderRenewalPriceReq.OrderProductRenewalPriceReq> orderProductReqs = new ArrayList<>();
        OrderRenewalPriceReq.OrderProductRenewalPriceReq orderProductReq = new OrderRenewalPriceReq.OrderProductRenewalPriceReq();
        orderProductReq.setOrderProductId(42990L);
        orderProductReq.setRenewalProductShowPrice(new BigDecimal("110.00"));
        orderProductReq.setRenewalBatchNo("CGTB2023010400000002727");
        orderProductReqs.add(orderProductReq);
        renewalPriceReq.setOrderProductReqs(orderProductReqs);

        // Mock external API call to get goods landing price
        GoodsLandingPriceResponse response = new GoodsLandingPriceResponse();
        response.setConvertLandingPrice(new BigDecimal("7.00"));
        JsonResult<GoodsLandingPriceResponse> priceResponseJsonResult = new JsonResult<>();
        priceResponseJsonResult.setData(response);
        when(goodsBusDistriFeignClient.getGoodsDistributionLandingPrice(any(GoodsLandingPricePramaDTO.class)))
                .thenReturn(priceResponseJsonResult);

        when(payIntegration.queryPayStatus(any())).thenReturn(false);

        // Mock order and product data
        OrderPO orderPO = new OrderPO();
        orderPO.setOrderSn("8223020125980717");
        orderPO.setStoreId(2140002L);
        orderPO.setExpressFee(new BigDecimal("3.00"));
        orderPO.setExchangeFlag(0);
        orderPO.setOrderState(10);
        orderPO.setPaymentCode("ONLINE");
        orderPO.setPaySn("1123020128370417");
        orderPO.setChannel("H5");
        orderPO.setOrderType(1);
        orderPO.setPerformanceModes("[0]");
        orderPO.setOrderAmountTotal(new BigDecimal("111"));
        orderPO.setOrderAmount(new BigDecimal(106));
        orderPO.setPayAmount(new BigDecimal("0"));
        orderPO.setMarginOrderAmount(new BigDecimal("0"));
        orderPO.setGoodsAmount(new BigDecimal("111"));
        orderPO.setExpressFeeTotal(new BigDecimal("0"));
        orderPO.setExpressFee(new BigDecimal("0"));
        orderPO.setActivityDiscountAmount(new BigDecimal("5"));
        orderPO.setStoreActivityAmount(new BigDecimal("0"));
        orderPO.setPlatformActivityAmount(new BigDecimal("0"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0"));
        orderPO.setPlatformVoucherAmount(new BigDecimal("5"));
        orderPO.setXzCardAmount(new BigDecimal("0"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0"));
        orderPO.setBalanceAmount(new BigDecimal("0"));
        orderPO.setIntegral(0);
        orderPO.setIntegralCashAmount(new BigDecimal("0"));
        orderPO.setServiceFee(new BigDecimal("0"));
        orderPO.setServiceFeeRate(new BigDecimal("0.03"));
        orderPO.setThirdpartnarFee(new BigDecimal("2.22"));
        orderPO.setThirdpartnarFeeRate(new BigDecimal("0.03"));
        orderPO.setOrderCommission(new BigDecimal("0"));
        orderPO.setBusinessCommission(new BigDecimal("0"));

        OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderSn("8223020125980717");
        orderProductPO.setStoreId(2140002L);
        orderProductPO.setStoreName("连锁18244");
        orderProductPO.setMemberId(241255);
        orderProductPO.setOrderProductId(42990L);
        orderProductPO.setBatchNo("CGTB2022121300000002478");
        orderProductPO.setProductId(200009490008L);
        orderProductPO.setChannelSkuId("************");
        orderProductPO.setGoodsName("Product X");
        orderProductPO.setProductShowPrice(new BigDecimal("111.00"));
        orderProductPO.setLandingPrice(new BigDecimal("1.25"));
        orderProductPO.setProductNum(1);
        orderProductPO.setGoodsAmountTotal(new BigDecimal("111"));
        orderProductPO.setMoneyAmount(new BigDecimal(106));
        orderProductPO.setActivityDiscountAmount(new BigDecimal("5"));
        orderProductPO.setStoreActivityAmount(new BigDecimal("0"));
        orderProductPO.setPlatformActivityAmount(new BigDecimal("0"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0"));
        orderProductPO.setPlatformVoucherAmount(new BigDecimal("5"));
        orderProductPO.setXzCardAmount(new BigDecimal("0"));
        orderProductPO.setIntegral(0);
        orderProductPO.setIntegralCashAmount(new BigDecimal("0"));
        orderProductPO.setCommissionRate(new BigDecimal("0"));
        orderProductPO.setCommissionAmount(new BigDecimal("0"));
        orderProductPO.setServiceFee(new BigDecimal("0"));
        orderProductPO.setThirdpartnarFee(new BigDecimal("2.22"));
        orderProductPO.setOrderCommission(new BigDecimal("0"));
        orderProductPO.setBusinessCommission(new BigDecimal("0"));
        orderProductPO.setReturnNumber(0);

        OrderPayPO orderPayPO = new OrderPayPO();
        orderPayPO.setPayId(20216);
        orderPayPO.setPaySn("1123020128370417");
        orderPayPO.setOrderSn("8223020125980717");
        orderPayPO.setPayAmount(new BigDecimal("106"));
        orderPayPO.setMemberId(241255);
        orderPayPO.setApiPayState("0");
        orderPayPO.setPaymentName("在线支付");
        orderPayPO.setPaymentCode("ONLINE");
        orderPayPO.setLoanSuccess(0);
        orderPayPO.setEnjoyPayVipFlag(0);

        //OrderServiceImpl orderService = Mockito.mock(OrderServiceImpl.class);
        Mockito.when(baseMapper.selectOne(any())).thenReturn(orderPO);
        Mockito.when(orderProductService.listByOrderSn(Mockito.anyString())).thenReturn(Collections.singletonList(orderProductPO));
        Mockito.when(orderExtendService.getOrderExtendByOrderSn(Mockito.anyString())).thenReturn(new OrderExtendPO());
        Mockito.when(orderPayService.getByPaySn(any())).thenReturn(orderPayPO);

        // Call the method to calculate renewal price
        Boolean result = mockOrderService.renewalPrice(renewalPriceReq, 1L,"Test User");

        // mock 金额更新
        when(baseMapper.update(any(),any())).thenReturn(1);
        when(orderPayService.update(any(),any())).thenReturn(true);
        when(orderProductService.updateBatchById(any())).thenReturn(true);
        when(orderPriceRecordService.save(any())).thenReturn(true);
        when(orderLogMapper.insert(any())).thenReturn(1);

        // mock 库存、收银台
        doNothing().when(goodsStockService).goodsStock(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any()
                , any(), any(), any(), any(), any(), any(),any());
        when(cashierIntegration.pushToCashier(any(),any())).thenReturn(true);

        // Call the method to calculate renewal price
        Boolean result1 = mockOrderService.renewalPrice(renewalPriceReq, orderPO, new OrderExtendPO(),
                Collections.singletonMap(42990L, orderProductPO),
                new OrderAmountDP(orderPO, Collections.singletonList(orderProductPO)),
                "Test User");

        // Check that the resulting renewal prices are as expected
        assertEquals(Boolean.TRUE, result);
        assertEquals(Boolean.TRUE, result1);
    }
    @Test
    public void testCreatePlacingSelfGetOrder() {
        // SetUp

        final OrderSubmitVO expectedResult = new OrderSubmitVO();
        expectedResult.setPaySn("paySn");
        expectedResult.setOrderSnList(Arrays.asList("value"));

        // Configure OrderCreateHelper.buildOrderAddressByPointId(...).
        final OrderAddressDTO orderAddressDTO = new OrderAddressDTO();
        orderAddressDTO.setReceiverName("receiverName");
        orderAddressDTO.setReceiverMobile("receiverMobile");
        orderAddressDTO.setProvince("province");
        orderAddressDTO.setCity("city");
        orderAddressDTO.setCityCode("cityCode");
        orderAddressDTO.setDistrict("district");
        orderAddressDTO.setTown("town");
        orderAddressDTO.setDetailAddress("detailAddress");
//        when(mockOrderCreateHelper.buildOrderAddressByPointId(0L)).thenReturn(orderAddressDTO);

        // Configure MemberFeignClient.getMemberByMemberId(...).
        final Member member = new Member();
        member.setMemberId(0);
        member.setUserNo("userNo");
        member.setCustNo("custNo");
        member.setCappCustNo("cappCustNo");
        member.setMemberName("orderPlaceUserName");
//        when(mockMemberFeignClient.getMemberByMemberId(0)).thenReturn(member);

        // Configure OrderCreateHelper.getStoreAreaCodeByAddress(...).
        final OrderAddressDTO addressDTO = new OrderAddressDTO();
        addressDTO.setReceiverName("receiverName");
        addressDTO.setReceiverMobile("receiverMobile");
        addressDTO.setProvince("province");
        addressDTO.setCity("city");
        addressDTO.setCityCode("cityCode");
        addressDTO.setDistrict("district");
        addressDTO.setTown("town");
        addressDTO.setDetailAddress("detailAddress");
        final Member member1 = new Member();
        member1.setMemberId(0);
        member1.setUserNo("userNo");
        member1.setCustNo("custNo");
        member1.setCappCustNo("cappCustNo");
        member1.setMemberName("orderPlaceUserName");
//        when(mockOrderCreateHelper.getStoreAreaCodeByAddress("storeId", addressDTO, member1)).thenReturn("areaCode");

        // Configure ICartService.buildCartList(...).
        final CartPO cartPO = new CartPO();
        cartPO.setCartId(0);
        cartPO.setMemberId(0);
        cartPO.setStoreId(0L);
        cartPO.setStoreName("storeName");
        cartPO.setAreaCode("areaCode");
        final List<CartPO> cartPOS = Arrays.asList(cartPO);
        final OrderSkuInfoDTO orderSkuInfoDTO1 = new OrderSkuInfoDTO();
        orderSkuInfoDTO1.setProductId(0L);
        orderSkuInfoDTO1.setNumber(0);
        orderSkuInfoDTO1.setTaxPrice(new BigDecimal("0.00"));
        orderSkuInfoDTO1.setFinanceRuleCode("financeRuleCode");
        orderSkuInfoDTO1.setAgriServiceFees(new BigDecimal("0.00"));
        final List<OrderSkuInfoDTO> skuInfoList = Arrays.asList(orderSkuInfoDTO1);
        final OrderAddressDTO addressDTO1 = new OrderAddressDTO();
        addressDTO1.setReceiverName("receiverName");
        addressDTO1.setReceiverMobile("receiverMobile");
        addressDTO1.setProvince("province");
        addressDTO1.setCity("city");
        addressDTO1.setCityCode("cityCode");
        addressDTO1.setDistrict("district");
        addressDTO1.setTown("town");
        addressDTO1.setDetailAddress("detailAddress");
//        when(mockCartService.buildCartList(skuInfoList, OrderTypeEnum.NORMAL, 0, "areaCode", "financeRuleCode",
//                "channel", addressDTO1)).thenReturn(cartPOS);

        // Configure OrderSubmitUtil.getOrderSubmitDTOV2(...).
        final Cart cart = new Cart();
        cart.setCartId(0);
        cart.setMemberId(0);
        cart.setStoreId(0L);
        cart.setStoreName("storeName");
        cart.setGoodsId(0L);
        final OrderSubmitParamDTO orderSubmitParamDTO = new OrderSubmitParamDTO();
        orderSubmitParamDTO.setChannel("channel");
        orderSubmitParamDTO.setUsrNo("userNo");
        orderSubmitParamDTO.setMemberId(0);
        orderSubmitParamDTO.setSource(0);
        orderSubmitParamDTO.setOrderFrom(0);
        orderSubmitParamDTO.setAddressId(0);
        orderSubmitParamDTO.setAreaCode("areaCode");
        orderSubmitParamDTO.setIsCart(false);
        orderSubmitParamDTO.setProductType(0);
        orderSubmitParamDTO.setIsAloneBuy(false);
        orderSubmitParamDTO.setOrderPattern(0);
        orderSubmitParamDTO.setGroupBuyingTag(0);
        orderSubmitParamDTO.setProductId(0L);
        orderSubmitParamDTO.setNumber(0);
        orderSubmitParamDTO.setPno("pno");
        orderSubmitParamDTO.setVerifyCode("verifyCode");
        orderSubmitParamDTO.setFinanceRuleCode("financeRuleCode");
        final OrderAddressDTO orderAddress = new OrderAddressDTO();
        orderAddress.setReceiverName("receiverName");
        orderAddress.setReceiverMobile("receiverMobile");
        orderAddress.setProvince("province");
        orderAddress.setCity("city");
        orderAddress.setCityCode("cityCode");
        orderAddress.setDistrict("district");
        orderAddress.setTown("town");
        orderAddress.setDetailAddress("detailAddress");
        orderSubmitParamDTO.setOrderAddress(orderAddress);
        final OrderAddressDTO purchaseOrderAddress = new OrderAddressDTO();
        purchaseOrderAddress.setReceiverName("receiverName");
        purchaseOrderAddress.setReceiverMobile("receiverMobile");
        purchaseOrderAddress.setProvince("province");
        purchaseOrderAddress.setCity("city");
        purchaseOrderAddress.setCityCode("cityCode");
        purchaseOrderAddress.setDistrict("district");
        purchaseOrderAddress.setTown("town");
        purchaseOrderAddress.setDetailAddress("detailAddress");
        orderSubmitParamDTO.setPurchaseOrderAddress(purchaseOrderAddress);
        orderSubmitParamDTO.setChannelOrder(false);
        orderSubmitParamDTO.setBankTransferable(false);
        orderSubmitParamDTO.setOrderType(OrderTypeEnum.NORMAL);
        final OrderPromotionParamDTO orderPromotionInfo = new OrderPromotionParamDTO();
        orderSubmitParamDTO.setOrderPromotionInfo(orderPromotionInfo);
        orderSubmitParamDTO.setShareResource(false);
        orderSubmitParamDTO.setPlacingCombinationFlag(false);
        orderSubmitParamDTO.setOrderPlaceUserRole(OrderPlaceUserRole.SELF);
        orderSubmitParamDTO.setLoanPayer("loanPayer");
        orderSubmitParamDTO.setLoanConfirmMethod("loanConfirmMethod");
        orderSubmitParamDTO.setAttachmentUrls(Arrays.asList("value"));
//        final OrderSubmitDTO orderSubmitDTO = new OrderSubmitDTO(Arrays.asList(cart), orderSubmitParamDTO);
        final CartPO cartPO1 = new CartPO();
        cartPO1.setCartId(0);
        cartPO1.setMemberId(0);
        cartPO1.setStoreId(0L);
        cartPO1.setStoreName("storeName");
        cartPO1.setAreaCode("areaCode");
        final List<CartPO> cartPOList = Arrays.asList(cartPO1);
        final OrderSubmitParamDTO dto1 = new OrderSubmitParamDTO();
        dto1.setChannel("channel");
        dto1.setUsrNo("userNo");
        dto1.setMemberId(0);
        dto1.setSource(0);
        dto1.setOrderFrom(0);
        dto1.setAddressId(0);
        dto1.setAreaCode("areaCode");
        dto1.setIsCart(false);
        dto1.setProductType(0);
        dto1.setIsAloneBuy(false);
        dto1.setOrderPattern(0);
        dto1.setGroupBuyingTag(0);
        dto1.setProductId(0L);
        dto1.setNumber(0);
        dto1.setPno("pno");
        dto1.setVerifyCode("verifyCode");
        dto1.setFinanceRuleCode("financeRuleCode");
        final OrderAddressDTO orderAddress1 = new OrderAddressDTO();
        orderAddress1.setReceiverName("receiverName");
        orderAddress1.setReceiverMobile("receiverMobile");
        orderAddress1.setProvince("province");
        orderAddress1.setCity("city");
        orderAddress1.setCityCode("cityCode");
        orderAddress1.setDistrict("district");
        orderAddress1.setTown("town");
        orderAddress1.setDetailAddress("detailAddress");
        dto1.setOrderAddress(orderAddress1);
        final OrderAddressDTO purchaseOrderAddress1 = new OrderAddressDTO();
        purchaseOrderAddress1.setReceiverName("receiverName");
        purchaseOrderAddress1.setReceiverMobile("receiverMobile");
        purchaseOrderAddress1.setProvince("province");
        purchaseOrderAddress1.setCity("city");
        purchaseOrderAddress1.setCityCode("cityCode");
        purchaseOrderAddress1.setDistrict("district");
        purchaseOrderAddress1.setTown("town");
        purchaseOrderAddress1.setDetailAddress("detailAddress");
        dto1.setPurchaseOrderAddress(purchaseOrderAddress1);
        dto1.setChannelOrder(false);
        dto1.setBankTransferable(false);
        dto1.setOrderType(OrderTypeEnum.NORMAL);
        final OrderPromotionParamDTO orderPromotionInfo1 = new OrderPromotionParamDTO();
        dto1.setOrderPromotionInfo(orderPromotionInfo1);
        dto1.setShareResource(false);
        dto1.setPlacingCombinationFlag(false);
        dto1.setOrderPlaceUserRole(OrderPlaceUserRole.SELF);
        dto1.setLoanPayer("loanPayer");
        dto1.setLoanConfirmMethod("loanConfirmMethod");
        dto1.setAttachmentUrls(Arrays.asList("value"));
//        when(mockOrderSubmitUtil.getOrderSubmitDTOV2(cartPOList, dto1, true)).thenReturn(orderSubmitDTO);

//        when(mockShardingId.next(SeqEnum.PNO, "memberId")).thenReturn(0L);
//        when(mockStringRedisTemplate.opsForValue()).thenReturn(null);

        // Configure OrderModel.submit(...).
        final Cart cart1 = new Cart();
        cart1.setCartId(0);
        cart1.setMemberId(0);
        cart1.setStoreId(0L);
        cart1.setStoreName("storeName");
        cart1.setGoodsId(0L);
        final OrderSubmitParamDTO orderSubmitParamDTO1 = new OrderSubmitParamDTO();
        orderSubmitParamDTO1.setChannel("channel");
        orderSubmitParamDTO1.setUsrNo("userNo");
        orderSubmitParamDTO1.setMemberId(0);
        orderSubmitParamDTO1.setSource(0);
        orderSubmitParamDTO1.setOrderFrom(0);
        orderSubmitParamDTO1.setAddressId(0);
        orderSubmitParamDTO1.setAreaCode("areaCode");
        orderSubmitParamDTO1.setIsCart(false);
        orderSubmitParamDTO1.setProductType(0);
        orderSubmitParamDTO1.setIsAloneBuy(false);
        orderSubmitParamDTO1.setOrderPattern(0);
        orderSubmitParamDTO1.setGroupBuyingTag(0);
        orderSubmitParamDTO1.setProductId(0L);
        orderSubmitParamDTO1.setNumber(0);
        orderSubmitParamDTO1.setPno("pno");
        orderSubmitParamDTO1.setVerifyCode("verifyCode");
        orderSubmitParamDTO1.setFinanceRuleCode("financeRuleCode");
        final OrderAddressDTO orderAddress2 = new OrderAddressDTO();
        orderAddress2.setReceiverName("receiverName");
        orderAddress2.setReceiverMobile("receiverMobile");
        orderAddress2.setProvince("province");
        orderAddress2.setCity("city");
        orderAddress2.setCityCode("cityCode");
        orderAddress2.setDistrict("district");
        orderAddress2.setTown("town");
        orderAddress2.setDetailAddress("detailAddress");
        orderSubmitParamDTO1.setOrderAddress(orderAddress2);
        final OrderAddressDTO purchaseOrderAddress2 = new OrderAddressDTO();
        purchaseOrderAddress2.setReceiverName("receiverName");
        purchaseOrderAddress2.setReceiverMobile("receiverMobile");
        purchaseOrderAddress2.setProvince("province");
        purchaseOrderAddress2.setCity("city");
        purchaseOrderAddress2.setCityCode("cityCode");
        purchaseOrderAddress2.setDistrict("district");
        purchaseOrderAddress2.setTown("town");
        purchaseOrderAddress2.setDetailAddress("detailAddress");
        orderSubmitParamDTO1.setPurchaseOrderAddress(purchaseOrderAddress2);
        orderSubmitParamDTO1.setChannelOrder(false);
        orderSubmitParamDTO1.setBankTransferable(false);
        orderSubmitParamDTO1.setOrderType(OrderTypeEnum.NORMAL);
        final OrderPromotionParamDTO orderPromotionInfo2 = new OrderPromotionParamDTO();
        orderSubmitParamDTO1.setOrderPromotionInfo(orderPromotionInfo2);
        orderSubmitParamDTO1.setShareResource(false);
        orderSubmitParamDTO1.setPlacingCombinationFlag(false);
        orderSubmitParamDTO1.setOrderPlaceUserRole(OrderPlaceUserRole.SELF);
        orderSubmitParamDTO1.setLoanPayer("loanPayer");
        orderSubmitParamDTO1.setLoanConfirmMethod("loanConfirmMethod");
        orderSubmitParamDTO1.setAttachmentUrls(Arrays.asList("value"));
//        final OrderSubmitDTO orderSubmitDTO1 = new OrderSubmitDTO(Arrays.asList(cart1), orderSubmitParamDTO1);
        final Member member2 = new Member();
        member2.setMemberId(0);
        member2.setUserNo("userNo");
        member2.setCustNo("custNo");
        member2.setCappCustNo("cappCustNo");
        member2.setMemberName("orderPlaceUserName");
        final OrderSubmitMqConsumerDTO consumerDTO = new OrderSubmitMqConsumerDTO();
        final OrderSubmitParamDTO paramDTO = new OrderSubmitParamDTO();
        paramDTO.setChannel("channel");
        paramDTO.setUsrNo("userNo");
        paramDTO.setMemberId(0);
        paramDTO.setSource(0);
        paramDTO.setOrderFrom(0);
        paramDTO.setAddressId(0);
        paramDTO.setAreaCode("areaCode");
        paramDTO.setIsCart(false);
        paramDTO.setProductType(0);
        paramDTO.setIsAloneBuy(false);
        paramDTO.setOrderPattern(0);
        paramDTO.setGroupBuyingTag(0);
        paramDTO.setProductId(0L);
        paramDTO.setNumber(0);
        paramDTO.setPno("pno");
        paramDTO.setVerifyCode("verifyCode");
        paramDTO.setFinanceRuleCode("financeRuleCode");
        final OrderAddressDTO orderAddress3 = new OrderAddressDTO();
        orderAddress3.setProvince("province");
        orderAddress3.setCity("city");
        orderAddress3.setCityCode("cityCode");
        orderAddress3.setDistrict("district");
        orderAddress3.setDetailAddress("detailAddress");
        paramDTO.setOrderAddress(orderAddress3);
        final OrderAddressDTO purchaseOrderAddress3 = new OrderAddressDTO();
        purchaseOrderAddress3.setProvince("province");
        purchaseOrderAddress3.setCity("city");
        purchaseOrderAddress3.setCityCode("cityCode");
        purchaseOrderAddress3.setDistrict("district");
        purchaseOrderAddress3.setDetailAddress("detailAddress");
        paramDTO.setPurchaseOrderAddress(purchaseOrderAddress3);
        paramDTO.setChannelOrder(false);
        paramDTO.setBankTransferable(false);
        paramDTO.setOrderType(OrderTypeEnum.NORMAL);
        final OrderPromotionParamDTO orderPromotionInfo3 = new OrderPromotionParamDTO();
        paramDTO.setOrderPromotionInfo(orderPromotionInfo3);
        paramDTO.setShareResource(false);
        paramDTO.setPlacingCombinationFlag(false);
        paramDTO.setOrderPlaceUserRole(OrderPlaceUserRole.SELF);
        consumerDTO.setParamDTO(paramDTO);
        consumerDTO.setMemberId(0);
        consumerDTO.setUserNo("userNo");
        consumerDTO.setPaySn("paySn");
        final PreOrderDTO preOrderDTO = new PreOrderDTO();
        preOrderDTO.setIsCalculateDiscount(false);
        preOrderDTO.setOrderType(0);
        consumerDTO.setPreOrderDTO(preOrderDTO);
        consumerDTO.setAreaCode("areaCode");
        final OrderSkuInfoDTO orderSkuInfoDTO2 = new OrderSkuInfoDTO();
        orderSkuInfoDTO2.setProductId(0L);
        orderSkuInfoDTO2.setNumber(0);
        orderSkuInfoDTO2.setTaxPrice(new BigDecimal("0.00"));
        orderSkuInfoDTO2.setFinanceRuleCode("financeRuleCode");
        orderSkuInfoDTO2.setAgriServiceFees(new BigDecimal("0.00"));
        consumerDTO.setSkuInfoList(Arrays.asList(orderSkuInfoDTO2));
        consumerDTO.setOrderType(OrderTypeEnum.NORMAL);
        final ChannelOrderSubmitDTO channelOrderSubmitDTO = new ChannelOrderSubmitDTO();
        channelOrderSubmitDTO.setChannel("channel");
        channelOrderSubmitDTO.setOutBizSource("outBizSource");
        channelOrderSubmitDTO.setOutBizId("outBizId");
        consumerDTO.setChannelOrderSubmitDTO(channelOrderSubmitDTO);
        final OrderOfflineParamDTO orderOfflineParamDTO = new OrderOfflineParamDTO();
        orderOfflineParamDTO.setOrderPattern(0);
        orderOfflineParamDTO.setEmployeeCode("employeeCode");
        final OrderAddressDTO address1 = new OrderAddressDTO();
        address1.setProvince("province");
        address1.setCity("city");
        address1.setCityCode("cityCode");
        address1.setDistrict("district");
        address1.setDetailAddress("detailAddress");
        orderOfflineParamDTO.setAddress(address1);
        orderOfflineParamDTO.setPointId(0L);
        final OrderOfflineDTO orderOfflineDTO = new OrderOfflineDTO();
        orderOfflineParamDTO.setOrderOfflineList(Arrays.asList(orderOfflineDTO));
        final OrderOfflineInfoDTO offlineInfoDTO = new OrderOfflineInfoDTO();
        orderOfflineParamDTO.setOfflineInfoDTO(offlineInfoDTO);
        orderOfflineParamDTO.setUserMobile("memberMobile");
        orderOfflineParamDTO.setIsContinueSubmit(false);
        consumerDTO.setOrderOfflineParamDTO(orderOfflineParamDTO);
        final GroupOrderProductSubmitDTO groupOrderProductSubmitDTO = new GroupOrderProductSubmitDTO();
        consumerDTO.setGroupOrderProductSubmitDTO(groupOrderProductSubmitDTO);
//        when(mockOrderModel.submit(orderSubmitDTO1, member2, consumerDTO)).thenReturn(Collections.emptyList());

        // Run the test
//        final OrderSubmitVO result = placingOrderServiceImplUnderTest.createRebateGiftOrder(dto);

        // Verify the results
//        assertEquals(expectedResult, result);
//        verify(mockStringRedisTemplate).expire("idempotent", 7L, TimeUnit.DAYS);

    }
}
