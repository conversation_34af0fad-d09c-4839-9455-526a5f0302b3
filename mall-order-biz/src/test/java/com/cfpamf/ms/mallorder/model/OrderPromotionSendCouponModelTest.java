package com.cfpamf.ms.mallorder.model;

import com.cfpamf.ms.mallorder.dto.OrderSubmitDTO;
import com.cfpamf.ms.mallorder.dto.OrderSubmitParamDTO;
import com.cfpamf.ms.mallorder.mapper.OrderPromotionSendCouponMapper;
import com.cfpamf.ms.mallorder.po.Cart;
import com.cfpamf.ms.mallorder.po.OrderPromotionSendCouponPO;
import com.cfpamf.ms.mallorder.request.OrderPromotionSendCouponExample;
import com.cfpamf.ms.mallorder.v2.service.OrderPromotionSendCouponService;
import com.slodon.bbc.core.response.PagerInfo;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderPromotionSendCouponModelTest {

    @Mock
    private OrderPromotionSendCouponMapper mockOrderPromotionSendCouponMapper;
    @Mock
    private OrderPromotionSendCouponService mockOrderPromotionSendCouponService;

    @InjectMocks
    private OrderPromotionSendCouponModel orderPromotionSendCouponModelUnderTest;

    @Test
    void testSaveOrderPromotionSendCoupon() {
        // Setup
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setPromotionType(0);
        orderPromotionSendCouponPO.setPromotionId("promotionId");
        orderPromotionSendCouponPO.setIsStore(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);

        // Configure OrderPromotionSendCouponMapper.insert(...).
        final OrderPromotionSendCouponPO entity = new OrderPromotionSendCouponPO();
        entity.setSendCouponId(0);
        entity.setOrderSn("orderSn");
        entity.setPromotionGrade(0);
        entity.setPromotionType(0);
        entity.setPromotionId("promotionId");
        entity.setIsStore(0);
        entity.setCouponId(0);
        entity.setNumber(0);
        when(mockOrderPromotionSendCouponMapper.insert(entity)).thenReturn(0);

        // Run the test
        final Integer result = orderPromotionSendCouponModelUnderTest.saveOrderPromotionSendCoupon(
                orderPromotionSendCouponPO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testDeleteOrderPromotionSendCoupon() {
        // Setup
        when(mockOrderPromotionSendCouponMapper.deleteByPrimaryKey(0)).thenReturn(0);

        // Run the test
        final Integer result = orderPromotionSendCouponModelUnderTest.deleteOrderPromotionSendCoupon(0);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdateOrderPromotionSendCoupon() {
        // Setup
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setPromotionType(0);
        orderPromotionSendCouponPO.setPromotionId("promotionId");
        orderPromotionSendCouponPO.setIsStore(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);

        // Configure OrderPromotionSendCouponMapper.updateByPrimaryKeySelective(...).
        final OrderPromotionSendCouponPO record = new OrderPromotionSendCouponPO();
        record.setSendCouponId(0);
        record.setOrderSn("orderSn");
        record.setPromotionGrade(0);
        record.setPromotionType(0);
        record.setPromotionId("promotionId");
        record.setIsStore(0);
        record.setCouponId(0);
        record.setNumber(0);
        when(mockOrderPromotionSendCouponMapper.updateByPrimaryKeySelective(record)).thenReturn(0);

        // Run the test
        final Integer result = orderPromotionSendCouponModelUnderTest.updateOrderPromotionSendCoupon(
                orderPromotionSendCouponPO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testGetOrderPromotionSendCouponBySendCouponId() {
        // Setup
        final OrderPromotionSendCouponPO expectedResult = new OrderPromotionSendCouponPO();
        expectedResult.setSendCouponId(0);
        expectedResult.setOrderSn("orderSn");
        expectedResult.setPromotionGrade(0);
        expectedResult.setPromotionType(0);
        expectedResult.setPromotionId("promotionId");
        expectedResult.setIsStore(0);
        expectedResult.setCouponId(0);
        expectedResult.setNumber(0);

        // Configure OrderPromotionSendCouponMapper.getByPrimaryKey(...).
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setPromotionType(0);
        orderPromotionSendCouponPO.setPromotionId("promotionId");
        orderPromotionSendCouponPO.setIsStore(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);
        when(mockOrderPromotionSendCouponMapper.getByPrimaryKey(0)).thenReturn(orderPromotionSendCouponPO);

        // Run the test
        final OrderPromotionSendCouponPO result = orderPromotionSendCouponModelUnderTest.getOrderPromotionSendCouponBySendCouponId(
                0);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetOrderPromotionSendCouponList() {
        // Setup
        final OrderPromotionSendCouponExample example = new OrderPromotionSendCouponExample();
        example.setSendCouponIdNotEquals(0);
        example.setSendCouponIdIn("sendCouponIdIn");
        example.setSendCouponId(0);
        example.setOrderSn("orderSn");
        example.setOrderSnLike("orderSnLike");

        final PagerInfo pager = new PagerInfo(0, 0);
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setPromotionType(0);
        orderPromotionSendCouponPO.setPromotionId("promotionId");
        orderPromotionSendCouponPO.setIsStore(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);
        final List<OrderPromotionSendCouponPO> expectedResult = Arrays.asList(orderPromotionSendCouponPO);

        // Configure OrderPromotionSendCouponMapper.countByExample(...).
        final OrderPromotionSendCouponExample example1 = new OrderPromotionSendCouponExample();
        example1.setSendCouponIdNotEquals(0);
        example1.setSendCouponIdIn("sendCouponIdIn");
        example1.setSendCouponId(0);
        example1.setOrderSn("orderSn");
        example1.setOrderSnLike("orderSnLike");
        when(mockOrderPromotionSendCouponMapper.countByExample(example1)).thenReturn(0);

        // Configure OrderPromotionSendCouponMapper.listPageByExample(...).
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO1 = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO1.setSendCouponId(0);
        orderPromotionSendCouponPO1.setOrderSn("orderSn");
        orderPromotionSendCouponPO1.setPromotionGrade(0);
        orderPromotionSendCouponPO1.setPromotionType(0);
        orderPromotionSendCouponPO1.setPromotionId("promotionId");
        orderPromotionSendCouponPO1.setIsStore(0);
        orderPromotionSendCouponPO1.setCouponId(0);
        orderPromotionSendCouponPO1.setNumber(0);
        final List<OrderPromotionSendCouponPO> orderPromotionSendCouponPOS = Arrays.asList(orderPromotionSendCouponPO1);
        final OrderPromotionSendCouponExample example2 = new OrderPromotionSendCouponExample();
        example2.setSendCouponIdNotEquals(0);
        example2.setSendCouponIdIn("sendCouponIdIn");
        example2.setSendCouponId(0);
        example2.setOrderSn("orderSn");
        example2.setOrderSnLike("orderSnLike");
        when(mockOrderPromotionSendCouponMapper.listPageByExample(example2, 0, 0))
                .thenReturn(orderPromotionSendCouponPOS);

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO2 = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO2.setSendCouponId(0);
        orderPromotionSendCouponPO2.setOrderSn("orderSn");
        orderPromotionSendCouponPO2.setPromotionGrade(0);
        orderPromotionSendCouponPO2.setPromotionType(0);
        orderPromotionSendCouponPO2.setPromotionId("promotionId");
        orderPromotionSendCouponPO2.setIsStore(0);
        orderPromotionSendCouponPO2.setCouponId(0);
        orderPromotionSendCouponPO2.setNumber(0);
        final List<OrderPromotionSendCouponPO> orderPromotionSendCouponPOS1 = Arrays.asList(
                orderPromotionSendCouponPO2);
        final OrderPromotionSendCouponExample example3 = new OrderPromotionSendCouponExample();
        example3.setSendCouponIdNotEquals(0);
        example3.setSendCouponIdIn("sendCouponIdIn");
        example3.setSendCouponId(0);
        example3.setOrderSn("orderSn");
        example3.setOrderSnLike("orderSnLike");
        when(mockOrderPromotionSendCouponMapper.listByExample(example3)).thenReturn(orderPromotionSendCouponPOS1);

        // Run the test
        final List<OrderPromotionSendCouponPO> result = orderPromotionSendCouponModelUnderTest.getOrderPromotionSendCouponList(
                example, pager);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetOrderPromotionSendCouponList_OrderPromotionSendCouponMapperListPageByExampleReturnsNoItems() {
        // Setup
        final OrderPromotionSendCouponExample example = new OrderPromotionSendCouponExample();
        example.setSendCouponIdNotEquals(0);
        example.setSendCouponIdIn("sendCouponIdIn");
        example.setSendCouponId(0);
        example.setOrderSn("orderSn");
        example.setOrderSnLike("orderSnLike");

        final PagerInfo pager = new PagerInfo(0, 0);

        // Configure OrderPromotionSendCouponMapper.countByExample(...).
        final OrderPromotionSendCouponExample example1 = new OrderPromotionSendCouponExample();
        example1.setSendCouponIdNotEquals(0);
        example1.setSendCouponIdIn("sendCouponIdIn");
        example1.setSendCouponId(0);
        example1.setOrderSn("orderSn");
        example1.setOrderSnLike("orderSnLike");
        when(mockOrderPromotionSendCouponMapper.countByExample(example1)).thenReturn(0);

        // Configure OrderPromotionSendCouponMapper.listPageByExample(...).
        final OrderPromotionSendCouponExample example2 = new OrderPromotionSendCouponExample();
        example2.setSendCouponIdNotEquals(0);
        example2.setSendCouponIdIn("sendCouponIdIn");
        example2.setSendCouponId(0);
        example2.setOrderSn("orderSn");
        example2.setOrderSnLike("orderSnLike");
        when(mockOrderPromotionSendCouponMapper.listPageByExample(example2, 0, 0)).thenReturn(Collections.emptyList());

        // Run the test
        final List<OrderPromotionSendCouponPO> result = orderPromotionSendCouponModelUnderTest.getOrderPromotionSendCouponList(
                example, pager);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetOrderPromotionSendCouponList_OrderPromotionSendCouponMapperListByExampleReturnsNoItems() {
        // Setup
        final OrderPromotionSendCouponExample example = new OrderPromotionSendCouponExample();
        example.setSendCouponIdNotEquals(0);
        example.setSendCouponIdIn("sendCouponIdIn");
        example.setSendCouponId(0);
        example.setOrderSn("orderSn");
        example.setOrderSnLike("orderSnLike");

        final PagerInfo pager = new PagerInfo(0, 0);

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponExample example1 = new OrderPromotionSendCouponExample();
        example1.setSendCouponIdNotEquals(0);
        example1.setSendCouponIdIn("sendCouponIdIn");
        example1.setSendCouponId(0);
        example1.setOrderSn("orderSn");
        example1.setOrderSnLike("orderSnLike");
        when(mockOrderPromotionSendCouponMapper.listByExample(example1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<OrderPromotionSendCouponPO> result = orderPromotionSendCouponModelUnderTest.getOrderPromotionSendCouponList(
                example, pager);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testInsertOrderPromotionSendCoupons() {
        // Setup
        final Cart cart = new Cart();
        cart.setCartId(0);
        cart.setMemberId(0);
        cart.setStoreId(0L);
        cart.setStoreName("storeName");
        cart.setGoodsId(0L);
        final OrderSubmitParamDTO orderSubmitParamDTO = new OrderSubmitParamDTO();
        orderSubmitParamDTO.setChannel("channel");
        orderSubmitParamDTO.setUsrNo("usrNo");
        orderSubmitParamDTO.setMemberId(0);
        orderSubmitParamDTO.setSource(0);
        orderSubmitParamDTO.setOrderFrom(0);
        final OrderSubmitDTO.OrderInfo orderInfo = new OrderSubmitDTO.OrderInfo(Arrays.asList(cart),
                orderSubmitParamDTO);

        // Configure OrderPromotionSendCouponMapper.insert(...).
        final OrderPromotionSendCouponPO entity = new OrderPromotionSendCouponPO();
        entity.setSendCouponId(0);
        entity.setOrderSn("orderSn");
        entity.setPromotionGrade(0);
        entity.setPromotionType(0);
        entity.setPromotionId("promotionId");
        entity.setIsStore(0);
        entity.setCouponId(0);
        entity.setNumber(0);
        when(mockOrderPromotionSendCouponMapper.insert(entity)).thenReturn(0);

        // Run the test
        orderPromotionSendCouponModelUnderTest.insertOrderPromotionSendCoupons(orderInfo, "orderSn");

        // Verify the results
    }

    @Test
    void testBuildOrderPromotionSendCoupons() {
        // Setup
        final Cart cart = new Cart();
        cart.setCartId(0);
        cart.setMemberId(0);
        cart.setStoreId(0L);
        cart.setStoreName("storeName");
        cart.setGoodsId(0L);
        final OrderSubmitParamDTO orderSubmitParamDTO = new OrderSubmitParamDTO();
        orderSubmitParamDTO.setChannel("channel");
        orderSubmitParamDTO.setUsrNo("usrNo");
        orderSubmitParamDTO.setMemberId(0);
        orderSubmitParamDTO.setSource(0);
        orderSubmitParamDTO.setOrderFrom(0);
        final OrderSubmitDTO.OrderInfo orderInfo = new OrderSubmitDTO.OrderInfo(Arrays.asList(cart),
                orderSubmitParamDTO);
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setPromotionType(0);
        orderPromotionSendCouponPO.setPromotionId("promotionId");
        orderPromotionSendCouponPO.setIsStore(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);
        final List<OrderPromotionSendCouponPO> expectedResult = Arrays.asList(orderPromotionSendCouponPO);

        // Run the test
        final List<OrderPromotionSendCouponPO> result = orderPromotionSendCouponModelUnderTest.buildOrderPromotionSendCoupons(
                orderInfo, "orderSn");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testSaveBatch() {
        // Setup
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setPromotionType(0);
        orderPromotionSendCouponPO.setPromotionId("promotionId");
        orderPromotionSendCouponPO.setIsStore(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);
        final List<OrderPromotionSendCouponPO> couponPromotionPoTotalList = Arrays.asList(orderPromotionSendCouponPO);

        // Run the test
        orderPromotionSendCouponModelUnderTest.saveBatch(couponPromotionPoTotalList);

        // Verify the results
        // Confirm OrderPromotionSendCouponService.saveBatch(...).
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO1 = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO1.setSendCouponId(0);
        orderPromotionSendCouponPO1.setOrderSn("orderSn");
        orderPromotionSendCouponPO1.setPromotionGrade(0);
        orderPromotionSendCouponPO1.setPromotionType(0);
        orderPromotionSendCouponPO1.setPromotionId("promotionId");
        orderPromotionSendCouponPO1.setIsStore(0);
        orderPromotionSendCouponPO1.setCouponId(0);
        orderPromotionSendCouponPO1.setNumber(0);
        final List<OrderPromotionSendCouponPO> entityList = Arrays.asList(orderPromotionSendCouponPO1);
        verify(mockOrderPromotionSendCouponService).saveBatch(entityList);
    }
}
