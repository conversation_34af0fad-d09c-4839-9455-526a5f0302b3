package com.cfpamf.ms.mallorder.model;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.mallorder.common.enums.OrderProductDeliveryEnum;
import com.cfpamf.ms.mallorder.dto.OrderAfterDTO;
import com.cfpamf.ms.mallorder.mapper.OrderAfterMapper;
import com.cfpamf.ms.mallorder.mapper.OrderExtendMapper;
import com.cfpamf.ms.mallorder.mapper.OrderMapper;
import com.cfpamf.ms.mallorder.mapper.OrderProductMapper;
import com.cfpamf.ms.mallorder.po.OrderExtendPO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderProductPO;
import com.cfpamf.ms.mallorder.service.IOrderAfterService;
import com.cfpamf.ms.mallorder.service.IOrderProductService;
import com.cfpamf.ms.mallorder.service.IOrderReturnService;
import com.cfpamf.ms.mallorder.service.IOrderService;
import com.cfpamf.ms.mallorder.service.impl.FrontAfterSaleApplyServiceImpl;
import com.cfpamf.ms.mallorder.service.impl.OrderAfterServiceImpl;
import com.cfpamf.ms.mallorder.validation.OrderReturnValidation;
import com.cfpamf.ms.mallorder.vo.AfsCountVO;
import com.cfpamf.ms.mallorder.vo.AfsOrderProductVO;
import com.cfpamf.ms.mallorder.vo.AfsProductVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.powermock.api.mockito.PowerMockito.when;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
public class OrderAfterModelTest {

	@InjectMocks
	private OrderAfterServiceModel orderAfterServiceModel;
	@Spy
	private IOrderAfterService orderAfterService;
	@Mock
	private OrderProductMapper orderProductMapper;
	@Mock
	private IOrderReturnService orderReturnService;
	@Mock
	private OrderExtendMapper orderExtendMapper;
	@Mock
	private OrderMapper orderMapper;

	@InjectMocks
	private OrderAfterServiceImpl orderAfterServiceInject;
	@Mock
	private OrderModel orderModel;
	@Mock
	private IOrderProductService orderProductService;
	@Mock
	private OrderProductModel orderProductModel;
	@Mock
	private IOrderService orderService;

	@Before
	public void setUp() {
		MockitoAnnotations.initMocks(this);
	}

	@Test
	public void dealAfsProductTest() {
		String orderAfterDtoJson = "{\"afsType\":3,\"goodsState\":0,\"applyReasonContent\":\"买多了\",\"afsDescription\":\"xwj测试\"," +
				"\"orderProductId46937\":10,\"applyImage\":\"\",\"channel\":\"WEB\",\"finalReturnAmount\":50.03," +
				"\"orderSn\":\"****************\",\"returnBy\":\"3\",\"productList\":[{\"afsNum\":10,\"orderProductId\":46937,\"returnAmount\":\"50.04\"}]}";
		OrderAfterDTO orderAfterDTO = JSONObject.parseObject(orderAfterDtoJson, OrderAfterDTO.class);

		String orderJson = "{\"activityDiscountAmount\":100,\"areaCode\":\"6710002-**********\",\"balanceAmount\":0," +
				"\"bankPayTrxNo\":\"2023042422001475860503361597\"," +
				"\"businessCommission\":0,\"channel\":\"H5\",\"channelServiceFee\":0.67,\"composePayName\":\"支付宝+乡助卡支付\"," +
				"\"createBy\":\"308640-***********\",\"createTime\":*************,\"customerConfirmStatus\":0,\"customerConfirmStatusDesc\":\"无需确认\"," +
				"\"exchangeFlag\":0,\"expressFee\":0.92,\"expressFeeTotal\":1,\"financeRuleCode\":\"\",\"goodsAmount\":220,\"integral\":0," +
				"\"integralCashAmount\":0,\"isDelivery\":1,\"isSettlement\":0,\"loanPayState\":0,\"lockState\":0,\"marginOrderAmount\":0,\"memberId\":308640," +
				"\"memberName\":\"***********\",\"newOrder\":true,\"orderAmount\":111,\"orderAmountTotal\":221,\"orderCommission\":0,\"orderId\":44025," +
				"\"orderPattern\":1,\"orderPlaceUserRoleCode\":1,\"orderPlaceUserRoleDesc\":\"本人\",\"orderSn\":\"****************\",\"orderState\":20," +
				"\"orderType\":1,\"parentSn\":\"****************\",\"payAmount\":111,\"paySn\":\"****************\",\"payTime\":1682326156000," +
				"\"payUpdateTime\":1682326156000,\"paymentCode\":\"ALIPAY\",\"paymentName\":\"支付宝\",\"performanceModes\":\"[0]\",\"planDiscountAmount\":0," +
				"\"platformActivityAmount\":0,\"platformVoucherAmount\":100,\"recommendStoreId\":1140004,\"recommendStoreName\":\"中和农服\",\"refundAmount\":50.03," +
				"\"serviceFee\":0,\"serviceFeeRate\":0.01,\"settleMode\":\"standard\",\"settlementPrice\":218.71," +
				"\"storeActivityAmount\":0,\"storeCompanyName\":\"中和农信（北京）供应链管理有限公司\"," +
				"\"storeId\":6710002,\"storeIsSelf\":1,\"storeName\":\"农服模式店铺\",\"storeVoucherAmount\":0,\"thirdpartnarFee\":2.21,\"thirdpartnarFeeRate\":0.01," +
				"\"userMobile\":\"***********\",\"userNo\":\"U2023032100006165864\",\"xzCardAmount\":10,\"xzCardExpressFeeAmount\":0.08}";
		OrderPO orderPO = JSONObject.parseObject(orderJson, OrderPO.class);

		String orderProductJson = "[{\"activityDiscountAmount\":100,\"barCode\":\"\",\"batchNo\":\"CGTB2023031500000003568\"," +
				"\"businessCommission\":0,\"channelSkuId\":\"************\"," +
				"\"channelSkuUnit\":\"1\",\"channelSource\":3,\"commissionAmount\":0,\"commissionRate\":0," +
				"\"goodsAmountTotal\":220,\"goodsCategoryId\":1000001717,\"goodsCategoryPath\":" +
				"\"农机农具->农膜/大棚/农具->ceshi111\",\"goodsId\":100010590006," +
				"\"goodsIsDistribute\":0,\"goodsName\":\"活动商品\",\"goodsParameter\":\"\",\"integral\":0," +
				"\"integralCashAmount\":0,\"isComment\":0,\"isDistribution\":0," +
				"\"isGift\":0,\"isVirtualGoods\":1,\"landingPrice\":10,\"logisticId\":0,\"memberId\":308640," +
				"\"moneyAmount\":110.08,\"orderCommission\":0,\"orderProductId\":46937," +
				"\"orderSn\":\"****************\",\"performanceChannel\":1,\"performanceMode\":0," +
				"\"performanceService\":\"0\",\"platformActivityAmount\":0,\"platformVoucherAmount\":100," +
				"\"productCode\":\"\",\"productEffectivePrice\":10,\"productId\":************,\"productNum\":22," +
				"\"productShowPrice\":10,\"productSpecJson\":\"颜色: 粉红色\"," +
				"\"productVersion\":\"1000004689158\",\"replacementNumber\":0,\"returnNumber\":10," +
				"\"ruleTag\":\"\",\"serviceFee\":0,\"skuMaterialCode\":\"WMS202292717572\"," +
				"\"skuMaterialName\":\"2022927自动化货品17572\",\"specValues\":\"粉红色\",\"spellTeamId\":0," +
				"\"spuOutId\":\"\",\"status\":2,\"storeActivityAmount\":0," +
				"\"storeCompanyName\":\"中和农信（北京）供应链管理有限公司\",\"storeId\":6710002," +
				"\"storeName\":\"农服模式店铺\",\"storeVoucherAmount\":0,\"supplierCode\":\"150039\"," +
				"\"supplierName\":\"8888\",\"taxPrice\":10,\"taxRate\":1,\"thirdpartnarFee\":2.21,\"updateBy\":\"-\"," +
				"\"updateTime\":1682326384000,\"usrNo\":\"\",\"weight\":5,\"xzCardAmount\":9.92}]";
		List<OrderProductPO> orderProductPOS = JSONArray.parseArray(orderProductJson, OrderProductPO.class);
		orderProductPOS.get(0).setDeliveryState(OrderProductDeliveryEnum.WAIT_DELIVERY);

		when(orderProductMapper.selectList(any())).thenReturn(orderProductPOS);
		when(orderProductMapper.getByPrimaryKey(any())).thenReturn(orderProductPOS.get(0));

		OrderExtendPO orderExtendPO = new OrderExtendPO();
		orderExtendPO.setReceiverMobile("13123234321");
		when(orderExtendMapper.listByExample(any())).thenReturn(Collections.singletonList(orderExtendPO));

		when(orderMapper.listByExample(any())).thenReturn(Collections.singletonList(orderPO));

		Map<String, BigDecimal> sumAmountByProduct = new HashMap<>();
		sumAmountByProduct.put("returnMoneyAmount", new BigDecimal("50.03"));
		sumAmountByProduct.put("actualReturnMoneyAmount", new BigDecimal("50.03"));
		sumAmountByProduct.put("returnIntegralAmount", new BigDecimal("0"));
		sumAmountByProduct.put("serviceFee", new BigDecimal("0"));
		sumAmountByProduct.put("thirdpartnarFee", new BigDecimal("1"));
		sumAmountByProduct.put("orderCommission", new BigDecimal("0"));
		sumAmountByProduct.put("xzCardAmount", new BigDecimal("4.51"));
		sumAmountByProduct.put("businessCommission", new BigDecimal("0"));
		sumAmountByProduct.put("commissionAmount", new BigDecimal("0"));
		sumAmountByProduct.put("platformVoucherAmount", new BigDecimal("45.45"));
		sumAmountByProduct.put("platformActivityAmount", new BigDecimal("0"));
		sumAmountByProduct.put("storeActivityAmount", new BigDecimal("0"));
		sumAmountByProduct.put("storeVoucherAmount", new BigDecimal("0"));
		when(orderReturnService.getSumAmountByProductId(any())).thenReturn(sumAmountByProduct);

		// 部分退款
		List<AfsProductVO> afsProductVOS = this.orderAfterServiceModel.dealAfsProduct(orderPO, orderAfterDTO);
		log.info("部分退款 result:{}", JSONObject.toJSONString(afsProductVOS));

		// 全部退款
		orderAfterDTO.getProductList().get(0).setAfsNum(12);
		List<AfsProductVO> afsProductVOS2 = this.orderAfterServiceModel.dealAfsProduct(orderPO, orderAfterDTO);
		log.info("全部退款 result:{}", JSONObject.toJSONString(afsProductVOS2));

	}

	@Test
	public void getAfsApplyInfoVOTest() {

		FrontAfterSaleApplyServiceImpl spyAfterSaleApplyService = Mockito.spy(FrontAfterSaleApplyServiceImpl.class);
		OrderAfterServiceModel spyOrderAfterServiceModel = Mockito.spy(OrderAfterServiceModel.class);
		IOrderAfterService orderAfterService = Mockito.spy(IOrderAfterService.class);
		OrderMapper orderMapper = Mockito.mock(OrderMapper.class);
		OrderProductMapper orderProductMapper = Mockito.mock(OrderProductMapper.class);
		OrderExtendMapper orderExtendMapper = Mockito.mock(OrderExtendMapper.class);
		OrderAfterMapper orderAfterMapper = Mockito.mock(OrderAfterMapper.class);
		OrderReturnValidation orderReturnValidation = Mockito.mock(OrderReturnValidation.class);


	}

	@Test
	public void refundCountMoneyInfoTest() {

//		IOrderProductService orderProductService = Mockito.mock(IOrderProductService.class);
//		OrderModel orderModel = Mockito.mock(OrderModel.class);
//		IOrderReturnService orderReturnService = Mockito.mock(IOrderReturnService.class);

		String orderJson = "{\"activityDiscountAmount\":0,\"balanceAmount\":0,\"exchangeFlag\":0," +
				"\"expressFee\":0,\"expressFeeTotal\":0," +
				"\"financeRuleCode\":\"1140004200000033003\",\"finishTime\":1682501206000," +
				"\"goodsAmount\":505,\"integral\":0,\"integralCashAmount\":0," +
				"\"isDelivery\":1,\"isSettlement\":0,\"lendingSuccessTime\":1682501429000," +
				"\"loanPayState\":70,\"lockState\":-2,\"marginOrderAmount\":0," +
				"\"memberId\":172546,\"memberName\":\"***********\",\"newOrder\":false," +
				"\"orderAmount\":505,\"orderAmountTotal\":505,\"orderCommission\":495," +
				"\"orderId\":44120,\"orderPattern\":1,\"orderPlaceUserRoleCode\":1," +
				"\"orderPlaceUserRoleDesc\":\"本人\",\"orderSn\":\"8223042629840731\"," +
				"\"orderState\":40,\"orderType\":1,\"parentSn\":\"8223042629840631\"," +
				"\"payAmount\":505,\"paySn\":\"1123042632640431\",\"paymentCode\":\"ENJOY_PAY\"," +
				"\"paymentName\":\"用呗支付\",\"performanceModes\":\"[0]\",\"planDiscountAmount\":0," +
				"\"platformActivityAmount\":0,\"platformVoucherAmount\":0," +
				"\"recommendStoreId\":1140004,\"recommendStoreName\":\"中和农服\",\"refundAmount\":0," +
				"\"serviceFee\":20.2,\"serviceFeeRate\":0.04,\"settleMode\":\"standard\"," +
				"\"settlementPrice\":8.99,\"storeActivityAmount\":0,\"storeCompanyName\":\"中和农信（北京）供应链管理有限公司\"," +
				"\"storeId\":7310003,\"storeIsSelf\":2," +
				"\"storeName\":\"现款现货店铺\",\"storeVoucherAmount\":0,\"thirdpartnarFee\":1.01," +
				"\"thirdpartnarFeeRate\":0.002,\"userMobile\":\"***********\"," +
				"\"userNo\":\"U2022092600005996115\",\"xzCardAmount\":0,\"xzCardExpressFeeAmount\":0}";
		OrderPO orderPO = JSONObject.parseObject(orderJson, OrderPO.class);

		String orderProductJson = "[{\"activityDiscountAmount\":0,\"businessCommission\":0,\"channelSource\":1," +
				"\"commissionAmount\":495,\"commissionRate\":0," +
				"\"enabledFlag\":1,\"financeRuleCode\":\"1140004200000033003\",\"giftId\":0," +
				"\"goodsAmountTotal\":500,\"goodsId\":100010650003,\"goodsIsDistribute\":0," +
				"\"goodsName\":\"自动化商品名称\",\"goodsParameter\":\"\",\"integral\":0," +
				"\"integralCashAmount\":0,\"landingPrice\":1,\"logisticId\":151858,\"memberId\":172546," +
				"\"moneyAmount\":500,\"orderCommission\":495,\"orderProductId\":47050," +
				"\"orderSn\":\"8223042629840731\",\"performanceChannel\":1,\"performanceMode\":0," +
				"\"performanceService\":\"0\",\"platformActivityAmount\":0,\"platformVoucherAmount\":0," +
				"\"productCode\":\"\",\"productEffectivePrice\":100,\"productId\":200011070006," +
				"\"productNum\":5,\"productShowPrice\":100,\"productVersion\":\"1000004702014\"," +
				"\"replacementNumber\":0,\"returnNumber\":0,\"ruleTag\":\"人工发货交易后起\"," +
				"\"serviceFee\":20,\"status\":0,\"storeActivityAmount\":0,\"storeCompanyName\":\"中和农信（北京）供应链管理有限公司\"," +
				"\"storeId\":7310003,\"storeName\":\"现款现货店铺\"," +
				"\"storeVoucherAmount\":0,\"taxPrice\":100,\"thirdpartnarFee\":1,\"weight\":0," +
				"\"xzCardAmount\":0},{\"activityDiscountAmount\":0,\"businessCommission\":0,\"channelSource\":1," +
				"\"commissionAmount\":0,\"commissionRate\":0,\"financeRuleCode\":\"1140004200000033003\"," +
				"\"giftId\":0,\"goodsAmountTotal\":5,\"goodsId\":100010650003,\"goodsIsDistribute\":0," +
				"\"goodsName\":\"自动化商品名称\",\"goodsParameter\":\"\",\"integral\":0,\"integralCashAmount\":0," +
				"\"isComment\":0,\"landingPrice\":1,\"logisticId\":151858,\"memberId\":172546," +
				"\"moneyAmount\":5,\"orderCommission\":0,\"orderProductId\":47049,\"orderSn\":\"8223042629840731\"," +
				"\"performanceChannel\":1,\"performanceMode\":0,\"performanceService\":\"0\"," +
				"\"platformActivityAmount\":0,\"platformVoucherAmount\":0,\"productEffectivePrice\":1," +
				"\"productId\":200011070005,\"productNum\":5,\"productShowPrice\":1,\"productUnit\":\"\"," +
				"\"productVersion\":\"1000004702013\",\"replacementNumber\":0,\"returnNumber\":0," +
				"\"ruleTag\":\"人工发货交易后起\",\"serviceFee\":0.2,\"status\":0,\"storeActivityAmount\":0," +
				"\"storeCompanyName\":\"中和农信（北京）供应链管理有限公司\",\"storeId\":7310003," +
				"\"storeName\":\"现款现货店铺\",\"storeVoucherAmount\":0,\"supplierName\":\"\",\"taxPrice\":1," +
				"\"thirdpartnarFee\":0.01,\"weight\":0,\"xzCardAmount\":0}]";
		List<OrderProductPO> orderProductPOS = JSONArray.parseArray(orderProductJson, OrderProductPO.class);


		when(orderModel.getOrderByOrderSn(any())).thenReturn(orderPO);
		when(orderProductService.selectOneByOrderProductId(47050L)).thenReturn(orderProductPOS.get(0));
		when(orderProductService.selectOneByOrderProductId(47049L)).thenReturn(orderProductPOS.get(1));

		Map<String, BigDecimal> sumAmountByProduct = new HashMap<>();
		sumAmountByProduct.put("returnMoneyAmount", new BigDecimal("0"));
		sumAmountByProduct.put("actualReturnMoneyAmount", new BigDecimal("0"));
		sumAmountByProduct.put("returnIntegralAmount", new BigDecimal("0"));
		sumAmountByProduct.put("serviceFee", new BigDecimal("0"));
		sumAmountByProduct.put("thirdpartnarFee", new BigDecimal("0"));
		sumAmountByProduct.put("orderCommission", new BigDecimal("0"));
		sumAmountByProduct.put("xzCardAmount", new BigDecimal("0"));
		sumAmountByProduct.put("businessCommission", new BigDecimal("0"));
		sumAmountByProduct.put("commissionAmount", new BigDecimal("0"));
		sumAmountByProduct.put("platformVoucherAmount", new BigDecimal("0"));
		sumAmountByProduct.put("platformActivityAmount", new BigDecimal("0"));
		sumAmountByProduct.put("storeActivityAmount", new BigDecimal("0"));
		sumAmountByProduct.put("storeVoucherAmount", new BigDecimal("0"));
		when(orderReturnService.getSumAmountByProductId(any())).thenReturn(sumAmountByProduct);

		// 设置参数
		String orderSn = "8223042629840731";
		String afsOrderProductInfos = "47050-5,47049-3";

		// 金额计算：全部退，部分退
		AfsCountVO afsCountVO = orderAfterServiceInject.refundCountMoneyInfo(orderSn, afsOrderProductInfos, null);
		log.info("result1:{}", JSONObject.toJSONString(afsCountVO));

		// 金额计算：全部退，多次全部退
		orderProductPOS.get(1).setReturnNumber(2);
		AfsCountVO afsCountVO1 = orderAfterServiceInject.refundCountMoneyInfo(orderSn, afsOrderProductInfos, null);
		log.info("result2:{}", JSONObject.toJSONString(afsCountVO1));

	}

	@Test
	public void getRefundableOrderProductTest() {
		String orderProductJson = "[{\"order_product_id\":47049,\"order_sn\":\"8223042629840731\",\"goods_id\":100010650003," +
				"\"goods_name\":\"自动化商品名称\",\"product_image\":\"2023/04/25/961e7d8b-dcc3-4570-9e8e-e4ec28ef26ff手机02.jpeg.a.jpeg\"," +
				"\"spec_values\":\"常规价格,一分钱\",\"product_id\":200011070005,\"product_num\":5,\"return_number\":0,\"money_amount\":5," +
				"\"integral\":0,\"is_gift\":0},{\"order_product_id\":47050,\"order_sn\":\"8223042629840731\",\"goods_id\":100010650003," +
				"\"goods_name\":\"自动化商品名称\",\"product_image\":\"2023/04/25/961e7d8b-dcc3-4570-9e8e-e4ec28ef26ff手机02.jpeg.a.jpeg\"," +
				"\"spec_values\":\"常规价格,常规价格\",\"product_id\":200011070006,\"product_num\":5,\"return_number\":0," +
				"\"money_amount\":500,\"integral\":0,\"is_gift\":0}]";
		List<OrderProductPO> orderProductPOS = JSONArray.parseArray(orderProductJson, OrderProductPO.class);

		String orderSn = "8223042629840731";
		Long orderProductId = 47050L;
		when(orderProductModel.selectRefundableOrderProduct(orderSn)).thenReturn(orderProductPOS);

		List<AfsOrderProductVO> refundableOrderProduct = orderAfterServiceInject.getRefundableOrderProduct(orderSn, orderProductId, null);
		log.info("refundableOrderProduct:{}", JSONObject.toJSONString(refundableOrderProduct));
	}

}
