package com.cfpamf.ms.mallorder.model;

import com.cfpamf.dts.biz.api.channel.SkyCraneFeignClient;
import com.cfpamf.mallpayment.facade.api.MallPaymentFacade;
import com.cfpamf.ms.mallmember.api.MemberFeignClient;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.common.calculate.RefundCalculator;
import com.cfpamf.ms.mallorder.common.config.ChannelFeeRateConfig;
import com.cfpamf.ms.mallorder.common.enums.OrderProductDeliveryEnum;
import com.cfpamf.ms.mallorder.controller.fegin.facade.CustomerServiceFeign;
import com.cfpamf.ms.mallorder.dto.OrderAfterDTO;
import com.cfpamf.ms.mallorder.integration.pay.PayIntegration;
import com.cfpamf.ms.mallorder.integration.promotion.facade.DiscountCouponFacade;
import com.cfpamf.ms.mallorder.mapper.*;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.request.OrderExample;
import com.cfpamf.ms.mallorder.request.OrderExtendExample;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.service.impl.OrderCreateHelper;
import com.cfpamf.ms.mallorder.service.payment.IPayHandleService;
import com.cfpamf.ms.mallorder.v2.service.OrderPayRecordService;
import com.cfpamf.ms.mallorder.v2.service.OrderPresellService;
import com.cfpamf.ms.mallorder.v2.service.OrderRefundRecordService;
import com.cfpamf.ms.mallorder.v2.service.PresellRefundService;
import com.cfpamf.ms.mallorder.validation.OrderCancelValidation;
import com.cfpamf.ms.mallorder.validation.OrderReturnValidation;
import com.cfpamf.ms.mallshop.api.StoreFeignClient;
import com.cfpamf.ms.mallshop.api.StoreIsolateWhiteListFeignClient;
import com.cfpamf.ms.mallsystem.api.ExpressFeignClient;
import com.cfpamf.smartid.client.utils.sharding.ShardingId;
import com.slodon.bbc.starter.mq.entity.MessageSendProperty;
import com.slodon.bbc.starter.mq.entity.MessageSendVO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;

@RunWith(MockitoJUnitRunner.class)
public class OrderAfterServiceModelTest2 {

    @Mock
    private OrderAfterMapper mockOrderAfterMapper;
    @Mock
    private OrderReturnMapper mockOrderReturnMapper;
    @Mock
    private OrderReplacementMapper mockOrderReplacementMapper;
    @Mock
    private OrderMapper mockOrderMapper;
    @Mock
    private OrderProductMapper mockOrderProductMapper;
    @Mock
    private OrderExtendMapper mockOrderExtendMapper;
    @Mock
    private OrderAfterSaleLogMapper mockOrderAfterSaleLogMapper;
    @Mock
    private OrderReturnTrackMapper mockOrderReturnTrackMapper;
    @Mock
    private ChannelFeeRateConfig mockChannelFeeRateConfig;
    @Mock
    private StringRedisTemplate mockStringRedisTemplate;
    @Mock
    private ExpressFeignClient mockExpressFeignClient;
    @Mock
    private RabbitTemplate mockRabbitTemplate;
    @Mock
    private ShardingId mockShardingId;
    @Mock
    private PayIntegration mockPayIntegration;
    @Mock
    private OrderModel mockOrderModel;
    @Mock
    private StoreFeignClient mockStoreFeignClient;
    @Mock
    private MemberFeignClient mockMemberFeignClient;
    @Mock
    private CustomerServiceFeign mockCustomerServiceFeign;
    @Mock
    private MallPaymentFacade mockMallPaymentFacade;
    @Mock
    private DiscountCouponFacade mockDiscountCouponFacade;
    @Mock
    private OrderAfterServiceModel mockOrderAfterServiceModel;
    @Mock
    private OrderReturnModel mockOrderReturnModel;
    @Mock
    private ILoanResultService mockLoanResultService;
    @Mock
    private IOrderService mockOrderService;
    @Mock
    private IOrderReturnService mockOrderReturnService;
    @Mock
    private IOrderPlacingService mockOrderPlacingService;
    @Mock
    private IOrderProductService mockOrderProductService;
    @Mock
    private IOrderAfterService mockOrderAfterService;
    @Mock
    private IPayHandleService mockIPayHandleService;
    @Mock
    private IOrderExtendFinanceService mockOrderExtendFinanceService;
    @Mock
    private PresellRefundService mockPresellRefundService;
    @Mock
    private OrderRefundRecordService mockOrderRefundRecordService;
    @Mock
    private IOrderProductExtendService mockOrderProductExtendService;
    @Mock
    private OrderPayRecordService mockOrderPayRecordService;
    @Mock
    private OrderCreateHelper mockOrderCreateHelper;
    @Mock
    private SkyCraneFeignClient mockSkyCraneFeignClient;
    @Mock
    private RefundCalculator mockRefundCalculator;
    @Mock
    private OrderReturnValidation mockOrderReturnValidation;
    @Mock
    private StoreIsolateWhiteListFeignClient mockStoreIsolateWhiteListFeignClient;
    @Mock
    private OrderCancelValidation mockOrderCancelValidation;
    @Mock
    private OrderPresellService mockOrderPresellService;

    @InjectMocks
    private OrderAfterServiceModel orderAfterServiceModelUnderTest;

    @Before
    public void setUp() throws Exception {
        ReflectionTestUtils.setField(orderAfterServiceModelUnderTest, "refundNotifyUrl", "notifyUrl");
    }

    @Test
    public void testApplyAfterServiceForRebate_RabbitTemplateThrowsAmqpException() throws Exception {
        // Setup
        final Map<Long, String> expectedResult = new HashMap<>();

        // Configure OrderModel.getOrderByOrderSn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("afsSn");
        orderPO.setUserNo("operator");
        orderPO.setUserMobile("phone");
        orderPO.setPaySn("paySn");
        orderPO.setStoreId(0L);
        orderPO.setStoreIsSelf(0);
        orderPO.setRecommendStoreId(0L);
        orderPO.setStoreName("storeName");
        orderPO.setMemberName("afsSn");
        orderPO.setMemberId(0);
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setIntegral(0);
        orderPO.setOrderType(0);
        orderPO.setOrderPattern(0);
        orderPO.setLockState(0);
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setAfterSalesDeadline(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setExchangeFlag(0);
//        when(mockOrderModel.getOrderByOrderSn("orderSn")).thenReturn(orderPO);

        // Configure IOrderProductService.listByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setMemberId(0);
        orderProductPO.setProductId(0L);
        orderProductPO.setProductNum(0);
        orderProductPO.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setXzCardAmount(new BigDecimal("0.00"));
        orderProductPO.setIntegral(0);
        orderProductPO.setCommissionAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setThirdpartnarFee(new BigDecimal("0.00"));
        orderProductPO.setOrderCommission(new BigDecimal("0.00"));
        orderProductPO.setBusinessCommission(new BigDecimal("0.00"));
        orderProductPO.setIsGift(0);
        orderProductPO.setGiftGroup(0);
        orderProductPO.setReturnNumber(0);
        orderProductPO.setReplacementNumber(0);
        orderProductPO.setDeliveryState(OrderProductDeliveryEnum.WAIT_DELIVERY);
        orderProductPO.setEnabledFlag(0);
        orderProductPO.setDeposit(new BigDecimal("0.00"));
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO);
//        when(mockOrderProductService.listByOrderSn("orderSn")).thenReturn(orderProductPOS);

        // Configure MemberFeignClient.getMemberByMemberId(...).
        final Member member = new Member();
        member.setMemberId(0);
        member.setUserNo("userNo");
        member.setCustNo("custNo");
        member.setMemberName("memberName");
        member.setMemberMobile("memberMobile");
//        when(mockMemberFeignClient.getMemberByMemberId(0)).thenReturn(member);

        // Configure IOrderPlacingService.getByOrderSn(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("afsSn");
        orderPO1.setUserNo("operator");
        orderPO1.setUserMobile("phone");
        orderPO1.setPaySn("paySn");
        orderPO1.setStoreId(0L);
        orderPO1.setStoreIsSelf(0);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setStoreName("storeName");
        orderPO1.setMemberName("afsSn");
        orderPO1.setMemberId(0);
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setIntegral(0);
        orderPO1.setOrderType(0);
        orderPO1.setOrderPattern(0);
        orderPO1.setLockState(0);
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setAfterSalesDeadline(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setExchangeFlag(0);
//        when(mockOrderPlacingService.getByOrderSn("orderSn")).thenReturn(orderPO1);

//        when(mockOrderReturnValidation.isInnerRefundStore(0L)).thenReturn(false);
//        when(mockOrderReturnService.hasDuringRefund("orderSn")).thenReturn(false);

        // Configure PresellRefundService.checkPresellOrder(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("afsSn");
        orderPO2.setUserNo("operator");
        orderPO2.setUserMobile("phone");
        orderPO2.setPaySn("paySn");
        orderPO2.setStoreId(0L);
        orderPO2.setStoreIsSelf(0);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setStoreName("storeName");
        orderPO2.setMemberName("afsSn");
        orderPO2.setMemberId(0);
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setIntegral(0);
        orderPO2.setOrderType(0);
        orderPO2.setOrderPattern(0);
        orderPO2.setLockState(0);
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setAfterSalesDeadline(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setExchangeFlag(0);
//        when(mockPresellRefundService.checkPresellOrder(orderPO2)).thenReturn(false);

//        when(mockStringRedisTemplate.opsForValue()).thenReturn(null);

        // Configure OrderPayRecordService.queryLoanNoRefundPayOrderByPaySn(...).
        final OrderPayRecordPO orderPayRecordPO = new OrderPayRecordPO();
        orderPayRecordPO.setEnabledFlag(0);
        orderPayRecordPO.setPaySn("paySn");
        orderPayRecordPO.setPaymentCode("paymentCode");
        orderPayRecordPO.setPayOrder(0);
        orderPayRecordPO.setPayStatus(0);
        final List<OrderPayRecordPO> orderPayRecordPOS = Arrays.asList(orderPayRecordPO);
//        when(mockOrderPayRecordService.queryLoanNoRefundPayOrderByPaySn("paySn")).thenReturn(orderPayRecordPOS);

        // Configure OrderMapper.listByExample(...).
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("afsSn");
        orderPO3.setUserNo("operator");
        orderPO3.setUserMobile("phone");
        orderPO3.setPaySn("paySn");
        orderPO3.setStoreId(0L);
        orderPO3.setStoreIsSelf(0);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setStoreName("storeName");
        orderPO3.setMemberName("afsSn");
        orderPO3.setMemberId(0);
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setIntegral(0);
        orderPO3.setOrderType(0);
        orderPO3.setOrderPattern(0);
        orderPO3.setLockState(0);
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setAfterSalesDeadline(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setExchangeFlag(0);
        final List<OrderPO> orderPOS = Arrays.asList(orderPO3);
        final OrderExample example = new OrderExample();
        example.setOrderIdNotEquals(0);
        example.setUserNo("userNo");
        example.setUserMobile("userMobile");
        example.setOrderSn("afsSn");
        example.setMemberId(0);
//        when(mockOrderMapper.listByExample(example)).thenReturn(orderPOS);

        // Configure IOrderProductService.list(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setMemberId(0);
        orderProductPO1.setProductId(0L);
        orderProductPO1.setProductNum(0);
        orderProductPO1.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderProductPO1.setIntegral(0);
        orderProductPO1.setCommissionAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setThirdpartnarFee(new BigDecimal("0.00"));
        orderProductPO1.setOrderCommission(new BigDecimal("0.00"));
        orderProductPO1.setBusinessCommission(new BigDecimal("0.00"));
        orderProductPO1.setIsGift(0);
        orderProductPO1.setGiftGroup(0);
        orderProductPO1.setReturnNumber(0);
        orderProductPO1.setReplacementNumber(0);
        orderProductPO1.setDeliveryState(OrderProductDeliveryEnum.WAIT_DELIVERY);
        orderProductPO1.setEnabledFlag(0);
        orderProductPO1.setDeposit(new BigDecimal("0.00"));
        final List<OrderProductPO> orderProductPOS1 = Arrays.asList(orderProductPO1);
//        when(mockOrderProductService.list(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS1);

        // Configure IOrderAfterService.isAllReturnApplied(...).
        final OrderAfterPO orderAfterPO = new OrderAfterPO();
        orderAfterPO.setAfsId(0);
        orderAfterPO.setAfsSn("afsSn");
        orderAfterPO.setStoreId(0L);
        orderAfterPO.setStoreIsSelf(0);
        orderAfterPO.setStoreName("storeName");
        orderAfterPO.setOrderSn("orderSn");
        orderAfterPO.setMemberId(0);
        orderAfterPO.setMemberName("afsSn");
        orderAfterPO.setGoodsId(0L);
        orderAfterPO.setOrderProductId(0L);
        orderAfterPO.setAfsNum(0);
        orderAfterPO.setBuyerExpressName("expressName");
        orderAfterPO.setBuyerExpressNumber("logisticsNumber");
        orderAfterPO.setBuyerExpressCode("expressCode");
        orderAfterPO.setBuyerDeliverTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderAfterPO.setApplyImage("applyImage");
        orderAfterPO.setContactName("afsSn");
        orderAfterPO.setContactPhone("contactPhone");
        orderAfterPO.setAfsType(0);
        orderAfterPO.setApplyReasonContent("operateRemark");
        orderAfterPO.setAfsDescription("afsDescription");
        orderAfterPO.setBuyerApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderAfterPO.setStoreAuditTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderAfterPO.setPlatformRemark("platformRemark");
        orderAfterPO.setStoreReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderAfterPO.setGoodsState(0);
        orderAfterPO.setProductDeliveryState(0);
        orderAfterPO.setSnapshotInformation("snapshotInformation");
        orderAfterPO.setEnabledFlag(0);
        orderAfterPO.setPerformanceMode(0);
        orderAfterPO.setRefundFailReason("failReason");
        final List<OrderAfterPO> afs = Arrays.asList(orderAfterPO);
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setMemberId(0);
        orderProductPO2.setProductId(0L);
        orderProductPO2.setProductNum(0);
        orderProductPO2.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderProductPO2.setIntegral(0);
        orderProductPO2.setCommissionAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setThirdpartnarFee(new BigDecimal("0.00"));
        orderProductPO2.setOrderCommission(new BigDecimal("0.00"));
        orderProductPO2.setBusinessCommission(new BigDecimal("0.00"));
        orderProductPO2.setIsGift(0);
        orderProductPO2.setGiftGroup(0);
        orderProductPO2.setReturnNumber(0);
        orderProductPO2.setReplacementNumber(0);
        orderProductPO2.setDeliveryState(OrderProductDeliveryEnum.WAIT_DELIVERY);
        orderProductPO2.setEnabledFlag(0);
        orderProductPO2.setDeposit(new BigDecimal("0.00"));
        final List<OrderProductPO> ops = Arrays.asList(orderProductPO2);
//        when(mockOrderAfterService.isAllReturnApplied(afs, ops)).thenReturn(true);

        // Configure IOrderAfterService.isAllReturnApplied(...).
        final OrderAfterDTO.AfterProduct afterProduct = new OrderAfterDTO.AfterProduct();
        afterProduct.setOrderProductId(0L);
        afterProduct.setAfsNum(0);
        afterProduct.setReturnAmount(new BigDecimal("0.00"));
        final List<OrderAfterDTO.AfterProduct> afsDTOs = Arrays.asList(afterProduct);
//        when(mockOrderAfterService.isAllReturnApplied(afsDTOs, "orderSn")).thenReturn(false);

        // Configure OrderProductMapper.getByPrimaryKey(...).
        final OrderProductPO orderProductPO3 = new OrderProductPO();
        orderProductPO3.setOrderProductId(0L);
        orderProductPO3.setOrderSn("orderSn");
        orderProductPO3.setMemberId(0);
        orderProductPO3.setProductId(0L);
        orderProductPO3.setProductNum(0);
        orderProductPO3.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO3.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderProductPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO3.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderProductPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderProductPO3.setIntegral(0);
        orderProductPO3.setCommissionAmount(new BigDecimal("0.00"));
        orderProductPO3.setServiceFee(new BigDecimal("0.00"));
        orderProductPO3.setThirdpartnarFee(new BigDecimal("0.00"));
        orderProductPO3.setOrderCommission(new BigDecimal("0.00"));
        orderProductPO3.setBusinessCommission(new BigDecimal("0.00"));
        orderProductPO3.setIsGift(0);
        orderProductPO3.setGiftGroup(0);
        orderProductPO3.setReturnNumber(0);
        orderProductPO3.setReplacementNumber(0);
        orderProductPO3.setDeliveryState(OrderProductDeliveryEnum.WAIT_DELIVERY);
        orderProductPO3.setEnabledFlag(0);
        orderProductPO3.setDeposit(new BigDecimal("0.00"));
//        when(mockOrderProductMapper.getByPrimaryKey(0L)).thenReturn(orderProductPO3);

        // Configure OrderExtendMapper.listByExample(...).
        final OrderExtendPO extendPO = new OrderExtendPO();
        extendPO.setExtendId(0);
        extendPO.setOrderSn("orderSn");
        extendPO.setOrderPointsCount(0);
        extendPO.setVoucherCode("returnVoucherCode");
        extendPO.setReceiverMobile("contactPhone");
        final List<OrderExtendPO> extendPOList = Arrays.asList(extendPO);
        final OrderExtendExample example1 = new OrderExtendExample();
        example1.setExtendIdNotEquals(0);
        example1.setExtendIdIn("extendIdIn");
        example1.setExtendId(0);
        example1.setOrderSn("afsSn");
        example1.setCustomerId("customerId");
//        when(mockOrderExtendMapper.listByExample(example1)).thenReturn(extendPOList);

//        when(mockOrderPayRecordService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));
//        when(mockRefundCalculator.calculateRefundPlatformVoucherFunderAmount(0L, CouponFunder.PLATFORM))
//                .thenReturn(new BigDecimal("0.00"));
//        when(mockRefundCalculator.calculateOrderProductPlatformVoucherRetailAmount(0L,
//                CouponFunder.PLATFORM)).thenReturn(new BigDecimal("0.00"));
//        when(mockOrderReturnValidation.refundProductCheck(0L)).thenReturn(false);
//        when(mockOrderReturnValidation.validReturnNumGtDeliverNum(0L, 0)).thenReturn(false);
//        when(mockShardingId.next(SeqEnum.RNO, "memberId")).thenReturn(0L);

        // Configure OrderAfterMapper.insert(...).
        final OrderAfterPO entity = new OrderAfterPO();
        entity.setAfsId(0);
        entity.setAfsSn("afsSn");
        entity.setStoreId(0L);
        entity.setStoreIsSelf(0);
        entity.setStoreName("storeName");
        entity.setOrderSn("orderSn");
        entity.setMemberId(0);
        entity.setMemberName("afsSn");
        entity.setGoodsId(0L);
        entity.setOrderProductId(0L);
        entity.setAfsNum(0);
        entity.setBuyerExpressName("expressName");
        entity.setBuyerExpressNumber("logisticsNumber");
        entity.setBuyerExpressCode("expressCode");
        entity.setBuyerDeliverTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setApplyImage("applyImage");
        entity.setContactName("afsSn");
        entity.setContactPhone("contactPhone");
        entity.setAfsType(0);
        entity.setApplyReasonContent("operateRemark");
        entity.setAfsDescription("afsDescription");
        entity.setBuyerApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setStoreAuditTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPlatformRemark("platformRemark");
        entity.setStoreReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setGoodsState(0);
        entity.setProductDeliveryState(0);
        entity.setSnapshotInformation("snapshotInformation");
        entity.setEnabledFlag(0);
        entity.setPerformanceMode(0);
        entity.setRefundFailReason("failReason");
//        when(mockOrderAfterMapper.insert(entity)).thenReturn(0);

        // Configure OrderAfterServiceModel.getAfterServiceByAfsSn(...).
        final OrderAfterPO orderAfterPO1 = new OrderAfterPO();
        orderAfterPO1.setAfsId(0);
        orderAfterPO1.setAfsSn("afsSn");
        orderAfterPO1.setStoreId(0L);
        orderAfterPO1.setStoreIsSelf(0);
        orderAfterPO1.setStoreName("storeName");
        orderAfterPO1.setOrderSn("orderSn");
        orderAfterPO1.setMemberId(0);
        orderAfterPO1.setMemberName("afsSn");
        orderAfterPO1.setGoodsId(0L);
        orderAfterPO1.setOrderProductId(0L);
        orderAfterPO1.setAfsNum(0);
        orderAfterPO1.setBuyerExpressName("expressName");
        orderAfterPO1.setBuyerExpressNumber("logisticsNumber");
        orderAfterPO1.setBuyerExpressCode("expressCode");
        orderAfterPO1.setBuyerDeliverTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderAfterPO1.setApplyImage("applyImage");
        orderAfterPO1.setContactName("afsSn");
        orderAfterPO1.setContactPhone("contactPhone");
        orderAfterPO1.setAfsType(0);
        orderAfterPO1.setApplyReasonContent("operateRemark");
        orderAfterPO1.setAfsDescription("afsDescription");
        orderAfterPO1.setBuyerApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderAfterPO1.setStoreAuditTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderAfterPO1.setPlatformRemark("platformRemark");
        orderAfterPO1.setStoreReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderAfterPO1.setGoodsState(0);
        orderAfterPO1.setProductDeliveryState(0);
        orderAfterPO1.setSnapshotInformation("snapshotInformation");
        orderAfterPO1.setEnabledFlag(0);
        orderAfterPO1.setPerformanceMode(0);
        orderAfterPO1.setRefundFailReason("failReason");
//        when(mockOrderAfterServiceModel.getAfterServiceByAfsSn("afsSn")).thenReturn(orderAfterPO1);

        // Configure IOrderProductService.selectOneByOrderProductId(...).
        final OrderProductPO orderProductPO4 = new OrderProductPO();
        orderProductPO4.setOrderProductId(0L);
        orderProductPO4.setOrderSn("orderSn");
        orderProductPO4.setMemberId(0);
        orderProductPO4.setProductId(0L);
        orderProductPO4.setProductNum(0);
        orderProductPO4.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO4.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderProductPO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO4.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderProductPO4.setXzCardAmount(new BigDecimal("0.00"));
        orderProductPO4.setIntegral(0);
        orderProductPO4.setCommissionAmount(new BigDecimal("0.00"));
        orderProductPO4.setServiceFee(new BigDecimal("0.00"));
        orderProductPO4.setThirdpartnarFee(new BigDecimal("0.00"));
        orderProductPO4.setOrderCommission(new BigDecimal("0.00"));
        orderProductPO4.setBusinessCommission(new BigDecimal("0.00"));
        orderProductPO4.setIsGift(0);
        orderProductPO4.setGiftGroup(0);
        orderProductPO4.setReturnNumber(0);
        orderProductPO4.setReplacementNumber(0);
        orderProductPO4.setDeliveryState(OrderProductDeliveryEnum.WAIT_DELIVERY);
        orderProductPO4.setEnabledFlag(0);
        orderProductPO4.setDeposit(new BigDecimal("0.00"));
//        when(mockOrderProductService.selectOneByOrderProductId(0L)).thenReturn(orderProductPO4);

        // Configure IOrderAfterService.isJdInterceptOrderProduct(...).
        final OrderProductPO orderProductPO5 = new OrderProductPO();
        orderProductPO5.setOrderProductId(0L);
        orderProductPO5.setOrderSn("orderSn");
        orderProductPO5.setMemberId(0);
        orderProductPO5.setProductId(0L);
        orderProductPO5.setProductNum(0);
        orderProductPO5.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO5.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO5.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderProductPO5.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO5.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderProductPO5.setXzCardAmount(new BigDecimal("0.00"));
        orderProductPO5.setIntegral(0);
        orderProductPO5.setCommissionAmount(new BigDecimal("0.00"));
        orderProductPO5.setServiceFee(new BigDecimal("0.00"));
        orderProductPO5.setThirdpartnarFee(new BigDecimal("0.00"));
        orderProductPO5.setOrderCommission(new BigDecimal("0.00"));
        orderProductPO5.setBusinessCommission(new BigDecimal("0.00"));
        orderProductPO5.setIsGift(0);
        orderProductPO5.setGiftGroup(0);
        orderProductPO5.setReturnNumber(0);
        orderProductPO5.setReplacementNumber(0);
        orderProductPO5.setDeliveryState(OrderProductDeliveryEnum.WAIT_DELIVERY);
        orderProductPO5.setEnabledFlag(0);
        orderProductPO5.setDeposit(new BigDecimal("0.00"));
//        when(mockOrderAfterService.isJdInterceptOrderProduct(orderProductPO5)).thenReturn(false);

        // Configure OrderReturnMapper.insert(...).
        final OrderReturnPO entity1 = new OrderReturnPO();
        entity1.setReturnId(0);
        entity1.setAfsSn("afsSn");
        entity1.setOrderSn("afsSn");
        entity1.setStoreId(0L);
        entity1.setStoreIsSelf(0);
        entity1.setStoreName("storeName");
        entity1.setMemberId(0);
        entity1.setMemberName("afsSn");
        entity1.setReturnMoneyType(0);
        entity1.setReturnType(0);
        entity1.setReturnNum(0);
        entity1.setRefundApplySumAmount(new BigDecimal("0.00"));
        entity1.setReturnMoneyAmount(new BigDecimal("0.00"));
        entity1.setRefundPunishAmount(new BigDecimal("0.00"));
        entity1.setReturnIntegralAmount(0);
        entity1.setDeductIntegralAmount(0);
        entity1.setReturnExpressAmount(new BigDecimal("0.00"));
        entity1.setReturnVoucherCode("returnVoucherCode");
        entity1.setCommissionRate(new BigDecimal("0.00"));
        entity1.setCommissionAmount(new BigDecimal("0.00"));
        entity1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        entity1.setPlatformVoucherAmountPlt(new BigDecimal("0.00"));
        entity1.setPlatformVoucherAmountStore(new BigDecimal("0.00"));
        entity1.setPlatformVoucherRetailAmountPlt(new BigDecimal("0.00"));
        entity1.setPlatformVoucherRetailAmountStore(new BigDecimal("0.00"));
        entity1.setPlatformActivityAmount(new BigDecimal("0.00"));
        entity1.setStoreActivityAmount(new BigDecimal("0.00"));
        entity1.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity1.setServiceFee(new BigDecimal("0.00"));
        entity1.setThirdpartnarFee(new BigDecimal("0.00"));
        entity1.setXzCardAmount(new BigDecimal("0.00"));
        entity1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity1.setOrderCommission(new BigDecimal("0.00"));
        entity1.setBusinessCommission(new BigDecimal("0.00"));
        entity1.setState(0);
        entity1.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity1.setPaymentMethod("value");
        entity1.setChannelServiceFee(new BigDecimal("0.00"));
        entity1.setActualReturnMoneyAmount(new BigDecimal("0.00"));
        entity1.setCustomerAssumeAmount(new BigDecimal("0.00"));
        entity1.setRefundType(0);
        entity1.setRefundStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity1.setOtherCompensationAmount(new BigDecimal("0.00"));
        entity1.setInterestPayer("interestPayer");
        entity1.setRemark("remark");
        entity1.setReturnBy(0);
        entity1.setBusinessType("businessType");
        entity1.setBusinessDescription("businessDescription");
        entity1.setEnabledFlag(0);
//        when(mockOrderReturnMapper.insert(entity1)).thenReturn(0);

//        when(mockOrderReturnService.isLastReturn("afsSn")).thenReturn(false);
//        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());
//        when(mockOrderReturnService.update(any(LambdaUpdateWrapper.class))).thenReturn(true);

        // Configure OrderAfterSaleLogMapper.insert(...).
        final OrderAfterSaleLogPO entity2 = new OrderAfterSaleLogPO();
        entity2.setLogRole(0);
        entity2.setLogUserId(0L);
        entity2.setLogUserName("afsSn");
        entity2.setAfsSn("afsSn");
        entity2.setAfsType(0);
        entity2.setState("102");
        entity2.setContent("买家申请仅退款");
        entity2.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        when(mockOrderAfterSaleLogMapper.insert(entity2)).thenReturn(0);

        // Configure OrderReturnValidation.refundPunishAmountSupport(...).
        final OrderReturnPO orderReturnPo = new OrderReturnPO();
        orderReturnPo.setReturnId(0);
        orderReturnPo.setAfsSn("afsSn");
        orderReturnPo.setOrderSn("afsSn");
        orderReturnPo.setStoreId(0L);
        orderReturnPo.setStoreIsSelf(0);
        orderReturnPo.setStoreName("storeName");
        orderReturnPo.setMemberId(0);
        orderReturnPo.setMemberName("afsSn");
        orderReturnPo.setReturnMoneyType(0);
        orderReturnPo.setReturnType(0);
        orderReturnPo.setReturnNum(0);
        orderReturnPo.setRefundApplySumAmount(new BigDecimal("0.00"));
        orderReturnPo.setReturnMoneyAmount(new BigDecimal("0.00"));
        orderReturnPo.setRefundPunishAmount(new BigDecimal("0.00"));
        orderReturnPo.setReturnIntegralAmount(0);
        orderReturnPo.setDeductIntegralAmount(0);
        orderReturnPo.setReturnExpressAmount(new BigDecimal("0.00"));
        orderReturnPo.setReturnVoucherCode("returnVoucherCode");
        orderReturnPo.setCommissionRate(new BigDecimal("0.00"));
        orderReturnPo.setCommissionAmount(new BigDecimal("0.00"));
        orderReturnPo.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderReturnPo.setPlatformVoucherAmountPlt(new BigDecimal("0.00"));
        orderReturnPo.setPlatformVoucherAmountStore(new BigDecimal("0.00"));
        orderReturnPo.setPlatformVoucherRetailAmountPlt(new BigDecimal("0.00"));
        orderReturnPo.setPlatformVoucherRetailAmountStore(new BigDecimal("0.00"));
        orderReturnPo.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderReturnPo.setStoreActivityAmount(new BigDecimal("0.00"));
        orderReturnPo.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderReturnPo.setServiceFee(new BigDecimal("0.00"));
        orderReturnPo.setThirdpartnarFee(new BigDecimal("0.00"));
        orderReturnPo.setXzCardAmount(new BigDecimal("0.00"));
        orderReturnPo.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderReturnPo.setOrderCommission(new BigDecimal("0.00"));
        orderReturnPo.setBusinessCommission(new BigDecimal("0.00"));
        orderReturnPo.setState(0);
        orderReturnPo.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderReturnPo.setPaymentMethod("value");
        orderReturnPo.setChannelServiceFee(new BigDecimal("0.00"));
        orderReturnPo.setActualReturnMoneyAmount(new BigDecimal("0.00"));
        orderReturnPo.setCustomerAssumeAmount(new BigDecimal("0.00"));
        orderReturnPo.setRefundType(0);
        orderReturnPo.setRefundStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderReturnPo.setOtherCompensationAmount(new BigDecimal("0.00"));
        orderReturnPo.setInterestPayer("interestPayer");
        orderReturnPo.setRemark("remark");
        orderReturnPo.setReturnBy(0);
        orderReturnPo.setBusinessType("businessType");
        orderReturnPo.setBusinessDescription("businessDescription");
        orderReturnPo.setEnabledFlag(0);
//        when(mockOrderReturnValidation.refundPunishAmountSupport(orderReturnPo)).thenReturn(false);

        // Configure RabbitTemplate.convertAndSend(...).
        final MessageSendVO object = new MessageSendVO();
        object.setDateName("dateName");
        object.setReceiveId(0L);
        object.setTplType("tplType");
        object.setMsgLinkInfo("msgLinkInfo");
        final MessageSendProperty messageSendProperty = new MessageSendProperty();
        object.setPropertyList(Arrays.asList(messageSendProperty));
//        doThrow(AmqpException.class).when(mockRabbitTemplate).convertAndSend("newmall_exchange",
//                "newmall_queue_seller_msg", object);

        // Configure OrderMapper.updateByPrimaryKeySelective(...).
        final OrderPO record = new OrderPO();
        record.setOrderId(0);
        record.setOrderSn("afsSn");
        record.setUserNo("operator");
        record.setUserMobile("phone");
        record.setPaySn("paySn");
        record.setStoreId(0L);
        record.setStoreIsSelf(0);
        record.setRecommendStoreId(0L);
        record.setStoreName("storeName");
        record.setMemberName("afsSn");
        record.setMemberId(0);
        record.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        record.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        record.setOrderState(0);
        record.setLoanPayState(0);
        record.setPaymentCode("paymentCode");
        record.setOrderAmount(new BigDecimal("0.00"));
        record.setGoodsAmount(new BigDecimal("0.00"));
        record.setExpressFee(new BigDecimal("0.00"));
        record.setActivityDiscountAmount(new BigDecimal("0.00"));
        record.setXzCardAmount(new BigDecimal("0.00"));
        record.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        record.setBalanceAmount(new BigDecimal("0.00"));
        record.setPayAmount(new BigDecimal("0.00"));
        record.setIntegral(0);
        record.setOrderType(0);
        record.setOrderPattern(0);
        record.setLockState(0);
        record.setChannelServiceFee(new BigDecimal("0.00"));
        record.setNewOrder(false);
        record.setAfterSalesDeadline(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        record.setExchangeFlag(0);
//        when(mockOrderMapper.updateByPrimaryKeySelective(record)).thenReturn(0);

        // Configure OrderCancelValidation.invokePresellClosePayment(...).
        final OrderPO orderPo = new OrderPO();
        orderPo.setOrderId(0);
        orderPo.setOrderSn("afsSn");
        orderPo.setUserNo("operator");
        orderPo.setUserMobile("phone");
        orderPo.setPaySn("paySn");
        orderPo.setStoreId(0L);
        orderPo.setStoreIsSelf(0);
        orderPo.setRecommendStoreId(0L);
        orderPo.setStoreName("storeName");
        orderPo.setMemberName("afsSn");
        orderPo.setMemberId(0);
        orderPo.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPo.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPo.setOrderState(0);
        orderPo.setLoanPayState(0);
        orderPo.setPaymentCode("paymentCode");
        orderPo.setOrderAmount(new BigDecimal("0.00"));
        orderPo.setGoodsAmount(new BigDecimal("0.00"));
        orderPo.setExpressFee(new BigDecimal("0.00"));
        orderPo.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPo.setXzCardAmount(new BigDecimal("0.00"));
        orderPo.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPo.setBalanceAmount(new BigDecimal("0.00"));
        orderPo.setPayAmount(new BigDecimal("0.00"));
        orderPo.setIntegral(0);
        orderPo.setOrderType(0);
        orderPo.setOrderPattern(0);
        orderPo.setLockState(0);
        orderPo.setChannelServiceFee(new BigDecimal("0.00"));
        orderPo.setNewOrder(false);
        orderPo.setAfterSalesDeadline(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPo.setExchangeFlag(0);
//        when(mockOrderCancelValidation.invokePresellClosePayment(orderPo)).thenReturn(false);

//        when(mockOrderPlacingService.update(any(LambdaUpdateWrapper.class))).thenReturn(false);
//        when(mockOrderPresellService.update(any(LambdaUpdateWrapper.class))).thenReturn(false);

        // Run the test
//        final Map<Long, String> result = orderAfterServiceModelUnderTest.applyAfterServiceForRebate("orderSn", 0);

        // Verify the results
//        assertEquals(expectedResult, result);
//        verify(mockOrderProductService).addReturnNumber(0L, 0);
//        verify(mockOrderProductService).dealOrderProductReturnStatus("APPLY", 0L);

        // Confirm OrderRefundRecordService.saveBatch(...).
        final OrderReturnPO orderReturnPO = new OrderReturnPO();
        orderReturnPO.setReturnId(0);
        orderReturnPO.setAfsSn("afsSn");
        orderReturnPO.setOrderSn("afsSn");
        orderReturnPO.setStoreId(0L);
        orderReturnPO.setStoreIsSelf(0);
        orderReturnPO.setStoreName("storeName");
        orderReturnPO.setMemberId(0);
        orderReturnPO.setMemberName("afsSn");
        orderReturnPO.setReturnMoneyType(0);
        orderReturnPO.setReturnType(0);
        orderReturnPO.setReturnNum(0);
        orderReturnPO.setRefundApplySumAmount(new BigDecimal("0.00"));
        orderReturnPO.setReturnMoneyAmount(new BigDecimal("0.00"));
        orderReturnPO.setRefundPunishAmount(new BigDecimal("0.00"));
        orderReturnPO.setReturnIntegralAmount(0);
        orderReturnPO.setDeductIntegralAmount(0);
        orderReturnPO.setReturnExpressAmount(new BigDecimal("0.00"));
        orderReturnPO.setReturnVoucherCode("returnVoucherCode");
        orderReturnPO.setCommissionRate(new BigDecimal("0.00"));
        orderReturnPO.setCommissionAmount(new BigDecimal("0.00"));
        orderReturnPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderReturnPO.setPlatformVoucherAmountPlt(new BigDecimal("0.00"));
        orderReturnPO.setPlatformVoucherAmountStore(new BigDecimal("0.00"));
        orderReturnPO.setPlatformVoucherRetailAmountPlt(new BigDecimal("0.00"));
        orderReturnPO.setPlatformVoucherRetailAmountStore(new BigDecimal("0.00"));
        orderReturnPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderReturnPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderReturnPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderReturnPO.setServiceFee(new BigDecimal("0.00"));
        orderReturnPO.setThirdpartnarFee(new BigDecimal("0.00"));
        orderReturnPO.setXzCardAmount(new BigDecimal("0.00"));
        orderReturnPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderReturnPO.setOrderCommission(new BigDecimal("0.00"));
        orderReturnPO.setBusinessCommission(new BigDecimal("0.00"));
        orderReturnPO.setState(0);
        orderReturnPO.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderReturnPO.setPaymentMethod("value");
        orderReturnPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderReturnPO.setActualReturnMoneyAmount(new BigDecimal("0.00"));
        orderReturnPO.setCustomerAssumeAmount(new BigDecimal("0.00"));
        orderReturnPO.setRefundType(0);
        orderReturnPO.setRefundStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderReturnPO.setOtherCompensationAmount(new BigDecimal("0.00"));
        orderReturnPO.setInterestPayer("interestPayer");
        orderReturnPO.setRemark("remark");
        orderReturnPO.setReturnBy(0);
        orderReturnPO.setBusinessType("businessType");
        orderReturnPO.setBusinessDescription("businessDescription");
        orderReturnPO.setEnabledFlag(0);
        final OrderPO orderPODb = new OrderPO();
        orderPODb.setOrderId(0);
        orderPODb.setOrderSn("afsSn");
        orderPODb.setUserNo("operator");
        orderPODb.setUserMobile("phone");
        orderPODb.setPaySn("paySn");
        orderPODb.setStoreId(0L);
        orderPODb.setStoreIsSelf(0);
        orderPODb.setRecommendStoreId(0L);
        orderPODb.setStoreName("storeName");
        orderPODb.setMemberName("afsSn");
        orderPODb.setMemberId(0);
        orderPODb.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPODb.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPODb.setOrderState(0);
        orderPODb.setLoanPayState(0);
        orderPODb.setPaymentCode("paymentCode");
        orderPODb.setOrderAmount(new BigDecimal("0.00"));
        orderPODb.setGoodsAmount(new BigDecimal("0.00"));
        orderPODb.setExpressFee(new BigDecimal("0.00"));
        orderPODb.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPODb.setXzCardAmount(new BigDecimal("0.00"));
        orderPODb.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPODb.setBalanceAmount(new BigDecimal("0.00"));
        orderPODb.setPayAmount(new BigDecimal("0.00"));
        orderPODb.setIntegral(0);
        orderPODb.setOrderType(0);
        orderPODb.setOrderPattern(0);
        orderPODb.setLockState(0);
        orderPODb.setChannelServiceFee(new BigDecimal("0.00"));
        orderPODb.setNewOrder(false);
        orderPODb.setAfterSalesDeadline(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPODb.setExchangeFlag(0);
//        verify(mockOrderRefundRecordService).saveBatch(orderReturnPO, orderPODb);
//        verify(mockOrderReturnTrackMapper).insert(OrderReturnTrackPO.builder()
//                .afsSn("afsSn")
//                .operateType(0)
//                .operator("operator")
//                .operatorRole(0)
//                .operateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
//                .operateResult(0)
//                .operateRemark("operateRemark")
//                .channel("channel")
//                .build());

        // Confirm OrderCreateHelper.addOrderReturnEvent(...).
        final OrderPO orderSn = new OrderPO();
        orderSn.setOrderId(0);
        orderSn.setOrderSn("afsSn");
        orderSn.setUserNo("operator");
        orderSn.setUserMobile("phone");
        orderSn.setPaySn("paySn");
        orderSn.setStoreId(0L);
        orderSn.setStoreIsSelf(0);
        orderSn.setRecommendStoreId(0L);
        orderSn.setStoreName("storeName");
        orderSn.setMemberName("afsSn");
        orderSn.setMemberId(0);
        orderSn.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderSn.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderSn.setOrderState(0);
        orderSn.setLoanPayState(0);
        orderSn.setPaymentCode("paymentCode");
        orderSn.setOrderAmount(new BigDecimal("0.00"));
        orderSn.setGoodsAmount(new BigDecimal("0.00"));
        orderSn.setExpressFee(new BigDecimal("0.00"));
        orderSn.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderSn.setXzCardAmount(new BigDecimal("0.00"));
        orderSn.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderSn.setBalanceAmount(new BigDecimal("0.00"));
        orderSn.setPayAmount(new BigDecimal("0.00"));
        orderSn.setIntegral(0);
        orderSn.setOrderType(0);
        orderSn.setOrderPattern(0);
        orderSn.setLockState(0);
        orderSn.setChannelServiceFee(new BigDecimal("0.00"));
        orderSn.setNewOrder(false);
        orderSn.setAfterSalesDeadline(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderSn.setExchangeFlag(0);
        final OrderReturnPO orderReturnPO1 = new OrderReturnPO();
        orderReturnPO1.setReturnId(0);
        orderReturnPO1.setAfsSn("afsSn");
        orderReturnPO1.setOrderSn("afsSn");
        orderReturnPO1.setStoreId(0L);
        orderReturnPO1.setStoreIsSelf(0);
        orderReturnPO1.setStoreName("storeName");
        orderReturnPO1.setMemberId(0);
        orderReturnPO1.setMemberName("afsSn");
        orderReturnPO1.setReturnMoneyType(0);
        orderReturnPO1.setReturnType(0);
        orderReturnPO1.setReturnNum(0);
        orderReturnPO1.setRefundApplySumAmount(new BigDecimal("0.00"));
        orderReturnPO1.setReturnMoneyAmount(new BigDecimal("0.00"));
        orderReturnPO1.setRefundPunishAmount(new BigDecimal("0.00"));
        orderReturnPO1.setReturnIntegralAmount(0);
        orderReturnPO1.setDeductIntegralAmount(0);
        orderReturnPO1.setReturnExpressAmount(new BigDecimal("0.00"));
        orderReturnPO1.setReturnVoucherCode("returnVoucherCode");
        orderReturnPO1.setCommissionRate(new BigDecimal("0.00"));
        orderReturnPO1.setCommissionAmount(new BigDecimal("0.00"));
        orderReturnPO1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderReturnPO1.setPlatformVoucherAmountPlt(new BigDecimal("0.00"));
        orderReturnPO1.setPlatformVoucherAmountStore(new BigDecimal("0.00"));
        orderReturnPO1.setPlatformVoucherRetailAmountPlt(new BigDecimal("0.00"));
        orderReturnPO1.setPlatformVoucherRetailAmountStore(new BigDecimal("0.00"));
        orderReturnPO1.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderReturnPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderReturnPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderReturnPO1.setServiceFee(new BigDecimal("0.00"));
        orderReturnPO1.setThirdpartnarFee(new BigDecimal("0.00"));
        orderReturnPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderReturnPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderReturnPO1.setOrderCommission(new BigDecimal("0.00"));
        orderReturnPO1.setBusinessCommission(new BigDecimal("0.00"));
        orderReturnPO1.setState(0);
        orderReturnPO1.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderReturnPO1.setPaymentMethod("value");
        orderReturnPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderReturnPO1.setActualReturnMoneyAmount(new BigDecimal("0.00"));
        orderReturnPO1.setCustomerAssumeAmount(new BigDecimal("0.00"));
        orderReturnPO1.setRefundType(0);
        orderReturnPO1.setRefundStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderReturnPO1.setOtherCompensationAmount(new BigDecimal("0.00"));
        orderReturnPO1.setInterestPayer("interestPayer");
        orderReturnPO1.setRemark("remark");
        orderReturnPO1.setReturnBy(0);
        orderReturnPO1.setBusinessType("businessType");
        orderReturnPO1.setBusinessDescription("businessDescription");
        orderReturnPO1.setEnabledFlag(0);
//        verify(mockOrderCreateHelper).addOrderReturnEvent(orderSn, new BigDecimal("0.00"), orderReturnPO1,
//                OrderEventEnum.REFUND_APPLY, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "applySource");
    }

    @Test     public void testApplyAfterServiceForRebate() throws Exception {
        // Setup
        final Map<Long,String> expectedResult = new HashMap<>();

        // Configure OrderModel.getOrderByOrderSn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("afsSn");
        orderPO.setUserNo("operator");
        orderPO.setUserMobile("phone");
        orderPO.setPaySn("paySn");
        orderPO.setStoreId(0L);
        orderPO.setStoreIsSelf(0);
        orderPO.setRecommendStoreId(0L);
        orderPO.setStoreName("storeName");
        orderPO.setMemberName("afsSn");
        orderPO.setMemberId(0);
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setIntegral(0);
        orderPO.setOrderType(0);
        orderPO.setOrderPattern(0);
        orderPO.setLockState(0);
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setAfterSalesDeadline(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setExchangeFlag(0);
//        when( mockOrderModel .getOrderByOrderSn("orderSn")).thenReturn(orderPO);

        // Configure IOrderProductService.listByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setMemberId(0);
        orderProductPO.setProductId(0L);
        orderProductPO.setProductNum(0);
        orderProductPO.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setXzCardAmount(new BigDecimal("0.00"));
        orderProductPO.setIntegral(0);
        orderProductPO.setCommissionAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setThirdpartnarFee(new BigDecimal("0.00"));
        orderProductPO.setOrderCommission(new BigDecimal("0.00"));
        orderProductPO.setBusinessCommission(new BigDecimal("0.00"));
        orderProductPO.setIsGift(0);
        orderProductPO.setGiftGroup(0);
        orderProductPO.setReturnNumber(0);
        orderProductPO.setReplacementNumber(0);
        orderProductPO.setDeliveryState(OrderProductDeliveryEnum.WAIT_DELIVERY);
        orderProductPO.setEnabledFlag(0);
        orderProductPO.setDeposit(new BigDecimal("0.00"));
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO);
//        when( mockOrderProductService .listByOrderSn("orderSn")).thenReturn(orderProductPOS);

        // Configure MemberFeignClient.getMemberByMemberId(...).
        final Member member = new Member();
        member.setMemberId(0);
        member.setUserNo("userNo");
        member.setCustNo("custNo");
        member.setMemberName("memberName");
        member.setMemberMobile("memberMobile");
//        when( mockMemberFeignClient .getMemberByMemberId(0)).thenReturn(member);

        // Configure IOrderPlacingService.getByOrderSn(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("afsSn");
        orderPO1.setUserNo("operator");
        orderPO1.setUserMobile("phone");
        orderPO1.setPaySn("paySn");
        orderPO1.setStoreId(0L);
        orderPO1.setStoreIsSelf(0);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setStoreName("storeName");
        orderPO1.setMemberName("afsSn");
        orderPO1.setMemberId(0);
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setIntegral(0);
        orderPO1.setOrderType(0);
        orderPO1.setOrderPattern(0);
        orderPO1.setLockState(0);
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setAfterSalesDeadline(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setExchangeFlag(0);
//        when( mockOrderPlacingService .getByOrderSn("orderSn")).thenReturn(orderPO1);

//        when( mockOrderReturnValidation .isInnerRefundStore(0L)).thenReturn( false );
//        when( mockOrderReturnService .hasDuringRefund("orderSn")).thenReturn( false );

        // Configure PresellRefundService.checkPresellOrder(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("afsSn");
        orderPO2.setUserNo("operator");
        orderPO2.setUserMobile("phone");
        orderPO2.setPaySn("paySn");
        orderPO2.setStoreId(0L);
        orderPO2.setStoreIsSelf(0);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setStoreName("storeName");
        orderPO2.setMemberName("afsSn");
        orderPO2.setMemberId(0);
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setIntegral(0);
        orderPO2.setOrderType(0);
        orderPO2.setOrderPattern(0);
        orderPO2.setLockState(0);
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setAfterSalesDeadline(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setExchangeFlag(0);
//        when( mockPresellRefundService .checkPresellOrder(orderPO2)).thenReturn( false );

//        when( mockStringRedisTemplate .opsForValue()).thenReturn( null );

        // Configure OrderPayRecordService.queryLoanNoRefundPayOrderByPaySn(...).
        final OrderPayRecordPO orderPayRecordPO = new OrderPayRecordPO();
        orderPayRecordPO.setEnabledFlag(0);
        orderPayRecordPO.setPaySn("paySn");
        orderPayRecordPO.setPaymentCode("paymentCode");
        orderPayRecordPO.setPayOrder(0);
        orderPayRecordPO.setPayStatus(0);
        final List<OrderPayRecordPO> orderPayRecordPOS = Arrays.asList(orderPayRecordPO);
//        when( mockOrderPayRecordService .queryLoanNoRefundPayOrderByPaySn("paySn")).thenReturn(orderPayRecordPOS);

        // Configure OrderMapper.listByExample(...).
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("afsSn");
        orderPO3.setUserNo("operator");
        orderPO3.setUserMobile("phone");
        orderPO3.setPaySn("paySn");
        orderPO3.setStoreId(0L);
        orderPO3.setStoreIsSelf(0);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setStoreName("storeName");
        orderPO3.setMemberName("afsSn");
        orderPO3.setMemberId(0);
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setIntegral(0);
        orderPO3.setOrderType(0);
        orderPO3.setOrderPattern(0);
        orderPO3.setLockState(0);
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setAfterSalesDeadline(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setExchangeFlag(0);
        final List<OrderPO> orderPOS = Arrays.asList(orderPO3);
        final OrderExample example = new OrderExample();
        example.setOrderIdNotEquals(0);
        example.setUserNo("userNo");
        example.setUserMobile("userMobile");
        example.setOrderSn("afsSn");
        example.setMemberId(0);
//        when( mockOrderMapper .listByExample(example)).thenReturn(orderPOS);

        // Configure IOrderProductService.list(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setMemberId(0);
        orderProductPO1.setProductId(0L);
        orderProductPO1.setProductNum(0);
        orderProductPO1.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderProductPO1.setIntegral(0);
        orderProductPO1.setCommissionAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setThirdpartnarFee(new BigDecimal("0.00"));
        orderProductPO1.setOrderCommission(new BigDecimal("0.00"));
        orderProductPO1.setBusinessCommission(new BigDecimal("0.00"));
        orderProductPO1.setIsGift(0);
        orderProductPO1.setGiftGroup(0);
        orderProductPO1.setReturnNumber(0);
        orderProductPO1.setReplacementNumber(0);
        orderProductPO1.setDeliveryState(OrderProductDeliveryEnum.WAIT_DELIVERY);
        orderProductPO1.setEnabledFlag(0);
        orderProductPO1.setDeposit(new BigDecimal("0.00"));
        final List<OrderProductPO> orderProductPOS1 = Arrays.asList(orderProductPO1);
//        when( mockOrderProductService .list(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS1);

        // Configure IOrderAfterService.isAllReturnApplied(...).
        final OrderAfterPO orderAfterPO = new OrderAfterPO();
        orderAfterPO.setAfsId(0);
        orderAfterPO.setAfsSn("afsSn");
        orderAfterPO.setStoreId(0L);
        orderAfterPO.setStoreIsSelf(0);
        orderAfterPO.setStoreName("storeName");
        orderAfterPO.setOrderSn("orderSn");
        orderAfterPO.setMemberId(0);
        orderAfterPO.setMemberName("afsSn");
        orderAfterPO.setGoodsId(0L);
        orderAfterPO.setOrderProductId(0L);
        orderAfterPO.setAfsNum(0);
        orderAfterPO.setBuyerExpressName("expressName");
        orderAfterPO.setBuyerExpressNumber("logisticsNumber");
        orderAfterPO.setBuyerExpressCode("expressCode");
        orderAfterPO.setBuyerDeliverTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderAfterPO.setApplyImage("applyImage");
        orderAfterPO.setContactName("afsSn");
        orderAfterPO.setContactPhone("contactPhone");
        orderAfterPO.setAfsType(0);
        orderAfterPO.setApplyReasonContent("operateRemark");
        orderAfterPO.setAfsDescription("afsDescription");
        orderAfterPO.setBuyerApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderAfterPO.setStoreAuditTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderAfterPO.setPlatformRemark("platformRemark");
        orderAfterPO.setStoreReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderAfterPO.setGoodsState(0);
        orderAfterPO.setProductDeliveryState(0);
        orderAfterPO.setSnapshotInformation("snapshotInformation");
        orderAfterPO.setEnabledFlag(0);
        orderAfterPO.setPerformanceMode(0);
        orderAfterPO.setRefundFailReason("failReason");
        final List<OrderAfterPO> afs = Arrays.asList(orderAfterPO);
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setMemberId(0);
        orderProductPO2.setProductId(0L);
        orderProductPO2.setProductNum(0);
        orderProductPO2.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderProductPO2.setIntegral(0);
        orderProductPO2.setCommissionAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setThirdpartnarFee(new BigDecimal("0.00"));
        orderProductPO2.setOrderCommission(new BigDecimal("0.00"));
        orderProductPO2.setBusinessCommission(new BigDecimal("0.00"));
        orderProductPO2.setIsGift(0);
        orderProductPO2.setGiftGroup(0);
        orderProductPO2.setReturnNumber(0);
        orderProductPO2.setReplacementNumber(0);
        orderProductPO2.setDeliveryState(OrderProductDeliveryEnum.WAIT_DELIVERY);
        orderProductPO2.setEnabledFlag(0);
        orderProductPO2.setDeposit(new BigDecimal("0.00"));
        final List<OrderProductPO> ops = Arrays.asList(orderProductPO2);
//        when( mockOrderAfterService .isAllReturnApplied(afs,ops)).thenReturn( true );

        // Configure IOrderAfterService.isAllReturnApplied(...).
        final OrderAfterDTO.AfterProduct afterProduct = new OrderAfterDTO.AfterProduct();
        afterProduct.setOrderProductId(0L);
        afterProduct.setAfsNum(0);
        afterProduct.setReturnAmount(new BigDecimal("0.00"));
        final List<OrderAfterDTO.AfterProduct> afsDTOs = Arrays.asList(afterProduct);
//        when( mockOrderAfterService .isAllReturnApplied(afsDTOs,"orderSn")).thenReturn( false );

        // Configure OrderProductMapper.getByPrimaryKey(...).
        final OrderProductPO orderProductPO3 = new OrderProductPO();
        orderProductPO3.setOrderProductId(0L);
        orderProductPO3.setOrderSn("orderSn");
        orderProductPO3.setMemberId(0);
        orderProductPO3.setProductId(0L);
        orderProductPO3.setProductNum(0);
        orderProductPO3.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO3.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderProductPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO3.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderProductPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderProductPO3.setIntegral(0);
        orderProductPO3.setCommissionAmount(new BigDecimal("0.00"));
        orderProductPO3.setServiceFee(new BigDecimal("0.00"));
        orderProductPO3.setThirdpartnarFee(new BigDecimal("0.00"));
        orderProductPO3.setOrderCommission(new BigDecimal("0.00"));
        orderProductPO3.setBusinessCommission(new BigDecimal("0.00"));
        orderProductPO3.setIsGift(0);
        orderProductPO3.setGiftGroup(0);
        orderProductPO3.setReturnNumber(0);
        orderProductPO3.setReplacementNumber(0);
        orderProductPO3.setDeliveryState(OrderProductDeliveryEnum.WAIT_DELIVERY);
        orderProductPO3.setEnabledFlag(0);
        orderProductPO3.setDeposit(new BigDecimal("0.00"));
//        when( mockOrderProductMapper .getByPrimaryKey(0L)).thenReturn(orderProductPO3);

        // Configure OrderExtendMapper.listByExample(...).
        final OrderExtendPO extendPO = new OrderExtendPO();
        extendPO.setExtendId(0);
        extendPO.setOrderSn("orderSn");
        extendPO.setOrderPointsCount(0);
        extendPO.setVoucherCode("returnVoucherCode");
        extendPO.setReceiverMobile("contactPhone");
        final List<OrderExtendPO> extendPOList = Arrays.asList(extendPO);
        final OrderExtendExample example1 = new OrderExtendExample();
        example1.setExtendIdNotEquals(0);
        example1.setExtendIdIn("extendIdIn");
        example1.setExtendId(0);
        example1.setOrderSn("afsSn");
        example1.setCustomerId("customerId");
//        when( mockOrderExtendMapper .listByExample(example1)).thenReturn(extendPOList);

//        when( mockOrderPayRecordService .lambdaQuery()).thenReturn( new LambdaQueryChainWrapper<>(null) );
//        when( mockRefundCalculator .calculateRefundPlatformVoucherFunderAmount(0L,CouponFunder.PLATFORM)).thenReturn( new BigDecimal("0.00") );
//        when( mockRefundCalculator .calculateOrderProductPlatformVoucherRetailAmount(0L,CouponFunder.PLATFORM)).thenReturn( new BigDecimal("0.00") );
//        when( mockOrderReturnService .getSumAmountByProductId(0L)).thenReturn( new HashMap<>() );
//        when( mockOrderReturnValidation .refundProductCheck(0L)).thenReturn( false );
//        when( mockOrderReturnValidation .validReturnNumGtDeliverNum(0L,0)).thenReturn( false );
//        when( mockShardingId .next(SeqEnum.RNO,"memberId")).thenReturn( 0L );

        // Configure OrderAfterMapper.insert(...).
        final OrderAfterPO entity = new OrderAfterPO();
        entity.setAfsId(0);
        entity.setAfsSn("afsSn");
        entity.setStoreId(0L);
        entity.setStoreIsSelf(0);
        entity.setStoreName("storeName");
        entity.setOrderSn("orderSn");
        entity.setMemberId(0);
        entity.setMemberName("afsSn");
        entity.setGoodsId(0L);
        entity.setOrderProductId(0L);
        entity.setAfsNum(0);
        entity.setBuyerExpressName("expressName");
        entity.setBuyerExpressNumber("logisticsNumber");
        entity.setBuyerExpressCode("expressCode");
        entity.setBuyerDeliverTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setApplyImage("applyImage");
        entity.setContactName("afsSn");
        entity.setContactPhone("contactPhone");
        entity.setAfsType(0);
        entity.setApplyReasonContent("operateRemark");
        entity.setAfsDescription("afsDescription");
        entity.setBuyerApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setStoreAuditTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPlatformRemark("platformRemark");
        entity.setStoreReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setGoodsState(0);
        entity.setProductDeliveryState(0);
        entity.setSnapshotInformation("snapshotInformation");
        entity.setEnabledFlag(0);
        entity.setPerformanceMode(0);
        entity.setRefundFailReason("failReason");
//        when( mockOrderAfterMapper .insert(entity)).thenReturn( 0 );

        // Configure OrderAfterServiceModel.getAfterServiceByAfsSn(...).
        final OrderAfterPO orderAfterPO1 = new OrderAfterPO();
        orderAfterPO1.setAfsId(0);
        orderAfterPO1.setAfsSn("afsSn");
        orderAfterPO1.setStoreId(0L);
        orderAfterPO1.setStoreIsSelf(0);
        orderAfterPO1.setStoreName("storeName");
        orderAfterPO1.setOrderSn("orderSn");
        orderAfterPO1.setMemberId(0);
        orderAfterPO1.setMemberName("afsSn");
        orderAfterPO1.setGoodsId(0L);
        orderAfterPO1.setOrderProductId(0L);
        orderAfterPO1.setAfsNum(0);
        orderAfterPO1.setBuyerExpressName("expressName");
        orderAfterPO1.setBuyerExpressNumber("logisticsNumber");
        orderAfterPO1.setBuyerExpressCode("expressCode");
        orderAfterPO1.setBuyerDeliverTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderAfterPO1.setApplyImage("applyImage");
        orderAfterPO1.setContactName("afsSn");
        orderAfterPO1.setContactPhone("contactPhone");
        orderAfterPO1.setAfsType(0);
        orderAfterPO1.setApplyReasonContent("operateRemark");
        orderAfterPO1.setAfsDescription("afsDescription");
        orderAfterPO1.setBuyerApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderAfterPO1.setStoreAuditTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderAfterPO1.setPlatformRemark("platformRemark");
        orderAfterPO1.setStoreReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderAfterPO1.setGoodsState(0);
        orderAfterPO1.setProductDeliveryState(0);
        orderAfterPO1.setSnapshotInformation("snapshotInformation");
        orderAfterPO1.setEnabledFlag(0);
        orderAfterPO1.setPerformanceMode(0);
        orderAfterPO1.setRefundFailReason("failReason");
//        when( mockOrderAfterServiceModel .getAfterServiceByAfsSn("afsSn")).thenReturn(orderAfterPO1);

        // Configure IOrderProductService.selectOneByOrderProductId(...).
        final OrderProductPO orderProductPO4 = new OrderProductPO();
        orderProductPO4.setOrderProductId(0L);
        orderProductPO4.setOrderSn("orderSn");
        orderProductPO4.setMemberId(0);
        orderProductPO4.setProductId(0L);
        orderProductPO4.setProductNum(0);
        orderProductPO4.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO4.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderProductPO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO4.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderProductPO4.setXzCardAmount(new BigDecimal("0.00"));
        orderProductPO4.setIntegral(0);
        orderProductPO4.setCommissionAmount(new BigDecimal("0.00"));
        orderProductPO4.setServiceFee(new BigDecimal("0.00"));
        orderProductPO4.setThirdpartnarFee(new BigDecimal("0.00"));
        orderProductPO4.setOrderCommission(new BigDecimal("0.00"));
        orderProductPO4.setBusinessCommission(new BigDecimal("0.00"));
        orderProductPO4.setIsGift(0);
        orderProductPO4.setGiftGroup(0);
        orderProductPO4.setReturnNumber(0);
        orderProductPO4.setReplacementNumber(0);
        orderProductPO4.setDeliveryState(OrderProductDeliveryEnum.WAIT_DELIVERY);
        orderProductPO4.setEnabledFlag(0);
        orderProductPO4.setDeposit(new BigDecimal("0.00"));
//        when( mockOrderProductService .selectOneByOrderProductId(0L)).thenReturn(orderProductPO4);

        // Configure IOrderAfterService.isJdInterceptOrderProduct(...).
        final OrderProductPO orderProductPO5 = new OrderProductPO();
        orderProductPO5.setOrderProductId(0L);
        orderProductPO5.setOrderSn("orderSn");
        orderProductPO5.setMemberId(0);
        orderProductPO5.setProductId(0L);
        orderProductPO5.setProductNum(0);
        orderProductPO5.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO5.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO5.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderProductPO5.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO5.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderProductPO5.setXzCardAmount(new BigDecimal("0.00"));
        orderProductPO5.setIntegral(0);
        orderProductPO5.setCommissionAmount(new BigDecimal("0.00"));
        orderProductPO5.setServiceFee(new BigDecimal("0.00"));
        orderProductPO5.setThirdpartnarFee(new BigDecimal("0.00"));
        orderProductPO5.setOrderCommission(new BigDecimal("0.00"));
        orderProductPO5.setBusinessCommission(new BigDecimal("0.00"));
        orderProductPO5.setIsGift(0);
        orderProductPO5.setGiftGroup(0);
        orderProductPO5.setReturnNumber(0);
        orderProductPO5.setReplacementNumber(0);
        orderProductPO5.setDeliveryState(OrderProductDeliveryEnum.WAIT_DELIVERY);
        orderProductPO5.setEnabledFlag(0);
        orderProductPO5.setDeposit(new BigDecimal("0.00"));
//        when( mockOrderAfterService .isJdInterceptOrderProduct(orderProductPO5)).thenReturn( false );

        // Configure OrderReturnMapper.insert(...).
        final OrderReturnPO entity1 = new OrderReturnPO();
        entity1.setReturnId(0);
        entity1.setAfsSn("afsSn");
        entity1.setOrderSn("afsSn");
        entity1.setStoreId(0L);
        entity1.setStoreIsSelf(0);
        entity1.setStoreName("storeName");
        entity1.setMemberId(0);
        entity1.setMemberName("afsSn");
        entity1.setReturnMoneyType(0);
        entity1.setReturnType(0);
        entity1.setReturnNum(0);
        entity1.setRefundApplySumAmount(new BigDecimal("0.00"));
        entity1.setReturnMoneyAmount(new BigDecimal("0.00"));
        entity1.setRefundPunishAmount(new BigDecimal("0.00"));
        entity1.setReturnIntegralAmount(0);
        entity1.setDeductIntegralAmount(0);
        entity1.setReturnExpressAmount(new BigDecimal("0.00"));
        entity1.setReturnVoucherCode("returnVoucherCode");
        entity1.setCommissionRate(new BigDecimal("0.00"));
        entity1.setCommissionAmount(new BigDecimal("0.00"));
        entity1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        entity1.setPlatformVoucherAmountPlt(new BigDecimal("0.00"));
        entity1.setPlatformVoucherAmountStore(new BigDecimal("0.00"));
        entity1.setPlatformVoucherRetailAmountPlt(new BigDecimal("0.00"));
        entity1.setPlatformVoucherRetailAmountStore(new BigDecimal("0.00"));
        entity1.setPlatformActivityAmount(new BigDecimal("0.00"));
        entity1.setStoreActivityAmount(new BigDecimal("0.00"));
        entity1.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity1.setServiceFee(new BigDecimal("0.00"));
        entity1.setThirdpartnarFee(new BigDecimal("0.00"));
        entity1.setXzCardAmount(new BigDecimal("0.00"));
        entity1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity1.setOrderCommission(new BigDecimal("0.00"));
        entity1.setBusinessCommission(new BigDecimal("0.00"));
        entity1.setState(0);
        entity1.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity1.setPaymentMethod("value");
        entity1.setChannelServiceFee(new BigDecimal("0.00"));
        entity1.setActualReturnMoneyAmount(new BigDecimal("0.00"));
        entity1.setCustomerAssumeAmount(new BigDecimal("0.00"));
        entity1.setRefundType(0);
        entity1.setRefundStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity1.setOtherCompensationAmount(new BigDecimal("0.00"));
        entity1.setInterestPayer("interestPayer");
        entity1.setRemark("remark");
        entity1.setReturnBy(0);
        entity1.setBusinessType("businessType");
        entity1.setBusinessDescription("businessDescription");
        entity1.setEnabledFlag(0);
//        when( mockOrderReturnMapper .insert(entity1)).thenReturn( 0 );

//        when( mockOrderReturnService .isLastReturn("afsSn")).thenReturn( false );
//        when( mockChannelFeeRateConfig .getMappedRate()).thenReturn( new HashMap<>() );
//        when( mockOrderReturnService .update(any(LambdaUpdateWrapper.class))).thenReturn( true );

        // Configure OrderAfterSaleLogMapper.insert(...).
        final OrderAfterSaleLogPO entity2 = new OrderAfterSaleLogPO();
        entity2.setLogRole(0);
        entity2.setLogUserId(0L);
        entity2.setLogUserName("afsSn");
        entity2.setAfsSn("afsSn");
        entity2.setAfsType(0);
        entity2.setState("102");
        entity2.setContent("买家申请仅退款");
        entity2.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        when( mockOrderAfterSaleLogMapper .insert(entity2)).thenReturn( 0 );

        // Configure OrderReturnValidation.refundPunishAmountSupport(...).
        final OrderReturnPO orderReturnPo = new OrderReturnPO();
        orderReturnPo.setReturnId(0);
        orderReturnPo.setAfsSn("afsSn");
        orderReturnPo.setOrderSn("afsSn");
        orderReturnPo.setStoreId(0L);
        orderReturnPo.setStoreIsSelf(0);
        orderReturnPo.setStoreName("storeName");
        orderReturnPo.setMemberId(0);
        orderReturnPo.setMemberName("afsSn");
        orderReturnPo.setReturnMoneyType(0);
        orderReturnPo.setReturnType(0);
        orderReturnPo.setReturnNum(0);
        orderReturnPo.setRefundApplySumAmount(new BigDecimal("0.00"));
        orderReturnPo.setReturnMoneyAmount(new BigDecimal("0.00"));
        orderReturnPo.setRefundPunishAmount(new BigDecimal("0.00"));
        orderReturnPo.setReturnIntegralAmount(0);
        orderReturnPo.setDeductIntegralAmount(0);
        orderReturnPo.setReturnExpressAmount(new BigDecimal("0.00"));
        orderReturnPo.setReturnVoucherCode("returnVoucherCode");
        orderReturnPo.setCommissionRate(new BigDecimal("0.00"));
        orderReturnPo.setCommissionAmount(new BigDecimal("0.00"));
        orderReturnPo.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderReturnPo.setPlatformVoucherAmountPlt(new BigDecimal("0.00"));
        orderReturnPo.setPlatformVoucherAmountStore(new BigDecimal("0.00"));
        orderReturnPo.setPlatformVoucherRetailAmountPlt(new BigDecimal("0.00"));
        orderReturnPo.setPlatformVoucherRetailAmountStore(new BigDecimal("0.00"));
        orderReturnPo.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderReturnPo.setStoreActivityAmount(new BigDecimal("0.00"));
        orderReturnPo.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderReturnPo.setServiceFee(new BigDecimal("0.00"));
        orderReturnPo.setThirdpartnarFee(new BigDecimal("0.00"));
        orderReturnPo.setXzCardAmount(new BigDecimal("0.00"));
        orderReturnPo.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderReturnPo.setOrderCommission(new BigDecimal("0.00"));
        orderReturnPo.setBusinessCommission(new BigDecimal("0.00"));
        orderReturnPo.setState(0);
        orderReturnPo.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderReturnPo.setPaymentMethod("value");
        orderReturnPo.setChannelServiceFee(new BigDecimal("0.00"));
        orderReturnPo.setActualReturnMoneyAmount(new BigDecimal("0.00"));
        orderReturnPo.setCustomerAssumeAmount(new BigDecimal("0.00"));
        orderReturnPo.setRefundType(0);
        orderReturnPo.setRefundStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderReturnPo.setOtherCompensationAmount(new BigDecimal("0.00"));
        orderReturnPo.setInterestPayer("interestPayer");
        orderReturnPo.setRemark("remark");
        orderReturnPo.setReturnBy(0);
        orderReturnPo.setBusinessType("businessType");
        orderReturnPo.setBusinessDescription("businessDescription");
        orderReturnPo.setEnabledFlag(0);
//        when( mockOrderReturnValidation .refundPunishAmountSupport(orderReturnPo)).thenReturn( false );

        // Configure OrderMapper.updateByPrimaryKeySelective(...).
        final OrderPO record = new OrderPO();
        record.setOrderId(0);
        record.setOrderSn("afsSn");
        record.setUserNo("operator");
        record.setUserMobile("phone");
        record.setPaySn("paySn");
        record.setStoreId(0L);
        record.setStoreIsSelf(0);
        record.setRecommendStoreId(0L);
        record.setStoreName("storeName");
        record.setMemberName("afsSn");
        record.setMemberId(0);
        record.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        record.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        record.setOrderState(0);
        record.setLoanPayState(0);
        record.setPaymentCode("paymentCode");
        record.setOrderAmount(new BigDecimal("0.00"));
        record.setGoodsAmount(new BigDecimal("0.00"));
        record.setExpressFee(new BigDecimal("0.00"));
        record.setActivityDiscountAmount(new BigDecimal("0.00"));
        record.setXzCardAmount(new BigDecimal("0.00"));
        record.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        record.setBalanceAmount(new BigDecimal("0.00"));
        record.setPayAmount(new BigDecimal("0.00"));
        record.setIntegral(0);
        record.setOrderType(0);
        record.setOrderPattern(0);
        record.setLockState(0);
        record.setChannelServiceFee(new BigDecimal("0.00"));
        record.setNewOrder(false);
        record.setAfterSalesDeadline(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        record.setExchangeFlag(0);
//        when( mockOrderMapper .updateByPrimaryKeySelective(record)).thenReturn( 0 );

        // Configure OrderCancelValidation.invokePresellClosePayment(...).
        final OrderPO orderPo = new OrderPO();
        orderPo.setOrderId(0);
        orderPo.setOrderSn("afsSn");
        orderPo.setUserNo("operator");
        orderPo.setUserMobile("phone");
        orderPo.setPaySn("paySn");
        orderPo.setStoreId(0L);
        orderPo.setStoreIsSelf(0);
        orderPo.setRecommendStoreId(0L);
        orderPo.setStoreName("storeName");
        orderPo.setMemberName("afsSn");
        orderPo.setMemberId(0);
        orderPo.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPo.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPo.setOrderState(0);
        orderPo.setLoanPayState(0);
        orderPo.setPaymentCode("paymentCode");
        orderPo.setOrderAmount(new BigDecimal("0.00"));
        orderPo.setGoodsAmount(new BigDecimal("0.00"));
        orderPo.setExpressFee(new BigDecimal("0.00"));
        orderPo.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPo.setXzCardAmount(new BigDecimal("0.00"));
        orderPo.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPo.setBalanceAmount(new BigDecimal("0.00"));
        orderPo.setPayAmount(new BigDecimal("0.00"));
        orderPo.setIntegral(0);
        orderPo.setOrderType(0);
        orderPo.setOrderPattern(0);
        orderPo.setLockState(0);
        orderPo.setChannelServiceFee(new BigDecimal("0.00"));
        orderPo.setNewOrder(false);
        orderPo.setAfterSalesDeadline(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPo.setExchangeFlag(0);
//        when( mockOrderCancelValidation .invokePresellClosePayment(orderPo)).thenReturn( false );

//        when( mockOrderPlacingService .update(any(LambdaUpdateWrapper.class))).thenReturn( false );
//        when( mockOrderPresellService .update(any(LambdaUpdateWrapper.class))).thenReturn( false );

        // Run the test
//        final Map<Long,String> result =  orderAfterServiceModelUnderTest.applyAfterServiceForRebate("orderSn",0);

        // Verify the results
//        assertEquals(expectedResult , result) ;
//        verify( mockOrderProductService ).addReturnNumber(0L,0);
//        verify( mockOrderProductService ).dealOrderProductReturnStatus("APPLY",0L);

        // Confirm OrderRefundRecordService.saveBatch(...).
        final OrderReturnPO orderReturnPO = new OrderReturnPO();
        orderReturnPO.setReturnId(0);
        orderReturnPO.setAfsSn("afsSn");
        orderReturnPO.setOrderSn("afsSn");
        orderReturnPO.setStoreId(0L);
        orderReturnPO.setStoreIsSelf(0);
        orderReturnPO.setStoreName("storeName");
        orderReturnPO.setMemberId(0);
        orderReturnPO.setMemberName("afsSn");
        orderReturnPO.setReturnMoneyType(0);
        orderReturnPO.setReturnType(0);
        orderReturnPO.setReturnNum(0);
        orderReturnPO.setRefundApplySumAmount(new BigDecimal("0.00"));
        orderReturnPO.setReturnMoneyAmount(new BigDecimal("0.00"));
        orderReturnPO.setRefundPunishAmount(new BigDecimal("0.00"));
        orderReturnPO.setReturnIntegralAmount(0);
        orderReturnPO.setDeductIntegralAmount(0);
        orderReturnPO.setReturnExpressAmount(new BigDecimal("0.00"));
        orderReturnPO.setReturnVoucherCode("returnVoucherCode");
        orderReturnPO.setCommissionRate(new BigDecimal("0.00"));
        orderReturnPO.setCommissionAmount(new BigDecimal("0.00"));
        orderReturnPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderReturnPO.setPlatformVoucherAmountPlt(new BigDecimal("0.00"));
        orderReturnPO.setPlatformVoucherAmountStore(new BigDecimal("0.00"));
        orderReturnPO.setPlatformVoucherRetailAmountPlt(new BigDecimal("0.00"));
        orderReturnPO.setPlatformVoucherRetailAmountStore(new BigDecimal("0.00"));
        orderReturnPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderReturnPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderReturnPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderReturnPO.setServiceFee(new BigDecimal("0.00"));
        orderReturnPO.setThirdpartnarFee(new BigDecimal("0.00"));
        orderReturnPO.setXzCardAmount(new BigDecimal("0.00"));
        orderReturnPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderReturnPO.setOrderCommission(new BigDecimal("0.00"));
        orderReturnPO.setBusinessCommission(new BigDecimal("0.00"));
        orderReturnPO.setState(0);
        orderReturnPO.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderReturnPO.setPaymentMethod("value");
        orderReturnPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderReturnPO.setActualReturnMoneyAmount(new BigDecimal("0.00"));
        orderReturnPO.setCustomerAssumeAmount(new BigDecimal("0.00"));
        orderReturnPO.setRefundType(0);
        orderReturnPO.setRefundStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderReturnPO.setOtherCompensationAmount(new BigDecimal("0.00"));
        orderReturnPO.setInterestPayer("interestPayer");
        orderReturnPO.setRemark("remark");
        orderReturnPO.setReturnBy(0);
        orderReturnPO.setBusinessType("businessType");
        orderReturnPO.setBusinessDescription("businessDescription");
        orderReturnPO.setEnabledFlag(0);
        final OrderPO orderPODb = new OrderPO();
        orderPODb.setOrderId(0);
        orderPODb.setOrderSn("afsSn");
        orderPODb.setUserNo("operator");
        orderPODb.setUserMobile("phone");
        orderPODb.setPaySn("paySn");
        orderPODb.setStoreId(0L);
        orderPODb.setStoreIsSelf(0);
        orderPODb.setRecommendStoreId(0L);
        orderPODb.setStoreName("storeName");
        orderPODb.setMemberName("afsSn");
        orderPODb.setMemberId(0);
        orderPODb.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPODb.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPODb.setOrderState(0);
        orderPODb.setLoanPayState(0);
        orderPODb.setPaymentCode("paymentCode");
        orderPODb.setOrderAmount(new BigDecimal("0.00"));
        orderPODb.setGoodsAmount(new BigDecimal("0.00"));
        orderPODb.setExpressFee(new BigDecimal("0.00"));
        orderPODb.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPODb.setXzCardAmount(new BigDecimal("0.00"));
        orderPODb.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPODb.setBalanceAmount(new BigDecimal("0.00"));
        orderPODb.setPayAmount(new BigDecimal("0.00"));
        orderPODb.setIntegral(0);
        orderPODb.setOrderType(0);
        orderPODb.setOrderPattern(0);
        orderPODb.setLockState(0);
        orderPODb.setChannelServiceFee(new BigDecimal("0.00"));
        orderPODb.setNewOrder(false);
        orderPODb.setAfterSalesDeadline(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPODb.setExchangeFlag(0);
//        verify( mockOrderRefundRecordService ).saveBatch(orderReturnPO,orderPODb);
//        verify( mockOrderReturnTrackMapper ).insert(OrderReturnTrackPO.builder()
//                .afsSn("afsSn")
//                .operateType(0)
//                .operator("operator")
//                .operatorRole(0)
//                .operateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
//                .operateResult(0)
//                .operateRemark("operateRemark")
//                .channel("channel")
//                .build());

        // Confirm RabbitTemplate.convertAndSend(...).
        final MessageSendVO object = new MessageSendVO();
        object.setDateName("dateName");
        object.setReceiveId(0L);
        object.setTplType("tplType");
        object.setMsgLinkInfo("msgLinkInfo");
        final MessageSendProperty messageSendProperty = new MessageSendProperty();
        object.setPropertyList(Arrays.asList(messageSendProperty));
//        verify( mockRabbitTemplate ).convertAndSend("newmall_exchange","newmall_queue_seller_msg",object);

        // Confirm OrderCreateHelper.addOrderReturnEvent(...).
        final OrderPO orderSn = new OrderPO();
        orderSn.setOrderId(0);
        orderSn.setOrderSn("afsSn");
        orderSn.setUserNo("operator");
        orderSn.setUserMobile("phone");
        orderSn.setPaySn("paySn");
        orderSn.setStoreId(0L);
        orderSn.setStoreIsSelf(0);
        orderSn.setRecommendStoreId(0L);
        orderSn.setStoreName("storeName");
        orderSn.setMemberName("afsSn");
        orderSn.setMemberId(0);
        orderSn.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderSn.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderSn.setOrderState(0);
        orderSn.setLoanPayState(0);
        orderSn.setPaymentCode("paymentCode");
        orderSn.setOrderAmount(new BigDecimal("0.00"));
        orderSn.setGoodsAmount(new BigDecimal("0.00"));
        orderSn.setExpressFee(new BigDecimal("0.00"));
        orderSn.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderSn.setXzCardAmount(new BigDecimal("0.00"));
        orderSn.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderSn.setBalanceAmount(new BigDecimal("0.00"));
        orderSn.setPayAmount(new BigDecimal("0.00"));
        orderSn.setIntegral(0);
        orderSn.setOrderType(0);
        orderSn.setOrderPattern(0);
        orderSn.setLockState(0);
        orderSn.setChannelServiceFee(new BigDecimal("0.00"));
        orderSn.setNewOrder(false);
        orderSn.setAfterSalesDeadline(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderSn.setExchangeFlag(0);
        final OrderReturnPO orderReturnPO1 = new OrderReturnPO();
        orderReturnPO1.setReturnId(0);
        orderReturnPO1.setAfsSn("afsSn");
        orderReturnPO1.setOrderSn("afsSn");
        orderReturnPO1.setStoreId(0L);
        orderReturnPO1.setStoreIsSelf(0);
        orderReturnPO1.setStoreName("storeName");
        orderReturnPO1.setMemberId(0);
        orderReturnPO1.setMemberName("afsSn");
        orderReturnPO1.setReturnMoneyType(0);
        orderReturnPO1.setReturnType(0);
        orderReturnPO1.setReturnNum(0);
        orderReturnPO1.setRefundApplySumAmount(new BigDecimal("0.00"));
        orderReturnPO1.setReturnMoneyAmount(new BigDecimal("0.00"));
        orderReturnPO1.setRefundPunishAmount(new BigDecimal("0.00"));
        orderReturnPO1.setReturnIntegralAmount(0);
        orderReturnPO1.setDeductIntegralAmount(0);
        orderReturnPO1.setReturnExpressAmount(new BigDecimal("0.00"));
        orderReturnPO1.setReturnVoucherCode("returnVoucherCode");
        orderReturnPO1.setCommissionRate(new BigDecimal("0.00"));
        orderReturnPO1.setCommissionAmount(new BigDecimal("0.00"));
        orderReturnPO1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderReturnPO1.setPlatformVoucherAmountPlt(new BigDecimal("0.00"));
        orderReturnPO1.setPlatformVoucherAmountStore(new BigDecimal("0.00"));
        orderReturnPO1.setPlatformVoucherRetailAmountPlt(new BigDecimal("0.00"));
        orderReturnPO1.setPlatformVoucherRetailAmountStore(new BigDecimal("0.00"));
        orderReturnPO1.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderReturnPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderReturnPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderReturnPO1.setServiceFee(new BigDecimal("0.00"));
        orderReturnPO1.setThirdpartnarFee(new BigDecimal("0.00"));
        orderReturnPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderReturnPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderReturnPO1.setOrderCommission(new BigDecimal("0.00"));
        orderReturnPO1.setBusinessCommission(new BigDecimal("0.00"));
        orderReturnPO1.setState(0);
        orderReturnPO1.setApplyTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderReturnPO1.setPaymentMethod("value");
        orderReturnPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderReturnPO1.setActualReturnMoneyAmount(new BigDecimal("0.00"));
        orderReturnPO1.setCustomerAssumeAmount(new BigDecimal("0.00"));
        orderReturnPO1.setRefundType(0);
        orderReturnPO1.setRefundStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderReturnPO1.setOtherCompensationAmount(new BigDecimal("0.00"));
        orderReturnPO1.setInterestPayer("interestPayer");
        orderReturnPO1.setRemark("remark");
        orderReturnPO1.setReturnBy(0);
        orderReturnPO1.setBusinessType("businessType");
        orderReturnPO1.setBusinessDescription("businessDescription");
        orderReturnPO1.setEnabledFlag(0);
//        verify( mockOrderCreateHelper ).addOrderReturnEvent(orderSn,new BigDecimal("0.00"),orderReturnPO1,OrderEventEnum.REFUND_APPLY,new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),"applySource");
    }
}
