package com.cfpamf.ms.mallorder.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cfpamf.ms.mall.account.vo.AccountCard;
import com.cfpamf.ms.mallorder.common.enums.trade.TradeBankEnum;
import com.cfpamf.ms.mallorder.controller.fegin.facade.AccountCardFacade;
import com.cfpamf.ms.mallorder.dto.OrderAutoPayDTO;
import com.cfpamf.ms.mallorder.integration.hrms.HrmsIntegration;
import com.cfpamf.ms.mallorder.integration.settlement.BillOperatinIntegration;
import com.cfpamf.ms.mallorder.integration.trade.CoreTradeIntegration;
import com.cfpamf.ms.mallorder.mapper.BzBankPayMapper;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.model.OrderPayModel;
import com.cfpamf.ms.mallorder.model.OrderReturnModel;
import com.cfpamf.ms.mallorder.po.BzBankPayPO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.req.PayV2Request;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.vo.EmployeePaySubjectRelationVO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BzBankPayServiceImplTest {

	@Mock
	BzBankPayMapper baseMapper;
	@Mock
	private IOrderPayService mockOrderPayService;
	@Mock
	private IOrderReturnService mockOrderReturnService;
	@Mock
	private OrderModel mockOrderModel;
	@Mock
	private OrderReturnModel mockOrderReturnModel;
	@Mock
	private CoreTradeIntegration mockCoreTradeIntegration;
	@Mock
	private IBzBankPayService mockIBzBankPayService;
	@Mock(lenient = true)
	private HrmsIntegration mockHrmsIntegration;
	@Mock(lenient = true)
	private BillOperatinIntegration mockBillOperatinIntegration;
	@Mock(lenient = true)
	private IOrderService mockIOrderService;
	@Mock
	private OrderPayModel mockOrderPayModel;
	@Mock
	private IOrderExtendService mockIOrderExtendService;
	@Mock
	private IOrderProductService mockIOrderProductService;
	@Mock
	private AccountCardFacade mockAccountCardFacade;
	@InjectMocks
	private BzBankPayServiceImpl bzBankPayServiceImplUnderTest;

	@Before
	public void setUp() {
		MockitoAnnotations.initMocks(this);
	}

	@Test
	public void testOrderAutoPayJob() throws Exception {
		// Setup
		// Configure IOrderPayService.listOrderAutoPay(...).
		final OrderAutoPayDTO orderAutoPayDTO = new OrderAutoPayDTO();
		orderAutoPayDTO.setOrderSn("orderSn");
		orderAutoPayDTO.setPaySn("paySn");
		orderAutoPayDTO.setParentSn("parentSn");
		orderAutoPayDTO.setStoreId("storeId");
		orderAutoPayDTO.setOrderAmount(new BigDecimal("0.00"));
		orderAutoPayDTO.setLoanOrgNo("loanOrgNo");
		orderAutoPayDTO.setUserNumber("userNumber");
		orderAutoPayDTO.setAccountNo("accountNo");
		orderAutoPayDTO.setAccountName("accountName");
		orderAutoPayDTO.setAccountType("accountType");
		orderAutoPayDTO.setBankNo("bankNo");
		final List<OrderAutoPayDTO> orderAutoPayDTOS = Arrays.asList(orderAutoPayDTO);
		when(mockOrderPayService.listOrderAutoPay()).thenReturn(orderAutoPayDTOS);

		// Configure BillOperatinIntegration.getAccount(...).
		AccountCard accountCard = new AccountCard();
		accountCard.setCardId(0L);
		accountCard.setAccountType(0);
		accountCard.setBankAccountName("accountName");
		accountCard.setBankAccountNumber("accountNo");
		accountCard.setBankNo("bankNo");
		when(mockBillOperatinIntegration.getAccount("storeId")).thenReturn(accountCard);

		// Configure HrmsIntegration.queryEmployeePaySubjectRelation(...).
		EmployeePaySubjectRelationVO employeePaySubjectRelationVO = new EmployeePaySubjectRelationVO();
		employeePaySubjectRelationVO.setEmployeeId(0L);
		employeePaySubjectRelationVO.setEmployeeName("employeeName");
		employeePaySubjectRelationVO.setEmployeeCode("userNumber");
		employeePaySubjectRelationVO.setEmployeeIdNo("employeeIdNo");
		employeePaySubjectRelationVO.setPaySubjectCode("loanOrgNo");
		final List<EmployeePaySubjectRelationVO> employeePaySubjectRelationVOS = Arrays.asList(
				employeePaySubjectRelationVO);
		when(mockHrmsIntegration.queryEmployeePaySubjectRelation(any()))
				.thenReturn(employeePaySubjectRelationVOS);

		// Configure IOrderService.getOne(...).
		final OrderPO orderPO = new OrderPO();
		orderPO.setOrderId(0);
		orderPO.setOrderSn("orderSn");
		orderPO.setUserNo("userNo");
		orderPO.setOrderState(0);
		orderPO.setOrderAmount(new BigDecimal("0.00"));
		when(mockIOrderService.getOne(any(LambdaQueryWrapper.class))).thenReturn(orderPO);

		// Run the test
		boolean result = bzBankPayServiceImplUnderTest.orderAutoPayJob();

		assertTrue(result);
	}

	@Test
	public void testOrderAutoPayJob_IOrderPayServiceReturnsNoItems() throws Exception {
		// Setup
		when(mockOrderPayService.listOrderAutoPay()).thenReturn(Collections.emptyList());

		// Configure BillOperatinIntegration.getAccount(...).
		final AccountCard accountCard = new AccountCard();
		accountCard.setCardId(0L);
		accountCard.setAccountType(0);
		accountCard.setBankAccountName("accountName");
		accountCard.setBankAccountNumber("accountNo");
		accountCard.setBankNo("bankNo");
		when(mockBillOperatinIntegration.getAccount("storeId")).thenReturn(accountCard);

		// Configure HrmsIntegration.queryEmployeePaySubjectRelation(...).
		final EmployeePaySubjectRelationVO employeePaySubjectRelationVO = new EmployeePaySubjectRelationVO();
		employeePaySubjectRelationVO.setEmployeeId(0L);
		employeePaySubjectRelationVO.setEmployeeName("employeeName");
		employeePaySubjectRelationVO.setEmployeeCode("employeeCode");
		employeePaySubjectRelationVO.setEmployeeIdNo("employeeIdNo");
		employeePaySubjectRelationVO.setPaySubjectCode("loanOrgNo");
		final List<EmployeePaySubjectRelationVO> employeePaySubjectRelationVOS = Arrays.asList(
				employeePaySubjectRelationVO);
		when(mockHrmsIntegration.queryEmployeePaySubjectRelation(Arrays.asList("value")))
				.thenReturn(employeePaySubjectRelationVOS);

		// Configure IOrderService.getOne(...).
		final OrderPO orderPO = new OrderPO();
		orderPO.setOrderId(0);
		orderPO.setOrderSn("orderSn");
		orderPO.setUserNo("userNo");
		orderPO.setOrderState(0);
		orderPO.setOrderAmount(new BigDecimal("0.00"));
		when(mockIOrderService.getOne(any(LambdaQueryWrapper.class))).thenReturn(orderPO);

		// Run the test
		boolean result = bzBankPayServiceImplUnderTest.orderAutoPayJob();

		// Verify the results
		assertTrue(result);

	}

	@Test
	public void testDealPay_success() {
		BzBankPayPO bzBankPayPO = new BzBankPayPO();
		bzBankPayPO.setStatus(TradeBankEnum.INIT.getValue());

		PayV2Request payV2Request = new PayV2Request();

		//when(mockCoreTradeIntegration.payV2(payV2Request)).thenReturn(any());
//		when(baseMapper.updateById(bzBankPayPO)).thenReturn(1);
//
//		bzBankPayServiceImplUnderTest.dealPay(bzBankPayPO, payV2Request);
//
//		Assert.assertEquals(TradeBankEnum.CREATE_SUCCESS.getValue(), bzBankPayPO.getStatus());
//		verify(mockCoreTradeIntegration, Mockito.times(1)).payV2(payV2Request);

	}

	@Test
	public void testDealPay_fail() {
		BzBankPayPO bzBankPayPO = new BzBankPayPO();
		bzBankPayPO.setStatus(TradeBankEnum.INIT.getValue());

		PayV2Request payV2Request = new PayV2Request();

		Mockito.doThrow(new RuntimeException("Error")).when(mockCoreTradeIntegration).payV2(payV2Request);
		when(baseMapper.updateById(bzBankPayPO)).thenReturn(1);

		bzBankPayServiceImplUnderTest.dealPay(bzBankPayPO, payV2Request);

		Assert.assertEquals(TradeBankEnum.FAIL.getValue(), bzBankPayPO.getStatus());
		assertTrue(bzBankPayPO.getReason().contains("Error"));
	}

}
