package com.cfpamf.ms.mallorder.model;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.cfpamf.ms.mallgoods.facade.vo.CombinationChildGoodsVO;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

public class CalculateAmountTest {
    public static void main(String[] args) {
        List<CombinationChildGoodsVO> combinationChildGoodsVOList = new ArrayList<>();
        CombinationChildGoodsVO v1 =  new  CombinationChildGoodsVO();
        //calculateAmount();
    }


    public BigDecimal calculateAmount(BigDecimal totalAmount, List<CombinationChildGoodsVO> combinationChildGoodsVOList, int buyNum, Long childProductId){
        BigDecimal bigDecimal = new BigDecimal(0);
        if (ObjectUtil.isEmpty(totalAmount) || totalAmount.compareTo(BigDecimal.ZERO)==0){
            return bigDecimal;
        }
        if (CollectionUtil.isEmpty(combinationChildGoodsVOList)){
            return bigDecimal;
        }
        //如果只有一个子商品，不用计算分摊
        if (combinationChildGoodsVOList.size() == 1){
            return totalAmount;
        }

        double goodsAmountD = 0d;
        for (CombinationChildGoodsVO vo : combinationChildGoodsVOList) {
            goodsAmountD+=(vo.getCount()*vo.getPrice().doubleValue()*buyNum);
        }
        BigDecimal goodsAmount = new BigDecimal(goodsAmountD);
        Map<Long,BigDecimal> map = new HashMap(combinationChildGoodsVOList.size());
        //按子商品金额倒序
        List<CombinationChildGoodsVO> collect = combinationChildGoodsVOList.stream().sorted(Comparator.comparing(e->new BigDecimal(e.getCount()).multiply(e.getPrice()))).collect(Collectors.toList());
        for (int i = collect.size() -1; i >= 0; i--) {
            if (collect.size() - map.size() == 1){
                //最后一个轧差
                BigDecimal subtract = totalAmount.subtract(map.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add));
                map.put(collect.get(i).getChildProductId(),subtract);
                break;
            }
            double multiply = (collect.get(i).getCount()*(collect.get(i).getPrice().doubleValue())*buyNum/goodsAmount.doubleValue()) * totalAmount.doubleValue();
            BigDecimal decimal = new BigDecimal(multiply) .setScale(2, BigDecimal.ROUND_DOWN);
            if (decimal.compareTo(new BigDecimal(0.01)) <= 0){
                //剩余的金额将全部分摊到小于等于0.01的商品的前一个商品上
                map.put(collect.get(i+1).getChildProductId(),map.get(collect.get(i+1).getChildProductId()).add(decimal));
                break;
            }
            map.put(collect.get(i).getChildProductId(),decimal);
        }

        return map.get(childProductId);
    }

}
