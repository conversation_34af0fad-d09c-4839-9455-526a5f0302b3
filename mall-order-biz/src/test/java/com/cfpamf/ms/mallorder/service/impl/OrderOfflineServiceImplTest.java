package com.cfpamf.ms.mallorder.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cfpamf.common.ms.result.CommonError;
import com.cfpamf.common.ms.result.ErrorContext;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.mallpayment.facade.vo.PaymentNotifyVO;
import com.cfpamf.ms.bizconfig.facade.vo.UserVo;
import com.cfpamf.ms.bms.facade.vo.DictionaryItemVO;
import com.cfpamf.ms.bms.facade.vo.DingTalkUserBmsVO;
import com.cfpamf.ms.bms.facade.vo.DingTalkUserDeptBmsVO;
import com.cfpamf.ms.bms.facade.vo.DingTalkUserRoleBmsVO;
import com.cfpamf.ms.customer.facade.vo.CustBaseInfoVo;
import com.cfpamf.ms.customer.facade.vo.CustDetailVo;
import com.cfpamf.ms.mallorder.common.enums.OrderCreateChannel;
import com.cfpamf.ms.mallorder.common.enums.OrderOfflineSettlementEnum;
import com.cfpamf.ms.mallorder.common.enums.OrderProductDeliveryEnum;
import com.cfpamf.ms.mallorder.dto.*;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.integration.cust.BizConfigIntegration;
import com.cfpamf.ms.mallorder.integration.cust.CustomerIntegration;
import com.cfpamf.ms.mallorder.integration.facade.BmsDingTalkFacade;
import com.cfpamf.ms.mallorder.integration.facade.BmsIntegration;
import com.cfpamf.ms.mallorder.integration.facade.dto.SiteQuery;
import com.cfpamf.ms.mallorder.integration.facade.dto.SiteVo;
import com.cfpamf.ms.mallorder.integration.wms.WmsIntegration;
import com.cfpamf.ms.mallorder.mapper.OrderOfflineMapper;
import com.cfpamf.ms.mallorder.model.OrderLogModel;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.model.OrderPayModel;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.req.OrderDeliveryReq;
import com.cfpamf.ms.mallorder.request.OrderOfflineRequest;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.v2.domain.vo.PayInfoExtraInfoVO;
import com.cfpamf.ms.mallorder.vo.CustInfoVo;
import com.cfpamf.ms.mallorder.vo.OrderOfflineInfoVO;
import com.cfpamf.ms.mallshop.resp.Vendor;
import com.cfpamf.ms.mallsystem.vo.Admin;
import com.slodon.bbc.core.exception.BusinessException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderOfflineServiceImplTest {

    @Mock
    private OrderOfflineMapper mockOrderOfflineMapper;
    @Mock
    private OrderOfflineService mockOrderOfflineService;
    @Mock
    private IOrderOfflineExtendService mockOrderOfflineExtendService;
    @Mock
    private OrderPayModel mockOrderPayModel;
    @Mock
    private IOrderService mockOrderService;
    @Mock
    private IOrderPayService mockOrderPayService;
    @Mock
    private IOrderExtendService mockOrderExtendService;
    @Mock
    private OrderCreateHelper mockOrderCreateHelper;
    @Mock
    private OrderLogModel mockOrderLogModel;
    @Mock
    private OrderModel mockOrderModel;
    @Mock
    private BizConfigIntegration mockBizConfigIntegration;
    @Mock
    private CustomerIntegration mockCustomerIntegration;
    @Mock
    private IOrderProductService mockOrderProductService;
    @Mock
    private WmsIntegration mockWmsIntegration;
    @Mock
    private BmsDingTalkFacade mockBmsDingTalkFacade;
    @Mock
    private BmsIntegration mockBmsIntegration;

    @InjectMocks
    private OrderOfflineServiceImpl orderOfflineServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        ReflectionTestUtils.setField(orderOfflineServiceImplUnderTest, "orderOfflineDeliveryName", "deliverName");
        ReflectionTestUtils.setField(orderOfflineServiceImplUnderTest, "orderOfflineDeliveryMobile", "deliverMobile");
        ReflectionTestUtils.setField(orderOfflineServiceImplUnderTest, "profilesActive", "profilesActive");
    }

    @Test
    void testQueryOrderOfflineList() {
        // Setup
        final OrderOfflinePO orderOfflinePO = new OrderOfflinePO();
        orderOfflinePO.setId(0L);
        orderOfflinePO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflinePO.setEnabledFlag(0);
        orderOfflinePO.setPaySn("paySn");
        orderOfflinePO.setCreateBy("createBy");
        orderOfflinePO.setUpdateBy("updateBy");
        final List<OrderOfflinePO> expectedResult = Arrays.asList(orderOfflinePO);

        // Configure OrderOfflineMapper.selectList(...).
        final OrderOfflinePO orderOfflinePO1 = new OrderOfflinePO();
        orderOfflinePO1.setId(0L);
        orderOfflinePO1.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflinePO1.setEnabledFlag(0);
        orderOfflinePO1.setPaySn("paySn");
        orderOfflinePO1.setCreateBy("createBy");
        orderOfflinePO1.setUpdateBy("updateBy");
        final List<OrderOfflinePO> orderOfflinePOS = Arrays.asList(orderOfflinePO1);
        when(mockOrderOfflineMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderOfflinePOS);

        // Run the test
        final List<OrderOfflinePO> result = orderOfflineServiceImplUnderTest.queryOrderOfflineList("paySn");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testQueryOrderOfflineList_OrderOfflineMapperReturnsNoItems() {
        // Setup
        when(mockOrderOfflineMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<OrderOfflinePO> result = orderOfflineServiceImplUnderTest.queryOrderOfflineList("paySn");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testQueryCustInfo() {
        // Setup
        final CustInfoVo expectedResult = new CustInfoVo();
        expectedResult.setUserCode("userCode");
        expectedResult.setUserName("managerName");
        expectedResult.setIdNo("idNo");
        expectedResult.setMobile("mobile");
        expectedResult.setBranchCode("branchCode");
        expectedResult.setBranchName("branchName");
        expectedResult.setManager("loanManager");
        expectedResult.setManagerName("managerName");

        // Configure BizConfigIntegration.getDefaultUserByUserCode(...).
        final UserVo userVo = new UserVo();
        userVo.setUserCode("userCode");
        userVo.setUserName("managerName");
        userVo.setBranchCode("branchCode");
        userVo.setBranchName("branchName");
        userVo.setUserIdNo("userIdNo");
        when(mockBizConfigIntegration.getDefaultUserByUserCode("employeeCode")).thenReturn(userVo);

        // Configure CustomerIntegration.baseInfoByIdNo(...).
        final CustBaseInfoVo custBaseInfoVo = new CustBaseInfoVo();
        custBaseInfoVo.setIdNo("idNo");
        custBaseInfoVo.setMobile("mobile");
        final CustDetailVo custDetail = new CustDetailVo();
        custDetail.setLoanBranch("branchCode");
        custDetail.setLoanManager("loanManager");
        custBaseInfoVo.setCustDetail(custDetail);
        when(mockCustomerIntegration.baseInfoByIdNo("userIdNo")).thenReturn(custBaseInfoVo);

        // Configure CustomerIntegration.getUserInfoByUserCode(...).
        final UserVo userVo1 = new UserVo();
        userVo1.setUserCode("userCode");
        userVo1.setUserName("managerName");
        userVo1.setBranchCode("branchCode");
        userVo1.setBranchName("branchName");
        userVo1.setUserIdNo("userIdNo");
        when(mockCustomerIntegration.getUserInfoByUserCode("loanManager", "branchCode")).thenReturn(userVo1);

        // Run the test
        final CustInfoVo result = orderOfflineServiceImplUnderTest.queryCustInfo("employeeCode");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testSaveBatch() {
        // Setup
        final OrderOfflinePO orderOfflinePO = new OrderOfflinePO();
        orderOfflinePO.setId(0L);
        orderOfflinePO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflinePO.setEnabledFlag(0);
        orderOfflinePO.setPaySn("paySn");
        orderOfflinePO.setCreateBy("createBy");
        orderOfflinePO.setUpdateBy("updateBy");
        final List<OrderOfflinePO> list = Arrays.asList(orderOfflinePO);

        // Run the test
        orderOfflineServiceImplUnderTest.saveBatch(list);

        // Verify the results
    }

    @Test
    void testPayOrder() {
        // Setup
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("orderSn");
        orderPO.setPaySn("paySn");
        orderPO.setStoreId(0L);
        orderPO.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setOrderType(0);
        orderPO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPaymentTag(0);

        // Configure IOrderPayService.getByPaySn(...).
        final OrderPayPO orderPayPO = new OrderPayPO();
        orderPayPO.setPayId(0);
        orderPayPO.setPaySn("orderOn");
        orderPayPO.setOrderSn("orderSn");
        orderPayPO.setPayAmount(new BigDecimal("0.00"));
        orderPayPO.setApiPayState("apiPayState");
        when(mockOrderPayService.getByPaySn("paySn")).thenReturn(orderPayPO);

        // Run the test
        orderOfflineServiceImplUnderTest.payOrder(orderPO);

        // Verify the results
        // Confirm OrderPayModel.wxAlipayCallBack(...).
        final PaymentNotifyVO req = new PaymentNotifyVO();
        req.setMainOrderNo("mainOrderNo");
        req.setOrderOn("orderOn");
        req.setRelPayAmt(new BigDecimal("0.00"));
        req.setPayStatus(0);
        req.setPayWay("value");
        verify(mockOrderPayModel).wxAlipayCallBack(req,
                new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"));
    }

    @Test
    void testDeliveryOrder() {
        // Setup
        final Vendor vendor = new Vendor();
        vendor.setVendorId(0L);
        vendor.setVendorName("vendorName");
        vendor.setVendorPassword("vendorPassword");
        vendor.setVendorMobile("vendorMobile");
        vendor.setVendorEmail("vendorEmail");

        // Configure IOrderService.getByOrderSn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("orderSn");
        orderPO.setPaySn("paySn");
        orderPO.setStoreId(0L);
        orderPO.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setOrderType(0);
        orderPO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPaymentTag(0);
        when(mockOrderService.getByOrderSn("orderSn")).thenReturn(orderPO);

        // Configure IOrderProductService.list(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setProductId(0L);
        orderProductPO.setDeliveryState(OrderProductDeliveryEnum.WAIT_DELIVERY);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO);
        when(mockOrderProductService.list(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Run the test
        orderOfflineServiceImplUnderTest.deliveryOrder("orderSn", vendor, "deliveryWarehouse", "deliveryWarehouseName");

        // Verify the results
        // Confirm IOrderService.deliveryV2(...).
        final Vendor vendor1 = new Vendor();
        vendor1.setVendorId(0L);
        vendor1.setVendorName("vendorName");
        vendor1.setVendorPassword("vendorPassword");
        vendor1.setVendorMobile("vendorMobile");
        vendor1.setVendorEmail("vendorEmail");
        verify(mockOrderService).deliveryV2(OrderDeliveryReq.builder()
                .orderSn("orderSn")
                .orderProductIds(Arrays.asList(0L))
                .productIds(Arrays.asList(0L))
                .deliverType(0)
                .deliverName("deliverName")
                .deliverMobile("deliverMobile")
                .allowNoLogistics(false)
                .channel(OrderCreateChannel.H5)
                .isOffLineOrder(false)
                .deliveryWarehouse("deliveryWarehouse")
                .deliveryWarehouseName("deliveryWarehouseName")
                .build(), vendor1);
    }

    @Test
    void testDeliveryOrder_IOrderProductServiceReturnsNoItems() {
        // Setup
        final Vendor vendor = new Vendor();
        vendor.setVendorId(0L);
        vendor.setVendorName("vendorName");
        vendor.setVendorPassword("vendorPassword");
        vendor.setVendorMobile("vendorMobile");
        vendor.setVendorEmail("vendorEmail");

        // Configure IOrderService.getByOrderSn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("orderSn");
        orderPO.setPaySn("paySn");
        orderPO.setStoreId(0L);
        orderPO.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setOrderType(0);
        orderPO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPaymentTag(0);
        when(mockOrderService.getByOrderSn("orderSn")).thenReturn(orderPO);

        when(mockOrderProductService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        orderOfflineServiceImplUnderTest.deliveryOrder("orderSn", vendor, "deliveryWarehouse", "deliveryWarehouseName");

        // Verify the results
        // Confirm IOrderService.deliveryV2(...).
        final Vendor vendor1 = new Vendor();
        vendor1.setVendorId(0L);
        vendor1.setVendorName("vendorName");
        vendor1.setVendorPassword("vendorPassword");
        vendor1.setVendorMobile("vendorMobile");
        vendor1.setVendorEmail("vendorEmail");
        verify(mockOrderService).deliveryV2(OrderDeliveryReq.builder()
                .orderSn("orderSn")
                .orderProductIds(Arrays.asList(0L))
                .productIds(Arrays.asList(0L))
                .deliverType(0)
                .deliverName("deliverName")
                .deliverMobile("deliverMobile")
                .allowNoLogistics(false)
                .channel(OrderCreateChannel.H5)
                .isOffLineOrder(false)
                .deliveryWarehouse("deliveryWarehouse")
                .deliveryWarehouseName("deliveryWarehouseName")
                .build(), vendor1);
    }

    @Test
    void testManualSettlement() {
        // Setup
        final Vendor vendor = new Vendor();
        vendor.setVendorId(0L);
        vendor.setVendorName("vendorName");
        vendor.setVendorPassword("vendorPassword");
        vendor.setVendorMobile("vendorMobile");
        vendor.setVendorEmail("vendorEmail");

        final OrderOfflineManualSettlementDTO orderOfflineManualSettlement = new OrderOfflineManualSettlementDTO();
        orderOfflineManualSettlement.setPaySn("paySn");
        orderOfflineManualSettlement.setOrderOfflineSettlement(OrderOfflineSettlementEnum.NO_PAYMENT);
        orderOfflineManualSettlement.setOrderType(OrderTypeEnum.NORMAL);
        orderOfflineManualSettlement.setRemark("remark");
        final OrderOfflineDTO orderOfflineDTO = new OrderOfflineDTO();
        orderOfflineDTO.setId(0L);
        orderOfflineDTO.setReceiptAccount("receiptAccount");
        orderOfflineDTO.setReceiptAmount(new BigDecimal("0.00"));
        orderOfflineDTO.setReceiptTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflineDTO.setEnabledFlag(0);
        orderOfflineManualSettlement.setOrderOfflineList(Arrays.asList(orderOfflineDTO));

        // Configure IOrderService.listByPaySn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("orderSn");
        orderPO.setPaySn("paySn");
        orderPO.setStoreId(0L);
        orderPO.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setOrderType(0);
        orderPO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPaymentTag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO);
        when(mockOrderService.listByPaySn("paySn")).thenReturn(orderPOList);

        // Configure IOrderPayService.getByPaySn(...).
        final OrderPayPO orderPayPO = new OrderPayPO();
        orderPayPO.setPayId(0);
        orderPayPO.setPaySn("orderOn");
        orderPayPO.setOrderSn("orderSn");
        orderPayPO.setPayAmount(new BigDecimal("0.00"));
        orderPayPO.setApiPayState("apiPayState");
        when(mockOrderPayService.getByPaySn("paySn")).thenReturn(orderPayPO);

        // Configure IOrderService.update(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("orderSn");
        entity.setPaySn("paySn");
        entity.setStoreId(0L);
        entity.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setOrderType(0);
        entity.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPaymentTag(0);
        when(mockOrderService.update(eq(entity), any(LambdaUpdateWrapper.class))).thenReturn(false);

        // Run the test
        orderOfflineServiceImplUnderTest.manualSettlement(vendor, orderOfflineManualSettlement);

        // Verify the results
        // Confirm OrderPayModel.wxAlipayCallBack(...).
        final PaymentNotifyVO req = new PaymentNotifyVO();
        req.setMainOrderNo("mainOrderNo");
        req.setOrderOn("orderOn");
        req.setRelPayAmt(new BigDecimal("0.00"));
        req.setPayStatus(0);
        req.setPayWay("value");
        verify(mockOrderPayModel).wxAlipayCallBack(req,
                new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"));
        verify(mockOrderExtendService).updateOrderRemark(new HashSet<>(Arrays.asList("value")), "remark");
        verify(mockOrderLogModel).insertOrderLog(2, 0L, "vendorName", "orderSn", 0, 0, 0, "商家手动结算订单",
                OrderCreateChannel.SELLER_WEB);
        verify(mockOrderCreateHelper).sendMqEventMessageByTransactionCommit(
                new OrderOperationEventNotifyDTO("code", "desc", "orderSn"), "exchang");
    }

    @Test
    void testManualSettlement_IOrderServiceListByPaySnReturnsNoItems() {
        // Setup
        final Vendor vendor = new Vendor();
        vendor.setVendorId(0L);
        vendor.setVendorName("vendorName");
        vendor.setVendorPassword("vendorPassword");
        vendor.setVendorMobile("vendorMobile");
        vendor.setVendorEmail("vendorEmail");

        final OrderOfflineManualSettlementDTO orderOfflineManualSettlement = new OrderOfflineManualSettlementDTO();
        orderOfflineManualSettlement.setPaySn("paySn");
        orderOfflineManualSettlement.setOrderOfflineSettlement(OrderOfflineSettlementEnum.NO_PAYMENT);
        orderOfflineManualSettlement.setOrderType(OrderTypeEnum.NORMAL);
        orderOfflineManualSettlement.setRemark("remark");
        final OrderOfflineDTO orderOfflineDTO = new OrderOfflineDTO();
        orderOfflineDTO.setId(0L);
        orderOfflineDTO.setReceiptAccount("receiptAccount");
        orderOfflineDTO.setReceiptAmount(new BigDecimal("0.00"));
        orderOfflineDTO.setReceiptTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflineDTO.setEnabledFlag(0);
        orderOfflineManualSettlement.setOrderOfflineList(Arrays.asList(orderOfflineDTO));

        when(mockOrderService.listByPaySn("paySn")).thenReturn(Collections.emptyList());

        // Configure IOrderPayService.getByPaySn(...).
        final OrderPayPO orderPayPO = new OrderPayPO();
        orderPayPO.setPayId(0);
        orderPayPO.setPaySn("orderOn");
        orderPayPO.setOrderSn("orderSn");
        orderPayPO.setPayAmount(new BigDecimal("0.00"));
        orderPayPO.setApiPayState("apiPayState");
        when(mockOrderPayService.getByPaySn("paySn")).thenReturn(orderPayPO);

        // Configure IOrderService.update(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("orderSn");
        entity.setPaySn("paySn");
        entity.setStoreId(0L);
        entity.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setOrderType(0);
        entity.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPaymentTag(0);
        when(mockOrderService.update(eq(entity), any(LambdaUpdateWrapper.class))).thenReturn(false);

        // Run the test
        orderOfflineServiceImplUnderTest.manualSettlement(vendor, orderOfflineManualSettlement);

        // Verify the results
        // Confirm OrderPayModel.wxAlipayCallBack(...).
        final PaymentNotifyVO req = new PaymentNotifyVO();
        req.setMainOrderNo("mainOrderNo");
        req.setOrderOn("orderOn");
        req.setRelPayAmt(new BigDecimal("0.00"));
        req.setPayStatus(0);
        req.setPayWay("value");
        verify(mockOrderPayModel).wxAlipayCallBack(req,
                new PayInfoExtraInfoVO("payMethod", "payNo", "paySn", "orderSn"));
        verify(mockOrderExtendService).updateOrderRemark(new HashSet<>(Arrays.asList("value")), "remark");
        verify(mockOrderLogModel).insertOrderLog(2, 0L, "vendorName", "orderSn", 0, 0, 0, "商家手动结算订单",
                OrderCreateChannel.SELLER_WEB);
        verify(mockOrderCreateHelper).sendMqEventMessageByTransactionCommit(
                new OrderOperationEventNotifyDTO("code", "desc", "orderSn"), "exchang");
    }

    @Test
    void testManualSettlementV2() {
        // Setup
        final Vendor vendor = new Vendor();
        vendor.setVendorId(0L);
        vendor.setVendorName("vendorName");
        vendor.setVendorPassword("vendorPassword");
        vendor.setVendorMobile("vendorMobile");
        vendor.setVendorEmail("vendorEmail");

        final OrderOfflineManualSettlementDTO orderOfflineManualSettlement = new OrderOfflineManualSettlementDTO();
        orderOfflineManualSettlement.setPaySn("paySn");
        orderOfflineManualSettlement.setOrderOfflineSettlement(OrderOfflineSettlementEnum.NO_PAYMENT);
        orderOfflineManualSettlement.setOrderType(OrderTypeEnum.NORMAL);
        orderOfflineManualSettlement.setRemark("remark");
        final OrderOfflineDTO orderOfflineDTO = new OrderOfflineDTO();
        orderOfflineDTO.setId(0L);
        orderOfflineDTO.setReceiptAccount("receiptAccount");
        orderOfflineDTO.setReceiptAmount(new BigDecimal("0.00"));
        orderOfflineDTO.setReceiptTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflineDTO.setEnabledFlag(0);
        orderOfflineManualSettlement.setOrderOfflineList(Arrays.asList(orderOfflineDTO));

        // Configure IOrderService.listByPaySn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("orderSn");
        orderPO.setPaySn("paySn");
        orderPO.setStoreId(0L);
        orderPO.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setOrderType(0);
        orderPO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPaymentTag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO);
        when(mockOrderService.listByPaySn("paySn")).thenReturn(orderPOList);

        // Configure IOrderService.update(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("orderSn");
        entity.setPaySn("paySn");
        entity.setStoreId(0L);
        entity.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setOrderType(0);
        entity.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPaymentTag(0);
        when(mockOrderService.update(eq(entity), any(LambdaUpdateWrapper.class))).thenReturn(false);

        // Run the test
        orderOfflineServiceImplUnderTest.manualSettlementV2(vendor, orderOfflineManualSettlement);

        // Verify the results
        verify(mockOrderExtendService).updateOrderRemark(new HashSet<>(Arrays.asList("value")), "remark");
        verify(mockOrderLogModel).insertOrderLog(2, 0L, "vendorName", "orderSn", 0, 0, 0, "商家手动结算订单",
                OrderCreateChannel.SELLER_WEB);
        verify(mockOrderCreateHelper).sendMqEventMessageByTransactionCommit(
                new OrderOperationEventNotifyDTO("code", "desc", "orderSn"), "exchang");
    }

    @Test
    void testManualSettlementV2_IOrderServiceListByPaySnReturnsNoItems() {
        // Setup
        final Vendor vendor = new Vendor();
        vendor.setVendorId(0L);
        vendor.setVendorName("vendorName");
        vendor.setVendorPassword("vendorPassword");
        vendor.setVendorMobile("vendorMobile");
        vendor.setVendorEmail("vendorEmail");

        final OrderOfflineManualSettlementDTO orderOfflineManualSettlement = new OrderOfflineManualSettlementDTO();
        orderOfflineManualSettlement.setPaySn("paySn");
        orderOfflineManualSettlement.setOrderOfflineSettlement(OrderOfflineSettlementEnum.NO_PAYMENT);
        orderOfflineManualSettlement.setOrderType(OrderTypeEnum.NORMAL);
        orderOfflineManualSettlement.setRemark("remark");
        final OrderOfflineDTO orderOfflineDTO = new OrderOfflineDTO();
        orderOfflineDTO.setId(0L);
        orderOfflineDTO.setReceiptAccount("receiptAccount");
        orderOfflineDTO.setReceiptAmount(new BigDecimal("0.00"));
        orderOfflineDTO.setReceiptTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflineDTO.setEnabledFlag(0);
        orderOfflineManualSettlement.setOrderOfflineList(Arrays.asList(orderOfflineDTO));

        when(mockOrderService.listByPaySn("paySn")).thenReturn(Collections.emptyList());

        // Configure IOrderService.update(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("orderSn");
        entity.setPaySn("paySn");
        entity.setStoreId(0L);
        entity.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setOrderType(0);
        entity.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPaymentTag(0);
        when(mockOrderService.update(eq(entity), any(LambdaUpdateWrapper.class))).thenReturn(false);

        // Run the test
        orderOfflineServiceImplUnderTest.manualSettlementV2(vendor, orderOfflineManualSettlement);

        // Verify the results
        verify(mockOrderExtendService).updateOrderRemark(new HashSet<>(Arrays.asList("value")), "remark");
        verify(mockOrderLogModel).insertOrderLog(2, 0L, "vendorName", "orderSn", 0, 0, 0, "商家手动结算订单",
                OrderCreateChannel.SELLER_WEB);
        verify(mockOrderCreateHelper).sendMqEventMessageByTransactionCommit(
                new OrderOperationEventNotifyDTO("code", "desc", "orderSn"), "exchang");
    }

    @Test
    void testGetManualInfo() {
        // Setup
        final OrderOfflineManualDTO expectedResult = new OrderOfflineManualDTO();
        expectedResult.setOrderSn("orderSn");
        expectedResult.setOrderType(0);
        expectedResult.setPaymentTag(0);
        expectedResult.setSignInImageUrl("signInImageUrl");
        expectedResult.setCustomerContract("customerContract");
        final OrderOfflineDTO orderOfflineDTO = new OrderOfflineDTO();
        orderOfflineDTO.setId(0L);
        orderOfflineDTO.setReceiptAccount("receiptAccount");
        orderOfflineDTO.setReceiptAmount(new BigDecimal("0.00"));
        orderOfflineDTO.setReceiptTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflineDTO.setEnabledFlag(0);
        expectedResult.setOrderOfflineList(Arrays.asList(orderOfflineDTO));
        expectedResult.setDeliveryWarehouse("deliveryWarehouse");
        expectedResult.setDeliveryWarehouseName("deliveryWarehouseName");
        expectedResult.setInvoiceStatus(0);
        expectedResult.setInvoiceAmount(new BigDecimal("0.00"));
        expectedResult.setInvoiceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expectedResult.setAccountPeriodDays(0);
        expectedResult.setOverdueTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure IOrderService.getByOrderSn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("orderSn");
        orderPO.setPaySn("paySn");
        orderPO.setStoreId(0L);
        orderPO.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setOrderType(0);
        orderPO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPaymentTag(0);
        when(mockOrderService.getByOrderSn("orderSn")).thenReturn(orderPO);

        // Configure IOrderExtendService.getOrderExtendByOrderSn(...).
        final OrderExtendPO orderExtendPO = new OrderExtendPO();
        orderExtendPO.setOrderSn("orderSn");
        orderExtendPO.setInvoiceStatus(0);
        orderExtendPO.setInvoiceAmount(new BigDecimal("0.00"));
        orderExtendPO.setInvoiceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderExtendPO.setSignInImageUrl("signInImageUrl");
        orderExtendPO.setCustomerContract("customerContract");
        when(mockOrderExtendService.getOrderExtendByOrderSn("orderSn")).thenReturn(orderExtendPO);

        // Configure OrderOfflineMapper.selectList(...).
        final OrderOfflinePO orderOfflinePO = new OrderOfflinePO();
        orderOfflinePO.setId(0L);
        orderOfflinePO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflinePO.setEnabledFlag(0);
        orderOfflinePO.setPaySn("paySn");
        orderOfflinePO.setCreateBy("createBy");
        orderOfflinePO.setUpdateBy("updateBy");
        final List<OrderOfflinePO> orderOfflinePOS = Arrays.asList(orderOfflinePO);
        when(mockOrderOfflineMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderOfflinePOS);

        // Configure IOrderOfflineExtendService.getOrderOfflineExtendByOrderSn(...).
        final OrderOfflineExtendPO orderOfflineExtendPO = new OrderOfflineExtendPO();
        orderOfflineExtendPO.setId(0L);
        orderOfflineExtendPO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflineExtendPO.setEnabledFlag(0);
        orderOfflineExtendPO.setOrderSn("orderSn");
        orderOfflineExtendPO.setOverdueTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflineExtendPO.setOverdueFlag(0);
        orderOfflineExtendPO.setOverdueDays(0);
        orderOfflineExtendPO.setAccountPeriodDays(0);
        when(mockOrderOfflineExtendService.getOrderOfflineExtendByOrderSn("orderSn")).thenReturn(orderOfflineExtendPO);

        // Run the test
        final OrderOfflineManualDTO result = orderOfflineServiceImplUnderTest.getManualInfo("orderSn");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetManualInfo_OrderOfflineMapperReturnsNoItems() {
        // Setup
        final OrderOfflineManualDTO expectedResult = new OrderOfflineManualDTO();
        expectedResult.setOrderSn("orderSn");
        expectedResult.setOrderType(0);
        expectedResult.setPaymentTag(0);
        expectedResult.setSignInImageUrl("signInImageUrl");
        expectedResult.setCustomerContract("customerContract");
        final OrderOfflineDTO orderOfflineDTO = new OrderOfflineDTO();
        orderOfflineDTO.setId(0L);
        orderOfflineDTO.setReceiptAccount("receiptAccount");
        orderOfflineDTO.setReceiptAmount(new BigDecimal("0.00"));
        orderOfflineDTO.setReceiptTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflineDTO.setEnabledFlag(0);
        expectedResult.setOrderOfflineList(Arrays.asList(orderOfflineDTO));
        expectedResult.setDeliveryWarehouse("deliveryWarehouse");
        expectedResult.setDeliveryWarehouseName("deliveryWarehouseName");
        expectedResult.setInvoiceStatus(0);
        expectedResult.setInvoiceAmount(new BigDecimal("0.00"));
        expectedResult.setInvoiceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expectedResult.setAccountPeriodDays(0);
        expectedResult.setOverdueTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure IOrderService.getByOrderSn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("orderSn");
        orderPO.setPaySn("paySn");
        orderPO.setStoreId(0L);
        orderPO.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setOrderType(0);
        orderPO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPaymentTag(0);
        when(mockOrderService.getByOrderSn("orderSn")).thenReturn(orderPO);

        // Configure IOrderExtendService.getOrderExtendByOrderSn(...).
        final OrderExtendPO orderExtendPO = new OrderExtendPO();
        orderExtendPO.setOrderSn("orderSn");
        orderExtendPO.setInvoiceStatus(0);
        orderExtendPO.setInvoiceAmount(new BigDecimal("0.00"));
        orderExtendPO.setInvoiceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderExtendPO.setSignInImageUrl("signInImageUrl");
        orderExtendPO.setCustomerContract("customerContract");
        when(mockOrderExtendService.getOrderExtendByOrderSn("orderSn")).thenReturn(orderExtendPO);

        when(mockOrderOfflineMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure IOrderOfflineExtendService.getOrderOfflineExtendByOrderSn(...).
        final OrderOfflineExtendPO orderOfflineExtendPO = new OrderOfflineExtendPO();
        orderOfflineExtendPO.setId(0L);
        orderOfflineExtendPO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflineExtendPO.setEnabledFlag(0);
        orderOfflineExtendPO.setOrderSn("orderSn");
        orderOfflineExtendPO.setOverdueTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflineExtendPO.setOverdueFlag(0);
        orderOfflineExtendPO.setOverdueDays(0);
        orderOfflineExtendPO.setAccountPeriodDays(0);
        when(mockOrderOfflineExtendService.getOrderOfflineExtendByOrderSn("orderSn")).thenReturn(orderOfflineExtendPO);

        // Run the test
        final OrderOfflineManualDTO result = orderOfflineServiceImplUnderTest.getManualInfo("orderSn");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testManualPay() {
        // Setup
        final OrderOfflineManualDTO orderOfflineManualDTO = new OrderOfflineManualDTO();
        orderOfflineManualDTO.setOrderSn("orderSn");
        orderOfflineManualDTO.setOrderType(0);
        orderOfflineManualDTO.setPaymentTag(0);
        orderOfflineManualDTO.setSignInImageUrl("signInImageUrl");
        orderOfflineManualDTO.setCustomerContract("customerContract");
        final OrderOfflineDTO orderOfflineDTO = new OrderOfflineDTO();
        orderOfflineDTO.setId(0L);
        orderOfflineDTO.setReceiptAccount("receiptAccount");
        orderOfflineDTO.setReceiptAmount(new BigDecimal("0.00"));
        orderOfflineDTO.setReceiptTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflineDTO.setEnabledFlag(0);
        orderOfflineManualDTO.setOrderOfflineList(Arrays.asList(orderOfflineDTO));
        orderOfflineManualDTO.setDeliveryWarehouse("deliveryWarehouse");
        orderOfflineManualDTO.setDeliveryWarehouseName("deliveryWarehouseName");
        orderOfflineManualDTO.setInvoiceStatus(0);
        orderOfflineManualDTO.setInvoiceAmount(new BigDecimal("0.00"));
        orderOfflineManualDTO.setInvoiceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflineManualDTO.setAccountPeriodDays(0);
        orderOfflineManualDTO.setOverdueTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        final Vendor vendor = new Vendor();
        vendor.setVendorId(0L);
        vendor.setVendorName("vendorName");
        vendor.setVendorPassword("vendorPassword");
        vendor.setVendorMobile("vendorMobile");
        vendor.setVendorEmail("vendorEmail");

        // Configure IOrderService.getByOrderSn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("orderSn");
        orderPO.setPaySn("paySn");
        orderPO.setStoreId(0L);
        orderPO.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setOrderType(0);
        orderPO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPaymentTag(0);
        when(mockOrderService.getByOrderSn("orderSn")).thenReturn(orderPO);

        // Configure IOrderService.listByPaySn(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("orderSn");
        orderPO1.setPaySn("paySn");
        orderPO1.setStoreId(0L);
        orderPO1.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setOrderType(0);
        orderPO1.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPaymentTag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO1);
        when(mockOrderService.listByPaySn("paySn")).thenReturn(orderPOList);

        // Run the test
        orderOfflineServiceImplUnderTest.manualPay(orderOfflineManualDTO, vendor);

        // Verify the results
        // Confirm OrderOfflineService.saveOrUpdateBatch(...).
        final OrderOfflinePO orderOfflinePO = new OrderOfflinePO();
        orderOfflinePO.setId(0L);
        orderOfflinePO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflinePO.setEnabledFlag(0);
        orderOfflinePO.setPaySn("paySn");
        orderOfflinePO.setCreateBy("createBy");
        orderOfflinePO.setUpdateBy("updateBy");
        final List<OrderOfflinePO> entityList = Arrays.asList(orderOfflinePO);
        verify(mockOrderOfflineService).saveOrUpdateBatch(entityList);
        verify(mockOrderOfflineService).removeByIds(Arrays.asList("value"));
        verify(mockOrderService).update(any(LambdaUpdateWrapper.class));
        verify(mockOrderLogModel).insertOrderLog(2, 0L, "vendorName", "orderSn", 0, 20, 0, "线下补录订单手动支付",
                OrderCreateChannel.H5);

        // Confirm OrderOfflineService.payOrder(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("orderSn");
        orderPO2.setPaySn("paySn");
        orderPO2.setStoreId(0L);
        orderPO2.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setOrderType(0);
        orderPO2.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPaymentTag(0);
        verify(mockOrderOfflineService).payOrder(orderPO2);
    }

    @Test
    void testManualPay_IOrderServiceListByPaySnReturnsNoItems() {
        // Setup
        final OrderOfflineManualDTO orderOfflineManualDTO = new OrderOfflineManualDTO();
        orderOfflineManualDTO.setOrderSn("orderSn");
        orderOfflineManualDTO.setOrderType(0);
        orderOfflineManualDTO.setPaymentTag(0);
        orderOfflineManualDTO.setSignInImageUrl("signInImageUrl");
        orderOfflineManualDTO.setCustomerContract("customerContract");
        final OrderOfflineDTO orderOfflineDTO = new OrderOfflineDTO();
        orderOfflineDTO.setId(0L);
        orderOfflineDTO.setReceiptAccount("receiptAccount");
        orderOfflineDTO.setReceiptAmount(new BigDecimal("0.00"));
        orderOfflineDTO.setReceiptTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflineDTO.setEnabledFlag(0);
        orderOfflineManualDTO.setOrderOfflineList(Arrays.asList(orderOfflineDTO));
        orderOfflineManualDTO.setDeliveryWarehouse("deliveryWarehouse");
        orderOfflineManualDTO.setDeliveryWarehouseName("deliveryWarehouseName");
        orderOfflineManualDTO.setInvoiceStatus(0);
        orderOfflineManualDTO.setInvoiceAmount(new BigDecimal("0.00"));
        orderOfflineManualDTO.setInvoiceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflineManualDTO.setAccountPeriodDays(0);
        orderOfflineManualDTO.setOverdueTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        final Vendor vendor = new Vendor();
        vendor.setVendorId(0L);
        vendor.setVendorName("vendorName");
        vendor.setVendorPassword("vendorPassword");
        vendor.setVendorMobile("vendorMobile");
        vendor.setVendorEmail("vendorEmail");

        // Configure IOrderService.getByOrderSn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("orderSn");
        orderPO.setPaySn("paySn");
        orderPO.setStoreId(0L);
        orderPO.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setOrderType(0);
        orderPO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPaymentTag(0);
        when(mockOrderService.getByOrderSn("orderSn")).thenReturn(orderPO);

        when(mockOrderService.listByPaySn("paySn")).thenReturn(Collections.emptyList());

        // Run the test
        orderOfflineServiceImplUnderTest.manualPay(orderOfflineManualDTO, vendor);

        // Verify the results
        // Confirm OrderOfflineService.saveOrUpdateBatch(...).
        final OrderOfflinePO orderOfflinePO = new OrderOfflinePO();
        orderOfflinePO.setId(0L);
        orderOfflinePO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflinePO.setEnabledFlag(0);
        orderOfflinePO.setPaySn("paySn");
        orderOfflinePO.setCreateBy("createBy");
        orderOfflinePO.setUpdateBy("updateBy");
        final List<OrderOfflinePO> entityList = Arrays.asList(orderOfflinePO);
        verify(mockOrderOfflineService).saveOrUpdateBatch(entityList);
        verify(mockOrderOfflineService).removeByIds(Arrays.asList("value"));
        verify(mockOrderService).update(any(LambdaUpdateWrapper.class));
        verify(mockOrderLogModel).insertOrderLog(2, 0L, "vendorName", "orderSn", 0, 20, 0, "线下补录订单手动支付",
                OrderCreateChannel.H5);

        // Confirm OrderOfflineService.payOrder(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("orderSn");
        orderPO1.setPaySn("paySn");
        orderPO1.setStoreId(0L);
        orderPO1.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setOrderType(0);
        orderPO1.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPaymentTag(0);
        verify(mockOrderOfflineService).payOrder(orderPO1);
    }

    @Test
    void testManualPayAdmin() {
        // Setup
        final OrderOfflineManualDTO orderOfflineManualDTO = new OrderOfflineManualDTO();
        orderOfflineManualDTO.setOrderSn("orderSn");
        orderOfflineManualDTO.setOrderType(0);
        orderOfflineManualDTO.setPaymentTag(0);
        orderOfflineManualDTO.setSignInImageUrl("signInImageUrl");
        orderOfflineManualDTO.setCustomerContract("customerContract");
        final OrderOfflineDTO orderOfflineDTO = new OrderOfflineDTO();
        orderOfflineDTO.setId(0L);
        orderOfflineDTO.setReceiptAccount("receiptAccount");
        orderOfflineDTO.setReceiptAmount(new BigDecimal("0.00"));
        orderOfflineDTO.setReceiptTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflineDTO.setEnabledFlag(0);
        orderOfflineManualDTO.setOrderOfflineList(Arrays.asList(orderOfflineDTO));
        orderOfflineManualDTO.setDeliveryWarehouse("deliveryWarehouse");
        orderOfflineManualDTO.setDeliveryWarehouseName("deliveryWarehouseName");
        orderOfflineManualDTO.setInvoiceStatus(0);
        orderOfflineManualDTO.setInvoiceAmount(new BigDecimal("0.00"));
        orderOfflineManualDTO.setInvoiceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflineManualDTO.setAccountPeriodDays(0);
        orderOfflineManualDTO.setOverdueTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        final Admin admin = new Admin();
        admin.setAdminId(0);
        admin.setAdminName("adminName");
        admin.setPhone("phone");
        admin.setEmail("email");
        admin.setEmployeeNumber("employeeNumber");

        // Configure IOrderService.getByOrderSn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("orderSn");
        orderPO.setPaySn("paySn");
        orderPO.setStoreId(0L);
        orderPO.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setOrderType(0);
        orderPO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPaymentTag(0);
        when(mockOrderService.getByOrderSn("orderSn")).thenReturn(orderPO);

        // Configure IOrderService.listByPaySn(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("orderSn");
        orderPO1.setPaySn("paySn");
        orderPO1.setStoreId(0L);
        orderPO1.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setOrderType(0);
        orderPO1.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPaymentTag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO1);
        when(mockOrderService.listByPaySn("paySn")).thenReturn(orderPOList);

        // Run the test
        orderOfflineServiceImplUnderTest.manualPayAdmin(orderOfflineManualDTO, admin);

        // Verify the results
        // Confirm OrderOfflineService.saveOrUpdateBatch(...).
        final OrderOfflinePO orderOfflinePO = new OrderOfflinePO();
        orderOfflinePO.setId(0L);
        orderOfflinePO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflinePO.setEnabledFlag(0);
        orderOfflinePO.setPaySn("paySn");
        orderOfflinePO.setCreateBy("createBy");
        orderOfflinePO.setUpdateBy("updateBy");
        final List<OrderOfflinePO> entityList = Arrays.asList(orderOfflinePO);
        verify(mockOrderOfflineService).saveOrUpdateBatch(entityList);
        verify(mockOrderOfflineService).removeByIds(Arrays.asList("value"));
        verify(mockOrderService).update(any(LambdaUpdateWrapper.class));
        verify(mockOrderLogModel).insertOrderLog(2, 0L, "adminName", "orderSn", 0, 20, 0, "线下补录订单运营端手动支付",
                OrderCreateChannel.H5);

        // Confirm OrderOfflineService.payOrder(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("orderSn");
        orderPO2.setPaySn("paySn");
        orderPO2.setStoreId(0L);
        orderPO2.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setOrderType(0);
        orderPO2.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPaymentTag(0);
        verify(mockOrderOfflineService).payOrder(orderPO2);
    }

    @Test
    void testManualPayAdmin_IOrderServiceListByPaySnReturnsNoItems() {
        // Setup
        final OrderOfflineManualDTO orderOfflineManualDTO = new OrderOfflineManualDTO();
        orderOfflineManualDTO.setOrderSn("orderSn");
        orderOfflineManualDTO.setOrderType(0);
        orderOfflineManualDTO.setPaymentTag(0);
        orderOfflineManualDTO.setSignInImageUrl("signInImageUrl");
        orderOfflineManualDTO.setCustomerContract("customerContract");
        final OrderOfflineDTO orderOfflineDTO = new OrderOfflineDTO();
        orderOfflineDTO.setId(0L);
        orderOfflineDTO.setReceiptAccount("receiptAccount");
        orderOfflineDTO.setReceiptAmount(new BigDecimal("0.00"));
        orderOfflineDTO.setReceiptTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflineDTO.setEnabledFlag(0);
        orderOfflineManualDTO.setOrderOfflineList(Arrays.asList(orderOfflineDTO));
        orderOfflineManualDTO.setDeliveryWarehouse("deliveryWarehouse");
        orderOfflineManualDTO.setDeliveryWarehouseName("deliveryWarehouseName");
        orderOfflineManualDTO.setInvoiceStatus(0);
        orderOfflineManualDTO.setInvoiceAmount(new BigDecimal("0.00"));
        orderOfflineManualDTO.setInvoiceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflineManualDTO.setAccountPeriodDays(0);
        orderOfflineManualDTO.setOverdueTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        final Admin admin = new Admin();
        admin.setAdminId(0);
        admin.setAdminName("adminName");
        admin.setPhone("phone");
        admin.setEmail("email");
        admin.setEmployeeNumber("employeeNumber");

        // Configure IOrderService.getByOrderSn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("orderSn");
        orderPO.setPaySn("paySn");
        orderPO.setStoreId(0L);
        orderPO.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setOrderType(0);
        orderPO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPaymentTag(0);
        when(mockOrderService.getByOrderSn("orderSn")).thenReturn(orderPO);

        when(mockOrderService.listByPaySn("paySn")).thenReturn(Collections.emptyList());

        // Run the test
        orderOfflineServiceImplUnderTest.manualPayAdmin(orderOfflineManualDTO, admin);

        // Verify the results
        // Confirm OrderOfflineService.saveOrUpdateBatch(...).
        final OrderOfflinePO orderOfflinePO = new OrderOfflinePO();
        orderOfflinePO.setId(0L);
        orderOfflinePO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflinePO.setEnabledFlag(0);
        orderOfflinePO.setPaySn("paySn");
        orderOfflinePO.setCreateBy("createBy");
        orderOfflinePO.setUpdateBy("updateBy");
        final List<OrderOfflinePO> entityList = Arrays.asList(orderOfflinePO);
        verify(mockOrderOfflineService).saveOrUpdateBatch(entityList);
        verify(mockOrderOfflineService).removeByIds(Arrays.asList("value"));
        verify(mockOrderService).update(any(LambdaUpdateWrapper.class));
        verify(mockOrderLogModel).insertOrderLog(2, 0L, "adminName", "orderSn", 0, 20, 0, "线下补录订单运营端手动支付",
                OrderCreateChannel.H5);

        // Confirm OrderOfflineService.payOrder(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("orderSn");
        orderPO1.setPaySn("paySn");
        orderPO1.setStoreId(0L);
        orderPO1.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setOrderType(0);
        orderPO1.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPaymentTag(0);
        verify(mockOrderOfflineService).payOrder(orderPO1);
    }

    @Test
    void testManualDelivery() {
        // Setup
        final OrderOfflineManualDTO orderOfflineManualDTO = new OrderOfflineManualDTO();
        orderOfflineManualDTO.setOrderSn("orderSn");
        orderOfflineManualDTO.setOrderType(0);
        orderOfflineManualDTO.setPaymentTag(0);
        orderOfflineManualDTO.setSignInImageUrl("signInImageUrl");
        orderOfflineManualDTO.setCustomerContract("customerContract");
        final OrderOfflineDTO orderOfflineDTO = new OrderOfflineDTO();
        orderOfflineDTO.setId(0L);
        orderOfflineDTO.setReceiptAccount("receiptAccount");
        orderOfflineDTO.setReceiptAmount(new BigDecimal("0.00"));
        orderOfflineDTO.setReceiptTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflineDTO.setEnabledFlag(0);
        orderOfflineManualDTO.setOrderOfflineList(Arrays.asList(orderOfflineDTO));
        orderOfflineManualDTO.setDeliveryWarehouse("deliveryWarehouse");
        orderOfflineManualDTO.setDeliveryWarehouseName("deliveryWarehouseName");
        orderOfflineManualDTO.setInvoiceStatus(0);
        orderOfflineManualDTO.setInvoiceAmount(new BigDecimal("0.00"));
        orderOfflineManualDTO.setInvoiceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflineManualDTO.setAccountPeriodDays(0);
        orderOfflineManualDTO.setOverdueTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        final Vendor vendor = new Vendor();
        vendor.setVendorId(0L);
        vendor.setVendorName("vendorName");
        vendor.setVendorPassword("vendorPassword");
        vendor.setVendorMobile("vendorMobile");
        vendor.setVendorEmail("vendorEmail");

        // Configure IOrderService.getByOrderSn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("orderSn");
        orderPO.setPaySn("paySn");
        orderPO.setStoreId(0L);
        orderPO.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setOrderType(0);
        orderPO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPaymentTag(0);
        when(mockOrderService.getByOrderSn("orderSn")).thenReturn(orderPO);

        // Run the test
        orderOfflineServiceImplUnderTest.manualDelivery(orderOfflineManualDTO, vendor);

        // Verify the results
        // Confirm OrderOfflineService.saveOrUpdateBatch(...).
        final OrderOfflinePO orderOfflinePO = new OrderOfflinePO();
        orderOfflinePO.setId(0L);
        orderOfflinePO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflinePO.setEnabledFlag(0);
        orderOfflinePO.setPaySn("paySn");
        orderOfflinePO.setCreateBy("createBy");
        orderOfflinePO.setUpdateBy("updateBy");
        final List<OrderOfflinePO> entityList = Arrays.asList(orderOfflinePO);
        verify(mockOrderOfflineService).saveOrUpdateBatch(entityList);
        verify(mockOrderOfflineService).removeByIds(Arrays.asList("value"));
        verify(mockOrderService).update(any(LambdaUpdateWrapper.class));
        verify(mockOrderExtendService).update(any(LambdaUpdateWrapper.class));

        // Confirm OrderOfflineService.deliveryOrder(...).
        final Vendor vendor1 = new Vendor();
        vendor1.setVendorId(0L);
        vendor1.setVendorName("vendorName");
        vendor1.setVendorPassword("vendorPassword");
        vendor1.setVendorMobile("vendorMobile");
        vendor1.setVendorEmail("vendorEmail");
        verify(mockOrderOfflineService).deliveryOrder("orderSn", vendor1, "deliveryWarehouse", "deliveryWarehouseName");
    }

    @Test
    void testManualReceive() {
        // Setup
        final OrderOfflineManualDTO orderOfflineManualDTO = new OrderOfflineManualDTO();
        orderOfflineManualDTO.setOrderSn("orderSn");
        orderOfflineManualDTO.setOrderType(0);
        orderOfflineManualDTO.setPaymentTag(0);
        orderOfflineManualDTO.setSignInImageUrl("signInImageUrl");
        orderOfflineManualDTO.setCustomerContract("customerContract");
        final OrderOfflineDTO orderOfflineDTO = new OrderOfflineDTO();
        orderOfflineDTO.setId(0L);
        orderOfflineDTO.setReceiptAccount("receiptAccount");
        orderOfflineDTO.setReceiptAmount(new BigDecimal("0.00"));
        orderOfflineDTO.setReceiptTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflineDTO.setEnabledFlag(0);
        orderOfflineManualDTO.setOrderOfflineList(Arrays.asList(orderOfflineDTO));
        orderOfflineManualDTO.setDeliveryWarehouse("deliveryWarehouse");
        orderOfflineManualDTO.setDeliveryWarehouseName("deliveryWarehouseName");
        orderOfflineManualDTO.setInvoiceStatus(0);
        orderOfflineManualDTO.setInvoiceAmount(new BigDecimal("0.00"));
        orderOfflineManualDTO.setInvoiceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflineManualDTO.setAccountPeriodDays(0);
        orderOfflineManualDTO.setOverdueTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        final Vendor vendor = new Vendor();
        vendor.setVendorId(0L);
        vendor.setVendorName("vendorName");
        vendor.setVendorPassword("vendorPassword");
        vendor.setVendorMobile("vendorMobile");
        vendor.setVendorEmail("vendorEmail");

        // Configure IOrderService.getByOrderSn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("orderSn");
        orderPO.setPaySn("paySn");
        orderPO.setStoreId(0L);
        orderPO.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setOrderType(0);
        orderPO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPaymentTag(0);
        when(mockOrderService.getByOrderSn("orderSn")).thenReturn(orderPO);

        // Run the test
        orderOfflineServiceImplUnderTest.manualReceive(orderOfflineManualDTO, vendor);

        // Verify the results
        // Confirm OrderOfflineService.saveOrUpdateBatch(...).
        final OrderOfflinePO orderOfflinePO = new OrderOfflinePO();
        orderOfflinePO.setId(0L);
        orderOfflinePO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflinePO.setEnabledFlag(0);
        orderOfflinePO.setPaySn("paySn");
        orderOfflinePO.setCreateBy("createBy");
        orderOfflinePO.setUpdateBy("updateBy");
        final List<OrderOfflinePO> entityList = Arrays.asList(orderOfflinePO);
        verify(mockOrderOfflineService).saveOrUpdateBatch(entityList);
        verify(mockOrderOfflineService).removeByIds(Arrays.asList("value"));
        verify(mockOrderService).update(any(LambdaUpdateWrapper.class));

        // Confirm OrderModel.receiveOrder(...).
        final OrderPO orderPODb = new OrderPO();
        orderPODb.setOrderId(0);
        orderPODb.setOrderSn("orderSn");
        orderPODb.setPaySn("paySn");
        orderPODb.setStoreId(0L);
        orderPODb.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPODb.setOrderState(0);
        orderPODb.setOrderAmount(new BigDecimal("0.00"));
        orderPODb.setOrderType(0);
        orderPODb.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPODb.setPaymentTag(0);
        verify(mockOrderModel).receiveOrder(orderPODb, 2, 0L, "vendorName", "线下补录订单手动签收", OrderCreateChannel.WEB);
    }

    @Test
    void testInfoSupplement() {
        // Setup
        final OrderOfflineManualDTO orderOfflineManualDTO = new OrderOfflineManualDTO();
        orderOfflineManualDTO.setOrderSn("orderSn");
        orderOfflineManualDTO.setOrderType(0);
        orderOfflineManualDTO.setPaymentTag(0);
        orderOfflineManualDTO.setSignInImageUrl("signInImageUrl");
        orderOfflineManualDTO.setCustomerContract("customerContract");
        final OrderOfflineDTO orderOfflineDTO = new OrderOfflineDTO();
        orderOfflineDTO.setId(0L);
        orderOfflineDTO.setReceiptAccount("receiptAccount");
        orderOfflineDTO.setReceiptAmount(new BigDecimal("0.00"));
        orderOfflineDTO.setReceiptTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflineDTO.setEnabledFlag(0);
        orderOfflineManualDTO.setOrderOfflineList(Arrays.asList(orderOfflineDTO));
        orderOfflineManualDTO.setDeliveryWarehouse("deliveryWarehouse");
        orderOfflineManualDTO.setDeliveryWarehouseName("deliveryWarehouseName");
        orderOfflineManualDTO.setInvoiceStatus(0);
        orderOfflineManualDTO.setInvoiceAmount(new BigDecimal("0.00"));
        orderOfflineManualDTO.setInvoiceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflineManualDTO.setAccountPeriodDays(0);
        orderOfflineManualDTO.setOverdueTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        final Vendor vendor = new Vendor();
        vendor.setVendorId(0L);
        vendor.setVendorName("vendorName");
        vendor.setVendorPassword("vendorPassword");
        vendor.setVendorMobile("vendorMobile");
        vendor.setVendorEmail("vendorEmail");

        // Configure IOrderService.getByOrderSn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("orderSn");
        orderPO.setPaySn("paySn");
        orderPO.setStoreId(0L);
        orderPO.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setOrderType(0);
        orderPO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPaymentTag(0);
        when(mockOrderService.getByOrderSn("orderSn")).thenReturn(orderPO);

        // Configure IOrderExtendService.getOrderExtendByOrderSn(...).
        final OrderExtendPO orderExtendPO = new OrderExtendPO();
        orderExtendPO.setOrderSn("orderSn");
        orderExtendPO.setInvoiceStatus(0);
        orderExtendPO.setInvoiceAmount(new BigDecimal("0.00"));
        orderExtendPO.setInvoiceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderExtendPO.setSignInImageUrl("signInImageUrl");
        orderExtendPO.setCustomerContract("customerContract");
        when(mockOrderExtendService.getOrderExtendByOrderSn("orderSn")).thenReturn(orderExtendPO);

        // Configure IOrderOfflineExtendService.getOrderOfflineExtendByOrderSn(...).
        final OrderOfflineExtendPO orderOfflineExtendPO = new OrderOfflineExtendPO();
        orderOfflineExtendPO.setId(0L);
        orderOfflineExtendPO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflineExtendPO.setEnabledFlag(0);
        orderOfflineExtendPO.setOrderSn("orderSn");
        orderOfflineExtendPO.setOverdueTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflineExtendPO.setOverdueFlag(0);
        orderOfflineExtendPO.setOverdueDays(0);
        orderOfflineExtendPO.setAccountPeriodDays(0);
        when(mockOrderOfflineExtendService.getOrderOfflineExtendByOrderSn("orderSn")).thenReturn(orderOfflineExtendPO);

        // Run the test
        orderOfflineServiceImplUnderTest.infoSupplement(orderOfflineManualDTO, vendor);

        // Verify the results
        verify(mockOrderExtendService).update(any(LambdaUpdateWrapper.class));
        verify(mockOrderLogModel).insertOrderLog(2, 0L, "vendorName", "orderSn", 0, 0, 0, "资料补充",
                OrderCreateChannel.WEB, "remark");
        verify(mockOrderService).update(any(LambdaUpdateWrapper.class));

        // Confirm OrderOfflineService.saveOrUpdateBatch(...).
        final OrderOfflinePO orderOfflinePO = new OrderOfflinePO();
        orderOfflinePO.setId(0L);
        orderOfflinePO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflinePO.setEnabledFlag(0);
        orderOfflinePO.setPaySn("paySn");
        orderOfflinePO.setCreateBy("createBy");
        orderOfflinePO.setUpdateBy("updateBy");
        final List<OrderOfflinePO> entityList = Arrays.asList(orderOfflinePO);
        verify(mockOrderOfflineService).saveOrUpdateBatch(entityList);
        verify(mockOrderOfflineService).removeByIds(Arrays.asList("value"));
        verify(mockOrderOfflineExtendService).update(any(LambdaUpdateWrapper.class));
    }

    @Test
    void testInfoSupplementAdmin() {
        // Setup
        final OrderOfflineManualDTO orderOfflineManualDTO = new OrderOfflineManualDTO();
        orderOfflineManualDTO.setOrderSn("orderSn");
        orderOfflineManualDTO.setOrderType(0);
        orderOfflineManualDTO.setPaymentTag(0);
        orderOfflineManualDTO.setSignInImageUrl("signInImageUrl");
        orderOfflineManualDTO.setCustomerContract("customerContract");
        final OrderOfflineDTO orderOfflineDTO = new OrderOfflineDTO();
        orderOfflineDTO.setId(0L);
        orderOfflineDTO.setReceiptAccount("receiptAccount");
        orderOfflineDTO.setReceiptAmount(new BigDecimal("0.00"));
        orderOfflineDTO.setReceiptTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflineDTO.setEnabledFlag(0);
        orderOfflineManualDTO.setOrderOfflineList(Arrays.asList(orderOfflineDTO));
        orderOfflineManualDTO.setDeliveryWarehouse("deliveryWarehouse");
        orderOfflineManualDTO.setDeliveryWarehouseName("deliveryWarehouseName");
        orderOfflineManualDTO.setInvoiceStatus(0);
        orderOfflineManualDTO.setInvoiceAmount(new BigDecimal("0.00"));
        orderOfflineManualDTO.setInvoiceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflineManualDTO.setAccountPeriodDays(0);
        orderOfflineManualDTO.setOverdueTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        final Admin admin = new Admin();
        admin.setAdminId(0);
        admin.setAdminName("adminName");
        admin.setPhone("phone");
        admin.setEmail("email");
        admin.setEmployeeNumber("employeeNumber");

        // Configure IOrderService.getByOrderSn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("orderSn");
        orderPO.setPaySn("paySn");
        orderPO.setStoreId(0L);
        orderPO.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setOrderType(0);
        orderPO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPaymentTag(0);
        when(mockOrderService.getByOrderSn("orderSn")).thenReturn(orderPO);

        // Configure IOrderExtendService.getOrderExtendByOrderSn(...).
        final OrderExtendPO orderExtendPO = new OrderExtendPO();
        orderExtendPO.setOrderSn("orderSn");
        orderExtendPO.setInvoiceStatus(0);
        orderExtendPO.setInvoiceAmount(new BigDecimal("0.00"));
        orderExtendPO.setInvoiceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderExtendPO.setSignInImageUrl("signInImageUrl");
        orderExtendPO.setCustomerContract("customerContract");
        when(mockOrderExtendService.getOrderExtendByOrderSn("orderSn")).thenReturn(orderExtendPO);

        // Configure IOrderOfflineExtendService.getOrderOfflineExtendByOrderSn(...).
        final OrderOfflineExtendPO orderOfflineExtendPO = new OrderOfflineExtendPO();
        orderOfflineExtendPO.setId(0L);
        orderOfflineExtendPO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflineExtendPO.setEnabledFlag(0);
        orderOfflineExtendPO.setOrderSn("orderSn");
        orderOfflineExtendPO.setOverdueTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflineExtendPO.setOverdueFlag(0);
        orderOfflineExtendPO.setOverdueDays(0);
        orderOfflineExtendPO.setAccountPeriodDays(0);
        when(mockOrderOfflineExtendService.getOrderOfflineExtendByOrderSn("orderSn")).thenReturn(orderOfflineExtendPO);

        // Run the test
        orderOfflineServiceImplUnderTest.infoSupplementAdmin(orderOfflineManualDTO, admin);

        // Verify the results
        verify(mockOrderExtendService).update(any(LambdaUpdateWrapper.class));
        verify(mockOrderLogModel).insertOrderLog(2, 0L, "adminName", "orderSn", 0, 0, 0, "资料补充", OrderCreateChannel.WEB,
                "remark");
        verify(mockOrderService).update(any(LambdaUpdateWrapper.class));

        // Confirm OrderOfflineService.saveOrUpdateBatch(...).
        final OrderOfflinePO orderOfflinePO = new OrderOfflinePO();
        orderOfflinePO.setId(0L);
        orderOfflinePO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflinePO.setEnabledFlag(0);
        orderOfflinePO.setPaySn("paySn");
        orderOfflinePO.setCreateBy("createBy");
        orderOfflinePO.setUpdateBy("updateBy");
        final List<OrderOfflinePO> entityList = Arrays.asList(orderOfflinePO);
        verify(mockOrderOfflineService).saveOrUpdateBatch(entityList);
        verify(mockOrderOfflineService).removeByIds(Arrays.asList("value"));
        verify(mockOrderOfflineExtendService).update(any(LambdaUpdateWrapper.class));
    }

    @Test
    void testGetSiteByPage() {
        // Setup
        final WmsSiteDTO wmsSiteDTO = new WmsSiteDTO();
        wmsSiteDTO.setSiteName("siteName");
        wmsSiteDTO.setSiteContacts("siteContacts");
        wmsSiteDTO.setSiteCode("siteCode");
        wmsSiteDTO.setBranchCode("branchCode");
        wmsSiteDTO.setBranchList(Arrays.asList("value"));

        // Configure WmsIntegration.getSiteByPage(...).
        final SiteQuery siteQuery = new SiteQuery();
        siteQuery.setSiteName("siteName");
        siteQuery.setSiteContacts("siteContacts");
        siteQuery.setSiteType(0);
        siteQuery.setSiteCode("siteCode");
        siteQuery.setSiteCategory(0);
        when(mockWmsIntegration.getSiteByPage(siteQuery)).thenReturn(new Page<>(0L, 0L, 0L, false));

        // Run the test
        final Page<SiteVo> result = orderOfflineServiceImplUnderTest.getSiteByPage(wmsSiteDTO);

        // Verify the results
    }

    @Test
    void testBatchSaveInvoiceLabel() {
        // Setup
        final OrderOfflineInvoiceLabelDTO orderOfflineInvoiceLabelDTO = new OrderOfflineInvoiceLabelDTO();
        orderOfflineInvoiceLabelDTO.setOrderList(Arrays.asList("value"));
        orderOfflineInvoiceLabelDTO.setInvoiceStatus(0);

        final Vendor vendor = new Vendor();
        vendor.setVendorId(0L);
        vendor.setVendorName("vendorName");
        vendor.setVendorPassword("vendorPassword");
        vendor.setVendorMobile("vendorMobile");
        vendor.setVendorEmail("vendorEmail");

        // Configure IOrderService.list(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("orderSn");
        orderPO.setPaySn("paySn");
        orderPO.setStoreId(0L);
        orderPO.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setOrderType(0);
        orderPO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPaymentTag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO);
        when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(orderPOList);

        // Run the test
        orderOfflineServiceImplUnderTest.batchSaveInvoiceLabel(orderOfflineInvoiceLabelDTO, vendor);

        // Verify the results
        verify(mockOrderExtendService).update(any(LambdaUpdateWrapper.class));
        verify(mockOrderLogModel).insertOrderLog(2, 0L, "vendorName", "orderSn", 0, 0, 0, "批量设置开票标签",
                OrderCreateChannel.WEB, "开票标签（已开票——>未开票）");
    }

    @Test
    void testBatchSaveInvoiceLabel_IOrderServiceReturnsNoItems() {
        // Setup
        final OrderOfflineInvoiceLabelDTO orderOfflineInvoiceLabelDTO = new OrderOfflineInvoiceLabelDTO();
        orderOfflineInvoiceLabelDTO.setOrderList(Arrays.asList("value"));
        orderOfflineInvoiceLabelDTO.setInvoiceStatus(0);

        final Vendor vendor = new Vendor();
        vendor.setVendorId(0L);
        vendor.setVendorName("vendorName");
        vendor.setVendorPassword("vendorPassword");
        vendor.setVendorMobile("vendorMobile");
        vendor.setVendorEmail("vendorEmail");

        when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        orderOfflineServiceImplUnderTest.batchSaveInvoiceLabel(orderOfflineInvoiceLabelDTO, vendor);

        // Verify the results
    }

    @Test
    void testBatchSaveAccountPeriodDays() {
        // Setup
        final OrderOfflineAccountPeriodDaysDTO orderOfflineAccountPeriodDaysDTO = new OrderOfflineAccountPeriodDaysDTO();
        orderOfflineAccountPeriodDaysDTO.setOrderList(Arrays.asList("value"));
        orderOfflineAccountPeriodDaysDTO.setAccountPeriodDays(0);

        final Vendor vendor = new Vendor();
        vendor.setVendorId(0L);
        vendor.setVendorName("vendorName");
        vendor.setVendorPassword("vendorPassword");
        vendor.setVendorMobile("vendorMobile");
        vendor.setVendorEmail("vendorEmail");

        // Configure IOrderOfflineExtendService.list(...).
        final OrderOfflineExtendPO orderOfflineExtendPO = new OrderOfflineExtendPO();
        orderOfflineExtendPO.setId(0L);
        orderOfflineExtendPO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflineExtendPO.setEnabledFlag(0);
        orderOfflineExtendPO.setOrderSn("orderSn");
        orderOfflineExtendPO.setOverdueTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderOfflineExtendPO.setOverdueFlag(0);
        orderOfflineExtendPO.setOverdueDays(0);
        orderOfflineExtendPO.setAccountPeriodDays(0);
        final List<OrderOfflineExtendPO> orderOfflineExtendPOS = Arrays.asList(orderOfflineExtendPO);
        when(mockOrderOfflineExtendService.list(any(LambdaQueryWrapper.class))).thenReturn(orderOfflineExtendPOS);

        // Configure OrderModel.getOrderByOrderSn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("orderSn");
        orderPO.setPaySn("paySn");
        orderPO.setStoreId(0L);
        orderPO.setFinishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setOrderType(0);
        orderPO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPaymentTag(0);
        when(mockOrderModel.getOrderByOrderSn("orderSn")).thenReturn(orderPO);

        // Run the test
        orderOfflineServiceImplUnderTest.batchSaveAccountPeriodDays(orderOfflineAccountPeriodDaysDTO, vendor);

        // Verify the results
        verify(mockOrderOfflineExtendService).update(any(LambdaUpdateWrapper.class));
        verify(mockOrderLogModel).insertOrderLog(2, 0L, "vendorName", "orderSn", 0, 0, 0, "批量设置账期",
                OrderCreateChannel.WEB, "remark");
    }

    @Test
    void testBatchSaveAccountPeriodDays_IOrderOfflineExtendServiceListReturnsNoItems() {
        // Setup
        final OrderOfflineAccountPeriodDaysDTO orderOfflineAccountPeriodDaysDTO = new OrderOfflineAccountPeriodDaysDTO();
        orderOfflineAccountPeriodDaysDTO.setOrderList(Arrays.asList("value"));
        orderOfflineAccountPeriodDaysDTO.setAccountPeriodDays(0);

        final Vendor vendor = new Vendor();
        vendor.setVendorId(0L);
        vendor.setVendorName("vendorName");
        vendor.setVendorPassword("vendorPassword");
        vendor.setVendorMobile("vendorMobile");
        vendor.setVendorEmail("vendorEmail");

        when(mockOrderOfflineExtendService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        orderOfflineServiceImplUnderTest.batchSaveAccountPeriodDays(orderOfflineAccountPeriodDaysDTO, vendor);

        // Verify the results
        verify(mockOrderOfflineExtendService).update(any(LambdaUpdateWrapper.class));
    }

    @Test
    void testGetEmployeeBranchCode() {
        // Setup
        // Configure OrderOfflineService.queryCustInfo(...).
        final CustInfoVo custInfoVo = new CustInfoVo();
        custInfoVo.setUserCode("userCode");
        custInfoVo.setUserName("managerName");
        custInfoVo.setIdNo("idNo");
        custInfoVo.setMobile("mobile");
        custInfoVo.setBranchCode("branchCode");
        custInfoVo.setBranchName("branchName");
        custInfoVo.setManager("loanManager");
        custInfoVo.setManagerName("managerName");
        when(mockOrderOfflineService.queryCustInfo("employeeCode")).thenReturn(custInfoVo);

        // Run the test
        final String result = orderOfflineServiceImplUnderTest.getEmployeeBranchCode("employeeCode");

        // Verify the results
        assertThat(result).isEqualTo("branchCode");
    }

    @Test
    void testNotifyByDingTalk() {
        // Setup
        when(mockOrderService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure BmsIntegration.listDictionaryItemsByTypeCode(...).
        final DictionaryItemVO dictionaryItemVO = new DictionaryItemVO();
        dictionaryItemVO.setId(0);
        dictionaryItemVO.setTypeId(0);
        dictionaryItemVO.setItemName("itemName");
        dictionaryItemVO.setItemCode("itemCode");
        dictionaryItemVO.setItemStatus(0);
        final List<DictionaryItemVO> dictionaryItemVOS = Arrays.asList(dictionaryItemVO);
        when(mockBmsIntegration.listDictionaryItemsByTypeCode("OFFLINE_NOTIFY_LIST", 0)).thenReturn(dictionaryItemVOS);

        // Configure BmsDingTalkFacade.listUserDetailVOByMobiles(...).
        final ErrorContext errorContext = new ErrorContext();
        final CommonError commonError = new CommonError();
        commonError.setErrorCode("errorCode");
        commonError.setErrorMsg("errorMsg");
        commonError.setLocation("location");
        commonError.setThrowable(new Exception("message"));
        errorContext.setErrorStack(Arrays.asList(commonError));
        final DingTalkUserBmsVO dingTalkUserBmsVO = new DingTalkUserBmsVO();
        final DingTalkUserDeptBmsVO dingTalkUserDeptBmsVO = new DingTalkUserDeptBmsVO();
        dingTalkUserDeptBmsVO.setDingTalkUserId("dingTalkUserId");
        dingTalkUserDeptBmsVO.setDingTalkIsLeader(false);
        dingTalkUserBmsVO.setDeptList(Arrays.asList(dingTalkUserDeptBmsVO));
        final DingTalkUserRoleBmsVO dingTalkUserRoleBmsVO = new DingTalkUserRoleBmsVO();
        dingTalkUserRoleBmsVO.setDingTalkUserId("dingTalkUserId");
        dingTalkUserBmsVO.setRoleList(Arrays.asList(dingTalkUserRoleBmsVO));
        final Result<List<DingTalkUserBmsVO>> listResult = new Result<>(false, errorContext,
                Arrays.asList(dingTalkUserBmsVO));
        when(mockBmsDingTalkFacade.listUserDetailVOByMobiles(Arrays.asList("value"))).thenReturn(listResult);

        // Run the test
        final Boolean result = orderOfflineServiceImplUnderTest.notifyByDingTalk("orderSn");

        // Verify the results
        assertThat(result).isFalse();
        verify(mockBmsDingTalkFacade).sendPlatformTextMessage(Arrays.asList("value"), "message");
    }

    @Test
    void testNotifyByDingTalk_BmsIntegrationReturnsNoItems() {
        // Setup
        when(mockOrderService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));
        when(mockBmsIntegration.listDictionaryItemsByTypeCode("OFFLINE_NOTIFY_LIST", 0))
                .thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(() -> orderOfflineServiceImplUnderTest.notifyByDingTalk("orderSn"))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    void testNotifyByDingTalk_BmsDingTalkFacadeListUserDetailVOByMobilesReturnsNoItems() {
        // Setup
        when(mockOrderService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure BmsIntegration.listDictionaryItemsByTypeCode(...).
        final DictionaryItemVO dictionaryItemVO = new DictionaryItemVO();
        dictionaryItemVO.setId(0);
        dictionaryItemVO.setTypeId(0);
        dictionaryItemVO.setItemName("itemName");
        dictionaryItemVO.setItemCode("itemCode");
        dictionaryItemVO.setItemStatus(0);
        final List<DictionaryItemVO> dictionaryItemVOS = Arrays.asList(dictionaryItemVO);
        when(mockBmsIntegration.listDictionaryItemsByTypeCode("OFFLINE_NOTIFY_LIST", 0)).thenReturn(dictionaryItemVOS);

        when(mockBmsDingTalkFacade.listUserDetailVOByMobiles(Arrays.asList("value")))
                .thenReturn(Result.ok(Collections.emptyList()));

        // Run the test
        final Boolean result = orderOfflineServiceImplUnderTest.notifyByDingTalk("orderSn");

        // Verify the results
        assertThat(result).isFalse();
        verify(mockBmsDingTalkFacade).sendPlatformTextMessage(Arrays.asList("value"), "message");
    }

    @Test
    void testGetReceiptInfoList() {
        // Setup
        final OrderOfflineRequest request = new OrderOfflineRequest();
        request.setOrderSn("orderSn");
        request.setPaySn("paySn");
        request.setPaymentCode("paymentCode");
        request.setOrderStateList(Arrays.asList(0));
        request.setUpdateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        final OrderOfflineInfoVO orderOfflineInfoVO = new OrderOfflineInfoVO();
        orderOfflineInfoVO.setUniqueNo("uniqueNo");
        orderOfflineInfoVO.setOrderSn("orderSn");
        orderOfflineInfoVO.setPaySn("paySn");
        orderOfflineInfoVO.setOrderState(0);
        orderOfflineInfoVO.setPaymentName("paymentName");
        final List<OrderOfflineInfoVO> expectedResult = Arrays.asList(orderOfflineInfoVO);

        // Configure OrderOfflineMapper.getReceiptInfoList(...).
        final OrderOfflineInfoVO orderOfflineInfoVO1 = new OrderOfflineInfoVO();
        orderOfflineInfoVO1.setUniqueNo("uniqueNo");
        orderOfflineInfoVO1.setOrderSn("orderSn");
        orderOfflineInfoVO1.setPaySn("paySn");
        orderOfflineInfoVO1.setOrderState(0);
        orderOfflineInfoVO1.setPaymentName("paymentName");
        final List<OrderOfflineInfoVO> orderOfflineInfoVOS = Arrays.asList(orderOfflineInfoVO1);
        final OrderOfflineRequest example = new OrderOfflineRequest();
        example.setOrderSn("orderSn");
        example.setPaySn("paySn");
        example.setPaymentCode("paymentCode");
        example.setOrderStateList(Arrays.asList(0));
        example.setUpdateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockOrderOfflineMapper.getReceiptInfoList(example)).thenReturn(orderOfflineInfoVOS);

        // Run the test
        final List<OrderOfflineInfoVO> result = orderOfflineServiceImplUnderTest.getReceiptInfoList(request);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetReceiptInfoList_OrderOfflineMapperReturnsNoItems() {
        // Setup
        final OrderOfflineRequest request = new OrderOfflineRequest();
        request.setOrderSn("orderSn");
        request.setPaySn("paySn");
        request.setPaymentCode("paymentCode");
        request.setOrderStateList(Arrays.asList(0));
        request.setUpdateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure OrderOfflineMapper.getReceiptInfoList(...).
        final OrderOfflineRequest example = new OrderOfflineRequest();
        example.setOrderSn("orderSn");
        example.setPaySn("paySn");
        example.setPaymentCode("paymentCode");
        example.setOrderStateList(Arrays.asList(0));
        example.setUpdateTimeBefore(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockOrderOfflineMapper.getReceiptInfoList(example)).thenReturn(Collections.emptyList());

        // Run the test
        final List<OrderOfflineInfoVO> result = orderOfflineServiceImplUnderTest.getReceiptInfoList(request);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
