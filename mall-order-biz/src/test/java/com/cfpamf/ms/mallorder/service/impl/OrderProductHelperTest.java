package com.cfpamf.ms.mallorder.service.impl;

import com.cfpamf.ms.mallpromotion.api.LadderGroupGoodsFeignClient;
import com.cfpamf.ms.mallpromotion.api.PresellFeignClient;
import com.cfpamf.ms.mallpromotion.api.PresellGoodsFeignClient;
import com.cfpamf.ms.mallpromotion.api.SpellGoodsFeignClient;
import com.cfpamf.ms.mallpromotion.request.LadderGroupGoods;
import com.cfpamf.ms.mallpromotion.request.PresellGoodsExample;
import com.cfpamf.ms.mallpromotion.request.SpellGoodsExample;
import com.cfpamf.ms.mallpromotion.vo.LadderGroupGoodsVO;
import com.cfpamf.ms.mallpromotion.vo.PreSellVO;
import com.cfpamf.ms.mallpromotion.vo.PresellGoodsVO;
import com.cfpamf.ms.mallpromotion.vo.SpellGoodsVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OrderProductHelperTest {

    @Mock
    private SpellGoodsFeignClient mockSpellGoodsFeignClient;
    @Mock
    private PresellFeignClient mockPresellFeignClient;
    @Mock
    private PresellGoodsFeignClient mockPresellGoodsFeignClient;
    @Mock
    private LadderGroupGoodsFeignClient mockLadderGroupGoodsFeignClient;

    @InjectMocks
    private OrderProductHelper orderProductHelperUnderTest;

    @Test
    public void testGetSpellGroupPrice3() {
        // Setup
        // Configure SpellGoodsFeignClient.getSpellGoodsList(...).
        final SpellGoodsVO spellGoodsVO = new SpellGoodsVO();
        spellGoodsVO.setSpellGoodsId(0);
        spellGoodsVO.setSpellId(0);
        spellGoodsVO.setGoodsId(0L);
        spellGoodsVO.setSpellPrice(new BigDecimal("0.00"));
        spellGoodsVO.setLeaderPrice(new BigDecimal("0.00"));
        final List<SpellGoodsVO> spellGoodsVOS = Arrays.asList(spellGoodsVO);
        final SpellGoodsExample spellGoodsExample = new SpellGoodsExample();
        spellGoodsExample.setSpellGoodsIdNotEquals(0);
        spellGoodsExample.setSpellGoodsIdIn("spellGoodsIdIn");
        spellGoodsExample.setSpellGoodsId(0);
        spellGoodsExample.setSpellId(0);
        spellGoodsExample.setProductId(0L);
//        when(mockSpellGoodsFeignClient.getSpellGoodsList(spellGoodsExample)).thenReturn(spellGoodsVOS);

        // Run the test
        final BigDecimal result = orderProductHelperUnderTest.getSpellGroupPrice(0, 0L, 0L);

        // Verify the results
//        assertEquals(new BigDecimal("0.00"), result);
    }

    @Test
    public void testGetSpellGroupPrice_SpellGoodsFeignClientReturnsNoItems2() {
        // Setup
        // Configure SpellGoodsFeignClient.getSpellGoodsList(...).
        final SpellGoodsExample spellGoodsExample = new SpellGoodsExample();
        spellGoodsExample.setSpellGoodsIdNotEquals(0);
        spellGoodsExample.setSpellGoodsIdIn("spellGoodsIdIn");
        spellGoodsExample.setSpellGoodsId(0);
        spellGoodsExample.setSpellId(0);
        spellGoodsExample.setProductId(0L);
//        when(mockSpellGoodsFeignClient.getSpellGoodsList(spellGoodsExample)).thenReturn(Collections.emptyList());

        // Run the test
        final BigDecimal result = orderProductHelperUnderTest.getSpellGroupPrice(0, 0L, 0L);

        // Verify the results
//        assertEquals(new BigDecimal("0.00"), result);
    }

    @Test
    public void testGetPreSellPrice4() {
        // Setup
        // Configure PresellFeignClient.getPresellByPresellId(...).
        final PreSellVO preSellVO = new PreSellVO();
        preSellVO.setPresellId(0);
        preSellVO.setPresellName("presellName");
        preSellVO.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        preSellVO.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        preSellVO.setType(0);
        when(mockPresellFeignClient.getPresellByPresellId(0)).thenReturn(preSellVO);

        // Configure PresellGoodsFeignClient.getPresellGoodsList(...).
        final PresellGoodsVO presellGoodsVO = new PresellGoodsVO();
        presellGoodsVO.setPresellGoodsId(0);
        presellGoodsVO.setPresellId(0);
        presellGoodsVO.setProductId(0L);
        presellGoodsVO.setPresellPrice(new BigDecimal("0.00"));
        presellGoodsVO.setFirstMoney(new BigDecimal("0.00"));
        final List<PresellGoodsVO> presellGoodsVOS = Arrays.asList(presellGoodsVO);
        final PresellGoodsExample presellGoodsExample = new PresellGoodsExample();
        presellGoodsExample.setPresellGoodsIdNotEquals(0);
        presellGoodsExample.setPresellGoodsIdIn("presellGoodsIdIn");
        presellGoodsExample.setPresellGoodsId(0);
        presellGoodsExample.setPresellId(0);
        presellGoodsExample.setProductId(0L);
//        when(mockPresellGoodsFeignClient.getPresellGoodsList(presellGoodsExample)).thenReturn(presellGoodsVOS);

        // Run the test
        final BigDecimal result = orderProductHelperUnderTest.getPreSellPrice(0, 0L);

        // Verify the results
//        assertEquals(new BigDecimal("0.00"), result);
    }

    @Test
    public void testGetPreSellPrice_PresellGoodsFeignClientReturnsNoItems5() {
        // Setup
        // Configure PresellFeignClient.getPresellByPresellId(...).
        final PreSellVO preSellVO = new PreSellVO();
        preSellVO.setPresellId(0);
        preSellVO.setPresellName("presellName");
        preSellVO.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        preSellVO.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        preSellVO.setType(0);
        when(mockPresellFeignClient.getPresellByPresellId(0)).thenReturn(preSellVO);

        // Configure PresellGoodsFeignClient.getPresellGoodsList(...).
        final PresellGoodsExample presellGoodsExample = new PresellGoodsExample();
        presellGoodsExample.setPresellGoodsIdNotEquals(0);
        presellGoodsExample.setPresellGoodsIdIn("presellGoodsIdIn");
        presellGoodsExample.setPresellGoodsId(0);
        presellGoodsExample.setPresellId(0);
        presellGoodsExample.setProductId(0L);
//        when(mockPresellGoodsFeignClient.getPresellGoodsList(presellGoodsExample)).thenReturn(Collections.emptyList());

        // Run the test
        final BigDecimal result = orderProductHelperUnderTest.getPreSellPrice(0, 0L);

        // Verify the results
//        assertEquals(new BigDecimal("0.00"), result);
    }

    @Test
    public void testGetLadderGroupPrice2() {
        // Setup
        // Configure LadderGroupGoodsFeignClient.getLadderGroupGoodsList(...).
        final LadderGroupGoodsVO ladderGroupGoodsVO = new LadderGroupGoodsVO();
        ladderGroupGoodsVO.setGroupGoodsId(0);
        ladderGroupGoodsVO.setGroupId(0);
        ladderGroupGoodsVO.setGoodsId(0L);
        ladderGroupGoodsVO.setGoodsName("goodsName");
        ladderGroupGoodsVO.setAdvanceDeposit(new BigDecimal("0.00"));
        final List<LadderGroupGoodsVO> ladderGroupGoodsVOS = Arrays.asList(ladderGroupGoodsVO);
        final LadderGroupGoods ladderGroupGoods = new LadderGroupGoods();
        ladderGroupGoods.setGroupGoodsId(0);
        ladderGroupGoods.setGroupId(0);
        ladderGroupGoods.setGoodsId(0L);
        ladderGroupGoods.setGoodsName("goodsName");
        ladderGroupGoods.setProductId(0L);
//        when(mockLadderGroupGoodsFeignClient.getLadderGroupGoodsList(ladderGroupGoods)).thenReturn(ladderGroupGoodsVOS);

        // Run the test
        final BigDecimal result = orderProductHelperUnderTest.getLadderGroupPrice(0, 0L);

        // Verify the results
//        assertEquals(new BigDecimal("0.00"), result);
    }

    @Test
    public void testGetLadderGroupPrice_LadderGroupGoodsFeignClientReturnsNoItems6() {
        // Setup
        // Configure LadderGroupGoodsFeignClient.getLadderGroupGoodsList(...).
        final LadderGroupGoods ladderGroupGoods = new LadderGroupGoods();
        ladderGroupGoods.setGroupGoodsId(0);
        ladderGroupGoods.setGroupId(0);
        ladderGroupGoods.setGoodsId(0L);
        ladderGroupGoods.setGoodsName("goodsName");
        ladderGroupGoods.setProductId(0L);
//        when(mockLadderGroupGoodsFeignClient.getLadderGroupGoodsList(ladderGroupGoods))
//                .thenReturn(Collections.emptyList());

        // Run the test
        final BigDecimal result = orderProductHelperUnderTest.getLadderGroupPrice(0, 0L);

        // Verify the results
//        assertEquals(new BigDecimal("0.00"), result);
    }

    @Test
    public void testGetSpellGroupPrice4() {
        // Setup
        // Configure SpellGoodsFeignClient.getSpellGoodsList(...).
        final SpellGoodsVO spellGoodsVO = new SpellGoodsVO();
        spellGoodsVO.setSpellGoodsId(0);
        spellGoodsVO.setSpellId(0);
        spellGoodsVO.setGoodsId(0L);
        spellGoodsVO.setSpellPrice(new BigDecimal("0.00"));
        spellGoodsVO.setLeaderPrice(new BigDecimal("0.00"));
        final List<SpellGoodsVO> spellGoodsVOS = Arrays.asList(spellGoodsVO);
        final SpellGoodsExample spellGoodsExample = new SpellGoodsExample();
        spellGoodsExample.setSpellGoodsIdNotEquals(0);
        spellGoodsExample.setSpellGoodsIdIn("spellGoodsIdIn");
        spellGoodsExample.setSpellGoodsId(0);
        spellGoodsExample.setSpellId(0);
        spellGoodsExample.setProductId(0L);
//        when(mockSpellGoodsFeignClient.getSpellGoodsList(spellGoodsExample)).thenReturn(spellGoodsVOS);

        // Run the test
        final BigDecimal result = orderProductHelperUnderTest.getSpellGroupPrice(0, 0L, 0L);

        // Verify the results
//        assertEquals(new BigDecimal("0.00"), result);
    }

    @Test
    public void testGetSpellGroupPrice_SpellGoodsFeignClientReturnsNoItem2s() {
        // Setup
        // Configure SpellGoodsFeignClient.getSpellGoodsList(...).
        final SpellGoodsExample spellGoodsExample = new SpellGoodsExample();
        spellGoodsExample.setSpellGoodsIdNotEquals(0);
        spellGoodsExample.setSpellGoodsIdIn("spellGoodsIdIn");
        spellGoodsExample.setSpellGoodsId(0);
        spellGoodsExample.setSpellId(0);
        spellGoodsExample.setProductId(0L);
//        when(mockSpellGoodsFeignClient.getSpellGoodsList(spellGoodsExample)).thenReturn(Collections.emptyList());

        // Run the test
        final BigDecimal result = orderProductHelperUnderTest.getSpellGroupPrice(0, 0L, 0L);

        // Verify the results
//        assertEquals(new BigDecimal("0.00"), result);
    }

    @Test
    public void testGetPreSellPric3() {
        // Setup
        // Configure PresellFeignClient.getPresellByPresellId(...).
        final PreSellVO preSellVO = new PreSellVO();
        preSellVO.setPresellId(0);
        preSellVO.setPresellName("presellName");
        preSellVO.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        preSellVO.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        preSellVO.setType(0);
        when(mockPresellFeignClient.getPresellByPresellId(0)).thenReturn(preSellVO);

        // Configure PresellGoodsFeignClient.getPresellGoodsList(...).
        final PresellGoodsVO presellGoodsVO = new PresellGoodsVO();
        presellGoodsVO.setPresellGoodsId(0);
        presellGoodsVO.setPresellId(0);
        presellGoodsVO.setProductId(0L);
        presellGoodsVO.setPresellPrice(new BigDecimal("0.00"));
        presellGoodsVO.setFirstMoney(new BigDecimal("0.00"));
        final List<PresellGoodsVO> presellGoodsVOS = Arrays.asList(presellGoodsVO);
        final PresellGoodsExample presellGoodsExample = new PresellGoodsExample();
        presellGoodsExample.setPresellGoodsIdNotEquals(0);
        presellGoodsExample.setPresellGoodsIdIn("presellGoodsIdIn");
        presellGoodsExample.setPresellGoodsId(0);
        presellGoodsExample.setPresellId(0);
        presellGoodsExample.setProductId(0L);
//        when(mockPresellGoodsFeignClient.getPresellGoodsList(presellGoodsExample)).thenReturn(presellGoodsVOS);

        // Run the test
        final BigDecimal result = orderProductHelperUnderTest.getPreSellPrice(0, 0L);

        // Verify the results
//        assertEquals(new BigDecimal("0.00"), result);
    }

    @Test
    public void testGetPreSellPrice_PresellGoodsFeignClientReturnsNoItems8() {
        // Setup
        // Configure PresellFeignClient.getPresellByPresellId(...).
        final PreSellVO preSellVO = new PreSellVO();
        preSellVO.setPresellId(0);
        preSellVO.setPresellName("presellName");
        preSellVO.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        preSellVO.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        preSellVO.setType(0);
        when(mockPresellFeignClient.getPresellByPresellId(0)).thenReturn(preSellVO);

        // Configure PresellGoodsFeignClient.getPresellGoodsList(...).
        final PresellGoodsExample presellGoodsExample = new PresellGoodsExample();
        presellGoodsExample.setPresellGoodsIdNotEquals(0);
        presellGoodsExample.setPresellGoodsIdIn("presellGoodsIdIn");
        presellGoodsExample.setPresellGoodsId(0);
        presellGoodsExample.setPresellId(0);
        presellGoodsExample.setProductId(0L);
//        when(mockPresellGoodsFeignClient.getPresellGoodsList(presellGoodsExample)).thenReturn(Collections.emptyList());

        // Run the test
        final BigDecimal result = orderProductHelperUnderTest.getPreSellPrice(0, 0L);

        // Verify the results
//        assertEquals(new BigDecimal("0.00"), result);
    }

    @Test
    public void testGetLadderGroupPrice212() {
        // Setup
        // Configure LadderGroupGoodsFeignClient.getLadderGroupGoodsList(...).
        final LadderGroupGoodsVO ladderGroupGoodsVO = new LadderGroupGoodsVO();
        ladderGroupGoodsVO.setGroupGoodsId(0);
        ladderGroupGoodsVO.setGroupId(0);
        ladderGroupGoodsVO.setGoodsId(0L);
        ladderGroupGoodsVO.setGoodsName("goodsName");
        ladderGroupGoodsVO.setAdvanceDeposit(new BigDecimal("0.00"));
        final List<LadderGroupGoodsVO> ladderGroupGoodsVOS = Arrays.asList(ladderGroupGoodsVO);
        final LadderGroupGoods ladderGroupGoods = new LadderGroupGoods();
        ladderGroupGoods.setGroupGoodsId(0);
        ladderGroupGoods.setGroupId(0);
        ladderGroupGoods.setGoodsId(0L);
        ladderGroupGoods.setGoodsName("goodsName");
        ladderGroupGoods.setProductId(0L);
//        when(mockLadderGroupGoodsFeignClient.getLadderGroupGoodsList(ladderGroupGoods)).thenReturn(ladderGroupGoodsVOS);

        // Run the test
        final BigDecimal result = orderProductHelperUnderTest.getLadderGroupPrice(0, 0L);

        // Verify the results
//        assertEquals(new BigDecimal("0.00"), result);
    }

    @Test
    public void testGetLadderGroupPrice_LadderGroupGoodsFeignClientReturnsNoItems1452() {
        // Setup
        // Configure LadderGroupGoodsFeignClient.getLadderGroupGoodsList(...).
        final LadderGroupGoods ladderGroupGoods = new LadderGroupGoods();
        ladderGroupGoods.setGroupGoodsId(0);
        ladderGroupGoods.setGroupId(0);
        ladderGroupGoods.setGoodsId(0L);
        ladderGroupGoods.setGoodsName("goodsName");
        ladderGroupGoods.setProductId(0L);
//        when(mockLadderGroupGoodsFeignClient.getLadderGroupGoodsList(ladderGroupGoods))
//                .thenReturn(Collections.emptyList());

        // Run the test
        final BigDecimal result = orderProductHelperUnderTest.getLadderGroupPrice(0, 0L);

        // Verify the results
//        assertEquals(new BigDecimal("0.00"), result);
    }

    @Test
    public void testGetSpellGroupPrice11() {
        // Setup
        // Configure SpellGoodsFeignClient.getSpellGoodsList(...).
        final SpellGoodsVO spellGoodsVO = new SpellGoodsVO();
        spellGoodsVO.setSpellGoodsId(0);
        spellGoodsVO.setSpellId(0);
        spellGoodsVO.setGoodsId(0L);
        spellGoodsVO.setSpellPrice(new BigDecimal("0.00"));
        spellGoodsVO.setLeaderPrice(new BigDecimal("0.00"));
        final List<SpellGoodsVO> spellGoodsVOS = Arrays.asList(spellGoodsVO);
        final SpellGoodsExample spellGoodsExample = new SpellGoodsExample();
        spellGoodsExample.setSpellGoodsIdNotEquals(0);
        spellGoodsExample.setSpellGoodsIdIn("spellGoodsIdIn");
        spellGoodsExample.setSpellGoodsId(0);
        spellGoodsExample.setSpellId(0);
        spellGoodsExample.setProductId(0L);
//        when(mockSpellGoodsFeignClient.getSpellGoodsList(spellGoodsExample)).thenReturn(spellGoodsVOS);

        // Run the test
        final BigDecimal result = orderProductHelperUnderTest.getSpellGroupPrice(0, 0L, 0L);

        // Verify the results
//        assertEquals(new BigDecimal("0.00"), result);
    }

    @Test
    public void testGetSpellGroupPrice_SpellGoodsFeignClientReturnsNoItems15() {
        // Setup
        // Configure SpellGoodsFeignClient.getSpellGoodsList(...).
        final SpellGoodsExample spellGoodsExample = new SpellGoodsExample();
        spellGoodsExample.setSpellGoodsIdNotEquals(0);
        spellGoodsExample.setSpellGoodsIdIn("spellGoodsIdIn");
        spellGoodsExample.setSpellGoodsId(0);
        spellGoodsExample.setSpellId(0);
        spellGoodsExample.setProductId(0L);
//        when(mockSpellGoodsFeignClient.getSpellGoodsList(spellGoodsExample)).thenReturn(Collections.emptyList());

        // Run the test
        final BigDecimal result = orderProductHelperUnderTest.getSpellGroupPrice(0, 0L, 0L);

        // Verify the results
//        assertEquals(new BigDecimal("0.00"), result);
    }

    @Test
    public void testGetPreSellPrice177() {
        // Setup
        // Configure PresellFeignClient.getPresellByPresellId(...).
        final PreSellVO preSellVO = new PreSellVO();
        preSellVO.setPresellId(0);
        preSellVO.setPresellName("presellName");
        preSellVO.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        preSellVO.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        preSellVO.setType(0);
        when(mockPresellFeignClient.getPresellByPresellId(0)).thenReturn(preSellVO);

        // Configure PresellGoodsFeignClient.getPresellGoodsList(...).
        final PresellGoodsVO presellGoodsVO = new PresellGoodsVO();
        presellGoodsVO.setPresellGoodsId(0);
        presellGoodsVO.setPresellId(0);
        presellGoodsVO.setProductId(0L);
        presellGoodsVO.setPresellPrice(new BigDecimal("0.00"));
        presellGoodsVO.setFirstMoney(new BigDecimal("0.00"));
        final List<PresellGoodsVO> presellGoodsVOS = Arrays.asList(presellGoodsVO);
        final PresellGoodsExample presellGoodsExample = new PresellGoodsExample();
        presellGoodsExample.setPresellGoodsIdNotEquals(0);
        presellGoodsExample.setPresellGoodsIdIn("presellGoodsIdIn");
        presellGoodsExample.setPresellGoodsId(0);
        presellGoodsExample.setPresellId(0);
        presellGoodsExample.setProductId(0L);
//        when(mockPresellGoodsFeignClient.getPresellGoodsList(presellGoodsExample)).thenReturn(presellGoodsVOS);

        // Run the test
        final BigDecimal result = orderProductHelperUnderTest.getPreSellPrice(0, 0L);

        // Verify the results
//        assertEquals(new BigDecimal("0.00"), result);
    }

    @Test
    public void testGetPreSellPrice_PresellGoodsFeignClientReturnsNoItems12() {
        // Setup
        // Configure PresellFeignClient.getPresellByPresellId(...).
        final PreSellVO preSellVO = new PreSellVO();
        preSellVO.setPresellId(0);
        preSellVO.setPresellName("presellName");
        preSellVO.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        preSellVO.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        preSellVO.setType(0);
        when(mockPresellFeignClient.getPresellByPresellId(0)).thenReturn(preSellVO);

        // Configure PresellGoodsFeignClient.getPresellGoodsList(...).
        final PresellGoodsExample presellGoodsExample = new PresellGoodsExample();
        presellGoodsExample.setPresellGoodsIdNotEquals(0);
        presellGoodsExample.setPresellGoodsIdIn("presellGoodsIdIn");
        presellGoodsExample.setPresellGoodsId(0);
        presellGoodsExample.setPresellId(0);
        presellGoodsExample.setProductId(0L);
//        when(mockPresellGoodsFeignClient.getPresellGoodsList(presellGoodsExample)).thenReturn(Collections.emptyList());

        // Run the test
        final BigDecimal result = orderProductHelperUnderTest.getPreSellPrice(0, 0L);

        // Verify the results
//        assertEquals(new BigDecimal("0.00"), result);
    }

    @Test
    public void testGetLadderGroupPrice12() {
        // Setup
        // Configure LadderGroupGoodsFeignClient.getLadderGroupGoodsList(...).
        final LadderGroupGoodsVO ladderGroupGoodsVO = new LadderGroupGoodsVO();
        ladderGroupGoodsVO.setGroupGoodsId(0);
        ladderGroupGoodsVO.setGroupId(0);
        ladderGroupGoodsVO.setGoodsId(0L);
        ladderGroupGoodsVO.setGoodsName("goodsName");
        ladderGroupGoodsVO.setAdvanceDeposit(new BigDecimal("0.00"));
        final List<LadderGroupGoodsVO> ladderGroupGoodsVOS = Arrays.asList(ladderGroupGoodsVO);
        final LadderGroupGoods ladderGroupGoods = new LadderGroupGoods();
        ladderGroupGoods.setGroupGoodsId(0);
        ladderGroupGoods.setGroupId(0);
        ladderGroupGoods.setGoodsId(0L);
        ladderGroupGoods.setGoodsName("goodsName");
        ladderGroupGoods.setProductId(0L);
//        when(mockLadderGroupGoodsFeignClient.getLadderGroupGoodsList(ladderGroupGoods)).thenReturn(ladderGroupGoodsVOS);

        // Run the test
        final BigDecimal result = orderProductHelperUnderTest.getLadderGroupPrice(0, 0L);

        // Verify the results
//        assertEquals(new BigDecimal("0.00"), result);
    }

    @Test
    public void testGetLadderGroupPrice_LadderGroupGoodsFeignClientReturnsNoItems12() {
        // Setup
        // Configure LadderGroupGoodsFeignClient.getLadderGroupGoodsList(...).
        final LadderGroupGoods ladderGroupGoods = new LadderGroupGoods();
        ladderGroupGoods.setGroupGoodsId(0);
        ladderGroupGoods.setGroupId(0);
        ladderGroupGoods.setGoodsId(0L);
        ladderGroupGoods.setGoodsName("goodsName");
        ladderGroupGoods.setProductId(0L);
//        when(mockLadderGroupGoodsFeignClient.getLadderGroupGoodsList(ladderGroupGoods))
//                .thenReturn(Collections.emptyList());

        // Run the test
        final BigDecimal result = orderProductHelperUnderTest.getLadderGroupPrice(0, 0L);

        // Verify the results
//        assertEquals(new BigDecimal("0.00"), result);
    }

    @Test
    public void testGetSpellGroupPrice12() {
        // Setup
        // Configure SpellGoodsFeignClient.getSpellGoodsList(...).
        final SpellGoodsVO spellGoodsVO = new SpellGoodsVO();
        spellGoodsVO.setSpellGoodsId(0);
        spellGoodsVO.setSpellId(0);
        spellGoodsVO.setGoodsId(0L);
        spellGoodsVO.setSpellPrice(new BigDecimal("0.00"));
        spellGoodsVO.setLeaderPrice(new BigDecimal("0.00"));
        final List<SpellGoodsVO> spellGoodsVOS = Arrays.asList(spellGoodsVO);
        final SpellGoodsExample spellGoodsExample = new SpellGoodsExample();
        spellGoodsExample.setSpellGoodsIdNotEquals(0);
        spellGoodsExample.setSpellGoodsIdIn("spellGoodsIdIn");
        spellGoodsExample.setSpellGoodsId(0);
        spellGoodsExample.setSpellId(0);
        spellGoodsExample.setProductId(0L);
//        when(mockSpellGoodsFeignClient.getSpellGoodsList(spellGoodsExample)).thenReturn(spellGoodsVOS);

        // Run the test
        final BigDecimal result = orderProductHelperUnderTest.getSpellGroupPrice(0, 0L, 0L);

        // Verify the results
//        assertEquals(new BigDecimal("0.00"), result);
    }

    @Test
    public void testGetSpellGroupPrice_SpellGoodsFeignClientReturnsNoItems12() {
        // Setup
        // Configure SpellGoodsFeignClient.getSpellGoodsList(...).
        final SpellGoodsExample spellGoodsExample = new SpellGoodsExample();
        spellGoodsExample.setSpellGoodsIdNotEquals(0);
        spellGoodsExample.setSpellGoodsIdIn("spellGoodsIdIn");
        spellGoodsExample.setSpellGoodsId(0);
        spellGoodsExample.setSpellId(0);
        spellGoodsExample.setProductId(0L);
//        when(mockSpellGoodsFeignClient.getSpellGoodsList(spellGoodsExample)).thenReturn(Collections.emptyList());

        // Run the test
        final BigDecimal result = orderProductHelperUnderTest.getSpellGroupPrice(0, 0L, 0L);

        // Verify the results
//        assertEquals(new BigDecimal("0.00"), result);
    }

    @Test
    public void testGetPreSellPrice16() {
        // Setup
        // Configure PresellFeignClient.getPresellByPresellId(...).
        final PreSellVO preSellVO = new PreSellVO();
        preSellVO.setPresellId(0);
        preSellVO.setPresellName("presellName");
        preSellVO.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        preSellVO.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        preSellVO.setType(0);
        when(mockPresellFeignClient.getPresellByPresellId(0)).thenReturn(preSellVO);

        // Configure PresellGoodsFeignClient.getPresellGoodsList(...).
        final PresellGoodsVO presellGoodsVO = new PresellGoodsVO();
        presellGoodsVO.setPresellGoodsId(0);
        presellGoodsVO.setPresellId(0);
        presellGoodsVO.setProductId(0L);
        presellGoodsVO.setPresellPrice(new BigDecimal("0.00"));
        presellGoodsVO.setFirstMoney(new BigDecimal("0.00"));
        final List<PresellGoodsVO> presellGoodsVOS = Arrays.asList(presellGoodsVO);
        final PresellGoodsExample presellGoodsExample = new PresellGoodsExample();
        presellGoodsExample.setPresellGoodsIdNotEquals(0);
        presellGoodsExample.setPresellGoodsIdIn("presellGoodsIdIn");
        presellGoodsExample.setPresellGoodsId(0);
        presellGoodsExample.setPresellId(0);
        presellGoodsExample.setProductId(0L);
//        when(mockPresellGoodsFeignClient.getPresellGoodsList(presellGoodsExample)).thenReturn(presellGoodsVOS);

        // Run the test
        final BigDecimal result = orderProductHelperUnderTest.getPreSellPrice(0, 0L);

        // Verify the results
//        assertEquals(new BigDecimal("0.00"), result);
    }

    @Test
    public void testGetPreSellPrice_PresellGoodsFeignClientReturnsNoItems112() {
        // Setup
        // Configure PresellFeignClient.getPresellByPresellId(...).
        final PreSellVO preSellVO = new PreSellVO();
        preSellVO.setPresellId(0);
        preSellVO.setPresellName("presellName");
        preSellVO.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        preSellVO.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        preSellVO.setType(0);
        when(mockPresellFeignClient.getPresellByPresellId(0)).thenReturn(preSellVO);

        // Configure PresellGoodsFeignClient.getPresellGoodsList(...).
        final PresellGoodsExample presellGoodsExample = new PresellGoodsExample();
        presellGoodsExample.setPresellGoodsIdNotEquals(0);
        presellGoodsExample.setPresellGoodsIdIn("presellGoodsIdIn");
        presellGoodsExample.setPresellGoodsId(0);
        presellGoodsExample.setPresellId(0);
        presellGoodsExample.setProductId(0L);
//        when(mockPresellGoodsFeignClient.getPresellGoodsList(presellGoodsExample)).thenReturn(Collections.emptyList());

        // Run the test
        final BigDecimal result = orderProductHelperUnderTest.getPreSellPrice(0, 0L);

        // Verify the results
//        assertEquals(new BigDecimal("0.00"), result);
    }

    @Test
    public void testGetLadderGroupPrice122() {
        // Setup
        // Configure LadderGroupGoodsFeignClient.getLadderGroupGoodsList(...).
        final LadderGroupGoodsVO ladderGroupGoodsVO = new LadderGroupGoodsVO();
        ladderGroupGoodsVO.setGroupGoodsId(0);
        ladderGroupGoodsVO.setGroupId(0);
        ladderGroupGoodsVO.setGoodsId(0L);
        ladderGroupGoodsVO.setGoodsName("goodsName");
        ladderGroupGoodsVO.setAdvanceDeposit(new BigDecimal("0.00"));
        final List<LadderGroupGoodsVO> ladderGroupGoodsVOS = Arrays.asList(ladderGroupGoodsVO);
        final LadderGroupGoods ladderGroupGoods = new LadderGroupGoods();
        ladderGroupGoods.setGroupGoodsId(0);
        ladderGroupGoods.setGroupId(0);
        ladderGroupGoods.setGoodsId(0L);
        ladderGroupGoods.setGoodsName("goodsName");
        ladderGroupGoods.setProductId(0L);
//        when(mockLadderGroupGoodsFeignClient.getLadderGroupGoodsList(ladderGroupGoods)).thenReturn(ladderGroupGoodsVOS);

        // Run the test
        final BigDecimal result = orderProductHelperUnderTest.getLadderGroupPrice(0, 0L);

        // Verify the results
//        assertEquals(new BigDecimal("0.00"), result);
    }

    @Test
    public void testGetLadderGroupPrice_LadderGroupGoodsFeignClientReturnsNoItems223() {
        // Setup
        // Configure LadderGroupGoodsFeignClient.getLadderGroupGoodsList(...).
        final LadderGroupGoods ladderGroupGoods = new LadderGroupGoods();
        ladderGroupGoods.setGroupGoodsId(0);
        ladderGroupGoods.setGroupId(0);
        ladderGroupGoods.setGoodsId(0L);
        ladderGroupGoods.setGoodsName("goodsName");
        ladderGroupGoods.setProductId(0L);
//        when(mockLadderGroupGoodsFeignClient.getLadderGroupGoodsList(ladderGroupGoods))
//                .thenReturn(Collections.emptyList());

        // Run the test
        final BigDecimal result = orderProductHelperUnderTest.getLadderGroupPrice(0, 0L);

        // Verify the results
//        assertEquals(new BigDecimal("0.00"), result);
    }

    @Test
    public void testGetSpellGroupPrice() {
        // Setup
        // Configure SpellGoodsFeignClient.getSpellGoodsList(...).
        final SpellGoodsVO spellGoodsVO = new SpellGoodsVO();
        spellGoodsVO.setSpellGoodsId(0);
        spellGoodsVO.setSpellId(0);
        spellGoodsVO.setGoodsId(0L);
        spellGoodsVO.setSpellPrice(new BigDecimal("0.00"));
        spellGoodsVO.setLeaderPrice(new BigDecimal("0.00"));
        final List<SpellGoodsVO> spellGoodsVOS = Arrays.asList(spellGoodsVO);
        final SpellGoodsExample spellGoodsExample = new SpellGoodsExample();
        spellGoodsExample.setSpellGoodsIdNotEquals(0);
        spellGoodsExample.setSpellGoodsIdIn("spellGoodsIdIn");
        spellGoodsExample.setSpellGoodsId(0);
        spellGoodsExample.setSpellId(0);
        spellGoodsExample.setProductId(0L);
//        when(mockSpellGoodsFeignClient.getSpellGoodsList(spellGoodsExample)).thenReturn(spellGoodsVOS);

        // Run the test
        final BigDecimal result = orderProductHelperUnderTest.getSpellGroupPrice(0, 0L, 0L);

        // Verify the results
//        assertEquals(new BigDecimal("0.00"), result);
    }

    @Test
    public void testGetSpellGroupPrice_SpellGoodsFeignClientReturnsNoItems() {
        // Setup
        // Configure SpellGoodsFeignClient.getSpellGoodsList(...).
        final SpellGoodsExample spellGoodsExample = new SpellGoodsExample();
        spellGoodsExample.setSpellGoodsIdNotEquals(0);
        spellGoodsExample.setSpellGoodsIdIn("spellGoodsIdIn");
        spellGoodsExample.setSpellGoodsId(0);
        spellGoodsExample.setSpellId(0);
        spellGoodsExample.setProductId(0L);
//        when(mockSpellGoodsFeignClient.getSpellGoodsList(spellGoodsExample)).thenReturn(Collections.emptyList());

        // Run the test
        final BigDecimal result = orderProductHelperUnderTest.getSpellGroupPrice(0, 0L, 0L);

        // Verify the results
//        assertEquals(new BigDecimal("0.00"), result);
    }

    @Test
    public void testGetPreSellPrice() {
        // Setup
        // Configure PresellFeignClient.getPresellByPresellId(...).
        final PreSellVO preSellVO = new PreSellVO();
        preSellVO.setPresellId(0);
        preSellVO.setPresellName("presellName");
        preSellVO.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        preSellVO.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        preSellVO.setType(0);
        when(mockPresellFeignClient.getPresellByPresellId(0)).thenReturn(preSellVO);

        // Configure PresellGoodsFeignClient.getPresellGoodsList(...).
        final PresellGoodsVO presellGoodsVO = new PresellGoodsVO();
        presellGoodsVO.setPresellGoodsId(0);
        presellGoodsVO.setPresellId(0);
        presellGoodsVO.setProductId(0L);
        presellGoodsVO.setPresellPrice(new BigDecimal("0.00"));
        presellGoodsVO.setFirstMoney(new BigDecimal("0.00"));
        final List<PresellGoodsVO> presellGoodsVOS = Arrays.asList(presellGoodsVO);
        final PresellGoodsExample presellGoodsExample = new PresellGoodsExample();
        presellGoodsExample.setPresellGoodsIdNotEquals(0);
        presellGoodsExample.setPresellGoodsIdIn("presellGoodsIdIn");
        presellGoodsExample.setPresellGoodsId(0);
        presellGoodsExample.setPresellId(0);
        presellGoodsExample.setProductId(0L);
//        when(mockPresellGoodsFeignClient.getPresellGoodsList(presellGoodsExample)).thenReturn(presellGoodsVOS);

        // Run the test
        final BigDecimal result = orderProductHelperUnderTest.getPreSellPrice(0, 0L);

        // Verify the results
//        assertEquals(new BigDecimal("0.00"), result);
    }

    @Test
    public void testGetPreSellPrice_PresellGoodsFeignClientReturnsNoItems() {
        // Setup
        // Configure PresellFeignClient.getPresellByPresellId(...).
        final PreSellVO preSellVO = new PreSellVO();
        preSellVO.setPresellId(0);
        preSellVO.setPresellName("presellName");
        preSellVO.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        preSellVO.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        preSellVO.setType(0);
        when(mockPresellFeignClient.getPresellByPresellId(0)).thenReturn(preSellVO);

        // Configure PresellGoodsFeignClient.getPresellGoodsList(...).
        final PresellGoodsExample presellGoodsExample = new PresellGoodsExample();
        presellGoodsExample.setPresellGoodsIdNotEquals(0);
        presellGoodsExample.setPresellGoodsIdIn("presellGoodsIdIn");
        presellGoodsExample.setPresellGoodsId(0);
        presellGoodsExample.setPresellId(0);
        presellGoodsExample.setProductId(0L);
//        when(mockPresellGoodsFeignClient.getPresellGoodsList(presellGoodsExample)).thenReturn(Collections.emptyList());

        // Run the test
        final BigDecimal result = orderProductHelperUnderTest.getPreSellPrice(0, 0L);

        // Verify the results
//        assertEquals(new BigDecimal("0.00"), result);
    }

    @Test
    public void testGetLadderGroupPrice() {
        // Setup
        // Configure LadderGroupGoodsFeignClient.getLadderGroupGoodsList(...).
        final LadderGroupGoodsVO ladderGroupGoodsVO = new LadderGroupGoodsVO();
        ladderGroupGoodsVO.setGroupGoodsId(0);
        ladderGroupGoodsVO.setGroupId(0);
        ladderGroupGoodsVO.setGoodsId(0L);
        ladderGroupGoodsVO.setGoodsName("goodsName");
        ladderGroupGoodsVO.setAdvanceDeposit(new BigDecimal("0.00"));
        final List<LadderGroupGoodsVO> ladderGroupGoodsVOS = Arrays.asList(ladderGroupGoodsVO);
        final LadderGroupGoods ladderGroupGoods = new LadderGroupGoods();
        ladderGroupGoods.setGroupGoodsId(0);
        ladderGroupGoods.setGroupId(0);
        ladderGroupGoods.setGoodsId(0L);
        ladderGroupGoods.setGoodsName("goodsName");
        ladderGroupGoods.setProductId(0L);
//        when(mockLadderGroupGoodsFeignClient.getLadderGroupGoodsList(ladderGroupGoods)).thenReturn(ladderGroupGoodsVOS);

        // Run the test
        final BigDecimal result = orderProductHelperUnderTest.getLadderGroupPrice(0, 0L);

        // Verify the results
//        assertEquals(new BigDecimal("0.00"), result);
    }

    @Test
    public void testGetLadderGroupPrice_LadderGroupGoodsFeignClientReturnsNoItems() {
        // Setup
        // Configure LadderGroupGoodsFeignClient.getLadderGroupGoodsList(...).
        final LadderGroupGoods ladderGroupGoods = new LadderGroupGoods();
        ladderGroupGoods.setGroupGoodsId(0);
        ladderGroupGoods.setGroupId(0);
        ladderGroupGoods.setGoodsId(0L);
        ladderGroupGoods.setGoodsName("goodsName");
        ladderGroupGoods.setProductId(0L);
//        when(mockLadderGroupGoodsFeignClient.getLadderGroupGoodsList(ladderGroupGoods))
//                .thenReturn(Collections.emptyList());

        // Run the test
        final BigDecimal result = orderProductHelperUnderTest.getLadderGroupPrice(0, 0L);

        // Verify the results
//        assertEquals(new BigDecimal("0.00"), result);
    }
}
