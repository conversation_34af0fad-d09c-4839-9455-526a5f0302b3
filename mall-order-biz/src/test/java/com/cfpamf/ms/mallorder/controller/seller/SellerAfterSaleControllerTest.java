//package com.cfpamf.ms.mallorder.controller.seller;
//
//import com.cfpamf.ms.mallorder.StubIntergration;
//import com.slodon.bbc.core.response.JsonResult;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//
//import static org.junit.jupiter.api.Assertions.*;
//
//class SellerAfterSaleControllerTest extends StubIntergration {
//
//    @Autowired
//    private SellerAfterSaleController afterSaleController;
//
//    @Test
//    void audit() {
//        //String afsSn, boolean isPass, String remark, Integer storeAddressId, String refuseReason
//        JsonResult result = afterSaleController.audit(null,"6621072901290308", true, "UT", null, null);
//        System.out.println(result);
//    }
//}