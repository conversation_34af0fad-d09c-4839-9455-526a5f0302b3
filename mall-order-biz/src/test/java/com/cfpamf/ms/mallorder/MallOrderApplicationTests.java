package com.cfpamf.ms.mallorder;

import com.slodon.bbc.starter.mq.SlodonMqAutoConfig;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.netflix.hystrix.EnableHystrix;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;
import springfox.documentation.swagger2.annotations.EnableSwagger2;


@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class,
		DataSourceTransactionManagerAutoConfiguration.class, SlodonMqAutoConfig.class})
@EnableSwagger2
@EnableRetry
@ComponentScan(basePackages = {
		"com.slodon.bbc.core",
		"com.cfpamf.smartid",
		"com.cfpamf.ms.mallorder.**",
		"com.cfpamf.ms.mall.filecenter"})
@EnableFeignClients(basePackages = {
		"com.cfpamf.mallpayment.facade.api",
		"com.cfpamf.ms.**.api",
		"com.cfpamf.dts.biz.api.channel",
		"com.cdfinance.ms.**.api",
		"com.cfpamf.ms.mallorder.**.facade",
		"com.cfpamf.ms.**.facade",
		"com.cfpamf.ms.mall.filecenter.facade"
})
@EnableDiscoveryClient
@EnableTransactionManagement
@EnableHystrix
@MapperScan(basePackages = "com.cfpamf.ms.mallorder.mapper")
@Slf4j
public class MallOrderApplicationTests extends WebMvcConfigurerAdapter {

}
