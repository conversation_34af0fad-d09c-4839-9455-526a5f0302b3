package com.cfpamf.ms.mallorder.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cfpamf.common.ms.result.CommonError;
import com.cfpamf.common.ms.result.ErrorContext;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.mallorder.common.enums.OrderCreateChannel;
import com.cfpamf.ms.mallorder.dto.ErpDeliveryDTO;
import com.cfpamf.ms.mallorder.dto.OrderPerformanceDeliveryDTO;
import com.cfpamf.ms.mallorder.dto.OrderPerformanceReceiptDTO;
import com.cfpamf.ms.mallorder.integration.facade.ERPFacade;
import com.cfpamf.ms.mallorder.integration.facade.dto.ErpDepotStockQuery;
import com.cfpamf.ms.mallorder.integration.facade.dto.ErpDepotVO;
import com.cfpamf.ms.mallorder.model.OrderLogModel;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.req.OrderDeliveryReq;
import com.cfpamf.ms.mallorder.service.IOrderLogisticService;
import com.cfpamf.ms.mallorder.service.IOrderService;
import com.cfpamf.ms.mallshop.resp.Vendor;
import com.slodon.bbc.core.exception.BusinessException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderExternalPerformanceServiceImplTest {

    @Mock
    private IOrderService mockOrderService;
    @Mock
    private OrderModel mockOrderModel;
    @Mock
    private OrderLogModel mockOrderLogModel;
    @Mock
    private ERPFacade mockErpFacade;
    @Mock
    private IOrderLogisticService mockOrderLogisticService;

    @InjectMocks
    private OrderExternalPerformanceServiceImpl orderExternalPerformanceServiceImplUnderTest;

    @Test
    void testExtDeliver() {
        // Setup
        final OrderPerformanceDeliveryDTO orderPerformanceDeliveryDTO = new OrderPerformanceDeliveryDTO();
        orderPerformanceDeliveryDTO.setOrderSn("orderSn");
        orderPerformanceDeliveryDTO.setProductIds(Arrays.asList(0L));
        orderPerformanceDeliveryDTO.setDeliverType(0);
        orderPerformanceDeliveryDTO.setExpressNumber("expressNumber");
        orderPerformanceDeliveryDTO.setExpressCompanyName("expressName");
        orderPerformanceDeliveryDTO.setExpressCompanyCode("expressCompanyCode");
        orderPerformanceDeliveryDTO.setChannel("channel");
        orderPerformanceDeliveryDTO.setDeliverName("deliverName");
        orderPerformanceDeliveryDTO.setDeliverMobile("deliverMobile");
        orderPerformanceDeliveryDTO.setDeliveryWarehouse("deliveryWarehouse");
        orderPerformanceDeliveryDTO.setDeliveryWarehouseName("deliveryWarehouseName");

        // Run the test
        orderExternalPerformanceServiceImplUnderTest.extDeliver(orderPerformanceDeliveryDTO);

        // Verify the results
        // Confirm IOrderService.deliveryV2(...).
        final Vendor vendor = new Vendor();
        vendor.setVendorId(0L);
        vendor.setVendorName("vendorName");
        vendor.setVendorPassword("vendorPassword");
        vendor.setVendorMobile("vendorMobile");
        vendor.setVendorEmail("vendorEmail");
        verify(mockOrderService).deliveryV2(OrderDeliveryReq.builder()
                .orderSn("orderSn")
                .productIds(Arrays.asList(0L))
                .deliverType(0)
                .expressName("expressName")
                .expressCompanyCode("expressCompanyCode")
                .expressNumber("expressNumber")
                .deliverName("deliverName")
                .deliverMobile("deliverMobile")
                .allowNoLogistics(false)
                .channel(OrderCreateChannel.H5)
                .deliveryWarehouse("deliveryWarehouse")
                .deliveryWarehouseName("deliveryWarehouseName")
                .build(), vendor);
    }

    @Test
    void testExtReceipt() {
        // Setup
        final OrderPerformanceReceiptDTO orderPerformanceReceiptDTO = new OrderPerformanceReceiptDTO();
        orderPerformanceReceiptDTO.setOrderSn("orderSn");
        orderPerformanceReceiptDTO.setChannel("channel");
        orderPerformanceReceiptDTO.setReceiptRole("receiptRole");
        orderPerformanceReceiptDTO.setReceiptId("receiptId");
        orderPerformanceReceiptDTO.setReceiptName("receiptName");

        // Configure OrderModel.getOrderByOrderSn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("orderSn");
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setLockState(0);
        when(mockOrderModel.getOrderByOrderSn("orderSn")).thenReturn(orderPO);

        // Run the test
        final boolean result = orderExternalPerformanceServiceImplUnderTest.extReceipt(orderPerformanceReceiptDTO);

        // Verify the results
        assertThat(result).isFalse();

        // Confirm OrderModel.receiveOrder(...).
        final OrderPO orderPODb = new OrderPO();
        orderPODb.setOrderId(0);
        orderPODb.setOrderSn("orderSn");
        orderPODb.setOrderState(0);
        orderPODb.setLoanPayState(0);
        orderPODb.setLockState(0);
        verify(mockOrderModel).receiveOrder(orderPODb, 0, 0L, "optUserName", "会员确认收货", OrderCreateChannel.H5);
    }

    @Test
    void testGetDepotListWithResult() {
        // Setup
        final ErpDepotVO erpDepotVO = new ErpDepotVO();
        erpDepotVO.setId(0L);
        erpDepotVO.setName("name");
        erpDepotVO.setDeptCode("deptCode");
        erpDepotVO.setStockType(0);
        erpDepotVO.setStockTypeName("stockTypeName");
        final List<ErpDepotVO> expectedResult = Arrays.asList(erpDepotVO);

        // Configure ERPFacade.getDepotStockList(...).
        final ErrorContext errorContext = new ErrorContext();
        final CommonError commonError = new CommonError();
        commonError.setErrorCode("errorCode");
        commonError.setErrorMsg("errorMsg");
        commonError.setLocation("location");
        commonError.setThrowable(new Exception("message"));
        errorContext.setErrorStack(Arrays.asList(commonError));
        final ErpDepotVO erpDepotVO1 = new ErpDepotVO();
        erpDepotVO1.setId(0L);
        erpDepotVO1.setName("name");
        erpDepotVO1.setDeptCode("deptCode");
        erpDepotVO1.setStockType(0);
        erpDepotVO1.setStockTypeName("stockTypeName");
        final Result<List<ErpDepotVO>> listResult = new Result<>(false, errorContext, Arrays.asList(erpDepotVO1));
        final ErpDepotStockQuery query = new ErpDepotStockQuery();
        query.setStoreId(0L);
        query.setBizSource("norm_mall");
        query.setTenantId(0L);
        query.setTypeList(Arrays.asList(0));
        query.setSkuIdList(Arrays.asList("value"));
        when(mockErpFacade.getDepotStockList(query)).thenReturn(listResult);

        // Run the test
        final List<ErpDepotVO> result = orderExternalPerformanceServiceImplUnderTest.getDepotListWithResult(0L,
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetDepotListWithResult_ERPFacadeReturnsNull() {
        // Setup
        // Configure ERPFacade.getDepotStockList(...).
        final ErpDepotStockQuery query = new ErpDepotStockQuery();
        query.setStoreId(0L);
        query.setBizSource("norm_mall");
        query.setTenantId(0L);
        query.setTypeList(Arrays.asList(0));
        query.setSkuIdList(Arrays.asList("value"));
        when(mockErpFacade.getDepotStockList(query)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> orderExternalPerformanceServiceImplUnderTest.getDepotListWithResult(0L,
                Arrays.asList("value"))).isInstanceOf(BusinessException.class);
    }

    @Test
    void testGetDepotListWithResult_ERPFacadeReturnsNoItems() {
        // Setup
        // Configure ERPFacade.getDepotStockList(...).
        final ErpDepotStockQuery query = new ErpDepotStockQuery();
        query.setStoreId(0L);
        query.setBizSource("norm_mall");
        query.setTenantId(0L);
        query.setTypeList(Arrays.asList(0));
        query.setSkuIdList(Arrays.asList("value"));
        when(mockErpFacade.getDepotStockList(query)).thenReturn(Result.ok(Collections.emptyList()));

        // Run the test
        final List<ErpDepotVO> result = orderExternalPerformanceServiceImplUnderTest.getDepotListWithResult(0L,
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testUpdateDeliveryInfo() {
        // Setup
        final ErpDeliveryDTO erpDeliveryDTO = new ErpDeliveryDTO();
        erpDeliveryDTO.setOrderSn("orderSn");
        erpDeliveryDTO.setChannel("channel");
        erpDeliveryDTO.setOptName("optName");
        erpDeliveryDTO.setOptId("optId");
        erpDeliveryDTO.setOrgExpressNubmer("orgExpressNubmer");
        erpDeliveryDTO.setExpressNumber("expressNumber");
        erpDeliveryDTO.setExpressName("expressName");
        erpDeliveryDTO.setExpressCompanyCode("expressCompanyCode");

        // Configure OrderModel.getOrderByOrderSn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("orderSn");
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setLockState(0);
        when(mockOrderModel.getOrderByOrderSn("orderSn")).thenReturn(orderPO);

        // Run the test
        final boolean result = orderExternalPerformanceServiceImplUnderTest.updateDeliveryInfo(erpDeliveryDTO);

        // Verify the results
        assertThat(result).isFalse();
        verify(mockOrderLogisticService).update(any(LambdaUpdateWrapper.class));
        verify(mockOrderLogModel).insertOrderLog(0, 0L, "optName", "orderSn", 0, 0, 0, "修改快递单号", OrderCreateChannel.H5);
    }
}
