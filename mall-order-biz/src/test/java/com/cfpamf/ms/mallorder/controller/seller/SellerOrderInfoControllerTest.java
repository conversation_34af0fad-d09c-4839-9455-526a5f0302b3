//package com.cfpamf.ms.mallorder.controller.seller;
//
//
//import com.cfpamf.ms.mallorder.StubIntergration;
//import com.cfpamf.ms.mallorder.service.IOrderService;
//import com.cfpamf.ms.mallshop.resp.Vendor;
//import org.junit.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//
//public class SellerOrderInfoControllerTest extends StubIntergration {
//
//
//    @Autowired
//    private IOrderService orderService;
//
//    @Test
//    public void deliver() {
//
//        Vendor vendor = new Vendor();
//        vendor.setStoreId(1810011L);
//        vendor.setVendorId(1810011L);
//        vendor.setVendorName("UTTTT");
//        orderService.delivery ("8221091403651408", 1, null,
//                null, "UT1", "19988886666", vendor);
//
//    }
//}