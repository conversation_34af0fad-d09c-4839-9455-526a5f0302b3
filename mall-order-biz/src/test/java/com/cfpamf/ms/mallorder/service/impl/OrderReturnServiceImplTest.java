package com.cfpamf.ms.mallorder.service.impl;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cfpamf.ms.mallorder.po.OrderAfterPO;
import com.cfpamf.ms.mallorder.po.OrderProductPO;
import com.cfpamf.ms.mallorder.po.OrderReturnPO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Collections;


@RunWith(SpringJUnit4ClassRunner.class)
public class OrderReturnServiceImplTest {

    @InjectMocks
    OrderReturnServiceImpl orderReturnService;

    @Mock
    OrderAfterServiceImpl orderAfterService;

    @Mock
    OrderProductServiceImpl orderProductService;

    @Mock
    BaseMapper<OrderReturnPO> baseMapper;

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void isAllReturned() {

        long orderProductId = 100;

        OrderReturnPO returnPO = new OrderReturnPO();
        returnPO.setReturnId(0);
        returnPO.setAfsSn("1");
        returnPO.setState(200);

        OrderAfterPO afterPO = new OrderAfterPO();
        afterPO.setOrderProductId(orderProductId);
        afterPO.setAfsNum(10);

        OrderProductPO productPO = new OrderProductPO();
        productPO.setOrderProductId(orderProductId);
        productPO.setProductNum(10);


        Mockito.when(baseMapper.selectOne(Mockito.any())).thenReturn(returnPO);

        Mockito.when(baseMapper.selectList(Mockito.any())).thenReturn(Collections.singletonList(returnPO));

        Mockito.when(orderAfterService.list(Mockito.any())).thenReturn(Collections.singletonList(afterPO));

        Mockito.when(orderProductService.list(Mockito.any())).thenReturn(Collections.singletonList(productPO));


        Assert.assertTrue(orderReturnService.isAllReturnedFinish("8221073002230501"));
    }

    @Test
    public void hasDuringRefund() {

        OrderReturnPO returnPO = new OrderReturnPO();
        returnPO.setReturnId(0);
        returnPO.setAfsSn("1");

        Mockito.when(baseMapper.selectList(Mockito.any()))
                .thenReturn(Collections.singletonList(returnPO));

        Assert.assertTrue(orderReturnService.hasDuringRefund(""));
    }
}