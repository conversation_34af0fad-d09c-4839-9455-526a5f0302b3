package com.cfpamf.ms.mallorder.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cdfinance.hrms.facade.vo.BranchRelationVO;
import com.cfpamf.common.ms.result.CommonError;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.bms.facade.vo.OrganizationBaseVO;
import com.cfpamf.ms.mallgoods.facade.api.ProductFeignClient;
import com.cfpamf.ms.mallgoods.facade.vo.Product;
import com.cfpamf.ms.mallorder.common.mq.RabbitMQUtils;
import com.cfpamf.ms.mallorder.common.mq.msg.GoodNewsMessage;
import com.cfpamf.ms.mallorder.controller.fegin.facade.DbcServiceFeign;
import com.cfpamf.ms.mallorder.controller.fegin.facade.loan.ScrmFacade;
import com.cfpamf.ms.mallorder.controller.fegin.facade.loan.WecatFacade;
import com.cfpamf.ms.mallorder.dto.datapush.OrderArgicMessageDTO;
import com.cfpamf.ms.mallorder.dto.loan.*;
import com.cfpamf.ms.mallorder.integration.bms.BmsOrgFeignIntegration;
import com.cfpamf.ms.mallorder.integration.erp.ERPIntegration;
import com.cfpamf.ms.mallorder.integration.facade.dto.UnitConvertVo;
import com.cfpamf.ms.mallorder.integration.hrms.HrmsIntegration;
import com.cfpamf.ms.mallorder.integration.system.TradeDocumentIntegration;
import com.cfpamf.ms.mallorder.mapper.OrderProductMapper;
import com.cfpamf.ms.mallorder.po.BzOrderProductCombinationPO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderPerformanceBelongsPO;
import com.cfpamf.ms.mallorder.po.OrderProductPO;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.service.impl.OrderDataPushServiceImpl;
import com.cfpamf.ms.mallorder.vo.DbcDistributionSaleOrderVO;
import com.cfpamf.ms.mallorder.vo.FreightCostItemVO;
import com.cfpamf.ms.mallorder.vo.ManagerCommissionItemVO;
import com.cfpamf.ms.mallsystem.request.OrderTradeMatchDTO;
import com.cfpamf.ms.mallsystem.vo.OrderDataPushHitObject;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OrderDataPushServiceImplTest {

    @Mock
    private IOrderService mockOrderService;
    @Mock
    private IOrderExtendService mockOrderExtendService;
    @Mock
    private IOrderProductService mockOrderProductService;
    @Mock
    private OrderProductMapper mockOrderProductMapper;
    @Mock
    private ProductFeignClient mockProductFeignClient;
    @Mock
    private TradeDocumentIntegration mockTradeDocumentIntegration;
    @Mock
    private ERPIntegration mockErpIntegration;
    @Mock
    private HrmsIntegration mockHrmsIntegration;
    @Mock
    private IOrderAmountStateRecordService mockOrderAmountStateRecordService;
    @Mock
    private ICommonMqEventService mockCommonMqEventService;
    @Mock
    private DbcServiceFeign mockDbcServiceFeign;
    @Mock
    private IOrderPerformanceBelongsService mockOrderPerformanceBelongsService;
    @Mock
    private RabbitMQUtils mockRabbitMQUtils;
    @Mock
    private WecatFacade mockWecatFacade;
    @Mock
    private ScrmFacade mockScrmFacade;
    @Mock
    private IBzOrderProductCombinationService mockOrderProductCombinationService;
    @Mock
    private BmsOrgFeignIntegration mockBmsOrgFeignIntegration;

    @InjectMocks
    private OrderDataPushServiceImpl orderDataPushServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        ReflectionTestUtils.setField(orderDataPushServiceImplUnderTest, "goodNewsProductIds",
                new String[]{"goodNewsProductIds"});
    }

    @Test
    public void testPaySuccessDataPush() {
        // Setup
        // Configure IOrderService.getOne(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderSn("orderSn");
        orderPO.setUserMobile("userMobile");
        orderPO.setStoreId(0L);
        orderPO.setMemberName("customerName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setOrderType(0);
        orderPO.setOrderPattern(0);
        orderPO.setEnabledFlag(0);
        orderPO.setChannel("channel");
        orderPO.setPerformanceModes("performanceModes");
//        when(mockOrderService.getOne(any(LambdaQueryWrapper.class))).thenReturn(orderPO);

        // Configure IOrderPerformanceBelongsService.getOne(...).
        final OrderPerformanceBelongsPO orderPerformanceBelongsPO = new OrderPerformanceBelongsPO();
        orderPerformanceBelongsPO.setOrderSn("orderSn");
        orderPerformanceBelongsPO.setBelongerName("belongerName");
        orderPerformanceBelongsPO.setBelongerEmployeeNo("belongerEmployeeNo");
        orderPerformanceBelongsPO.setEmployeeBranchCode("branch");
        orderPerformanceBelongsPO.setEmployeeBranchName("branchName");
        orderPerformanceBelongsPO.setBindStateCode(0);
//        when(mockOrderPerformanceBelongsService.getOne(any(LambdaQueryWrapper.class)))
//                .thenReturn(orderPerformanceBelongsPO);

        // Configure HrmsIntegration.getBranchRelation(...).
        final BranchRelationVO branchRelationVO = new BranchRelationVO();
        branchRelationVO.setBranchCode("branchCode");
        branchRelationVO.setZoneName("zoneName");
        branchRelationVO.setZoneCode("zoneCode");
        branchRelationVO.setRegionCode("areaCode");
        branchRelationVO.setRegionName("areaName");
//        when(mockHrmsIntegration.getBranchRelation("branch")).thenReturn(branchRelationVO);

        // Configure IOrderProductService.list(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setGoodsCategoryPath("goodsCategoryPath");
        orderProductPO.setProductId(0L);
        orderProductPO.setWeight(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO.setSkuMaterialName("skuMaterialName");
        orderProductPO.setChannelSkuUnit("channelSkuUnit");
        orderProductPO.setChannelSkuId("channelSkuId");
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO);
//        when(mockOrderProductService.list(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure ProductFeignClient.getProductListByProductIds(...).
        final Product product = new Product();
        product.setProductId(0L);
        product.setGoodsId(0L);
        product.setCategoryId1(0);
        product.setCategoryId2(0);
        product.setCategoryId3(0);
        final List<Product> products = Arrays.asList(product);
//        when(mockProductFeignClient.getProductListByProductIds(Arrays.asList(0L))).thenReturn(products);

        // Configure ScrmFacade.getJobNumberFile(...).
        final WechatResult<Boolean> booleanWechatResult = new WechatResult<>();
        booleanWechatResult.setSuccess(false);
        final ErrorContext errorContext = new ErrorContext();
        final ErrorStack errorStack = new ErrorStack();
        errorStack.setErrorCode("errorCode");
        errorContext.setErrorStack(Arrays.asList(errorStack));
        booleanWechatResult.setErrorContext(errorContext);
        booleanWechatResult.setData(false);
//        when(mockScrmFacade.getJobNumberFile("userMobile")).thenReturn(booleanWechatResult);

        // Configure WecatFacade.getUserInfoByUserIdList(...).
        final WechatResult<List<WechatUserInfoPO>> listWechatResult = new WechatResult<>();
        listWechatResult.setSuccess(false);
        final ErrorContext errorContext1 = new ErrorContext();
        final ErrorStack errorStack1 = new ErrorStack();
        errorContext1.setErrorStack(Arrays.asList(errorStack1));
        listWechatResult.setErrorContext(errorContext1);
        final WechatUserInfoPO wechatUserInfoPO = new WechatUserInfoPO();
        wechatUserInfoPO.setAvatar("avatar");
        listWechatResult.setData(Arrays.asList(wechatUserInfoPO));
        final WechatUserInfoRequest var1 = new WechatUserInfoRequest();
        var1.setWechatUserIdList(Arrays.asList("value"));
//        when(mockWecatFacade.getUserInfoByUserIdList(var1)).thenReturn(listWechatResult);

        // Configure TradeDocumentIntegration.orderDataPushMatch(...).
        final OrderDataPushHitObject orderDataPushHitObject = new OrderDataPushHitObject();
        orderDataPushHitObject.setDesc("desc");
        orderDataPushHitObject.setCode("code");
        orderDataPushHitObject.setValue(0.0);
        orderDataPushHitObject.setReason("reason");
        final List<OrderDataPushHitObject> orderDataPushHitObjects = Arrays.asList(orderDataPushHitObject);
        final OrderTradeMatchDTO orderTradeMatchDTO = new OrderTradeMatchDTO();
        orderTradeMatchDTO.setStoreId("storeId");
        orderTradeMatchDTO.setOrderPattern(0);
        orderTradeMatchDTO.setPerformanceModes("performanceModes");
        orderTradeMatchDTO.setOrderAmount(new BigDecimal("0.00"));
        orderTradeMatchDTO.setGoodsCategoryId1("categoryId1");
        orderTradeMatchDTO.setGoodsCategoryId2("categoryId2");
        orderTradeMatchDTO.setGoodsCategoryId3("categoryId3");
        orderTradeMatchDTO.setPaymentCode("paymentCode");
        orderTradeMatchDTO.setBranchCode("branch");
        orderTradeMatchDTO.setZoneCode("zoneCode");
        orderTradeMatchDTO.setAreaCode("areaCode");
        orderTradeMatchDTO.setTonWeight(new BigDecimal("0.00"));
        orderTradeMatchDTO.setIsAgricMcnUser(0);
        orderTradeMatchDTO.setUserTag("userTag");
        orderTradeMatchDTO.setChannel("channel");
        orderTradeMatchDTO.setGoodsId(0L);
        orderTradeMatchDTO.setProductId(0L);
//        when(mockTradeDocumentIntegration.orderDataPushMatch(orderTradeMatchDTO)).thenReturn(orderDataPushHitObjects);

        // Configure BmsOrgFeignIntegration.listBranchesByHrOrgCodesV2(...).
        final OrganizationBaseVO organizationBaseVO = new OrganizationBaseVO();
        organizationBaseVO.setBatchNo("batchNo");
        organizationBaseVO.setOrgId(0);
        organizationBaseVO.setHrOrgId(0);
        organizationBaseVO.setOrgCode("orgCode");
        organizationBaseVO.setOrgName("orgName");
        final List<OrganizationBaseVO> organizationBaseVOS = Arrays.asList(organizationBaseVO);
//        when(mockBmsOrgFeignIntegration.listBranchesByHrOrgCodesV2("areaCode")).thenReturn(organizationBaseVOS);

//        when(mockOrderProductMapper.countPayOrderNum("orderSn", Arrays.asList("value"),
//                Arrays.asList("value"))).thenReturn(0);

        // Configure IBzOrderProductCombinationService.getOne(...).
        final BzOrderProductCombinationPO bzOrderProductCombinationPO = BzOrderProductCombinationPO.builder()
                .orderSn("orderSn")
                .mainProductId(0L)
                .build();
//        when(mockOrderProductCombinationService.getOne(any(LambdaQueryWrapper.class)))
//                .thenReturn(bzOrderProductCombinationPO);

        // Configure ERPIntegration.unitConvert(...).
        final UnitConvertVo unitConvertVo = new UnitConvertVo();
        unitConvertVo.setTagId("tagId");
        unitConvertVo.setBatchNo("batchNo");
        unitConvertVo.setSkuId("skuId");
        unitConvertVo.setPackageNum(new BigDecimal("0.00"));
        unitConvertVo.setUnitName("套");
        final List<UnitConvertVo> unitConvertVos = Arrays.asList(unitConvertVo);
//        when(mockErpIntegration.unitConvert(Arrays.asList(new HashMap<>()))).thenReturn(unitConvertVos);

        // Configure DbcServiceFeign.managerCommissionByCommodities(...).
        final com.cfpamf.common.ms.result.ErrorContext errorContext2 = new com.cfpamf.common.ms.result.ErrorContext();
        errorContext2.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext2.setThirdPartyError("thirdPartyError");
        final ManagerCommissionItemVO managerCommissionItemVO = new ManagerCommissionItemVO();
        managerCommissionItemVO.setOrderId("orderId");
        managerCommissionItemVO.setCommodityCode("commodityCode");
        managerCommissionItemVO.setManagerCommission(new BigDecimal("0.00"));
        final Result<List<ManagerCommissionItemVO>> listResult = new Result<>(false, errorContext2,
                Arrays.asList(managerCommissionItemVO));
//        when(mockDbcServiceFeign.managerCommissionByCommodities("orderSn", 5, "storeId")).thenReturn(listResult);

        // Configure DbcServiceFeign.freightCostByCommodities(...).
        final com.cfpamf.common.ms.result.ErrorContext errorContext3 = new com.cfpamf.common.ms.result.ErrorContext();
        errorContext3.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext3.setThirdPartyError("thirdPartyError");
        final FreightCostItemVO freightCostItemVO = new FreightCostItemVO();
        freightCostItemVO.setOrderId("orderId");
        freightCostItemVO.setCommodityCode("commodityCode");
        freightCostItemVO.setFreightCost(new BigDecimal("0.00"));
        final Result<List<FreightCostItemVO>> listResult1 = new Result<>(false, errorContext3,
                Arrays.asList(freightCostItemVO));
//        when(mockDbcServiceFeign.freightCostByCommodities("orderSn", 5, "storeId")).thenReturn(listResult1);

        // Configure DbcServiceFeign.getListByOrderId(...).
        final com.cfpamf.common.ms.result.ErrorContext errorContext4 = new com.cfpamf.common.ms.result.ErrorContext();
        errorContext4.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext4.setThirdPartyError("thirdPartyError");
        final DbcDistributionSaleOrderVO dbcDistributionSaleOrderVO = new DbcDistributionSaleOrderVO();
        dbcDistributionSaleOrderVO.setId(0L);
        dbcDistributionSaleOrderVO.setOrderId("orderId");
        dbcDistributionSaleOrderVO.setOrderCommission(new BigDecimal("0.00"));
        dbcDistributionSaleOrderVO.setCommodityCode("commodityCode");
        dbcDistributionSaleOrderVO.setCommissionOrderType(0);
        final Result<List<DbcDistributionSaleOrderVO>> listResult2 = new Result<>(false, errorContext4,
                Arrays.asList(dbcDistributionSaleOrderVO));
//        when(mockDbcServiceFeign.getListByOrderId("orderSn")).thenReturn(listResult2);

//        when(mockOrderProductMapper.airEnergySalesCount("branch", "areaCode")).thenReturn(0);

        // Configure ICommonMqEventService.saveEvent(...).
        final GoodNewsMessage body = new GoodNewsMessage();
        body.setReportType("reportType");
        body.setBranch("branch");
        body.setMarketingCode("2023MALL_PAY_ORDER");
        body.setReportLevel(0);
        body.setPushParam("pushParam");
        body.setStatisticalObject("belongerEmployeeNo");
        body.setStatisticalTime("orderSn");
        body.setParamAddStatisticalCount(false);
        body.setMessageId("messageId");
        body.setText("text");
//        when(mockCommonMqEventService.saveEvent(body, "agric.performance.goodNews.exchange")).thenReturn(0L);

        // Run the test
//        orderDataPushServiceImplUnderTest.paySuccessDataPush("orderSn");

        // Verify the results
//        verify(mockRabbitMQUtils).sendByEventId(0L);
    }

    @Test
    public void testPaySuccessDataPush_IOrderPerformanceBelongsServiceReturnsNull() {
        // Setup
        // Configure IOrderService.getOne(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderSn("orderSn");
        orderPO.setUserMobile("userMobile");
        orderPO.setStoreId(0L);
        orderPO.setMemberName("customerName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setOrderType(0);
        orderPO.setOrderPattern(0);
        orderPO.setEnabledFlag(0);
        orderPO.setChannel("channel");
        orderPO.setPerformanceModes("performanceModes");
//        when(mockOrderService.getOne(any(LambdaQueryWrapper.class))).thenReturn(orderPO);

//        when(mockOrderPerformanceBelongsService.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
//        orderDataPushServiceImplUnderTest.paySuccessDataPush("orderSn");

        // Verify the results
    }

    @Test
    public void testPaySuccessDataPush_HrmsIntegrationReturnsNull() {
        // Setup
        // Configure IOrderService.getOne(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderSn("orderSn");
        orderPO.setUserMobile("userMobile");
        orderPO.setStoreId(0L);
        orderPO.setMemberName("customerName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setOrderType(0);
        orderPO.setOrderPattern(0);
        orderPO.setEnabledFlag(0);
        orderPO.setChannel("channel");
        orderPO.setPerformanceModes("performanceModes");
//        when(mockOrderService.getOne(any(LambdaQueryWrapper.class))).thenReturn(orderPO);

        // Configure IOrderPerformanceBelongsService.getOne(...).
        final OrderPerformanceBelongsPO orderPerformanceBelongsPO = new OrderPerformanceBelongsPO();
        orderPerformanceBelongsPO.setOrderSn("orderSn");
        orderPerformanceBelongsPO.setBelongerName("belongerName");
        orderPerformanceBelongsPO.setBelongerEmployeeNo("belongerEmployeeNo");
        orderPerformanceBelongsPO.setEmployeeBranchCode("branch");
        orderPerformanceBelongsPO.setEmployeeBranchName("branchName");
        orderPerformanceBelongsPO.setBindStateCode(0);
//        when(mockOrderPerformanceBelongsService.getOne(any(LambdaQueryWrapper.class)))
//                .thenReturn(orderPerformanceBelongsPO);

//        when(mockHrmsIntegration.getBranchRelation("branch")).thenReturn(null);

        // Configure IOrderProductService.list(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setGoodsCategoryPath("goodsCategoryPath");
        orderProductPO.setProductId(0L);
        orderProductPO.setWeight(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO.setSkuMaterialName("skuMaterialName");
        orderProductPO.setChannelSkuUnit("channelSkuUnit");
        orderProductPO.setChannelSkuId("channelSkuId");
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO);
//        when(mockOrderProductService.list(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure ProductFeignClient.getProductListByProductIds(...).
        final Product product = new Product();
        product.setProductId(0L);
        product.setGoodsId(0L);
        product.setCategoryId1(0);
        product.setCategoryId2(0);
        product.setCategoryId3(0);
        final List<Product> products = Arrays.asList(product);
//        when(mockProductFeignClient.getProductListByProductIds(Arrays.asList(0L))).thenReturn(products);

        // Configure ScrmFacade.getJobNumberFile(...).
        final WechatResult<Boolean> booleanWechatResult = new WechatResult<>();
        booleanWechatResult.setSuccess(false);
        final ErrorContext errorContext = new ErrorContext();
        final ErrorStack errorStack = new ErrorStack();
        errorStack.setErrorCode("errorCode");
        errorContext.setErrorStack(Arrays.asList(errorStack));
        booleanWechatResult.setErrorContext(errorContext);
        booleanWechatResult.setData(false);
//        when(mockScrmFacade.getJobNumberFile("userMobile")).thenReturn(booleanWechatResult);

        // Configure WecatFacade.getUserInfoByUserIdList(...).
        final WechatResult<List<WechatUserInfoPO>> listWechatResult = new WechatResult<>();
        listWechatResult.setSuccess(false);
        final ErrorContext errorContext1 = new ErrorContext();
        final ErrorStack errorStack1 = new ErrorStack();
        errorContext1.setErrorStack(Arrays.asList(errorStack1));
        listWechatResult.setErrorContext(errorContext1);
        final WechatUserInfoPO wechatUserInfoPO = new WechatUserInfoPO();
        wechatUserInfoPO.setAvatar("avatar");
        listWechatResult.setData(Arrays.asList(wechatUserInfoPO));
        final WechatUserInfoRequest var1 = new WechatUserInfoRequest();
        var1.setWechatUserIdList(Arrays.asList("value"));
//        when(mockWecatFacade.getUserInfoByUserIdList(var1)).thenReturn(listWechatResult);

        // Configure TradeDocumentIntegration.orderDataPushMatch(...).
        final OrderDataPushHitObject orderDataPushHitObject = new OrderDataPushHitObject();
        orderDataPushHitObject.setDesc("desc");
        orderDataPushHitObject.setCode("code");
        orderDataPushHitObject.setValue(0.0);
        orderDataPushHitObject.setReason("reason");
        final List<OrderDataPushHitObject> orderDataPushHitObjects = Arrays.asList(orderDataPushHitObject);
        final OrderTradeMatchDTO orderTradeMatchDTO = new OrderTradeMatchDTO();
        orderTradeMatchDTO.setStoreId("storeId");
        orderTradeMatchDTO.setOrderPattern(0);
        orderTradeMatchDTO.setPerformanceModes("performanceModes");
        orderTradeMatchDTO.setOrderAmount(new BigDecimal("0.00"));
        orderTradeMatchDTO.setGoodsCategoryId1("categoryId1");
        orderTradeMatchDTO.setGoodsCategoryId2("categoryId2");
        orderTradeMatchDTO.setGoodsCategoryId3("categoryId3");
        orderTradeMatchDTO.setPaymentCode("paymentCode");
        orderTradeMatchDTO.setBranchCode("branch");
        orderTradeMatchDTO.setZoneCode("zoneCode");
        orderTradeMatchDTO.setAreaCode("areaCode");
        orderTradeMatchDTO.setTonWeight(new BigDecimal("0.00"));
        orderTradeMatchDTO.setIsAgricMcnUser(0);
        orderTradeMatchDTO.setUserTag("userTag");
        orderTradeMatchDTO.setChannel("channel");
        orderTradeMatchDTO.setGoodsId(0L);
        orderTradeMatchDTO.setProductId(0L);
//        when(mockTradeDocumentIntegration.orderDataPushMatch(orderTradeMatchDTO)).thenReturn(orderDataPushHitObjects);

        // Configure BmsOrgFeignIntegration.listBranchesByHrOrgCodesV2(...).
        final OrganizationBaseVO organizationBaseVO = new OrganizationBaseVO();
        organizationBaseVO.setBatchNo("batchNo");
        organizationBaseVO.setOrgId(0);
        organizationBaseVO.setHrOrgId(0);
        organizationBaseVO.setOrgCode("orgCode");
        organizationBaseVO.setOrgName("orgName");
        final List<OrganizationBaseVO> organizationBaseVOS = Arrays.asList(organizationBaseVO);
//        when(mockBmsOrgFeignIntegration.listBranchesByHrOrgCodesV2("areaCode")).thenReturn(organizationBaseVOS);

//        when(mockOrderProductMapper.countPayOrderNum("orderSn", Arrays.asList("value"),
//                Arrays.asList("value"))).thenReturn(0);

        // Configure IBzOrderProductCombinationService.getOne(...).
        final BzOrderProductCombinationPO bzOrderProductCombinationPO = BzOrderProductCombinationPO.builder()
                .orderSn("orderSn")
                .mainProductId(0L)
                .build();
//        when(mockOrderProductCombinationService.getOne(any(LambdaQueryWrapper.class)))
//                .thenReturn(bzOrderProductCombinationPO);

        // Configure ERPIntegration.unitConvert(...).
        final UnitConvertVo unitConvertVo = new UnitConvertVo();
        unitConvertVo.setTagId("tagId");
        unitConvertVo.setBatchNo("batchNo");
        unitConvertVo.setSkuId("skuId");
        unitConvertVo.setPackageNum(new BigDecimal("0.00"));
        unitConvertVo.setUnitName("套");
        final List<UnitConvertVo> unitConvertVos = Arrays.asList(unitConvertVo);
//        when(mockErpIntegration.unitConvert(Arrays.asList(new HashMap<>()))).thenReturn(unitConvertVos);

        // Configure DbcServiceFeign.managerCommissionByCommodities(...).
        final com.cfpamf.common.ms.result.ErrorContext errorContext2 = new com.cfpamf.common.ms.result.ErrorContext();
        errorContext2.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext2.setThirdPartyError("thirdPartyError");
        final ManagerCommissionItemVO managerCommissionItemVO = new ManagerCommissionItemVO();
        managerCommissionItemVO.setOrderId("orderId");
        managerCommissionItemVO.setCommodityCode("commodityCode");
        managerCommissionItemVO.setManagerCommission(new BigDecimal("0.00"));
        final Result<List<ManagerCommissionItemVO>> listResult = new Result<>(false, errorContext2,
                Arrays.asList(managerCommissionItemVO));
//        when(mockDbcServiceFeign.managerCommissionByCommodities("orderSn", 5, "storeId")).thenReturn(listResult);

        // Configure DbcServiceFeign.freightCostByCommodities(...).
        final com.cfpamf.common.ms.result.ErrorContext errorContext3 = new com.cfpamf.common.ms.result.ErrorContext();
        errorContext3.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext3.setThirdPartyError("thirdPartyError");
        final FreightCostItemVO freightCostItemVO = new FreightCostItemVO();
        freightCostItemVO.setOrderId("orderId");
        freightCostItemVO.setCommodityCode("commodityCode");
        freightCostItemVO.setFreightCost(new BigDecimal("0.00"));
        final Result<List<FreightCostItemVO>> listResult1 = new Result<>(false, errorContext3,
                Arrays.asList(freightCostItemVO));
//        when(mockDbcServiceFeign.freightCostByCommodities("orderSn", 5, "storeId")).thenReturn(listResult1);

        // Configure DbcServiceFeign.getListByOrderId(...).
        final com.cfpamf.common.ms.result.ErrorContext errorContext4 = new com.cfpamf.common.ms.result.ErrorContext();
        errorContext4.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext4.setThirdPartyError("thirdPartyError");
        final DbcDistributionSaleOrderVO dbcDistributionSaleOrderVO = new DbcDistributionSaleOrderVO();
        dbcDistributionSaleOrderVO.setId(0L);
        dbcDistributionSaleOrderVO.setOrderId("orderId");
        dbcDistributionSaleOrderVO.setOrderCommission(new BigDecimal("0.00"));
        dbcDistributionSaleOrderVO.setCommodityCode("commodityCode");
        dbcDistributionSaleOrderVO.setCommissionOrderType(0);
        final Result<List<DbcDistributionSaleOrderVO>> listResult2 = new Result<>(false, errorContext4,
                Arrays.asList(dbcDistributionSaleOrderVO));
//        when(mockDbcServiceFeign.getListByOrderId("orderSn")).thenReturn(listResult2);

//        when(mockOrderProductMapper.airEnergySalesCount("branch", "areaCode")).thenReturn(0);

        // Configure ICommonMqEventService.saveEvent(...).
        final GoodNewsMessage body = new GoodNewsMessage();
        body.setReportType("reportType");
        body.setBranch("branch");
        body.setMarketingCode("2023MALL_PAY_ORDER");
        body.setReportLevel(0);
        body.setPushParam("pushParam");
        body.setStatisticalObject("belongerEmployeeNo");
        body.setStatisticalTime("orderSn");
        body.setParamAddStatisticalCount(false);
        body.setMessageId("messageId");
        body.setText("text");
//        when(mockCommonMqEventService.saveEvent(body, "agric.performance.goodNews.exchange")).thenReturn(0L);

        // Run the test
//        orderDataPushServiceImplUnderTest.paySuccessDataPush("orderSn");

        // Verify the results
//        verify(mockRabbitMQUtils).sendByEventId(0L);
    }

    @Test
    public void testPaySuccessDataPush_IOrderProductServiceReturnsNoItems() {
        // Setup
        // Configure IOrderService.getOne(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderSn("orderSn");
        orderPO.setUserMobile("userMobile");
        orderPO.setStoreId(0L);
        orderPO.setMemberName("customerName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setOrderType(0);
        orderPO.setOrderPattern(0);
        orderPO.setEnabledFlag(0);
        orderPO.setChannel("channel");
        orderPO.setPerformanceModes("performanceModes");
//        when(mockOrderService.getOne(any(LambdaQueryWrapper.class))).thenReturn(orderPO);

        // Configure IOrderPerformanceBelongsService.getOne(...).
        final OrderPerformanceBelongsPO orderPerformanceBelongsPO = new OrderPerformanceBelongsPO();
        orderPerformanceBelongsPO.setOrderSn("orderSn");
        orderPerformanceBelongsPO.setBelongerName("belongerName");
        orderPerformanceBelongsPO.setBelongerEmployeeNo("belongerEmployeeNo");
        orderPerformanceBelongsPO.setEmployeeBranchCode("branch");
        orderPerformanceBelongsPO.setEmployeeBranchName("branchName");
        orderPerformanceBelongsPO.setBindStateCode(0);
//        when(mockOrderPerformanceBelongsService.getOne(any(LambdaQueryWrapper.class)))
//                .thenReturn(orderPerformanceBelongsPO);

        // Configure HrmsIntegration.getBranchRelation(...).
        final BranchRelationVO branchRelationVO = new BranchRelationVO();
        branchRelationVO.setBranchCode("branchCode");
        branchRelationVO.setZoneName("zoneName");
        branchRelationVO.setZoneCode("zoneCode");
        branchRelationVO.setRegionCode("areaCode");
        branchRelationVO.setRegionName("areaName");
//        when(mockHrmsIntegration.getBranchRelation("branch")).thenReturn(branchRelationVO);

//        when(mockOrderProductService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure ProductFeignClient.getProductListByProductIds(...).
        final Product product = new Product();
        product.setProductId(0L);
        product.setGoodsId(0L);
        product.setCategoryId1(0);
        product.setCategoryId2(0);
        product.setCategoryId3(0);
        final List<Product> products = Arrays.asList(product);
//        when(mockProductFeignClient.getProductListByProductIds(Arrays.asList(0L))).thenReturn(products);

        // Configure ScrmFacade.getJobNumberFile(...).
        final WechatResult<Boolean> booleanWechatResult = new WechatResult<>();
        booleanWechatResult.setSuccess(false);
        final ErrorContext errorContext = new ErrorContext();
        final ErrorStack errorStack = new ErrorStack();
        errorStack.setErrorCode("errorCode");
        errorContext.setErrorStack(Arrays.asList(errorStack));
        booleanWechatResult.setErrorContext(errorContext);
        booleanWechatResult.setData(false);
//        when(mockScrmFacade.getJobNumberFile("userMobile")).thenReturn(booleanWechatResult);

        // Configure WecatFacade.getUserInfoByUserIdList(...).
        final WechatResult<List<WechatUserInfoPO>> listWechatResult = new WechatResult<>();
        listWechatResult.setSuccess(false);
        final ErrorContext errorContext1 = new ErrorContext();
        final ErrorStack errorStack1 = new ErrorStack();
        errorContext1.setErrorStack(Arrays.asList(errorStack1));
        listWechatResult.setErrorContext(errorContext1);
        final WechatUserInfoPO wechatUserInfoPO = new WechatUserInfoPO();
        wechatUserInfoPO.setAvatar("avatar");
        listWechatResult.setData(Arrays.asList(wechatUserInfoPO));
        final WechatUserInfoRequest var1 = new WechatUserInfoRequest();
        var1.setWechatUserIdList(Arrays.asList("value"));
//        when(mockWecatFacade.getUserInfoByUserIdList(var1)).thenReturn(listWechatResult);

        // Run the test
//        orderDataPushServiceImplUnderTest.paySuccessDataPush("orderSn");

        // Verify the results
    }

    @Test
    public void testPaySuccessDataPush_ProductFeignClientReturnsNoItems() {
        // Setup
        // Configure IOrderService.getOne(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderSn("orderSn");
        orderPO.setUserMobile("userMobile");
        orderPO.setStoreId(0L);
        orderPO.setMemberName("customerName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setOrderType(0);
        orderPO.setOrderPattern(0);
        orderPO.setEnabledFlag(0);
        orderPO.setChannel("channel");
        orderPO.setPerformanceModes("performanceModes");
//        when(mockOrderService.getOne(any(LambdaQueryWrapper.class))).thenReturn(orderPO);

        // Configure IOrderPerformanceBelongsService.getOne(...).
        final OrderPerformanceBelongsPO orderPerformanceBelongsPO = new OrderPerformanceBelongsPO();
        orderPerformanceBelongsPO.setOrderSn("orderSn");
        orderPerformanceBelongsPO.setBelongerName("belongerName");
        orderPerformanceBelongsPO.setBelongerEmployeeNo("belongerEmployeeNo");
        orderPerformanceBelongsPO.setEmployeeBranchCode("branch");
        orderPerformanceBelongsPO.setEmployeeBranchName("branchName");
        orderPerformanceBelongsPO.setBindStateCode(0);
//        when(mockOrderPerformanceBelongsService.getOne(any(LambdaQueryWrapper.class)))
//                .thenReturn(orderPerformanceBelongsPO);

        // Configure HrmsIntegration.getBranchRelation(...).
        final BranchRelationVO branchRelationVO = new BranchRelationVO();
        branchRelationVO.setBranchCode("branchCode");
        branchRelationVO.setZoneName("zoneName");
        branchRelationVO.setZoneCode("zoneCode");
        branchRelationVO.setRegionCode("areaCode");
        branchRelationVO.setRegionName("areaName");
//        when(mockHrmsIntegration.getBranchRelation("branch")).thenReturn(branchRelationVO);

        // Configure IOrderProductService.list(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setGoodsCategoryPath("goodsCategoryPath");
        orderProductPO.setProductId(0L);
        orderProductPO.setWeight(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO.setSkuMaterialName("skuMaterialName");
        orderProductPO.setChannelSkuUnit("channelSkuUnit");
        orderProductPO.setChannelSkuId("channelSkuId");
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO);
//        when(mockOrderProductService.list(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

//        when(mockProductFeignClient.getProductListByProductIds(Arrays.asList(0L))).thenReturn(Collections.emptyList());

        // Configure ScrmFacade.getJobNumberFile(...).
        final WechatResult<Boolean> booleanWechatResult = new WechatResult<>();
        booleanWechatResult.setSuccess(false);
        final ErrorContext errorContext = new ErrorContext();
        final ErrorStack errorStack = new ErrorStack();
        errorStack.setErrorCode("errorCode");
        errorContext.setErrorStack(Arrays.asList(errorStack));
        booleanWechatResult.setErrorContext(errorContext);
        booleanWechatResult.setData(false);
//        when(mockScrmFacade.getJobNumberFile("userMobile")).thenReturn(booleanWechatResult);

        // Configure WecatFacade.getUserInfoByUserIdList(...).
        final WechatResult<List<WechatUserInfoPO>> listWechatResult = new WechatResult<>();
        listWechatResult.setSuccess(false);
        final ErrorContext errorContext1 = new ErrorContext();
        final ErrorStack errorStack1 = new ErrorStack();
        errorContext1.setErrorStack(Arrays.asList(errorStack1));
        listWechatResult.setErrorContext(errorContext1);
        final WechatUserInfoPO wechatUserInfoPO = new WechatUserInfoPO();
        wechatUserInfoPO.setAvatar("avatar");
        listWechatResult.setData(Arrays.asList(wechatUserInfoPO));
        final WechatUserInfoRequest var1 = new WechatUserInfoRequest();
        var1.setWechatUserIdList(Arrays.asList("value"));
//        when(mockWecatFacade.getUserInfoByUserIdList(var1)).thenReturn(listWechatResult);

        // Configure TradeDocumentIntegration.orderDataPushMatch(...).
        final OrderDataPushHitObject orderDataPushHitObject = new OrderDataPushHitObject();
        orderDataPushHitObject.setDesc("desc");
        orderDataPushHitObject.setCode("code");
        orderDataPushHitObject.setValue(0.0);
        orderDataPushHitObject.setReason("reason");
        final List<OrderDataPushHitObject> orderDataPushHitObjects = Arrays.asList(orderDataPushHitObject);
        final OrderTradeMatchDTO orderTradeMatchDTO = new OrderTradeMatchDTO();
        orderTradeMatchDTO.setStoreId("storeId");
        orderTradeMatchDTO.setOrderPattern(0);
        orderTradeMatchDTO.setPerformanceModes("performanceModes");
        orderTradeMatchDTO.setOrderAmount(new BigDecimal("0.00"));
        orderTradeMatchDTO.setGoodsCategoryId1("categoryId1");
        orderTradeMatchDTO.setGoodsCategoryId2("categoryId2");
        orderTradeMatchDTO.setGoodsCategoryId3("categoryId3");
        orderTradeMatchDTO.setPaymentCode("paymentCode");
        orderTradeMatchDTO.setBranchCode("branch");
        orderTradeMatchDTO.setZoneCode("zoneCode");
        orderTradeMatchDTO.setAreaCode("areaCode");
        orderTradeMatchDTO.setTonWeight(new BigDecimal("0.00"));
        orderTradeMatchDTO.setIsAgricMcnUser(0);
        orderTradeMatchDTO.setUserTag("userTag");
        orderTradeMatchDTO.setChannel("channel");
        orderTradeMatchDTO.setGoodsId(0L);
        orderTradeMatchDTO.setProductId(0L);
//        when(mockTradeDocumentIntegration.orderDataPushMatch(orderTradeMatchDTO)).thenReturn(orderDataPushHitObjects);

        // Configure BmsOrgFeignIntegration.listBranchesByHrOrgCodesV2(...).
        final OrganizationBaseVO organizationBaseVO = new OrganizationBaseVO();
        organizationBaseVO.setBatchNo("batchNo");
        organizationBaseVO.setOrgId(0);
        organizationBaseVO.setHrOrgId(0);
        organizationBaseVO.setOrgCode("orgCode");
        organizationBaseVO.setOrgName("orgName");
        final List<OrganizationBaseVO> organizationBaseVOS = Arrays.asList(organizationBaseVO);
//        when(mockBmsOrgFeignIntegration.listBranchesByHrOrgCodesV2("areaCode")).thenReturn(organizationBaseVOS);

//        when(mockOrderProductMapper.countPayOrderNum("orderSn", Arrays.asList("value"),
//                Arrays.asList("value"))).thenReturn(0);

        // Configure IBzOrderProductCombinationService.getOne(...).
        final BzOrderProductCombinationPO bzOrderProductCombinationPO = BzOrderProductCombinationPO.builder()
                .orderSn("orderSn")
                .mainProductId(0L)
                .build();
//        when(mockOrderProductCombinationService.getOne(any(LambdaQueryWrapper.class)))
//                .thenReturn(bzOrderProductCombinationPO);

        // Configure ERPIntegration.unitConvert(...).
        final UnitConvertVo unitConvertVo = new UnitConvertVo();
        unitConvertVo.setTagId("tagId");
        unitConvertVo.setBatchNo("batchNo");
        unitConvertVo.setSkuId("skuId");
        unitConvertVo.setPackageNum(new BigDecimal("0.00"));
        unitConvertVo.setUnitName("套");
        final List<UnitConvertVo> unitConvertVos = Arrays.asList(unitConvertVo);
//        when(mockErpIntegration.unitConvert(Arrays.asList(new HashMap<>()))).thenReturn(unitConvertVos);

        // Configure DbcServiceFeign.managerCommissionByCommodities(...).
        final com.cfpamf.common.ms.result.ErrorContext errorContext2 = new com.cfpamf.common.ms.result.ErrorContext();
        errorContext2.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext2.setThirdPartyError("thirdPartyError");
        final ManagerCommissionItemVO managerCommissionItemVO = new ManagerCommissionItemVO();
        managerCommissionItemVO.setOrderId("orderId");
        managerCommissionItemVO.setCommodityCode("commodityCode");
        managerCommissionItemVO.setManagerCommission(new BigDecimal("0.00"));
        final Result<List<ManagerCommissionItemVO>> listResult = new Result<>(false, errorContext2,
                Arrays.asList(managerCommissionItemVO));
//        when(mockDbcServiceFeign.managerCommissionByCommodities("orderSn", 5, "storeId")).thenReturn(listResult);

        // Configure DbcServiceFeign.freightCostByCommodities(...).
        final com.cfpamf.common.ms.result.ErrorContext errorContext3 = new com.cfpamf.common.ms.result.ErrorContext();
        errorContext3.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext3.setThirdPartyError("thirdPartyError");
        final FreightCostItemVO freightCostItemVO = new FreightCostItemVO();
        freightCostItemVO.setOrderId("orderId");
        freightCostItemVO.setCommodityCode("commodityCode");
        freightCostItemVO.setFreightCost(new BigDecimal("0.00"));
        final Result<List<FreightCostItemVO>> listResult1 = new Result<>(false, errorContext3,
                Arrays.asList(freightCostItemVO));
//        when(mockDbcServiceFeign.freightCostByCommodities("orderSn", 5, "storeId")).thenReturn(listResult1);

        // Configure DbcServiceFeign.getListByOrderId(...).
        final com.cfpamf.common.ms.result.ErrorContext errorContext4 = new com.cfpamf.common.ms.result.ErrorContext();
        errorContext4.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext4.setThirdPartyError("thirdPartyError");
        final DbcDistributionSaleOrderVO dbcDistributionSaleOrderVO = new DbcDistributionSaleOrderVO();
        dbcDistributionSaleOrderVO.setId(0L);
        dbcDistributionSaleOrderVO.setOrderId("orderId");
        dbcDistributionSaleOrderVO.setOrderCommission(new BigDecimal("0.00"));
        dbcDistributionSaleOrderVO.setCommodityCode("commodityCode");
        dbcDistributionSaleOrderVO.setCommissionOrderType(0);
        final Result<List<DbcDistributionSaleOrderVO>> listResult2 = new Result<>(false, errorContext4,
                Arrays.asList(dbcDistributionSaleOrderVO));
//        when(mockDbcServiceFeign.getListByOrderId("orderSn")).thenReturn(listResult2);

//        when(mockOrderProductMapper.airEnergySalesCount("branch", "areaCode")).thenReturn(0);

        // Configure ICommonMqEventService.saveEvent(...).
        final GoodNewsMessage body = new GoodNewsMessage();
        body.setReportType("reportType");
        body.setBranch("branch");
        body.setMarketingCode("2023MALL_PAY_ORDER");
        body.setReportLevel(0);
        body.setPushParam("pushParam");
        body.setStatisticalObject("belongerEmployeeNo");
        body.setStatisticalTime("orderSn");
        body.setParamAddStatisticalCount(false);
        body.setMessageId("messageId");
        body.setText("text");
//        when(mockCommonMqEventService.saveEvent(body, "agric.performance.goodNews.exchange")).thenReturn(0L);

        // Run the test
//        orderDataPushServiceImplUnderTest.paySuccessDataPush("orderSn");

        // Verify the results
//        verify(mockRabbitMQUtils).sendByEventId(0L);
    }

    @Test
    public void testPaySuccessDataPush_TradeDocumentIntegrationReturnsNoItems() {
        // Setup
        // Configure IOrderService.getOne(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderSn("orderSn");
        orderPO.setUserMobile("userMobile");
        orderPO.setStoreId(0L);
        orderPO.setMemberName("customerName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setOrderType(0);
        orderPO.setOrderPattern(0);
        orderPO.setEnabledFlag(0);
        orderPO.setChannel("channel");
        orderPO.setPerformanceModes("performanceModes");
//        when(mockOrderService.getOne(any(LambdaQueryWrapper.class))).thenReturn(orderPO);

        // Configure IOrderPerformanceBelongsService.getOne(...).
        final OrderPerformanceBelongsPO orderPerformanceBelongsPO = new OrderPerformanceBelongsPO();
        orderPerformanceBelongsPO.setOrderSn("orderSn");
        orderPerformanceBelongsPO.setBelongerName("belongerName");
        orderPerformanceBelongsPO.setBelongerEmployeeNo("belongerEmployeeNo");
        orderPerformanceBelongsPO.setEmployeeBranchCode("branch");
        orderPerformanceBelongsPO.setEmployeeBranchName("branchName");
        orderPerformanceBelongsPO.setBindStateCode(0);
//        when(mockOrderPerformanceBelongsService.getOne(any(LambdaQueryWrapper.class)))
//                .thenReturn(orderPerformanceBelongsPO);

        // Configure HrmsIntegration.getBranchRelation(...).
        final BranchRelationVO branchRelationVO = new BranchRelationVO();
        branchRelationVO.setBranchCode("branchCode");
        branchRelationVO.setZoneName("zoneName");
        branchRelationVO.setZoneCode("zoneCode");
        branchRelationVO.setRegionCode("areaCode");
        branchRelationVO.setRegionName("areaName");
//        when(mockHrmsIntegration.getBranchRelation("branch")).thenReturn(branchRelationVO);

        // Configure IOrderProductService.list(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setGoodsCategoryPath("goodsCategoryPath");
        orderProductPO.setProductId(0L);
        orderProductPO.setWeight(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO.setSkuMaterialName("skuMaterialName");
        orderProductPO.setChannelSkuUnit("channelSkuUnit");
        orderProductPO.setChannelSkuId("channelSkuId");
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO);
//        when(mockOrderProductService.list(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure ProductFeignClient.getProductListByProductIds(...).
        final Product product = new Product();
        product.setProductId(0L);
        product.setGoodsId(0L);
        product.setCategoryId1(0);
        product.setCategoryId2(0);
        product.setCategoryId3(0);
        final List<Product> products = Arrays.asList(product);
//        when(mockProductFeignClient.getProductListByProductIds(Arrays.asList(0L))).thenReturn(products);

        // Configure ScrmFacade.getJobNumberFile(...).
        final WechatResult<Boolean> booleanWechatResult = new WechatResult<>();
        booleanWechatResult.setSuccess(false);
        final ErrorContext errorContext = new ErrorContext();
        final ErrorStack errorStack = new ErrorStack();
        errorStack.setErrorCode("errorCode");
        errorContext.setErrorStack(Arrays.asList(errorStack));
        booleanWechatResult.setErrorContext(errorContext);
        booleanWechatResult.setData(false);
//        when(mockScrmFacade.getJobNumberFile("userMobile")).thenReturn(booleanWechatResult);

        // Configure WecatFacade.getUserInfoByUserIdList(...).
        final WechatResult<List<WechatUserInfoPO>> listWechatResult = new WechatResult<>();
        listWechatResult.setSuccess(false);
        final ErrorContext errorContext1 = new ErrorContext();
        final ErrorStack errorStack1 = new ErrorStack();
        errorContext1.setErrorStack(Arrays.asList(errorStack1));
        listWechatResult.setErrorContext(errorContext1);
        final WechatUserInfoPO wechatUserInfoPO = new WechatUserInfoPO();
        wechatUserInfoPO.setAvatar("avatar");
        listWechatResult.setData(Arrays.asList(wechatUserInfoPO));
        final WechatUserInfoRequest var1 = new WechatUserInfoRequest();
        var1.setWechatUserIdList(Arrays.asList("value"));
//        when(mockWecatFacade.getUserInfoByUserIdList(var1)).thenReturn(listWechatResult);

        // Configure TradeDocumentIntegration.orderDataPushMatch(...).
        final OrderTradeMatchDTO orderTradeMatchDTO = new OrderTradeMatchDTO();
        orderTradeMatchDTO.setStoreId("storeId");
        orderTradeMatchDTO.setOrderPattern(0);
        orderTradeMatchDTO.setPerformanceModes("performanceModes");
        orderTradeMatchDTO.setOrderAmount(new BigDecimal("0.00"));
        orderTradeMatchDTO.setGoodsCategoryId1("categoryId1");
        orderTradeMatchDTO.setGoodsCategoryId2("categoryId2");
        orderTradeMatchDTO.setGoodsCategoryId3("categoryId3");
        orderTradeMatchDTO.setPaymentCode("paymentCode");
        orderTradeMatchDTO.setBranchCode("branch");
        orderTradeMatchDTO.setZoneCode("zoneCode");
        orderTradeMatchDTO.setAreaCode("areaCode");
        orderTradeMatchDTO.setTonWeight(new BigDecimal("0.00"));
        orderTradeMatchDTO.setIsAgricMcnUser(0);
        orderTradeMatchDTO.setUserTag("userTag");
        orderTradeMatchDTO.setChannel("channel");
        orderTradeMatchDTO.setGoodsId(0L);
        orderTradeMatchDTO.setProductId(0L);
//        when(mockTradeDocumentIntegration.orderDataPushMatch(orderTradeMatchDTO)).thenReturn(Collections.emptyList());

        // Configure BmsOrgFeignIntegration.listBranchesByHrOrgCodesV2(...).
        final OrganizationBaseVO organizationBaseVO = new OrganizationBaseVO();
        organizationBaseVO.setBatchNo("batchNo");
        organizationBaseVO.setOrgId(0);
        organizationBaseVO.setHrOrgId(0);
        organizationBaseVO.setOrgCode("orgCode");
        organizationBaseVO.setOrgName("orgName");
        final List<OrganizationBaseVO> organizationBaseVOS = Arrays.asList(organizationBaseVO);
//        when(mockBmsOrgFeignIntegration.listBranchesByHrOrgCodesV2("areaCode")).thenReturn(organizationBaseVOS);

//        when(mockOrderProductMapper.countPayOrderNum("orderSn", Arrays.asList("value"),
//                Arrays.asList("value"))).thenReturn(0);

        // Configure IBzOrderProductCombinationService.getOne(...).
        final BzOrderProductCombinationPO bzOrderProductCombinationPO = BzOrderProductCombinationPO.builder()
                .orderSn("orderSn")
                .mainProductId(0L)
                .build();
//        when(mockOrderProductCombinationService.getOne(any(LambdaQueryWrapper.class)))
//                .thenReturn(bzOrderProductCombinationPO);

        // Configure ERPIntegration.unitConvert(...).
        final UnitConvertVo unitConvertVo = new UnitConvertVo();
        unitConvertVo.setTagId("tagId");
        unitConvertVo.setBatchNo("batchNo");
        unitConvertVo.setSkuId("skuId");
        unitConvertVo.setPackageNum(new BigDecimal("0.00"));
        unitConvertVo.setUnitName("套");
        final List<UnitConvertVo> unitConvertVos = Arrays.asList(unitConvertVo);
//        when(mockErpIntegration.unitConvert(Arrays.asList(new HashMap<>()))).thenReturn(unitConvertVos);

        // Configure DbcServiceFeign.managerCommissionByCommodities(...).
        final com.cfpamf.common.ms.result.ErrorContext errorContext2 = new com.cfpamf.common.ms.result.ErrorContext();
        errorContext2.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext2.setThirdPartyError("thirdPartyError");
        final ManagerCommissionItemVO managerCommissionItemVO = new ManagerCommissionItemVO();
        managerCommissionItemVO.setOrderId("orderId");
        managerCommissionItemVO.setCommodityCode("commodityCode");
        managerCommissionItemVO.setManagerCommission(new BigDecimal("0.00"));
        final Result<List<ManagerCommissionItemVO>> listResult = new Result<>(false, errorContext2,
                Arrays.asList(managerCommissionItemVO));
//        when(mockDbcServiceFeign.managerCommissionByCommodities("orderSn", 5, "storeId")).thenReturn(listResult);

        // Configure DbcServiceFeign.freightCostByCommodities(...).
        final com.cfpamf.common.ms.result.ErrorContext errorContext3 = new com.cfpamf.common.ms.result.ErrorContext();
        errorContext3.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext3.setThirdPartyError("thirdPartyError");
        final FreightCostItemVO freightCostItemVO = new FreightCostItemVO();
        freightCostItemVO.setOrderId("orderId");
        freightCostItemVO.setCommodityCode("commodityCode");
        freightCostItemVO.setFreightCost(new BigDecimal("0.00"));
        final Result<List<FreightCostItemVO>> listResult1 = new Result<>(false, errorContext3,
                Arrays.asList(freightCostItemVO));
//        when(mockDbcServiceFeign.freightCostByCommodities("orderSn", 5, "storeId")).thenReturn(listResult1);

        // Configure DbcServiceFeign.getListByOrderId(...).
        final com.cfpamf.common.ms.result.ErrorContext errorContext4 = new com.cfpamf.common.ms.result.ErrorContext();
        errorContext4.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext4.setThirdPartyError("thirdPartyError");
        final DbcDistributionSaleOrderVO dbcDistributionSaleOrderVO = new DbcDistributionSaleOrderVO();
        dbcDistributionSaleOrderVO.setId(0L);
        dbcDistributionSaleOrderVO.setOrderId("orderId");
        dbcDistributionSaleOrderVO.setOrderCommission(new BigDecimal("0.00"));
        dbcDistributionSaleOrderVO.setCommodityCode("commodityCode");
        dbcDistributionSaleOrderVO.setCommissionOrderType(0);
        final Result<List<DbcDistributionSaleOrderVO>> listResult2 = new Result<>(false, errorContext4,
                Arrays.asList(dbcDistributionSaleOrderVO));
//        when(mockDbcServiceFeign.getListByOrderId("orderSn")).thenReturn(listResult2);

//        when(mockOrderProductMapper.airEnergySalesCount("branch", "areaCode")).thenReturn(0);

        // Configure ICommonMqEventService.saveEvent(...).
        final GoodNewsMessage body = new GoodNewsMessage();
        body.setReportType("reportType");
        body.setBranch("branch");
        body.setMarketingCode("2023MALL_PAY_ORDER");
        body.setReportLevel(0);
        body.setPushParam("pushParam");
        body.setStatisticalObject("belongerEmployeeNo");
        body.setStatisticalTime("orderSn");
        body.setParamAddStatisticalCount(false);
        body.setMessageId("messageId");
        body.setText("text");
//        when(mockCommonMqEventService.saveEvent(body, "agric.performance.goodNews.exchange")).thenReturn(0L);

        // Run the test
//        orderDataPushServiceImplUnderTest.paySuccessDataPush("orderSn");

        // Verify the results
//        verify(mockRabbitMQUtils).sendByEventId(0L);
    }

    @Test
    public void testPaySuccessDataPush_BmsOrgFeignIntegrationReturnsNoItems() {
        // Setup
        // Configure IOrderService.getOne(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderSn("orderSn");
        orderPO.setUserMobile("userMobile");
        orderPO.setStoreId(0L);
        orderPO.setMemberName("customerName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setOrderType(0);
        orderPO.setOrderPattern(0);
        orderPO.setEnabledFlag(0);
        orderPO.setChannel("channel");
        orderPO.setPerformanceModes("performanceModes");
//        when(mockOrderService.getOne(any(LambdaQueryWrapper.class))).thenReturn(orderPO);

        // Configure IOrderPerformanceBelongsService.getOne(...).
        final OrderPerformanceBelongsPO orderPerformanceBelongsPO = new OrderPerformanceBelongsPO();
        orderPerformanceBelongsPO.setOrderSn("orderSn");
        orderPerformanceBelongsPO.setBelongerName("belongerName");
        orderPerformanceBelongsPO.setBelongerEmployeeNo("belongerEmployeeNo");
        orderPerformanceBelongsPO.setEmployeeBranchCode("branch");
        orderPerformanceBelongsPO.setEmployeeBranchName("branchName");
        orderPerformanceBelongsPO.setBindStateCode(0);
//        when(mockOrderPerformanceBelongsService.getOne(any(LambdaQueryWrapper.class)))
//                .thenReturn(orderPerformanceBelongsPO);

        // Configure HrmsIntegration.getBranchRelation(...).
        final BranchRelationVO branchRelationVO = new BranchRelationVO();
        branchRelationVO.setBranchCode("branchCode");
        branchRelationVO.setZoneName("zoneName");
        branchRelationVO.setZoneCode("zoneCode");
        branchRelationVO.setRegionCode("areaCode");
        branchRelationVO.setRegionName("areaName");
//        when(mockHrmsIntegration.getBranchRelation("branch")).thenReturn(branchRelationVO);

        // Configure IOrderProductService.list(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setGoodsCategoryPath("goodsCategoryPath");
        orderProductPO.setProductId(0L);
        orderProductPO.setWeight(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO.setSkuMaterialName("skuMaterialName");
        orderProductPO.setChannelSkuUnit("channelSkuUnit");
        orderProductPO.setChannelSkuId("channelSkuId");
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO);
//        when(mockOrderProductService.list(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure ProductFeignClient.getProductListByProductIds(...).
        final Product product = new Product();
        product.setProductId(0L);
        product.setGoodsId(0L);
        product.setCategoryId1(0);
        product.setCategoryId2(0);
        product.setCategoryId3(0);
        final List<Product> products = Arrays.asList(product);
//        when(mockProductFeignClient.getProductListByProductIds(Arrays.asList(0L))).thenReturn(products);

        // Configure ScrmFacade.getJobNumberFile(...).
        final WechatResult<Boolean> booleanWechatResult = new WechatResult<>();
        booleanWechatResult.setSuccess(false);
        final ErrorContext errorContext = new ErrorContext();
        final ErrorStack errorStack = new ErrorStack();
        errorStack.setErrorCode("errorCode");
        errorContext.setErrorStack(Arrays.asList(errorStack));
        booleanWechatResult.setErrorContext(errorContext);
        booleanWechatResult.setData(false);
//        when(mockScrmFacade.getJobNumberFile("userMobile")).thenReturn(booleanWechatResult);

        // Configure WecatFacade.getUserInfoByUserIdList(...).
        final WechatResult<List<WechatUserInfoPO>> listWechatResult = new WechatResult<>();
        listWechatResult.setSuccess(false);
        final ErrorContext errorContext1 = new ErrorContext();
        final ErrorStack errorStack1 = new ErrorStack();
        errorContext1.setErrorStack(Arrays.asList(errorStack1));
        listWechatResult.setErrorContext(errorContext1);
        final WechatUserInfoPO wechatUserInfoPO = new WechatUserInfoPO();
        wechatUserInfoPO.setAvatar("avatar");
        listWechatResult.setData(Arrays.asList(wechatUserInfoPO));
        final WechatUserInfoRequest var1 = new WechatUserInfoRequest();
        var1.setWechatUserIdList(Arrays.asList("value"));
//        when(mockWecatFacade.getUserInfoByUserIdList(var1)).thenReturn(listWechatResult);

        // Configure TradeDocumentIntegration.orderDataPushMatch(...).
        final OrderDataPushHitObject orderDataPushHitObject = new OrderDataPushHitObject();
        orderDataPushHitObject.setDesc("desc");
        orderDataPushHitObject.setCode("code");
        orderDataPushHitObject.setValue(0.0);
        orderDataPushHitObject.setReason("reason");
        final List<OrderDataPushHitObject> orderDataPushHitObjects = Arrays.asList(orderDataPushHitObject);
        final OrderTradeMatchDTO orderTradeMatchDTO = new OrderTradeMatchDTO();
        orderTradeMatchDTO.setStoreId("storeId");
        orderTradeMatchDTO.setOrderPattern(0);
        orderTradeMatchDTO.setPerformanceModes("performanceModes");
        orderTradeMatchDTO.setOrderAmount(new BigDecimal("0.00"));
        orderTradeMatchDTO.setGoodsCategoryId1("categoryId1");
        orderTradeMatchDTO.setGoodsCategoryId2("categoryId2");
        orderTradeMatchDTO.setGoodsCategoryId3("categoryId3");
        orderTradeMatchDTO.setPaymentCode("paymentCode");
        orderTradeMatchDTO.setBranchCode("branch");
        orderTradeMatchDTO.setZoneCode("zoneCode");
        orderTradeMatchDTO.setAreaCode("areaCode");
        orderTradeMatchDTO.setTonWeight(new BigDecimal("0.00"));
        orderTradeMatchDTO.setIsAgricMcnUser(0);
        orderTradeMatchDTO.setUserTag("userTag");
        orderTradeMatchDTO.setChannel("channel");
        orderTradeMatchDTO.setGoodsId(0L);
        orderTradeMatchDTO.setProductId(0L);
//        when(mockTradeDocumentIntegration.orderDataPushMatch(orderTradeMatchDTO)).thenReturn(orderDataPushHitObjects);

//        when(mockBmsOrgFeignIntegration.listBranchesByHrOrgCodesV2("areaCode")).thenReturn(Collections.emptyList());

        // Configure IBzOrderProductCombinationService.getOne(...).
        final BzOrderProductCombinationPO bzOrderProductCombinationPO = BzOrderProductCombinationPO.builder()
                .orderSn("orderSn")
                .mainProductId(0L)
                .build();
//        when(mockOrderProductCombinationService.getOne(any(LambdaQueryWrapper.class)))
//                .thenReturn(bzOrderProductCombinationPO);

        // Configure ERPIntegration.unitConvert(...).
        final UnitConvertVo unitConvertVo = new UnitConvertVo();
        unitConvertVo.setTagId("tagId");
        unitConvertVo.setBatchNo("batchNo");
        unitConvertVo.setSkuId("skuId");
        unitConvertVo.setPackageNum(new BigDecimal("0.00"));
        unitConvertVo.setUnitName("套");
        final List<UnitConvertVo> unitConvertVos = Arrays.asList(unitConvertVo);
//        when(mockErpIntegration.unitConvert(Arrays.asList(new HashMap<>()))).thenReturn(unitConvertVos);

        // Configure DbcServiceFeign.managerCommissionByCommodities(...).
        final com.cfpamf.common.ms.result.ErrorContext errorContext2 = new com.cfpamf.common.ms.result.ErrorContext();
        errorContext2.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext2.setThirdPartyError("thirdPartyError");
        final ManagerCommissionItemVO managerCommissionItemVO = new ManagerCommissionItemVO();
        managerCommissionItemVO.setOrderId("orderId");
        managerCommissionItemVO.setCommodityCode("commodityCode");
        managerCommissionItemVO.setManagerCommission(new BigDecimal("0.00"));
        final Result<List<ManagerCommissionItemVO>> listResult = new Result<>(false, errorContext2,
                Arrays.asList(managerCommissionItemVO));
//        when(mockDbcServiceFeign.managerCommissionByCommodities("orderSn", 5, "storeId")).thenReturn(listResult);

        // Configure DbcServiceFeign.freightCostByCommodities(...).
        final com.cfpamf.common.ms.result.ErrorContext errorContext3 = new com.cfpamf.common.ms.result.ErrorContext();
        errorContext3.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext3.setThirdPartyError("thirdPartyError");
        final FreightCostItemVO freightCostItemVO = new FreightCostItemVO();
        freightCostItemVO.setOrderId("orderId");
        freightCostItemVO.setCommodityCode("commodityCode");
        freightCostItemVO.setFreightCost(new BigDecimal("0.00"));
        final Result<List<FreightCostItemVO>> listResult1 = new Result<>(false, errorContext3,
                Arrays.asList(freightCostItemVO));
//        when(mockDbcServiceFeign.freightCostByCommodities("orderSn", 5, "storeId")).thenReturn(listResult1);

        // Configure DbcServiceFeign.getListByOrderId(...).
        final com.cfpamf.common.ms.result.ErrorContext errorContext4 = new com.cfpamf.common.ms.result.ErrorContext();
        errorContext4.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext4.setThirdPartyError("thirdPartyError");
        final DbcDistributionSaleOrderVO dbcDistributionSaleOrderVO = new DbcDistributionSaleOrderVO();
        dbcDistributionSaleOrderVO.setId(0L);
        dbcDistributionSaleOrderVO.setOrderId("orderId");
        dbcDistributionSaleOrderVO.setOrderCommission(new BigDecimal("0.00"));
        dbcDistributionSaleOrderVO.setCommodityCode("commodityCode");
        dbcDistributionSaleOrderVO.setCommissionOrderType(0);
        final Result<List<DbcDistributionSaleOrderVO>> listResult2 = new Result<>(false, errorContext4,
                Arrays.asList(dbcDistributionSaleOrderVO));
//        when(mockDbcServiceFeign.getListByOrderId("orderSn")).thenReturn(listResult2);

//        when(mockOrderProductMapper.airEnergySalesCount("branch", "areaCode")).thenReturn(0);

        // Configure ICommonMqEventService.saveEvent(...).
        final GoodNewsMessage body = new GoodNewsMessage();
        body.setReportType("reportType");
        body.setBranch("branch");
        body.setMarketingCode("2023MALL_PAY_ORDER");
        body.setReportLevel(0);
        body.setPushParam("pushParam");
        body.setStatisticalObject("belongerEmployeeNo");
        body.setStatisticalTime("orderSn");
        body.setParamAddStatisticalCount(false);
        body.setMessageId("messageId");
        body.setText("text");
//        when(mockCommonMqEventService.saveEvent(body, "agric.performance.goodNews.exchange")).thenReturn(0L);

        // Run the test
//        orderDataPushServiceImplUnderTest.paySuccessDataPush("orderSn");

        // Verify the results
//        verify(mockRabbitMQUtils).sendByEventId(0L);
    }

    @Test
    public void testPaySuccessDataPush_OrderProductMapperCountPayOrderNumReturnsNull() {
        // Setup
        // Configure IOrderService.getOne(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderSn("orderSn");
        orderPO.setUserMobile("userMobile");
        orderPO.setStoreId(0L);
        orderPO.setMemberName("customerName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setOrderType(0);
        orderPO.setOrderPattern(0);
        orderPO.setEnabledFlag(0);
        orderPO.setChannel("channel");
        orderPO.setPerformanceModes("performanceModes");
//        when(mockOrderService.getOne(any(LambdaQueryWrapper.class))).thenReturn(orderPO);

        // Configure IOrderPerformanceBelongsService.getOne(...).
        final OrderPerformanceBelongsPO orderPerformanceBelongsPO = new OrderPerformanceBelongsPO();
        orderPerformanceBelongsPO.setOrderSn("orderSn");
        orderPerformanceBelongsPO.setBelongerName("belongerName");
        orderPerformanceBelongsPO.setBelongerEmployeeNo("belongerEmployeeNo");
        orderPerformanceBelongsPO.setEmployeeBranchCode("branch");
        orderPerformanceBelongsPO.setEmployeeBranchName("branchName");
        orderPerformanceBelongsPO.setBindStateCode(0);
//        when(mockOrderPerformanceBelongsService.getOne(any(LambdaQueryWrapper.class)))
//                .thenReturn(orderPerformanceBelongsPO);

        // Configure HrmsIntegration.getBranchRelation(...).
        final BranchRelationVO branchRelationVO = new BranchRelationVO();
        branchRelationVO.setBranchCode("branchCode");
        branchRelationVO.setZoneName("zoneName");
        branchRelationVO.setZoneCode("zoneCode");
        branchRelationVO.setRegionCode("areaCode");
        branchRelationVO.setRegionName("areaName");
//        when(mockHrmsIntegration.getBranchRelation("branch")).thenReturn(branchRelationVO);

        // Configure IOrderProductService.list(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setGoodsCategoryPath("goodsCategoryPath");
        orderProductPO.setProductId(0L);
        orderProductPO.setWeight(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO.setSkuMaterialName("skuMaterialName");
        orderProductPO.setChannelSkuUnit("channelSkuUnit");
        orderProductPO.setChannelSkuId("channelSkuId");
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO);
//        when(mockOrderProductService.list(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure ProductFeignClient.getProductListByProductIds(...).
        final Product product = new Product();
        product.setProductId(0L);
        product.setGoodsId(0L);
        product.setCategoryId1(0);
        product.setCategoryId2(0);
        product.setCategoryId3(0);
        final List<Product> products = Arrays.asList(product);
//        when(mockProductFeignClient.getProductListByProductIds(Arrays.asList(0L))).thenReturn(products);

        // Configure ScrmFacade.getJobNumberFile(...).
        final WechatResult<Boolean> booleanWechatResult = new WechatResult<>();
        booleanWechatResult.setSuccess(false);
        final ErrorContext errorContext = new ErrorContext();
        final ErrorStack errorStack = new ErrorStack();
        errorStack.setErrorCode("errorCode");
        errorContext.setErrorStack(Arrays.asList(errorStack));
        booleanWechatResult.setErrorContext(errorContext);
        booleanWechatResult.setData(false);
//        when(mockScrmFacade.getJobNumberFile("userMobile")).thenReturn(booleanWechatResult);

        // Configure WecatFacade.getUserInfoByUserIdList(...).
        final WechatResult<List<WechatUserInfoPO>> listWechatResult = new WechatResult<>();
        listWechatResult.setSuccess(false);
        final ErrorContext errorContext1 = new ErrorContext();
        final ErrorStack errorStack1 = new ErrorStack();
        errorContext1.setErrorStack(Arrays.asList(errorStack1));
        listWechatResult.setErrorContext(errorContext1);
        final WechatUserInfoPO wechatUserInfoPO = new WechatUserInfoPO();
        wechatUserInfoPO.setAvatar("avatar");
        listWechatResult.setData(Arrays.asList(wechatUserInfoPO));
        final WechatUserInfoRequest var1 = new WechatUserInfoRequest();
        var1.setWechatUserIdList(Arrays.asList("value"));
//        when(mockWecatFacade.getUserInfoByUserIdList(var1)).thenReturn(listWechatResult);

        // Configure TradeDocumentIntegration.orderDataPushMatch(...).
        final OrderDataPushHitObject orderDataPushHitObject = new OrderDataPushHitObject();
        orderDataPushHitObject.setDesc("desc");
        orderDataPushHitObject.setCode("code");
        orderDataPushHitObject.setValue(0.0);
        orderDataPushHitObject.setReason("reason");
        final List<OrderDataPushHitObject> orderDataPushHitObjects = Arrays.asList(orderDataPushHitObject);
        final OrderTradeMatchDTO orderTradeMatchDTO = new OrderTradeMatchDTO();
        orderTradeMatchDTO.setStoreId("storeId");
        orderTradeMatchDTO.setOrderPattern(0);
        orderTradeMatchDTO.setPerformanceModes("performanceModes");
        orderTradeMatchDTO.setOrderAmount(new BigDecimal("0.00"));
        orderTradeMatchDTO.setGoodsCategoryId1("categoryId1");
        orderTradeMatchDTO.setGoodsCategoryId2("categoryId2");
        orderTradeMatchDTO.setGoodsCategoryId3("categoryId3");
        orderTradeMatchDTO.setPaymentCode("paymentCode");
        orderTradeMatchDTO.setBranchCode("branch");
        orderTradeMatchDTO.setZoneCode("zoneCode");
        orderTradeMatchDTO.setAreaCode("areaCode");
        orderTradeMatchDTO.setTonWeight(new BigDecimal("0.00"));
        orderTradeMatchDTO.setIsAgricMcnUser(0);
        orderTradeMatchDTO.setUserTag("userTag");
        orderTradeMatchDTO.setChannel("channel");
        orderTradeMatchDTO.setGoodsId(0L);
        orderTradeMatchDTO.setProductId(0L);
//        when(mockTradeDocumentIntegration.orderDataPushMatch(orderTradeMatchDTO)).thenReturn(orderDataPushHitObjects);

        // Configure BmsOrgFeignIntegration.listBranchesByHrOrgCodesV2(...).
        final OrganizationBaseVO organizationBaseVO = new OrganizationBaseVO();
        organizationBaseVO.setBatchNo("batchNo");
        organizationBaseVO.setOrgId(0);
        organizationBaseVO.setHrOrgId(0);
        organizationBaseVO.setOrgCode("orgCode");
        organizationBaseVO.setOrgName("orgName");
        final List<OrganizationBaseVO> organizationBaseVOS = Arrays.asList(organizationBaseVO);
//        when(mockBmsOrgFeignIntegration.listBranchesByHrOrgCodesV2("areaCode")).thenReturn(organizationBaseVOS);

//        when(mockOrderProductMapper.countPayOrderNum("orderSn", Arrays.asList("value"),
//                Arrays.asList("value"))).thenReturn(null);

        // Configure IBzOrderProductCombinationService.getOne(...).
        final BzOrderProductCombinationPO bzOrderProductCombinationPO = BzOrderProductCombinationPO.builder()
                .orderSn("orderSn")
                .mainProductId(0L)
                .build();
//        when(mockOrderProductCombinationService.getOne(any(LambdaQueryWrapper.class)))
//                .thenReturn(bzOrderProductCombinationPO);

        // Configure ERPIntegration.unitConvert(...).
        final UnitConvertVo unitConvertVo = new UnitConvertVo();
        unitConvertVo.setTagId("tagId");
        unitConvertVo.setBatchNo("batchNo");
        unitConvertVo.setSkuId("skuId");
        unitConvertVo.setPackageNum(new BigDecimal("0.00"));
        unitConvertVo.setUnitName("套");
        final List<UnitConvertVo> unitConvertVos = Arrays.asList(unitConvertVo);
//        when(mockErpIntegration.unitConvert(Arrays.asList(new HashMap<>()))).thenReturn(unitConvertVos);

        // Configure DbcServiceFeign.managerCommissionByCommodities(...).
        final com.cfpamf.common.ms.result.ErrorContext errorContext2 = new com.cfpamf.common.ms.result.ErrorContext();
        errorContext2.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext2.setThirdPartyError("thirdPartyError");
        final ManagerCommissionItemVO managerCommissionItemVO = new ManagerCommissionItemVO();
        managerCommissionItemVO.setOrderId("orderId");
        managerCommissionItemVO.setCommodityCode("commodityCode");
        managerCommissionItemVO.setManagerCommission(new BigDecimal("0.00"));
        final Result<List<ManagerCommissionItemVO>> listResult = new Result<>(false, errorContext2,
                Arrays.asList(managerCommissionItemVO));
//        when(mockDbcServiceFeign.managerCommissionByCommodities("orderSn", 5, "storeId")).thenReturn(listResult);

        // Configure DbcServiceFeign.freightCostByCommodities(...).
        final com.cfpamf.common.ms.result.ErrorContext errorContext3 = new com.cfpamf.common.ms.result.ErrorContext();
        errorContext3.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext3.setThirdPartyError("thirdPartyError");
        final FreightCostItemVO freightCostItemVO = new FreightCostItemVO();
        freightCostItemVO.setOrderId("orderId");
        freightCostItemVO.setCommodityCode("commodityCode");
        freightCostItemVO.setFreightCost(new BigDecimal("0.00"));
        final Result<List<FreightCostItemVO>> listResult1 = new Result<>(false, errorContext3,
                Arrays.asList(freightCostItemVO));
//        when(mockDbcServiceFeign.freightCostByCommodities("orderSn", 5, "storeId")).thenReturn(listResult1);

        // Configure DbcServiceFeign.getListByOrderId(...).
        final com.cfpamf.common.ms.result.ErrorContext errorContext4 = new com.cfpamf.common.ms.result.ErrorContext();
        errorContext4.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext4.setThirdPartyError("thirdPartyError");
        final DbcDistributionSaleOrderVO dbcDistributionSaleOrderVO = new DbcDistributionSaleOrderVO();
        dbcDistributionSaleOrderVO.setId(0L);
        dbcDistributionSaleOrderVO.setOrderId("orderId");
        dbcDistributionSaleOrderVO.setOrderCommission(new BigDecimal("0.00"));
        dbcDistributionSaleOrderVO.setCommodityCode("commodityCode");
        dbcDistributionSaleOrderVO.setCommissionOrderType(0);
        final Result<List<DbcDistributionSaleOrderVO>> listResult2 = new Result<>(false, errorContext4,
                Arrays.asList(dbcDistributionSaleOrderVO));
//        when(mockDbcServiceFeign.getListByOrderId("orderSn")).thenReturn(listResult2);

//        when(mockOrderProductMapper.airEnergySalesCount("branch", "areaCode")).thenReturn(0);

        // Configure ICommonMqEventService.saveEvent(...).
        final GoodNewsMessage body = new GoodNewsMessage();
        body.setReportType("reportType");
        body.setBranch("branch");
        body.setMarketingCode("2023MALL_PAY_ORDER");
        body.setReportLevel(0);
        body.setPushParam("pushParam");
        body.setStatisticalObject("belongerEmployeeNo");
        body.setStatisticalTime("orderSn");
        body.setParamAddStatisticalCount(false);
        body.setMessageId("messageId");
        body.setText("text");
//        when(mockCommonMqEventService.saveEvent(body, "agric.performance.goodNews.exchange")).thenReturn(0L);

        // Run the test
//        orderDataPushServiceImplUnderTest.paySuccessDataPush("orderSn");

        // Verify the results
//        verify(mockRabbitMQUtils).sendByEventId(0L);
    }

    @Test
    public void testPaySuccessDataPush_ERPIntegrationReturnsNoItems() {
        // Setup
        // Configure IOrderService.getOne(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderSn("orderSn");
        orderPO.setUserMobile("userMobile");
        orderPO.setStoreId(0L);
        orderPO.setMemberName("customerName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setOrderType(0);
        orderPO.setOrderPattern(0);
        orderPO.setEnabledFlag(0);
        orderPO.setChannel("channel");
        orderPO.setPerformanceModes("performanceModes");
//        when(mockOrderService.getOne(any(LambdaQueryWrapper.class))).thenReturn(orderPO);

        // Configure IOrderPerformanceBelongsService.getOne(...).
        final OrderPerformanceBelongsPO orderPerformanceBelongsPO = new OrderPerformanceBelongsPO();
        orderPerformanceBelongsPO.setOrderSn("orderSn");
        orderPerformanceBelongsPO.setBelongerName("belongerName");
        orderPerformanceBelongsPO.setBelongerEmployeeNo("belongerEmployeeNo");
        orderPerformanceBelongsPO.setEmployeeBranchCode("branch");
        orderPerformanceBelongsPO.setEmployeeBranchName("branchName");
        orderPerformanceBelongsPO.setBindStateCode(0);
//        when(mockOrderPerformanceBelongsService.getOne(any(LambdaQueryWrapper.class)))
//                .thenReturn(orderPerformanceBelongsPO);

        // Configure HrmsIntegration.getBranchRelation(...).
        final BranchRelationVO branchRelationVO = new BranchRelationVO();
        branchRelationVO.setBranchCode("branchCode");
        branchRelationVO.setZoneName("zoneName");
        branchRelationVO.setZoneCode("zoneCode");
        branchRelationVO.setRegionCode("areaCode");
        branchRelationVO.setRegionName("areaName");
//        when(mockHrmsIntegration.getBranchRelation("branch")).thenReturn(branchRelationVO);

        // Configure IOrderProductService.list(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setGoodsCategoryPath("goodsCategoryPath");
        orderProductPO.setProductId(0L);
        orderProductPO.setWeight(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO.setSkuMaterialName("skuMaterialName");
        orderProductPO.setChannelSkuUnit("channelSkuUnit");
        orderProductPO.setChannelSkuId("channelSkuId");
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO);
//        when(mockOrderProductService.list(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure ProductFeignClient.getProductListByProductIds(...).
        final Product product = new Product();
        product.setProductId(0L);
        product.setGoodsId(0L);
        product.setCategoryId1(0);
        product.setCategoryId2(0);
        product.setCategoryId3(0);
        final List<Product> products = Arrays.asList(product);
//        when(mockProductFeignClient.getProductListByProductIds(Arrays.asList(0L))).thenReturn(products);

        // Configure ScrmFacade.getJobNumberFile(...).
        final WechatResult<Boolean> booleanWechatResult = new WechatResult<>();
        booleanWechatResult.setSuccess(false);
        final ErrorContext errorContext = new ErrorContext();
        final ErrorStack errorStack = new ErrorStack();
        errorStack.setErrorCode("errorCode");
        errorContext.setErrorStack(Arrays.asList(errorStack));
        booleanWechatResult.setErrorContext(errorContext);
        booleanWechatResult.setData(false);
//        when(mockScrmFacade.getJobNumberFile("userMobile")).thenReturn(booleanWechatResult);

        // Configure WecatFacade.getUserInfoByUserIdList(...).
        final WechatResult<List<WechatUserInfoPO>> listWechatResult = new WechatResult<>();
        listWechatResult.setSuccess(false);
        final ErrorContext errorContext1 = new ErrorContext();
        final ErrorStack errorStack1 = new ErrorStack();
        errorContext1.setErrorStack(Arrays.asList(errorStack1));
        listWechatResult.setErrorContext(errorContext1);
        final WechatUserInfoPO wechatUserInfoPO = new WechatUserInfoPO();
        wechatUserInfoPO.setAvatar("avatar");
        listWechatResult.setData(Arrays.asList(wechatUserInfoPO));
        final WechatUserInfoRequest var1 = new WechatUserInfoRequest();
        var1.setWechatUserIdList(Arrays.asList("value"));
//        when(mockWecatFacade.getUserInfoByUserIdList(var1)).thenReturn(listWechatResult);

        // Configure TradeDocumentIntegration.orderDataPushMatch(...).
        final OrderDataPushHitObject orderDataPushHitObject = new OrderDataPushHitObject();
        orderDataPushHitObject.setDesc("desc");
        orderDataPushHitObject.setCode("code");
        orderDataPushHitObject.setValue(0.0);
        orderDataPushHitObject.setReason("reason");
        final List<OrderDataPushHitObject> orderDataPushHitObjects = Arrays.asList(orderDataPushHitObject);
        final OrderTradeMatchDTO orderTradeMatchDTO = new OrderTradeMatchDTO();
        orderTradeMatchDTO.setStoreId("storeId");
        orderTradeMatchDTO.setOrderPattern(0);
        orderTradeMatchDTO.setPerformanceModes("performanceModes");
        orderTradeMatchDTO.setOrderAmount(new BigDecimal("0.00"));
        orderTradeMatchDTO.setGoodsCategoryId1("categoryId1");
        orderTradeMatchDTO.setGoodsCategoryId2("categoryId2");
        orderTradeMatchDTO.setGoodsCategoryId3("categoryId3");
        orderTradeMatchDTO.setPaymentCode("paymentCode");
        orderTradeMatchDTO.setBranchCode("branch");
        orderTradeMatchDTO.setZoneCode("zoneCode");
        orderTradeMatchDTO.setAreaCode("areaCode");
        orderTradeMatchDTO.setTonWeight(new BigDecimal("0.00"));
        orderTradeMatchDTO.setIsAgricMcnUser(0);
        orderTradeMatchDTO.setUserTag("userTag");
        orderTradeMatchDTO.setChannel("channel");
        orderTradeMatchDTO.setGoodsId(0L);
        orderTradeMatchDTO.setProductId(0L);
//        when(mockTradeDocumentIntegration.orderDataPushMatch(orderTradeMatchDTO)).thenReturn(orderDataPushHitObjects);

        // Configure BmsOrgFeignIntegration.listBranchesByHrOrgCodesV2(...).
        final OrganizationBaseVO organizationBaseVO = new OrganizationBaseVO();
        organizationBaseVO.setBatchNo("batchNo");
        organizationBaseVO.setOrgId(0);
        organizationBaseVO.setHrOrgId(0);
        organizationBaseVO.setOrgCode("orgCode");
        organizationBaseVO.setOrgName("orgName");
        final List<OrganizationBaseVO> organizationBaseVOS = Arrays.asList(organizationBaseVO);
//        when(mockBmsOrgFeignIntegration.listBranchesByHrOrgCodesV2("areaCode")).thenReturn(organizationBaseVOS);

//        when(mockOrderProductMapper.countPayOrderNum("orderSn", Arrays.asList("value"),
//                Arrays.asList("value"))).thenReturn(0);
//        when(mockErpIntegration.unitConvert(Arrays.asList(new HashMap<>()))).thenReturn(Collections.emptyList());

        // Configure DbcServiceFeign.managerCommissionByCommodities(...).
        final com.cfpamf.common.ms.result.ErrorContext errorContext2 = new com.cfpamf.common.ms.result.ErrorContext();
        errorContext2.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext2.setThirdPartyError("thirdPartyError");
        final ManagerCommissionItemVO managerCommissionItemVO = new ManagerCommissionItemVO();
        managerCommissionItemVO.setOrderId("orderId");
        managerCommissionItemVO.setCommodityCode("commodityCode");
        managerCommissionItemVO.setManagerCommission(new BigDecimal("0.00"));
        final Result<List<ManagerCommissionItemVO>> listResult = new Result<>(false, errorContext2,
                Arrays.asList(managerCommissionItemVO));
//        when(mockDbcServiceFeign.managerCommissionByCommodities("orderSn", 5, "storeId")).thenReturn(listResult);

        // Configure DbcServiceFeign.freightCostByCommodities(...).
        final com.cfpamf.common.ms.result.ErrorContext errorContext3 = new com.cfpamf.common.ms.result.ErrorContext();
        errorContext3.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext3.setThirdPartyError("thirdPartyError");
        final FreightCostItemVO freightCostItemVO = new FreightCostItemVO();
        freightCostItemVO.setOrderId("orderId");
        freightCostItemVO.setCommodityCode("commodityCode");
        freightCostItemVO.setFreightCost(new BigDecimal("0.00"));
        final Result<List<FreightCostItemVO>> listResult1 = new Result<>(false, errorContext3,
                Arrays.asList(freightCostItemVO));
//        when(mockDbcServiceFeign.freightCostByCommodities("orderSn", 5, "storeId")).thenReturn(listResult1);

        // Configure DbcServiceFeign.getListByOrderId(...).
        final com.cfpamf.common.ms.result.ErrorContext errorContext4 = new com.cfpamf.common.ms.result.ErrorContext();
        errorContext4.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext4.setThirdPartyError("thirdPartyError");
        final DbcDistributionSaleOrderVO dbcDistributionSaleOrderVO = new DbcDistributionSaleOrderVO();
        dbcDistributionSaleOrderVO.setId(0L);
        dbcDistributionSaleOrderVO.setOrderId("orderId");
        dbcDistributionSaleOrderVO.setOrderCommission(new BigDecimal("0.00"));
        dbcDistributionSaleOrderVO.setCommodityCode("commodityCode");
        dbcDistributionSaleOrderVO.setCommissionOrderType(0);
        final Result<List<DbcDistributionSaleOrderVO>> listResult2 = new Result<>(false, errorContext4,
                Arrays.asList(dbcDistributionSaleOrderVO));
//        when(mockDbcServiceFeign.getListByOrderId("orderSn")).thenReturn(listResult2);

//        when(mockOrderProductMapper.airEnergySalesCount("branch", "areaCode")).thenReturn(0);

        // Configure ICommonMqEventService.saveEvent(...).
        final GoodNewsMessage body = new GoodNewsMessage();
        body.setReportType("reportType");
        body.setBranch("branch");
        body.setMarketingCode("2023MALL_PAY_ORDER");
        body.setReportLevel(0);
        body.setPushParam("pushParam");
        body.setStatisticalObject("belongerEmployeeNo");
        body.setStatisticalTime("orderSn");
        body.setParamAddStatisticalCount(false);
        body.setMessageId("messageId");
        body.setText("text");
//        when(mockCommonMqEventService.saveEvent(body, "agric.performance.goodNews.exchange")).thenReturn(0L);

        // Run the test
//        orderDataPushServiceImplUnderTest.paySuccessDataPush("orderSn");

        // Verify the results
//        verify(mockRabbitMQUtils).sendByEventId(0L);
    }

    @Test
    public void testPaySuccessDataPush_DbcServiceFeignManagerCommissionByCommoditiesReturnsNull() {
        // Setup
        // Configure IOrderService.getOne(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderSn("orderSn");
        orderPO.setUserMobile("userMobile");
        orderPO.setStoreId(0L);
        orderPO.setMemberName("customerName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setOrderType(0);
        orderPO.setOrderPattern(0);
        orderPO.setEnabledFlag(0);
        orderPO.setChannel("channel");
        orderPO.setPerformanceModes("performanceModes");
//        when(mockOrderService.getOne(any(LambdaQueryWrapper.class))).thenReturn(orderPO);

        // Configure IOrderPerformanceBelongsService.getOne(...).
        final OrderPerformanceBelongsPO orderPerformanceBelongsPO = new OrderPerformanceBelongsPO();
        orderPerformanceBelongsPO.setOrderSn("orderSn");
        orderPerformanceBelongsPO.setBelongerName("belongerName");
        orderPerformanceBelongsPO.setBelongerEmployeeNo("belongerEmployeeNo");
        orderPerformanceBelongsPO.setEmployeeBranchCode("branch");
        orderPerformanceBelongsPO.setEmployeeBranchName("branchName");
        orderPerformanceBelongsPO.setBindStateCode(0);
//        when(mockOrderPerformanceBelongsService.getOne(any(LambdaQueryWrapper.class)))
//                .thenReturn(orderPerformanceBelongsPO);

        // Configure HrmsIntegration.getBranchRelation(...).
        final BranchRelationVO branchRelationVO = new BranchRelationVO();
        branchRelationVO.setBranchCode("branchCode");
        branchRelationVO.setZoneName("zoneName");
        branchRelationVO.setZoneCode("zoneCode");
        branchRelationVO.setRegionCode("areaCode");
        branchRelationVO.setRegionName("areaName");
//        when(mockHrmsIntegration.getBranchRelation("branch")).thenReturn(branchRelationVO);

        // Configure IOrderProductService.list(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setGoodsCategoryPath("goodsCategoryPath");
        orderProductPO.setProductId(0L);
        orderProductPO.setWeight(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO.setSkuMaterialName("skuMaterialName");
        orderProductPO.setChannelSkuUnit("channelSkuUnit");
        orderProductPO.setChannelSkuId("channelSkuId");
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO);
//        when(mockOrderProductService.list(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure ProductFeignClient.getProductListByProductIds(...).
        final Product product = new Product();
        product.setProductId(0L);
        product.setGoodsId(0L);
        product.setCategoryId1(0);
        product.setCategoryId2(0);
        product.setCategoryId3(0);
        final List<Product> products = Arrays.asList(product);
//        when(mockProductFeignClient.getProductListByProductIds(Arrays.asList(0L))).thenReturn(products);

        // Configure ScrmFacade.getJobNumberFile(...).
        final WechatResult<Boolean> booleanWechatResult = new WechatResult<>();
        booleanWechatResult.setSuccess(false);
        final ErrorContext errorContext = new ErrorContext();
        final ErrorStack errorStack = new ErrorStack();
        errorStack.setErrorCode("errorCode");
        errorContext.setErrorStack(Arrays.asList(errorStack));
        booleanWechatResult.setErrorContext(errorContext);
        booleanWechatResult.setData(false);
//        when(mockScrmFacade.getJobNumberFile("userMobile")).thenReturn(booleanWechatResult);

        // Configure WecatFacade.getUserInfoByUserIdList(...).
        final WechatResult<List<WechatUserInfoPO>> listWechatResult = new WechatResult<>();
        listWechatResult.setSuccess(false);
        final ErrorContext errorContext1 = new ErrorContext();
        final ErrorStack errorStack1 = new ErrorStack();
        errorContext1.setErrorStack(Arrays.asList(errorStack1));
        listWechatResult.setErrorContext(errorContext1);
        final WechatUserInfoPO wechatUserInfoPO = new WechatUserInfoPO();
        wechatUserInfoPO.setAvatar("avatar");
        listWechatResult.setData(Arrays.asList(wechatUserInfoPO));
        final WechatUserInfoRequest var1 = new WechatUserInfoRequest();
        var1.setWechatUserIdList(Arrays.asList("value"));
//        when(mockWecatFacade.getUserInfoByUserIdList(var1)).thenReturn(listWechatResult);

        // Configure TradeDocumentIntegration.orderDataPushMatch(...).
        final OrderDataPushHitObject orderDataPushHitObject = new OrderDataPushHitObject();
        orderDataPushHitObject.setDesc("desc");
        orderDataPushHitObject.setCode("code");
        orderDataPushHitObject.setValue(0.0);
        orderDataPushHitObject.setReason("reason");
        final List<OrderDataPushHitObject> orderDataPushHitObjects = Arrays.asList(orderDataPushHitObject);
        final OrderTradeMatchDTO orderTradeMatchDTO = new OrderTradeMatchDTO();
        orderTradeMatchDTO.setStoreId("storeId");
        orderTradeMatchDTO.setOrderPattern(0);
        orderTradeMatchDTO.setPerformanceModes("performanceModes");
        orderTradeMatchDTO.setOrderAmount(new BigDecimal("0.00"));
        orderTradeMatchDTO.setGoodsCategoryId1("categoryId1");
        orderTradeMatchDTO.setGoodsCategoryId2("categoryId2");
        orderTradeMatchDTO.setGoodsCategoryId3("categoryId3");
        orderTradeMatchDTO.setPaymentCode("paymentCode");
        orderTradeMatchDTO.setBranchCode("branch");
        orderTradeMatchDTO.setZoneCode("zoneCode");
        orderTradeMatchDTO.setAreaCode("areaCode");
        orderTradeMatchDTO.setTonWeight(new BigDecimal("0.00"));
        orderTradeMatchDTO.setIsAgricMcnUser(0);
        orderTradeMatchDTO.setUserTag("userTag");
        orderTradeMatchDTO.setChannel("channel");
        orderTradeMatchDTO.setGoodsId(0L);
        orderTradeMatchDTO.setProductId(0L);
//        when(mockTradeDocumentIntegration.orderDataPushMatch(orderTradeMatchDTO)).thenReturn(orderDataPushHitObjects);

        // Configure BmsOrgFeignIntegration.listBranchesByHrOrgCodesV2(...).
        final OrganizationBaseVO organizationBaseVO = new OrganizationBaseVO();
        organizationBaseVO.setBatchNo("batchNo");
        organizationBaseVO.setOrgId(0);
        organizationBaseVO.setHrOrgId(0);
        organizationBaseVO.setOrgCode("orgCode");
        organizationBaseVO.setOrgName("orgName");
        final List<OrganizationBaseVO> organizationBaseVOS = Arrays.asList(organizationBaseVO);
//        when(mockBmsOrgFeignIntegration.listBranchesByHrOrgCodesV2("areaCode")).thenReturn(organizationBaseVOS);

//        when(mockOrderProductMapper.countPayOrderNum("orderSn", Arrays.asList("value"),
//                Arrays.asList("value"))).thenReturn(0);

        // Configure IBzOrderProductCombinationService.getOne(...).
        final BzOrderProductCombinationPO bzOrderProductCombinationPO = BzOrderProductCombinationPO.builder()
                .orderSn("orderSn")
                .mainProductId(0L)
                .build();
//        when(mockOrderProductCombinationService.getOne(any(LambdaQueryWrapper.class)))
//                .thenReturn(bzOrderProductCombinationPO);

//        when(mockDbcServiceFeign.managerCommissionByCommodities("orderSn", 5, "storeId")).thenReturn(null);

        // Configure DbcServiceFeign.getListByOrderId(...).
        final com.cfpamf.common.ms.result.ErrorContext errorContext2 = new com.cfpamf.common.ms.result.ErrorContext();
        errorContext2.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext2.setThirdPartyError("thirdPartyError");
        final DbcDistributionSaleOrderVO dbcDistributionSaleOrderVO = new DbcDistributionSaleOrderVO();
        dbcDistributionSaleOrderVO.setId(0L);
        dbcDistributionSaleOrderVO.setOrderId("orderId");
        dbcDistributionSaleOrderVO.setOrderCommission(new BigDecimal("0.00"));
        dbcDistributionSaleOrderVO.setCommodityCode("commodityCode");
        dbcDistributionSaleOrderVO.setCommissionOrderType(0);
        final Result<List<DbcDistributionSaleOrderVO>> listResult = new Result<>(false, errorContext2,
                Arrays.asList(dbcDistributionSaleOrderVO));
//        when(mockDbcServiceFeign.getListByOrderId("orderSn")).thenReturn(listResult);

//        when(mockOrderProductMapper.airEnergySalesCount("branch", "areaCode")).thenReturn(0);

        // Configure ICommonMqEventService.saveEvent(...).
        final GoodNewsMessage body = new GoodNewsMessage();
        body.setReportType("reportType");
        body.setBranch("branch");
        body.setMarketingCode("2023MALL_PAY_ORDER");
        body.setReportLevel(0);
        body.setPushParam("pushParam");
        body.setStatisticalObject("belongerEmployeeNo");
        body.setStatisticalTime("orderSn");
        body.setParamAddStatisticalCount(false);
        body.setMessageId("messageId");
        body.setText("text");
//        when(mockCommonMqEventService.saveEvent(body, "agric.performance.goodNews.exchange")).thenReturn(0L);

        // Run the test
//        orderDataPushServiceImplUnderTest.paySuccessDataPush("orderSn");

        // Verify the results
//        verify(mockRabbitMQUtils).sendByEventId(0L);
    }

    @Test
    public void testPaySuccessDataPush_DbcServiceFeignManagerCommissionByCommoditiesReturnsNoItems() {
        // Setup
        // Configure IOrderService.getOne(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderSn("orderSn");
        orderPO.setUserMobile("userMobile");
        orderPO.setStoreId(0L);
        orderPO.setMemberName("customerName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setOrderType(0);
        orderPO.setOrderPattern(0);
        orderPO.setEnabledFlag(0);
        orderPO.setChannel("channel");
        orderPO.setPerformanceModes("performanceModes");
//        when(mockOrderService.getOne(any(LambdaQueryWrapper.class))).thenReturn(orderPO);

        // Configure IOrderPerformanceBelongsService.getOne(...).
        final OrderPerformanceBelongsPO orderPerformanceBelongsPO = new OrderPerformanceBelongsPO();
        orderPerformanceBelongsPO.setOrderSn("orderSn");
        orderPerformanceBelongsPO.setBelongerName("belongerName");
        orderPerformanceBelongsPO.setBelongerEmployeeNo("belongerEmployeeNo");
        orderPerformanceBelongsPO.setEmployeeBranchCode("branch");
        orderPerformanceBelongsPO.setEmployeeBranchName("branchName");
        orderPerformanceBelongsPO.setBindStateCode(0);
//        when(mockOrderPerformanceBelongsService.getOne(any(LambdaQueryWrapper.class)))
//                .thenReturn(orderPerformanceBelongsPO);

        // Configure HrmsIntegration.getBranchRelation(...).
        final BranchRelationVO branchRelationVO = new BranchRelationVO();
        branchRelationVO.setBranchCode("branchCode");
        branchRelationVO.setZoneName("zoneName");
        branchRelationVO.setZoneCode("zoneCode");
        branchRelationVO.setRegionCode("areaCode");
        branchRelationVO.setRegionName("areaName");
//        when(mockHrmsIntegration.getBranchRelation("branch")).thenReturn(branchRelationVO);

        // Configure IOrderProductService.list(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setGoodsCategoryPath("goodsCategoryPath");
        orderProductPO.setProductId(0L);
        orderProductPO.setWeight(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO.setSkuMaterialName("skuMaterialName");
        orderProductPO.setChannelSkuUnit("channelSkuUnit");
        orderProductPO.setChannelSkuId("channelSkuId");
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO);
//        when(mockOrderProductService.list(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure ProductFeignClient.getProductListByProductIds(...).
        final Product product = new Product();
        product.setProductId(0L);
        product.setGoodsId(0L);
        product.setCategoryId1(0);
        product.setCategoryId2(0);
        product.setCategoryId3(0);
        final List<Product> products = Arrays.asList(product);
//        when(mockProductFeignClient.getProductListByProductIds(Arrays.asList(0L))).thenReturn(products);

        // Configure ScrmFacade.getJobNumberFile(...).
        final WechatResult<Boolean> booleanWechatResult = new WechatResult<>();
        booleanWechatResult.setSuccess(false);
        final ErrorContext errorContext = new ErrorContext();
        final ErrorStack errorStack = new ErrorStack();
        errorStack.setErrorCode("errorCode");
        errorContext.setErrorStack(Arrays.asList(errorStack));
        booleanWechatResult.setErrorContext(errorContext);
        booleanWechatResult.setData(false);
//        when(mockScrmFacade.getJobNumberFile("userMobile")).thenReturn(booleanWechatResult);

        // Configure WecatFacade.getUserInfoByUserIdList(...).
        final WechatResult<List<WechatUserInfoPO>> listWechatResult = new WechatResult<>();
        listWechatResult.setSuccess(false);
        final ErrorContext errorContext1 = new ErrorContext();
        final ErrorStack errorStack1 = new ErrorStack();
        errorContext1.setErrorStack(Arrays.asList(errorStack1));
        listWechatResult.setErrorContext(errorContext1);
        final WechatUserInfoPO wechatUserInfoPO = new WechatUserInfoPO();
        wechatUserInfoPO.setAvatar("avatar");
        listWechatResult.setData(Arrays.asList(wechatUserInfoPO));
        final WechatUserInfoRequest var1 = new WechatUserInfoRequest();
        var1.setWechatUserIdList(Arrays.asList("value"));
//        when(mockWecatFacade.getUserInfoByUserIdList(var1)).thenReturn(listWechatResult);

        // Configure TradeDocumentIntegration.orderDataPushMatch(...).
        final OrderDataPushHitObject orderDataPushHitObject = new OrderDataPushHitObject();
        orderDataPushHitObject.setDesc("desc");
        orderDataPushHitObject.setCode("code");
        orderDataPushHitObject.setValue(0.0);
        orderDataPushHitObject.setReason("reason");
        final List<OrderDataPushHitObject> orderDataPushHitObjects = Arrays.asList(orderDataPushHitObject);
        final OrderTradeMatchDTO orderTradeMatchDTO = new OrderTradeMatchDTO();
        orderTradeMatchDTO.setStoreId("storeId");
        orderTradeMatchDTO.setOrderPattern(0);
        orderTradeMatchDTO.setPerformanceModes("performanceModes");
        orderTradeMatchDTO.setOrderAmount(new BigDecimal("0.00"));
        orderTradeMatchDTO.setGoodsCategoryId1("categoryId1");
        orderTradeMatchDTO.setGoodsCategoryId2("categoryId2");
        orderTradeMatchDTO.setGoodsCategoryId3("categoryId3");
        orderTradeMatchDTO.setPaymentCode("paymentCode");
        orderTradeMatchDTO.setBranchCode("branch");
        orderTradeMatchDTO.setZoneCode("zoneCode");
        orderTradeMatchDTO.setAreaCode("areaCode");
        orderTradeMatchDTO.setTonWeight(new BigDecimal("0.00"));
        orderTradeMatchDTO.setIsAgricMcnUser(0);
        orderTradeMatchDTO.setUserTag("userTag");
        orderTradeMatchDTO.setChannel("channel");
        orderTradeMatchDTO.setGoodsId(0L);
        orderTradeMatchDTO.setProductId(0L);
//        when(mockTradeDocumentIntegration.orderDataPushMatch(orderTradeMatchDTO)).thenReturn(orderDataPushHitObjects);

        // Configure BmsOrgFeignIntegration.listBranchesByHrOrgCodesV2(...).
        final OrganizationBaseVO organizationBaseVO = new OrganizationBaseVO();
        organizationBaseVO.setBatchNo("batchNo");
        organizationBaseVO.setOrgId(0);
        organizationBaseVO.setHrOrgId(0);
        organizationBaseVO.setOrgCode("orgCode");
        organizationBaseVO.setOrgName("orgName");
        final List<OrganizationBaseVO> organizationBaseVOS = Arrays.asList(organizationBaseVO);
//        when(mockBmsOrgFeignIntegration.listBranchesByHrOrgCodesV2("areaCode")).thenReturn(organizationBaseVOS);

//        when(mockOrderProductMapper.countPayOrderNum("orderSn", Arrays.asList("value"),
//                Arrays.asList("value"))).thenReturn(0);

        // Configure IBzOrderProductCombinationService.getOne(...).
        final BzOrderProductCombinationPO bzOrderProductCombinationPO = BzOrderProductCombinationPO.builder()
                .orderSn("orderSn")
                .mainProductId(0L)
                .build();
//        when(mockOrderProductCombinationService.getOne(any(LambdaQueryWrapper.class)))
//                .thenReturn(bzOrderProductCombinationPO);

//        when(mockDbcServiceFeign.managerCommissionByCommodities("orderSn", 5, "storeId"))
//                .thenReturn(Result.ok(Collections.emptyList()));

        // Configure DbcServiceFeign.freightCostByCommodities(...).
        final com.cfpamf.common.ms.result.ErrorContext errorContext2 = new com.cfpamf.common.ms.result.ErrorContext();
        errorContext2.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext2.setThirdPartyError("thirdPartyError");
        final FreightCostItemVO freightCostItemVO = new FreightCostItemVO();
        freightCostItemVO.setOrderId("orderId");
        freightCostItemVO.setCommodityCode("commodityCode");
        freightCostItemVO.setFreightCost(new BigDecimal("0.00"));
        final Result<List<FreightCostItemVO>> listResult = new Result<>(false, errorContext2,
                Arrays.asList(freightCostItemVO));
//        when(mockDbcServiceFeign.freightCostByCommodities("orderSn", 5, "storeId")).thenReturn(listResult);

        // Configure DbcServiceFeign.getListByOrderId(...).
        final com.cfpamf.common.ms.result.ErrorContext errorContext3 = new com.cfpamf.common.ms.result.ErrorContext();
        errorContext3.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext3.setThirdPartyError("thirdPartyError");
        final DbcDistributionSaleOrderVO dbcDistributionSaleOrderVO = new DbcDistributionSaleOrderVO();
        dbcDistributionSaleOrderVO.setId(0L);
        dbcDistributionSaleOrderVO.setOrderId("orderId");
        dbcDistributionSaleOrderVO.setOrderCommission(new BigDecimal("0.00"));
        dbcDistributionSaleOrderVO.setCommodityCode("commodityCode");
        dbcDistributionSaleOrderVO.setCommissionOrderType(0);
        final Result<List<DbcDistributionSaleOrderVO>> listResult1 = new Result<>(false, errorContext3,
                Arrays.asList(dbcDistributionSaleOrderVO));
//        when(mockDbcServiceFeign.getListByOrderId("orderSn")).thenReturn(listResult1);
//
//        when(mockOrderProductMapper.airEnergySalesCount("branch", "areaCode")).thenReturn(0);

        // Configure ICommonMqEventService.saveEvent(...).
        final GoodNewsMessage body = new GoodNewsMessage();
        body.setReportType("reportType");
        body.setBranch("branch");
        body.setMarketingCode("2023MALL_PAY_ORDER");
        body.setReportLevel(0);
        body.setPushParam("pushParam");
        body.setStatisticalObject("belongerEmployeeNo");
        body.setStatisticalTime("orderSn");
        body.setParamAddStatisticalCount(false);
        body.setMessageId("messageId");
        body.setText("text");
//        when(mockCommonMqEventService.saveEvent(body, "agric.performance.goodNews.exchange")).thenReturn(0L);

        // Run the test
//        orderDataPushServiceImplUnderTest.paySuccessDataPush("orderSn");

        // Verify the results
//        verify(mockRabbitMQUtils).sendByEventId(0L);
    }

    @Test
    public void testPaySuccessDataPush_DbcServiceFeignFreightCostByCommoditiesReturnsNull() {
        // Setup
        // Configure IOrderService.getOne(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderSn("orderSn");
        orderPO.setUserMobile("userMobile");
        orderPO.setStoreId(0L);
        orderPO.setMemberName("customerName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setOrderType(0);
        orderPO.setOrderPattern(0);
        orderPO.setEnabledFlag(0);
        orderPO.setChannel("channel");
        orderPO.setPerformanceModes("performanceModes");
//        when(mockOrderService.getOne(any(LambdaQueryWrapper.class))).thenReturn(orderPO);

        // Configure IOrderPerformanceBelongsService.getOne(...).
        final OrderPerformanceBelongsPO orderPerformanceBelongsPO = new OrderPerformanceBelongsPO();
        orderPerformanceBelongsPO.setOrderSn("orderSn");
        orderPerformanceBelongsPO.setBelongerName("belongerName");
        orderPerformanceBelongsPO.setBelongerEmployeeNo("belongerEmployeeNo");
        orderPerformanceBelongsPO.setEmployeeBranchCode("branch");
        orderPerformanceBelongsPO.setEmployeeBranchName("branchName");
        orderPerformanceBelongsPO.setBindStateCode(0);
//        when(mockOrderPerformanceBelongsService.getOne(any(LambdaQueryWrapper.class)))
//                .thenReturn(orderPerformanceBelongsPO);

        // Configure HrmsIntegration.getBranchRelation(...).
        final BranchRelationVO branchRelationVO = new BranchRelationVO();
        branchRelationVO.setBranchCode("branchCode");
        branchRelationVO.setZoneName("zoneName");
        branchRelationVO.setZoneCode("zoneCode");
        branchRelationVO.setRegionCode("areaCode");
        branchRelationVO.setRegionName("areaName");
//        when(mockHrmsIntegration.getBranchRelation("branch")).thenReturn(branchRelationVO);

        // Configure IOrderProductService.list(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setGoodsCategoryPath("goodsCategoryPath");
        orderProductPO.setProductId(0L);
        orderProductPO.setWeight(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO.setSkuMaterialName("skuMaterialName");
        orderProductPO.setChannelSkuUnit("channelSkuUnit");
        orderProductPO.setChannelSkuId("channelSkuId");
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO);
//        when(mockOrderProductService.list(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure ProductFeignClient.getProductListByProductIds(...).
        final Product product = new Product();
        product.setProductId(0L);
        product.setGoodsId(0L);
        product.setCategoryId1(0);
        product.setCategoryId2(0);
        product.setCategoryId3(0);
        final List<Product> products = Arrays.asList(product);
//        when(mockProductFeignClient.getProductListByProductIds(Arrays.asList(0L))).thenReturn(products);

        // Configure ScrmFacade.getJobNumberFile(...).
        final WechatResult<Boolean> booleanWechatResult = new WechatResult<>();
        booleanWechatResult.setSuccess(false);
        final ErrorContext errorContext = new ErrorContext();
        final ErrorStack errorStack = new ErrorStack();
        errorStack.setErrorCode("errorCode");
        errorContext.setErrorStack(Arrays.asList(errorStack));
        booleanWechatResult.setErrorContext(errorContext);
        booleanWechatResult.setData(false);
//        when(mockScrmFacade.getJobNumberFile("userMobile")).thenReturn(booleanWechatResult);

        // Configure WecatFacade.getUserInfoByUserIdList(...).
        final WechatResult<List<WechatUserInfoPO>> listWechatResult = new WechatResult<>();
        listWechatResult.setSuccess(false);
        final ErrorContext errorContext1 = new ErrorContext();
        final ErrorStack errorStack1 = new ErrorStack();
        errorContext1.setErrorStack(Arrays.asList(errorStack1));
        listWechatResult.setErrorContext(errorContext1);
        final WechatUserInfoPO wechatUserInfoPO = new WechatUserInfoPO();
        wechatUserInfoPO.setAvatar("avatar");
        listWechatResult.setData(Arrays.asList(wechatUserInfoPO));
        final WechatUserInfoRequest var1 = new WechatUserInfoRequest();
        var1.setWechatUserIdList(Arrays.asList("value"));
//        when(mockWecatFacade.getUserInfoByUserIdList(var1)).thenReturn(listWechatResult);

        // Configure TradeDocumentIntegration.orderDataPushMatch(...).
        final OrderDataPushHitObject orderDataPushHitObject = new OrderDataPushHitObject();
        orderDataPushHitObject.setDesc("desc");
        orderDataPushHitObject.setCode("code");
        orderDataPushHitObject.setValue(0.0);
        orderDataPushHitObject.setReason("reason");
        final List<OrderDataPushHitObject> orderDataPushHitObjects = Arrays.asList(orderDataPushHitObject);
        final OrderTradeMatchDTO orderTradeMatchDTO = new OrderTradeMatchDTO();
        orderTradeMatchDTO.setStoreId("storeId");
        orderTradeMatchDTO.setOrderPattern(0);
        orderTradeMatchDTO.setPerformanceModes("performanceModes");
        orderTradeMatchDTO.setOrderAmount(new BigDecimal("0.00"));
        orderTradeMatchDTO.setGoodsCategoryId1("categoryId1");
        orderTradeMatchDTO.setGoodsCategoryId2("categoryId2");
        orderTradeMatchDTO.setGoodsCategoryId3("categoryId3");
        orderTradeMatchDTO.setPaymentCode("paymentCode");
        orderTradeMatchDTO.setBranchCode("branch");
        orderTradeMatchDTO.setZoneCode("zoneCode");
        orderTradeMatchDTO.setAreaCode("areaCode");
        orderTradeMatchDTO.setTonWeight(new BigDecimal("0.00"));
        orderTradeMatchDTO.setIsAgricMcnUser(0);
        orderTradeMatchDTO.setUserTag("userTag");
        orderTradeMatchDTO.setChannel("channel");
        orderTradeMatchDTO.setGoodsId(0L);
        orderTradeMatchDTO.setProductId(0L);
//        when(mockTradeDocumentIntegration.orderDataPushMatch(orderTradeMatchDTO)).thenReturn(orderDataPushHitObjects);

        // Configure BmsOrgFeignIntegration.listBranchesByHrOrgCodesV2(...).
        final OrganizationBaseVO organizationBaseVO = new OrganizationBaseVO();
        organizationBaseVO.setBatchNo("batchNo");
        organizationBaseVO.setOrgId(0);
        organizationBaseVO.setHrOrgId(0);
        organizationBaseVO.setOrgCode("orgCode");
        organizationBaseVO.setOrgName("orgName");
        final List<OrganizationBaseVO> organizationBaseVOS = Arrays.asList(organizationBaseVO);
//        when(mockBmsOrgFeignIntegration.listBranchesByHrOrgCodesV2("areaCode")).thenReturn(organizationBaseVOS);
//
//        when(mockOrderProductMapper.countPayOrderNum("orderSn", Arrays.asList("value"),
//                Arrays.asList("value"))).thenReturn(0);

        // Configure IBzOrderProductCombinationService.getOne(...).
        final BzOrderProductCombinationPO bzOrderProductCombinationPO = BzOrderProductCombinationPO.builder()
                .orderSn("orderSn")
                .mainProductId(0L)
                .build();
//        when(mockOrderProductCombinationService.getOne(any(LambdaQueryWrapper.class)))
//                .thenReturn(bzOrderProductCombinationPO);

        // Configure DbcServiceFeign.managerCommissionByCommodities(...).
        final com.cfpamf.common.ms.result.ErrorContext errorContext2 = new com.cfpamf.common.ms.result.ErrorContext();
        errorContext2.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext2.setThirdPartyError("thirdPartyError");
        final ManagerCommissionItemVO managerCommissionItemVO = new ManagerCommissionItemVO();
        managerCommissionItemVO.setOrderId("orderId");
        managerCommissionItemVO.setCommodityCode("commodityCode");
        managerCommissionItemVO.setManagerCommission(new BigDecimal("0.00"));
        final Result<List<ManagerCommissionItemVO>> listResult = new Result<>(false, errorContext2,
                Arrays.asList(managerCommissionItemVO));
//        when(mockDbcServiceFeign.managerCommissionByCommodities("orderSn", 5, "storeId")).thenReturn(listResult);
//
//        when(mockDbcServiceFeign.freightCostByCommodities("orderSn", 5, "storeId")).thenReturn(null);

        // Configure DbcServiceFeign.getListByOrderId(...).
        final com.cfpamf.common.ms.result.ErrorContext errorContext3 = new com.cfpamf.common.ms.result.ErrorContext();
        errorContext3.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext3.setThirdPartyError("thirdPartyError");
        final DbcDistributionSaleOrderVO dbcDistributionSaleOrderVO = new DbcDistributionSaleOrderVO();
        dbcDistributionSaleOrderVO.setId(0L);
        dbcDistributionSaleOrderVO.setOrderId("orderId");
        dbcDistributionSaleOrderVO.setOrderCommission(new BigDecimal("0.00"));
        dbcDistributionSaleOrderVO.setCommodityCode("commodityCode");
        dbcDistributionSaleOrderVO.setCommissionOrderType(0);
        final Result<List<DbcDistributionSaleOrderVO>> listResult1 = new Result<>(false, errorContext3,
                Arrays.asList(dbcDistributionSaleOrderVO));
//        when(mockDbcServiceFeign.getListByOrderId("orderSn")).thenReturn(listResult1);
//
//        when(mockOrderProductMapper.airEnergySalesCount("branch", "areaCode")).thenReturn(0);

        // Configure ICommonMqEventService.saveEvent(...).
        final GoodNewsMessage body = new GoodNewsMessage();
        body.setReportType("reportType");
        body.setBranch("branch");
        body.setMarketingCode("2023MALL_PAY_ORDER");
        body.setReportLevel(0);
        body.setPushParam("pushParam");
        body.setStatisticalObject("belongerEmployeeNo");
        body.setStatisticalTime("orderSn");
        body.setParamAddStatisticalCount(false);
        body.setMessageId("messageId");
        body.setText("text");
//        when(mockCommonMqEventService.saveEvent(body, "agric.performance.goodNews.exchange")).thenReturn(0L);

        // Run the test
//        orderDataPushServiceImplUnderTest.paySuccessDataPush("orderSn");

        // Verify the results
//        verify(mockRabbitMQUtils).sendByEventId(0L);
    }

    @Test
    public void testPaySuccessDataPush_DbcServiceFeignFreightCostByCommoditiesReturnsNoItems() {
        // Setup
        // Configure IOrderService.getOne(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderSn("orderSn");
        orderPO.setUserMobile("userMobile");
        orderPO.setStoreId(0L);
        orderPO.setMemberName("customerName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setOrderType(0);
        orderPO.setOrderPattern(0);
        orderPO.setEnabledFlag(0);
        orderPO.setChannel("channel");
        orderPO.setPerformanceModes("performanceModes");
//        when(mockOrderService.getOne(any(LambdaQueryWrapper.class))).thenReturn(orderPO);

        // Configure IOrderPerformanceBelongsService.getOne(...).
        final OrderPerformanceBelongsPO orderPerformanceBelongsPO = new OrderPerformanceBelongsPO();
        orderPerformanceBelongsPO.setOrderSn("orderSn");
        orderPerformanceBelongsPO.setBelongerName("belongerName");
        orderPerformanceBelongsPO.setBelongerEmployeeNo("belongerEmployeeNo");
        orderPerformanceBelongsPO.setEmployeeBranchCode("branch");
        orderPerformanceBelongsPO.setEmployeeBranchName("branchName");
        orderPerformanceBelongsPO.setBindStateCode(0);
//        when(mockOrderPerformanceBelongsService.getOne(any(LambdaQueryWrapper.class)))
//                .thenReturn(orderPerformanceBelongsPO);

        // Configure HrmsIntegration.getBranchRelation(...).
        final BranchRelationVO branchRelationVO = new BranchRelationVO();
        branchRelationVO.setBranchCode("branchCode");
        branchRelationVO.setZoneName("zoneName");
        branchRelationVO.setZoneCode("zoneCode");
        branchRelationVO.setRegionCode("areaCode");
        branchRelationVO.setRegionName("areaName");
//        when(mockHrmsIntegration.getBranchRelation("branch")).thenReturn(branchRelationVO);

        // Configure IOrderProductService.list(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setGoodsCategoryPath("goodsCategoryPath");
        orderProductPO.setProductId(0L);
        orderProductPO.setWeight(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO.setSkuMaterialName("skuMaterialName");
        orderProductPO.setChannelSkuUnit("channelSkuUnit");
        orderProductPO.setChannelSkuId("channelSkuId");
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO);
//        when(mockOrderProductService.list(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure ProductFeignClient.getProductListByProductIds(...).
        final Product product = new Product();
        product.setProductId(0L);
        product.setGoodsId(0L);
        product.setCategoryId1(0);
        product.setCategoryId2(0);
        product.setCategoryId3(0);
        final List<Product> products = Arrays.asList(product);
//        when(mockProductFeignClient.getProductListByProductIds(Arrays.asList(0L))).thenReturn(products);

        // Configure ScrmFacade.getJobNumberFile(...).
        final WechatResult<Boolean> booleanWechatResult = new WechatResult<>();
        booleanWechatResult.setSuccess(false);
        final ErrorContext errorContext = new ErrorContext();
        final ErrorStack errorStack = new ErrorStack();
        errorStack.setErrorCode("errorCode");
        errorContext.setErrorStack(Arrays.asList(errorStack));
        booleanWechatResult.setErrorContext(errorContext);
        booleanWechatResult.setData(false);
//        when(mockScrmFacade.getJobNumberFile("userMobile")).thenReturn(booleanWechatResult);

        // Configure WecatFacade.getUserInfoByUserIdList(...).
        final WechatResult<List<WechatUserInfoPO>> listWechatResult = new WechatResult<>();
        listWechatResult.setSuccess(false);
        final ErrorContext errorContext1 = new ErrorContext();
        final ErrorStack errorStack1 = new ErrorStack();
        errorContext1.setErrorStack(Arrays.asList(errorStack1));
        listWechatResult.setErrorContext(errorContext1);
        final WechatUserInfoPO wechatUserInfoPO = new WechatUserInfoPO();
        wechatUserInfoPO.setAvatar("avatar");
        listWechatResult.setData(Arrays.asList(wechatUserInfoPO));
        final WechatUserInfoRequest var1 = new WechatUserInfoRequest();
        var1.setWechatUserIdList(Arrays.asList("value"));
//        when(mockWecatFacade.getUserInfoByUserIdList(var1)).thenReturn(listWechatResult);

        // Configure TradeDocumentIntegration.orderDataPushMatch(...).
        final OrderDataPushHitObject orderDataPushHitObject = new OrderDataPushHitObject();
        orderDataPushHitObject.setDesc("desc");
        orderDataPushHitObject.setCode("code");
        orderDataPushHitObject.setValue(0.0);
        orderDataPushHitObject.setReason("reason");
        final List<OrderDataPushHitObject> orderDataPushHitObjects = Arrays.asList(orderDataPushHitObject);
        final OrderTradeMatchDTO orderTradeMatchDTO = new OrderTradeMatchDTO();
        orderTradeMatchDTO.setStoreId("storeId");
        orderTradeMatchDTO.setOrderPattern(0);
        orderTradeMatchDTO.setPerformanceModes("performanceModes");
        orderTradeMatchDTO.setOrderAmount(new BigDecimal("0.00"));
        orderTradeMatchDTO.setGoodsCategoryId1("categoryId1");
        orderTradeMatchDTO.setGoodsCategoryId2("categoryId2");
        orderTradeMatchDTO.setGoodsCategoryId3("categoryId3");
        orderTradeMatchDTO.setPaymentCode("paymentCode");
        orderTradeMatchDTO.setBranchCode("branch");
        orderTradeMatchDTO.setZoneCode("zoneCode");
        orderTradeMatchDTO.setAreaCode("areaCode");
        orderTradeMatchDTO.setTonWeight(new BigDecimal("0.00"));
        orderTradeMatchDTO.setIsAgricMcnUser(0);
        orderTradeMatchDTO.setUserTag("userTag");
        orderTradeMatchDTO.setChannel("channel");
        orderTradeMatchDTO.setGoodsId(0L);
        orderTradeMatchDTO.setProductId(0L);
//        when(mockTradeDocumentIntegration.orderDataPushMatch(orderTradeMatchDTO)).thenReturn(orderDataPushHitObjects);

        // Configure BmsOrgFeignIntegration.listBranchesByHrOrgCodesV2(...).
        final OrganizationBaseVO organizationBaseVO = new OrganizationBaseVO();
        organizationBaseVO.setBatchNo("batchNo");
        organizationBaseVO.setOrgId(0);
        organizationBaseVO.setHrOrgId(0);
        organizationBaseVO.setOrgCode("orgCode");
        organizationBaseVO.setOrgName("orgName");
        final List<OrganizationBaseVO> organizationBaseVOS = Arrays.asList(organizationBaseVO);
//        when(mockBmsOrgFeignIntegration.listBranchesByHrOrgCodesV2("areaCode")).thenReturn(organizationBaseVOS);
//
//        when(mockOrderProductMapper.countPayOrderNum("orderSn", Arrays.asList("value"),
//                Arrays.asList("value"))).thenReturn(0);

        // Configure IBzOrderProductCombinationService.getOne(...).
        final BzOrderProductCombinationPO bzOrderProductCombinationPO = BzOrderProductCombinationPO.builder()
                .orderSn("orderSn")
                .mainProductId(0L)
                .build();
//        when(mockOrderProductCombinationService.getOne(any(LambdaQueryWrapper.class)))
//                .thenReturn(bzOrderProductCombinationPO);

        // Configure DbcServiceFeign.managerCommissionByCommodities(...).
        final com.cfpamf.common.ms.result.ErrorContext errorContext2 = new com.cfpamf.common.ms.result.ErrorContext();
        errorContext2.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext2.setThirdPartyError("thirdPartyError");
        final ManagerCommissionItemVO managerCommissionItemVO = new ManagerCommissionItemVO();
        managerCommissionItemVO.setOrderId("orderId");
        managerCommissionItemVO.setCommodityCode("commodityCode");
        managerCommissionItemVO.setManagerCommission(new BigDecimal("0.00"));
        final Result<List<ManagerCommissionItemVO>> listResult = new Result<>(false, errorContext2,
                Arrays.asList(managerCommissionItemVO));
//        when(mockDbcServiceFeign.managerCommissionByCommodities("orderSn", 5, "storeId")).thenReturn(listResult);
//
//        when(mockDbcServiceFeign.freightCostByCommodities("orderSn", 5, "storeId"))
//                .thenReturn(Result.ok(Collections.emptyList()));

        // Configure DbcServiceFeign.getListByOrderId(...).
        final com.cfpamf.common.ms.result.ErrorContext errorContext3 = new com.cfpamf.common.ms.result.ErrorContext();
        errorContext3.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext3.setThirdPartyError("thirdPartyError");
        final DbcDistributionSaleOrderVO dbcDistributionSaleOrderVO = new DbcDistributionSaleOrderVO();
        dbcDistributionSaleOrderVO.setId(0L);
        dbcDistributionSaleOrderVO.setOrderId("orderId");
        dbcDistributionSaleOrderVO.setOrderCommission(new BigDecimal("0.00"));
        dbcDistributionSaleOrderVO.setCommodityCode("commodityCode");
        dbcDistributionSaleOrderVO.setCommissionOrderType(0);
        final Result<List<DbcDistributionSaleOrderVO>> listResult1 = new Result<>(false, errorContext3,
                Arrays.asList(dbcDistributionSaleOrderVO));
//        when(mockDbcServiceFeign.getListByOrderId("orderSn")).thenReturn(listResult1);
//
//        when(mockOrderProductMapper.airEnergySalesCount("branch", "areaCode")).thenReturn(0);

        // Configure ICommonMqEventService.saveEvent(...).
        final GoodNewsMessage body = new GoodNewsMessage();
        body.setReportType("reportType");
        body.setBranch("branch");
        body.setMarketingCode("2023MALL_PAY_ORDER");
        body.setReportLevel(0);
        body.setPushParam("pushParam");
        body.setStatisticalObject("belongerEmployeeNo");
        body.setStatisticalTime("orderSn");
        body.setParamAddStatisticalCount(false);
        body.setMessageId("messageId");
        body.setText("text");
//        when(mockCommonMqEventService.saveEvent(body, "agric.performance.goodNews.exchange")).thenReturn(0L);

        // Run the test
//        orderDataPushServiceImplUnderTest.paySuccessDataPush("orderSn");

        // Verify the results
//        verify(mockRabbitMQUtils).sendByEventId(0L);
    }

    @Test
    public void testPaySuccessDataPush_DbcServiceFeignGetListByOrderIdReturnsNull() {
        // Setup
        // Configure IOrderService.getOne(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderSn("orderSn");
        orderPO.setUserMobile("userMobile");
        orderPO.setStoreId(0L);
        orderPO.setMemberName("customerName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setOrderType(0);
        orderPO.setOrderPattern(0);
        orderPO.setEnabledFlag(0);
        orderPO.setChannel("channel");
        orderPO.setPerformanceModes("performanceModes");
//        when(mockOrderService.getOne(any(LambdaQueryWrapper.class))).thenReturn(orderPO);

        // Configure IOrderPerformanceBelongsService.getOne(...).
        final OrderPerformanceBelongsPO orderPerformanceBelongsPO = new OrderPerformanceBelongsPO();
        orderPerformanceBelongsPO.setOrderSn("orderSn");
        orderPerformanceBelongsPO.setBelongerName("belongerName");
        orderPerformanceBelongsPO.setBelongerEmployeeNo("belongerEmployeeNo");
        orderPerformanceBelongsPO.setEmployeeBranchCode("branch");
        orderPerformanceBelongsPO.setEmployeeBranchName("branchName");
        orderPerformanceBelongsPO.setBindStateCode(0);
//        when(mockOrderPerformanceBelongsService.getOne(any(LambdaQueryWrapper.class)))
//                .thenReturn(orderPerformanceBelongsPO);

        // Configure HrmsIntegration.getBranchRelation(...).
        final BranchRelationVO branchRelationVO = new BranchRelationVO();
        branchRelationVO.setBranchCode("branchCode");
        branchRelationVO.setZoneName("zoneName");
        branchRelationVO.setZoneCode("zoneCode");
        branchRelationVO.setRegionCode("areaCode");
        branchRelationVO.setRegionName("areaName");
//        when(mockHrmsIntegration.getBranchRelation("branch")).thenReturn(branchRelationVO);

        // Configure IOrderProductService.list(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setGoodsCategoryPath("goodsCategoryPath");
        orderProductPO.setProductId(0L);
        orderProductPO.setWeight(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO.setSkuMaterialName("skuMaterialName");
        orderProductPO.setChannelSkuUnit("channelSkuUnit");
        orderProductPO.setChannelSkuId("channelSkuId");
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO);
//        when(mockOrderProductService.list(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure ProductFeignClient.getProductListByProductIds(...).
        final Product product = new Product();
        product.setProductId(0L);
        product.setGoodsId(0L);
        product.setCategoryId1(0);
        product.setCategoryId2(0);
        product.setCategoryId3(0);
        final List<Product> products = Arrays.asList(product);
//        when(mockProductFeignClient.getProductListByProductIds(Arrays.asList(0L))).thenReturn(products);

        // Configure ScrmFacade.getJobNumberFile(...).
        final WechatResult<Boolean> booleanWechatResult = new WechatResult<>();
        booleanWechatResult.setSuccess(false);
        final ErrorContext errorContext = new ErrorContext();
        final ErrorStack errorStack = new ErrorStack();
        errorStack.setErrorCode("errorCode");
        errorContext.setErrorStack(Arrays.asList(errorStack));
        booleanWechatResult.setErrorContext(errorContext);
        booleanWechatResult.setData(false);
//        when(mockScrmFacade.getJobNumberFile("userMobile")).thenReturn(booleanWechatResult);

        // Configure WecatFacade.getUserInfoByUserIdList(...).
        final WechatResult<List<WechatUserInfoPO>> listWechatResult = new WechatResult<>();
        listWechatResult.setSuccess(false);
        final ErrorContext errorContext1 = new ErrorContext();
        final ErrorStack errorStack1 = new ErrorStack();
        errorContext1.setErrorStack(Arrays.asList(errorStack1));
        listWechatResult.setErrorContext(errorContext1);
        final WechatUserInfoPO wechatUserInfoPO = new WechatUserInfoPO();
        wechatUserInfoPO.setAvatar("avatar");
        listWechatResult.setData(Arrays.asList(wechatUserInfoPO));
        final WechatUserInfoRequest var1 = new WechatUserInfoRequest();
        var1.setWechatUserIdList(Arrays.asList("value"));
//        when(mockWecatFacade.getUserInfoByUserIdList(var1)).thenReturn(listWechatResult);

        // Configure TradeDocumentIntegration.orderDataPushMatch(...).
        final OrderDataPushHitObject orderDataPushHitObject = new OrderDataPushHitObject();
        orderDataPushHitObject.setDesc("desc");
        orderDataPushHitObject.setCode("code");
        orderDataPushHitObject.setValue(0.0);
        orderDataPushHitObject.setReason("reason");
        final List<OrderDataPushHitObject> orderDataPushHitObjects = Arrays.asList(orderDataPushHitObject);
        final OrderTradeMatchDTO orderTradeMatchDTO = new OrderTradeMatchDTO();
        orderTradeMatchDTO.setStoreId("storeId");
        orderTradeMatchDTO.setOrderPattern(0);
        orderTradeMatchDTO.setPerformanceModes("performanceModes");
        orderTradeMatchDTO.setOrderAmount(new BigDecimal("0.00"));
        orderTradeMatchDTO.setGoodsCategoryId1("categoryId1");
        orderTradeMatchDTO.setGoodsCategoryId2("categoryId2");
        orderTradeMatchDTO.setGoodsCategoryId3("categoryId3");
        orderTradeMatchDTO.setPaymentCode("paymentCode");
        orderTradeMatchDTO.setBranchCode("branch");
        orderTradeMatchDTO.setZoneCode("zoneCode");
        orderTradeMatchDTO.setAreaCode("areaCode");
        orderTradeMatchDTO.setTonWeight(new BigDecimal("0.00"));
        orderTradeMatchDTO.setIsAgricMcnUser(0);
        orderTradeMatchDTO.setUserTag("userTag");
        orderTradeMatchDTO.setChannel("channel");
        orderTradeMatchDTO.setGoodsId(0L);
        orderTradeMatchDTO.setProductId(0L);
//        when(mockTradeDocumentIntegration.orderDataPushMatch(orderTradeMatchDTO)).thenReturn(orderDataPushHitObjects);

        // Configure BmsOrgFeignIntegration.listBranchesByHrOrgCodesV2(...).
        final OrganizationBaseVO organizationBaseVO = new OrganizationBaseVO();
        organizationBaseVO.setBatchNo("batchNo");
        organizationBaseVO.setOrgId(0);
        organizationBaseVO.setHrOrgId(0);
        organizationBaseVO.setOrgCode("orgCode");
        organizationBaseVO.setOrgName("orgName");
        final List<OrganizationBaseVO> organizationBaseVOS = Arrays.asList(organizationBaseVO);
//        when(mockBmsOrgFeignIntegration.listBranchesByHrOrgCodesV2("areaCode")).thenReturn(organizationBaseVOS);

//        when(mockOrderProductMapper.countPayOrderNum("orderSn", Arrays.asList("value"),
//                Arrays.asList("value"))).thenReturn(0);

        // Configure IBzOrderProductCombinationService.getOne(...).
        final BzOrderProductCombinationPO bzOrderProductCombinationPO = BzOrderProductCombinationPO.builder()
                .orderSn("orderSn")
                .mainProductId(0L)
                .build();
//        when(mockOrderProductCombinationService.getOne(any(LambdaQueryWrapper.class)))
//                .thenReturn(bzOrderProductCombinationPO);

        // Configure DbcServiceFeign.managerCommissionByCommodities(...).
        final com.cfpamf.common.ms.result.ErrorContext errorContext2 = new com.cfpamf.common.ms.result.ErrorContext();
        errorContext2.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext2.setThirdPartyError("thirdPartyError");
        final ManagerCommissionItemVO managerCommissionItemVO = new ManagerCommissionItemVO();
        managerCommissionItemVO.setOrderId("orderId");
        managerCommissionItemVO.setCommodityCode("commodityCode");
        managerCommissionItemVO.setManagerCommission(new BigDecimal("0.00"));
        final Result<List<ManagerCommissionItemVO>> listResult = new Result<>(false, errorContext2,
                Arrays.asList(managerCommissionItemVO));
//        when(mockDbcServiceFeign.managerCommissionByCommodities("orderSn", 5, "storeId")).thenReturn(listResult);

        // Configure DbcServiceFeign.freightCostByCommodities(...).
        final com.cfpamf.common.ms.result.ErrorContext errorContext3 = new com.cfpamf.common.ms.result.ErrorContext();
        errorContext3.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext3.setThirdPartyError("thirdPartyError");
        final FreightCostItemVO freightCostItemVO = new FreightCostItemVO();
        freightCostItemVO.setOrderId("orderId");
        freightCostItemVO.setCommodityCode("commodityCode");
        freightCostItemVO.setFreightCost(new BigDecimal("0.00"));
        final Result<List<FreightCostItemVO>> listResult1 = new Result<>(false, errorContext3,
                Arrays.asList(freightCostItemVO));
//        when(mockDbcServiceFeign.freightCostByCommodities("orderSn", 5, "storeId")).thenReturn(listResult1);
//
//        when(mockDbcServiceFeign.getListByOrderId("orderSn")).thenReturn(null);
//        when(mockOrderProductMapper.airEnergySalesCount("branch", "areaCode")).thenReturn(0);

        // Configure ICommonMqEventService.saveEvent(...).
        final GoodNewsMessage body = new GoodNewsMessage();
        body.setReportType("reportType");
        body.setBranch("branch");
        body.setMarketingCode("2023MALL_PAY_ORDER");
        body.setReportLevel(0);
        body.setPushParam("pushParam");
        body.setStatisticalObject("belongerEmployeeNo");
        body.setStatisticalTime("orderSn");
        body.setParamAddStatisticalCount(false);
        body.setMessageId("messageId");
        body.setText("text");
//        when(mockCommonMqEventService.saveEvent(body, "agric.performance.goodNews.exchange")).thenReturn(0L);

        // Run the test
//        orderDataPushServiceImplUnderTest.paySuccessDataPush("orderSn");

        // Verify the results
//        verify(mockRabbitMQUtils).sendByEventId(0L);
    }

    @Test
    public void testPaySuccessDataPush_DbcServiceFeignGetListByOrderIdReturnsNoItems() {
        // Setup
        // Configure IOrderService.getOne(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderSn("orderSn");
        orderPO.setUserMobile("userMobile");
        orderPO.setStoreId(0L);
        orderPO.setMemberName("customerName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setOrderType(0);
        orderPO.setOrderPattern(0);
        orderPO.setEnabledFlag(0);
        orderPO.setChannel("channel");
        orderPO.setPerformanceModes("performanceModes");
//        when(mockOrderService.getOne(any(LambdaQueryWrapper.class))).thenReturn(orderPO);

        // Configure IOrderPerformanceBelongsService.getOne(...).
        final OrderPerformanceBelongsPO orderPerformanceBelongsPO = new OrderPerformanceBelongsPO();
        orderPerformanceBelongsPO.setOrderSn("orderSn");
        orderPerformanceBelongsPO.setBelongerName("belongerName");
        orderPerformanceBelongsPO.setBelongerEmployeeNo("belongerEmployeeNo");
        orderPerformanceBelongsPO.setEmployeeBranchCode("branch");
        orderPerformanceBelongsPO.setEmployeeBranchName("branchName");
        orderPerformanceBelongsPO.setBindStateCode(0);
//        when(mockOrderPerformanceBelongsService.getOne(any(LambdaQueryWrapper.class)))
//                .thenReturn(orderPerformanceBelongsPO);

        // Configure HrmsIntegration.getBranchRelation(...).
        final BranchRelationVO branchRelationVO = new BranchRelationVO();
        branchRelationVO.setBranchCode("branchCode");
        branchRelationVO.setZoneName("zoneName");
        branchRelationVO.setZoneCode("zoneCode");
        branchRelationVO.setRegionCode("areaCode");
        branchRelationVO.setRegionName("areaName");
//        when(mockHrmsIntegration.getBranchRelation("branch")).thenReturn(branchRelationVO);

        // Configure IOrderProductService.list(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setGoodsCategoryPath("goodsCategoryPath");
        orderProductPO.setProductId(0L);
        orderProductPO.setWeight(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO.setSkuMaterialName("skuMaterialName");
        orderProductPO.setChannelSkuUnit("channelSkuUnit");
        orderProductPO.setChannelSkuId("channelSkuId");
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO);
//        when(mockOrderProductService.list(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure ProductFeignClient.getProductListByProductIds(...).
        final Product product = new Product();
        product.setProductId(0L);
        product.setGoodsId(0L);
        product.setCategoryId1(0);
        product.setCategoryId2(0);
        product.setCategoryId3(0);
        final List<Product> products = Arrays.asList(product);
//        when(mockProductFeignClient.getProductListByProductIds(Arrays.asList(0L))).thenReturn(products);

        // Configure ScrmFacade.getJobNumberFile(...).
        final WechatResult<Boolean> booleanWechatResult = new WechatResult<>();
        booleanWechatResult.setSuccess(false);
        final ErrorContext errorContext = new ErrorContext();
        final ErrorStack errorStack = new ErrorStack();
        errorStack.setErrorCode("errorCode");
        errorContext.setErrorStack(Arrays.asList(errorStack));
        booleanWechatResult.setErrorContext(errorContext);
        booleanWechatResult.setData(false);
//        when(mockScrmFacade.getJobNumberFile("userMobile")).thenReturn(booleanWechatResult);

        // Configure WecatFacade.getUserInfoByUserIdList(...).
        final WechatResult<List<WechatUserInfoPO>> listWechatResult = new WechatResult<>();
        listWechatResult.setSuccess(false);
        final ErrorContext errorContext1 = new ErrorContext();
        final ErrorStack errorStack1 = new ErrorStack();
        errorContext1.setErrorStack(Arrays.asList(errorStack1));
        listWechatResult.setErrorContext(errorContext1);
        final WechatUserInfoPO wechatUserInfoPO = new WechatUserInfoPO();
        wechatUserInfoPO.setAvatar("avatar");
        listWechatResult.setData(Arrays.asList(wechatUserInfoPO));
        final WechatUserInfoRequest var1 = new WechatUserInfoRequest();
        var1.setWechatUserIdList(Arrays.asList("value"));
//        when(mockWecatFacade.getUserInfoByUserIdList(var1)).thenReturn(listWechatResult);

        // Configure TradeDocumentIntegration.orderDataPushMatch(...).
        final OrderDataPushHitObject orderDataPushHitObject = new OrderDataPushHitObject();
        orderDataPushHitObject.setDesc("desc");
        orderDataPushHitObject.setCode("code");
        orderDataPushHitObject.setValue(0.0);
        orderDataPushHitObject.setReason("reason");
        final List<OrderDataPushHitObject> orderDataPushHitObjects = Arrays.asList(orderDataPushHitObject);
        final OrderTradeMatchDTO orderTradeMatchDTO = new OrderTradeMatchDTO();
        orderTradeMatchDTO.setStoreId("storeId");
        orderTradeMatchDTO.setOrderPattern(0);
        orderTradeMatchDTO.setPerformanceModes("performanceModes");
        orderTradeMatchDTO.setOrderAmount(new BigDecimal("0.00"));
        orderTradeMatchDTO.setGoodsCategoryId1("categoryId1");
        orderTradeMatchDTO.setGoodsCategoryId2("categoryId2");
        orderTradeMatchDTO.setGoodsCategoryId3("categoryId3");
        orderTradeMatchDTO.setPaymentCode("paymentCode");
        orderTradeMatchDTO.setBranchCode("branch");
        orderTradeMatchDTO.setZoneCode("zoneCode");
        orderTradeMatchDTO.setAreaCode("areaCode");
        orderTradeMatchDTO.setTonWeight(new BigDecimal("0.00"));
        orderTradeMatchDTO.setIsAgricMcnUser(0);
        orderTradeMatchDTO.setUserTag("userTag");
        orderTradeMatchDTO.setChannel("channel");
        orderTradeMatchDTO.setGoodsId(0L);
        orderTradeMatchDTO.setProductId(0L);
//        when(mockTradeDocumentIntegration.orderDataPushMatch(orderTradeMatchDTO)).thenReturn(orderDataPushHitObjects);

        // Configure BmsOrgFeignIntegration.listBranchesByHrOrgCodesV2(...).
        final OrganizationBaseVO organizationBaseVO = new OrganizationBaseVO();
        organizationBaseVO.setBatchNo("batchNo");
        organizationBaseVO.setOrgId(0);
        organizationBaseVO.setHrOrgId(0);
        organizationBaseVO.setOrgCode("orgCode");
        organizationBaseVO.setOrgName("orgName");
        final List<OrganizationBaseVO> organizationBaseVOS = Arrays.asList(organizationBaseVO);
//        when(mockBmsOrgFeignIntegration.listBranchesByHrOrgCodesV2("areaCode")).thenReturn(organizationBaseVOS);

//        when(mockOrderProductMapper.countPayOrderNum("orderSn", Arrays.asList("value"),
//                Arrays.asList("value"))).thenReturn(0);
//
        // Configure IBzOrderProductCombinationService.getOne(...).
        final BzOrderProductCombinationPO bzOrderProductCombinationPO = BzOrderProductCombinationPO.builder()
                .orderSn("orderSn")
                .mainProductId(0L)
                .build();
//        when(mockOrderProductCombinationService.getOne(any(LambdaQueryWrapper.class)))
//                .thenReturn(bzOrderProductCombinationPO);

        // Configure DbcServiceFeign.managerCommissionByCommodities(...).
        final com.cfpamf.common.ms.result.ErrorContext errorContext2 = new com.cfpamf.common.ms.result.ErrorContext();
        errorContext2.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext2.setThirdPartyError("thirdPartyError");
        final ManagerCommissionItemVO managerCommissionItemVO = new ManagerCommissionItemVO();
        managerCommissionItemVO.setOrderId("orderId");
        managerCommissionItemVO.setCommodityCode("commodityCode");
        managerCommissionItemVO.setManagerCommission(new BigDecimal("0.00"));
        final Result<List<ManagerCommissionItemVO>> listResult = new Result<>(false, errorContext2,
                Arrays.asList(managerCommissionItemVO));
//        when(mockDbcServiceFeign.managerCommissionByCommodities("orderSn", 5, "storeId")).thenReturn(listResult);

        // Configure DbcServiceFeign.freightCostByCommodities(...).
        final com.cfpamf.common.ms.result.ErrorContext errorContext3 = new com.cfpamf.common.ms.result.ErrorContext();
        errorContext3.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext3.setThirdPartyError("thirdPartyError");
        final FreightCostItemVO freightCostItemVO = new FreightCostItemVO();
        freightCostItemVO.setOrderId("orderId");
        freightCostItemVO.setCommodityCode("commodityCode");
        freightCostItemVO.setFreightCost(new BigDecimal("0.00"));
        final Result<List<FreightCostItemVO>> listResult1 = new Result<>(false, errorContext3,
                Arrays.asList(freightCostItemVO));
//        when(mockDbcServiceFeign.freightCostByCommodities("orderSn", 5, "storeId")).thenReturn(listResult1);
//
//        when(mockDbcServiceFeign.getListByOrderId("orderSn")).thenReturn(Result.ok(Collections.emptyList()));
//        when(mockOrderProductMapper.airEnergySalesCount("branch", "areaCode")).thenReturn(0);

        // Configure ICommonMqEventService.saveEvent(...).
        final GoodNewsMessage body = new GoodNewsMessage();
        body.setReportType("reportType");
        body.setBranch("branch");
        body.setMarketingCode("2023MALL_PAY_ORDER");
        body.setReportLevel(0);
        body.setPushParam("pushParam");
        body.setStatisticalObject("belongerEmployeeNo");
        body.setStatisticalTime("orderSn");
        body.setParamAddStatisticalCount(false);
        body.setMessageId("messageId");
        body.setText("text");
//        when(mockCommonMqEventService.saveEvent(body, "agric.performance.goodNews.exchange")).thenReturn(0L);

        // Run the test
//        orderDataPushServiceImplUnderTest.paySuccessDataPush("orderSn");

        // Verify the results
//        verify(mockRabbitMQUtils).sendByEventId(0L);
    }

    @Test
    public void testGetText() {
        // Setup
        final OrderArgicMessageDTO param = new OrderArgicMessageDTO();
        param.setOrderSn("orderSn");
        param.setStoreId(0L);
        param.setOrderAmount(new BigDecimal("0.00"));
        param.setGoodsAmount(new BigDecimal("0.00"));
        param.setOrderCommission(new BigDecimal("0.00"));
        param.setOrderTotalCommission(new BigDecimal("0.00"));
        param.setPushTime("pushTime");
        param.setBranchName("branchName");
        param.setAreaName("areaName");
        param.setManagerName("belongerName");
        param.setCustomerName("customerName");
        param.setGoodsCategoryNum(0);
        param.setGoodsCategory1("goodsCategory1");
        param.setGoodsCategory2("goodsCategory2");
        param.setGoodsCategory3("goodsCategory3");
        param.setProductIdList(Arrays.asList(0L));
        param.setWeight(0);
        param.setUnitNum(0);
        param.setUnitName("套");
        param.setTon(new BigDecimal("0.00"));
        param.setBranchTotalNum(0);
        param.setAreaTotalNum(0);
        param.setProductCount(0);
        param.setOrderNum(0);
        param.setImageUrl("managerAvatar");
        param.setSkuMaterialName("skuMaterialName");

        // Run the test
//        final String result = orderDataPushServiceImplUnderTest.getText("businessType", param);

        // Verify the results
//        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testReplacePlaceholders() {
        // Setup
        final Map<String, Object> replacements = new HashMap<>();

        // Run the test
//        final String result = OrderDataPushServiceImpl.replacePlaceholders("template", replacements);

        // Verify the results
        assertThat("template").isEqualTo("template");
    }
}
