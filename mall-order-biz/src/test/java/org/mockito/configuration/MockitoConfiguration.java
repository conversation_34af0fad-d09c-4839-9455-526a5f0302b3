package org.mockito.configuration;

import com.alibaba.fastjson.util.ParameterizedTypeImpl;
import com.github.jsonzou.jmockdata.JMockData;
import com.github.jsonzou.jmockdata.TypeReference;
import org.mockito.stubbing.Answer;

import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> 2020/3/10 14:54
 */
public class MockitoConfiguration extends DefaultMockitoConfiguration {

    @Override
    public Answer<Object> getDefaultAnswer() {
        return invocation -> {

            try {
                Object mock1 = invocation.getMock();
                ParameterizedType type = null;

                Method method = invocation.getMethod();
                Class<?> returnType = method.getReturnType();
                if (returnType != void.class && returnType != Void.class) {

                    Type genericReturnType = method.getGenericReturnType();

                    Map<String, Type> val = new HashMap<>(1);
                    val.put("val", genericReturnType);
                    Object mock = null;
                    if (Objects.equals(returnType.toString(), genericReturnType.toString())) {
                        mock = JMockData.mock(returnType);
                    } else {
                        String s = genericReturnType.toString();
                        if (s.contains("<T>")) {
                            if (genericReturnType instanceof ParameterizedType) {
                                ParameterizedType tmp = (ParameterizedType) genericReturnType;
                                genericReturnType = new ParameterizedTypeImpl(
                                        type.getActualTypeArguments(), tmp.getOwnerType(), tmp.getRawType());
                                val.put("val", genericReturnType);
                            }

                        } else if ("T".equals(s)) {
                            val.put("val", type.getActualTypeArguments()[0]);
                        }
                        mock = JMockData.mock(new TypeReference<Object>() {
                            @Override
                            public Type getType() {
                                return val.get("val");
                            }

                        });
                    }
                    return mock;
                }
            } catch (Exception e) {

                return null;
            }
            return null;
        };
    }

}
