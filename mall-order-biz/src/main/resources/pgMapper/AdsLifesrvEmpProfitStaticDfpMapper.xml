<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.pgMapper.AdsLifesrvEmpProfitStaticDfpMapper">

    <select id="getBasicMetrics" resultType="com.cfpamf.ms.mallorder.po.pgrpt.AdsLifesrvEmpProfitSaticDfpPO">
        <if test="request.interval == 'year'">
            select
                bch_code ,
                bch_name ,
                emp_id ,
                emp_name ,
                sy_all_revenue_amt ,
                sy_all_base_commission_amt
            from wine.ads_lifesrv_emp_profit_static_dfp alepsd
            where (is_work = 1 or sy_all_revenue_amt > 0)
            <if test="request.orgCode != null and request.orgCode != ''">
                and (area_code = #{request.orgCode} or bch_code = #{request.orgCode} or district_code = #{request.orgCode})
            </if>
            <if test="request.empName != null and request.empName != ''">
                and (emp_name like concat('%',#{request.empName},'%')
                    or emp_id like concat('%',#{request.empName},'%'))
            </if>
            <if test="request.empCodeList != null and request.empCodeList.size() != 0">
                and emp_id in
                <foreach collection="request.empCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.orderSql == null or request.orderSql == ''">
                order by sy_all_base_commission_amt desc,bch_code asc
            </if>
            <if test="request.orderSql != null">
                order by ${request.orderSql},bch_code asc
            </if>
            <if test="request.pageIndex != null">
                limit #{request.pageSize} offset #{request.offset}
            </if>
        </if>
        <if test="request.interval == 'month'">
            select
                bch_code ,
                bch_name ,
                emp_id ,
                emp_name ,
                sm_all_revenue_amt ,
                sm_all_base_commission_amt
            from wine.ads_lifesrv_emp_profit_static_dfp alepsd
            where (is_work = 1 or sm_all_revenue_amt > 0)
            <if test="request.orgCode != null and request.orgCode != ''">
                and (area_code = #{request.orgCode} or bch_code = #{request.orgCode} or district_code = #{request.orgCode})
            </if>
            <if test="request.empName != null and request.empName != ''">
                and (emp_name like concat('%',#{request.empName},'%')
                    or emp_id like concat('%',#{request.empName},'%'))
            </if>
            <if test="request.empCodeList != null and request.empCodeList.size() != 0">
                and emp_id in
                <foreach collection="request.empCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.orderSql == null or request.orderSql == ''">
                order by sm_all_base_commission_amt desc,bch_code asc
            </if>
            <if test="request.orderSql != null">
                order by ${request.orderSql},bch_code asc
            </if>
            <if test="request.pageIndex != null">
                limit #{request.pageSize} offset #{request.offset}
            </if>
        </if>
    </select>

    <select id="getSummary" resultType="com.cfpamf.ms.mallorder.vo.pgrpt.LifesrvEmpProfitSummaryVO">
        select
            count(1) as count,
            sum(sy_all_revenue_amt) as syAllRevenueAmt,
            sum(sm_all_revenue_amt) as smAllRevenueAmt,
            sum(sy_all_base_commission_amt) as syAllBaseCommissionAmt,
            sum(sm_all_base_commission_amt) as smAllBaseCommissionAmt
        from wine.ads_lifesrv_emp_profit_static_dfp alepsd
        where 1=1
        <if test="request.interval == 'year'">
            and (is_work = 1 or sy_all_revenue_amt > 0)
        </if>
        <if test="request.interval == 'month'">
            and (is_work = 1 or sm_all_revenue_amt > 0)
        </if>
        <if test="request.orgCode != null and request.orgCode != ''">
            and (area_code = #{request.orgCode} or bch_code = #{request.orgCode} or district_code = #{request.orgCode})
        </if>
        <if test="request.empName != null and request.empName != ''">
            and (emp_name like concat('%',#{request.empName},'%')
                or emp_id like concat('%',#{request.empName},'%'))
        </if>
        <if test="request.empCodeList != null and request.empCodeList.size() != 0">
            and emp_id in
            <foreach collection="request.empCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>
