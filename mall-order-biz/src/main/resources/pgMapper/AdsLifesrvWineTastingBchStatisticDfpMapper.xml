<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.pgMapper.AdsLifesrvWineTastingBchStatisticDfpMapper">

    <select id="getPageInfo" resultType="com.cfpamf.ms.mallorder.po.pgrpt.AdsLifesrvWineTastingBchStatisticDfpPO">
        select
            area_code,
            area_name,
            district_code,
            district_name,
            activity_bch_code,
            activity_bch_name,
            tasting_type,
            activity_name,
            <![CDATA[
                COALESCE(
                    case when activity_time_start < '2025-05-01 00:00:00'
                    then
                        case when tasting_scheme='A'
                        then fd3_wine_tasting_revenue_amt
                        else fd7_wine_activity_revenue_amt end
                    else fd4_wine_tasting_revenue_amt end
                ,0) cdWineActivityRevenueAmt,
                COALESCE(
                    case when activity_time_start < '2025-05-01 00:00:00'
                    then
                        case when tasting_scheme='A' then fd3_wine_tasting_trade_user_cnt
                        else fd7_wine_activity_trade_user_cnt end
                else fd4_wine_tasting_trade_user_cnt end
            ,0) cdWineActivityTradeUserCnt,
            COALESCE(
                case when activity_time_start < '2025-05-01 00:00:00'
                then
                    case when tasting_scheme='A' then fd3_wine_tasting_valid_trade_box_num
                    else fd7_wine_activity_effective_trade_box_num end
                else fd4_wine_tasting_valid_trade_box_num end
            ,0) cdWineActivityEffectiveTradeBoxNum,
            ]]>
            activity_time_start,
            COALESCE(acm_wine_tasting_signed_user_cnt,0) acmWineTastingSignedUserCnt,
            COALESCE(invitee_customer_cnt,0) inviteeCustomerCnt
        from
        wine.ads_lifesrv_wine_tasting_bch_statistic_dfp
        where pt = #{request.pt} and activity_time_start >= '2025-01-01 00:00:00'
        <if test="request.orgCode != null and request.orgCode != '' and (request.bchCode == null or request.bchCode == '')">
            and activity_bch_code = #{request.orgCode}
        </if>
        <if test="request.bchCode != null and request.bchCode != ''">
            and (activity_bch_code = #{request.bchCode} or area_code = #{request.bchCode} or district_code = #{request.bchCode})
        </if>
        <if test="request.areaCode != null and request.areaCode != ''">
            and area_code = #{request.areaCode}
        </if>
        <if test="request.districtCode != null and request.districtCode != ''">
            and district_code = #{request.districtCode}
        </if>
        <if test="request.startDate != null and request.endDate != ''">
            and activity_time_start >= CAST(#{request.startDate} AS timestamp)
        </if>
        <if test="request.endDate != null and request.endDate != ''">
            <![CDATA[ and activity_time_start <= CAST(#{request.endDate} AS timestamp) ]]>
        </if>
        <if test="request.orderSql == null or request.orderSql == ''">
            order by activity_time_start desc,activity_bch_code asc
        </if>
        <if test="request.orderSql != null">
            order by ${request.orderSql},activity_bch_code asc
        </if>
        <if test="request.pageIndex != null">
            limit #{request.pageSize} offset #{request.offset}
        </if>
    </select>

    <select id="getCount" resultType="java.lang.Integer">
        select
            count(*)
        from
        wine.ads_lifesrv_wine_tasting_bch_statistic_dfp
        where pt = #{request.pt} and activity_time_start >= '2025-01-01 00:00:00'
        <if test="request.orgCode != null and request.orgCode != '' and (request.bchCode == null or request.bchCode == '')">
            and activity_bch_code = #{request.orgCode}
        </if>
        <if test="request.bchCode != null and request.bchCode != ''">
            and (activity_bch_code = #{request.bchCode} or area_code = #{request.bchCode} or district_code = #{request.bchCode})
        </if>
        <if test="request.areaCode != null and request.areaCode != ''">
            and area_code = #{request.areaCode}
        </if>
        <if test="request.districtCode != null and request.districtCode != ''">
            and district_code = #{request.districtCode}
        </if>
        <if test="request.startDate != null and request.endDate != ''">
            and activity_time_start >= CAST(#{request.startDate} AS timestamp)
        </if>
        <if test="request.endDate != null and request.endDate != ''">
            <![CDATA[ and activity_time_start <= CAST(#{request.endDate} AS timestamp) ]]>
        </if>
    </select>

</mapper>
