<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.pgMapper.JsPkDynamicPerformanceMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.mallorder.po.JsPkDynamicPerformancePO">
        <result column="rpt_date" property="rptDate" />
        <result column="mall_order_no" property="mallOrderNo" />
        <result column="pay_time" property="payTime" />
        <result column="product_num" property="productNum" />
        <result column="refund_num" property="refundNum" />
        <result column="unit_price" property="unitPrice" />
        <result column="to_c_unit_price" property="toCUnitPrice" />
        <result column="bch_code" property="bchCode" />
        <result column="recommend_user_id" property="recommendUserId" />
        <result column="recommend_user_name" property="recommendUserName" />
        <result column="bch_name" property="bchName" />
        <result column="wine_valid_sale_amt" property="wineValidSaleAmt" />
        <result column="wine_revenue_amt" property="wineRevenueAmt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        rpt_date,
        mall_order_no,
        pay_time,
        product_num,
        refund_num,
        unit_price,
        to_c_unit_price,
        recommend_user_id,
        recommend_user_name,
        bch_name,
        bch_code,
        wine_valid_sale_amt,
        wine_revenue_amt
    </sql>

    <select id="listByQuery" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        loan.ads_market_wine_scrm_order_goods_detail_mt_hfp
        WHERE rpt_date between #{rptDateStart} and #{rptDateEnd}
    </select>


</mapper>
