<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.pgMapper.AdsLifesrvWineTastingAreaStatisticDfpMapper">

    <sql id="year_base_column">
        ,COALESCE(sy_activity_emp_unstart_num,0) sy_activity_emp_unstart_num--当年个人专场已申请未举办场次数
        ,COALESCE(sy_activity_bch_unstart_num,0) sy_activity_bch_unstart_num --当年分支标准场已申请未举办场次数
        ,COALESCE(sy_activity_bch_num,0) sy_activity_bch_num --当年分支标准场已举办场次数
        ,COALESCE(sy_activity_emp_num,0) sy_activity_emp_num --当年人专场已举办场次数
        ,COALESCE((sy_activity_bch_num+sy_activity_emp_num),0) syActivityCnt --当年已举办场次
        ,COALESCE((sy_activity_bch_unstart_num+sy_activity_emp_unstart_num),0) syUnstartActivityCnt --当年已提交未举办场次
        ,COALESCE(sy_activity_unsigned_num,0) sy_activity_unsigned_num--当年未签到场次数
        ,COALESCE(sy_signed_user_cnt,0) sy_signed_user_cnt--当年签到人数
        ,COALESCE(sy_activity_bch_cnt,0) sy_activity_bch_cnt--当年已举办机构数
        ,COALESCE(sy_noactivity_bch_cnt,0) sy_noactivity_bch_cnt--当年未举办机构数
        ,case
        when sy_activity_bch_cnt+sy_noactivity_bch_cnt = 0 then 0
        else
            round(cast(sy_activity_bch_cnt as numeric(24, 4))/cast((sy_activity_bch_cnt+sy_noactivity_bch_cnt) as numeric(24, 4)),4)*100
        end syActivityBchRatio
        ,COALESCE(sy_wine_tasting_trade_user_cnt,0) sy_wine_tasting_trade_user_cnt --当年活动有效交易客户数
        ,COALESCE(sy_wine_tasting_effective_trade_box_num,0) sy_wine_tasting_effective_trade_box_num--当年活动有效交易箱数
        ,COALESCE(sy_wine_tasting_revenue_amt,0) sy_wine_tasting_revenue_amt--当年活动营收
        ,COALESCE(sy_wine_tasting_avg_revenue_amt,0) sy_wine_tasting_avg_revenue_amt--当年场均营收
    </sql>

    <sql id="month_base_column">
        ,COALESCE(sm_activity_emp_unstart_num,0) sm_activity_emp_unstart_num--当月个人专场已申请未举办场次数
        ,COALESCE(sm_activity_bch_unstart_num,0) sm_activity_bch_unstart_num --当月分支标准场已申请未举办场次数
        ,COALESCE(sm_activity_bch_num,0) sm_activity_bch_num --当月分支标准场已举办场次数
        ,COALESCE(sm_activity_emp_num,0) sm_activity_emp_num --当月人专场已举办场次数
        ,COALESCE((sm_activity_bch_num+sm_activity_emp_num),0) smActivityCnt --当月已举办场次
        ,COALESCE(sm_activity_bch_unstart_num+sm_activity_emp_unstart_num,0) smUnstartActivityCnt --当月已提交未举办场次
        ,COALESCE(sm_activity_unsigned_num,0) sm_activity_unsigned_num--当月未签到场次数
        ,COALESCE(sm_signed_user_cnt,0) sm_signed_user_cnt--当月签到人数
        ,COALESCE(sm_activity_bch_cnt,0) sm_activity_bch_cnt--当月已举办机构数
        ,COALESCE(sm_noactivity_bch_cnt,0) sm_noactivity_bch_cnt--当月未举办机构数
        ,case
        when sm_activity_bch_cnt+sm_noactivity_bch_cnt = 0 then 0
        else
        round(cast(sm_activity_bch_cnt as numeric(24, 4))/cast((sm_activity_bch_cnt+sm_noactivity_bch_cnt) as numeric(24, 4)),4)*100
        end smActivityBchRatio
        ,COALESCE(sm_wine_tasting_trade_user_cnt,0) sm_wine_tasting_trade_user_cnt --当月活动有效交易客户数
        ,COALESCE(sm_wine_tasting_effective_trade_box_num,0) sm_wine_tasting_effective_trade_box_num--当月活动有效交易箱数
        ,COALESCE(sm_wine_tasting_revenue_amt,0) sm_wine_tasting_revenue_amt--当月活动营收
        ,COALESCE(sm_wine_tasting_avg_revenue_amt,0) sm_wine_tasting_avg_revenue_amt--当月场均营收
    </sql>

    <select id="getBasicMetrics" resultType="com.cfpamf.ms.mallorder.po.pgrpt.AdsLifesrvWineTastingAreaStatisticDfpPO">
        select
            area_code,
            area_name,
            district_code,
            district_name,
            bch_code,
            bch_name,
            cur_bch_cnt,
            dim_type
            <if test="request.interval == 'year'">
                    <include refid="year_base_column"/>
            </if>
            <if test="request.interval == 'month'">
                    <include refid="month_base_column"/>
            </if>
        from
        wine.ads_lifesrv_wine_tasting_area_statistic_dfp
        where pt = #{request.pt}
        <if test="request.authType != null and request.authType == 'nation'">
            and dim_type = 'all'
        </if>
        <if test="request.authType != null and request.authType != '' and request.authType != 'nation'">
            and dim_type = #{request.authType}
        </if>
        <if test="request.orgCode != null and request.orgCode != ''">
            and bch_code = #{request.orgCode}
        </if>
        <if test="request.areaCode != null and request.areaCode != ''">
            and area_code = #{request.areaCode}
        </if>
        <if test="request.districtCode != null and request.districtCode != ''">
            and district_code = #{request.districtCode}
        </if>
        limit 1
    </select>

    <select id="getTOP10RankList" resultType="com.cfpamf.ms.mallorder.po.pgrpt.AdsLifesrvWineTastingAreaStatisticDfpPO">
        select
        area_code,
        area_name,
        district_code,
        district_name,
        bch_code,
        bch_name,
        cur_bch_cnt,
        dim_type
        <if test="request.interval == 'year'">
            <include refid="year_base_column"/>
            ,row_number()  over (order by COALESCE(sy_wine_tasting_revenue_amt,0) desc)
            rank_num
        </if>
        <if test="request.interval == 'month'">
            <include refid="month_base_column"/>
            ,row_number()  over (order by COALESCE(sm_wine_tasting_revenue_amt,0) desc)
            rank_num
        </if>
        from
        wine.ads_lifesrv_wine_tasting_area_statistic_dfp
        where pt = #{request.pt}
        and dim_type = 'bch'
        <if test="type != null and type == 'area'">
            and area_code = #{request.areaCode}
        </if>
        <if test="request.interval == 'year'">
            order by COALESCE(sy_wine_tasting_revenue_amt,0) desc
        </if>
        <if test="request.interval == 'month'">
            order by COALESCE(sm_wine_tasting_revenue_amt,0) desc
        </if>
        limit 10
    </select>
</mapper>
