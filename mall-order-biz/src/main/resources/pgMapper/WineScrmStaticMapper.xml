<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.pgMapper.WineScrmStaticMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="resultMap" type="com.cfpamf.ms.mallorder.po.pgrpt.WineScrmStaticPO">
        <result column="rpt_date" property="rptDate" />
        <result column="emp_id" property="empId" />
        <result column="emp_name" property="empName" />
        <result column="area_code" property="areaCode" />
        <result column="area_name" property="areaName" />
        <result column="district_code" property="districtCode" />
        <result column="district_name" property="districtName" />
        <result column="bch_code" property="bchCode" />
        <result column="bch_name" property="bchName" />
        <result column="join_months" property="joinMonths" />
        <result column="job_code" property="jobCode" />
        <result column="job_name" property="jobName" />
        <result column="acm_work_emp_cnt" property="acmWorkEmpCnt" />
        <result column="sm_wine_revenue_amt" property="smWineRevenueAmt" />
        <result column="lm_wine_revenue_amt" property="lmWineRevenueAmt" />
        <result column="sm_wine_vaild_sale_amt" property="smWineValidSaleAmt" />
        <result column="lm_wine_vaild_sale_amt" property="lmWineValidSaleAmt" />
        <result column="sm_wine_avg_vaild_sale_amt" property="smWineAvgValidSaleAmt" />
        <result column="lm_wine_avg_vaild_sale_amt" property="lmWineAvgValidSaleAmt" />
        <result column="sm_sy_wine_valid_sales_amt_target_finish_radio" property="smSyWineValidSalesAmtTargetFinishRadio" />
        <result column="lm_sy_wine_valid_sales_amt_target_finish_radio" property="lmSyWineValidSalesAmtTargetFinishRadio" />
    </resultMap>

    <select id="pageList" resultMap="resultMap">
        SELECT dfp.* FROM loan.ads_market_wine_scrm_static_dfp dfp
        <include refid="wineWhereCondition"></include>
    </select>

    <select id="list" resultMap="resultMap">
        SELECT dfp.* FROM loan.ads_market_wine_scrm_static_dfp dfp
        <include refid="wineWhereCondition"></include>
    </select>

    <select id="selectByEmpId" resultMap="resultMap">
        SELECT dfp.*
        FROM loan.ads_market_wine_scrm_static_dfp dfp
        where dfp.emp_id = #{empId} and dfp.job_code = #{jobCode}
        order by rpt_date desc limit 1
    </select>

    <select id="getLatestStatisticsTime" resultType="java.lang.String">
        SELECT dfp.rpt_date
        FROM loan.ads_market_wine_scrm_static_dfp dfp
        order by rpt_date desc
        limit 1
    </select>

    <sql id="wineWhereCondition">
        <where>
            <if test="query.rptDate != null and query.rptDate != '' ">
                dfp.rpt_date = #{query.rptDate}
            </if>
            <if test="query.empId != null and query.empId != '' ">
                and dfp.emp_id = #{query.empId}
            </if>
            <if test="query.empIdIn != null">
                and dfp.emp_id in
                <foreach collection="query.empIdIn" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.empName != null and query.empName != '' ">
                and dfp.emp_name like CONCAT('%',#{query.empName},'%')
            </if>
            <if test="query.areaCode != null and query.areaCode != '' ">
                and dfp.area_code = #{query.areaCode}
            </if>
            <if test="query.areaCode != null">
                and dfp.area_code in
                <foreach collection="query.areaCode" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.areaName != null and query.areaName != '' ">
                and dfp.area_name like CONCAT('%',#{query.areaName},'%')
            </if>
            <if test="query.bchCode != null and query.bchCode != '' ">
                and dfp.bch_code = #{query.bchCode}
            </if>
            <if test="query.bchCodeIn != null">
                and dfp.bch_code in
                <foreach collection="query.bchCodeIn" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.bchName != null and query.bchName != '' ">
                and dfp.bch_name like CONCAT('%',#{query.bchName},'%')
            </if>
            <if test="query.jobCode != null and query.jobCode != '' ">
                and dfp.job_code = #{query.jobCode}
            </if>
            <if test="query.lmWineRevenueAmtMax != null ">
                and dfp.lm_wine_revenue_amt &lt;= #{query.lmWineRevenueAmtMax}
            </if>
            <if test="query.lmWineRevenueAmtMin != null ">
                and dfp.lm_wine_revenue_amt &gt;= #{query.lmWineRevenueAmtMin}
            </if>
            <if test="query.lmWineValidSaleAmtMax != null ">
                and dfp.lm_wine_vaild_sale_amt &lt;= #{query.lmWineValidSaleAmtMax}
            </if>
            <if test="query.lmWineValidSaleAmtMin != null ">
                and dfp.lm_wine_vaild_sale_amt &gt;= #{query.lmWineValidSaleAmtMin}
            </if>
            <if test="query.lmWineAvgValidSaleAmtMax != null ">
                and dfp.lm_wine_avg_vaild_sale_amt &lt;= #{query.lmWineAvgValidSaleAmtMax}
            </if>
            <if test="query.lmWineAvgValidSaleAmtMin != null ">
                and dfp.lm_wine_avg_vaild_sale_amt &gt;= #{query.lmWineAvgValidSaleAmtMin}
            </if>
            <if test="query.lmSyWineValidSalesAmtTargetFinishRadioMax != null ">
                and dfp.lm_sy_wine_valid_sales_amt_target_finish_radio &lt;= #{query.lmSyWineValidSalesAmtTargetFinishRadioMax}
            </if>
            <if test="query.lmSyWineValidSalesAmtTargetFinishRadioMin != null ">
                and dfp.lm_sy_wine_valid_sales_amt_target_finish_radio &gt;= #{query.lmSyWineValidSalesAmtTargetFinishRadioMin}
            </if>
        </where>
    </sql>
</mapper>
