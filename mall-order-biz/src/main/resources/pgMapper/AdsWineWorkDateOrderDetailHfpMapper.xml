<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.pgMapper.AdsWineWorkDateOrderDetailHfpMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.mallorder.po.pgrpt.AdsWineWorkDateOrderDetailHfpPO">
        <result column="id" property="id" />
        <result column="update_time" property="updateTime" />
        <result column="update_time" property="updateTime" />
        <result column="update_by" property="createBy" />
        <result column="create_by" property="updateBy" />
        <result column="enabled_flag" property="enabledFlag" />

        <result column="mall_order_no" property="mallOrderNo" />
        <result column="pay_time" property="payTime" />
        <result column="revenue_amt" property="revenueAmt" />
        <result column="mall_order_status_code" property="mallOrderStatusCode" />
        <result column="hr_org_id" property="hrOrgId" />
        <result column="bch_code" property="bchCode" />
        <result column="bch_name" property="bchName" />
        <result column="area_code" property="areaCode" />
        <result column="area_name" property="areaName" />
        <result column="achievement_emp_id" property="achievementEmpId" />
        <result column="achievement_emp_name" property="achievementEmpName" />
        <result column="mall_goods_sku_id" property="mallGoodsSkuId" />
        <result column="valid_trade_goods_num" property="validTradeGoodsNum" />
        <result column="valid_trade_box_num" property="validTradeBoxNum" />
        <result column="mall_store_id" property="mallStoreId" />
        <result column="erp_material_lvl1_category_id" property="erpMaterialLvl1CategoryId" />
        <result column="erp_material_lvl1_category_name" property="erpMaterialLvl1CategoryName" />
        <result column="is_normal_bch" property="isNormalBch" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,mall_order_no, pay_time, revenue_amt, mall_order_status_code, hr_org_id, bch_code, bch_name, area_code, area_name, achievement_emp_id, achievement_emp_name, mall_goods_sku_id, valid_trade_goods_num, valid_trade_box_num, mall_store_id, erp_material_lvl1_category_id, erp_material_lvl1_category_name,
        is_normal_bch,
        update_time,
        create_time,
        enabled_flag,
        create_by,
        update_by
    </sql>
    <select id="getList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include>
        from wine.ads_wine_work_date_order_detail_hfp t
        where t.mall_order_status_code in ('20','30','40')
        <if test="request != null">
            <if test="request.finalSearchBranchList != null and request.finalSearchBranchList.size > 0">
                and t.bch_code  in (
                    <foreach collection="request.finalSearchBranchList" item="branch" separator=",">
                        #{branch}
                    </foreach>
                )
            </if>
            <if test="request.managerList != null and request.managerList.size > 0">
                and t.achievement_emp_id  in (
                    <foreach collection="request.managerList" item="manager" separator=",">
                        #{manager}
                    </foreach>
                )
            </if>

            <if test="request.storeIdList != null and request.storeIdList.size > 0">
                and t.mall_store_id  in (
                <foreach collection="request.storeIdList" item="storeId" separator=",">
                    #{storeId}
                </foreach>
                )
            </if>

            <if test="request.beginTime != null">
                and t.pay_time  > #{request.beginTime}::timestamp
            </if>
            <if test="request.endTime != null">
                and t.pay_time  <![CDATA[  <   ]]>   #{request.endTime}::timestamp
            </if>
        </if>

    </select>

</mapper>
