<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.pgMapper.AdsLifesrvDepartmentProfitStaticDfpMapper">

    <sql id="year_base_column">
        ,sy_all_profit_amt
        ,sy_lifesrv_profit_target
        ,round(sy_lifesrv_finish_profit_target_rate,4)*100 sy_lifesrv_finish_profit_target_rate
        ,sy_all_revenue_amt
        ,sy_all_base_commission_amt
    </sql>

    <sql id="month_base_column">
        ,sm_all_profit_amt
        ,sm_lifesrv_profit_target
        ,round(sm_lifesrv_finish_profit_target_rate,4)*100 sm_lifesrv_finish_profit_target_rate
        ,sm_all_revenue_amt
        ,sm_all_base_commission_amt
    </sql>

    <select id="getBasicMetrics" resultType="com.cfpamf.ms.mallorder.po.pgrpt.AdsLifesrvDepartmentProfitStaticDfpPO">
        select
            department_code,
            department_name,
            department_type
            <if test="request.interval == 'year'">
                    <include refid="year_base_column"/>
            </if>
            <if test="request.interval == 'month'">
                    <include refid="month_base_column"/>
            </if>
        from
        wine.ads_lifesrv_department_profit_static_dfp
        where 1=1
        <if test="request.orgCode != null and request.orgCode != ''">
            and department_code = #{request.orgCode}
        </if>
        <if test="request.orgCode == null or request.orgCode == ''">
            and department_type = '0'
        </if>
        limit 1;
    </select>
</mapper>
