<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.pgMapper.AdsJiaqingWorkDateOrderDetailHfpMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.mallorder.po.pgrpt.AdsJiaqingWorkDateOrderDetailHfpPO">
        <result column="id" property="id" />
        <result column="update_time" property="updateTime" />
        <result column="create_time" property="createTime" />
        <result column="enabled_flag" property="enabledFlag" />
        <result column="mall_order_no" property="mallOrderNo" />
        <result column="pay_time" property="payTime" />
        <result column="revenue_amt" property="revenueAmt" />
        <result column="mall_order_status_code" property="mallOrderStatusCode" />
        <result column="hr_org_id" property="hrOrgId" />
        <result column="area_code" property="areaCode" />
        <result column="area_name" property="areaName" />
        <result column="bch_name" property="bchName" />
        <result column="bch_code" property="bchCode" />
        <result column="achievement_emp_id" property="achievementEmpId" />
        <result column="achievement_emp_name" property="achievementEmpName" />
        <result column="mall_goods_sku_id" property="mallGoodsSkuId" />
        <result column="valid_trade_goods_num" property="validTradeGoodsNum" />
        <result column="mall_store_id" property="mallStoreId" />
        <result column="erp_material_lvl1_category_id" property="erpMaterialLvl1CategoryId" />
        <result column="erp_material_lvl1_category_name" property="erpMaterialLvl1CategoryName" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="is_normal_bch" property="isNormalBch" />
        <result column="pt" property="pt" />
        <result column="mall_sub_order_no" property="mallSubOrderNo" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        update_time,
        create_time,
        enabled_flag,
        mall_order_no, pay_time, revenue_amt, mall_order_status_code, hr_org_id, area_code, area_name, bch_name, bch_code, achievement_emp_id, achievement_emp_name, mall_goods_sku_id, valid_trade_goods_num, mall_store_id, erp_material_lvl1_category_id, erp_material_lvl1_category_name, create_by, update_by, is_normal_bch, pt, mall_sub_order_no
    </sql>

</mapper>
