#nacos配置中心
spring:
  application:
    name: mall-order
  cloud:
    nacos:
      config:
        enabled: @nacos.config.enable@
        file-extension: yml
        extension-configs:
          #- data-id: ${spring.application.name}-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension} #nacos data-id mall-order-test.yml
          - data-id: mall-order.yml #配置id，与配置中心的id对应，如果无此配置则使用 application.yml 中的配置
            group: MALL_GROUP #分组名称
            refresh: true #是否自动刷新配置
          - data-id: common.yml #配置id，与配置中心的id对应，如果无此配置则使用 application.yml 中的配置
            group: MALL_GROUP #分组名称
            refresh: true #是否自动刷新配置
        namespace: @config-namespace@  #配置中心命名空间
        server-addr: @config-server@ #配置中心地址
        password: @password@
        username: @username@
#      discovery:
#        namespace: @config-namespace@ #注册中心命名空间
#        server-addr: @config-server@ #注册中心地址
#        group: MALL_GROUP
#        password: @password@
#        username: @username@  