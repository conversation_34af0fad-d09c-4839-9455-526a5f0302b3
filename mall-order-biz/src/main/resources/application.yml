server:
  port: 12002
spring:
  profiles:
    active: @package.environment@
  main:
    allow-bean-definition-overriding: true
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
mybatis-plus:
  # 支持统配符 * 或者 ; 分割
  typeEnumsPackage: com.cfpamf.ms.mallorder.common.enums
#  autoconfigure:
#    exclude: com.alibaba.cloud.sentinel.SentinelWebAutoConfiguration #排除sentinel web自动配置，使用自定义配置
management: # spring-boot-admin监控管理配置
  endpoints:
    web:
      exposure:
        include: '*'

  endpoint:
    health:
      show-details: always
  health:
    elasticsearch:
      enabled: false
#    rabbit:
#      enabled: false

#seata:
#  application-id: ${spring.application.name}
#  tx-service-group: seata_newmall_tx_group
#  enableAutoDataSourceProxy: true
#  config: #从配置中心获取seata service的配置
#    type: nacos #type 默认为file
#    nacos:
#      namespace: #配置中心命名空间
#      serverAddr: @config-server@ #注册中心地址
#      group: SEATA_GROUP
#      password: @password@
#      username: @username@
#  registry: # 从注册中心获取seata-server服务端
#    type: nacos #type 默认为file
#    nacos:
#      application: seata-server
#      namespace:  #注册中心命名空间
#      server-addr: @config-server@ #注册中心地址
#      group: SEATA_GROUP
#      password: @password@
#      username: @username@

#ahas限流熔断配置项
#建议配置,区分不同的环境 其他环境可以不加 默认default
ahas.namespace: @package.environment@

feign:
  client:
    config:
      default:
        # FeignClientConfiguration
        connectTimeout: 10000 # Feign的连接建立超时时间
        readTimeout: 10000 # Feign的请求处理超时时间
        loggerLevel: full #

  httpclient:
    enabled: true
    connection-timeout: 10000
  sentinel:
    enabled: true

logging:
  level:
    com.cfpamf.ms.mallorder.mapper: info
    com.cfpamf.ms.mallorder.pgMapper: info
    io.seata: error
    com.alibaba.nacos: error
    com.slodon.bbc: info
    ROOT: INFO
    org.springframework: INFO
    com.cfpamf.ms.mallorder.feign: INFO
    jdbc:
      sqltiming: WARN #包含 SQL 语句实际的执行时间
      audit: 'OFF' # 	除了 ResultSet 之外的所有JDBC调用信息，篇幅较长
      resultset: 'OFF' #包含 ResultSet 的信息，输出篇幅较长
      connection: 'OFF' #连接信息
      sqlonly: WARN #仅仅记录 SQL 语句，会将占位符替换为实际的参数
      resultsettable: 'OFF'

knife4j:
  production: false

