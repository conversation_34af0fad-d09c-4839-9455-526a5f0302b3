<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.BzOrderProductInstallMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.mallorder.po.BzOrderProductInstallPO">
        <result column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="enabled_flag" property="enabledFlag" />
        <result column="order_product_id" property="orderProductId" />
        <result column="order_sn" property="orderSn" />
        <result column="store_id" property="storeId" />
        <result column="product_id" property="productId" />
        <result column="supplier_name" property="supplierName" />
        <result column="supplier_mobile" property="supplierMobile" />
        <result column="install_images_url" property="installImagesUrl" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_time,
        update_time,
        enabled_flag,
        order_product_id, order_sn, store_id, product_id, supplier_name, supplier_mobile, install_images_url, create_by, update_by
    </sql>

</mapper>
