<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.OrderGroupBuyingBindMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.mallorder.po.OrderGroupBuyingBindPO">
        <result column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="enabled_flag" property="enabledFlag" />
        <result column="group_buying_code" property="groupBuyingCode" />
        <result column="gift_order_sn" property="giftOrderSn" />
        <result column="group_order_sn" property="groupOrderSn" />
        <result column="order_product_id" property="orderProductId" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        create_time,
        update_time,
        enabled_flag,
        group_buying_code, gift_order_sn, group_order_sn, order_product_id, create_by, update_by
    </sql>

    <sql id="whereCondition">
        <if test="query != null">
            <if test="query.groupBuyingCode != null and query.groupBuyingCode != ''">
                AND gbb.`group_buying_code` = #{query.groupBuyingCode}
            </if>
            <if test="query.giftOrderSn != null and query.giftOrderSn != ''">
                AND gbb.`gift_order_sn` = #{query.giftOrderSn}
            </if>
            <if test="query.groupOrderSn != null and query.groupOrderSn != ''">
                AND gbb.`group_order_sn` = #{query.groupOrderSn}
            </if>
            <if test="query.orderProductId != null and query.orderProductId != ''">
                AND gbb.`order_product_id` = #{query.orderProductId}
            </if>
        </if>
    </sql>

    <select id="listGroupBuyingBind" resultType="com.cfpamf.ms.mallorder.po.OrderGroupBuyingBindPO">
        select *
        from bz_order_group_buying_bind gbb
        where gbb.enabled_flag = 1
        <include refid="whereCondition"/>
    </select>

    <select id="listBindOrderProduct" resultType="com.cfpamf.ms.mallorder.vo.GroupOrderProductVO">
        select gbb.id,
        gbb.group_buying_code,
        gbb.gift_order_sn,
        gbb.group_order_sn,
        gbb.order_product_id,
        bop.goods_id,
        bop.goods_name,
        bop.product_id,
        bop.channel_sku_unit,
        bop.channel_sku_id
        from bz_order_product bop,
        bz_order_group_buying_bind gbb
        where bop.order_product_id = gbb.order_product_id and gbb.enabled_flag = 1
        <include refid="whereCondition"/>
    </select>

    <resultMap id="groupOrderMap" type="com.cfpamf.ms.mallorder.vo.GroupOrderVO">
        <id column="order_sn" property="orderSn"/>
        <result column="member_name" property="memberName"/>
        <result column="member_id" property="memberId"/>
        <result column="store_id" property="storeId"/>
        <result column="store_name" property="storeName"/>
        <result column="manager" property="manager"/>
        <result column="manager_name" property="managerName"/>
        <result column="branch_code" property="branchCode"/>
        <result column="branch_name" property="branchName"/>
        <result column="area_code" property="areaCode"/>
        <result column="area_name" property="areaName"/>
        <result column="order_state" property="orderState"/>
        <result column="order_type" property="orderType"/>
        <result column="order_pattern" property="orderPattern"/>
        <result column="performance_modes" property="performanceModes"/>
        <result column="create_time" property="createTime"/>

        <collection property="orderProductVOs" ofType="com.cfpamf.ms.mallorder.vo.GroupOrderProductVO">
            <id column="order_product_id" property="orderProductId"/>
            <result column="order_sn" property="orderSn"/>
            <result column="goods_id" property="goodsId"/>
            <result column="goods_name" property="goodsName"/>
            <result column="product_image" property="productImage"/>
            <result column="product_id" property="productId"/>
            <result column="product_show_price" property="productShowPrice"/>
            <result column="product_num" property="productNum"/>
            <result column="return_number" property="returnNumber"/>
            <result column="validNum" property="validNum"/>
            <result column="channel_sku_unit" property="channelSkuUnit"/>
            <result column="channel_sku_id" property="channelSkuId"/>
            <result column="supplier_name" property="supplierName"/>
            <result column="supplier_code" property="supplierCode"/>
            <result column="is_gift" property="isGift"/>
            <result column="gift_group" property="giftGroup"/>
            <result column="performance_service" property="performanceService"/>
            <result column="spec_values" property="specValues"/>
            <result column="store_category_id" property="storeCategoryId"/>
            <result column="delivery_state" property="deliveryState"/>
            <result column="group_buying_tag" property="groupBuyingTag"/>
        </collection>
    </resultMap>

    <select id="listBindGroupOrder" resultMap="groupOrderMap">
        select bo.order_sn,
               bo.store_id,
               bo.store_name,
               bo.member_id,
               bo.member_name,
               bo.order_state,
               bo.order_type,
               bo.order_pattern,
               bo.performance_modes,
               bo.create_time,
               boe.manager,
               boe.manager_name,
               boe.area_code,
               boe.area_name,
               boe.branch branch_code,
               boe.branch_name,
               bop.order_product_id,
               bop.goods_id,
               bop.goods_name,
               bop.product_image,
               bop.product_id,
               bop.product_show_price,
               bop.product_num,
               bop.return_number,
               bop.product_num - bop.return_number as validNum,
               bop.channel_sku_id,
               bop.channel_sku_unit,
               bop.supplier_code,
               bop.supplier_name,
               bop.is_gift,
               bop.gift_group,
               bop.performance_service,
               bop.spec_values,
               bop.store_category_id,
               bop.delivery_state,
               bop.group_buying_tag
        from bz_order bo
           , bz_order_extend boe
           , bz_order_product bop
           , bz_order_group_buying_bind gbb
        where bo.order_sn = boe.order_sn
          and bo.order_sn = bop.order_sn
          and gbb.order_product_id = bop.order_product_id
          and gbb.enabled_flag = 1
        <include refid="whereCondition"/>
        order by bo.create_time desc
    </select>

</mapper>
