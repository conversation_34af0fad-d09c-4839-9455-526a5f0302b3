<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.LoanResultMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.mallorder.po.LoanResultPO">
        <result column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="enabled_flag" property="enabledFlag" />
        <result column="pay_no" property="payNo" />
        <result column="last_card" property="lastCard" />
        <result column="loan_result" property="loanResult" />
        <result column="failure_reason" property="failureReason" />
        <result column="retry_times" property="retryTimes" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_time,
        update_time,
        enabled_flag,
        pay_no, last_card, loan_result, failure_reason, retry_times, create_by, update_by
    </sql>

    <select id="countByRequest" resultType="java.lang.Integer">
        SELECT
            COUNT(*)
        FROM
            bz_loan_result blr
            LEFT JOIN bz_order bo ON blr.pay_no = bo.order_sn
            LEFT JOIN bz_order_extend boe ON boe.order_sn = bo.order_sn
        <include refid="whereCondition" />
    </select>

    <!-- 查询放款的结果信息 -->
    <select id="listPageByRequest" resultType="com.cfpamf.ms.mallorder.vo.LoanResultVo">
        SELECT
	        blr.pay_no payNo,
	        blr.last_card lastCard,
	        blr.retry_times retryTimes,
	        boe.customer_id customerId,
	        boe.customer_name customerName,
	        bo.order_amount priceSubtotal,
	        bo.deliver_time deliveryTime,
	        bo.payment_code loanProductCode,
	        boe.branch_name branchName,
            boe.branch branchCode,
	        boe.manager_name managerName,
	        boe.manager manager,
	        blr.loan_result loanResult,
	        blr.failure_reason failureReason,
	        blr.failure_type failureType
        FROM
	        bz_loan_result blr
	        LEFT JOIN bz_order bo ON blr.pay_no = bo.order_sn
	        LEFT JOIN bz_order_extend boe ON boe.order_sn = bo.order_sn
        <include refid="whereCondition" />
        ORDER BY
	        blr.create_time DESC
        <if test="size != null and size &gt; 0">
            limit #{startRow}, #{size}
        </if>
    </select>
    <!-- 查询放款的结果信息 -->
    <select id="listAutoLoanLendingMakeUp" resultType="com.cfpamf.ms.mallorder.vo.LoanResultVo">
        SELECT
        blr.pay_no payNo,
        blr.last_card lastCard,
        blr.retry_times retryTimes,
        boe.customer_id customerId,
        boe.customer_name customerName,
        bo.order_amount priceSubtotal,
        bo.deliver_time deliveryTime,
        bo.payment_code loanProductCode,
        boe.branch_name branchName,
        boe.branch branchCode,
        boe.manager_name managerName,
        boe.manager manager,
        blr.loan_result loanResult,
        blr.failure_reason failureReason,
        blr.failure_type failureType
        FROM
        bz_loan_result blr
        LEFT JOIN bz_order bo ON blr.pay_no = bo.order_sn
        LEFT JOIN bz_order_extend boe ON boe.order_sn = bo.order_sn
        where blr.enabled_flag = 1
        and bo.order_state !=50
        and blr.loan_result = 'FAIL'
        and blr.create_time &gt; DATE_ADD(NOW(),INTERVAL -15 day)
        ORDER BY
        blr.create_time DESC
    </select>
    <!--操作条件-->
    <sql id="whereCondition">
        <where>
            blr.enabled_flag = 1  and bo.order_state !=50
            <if test="params.payNo != null and params.payNo != ''">
                and blr.pay_no = #{params.payNo}
            </if>
            <if test="params.customerName != null and params.customerName != ''">
                and boe.customer_name = #{params.customerName}
            </if>
            <if test="params.deliveryTimeStart != null">
                and bo.deliver_time <![CDATA[ >= ]]> #{params.deliveryTimeStart}
            </if>
            <if test="params.deliveryTimeEnd != null">
                and bo.deliver_time <![CDATA[ <= ]]> #{params.deliveryTimeEnd}
            </if>
            <if test="params.loanResult != null and params.loanResult != ''">
                and blr.loan_result = #{params.loanResult}
            </if>
            <if test="params.handleType != null and params.handleType != ''">
                and blr.failure_type = #{params.handleType}
            </if>
        </where>
    </sql>


</mapper>
