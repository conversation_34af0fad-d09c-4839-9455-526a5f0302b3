<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.BzPayMethodTrackMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.mallorder.po.BzPayMethodTrackPO">
        <result column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="enabled_flag" property="enabledFlag" />
        <result column="flow_name" property="flowName" />
        <result column="operate_time" property="operateTime" />
        <result column="operate_user_name" property="operateUserName" />
        <result column="operate_result" property="operateResult" />
        <result column="remark" property="remark" />
        <result column="channel" property="channel" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_time,
        update_time,
        enabled_flag,
        flow_name, operate_time, operate_user_name, operate_result, remark, channel, create_by, update_by
    </sql>

</mapper>
