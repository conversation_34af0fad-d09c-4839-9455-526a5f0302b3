<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.ProofDefinitionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.mallorder.po.ProofDefinitionPO">
        <result column="proof_definition_id" property="proofDefinitionId" />
        <result column="proof_definition_code" property="proofDefinitionCode" />
        <result column="proof_definition_name" property="proofDefinitionName" />
        <result column="proof_definition_desc" property="proofDefinitionDesc" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="enabled_flag" property="enabledFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        proof_definition_id,
        proof_definition_code,
        proof_definition_name,
        proof_definition_desc,
        create_by, update_by,create_time,update_time, enabled_flag
    </sql>

    <select id="getProofDefinitionByCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from bz_proof_definition
        where proof_definition_code = #{proof_definition_code}
    </select>

</mapper>