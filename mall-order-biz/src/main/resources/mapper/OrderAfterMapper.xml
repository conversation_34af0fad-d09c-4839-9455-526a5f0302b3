<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cfpamf.ms.mallorder.mapper.OrderAfterMapper">
  <resultMap id="resultMap" type="com.cfpamf.ms.mallorder.po.OrderAfterPO">
      <id column="afs_id" property="afsId" />
      <result column="create_time" property="createTime" />
      <result column="update_time" property="updateTime" />
      <result column="enabled_flag" property="enabledFlag" />
      <result column="afs_sn" property="afsSn" />
      <result column="store_id" property="storeId" />
      <result column="store_name" property="storeName" />
      <result column="order_sn" property="orderSn" />
      <result column="member_id" property="memberId" />
      <result column="member_name" property="memberName" />
      <result column="goods_id" property="goodsId" />
      <result column="order_product_id" property="orderProductId" />
      <result column="afs_num" property="afsNum" />
      <result column="store_afs_address" property="storeAfsAddress" />
      <result column="buyer_express_name" property="buyerExpressName" />
      <result column="buyer_express_number" property="buyerExpressNumber" />
      <result column="buyer_express_code" property="buyerExpressCode" />
      <result column="buyer_deliver_time" property="buyerDeliverTime" />
      <result column="apply_image" property="applyImage" />
      <result column="contact_name" property="contactName" />
      <result column="contact_phone" property="contactPhone" />
      <result column="afs_type" property="afsType" />
      <result column="apply_reason_content" property="applyReasonContent" />
      <result column="afs_description" property="afsDescription" />
      <result column="buyer_apply_time" property="buyerApplyTime" />
      <result column="store_audit_time" property="storeAuditTime" />
      <result column="platform_audit_time" property="platformAuditTime" />
      <result column="buyer_remark" property="buyerRemark" />
      <result column="platform_remark" property="platformRemark" />
      <result column="store_remark" property="storeRemark" />
      <result column="store_receive_time" property="storeReceiveTime" />
      <result column="goods_state" property="goodsState" />
      <result column="create_by" property="createBy" />
      <result column="update_by" property="updateBy" />
      <result column="snapshot_information" property="snapshotInformation" />
      <result column="performance_mode" property="performanceMode" />
      <result column="gift_return_order_product_id" property="giftReturnOrderProductId" />
  </resultMap>
  <!--除主键外的所有字段，用于插入操作-->
  <sql id="columns">
    <trim suffixOverrides=",">
      <if test="afsSn != null">
        `afs_sn`,
      </if>
      <if test="storeId != null">
        `store_id`,
      </if>
      <if test="storeName != null">
        `store_name`,
      </if>
      <if test="orderSn != null">
        `order_sn`,
      </if>
      <if test="memberId != null">
        `member_id`,
      </if>
      <if test="memberName != null">
        `member_name`,
      </if>
      <if test="goodsId != null">
        `goods_id`,
      </if>
      <if test="orderProductId != null">
        `order_product_id`,
      </if>
      <if test="afsNum != null">
        `afs_num`,
      </if>
      <if test="storeAfsAddress != null">
        `store_afs_address`,
      </if>
      <if test="buyerExpressName != null">
        `buyer_express_name`,
      </if>
      <if test="buyerExpressNumber != null">
        `buyer_express_number`,
      </if>
      <if test="buyerExpressCode != null">
        `buyer_express_code`,
      </if>
      <if test="buyerDeliverTime != null">
        `buyer_deliver_time`,
      </if>
      <if test="applyImage != null">
        `apply_image`,
      </if>
      <if test="contactName != null">
        `contact_name`,
      </if>
      <if test="contactPhone != null">
        `contact_phone`,
      </if>
      <if test="afsType != null">
        `afs_type`,
      </if>
      <if test="applyReasonContent != null">
        `apply_reason_content`,
      </if>
      <if test="afsDescription != null">
        `afs_description`,
      </if>
      <if test="buyerApplyTime != null">
        `buyer_apply_time`,
      </if>
      <if test="storeAuditTime != null">
        `store_audit_time`,
      </if>
      <if test="platformAuditTime != null">
        `platform_audit_time`,
      </if>
      <if test="buyerRemark != null">
        `buyer_remark`,
      </if>
      <if test="platformRemark != null">
        `platform_remark`,
      </if>
      <if test="storeRemark != null">
        `store_remark`,
      </if>
      <if test="storeReceiveTime != null">
        `store_receive_time`,
      </if>
      <if test="goodsState != null">
        `goods_state`,
      </if>
      <if test="snapshotInformation != null">
        `snapshot_information`,
      </if>
      <if test="performanceMode != null">
        `performance_mode`,
      </if>
      <if test="productDeliveryState != null">
        `product_delivery_state`,
      </if>
    </trim>
  </sql>
  <!--按照主键值进行操作-->
  <sql id="pkWhere">
    WHERE `afs_id` = #{primaryKey} AND enabled_flag =1
  </sql>
  <!--操作条件-->
  <sql id="whereCondition">
    <if test="example != null">
      <trim prefix="WHERE" prefixOverrides="AND|OR">
        AND enabled_flag =1
        <if test="example.afsIdNotEquals != null">
          AND `afs_id` != #{example.afsIdNotEquals}
        </if>
        <if test="example.afsIdIn != null">
          AND `afs_id` in (${example.afsIdIn})
        </if>
        <if test="example.afsSn != null">
          AND `afs_sn` = #{example.afsSn}
        </if>
        <if test="example.afsSnLike != null">
          AND `afs_sn` like concat('%',#{example.afsSnLike},'%')
        </if>
        <if test="example.storeId != null">
          AND `store_id` = #{example.storeId}
        </if>
        <if test="example.storeName != null">
          AND `store_name` = #{example.storeName}
        </if>
        <if test="example.storeNameLike != null">
          AND `store_name` like concat('%',#{example.storeNameLike},'%')
        </if>
        <if test="example.orderSn != null">
          AND `order_sn` = #{example.orderSn}
        </if>
        <if test="example.orderSnLike != null">
          AND `order_sn` like concat('%',#{example.orderSnLike},'%')
        </if>
        <if test="example.memberId != null">
          AND `member_id` = #{example.memberId}
        </if>
        <if test="example.memberName != null">
          AND `member_name` = #{example.memberName}
        </if>
        <if test="example.memberNameLike != null">
          AND `member_name` like concat('%',#{example.memberNameLike},'%')
        </if>
        <if test="example.goodsId != null">
          AND `goods_id` = #{example.goodsId}
        </if>
        <if test="example.orderProductId != null">
          AND `order_product_id` = #{example.orderProductId}
        </if>
        <if test="example.afsNum != null">
          AND `afs_num` = #{example.afsNum}
        </if>
        <if test="example.afsNumLike != null">
          AND `afs_num` like concat('%',#{example.afsNumLike},'%')
        </if>
        <if test="example.storeAfsAddress != null">
          AND `store_afs_address` = #{example.storeAfsAddress}
        </if>
        <if test="example.buyerExpressName != null">
          AND `buyer_express_name` = #{example.buyerExpressName}
        </if>
        <if test="example.buyerExpressNameLike != null">
          AND `buyer_express_name` like concat('%',#{example.buyerExpressNameLike},'%')
        </if>
        <if test="example.buyerExpressNumber != null">
          AND `buyer_express_number` = #{example.buyerExpressNumber}
        </if>
        <if test="example.buyerExpressNumberLike != null">
          AND `buyer_express_number` like concat('%',#{example.buyerExpressNumberLike},'%')
        </if>
        <if test="example.buyerExpressCode != null">
          AND `buyer_express_code` = #{example.buyerExpressCode}
        </if>
        <if test="example.buyerDeliverTimeAfter != null">
          AND `buyer_deliver_time` <![CDATA[ >= ]]> #{example.buyerDeliverTimeAfter}
        </if>
        <if test="example.buyerDeliverTimeBefore != null">
          AND `buyer_deliver_time` <![CDATA[ <= ]]> #{example.buyerDeliverTimeBefore}
        </if>
        <if test="example.applyImage != null">
          AND `apply_image` = #{example.applyImage}
        </if>
        <if test="example.contactName != null">
          AND `contact_name` = #{example.contactName}
        </if>
        <if test="example.contactNameLike != null">
          AND `contact_name` like concat('%',#{example.contactNameLike},'%')
        </if>
        <if test="example.contactPhone != null">
          AND `contact_phone` = #{example.contactPhone}
        </if>
        <if test="example.afsType != null">
          AND `afs_type` = #{example.afsType}
        </if>
        <if test="example.applyReasonContent != null">
          AND `apply_reason_content` = #{example.applyReasonContent}
        </if>
        <if test="example.applyReasonContentLike != null">
          AND `apply_reason_content` like concat('%',#{example.applyReasonContentLike},'%')
        </if>
        <if test="example.afsDescription != null">
          AND `afs_description` = #{example.afsDescription}
        </if>
        <if test="example.buyerApplyTimeAfter != null">
          AND `buyer_apply_time` <![CDATA[ >= ]]> #{example.buyerApplyTimeAfter}
        </if>
        <if test="example.buyerApplyTimeBefore != null">
          AND `buyer_apply_time` <![CDATA[ <= ]]> #{example.buyerApplyTimeBefore}
        </if>
        <if test="example.storeAuditTimeAfter != null">
          AND `store_audit_time` <![CDATA[ >= ]]> #{example.storeAuditTimeAfter}
        </if>
        <if test="example.storeAuditTimeBefore != null">
          AND `store_audit_time` <![CDATA[ <= ]]> #{example.storeAuditTimeBefore}
        </if>
        <if test="example.platformAuditTimeAfter != null">
          AND `platform_audit_time` <![CDATA[ >= ]]> #{example.platformAuditTimeAfter}
        </if>
        <if test="example.platformAuditTimeBefore != null">
          AND `platform_audit_time` <![CDATA[ <= ]]> #{example.platformAuditTimeBefore}
        </if>
        <if test="example.buyerRemark != null">
          AND `buyer_remark` = #{example.buyerRemark}
        </if>
        <if test="example.platformRemark != null">
          AND `platform_remark` = #{example.platformRemark}
        </if>
        <if test="example.storeRemark != null">
          AND `store_remark` = #{example.storeRemark}
        </if>
        <if test="example.storeReceiveTimeAfter != null">
          AND `store_receive_time` <![CDATA[ >= ]]> #{example.storeReceiveTimeAfter}
        </if>
        <if test="example.storeReceiveTimeBefore != null">
          AND `store_receive_time` <![CDATA[ <= ]]> #{example.storeReceiveTimeBefore}
        </if>
        <if test="example.goodsState != null">
          AND `goods_state` = #{example.goodsState}
        </if>
        <if test="example.snapshotInformation != null">
          AND `snapshot_information` = #{example.snapshotInformation}
        </if>
      </trim>
    </if>
    <if test="example == null">
      where enabled_flag =1
    </if>
  </sql>
  <!--排序条件-->
  <sql id="orderBy">
    ORDER BY `afs_id` DESC
  </sql>
  <sql id="orderByOther">
    order by ${example.orderBy}
  </sql>
  <!--分组条件-->
  <sql id="groupBy">
    group by ${example.groupBy}
  </sql>
  <!--分页条件-->
  <sql id="limit">
    <if test="size != null and size &gt; 0">
      limit #{startRow},#{size}
    </if>
  </sql>
  <!--查询符合条件的记录数-->
  <select id="countByExample" parameterType="com.cfpamf.ms.mallorder.request.OrderAfterServiceExample" resultType="java.lang.Integer">
    SELECT
      COUNT(*)
    FROM `bz_order_after_service`
    <include refid="whereCondition" />
  </select>
  <!--根据主键查询记录-->
  <select id="getByPrimaryKey" resultMap="resultMap">
    SELECT
      *
    FROM `bz_order_after_service`
    <include refid="pkWhere" />
  </select>
  <!--查询符合条件的记录(所有字段)-->
  <select id="listByExample" resultMap="resultMap">
    SELECT
      *
    FROM `bz_order_after_service`
    <include refid="whereCondition" />
    <if test="example.groupBy != null">
      <include refid="groupBy" />
    </if>
    <choose>
      <when test="example.orderBy != null">
        <include refid="orderByOther" />
      </when>
      <otherwise>
        <include refid="orderBy" />
      </otherwise>
    </choose>
  </select>
  <!--分页查询符合条件的记录(所有字段)-->
  <select id="listPageByExample" resultMap="resultMap">
    SELECT
      *
    FROM `bz_order_after_service`
    <include refid="whereCondition" />
    <if test="example.groupBy != null">
      <include refid="groupBy" />
    </if>
    <choose>
      <when test="example.orderBy != null">
        <include refid="orderByOther" />
      </when>
      <otherwise>
        <include refid="orderBy" />
      </otherwise>
    </choose>
    <include refid="limit" />
  </select>
  <!--查询符合条件的记录(指定字段)-->
  <select id="listFieldsByExample" resultMap="resultMap">
    SELECT
      ${fields}
    FROM `bz_order_after_service`
    <include refid="whereCondition" />
    <if test="example.groupBy != null">
      <include refid="groupBy" />
    </if>
    <choose>
      <when test="example.orderBy != null">
        <include refid="orderByOther" />
      </when>
      <otherwise>
        <include refid="orderBy" />
      </otherwise>
    </choose>
  </select>
  <!--分页查询符合条件的记录(指定字段)-->
  <select id="listFieldsPageByExample" resultMap="resultMap">
    SELECT
      ${fields}
    FROM `bz_order_after_service`
    <include refid="whereCondition" />
    <if test="example.groupBy != null">
      <include refid="groupBy" />
    </if>
    <choose>
      <when test="example.orderBy != null">
        <include refid="orderByOther" />
      </when>
      <otherwise>
        <include refid="orderBy" />
      </otherwise>
    </choose>
    <include refid="limit" />
  </select>
  <!--根据条件删除记录，可多条删除-->
  <update id="deleteByExample">
    update `bz_order_after_service`SET enabled_flag =0
    <include refid="whereCondition" />
  </update>
  <!--根据主键删除记录-->
  <update id="deleteByPrimaryKey">
    update `bz_order_after_service`SET enabled_flag =0
    <include refid="pkWhere" />
  </update>
  <!--插入一条记录-->
  <insert id="insert" keyColumn="afs_id" keyProperty="afsId" parameterType="com.cfpamf.ms.mallorder.po.OrderAfterPO" useGeneratedKeys="true">
    INSERT INTO `bz_order_after_service`(
    <include refid="columns" />
    )
    VALUES(
    <trim suffixOverrides=",">
      <if test="afsSn != null">
        #{afsSn},
      </if>
      <if test="storeId != null">
        #{storeId},
      </if>
      <if test="storeName != null">
        #{storeName},
      </if>
      <if test="orderSn != null">
        #{orderSn},
      </if>
      <if test="memberId != null">
        #{memberId},
      </if>
      <if test="memberName != null">
        #{memberName},
      </if>
      <if test="goodsId != null">
        #{goodsId},
      </if>
      <if test="orderProductId != null">
        #{orderProductId},
      </if>
      <if test="afsNum != null">
        #{afsNum},
      </if>
      <if test="storeAfsAddress != null">
        #{storeAfsAddress},
      </if>
      <if test="buyerExpressName != null">
        #{buyerExpressName},
      </if>
      <if test="buyerExpressNumber != null">
        #{buyerExpressNumber},
      </if>
      <if test="buyerExpressCode != null">
        #{buyerExpressCode},
      </if>
      <if test="buyerDeliverTime != null">
        #{buyerDeliverTime},
      </if>
      <if test="applyImage != null">
        #{applyImage},
      </if>
      <if test="contactName != null">
        #{contactName},
      </if>
      <if test="contactPhone != null">
        #{contactPhone},
      </if>
      <if test="afsType != null">
        #{afsType},
      </if>
      <if test="applyReasonContent != null">
        #{applyReasonContent},
      </if>
      <if test="afsDescription != null">
        #{afsDescription},
      </if>
      <if test="buyerApplyTime != null">
        #{buyerApplyTime},
      </if>
      <if test="storeAuditTime != null">
        #{storeAuditTime},
      </if>
      <if test="platformAuditTime != null">
        #{platformAuditTime},
      </if>
      <if test="buyerRemark != null">
        #{buyerRemark},
      </if>
      <if test="platformRemark != null">
        #{platformRemark},
      </if>
      <if test="storeRemark != null">
        #{storeRemark},
      </if>
      <if test="storeReceiveTime != null">
        #{storeReceiveTime},
      </if>
      <if test="goodsState != null">
        #{goodsState},
      </if>
      <if test="snapshotInformation != null">
        #{snapshotInformation},
      </if>
      <if test="performanceMode != null">
        #{performanceMode},
      </if>
      <if test="productDeliveryState != null">
        #{productDeliveryState},
      </if>
    </trim>
    )
  </insert>
  <!--按条件更新记录中不为空的字段-->
  <update id="updateByExampleSelective">
    UPDATE `bz_order_after_service`
    <trim prefix="SET" suffixOverrides=",">
      <if test="record.afsSn != null">
        `afs_sn` = #{record.afsSn},
      </if>
      <if test="record.storeId != null">
        `store_id` = #{record.storeId},
      </if>
      <if test="record.storeName != null">
        `store_name` = #{record.storeName},
      </if>
      <if test="record.orderSn != null">
        `order_sn` = #{record.orderSn},
      </if>
      <if test="record.memberId != null">
        `member_id` = #{record.memberId},
      </if>
      <if test="record.memberName != null">
        `member_name` = #{record.memberName},
      </if>
      <if test="record.goodsId != null">
        `goods_id` = #{record.goodsId},
      </if>
      <if test="record.orderProductId != null">
        `order_product_id` = #{record.orderProductId},
      </if>
      <if test="record.afsNum != null">
        `afs_num` = #{record.afsNum},
      </if>
      <if test="record.storeAfsAddress != null">
        `store_afs_address` = #{record.storeAfsAddress},
      </if>
      <if test="record.buyerExpressName != null">
        `buyer_express_name` = #{record.buyerExpressName},
      </if>
      <if test="record.buyerExpressNumber != null">
        `buyer_express_number` = #{record.buyerExpressNumber},
      </if>
      <if test="record.buyerExpressCode != null">
        `buyer_express_code` = #{record.buyerExpressCode},
      </if>
      <if test="record.buyerDeliverTime != null">
        `buyer_deliver_time` = #{record.buyerDeliverTime},
      </if>
      <if test="record.applyImage != null">
        `apply_image` = #{record.applyImage},
      </if>
      <if test="record.contactName != null">
        `contact_name` = #{record.contactName},
      </if>
      <if test="record.contactPhone != null">
        `contact_phone` = #{record.contactPhone},
      </if>
      <if test="record.afsType != null">
        `afs_type` = #{record.afsType},
      </if>
      <if test="record.applyReasonContent != null">
        `apply_reason_content` = #{record.applyReasonContent},
      </if>
      <if test="record.afsDescription != null">
        `afs_description` = #{record.afsDescription},
      </if>
      <if test="record.buyerApplyTime != null">
        `buyer_apply_time` = #{record.buyerApplyTime},
      </if>
      <if test="record.storeAuditTime != null">
        `store_audit_time` = #{record.storeAuditTime},
      </if>
      <if test="record.platformAuditTime != null">
        `platform_audit_time` = #{record.platformAuditTime},
      </if>
      <if test="record.buyerRemark != null">
        `buyer_remark` = #{record.buyerRemark},
      </if>
      <if test="record.platformRemark != null">
        `platform_remark` = #{record.platformRemark},
      </if>
      <if test="record.storeRemark != null">
        `store_remark` = #{record.storeRemark},
      </if>
      <if test="record.storeReceiveTime != null">
        `store_receive_time` = #{record.storeReceiveTime},
      </if>
      <if test="record.goodsState != null">
        `goods_state` = #{record.goodsState},
      </if>
      <if test="record.snapshotInformation != null">
        `snapshot_information` = #{record.snapshotInformation},
      </if>
    </trim>
    <include refid="whereCondition" />
  </update>
  <!--按照主键更新记录中不为空的字段-->
  <update id="updateByPrimaryKeySelective">
    UPDATE `bz_order_after_service`
    <trim prefix="SET" suffixOverrides=",">
      <if test="afsSn != null">
        `afs_sn` = #{afsSn},
      </if>
      <if test="storeId != null">
        `store_id` = #{storeId},
      </if>
      <if test="storeName != null">
        `store_name` = #{storeName},
      </if>
      <if test="orderSn != null">
        `order_sn` = #{orderSn},
      </if>
      <if test="memberId != null">
        `member_id` = #{memberId},
      </if>
      <if test="memberName != null">
        `member_name` = #{memberName},
      </if>
      <if test="goodsId != null">
        `goods_id` = #{goodsId},
      </if>
      <if test="orderProductId != null">
        `order_product_id` = #{orderProductId},
      </if>
      <if test="afsNum != null">
        `afs_num` = #{afsNum},
      </if>
      <if test="storeAfsAddress != null">
        `store_afs_address` = #{storeAfsAddress},
      </if>
      <if test="buyerExpressName != null">
        `buyer_express_name` = #{buyerExpressName},
      </if>
      <if test="buyerExpressNumber != null">
        `buyer_express_number` = #{buyerExpressNumber},
      </if>
      <if test="buyerExpressCode != null">
        `buyer_express_code` = #{buyerExpressCode},
      </if>
      <if test="buyerDeliverTime != null">
        `buyer_deliver_time` = #{buyerDeliverTime},
      </if>
      <if test="applyImage != null">
        `apply_image` = #{applyImage},
      </if>
      <if test="contactName != null">
        `contact_name` = #{contactName},
      </if>
      <if test="contactPhone != null">
        `contact_phone` = #{contactPhone},
      </if>
      <if test="afsType != null">
        `afs_type` = #{afsType},
      </if>
      <if test="applyReasonContent != null">
        `apply_reason_content` = #{applyReasonContent},
      </if>
      <if test="afsDescription != null">
        `afs_description` = #{afsDescription},
      </if>
      <if test="buyerApplyTime != null">
        `buyer_apply_time` = #{buyerApplyTime},
      </if>
      <if test="storeAuditTime != null">
        `store_audit_time` = #{storeAuditTime},
      </if>
      <if test="refundTerminationTime != null">
        `refund_termination_time` = #{refundTerminationTime},
      </if>
      <if test="platformAuditTime != null">
        `platform_audit_time` = #{platformAuditTime},
      </if>
      <if test="buyerRemark != null">
        `buyer_remark` = #{buyerRemark},
      </if>
      <if test="platformRemark != null">
        `platform_remark` = #{platformRemark},
      </if>
      <if test="storeRemark != null">
        `store_remark` = #{storeRemark},
      </if>
      <if test="storeReceiveTime != null">
        `store_receive_time` = #{storeReceiveTime},
      </if>
      <if test="goodsState != null">
        `goods_state` = #{goodsState},
      </if>
      <if test="snapshotInformation != null">
        `snapshot_information` = #{snapshotInformation},
      </if>
    </trim>
    WHERE `afs_id` = #{afsId}
  </update>
  <!--按照主键更新记录中不为空的字段-->
  <update id="updateByAfsSn">
    UPDATE `bz_order_after_service`
    <trim prefix="SET" suffixOverrides=",">
      <if test="storeId != null">
        `store_id` = #{storeId},
      </if>
      <if test="storeName != null">
        `store_name` = #{storeName},
      </if>
      <if test="orderSn != null">
        `order_sn` = #{orderSn},
      </if>
      <if test="refundTerminationTime != null">
        `refund_termination_time` = #{refundTerminationTime},
      </if>
      <if test="memberId != null">
        `member_id` = #{memberId},
      </if>
      <if test="memberName != null">
        `member_name` = #{memberName},
      </if>
      <if test="goodsId != null">
        `goods_id` = #{goodsId},
      </if>
      <if test="orderProductId != null">
        `order_product_id` = #{orderProductId},
      </if>
      <if test="afsNum != null">
        `afs_num` = #{afsNum},
      </if>
      <if test="storeAfsAddress != null">
        `store_afs_address` = #{storeAfsAddress},
      </if>
      <if test="buyerExpressName != null">
        `buyer_express_name` = #{buyerExpressName},
      </if>
      <if test="buyerExpressNumber != null">
        `buyer_express_number` = #{buyerExpressNumber},
      </if>
      <if test="buyerExpressCode != null">
        `buyer_express_code` = #{buyerExpressCode},
      </if>
      <if test="buyerDeliverTime != null">
        `buyer_deliver_time` = #{buyerDeliverTime},
      </if>
      <if test="applyImage != null">
        `apply_image` = #{applyImage},
      </if>
      <if test="contactName != null">
        `contact_name` = #{contactName},
      </if>
      <if test="contactPhone != null">
        `contact_phone` = #{contactPhone},
      </if>
      <if test="afsType != null">
        `afs_type` = #{afsType},
      </if>
      <if test="applyReasonContent != null">
        `apply_reason_content` = #{applyReasonContent},
      </if>
      <if test="afsDescription != null">
        `afs_description` = #{afsDescription},
      </if>
      <if test="buyerApplyTime != null">
        `buyer_apply_time` = #{buyerApplyTime},
      </if>
      <if test="storeAuditTime != null">
        `store_audit_time` = #{storeAuditTime},
      </if>
      <if test="platformAuditTime != null">
        `platform_audit_time` = #{platformAuditTime},
      </if>
      <if test="buyerRemark != null">
        `buyer_remark` = #{buyerRemark},
      </if>
      <if test="platformRemark != null">
        `platform_remark` = #{platformRemark},
      </if>
      <if test="storeRemark != null">
        `store_remark` = #{storeRemark},
      </if>
      <if test="storeReceiveTime != null">
        `store_receive_time` = #{storeReceiveTime},
      </if>
      <if test="goodsState != null">
        `goods_state` = #{goodsState},
      </if>
      <if test="snapshotInformation != null">
        `snapshot_information` = #{snapshotInformation},
      </if>
      <if test="refundFailReason != null">
        `refund_fail_reason` = #{refundFailReason},
      </if>
    </trim>
    WHERE `afs_sn` = #{afsSn}
  </update>

  <select id="getProductRefundCount" resultType="com.cfpamf.ms.mallorder.dto.OrderRefundCountDTO">
    select order_product_id as orderProductId,sum(t.return_num)  as refundNum
    from bz_order_return t,bz_order_after_service s
    where t.afs_sn = s.afs_sn
      and t.return_type = 1
      and t.state in (300,400)
      <if test="orderProductIdList != null and orderProductIdList.size() > 0">
        and s.order_product_id in
        <foreach collection="orderProductIdList" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    group by s.order_product_id
  </select>

  <select id="getRefundStatusCountByIdAndState" resultType="int">
    select ifnull(count(1),0) as refundCount
    from bz_order_return t,bz_order_after_service s
    where t.afs_sn = s.afs_sn
      <if test="refundStateList != null and refundStateList.size() > 0">
        and t.state not in
        <foreach collection="refundStateList" item="item" open="(" separator="," close=")">
          #{item}
       </foreach>
      </if>
      <if test="orderProductIdList != null and orderProductIdList.size() > 0">
        and s.order_product_id in
        <foreach collection="orderProductIdList" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
  </select>
</mapper>
