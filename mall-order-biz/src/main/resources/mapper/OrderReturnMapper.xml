<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.OrderReturnMapper">
    <resultMap id="resultMap" type="com.cfpamf.ms.mallorder.po.OrderReturnPO">
        <id column="return_id" property="returnId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="enabled_flag" property="enabledFlag"/>
        <result column="afs_sn" property="afsSn"/>
        <result column="order_sn" property="orderSn"/>
        <result column="store_id" property="storeId"/>
        <result column="store_name" property="storeName"/>
        <result column="member_id" property="memberId"/>
        <result column="member_name" property="memberName"/>
        <result column="return_money_type" property="returnMoneyType"/>
        <result column="return_type" property="returnType"/>
        <result column="return_num" property="returnNum"/>
        <result column="return_money_amount" property="returnMoneyAmount"/>
        <result column="return_integral_amount" property="returnIntegralAmount"/>
        <result column="deduct_integral_amount" property="deductIntegralAmount"/>
        <result column="return_express_amount" property="returnExpressAmount"/>
        <result column="return_voucher_code" property="returnVoucherCode"/>
        <result column="commission_rate" property="commissionRate"/>
        <result column="commission_amount" property="commissionAmount"/>
        <result column="platform_activity_amount" property="platformActivityAmount"/>
        <result column="platform_voucher_amount" property="platformVoucherAmount"/>
        <result column="service_fee" property="serviceFee"/>
        <result column="thirdpartnar_fee" property="thirdpartnarFee"/>
        <result column="order_commission" property="orderCommission"/>
        <result column="business_commission" property="businessCommission"/>
        <result column="state" property="state"/>
        <result column="apply_time" property="applyTime"/>
        <result column="complete_time" property="completeTime"/>
        <result column="refuse_reason" property="refuseReason"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="payment_method" property="paymentMethod"/>
        <result column="actual_return_money_amount" property="actualReturnMoneyAmount"/>
        <result column="customer_assume_amount" property="customerAssumeAmount"/>
        <result column="refund_type" property="refundType"/>
        <result column="other_compensation_amount" property="otherCompensationAmount"/>
        <result column="plan_discount_amount" property="planDiscountAmount"/>
        <result column="interest_payer" property="interestPayer"/>
        <result column="remark" property="remark"/>
        <result column="refund_start_time" property="refundStartTime"/>
        <result column="refund_end_time" property="refundEndTime"/>
    </resultMap>
    <!--除主键外的所有字段，用于插入操作-->
    <sql id="columns">
        <trim suffixOverrides=",">
            <if test="afsSn != null">
                `afs_sn`,
            </if>
            <if test="orderSn != null">
                `order_sn`,
            </if>
            <if test="storeId != null">
                `store_id`,
            </if>
            <if test="storeName != null">
                `store_name`,
            </if>
            <if test="memberId != null">
                `member_id`,
            </if>
            <if test="memberName != null">
                `member_name`,
            </if>
            <if test="returnMoneyType != null">
                `return_money_type`,
            </if>
            <if test="returnType != null">
                `return_type`,
            </if>
            <if test="returnNum != null">
                `return_num`,
            </if>
            <if test="returnMoneyAmount != null">
                `return_money_amount`,
            </if>
            <if test="returnIntegralAmount != null">
                `return_integral_amount`,
            </if>
            <if test="deductIntegralAmount != null">
                `deduct_integral_amount`,
            </if>
            <if test="returnExpressAmount != null">
                `return_express_amount`,
            </if>
            <if test="actualReturnMoneyAmount != null">
                `actual_return_money_amount`,
            </if>
            <if test="customerAssumeAmount != null">
                `customer_assume_amount`,
            </if>
            <if test="refundType != null">
                `refund_type`,
            </if>
            <if test="refundStartTime != null">
                `refund_start_time`,
            </if>
            <if test="refundEndTime != null">
                `refund_end_time`,
            </if>
            <if test="otherCompensationAmount != null">
                `other_compensation_amount`,
            </if>
            <if test="planDiscountAmount != null">
                `plan_discount_amount`,
            </if>
            <if test="interestPayer != null">
                `interest_payer`,
            </if>
            <if test="remark != null">
                `remark`,
            </if>
            <if test="paymentMethod != null">
                `payment_method`,
            </if>
            <if test="returnVoucherCode != null">
                `return_voucher_code`,
            </if>
            <if test="commissionRate != null">
                `commission_rate`,
            </if>
            <if test="commissionAmount != null">
                `commission_amount`,
            </if>
            <if test="platformVoucherAmount != null">
                `platform_voucher_amount`,
            </if>
            <if test="platformActivityAmount != null">
                `platform_activity_amount`,
            </if>
            <if test="serviceFee != null">
                `service_fee`,
            </if>
            <if test="thirdpartnarFee != null">
                `thirdpartnar_fee`,
            </if>
            <if test="orderCommission != null">
                `order_commission`,
            </if>
            <if test="businessCommission != null">
                `business_commission`,
            </if>
            <if test="state != null">
                `state`,
            </if>
            <if test="applyTime != null">
                `apply_time`,
            </if>
            <if test="completeTime != null">
                `complete_time`,
            </if>
            <if test="refuseReason != null">
                `refuse_reason`,
            </if>
            <if test="returnBy != null">
                `return_by`,
            </if>
        </trim>
    </sql>
    <!--按照主键值进行操作-->
    <sql id="pkWhere">
        WHERE `return_id` = #{primaryKey} AND enabled_flag = 1
    </sql>
    <!--操作条件-->
    <sql id="whereCondition">
        <if test="example != null">
            <trim prefix="WHERE" prefixOverrides="AND|OR">
                AND enabled_flag =1
                <if test="example.returnIdNotEquals != null">
                    AND `return_id` != #{example.returnIdNotEquals}
                </if>
                <if test="example.returnIdIn != null">
                    AND `return_id` in (${example.returnIdIn})
                </if>
                <if test="example.returnId != null">
                    AND `return_id` = ${example.returnId}
                </if>
                <if test="example.afsSn != null">
                    AND `afs_sn` = #{example.afsSn}
                </if>
                <if test="example.afsSnLike != null">
                    AND `afs_sn` like concat('%',#{example.afsSnLike},'%')
                </if>
                <if test="example.orderSn != null">
                    AND `order_sn` = #{example.orderSn}
                </if>
                <if test="example.orderSnLike != null">
                    AND `order_sn` like concat('%',#{example.orderSnLike},'%')
                </if>
                <if test="example.storeId != null">
                    AND `store_id` = #{example.storeId}
                </if>
                <if test="example.storeName != null">
                    AND `store_name` = #{example.storeName}
                </if>
                <if test="example.storeNameLike != null">
                    AND `store_name` like concat('%',#{example.storeNameLike},'%')
                </if>
                <if test="example.memberId != null">
                    AND `member_id` = #{example.memberId}
                </if>
                <if test="example.memberName != null">
                    AND `member_name` = #{example.memberName}
                </if>
                <if test="example.memberNameLike != null">
                    AND `member_name` like concat('%',#{example.memberNameLike},'%')
                </if>
                <if test="example.returnMoneyType != null">
                    AND `return_money_type` = #{example.returnMoneyType}
                </if>
                <if test="example.returnType != null">
                    AND `return_type` = #{example.returnType}
                </if>
                <if test="example.returnNum != null">
                    AND `return_num` = #{example.returnNum}
                </if>
                <if test="example.returnMoneyAmount != null">
                    AND `return_money_amount` = #{example.returnMoneyAmount}
                </if>
                <if test="example.returnIntegralAmount != null">
                    AND `return_integral_amount` = #{example.returnIntegralAmount}
                </if>
                <if test="example.deductIntegralAmount != null">
                    AND `deduct_integral_amount` = #{example.deductIntegralAmount}
                </if>
                <if test="example.returnExpressAmount != null">
                    AND `return_express_amount` = #{example.returnExpressAmount}
                </if>
                <if test="example.interestPayer != null">
                    AND `interest_payer` = #{example.interestPayer}
                </if>
                <if test="example.returnVoucherCode != null">
                    AND `return_voucher_code` = #{example.returnVoucherCode}
                </if>
                <if test="example.returnVoucherCode != null">
                    AND `return_voucher_code` = #{example.returnVoucherCode}
                </if>
                <if test="example.commissionRate != null">
                    AND `commission_rate` = #{example.commissionRate}
                </if>
                <if test="example.commissionAmount != null">
                    AND `commission_amount` = #{example.commissionAmount}
                </if>
                <if test="example.state != null">
                    AND `state` = #{example.state}
                </if>
                <if test="example.stateIn != null">
                    AND `state` in (${example.stateIn})
                </if>
                <if test="example.stateNotIn != null">
                    AND `state` not in (${example.stateNotIn})
                </if>
                <if test="example.stateNotEquals != null">
                    AND `state` != #{example.stateNotEquals}
                </if>
                <if test="example.applyTimeAfter != null">
                    AND `apply_time` <![CDATA[ >= ]]> #{example.applyTimeAfter}
                </if>
                <if test="example.applyTimeBefore != null">
                    AND `apply_time` <![CDATA[ <= ]]> #{example.applyTimeBefore}
                </if>
                <if test="example.completeTimeAfter != null">
                    AND `complete_time` <![CDATA[ >= ]]> #{example.completeTimeAfter}
                </if>
                <if test="example.completeTimeBefore != null">
                    AND `complete_time` <![CDATA[ <= ]]> #{example.completeTimeBefore}
                </if>
                <if test="example.refuseReason != null">
                    AND `refuse_reason` = #{example.refuseReason}
                </if>
                <if test="example.refuseReasonLike != null">
                    AND `refuse_reason` like concat('%',#{example.refuseReasonLike},'%')
                </if>
                <if test="example.orderProductId != null">
                    AND `afs_sn` in (SELECT afs_sn FROM bz_order_after_service WHERE order_product_id = #{example.orderProductId})
                </if>
                <if test="example.deliverTimeEnd != null">
                    AND `afs_sn` in (SELECT afs_sn FROM bz_order_after_service WHERE buyer_deliver_time &lt;= #{example.deliverTimeEnd} )
                </if>
            </trim>
        </if>
        <if test="example == null">
            where enabled_flag =1
        </if>
    </sql>
    <!--排序条件-->
    <sql id="orderBy">
        ORDER BY `return_id` DESC
    </sql>

    <sql id="orderByOther">
        order by ${example.orderBy}
    </sql>
    <!--分组条件-->
    <sql id="groupBy">
        group by ${example.groupBy}
    </sql>

    <!--操作条件-->
    <sql id="whereConditionWithJoin">
        <if test="example != null">
            <trim prefix="WHERE" prefixOverrides="AND|OR">
                AND bor.enabled_flag =1
                <if test="example.returnIdNotEquals != null">
                    AND bor.return_id != #{example.returnIdNotEquals}
                </if>
                <if test="example.returnIdIn != null">
                    AND bor.return_id in (${example.returnIdIn})
                </if>
                <if test="example.returnId != null">
                    AND bor.return_id = ${example.returnId}
                </if>
                <if test="example.afsSn != null">
                    AND bor.afs_sn = #{example.afsSn}
                </if>
                <if test="example.afsSnLike != null">
                    AND bor.afs_sn like concat('%',#{example.afsSnLike},'%')
                </if>
                <if test="example.orderSn != null">
                    AND bor.order_sn = #{example.orderSn}
                </if>
                <if test="example.orderSnLike != null">
                    AND bor.order_sn like concat('%',#{example.orderSnLike},'%')
                </if>
                <if test="example.storeId != null">
                    AND bor.store_id = #{example.storeId}
                </if>
                <if test="example.storeIdIn != null">
                    AND bor.store_id in (${example.storeIdIn})
                </if>
                <if test="example.storeName != null">
                    AND bor.store_name = #{example.storeName}
                </if>
                <if test="example.storeNameLike != null">
                    AND bor.store_name like concat('%',#{example.storeNameLike},'%')
                </if>
                <if test="example.memberId != null">
                    AND bor.member_id = #{example.memberId}
                </if>
                <if test="example.memberIdIn != null">
                    AND bor.member_id in (${example.memberIdIn})
                </if>
                <if test="example.memberName != null">
                    AND bor.member_name = #{example.memberName}
                </if>
                <if test="example.memberNameLike != null">
                    AND bor.member_name like concat('%',#{example.memberNameLike},'%')
                </if>
                <if test="example.returnMoneyType != null">
                    AND bor.return_money_type = #{example.returnMoneyType}
                </if>
                <if test="example.returnType != null">
                    AND bor.return_type = #{example.returnType}
                </if>
                <if test="example.returnTypeIn != null">
                    AND bor.return_type in (${example.returnTypeIn})
                </if>
                <if test="example.refundType != null">
                    AND bor.refund_type = #{example.refundType}
                </if>
                <if test="example.returnNum != null">
                    AND bor.return_num = #{example.returnNum}
                </if>
                <if test="example.returnMoneyAmount != null">
                    AND bor.return_money_amount = #{example.returnMoneyAmount}
                </if>
                <if test="example.returnIntegralAmount != null">
                    AND bor.return_integral_amount = #{example.returnIntegralAmount}
                </if>
                <if test="example.deductIntegralAmount != null">
                    AND bor.deduct_integral_amount = #{example.deductIntegralAmount}
                </if>
                <if test="example.returnExpressAmount != null">
                    AND bor.return_express_amount = #{example.returnExpressAmount}
                </if>
                <if test="example.interestPayer != null">
                    AND bor.interest_payer = #{example.interestPayer}
                </if>
                <if test="example.returnVoucherCode != null">
                    AND bor.return_voucher_code = #{example.returnVoucherCode}
                </if>
                <if test="example.returnVoucherCode != null">
                    AND bor.return_voucher_code = #{example.returnVoucherCode}
                </if>
                <if test="example.commissionRate != null">
                    AND bor.commission_rate = #{example.commissionRate}
                </if>
                <if test="example.commissionAmount != null">
                    AND bor.commission_amount = #{example.commissionAmount}
                </if>
                <if test="example.state != null">
                    AND bor.state = #{example.state}
                </if>
                <if test="example.stateIn != null">
                    AND bor.state in (${example.stateIn})
                </if>
                <if test="example.stateNotIn != null">
                    AND bor.state not in (${example.stateNotIn})
                </if>
                <if test="example.stateNotEquals != null">
                    AND bor.state != #{example.stateNotEquals}
                </if>
                <if test="example.applyTimeAfter != null">
                    AND bor.apply_time <![CDATA[ >= ]]> #{example.applyTimeAfter}
                </if>
                <if test="example.applyTimeBefore != null">
                    AND bor.apply_time <![CDATA[ <= ]]> #{example.applyTimeBefore}
                </if>
                <if test="example.completeTimeAfter != null">
                    AND bor.complete_time <![CDATA[ >= ]]> #{example.completeTimeAfter}
                </if>
                <if test="example.refundStartTime != null">
                    AND bor.refund_end_time <![CDATA[ >= ]]> #{example.refundStartTime}
                </if>
                <if test="example.refundEndTime != null">
                    AND bor.refund_end_time <![CDATA[ <= ]]> #{example.refundEndTime}
                </if>
                <if test="example.jdInterceptStatus != null">
                    AND bor.jd_intercept_status =  #{example.jdInterceptStatus}
                </if>
                <if test="example.platformAuditStartTime != null">
                    AND boas.platform_audit_time <![CDATA[ >= ]]> #{example.platformAuditStartTime}
                </if>
                <if test="example.platformAuditEndTime != null">
                    AND boas.platform_audit_time <![CDATA[ <= ]]> #{example.platformAuditEndTime}
                </if>
                <if test="example.completeTimeBefore != null">
                    AND bor.complete_time <![CDATA[ <= ]]> #{example.completeTimeBefore}
                </if>
                <if test="example.refuseReason != null">
                    AND bor.refuse_reason = #{example.refuseReason}
                </if>
                <if test="example.refuseReasonLike != null">
                    AND bor.refuse_reason like concat('%',#{example.refuseReasonLike},'%')
                </if>
                <if test="example.orderProductId != null">
                    AND bor.afs_sn in (SELECT afs_sn FROM bz_order_after_service WHERE order_product_id = #{example.orderProductId})
                </if>
                <if test="example.deliverTimeEnd != null">
                    AND bor.afs_sn in (SELECT afs_sn FROM bz_order_after_service WHERE buyer_deliver_time &lt;= #{example.deliverTimeEnd} )
                </if>
                <if test="example.channel != null">
                    AND bo.channel = #{example.channel}
                </if>
                <if test="example.customerId != null and example.customerId != ''">
                    AND boe.customer_id = #{example.customerId}
                </if>
                <if test="example.customerName != null and example.customerName != ''">
                    AND boe.customer_name like concat('%',#{example.customerName},'%')
                </if>
                <if test="example.branchNameLike != null">
                    AND boe.branch_name like concat('%',#{example.branchNameLike},'%')
                </if>
                <if test="example.areaNameLike != null">
                    AND boe.area_name like concat('%',#{example.areaNameLike},'%')
                </if>
                <if test="example.manager != null">
                    AND boe.manager_name = #{example.manager}
                </if>
                <if test="example.managerNameLike != null">
                    AND boe.manager_name like concat('%',#{example.managerNameLike},'%')
                </if>
                <if test="example.branchCodeIn != null and example.branchCodeIn.size() > 0">
                    AND EXISTS (select 1 from bz_order_performance_belongs bopb where bopb.order_sn = bo.order_sn and bopb.employee_branch_code in
                    <foreach collection="example.branchCodeIn" item="branchCode" open="(" separator="," close=")">
                        #{branchCode}
                    </foreach>
                    )
                </if>
                <if test="example.managerNameList != null and example.managerNameList.size() > 0">
                    AND EXISTS (select 1 from bz_order_performance_belongs bopb where bopb.order_sn = bo.order_sn and bopb.belonger_name in
                    <foreach collection="example.managerNameList" item="managerName" open="(" separator="," close=")">
                        #{managerName}
                    </foreach>
                    )
                </if>
                <if test="example.goodsName != null and example.goodsName != ''">
                    AND bop.goods_name like concat('%',#{example.goodsName},'%')
                </if>
                <if test="example.storeName != null and example.storeName != ''">
                    AND bor.store_name like concat('%',#{example.storeName},'%')
                </if>
                <if test="example.recommendStoreId != null and example.recommendStoreId != ''">
                    AND bo.recommend_store_id = #{example.recommendStoreId}
                </if>
                <if test="example.orderPattern != null and example.orderPattern != ''">
                    AND bo.order_pattern = #{example.orderPattern}
                </if>
                <if test="example.orderType != null and example.orderType != ''">
                    AND bo.order_type = #{example.orderType}
                </if>
                <if test="example.contactName != null and example.contactName != ''">
                    AND boas.contact_name = #{example.contactName}
                </if>
                <if test="example.returnBy != null and example.returnBy != ''">
                    AND bor.return_by = #{example.returnBy}
                </if>
                <if test="example.userMobile != null and example.userMobile != ''">
                    AND bo.user_mobile like concat('%',#{example.userMobile},'%')
                </if>
                <if test="example.exchangeSn != null and example.exchangeSn != ''">
                    AND boex.exchange_sn = #{example.exchangeSn}
                </if>
                <if test="example.exchangeOrderState != null and example.exchangeOrderState != ''">
                    AND boex.exchange_order_state = #{example.exchangeOrderState}
                </if>
                <if test="example.productDeliveryState != null">
                    AND boas.product_delivery_state = #{example.productDeliveryState}
                </if>
            </trim>
        </if>
        <if test="example == null">
            where bor.enabled_flag =1
        </if>
    </sql>
    <!--排序条件-->
    <sql id="orderByWithJoin">
        ORDER BY bor.return_id DESC
    </sql>

    <!--分页条件-->
    <sql id="limit">
        <if test="size != null and size &gt; 0">
            limit #{startRow},#{size}
        </if>
    </sql>
    <!--查询符合条件的记录数-->
    <select id="countByExample" parameterType="com.cfpamf.ms.mallorder.request.OrderReturnExample"
            resultType="java.lang.Integer">
        SELECT
        COUNT(*)
        FROM `bz_order_return`
        <include refid="whereCondition"/>
    </select>
    <!--根据主键查询记录-->
    <select id="getByPrimaryKey" resultMap="resultMap">
        SELECT
        *
        FROM `bz_order_return`
        <include refid="pkWhere"/>
    </select>
    <!--查询符合条件的记录(所有字段)-->
    <select id="listByExample" resultMap="resultMap">
        SELECT
        *
        FROM `bz_order_return`
        <include refid="whereCondition"/>
        <if test="example.groupBy != null">
            <include refid="groupBy"/>
        </if>
        <choose>
            <when test="example.orderBy != null">
                <include refid="orderByOther"/>
            </when>
            <otherwise>
                <include refid="orderBy"/>
            </otherwise>
        </choose>
    </select>
    <!--分页查询符合条件的记录(所有字段)-->
    <select id="listPageByExample" resultMap="resultMap">
        SELECT
        *
        FROM `bz_order_return`
        <include refid="whereCondition"/>
        <if test="example.groupBy != null">
            <include refid="groupBy"/>
        </if>
        <choose>
            <when test="example.orderBy != null">
                <include refid="orderByOther"/>
            </when>
            <otherwise>
                <include refid="orderBy"/>
            </otherwise>
        </choose>
        <include refid="limit"/>
    </select>
    <!--查询符合条件的记录(指定字段)-->
    <select id="listFieldsByExample" resultMap="resultMap">
        SELECT
        ${fields}
        FROM `bz_order_return`
        <include refid="whereCondition"/>
        <if test="example.groupBy != null">
            <include refid="groupBy"/>
        </if>
        <choose>
            <when test="example.orderBy != null">
                <include refid="orderByOther"/>
            </when>
            <otherwise>
                <include refid="orderBy"/>
            </otherwise>
        </choose>
    </select>
    <!--分页查询符合条件的记录(指定字段)-->
    <select id="listFieldsPageByExample" resultMap="resultMap">
        SELECT
        ${fields}
        FROM `bz_order_return`
        <include refid="whereCondition"/>
        <if test="example.groupBy != null">
            <include refid="groupBy"/>
        </if>
        <choose>
            <when test="example.orderBy != null">
                <include refid="orderByOther"/>
            </when>
            <otherwise>
                <include refid="orderBy"/>
            </otherwise>
        </choose>
        <include refid="limit"/>
    </select>
    <!--根据条件删除记录，可多条删除-->
    <update id="deleteByExample">
        update `bz_order_return`SET enabled_flag =0
        <include refid="whereCondition"/>
    </update>
    <!--根据主键删除记录-->
    <update id="deleteByPrimaryKey">
        update `bz_order_return`SET enabled_flag =0
        <include refid="pkWhere"/>
    </update>

    <!--按条件更新记录中不为空的字段-->
    <update id="updateByExampleSelective">
        UPDATE `bz_order_return`
        <trim prefix="SET" suffixOverrides=",">
            <if test="record.afsSn != null">
                `afs_sn` = #{record.afsSn},
            </if>
            <if test="record.orderSn != null">
                `order_sn` = #{record.orderSn},
            </if>
            <if test="record.storeId != null">
                `store_id` = #{record.storeId},
            </if>
            <if test="record.storeName != null">
                `store_name` = #{record.storeName},
            </if>
            <if test="record.memberId != null">
                `member_id` = #{record.memberId},
            </if>
            <if test="record.memberName != null">
                `member_name` = #{record.memberName},
            </if>
            <if test="record.returnMoneyType != null">
                `return_money_type` = #{record.returnMoneyType},
            </if>
            <if test="record.returnType != null">
                `return_type` = #{record.returnType},
            </if>
            <if test="record.returnNum != null">
                `return_num` = #{record.returnNum},
            </if>
            <if test="record.returnMoneyAmount != null">
                `return_money_amount` = #{record.returnMoneyAmount},
            </if>
            <if test="record.returnIntegralAmount != null">
                `return_integral_amount` = #{record.returnIntegralAmount},
            </if>
            <if test="record.deductIntegralAmount != null">
                `deduct_integral_amount` = #{record.deductIntegralAmount},
            </if>
            <if test="record.returnExpressAmount != null">
                `return_express_amount` = #{record.returnExpressAmount},
            </if>
            <if test="record.actualReturnMoneyAmount != null">
                `actual_return_money_amount` = #{record.actualReturnMoneyAmount}
            </if>
            <if test="record.customerAssumeAmount != null">
                `customer_assume_amount` = #{record.customerAssumeAmount}
            </if>
            <if test="record.refundType != null">
                `refund_type` = #{record.refundType}
            </if>
            <if test="record.refundStartTime != null">
                `refund_start_time` = #{record.refundStartTime}
            </if>
            <if test="record.refundEndTime != null">
                `refund_end_time` = #{record.refundEndTime}
            </if>
            <if test="record.otherCompensationAmount != null">
                `other_compensation_amount` = #{record.otherCompensationAmount}
            </if>
            <if test="record.planDiscountAmount != null">
                `plan_discount_amount` = #{record.planDiscountAmount}
            </if>
            <if test="record.interestPayer != null">
                `interest_payer` = #{record.interestPayer}
            </if>
            <if test="record.remark != null">
                `remark` = #{record.remark}
            </if>
            <if test="record.returnVoucherCode != null">
                `return_voucher_code` = #{record.returnVoucherCode},
            </if>
            <if test="record.commissionRate != null">
                `commission_rate` = #{record.commissionRate},
            </if>
            <if test="record.commissionAmount != null">
                `commission_amount` = #{record.commissionAmount},
            </if>
            <if test="record.serviceFee != null">
                `service_fee` = #{record.serviceFee},
            </if>
            <if test="record.thirdpartnarFee != null">
                `thirdpartnar_fee` = #{record.thirdpartnarFee},
            </if>
            <if test="record.orderCommission != null">
                `order_commission` = #{record.orderCommission},
            </if>
            <if test="record.businessCommission != null">
                `business_commission` = #{record.businessCommission},
            </if>
            <if test="record.state != null">
                `state` = #{record.state},
            </if>
            <if test="record.applyTime != null">
                `apply_time` = #{record.applyTime},
            </if>
            <if test="record.completeTime != null">
                `complete_time` = #{record.completeTime},
            </if>
            <if test="record.refuseReason != null">
                `refuse_reason` = #{record.refuseReason},
            </if>
        </trim>
        <include refid="whereCondition"/>
    </update>
    <!--按照主键更新记录中不为空的字段-->
    <update id="updateByPrimaryKeySelective">
        UPDATE `bz_order_return`
        <trim prefix="SET" suffixOverrides=",">
            <if test="afsSn != null">
                `afs_sn` = #{afsSn},
            </if>
            <if test="orderSn != null">
                `order_sn` = #{orderSn},
            </if>
            <if test="storeId != null">
                `store_id` = #{storeId},
            </if>
            <if test="storeName != null">
                `store_name` = #{storeName},
            </if>
            <if test="memberId != null">
                `member_id` = #{memberId},
            </if>
            <if test="memberName != null">
                `member_name` = #{memberName},
            </if>
            <if test="returnMoneyType != null">
                `return_money_type` = #{returnMoneyType},
            </if>
            <if test="returnType != null">
                `return_type` = #{returnType},
            </if>
            <if test="returnNum != null">
                `return_num` = #{returnNum},
            </if>
            <if test="returnMoneyAmount != null">
                `return_money_amount` = #{returnMoneyAmount},
            </if>
            <if test="returnIntegralAmount != null">
                `return_integral_amount` = #{returnIntegralAmount},
            </if>
            <if test="deductIntegralAmount != null">
                `deduct_integral_amount` = #{deductIntegralAmount},
            </if>
            <if test="returnExpressAmount != null">
                `return_express_amount` = #{returnExpressAmount},
            </if>
            <if test="actualReturnMoneyAmount != null">
                `actual_return_money_amount` = #{actualReturnMoneyAmount},
            </if>
            <if test="customerAssumeAmount != null">
                `customer_assume_amount` = #{customerAssumeAmount},
            </if>
            <if test="refundType != null">
                `refund_type` = #{refundType},
            </if>
            <if test="refundStartTime != null">
                `refund_start_time` = #{refundStartTime},
            </if>
            <if test="refundEndTime != null">
                `refund_end_time` = #{refundEndTime},
            </if>
            <if test="otherCompensationAmount != null">
                `other_compensation_amount` = #{otherCompensationAmount},
            </if>
            <if test="planDiscountAmount != null">
                `plan_discount_amount` = #{planDiscountAmount},
            </if>
            <if test="interestPayer != null">
                `interest_payer` = #{interestPayer},
            </if>
            <if test="remark != null">
                `remark` = #{remark},
            </if>
            <if test="returnVoucherCode != null">
                `return_voucher_code` = #{returnVoucherCode},
            </if>
            <if test="commissionRate != null">
                `commission_rate` = #{commissionRate},
            </if>
            <if test="commissionAmount != null">
                `commission_amount` = #{commissionAmount},
            </if>
            <if test="serviceFee != null">
                `service_fee` = #{serviceFee},
            </if>
            <if test="thirdpartnarFee != null">
                `thirdpartnar_fee` = #{thirdpartnarFee},
            </if>
            <if test="orderCommission != null">
                `order_commission` = #{orderCommission},
            </if>
            <if test="businessCommission != null">
                `business_commission` = #{businessCommission},
            </if>
            <if test="state != null">
                `state` = #{state},
            </if>
            <if test="applyTime != null">
                `apply_time` = #{applyTime},
            </if>
            <if test="completeTime != null">
                `complete_time` = #{completeTime},
            </if>
            <if test="refuseReason != null">
                `refuse_reason` = #{refuseReason},
            </if>
        </trim>
        WHERE `return_id` = #{returnId}
    </update>

    <update id="updateByAfsSn">
        UPDATE `bz_order_return`
        <trim prefix="SET" suffixOverrides=",">
            <if test="storeId != null">
                `store_id` = #{storeId},
            </if>
            <if test="storeName != null">
                `store_name` = #{storeName},
            </if>
            <if test="memberId != null">
                `member_id` = #{memberId},
            </if>
            <if test="memberName != null">
                `member_name` = #{memberName},
            </if>
            <if test="returnMoneyType != null">
                `return_money_type` = #{returnMoneyType},
            </if>
            <if test="returnType != null">
                `return_type` = #{returnType},
            </if>
            <if test="returnNum != null">
                `return_num` = #{returnNum},
            </if>
            <if test="returnMoneyAmount != null">
                `return_money_amount` = #{returnMoneyAmount},
            </if>
            <if test="returnIntegralAmount != null">
                `return_integral_amount` = #{returnIntegralAmount},
            </if>
            <if test="deductIntegralAmount != null">
                `deduct_integral_amount` = #{deductIntegralAmount},
            </if>
            <if test="returnExpressAmount != null">
                `return_express_amount` = #{returnExpressAmount},
            </if>
            <if test="actualReturnMoneyAmount != null">
                `actual_return_money_amount` = #{actualReturnMoneyAmount},
            </if>
            <if test="customerAssumeAmount != null">
                `customer_assume_amount` = #{customerAssumeAmount},
            </if>
            <if test="refundType != null">
                `refund_type` = #{refundType},
            </if>
            <if test="refundStartTime != null">
                `refund_start_time` = #{refundStartTime},
            </if>
            <if test="refundEndTime != null">
                `refund_end_time` = #{refundEndTime},
            </if>
            <if test="otherCompensationAmount != null">
                `other_compensation_amount` = #{otherCompensationAmount},
            </if>
            <if test="planDiscountAmount != null">
                `plan_discount_amount` = #{planDiscountAmount},
            </if>
            <if test="interestPayer != null">
                `interest_payer` = #{interestPayer},
            </if>
            <if test="remark != null">
                `remark` = #{remark},
            </if>
            <if test="returnVoucherCode != null">
                `return_voucher_code` = #{returnVoucherCode},
            </if>
            <if test="commissionRate != null">
                `commission_rate` = #{commissionRate},
            </if>
            <if test="commissionAmount != null">
                `commission_amount` = #{commissionAmount},
            </if>
            <if test="serviceFee != null">
                `service_fee` = #{serviceFee},
            </if>
            <if test="thirdpartnarFee != null">
                `thirdpartnar_fee` = #{thirdpartnarFee},
            </if>
            <if test="orderCommission != null">
                `order_commission` = #{orderCommission},
            </if>
            <if test="businessCommission != null">
                `business_commission` = #{businessCommission},
            </if>
            <if test="state != null">
                `state` = #{state},
            </if>
            <if test="applyTime != null">
                `apply_time` = #{applyTime},
            </if>
            <if test="completeTime != null">
                `complete_time` = #{completeTime},
            </if>
            <if test="refuseReason != null">
                `refuse_reason` = #{refuseReason},
            </if>
            <if test="refundFailTimes != null">
                `refund_fail_times` = #{refundFailTimes},
            </if>
        </trim>
        where `afs_sn` = #{afsSn}
    </update>
    <update id="updateByOrderSn">
        UPDATE `bz_order_return`
        <trim prefix="SET" suffixOverrides=",">
            <if test="storeId != null">
                `store_id` = #{storeId},
            </if>
            <if test="storeName != null">
                `store_name` = #{storeName},
            </if>
            <if test="memberId != null">
                `member_id` = #{memberId},
            </if>
            <if test="memberName != null">
                `member_name` = #{memberName},
            </if>
            <if test="returnMoneyType != null">
                `return_money_type` = #{returnMoneyType},
            </if>
            <if test="returnType != null">
                `return_type` = #{returnType},
            </if>
            <if test="returnNum != null">
                `return_num` = #{returnNum},
            </if>
            <if test="returnMoneyAmount != null">
                `return_money_amount` = #{returnMoneyAmount},
            </if>
            <if test="returnIntegralAmount != null">
                `return_integral_amount` = #{returnIntegralAmount},
            </if>
            <if test="deductIntegralAmount != null">
                `deduct_integral_amount` = #{deductIntegralAmount},
            </if>
            <if test="returnExpressAmount != null">
                `return_express_amount` = #{returnExpressAmount},
            </if>
            <if test="actualReturnMoneyAmount != null">
                `actual_return_money_amount` = #{actualReturnMoneyAmount},
            </if>
            <if test="customerAssumeAmount != null">
                `customer_assume_amount` = #{customerAssumeAmount},
            </if>
            <if test="refundType != null">
                `refund_type` = #{refundType},
            </if>
            <if test="refundStartTime != null">
                `refund_start_time` = #{refundStartTime},
            </if>
            <if test="refundEndTime != null">
                `refund_end_time` = #{refundEndTime},
            </if>
            <if test="otherCompensationAmount != null">
                `other_compensation_amount` = #{otherCompensationAmount},
            </if>
            <if test="planDiscountAmount != null">
                `plan_discount_amount` = #{planDiscountAmount},
            </if>
            <if test="interestPayer != null">
                `interest_payer` = #{interestPayer},
            </if>
            <if test="remark != null">
                `remark` = #{remark},
            </if>
            <if test="returnVoucherCode != null">
                `return_voucher_code` = #{returnVoucherCode},
            </if>
            <if test="commissionRate != null">
                `commission_rate` = #{commissionRate},
            </if>
            <if test="commissionAmount != null">
                `commission_amount` = #{commissionAmount},
            </if>
            <if test="serviceFee != null">
                `service_fee` = #{serviceFee},
            </if>
            <if test="thirdpartnarFee != null">
                `thirdpartnar_fee` = #{thirdpartnarFee},
            </if>
            <if test="orderCommission != null">
                `order_commission` = #{orderCommission},
            </if>
            <if test="businessCommission != null">
                `business_commission` = #{businessCommission},
            </if>
            <if test="state != null">
                `state` = #{state},
            </if>
            <if test="applyTime != null">
                `apply_time` = #{applyTime},
            </if>
            <if test="completeTime != null">
                `complete_time` = #{completeTime},
            </if>
            <if test="refuseReason != null">
                `refuse_reason` = #{refuseReason},
            </if>
            <if test="refundFailTimes != null">
                `refund_fail_times` = #{refundFailTimes},
            </if>
        </trim>
        where `order_sn` = #{orderSn}
    </update>

    <select id="sumReturnedExpressFee" resultType="java.math.BigDecimal">
        select sum(bor.return_express_amount)
        from  bz_order_return bor
        where
            order_sn = #{orderSn}
          and bor.state in (100, 101, 102,200,201,203,300,400)
          and bor.enabled_flag = 1
    </select>



    <!--分页查询符合条件的记录，连表查询(所有字段)-->
    <select id="listPageByExampleWithJoin" resultType="com.cfpamf.ms.mallorder.vo.OrderReturnVOV2">
        SELECT
            bor.return_id returnId,
            bop.goods_name goodsName,
            bop.goods_id goodsId,
            bop.order_product_id orderProductId,
            bop.product_id productId,
            bop.is_gift,
            bop.gift_group giftGroup,
            bop.return_number productReturnNum,
            bop.product_image productImage,
            bop.spec_values specValues,
            bop.delivery_state deliveryState,
            bo.order_sn orderSn,
            bo.order_type orderType,
            bo.order_pattern orderPattern,
            bor.afs_sn afsSn,
            bor.return_num returnNum,
            bor.actual_return_money_amount + bor.return_express_amount returnMoneyAmount,
            bor.return_express_amount returnExpressAmount,
            bor.xz_card_express_fee_amount,
            bor.xz_card_amount,
            bor.store_id storeId,
            bor.store_name storeName,
            bor.member_id memberId,
            bor.member_name memberName,
            bor.return_type returnType,
            bor.state state,
            bor.apply_time applyTime,
            bo.order_state orderState,
            bo.pay_amount,
            bor.refund_type refundType,
            bor.refund_start_time refundStartTime,
            bor.refund_end_time refundEndTime,
            boe.customer_id customerId,
            boe.customer_name customerName,
            boe.manager_name managerName,
            boe.branch_name branchName,
            bo.user_no,
            bo.recommend_store_name recommendStoreName,
            bo.order_pattern orderPattern,
            bo.user_mobile userMobile,
            boas.contact_name contactName,
            boas.apply_reason_content applyReasonContent,
            boas.performance_mode performanceMode,
            boas.platform_audit_time platformAuditTime,
            boas.gift_return_order_product_id giftReturnOrderProductId,
            boex.exchange_order_state exchangeOrderState,
            boed.exchange_order_sn exchangeOrderSn,
            boex.exchange_sn exchangeSn,
            bor.jd_intercept_status jdInterceptStatus
        FROM `bz_order_return` bor
        LEFT JOIN `bz_order` bo ON bor.order_sn = bo.order_sn
        LEFT JOIN `bz_order_after_service` boas ON bor.afs_sn = boas.afs_sn
        LEFT JOIN `bz_order_product` bop ON boas.order_product_id = bop.order_product_id
        LEFT JOIN `bz_order_extend` boe ON boe.order_sn = bor.order_sn
        LEFT JOIN `bz_order_exchange_detail` boed ON bor.afs_sn = boed.afs_sn
        LEFT JOIN `bz_order_exchange` boex ON boex.exchange_sn = boed.exchange_sn
        <include refid="whereConditionWithJoin"/>
        <if test="example.groupBy != null">
            <include refid="groupBy"/>
        </if>
        <choose>
            <when test="example.orderBy != null">
                <include refid="orderByOther"/>
            </when>
            <otherwise>
                <include refid="orderByWithJoin"/>
            </otherwise>
        </choose>
        <include refid="limit"/>
    </select>
    <!--查询符合条件的记录(所有字段)-->
    <select id="listByExampleWithJoin" resultType="com.cfpamf.ms.mallorder.vo.OrderReturnVOV2">
        SELECT
            bor.return_id returnId,
            bop.goods_name goodsName,
            bop.goods_id goodsId,
            bop.product_id productId,
            bop.product_image productImage,
            bop.spec_values specValues,
            bop.delivery_state deliveryState,
            bo.order_sn orderSn,
            bo.order_pattern orderPattern,
            bor.afs_sn afsSn,
            bor.return_num returnNum,
            bor.return_money_amount + bor.return_express_amount + bor.other_compensation_amount - bor.customer_assume_amount returnMoneyAmount,
            bor.return_express_amount returnExpressAmount,
            bor.store_id storeId,
            bor.store_name storeName,
            bor.member_id memberId,
            bor.member_name memberName,
            bor.return_type returnType,
            bor.state state,
            bor.apply_time applyTime,
            bo.order_state orderState,
            bor.refund_type refundType,
            bor.refund_start_time refundStartTime,
            bor.refund_end_time refundEndTime,
            boe.customer_id customerId,
            boe.customer_name customerName,
            bor.jd_intercept_status jdInterceptStatus
        FROM `bz_order_return` bor
        LEFT JOIN `bz_order` bo ON bor.order_sn = bo.order_sn
        LEFT JOIN `bz_order_after_service` boas ON bor.afs_sn = boas.afs_sn
        LEFT JOIN `bz_order_product` bop ON boas.order_product_id = bop.order_product_id
        LEFT JOIN `bz_order_extend` boe ON boe.order_sn = bor.order_sn
        LEFT JOIN `bz_order_exchange_detail` boed ON bor.afs_sn = boed.afs_sn
        LEFT JOIN `bz_order_exchange` boex ON boex.exchange_sn = boed.exchange_sn
        <include refid="whereConditionWithJoin"/>
        <if test="example.groupBy != null">
            <include refid="groupBy"/>
        </if>
        <choose>
            <when test="example.orderBy != null">
                <include refid="orderByOther"/>
            </when>
            <otherwise>
                <include refid="orderBy"/>
            </otherwise>
        </choose>
    </select>
    <!--查询符合条件的记录数-->
    <select id="countByExampleWithJoin" parameterType="com.cfpamf.ms.mallorder.request.OrderReturnExample"
            resultType="java.lang.Integer">
        SELECT
            COUNT(*)
        FROM `bz_order_return` bor
        LEFT JOIN `bz_order` bo ON bor.order_sn = bo.order_sn
        LEFT JOIN `bz_order_after_service` boas ON bor.afs_sn = boas.afs_sn
        LEFT JOIN `bz_order_product` bop ON boas.order_product_id = bop.order_product_id
        LEFT JOIN `bz_order_extend` boe ON boe.order_sn = bor.order_sn
        LEFT JOIN `bz_order_exchange_detail` boed ON bor.afs_sn = boed.afs_sn
        LEFT JOIN `bz_order_exchange` boex ON boex.exchange_sn = boed.exchange_sn
        <include refid="whereConditionWithJoin"/>
    </select>

    <select id="orderRefundExportByPage" resultType="com.cfpamf.ms.mallorder.vo.exportvo.OrderRefundExportVO"
            parameterType="com.cfpamf.ms.mallorder.req.exportreq.OrderRefundExportRequest">
        SELECT
            bor.afs_sn refundNum,
	        bor.order_sn orderSn,
	        bo.payment_name payMethod,
	        bor.create_time refundStartTime,
	        boe.customer_id customerNo,
	        boe.customer_name customerName,
	        bo.member_name memberName,
	        boe.manager_name customerManager,
	        boe.branch_name branchName,
            boe.area_name areaName,
            boe.zone_name zoneName,
            boe.zone_code zoneCode,
	        bo.recommend_store_name recommendStoreName,
	        bo.store_name storeName,
	        bop.product_id productId,
	        CONCAT( bop.goods_name, bop.spec_values ) productName,
	        bop.product_num buyNum,
            bop.sku_material_code skuMaterialCode,
            bop.sku_material_name skuMaterialName,
	        bop.money_amount productAmount,
	        bor.return_num returnNum,
	        (bor.actual_return_money_amount + bor.return_express_amount) productActualReturnAmount,
	        bor.return_money_amount productReturnAmount,
            bor.return_express_amount returnExpressAmount,
	        bor.other_compensation_amount otherCompensationAmount,
	        bor.customer_assume_amount customerAssumeAmount,
	        bor.refund_apply_sum_amount refundApplySumAmount,
	        bor.interest_payer interestPayer,
            bor.refund_punish_amount refundPunishAmount,
	        bor.state state,
	        bor.apply_time applyTime,
	        bor.return_type returnType,
	        bor.refund_type refundType,
	        bor.refund_end_time refundEndTime,
            bor.xz_card_amount,
	        IFNULL( boe.customer_id, bo.user_no ) refundAccount,
            case when bor.reversal_date is null then '否' else '是' end isReverse,
            bor.reversal_date reverseTime,
	        bo.create_time orderCreateTime,
	        bo.pay_time orderPayTime,
	        bo.finish_time orderFinishTime,
	        bo.order_pattern orderPattern,
	        boe.deliver_place deliverPlace,
	        boe.estimate_express_fee estimateExpressFee,
	        boe.delivery_requirements deliveryRequirements,
            bo.deliver_time deliverTime,
	        bor.store_id storeId,
            boas.product_delivery_state productDeliveryState,
            boas.platform_audit_time as platformAuditTime,
            bop.channel_new_sku_id
        FROM
	        bz_order_return bor
	    LEFT JOIN bz_order bo ON bor.order_sn = bo.order_sn
	    LEFT JOIN bz_order_after_service boas ON boas.afs_sn = bor.afs_sn
	    LEFT JOIN bz_order_product bop ON boas.order_product_id = bop.order_product_id
	    LEFT JOIN bz_order_extend boe ON bo.order_sn = boe.order_sn
        <include refid="refundExportWhereCondition"/>
        <include refid="limit" />
    </select>


    <select id="orderRefundExport" resultType="com.cfpamf.ms.mallorder.vo.exportvo.OrderRefundExportVO"
            parameterType="com.cfpamf.ms.mallorder.req.exportreq.OrderRefundExportRequest">
        SELECT
        bor.afs_sn refundNum,
        bor.order_sn orderSn,
        bo.payment_name payMethod,
        bor.create_time refundStartTime,
        boe.customer_id customerNo,
        boe.customer_name customerName,
        bo.member_name memberName,
        boe.manager_name customerManager,
        boe.branch_name branchName,
        boe.area_name areaName,
        boe.zone_name zoneName,
        boe.zone_code zoneCode,
        bo.recommend_store_name recommendStoreName,
        bo.store_name storeName,
        bop.product_id productId,
        CONCAT( bop.goods_name, bop.spec_values ) productName,
        bop.product_num buyNum,
        bop.sku_material_code skuMaterialCode,
        bop.sku_material_name skuMaterialName,
        bop.money_amount productAmount,
        bor.return_num returnNum,
        (bor.actual_return_money_amount + bor.return_express_amount) productActualReturnAmount,
        bor.return_money_amount productReturnAmount,
        bor.return_express_amount returnExpressAmount,
        bor.other_compensation_amount otherCompensationAmount,
        bor.customer_assume_amount customerAssumeAmount,
        bor.refund_apply_sum_amount refundApplySumAmount,
        bor.interest_payer interestPayer,
        bor.refund_punish_amount refundPunishAmount,
        bor.state state,
        bor.apply_time applyTime,
        bor.return_type returnType,
        bor.refund_type refundType,
        bor.refund_end_time refundEndTime,
        bor.xz_card_amount,
        IFNULL( boe.customer_id, bo.user_no ) refundAccount,
        case when bor.reversal_date is null then '否' else '是' end isReverse,
        bor.reversal_date reverseTime,
        bo.create_time orderCreateTime,
        bo.pay_time orderPayTime,
        bo.finish_time orderFinishTime,
        bo.order_pattern orderPattern,
        boe.deliver_place deliverPlace,
        boe.estimate_express_fee estimateExpressFee,
        boe.delivery_requirements deliveryRequirements,
        bor.store_id storeId
        FROM
        bz_order_return bor
        LEFT JOIN bz_order bo ON bor.order_sn = bo.order_sn
        LEFT JOIN bz_order_after_service boas ON boas.afs_sn = bor.afs_sn
        LEFT JOIN bz_order_product bop ON boas.order_product_id = bop.order_product_id
        LEFT JOIN bz_order_extend boe ON bo.order_sn = boe.order_sn
        <include refid="refundExportWhereCondition" />
    </select>

    <select id="orderRefundExportCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM
        bz_order_return bor
        LEFT JOIN bz_order bo ON bor.order_sn = bo.order_sn
        LEFT JOIN bz_order_after_service boas ON boas.afs_sn = bor.afs_sn
        LEFT JOIN bz_order_product bop ON boas.order_product_id = bop.order_product_id
        LEFT JOIN bz_order_extend boe ON bo.order_sn = boe.order_sn
        <include refid="refundExportWhereCondition" />
    </select>

    <sql id="refundExportWhereCondition">
        <where>
            bor.enabled_flag = 1
            <if test="exportRequest != null">
                <if test="exportRequest.storeId != null and exportRequest.storeId != ''">
                    AND bor.store_id = #{exportRequest.storeId}
                </if>
                <if test="exportRequest.orderSn != null and exportRequest.orderSn != ''">
                    AND bor.order_sn = #{exportRequest.orderSn}
                </if>
                <if test="exportRequest.afsSn != null and exportRequest.afsSn != ''">
                    AND bor.afs_sn = #{exportRequest.afsSn}
                </if>
                <if test="exportRequest.memberName != null and exportRequest.memberName != ''">
                    AND boas.member_name = #{exportRequest.memberName}
                </if>
                <if test="exportRequest.refundType != null and exportRequest.refundType != ''">
                    AND bor.refund_type = #{exportRequest.refundType}
                </if>
                <if test="exportRequest.returnType != null and exportRequest.returnType != ''">
                    AND bor.return_type = #{exportRequest.returnType}
                </if>
                <if test="exportRequest.startTime != null">
                    AND bor.apply_time <![CDATA[ >= ]]> #{exportRequest.startTime}
                </if>
                <if test="exportRequest.endTime != null">
                    AND bor.apply_time <![CDATA[ <= ]]> #{exportRequest.endTime}
                </if>
                <if test="exportRequest.refundStartTime != null">
                    AND bor.refund_end_time <![CDATA[ >= ]]> #{exportRequest.refundStartTime}
                </if>
                <if test="exportRequest.refundEndTime != null">
                    AND bor.refund_end_time <![CDATA[ <= ]]> #{exportRequest.refundEndTime}
                </if>
                <if test="exportRequest.platformAuditStartTime != null">
                    AND boas.platform_audit_time <![CDATA[ >= ]]> #{exportRequest.platformAuditStartTime}
                </if>
                <if test="exportRequest.platformAuditEndTime != null">
                    AND boas.platform_audit_time <![CDATA[ <= ]]> #{exportRequest.platformAuditEndTime}
                </if>
                <if test="exportRequest.state != null and exportRequest.state != ''">
                    AND bor.state = #{exportRequest.state}
                </if>
                <if test="exportRequest.stateIn != null">
                    AND bor.state in (${exportRequest.stateIn})
                </if>
                <if test="exportRequest.customerId != null and exportRequest.customerId != ''">
                    AND (boe.customer_id = #{exportRequest.customerId} OR bo.user_no = #{exportRequest.customerId})
                </if>
                <if test="exportRequest.customerName != null and exportRequest.customerName != ''">
                    AND boe.customer_name = #{exportRequest.customerName}
                </if>
                <if test="exportRequest.goodsName != null and exportRequest.goodsName != ''">
                    AND bop.goods_name like concat('%',#{exportRequest.goodsName},'%')
                </if>
                <if test="exportRequest.storeName != null and exportRequest.storeName != ''">
                    AND bo.store_name like concat('%',#{exportRequest.storeName},'%')
                </if>
                <if test="exportRequest.recommendStoreId != null and exportRequest.recommendStoreId != ''">
                    AND bo.recommend_store_id = #{exportRequest.recommendStoreId}
                </if>
                <if test="exportRequest.orderPattern != null and exportRequest.orderPattern != ''">
                    AND bo.order_pattern = #{exportRequest.orderPattern}
                </if>
                <if test="exportRequest.orderType != null and exportRequest.orderType != ''">
                    AND bo.order_type = #{exportRequest.orderType}
                </if>
                <if test="exportRequest.manager != null">
                    AND boe.manager_name = #{exportRequest.manager}
                </if>
                <if test="exportRequest.managerName != null">
                    AND boe.manager_name like concat('%',#{exportRequest.managerName},'%')
                </if>
                <if test="exportRequest.managerNameList != null and exportRequest.managerNameList.size() > 0">
                    AND EXISTS (select 1 from bz_order_performance_belongs bopb where bopb.order_sn = bo.order_sn and bopb.belonger_name in
                    <foreach collection="exportRequest.managerNameList" item="managerName" open="(" separator="," close=")">
                        #{managerName}
                    </foreach>
                    )
                </if>
                <if test="exportRequest.branchCodeList != null and exportRequest.branchCodeList.size() > 0">
                    AND EXISTS (select 1 from bz_order_performance_belongs bopb where bopb.order_sn = bo.order_sn and bopb.employee_branch_code in
                    <foreach collection="exportRequest.branchCodeList" item="branchCode" open="(" separator="," close=")">
                        #{branchCode}
                    </foreach>
                    )
                </if>
                <if test="exportRequest.productDeliveryState != null">
                    AND boas.product_delivery_state = #{exportRequest.productDeliveryState}
                </if>
            </if>
        </where>
    </sql>

    <select id="getAfsCountInPlatformAuditOvertime" resultType="java.lang.Integer">
        SELECT
	        count(1)
        FROM
	        bz_order_after_service boas
	    LEFT JOIN bz_order_return bor ON boas.afs_sn = bor.afs_sn
        WHERE
	        TO_DAYS(NOW()) - TO_DAYS( boas.store_audit_time ) <![CDATA[ >= ]]> #{overtime}
	    AND bor.state IN (200,203)
    </select>

    <select id="getAfsCountInStoreAuditOvertime" resultType="java.lang.Integer">
        SELECT
	        count(1)
        FROM
	        bz_order_after_service boas LEFT JOIN bz_order_return bor ON boas.afs_sn = bor.afs_sn
	        LEFT JOIN bz_order bo ON bor.order_sn = bo.order_sn
        WHERE
	        TO_DAYS(NOW()) - TO_DAYS( boas.buyer_apply_time ) <![CDATA[ >= ]]> #{overtime}
        <if test="storeId != null and storeId != ''">
        AND boas.store_id = #{storeId}
        </if>
        <if test="recommendStoreId != null and recommendStoreId != ''">
        AND bo.recommend_store_id = #{recommendStoreId}
        </if>
        AND bor.state IN (100,101,102)
    </select>

    <select id="getProduct" resultType="com.cfpamf.ms.mallorder.po.OrderProductPO">
        select odp.* from bz_order_product odp, bz_order_after_service afs
        where odp.order_product_id = afs.order_product_id
          and afs.afs_sn = #{afsSn};
    </select>

    <select id="getProductList" resultType="java.util.Map">
        select odp.order_sn orderSn,odp.goods_name goodsName,odp.product_id productId,afs.afs_sn afsSn
        from bz_order_product odp, bz_order_after_service afs
        where odp.order_product_id = afs.order_product_id
          and afs.afs_sn in
        <foreach collection="afsSnList" separator="," item="afsSn" open="(" close=")" >
            #{afsSn}
        </foreach>
    </select>

    <select id="getPlatformAutoAuditOrder" resultType="com.cfpamf.ms.mallorder.po.OrderReturnPO">
        select bor.*
        from bz_order_return bor,
             bz_order bo
        where bor.order_sn = bo.order_sn
          and bor.state in (200, 203)
          and bor.refund_type in (5, 6, 9, 11)
          and not locate('4', performance_modes)
    </select>

    <select id="getSumAmountByProductId" resultType="java.util.Map">
        select ifnull(sum(oretn.return_money_amount), 0)        returnMoneyAmount,
               ifnull(sum(oretn.actual_return_money_amount), 0) actualReturnMoneyAmount,
               ifnull(sum(oretn.return_integral_amount), 0)     returnIntegralAmount,
               ifnull(sum(oretn.service_fee), 0)                serviceFee,
               ifnull(sum(oretn.thirdpartnar_fee), 0)           thirdpartnarFee,
               ifnull(sum(oretn.order_commission), 0)           orderCommission,
               ifnull(sum(oretn.xz_card_amount), 0)             xzCardAmount,
               ifnull(sum(oretn.business_commission), 0)        businessCommission,
               ifnull(sum(oretn.commission_amount), 0)          commissionAmount,
               ifnull(sum(oretn.platform_voucher_amount), 0)    platformVoucherAmount,
               ifnull(sum(oretn.platform_voucher_amount_plt), 0)      platformVoucherAmountPlt,
               ifnull(sum(oretn.platform_voucher_amount_store), 0)      platformVoucherAmountStore,
               ifnull(sum(oretn.platform_voucher_retail_amount_plt), 0)      platformVoucherRetailAmountPlt,
               ifnull(sum(oretn.platform_voucher_retail_amount_store), 0)    platformVoucherRetailAmountStore,
               ifnull(sum(oretn.platform_voucher_amount_store), 0)    platformVoucherAmountStore,
               ifnull(sum(oretn.platform_activity_amount), 0)   platformActivityAmount,
               ifnull(sum(oretn.store_activity_amount), 0)      storeActivityAmount,
               ifnull(sum(oretn.store_voucher_amount), 0)       storeVoucherAmount
        from bz_order_after_service oafs,
             bz_order_return oretn
        where oafs.afs_sn = oretn.afs_sn
          and oafs.order_product_id = #{orderProductId}
          and oretn.state = 400;
    </select>

    <select id="listOrderReturnByProductId" resultType="com.cfpamf.ms.mallorder.vo.OrderReturnVOV2">

        select oafs.order_product_id,
               oafs.afs_sn,
               oafs.store_id,
               oafs.store_name,
               oafs.order_sn,
               oafs.member_id,
               oafs.member_name,
               oafs.goods_id,
               oretn.return_id,
               oretn.state,
               oretn.payment_method,
               oretn.channel_service_fee,
               oretn.return_money_type,
               oretn.return_type,
               oretn.refund_type,
               oretn.return_num,
               oretn.refund_apply_sum_amount,
               oretn.return_money_amount,
               oretn.customer_assume_amount,
               oretn.other_compensation_amount,
               oretn.plan_discount_amount,
               oretn.actual_return_money_amount,
               oretn.return_integral_amount,
               oretn.deduct_integral_amount,
               oretn.return_express_amount,
               oretn.commission_rate,
               oretn.commission_amount,
               oretn.platform_voucher_amount,
               oretn.platform_activity_amount,
               oretn.store_activity_amount,
               oretn.store_voucher_amount,
               oretn.xz_card_express_fee_amount,
               oretn.xz_card_amount,
               oretn.service_fee,
               oretn.thirdpartnar_fee,
               oretn.order_commission,
               oretn.business_commission,
               oretn.refund_punish_amount,
               oretn.return_voucher_code,
               oretn.store_activity_amount,
               oretn.store_voucher_amount
        from bz_order_after_service oafs,
             bz_order_return oretn
        where oafs.afs_sn = oretn.afs_sn
          and oafs.order_product_id = #{orderProductId};
    </select>

    <update id="revokeRefundUpdate">
        UPDATE bz_order_return
        SET state = 302
        WHERE
	        afs_sn = #{afsSn}
	        AND enabled_flag = 1
           AND (((return_type = 1  or return_type = 3 ) AND state IN (100,200)) OR (return_type = 2 AND state IN (101,201,102,103)))
    </update>

    <select id="getBappOrderReturnList" resultType="com.cfpamf.ms.mallorder.vo.OrderReturnVOV2">
        SELECT
        bor.return_id returnId,
        bop.goods_name goodsName,
        bop.goods_id goodsId,
        bop.order_product_id orderProductId,
        bop.product_id productId,
        bop.is_gift,
        bop.gift_group giftGroup,
        bop.return_number productReturnNum,
        bop.product_image productImage,
        bop.spec_values specValues,
        bop.delivery_state deliveryState,
        bo.order_sn orderSn,
        bo.order_type orderType,
        bor.afs_sn afsSn,
        bor.return_num returnNum,
        bor.actual_return_money_amount + bor.return_express_amount returnMoneyAmount,
        bor.return_express_amount returnExpressAmount,
        bor.xz_card_express_fee_amount,
        bor.xz_card_amount,
        bor.store_id storeId,
        bor.store_name storeName,
        bor.member_id memberId,
        bor.member_name memberName,
        bor.return_type returnType,
        bor.state state,
        bor.apply_time applyTime,
        bo.order_state orderState,
        bo.pay_amount,
        bor.refund_type refundType,
        bor.refund_start_time refundStartTime,
        bor.refund_end_time refundEndTime,
        boe.customer_id customerId,
        boe.customer_name customerName,
        boe.manager_name managerName,
        boe.branch_name branchName,
        bo.user_no,
        bo.recommend_store_name recommendStoreName,
        bo.order_pattern orderPattern,
        bo.user_mobile userMobile,
        boas.contact_name contactName,
        boas.apply_reason_content applyReasonContent,
        boas.performance_mode performanceMode,
        boas.platform_audit_time platformAuditTime,
        boas.gift_return_order_product_id giftReturnOrderProductId,
        bor.jd_intercept_status jdInterceptStatus
        FROM `bz_order_return` bor
        LEFT JOIN `bz_order` bo ON bor.order_sn = bo.order_sn
        LEFT JOIN `bz_order_after_service` boas ON bor.afs_sn = boas.afs_sn
        LEFT JOIN `bz_order_product` bop ON boas.order_product_id = bop.order_product_id
        LEFT JOIN `bz_order_extend` boe ON boe.order_sn = bor.order_sn
        where bor.enabled_flag = 1
        <if test="request.branchList != null and request.branchList.size() >0">
            and boe.branch in
            <foreach collection="request.branchList" item="branch" index="index" open="(" close=")" separator=",">
                #{branch}
            </foreach>
        </if>
        <if test="request.userList != null and request.userList.size() >0">
            and boe.manager in
            <foreach collection="request.userList" item="manager" index="index" open="(" close=")" separator=",">
                #{manager}
            </foreach>
        </if>
        order by bor.apply_time desc
        limit #{request.startRow},#{request.pageSize}
    </select>

    <select id="getBappOrderReturnCount" resultType="java.lang.Integer">
        SELECT
        count(*)
        FROM `bz_order_return` bor
        LEFT JOIN `bz_order` bo ON bor.order_sn = bo.order_sn
        LEFT JOIN `bz_order_after_service` boas ON bor.afs_sn = boas.afs_sn
        LEFT JOIN `bz_order_product` bop ON boas.order_product_id = bop.order_product_id
        LEFT JOIN `bz_order_extend` boe ON boe.order_sn = bor.order_sn
        where bor.enabled_flag = 1
        <if test="request.branchList != null and request.branchList.size() >0">
            and boe.branch in
            <foreach collection="request.branchList" item="branch" index="index" open="(" close=")" separator=",">
                #{branch}
            </foreach>
        </if>
        <if test="request.userList != null and request.userList.size() >0">
            and boe.manager in
            <foreach collection="request.userList" item="manager" index="index" open="(" close=")" separator=",">
                #{manager}
            </foreach>
        </if>
    </select>
</mapper>
