<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.OrderAmountStateRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.mallorder.po.OrderAmountStateRecordPO">
        <result column="id" property="id" />
        <result column="update_time" property="updateTime" />
        <result column="create_time" property="createTime" />
        <result column="enabled_flag" property="enabledFlag" />
        <result column="order_sn" property="orderSn" />
        <result column="amount_type" property="amountType" />
        <result column="amount" property="amount" />
        <result column="record_state" property="recordState" />
        <result column="effect_time" property="effectTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        update_time,
        create_time,
        enabled_flag,
        order_sn, amount_type, amount, record_state, effect_time, create_by, update_by
    </sql>

</mapper>
