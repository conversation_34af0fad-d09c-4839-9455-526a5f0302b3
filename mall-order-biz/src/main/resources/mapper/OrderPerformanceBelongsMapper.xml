<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.OrderPerformanceBelongsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.mallorder.po.OrderPerformanceBelongsPO">
        <result column="id" property="id" />
        <result column="update_time" property="updateTime" />
        <result column="create_time" property="createTime" />
        <result column="enabled_flag" property="enabledFlag" />
        <result column="order_sn" property="orderSn" />
        <result column="performance_type" property="performanceType" />
        <result column="performance_desc" property="performanceDesc" />
        <result column="belonger_name" property="belongerName" />
        <result column="belonger_employee_no" property="belongerEmployeeNo" />
        <result column="employee_branch_org_id" property="employeeBranchOrgId" />
        <result column="employee_branch_code" property="employeeBranchCode" />
        <result column="employee_branch_name" property="employeeBranchName" />
        <result column="bind_state_code" property="bindStateCode" />
        <result column="bind_state_desc" property="bindStateDesc" />
        <result column="effect_time" property="effectTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        update_time,
        create_time,
        enabled_flag,
        order_sn, performance_type, performance_desc, belonger_name, belonger_employee_no, employee_branch_org_id, employee_branch_code, employee_branch_name, bind_state_code, bind_state_desc, effect_time, create_by, update_by
    </sql>

</mapper>
