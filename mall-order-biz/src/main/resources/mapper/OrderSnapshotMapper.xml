<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.OrderSnapshotMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.mallorder.po.OrderSnapshotPO">
        <result column="snapshot_id" property="snapshotId"/>
        <result column="biz_sn" property="bizSn"/>
        <result column="sub_biz_sn" property="subBizSn"/>
        <result column="biz_type" property="bizType"/>
        <result column="biz_type_desc" property="bizTypeDesc"/>
        <result column="remark" property="remark" />
        <result column="snapshot" property="snapshot"/>
        <result column="order_snapshot" property="orderSnapshot" javaType="com.alibaba.fastjson.JSONObject"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="enabled_flag" property="enabledFlag"/>
    </resultMap>

    <sql id="whereCondition">
        where enabled_flag =1
        <if test="example != null">
            <if test="example.snapshotId != null">
                AND `snapshot_id` != #{example.snapshotId}
            </if>
            <if test="example.bizSn != null and example.bizSn != ''">
                AND `biz_sn` = #{example.bizSn}
            </if>
            <if test="example.subBizSn != null and example.subBizSn != ''">
                AND `sub_biz_sn` = #{example.subBizSn}
            </if>
            <if test="example.bizType != null and example.bizType != ''">
                AND `biz_type` = #{example.bizType}
            </if>
        </if>
    </sql>

    <select id="list" resultMap="BaseResultMap">
        SELECT
        *
        FROM `bz_order_snapshot`
        <include refid="whereCondition"/>
    </select>

</mapper>
