<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.OrderPayMapper">
    <resultMap id="resultMap" type="com.cfpamf.ms.mallorder.po.OrderPayPO">
        <id column="pay_id" property="payId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="enabled_flag" property="enabledFlag"/>
        <result column="pay_sn" property="paySn"/>
        <result column="order_sn" property="orderSn"/>
        <result column="pay_amount" property="payAmount"/>
        <result column="member_id" property="memberId"/>
        <result column="api_pay_state" property="apiPayState"/>
        <result column="callback_time" property="callbackTime"/>
        <result column="trade_sn" property="tradeSn"/>
        <result column="payment_name" property="paymentName"/>
        <result column="payment_code" property="paymentCode"/>
        <result column="loan_success" property="loanSuccess"/>
        <result column="out_biz_id" property="outBizId"/>
        <result column="out_biz_source" property="outBizSource"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="pay_way_extra_info" property="payWayExtraInfo" javaType="com.alibaba.fastjson.JSONObject"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
    </resultMap>
    <!--除主键外的所有字段，用于插入操作-->
    <sql id="columns">
        <trim suffixOverrides=",">
            <if test="paySn != null">
                `pay_sn`,
            </if>
            <if test="orderSn != null">
                `order_sn`,
            </if>
            <if test="payAmount != null">
                `pay_amount`,
            </if>
            <if test="memberId != null">
                `member_id`,
            </if>
            <if test="apiPayState != null">
                `api_pay_state`,
            </if>
            <if test="callbackTime != null">
                `callback_time`,
            </if>
            <if test="tradeSn != null">
                `trade_sn`,
            </if>
            <if test="paymentName != null">
                `payment_name`,
            </if>
            <if test="paymentCode != null">
                `payment_code`,
            </if>
            <if test="outBizId != null">
                `out_biz_id`,
            </if>
            <if test="outBizSource != null">
                `out_biz_source`,
            </if>
        </trim>
    </sql>
    <!--按照主键值进行操作-->
    <sql id="pkWhere">
    WHERE `pay_sn` = #{primaryKey} AND enabled_flag =1
  </sql>
    <!--操作条件-->
    <sql id="whereCondition">
        <if test="example != null">
            <trim prefix="WHERE" prefixOverrides="AND|OR">
                AND enabled_flag =1
                <if test="example.paySnNotEquals != null">
                    AND `pay_sn` != #{example.paySnNotEquals}
                </if>
                <if test="example.paySn != null">
                    AND `pay_sn` = #{example.paySn}
                </if>
                <if test="example.paySnIn != null">
                    AND `pay_sn` in (${example.paySnIn})
                </if>
                <if test="example.orderSn != null">
                    AND `order_sn` = #{example.orderSn}
                </if>
                <if test="example.orderSnLike != null">
                    AND `order_sn` like concat('%',#{example.orderSnLike},'%')
                </if>
                <if test="example.payAmount != null">
                    AND `pay_amount` = #{example.payAmount}
                </if>
                <if test="example.memberId != null">
                    AND `member_id` = #{example.memberId}
                </if>
                <if test="example.apiPayState != null">
                    AND `api_pay_state` = #{example.apiPayState}
                </if>
                <if test="example.callbackTimeAfter != null">
                    AND `callback_time` <![CDATA[ >= ]]> #{example.callbackTimeAfter}
                </if>
                <if test="example.callbackTimeBefore != null">
                    AND `callback_time` <![CDATA[ <= ]]> #{example.callbackTimeBefore}
                </if>
                <if test="example.tradeSn != null">
                    AND `trade_sn` = #{example.tradeSn}
                </if>
                <if test="example.tradeSnLike != null">
                    AND `trade_sn` like concat('%',#{example.tradeSnLike},'%')
                </if>
                <if test="example.paymentName != null">
                    AND `payment_name` = #{example.paymentName}
                </if>
                <if test="example.paymentNameLike != null">
                    AND `payment_name` like concat('%',#{example.paymentNameLike},'%')
                </if>
                <if test="example.paymentCode != null">
                    AND `payment_code` = #{example.paymentCode}
                </if>
                <if test="example.outBizId != null">
                    AND `out_biz_id` = #{example.outBizId}
                </if>
                <if test="example.outBizSource != null">
                    AND `out_biz_source` = #{example.outBizSource}
                </if>
            </trim>
        </if>
        <if test="example == null">
            where enabled_flag =1
        </if>
    </sql>
    <!--排序条件-->
    <sql id="orderBy">
    ORDER BY `pay_sn` DESC
  </sql>
    <sql id="orderByOther">
    order by ${example.orderBy}
  </sql>
    <!--分组条件-->
    <sql id="groupBy">
    group by ${example.groupBy}
  </sql>
    <!--分页条件-->
    <sql id="limit">
        <if test="size != null and size &gt; 0">
            limit #{startRow},#{size}
        </if>
    </sql>
    <!--查询符合条件的记录数-->
    <select id="countByExample" parameterType="com.cfpamf.ms.mallorder.request.OrderPayExample"
            resultType="java.lang.Integer">
        SELECT
        COUNT(*)
        FROM `bz_order_pay`
        <include refid="whereCondition"/>
    </select>
    <!--根据主键查询记录-->
    <select id="getByPrimaryKey" resultMap="resultMap">
        SELECT
        *
        FROM `bz_order_pay`
        <include refid="pkWhere"/>
    </select>
    <!--查询符合条件的记录(所有字段)-->
    <select id="listByExample" resultMap="resultMap">
        SELECT
        *
        FROM `bz_order_pay`
        <include refid="whereCondition"/>
        <if test="example.groupBy != null">
            <include refid="groupBy"/>
        </if>
        <choose>
            <when test="example.orderBy != null">
                <include refid="orderByOther"/>
            </when>
            <otherwise>
                <include refid="orderBy"/>
            </otherwise>
        </choose>
    </select>
    <!--分页查询符合条件的记录(所有字段)-->
    <select id="listPageByExample" resultMap="resultMap">
        SELECT
        *
        FROM `bz_order_pay`
        <include refid="whereCondition"/>
        <if test="example.groupBy != null">
            <include refid="groupBy"/>
        </if>
        <choose>
            <when test="example.orderBy != null">
                <include refid="orderByOther"/>
            </when>
            <otherwise>
                <include refid="orderBy"/>
            </otherwise>
        </choose>
        <include refid="limit"/>
    </select>
    <!--查询符合条件的记录(指定字段)-->
    <select id="listFieldsByExample" resultMap="resultMap">
        SELECT
        ${fields}
        FROM `bz_order_pay`
        <include refid="whereCondition"/>
        <if test="example.groupBy != null">
            <include refid="groupBy"/>
        </if>
        <choose>
            <when test="example.orderBy != null">
                <include refid="orderByOther"/>
            </when>
            <otherwise>
                <include refid="orderBy"/>
            </otherwise>
        </choose>
    </select>
    <!--分页查询符合条件的记录(指定字段)-->
    <select id="listFieldsPageByExample" resultMap="resultMap">
        SELECT
        ${fields}
        FROM `bz_order_pay`
        <include refid="whereCondition"/>
        <if test="example.groupBy != null">
            <include refid="groupBy"/>
        </if>
        <choose>
            <when test="example.orderBy != null">
                <include refid="orderByOther"/>
            </when>
            <otherwise>
                <include refid="orderBy"/>
            </otherwise>
        </choose>
        <include refid="limit"/>
    </select>
    <!--根据条件删除记录，可多条删除-->
    <update id="deleteByExample">
        update `bz_order_pay`SET enabled_flag =0
        <include refid="whereCondition"/>
    </update>
    <!--根据主键删除记录-->
    <update id="deleteByPrimaryKey">
        update `bz_order_pay`SET enabled_flag =0
        <include refid="pkWhere"/>
    </update>
    <!--插入一条记录-->
    <insert id="insert" keyColumn="pay_sn" keyProperty="paySn" parameterType="com.cfpamf.ms.mallorder.po.OrderPayPO"
            useGeneratedKeys="true">
        INSERT INTO `bz_order_pay`(
        <include refid="columns"/>
        )
        VALUES(
        <trim suffixOverrides=",">
            <if test="paySn != null">
                #{paySn},
            </if>
            <if test="orderSn != null">
                #{orderSn},
            </if>
            <if test="payAmount != null">
                #{payAmount},
            </if>
            <if test="memberId != null">
                #{memberId},
            </if>
            <if test="apiPayState != null">
                #{apiPayState},
            </if>
            <if test="callbackTime != null">
                #{callbackTime},
            </if>
            <if test="tradeSn != null">
                #{tradeSn},
            </if>
            <if test="paymentName != null">
                #{paymentName},
            </if>
            <if test="paymentCode != null">
                #{paymentCode},
            </if>
            <if test="outBizId != null">
                #{outBizId},
            </if>
            <if test="outBizSource != null">
                #{outBizSource},
            </if>
        </trim>
        )
    </insert>
    <!--按条件更新记录中不为空的字段-->
    <update id="updateByExampleSelective">
        UPDATE `bz_order_pay`
        <trim prefix="SET" suffixOverrides=",">
            <if test="record.orderSn != null">
                `order_sn` = #{record.orderSn},
            </if>
            <if test="record.payAmount != null">
                `pay_amount` = #{record.payAmount},
            </if>
            <if test="record.loanSuccess != null">
                `loan_success` = #{record.loanSuccess},
            </if>
            <if test="record.memberId != null">
                `member_id` = #{record.memberId},
            </if>
            <if test="record.apiPayState != null">
                `api_pay_state` = #{record.apiPayState},
            </if>
            <if test="record.callbackTime != null">
                `callback_time` = #{record.callbackTime},
            </if>
            <if test="record.tradeSn != null">
                `trade_sn` = #{record.tradeSn},
            </if>
            <if test="record.paymentName != null">
                `payment_name` = #{record.paymentName},
            </if>
            <if test="record.paymentCode != null">
                `payment_code` = #{record.paymentCode},
            </if>
            <if test="record.outBizId != null">
                `out_biz_id` = #{record.outBizId},
            </if>
            <if test="record.outBizSource != null">
                `out_biz_source` = #{record.outBizSource},
            </if>
            <if test="record.enjoyPayVipFlag != null">
                `enjoy_pay_vip_flag` = #{record.enjoyPayVipFlag},
            </if>
        </trim>
        <include refid="whereCondition"/>
    </update>
    <!--按照主键更新记录中不为空的字段-->
    <update id="updateByPrimaryKeySelective">
        UPDATE `bz_order_pay`
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderSn != null">
                `order_sn` = #{orderSn},
            </if>
            <if test="payAmount != null">
                `pay_amount` = #{payAmount},
            </if>
            <if test="loanSuccess != null">
                `loan_success` = #{loanSuccess},
            </if>
            <if test="memberId != null">
                `member_id` = #{memberId},
            </if>
            <if test="apiPayState != null">
                `api_pay_state` = #{apiPayState},
            </if>
            <if test="callbackTime != null">
                `callback_time` = #{callbackTime},
            </if>
            <if test="tradeSn != null">
                `trade_sn` = #{tradeSn},
            </if>
            <if test="paymentName != null">
                `payment_name` = #{paymentName},
            </if>
            <if test="paymentCode != null">
                `payment_code` = #{paymentCode},
            </if>
            <if test="outBizId != null">
                `out_biz_id` = #{outBizId},
            </if>
            <if test="outBizSource != null">
                `out_biz_source` = #{outBizSource},
            </if>
           <if test="enjoyPayVipFlag != null">
                `enjoy_pay_vip_flag` = #{enjoyPayVipFlag},
            </if>
        </trim>
        WHERE `pay_sn` = #{paySn}
    </update>


    <select id="listOrderAutoPay" resultType="com.cfpamf.ms.mallorder.dto.OrderAutoPayDTO">
        SELECT
                 bo.pay_sn AS paySn,
                 bo.store_id AS storeId,
                 bo.user_mobile AS  userMobile,
                 bo.parent_sn as parentSn,
                 bo.order_sn AS orderSn,
                 bo.user_no AS userNo,
                 bo.order_amount AS orderAmount,
                 boe.user_code AS userNumber
        FROM
            bz_order bo ,
            bz_order_extend boe
        WHERE   bo.order_sn = boe.order_sn
            AND bo.channel = 'OMS'
            and bo.order_state =10
            AND bo.create_time &lt; DATE_ADD(now(), INTERVAL -10 MINUTE);
    </select>

</mapper>
