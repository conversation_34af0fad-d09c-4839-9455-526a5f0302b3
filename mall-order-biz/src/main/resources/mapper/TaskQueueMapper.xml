<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.TaskQueueMapper">

    <delete id="modifyTaskQueue">
        delete from task_queue
        <where>
            biz_id = #{bizId}
            and biz_type = #{bizType}
            <if test="excludeId!=null ">
                and id != #{excludeId}
            </if>
        </where>
    </delete>

</mapper>
