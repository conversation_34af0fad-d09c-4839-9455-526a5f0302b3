<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.OrderMapper">
    <resultMap id="resultMap" type="com.cfpamf.ms.mallorder.po.OrderPO">
        <id column="order_id" property="orderId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="enabled_flag" property="enabledFlag"/>
        <result column="order_sn" property="orderSn"/>
        <result column="order_type" property="orderType"/>
        <result column="pay_sn" property="paySn"/>
        <result column="parent_sn" property="parentSn"/>
        <result column="order_state" property="orderState"/>
        <result column="member_id" property="memberId"/>
        <result column="member_name" property="memberName"/>
        <result column="user_no" property="userNo"/>
        <result column="user_mobile" property="userMobile"/>
        <result column="area_code" property="areaCode"/>
        <result column="store_id" property="storeId"/>
        <result column="store_name" property="storeName"/>
        <result column="recommend_store_id" property="recommendStoreId"/>
        <result column="pay_time" property="payTime"/>
        <result column="finish_time" property="finishTime"/>
        <result column="loan_pay_state" property="loanPayState"/>
        <result column="lending_success_time" property="lendingSuccessTime"/>
        <result column="payment_name" property="paymentName"/>
        <result column="payment_code" property="paymentCode"/>
        <result column="goods_amount" property="goodsAmount"/>
        <result column="express_fee" property="expressFee"/>
        <result column="activity_discount_amount" property="activityDiscountAmount"/>
        <result column="activity_discount_detail" property="activityDiscountDetail"/>
        <result column="store_voucher_amount" property="storeVoucherAmount"/>
        <result column="store_activity_amount" property="storeActivityAmount"/>
        <result column="platform_voucher_amount" property="platformVoucherAmount"/>
        <result column="platform_activity_amount" property="platformActivityAmount"/>
        <result column="xz_card_amount" property="xzCardAmount"/>
        <result column="order_amount" property="orderAmount"/>
        <result column="xz_card_express_fee_amount" property="xzCardExpressFeeAmount"/>
        <result column="compose_pay_name" property="composePayName"/>
        <result column="balance_amount" property="balanceAmount"/>
        <result column="pay_amount" property="payAmount"/>
        <result column="refund_amount" property="refundAmount"/>
        <result column="integral_cash_amount" property="integralCashAmount"/>
        <result column="integral" property="integral"/>
        <result column="delay_days" property="delayDays"/>
        <result column="delay_times" property="delayTimes"/>
        <result column="evaluate_state" property="evaluateState"/>
        <result column="lock_state" property="lockState"/>
        <result column="service_fee" property="serviceFee"/>
        <result column="service_fee_rate" property="serviceFeeRate"/>
        <result column="thirdpartnar_fee" property="thirdpartnarFee"/>
        <result column="thirdpartnar_fee_rate" property="thirdpartnarFeeRate"/>
        <result column="order_commission" property="orderCommission"/>
        <result column="business_commission" property="businessCommission"/>
        <result column="settlement_price" property="settlementPrice"/>
        <result column="settle_mode" property="settleMode"/>
        <result column="is_settlement" property="isSettlement"/>
        <result column="channel" property="channel"/>
        <result column="is_delivery" property="isDelivery"/>
        <result column="refuse_reason" property="refuseReason"/>
        <result column="refuse_remark" property="refuseRemark"/>
        <result column="delete_state" property="deleteState"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="finance_rule_code" property="financeRuleCode"/>
        <result column="rule_tag" property="ruleTag"/>
        <result column="new_order" property="newOrder"/>
        <result column="pay_update_time" property="payUpdateTime"/>
        <result column="performance_modes" property="performanceModes"/>
        <result column="exchange_flag" property="exchangeFlag"/>

    </resultMap>
    <!--除主键外的所有字段，用于插入操作-->
    <sql id="columns">
        <trim suffixOverrides=",">
            <if test="orderSn != null">
                `order_sn`,
            </if>
            <if test="paySn != null">
                `pay_sn`,
            </if>
            <if test="parentSn != null">
                `parent_sn`,
            </if>
            <if test="storeId != null">
                `store_id`,
            </if>
            <if test="storeName != null">
                `store_name`,
            </if>
            <if test="memberName != null">
                `member_name`,
            </if>
            <if test="memberId != null">
                `member_id`,
            </if>
            <if test="payTime != null">
                `pay_time`,
            </if>
            <if test="payUpdateTime != null">
                `pay_update_time`,
            </if>
            <if test="createTime != null">
                `create_time`,
            </if>
            <if test="finishTime != null">
                `finish_time`,
            </if>
            <if test="orderState != null">
                `order_state`,
            </if>
            <if test="loanPayState != null">
                `loan_pay_state`,
            </if>
            <if test="lendingSuccessTime != null">
                `lending_success_time`,
            </if>
            <if test="paymentName != null">
                `payment_name`,
            </if>
            <if test="paymentCode != null">
                `payment_code`,
            </if>
            <if test="goodsAmount != null">
                `goods_amount`,
            </if>
            <if test="expressFee != null">
                `express_fee`,
            </if>
            <if test="activityDiscountAmount != null">
                `activity_discount_amount`,
            </if>
            <if test="activityDiscountDetail != null">
                `activity_discount_detail`,
            </if>
            <if test="orderAmount != null">
                `order_amount`,
            </if>
            <if test="xzCardExpressFeeAmount != null">
                `xz_card_express_fee_amount`,
            </if>
            <if test="composePayName != null">
                `compose_pay_name`,
            </if>
            <if test="xzCardAmount != null">
                `xz_card_amount`,
            </if>
            <if test="balanceAmount != null">
                `balance_amount`,
            </if>
            <if test="payAmount != null">
                `pay_amount`,
            </if>
            <if test="refundAmount != null">
                `refund_amount`,
            </if>
            <if test="integralCashAmount != null">
                `integral_cash_amount`,
            </if>
            <if test="integral != null">
                `integral`,
            </if>
            <if test="delayDays != null">
                `delay_days`,
            </if>
            <if test="evaluateState != null">
                `evaluate_state`,
            </if>
            <if test="orderType != null">
                `order_type`,
            </if>
            <if test="lockState != null">
                `lock_state`,
            </if>
            <if test="deleteState != null">
                `delete_state`,
            </if>
            <if test="refuseReason != null">
                `refuse_reason`,
            </if>
            <if test="refuseRemark != null">
                `refuse_remark`,
            </if>
            <!--      <if test="isGenerateFacesheet != null">-->
            <!--        `is_generate_facesheet`,-->
            <!--      </if>-->
            <if test="serviceFee != null">
                `service_fee`,
            </if>
            <if test="serviceFeeRate != null">
                `service_fee_rate`,
            </if>
            <if test="thirdpartnarFee != null">
                `thirdpartnar_fee`,
            </if>
            <if test="thirdpartnarFeeRate != null">
                `thirdpartnar_fee_rate`,
            </if>
            <if test="orderCommission != null">
                `order_commission`,
            </if>
            <if test="businessCommission != null">
                `business_commission`,
            </if>
            <if test="settlementPrice != null">
                `settlement_price`,
            </if>
            <if test="settleMode != null">
                `settle_mode`,
            </if>
            <if test="isSettlement != null">
                `is_settlement`,
            </if>
            <if test="areaCode != null">
                `area_code`,
            </if>
            <if test="financeRuleCode != null">
                `finance_rule_code`,
            </if>
            <if test="ruleTag != null">
                `rule_tag`,
            </if>
            <if test="newOrder != null">
                `new_order`,
            </if>
        </trim>
    </sql>
    <!--按照主键值进行操作-->
    <sql id="pkWhere">
        WHERE `order_id` =
        #{primaryKey}
        AND
        enabled_flag
        =
        1
    </sql>
    <!--操作条件-->
    <sql id="whereCondition">
        <if test="example != null">
            <trim prefix="WHERE" prefixOverrides="AND|OR">
                AND enabled_flag =1
                <if test="example.orderIdNotEquals != null">
                    AND `order_id` != #{example.orderIdNotEquals}
                </if>
                <if test="example.orderIdIn != null">
                    AND `order_id` in (${example.orderIdIn})
                </if>
                <if test="example.orderSnList != null and example.orderSnList.size > 0">
                    AND `order_sn` in
                    <foreach collection="example.orderSnList" separator="," item="orderSn" open="(" close=")">
                        #{orderSn}
                    </foreach>
                </if>
                <if test="example.orderSn != null">
                    AND `order_sn` = #{example.orderSn}
                </if>
                <if test="example.userNo != null and example.userNo != ''">
                    AND user_no = #{example.userNo}
                </if>
                <if test="example.orderSnLike != null">
                    AND `order_sn` like concat('%',#{example.orderSnLike},'%')
                </if>
                <if test="example.paySn != null">
                    AND `pay_sn` = #{example.paySn}
                </if>
                <if test="example.paySnLike != null">
                    AND `pay_sn` like concat('%',#{example.paySnLike},'%')
                </if>
                <if test="example.parentSn != null">
                    AND `parent_sn` = #{example.parentSn}
                </if>
                <if test="example.parentSnLike != null">
                    AND `parent_sn` like concat('%',#{example.parentSnLike},'%')
                </if>
                <if test="example.storeId != null">
                    AND `store_id` = #{example.storeId}
                </if>
                <if test="example.storeName != null">
                    AND `store_name` = #{example.storeName}
                </if>
                <if test="example.storeNameLike != null">
                    AND `store_name` like concat('%',#{example.storeNameLike},'%')
                </if>
                <if test="example.memberName != null">
                    AND `member_name` = #{example.memberName}
                </if>
                <if test="example.memberNameLike != null">
                    AND `member_name` like concat('%',#{example.memberNameLike},'%')
                </if>
                <if test="example.memberId != null">
                    AND `member_id` = #{example.memberId}
                </if>
                <if test="example.payTimeAfter != null">
                    AND `pay_time` <![CDATA[ >= ]]> #{example.payTimeAfter}
                </if>
                <if test="example.payTimeBefore != null">
                    AND `pay_time` <![CDATA[ <= ]]> #{example.payTimeBefore}
                </if>
                <if test="example.payUpdateTimeAfter != null">
                    AND `pay_update_time` <![CDATA[ >= ]]> #{example.payUpdateTimeAfter}
                </if>
                <if test="example.payUpdateTimeBefore != null">
                    AND `pay_update_time` <![CDATA[ <= ]]> #{example.payUpdateTimeBefore}
                </if>
                <if test="example.createTimeAfter != null">
                    AND `create_time` <![CDATA[ >= ]]> #{example.createTimeAfter}
                </if>
                <if test="example.createTimeBefore != null">
                    AND `create_time` <![CDATA[ <= ]]> #{example.createTimeBefore}
                </if>
                <if test="example.finishTimeAfter != null">
                    AND `finish_time` <![CDATA[ >= ]]> #{example.finishTimeAfter}
                </if>
                <if test="example.finishTimeBefore != null">
                    AND `finish_time` <![CDATA[ <= ]]> #{example.finishTimeBefore}
                </if>
                <if test="example.orderState != null">
                    AND `order_state` = #{example.orderState}
                </if>
                <if test="example.loanPayState != null">
                    AND `loan_pay_state` = #{example.loanPayState}
                </if>
                <if test="example.lendingSuccessTime != null">
                    AND `lending_success_time` = #{example.lendingSuccessTime}
                </if>
                <if test="example.orderStateIn != null">
                    AND `order_state` in (${example.orderStateIn})
                </if>
                <if test="example.orderStateNotIn != null">
                    AND `order_state` not in (${example.orderStateNotIn})
                </if>
                <if test="example.orderStateList != null">
                    AND `order_state` in
                    <foreach collection="example.orderStateList" item="status" open="(" separator="," close=")">
                        #{status}
                    </foreach>
                </if>

                <if test="example.orderStateNotEquals != null">
                    AND `order_state` != #{example.orderStateNotEquals}
                </if>
                <if test="example.paymentName != null">
                    AND `payment_name` = #{example.paymentName}
                </if>
                <if test="example.paymentNameLike != null">
                    AND `payment_name` like concat('%',#{example.paymentNameLike},'%')
                </if>
                <if test="example.paymentCode != null and example.paymentCode != 'CARD'">
                    AND `payment_code` = #{example.paymentCode}
                </if>
                <if test="example.paymentCode != null and example.paymentCode == 'CARD'">
                    AND (`payment_code` = #{example.paymentCode} or `compose_pay_name` like '%乡助卡%')
                </if>
                <if test="example.goodsAmount != null">
                    AND `goods_amount` = #{example.goodsAmount}
                </if>
                <if test="example.expressFee != null">
                    AND `express_fee` = #{example.expressFee}
                </if>
                <if test="example.activityDiscountAmount != null">
                    AND `activity_discount_amount` = #{example.activityDiscountAmount}
                </if>
                <if test="example.activityDiscountDetail != null">
                    AND `activity_discount_detail` = #{example.activityDiscountDetail}
                </if>
                <if test="example.orderAmount != null">
                    AND `order_amount` = #{example.orderAmount}
                </if>
                <if test="example.xzCardAmount != null">
                    AND `xz_card_amount` = #{example.xzCardAmount}
                </if>
                <if test="example.xzCardExpressFeeAmount != null">
                    AND `xz_card_express_fee_amount` = #{example.xzCardExpressFeeAmount}
                </if>
                <if test="example.composePayName != null">
                    AND `compose_pay_name` = #{example.composePayName}
                </if>
                <if test="example.balanceAmount != null">
                    AND `balance_amount` = #{example.balanceAmount}
                </if>
                <if test="example.payAmount != null">
                    AND `pay_amount` = #{example.payAmount}
                </if>
                <if test="example.refundAmount != null">
                    AND `refund_amount` = #{example.refundAmount}
                </if>
                <if test="example.integralCashAmount != null">
                    AND `integral_cash_amount` = #{example.integralCashAmount}
                </if>
                <if test="example.integral != null">
                    AND `integral` = #{example.integral}
                </if>
                <if test="example.delayDays != null">
                    AND `delay_days` = #{example.delayDays}
                </if>
                <if test="example.evaluateState != null">
                    AND `evaluate_state` = #{example.evaluateState}
                </if>
                <if test="example.evaluateStateIn != null">
                    AND `evaluate_state` in (${example.evaluateStateIn})
                </if>
                <if test="example.evaluateStateNotIn != null">
                    AND `evaluate_state` not in (${example.evaluateStateNotIn})
                </if>
                <if test="example.evaluateStateNotEquals != null">
                    AND `evaluate_state` != #{example.evaluateStateNotEquals}
                </if>
                <if test="example.orderType != null">
                    AND `order_type` = #{example.orderType}
                </if>
                <if test="example.orderTypeIn != null">
                    AND `order_type` in (${example.orderTypeIn})
                </if>
                <if test="example.lockState != null">
                    AND `lock_state` = #{example.lockState}
                </if>
                <if test="example.lockStateIn != null">
                    AND `lock_state` in (${example.lockStateIn})
                </if>
                <if test="example.lockStateNotIn != null">
                    AND `lock_state` not in (${example.lockStateNotIn})
                </if>
                <if test="example.lockStateNotEquals != null">
                    AND `lock_state` != #{example.lockStateNotEquals}
                </if>
                <if test="example.deleteState != null">
                    AND `delete_state` = #{example.deleteState}
                </if>
                <if test="example.deleteStateIn != null">
                    AND `delete_state` in (${example.deleteStateIn})
                </if>
                <if test="example.deleteStateNotIn != null">
                    AND `delete_state` not in (${example.deleteStateNotIn})
                </if>
                <if test="example.deleteStateNotEquals != null">
                    AND `delete_state` != #{example.deleteStateNotEquals}
                </if>
                <if test="example.refuseReason != null">
                    AND `refuse_reason` = #{example.refuseReason}
                </if>
                <if test="example.refuseReasonLike != null">
                    AND `refuse_reason` like concat('%',#{example.refuseReasonLike},'%')
                </if>
                <if test="example.refuseRemark != null">
                    AND `refuse_remark` = #{example.refuseRemark}
                </if>
                <!--        <if test="example.isGenerateFacesheet != null">-->
                <!--          AND `is_generate_facesheet` = #{example.isGenerateFacesheet}-->
                <!--        </if>-->
                <if test="example.isSettlement != null">
                    AND `is_settlement` = #{example.isSettlement}
                </if>
                <if test="example.financeRuleCode != null">
                    AND `finance_rule_code` = #{example.financeRuleCode}
                </if>
                <if test="example.ruleTag != null">
                    AND `rule_tag` = #{example.ruleTag}
                </if>
                <if test="example.orderReturnState != null and example.orderReturnState == 1">
                    AND exists (select 1 from bz_order_return bor where bor.order_sn = bz_order.order_sn and bor.state
                    in (100, 101, 102, 200, 201, 203))
                </if>
                <if test="example.channel != null">
                    AND `channel` = #{example.channel}
                </if>
                <if test="example.orderPattern != null">
                    AND `order_pattern` = #{example.orderPattern}
                </if>
                <if test="example.orderPatternNotIn != null">
                    AND `order_pattern` not in (${example.orderPatternNotIn})
                </if>
                <if test="example.newOrder != null">
                    AND `new_order` = #{example.newOrder}
                </if>
                <if test="example.performanceMode != null">
                    AND find_in_set(#{example.performanceMode}, `performance_modes`)
                </if>
                <if test="example.exchangeFlag != null">
                    AND `exchange_flag` = #{example.exchangeFlag}
                </if>
                <if test="example.exchangeFlagNotEquals != null">
                    AND `exchange_flag` != #{example.exchangeFlagNotEquals}
                </if>
                <if test="example.afterSalesDeadlineAfter != null">
                    AND `after_sales_deadline` <![CDATA[ >= ]]> #{example.afterSalesDeadlineAfter}
                </if>
                <if test="example.afterSalesDeadlineBefore != null">
                    AND `after_sales_deadline` <![CDATA[ <= ]]> #{example.afterSalesDeadlineBefore}
                </if>
            </trim>
        </if>
        <if test="example == null">
            where enabled_flag =1
        </if>
    </sql>
    <!--排序条件-->
    <sql id="orderBy">
        ORDER BY `order_id` DESC
    </sql>
    <sql id="orderByOther">
        order by
        ${example.orderBy}
    </sql>
    <!--分组条件-->
    <sql id="groupBy">
        group by
        ${example.groupBy}
    </sql>
    <!--分页条件-->
    <sql id="limit">
        <if test="size != null and size &gt; 0">
            limit #{startRow},#{size}
        </if>
    </sql>
    <!--查询符合条件的记录数-->
    <select id="countByExample" parameterType="com.cfpamf.ms.mallorder.request.OrderExample"
            resultType="java.lang.Integer">
        SELECT
        COUNT(*)
        FROM `bz_order`
        <include refid="whereCondition"/>
    </select>
    <!--根据主键查询记录-->
    <select id="getByPrimaryKey" resultMap="resultMap">
        SELECT
        *
        FROM `bz_order`
        <include refid="pkWhere"/>
    </select>
    <!--查询符合条件的记录(所有字段)-->
    <select id="listByExample" resultType="com.cfpamf.ms.mallorder.po.OrderPO">
        SELECT
        *
        FROM `bz_order`
        <include refid="whereCondition"/>
        <if test="example.groupBy != null">
            <include refid="groupBy"/>
        </if>
        <choose>
            <when test="example.orderBy != null">
                <include refid="orderByOther"/>
            </when>
            <otherwise>
                <include refid="orderBy"/>
            </otherwise>
        </choose>
    </select>
    <!--分页查询符合条件的记录(所有字段)-->
    <select id="listPageByExample" resultMap="resultMap">
        SELECT
        *
        FROM `bz_order`
        <include refid="whereCondition"/>
        <if test="example.groupBy != null">
            <include refid="groupBy"/>
        </if>
        <choose>
            <when test="example.orderBy != null">
                <include refid="orderByOther"/>
            </when>
            <otherwise>
                <include refid="orderBy"/>
            </otherwise>
        </choose>
        <include refid="limit"/>
    </select>
    <!--查询符合条件的记录(指定字段)-->
    <select id="listFieldsByExample" resultMap="resultMap">
        SELECT
        ${fields}
        FROM `bz_order`
        <include refid="whereCondition"/>
        <if test="example.groupBy != null">
            <include refid="groupBy"/>
        </if>
        <choose>
            <when test="example.orderBy != null">
                <include refid="orderByOther"/>
            </when>
            <otherwise>
                <include refid="orderBy"/>
            </otherwise>
        </choose>
    </select>

    <select id="selectOrderListByPaySns" resultMap="resultMap">
        SELECT
        *
        FROM `bz_order`
        where
        enabled_flag = 1
        <if test="paySns != null and paySns.size() > 0">
            and
            pay_sn in
            <foreach collection="paySns" item="paySn" open="(" separator="," close=")">
                #{paySn}
            </foreach>
        </if>
    </select>

    <!--查询符合条件的分组记录数-->
    <select id="countGroupFieldsByExample" parameterType="com.cfpamf.ms.mallorder.request.OrderExample"
            resultType="java.lang.Integer">
        SELECT
        COUNT(DISTINCT ${fields})
        FROM `bz_order`
        <include refid="whereCondition"/>
    </select>


    <select id="getSaleTotalDayDto" resultType="com.cfpamf.ms.mallorder.dto.SaleTotalDayDTO">
        select
        left(create_time,10) as `day`,
        sum(order_amount) as `amount`
        from `bz_order`
        <include refid="whereCondition"/>
        group by `day`
        order by `day` desc
    </select>

    <select id="getOrderDayDto" resultType="com.cfpamf.ms.mallorder.dto.OrderDayDTO">
        select
        left(create_time,10) as orderDay,
        sum(goods_amount) as goodsAmount,
        sum(express_fee) as expressFee,
        sum(order_amount) as orderAmount,
        sum(balance_amount) as balanceAmount,
        sum(pay_amount) as payAmount,
        sum(refund_amount) as refundAmount,
        count(1) as count
        from `bz_order`
        <include refid="whereCondition"/>
        group by orderDay
        order by orderDay desc
    </select>


    <select id="getOrdersList4AutoFinish" resultMap="resultMap">
        SELECT o.* FROM bz_order o
        <where>
            <if test="example.orderState != null">
                and o.`order_state`= #{example.orderState}
            </if>
            <if test="example.loanPayState != null">
                AND o.`loan_pay_state` = #{example.loanPayState}
            </if>
            <if test="example.lendingSuccessTime != null">
                AND o.`lending_success_time` = #{example.lendingSuccessTime}
            </if>
            <if test="example.deliverTimeEnd != null">
                and o.`deliver_time` &lt;= #{example.deliverTimeEnd}
            </if>
        </where>
    </select>

    <select id="getOrderExportList" resultType="com.cfpamf.ms.mallorder.dto.OrderExportDTO">
        SELECT
        o.order_sn,
        o.pay_sn,
        o.bank_pay_trx_no,
        o.order_state,
        o.order_amount,
        o.express_fee,
        o.member_id,
        o.member_name,
        o.store_id,
        o.store_name,
        o.goods_amount + o.express_fee + o.xz_card_express_fee_amount AS orderNeedAmount,
        o.recommend_store_id,
        o.recommend_store_name,
        o.create_time,
        o.pay_time,
        o.deliver_time,
        o.payment_name,
        o.finish_time,
        o.compose_pay_name,
        p.xz_card_amount,
        o.xz_card_express_fee_amount,
        o.order_commission,
        o.business_commission,
        o.payment_tag,
        e.invoice_status,
        e.invoice_amount,
        e.invoice_time,
        e.order_remark,
        e.receiver_name,
        e.receiver_mobile,
        e.receiver_province_code,
        e.receiver_city_code,
        e.receiver_district_code,
        e.receiver_town_code,
        e.receiver_address,
        e.invoice_info,
        p.order_product_id,
        p.goods_id,
        p.distribute_parent,
        p.goods_name,
        p.goods_parameter,
        p.spec_values,
        p.product_num,
        p.weight,
        p.sku_material_code skuMaterialCode,
        p.sku_material_name skuMaterialName,
        p.product_num - p.return_number as validNum,
        p.cost,
        p.landing_price,
        p.tax_rate,
        p.product_show_price,
        p.product_show_price * p.product_num as productShouldPay,
        o.activity_discount_amount,
        p.activity_discount_amount AS goodsActivityDiscountAmount,
        p.money_amount,
        p.store_activity_amount + p.store_voucher_amount as storeDiscountAmount,
        p.platform_voucher_amount + p.platform_activity_amount as platformDiscountAmount,
        p.integral_cash_amount,
        p.ret_money_model,
        o.channel,
        o.order_type,
        e.customer_id,
        e.customer_name,
        e.category_code,
        e.category_name,
        e.branch,
        e.branch_name,
        e.area_code,
        e.area_name,
        e.zone_code,
        e.zone_name,
        e.manager,
        e.manager_name,
        e.user_code,
        p.product_id,
        p.spu_out_id,
        p.product_code,
        p.bar_code,
        p.supplier_name AS supplierName,
        case
        when p.return_number != 0 then '是'
        else
        '否'
        end returnState,
        p.status productStatus,
        l.express_id expressId,
        l.express_name expressName,
        l.express_company_code expressCode,
        l.express_number expressNumber,
        l.deliver_type deliverType,
        l.create_by deliverName,
        l.deliver_mobile deliverMobile,
        l.create_time productDeliverTime,
        p.delivery_state deliverState,
        case
        when (not exists (select 1 from bz_order_return bor where bor.order_sn = o.order_sn and bor.state in
        (100, 101, 102, 200, 201, 203))
        AND o.`is_delivery` = 1 and o.order_state in (20, 25) and p.delivery_state = 0) then '是'
        else '否'
        end deliverable,
        p.goods_category_path,
        bbp.loan_org_no payOrg,
        bop.trade_sn,
        o.order_pattern,
        e.deliver_place,
        e.estimate_express_fee,
        e.delivery_requirements,
        p.deposit,
        p.balance,
        e.delivery_requirements,
        p.performance_mode performanceMode,
        IFNULL(p.product_effective_price,0) productEffectivePrice,
        o.finance_rule_code,
        o.rule_tag,
        e.store_branch_name,
        e.settled_type,
        e.company_name,
        o.customer_confirm_status,
        o.customer_confirm_status_desc,
        p.batch_no batchNo,
        p.product_spec_json,
        booe.account_period_days,
        booe.overdue_time,
        booe.overdue_flag,
        pe.product_category_path,
        pe.product_category_path_name,
        booe.overdue_days,
        booe.buyer_supplier_code buyerSupplierCode,
        booe.buyer_supplier_name buyerSupplierName,
        p.channel_new_sku_id
        FROM bz_order o
        LEFT JOIN bz_order_extend e ON o.order_sn = e.order_sn
        LEFT JOIN bz_order_product p ON o.order_sn = p.order_sn
        left join bz_order_logistic l on p.logistic_id = l.logistic_id
        LEFT JOIN bz_order_pay bop ON bop.pay_sn = o.pay_sn
        LEFT JOIN bz_bank_pay bbp ON o.order_sn = bbp.order_sn
        left join bz_order_offline_extend booe on o.order_sn = booe.order_sn
        left join bz_order_product_erp_extend pe on o.order_sn = pe.order_sn and p.order_product_id =
        pe.order_product_id
        LEFT JOIN bz_order_performance_belongs pb ON o.order_sn = pb.order_sn
        <include refid="exportWhereCondition"/>
        <include refid="orderOfflineExtendWhereCondition"/>
        order by p.order_product_id
    </select>

    <!--操作条件-->
    <sql id="exportWhereCondition">
        <if test="example != null">
            <trim prefix="WHERE" prefixOverrides="AND|OR">
                AND o.order_sn = e.order_sn
                AND o.enabled_flag = 1
                AND o.order_sn = p.order_sn
                <if test="example.orderIdNotEquals != null">
                    AND o.`order_id` != #{example.orderIdNotEquals}
                </if>
                <if test="example.orderIdIn != null">
                    AND o.`order_id` in (${example.orderIdIn})
                </if>
                <if test="example.orderSn != null">
                    AND o.`order_sn` = #{example.orderSn}
                </if>
                <if test="example.orderSnLike != null">
                    AND o.`order_sn` like concat('%',#{example.orderSnLike},'%')
                </if>
                <if test="example.paySn != null">
                    AND o.`pay_sn` = #{example.paySn}
                </if>
                <if test="example.paySnLike != null">
                    AND o.`pay_sn` like concat('%',#{example.paySnLike},'%')
                </if>
                <if test="example.parentSn != null">
                    AND o.`parent_sn` = #{example.parentSn}
                </if>
                <if test="example.parentSnLike != null">
                    AND o.`parent_sn` like concat('%',#{example.parentSnLike},'%')
                </if>
                <if test="example.storeId != null">
                    AND o.`store_id` = #{example.storeId}
                </if>
                <if test="example.storeName != null">
                    AND o.`store_name` = #{example.storeName}
                </if>
                <if test="example.storeNameLike != null">
                    AND o.`store_name` like concat('%',#{example.storeNameLike},'%')
                </if>
                <if test="example.memberName != null">
                    AND o.`member_name` = #{example.memberName}
                </if>
                <if test="example.memberNameLike != null">
                    AND o.`member_name` like concat('%',#{example.memberNameLike},'%')
                </if>
                <if test="example.memberId != null">
                    AND o.`member_id` = #{example.memberId}
                </if>
                <if test="example.payTimeAfter != null">
                    AND o.`pay_time` <![CDATA[ >= ]]> #{example.payTimeAfter}
                </if>
                <if test="example.payTimeBefore != null">
                    AND o.`pay_time` <![CDATA[ <= ]]> #{example.payTimeBefore}
                </if>
                <if test="example.payUpdateTimeAfter != null">
                    AND o.`pay_update_time` <![CDATA[ >= ]]> #{example.payUpdateTimeAfter}
                </if>
                <if test="example.payUpdateTimeBefore != null">
                    AND o.`pay_update_time` <![CDATA[ <= ]]> #{example.payUpdateTimeBefore}
                </if>
                <if test="example.createTimeAfter != null">
                    AND o.`create_time` <![CDATA[ >= ]]> #{example.createTimeAfter}
                </if>
                <if test="example.createTimeBefore != null">
                    AND o.`create_time` <![CDATA[ <= ]]> #{example.createTimeBefore}
                </if>
                <if test="example.finishTimeAfter != null">
                    AND o.`finish_time` <![CDATA[ >= ]]> #{example.finishTimeAfter}
                </if>
                <if test="example.finishTimeBefore != null">
                    AND o.`finish_time` <![CDATA[ <= ]]> #{example.finishTimeBefore}
                </if>
                <if test="example.orderState != null">
                    AND o.`order_state` = #{example.orderState}
                </if>
                <if test="example.orderState != null and example.orderState == 20">
                    AND not exists (select 1 from bz_order_return bor where bor.order_sn = o.order_sn and bor.state in
                    (100, 101, 102, 200, 201, 203))
                    AND o.`is_delivery` = 1
                </if>
                <if test="example.orderStateIn != null">
                    AND o.`order_state` in (${example.orderStateIn})
                </if>
                <if test="example.orderStateIn != null and example.orderStateIn == '20,25'">
                    AND not exists (select 1 from bz_order_return bor where bor.order_sn = o.order_sn and bor.state in
                    (100, 101, 102, 200, 201, 203))
                    AND o.`is_delivery` = 1
                    and not exists (select bop.order_sn from bz_order_product bop where bop.order_sn = o.order_sn and
                    bop.delivery_state = 2)
                </if>
                <if test="example.orderStateNotIn != null">
                    AND o.`order_state` not in (${example.orderStateNotIn})
                </if>
                <if test="example.orderStateNotEquals != null">
                    AND o.`order_state` != #{example.orderStateNotEquals}
                </if>
                <if test="example.paymentName != null">
                    AND o.`payment_name` = #{example.paymentName}
                </if>
                <if test="example.paymentNameLike != null">
                    AND o.`payment_name` like concat('%',#{example.paymentNameLike},'%')
                </if>
                <if test="example.paymentCode != null and example.paymentCode != 'CARD'">
                    AND o.`payment_code` = #{example.paymentCode}
                </if>
                <if test="example.paymentCode != null and example.paymentCode == 'CARD'">
                    AND (o.`payment_code` = #{example.paymentCode} or o.`compose_pay_name` like '%乡助卡%')
                </if>
                <if test="example.goodsAmount != null">
                    AND o.`goods_amount` = #{example.goodsAmount}
                </if>
                <if test="example.expressFee != null">
                    AND o.`express_fee` = #{example.expressFee}
                </if>
                <if test="example.activityDiscountAmount != null">
                    AND o.`activity_discount_amount` = #{example.activityDiscountAmount}
                </if>
                <if test="example.activityDiscountDetail != null">
                    AND o.`activity_discount_detail` = #{example.activityDiscountDetail}
                </if>
                <if test="example.orderAmount != null">
                    AND o.`order_amount` = #{example.orderAmount}
                </if>
                <if test="example.xzCardAmount != null">
                    AND o.`xz_card_amount` = #{example.xzCardAmount}
                </if>
                <if test="example.xzCardExpressFeeAmount != null">
                    AND o.`xz_card_express_fee_amount` = #{example.xzCardExpressFeeAmount}
                </if>
                <if test="example.composePayName != null">
                    AND o.`compose_pay_name` = #{example.composePayName}
                </if>
                <if test="example.balanceAmount != null">
                    AND o.`balance_amount` = #{example.balanceAmount}
                </if>
                <if test="example.payAmount != null">
                    AND o.`pay_amount` = #{example.payAmount}
                </if>
                <if test="example.refundAmount != null">
                    AND o.`refund_amount` = #{example.refundAmount}
                </if>
                <if test="example.integralCashAmount != null">
                    AND o.`integral_cash_amount` = #{example.integralCashAmount}
                </if>
                <if test="example.integral != null">
                    AND o.`integral` = #{example.integral}
                </if>
                <if test="example.delayDays != null">
                    AND o.`delay_days` = #{example.delayDays}
                </if>
                <if test="example.evaluateState != null">
                    AND o.`evaluate_state` = #{example.evaluateState}
                </if>
                <if test="example.evaluateStateIn != null">
                    AND o.`evaluate_state` in (${example.evaluateStateIn})
                </if>
                <if test="example.evaluateStateNotIn != null">
                    AND o.`evaluate_state` not in (${example.evaluateStateNotIn})
                </if>
                <if test="example.evaluateStateNotEquals != null">
                    AND o.`evaluate_state` != #{example.evaluateStateNotEquals}
                </if>
                <if test="example.orderTypeIn != null">
                    AND `order_type` in (${example.orderTypeIn})
                </if>
                <if test="example.orderType != null">
                    AND o.`order_type` = #{example.orderType}
                </if>
                <if test="example.lockState != null">
                    AND o.`lock_state` = #{example.lockState}
                </if>
                <if test="example.lockStateIn != null">
                    AND o.`lock_state` in (${example.lockStateIn})
                </if>
                <if test="example.lockStateNotIn != null">
                    AND o.`lock_state` not in (${example.lockStateNotIn})
                </if>
                <if test="example.lockStateNotEquals != null">
                    AND o.`lock_state` != #{example.lockStateNotEquals}
                </if>
                <if test="example.deleteState != null">
                    AND o.`delete_state` = #{example.deleteState}
                </if>
                <if test="example.deleteStateIn != null">
                    AND o.`delete_state` in (${example.deleteStateIn})
                </if>
                <if test="example.deleteStateNotIn != null">
                    AND o.`delete_state` not in (${example.deleteStateNotIn})
                </if>
                <if test="example.deleteStateNotEquals != null">
                    AND o.`delete_state` != #{example.deleteStateNotEquals}
                </if>
                <if test="example.refuseReason != null">
                    AND o.`refuse_reason` = #{example.refuseReason}
                </if>
                <if test="example.refuseReasonLike != null">
                    AND o.`refuse_reason` like concat('%',#{example.refuseReasonLike},'%')
                </if>
                <if test="example.refuseRemark != null">
                    AND o.`refuse_remark` = #{example.refuseRemark}
                </if>
                <if test="example.isSettlement != null">
                    AND o.`is_settlement` = #{example.isSettlement}
                </if>
                <if test="example.goodsNameLike != null">
                    AND p.`goods_name` like concat('%',#{example.goodsNameLike},'%')
                </if>
                <if test="example.orderReturnState != null and example.orderReturnState != 0 and example.orderReturnState != 2">
                    AND exists (select 1 from bz_order_product bopr where bopr.order_sn = o.order_sn and bopr.status =
                    #{example.orderReturnState})
                </if>
                <if test="example.orderReturnState != null and example.orderReturnState == 2">
                    AND exists (select 1 from bz_order_product bopr where bopr.order_sn = o.order_sn and (bopr.status =
                    2 or (bopr.status = 1 and bopr.return_number != bopr.product_num)))
                </if>
                <if test="example.channel != null">
                    AND o.`channel` = #{example.channel}
                </if>
                <if test="example.orderPattern != null">
                    AND o.`order_pattern` = #{example.orderPattern}
                </if>
                <if test="example.recommendStoreId != null">
                    AND o.recommend_store_id = #{example.recommendStoreId}
                </if>
                <if test="example.customerId != null">
                    AND e.customer_id = #{example.customerId}
                </if>
                <if test="example.customerName != null">
                    AND e.customer_name like concat('%',#{example.customerName},'%')
                </if>
                <if test="example.invoiceStatus != null">
                    AND e.invoice_status = #{example.invoiceStatus}
                </if>
                <if test="example.paymentTag != null">
                    AND o.payment_tag = #{example.paymentTag}
                </if>
                <if test="example.branchNameLike != null">
                    AND e.branch_name like concat('%',#{example.branchNameLike},'%')
                </if>
                <if test="example.areaNameLike != null">
                    AND e.area_name like concat('%',#{example.areaNameLike},'%')
                </if>
                <if test="example.goodsNameLike != null">
                    AND p.goods_name like concat('%',#{example.goodsNameLike},'%')
                </if>
                <if test="example.receiverNameLike != null and example.receiverNameLike != ''">
                    AND e.receiver_name like concat('%',#{example.receiverNameLike},'%')
                </if>
                <if test="example.receiverMobile != null and example.receiverMobile != ''">
                    AND e.receiver_mobile = #{example.receiverMobile}
                </if>
                <if test="example.userMobile != null and example.userMobile != ''">
                    AND o.user_mobile = #{example.userMobile}
                </if>
                <if test="example.newOrder != null">
                    AND o.new_order = #{example.newOrder}
                </if>
                <if test="example.performanceService != null">
                    AND locate(#{example.performanceService}, o.`performance_modes`)
                </if>
                <if test="example.customerConfirmStatus != null">
                    AND o.customer_confirm_status = #{example.customerConfirmStatus}
                </if>
                <if test="example.managerNameLike != null">
                    AND e.manager_name like concat('%',#{example.managerNameLike},'%')
                </if>
                <if test="example.managerNameList != null and example.managerNameList.size() > 0">
                    AND e.manager_name in
                    <foreach collection="example.managerNameList" item="managerName" open="(" separator="," close=")">
                        #{managerName}
                    </foreach>
                </if>
                <if test="example.branchCodeIn != null and example.branchCodeIn.size() > 0">
                    AND e.branch in
                    <foreach collection="example.branchCodeIn" item="branchCode" open="(" separator="," close=")">
                        #{branchCode}
                    </foreach>
                </if>
                <if test="example.storeBranchCodeIn != null and example.storeBranchCodeIn.size() > 0">
                    AND e.store_branch in
                    <foreach collection="example.storeBranchCodeIn" item="storeBranchCode" open="(" separator=","
                             close=")">
                        #{storeBranchCode}
                    </foreach>
                </if>
                <if test="example.performanceBranchCodeList != null and example.performanceBranchCodeList.size() > 0">
                    AND pb.employee_branch_code in
                    <foreach collection="example.performanceBranchCodeList" item="performanceBranchCode" open="(" separator="," close=")">
                        #{performanceBranchCode}
                    </foreach>
                </if>
                <if test="example.performanceBelongerNameList != null and example.performanceBelongerNameList.size() > 0">
                    AND pb.belonger_name in
                    <foreach collection="example.performanceBelongerNameList" item="performanceBelongerName" open="(" separator="," close=")">
                        #{performanceBelongerName}
                    </foreach>
                </if>
                <if test="example.performanceBelongerEmployeeNoList != null and example.performanceBelongerEmployeeNoList.size() > 0">
                    AND pb.belonger_employee_no in
                    <foreach collection="example.performanceBelongerEmployeeNoList" item="performanceBelongerEmployeeNo" open="(" separator="," close=")">
                        #{performanceBelongerEmployeeNo}
                    </foreach>
                </if>
                <if test="example.supplierCode != null">
                    AND p.supplier_code = #{example.supplierCode}
                </if>
                <if test="example.goodsCategoryIdIn != null and example.goodsCategoryIdIn.size() > 0">
                    AND p.goods_category_id IN
                    <foreach collection="example.goodsCategoryIdIn" item="goodsCategoryId" open="(" separator=","
                             close=")">
                        #{goodsCategoryId}
                    </foreach>
                </if>
                <if test="example.pointId != null">
                    AND e.point_id = #{example.pointId}
                </if>
                <if test="example.receiveMaterialStatus != null">
                    AND e.receive_material_status = #{example.receiveMaterialStatus}
                </if>
            </trim>
        </if>
    </sql>
    <!--查询符合条件的记录数-->
    <select id="selectByOrderSnOrpaySn" parameterType="com.cfpamf.ms.mallorder.request.OrderExample"
            resultMap="resultMap">
        SELECT
        *
        FROM `bz_order`
        <where>
            <if test="example.orderSn != null">
                `order_sn` = #{example.orderSn}
            </if>
            <if test="example.paySn != null">
                or `pay_sn` = #{example.paySn}
            </if>
        </where>
    </select>
    <!--分页查询符合条件的记录(指定字段)-->
    <select id="listFieldsPageByExample" resultMap="resultMap">
        SELECT
        ${fields}
        FROM `bz_order`
        <include refid="whereCondition"/>
        <if test="example.groupBy != null">
            <include refid="groupBy"/>
        </if>
        <choose>
            <when test="example.orderBy != null">
                <include refid="orderByOther"/>
            </when>
            <otherwise>
                <include refid="orderBy"/>
            </otherwise>
        </choose>
        <include refid="limit"/>
    </select>
    <!--根据条件删除记录，可多条删除-->
    <update id="deleteByExample">
        update `bz_order`SET enabled_flag =0
        <include refid="whereCondition"/>
    </update>
    <!--根据主键删除记录-->
    <update id="deleteByPrimaryKey">
        update `bz_order`SET enabled_flag =0
        <include refid="pkWhere"/>
    </update>

    <!--按条件更新记录中不为空的字段-->
    <update id="updateByExampleSelective">
        UPDATE `bz_order`
        <trim prefix="SET" suffixOverrides=",">
            <if test="record.orderSn != null">
                `order_sn` = #{record.orderSn},
            </if>
            <if test="record.paySn != null">
                `pay_sn` = #{record.paySn},
            </if>
            <if test="record.parentSn != null">
                `parent_sn` = #{record.parentSn},
            </if>
            <if test="record.storeId != null">
                `store_id` = #{record.storeId},
            </if>
            <if test="record.storeName != null">
                `store_name` = #{record.storeName},
            </if>
            <if test="record.memberName != null">
                `member_name` = #{record.memberName},
            </if>
            <if test="record.memberId != null">
                `member_id` = #{record.memberId},
            </if>
            <if test="record.payTime != null">
                `pay_time` = #{record.payTime},
            </if>
            <if test="record.payUpdateTime != null">
                `pay_update_time` = #{record.payUpdateTime},
            </if>
            <if test="record.createTime != null">
                `create_time` = #{record.createTime},
            </if>
            <if test="record.finishTime != null">
                `finish_time` = #{record.finishTime},
            </if>
            <if test="record.orderState != null">
                `order_state` = #{record.orderState},
            </if>
            <if test="record.loanPayState != null">
                `loan_pay_state` = #{record.loanPayState},
            </if>
            <if test="record.lendingSuccessTime != null">
                `lending_success_time` = #{record.lendingSuccessTime},
            </if>
            <if test="record.paymentName != null">
                `payment_name` = #{record.paymentName},
            </if>
            <if test="record.paymentCode != null">
                `payment_code` = #{record.paymentCode},
            </if>
            <if test="record.goodsAmount != null">
                `goods_amount` = #{record.goodsAmount},
            </if>
            <if test="record.expressFee != null">
                `express_fee` = #{record.expressFee},
            </if>
            <if test="record.activityDiscountAmount != null">
                `activity_discount_amount` = #{record.activityDiscountAmount},
            </if>
            <if test="record.activityDiscountDetail != null">
                `activity_discount_detail` = #{record.activityDiscountDetail},
            </if>
            <if test="record.orderAmount != null">
                `order_amount` = #{record.orderAmount},
            </if>
            <if test="record.xzCardAmount != null">
                `xz_card_amount` = #{record.xzCardAmount},
            </if>
            <if test="record.xzCardExpressFeeAmount != null">
                `xz_card_express_fee_amount` = #{record.xzCardExpressFeeAmount},
            </if>
            <if test="record.composePayName != null">
                `compose_pay_name` = #{record.composePayName},
            </if>
            <if test="record.balanceAmount != null">
                `balance_amount` = #{record.balanceAmount},
            </if>
            <if test="record.payAmount != null">
                `pay_amount` = #{record.payAmount},
            </if>
            <if test="record.refundAmount != null">
                `refund_amount` = #{record.refundAmount},
            </if>
            <if test="record.integralCashAmount != null">
                `integral_cash_amount` = #{record.integralCashAmount},
            </if>
            <if test="record.integral != null">
                `integral` = #{record.integral},
            </if>
            <if test="record.delayDays != null">
                `delay_days` = #{record.delayDays},
            </if>
            <if test="record.evaluateState != null">
                `evaluate_state` = #{record.evaluateState},
            </if>
            <if test="record.orderType != null">
                `order_type` = #{record.orderType},
            </if>
            <if test="record.lockState != null">
                `lock_state` = #{record.lockState},
            </if>
            <if test="record.deleteState != null">
                `delete_state` = #{record.deleteState},
            </if>
            <if test="record.refuseReason != null">
                `refuse_reason` = #{record.refuseReason},
            </if>
            <if test="record.refuseRemark != null">
                `refuse_remark` = #{record.refuseRemark},
            </if>
            <!--      <if test="record.isGenerateFacesheet != null">-->
            <!--        `is_generate_facesheet` = #{record.isGenerateFacesheet},-->
            <!--      </if>-->
            <if test="record.serviceFee != null">
                `service_fee` = #{record.serviceFee},
            </if>
            <if test="record.serviceFeeRate != null">
                `service_fee_rate` = #{record.serviceFeeRate},
            </if>
            <if test="record.thirdpartnarFee != null">
                `thirdpartnar_fee` = #{record.thirdpartnarFee},
            </if>
            <if test="record.thirdpartnarFeeRate != null">
                `thirdpartnar_fee_rate` = #{record.thirdpartnarFeeRate},
            </if>
            <if test="record.orderCommission != null">
                `order_commission` = #{record.orderCommission},
            </if>
            <if test="record.businessCommission != null">
                `business_commission` = #{record.businessCommission},
            </if>
            <if test="record.settlementPrice != null">
                `settlement_price` = #{record.settlementPrice},
            </if>
            <if test="record.settleMode != null">
                `settle_mode` = #{record.settleMode},
            </if>
            <if test="record.isSettlement != null">
                `is_settlement` = #{record.isSettlement},
            </if>
            <if test="record.financeRuleCode != null">
                `finance_rule_code` = #{record.financeRuleCode},
            </if>
            <if test="record.ruleTag != null">
                `rule_tag` = #{record.ruleTag},
            </if>
            <if test="record.isDelivery != null">
                `is_delivery` = #{record.isDelivery},
            </if>
            <if test="record.newOrder != null">
                `new_order` = #{record.newOrder},
            </if>
            <if test="record.afterSalesDeadline != null">
                `after_sales_deadline` = #{record.afterSalesDeadline},
            </if>
        </trim>
        <include refid="whereCondition"/>
    </update>
    <!--按照主键更新记录中不为空的字段-->
    <update id="updateByPrimaryKeySelective">
        UPDATE `bz_order`
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderSn != null">
                `order_sn` = #{orderSn},
            </if>
            <if test="paySn != null">
                `pay_sn` = #{paySn},
            </if>
            <if test="parentSn != null">
                `parent_sn` = #{parentSn},
            </if>
            <if test="storeId != null">
                `store_id` = #{storeId},
            </if>
            <if test="storeName != null">
                `store_name` = #{storeName},
            </if>
            <if test="memberName != null">
                `member_name` = #{memberName},
            </if>
            <if test="memberId != null">
                `member_id` = #{memberId},
            </if>
            <if test="payTime != null">
                `pay_time` = #{payTime},
            </if>
            <if test="payUpdateTime != null">
                `pay_update_time` = #{payUpdateTime},
            </if>
            <if test="createTime != null">
                `create_time` = #{createTime},
            </if>
            <if test="finishTime != null">
                `finish_time` = #{finishTime},
            </if>
            <if test="orderState != null">
                `order_state` = #{orderState},
            </if>
            <if test="loanPayState != null">
                `loan_pay_state` = #{loanPayState},
            </if>
            <if test="lendingSuccessTime != null">
                `lending_success_time` = #{lendingSuccessTime},
            </if>
            <if test="paymentName != null">
                `payment_name` = #{paymentName},
            </if>
            <if test="paymentCode != null">
                `payment_code` = #{paymentCode},
            </if>
            <if test="goodsAmount != null">
                `goods_amount` = #{goodsAmount},
            </if>
            <if test="expressFee != null">
                `express_fee` = #{expressFee},
            </if>
            <if test="activityDiscountAmount != null">
                `activity_discount_amount` = #{activityDiscountAmount},
            </if>
            <if test="activityDiscountDetail != null">
                `activity_discount_detail` = #{activityDiscountDetail},
            </if>
            <if test="orderAmount != null">
                `order_amount` = #{orderAmount},
            </if>
            <if test="xzCardAmount != null">
                `xz_card_amount` = #{xzCardAmount},
            </if>
            <if test="xzCardExpressFeeAmount != null">
                `xz_card_express_fee_amount` = #{xzCardExpressFeeAmount},
            </if>
            <if test="composePayName != null">
                `compose_pay_name` = #{composePayName},
            </if>
            <if test="balanceAmount != null">
                `balance_amount` = #{balanceAmount},
            </if>
            <if test="payAmount != null">
                `pay_amount` = #{payAmount},
            </if>
            <if test="refundAmount != null">
                `refund_amount` = #{refundAmount},
            </if>
            <if test="integralCashAmount != null">
                `integral_cash_amount` = #{integralCashAmount},
            </if>
            <if test="integral != null">
                `integral` = #{integral},
            </if>
            <if test="delayDays != null">
                `delay_days` = #{delayDays},
            </if>
            <if test="evaluateState != null">
                `evaluate_state` = #{evaluateState},
            </if>
            <if test="orderType != null">
                `order_type` = #{orderType},
            </if>
            <if test="lockState != null">
                `lock_state` = #{lockState},
            </if>
            <if test="deleteState != null">
                `delete_state` = #{deleteState},
            </if>
            <if test="refuseReason != null">
                `refuse_reason` = #{refuseReason},
            </if>
            <if test="refuseRemark != null">
                `refuse_remark` = #{refuseRemark},
            </if>
            <!--      <if test="isGenerateFacesheet != null">-->
            <!--        `is_generate_facesheet` = #{isGenerateFacesheet},-->
            <!--      </if>-->
            <if test="serviceFee != null">
                `service_fee` = #{serviceFee},
            </if>
            <if test="serviceFeeRate != null">
                `service_fee_rate` = #{serviceFeeRate},
            </if>
            <if test="thirdpartnarFee != null">
                `thirdpartnar_fee` = #{thirdpartnarFee},
            </if>
            <if test="thirdpartnarFeeRate != null">
                `thirdpartnar_fee_rate` = #{thirdpartnarFeeRate},
            </if>
            <if test="orderCommission != null">
                `order_commission` = #{orderCommission},
            </if>
            <if test="businessCommission != null">
                `business_commission` = #{businessCommission},
            </if>
            <if test="settlementPrice != null">
                `settlement_price` = #{settlementPrice},
            </if>
            <if test="settleMode != null">
                `settle_mode` = #{settleMode},
            </if>
            <if test="isSettlement != null">
                `is_settlement` = #{isSettlement},
            </if>
            <if test="financeRuleCode != null">
                `finance_rule_code` = #{record.financeRuleCode},
            </if>
            <if test="ruleTag != null">
                `rule_tag` = #{record.ruleTag},
            </if>
            <if test="newOrder != null">
                `new_order` = #{record.newOrder},
            </if>
        </trim>
        WHERE `order_id` = #{orderId}
    </update>

    <select id="getOrderAmount" resultType="com.cfpamf.ms.mallorder.po.OrderPO">
        SELECT order_id,
        order_sn,
        pay_sn,
        order_state,
        order_pattern,
        store_id,
        store_name,
        recommend_store_id,
        order_type,
        pay_time,
        finish_time,
        loan_pay_state,
        payment_name,
        payment_code,
        compose_pay_name,
        xz_card_amount,
        order_amount,
        platform_activity_amount,
        platform_voucher_amount,
        integral_cash_amount,
        service_fee,
        thirdpartnar_fee,
        order_commission,
        business_commission,
        channel_service_fee,
        new_order
        FROM bz_order
        WHERE order_sn = #{orderSn};
    </select>

    <select id="pageStoreOrderList" resultMap="resultMap">
        SELECT bo.*
        from bz_order bo ,bz_order_extend boe
        where bo.enabled_flag=1
        AND bo.order_sn =boe.order_sn
        and bo.store_id = #{queryDTO.storeId}
        <if test="queryDTO.orderSn !=null and queryDTO.orderSn !=''">
            and bo.order_sn = #{queryDTO.orderSn}
        </if>
        <if test="queryDTO.goodsName !=null and queryDTO.goodsName !=''">
            AND bo.order_sn in(
            select order_sn from bz_order_product
            where enabled_flag = 1
            and goods_name like concat('%',#{queryDTO.goodsName},'%')
            )
        </if>
        <if test="queryDTO.userMobile !=null and queryDTO.userMobile !=''">
            and bo.user_mobile like concat('%',#{queryDTO.userMobile},'%')
        </if>
        <if test="queryDTO.orderState !=null">
            and bo.order_state =#{queryDTO.orderState}
        </if>
        <if test="queryDTO.orderStateIn != null">
            and bo.order_state in (${queryDTO.orderStateIn})
        </if>
        <if test="queryDTO.evaluateState !=null">
            and bo.evaluate_state =#{queryDTO.evaluateState}
        </if>
        group by bo.order_sn
        <choose>
            <when test="queryDTO.orderState == 10">
                order by bo.create_time desc
            </when>
            <when test="queryDTO.orderState == 20">
                order by bo.pay_time asc
            </when>
            <when test="queryDTO.orderState == 30">
                order by boe.deliver_time desc
            </when>
            <when test="(queryDTO.orderState == '' or queryDTO.orderState == null) and (queryDTO.evaluateState == '' or  queryDTO.evaluateState == null)">
                order by bo.create_time desc
            </when>
            <when test="queryDTO.evaluateState >1 and (queryDTO.orderState == '' or queryDTO.orderState == null) ">
                order by bo.finish_time desc
            </when>
        </choose>
    </select>

    <select id="getOrderCountInfo" resultType="com.cfpamf.ms.mallorder.vo.OrderStatusCountVO">
        SELECT sum(tmp.waitPayCount) AS waitPayCount,
        sum(tmp.toDeliveredCount) AS toDeliveredCount,
        sum(tmp.waitDeliveredCount) AS waitDeliveredCount,
        sum(tmp.waitReturnAuditCount) AS waitReturnAuditCount,
        sum(tmp.waitOnlyReturnAmountCount) AS waitOnlyReturnAmountCount,
        sum(tmp.waitReturnCount) AS waitReturnCount
        FROM (
        SELECT ifnull(sum(case when order_state = 10 then 1 end), 0) as waitPayCount,
        ifnull(sum(case when order_state = 20 then 1 end), 0) as toDeliveredCount,
        ifnull(sum(case when order_state = 30 then 1 end), 0) as waitDeliveredCount,
        0 AS waitReturnAuditCount,
        0 As waitOnlyReturnAmountCount,
        0 AS waitReturnCount
        FROM bz_order
        WHERE store_id = #{storeId}
        union
        SELECT 0 as waitPayCount,
        0 as toDeliveredCount,
        0 as waitDeliveredCount,
        ifnull(sum(case when state = 100 or state = 101 then 1 end), 0) AS waitReturnAuditCount,
        ifnull(sum(case when return_type = 1 then 1 end), 0) As waitOnlyReturnAmountCount,
        ifnull(sum(case when return_type = 2 then 1 end), 0) AS waitReturnCount
        FROM bz_order_return
        WHERE store_id = #{storeId}
        ) tmp
    </select>

    <select id="getAutoDeliverOrder" resultType="java.lang.String">
        select bo.order_sn
        from bz_order bo,
        bz_order_extend_finance boef
        where bo.order_sn = boef.order_sn
        and bo.order_state = 20
        and boef.deliver_method = 1
        and auto_deliver_time &lt; #{deadline};
    </select>

    <select id="getAutoReceiveOrder" resultType="com.cfpamf.ms.mallorder.po.OrderPO">
        select bo.*
        from bz_order bo,
        bz_order_extend_finance boef
        where bo.order_sn = boef.order_sn
        and bo.lock_state = 0
        and bo.order_state = 30
        and bo.exchange_flag != 2
        and bo.payment_code IN ('ENJOY_PAY','CREDIT_PAY','FOLLOW_HEART')
        and bo.order_type not in (5,6)
        and DATEDIFF(now(), bo.deliver_time) >= boef.auto_receive_days;
    </select>

    <select id="getStoreValidOrderAmountSum" resultType="java.math.BigDecimal">
        SELECT
        sum(bo.order_amount) orderAmountSum
        FROM
        bz_order bo
        WHERE
        bo.store_id = #{storeId}
        AND DATEDIFF(now(), bo.finish_time) <![CDATA[ >= ]]> 7
        AND NOT EXISTS (SELECT 1 FROM bz_order_return bor WHERE bor.order_sn = bo.order_sn);
    </select>


    <select id="getAutoDeliveryOrders" resultType="com.cfpamf.ms.mallorder.po.OrderPO">
        SELECT
        bo.*
        FROM
        bz_order bo
        WHERE
        bo.order_state = 30
        AND DATEDIFF(now(),bo.deliver_time) &gt;= bo.auto_receive_day
        AND bo.settle_mode != 'borrow'
        AND bo.auto_receive_day &gt; 0
        AND !(bo.payment_code IN ('ENJOY_PAY','CREDIT_PAY','FOLLOW_HEART') and bo.`finance_rule_code` is not null and
        bo.`finance_rule_code` != '')
        AND bo.exchange_flag != 2
        AND NOT EXISTS ( SELECT 1 FROM `bz_order_return` bor WHERE bo.`order_sn` = bor.`order_sn` AND bor.`state` IN
        (100,101,102,200,201,203,300))
        order by bo.deliver_time desc
        LIMIT 0, 100
    </select>


    <resultMap id="OrderListVOV2" type="com.cfpamf.ms.mallorder.vo.OrderListVOV2">
        <id column="orderId" property="orderId"/>
        <result column="create_time" property="createTime"/>
        <result column="orderSn" property="orderSn"/>
        <result column="paySn" property="paySn"/>
        <result column="storeName" property="storeName"/>
        <result column="createTime" property="createTime"/>
        <result column="memberName" property="memberName"/>
        <result column="customerId" property="customerId"/>
        <result column="userMobile" property="userMobile"/>
        <result column="customerName" property="customerName"/>
        <result column="orderAmount" property="orderAmount"/>
        <result column="expressFee" property="expressFee"/>
        <result column="paymentName" property="paymentName"/>
        <result column="paymentCode" property="paymentCode"/>
        <result column="orderState" property="orderState"/>
        <result column="receiverName" property="receiverName"/>
        <result column="receiverAreaInfo" property="receiverAreaInfo"/>
        <result column="receiverAddress" property="receiverAddress"/>
        <result column="receiverProvince" property="receiverProvince"/>
        <result column="receiverCity" property="receiverCity"/>
        <result column="receiverDistrict" property="receiverDistrict"/>
        <result column="receiverTown" property="receiverTown"/>
        <result column="receiverMobile" property="receiverMobile"/>
        <result column="lockState" property="lockState"/>
        <result column="orderType" property="orderType"/>
        <result column="isDelivery" property="isDelivery"/>
        <result column="activityDiscountAmount" property="activityDiscountAmount"/>
        <result column="financeRuleCode" property="financeRuleCode"/>
        <result column="ruleTag" property="ruleTag"/>
        <result column="channel" property="channel"/>
        <result column="composePayName" property="composePayName"/>
        <result column="xzCardAmount" property="xzCardAmount"/>
        <result column="xzCardExpressFeeAmount" property="xzCardExpressFeeAmount"/>
        <result column="orderPattern" property="orderPattern"/>
        <result column="areaCode" property="areaCode"/>
        <result column="storeId" property="storeId"/>
        <result column="performanceModes" property="performanceModes"/>

        <result column="managerName" property="managerName"/>
        <result column="branchName" property="branchName"/>
        <result column="receiveCode" property="receiveCode"/>
        <result column="storeBranchName" property="storeBranchName"/>
        <result column="settledType" property="settledType"/>
        <result column="companyName" property="companyName"/>

        <result column="customer_confirm_status" property="customerConfirmStatus"/>
        <result column="customer_confirm_status_desc" property="customerConfirmStatusDesc"/>
        <result column="exchangeFlag" property="exchangeFlag"/>
        <result column="paymentTag" property="paymentTag"/>
        <result column="kingdeeStockPushMode" property="kingdeeStockPushMode"/>
        <result column="invoiceStatus" property="invoiceStatus"/>
        <result column="invoiceAmount" property="invoiceAmount"/>
        <result column="invoiceTime" property="invoiceTime"/>
        <result column="pointId" property="pointId"/>
        <result column="pointName" property="pointName"/>
        <result column="receiveMaterialStatus" property="receiveMaterialStatus"/>
        <result column="performanceBranchCode" property="performanceBranchCode"/>
        <result column="performanceBranchName" property="performanceBranchName"/>

        <collection property="orderProductListVOList" ofType="com.cfpamf.ms.mallorder.vo.OrderProductListVO"
                    column="{orderSn=orderSn}"
                    select="com.cfpamf.ms.mallorder.mapper.OrderMapper.getProductForOrder">
            <id column="orderProductId" property="orderProductId"/>
            <result column="goodsName" property="goodsName"/>
            <result column="productShowPrice" property="productShowPrice"/>
            <result column="productNum" property="productNum"/>
            <result column="goodsId" property="goodsId"/>
            <result column="productId" property="productId"/>
            <result column="specValues" property="specValues"/>
            <result column="productImage" property="productImage"/>
            <result column="isComment" property="isComment"/>
            <result column="isGift" property="isGift"/>
            <result column="giftGroup" property="giftGroup"/>
            <result column="deliveryState" property="deliveryState"/>
            <result column="xzCardAmount" property="xzCardAmount"/>
            <result column="financeRuleCode" property="financeRuleCode"/>
            <result column="ruleTag" property="ruleTag"/>
            <result column="status" property="status"/>
            <result column="supplierName" property="supplierName"/>
            <result column="performanceMode" property="performanceMode"/>
            <result column="performanceChannel" property="performanceChannel"/>
            <result column="performanceService" property="performanceService"/>
            <result column="returnNumber" property="returnNumber"/>
            <result column="channelSkuId" property="channelSkuId"/>
        </collection>
    </resultMap>


    <resultMap id="OfflineOrderListVOV2" type="com.cfpamf.ms.mallorder.vo.OfflineOrderListVOV2">
        <id column="orderId" property="orderId"/>
        <result column="create_time" property="createTime"/>
        <result column="orderSn" property="orderSn"/>
        <result column="paySn" property="paySn"/>
        <result column="storeName" property="storeName"/>
        <result column="createTime" property="createTime"/>
        <result column="memberName" property="memberName"/>
        <result column="customerId" property="customerId"/>
        <result column="userMobile" property="userMobile"/>
        <result column="customerName" property="customerName"/>
        <result column="orderAmount" property="orderAmount"/>
        <result column="expressFee" property="expressFee"/>
        <result column="paymentName" property="paymentName"/>
        <result column="paymentCode" property="paymentCode"/>
        <result column="orderState" property="orderState"/>
        <result column="receiverName" property="receiverName"/>
        <result column="receiverAreaInfo" property="receiverAreaInfo"/>
        <result column="receiverAddress" property="receiverAddress"/>
        <result column="receiverProvince" property="receiverProvince"/>
        <result column="receiverCity" property="receiverCity"/>
        <result column="receiverDistrict" property="receiverDistrict"/>
        <result column="receiverTown" property="receiverTown"/>
        <result column="receiverMobile" property="receiverMobile"/>
        <result column="lockState" property="lockState"/>
        <result column="orderType" property="orderType"/>
        <result column="isDelivery" property="isDelivery"/>
        <result column="activityDiscountAmount" property="activityDiscountAmount"/>
        <result column="financeRuleCode" property="financeRuleCode"/>
        <result column="ruleTag" property="ruleTag"/>
        <result column="channel" property="channel"/>
        <result column="composePayName" property="composePayName"/>
        <result column="xzCardAmount" property="xzCardAmount"/>
        <result column="xzCardExpressFeeAmount" property="xzCardExpressFeeAmount"/>
        <result column="orderPattern" property="orderPattern"/>
        <result column="areaCode" property="areaCode"/>
        <result column="storeId" property="storeId"/>
        <result column="performanceModes" property="performanceModes"/>

        <result column="managerName" property="managerName"/>
        <result column="branchName" property="branchName"/>
        <result column="storeBranchName" property="storeBranchName"/>
        <result column="settledType" property="settledType"/>
        <result column="companyName" property="companyName"/>

        <result column="customer_confirm_status" property="customerConfirmStatus"/>
        <result column="customer_confirm_status_desc" property="customerConfirmStatusDesc"/>
        <result column="exchangeFlag" property="exchangeFlag"/>
        <result column="paymentTag" property="paymentTag"/>
        <result column="kingdeeStockPushMode" property="kingdeeStockPushMode"/>
        <result column="invoiceStatus" property="invoiceStatus"/>
        <result column="invoiceAmount" property="invoiceAmount"/>
        <result column="invoiceTime" property="invoiceTime"/>

        <result column="overdueFlag" property="overdueFlag"/>
        <result column="overdueTime" property="overdueTime"/>
        <result column="buyerSupplierCode" property="buyerSupplierCode"/>
        <result column="buyerSupplierName" property="buyerSupplierName"/>

        <collection property="orderProductListVOList" ofType="com.cfpamf.ms.mallorder.vo.OrderProductListVO"
                    column="{orderSn=orderSn}"
                    select="com.cfpamf.ms.mallorder.mapper.OrderMapper.getProductForOrder">
            <id column="orderProductId" property="orderProductId"/>
            <result column="goodsName" property="goodsName"/>
            <result column="productShowPrice" property="productShowPrice"/>
            <result column="productNum" property="productNum"/>
            <result column="goodsId" property="goodsId"/>
            <result column="productId" property="productId"/>
            <result column="specValues" property="specValues"/>
            <result column="productImage" property="productImage"/>
            <result column="isComment" property="isComment"/>
            <result column="isGift" property="isGift"/>
            <result column="giftGroup" property="giftGroup"/>
            <result column="deliveryState" property="deliveryState"/>
            <result column="xzCardAmount" property="xzCardAmount"/>
            <result column="financeRuleCode" property="financeRuleCode"/>
            <result column="ruleTag" property="ruleTag"/>
            <result column="status" property="status"/>
            <result column="supplierName" property="supplierName"/>
            <result column="performanceMode" property="performanceMode"/>
            <result column="performanceChannel" property="performanceChannel"/>
            <result column="performanceService" property="performanceService"/>
            <result column="returnNumber" property="returnNumber"/>
            <result column="channelSkuId" property="channelSkuId"/>
        </collection>
    </resultMap>


    <!-- 订单查询（关联其它表） -->
    <select id="countByExampleWithJoin" parameterType="com.cfpamf.ms.mallorder.request.OrderExample"
            resultType="java.lang.Integer">
        SELECT
        COUNT(*)
        FROM bz_order bo
        LEFT JOIN bz_order_extend boe on bo.order_sn = boe.order_sn
        <include refid="whereConditionWithJoin"/>
    </select>

    <select id="countOfflineOrderByExampleWithJoin" parameterType="com.cfpamf.ms.mallorder.request.OrderExample"
            resultType="java.lang.Integer">
        SELECT
        COUNT(*)
        FROM bz_order bo
        LEFT JOIN bz_order_extend boe on bo.order_sn = boe.order_sn
        left join bz_order_offline_extend booe on bo.order_sn = booe.order_sn
        <include refid="whereConditionWithJoin"/>
        <include refid="orderOfflineExtendWhereCondition"/>
    </select>

    <sql id="selectOrderAndExtendColumn">
        bo.order_id orderId,
        bo.pay_sn paySn,
        bo.order_sn orderSn,
        bo.user_mobile userMobile,
        bo.store_name storeName,
        bo.create_time createTime,
        bo.member_name memberName,
        bo.user_no userNo,
        boe.customer_id customerId,
        boe.customer_name customerName,
        boe.receive_code receiveCode,
        bo.order_amount orderAmount,
        bo.express_fee expressFee,
        bo.payment_name paymentName,
        bo.payment_code paymentCode,
        bo.order_state orderState,
        boe.receiver_name receiverName,
        boe.receiver_area_info receiverAreaInfo,
        boe.receiver_address receiverAddress,
        boe.receiver_province_code receiverProvince,
        boe.receiver_city_code receiverCity,
        boe.receiver_district_code receiverDistrict,
        boe.receiver_town_code receiverTown,
        boe.receiver_mobile receiverMobile,
        bo.lock_state lockState,
        bo.order_type orderType,
        bo.is_delivery isDelivery,
        bo.activity_discount_amount activityDiscountAmount,
        bo.finance_rule_code financeRuleCode,
        bo.rule_tag ruleTag,
        bo.channel channel,
        bo.customer_confirm_status,
        bo.customer_confirm_status_desc,
        bo.compose_pay_name composePayName,
        bo.xz_card_amount xzCardAmount,
        bo.xz_card_express_fee_amount xzCardExpressFeeAmount,
        bo.order_pattern orderPattern,
        bo.store_id storeId,
        bo.performance_modes performanceModes,
        boe.manager manager,
        boe.manager_name managerName,
        boe.branch_name branchName,
        boe.store_branch_name storeBranchName,
        boe.settled_type settledType,
        boe.company_name companyName,
        bo.exchange_flag exchangeFlag,
        bo.area_code areaCode,
        bo.after_sales_deadline afterSalesDeadline,
        bo.finish_time finishTime,
        bo.payment_tag paymentTag,
        boe.kingdee_stock_push_mode kingdeeStockPushMode,
        boe.invoice_status invoiceStatus,
        boe.invoice_amount invoiceAmount,
        boe.invoice_time invoiceTime,
        boe.point_id pointId,
        boe.point_name pointName,
        boe.receive_material_status receiveMaterialStatus,
        (select bopb.employee_branch_code from bz_order_performance_belongs bopb where bopb.order_sn = bo.order_sn limit 1) performanceBranchCode,
        (select bopb.employee_branch_name from bz_order_performance_belongs bopb where bopb.order_sn = bo.order_sn limit 1) performanceBranchName
    </sql>

    <!--关联其它表分页查询符合条件的记录(所有字段)-->
    <select id="listPageByExampleWithJoin" resultMap="OrderListVOV2">
        SELECT
        <include refid="selectOrderAndExtendColumn"/>
        FROM bz_order bo
        LEFT JOIN bz_order_extend boe on bo.order_sn = boe.order_sn
        <include refid="whereConditionWithJoin"/>
        <if test="example.groupBy != null">
            group by bo.${example.groupBy}
        </if>
        <choose>
            <when test="example.orderBy != null">
                order by bo.${example.orderBy}
            </when>
            <otherwise>
                ORDER BY bo.order_id DESC
            </otherwise>
        </choose>
        <include refid="limit"/>
    </select>

    <select id="listOfflineOrderPageByExampleWithJoin" resultMap="OfflineOrderListVOV2">
        SELECT
        booe.overdue_flag overdueFlag,
        booe.overdue_time overdueTime,
        booe.buyer_supplier_code buyerSupplierCode,
        booe.buyer_supplier_name buyerSupplierName,
        <include refid="selectOrderAndExtendColumn"/>
        FROM bz_order bo
        LEFT JOIN bz_order_extend boe on bo.order_sn = boe.order_sn
        left join bz_order_offline_extend booe on bo.order_sn = booe.order_sn
        <include refid="whereConditionWithJoin"/>
        <include refid="orderOfflineExtendWhereCondition"/>

        <if test="example.groupBy != null">
            group by bo.${example.groupBy}
        </if>
        <choose>
            <when test="example.orderBy != null">
                order by bo.${example.orderBy}
            </when>
            <otherwise>
                ORDER BY bo.order_id DESC
            </otherwise>
        </choose>
        <include refid="limit"/>
    </select>

    <select id="listByExampleWithJoin" resultMap="OrderListVOV2">
        SELECT
        <include refid="selectOrderAndExtendColumn"/>
        FROM bz_order bo
        LEFT JOIN bz_order_extend boe on bo.order_sn = boe.order_sn
        <include refid="whereConditionWithJoin"/>
        <if test="example.groupBy != null">
            group by bo.${example.groupBy}
        </if>
        <choose>
            <when test="example.orderBy != null">
                order by bo.${example.orderBy}
            </when>
            <otherwise>
                ORDER BY bo.order_id DESC
            </otherwise>
        </choose>
    </select>

    <select id="getProductForOrder" resultType="com.cfpamf.ms.mallorder.vo.OrderProductListVO">
        SELECT
        bop.order_product_id orderProductId,
        bop.goods_name goodsName,
        bop.product_show_price productShowPrice,
        bop.product_num productNum,
        bop.goods_id goodsId,
        bop.product_id productId,
        bop.spec_values specValues,
        bop.product_image productImage,
        bop.is_comment isComment,
        bop.is_gift isGift,
        bop.gift_group giftGroup,
        bop.delivery_state deliveryState,
        bop.xz_card_amount xzCardAmount,
        bop.finance_rule_code financeRuleCode,
        bop.rule_tag ruleTag,
        bop.status status,
        bop.supplier_name supplierName,
        bop.performance_mode performanceMode,
        bop.performance_channel performanceChannel,
        bop.performance_service performanceService,
        bop.return_number returnNumber,
        bop.delivery_num deliveryNum,
        bop.channel_sku_id channelSkuId
        FROM bz_order_product bop
        WHERE bop.order_sn = #{orderSn}
    </select>


    <!-- 查询条件（关联其它表） -->
    <sql id="whereConditionWithJoin">
        <if test="example != null">
            <trim prefix="WHERE" prefixOverrides="AND|OR">
                AND bo.enabled_flag =1
                <if test="example.orderIdNotEquals != null">
                    AND bo.order_id != #{example.orderIdNotEquals}
                </if>
                <if test="example.orderIdIn != null">
                    AND bo.order_id in (${example.orderIdIn})
                </if>
                <if test="example.orderSn != null">
                    AND bo.order_sn = #{example.orderSn}
                </if>
                <if test="example.orderSnLike != null">
                    AND bo.order_sn like concat('%',#{example.orderSnLike},'%')
                </if>
                <if test="example.paySn != null">
                    AND bo.pay_sn = #{example.paySn}
                </if>
                <if test="example.paySnLike != null">
                    AND bo.pay_sn like concat('%',#{example.paySnLike},'%')
                </if>
                <if test="example.parentSn != null">
                    AND bo.parent_sn = #{example.parentSn}
                </if>
                <if test="example.parentSnLike != null">
                    AND bo.parent_sn like concat('%',#{example.parentSnLike},'%')
                </if>
                <if test="example.storeId != null">
                    AND bo.store_id = #{example.storeId}
                </if>
                <if test="example.storeName != null">
                    AND bo.store_name = #{example.storeName}
                </if>
                <if test="example.storeNameLike != null">
                    AND bo.store_name like concat('%',#{example.storeNameLike},'%')
                </if>
                <if test="example.memberName != null">
                    AND bo.member_name = #{example.memberName}
                </if>
                <if test="example.memberNameLike != null">
                    AND bo.member_name like concat('%',#{example.memberNameLike},'%')
                </if>
                <if test="example.memberId != null">
                    AND bo.member_id = #{example.memberId}
                </if>
                <if test="example.payTimeAfter != null">
                    AND bo.pay_time <![CDATA[ >= ]]> #{example.payTimeAfter}
                </if>
                <if test="example.payTimeBefore != null">
                    AND bo.pay_time <![CDATA[ <= ]]> #{example.payTimeBefore}
                </if>
                <if test="example.payUpdateTimeAfter != null">
                    AND bo.pay_update_time <![CDATA[ >= ]]> #{example.payUpdateTimeAfter}
                </if>
                <if test="example.payTimeBefore != null">
                    AND bo.pay_update_time <![CDATA[ <= ]]> #{example.payUpdateTimeBefore}
                </if>
                <if test="example.createTimeAfter != null">
                    AND bo.create_time <![CDATA[ >= ]]> #{example.createTimeAfter}
                </if>
                <if test="example.createTimeBefore != null">
                    AND bo.create_time <![CDATA[ <= ]]> #{example.createTimeBefore}
                </if>
                <if test="example.finishTimeAfter != null">
                    AND bo.finish_time <![CDATA[ >= ]]> #{example.finishTimeAfter}
                </if>
                <if test="example.finishTimeBefore != null">
                    AND bo.finish_time <![CDATA[ <= ]]> #{example.finishTimeBefore}
                </if>
                <if test="example.orderState != null">
                    AND bo.order_state = #{example.orderState}
                </if>
                <if test="example.orderState != null and example.orderState == 20">
                    AND not exists (select 1 from bz_order_return bor where bor.order_sn = bo.order_sn and bor.state in
                    (100, 101, 102, 200, 201, 203))
                    AND bo.`is_delivery` = 1
                </if>
                <if test="example.loanPayState != null">
                    AND bo.loan_pay_state = #{example.loanPayState}
                </if>
                <if test="example.lendingSuccessTime != null">
                    AND bo.lending_success_time = #{example.lendingSuccessTime}
                </if>
                <if test="example.orderStateIn != null">
                    AND bo.order_state in (${example.orderStateIn})
                </if>
                <if test="example.orderStateNotIn != null">
                    AND bo.order_state not in (${example.orderStateNotIn})
                </if>
                <if test="example.orderStateIn != null and example.orderStateIn == '20,25' and (example.orderExchangeFlag == null or example.orderExchangeFlag != 1)">
                    AND not exists (select 1 from bz_order_return bor where bor.order_sn = bo.order_sn and bor.state in
                    (100, 101, 102, 200, 201, 203))
                    AND bo.`is_delivery` = 1
                    and not exists (select bop.order_sn from bz_order_product bop where bop.order_sn = bo.order_sn and
                    bop.delivery_state = 2)
                </if>
                <if test="example.orderExchangeFlag != null and example.orderExchangeFlag == 1">
                    AND bo.exchange_flag != 2
                </if>
                <if test="example.orderStateNotEquals != null">
                    AND bo.order_state != #{example.orderStateNotEquals}
                </if>
                <if test="example.paymentName != null">
                    AND bo.payment_name = #{example.paymentName}
                </if>
                <if test="example.paymentNameLike != null">
                    AND bo.payment_name like concat('%',#{example.paymentNameLike},'%')
                </if>
                <if test="example.paymentCode != null and example.paymentCode != 'CARD'">
                    AND bo.payment_code = #{example.paymentCode}
                </if>
                <if test="example.paymentCode != null and example.paymentCode == 'CARD'">
                    AND (bo.payment_code = #{example.paymentCode} or bo.compose_pay_name like '%乡助卡%')
                </if>
                <if test="example.goodsAmount != null">
                    AND bo.goods_amount = #{example.goodsAmount}
                </if>
                <if test="example.expressFee != null">
                    AND bo.express_fee = #{example.expressFee}
                </if>
                <if test="example.activityDiscountAmount != null">
                    AND bo.activity_discount_amount = #{example.activityDiscountAmount}
                </if>
                <if test="example.activityDiscountDetail != null">
                    AND bo.activity_discount_detail = #{example.activityDiscountDetail}
                </if>
                <if test="example.orderAmount != null">
                    AND bo.order_amount = #{example.orderAmount}
                </if>
                <if test="example.xzCardAmount != null">
                    AND bo.xz_card_amount = #{example.xzCardAmount}
                </if>
                <if test="example.xzCardExpressFeeAmount != null">
                    AND bo.xz_card_express_fee_amount = #{example.xzCardExpressFeeAmount}
                </if>
                <if test="example.composePayName != null">
                    AND bo.compose_pay_name = #{example.composePayName}
                </if>
                <if test="example.balanceAmount != null">
                    AND bo.balance_amount = #{example.balanceAmount}
                </if>
                <if test="example.payAmount != null">
                    AND bo.pay_amount = #{example.payAmount}
                </if>
                <if test="example.refundAmount != null">
                    AND bo.refund_amount = #{example.refundAmount}
                </if>
                <if test="example.integralCashAmount != null">
                    AND bo.integral_cash_amount = #{example.integralCashAmount}
                </if>
                <if test="example.integral != null">
                    AND bo.integral = #{example.integral}
                </if>
                <if test="example.delayDays != null">
                    AND bo.delay_days = #{example.delayDays}
                </if>
                <if test="example.evaluateState != null">
                    AND bo.evaluate_state = #{example.evaluateState}
                </if>
                <if test="example.evaluateStateIn != null">
                    AND bo.evaluate_state in (${example.evaluateStateIn})
                </if>
                <if test="example.evaluateStateNotIn != null">
                    AND bo.evaluate_state not in (${example.evaluateStateNotIn})
                </if>
                <if test="example.evaluateStateNotEquals != null">
                    AND bo.evaluate_state != #{example.evaluateStateNotEquals}
                </if>
                <if test="example.orderType != null">
                    AND bo.order_type = #{example.orderType}
                </if>
                <if test="example.orderTypeIn != null">
                    AND bo.order_type in (${example.orderTypeIn})
                </if>
                <if test="example.lockState != null">
                    AND bo.lock_state = #{example.lockState}
                </if>
                <if test="example.lockStateIn != null">
                    AND bo.lock_state in (${example.lockStateIn})
                </if>
                <if test="example.lockStateNotIn != null">
                    AND bo.lock_state not in (${example.lockStateNotIn})
                </if>
                <if test="example.orderTypeNotIn != null">
                    AND bo.order_type not in (${example.orderTypeNotIn})
                </if>
                <if test="example.lockStateNotEquals != null">
                    AND bo.lock_state != #{example.lockStateNotEquals}
                </if>
                <if test="example.deleteState != null">
                    AND bo.delete_state = #{example.deleteState}
                </if>
                <if test="example.deleteStateIn != null">
                    AND bo.delete_state in (${example.deleteStateIn})
                </if>
                <if test="example.deleteStateNotIn != null">
                    AND bo.delete_state not in (${example.deleteStateNotIn})
                </if>
                <if test="example.deleteStateNotEquals != null">
                    AND bo.delete_state != #{example.deleteStateNotEquals}
                </if>
                <if test="example.refuseReason != null">
                    AND bo.refuse_reason = #{example.refuseReason}
                </if>
                <if test="example.refuseReasonLike != null">
                    AND bo.refuse_reason like concat('%',#{example.refuseReasonLike},'%')
                </if>
                <if test="example.refuseRemark != null">
                    AND bo.refuse_remark = #{example.refuseRemark}
                </if>
                <if test="example.isSettlement != null">
                    AND bo.is_settlement = #{example.isSettlement}
                </if>
                <if test="example.financeRuleCode != null">
                    AND bo.finance_rule_code = #{example.financeRuleCode}
                </if>
                <if test="example.ruleTag != null">
                    AND bo.rule_tag = #{example.ruleTag}
                </if>
                <if test="example.orderReturnState != null and example.orderReturnState != 0 and example.orderReturnState != 2">
                    AND exists (select 1 from bz_order_product bopr where bopr.order_sn = bo.order_sn and bopr.status =
                    #{example.orderReturnState})
                </if>
                <if test="example.orderReturnState != null and example.orderReturnState == 2">
                    AND exists (select 1 from bz_order_product bopr where bopr.order_sn = bo.order_sn and (bopr.status =
                    2 or (bopr.status = 1 and bopr.return_number != bopr.product_num)))
                </if>
                <if test="example.channel != null">
                    AND bo.channel = #{example.channel}
                </if>
                <if test="example.recommendStoreName != null">
                    AND bo.recommend_store_name = #{example.recommendStoreName}
                </if>
                <if test="example.recommendStoreId != null">
                    AND bo.recommend_store_id = #{example.recommendStoreId}
                </if>
                <if test="example.paymentTag != null">
                    AND bo.payment_tag = #{example.paymentTag}
                </if>
                <if test="example.customerId != null">
                    AND boe.customer_id = #{example.customerId}
                </if>
                <if test="example.customerName != null">
                    AND boe.customer_name like concat(#{example.customerName},'%')
                </if>
                <if test="example.branchNameLike != null">
                    AND boe.branch_name like concat('%',#{example.branchNameLike},'%')
                </if>
                <if test="example.areaNameLike != null ">
                    AND boe.area_name like concat('%',#{example.areaNameLike},'%')
                </if>
                <if test="example.receiverNameLike != null and example.receiverNameLike != ''">
                    AND boe.receiver_name like concat('%',#{example.receiverNameLike},'%')
                </if>
                <if test="example.receiverMobile != null and example.receiverMobile != ''">
                    AND boe.receiver_mobile = #{example.receiverMobile}
                </if>
                <if test="example.userMobile != null and example.userMobile != ''">
                    AND bo.user_mobile = #{example.userMobile}
                </if>
                <if test="example.userNo != null and example.userNo != ''">
                    AND bo.user_no = #{example.userNo}
                </if>
                <if test="example.goodsNameLike != null">
                    AND EXISTS (select 1 from bz_order_product bop where bop.order_sn = bo.order_sn and bop.goods_name
                    like concat('%',#{example.goodsNameLike},'%'))
                </if>
                <if test="example.orderPattern != null">
                    AND bo.order_pattern = #{example.orderPattern}
                </if>
                <if test="example.newOrder != null">
                    AND bo.new_order = #{example.newOrder}
                </if>
                <if test="example.performanceService != null">
                    AND locate(#{example.performanceService}, bo.`performance_modes`)
                </if>
                <if test="example.performanceServiceNotIn != null">
                    AND not locate(#{example.performanceServiceNotIn}, bo.`performance_modes`)
                </if>
                <if test="example.performanceService == null and example.performanceMode != null">
                    AND EXISTS (select 1 from bz_order_product bop where bop.order_sn = bo.order_sn and
                    bop.performance_mode = #{example.performanceMode})
                </if>
                <if test="example.supplierCode != null">
                    AND EXISTS (select 1 from bz_order_product bop where bop.order_sn = bo.order_sn and
                    bop.supplier_code = #{example.supplierCode})
                </if>
                <if test="example.manager != null">
                    AND boe.manager = #{example.manager}
                </if>
                <if test="example.managerNameLike != null">
                    AND boe.manager_name like concat('%',#{example.managerNameLike},'%')
                </if>
                <if test="example.stationMaster != null">
                    AND boe.station_master = #{example.stationMaster}
                </if>
                <if test="example.stationMasterNameLike != null">
                    AND boe.station_master_name like concat('%',#{example.stationMasterNameLike},'%')
                </if>

                <if test="example.invoiceStatus != null">
                    AND boe.invoice_status = #{example.invoiceStatus}
                </if>

                <if test="example.branchCodeIn != null and example.branchCodeIn.size() > 0">
                    AND boe.branch in
                    <foreach collection="example.branchCodeIn" item="branchCode" open="(" separator="," close=")">
                        #{branchCode}
                    </foreach>
                </if>
                <if test="example.managerNameList != null and example.managerNameList.size() > 0">
                    AND boe.manager_name in
                    <foreach collection="example.managerNameList" item="managerName" open="(" separator="," close=")">
                        #{managerName}
                    </foreach>
                </if>
                <if test="example.goodsCategoryIdIn != null and example.goodsCategoryIdIn.size() > 0">
                    AND EXISTS (select 1 from bz_order_product bop where bop.order_sn = bo.order_sn
                    AND bop.goods_category_id in
                    <foreach collection="example.goodsCategoryIdIn" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="example.companyNameLike != null">
                    AND boe.company_name like concat('%',#{example.companyNameLike},'%')
                </if>

                <if test="example.settledType != null">
                    AND boe.settled_type = #{example.settledType}
                </if>

                <if test="example.storeBranchCodeIn != null and example.storeBranchCodeIn.size() > 0">
                    AND boe.store_branch in
                    <foreach collection="example.storeBranchCodeIn" item="storeBranchCode" open="(" separator=","
                             close=")">
                        #{storeBranchCode}
                    </foreach>
                </if>
                <if test="example.customerConfirmStatus != null">
                    AND bo.customer_confirm_status = #{example.customerConfirmStatus}
                </if>
                <if test="example.pointId != null">
                    AND boe.point_id = #{example.pointId}
                </if>
                <if test="example.receiveMaterialStatus != null">
                    AND boe.receive_material_status = #{example.receiveMaterialStatus}
                </if>
                <if test="example.performanceBranchCode != null">
                    AND EXISTS (select 1 from bz_order_performance_belongs bopb where bopb.order_sn = bo.order_sn and bopb.employee_branch_code = #{example.performanceBranchCode})
                </if>
                <if test="example.performanceBranchCodeList != null and example.performanceBranchCodeList.size() > 0">
                    AND EXISTS (select 1 from bz_order_performance_belongs bopb where bopb.order_sn = bo.order_sn and bopb.employee_branch_code in
                    <foreach collection="example.performanceBranchCodeList" item="branchCode" open="(" separator="," close=")">
                        #{branchCode}
                    </foreach>
                    )
                </if>
            </trim>
        </if>
        <if test="example == null">
            where bo.enabled_flag =1
        </if>
    </sql>

    <sql id="orderOfflineExtendWhereCondition">
        <if test="example != null">
            <if test="example.overdueFlag != null">
                AND booe.overdue_flag = #{example.overdueFlag}
            </if>
            <if test="example.overdueTimeAfter != null">
                AND booe.overdue_time <![CDATA[ >= ]]> #{example.overdueTimeAfter}
            </if>
            <if test="example.overdueTimeBefore != null">
                AND booe.overdue_time <![CDATA[ <= ]]> #{example.overdueTimeBefore}
            </if>
            <if test="example.buyerSupplierCode != null">
                AND booe.buyer_supplier_code = #{example.buyerSupplierCode}
            </if>
        </if>

    </sql>

    <update id="updateData">
        update ${tableName}
        set ${columnName} = ${columnValue}
        where
        ${idColumn} = ${idValue}
    </update>

    <select id="selectUpdateData" resultType="java.util.HashMap">
        select *
        from ${tableName}
        where
        ${idColumn} = ${idValue}
    </select>

    <select id="selectClosedOrderParentSn" parameterType="java.lang.String"
            resultType="java.lang.String">
        SELECT DISTINCT(bo.parent_sn) AS parent_sn FROM bz_order bo
        WHERE bo.parent_sn IN(
        <foreach collection="parentSns" item="parentSn" separator=",">
            #{parentSn}
        </foreach>
        )
        AND
        NOT EXISTS(SELECT parent_sn, order_state FROM(
        SELECT t.parent_sn,t.order_state FROM bz_order t
        JOIN(
        SELECT parent_sn FROM bz_order bo
        WHERE bo.parent_sn IN(
        <foreach collection="parentSns" item="parentSn" separator=",">
            #{parentSn}
        </foreach>
        )
        AND bo.order_state NOT IN(0, 50) GROUP BY parent_sn) AS temp
        ON temp.parent_sn= t.parent_sn
        WHERE t.parent_sn IN(
        <foreach collection="parentSns" item="parentSn" separator=",">
            #{parentSn}
        </foreach>
        )
        ) AS parent_order
        WHERE parent_order.parent_sn= bo.parent_sn)
    </select>


    <select id="getDepositRemindCount"
            resultType="java.lang.Integer">
        select count(1)
        from bz_order_presell bop
        left join bz_order bord on bord.order_sn = bop.order_sn
        where bop.dead_time >= now()
        <![CDATA[
            and bop.dead_time <= date_add(now(), INTERVAL + #{remindDays} DAY)
            ]]>
        <if test="storeId != null and storeId !=''  ">
            and bord.store_id = #{storeId}
        </if>
        and bop.type = 2
        and bop.pay_status = 1
        and bord.order_state = 10
    </select>

    <select id="countByStatus" resultType="java.util.Map">
        select order_state AS orderState,count(1) AS num,user_no AS userNo from bz_order where member_id = #{memberId}
        and exchange_flag != 2
        group by order_state;
    </select>

    <select id="countStatusByStationMaster" resultType="java.util.Map">
        select order_state AS orderState,
        count(1) AS num,
        station_master AS stationMaster
        from bz_order t
        join bz_order_extend e on t.order_sn = e.order_sn
        where e.station_master = #{stationMaster}
        and t.exchange_flag != 2
        group by order_state
    </select>


    <select id="expressNumberIsUsed" resultType="java.lang.String">
        select t.order_sn
        from bz_order t
        where t.express_number = #{expressNumber}
        and t.order_state >= 30
    </select>

    <select id="expressNumberUseCount" resultType="java.lang.String">
        select t.order_sn
        from bz_order t
        where t.express_number = #{expressNumber}
    </select>


    <select id="batchExpressNumberUseCount" resultType="com.cfpamf.ms.mallorder.dto.ExpressNumberDTO">
        select * from (
        select t.express_number expressNumber,count(distinct t.order_sn) expressNoFinishedUsedCount
        from bz_order t
        <if test="expressNumber != null and expressNumber != ''">
            where t.express_number in
            <foreach collection="expressNumberList" item="expressNumber" open="(" separator="," close=")">
                #{expressNumber}
            </foreach>
        </if>
        ) a where a.expressUsedCount >= 3
    </select>

    <select id="batchExpressNumberIsUsed" resultType="com.cfpamf.ms.mallorder.dto.ExpressNumberDTO">
        select * from (
        select t.express_number expressNumber,count(1) expressUsedCount
        from bz_order t
        where t.order_state >= 30
        <if test="expressNumber != null and expressNumber != ''">
            and t.express_number in
            <foreach collection="expressNumberList" item="expressNumber" open="(" separator="," close=")">
                #{expressNumber}
            </foreach>
        </if>
        ) a where a.expressNoFinishedUsedCount > 0
    </select>

    <select id="getInstallOrderCountByUserNo" resultType="java.lang.Integer">
        select count(1)
        from (
        select t.order_sn
        from bz_order t
        where t.user_no = #{userNo} and t.order_state in (20,25)
        ) a,bz_order_product p
        where a.order_sn = p.order_sn and p.performance_mode = 2
    </select>

    <select id="orderSaleInfo" resultType="com.cfpamf.ms.mallorder.dto.OrderSaleInfoDTO">
        select COUNT(*) totalNum, ifnull(sum(`order_amount`),0) totalAmount
        from `bz_order`
        WHERE `order_state` in
        <foreach collection="orderStateList" item="status" open="(" separator="," close=")">
            #{status}
        </foreach>
        and `create_time` >= #{createTimeAfter}
        and `create_time` &lt;= #{createTimeBefore}
    </select>

    <select id="storeSaleRank" resultType="com.cfpamf.ms.mallorder.dto.OrderSaleInfoDTO">
        select store_name,
        ifnull(sum(`order_amount` - `refund_amount`), 0) totalAmount
        from `bz_order`
        WHERE `order_state` in
        <foreach collection="orderStateList" item="status" open="(" separator="," close=")">
            #{status}
        </foreach>
        and `create_time` >= #{createTimeAfter}
        and `create_time` &lt;= #{createTimeBefore}
        GROUP BY store_name
        order by totalAmount desc
        limit 20;
    </select>

    <select id="goodsSaleRank" resultType="com.cfpamf.ms.mallorder.dto.OrderSaleInfoDTO">
        select bop.`goods_name`,
        ifnull(sum(bop.`product_num`- bop.`return_number`), 0) totalNum
        from `bz_order` bo, `bz_order_product` bop
        WHERE bo.`order_sn`= bop.`order_sn`
        and bo.`order_state` in
        <foreach collection="orderStateList" item="status" open="(" separator="," close=")">
            #{status}
        </foreach>
        and bo.`create_time` >= #{createTimeAfter}
        and bo.`create_time` &lt;= #{createTimeBefore}
        GROUP BY bop.goods_name
        order by totalNum desc
        limit 20;
    </select>

    <select id="getProductList" resultType="java.util.Map">
        SELECT order_sn orderSn,count(1) num, GROUP_CONCAT(goods_name) goodsName,GROUP_CONCAT(product_id) productId
        FROM bz_order_product
        WHERE order_sn in
        <foreach collection="orderSnList" separator="," item="orderSn" open="(" close=")">
            #{orderSn}
        </foreach>
        GROUP BY order_sn
    </select>

    <select id="getOrderExportListByPage" resultType="com.cfpamf.ms.mallorder.dto.OrderExportDTO">
        SELECT o.order_sn,
        o.pay_sn,
        o.bank_pay_trx_no,
        o.order_state,
        o.order_amount,
        o.order_commission,
        o.business_commission,
        o.express_fee,
        o.member_id,
        o.member_name,
        o.store_id,
        o.store_name,
        o.goods_amount+ o.express_fee+ o.xz_card_express_fee_amount AS orderNeedAmount,
        o.recommend_store_id,
        o.recommend_store_name,
        o.create_time,
        o.pay_time,
        o.deliver_time,
        o.payment_name,
        o.finish_time,
        o.compose_pay_name,
        p.xz_card_amount,
        o.xz_card_express_fee_amount,
        e.order_remark,
        e.receiver_name,
        e.receiver_mobile,
        e.receiver_province_code,
        e.receiver_city_code,
        e.receiver_district_code,
        e.receiver_town_code,
        e.receiver_address,
        e.invoice_info,
        p.order_product_id,
        p.goods_id,
        p.distribute_parent,
        p.goods_name,
        p.goods_parameter,
        p.spec_values,
        p.product_num,
        p.weight,
        p.sku_material_code skuMaterialCode,
        p.sku_material_name skuMaterialName,
        p.product_num - p.return_number as validNum,
        p.cost,
        p.landing_price,
        p.tax_rate,
        p.product_show_price,
        p.product_show_price * p.product_num as productShouldPay,
        o.activity_discount_amount,
        p.activity_discount_amount AS goodsActivityDiscountAmount,
        p.money_amount,
        p.store_activity_amount+ p.store_voucher_amount as storeDiscountAmount,
        p.platform_voucher_amount+ p.platform_activity_amount as platformDiscountAmount,
        p.integral_cash_amount,
        o.channel,
        o.order_type,
        e.customer_id,
        e.customer_name,
        e.category_code,
        e.category_name,
        e.branch,
        e.branch_name,
        e.zone_code,
        e.zone_name,
        e.area_code,
        e.area_name,
        e.manager,
        e.manager_name,
        e.user_code,
        p.product_id,
        p.spu_out_id,
        p.product_code,
        p.bar_code,
        p.supplier_name AS supplierName,
        case when p.return_number!= 0 then '是' else '否' end returnState,
        p.status productStatus,
        l.express_id expressId,
        l.express_name expressName,
        l.express_company_code expressCode,
        l.express_number expressNumber,
        l.deliver_type deliverType,
        l.deliver_name deliverName,
        l.deliver_mobile deliverMobile,
        l.create_time productDeliverTime,
        p.delivery_state deliverState,
        case when(not exists(
        select 1 from bz_order_return bor where bor.order_sn= o.order_sn
        and bor.state in (100, 101, 102, 200, 201, 203))
        AND o.`is_delivery`= 1
        and o.order_state in (20, 25)
        and p.delivery_state= 0) then '是' else '否' end deliverable,
        p.goods_category_path,
        bbp.loan_org_no payOrg,
        bop.trade_sn,
        o.order_pattern,
        e.deliver_place,
        e.estimate_express_fee,
        p.deposit,
        p.balance,
        e.delivery_requirements,
        p.performance_mode performanceMode,
        IFNULL(p.product_effective_price, 0) productEffectivePrice,
        o.finance_rule_code,
        o.rule_tag,
        o.customer_confirm_status,
        o.customer_confirm_status_desc,
        p.batch_no batchNo,
        p.product_spec_json
        FROM bz_order_product p

        JOIN(
        SELECT order_product_id
        FROM bz_order_product p JOIN bz_order o ON o.order_sn= p.order_sn
        LEFT JOIN bz_order_extend e ON p.order_sn= e.order_sn
        left join bz_order_logistic l on p.logistic_id= l.logistic_id
        LEFT JOIN bz_order_pay bop ON bop.pay_sn= o.pay_sn
        LEFT JOIN bz_bank_pay bbp ON o.order_sn= bbp.order_sn
        <include refid="exportWhereCondition"/>
        order by p.order_product_id
        LIMIT #{example.pager.start},#{example.pager.pageSize}
        ) temp_bo ON p.order_product_id= temp_bo.order_product_id

        LEFT JOIN bz_order o ON o.order_sn= p.order_sn
        LEFT JOIN bz_order_extend e ON o.order_sn= e.order_sn
        left join bz_order_logistic l on p.logistic_id= l.logistic_id
        LEFT JOIN bz_order_pay bop ON bop.pay_sn= o.pay_sn
        LEFT JOIN bz_bank_pay bbp ON o.order_sn= bbp.order_sn
    </select>

    <select id="countOrderExportList" resultType="java.lang.Integer">
        SELECT count(*) from
        (
        select o.order_id
        from bz_order o
        <include refid="exportOrderCondition"/>
        ) pp
        join bz_order o on pp.order_id = o.order_id
        LEFT JOIN bz_order_extend e ON o.order_sn = e.order_sn
        LEFT JOIN bz_order_product p ON o.order_sn = p.order_sn
        left join bz_order_logistic l on p.logistic_id = l.logistic_id
        LEFT JOIN bz_order_pay bop ON bop.pay_sn = o.pay_sn
        LEFT JOIN bz_bank_pay bbp ON o.order_sn = bbp.order_sn
        LEFT JOIN bz_order_performance_belongs pb ON o.order_sn = pb.order_sn
        <include refid="exportWhereCondition"/>
    </select>


    <resultMap id="orderExportDTO" type="com.cfpamf.ms.mallorder.dto.OrderExportDTO">
        <result column="order_id" property="orderId"/>
        <result column="orderSn" property="orderSn"/>
        <result column="promotion_id" property="promotionId"/>
        <result column="pay_sn" property="paySn"/>
        <result column="bank_pay_trx_no" property="bankPayTrxNo"/>
        <result column="order_amount" property="orderAmount"/>
        <result column="order_commission" property="orderCommission"/>
        <result column="productOrderCommission" property="productOrderCommission"/>
        <result column="business_commission" property="businessCommission"/>
        <result column="express_fee" property="expressFee"/>
        <result column="member_id" property="memberId"/>
        <result column="member_name" property="memberName"/>
        <result column="store_id" property="storeId"/>
        <result column="store_name" property="storeName"/>
        <result column="orderNeedAmount" property="orderNeedAmount"/>
        <result column="recommend_store_id" property="recommendStoreId"/>
        <result column="recommend_store_name" property="recommendStoreName"/>
        <result column="create_time" property="createTime"/>
        <result column="pay_time" property="payTime"/>
        <result column="payment_name" property="paymentName"/>
        <result column="finish_time" property="finishTime"/>
        <result column="compose_pay_name" property="composePayName"/>
        <result column="xz_card_amount" property="xzCardAmount"/>
        <result column="xz_card_express_fee_amount" property="xzCardExpressFeeAmount"/>
        <result column="order_remark" property="orderRemark"/>
        <result column="receiver_name" property="receiverName"/>
        <result column="receiver_mobile" property="receiverMobile"/>
        <result column="receiver_province_code" property="receiverProvinceCode"/>
        <result column="receiver_city_code" property="receiverCityCode"/>
        <result column="receiver_district_code" property="receiverDistrictCode"/>
        <result column="receiver_town_code" property="receiverTownCode"/>
        <result column="receiver_address" property="receiverAddress"/>
        <result column="invoice_info" property="invoiceInfo"/>
        <result column="orderProductId" property="orderProductId"/>
        <result column="distribute_parent" property="distributeParent"/>
        <result column="goods_name" property="goodsName"/>
        <result column="goods_parameter" property="goodsParameter"/>
        <result column="spec_values" property="specValues"/>
        <result column="product_num" property="productNum"/>
        <result column="weight" property="weight"/>
        <result column="skuMaterialCode" property="skuMaterialCode"/>
        <result column="skuMaterialName" property="skuMaterialName"/>
        <result column="validNum" property="validNum"/>
        <result column="cost" property="cost"/>
        <result column="landing_price" property="landingPrice"/>
        <result column="tax_rate" property="taxRate"/>
        <result column="product_show_price" property="productShowPrice"/>
        <result column="productShouldPay" property="productShouldPay"/>
        <result column="activity_discount_amount" property="activityDiscountAmount"/>
        <result column="goodsActivityDiscountAmount" property="goodsActivityDiscountAmount"/>
        <result column="money_amount" property="moneyAmount"/>
        <result column="storeDiscountAmount" property="storeDiscountAmount"/>
        <result column="platformDiscountAmount" property="platformDiscountAmount"/>
        <result column="integral_cash_amount" property="integralCashAmount"/>
        <result column="ret_money_model" property="retMoneyModel"/>
        <result column="channel" property="channel"/>
        <result column="order_type" property="orderType"/>
        <result column="customer_id" property="customerId"/>
        <result column="customer_name" property="customerName"/>
        <result column="category_code" property="categoryCode"/>
        <result column="category_name" property="categoryName"/>
        <result column="branch" property="branch"/>
        <result column="branch_name" property="branchName"/>
        <result column="zone_code" property="zoneCode"/>
        <result column="zone_name" property="zoneName"/>
        <result column="area_code" property="areaCode"/>
        <result column="manager" property="manager"/>
        <result column="manager_name" property="managerName"/>
        <result column="product_id" property="productId"/>
        <result column="spu_out_id" property="spuOutId"/>
        <result column="product_code" property="productCode"/>
        <result column="bar_code" property="barCode"/>
        <result column="supplierName" property="supplierName"/>
        <result column="returnState" property="returnState"/>
        <result column="productStatus" property="productStatus"/>
        <result column="deliverState" property="deliverState"/>
        <result column="deliverable" property="deliverable"/>
        <result column="goods_category_path" property="goodsCategoryPath"/>
        <result column="payOrg" property="payOrg"/>
        <result column="trade_sn" property="tradeSn"/>
        <result column="order_pattern" property="orderPattern"/>
        <result column="deliver_place" property="deliverPlace"/>
        <result column="estimate_express_fee" property="estimateExpressFee"/>
        <result column="deposit" property="deposit"/>
        <result column="balance" property="balance"/>
        <result column="delivery_requirements" property="deliveryRequirements"/>
        <result column="performanceMode" property="performanceMode"/>
        <result column="performance_channel" property="performanceChannel"/>
        <result column="performance_service" property="performanceService"/>
        <result column="productEffectivePrice" property="productEffectivePrice"/>
        <result column="finance_rule_code" property="financeRuleCode"/>
        <result column="rule_tag" property="ruleTag"/>
        <result column="customer_confirm_status" property="customerConfirmStatus"/>
        <result column="customer_confirm_status_desc" property="customerConfirmStatusDesc"/>
        <result column="batchNo" property="batchNo"/>
        <result column="product_spec_json" property="productSpecJson"/>
        <result column="delivery_num" property="deliveryNum"/>
        <result column="kingdee_stock_push_mode" property="kingdeeStockPushMode"/>
        <result column="point_id" property="pointId"/>
        <result column="point_name" property="pointName"/>
        <collection property="orderDeliveryPackageDTOList"
                    ofType="com.cfpamf.ms.mallorder.dto.OrderDeliveryPackageDTO"
                    column="{orderProductId=orderProductId}"
                    select="com.cfpamf.ms.mallorder.mapper.OrderLogisticMapper.getExportOrderLogisticListByOrderProductId">

            <result column="create_time" property="createTime"/>
            <result column="express_name" property="expressName"/>
            <result column="express_company_code" property="expressCompanyCode"/>
            <result column="express_number" property="expressNumber"/>
            <result column="deliver_type" property="deliverType"/>
            <result column="deliver_name" property="deliverName"/>
            <result column="deliver_mobile" property="deliverMobile"/>
            <result column="deliver_warehouse" property="deliverWarehouse"/>
            <result column="deliver_warehouse_name" property="deliverWarehouseName"/>
        </collection>
    </resultMap>


    <select id="getOrderExportListByPageNew" resultMap="orderExportDTO">
        select
        o.order_id,
        o.order_sn orderSn,
        o.promotion_id,
        o.pay_sn,
        o.bank_pay_trx_no,
        o.order_state,
        o.order_amount,
        o.order_commission,
        p.order_commission AS productOrderCommission,
        o.business_commission,
        o.express_fee,
        o.member_id,
        o.member_name,
        o.store_id,
        o.store_name,
        o.goods_amount+ o.express_fee+ o.xz_card_express_fee_amount AS orderNeedAmount,
        o.recommend_store_id,
        o.recommend_store_name,
        o.create_time,
        o.pay_time,
        o.payment_name,
        o.finish_time,
        o.compose_pay_name,
        p.xz_card_amount,
        o.xz_card_express_fee_amount,
        e.order_remark,
        e.receiver_name,
        e.receiver_mobile,
        e.receiver_province_code,
        e.receiver_city_code,
        e.receiver_district_code,
        e.receiver_town_code,
        e.receiver_address,
        e.invoice_info,
        p.order_product_id orderProductId,
        p.goods_id,
        p.distribute_parent,
        p.goods_name,
        p.goods_parameter,
        p.spec_values,
        p.product_num,
        p.weight,
        p.sku_material_code skuMaterialCode,
        p.sku_material_name skuMaterialName,
        p.product_num - p.return_number as validNum,
        p.cost,
        p.landing_price,
        p.tax_rate,
        p.product_show_price,
        p.product_show_price * p.product_num as productShouldPay,
        o.activity_discount_amount,
        p.activity_discount_amount AS goodsActivityDiscountAmount,
        p.money_amount,
        p.store_activity_amount+ p.store_voucher_amount as storeDiscountAmount,
        p.platform_voucher_amount+ p.platform_activity_amount as platformDiscountAmount,
        p.integral_cash_amount,
        p.ret_money_model,
        o.channel,
        o.order_type,
        e.customer_id,
        e.customer_name,
        e.category_code,
        e.category_name,
        e.branch,
        e.branch_name,
        e.zone_code,
        e.zone_name,
        e.area_code,
        e.area_name,
        e.manager,
        e.manager_name,
        e.user_code,
        p.product_id,
        p.spu_out_id,
        p.product_code,
        p.bar_code,
        p.supplier_name AS supplierName,
        case when p.return_number!= 0 then '是' else '否' end returnState,
        p.status productStatus,
        <!--l.create_time deliverTime,
        l.express_id expressId,
        l.express_name expressName,
        l.express_company_code expressCode,
        l.express_number expressNumber,
        l.deliver_type deliverType,
        l.deliver_name deliverName,
        l.deliver_mobile deliverMobile,
        l.create_time productDeliverTime,-->
        p.delivery_state deliverState,
        case when(not exists(
        select 1 from bz_order_return bor where bor.order_sn= o.order_sn
        and bor.state in (100, 101, 102, 200, 201, 203))
        AND o.`is_delivery`= 1
        and o.order_state in (20, 25)
        and p.delivery_state= 0) then '是' else '否' end deliverable,
        p.goods_category_path,
        bbp.loan_org_no payOrg,
        bop.trade_sn,
        o.order_pattern,
        e.deliver_place,
        e.estimate_express_fee,
        p.deposit,
        p.balance,
        e.delivery_requirements,
        p.performance_mode performanceMode,
        p.performance_channel,
        p.performance_service,
        IFNULL(p.product_effective_price, 0) productEffectivePrice,
        o.finance_rule_code,
        o.rule_tag,
        o.customer_confirm_status,
        o.customer_confirm_status_desc,
        p.batch_no batchNo,
        p.product_spec_json,
        p.delivery_num,
        e.kingdee_stock_push_mode,
        pae.sales_season_code,
        pfbl.belonger_name,
        pfbl.belonger_employee_no,
        pfbl.employee_branch_code,
        pfbl.employee_branch_name,
        pe.product_category_path,
        pe.product_category_path_name,
        bope.funder,
        bope.retail_price,
        e.point_id,
        e.point_name,
        p.channel_new_sku_id
        from
        (
        select o.order_id
        from bz_order o
        <include refid="exportOrderCondition"/>

        ) pp join bz_order o on pp.order_id = o.order_id
        left join bz_order_product p on o.order_sn = p.order_sn
        left join bz_order_extend e on o.order_sn = e.order_sn
        <!--
                left join bz_order_logistic l on p.logistic_id= l.logistic_id
        -->
        left join bz_order_pay bop on bop.pay_sn = o.pay_sn
        left join bz_bank_pay bbp on o.order_sn = bbp.order_sn
        left join bz_order_product_agric_extend pae on p.order_product_id = pae.order_product_id
        left join bz_order_performance_belongs pfbl on o.order_sn = pfbl.order_sn
        left join bz_order_product_erp_extend pe on o.order_sn = pe.order_sn and p.order_product_id =
        pe.order_product_id
        left join bz_order_product_extend bope on o.order_sn = bope.order_sn and p.order_product_id =
        bope.order_product_id
        <include refid="exportWhereConditionNew"/>
        <if test="example.pager != null">
            LIMIT #{example.pager.start},#{example.pager.pageSize}
        </if>

    </select>

    <select id="getSelfLiftOrdersToDelivery" resultType="java.lang.String">
        select bo.order_sn
        from bz_order bo
        join bz_order_performance_belongs bl
        on bo.order_sn = bl.order_sn
        where bo.order_state = 20
        and bo.order_pattern = 6
        and bl.bind_state_code in (1,2)
        and bl.effect_time &lt; DATE_SUB(NOW(), INTERVAL 10 MINUTE)
        and bo.store_id in
        <foreach collection="autoDeliveryStoreIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="getMissingLogisticOrder" resultType="java.lang.String">
        SELECT bo.`order_sn`
        FROM `bz_order` bo, `bz_order_product` bop
        WHERE bo.`order_sn` = bop.`order_sn` and bo.`order_state` in(30,40)
        and bop.`logistic_id` = 0 and bo.`order_pattern` = 6
    </select>

    <select id="getMissingHistoryLogisticOrder" resultType="java.lang.String">
        select t.order_sn
        from bz_order t,bz_order_product p
        where t.order_sn = p.order_sn
        and t.order_state in (30,40)
        and p.logistic_id = 0
        and t.order_pattern != 6
        and t.create_time > '2023-01-01'
    </select>

    <sql id="exportOrderCondition">
        <if test="example != null">
            <trim prefix="WHERE" prefixOverrides="AND|OR">
                AND o.enabled_flag = 1
                <if test="example.orderId != null">
                    AND o.`order_id` > #{example.orderId}
                </if>
                <if test="example.orderIdNotEquals != null">
                    AND o.`order_id` != #{example.orderIdNotEquals}
                </if>
                <if test="example.orderIdIn != null">
                    AND o.`order_id` in (${example.orderIdIn})
                </if>
                <if test="example.orderSn != null">
                    AND o.`order_sn` = #{example.orderSn}
                </if>
                <if test="example.orderSnLike != null">
                    AND o.`order_sn` like concat('%',#{example.orderSnLike},'%')
                </if>
                <if test="example.paySn != null">
                    AND o.`pay_sn` = #{example.paySn}
                </if>
                <if test="example.paySnLike != null">
                    AND o.`pay_sn` like concat('%',#{example.paySnLike},'%')
                </if>
                <if test="example.parentSn != null">
                    AND o.`parent_sn` = #{example.parentSn}
                </if>
                <if test="example.parentSnLike != null">
                    AND o.`parent_sn` like concat('%',#{example.parentSnLike},'%')
                </if>
                <if test="example.storeId != null">
                    AND o.`store_id` = #{example.storeId}
                </if>
                <if test="example.storeName != null">
                    AND o.`store_name` = #{example.storeName}
                </if>
                <if test="example.storeNameLike != null">
                    AND o.`store_name` like concat('%',#{example.storeNameLike},'%')
                </if>
                <if test="example.memberName != null">
                    AND o.`member_name` = #{example.memberName}
                </if>
                <if test="example.memberNameLike != null">
                    AND o.`member_name` like concat('%',#{example.memberNameLike},'%')
                </if>
                <if test="example.memberId != null">
                    AND o.`member_id` = #{example.memberId}
                </if>
                <if test="example.payTimeAfter != null">
                    AND o.`pay_time` <![CDATA[ >= ]]> #{example.payTimeAfter}
                </if>
                <if test="example.payTimeBefore != null">
                    AND o.`pay_time` <![CDATA[ <= ]]> #{example.payTimeBefore}
                </if>
                <if test="example.payUpdateTimeAfter != null">
                    AND o.`pay_update_time` <![CDATA[ >= ]]> #{example.payUpdateTimeAfter}
                </if>
                <if test="example.payUpdateTimeBefore != null">
                    AND o.`pay_update_time` <![CDATA[ <= ]]> #{example.payUpdateTimeBefore}
                </if>
                <if test="example.createTimeAfter != null">
                    AND o.`create_time` <![CDATA[ >= ]]> #{example.createTimeAfter}
                </if>
                <if test="example.createTimeBefore != null">
                    AND o.`create_time` <![CDATA[ <= ]]> #{example.createTimeBefore}
                </if>
                <if test="example.finishTimeAfter != null">
                    AND o.`finish_time` <![CDATA[ >= ]]> #{example.finishTimeAfter}
                </if>
                <if test="example.finishTimeBefore != null">
                    AND o.`finish_time` <![CDATA[ <= ]]> #{example.finishTimeBefore}
                </if>
                <if test="example.orderState != null">
                    AND o.`order_state` = #{example.orderState}
                </if>
                <if test="example.orderState != null and example.orderState == 20">
                    AND not exists (select 1 from bz_order_return bor where bor.order_sn = o.order_sn and bor.state in
                    (100, 101, 102, 200, 201, 203))
                    AND o.`is_delivery` = 1
                </if>
                <if test="example.orderStateIn != null">
                    AND o.`order_state` in (${example.orderStateIn})
                </if>
                <if test="example.orderStateIn != null and example.orderStateIn == '20,25'">
                    AND not exists (select 1 from bz_order_return bor where bor.order_sn = o.order_sn and bor.state in
                    (100, 101, 102, 200, 201, 203))
                    AND o.`is_delivery` = 1
                    and not exists (select bop.order_sn from bz_order_product bop where bop.order_sn = o.order_sn and
                    bop.delivery_state = 2)
                </if>
                <if test="example.orderStateNotIn != null">
                    AND o.`order_state` not in (${example.orderStateNotIn})
                </if>
                <if test="example.orderStateNotEquals != null">
                    AND o.`order_state` != #{example.orderStateNotEquals}
                </if>
                <if test="example.paymentName != null">
                    AND o.`payment_name` = #{example.paymentName}
                </if>
                <if test="example.paymentNameLike != null">
                    AND o.`payment_name` like concat('%',#{example.paymentNameLike},'%')
                </if>
                <if test="example.paymentCode != null and example.paymentCode != 'CARD'">
                    AND o.`payment_code` = #{example.paymentCode}
                </if>
                <if test="example.paymentCode != null and example.paymentCode == 'CARD'">
                    AND (o.`payment_code` = #{example.paymentCode} or o.`compose_pay_name` like '%乡助卡%')
                </if>
                <if test="example.goodsAmount != null">
                    AND o.`goods_amount` = #{example.goodsAmount}
                </if>
                <if test="example.expressFee != null">
                    AND o.`express_fee` = #{example.expressFee}
                </if>
                <if test="example.activityDiscountAmount != null">
                    AND o.`activity_discount_amount` = #{example.activityDiscountAmount}
                </if>
                <if test="example.activityDiscountDetail != null">
                    AND o.`activity_discount_detail` = #{example.activityDiscountDetail}
                </if>
                <if test="example.orderAmount != null">
                    AND o.`order_amount` = #{example.orderAmount}
                </if>
                <if test="example.xzCardAmount != null">
                    AND o.`xz_card_amount` = #{example.xzCardAmount}
                </if>
                <if test="example.xzCardExpressFeeAmount != null">
                    AND o.`xz_card_express_fee_amount` = #{example.xzCardExpressFeeAmount}
                </if>
                <if test="example.composePayName != null">
                    AND o.`compose_pay_name` = #{example.composePayName}
                </if>
                <if test="example.balanceAmount != null">
                    AND o.`balance_amount` = #{example.balanceAmount}
                </if>
                <if test="example.payAmount != null">
                    AND o.`pay_amount` = #{example.payAmount}
                </if>
                <if test="example.refundAmount != null">
                    AND o.`refund_amount` = #{example.refundAmount}
                </if>
                <if test="example.integralCashAmount != null">
                    AND o.`integral_cash_amount` = #{example.integralCashAmount}
                </if>
                <if test="example.integral != null">
                    AND o.`integral` = #{example.integral}
                </if>
                <if test="example.delayDays != null">
                    AND o.`delay_days` = #{example.delayDays}
                </if>
                <if test="example.evaluateState != null">
                    AND o.`evaluate_state` = #{example.evaluateState}
                </if>
                <if test="example.evaluateStateIn != null">
                    AND o.`evaluate_state` in (${example.evaluateStateIn})
                </if>
                <if test="example.evaluateStateNotIn != null">
                    AND o.`evaluate_state` not in (${example.evaluateStateNotIn})
                </if>
                <if test="example.evaluateStateNotEquals != null">
                    AND o.`evaluate_state` != #{example.evaluateStateNotEquals}
                </if>
                <if test="example.orderTypeIn != null">
                    AND o.`order_type` in (${example.orderTypeIn})
                </if>
                <if test="example.orderType != null">
                    AND o.`order_type` = #{example.orderType}
                </if>
                <if test="example.lockState != null">
                    AND o.`lock_state` = #{example.lockState}
                </if>
                <if test="example.lockStateIn != null">
                    AND o.`lock_state` in (${example.lockStateIn})
                </if>
                <if test="example.lockStateNotIn != null">
                    AND o.`lock_state` not in (${example.lockStateNotIn})
                </if>
                <if test="example.lockStateNotEquals != null">
                    AND o.`lock_state` != #{example.lockStateNotEquals}
                </if>
                <if test="example.deleteState != null">
                    AND o.`delete_state` = #{example.deleteState}
                </if>
                <if test="example.deleteStateIn != null">
                    AND o.`delete_state` in (${example.deleteStateIn})
                </if>
                <if test="example.deleteStateNotIn != null">
                    AND o.`delete_state` not in (${example.deleteStateNotIn})
                </if>
                <if test="example.deleteStateNotEquals != null">
                    AND o.`delete_state` != #{example.deleteStateNotEquals}
                </if>
                <if test="example.refuseReason != null">
                    AND o.`refuse_reason` = #{example.refuseReason}
                </if>
                <if test="example.refuseReasonLike != null">
                    AND o.`refuse_reason` like concat('%',#{example.refuseReasonLike},'%')
                </if>
                <if test="example.refuseRemark != null">
                    AND o.`refuse_remark` = #{example.refuseRemark}
                </if>
                <if test="example.isSettlement != null">
                    AND o.`is_settlement` = #{example.isSettlement}
                </if>
                <if test="example.orderReturnState != null and example.orderReturnState != 0 and example.orderReturnState != 2">
                    AND exists (select 1 from bz_order_product bopr where bopr.order_sn = o.order_sn and bopr.status =
                    #{example.orderReturnState})
                </if>
                <if test="example.orderReturnState != null and example.orderReturnState == 2">
                    AND exists (select 1 from bz_order_product bopr where bopr.order_sn = o.order_sn and (bopr.status =
                    2 or (bopr.status = 1 and bopr.return_number != bopr.product_num)))
                </if>
                <if test="example.channel != null">
                    AND o.`channel` = #{example.channel}
                </if>
                <if test="example.orderPattern != null">
                    AND o.`order_pattern` = #{example.orderPattern}
                </if>
                <if test="example.recommendStoreId != null">
                    AND o.recommend_store_id = #{example.recommendStoreId}
                </if>

                <if test="example.userMobile != null and example.userMobile != ''">
                    AND o.user_mobile = #{example.userMobile}
                </if>
                <if test="example.newOrder != null">
                    AND o.new_order = #{example.newOrder}
                </if>
                <if test="example.customerConfirmStatus != null">
                    AND o.customer_confirm_status = #{example.customerConfirmStatus}
                </if>
                <if test="example.performanceService != null">
                    AND locate(#{example.performanceService}, o.`performance_modes`)
                </if>
                <if test="example.performanceServiceNotIn != null">
                    AND not locate(#{example.performanceServiceNotIn}, o.`performance_modes`)
                </if>
            </trim>
        </if>
    </sql>
    <sql id="exportWhereConditionNew">
        <if test="example != null">
            <trim prefix="WHERE" prefixOverrides="AND|OR">

                <if test="example.goodsNameLike != null">
                    AND p.goods_name like concat('%',#{example.goodsNameLike},'%')
                </if>
                <if test="example.supplierCode != null">
                    AND p.supplier_code = #{example.supplierCode}
                </if>
                <if test="example.customerId != null">
                    AND e.customer_id = #{example.customerId}
                </if>
                <if test="example.customerName != null">
                    AND e.customer_name like concat('%',#{example.customerName},'%')
                </if>
                <if test="example.branchNameLike != null">
                    AND e.branch_name like concat('%',#{example.branchNameLike},'%')
                </if>
                <if test="example.areaNameLike != null">
                    AND e.area_name like concat('%',#{example.areaNameLike},'%')
                </if>
                <if test="example.receiverNameLike != null and example.receiverNameLike != ''">
                    AND e.receiver_name like concat('%',#{example.receiverNameLike},'%')
                </if>
                <if test="example.receiverMobile != null and example.receiverMobile != ''">
                    AND e.receiver_mobile = #{example.receiverMobile}
                </if>

                <if test="example.managerNameLike != null">
                    AND e.manager_name like concat('%',#{example.managerNameLike},'%')
                </if>
                <if test="example.managerNameList != null and example.managerNameList.size() > 0">
                    AND e.manager_name in
                    <foreach collection="example.managerNameList" item="managerName" open="(" separator="," close=")">
                        #{managerName}
                    </foreach>
                </if>
                <if test="example.branchCodeIn != null and example.branchCodeIn.size() > 0">
                    AND e.branch in
                    <foreach collection="example.branchCodeIn" item="branchCode" open="(" separator="," close=")">
                        #{branchCode}
                    </foreach>
                </if>
                <if test="example.performanceBranchCodeList != null and example.performanceBranchCodeList.size() > 0">
                    AND pfbl.employee_branch_code in
                    <foreach collection="example.performanceBranchCodeList" item="performanceBranchCode" open="(" separator="," close=")">
                        #{performanceBranchCode}
                    </foreach>
                </if>
                <if test="example.performanceBelongerNameList != null and example.performanceBelongerNameList.size() > 0">
                    AND pfbl.belonger_name in
                    <foreach collection="example.performanceBelongerNameList" item="performanceBelongerName" open="(" separator="," close=")">
                        #{performanceBelongerName}
                    </foreach>
                </if>
                <if test="example.performanceBelongerEmployeeNoList != null and example.performanceBelongerEmployeeNoList.size() > 0">
                    AND pfbl.belonger_employee_no in
                    <foreach collection="example.performanceBelongerEmployeeNoList" item="performanceBelongerEmployeeNo" open="(" separator="," close=")">
                        #{performanceBelongerEmployeeNo}
                    </foreach>
                </if>
                <if test="example.goodsCategoryIdIn != null and example.goodsCategoryIdIn.size() > 0">
                    AND p.goods_category_id in
                    <foreach collection="example.goodsCategoryIdIn" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="example.pointId != null">
                    AND e.point_id =#{example.pointId}
                </if>
                <if test="example.receiveMaterialStatus != null">
                    AND e.receive_material_status = #{example.receiveMaterialStatus}
                </if>
            </trim>
        </if>
    </sql>

    <select id="getFinalStateOfflineOrderIdList" resultType="java.lang.Integer">
        SELECT order_id
        FROM bz_order bo1
        WHERE pay_sn = #{paySn}
        AND NOT EXISTS(
        SELECT order_id
        FROM bz_order bo2
        WHERE order_state IN (0,50,40)
        AND order_type = 6
        AND bo1.order_id = bo2.order_id
        )
    </select>

    <select id="getUserMobile" resultType="java.lang.String">
        SELECT DISTINCT( `user_mobile`) FROM `bz_order`
    </select>

    <select id="employeeTotalAmount" resultType="java.math.BigDecimal">
        SELECT SUM((bop.`money_amount`+ bop.`platform_activity_amount` + bop.`platform_voucher_amount`) * ((bop.product_num - bop.return_number))/bop.product_num) effectiveAmount
        FROM `bz_order` bo, `bz_order_product` bop
        WHERE bo.`order_sn` = bop.`order_sn`
        and bo.`order_state` NOT IN (0,50)
        and bo.`channel` != 'OMS'
        and bop.`is_virtual_goods` != 10
        and bo.`create_time` >= #{startTime}
        and bo.`user_mobile` = #{memberMobile}
        and bo.`recommend_store_id` = #{recommendStoreId}
    </select>
    <select id="tradingRiskWareNing" resultType="com.cfpamf.ms.mallorder.dto.RiskWarningDto">
        SELECT
        bo.order_sn,
        boe.manager_name,
        boe.manager,
        boe.supervisor,
        boe.supervisor_name,
        boe.store_area_code,
        boe.store_area_name,
        boe.area_code,
        boe.branch_name,
        bo.store_name,
        bo.store_id,
        bo.member_id,
        count(bo.order_sn) as count
        FROM
        bz_order bo
        LEFT JOIN bz_order_extend boe ON bo.order_sn = boe.order_sn
        WHERE
        bo.payment_code IN ( 'ENJOY_PAY', 'FOLLOW_HEART','CREDIT_PAY' )
        AND bo.order_state &gt;= 20 AND bo.order_state &lt; 50
        AND bo.create_time > DATE_SUB( CURDATE(), INTERVAL #{days} DAY )
        AND bo.store_is_self = 2
        AND boe.manage_type != 3
        AND boe.manager = #{manager}
        <if test="excludeCategory != null and excludeCategory.size() > 0">
            and EXISTS (
            SELECT bop.`order_sn` from `mall_order`.`bz_order_product` bop
            where bop.`order_sn` = boe.`order_sn`
            and bop.`goods_category_id` not in
            <foreach collection="excludeCategory" separator="," open="(" close=")" item="ec">
                #{ec}
            </foreach>
            HAVING COUNT(1) >= 1
            )
        </if>
        GROUP BY boe.manager
        HAVING COUNT(boe.manager) >= #{num}
    </select>
    <select id="tradingRiskByStoreId" resultType="com.cfpamf.ms.mallorder.dto.RiskWarningDto">
        SELECT
        bo.order_sn,
        boe.manager_name,
        boe.manager,
        boe.supervisor,
        boe.supervisor_name,
        boe.store_area_code,
        boe.store_area_name,
        bo.store_id,
        COUNT(bo.order_sn) as total,
        boe.area_code,
        boe.branch_name,
        bo.store_name,
        bo.store_id,
        bo.member_id,
        count(bo.order_sn) as count
        FROM
        bz_order bo
        LEFT JOIN bz_order_extend boe ON bo.order_sn = boe.order_sn
        WHERE
        bo.payment_code IN ( 'ENJOY_PAY', 'FOLLOW_HEART','CREDIT_PAY' )
        AND bo.order_state &gt;= 20 AND bo.order_state &lt; 50
        AND bo.create_time > DATE_SUB( CURDATE(), INTERVAL #{days} DAY )
        AND boe.manage_type != 3
        AND bo.store_is_self = 2
        AND boe.manager = #{manager}
        <if test="excludeCategory != null and excludeCategory.size() > 0">
            and EXISTS (
            SELECT bop.`order_sn` from `mall_order`.`bz_order_product` bop
            where bop.`order_sn` = boe.`order_sn`
            and bop.`goods_category_id` not in
            <foreach collection="excludeCategory" separator="," open="(" close=")" item="ec">
                #{ec}
            </foreach>
            HAVING COUNT(1) >= 1
            )
        </if>
        GROUP BY bo.store_id,boe.manager
        HAVING count( boe.manager) >= #{num}
    </select>
    <select id="tradingRiskByStoreIdTwo" resultType="com.cfpamf.ms.mallorder.dto.RiskWarningDto">
        SELECT
        bo.order_sn,
        boe.manager_name,
        boe.manager,
        boe.supervisor,
        boe.supervisor_name,
        boe.store_area_code,
        boe.store_area_name,
        bo.store_id,
        COUNT(bo.order_sn) as total,
        boe.area_code,
        boe.branch_name,
        bo.store_name,
        bo.store_id,
        bo.member_id,
        count(bo.order_sn) as count
        FROM
        bz_order bo
        LEFT JOIN bz_order_extend boe ON bo.order_sn = boe.order_sn
        WHERE
        bo.payment_code IN ( 'ENJOY_PAY', 'FOLLOW_HEART','CREDIT_PAY' )
        AND bo.order_state &gt;= 20 AND bo.order_state &lt; 50
        AND bo.create_time > DATE_SUB( CURDATE(), INTERVAL #{days} DAY )
        AND boe.manage_type != 3
        AND bo.store_is_self = 2
        AND boe.manager = #{manager}
        <if test="excludeCategory != null and excludeCategory.size() > 0">
            and EXISTS (
            SELECT bop.`order_sn` from `mall_order`.`bz_order_product` bop
            where bop.`order_sn` = boe.`order_sn`
            and bop.`goods_category_id` not in
            <foreach collection="excludeCategory" separator="," open="(" close=")" item="ec">
                #{ec}
            </foreach>
            HAVING COUNT(1) >= 1
            )
        </if>
        GROUP BY bo.store_id,boe.manager
        HAVING count(boe.manager) >= #{num}
    </select>
    <select id="tradingRiskWarningSameStore" resultType="com.cfpamf.ms.mallorder.dto.RiskWarningDto">
        SELECT
        bo.order_sn,
        bo.store_id,
        bo.store_name,
        bo.member_id,
        boe.customer_name,
        bo.member_id,
        count(bo.order_sn) as count
        FROM
        bz_order bo
        LEFT JOIN bz_order_extend boe ON bo.order_sn = boe.order_sn
        WHERE
        bo.payment_code IN ( 'ENJOY_PAY', 'FOLLOW_HEART','CREDIT_PAY' )
        AND bo.order_state &gt;= 20 AND bo.order_state &lt; 50
        AND bo.create_time > DATE_SUB( CURDATE(), INTERVAL #{days} DAY )
        AND bo.store_is_self = 2
        AND bo.member_id = #{memberId}
        and bo.store_id = #{storeId}
        GROUP BY bo.store_id
        HAVING count(bo.order_sn) >= #{num}
    </select>
    <select id="existOrderStoreIdByStoreId" resultType="java.lang.Long">

        select distinct store_id from bz_order
        <where>
            create_time >= #{startTime}
            <if test="storeIdList != null and storeIdList.size() > 0">
                AND store_id in
                <foreach collection="storeIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>

    </select>
    <select id="listOrderPointFix" resultType="com.cfpamf.ms.mallorder.dto.OrderPointFixDTO">
        select
        boe.extend_id ,
        boe.order_sn ,
        boe.branch ,
        boe.point_id,
        bo.order_id,
        bo.store_id
        from
        mall_order.bz_order bo
        left join mall_order.bz_order_extend boe on
        boe.order_sn = bo.order_sn
        where
        bo.order_pattern  = 6
        and boe.point_id is null
        and bo.enabled_flag = 1
        and boe.enabled_flag = 1
        <if test="orderSnList != null and orderSnList.size() > 0">
            and bo.order_sn in
            <foreach collection="orderSnList" item="orderSn" separator="," open="(" close=")">
                #{orderSn}
            </foreach>
        </if>
    </select>
    <select id="getOrderFilterBriefByBranch" resultType="com.cfpamf.ms.mallorder.vo.OrderInfoForThemeVO">
        select
            bo.`order_sn`,
            bo.`order_type`,
            bo.`payment_code`,
            bo.`payment_name`,
            bo.`order_state`,
            bo.`create_time`,
            bo.`pay_time`,
            bo.`finish_time`,
            bo.`channel`,
            bo.`store_id`,
            bo.`store_name`,
            bo.`user_no`,
            bo.`user_mobile`,
            bo.`order_amount`,
            boe.`manager`,
            boe.`manager_name`,
            boe.`customer_name`,
            bpl.`employee_branch_code` as `branch`
        from
        `mall_order`.`bz_order` bo
        inner join mall_order.`bz_order_extend` boe on bo.`order_sn` = boe.`order_sn`
        left join mall_order.`bz_order_performance_belongs` bpl on bo.`order_sn` = bpl.`order_sn`
        where
        `bo`.`pay_time` <![CDATA[>=]]> #{orderForThemeDTO.payStartTime}
        and `bo`.`pay_time` <![CDATA[<=]]> #{orderForThemeDTO.payEndTime}
        and bo.`enabled_flag` = 1
        and boe.`enabled_flag` = 1
        and bo.`order_state` in (20,25,30,40)
        <if test="orderForThemeDTO.storeIdList != null and orderForThemeDTO.storeIdList.size() >0">
            and bo.`store_id` IN
            <foreach collection="orderForThemeDTO.storeIdList" item="storeId" separator="," open="(" close=")">
                #{storeId}
            </foreach>
        </if>
        <if test="orderForThemeDTO.branchCode != null">
            and bpl.`employee_branch_code` = #{orderForThemeDTO.branchCode}
        </if>
        <if test="orderForThemeDTO.searchInput !=null and orderForThemeDTO.searchInput != ''">
            and (boe.`customer_name` = #{orderForThemeDTO.searchInput} or bo.order_sn = #{orderForThemeDTO.searchInput})
        </if>
        <if test="orderForThemeDTO.orderSnList != null and orderForThemeDTO.orderSnList.size() > 0">
            and bo.order_sn in
            <foreach collection="orderForThemeDTO.orderSnList" item="orderSn" separator="," open="(" close=")">
                #{orderSn}
            </foreach>
        </if>
        LIMIT #{startRow},#{pageSize}
    </select>
    <select id="countOrderFilterBriefByBranch" resultType="java.lang.Integer">
        select
          count(1)
        from
        `mall_order`.`bz_order` bo
        inner join mall_order.`bz_order_extend` boe on bo.`order_sn` = boe.`order_sn`
        left join mall_order.`bz_order_performance_belongs` bpl on bo.`order_sn` = bpl.`order_sn`
        where
        `bo`.`pay_time` <![CDATA[>=]]> #{orderForThemeDTO.payStartTime}
        and `bo`.`pay_time` <![CDATA[<=]]> #{orderForThemeDTO.payEndTime}
        and bo.`enabled_flag` = 1
        and boe.`enabled_flag` = 1
        and bo.`order_state` in (20,25,30,40)
        <if test="orderForThemeDTO.storeIdList != null and orderForThemeDTO.storeIdList.size() >0">
            and bo.`store_id` IN
            <foreach collection="orderForThemeDTO.storeIdList" item="storeId" separator="," open="(" close=")">
                #{storeId}
            </foreach>
        </if>
        <if test="orderForThemeDTO.branchCode != null">
            and bpl.`employee_branch_code` = #{orderForThemeDTO.branchCode}
        </if>
        <if test="orderForThemeDTO.searchInput !=null and orderForThemeDTO.searchInput != ''">
            and (boe.`customer_name` = #{orderForThemeDTO.searchInput} or bo.order_sn = #{orderForThemeDTO.searchInput})
        </if>
        <if test="orderForThemeDTO.orderSnList != null and orderForThemeDTO.orderSnList.size() > 0">
            and bo.order_sn in
            <foreach collection="orderForThemeDTO.orderSnList" item="orderSn" separator="," open="(" close=")">
                #{orderSn}
            </foreach>
        </if>
    </select>
    <select id="listOrderFilterBriefByBranch" resultType="com.cfpamf.ms.mallorder.vo.OrderInfoForThemeVO">
        select
            bo.`order_sn`,
            bo.`order_type`,
            bo.`payment_code`,
            bo.`payment_name`,
            bo.`order_state`,
            bo.`create_time`,
            bo.`pay_time`,
            bo.`finish_time`,
            bo.`channel`,
            bo.`store_id`,
            bo.`store_name`,
            bo.`user_no`,
            bo.`user_mobile`,
            bo.`order_amount`,
            boe.`manager`,
            boe.`manager_name`,
            boe.`customer_name`,
            bpl.`employee_branch_code` as `branch`
        from
        `mall_order`.`bz_order` bo
        inner join mall_order.`bz_order_extend` boe on bo.`order_sn` = boe.`order_sn`
        left join mall_order.`bz_order_performance_belongs` bpl on bo.`order_sn` = bpl.`order_sn`
        where
        `bo`.`pay_time` <![CDATA[>=]]> #{orderForThemeDTO.payStartTime}
        and `bo`.`pay_time` <![CDATA[<=]]> #{orderForThemeDTO.payEndTime}
        and bo.`enabled_flag` = 1
        and boe.`enabled_flag` = 1
        and bo.`order_state` in (20,25,30,40)
        <if test="orderForThemeDTO.storeIdList != null and orderForThemeDTO.storeIdList.size() >0">
            and bo.`store_id` IN
            <foreach collection="orderForThemeDTO.storeIdList" item="storeId" separator="," open="(" close=")">
                #{storeId}
            </foreach>
        </if>
        <if test="orderForThemeDTO.branchCode != null">
            and bpl.`employee_branch_code` = #{orderForThemeDTO.branchCode}
        </if>
        <if test="orderForThemeDTO.orderSnList != null and orderForThemeDTO.orderSnList.size() > 0">
            and bo.order_sn in
            <foreach collection="orderForThemeDTO.orderSnList" item="orderSn" separator="," open="(" close=")">
                #{orderSn}
            </foreach>
        </if>
    </select>
</mapper>
