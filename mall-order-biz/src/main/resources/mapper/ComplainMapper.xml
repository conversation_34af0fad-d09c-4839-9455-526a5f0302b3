<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.ComplainMapper">
  <resultMap id="resultMap" type="com.cfpamf.ms.mallorder.po.ComplainPO">
    <id column="complain_id" property="complainId" />
    <result column="complain_subject_id" property="complainSubjectId" />
    <result column="afs_sn" property="afsSn" />
    <result column="order_sn" property="orderSn" />
    <result column="order_product_id" property="orderProductId" />
    <result column="goods_id" property="goodsId" />
    <result column="product_id" property="productId" />
    <result column="goods_name" property="goodsName" />
    <result column="goods_image" property="goodsImage" />
    <result column="spec_values" property="specValues" />
    <result column="complain_member_id" property="complainMemberId" />
    <result column="complain_member_name" property="complainMemberName" />
    <result column="store_id" property="storeId" />
    <result column="store_name" property="storeName" />
    <result column="complain_content" property="complainContent" />
    <result column="complain_pic" property="complainPic" />
    <result column="complain_time" property="complainTime" />
    <result column="complain_audit_time" property="complainAuditTime" />
    <result column="complain_audit_admin_id" property="complainAuditAdminId" />
    <result column="refuse_reason" property="refuseReason" />
    <result column="appeal_content" property="appealContent" />
    <result column="appeal_image" property="appealImage" />
    <result column="appeal_time" property="appealTime" />
    <result column="appeal_vendor_id" property="appealVendorId" />
    <result column="admin_handle_content" property="adminHandleContent" />
    <result column="admin_handle_time" property="adminHandleTime" />
    <result column="admin_handle_id" property="adminHandleId" />
    <result column="complain_state" property="complainState" />
    <result column="handle_deadline" property="handleDeadline" />
  </resultMap>
  <!--除主键外的所有字段，用于插入操作-->
  <sql id="columns">
    <trim suffixOverrides=",">
      <if test="complainSubjectId != null">
        `complain_subject_id`,
      </if>
      <if test="afsSn != null">
        `afs_sn`,
      </if>
      <if test="orderSn != null">
        `order_sn`,
      </if>
      <if test="orderProductId != null">
        `order_product_id`,
      </if>
      <if test="goodsId != null">
        `goods_id`,
      </if>
      <if test="productId != null">
        `product_id`,
      </if>
      <if test="goodsName != null">
        `goods_name`,
      </if>
      <if test="goodsImage != null">
        `goods_image`,
      </if>
      <if test="specValues != null">
        `spec_values`,
      </if>
      <if test="complainMemberId != null">
        `complain_member_id`,
      </if>
      <if test="complainMemberName != null">
        `complain_member_name`,
      </if>
      <if test="storeId != null">
        `store_id`,
      </if>
      <if test="storeName != null">
        `store_name`,
      </if>
      <if test="complainContent != null">
        `complain_content`,
      </if>
      <if test="complainPic != null">
        `complain_pic`,
      </if>
      <if test="complainTime != null">
        `complain_time`,
      </if>
      <if test="complainAuditTime != null">
        `complain_audit_time`,
      </if>
      <if test="complainAuditAdminId != null">
        `complain_audit_admin_id`,
      </if>
      <if test="refuseReason != null">
        `refuse_reason`,
      </if>
      <if test="appealContent != null">
        `appeal_content`,
      </if>
      <if test="appealImage != null">
        `appeal_image`,
      </if>
      <if test="appealTime != null">
        `appeal_time`,
      </if>
      <if test="appealVendorId != null">
        `appeal_vendor_id`,
      </if>
      <if test="adminHandleContent != null">
        `admin_handle_content`,
      </if>
      <if test="adminHandleTime != null">
        `admin_handle_time`,
      </if>
      <if test="adminHandleId != null">
        `admin_handle_id`,
      </if>
      <if test="complainState != null">
        `complain_state`,
      </if>
      <if test="handleDeadline != null">
        `handle_deadline`,
      </if>
    </trim>
  </sql>
  <!--按照主键值进行操作-->
  <sql id="pkWhere">
    WHERE `complain_id` = #{primaryKey} AND enabled_flag =1
  </sql>
  <!--操作条件-->
  <sql id="whereCondition">
    <if test="example != null">
      <trim prefix="WHERE" prefixOverrides="AND|OR">
        AND enabled_flag =1
        <if test="example.complainIdNotEquals != null">
          AND `complain_id` != #{example.complainIdNotEquals}
        </if>
        <if test="example.complainIdIn != null">
          AND `complain_id` in (${example.complainIdIn})
        </if>
        <if test="example.complainId != null">
          AND `complain_id` = #{example.complainId}
        </if>
        <if test="example.complainSubjectId != null">
          AND `complain_subject_id` = #{example.complainSubjectId}
        </if>
        <if test="example.afsSn != null">
          AND `afs_sn` = #{example.afsSn}
        </if>
        <if test="example.afsSnLike != null">
          AND `afs_sn` like concat('%',#{example.afsSnLike},'%')
        </if>
        <if test="example.orderSn != null">
          AND `order_sn` = #{example.orderSn}
        </if>
        <if test="example.orderSnLike != null">
          AND `order_sn` like concat('%',#{example.orderSnLike},'%')
        </if>
        <if test="example.orderProductId != null">
          AND `order_product_id` = #{example.orderProductId}
        </if>
        <if test="example.goodsId != null">
          AND `goods_id` = #{example.goodsId}
        </if>
        <if test="example.productId != null">
          AND `product_id` = #{example.productId}
        </if>
        <if test="example.goodsName != null">
          AND `goods_name` = #{example.goodsName}
        </if>
        <if test="example.goodsNameLike != null">
          AND `goods_name` like concat('%',#{example.goodsNameLike},'%')
        </if>
        <if test="example.goodsImage != null">
          AND `goods_image` = #{example.goodsImage}
        </if>
        <if test="example.specValues != null">
          AND `spec_values` = #{example.specValues}
        </if>
        <if test="example.complainMemberId != null">
          AND `complain_member_id` = #{example.complainMemberId}
        </if>
        <if test="example.complainMemberName != null">
          AND `complain_member_name` = #{example.complainMemberName}
        </if>
        <if test="example.complainMemberNameLike != null">
          AND `complain_member_name` like concat('%',#{example.complainMemberNameLike},'%')
        </if>
        <if test="example.storeId != null">
          AND `store_id` = #{example.storeId}
        </if>
        <if test="example.storeName != null">
          AND `store_name` = #{example.storeName}
        </if>
        <if test="example.storeNameLike != null">
          AND `store_name` like concat('%',#{example.storeNameLike},'%')
        </if>
        <if test="example.complainContent != null">
          AND `complain_content` = #{example.complainContent}
        </if>
        <if test="example.complainContentLike != null">
          AND `complain_content` like concat('%',#{example.complainContentLike},'%')
        </if>
        <if test="example.complainPic != null">
          AND `complain_pic` = #{example.complainPic}
        </if>
        <if test="example.complainTimeAfter != null">
          AND `complain_time` <![CDATA[ >= ]]> #{example.complainTimeAfter}
        </if>
        <if test="example.complainTimeBefore != null">
          AND `complain_time` <![CDATA[ <= ]]> #{example.complainTimeBefore}
        </if>
        <if test="example.complainAuditTimeAfter != null">
          AND `complain_audit_time` <![CDATA[ >= ]]> #{example.complainAuditTimeAfter}
        </if>
        <if test="example.complainAuditTimeBefore != null">
          AND `complain_audit_time` <![CDATA[ <= ]]> #{example.complainAuditTimeBefore}
        </if>
        <if test="example.complainAuditAdminId != null">
          AND `complain_audit_admin_id` = #{example.complainAuditAdminId}
        </if>
        <if test="example.refuseReason != null">
          AND `refuse_reason` = #{example.refuseReason}
        </if>
        <if test="example.appealContent != null">
          AND `appeal_content` = #{example.appealContent}
        </if>
        <if test="example.appealContentLike != null">
          AND `appeal_content` like concat('%',#{example.appealContentLike},'%')
        </if>
        <if test="example.appealImage != null">
          AND `appeal_image` = #{example.appealImage}
        </if>
        <if test="example.appealTimeAfter != null">
          AND `appeal_time` <![CDATA[ >= ]]> #{example.appealTimeAfter}
        </if>
        <if test="example.appealTimeBefore != null">
          AND `appeal_time` <![CDATA[ <= ]]> #{example.appealTimeBefore}
        </if>
        <if test="example.appealVendorId != null">
          AND `appeal_vendor_id` = #{example.appealVendorId}
        </if>
        <if test="example.adminHandleContent != null">
          AND `admin_handle_content` = #{example.adminHandleContent}
        </if>
        <if test="example.adminHandleContentLike != null">
          AND `admin_handle_content` like concat('%',#{example.adminHandleContentLike},'%')
        </if>
        <if test="example.adminHandleTimeAfter != null">
          AND `admin_handle_time` <![CDATA[ >= ]]> #{example.adminHandleTimeAfter}
        </if>
        <if test="example.adminHandleTimeBefore != null">
          AND `admin_handle_time` <![CDATA[ <= ]]> #{example.adminHandleTimeBefore}
        </if>
        <if test="example.adminHandleId != null">
          AND `admin_handle_id` = #{example.adminHandleId}
        </if>
        <if test="example.complainState != null">
          AND `complain_state` = #{example.complainState}
        </if>
        <if test="example.handleDeadlineAfter != null">
          AND `handle_deadline` <![CDATA[ >= ]]> #{example.handleDeadlineAfter}
        </if>
        <if test="example.handleDeadlineBefore != null">
          AND `handle_deadline` <![CDATA[ <= ]]> #{example.handleDeadlineBefore}
        </if>
      </trim>
    </if>
    <if test="example == null">
      where enabled_flag =1
    </if>
  </sql>
  <!--排序条件-->
  <sql id="orderBy">
    ORDER BY `complain_id` DESC
  </sql>
  <sql id="orderByOther">
    order by ${example.orderBy}
  </sql>
  <!--分组条件-->
  <sql id="groupBy">
    group by ${example.groupBy}
  </sql>
  <!--分页条件-->
  <sql id="limit">
    <if test="size != null and size &gt; 0">
      limit #{startRow},#{size}
    </if>
  </sql>
  <!--查询符合条件的记录数-->
  <select id="countByExample" parameterType="com.cfpamf.ms.mallorder.request.ComplainExample" resultType="java.lang.Integer">
    SELECT
      COUNT(*)
    FROM `bz_complain`
    <include refid="whereCondition" />
  </select>
  <!--根据主键查询记录-->
  <select id="getByPrimaryKey" resultMap="resultMap">
    SELECT
      *
    FROM `bz_complain`
    <include refid="pkWhere" />
  </select>
  <!--查询符合条件的记录(所有字段)-->
  <select id="listByExample" resultMap="resultMap">
    SELECT
      *
    FROM `bz_complain`
    <include refid="whereCondition" />
    <if test="example.groupBy != null">
      <include refid="groupBy" />
    </if>
    <choose>
      <when test="example.orderBy != null">
        <include refid="orderByOther" />
      </when>
      <otherwise>
        <include refid="orderBy" />
      </otherwise>
    </choose>
  </select>
  <!--分页查询符合条件的记录(所有字段)-->
  <select id="listPageByExample" resultMap="resultMap">
    SELECT
      *
    FROM `bz_complain`
    <include refid="whereCondition" />
    <if test="example.groupBy != null">
      <include refid="groupBy" />
    </if>
    <choose>
      <when test="example.orderBy != null">
        <include refid="orderByOther" />
      </when>
      <otherwise>
        <include refid="orderBy" />
      </otherwise>
    </choose>
    <include refid="limit" />
  </select>
  <!--查询符合条件的记录(指定字段)-->
  <select id="listFieldsByExample" resultMap="resultMap">
    SELECT
      ${fields}
    FROM `bz_complain`
    <include refid="whereCondition" />
    <if test="example.groupBy != null">
      <include refid="groupBy" />
    </if>
    <choose>
      <when test="example.orderBy != null">
        <include refid="orderByOther" />
      </when>
      <otherwise>
        <include refid="orderBy" />
      </otherwise>
    </choose>
  </select>
  <!--分页查询符合条件的记录(指定字段)-->
  <select id="listFieldsPageByExample" resultMap="resultMap">
    SELECT
      ${fields}
    FROM `bz_complain`
    <include refid="whereCondition" />
    <if test="example.groupBy != null">
      <include refid="groupBy" />
    </if>
    <choose>
      <when test="example.orderBy != null">
        <include refid="orderByOther" />
      </when>
      <otherwise>
        <include refid="orderBy" />
      </otherwise>
    </choose>
    <include refid="limit" />
  </select>
  <!--根据条件删除记录，可多条删除-->
  <update id="deleteByExample">
    update `bz_complain`SET enabled_flag =0
    <include refid="whereCondition" />
  </update>
  <!--根据主键删除记录-->
  <update id="deleteByPrimaryKey">
    update `bz_complain`SET enabled_flag =0
    <include refid="pkWhere" />
  </update>
  <!--插入一条记录-->
  <insert id="insert" keyColumn="complain_id" keyProperty="complainId" parameterType="com.cfpamf.ms.mallorder.po.ComplainPO" useGeneratedKeys="true">
    INSERT INTO `bz_complain`(
    <include refid="columns" />
    )
    VALUES(
    <trim suffixOverrides=",">
      <if test="complainSubjectId != null">
        #{complainSubjectId},
      </if>
      <if test="afsSn != null">
        #{afsSn},
      </if>
      <if test="orderSn != null">
        #{orderSn},
      </if>
      <if test="orderProductId != null">
        #{orderProductId},
      </if>
      <if test="goodsId != null">
        #{goodsId},
      </if>
      <if test="productId != null">
        #{productId},
      </if>
      <if test="goodsName != null">
        #{goodsName},
      </if>
      <if test="goodsImage != null">
        #{goodsImage},
      </if>
      <if test="specValues != null">
        #{specValues},
      </if>
      <if test="complainMemberId != null">
        #{complainMemberId},
      </if>
      <if test="complainMemberName != null">
        #{complainMemberName},
      </if>
      <if test="storeId != null">
        #{storeId},
      </if>
      <if test="storeName != null">
        #{storeName},
      </if>
      <if test="complainContent != null">
        #{complainContent},
      </if>
      <if test="complainPic != null">
        #{complainPic},
      </if>
      <if test="complainTime != null">
        #{complainTime},
      </if>
      <if test="complainAuditTime != null">
        #{complainAuditTime},
      </if>
      <if test="complainAuditAdminId != null">
        #{complainAuditAdminId},
      </if>
      <if test="refuseReason != null">
        #{refuseReason},
      </if>
      <if test="appealContent != null">
        #{appealContent},
      </if>
      <if test="appealImage != null">
        #{appealImage},
      </if>
      <if test="appealTime != null">
        #{appealTime},
      </if>
      <if test="appealVendorId != null">
        #{appealVendorId},
      </if>
      <if test="adminHandleContent != null">
        #{adminHandleContent},
      </if>
      <if test="adminHandleTime != null">
        #{adminHandleTime},
      </if>
      <if test="adminHandleId != null">
        #{adminHandleId},
      </if>
      <if test="complainState != null">
        #{complainState},
      </if>
      <if test="handleDeadline != null">
        #{handleDeadline},
      </if>
    </trim>
    )
  </insert>
  <!--按条件更新记录中不为空的字段-->
  <update id="updateByExampleSelective">
    UPDATE `bz_complain`
    <trim prefix="SET" suffixOverrides=",">
      <if test="record.complainSubjectId != null">
        `complain_subject_id` = #{record.complainSubjectId},
      </if>
      <if test="record.afsSn != null">
        `afs_sn` = #{record.afsSn},
      </if>
      <if test="record.orderSn != null">
        `order_sn` = #{record.orderSn},
      </if>
      <if test="record.orderProductId != null">
        `order_product_id` = #{record.orderProductId},
      </if>
      <if test="record.goodsId != null">
        `goods_id` = #{record.goodsId},
      </if>
      <if test="record.productId != null">
        `product_id` = #{record.productId},
      </if>
      <if test="record.goodsName != null">
        `goods_name` = #{record.goodsName},
      </if>
      <if test="record.goodsImage != null">
        `goods_image` = #{record.goodsImage},
      </if>
      <if test="record.specValues != null">
        `spec_values` = #{record.specValues},
      </if>
      <if test="record.complainMemberId != null">
        `complain_member_id` = #{record.complainMemberId},
      </if>
      <if test="record.complainMemberName != null">
        `complain_member_name` = #{record.complainMemberName},
      </if>
      <if test="record.storeId != null">
        `store_id` = #{record.storeId},
      </if>
      <if test="record.storeName != null">
        `store_name` = #{record.storeName},
      </if>
      <if test="record.complainContent != null">
        `complain_content` = #{record.complainContent},
      </if>
      <if test="record.complainPic != null">
        `complain_pic` = #{record.complainPic},
      </if>
      <if test="record.complainTime != null">
        `complain_time` = #{record.complainTime},
      </if>
      <if test="record.complainAuditTime != null">
        `complain_audit_time` = #{record.complainAuditTime},
      </if>
      <if test="record.complainAuditAdminId != null">
        `complain_audit_admin_id` = #{record.complainAuditAdminId},
      </if>
      <if test="record.refuseReason != null">
        `refuse_reason` = #{record.refuseReason},
      </if>
      <if test="record.appealContent != null">
        `appeal_content` = #{record.appealContent},
      </if>
      <if test="record.appealImage != null">
        `appeal_image` = #{record.appealImage},
      </if>
      <if test="record.appealTime != null">
        `appeal_time` = #{record.appealTime},
      </if>
      <if test="record.appealVendorId != null">
        `appeal_vendor_id` = #{record.appealVendorId},
      </if>
      <if test="record.adminHandleContent != null">
        `admin_handle_content` = #{record.adminHandleContent},
      </if>
      <if test="record.adminHandleTime != null">
        `admin_handle_time` = #{record.adminHandleTime},
      </if>
      <if test="record.adminHandleId != null">
        `admin_handle_id` = #{record.adminHandleId},
      </if>
      <if test="record.complainState != null">
        `complain_state` = #{record.complainState},
      </if>
      <if test="record.handleDeadline != null">
        `handle_deadline` = #{record.handleDeadline},
      </if>
    </trim>
    <include refid="whereCondition" />
  </update>
  <!--按照主键更新记录中不为空的字段-->
  <update id="updateByPrimaryKeySelective">
    UPDATE `bz_complain`
    <trim prefix="SET" suffixOverrides=",">
      <if test="complainSubjectId != null">
        `complain_subject_id` = #{complainSubjectId},
      </if>
      <if test="afsSn != null">
        `afs_sn` = #{afsSn},
      </if>
      <if test="orderSn != null">
        `order_sn` = #{orderSn},
      </if>
      <if test="orderProductId != null">
        `order_product_id` = #{orderProductId},
      </if>
      <if test="goodsId != null">
        `goods_id` = #{goodsId},
      </if>
      <if test="productId != null">
        `product_id` = #{productId},
      </if>
      <if test="goodsName != null">
        `goods_name` = #{goodsName},
      </if>
      <if test="goodsImage != null">
        `goods_image` = #{goodsImage},
      </if>
      <if test="specValues != null">
        `spec_values` = #{specValues},
      </if>
      <if test="complainMemberId != null">
        `complain_member_id` = #{complainMemberId},
      </if>
      <if test="complainMemberName != null">
        `complain_member_name` = #{complainMemberName},
      </if>
      <if test="storeId != null">
        `store_id` = #{storeId},
      </if>
      <if test="storeName != null">
        `store_name` = #{storeName},
      </if>
      <if test="complainContent != null">
        `complain_content` = #{complainContent},
      </if>
      <if test="complainPic != null">
        `complain_pic` = #{complainPic},
      </if>
      <if test="complainTime != null">
        `complain_time` = #{complainTime},
      </if>
      <if test="complainAuditTime != null">
        `complain_audit_time` = #{complainAuditTime},
      </if>
      <if test="complainAuditAdminId != null">
        `complain_audit_admin_id` = #{complainAuditAdminId},
      </if>
      <if test="refuseReason != null">
        `refuse_reason` = #{refuseReason},
      </if>
      <if test="appealContent != null">
        `appeal_content` = #{appealContent},
      </if>
      <if test="appealImage != null">
        `appeal_image` = #{appealImage},
      </if>
      <if test="appealTime != null">
        `appeal_time` = #{appealTime},
      </if>
      <if test="appealVendorId != null">
        `appeal_vendor_id` = #{appealVendorId},
      </if>
      <if test="adminHandleContent != null">
        `admin_handle_content` = #{adminHandleContent},
      </if>
      <if test="adminHandleTime != null">
        `admin_handle_time` = #{adminHandleTime},
      </if>
      <if test="adminHandleId != null">
        `admin_handle_id` = #{adminHandleId},
      </if>
      <if test="complainState != null">
        `complain_state` = #{complainState},
      </if>
      <if test="handleDeadline != null">
        `handle_deadline` = #{handleDeadline},
      </if>
    </trim>
    WHERE `complain_id` = #{complainId}
  </update>
</mapper>