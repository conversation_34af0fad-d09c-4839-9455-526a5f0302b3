<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.PlanLoanDateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.mallorder.po.PlanLoanDatePO">
        <result column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="enabled_flag" property="enabledFlag"/>
        <result column="order_sn" property="orderSn"/>
        <result column="store_id" property="storeId"/>
        <result column="store_name" property="storeName"/>
        <result column="remark" property="remark"/>
        <result column="order_amount" property="orderAmount"/>
        <result column="org_plan_loan_date" property="orgPlanLoanDate"/>
        <result column="plan_loan_date" property="planLoanDate"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_time,
        update_time,
        enabled_flag,order_sn,store_id,store_name,remark，order_amount,org_plan_loan_date,plan_loan_date,create_by,update_by
    </sql>

    <select id="getPlanLoanDateListCount" resultType="int">

        select count(1)
        from bz_plan_loan_date
        <include refid="whereCondition"/>
    </select>

    <select id="getPlanLoanDateListByPage" resultType="com.cfpamf.ms.mallorder.vo.PlanLoanDateVO">

        select  order_sn,
                store_id,
                store_name,
                order_amount,
                org_plan_loan_date,
                plan_loan_date,
                remark,
                update_by,
                update_time
        from bz_plan_loan_date t
        <include refid="whereCondition"/>
        order by t.create_time desc
        <if test="size != null and size &gt; 0">
            limit #{startRow},#{size}
        </if>

    </select>

    <sql id="whereCondition">
        <if test="req != null">
            <trim prefix="WHERE" prefixOverrides="AND|OR">
                AND enabled_flag =1
            </trim>
            <if test="req.storeId != null">
                AND store_id = #{req.storeId}
            </if>
            <if test="req.orderSn != null">
                AND order_sn = #{req.orderSn}
            </if>
            <if test="req.storeName != null">
                AND store_name like concat('%',#{req.storeName},'%')
            </if>
            <if test="req.createTimeAfter != null">
                AND create_time >= #{req.createTimeAfter}
            </if>
            <if test="req.createTimeBefore != null">
                AND create_time <![CDATA[    <= #{req.createTimeBefore}     ]]>
            </if>
        </if>

    </sql>

</mapper>
