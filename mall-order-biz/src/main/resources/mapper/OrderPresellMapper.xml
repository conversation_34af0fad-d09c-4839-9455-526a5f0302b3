<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.OrderPresellMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.mallorder.po.OrderPresellPO">
        <result column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="order_sn" property="orderSn" />
        <result column="pay_sn" property="paySn" />
        <result column="pay_no" property="payNo" />
        <result column="total_amount" property="totalAmount" />
        <result column="discount_amount" property="discountAmount" />
        <result column="amount" property="amount" />
        <result column="pay_amount" property="payAmount" />
        <result column="channel_service_fee" property="channelServiceFee" />
        <result column="channel_service_rate" property="channelServiceRate" />
        <result column="dead_time" property="deadTime" />
        <result column="type" property="type" />
        <result column="payment_code" property="paymentCode" />
        <result column="payment_name" property="paymentName" />
        <result column="pay_status" property="payStatus" />
        <result column="pay_time" property="payTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="enabled_flag" property="enabledFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_time,
        update_time,
        order_sn, pay_sn, pay_no, total_amount, amount, discount_amount, pay_amount, channel_service_fee,channel_service_rate,
        dead_time, type, payment_code, payment_name, pay_status, pay_time,create_by, update_by, enabled_flag
    </sql>

    <select id="getPreSellOrderDetailByOrderSn" parameterType="java.lang.String" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" /> from bz_order_presell where order_sn = #{orderSn}
    </select>


    <select id="getPreSellOrderDetailByOrderSnList" parameterType="java.lang.String" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from bz_order_presell
        <where>
           <if test="orderSnList != null and orderSnList.size() > 0">


            order_sn in
            <foreach collection="orderSnList" item="orderSn"  open="(" separator="," close=")">
                #{orderSn}
            </foreach>
           </if>

        </where>

    </select>

    <select id="selectBalanceDealLineOrder" resultType="com.cfpamf.ms.mallorder.po.OrderPO">
        SELECT
            bo.*
        FROM
            bz_order_presell bopp
            LEFT JOIN bz_order bo ON bopp.order_sn = bo.order_sn
        WHERE
            bopp.type = 2
            AND (bopp.pay_status = 1 or(bopp.pay_status = 2 and bopp.payment_code = 'BANK_TRANSFER'))
            AND bo.order_type = 107
            AND bo.order_state in (10,15)
            AND bopp.enabled_flag = 1
            AND bo.enabled_flag = 1
            AND bopp.dead_time  <![CDATA[<]]> now()
        ORDER BY
            create_time DESC;
    </select>

</mapper>
