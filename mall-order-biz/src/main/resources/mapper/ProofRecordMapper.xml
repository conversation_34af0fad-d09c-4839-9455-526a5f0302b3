<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.ProofRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.mallorder.po.ProofRecordPO">
        <result column="proof_definition_record_id" property="proofDefinitionRecordId" />
        <result column="proof_definition_code" property="proofDefinitionCode" />
        <result column="proof_key" property="proofKey" />
        <result column="proof_value" property="proofValue" />
        <result column="proof_key_desc" property="proofKeyDesc" />
        <result column="proof_images" property="proofImages" />
        <result column="proof_upload_user_id" property="proofUploadUserId" />
        <result column="proof_upload_user_role" property="proofUploadUserRole" />
        <result column="proof_upload_user_name" property="proofUploadUserName" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="enabled_flag" property="enabledFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        proof_definition_record_id,
        proof_definition_code,
        proof_key,
        proof_value,
        proof_key_desc,
        proof_images,
        proof_upload_user_id,
        proof_upload_user_role,
        proof_upload_user_name,
        create_by, update_by,create_time,update_time, enabled_flag
    </sql>

    <select id="getProofRecords" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from(
            select *
            from bz_proof_record
            where proof_definition_code = #{proofDefinitionCode}
            and proof_value = #{proofValue}
        ) t
        order by t.create_time desc
    </select>
    <select id="getByKeyValue" resultMap="BaseResultMap">

        select <include refid="Base_Column_List" />
        from bz_proof_record
        where proof_definition_code = #{proofDefinitionCode}
            and proof_value = #{proofValue}
            and proof_key = #{proofKey}

    </select>


</mapper>