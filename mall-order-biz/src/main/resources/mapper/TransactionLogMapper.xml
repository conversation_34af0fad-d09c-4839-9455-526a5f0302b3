<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.TransactionLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.mallorder.po.TransactionLogPO">
        <id column="id" property="id" />
        <result column="enabled_flag" property="enabledFlag" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="log_type" property="logType" />
        <result column="role" property="role" />
        <result column="status" property="status" />
        <result column="transaction_id" property="transactionId" />
        <result column="request_source" property="requestSource" />
        <result column="request_time" property="requestTime" />
        <result column="request_param" property="requestParam" />
        <result column="rollback_time" property="rollbackTime" />
        <result column="commit_time" property="commitTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        enabled_flag,
        create_time,
        update_time,
        transaction_log_id, log_type, role, status, transaction_id, request_source, request_time, request_param, rollback_time, commit_time
    </sql>

</mapper>
