<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.BzBankTransferMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.mallorder.po.BzBankTransferPO">
        <result column="id" property="id" />
        <result column="pay_sn" property="paySn" />
        <result column="out_order_no" property="outOrderNo" />
        <result column="pay_amount" property="payAmount" />
        <result column="trade_id" property="tradeId" />
        <result column="currency_code" property="currencyCode" />
        <result column="transfer_state" property="transferState" />
        <result column="expire_time" property="expireTime" />
        <result column="member_id" property="memberId" />
        <result column="member_name" property="memberName" />
        <result column="user_no" property="userNo" />
        <result column="user_mobile" property="userMobile" />
        <result column="payment_account" property="paymentAccount" />
        <result column="payment_name" property="paymentName" />
        <result column="payment_bank_name" property="paymentBankName" />
        <result column="payment_bank_code" property="paymentBankCode" />
        <result column="verify_code" property="verifyCode" />
        <result column="recommend_store_id" property="recommendStoreId" />
        <result column="recommend_store_name" property="recommendStoreName" />
        <result column="recv_balance_acct_id" property="recvBalanceAcctId" />
        <result column="receipt_account" property="receiptAccount" />
        <result column="receipt_name" property="receiptName" />
        <result column="receipt_bank_name" property="receiptBankName" />
        <result column="receipt_bank_code" property="receiptBankCode" />
        <result column="transaction_time" property="transactionTime" />
        <result column="transaction_no" property="transactionNo" />
        <result column="update_time" property="updateTime" />
        <result column="create_time" property="createTime" />
        <result column="enabled_flag" property="enabledFlag" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, pay_sn, out_order_no, pay_amount, trade_id, currency_code, transfer_state, expire_time, member_id, member_name, user_no, user_mobile,
        payment_account, payment_name, payment_bank_name, payment_bank_code, verify_code, recommend_store_id,
        recommend_store_name, recv_balance_acct_id, receipt_account, receipt_name, receipt_bank_name, receipt_bank_code, transaction_time,
        transaction_no, update_time, create_time, enabled_flag, create_by, update_by
    </sql>

</mapper>
