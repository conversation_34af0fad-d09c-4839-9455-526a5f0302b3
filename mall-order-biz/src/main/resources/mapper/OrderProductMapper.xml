<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.OrderProductMapper">
  <resultMap id="resultMap" type="com.cfpamf.ms.mallorder.po.OrderProductPO">
      <id column="order_product_id" property="orderProductId" />
      <result column="create_time" property="createTime" />
      <result column="update_time" property="updateTime" />
      <result column="enabled_flag" property="enabledFlag" />
      <result column="cost" property="cost" />
      <result column="tax_rate" property="taxRate" />
      <result column="order_sn" property="orderSn" />
      <result column="store_id" property="storeId" />
      <result column="store_name" property="storeName" />
      <result column="member_id" property="memberId" />
      <result column="goods_category_id" property="goodsCategoryId" />
      <result column="goods_id" property="goodsId" />
      <result column="goods_name" property="goodsName" />
      <result column="product_image" property="productImage" />
      <result column="spec_values" property="specValues" />
      <result column="product_id" property="productId" />
      <result column="product_show_price" property="productShowPrice" />
      <result column="product_num" property="productNum" />
      <result column="activity_discount_amount" property="activityDiscountAmount" />
      <result column="activity_discount_detail" property="activityDiscountDetail" />
      <result column="store_activity_amount" property="storeActivityAmount" />
      <result column="platform_activity_amount" property="platformActivityAmount" />
      <result column="store_voucher_amount" property="storeVoucherAmount" />
      <result column="platform_voucher_amount" property="platformVoucherAmount" />
      <result column="platform_voucher_net_value" property="platformVoucherNetValue" />
      <result column="integral" property="integral" />
      <result column="integral_cash_amount" property="integralCashAmount" />
      <result column="money_amount" property="moneyAmount" />
       <result column="xz_card_amount" property="xzCardAmount"/>
      <result column="commission_rate" property="commissionRate" />
      <result column="commission_amount" property="commissionAmount" />
      <result column="order_commission" property="orderCommission" />
      <result column="business_commission" property="businessCommission" />
      <result column="service_fee" property="serviceFee" />
      <result column="thirdpartnar_fee" property="thirdpartnarFee" />
      <result column="spell_team_id" property="spellTeamId" />
      <result column="is_gift" property="isGift" />
      <result column="gift_id" property="giftId" />
      <result column="return_number" property="returnNumber" />
      <result column="replacement_number" property="replacementNumber" />
      <result column="is_comment" property="isComment" />
      <result column="finance_rule_code" property="financeRuleCode" />
      <result column="rule_tag" property="ruleTag" />
      <result column="comment_time" property="commentTime" />
      <result column="create_by" property="createBy" />
      <result column="update_by" property="updateBy" />
      <result column="landing_price" property="landingPrice" />
      <result column="tax_price" property="taxPrice" />
      <result column="usr_no" property="usrNo" />
      <result column="delivery_deadline" property="deliveryDeadline" />
      <result column="distribute_parent" property="distributeParent"/>
      <result column="deposit" property="deposit"/>
      <result column="balance" property="balance"/>
      <result column="gift_group" property="giftGroup"/>
      <result column="status" property="status"/>
      <result column="performance_mode" property="performanceMode"/>
      <result column="channel_new_sku_id" property="channelNewSkuId"/>
      <result column="channel_sku_id" property="channelSkuId"/>
  </resultMap>
  <!--除主键外的所有字段，用于插入操作-->
  <sql id="columns">
    <trim suffixOverrides=",">
      <if test="orderSn != null">
        `order_sn`,
      </if>
      <if test="taxRate != null">
        `tax_rate`,
      </if>
      <if test="cost != null">
        `cost`,
      </if>
      <if test="financeRuleCode != null">
        `finance_rule_code`,
      </if>
      <if test="ruleTag != null">
        `rule_tag`,
      </if>
      <if test="storeId != null">
        `store_id`,
      </if>
      <if test="storeName != null">
        `store_name`,
      </if>
      <if test="memberId != null">
        `member_id`,
      </if>
      <if test="goodsCategoryId != null">
        `goods_category_id`,
      </if>
      <if test="goodsId != null">
        `goods_id`,
      </if>
      <if test="goodsName != null">
        `goods_name`,
      </if>
      <if test="productImage != null">
        `product_image`,
      </if>
      <if test="specValues != null">
        `spec_values`,
      </if>
      <if test="productId != null">
        `product_id`,
      </if>
      <if test="productShowPrice != null">
        `product_show_price`,
      </if>
      <if test="productNum != null">
        `product_num`,
      </if>
      <if test="activityDiscountAmount != null">
        `activity_discount_amount`,
      </if>
      <if test="activityDiscountDetail != null">
        `activity_discount_detail`,
      </if>
      <if test="storeActivityAmount != null">
        `store_activity_amount`,
      </if>
      <if test="platformActivityAmount != null">
        `platform_activity_amount`,
      </if>
      <if test="xzCardAmount != null">
        `xz_card_amount`,
      </if>
      <if test="storeVoucherAmount != null">
        `store_voucher_amount`,
      </if>
      <if test="platformVoucherAmount != null">
        `platform_voucher_amount`,
      </if>
      <if test="integral != null">
        `integral`,
      </if>
      <if test="integralCashAmount != null">
        `integral_cash_amount`,
      </if>
      <if test="moneyAmount != null">
        `money_amount`,
      </if>
      <if test="commissionRate != null">
        `commission_rate`,
      </if>
      <if test="commissionAmount != null">
        `commission_amount`,
      </if>
      <if test="orderCommission != null">
        `order_commission`,
      </if>
      <if test="businessCommission != null">
        `business_commission`,
      </if>
      <if test="serviceFee != null">
        `service_fee`,
      </if>
      <if test="thirdpartnarFee != null">
        `thirdpartnar_fee`,
      </if>
      <if test="spellTeamId != null">
        `spell_team_id`,
      </if>
      <if test="isGift != null">
        `is_gift`,
      </if>
      <if test="giftGroup != null">
        `gift_group`,
      </if>
      <if test="giftId != null">
        `gift_id`,
      </if>
      <if test="returnNumber != null">
        `return_number`,
      </if>
      <if test="replacementNumber != null">
        `replacement_number`,
      </if>
      <if test="isComment != null">
        `is_comment`,
      </if>
      <if test="commentTime != null">
        `comment_time`,
      </if>
      <if test="landingPrice != null">
        `landing_price`,
      </if>
      <if test="taxPrice != null">
        `tax_price`,
      </if>
      <if test="usrNo != null">
        `usr_no`,
      </if>
      <if test="deliveryDeadline != null">
        `delivery_deadline`,
      </if>
      <if test="distributeParent != null">
        `distribute_parent`,
      </if>
    </trim>
  </sql>
  <!--按照主键值进行操作-->
  <sql id="pkWhere">
    WHERE `order_product_id` = #{primaryKey} AND enabled_flag = 1
  </sql>
  <!--操作条件-->
  <sql id="whereCondition">
    <if test="example != null">
      <trim prefix="WHERE" prefixOverrides="AND|OR">
        AND bop.enabled_flag =1
        <if test="example.orderProductIdNotEquals != null">
          AND bop.`order_product_id` != #{example.orderProductIdNotEquals}
        </if>
        <if test="example.orderProductIdIn != null">
          AND bop.`order_product_id` in (${example.orderProductIdIn})
        </if>
        <if test="example.orderProductIdLessThan != null">
          AND bop.`order_product_id` <![CDATA[ < ]]> #{example.orderProductIdLessThan}
        </if>
        <if test="example.orderSn != null">
          AND bop.`order_sn` = #{example.orderSn}
        </if>
        <if test="example.ruleTag  != null">
          AND bop.`rule_tag` = #{example.ruleTag}
        </if>
        <if test="example.financeRuleCode  != null">
          AND bop.`finance_rule_code` = #{example.financeRuleCode}
        </if>
        <if test="example.orderSnLike != null">
          AND bop.`order_sn` like concat('%',#{example.orderSnLike},'%')
        </if>
        <if test="example.orderSnIn != null and example.orderSnIn.size() > 0">
          AND bop.`order_sn` in
          <foreach collection="example.orderSnIn" separator="," item="orderSn" open="(" close=")" >
             #{orderSn}
          </foreach>
        </if>
        <if test="example.productIdIn != null">
          AND bop.`product_id` in (${example.productIdIn})
        </if>
        <if test="example.storeId != null">
          AND bop.`store_id` = #{example.storeId}
        </if>
        <if test="example.storeName != null">
          AND bop.`store_name` = #{example.storeName}
        </if>
        <if test="example.storeNameLike != null">
          AND bop.`store_name` like concat('%',#{example.storeNameLike},'%')
        </if>
        <if test="example.memberId != null">
          AND bop.`member_id` = #{example.memberId}
        </if>
        <if test="example.goodsCategoryId != null">
          AND bop.`goods_category_id` = #{example.goodsCategoryId}
        </if>
        <if test="example.goodsId != null">
          AND bop.`goods_id` = #{example.goodsId}
        </if>
        <if test="example.goodsName != null">
          AND bop.`goods_name` = #{example.goodsName}
        </if>
        <if test="example.goodsNameLike != null">
          AND bop.`goods_name` like concat('%',#{example.goodsNameLike},'%')
        </if>
        <if test="example.productImage != null">
          AND bop.`product_image` = #{example.productImage}
        </if>
        <if test="example.specValues != null">
          AND bop.`spec_values` = #{example.specValues}
        </if>
        <if test="example.productId != null">
          AND bop.`product_id` = #{example.productId}
        </if>
        <if test="example.productShowPrice != null">
          AND bop.`product_show_price` = #{example.productShowPrice}
        </if>
        <if test="example.productNum != null">
          AND bop.`product_num` = #{example.productNum}
        </if>
        <if test="example.activityDiscountAmount != null">
          AND bop.`activity_discount_amount` = #{example.activityDiscountAmount}
        </if>
        <if test="example.activityDiscountDetail != null">
          AND bop.`activity_discount_detail` = #{example.activityDiscountDetail}
        </if>
        <if test="example.storeActivityAmount != null">
          AND bop.`store_activity_amount` = #{example.storeActivityAmount}
        </if>
        <if test="example.platformActivityAmount != null">
          AND bop.`platform_activity_amount` = #{example.platformActivityAmount}
        </if>
        <if test="example.xzCardAmount != null">
          AND bop.`xz_card_amount` = #{example.xzCardAmount}
        </if>
        <if test="example.storeVoucherAmount != null">
          AND bop.`store_voucher_amount` = #{example.storeVoucherAmount}
        </if>
        <if test="example.platformVoucherAmount != null">
          AND bop.`platform_voucher_amount` = #{example.platformVoucherAmount}
        </if>
        <if test="example.deliveryState != null">
          AND bop.`delivery_state` = #{example.deliveryState}
        </if>
        <if test="example.deliveryStateNotEquals != null">
          AND bop.`delivery_state` != #{example.deliveryStateNotEquals}
        </if>
        <if test="example.integral != null">
          AND bop.`integral` = #{example.integral}
        </if>
        <if test="example.integralCashAmount != null">
          AND bop.`integral_cash_amount` = #{example.integralCashAmount}
        </if>
        <if test="example.moneyAmount != null">
          AND bop.`money_amount` = #{example.moneyAmount}
        </if>
        <if test="example.commissionRate != null">
          AND bop.`commission_rate` = #{example.commissionRate}
        </if>
        <if test="example.commissionAmount != null">
          AND bop.`commission_amount` = #{example.commissionAmount}
        </if>
        <if test="example.spellTeamId != null">
          AND bop.`spell_team_id` = #{example.spellTeamId}
        </if>
        <if test="example.isGift != null">
          AND bop.`is_gift` = #{example.isGift}
        </if>
        <if test="example.giftId != null">
          AND bop.`gift_id` = #{example.giftId}
        </if>
        <if test="example.returnNumber != null">
          AND bop.`return_number` = #{example.returnNumber}
        </if>
        <if test="example.replacementNumber != null">
          AND bop.`replacement_number` = #{example.replacementNumber}
        </if>
        <if test="example.isComment != null">
          AND bop.`is_comment` = #{example.isComment}
        </if>
        <if test="example.commentTimeAfter != null">
          AND bop.`comment_time` <![CDATA[ >= ]]> #{example.commentTimeAfter}
        </if>
        <if test="example.commentTimeBefore != null">
          AND bop.`comment_time` <![CDATA[ <= ]]> #{example.commentTimeBefore}
        </if>
        <if test="example.deliveryDeadlineAfter != null">
          AND bop.`delivery_deadline` <![CDATA[ > ]]> #{example.deliveryDeadlineAfter}
        </if>
        <if test="example.deliveryDeadlineBefore != null">
          AND bop.`delivery_deadline` <![CDATA[ <= ]]> #{example.deliveryDeadlineBefore}
        </if>
        <if test="example.distributeParent != null">
          AND bop.`distribute_parent` = #{example.distributeParent}
        </if>
        <if test="example.performanceChannel != null">
          AND bop.`performance_channel` = #{example.performanceChannel}
        </if>
      </trim>
    </if>
    <if test="example == null">
      where bop.enabled_flag =1
    </if>
  </sql>
  <!--排序条件-->
  <sql id="orderBy">
    ORDER BY `order_product_id` DESC
  </sql>
  <sql id="orderByOther">
    order by ${example.orderBy}
  </sql>
  <!--分组条件-->
  <sql id="groupBy">
    group by ${example.groupBy}
  </sql>
  <!--分页条件-->
  <sql id="limit">
    <if test="size != null and size &gt; 0">
      limit #{startRow},#{size}
    </if>
  </sql>
  <!--查询符合条件的记录数-->
  <select id="countByExample" parameterType="com.cfpamf.ms.mallorder.request.OrderProductExample" resultType="java.lang.Integer">
    SELECT
      COUNT(*)
    FROM `bz_order_product` bop
    <include refid="whereCondition" />
  </select>
  <!--根据主键查询记录-->
  <select id="getByPrimaryKey" resultMap="resultMap">
    SELECT
      *
    FROM `bz_order_product`
    <include refid="pkWhere" />
  </select>
  <!--查询符合条件的记录(所有字段)-->
  <select id="listByExample" resultType="com.cfpamf.ms.mallorder.po.OrderProductPO">
    SELECT
      *
    FROM `bz_order_product` bop
    <include refid="whereCondition" />
    <if test="example.groupBy != null">
      <include refid="groupBy" />
    </if>
    <choose>
      <when test="example.orderBy != null">
        <include refid="orderByOther" />
      </when>
      <otherwise>
        <include refid="orderBy" />
      </otherwise>
    </choose>
  </select>
  <!--分页查询符合条件的记录(所有字段)-->
  <select id="listPageByExample" resultMap="resultMap">
    SELECT
      *
    FROM `bz_order_product` bop
    <include refid="whereCondition" />
    <if test="example.groupBy != null">
      <include refid="groupBy" />
    </if>
    <choose>
      <when test="example.orderBy != null">
        <include refid="orderByOther" />
      </when>
      <otherwise>
        <include refid="orderBy" />
      </otherwise>
    </choose>
    <include refid="limit" />
  </select>
  <!--查询符合条件的记录(指定字段)-->
  <select id="listFieldsByExample" resultMap="resultMap">
    SELECT
      ${fields}
    FROM `bz_order_product` bop
    <include refid="whereCondition" />
    <if test="example.groupBy != null">
      <include refid="groupBy" />
    </if>
    <choose>
      <when test="example.orderBy != null">
        <include refid="orderByOther" />
      </when>
      <otherwise>
        <include refid="orderBy" />
      </otherwise>
    </choose>
  </select>
  <!--分页查询符合条件的记录(指定字段)-->
  <select id="listFieldsPageByExample" resultMap="resultMap">
    SELECT
      ${fields}
    FROM `bz_order_product` bop
    <include refid="whereCondition" />
    <if test="example.groupBy != null">
      <include refid="groupBy" />
    </if>
    <choose>
      <when test="example.orderBy != null">
        <include refid="orderByOther" />
      </when>
      <otherwise>
        <include refid="orderBy" />
      </otherwise>
    </choose>
    <include refid="limit" />
  </select>
  <!--根据条件删除记录，可多条删除-->
  <update id="deleteByExample">
    update `bz_order_product` bop SET enabled_flag =0
    <include refid="whereCondition" />
  </update>
  <!--根据主键删除记录-->
  <update id="deleteByPrimaryKey">
    update `bz_order_product`SET enabled_flag =0
    <include refid="pkWhere" />
  </update>
  <!--&lt;!&ndash;插入一条记录&ndash;&gt;
  <insert id="insert" keyColumn="order_product_id" keyProperty="orderProductId" parameterType="com.cfpamf.ms.mallorder.po.OrderProductPO" useGeneratedKeys="true">
    INSERT INTO `bz_order_product`(
    <include refid="columns" />
    )
    VALUES(
    <trim suffixOverrides=",">
      <if test="orderSn != null">
        #{orderSn},
      </if>
      <if test="taxRate != null">
        #{taxRate},
      </if>
      <if test="cost != null">
        #{cost},
      </if>
      <if test="financeRuleCode != null">
        #{financeRuleCode},
      </if>
      <if test="ruleTag != null">
        #{ruleTag},
      </if>
      <if test="distributeParent != null">
        #{distributeParent},
      </if>
      <if test="storeId != null">
        #{storeId},
      </if>
      <if test="storeName != null">
        #{storeName},
      </if>
      <if test="memberId != null">
        #{memberId},
      </if>
      <if test="goodsCategoryId != null">
        #{goodsCategoryId},
      </if>
      <if test="goodsId != null">
        #{goodsId},
      </if>
      <if test="goodsName != null">
        #{goodsName},
      </if>
      <if test="productImage != null">
        #{productImage},
      </if>
      <if test="specValues != null">
        #{specValues},
      </if>
      <if test="productId != null">
        #{productId},
      </if>
      <if test="productShowPrice != null">
        #{productShowPrice},
      </if>
      <if test="productNum != null">
        #{productNum},
      </if>
      <if test="activityDiscountAmount != null">
        #{activityDiscountAmount},
      </if>
      <if test="activityDiscountDetail != null">
        #{activityDiscountDetail},
      </if>
      <if test="storeActivityAmount != null">
        #{storeActivityAmount},
      </if>
      <if test="platformActivityAmount != null">
        #{platformActivityAmount},
      </if>
      <if test="storeVoucherAmount != null">
        #{storeVoucherAmount},
      </if>
      <if test="platformVoucherAmount != null">
        #{platformVoucherAmount},
      </if>
      <if test="integral != null">
        #{integral},
      </if>
      <if test="integralCashAmount != null">
        #{integralCashAmount},
      </if>
      <if test="moneyAmount != null">
        #{moneyAmount},
      </if>
      <if test="commissionRate != null">
        #{commissionRate},
      </if>
      <if test="commissionAmount != null">
        #{commissionAmount},
      </if>
      <if test="orderCommission != null">
        #{orderCommission},
      </if>
      <if test="businessCommission != null">
        #{businessCommission},
      </if>
      <if test="serviceFee != null">
        #{serviceFee},
      </if>
      <if test="thirdpartnarFee != null">
        #{thirdpartnarFee},
      </if>
      <if test="spellTeamId != null">
        #{spellTeamId},
      </if>
      <if test="isGift != null">
        #{isGift},
      </if>
      <if test="giftId != null">
        #{giftId},
      </if>
      <if test="returnNumber != null">
        #{returnNumber},
      </if>
      <if test="replacementNumber != null">
        #{replacementNumber},
      </if>
      <if test="isComment != null">
        #{isComment},
      </if>
      <if test="commentTime != null">
        #{commentTime},
      </if>
      <if test="landingPrice != null">
        #{landingPrice},
      </if>
      <if test="taxPrice != null">
        #{taxPrice},
      </if>
      <if test="usrNo != null">
        #{usrNo},
      </if>
    </trim>
    )
  </insert>-->
  <!--按条件更新记录中不为空的字段-->
  <update id="updateByExampleSelective">
    UPDATE `bz_order_product` bop
    <trim prefix="SET" suffixOverrides=",">
      <if test="record.orderSn != null">
        `order_sn` = #{record.orderSn},
      </if>
      <if test="record.financeRuleCode != null">
        `finance_rule_code` = #{record.financeRuleCode},
      </if>
      <if test="record.ruleTag != null">
        `rule_tag` = #{record.ruleTag},
      </if>
        <if test="record.distributeParent != null">
            `distribute_parent` = #{record.distributeParent},
        </if>
      <if test="record.storeId != null">
        `store_id` = #{record.storeId},
      </if>
      <if test="record.storeName != null">
        `store_name` = #{record.storeName},
      </if>
      <if test="record.memberId != null">
        `member_id` = #{record.memberId},
      </if>
      <if test="record.goodsCategoryId != null">
        `goods_category_id` = #{record.goodsCategoryId},
      </if>
      <if test="record.goodsId != null">
        `goods_id` = #{record.goodsId},
      </if>
      <if test="record.goodsName != null">
        `goods_name` = #{record.goodsName},
      </if>
      <if test="record.productImage != null">
        `product_image` = #{record.productImage},
      </if>
      <if test="record.specValues != null">
        `spec_values` = #{record.specValues},
      </if>
      <if test="record.productId != null">
        `product_id` = #{record.productId},
      </if>
      <if test="record.productShowPrice != null">
        `product_show_price` = #{record.productShowPrice},
      </if>
      <if test="record.productNum != null">
        `product_num` = #{record.productNum},
      </if>
      <if test="record.activityDiscountAmount != null">
        `activity_discount_amount` = #{record.activityDiscountAmount},
      </if>
      <if test="record.activityDiscountDetail != null">
        `activity_discount_detail` = #{record.activityDiscountDetail},
      </if>
      <if test="record.storeActivityAmount != null">
        `store_activity_amount` = #{record.storeActivityAmount},
      </if>
      <if test="record.platformActivityAmount != null">
        `platform_activity_amount` = #{record.platformActivityAmount},
      </if>
      <if test="record.storeVoucherAmount != null">
        `store_voucher_amount` = #{record.storeVoucherAmount},
      </if>
      <if test="record.platformVoucherAmount != null">
        `platform_voucher_amount` = #{record.platformVoucherAmount},
      </if>
      <if test="record.platformVoucherNetValue != null">
        `platform_voucher_net_value` = #{record.platformVoucherNetValue},
      </if>
      <if test="record.xzCardAmount != null">
        `xz_card_amount` = #{record.xzCardAmount},
      </if>

      <if test="record.integral != null">
        `integral` = #{record.integral},
      </if>
      <if test="record.integralCashAmount != null">
        `integral_cash_amount` = #{record.integralCashAmount},
      </if>
      <if test="record.moneyAmount != null">
        `money_amount` = #{record.moneyAmount},
      </if>
      <if test="record.deliveryDeadline != null">
        `delivery_deadline` = #{record.deliveryDeadline},
      </if>
      <if test="record.commissionRate != null">
        `commission_rate` = #{record.commissionRate},
      </if>
      <if test="record.commissionAmount != null">
        `commission_amount` = #{record.commissionAmount},
      </if>
      <if test="record.orderCommission != null">
        `order_commission` = #{record.orderCommission},
      </if>
      <if test="record.businessCommission != null">
        `business_commission` = #{record.businessCommission},
      </if>
      <if test="record.serviceFee != null">
        `service_fee` = #{record.serviceFee},
      </if>
      <if test="record.thirdpartnarFee != null">
        `thirdpartnar_fee` = #{record.thirdpartnarFee},
      </if>
      <if test="record.spellTeamId != null">
        `spell_team_id` = #{record.spellTeamId},
      </if>
      <if test="record.isGift != null">
        `is_gift` = #{record.isGift},
      </if>
      <if test="record.giftId != null">
        `gift_id` = #{record.giftId},
      </if>
      <if test="record.returnNumber != null">
        `return_number` = #{record.returnNumber},
      </if>
      <if test="record.replacementNumber != null">
        `replacement_number` = #{record.replacementNumber},
      </if>
      <if test="record.isComment != null">
        `is_comment` = #{record.isComment},
      </if>
      <if test="record.commentTime != null">
        `comment_time` = #{record.commentTime},
      </if>
    </trim>
    <include refid="whereCondition" />
  </update>
  <!--按照主键更新记录中不为空的字段-->
  <update id="updateByPrimaryKeySelective">
    UPDATE `bz_order_product`
    <trim prefix="SET" suffixOverrides=",">
      <if test="orderSn != null">
        `order_sn` = #{orderSn},
      </if>
      <if test="financeRuleCode != null">
        `finance_rule_code` = #{financeRuleCode},
      </if>
      <if test="ruleTag != null">
        `rule_tag` = #{ruleTag},
      </if>
        <if test="distributeParent != null">
            `distribute_parent` = #{distributeParent},
        </if>
      <if test="storeId != null">
        `store_id` = #{storeId},
      </if>
      <if test="storeName != null">
        `store_name` = #{storeName},
      </if>
      <if test="memberId != null">
        `member_id` = #{memberId},
      </if>
      <if test="goodsCategoryId != null">
        `goods_category_id` = #{goodsCategoryId},
      </if>
      <if test="goodsId != null">
        `goods_id` = #{goodsId},
      </if>
      <if test="goodsName != null">
        `goods_name` = #{goodsName},
      </if>
      <if test="productImage != null">
        `product_image` = #{productImage},
      </if>
      <if test="specValues != null">
        `spec_values` = #{specValues},
      </if>
      <if test="productId != null">
        `product_id` = #{productId},
      </if>
      <if test="productShowPrice != null">
        `product_show_price` = #{productShowPrice},
      </if>
      <if test="productNum != null">
        `product_num` = #{productNum},
      </if>
      <if test="activityDiscountAmount != null">
        `activity_discount_amount` = #{activityDiscountAmount},
      </if>
      <if test="xzCardAmount != null">
        `xz_card_amount` = #{xzCardAmount},
      </if>
      <if test="activityDiscountDetail != null">
        `activity_discount_detail` = #{activityDiscountDetail},
      </if>
      <if test="storeActivityAmount != null">
        `store_activity_amount` = #{storeActivityAmount},
      </if>
      <if test="platformActivityAmount != null">
        `platform_activity_amount` = #{platformActivityAmount},
      </if>
      <if test="storeVoucherAmount != null">
        `store_voucher_amount` = #{storeVoucherAmount},
      </if>
      <if test="platformVoucherAmount != null">
        `platform_voucher_amount` = #{platformVoucherAmount},
      </if>
      <if test="platformVoucherNetValue != null">
        `platform_voucher_net_value` = #{platformVoucherNetValue},
      </if>
      <if test="integral != null">
        `integral` = #{integral},
      </if>
      <if test="integralCashAmount != null">
        `integral_cash_amount` = #{integralCashAmount},
      </if>
      <if test="moneyAmount != null">
        `money_amount` = #{moneyAmount},
      </if>
      <if test="deliveryDeadline != null">
        `delivery_deadline` = #{deliveryDeadline},
      </if>
      <if test="commissionRate != null">
        `commission_rate` = #{commissionRate},
      </if>
      <if test="commissionAmount != null">
        `commission_amount` = #{commissionAmount},
      </if>
      <if test="orderCommission != null">
        `order_commission` = #{orderCommission},
      </if>
      <if test="businessCommission != null">
        `business_commission` = #{businessCommission},
      </if>
      <if test="serviceFee != null">
        `service_fee` = #{serviceFee},
      </if>
      <if test="thirdpartnarFee != null">
        `thirdpartnar_fee` = #{thirdpartnarFee},
      </if>
      <if test="spellTeamId != null">
        `spell_team_id` = #{spellTeamId},
      </if>
      <if test="isGift != null">
        `is_gift` = #{isGift},
      </if>
      <if test="giftId != null">
        `gift_id` = #{giftId},
      </if>
      <if test="returnNumber != null">
        `return_number` = #{returnNumber},
      </if>
      <if test="replacementNumber != null">
        `replacement_number` = #{replacementNumber},
      </if>
      <if test="isComment != null">
        `is_comment` = #{isComment},
      </if>
      <if test="commentTime != null">
        `comment_time` = #{commentTime},
      </if>
    </trim>
    WHERE `order_product_id` = #{orderProductId}
  </update>

  <update id="returnAll" parameterType="java.lang.String">
    update bz_order_product set return_number = product_num
    where order_sn = #{orderSn}
  </update>

  <update id="addReturnNumber" parameterType="com.cfpamf.ms.mallorder.po.OrderProductPO">
        update bz_order_product set return_number = return_number + #{returnNumber}
        where order_product_id = #{orderProductId}
        and return_number + #{returnNumber} &lt;= product_num
  </update>

  <update id="deductReturnNumber" parameterType="com.cfpamf.ms.mallorder.po.OrderProductPO">
    update bz_order_product set return_number = return_number - #{returnNumber}
    where order_product_id = #{orderProductId}
      and return_number >= #{returnNumber}
  </update>

  <select id="salesGroupByCategoryId" resultType="com.cfpamf.ms.mallorder.po.OrderProductPO">
    SELECT bop.goods_category_id , sum(bop.money_amount) as money_amount
    FROM bz_order bo, bz_order_product bop
    WHERE bo.order_sn= bop.order_sn
    and bo.enabled_flag= 1 and bop.enabled_flag= 1
    AND pay_time>= #{payTimeAfter}
    AND pay_time&lt; #{payTimeBefore}
    AND order_state in
    <foreach collection="orderStateList" item="status" open="(" separator="," close=")">
      #{status}
    </foreach>
    group by bop.goods_category_id
  </select>

  <select id="orderProductDelivery" resultType="com.cfpamf.ms.mallorder.vo.OrderProductDeliveryVO">
    select bop.order_sn
         , bop.order_product_id
         , bop.goods_id
         , bop.goods_name
         , bop.is_gift
         , bop.gift_group
         , bop.product_show_price
         , bop.money_amount
         , bop.product_num
         , bop.delivery_num
         , bop.product_id
         , bop.spec_values
         , bop.product_image
         , bop.delivery_state
         ,bop.channel_sku_id
         ,bop.channel_new_sku_id
         , bol.logistic_id
         , bol.order_sn
         , bol.deliver_type
         , bol.express_id
         , bol.express_name
         , bol.express_company_code
         , bol.express_number
         , bol.deliver_name
         , bol.deliver_mobile
         , bol.create_time deliverTime
         , bop.performance_mode performanceMode
         , bop.return_number returnNumber
         , boe.receiver_name
         , boe.receiver_mobile
         , boe.receiver_address
         , boe.receiver_info
         , boe.receiver_area_info
         , bol.deliver_warehouse
         , bol.deliver_warehouse_name
    from bz_order_product bop
           left join bz_order_logistic bol on bop.logistic_id = bol.logistic_id
    left join bz_order_extend boe on bop.order_sn = boe.order_sn
    where bop.enabled_flag = 1
    <if test="orderSn != null and orderSn != ''">
      and bop.order_sn = #{orderSn}
    </if>
    <if test="orderProductId != null">
      and bop.order_product_id = #{orderProductId}
    </if>

  </select>

  <select id="orderProductDeliveryTrace" resultType="com.cfpamf.ms.mallorder.vo.OrderProductDeliveryVO">
    select bop.order_sn
    , bop.order_product_id
    , bop.goods_id
    , bop.goods_name
    , bop.product_show_price
    , bop.money_amount
    , bop.product_num
    , bop.delivery_num
    , bop.product_id
    , bop.spec_values
    , bop.product_image
    , bop.delivery_state
    ,bop.channel_sku_id
    ,bop.channel_new_sku_id
    , bol.logistic_id
    , bol.order_sn
    , bol.deliver_type
    , bol.express_id
    , bol.express_name
    , bol.express_company_code
    , bol.express_number
    , bol.deliver_name
    , bol.deliver_mobile
    , bol.create_time deliverTime
    , bop.performance_mode performanceMode
    , bop.return_number returnNumber
    , boe.receiver_name
    , boe.receiver_mobile
    , boe.receiver_address
    , boe.receiver_info
    , boe.receiver_area_info
    , bol.deliver_warehouse
    , bol.deliver_warehouse_name
    , boli.delivery_num deliveringNum
    from bz_order_product bop
    left join bz_order_logistic bol on bop.logistic_id = bol.logistic_id
    left join bz_order_extend boe on bop.order_sn = boe.order_sn
    left join bz_order_logistic_item boli on bop.order_product_id = boli.order_product_id
    where bop.enabled_flag = 1
    <if test="orderSn != null and orderSn != ''">
      and bop.order_sn = #{orderSn}
    </if>
    <if test="orderProductId != null">
      and bop.order_product_id = #{orderProductId}
    </if>

  </select>

  <select id="listProductDeliveryInfo" parameterType="com.cfpamf.ms.mallorder.request.OrderProductExample" resultType="com.cfpamf.ms.mallorder.vo.OrderProductDeliveryVO">
    select bop.order_sn, bop.order_product_id, bop.store_id, bop.store_name, bop.goods_id, bop.goods_name, bop.product_show_price, bop.money_amount, bop.product_num, bop.product_id, bop.spec_values, bop.product_image,
    bop.delivery_state, bop.logistic_id, bop.performance_mode performanceMode, bop.return_number returnNumber, boe.receiver_name, boe.receiver_mobile, boe.receiver_address, boe.receiver_info, boe.receiver_area_info
    from bz_order_product bop
    left join bz_order_extend boe
    on bop.order_sn = boe.order_sn
    join bz_order bo
    on bop.order_sn = bo.order_sn and bo.order_state in (20,25)
    <include refid="whereCondition" />
    <if test="example.groupBy != null">
      <include refid="groupBy" />
    </if>
    <choose>
      <when test="example.orderBy != null">
        <include refid="orderByOther" />
      </when>
      <otherwise>
        <include refid="orderBy" />
      </otherwise>
    </choose>
    <if test="example.limit != null and example.limit > 0">
      limit #{example.limit}
    </if>
  </select>

  <select id="obtainProductDeliveryNum" resultType="java.util.Map">
    select  bop.channel_sku_id                                  skuId,
            bop.channel_sku_unit                                skuUnit,
            ifnull(sum(bop.product_num - bop.return_number), 0) deliveryQty
    from bz_order_product bop,
         bz_order_extend boe,
         bz_order_logistic bol
    where bop.order_sn = boe.order_sn
      and bop.logistic_id = bol.logistic_id
      and bop.delivery_state = 1
      and bop.channel_sku_id in
      <foreach collection="skuUnitCodeList" separator="," item="skuUnitCode" open="(" close=")" >
        #{skuUnitCode}
      </foreach>
      and bop.store_id = #{storeId}
      and boe.branch = #{branch}
      and bol.create_time > '2022-03-01'
      group by bop.channel_sku_id, bop.channel_sku_unit;
  </select>

  <select id="selectRefundableOrderProduct" resultType="com.cfpamf.ms.mallorder.po.OrderProductPO">
    SELECT order_product_id,
      member_id,
      order_sn,
      goods_id,
      goods_name,
      product_image,
      spec_values,
      product_id,
      product_num,
      return_number,
      money_amount,
      integral,
      is_gift,
      gift_group
    FROM `bz_order_product` bop
    where bop.enabled_flag = 1
      and bop.order_sn = #{orderSn}
      and bop.return_number &lt; bop.product_num
    order by order_product_id desc
  </select>

  <select id="selectCostByOrderSn" resultType="com.cfpamf.ms.mallorder.po.OrderProductPO">
    select t.*
    from bz_order_product t
    where t.cost is null
      and t.batch_no is not null
      and t.batch_no != ''
      <if test = "orderSn != null and orderSn != ''">
        and t.order_sn = #{orderSn}
      </if>
  </select>

  <select id="listSkuMaterialCode" resultType="java.lang.String">
    select distinct(sku_material_code)
    FROM `bz_order_product`
    WHERE `sku_material_code` IS NOT NULL
  </select>

  <select id="updatePerformanceChannel" >
    update bz_order t,bz_order_product p
    set p.performance_channel = 4
    where t.order_sn = p.order_sn
      and t.order_state <![CDATA[ < ]]> 30
      and p.delivery_state = 0
      and t.order_state != 0
      and t.store_id in (230002,283303,287802,313822,335135)
  </select>

  <select id="getOrderProductToInitPackageItem" resultType="com.cfpamf.ms.mallorder.po.OrderProductPO">

    select *
    from bz_order_product t
    where t.logistic_id != 0
    order by t.order_product_id
    limit #{start},#{end}

  </select>

  <select id="airEnergySalesCount" resultType="java.lang.Integer">
    SELECT
      IFNULL(sum(bop.product_num), 0) totalNum
    FROM
      bz_order bo,
      bz_order_extend boe,
      bz_order_product bop
    WHERE
      bo.order_sn = boe.order_sn
      AND bo.order_sn = bop.order_sn
      AND bo.store_id IN (293539, 337703)
      and bop.goods_category_id = '1000001759'
      AND bo.order_state in (20,25,30,40)
    <if test="branchCode!= null and branchCode!= ''">
      AND boe.branch = #{branchCode}
    </if>
    <if test="areaCode!= null and areaCode!= ''">
      AND boe.area_code = #{areaCode}
    </if>
  </select>

  <update id="updateDeliveryState">
    update bz_order_product
    set delivery_num = product_num,delivery_state = #{deliveryState}
    where
    <if test="orderProductIdList != null and orderProductIdList.size() > 0">
       order_product_id in
      <foreach collection="orderProductIdList" separator="," item="orderProductId" open="(" close=")" >
        #{orderProductId}
      </foreach>
    </if>
  </update>

  <select id="queryByRebateProduct" resultType="com.cfpamf.ms.mallorder.vo.OrderProductRebateVO">
    SELECT
      bop.`order_sn`,
      bo.`pay_time`,
      bop.`product_id`,
      bop.`goods_id`,
      bop.`sku_material_code`,
      bop.`sku_material_name`,
      bop.`channel_source` ,
      bop.`channel_sku_unit`,
      bop.`channel_sku_id` ,
      bop.`goods_name` ,
      bop.`product_num` ,
      bop.`return_number` ,
      (bop.`product_num` - bop.`return_number`) as validProductNum,
      bop.`spec_values` ,
      bop.`weight`
    FROM
      `bz_order` bo
      join `bz_order_product` bop on bo.`order_sn` = bop.`order_sn` and bop.`enabled_flag` = 1
    WHERE bo.enabled_flag = 1
      and bo.order_state = 40
      and bo.user_no = #{dto.userNo}
      and bop.sku_material_code in
      <foreach collection="dto.materialCodeList" item="materialCode" open="(" close=")" separator=",">
        #{materialCode}
      </foreach>
      and bo.`pay_time` <![CDATA[ >= ]]> CONCAT(#{dto.payStartTime},' ','00:00:00')
      and bo.`pay_time` <![CDATA[ <= ]]> CONCAT(#{dto.payEndTime},' ','23:59:59')
  </select>
  <select id="countPayOrderNum" resultType="java.lang.Integer">
      select
        count(DISTINCT bo.order_sn )
      from
        bz_order bo
      left join bz_order_product bop on bo.order_sn = bop.order_sn
      left join bz_order_performance_belongs bopb on bopb.order_sn = bo.order_sn
      where
        pay_time is not null
        and bop.product_id in
        <foreach collection="productIdList" item="item" open="(" close=")" separator=",">
          #{item}
        </foreach>
        and #{orderSn} >= bo.order_sn
        and bopb.employee_branch_code in
        <foreach collection="branchCodeList" item="item" open="(" close=")" separator=",">
          #{item}
        </foreach>
  </select>

  <select id="queryBuyCommodityRebateProduct" resultType="com.cfpamf.ms.mallorder.vo.OrderProductRebateVO">
    SELECT
    bop.`order_sn`,
    bo.`pay_time`,
    bop.`product_id`,
    bop.`goods_id`,
    bop.`sku_material_code`,
    bop.`sku_material_name`,
    bop.`channel_source` ,
    bop.`channel_sku_unit`,
    bop.`channel_sku_id` ,
    bop.`goods_name` ,
    bop.`product_num` ,
    bop.`return_number` ,
    (bop.`product_num` - bop.`return_number`) as validProductNum,
    bop.`spec_values` ,
    bop.`weight`
    FROM
    `bz_order` bo
    join `bz_order_product` bop on bo.`order_sn` = bop.`order_sn` and bop.`enabled_flag` = 1
    join `bz_order_performance_belongs` boe on bo.`order_sn` = boe.`order_sn` and boe.`enabled_flag` = 1
    WHERE bo.enabled_flag = 1
    and bo.order_state = 40
    and bop.sku_material_code in
    <foreach collection="dto.materialCodeList" item="materialCode" open="(" close=")" separator=",">
      #{materialCode}
    </foreach>
    and boe.`belonger_employee_no` = #{dto.managerJobNum}
    <if test="dto.branchCode != null and dto.branchCode != ''">
      and boe.employee_branch_code = #{dto.branchCode}
    </if>
    and bo.`pay_time` <![CDATA[ >= ]]> CONCAT(#{dto.payStartTime},' ','00:00:00')
    and bo.`pay_time` <![CDATA[ <= ]]> CONCAT(#{dto.payEndTime},' ','23:59:59')
    and bo.finish_time <![CDATA[ < ]]> CONCAT(#{dto.orderFinishTime},' ','00:00:00')

    <if test="dto.whiteOrderSnList != null and dto.whiteOrderSnList.size() > 0">
      union

      SELECT
      bop.`order_sn`,
      bo.`pay_time`,
      bop.`product_id`,
      bop.`goods_id`,
      bop.`sku_material_code`,
      bop.`sku_material_name`,
      bop.`channel_source` ,
      bop.`channel_sku_unit`,
      bop.`channel_sku_id` ,
      bop.`goods_name` ,
      bop.`product_num` ,
      bop.`return_number` ,
      (bop.`product_num` - bop.`return_number`) as validProductNum,
      bop.`spec_values` ,
      bop.`weight`
      FROM
      `bz_order` bo
      join `bz_order_product` bop on bo.`order_sn` = bop.`order_sn` and bop.`enabled_flag` = 1
      join `bz_order_performance_belongs` boe on bo.`order_sn` = boe.`order_sn` and boe.`enabled_flag` = 1
      WHERE bo.enabled_flag = 1
      and bo.order_state = 40
      and bop.sku_material_code in
      <foreach collection="dto.materialCodeList" item="materialCode" open="(" close=")" separator=",">
        #{materialCode}
      </foreach>
      and boe.`belonger_employee_no` = #{dto.managerJobNum}
      <if test="dto.branchCode != null and dto.branchCode != ''">
        and boe.employee_branch_code = #{dto.branchCode}
      </if>
      and bo.`pay_time` <![CDATA[ >= ]]> CONCAT(#{dto.payStartTime},' ','00:00:00')
      and bo.`order_sn` in
      <foreach collection="dto.whiteOrderSnList" item="orderSn" open="(" close=")" separator=",">
        #{orderSn}
      </foreach>
    </if>

  </select>


  <select id="queryBuyWineGift" resultType="com.cfpamf.ms.mallorder.vo.OrderProductRebateVO">
    SELECT
      bop.`order_sn`,
      bo.`pay_time`,
      bop.`product_id`,
      bop.`goods_id`,
      bop.`sku_material_code`,
      bop.`sku_material_name`,
      bop.`channel_source` ,
      bop.`channel_sku_unit`,
      bop.`channel_sku_id` ,
      bop.`goods_name` ,
      bop.`product_num` ,
      bop.`return_number` ,
      (bop.`product_num` - bop.`return_number`) as validProductNum,
      bop.`spec_values` ,
      bop.`weight`,
      boe.employee_branch_code as branchCode
    FROM
    `bz_order` bo
    join `bz_order_product` bop on bo.`order_sn` = bop.`order_sn` and bop.`enabled_flag` = 1
    join bz_order_performance_belongs boe on bo.order_sn = boe.order_sn and boe.enabled_flag = 1
    WHERE bo.enabled_flag = 1
    and bo.order_state = 40
    and bo.channel != 'OMS'
    and bop.store_id = #{dto.storeId}
    and bop.goods_id in
    <foreach collection="dto.buySpuId" item="spuId" open="(" close=")" separator=",">
      #{spuId}
    </foreach>
    and bo.finish_time <![CDATA[ <= ]]> TIMESTAMPADD(DAY, -#{dto.tradeSucDays}, now())
    <if test="dto.branchCodeList != null and dto.branchCodeList.size() > 0">
      and boe.employee_branch_code in
      <foreach collection="dto.branchCodeList" item="branchCode" open="(" close=")" separator=",">
        #{branchCode}
      </foreach>
    </if>
    <if test="dto.filterOrderTypeList != null and dto.filterOrderTypeList.size() > 0">
      and bo.order_type not in
      <foreach collection="dto.filterOrderTypeList" item="orderType" open="(" close=")" separator=",">
        #{orderType}
      </foreach>
    </if>
    and bo.`pay_time` <![CDATA[ >= ]]> CONCAT(#{dto.buyStartTime},' ','00:00:00')
    and bo.`pay_time` <![CDATA[ <= ]]> CONCAT(#{dto.buyEndTime},' ','23:59:59')

    <if test="dto.whiteOrderSnList != null and dto.whiteOrderSnList.size() > 0">
      union

      SELECT
        bop.`order_sn`,
        bo.`pay_time`,
        bop.`product_id`,
        bop.`goods_id`,
        bop.`sku_material_code`,
        bop.`sku_material_name`,
        bop.`channel_source` ,
        bop.`channel_sku_unit`,
        bop.`channel_sku_id` ,
        bop.`goods_name` ,
        bop.`product_num` ,
        bop.`return_number` ,
        (bop.`product_num` - bop.`return_number`) as validProductNum,
        bop.`spec_values` ,
        bop.`weight`,
        boe.employee_branch_code as branchCode
      FROM
      `bz_order` bo
      join `bz_order_product` bop on bo.`order_sn` = bop.`order_sn` and bop.`enabled_flag` = 1
      join bz_order_performance_belongs boe on bo.order_sn = boe.order_sn and boe.enabled_flag = 1
      WHERE bo.enabled_flag = 1
      and bo.order_state = 40
      and bop.store_id = #{dto.storeId}
      and bop.goods_id in
      <foreach collection="dto.buySpuId" item="spuId" open="(" close=")" separator=",">
        #{spuId}
      </foreach>
      and bo.finish_time <![CDATA[ <= ]]> TIMESTAMPADD(DAY, -#{dto.tradeSucDays}, now())
      <if test="dto.branchCodeList != null and dto.branchCodeList.size() > 0">
        and boe.employee_branch_code in
        <foreach collection="dto.branchCodeList" item="branchCode" open="(" close=")" separator=",">
          #{branchCode}
        </foreach>
      </if>
      and bo.`order_sn` in
      <foreach collection="dto.whiteOrderSnList" item="orderSn" open="(" close=")" separator=",">
        #{orderSn}
      </foreach>
    </if>
  </select>
  <select id="getDistinctChannelSkuIdStartByEight" resultType="java.lang.String">
      SELECT
        DISTINCT (channel_sku_id)
      FROM
        `bz_order_product`
      where
         `enabled_flag` = 1
         and `channel_new_sku_id` is null
  </select>

  <select id="tjBuyNzRebateGift" resultType="com.cfpamf.ms.mallorder.vo.OrderProductRebateVO">
    SELECT
      bop.`order_sn`,
      bo.`pay_time`,
      bop.`product_id`,
      bop.`goods_id`,
      bop.`sku_material_code`,
      bop.`sku_material_name`,
      bop.`channel_source` ,
      bop.`channel_sku_unit`,
      bop.`channel_sku_id` ,
      bop.`goods_name` ,
      bop.`product_num` ,
      bop.`return_number` ,
      (bop.`product_num` - bop.`return_number`) as validProductNum,
      bop.`spec_values` ,
      bop.`weight`
    FROM
    `bz_order` bo
    join `bz_order_product` bop on bo.`order_sn` = bop.`order_sn` and bop.`enabled_flag` = 1
    join `bz_order_extend` boe on bo.`order_sn` = boe.`order_sn` and boe.`enabled_flag` = 1
    WHERE bo.enabled_flag = 1
    and bo.user_no = #{dto.userNo}
    and bo.order_type != 14
    and bo.order_state in
    <foreach collection="dto.orderStateList" item="orderState" open="(" close=")" separator=",">
      #{orderState}
    </foreach>
    <if test="dto.orderPatternList != null and dto.orderPatternList.size() > 0">
      and bo.order_pattern in
      <foreach collection="dto.orderPatternList" item="orderPattern" open="(" close=")" separator=",">
        #{orderPattern}
      </foreach>
    </if>
    and bop.product_id in
    <foreach collection="dto.buySkuId" item="skuId" open="(" close=")" separator=",">
      #{skuId}
    </foreach>
    <if test="dto.branchCode != null and dto.branchCode != ''">
      and boe.branch = #{dto.branchCode}
    </if>
    and bo.`pay_time` <![CDATA[ >= ]]> CONCAT(#{dto.buyStartTime},' ','00:00:00')
    and bo.`pay_time` <![CDATA[ <= ]]> CONCAT(#{dto.buyEndTime},' ','23:59:59')

    <if test="dto.whiteOrderSnList != null and dto.whiteOrderSnList.size() > 0">
      union

      SELECT
        bop.`order_sn`,
        bo.`pay_time`,
        bop.`product_id`,
        bop.`goods_id`,
        bop.`sku_material_code`,
        bop.`sku_material_name`,
        bop.`channel_source` ,
        bop.`channel_sku_unit`,
        bop.`channel_sku_id` ,
        bop.`goods_name` ,
        bop.`product_num` ,
        bop.`return_number` ,
        (bop.`product_num` - bop.`return_number`) as validProductNum,
        bop.`spec_values` ,
        bop.`weight`
      FROM
      `bz_order` bo
      join `bz_order_product` bop on bo.`order_sn` = bop.`order_sn` and bop.`enabled_flag` = 1
      join `bz_order_extend` boe on bo.`order_sn` = boe.`order_sn` and boe.`enabled_flag` = 1
      WHERE bo.enabled_flag = 1
      and bo.user_no = #{dto.userNo}
      and bo.order_type != 14
      and bo.order_sn in
      <foreach collection="dto.whiteOrderSnList" item="orderSn" open="(" close=")" separator=",">
        #{orderSn}
      </foreach>
      <if test="dto.branchCode != null and dto.branchCode != ''">
        and boe.branch = #{dto.branchCode}
      </if>
      and bop.product_id in
      <foreach collection="dto.buySkuId" item="skuId" open="(" close=")" separator=",">
        #{skuId}
      </foreach>
    </if>
  </select>

  <select id="queryOrderInfoByOrderIdList" resultType="com.cfpamf.ms.mallorder.po.OrderProductPO">
    select
      bop.*
    from
    `bz_order` bo
    INNER JOIN `bz_order_product` bop on bo.`order_sn` = bop.`order_sn` and bop.`enabled_flag` = 1
    where
    `bop`.`order_sn` in
    <foreach collection="dtoOrderSnList" item="orderSn" separator="," open="(" close=")">
      #{orderSn}
    </foreach>
    <if test="startPayDate != null">
      and `bo`.`pay_time` <![CDATA[>=]]> #{startPayDate}
    </if>
    and bo.`enabled_flag` = 1
  </select>
  <select id="queryUserAddress" resultType="com.cfpamf.ms.mallorder.dto.OrderAddressDTO">
    select
      boe.receiver_name ,
      boe.receiver_mobile ,
      boe.receiver_province_code as province,
      boe.receiver_city_code as city,
      boe.receiver_district_code as district,
      boe.receiver_town_code as town,
      boe.receiver_address as detailAddress
    from
    `bz_order` bo
    join `bz_order_product` bop on bo.`order_sn` = bop.`order_sn` and bop.`enabled_flag` = 1
    join `bz_order_extend` boe on bo.`order_sn` = boe.`order_sn` and boe.`enabled_flag` = 1
    where bo.enabled_flag = 1
    and bo.user_no = #{userNo}
    <if test="skuId != null">
      and bop.product_id = #{skuId}
    </if>
    order by bo.create_time desc
    limit 1
  </select>
  <select id="queryOrderSnByProductIdAndFinanceRule" resultMap="resultMap">
    select
    *
    from
    `mall_order`.`bz_order_product` bop
    inner join mall_order.bz_order bo on bo.`order_sn` = bop.`order_sn`
    where
    `bo`.`pay_time` <![CDATA[>=]]> '2025-01-01 00:00:00'
    and `bo`.`pay_time` <![CDATA[<=]]> '2025-01-14 23:59:59'
    and bo.`enabled_flag` = 1
    and bop.`enabled_flag` = 1
    <trim prefix="AND (" suffix=")" prefixOverrides="OR">
        <if test="with != null and with.size() > 0">
          (bop.`product_id`,
          bop.`finance_rule_code`) in
          <foreach collection="with" item="w" separator="," open="(" close=")">
            (#{w.productId},#{w.financeRuleCode})
          </foreach>
        </if>
        <if test="without != null and without.size() > 0">
          or (bop.`product_id` in
          <foreach collection="without" item="w" separator="," open="(" close=")">
            #{w.productId}
          </foreach>
          and bop.`finance_rule_code` = ''
          )
        </if>
    </trim>
  </select>
</mapper>
