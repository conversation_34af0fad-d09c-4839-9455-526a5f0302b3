<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.OrderTradeProofMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.mallorder.po.OrderTradeProofPO">
        <result column="id" property="id" />
        <result column="update_time" property="updateTime" />
        <result column="create_time" property="createTime" />
        <result column="enabled_flag" property="enabledFlag" />
        <result column="order_sn" property="orderSn" />
        <result column="order_product_id" property="orderProductId" />
        <result column="material_no" property="materialNo" />
        <result column="material_name" property="materialName" />
        <result column="material_type" property="materialType" />
        <result column="requisite" property="requisite" />
        <result column="max_uploads" property="maxUploads" />
        <result column="scene_no" property="sceneNo" />
        <result column="scene_name" property="sceneName" />
        <result column="is_upload" property="isUpload" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        update_time,
        create_time,
        enabled_flag,
        order_sn, material_no, material_name, material_type, requisite, max_uploads, scene_no, scene_name, is_upload, create_by, update_by
    </sql>

    <select id="queryOrderProofList" resultType="com.cfpamf.ms.mallorder.vo.OrderTradeProofVO">
        select id,
            order_sn,
            order_product_id,
            material_no,
            material_name,
            material_type,
            material_remark,
            example,
            requisite,
            unique_check,
            max_uploads,
            scene_no,
            scene_name,
            same_link_consistent_check,
            dif_link_consistent_check,
            his_consistent_check,
            same_link_pic_repeat_check,
            his_pic_repeat_check,
            ocr_field,
            check_group,
            pic_encrypt,

        is_upload,
        contract_no,
        need_face_auth,
        fdd_contract_no
        from bz_order_trade_proof
        where enabled_flag = 1
        <if test="orderSn != null and orderSn != ''">
            and order_sn = #{orderSn}
        </if>
        <if test="orderProductIdList != null and orderProductIdList.size() > 0">
            AND order_product_id in
            <foreach collection="orderProductIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="sceneNo != null and sceneNo != ''">
            and scene_no = #{sceneNo}
        </if>
        <if test="materialType != null and materialType != ''">
            and material_type = #{materialType}
        </if>
        <if test="materialNo != null and materialNo != ''">
            and material_no = #{materialNo}
        </if>
    </select>

</mapper>
