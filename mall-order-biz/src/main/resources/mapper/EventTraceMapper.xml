<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.EventTraceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.mallorder.po.EventTracePO">
        <result column="id" property="id" />
        <result column="update_time" property="updateTime" />
        <result column="create_time" property="createTime" />
        <result column="enabled_flag" property="enabledFlag" />
        <result column="app_name" property="appName" />
        <result column="executor_no" property="executorNo" />
        <result column="executor_name" property="executorName" />
        <result column="executor_phone" property="executorPhone" />
        <result column="event_channel" property="eventChannel" />
        <result column="operate_type" property="operateType" />
        <result column="node_name" property="nodeName" />
        <result column="target_domain" property="targetDomain" />
        <result column="target_object" property="targetObject" />
        <result column="operate_time" property="operateTime" />
        <result column="content_title" property="contentTitle" />
        <result column="before_value" property="beforeValue" />
        <result column="after_value" property="afterValue" />
        <result column="operate_result" property="operateResult" />
        <result column="operate_remark" property="operateRemark" />
        <result column="create_by" property="createBy" />
        <result column="create_by_name" property="createByName" />
        <result column="update_by" property="updateBy" />
        <result column="update_by_name" property="updateByName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        update_time,
        create_time,
        enabled_flag,
        app_name, executor_no, executor_name, executor_phone, event_channel, operate_type, node_name, target_domain, target_object, operate_time, content_title, before_value, after_value, operate_result, operate_remark, create_by, create_by_name, update_by, update_by_name
    </sql>

</mapper>
