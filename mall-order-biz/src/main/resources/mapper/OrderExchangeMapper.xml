<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.OrderExchangeMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.mallorder.po.OrderExchangePO">
        <result column="exchange_order_id" property="exchangeOrderId" />
        <result column="exchange_sn" property="exchangeSn" />
        <result column="member_id" property="memberId" />
        <result column="store_id" property="storeId" />
        <result column="exchange_reason" property="exchangeReason" />
        <result column="buyer_confirm_flag" property="buyerConfirmFlag" />
        <result column="exchange_order_state" property="exchangeOrderState" />
        <result column="applicant_id" property="applicantId" />
        <result column="applicant_name" property="applicantName" />
        <result column="applicant_role" property="applicantRole" />
        <result column="approver_id" property="approverId" />
        <result column="approver_name" property="approverName" />
        <result column="approver_role" property="approverRole" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="enabled_flag" property="enabledFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        exchange_order_id,exchange_sn,member_id,store_id,exchange_reason,buyer_confirm_flag,exchange_order_state,
        applicant_id,applicant_name,applicant_role,approver_id,approver_name,approver_role,
        create_by, update_by,create_time,update_time, enabled_flag
    </sql>



    <select id="getOrderExchangeByExchangeSn" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from bz_order_exchange
        where exchange_sn = #{exchangeSn,jdbcType=VARCHAR}
    </select>

    <select id="getOrderExchange" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from bz_order_exchange
        <include refid="whereCondition"/>
    </select>

    <select id="getExchangeOrderAutoAuditData" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from bz_order_exchange t
        where t.buyer_confirm_flag = 0
        and t.exchange_order_state = 0
        and t.enabled_flag = 1
        and t.create_time <![CDATA[<=]]> DATE_SUB(now(), INTERVAL + 1 DAY)
    </select>


    <select id="exchangeOrderAutoCancelData" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from bz_order_exchange t
        where t.buyer_confirm_flag = 1
            and t.exchange_order_state = 0
            and t.create_time &lt; #{applicationTime}
    </select>

    <sql id="whereCondition">
        <if test="example != null">
            <trim prefix="WHERE" prefixOverrides="AND|OR">
                AND enabled_flag =1
            </trim>
            <if test="example.exchangeSn != null">
                AND `exchange_sn` = #{example.exchangeSn}
            </if>
            <if test="example.memberId != null">
                AND `member_id` = #{example.memberId}
            </if>
            <if test="example.storeId != null">
                AND `store_id` = #{example.storeId}
            </if>
        </if>
    </sql>

    <select id="getExchangeOrderListByPage" resultType="com.cfpamf.ms.mallorder.dto.ExchangeOrderDetailDTO">
        select boe.exchange_sn,
            boe.exchange_order_state,
            boe.buyer_confirm_flag,
            boe.create_time,
            boed.order_sn,
            boed.order_product_id,
            boed.product_num,
            boed.exchange_order_sn,
            boed.exchange_order_product_id,
            boed.refund_amount,
            bop.product_id,
            bop.goods_id,
            boed.afs_sn
        from bz_order_exchange boe
            left join bz_order_exchange_detail boed on boe.exchange_sn = boed.exchange_sn
            left join bz_order_product bop on boed.exchange_order_product_id = bop.order_product_id
        <include refid="exchangeOrderListWhere"/>
        order by boe.create_time desc
        <include refid="limit"/>
    </select>


    <select id="getExchangeApplyListByPage" resultType="com.cfpamf.ms.mallorder.dto.ExchangeOrderDetailDTO">
        select boe.exchange_sn,
        boe.exchange_order_state,
        boe.buyer_confirm_flag,
        boe.create_time,
        boed.order_sn,
        boed.order_product_id,
        boed.product_num,
        boed.exchange_order_sn,
        boed.exchange_order_product_id,
        boed.refund_amount,
        bop.product_id,
        bop.goods_id,
        boed.afs_sn
        from bz_order_exchange boe
        left join bz_order_exchange_detail boed on boe.exchange_sn = boed.exchange_sn
        left join bz_order_product bop on boed.exchange_order_product_id = bop.order_product_id
        left join bz_order o on bop.order_sn = o.order_sn
        left join bz_order_extend e on e.order_sn = o.order_sn
        <include refid="exchangeOrderListWhereNew"/>
        order by boe.create_time desc
        <include refid="limit"/>
    </select>


    <select id="getExchangeOrderList" resultType="com.cfpamf.ms.mallorder.dto.ExchangeOrderDetailDTO">
            select boe.exchange_sn,
                boe.exchange_order_state,
                boe.buyer_confirm_flag,
                boe.create_time,
                boed.order_sn,
                boed.order_product_id,
                boed.product_num,
                boed.exchange_order_sn,
                boed.exchange_order_product_id,
                boed.refund_amount,
                bop.product_id,
                bop.goods_id,
                boed.afs_sn
            from bz_order_exchange boe
                left join bz_order_exchange_detail boed on boe.exchange_sn = boed.exchange_sn
                left join bz_order_product bop on boed.exchange_order_product_id = bop.order_product_id
            <include refid="exchangeOrderListWhere"/>
            order by boe.create_time desc
        </select>


    <sql id="exchangeOrderListWhere">
        <where>
            boe.enabled_flag = 1
            <if test="example.memberId != null">
                AND boe.member_id = #{example.memberId}
            </if>
            <if test="example.memberIdList != null and example.memberIdList.size > 0">
                AND boe.member_id in
                <foreach collection="example.memberIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="example.storeId != null">
                AND boe.store_id = #{example.storeId}
            </if>
            <if test="example.exchangeSn != null">
                AND boe.exchange_sn = #{example.exchangeSn}
            </if>
            <if test="example.orderSn != null">
                AND boed.order_sn = #{example.orderSn}
            </if>
            <if test="example.exchangeOrderSn != null">
                AND boed.exchange_order_sn = #{example.exchangeOrderSn}
            </if>
            <if test="example.createTimeBefore != null">
                AND boe.create_time <![CDATA[ <= ]]> #{example.createTimeBefore}
            </if>
            <if test="example.createTimeAfter != null">
                AND boe.create_time >= #{example.createTimeAfter}
            </if>
            <!-- 原商品的order_product_id -->
            <if test="example.orderProductId != null">
                AND boed.order_product_id = #{example.orderProductId}
            </if>
            <if test="example.exchangeOrderState != null">
                AND boe.exchange_order_state = #{example.exchangeOrderState}
            </if>
            <if test="example.exchangeOrderStateList != null and example.exchangeOrderStateList.size > 0">
                AND boe.exchange_order_state in
                <foreach collection="example.exchangeOrderStateList" item="exchangeOrderState" open="(" separator="," close=")">
                    #{exchangeOrderState}
                </foreach>
            </if>
        </where>
    </sql>
    <sql id="exchangeOrderListWhereNew">
        <where>
            boe.enabled_flag = 1
            <if test="example.storeId != null">
                AND boe.store_id = #{example.storeId}
            </if>
            <if test="example.exchangeSn != null">
                AND boe.exchange_sn = #{example.exchangeSn}
            </if>
            <if test="example.orderSn != null">
                AND boed.order_sn = #{example.orderSn}
            </if>
            <if test="example.exchangeOrderSn != null">
                AND boed.exchange_order_sn = #{example.exchangeOrderSn}
            </if>
            <if test="example.createTimeBefore != null">
                AND boe.create_time <![CDATA[ <= ]]> #{example.createTimeBefore}
            </if>
            <if test="example.createTimeAfter != null">
                AND boe.create_time >= #{example.createTimeAfter}
            </if>
           <if test="example.exchangeOrderState != null">
                AND boe.exchange_order_state = #{example.exchangeOrderState}
            </if>

            <if test="example.branchCodeList != null and example.branchCodeList.size() > 0">
                AND EXISTS (select 1 from bz_order_performance_belongs bopb where bopb.order_sn = bo.order_sn and bopb.employee_branch_code in
                <foreach collection="example.branchCodeList" item="branchCode" open="(" separator="," close=")">
                    #{branchCode}
                </foreach>
                )
            </if>
            <if test="example.goodsName != null">
                AND bop.goods_name like concat('%',#{example.goodsName},'%')
            </if>
            <if test="example.storeName != null">
                AND o.store_name like concat('%',#{example.storeName},'%')
            </if>
            <if test="example.orderState != null">
                AND  o.order_state = #{example.orderState}
            </if>
            <if test="example.customerId != null">
                AND e.customer_id = #{example.customerId}
            </if>
            <if test="example.managerName != null and example.managerName != ''">
                AND EXISTS (select 1 from bz_order_performance_belongs bopb where bopb.order_sn = bo.order_sn and bopb.belonger_name like concat('%',#{example.managerName},'%'))
            </if>
            <if test="example.customerName != null">
                AND e.customer_name like concat('%',#{example.customerName},'%')
            </if>
            <if test="example.receiverMobile != null and example.receiverMobile != ''">
                AND e.receiver_mobile = #{example.receiverMobile}
            </if>
            <if test="example.receiverName != null and example.receiverName != ''">
                AND e.receiver_name  like concat('%',#{example.receiverName},'%')
            </if>
        </where>
    </sql>

    <!--分页条件-->
    <sql id="limit">
        <if test="size != null and size &gt; 0">
            limit #{startRow},#{size}
        </if>
    </sql>

    <select id="getExchangeOrderListCount" resultType="int">
        select count(1)
        from bz_order_exchange boe
        left join bz_order_exchange_detail boed on boe.exchange_sn = boed.exchange_sn
        left join bz_order_product bop on boed.exchange_order_product_id = bop.order_product_id
        <include refid="exchangeOrderListWhere"/>
    </select>

    <select id="getExchangeApplyListCount" resultType="int">
        select count(1)
        from bz_order_exchange boe
        left join bz_order_exchange_detail boed on boe.exchange_sn = boed.exchange_sn
        left join bz_order_product bop on boed.exchange_order_product_id = bop.order_product_id
        left join bz_order o on bop.order_sn = o.order_sn
        left join bz_order_extend e on e.order_sn = o.order_sn
        <include refid="exchangeOrderListWhereNew"/>
    </select>




    <select id="getExchangeApplyListCountNew" resultType="int">
        select count(1)
        from (
            select a.order_product_id,a.order_sn
            from
            (
            select boed.order_sn,
                boed.order_product_id,
                boed.exchange_order_product_id,
                boed.exchange_order_sn
        from bz_order_exchange boe,bz_order_exchange_detail boed
            where boe.exchange_sn  = boed.exchange_sn
                and boe.enabled_flag = 1
                <include refid="exchangeApplyBaseCondition"/>
            ) a ,bz_order_product bop,bz_order bo,bz_order_extend e
            where a.exchange_order_product_id = bop.order_product_id
            and a.exchange_order_sn = bo.order_sn
            and bo.order_sn = bop.order_sn
            and e.order_sn = bo.order_sn
                <include refid="exchangeApplyNewProductCondition"/>
        ) b
        left join bz_order_product bp on b.order_product_id = bp.order_product_id
    </select>
    <select id="getExchangeApplyListPageNew" resultType="com.cfpamf.ms.mallorder.vo.OrderExchangeReturnListVO">
        select b.*,
            bp.goods_name orgGoodsName,
            bp.supplier_name orgSupplierName,
            bp.product_id orgProductId,
            bp.product_show_price orgProductShowPrice
        from (
             <!--查询换货商品信息及拓展信息-->
            select a.*,
            bop.product_id,
            bop.goods_id,
            bop.goods_name,
            bo.order_state,
            bo.store_id,
            bo.store_name,
            bo.user_no,
            bo.user_mobile,
            bo.payment_name,
            bo.order_amount_total orderAmount,
            bop.product_show_price,
            bop.supplier_name,
            bop.product_num,
            bop.product_image,
            bop.spec_values,
            e.customer_name,
            e.manager_name,
            e.branch_name
            from
            (
                    <!--查询换货单详情及原商品信息-->
                select boe.exchange_sn,
                    boe.exchange_order_state,
                    boe.buyer_confirm_flag,
                    boe.create_time,
                    boed.order_sn,
                    boed.afs_sn orgAfsSn,
                    boed.order_product_id,
                    boed.product_num orgProductNum,
                    boed.exchange_order_sn,
                    boed.exchange_order_product_id,
                    boed.refund_amount,
                    obop.product_image orgProductImage,
                    obop.spec_values orgSpecValues
                from bz_order_exchange boe,
                     bz_order_exchange_detail boed,
                     bz_order_product obop
                where boe.exchange_sn  = boed.exchange_sn
                    and obop.order_product_id = boed.order_product_id
                    and boe.enabled_flag = 1
                    <include refid="exchangeApplyBaseCondition"/>
                ) a ,
                    bz_order_product bop,
                    bz_order bo,
                    bz_order_extend e
                where a.exchange_order_product_id = bop.order_product_id
                    and a.exchange_order_sn = bo.order_sn
                    and bo.order_sn = bop.order_sn
                    and e.order_sn = bo.order_sn
                    <include refid="exchangeApplyNewProductCondition"/>
            ) b
            left join bz_order_product bp on b.order_product_id = bp.order_product_id
            order by b.create_time desc
            <include refid="limit"/>
    </select>

    <sql id="exchangeApplyBaseCondition">
        <if test="example.storeId != null">
            AND boe.store_id = #{example.storeId}
        </if>
        <if test="example.exchangeSn != null">
            AND boe.exchange_sn = #{example.exchangeSn}
        </if>
        <if test="example.orderSn != null">
            AND boed.order_sn = #{example.orderSn}
        </if>
        <if test="example.exchangeOrderSn != null">
            AND boed.exchange_order_sn = #{example.exchangeOrderSn}
        </if>
        <if test="example.createTimeBefore != null">
            AND boe.create_time <![CDATA[ <= ]]> #{example.createTimeBefore}
        </if>
        <if test="example.createTimeAfter != null">
            AND boe.create_time >= #{example.createTimeAfter}
        </if>
        <if test="example.exchangeOrderState != null">
            AND boe.exchange_order_state = #{example.exchangeOrderState}
        </if>
    </sql>


    <sql id="exchangeApplyNewProductCondition">
        <if test="example.goodsName != null">
            AND bop.goods_name like concat('%',#{example.goodsName},'%')
        </if>
        <if test="example.storeName != null">
            AND bo.store_name like concat('%',#{example.storeName},'%')
        </if>
        <if test="example.orderState != null">
            AND  bo.order_state = #{example.orderState}
        </if>
        <if test="example.branchCodeList != null and example.branchCodeList.size() > 0">
            AND EXISTS (select 1 from bz_order_performance_belongs bopb where bopb.order_sn = bo.order_sn and bopb.employee_branch_code in
            <foreach collection="example.branchCodeList" item="branchCode" open="(" separator="," close=")">
                #{branchCode}
            </foreach>
            )
        </if>
        <if test="example.customerId != null">
            AND e.customer_id = #{example.customerId}
        </if>
        <if test="example.managerName != null and example.managerName != ''">
            AND EXISTS (select 1 from bz_order_performance_belongs bopb where bopb.order_sn = bo.order_sn and bopb.belonger_name like concat('%',#{example.managerName},'%'))
        </if>
        <if test="example.managerNameList != null and example.managerNameList.size() > 0">
            AND EXISTS (select 1 from bz_order_performance_belongs bopb where bopb.order_sn = bo.order_sn and bopb.belonger_name in
            <foreach collection="example.managerNameList" item="managerName" open="(" separator="," close=")">
                #{managerName}
            </foreach>
            )
        </if>
        <if test="example.customerName != null">
            AND e.customer_name like concat('%',#{example.customerName},'%')
        </if>
        <if test="example.receiverMobile != null and example.receiverMobile != ''">
            AND e.receiver_mobile = #{example.receiverMobile}
        </if>
        <if test="example.receiverName != null and example.receiverName != ''">
            AND e.receiver_name  like concat('%',#{example.receiverName},'%')
        </if>
    </sql>

    <select id="getBappOrderExchangeCount" resultType="java.lang.Integer">
        select count(1)
        from bz_order_exchange boe
        left join bz_order_exchange_detail boed on boe.exchange_sn = boed.exchange_sn
        left join bz_order_product bop on boed.exchange_order_product_id = bop.order_product_id
        left join bz_order_extend be on boed.order_sn = be.order_sn
        where boe.enabled_flag = 1
        <if test="request.branchList != null and request.branchList.size() >0">
            and be.branch in
            <foreach collection="request.branchList" item="branch" index="index" open="(" close=")" separator=",">
                #{branch}
            </foreach>
        </if>
        <if test="request.userList != null and request.userList.size() >0">
            and be.manager in
            <foreach collection="request.userList" item="manager" index="index" open="(" close=")" separator=",">
                #{manager}
            </foreach>
        </if>
    </select>

    <select id="getBappOrderExchangeList" resultType="com.cfpamf.ms.mallorder.dto.ExchangeOrderDetailDTO">
        select boe.exchange_sn,
        boe.exchange_order_state,
        boe.buyer_confirm_flag,
        boe.create_time,
        boed.order_sn,
        boed.order_product_id,
        boed.product_num,
        boed.exchange_order_sn,
        boed.exchange_order_product_id,
        boed.refund_amount,
        bop.product_id,
        bop.goods_id,
        boed.afs_sn
        from bz_order_exchange boe
        left join bz_order_exchange_detail boed on boe.exchange_sn = boed.exchange_sn
        left join bz_order_product bop on boed.exchange_order_product_id = bop.order_product_id
        left join bz_order_extend be on boed.order_sn = be.order_sn
        where boe.enabled_flag = 1
        <if test="request.branchList != null and request.branchList.size() >0">
            and be.branch in
            <foreach collection="request.branchList" item="branch" index="index" open="(" close=")" separator=",">
                #{branch}
            </foreach>
        </if>
        <if test="request.userList != null and request.userList.size() >0">
            and be.manager in
            <foreach collection="request.userList" item="manager" index="index" open="(" close=")" separator=",">
                #{manager}
            </foreach>
        </if>
        order by boe.create_time desc
        limit #{request.startRow},#{request.pageSize}
    </select>

</mapper>
