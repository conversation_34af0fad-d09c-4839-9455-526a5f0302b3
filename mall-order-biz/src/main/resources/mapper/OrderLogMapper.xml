<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.OrderLogMapper">
  <resultMap id="resultMap" type="com.cfpamf.ms.mallorder.po.OrderLogPO">
      <id column="log_id" property="logId" />
      <result column="create_time" property="createTime" />
      <result column="update_time" property="updateTime" />
      <result column="enabled_flag" property="enabledFlag" />
      <result column="log_role" property="logRole" />
      <result column="log_user_id" property="logUserId" />
      <result column="log_user_name" property="logUserName" />
      <result column="order_sn" property="orderSn" />
      <result column="channel" property="channel" />
      <result column="order_state_log" property="orderStateLog" />
      <result column="loan_pay_state" property="loanPayState" />
      <result column="log_time" property="logTime" />
      <result column="log_content" property="logContent" />
      <result column="remark" property="remark" />
      <result column="create_by" property="createBy" />
      <result column="update_by" property="updateBy" />
  </resultMap>
  <!--除主键外的所有字段，用于插入操作-->
  <sql id="columns">
    <trim suffixOverrides=",">
      <if test="logRole != null">
        `log_role`,
      </if>
      <if test="logUserId != null">
        `log_user_id`,
      </if>
      <if test="logUserName != null">
        `log_user_name`,
      </if>
      <if test="orderSn != null">
        `order_sn`,
      </if>
      <if test="orderStateLog != null">
        `order_state_log`,
      </if>
      <if test="loanPayState != null">
        `loan_pay_state`,
      </if>
      <if test="logTime != null">
        `log_time`,
      </if>
      <if test="logContent != null">
        `log_content`,
      </if>
    </trim>
  </sql>
  <!--按照主键值进行操作-->
  <sql id="pkWhere">
    WHERE `log_id` = #{primaryKey} AND enabled_flag =1
  </sql>
  <!--操作条件-->
  <sql id="whereCondition">
    <if test="example != null">
      <trim prefix="WHERE" prefixOverrides="AND|OR">
        AND enabled_flag =1
        <if test="example.logIdNotEquals != null">
          AND `log_id` != #{example.logIdNotEquals}
        </if>
        <if test="example.logIdIn != null">
          AND `log_id` in (${example.logIdIn})
        </if>
        <if test="example.logRole != null">
          AND `log_role` = #{example.logRole}
        </if>
        <if test="example.logUserId != null">
          AND `log_user_id` = #{example.logUserId}
        </if>
        <if test="example.logUserName != null">
          AND `log_user_name` = #{example.logUserName}
        </if>
        <if test="example.logUserNameLike != null">
          AND `log_user_name` like concat('%',#{example.logUserNameLike},'%')
        </if>
        <if test="example.orderSn != null">
          AND `order_sn` = #{example.orderSn}
        </if>
        <if test="example.orderSnLike != null">
          AND `order_sn` like concat('%',#{example.orderSnLike},'%')
        </if>
        <if test="example.orderPreState != null">
          AND `order_pre_state` = #{example.orderPreState}
        </if>
        <if test="example.orderStateLog != null">
          AND `order_state_log` = #{example.orderStateLog}
        </if>
        <if test="example.loanPayState != null">
          AND `loan_pay_state` = #{example.loanPayState}
        </if>
        <if test="example.logTimeAfter != null">
          AND `log_time` <![CDATA[ >= ]]> #{example.logTimeAfter}
        </if>
        <if test="example.logTimeBefore != null">
          AND `log_time` <![CDATA[ <= ]]> #{example.logTimeBefore}
        </if>
        <if test="example.logContent != null">
          AND `log_content` = #{example.logContent}
        </if>
        <if test="example.logContentLike != null">
          AND `log_content` like concat('%',#{example.logContentLike},'%')
        </if>
      </trim>
    </if>
    <if test="example == null">
      where enabled_flag =1
    </if>
  </sql>
  <!--排序条件-->
  <sql id="orderBy">
    ORDER BY `log_id` DESC
  </sql>
  <sql id="orderByOther">
    order by ${example.orderBy}
  </sql>
  <!--分组条件-->
  <sql id="groupBy">
    group by ${example.groupBy}
  </sql>
  <!--分页条件-->
  <sql id="limit">
    <if test="size != null and size &gt; 0">
      limit #{startRow},#{size}
    </if>
  </sql>
  <!--查询符合条件的记录数-->
  <select id="countByExample" parameterType="com.cfpamf.ms.mallorder.request.OrderLogExample" resultType="java.lang.Integer">
    SELECT
      COUNT(*)
    FROM `bz_order_log`
    <include refid="whereCondition" />
  </select>
  <!--根据主键查询记录-->
  <select id="getByPrimaryKey" resultType="com.cfpamf.ms.mallorder.po.OrderLogPO">
    SELECT
      *
    FROM `bz_order_log`
    <include refid="pkWhere" />
  </select>
  <!--查询符合条件的记录(所有字段)-->
  <select id="listByExample" resultType="com.cfpamf.ms.mallorder.po.OrderLogPO">
    SELECT
      *
    FROM `bz_order_log`
    <include refid="whereCondition" />
    <if test="example.groupBy != null">
      <include refid="groupBy" />
    </if>
    <choose>
      <when test="example.orderBy != null">
        <include refid="orderByOther" />
      </when>
      <otherwise>
        <include refid="orderBy" />
      </otherwise>
    </choose>
  </select>
  <!--分页查询符合条件的记录(所有字段)-->
  <select id="listPageByExample" resultType="com.cfpamf.ms.mallorder.po.OrderLogPO">
    SELECT
      *
    FROM `bz_order_log`
    <include refid="whereCondition" />
    <if test="example.groupBy != null">
      <include refid="groupBy" />
    </if>
    <choose>
      <when test="example.orderBy != null">
        <include refid="orderByOther" />
      </when>
      <otherwise>
        <include refid="orderBy" />
      </otherwise>
    </choose>
    <include refid="limit" />
  </select>
  <!--查询符合条件的记录(指定字段)-->
  <select id="listFieldsByExample" resultMap="resultMap">
    SELECT
      ${fields}
    FROM `bz_order_log`
    <include refid="whereCondition" />
    <if test="example.groupBy != null">
      <include refid="groupBy" />
    </if>
    <choose>
      <when test="example.orderBy != null">
        <include refid="orderByOther" />
      </when>
      <otherwise>
        <include refid="orderBy" />
      </otherwise>
    </choose>
  </select>
  <!--分页查询符合条件的记录(指定字段)-->
  <select id="listFieldsPageByExample" resultMap="resultMap">
    SELECT
      ${fields}
    FROM `bz_order_log`
    <include refid="whereCondition" />
    <if test="example.groupBy != null">
      <include refid="groupBy" />
    </if>
    <choose>
      <when test="example.orderBy != null">
        <include refid="orderByOther" />
      </when>
      <otherwise>
        <include refid="orderBy" />
      </otherwise>
    </choose>
    <include refid="limit" />
  </select>
  <!--根据条件删除记录，可多条删除-->
  <update id="deleteByExample">
    update `bz_order_log`SET enabled_flag =0
    <include refid="whereCondition" />
  </update>
  <!--根据主键删除记录-->
  <update id="deleteByPrimaryKey">
    update `bz_order_log`SET enabled_flag =0
    <include refid="pkWhere" />
  </update>
  <!--插入一条记录-->
  <!--<insert id="insert" keyColumn="log_id" keyProperty="logId" parameterType="com.cfpamf.ms.mallorder.po.OrderLogPO" useGeneratedKeys="true">
    INSERT INTO `bz_order_log`(
    <include refid="columns" />
    )
    VALUES(
    <trim suffixOverrides=",">
      <if test="logRole != null">
        #{logRole},
      </if>
      <if test="logUserId != null">
        #{logUserId},
      </if>
      <if test="logUserName != null">
        #{logUserName},
      </if>
      <if test="orderSn != null">
        #{orderSn},
      </if>
      <if test="orderStateLog != null">
        #{orderStateLog},
      </if>
      <if test="loanPayState != null">
        #{loanPayState},
      </if>
      <if test="logTime != null">
        #{logTime},
      </if>
      <if test="logContent != null">
        #{logContent},
      </if>
    </trim>
    )
  </insert>-->
  <!--按条件更新记录中不为空的字段-->
  <update id="updateByExampleSelective">
    UPDATE `bz_order_log`
    <trim prefix="SET" suffixOverrides=",">
      <if test="record.logRole != null">
        `log_role` = #{record.logRole},
      </if>
      <if test="record.logUserId != null">
        `log_user_id` = #{record.logUserId},
      </if>
      <if test="record.logUserName != null">
        `log_user_name` = #{record.logUserName},
      </if>
      <if test="record.orderSn != null">
        `order_sn` = #{record.orderSn},
      </if>
      <if test="record.orderStateLog != null">
        `order_state_log` = #{record.orderStateLog},
      </if>
      <if test="record.loanPayState != null">
        `loan_pay_state` = #{record.loanPayState},
      </if>
      <if test="record.logTime != null">
        `log_time` = #{record.logTime},
      </if>
      <if test="record.logContent != null">
        `log_content` = #{record.logContent},
      </if>
    </trim>
    <include refid="whereCondition" />
  </update>
  <!--按照主键更新记录中不为空的字段-->
  <update id="updateByPrimaryKeySelective">
    UPDATE `bz_order_log`
    <trim prefix="SET" suffixOverrides=",">
      <if test="logRole != null">
        `log_role` = #{logRole},
      </if>
      <if test="logUserId != null">
        `log_user_id` = #{logUserId},
      </if>
      <if test="logUserName != null">
        `log_user_name` = #{logUserName},
      </if>
      <if test="orderSn != null">
        `order_sn` = #{orderSn},
      </if>
      <if test="orderStateLog != null">
        `order_state_log` = #{orderStateLog},
      </if>
      <if test="loanPayState != null">
        `loan_pay_state` = #{loanPayState},
      </if>
      <if test="logTime != null">
        `log_time` = #{logTime},
      </if>
      <if test="logContent != null">
        `log_content` = #{logContent},
      </if>
    </trim>
    WHERE `log_id` = #{logId}
  </update>
</mapper>
