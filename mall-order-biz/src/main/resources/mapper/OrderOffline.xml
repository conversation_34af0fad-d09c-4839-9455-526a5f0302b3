<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.OrderOfflineMapper">



    <select id="getReceiptInfoList" resultType="com.cfpamf.ms.mallorder.vo.OrderOfflineInfoVO">
        select bo.order_sn,
        concat(bo.order_sn,'_',off.id) as uniqueNo,
        bo.payment_code,
        bo.payment_name,
        boe.customer_name,
        boe.receiver_name,
        boe.receiver_info,
        bo.pay_sn,
        bo.order_state,
        off.receipt_amount,
        off.receipt_time,
        off.receipt_account,
        off.receipt_time,
        off.enabled_flag
        from bz_order_offline off
        LEFT JOIN bz_order bo ON bo.pay_sn = off.pay_sn
        LEFT JOIN bz_order_extend boe ON bo.order_sn = boe.order_sn
        where bo.order_type in (5,6)
        <if test="example.orderSn != '' and example.orderSn != null ">
            and bo.order_sn = #{example.orderSn}
        </if>
        <if test="example.paySn != '' and example.paySn != null ">
            and bo.pay_sn = #{example.paySn}
        </if>
        <if test="example.paymentCode != '' and example.paymentCode != null ">
            and bo.payment_code = #{example.paymentCode}
        </if>
        <if test="example.updateTimeAfter != null ">
            and off.update_time >= #{example.updateTimeAfter}
        </if>
        <if test="example.updateTimeAfter != null ">
            <![CDATA[
                and off.update_time <= #{example.updateTimeBefore}
            ]]>
        </if>

        <if test="example.orderStateList != null and example.orderStateList.size > 0">
            and bo.order_state in
            <foreach collection="example.orderStateList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>

    </select>

    <select id="getOrderOfflineForAgriFee" resultType="com.cfpamf.ms.mallorder.dto.OrderOfflineAgriFeeDTO">
        select select p.order_product_id orderProductId,
            p.product_id productId,
            t.area_code areaCode,
            p.tax_price taxPrice,
            t.finance_rule_code financeRuleCode,
            p.product_num productNum
        from bz_order t
            left join bz_order_product p on t.order_sn = p.order_sn
                and t.order_type in (5,6)
        where t.enabled_flag = 1
            and p.agri_service_fees = 0
        limit 100
    </select>

</mapper>
