<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.ComplainSubjectMapper">
  <resultMap id="resultMap" type="com.cfpamf.ms.mallorder.po.ComplainSubject">
    <id column="complain_subject_id" property="complainSubjectId" />
    <result column="complain_subject_name" property="complainSubjectName" />
    <result column="complain_subject_desc" property="complainSubjectDesc" />
    <result column="is_show" property="isShow" />
    <result column="sort" property="sort" />
    <result column="create_time" property="createTime" />
    <result column="create_admin_id" property="createAdminId" />
  </resultMap>
  <!--除主键外的所有字段，用于插入操作-->
  <sql id="columns">
    <trim suffixOverrides=",">
      <if test="complainSubjectName != null">
        `complain_subject_name`,
      </if>
      <if test="complainSubjectDesc != null">
        `complain_subject_desc`,
      </if>
      <if test="isShow != null">
        `is_show`,
      </if>
      <if test="sort != null">
        `sort`,
      </if>
      <if test="createTime != null">
        `create_time`,
      </if>
      <if test="createAdminId != null">
        `create_admin_id`,
      </if>
    </trim>
  </sql>
  <!--按照主键值进行操作-->
  <sql id="pkWhere">
    WHERE `complain_subject_id` = #{primaryKey} AND enabled_flag =1
  </sql>
  <!--操作条件-->
  <sql id="whereCondition">
    <if test="example != null">
      <trim prefix="WHERE" prefixOverrides="AND|OR">
        AND enabled_flag =1
        <if test="example.complainSubjectIdNotEquals != null">
          AND `complain_subject_id` != #{example.complainSubjectIdNotEquals}
        </if>
        <if test="example.complainSubjectIdIn != null">
          AND `complain_subject_id` in (${example.complainSubjectIdIn})
        </if>
        <if test="example.complainSubjectName != null">
          AND `complain_subject_name` = #{example.complainSubjectName}
        </if>
        <if test="example.complainSubjectNameLike != null">
          AND `complain_subject_name` like concat('%',#{example.complainSubjectNameLike},'%')
        </if>
        <if test="example.complainSubjectDesc != null">
          AND `complain_subject_desc` = #{example.complainSubjectDesc}
        </if>
        <if test="example.isShow != null">
          AND `is_show` = #{example.isShow}
        </if>
        <if test="example.sort != null">
          AND `sort` = #{example.sort}
        </if>
        <if test="example.createTimeAfter != null">
          AND `create_time` <![CDATA[ >= ]]> #{example.createTimeAfter}
        </if>
        <if test="example.createTimeBefore != null">
          AND `create_time` <![CDATA[ <= ]]> #{example.createTimeBefore}
        </if>
        <if test="example.createAdminId != null">
          AND `create_admin_id` = #{example.createAdminId}
        </if>
      </trim>
    </if>
    <if test="example == null">
      where enabled_flag =1
    </if>
  </sql>
  <!--排序条件-->
  <sql id="orderBy">
    ORDER BY `complain_subject_id` DESC
  </sql>
  <sql id="orderByOther">
    order by ${example.orderBy}
  </sql>
  <!--分组条件-->
  <sql id="groupBy">
    group by ${example.groupBy}
  </sql>
  <!--分页条件-->
  <sql id="limit">
    <if test="size != null and size &gt; 0">
      limit #{startRow},#{size}
    </if>
  </sql>
  <!--查询符合条件的记录数-->
  <select id="countByExample" parameterType="com.cfpamf.ms.mallorder.request.ComplainSubjectExample" resultType="java.lang.Integer">
    SELECT
      COUNT(*)
    FROM `bz_complain_subject`
    <include refid="whereCondition" />
  </select>
  <!--根据主键查询记录-->
  <select id="getByPrimaryKey" resultMap="resultMap">
    SELECT
      *
    FROM `bz_complain_subject`
    <include refid="pkWhere" />
  </select>
  <!--查询符合条件的记录(所有字段)-->
  <select id="listByExample" resultMap="resultMap">
    SELECT
      *
    FROM `bz_complain_subject`
    <include refid="whereCondition" />
    <if test="example.groupBy != null">
      <include refid="groupBy" />
    </if>
    <choose>
      <when test="example.orderBy != null">
        <include refid="orderByOther" />
      </when>
      <otherwise>
        <include refid="orderBy" />
      </otherwise>
    </choose>
  </select>
  <!--分页查询符合条件的记录(所有字段)-->
  <select id="listPageByExample" resultMap="resultMap">
    SELECT
      *
    FROM `bz_complain_subject`
    <include refid="whereCondition" />
    <if test="example.groupBy != null">
      <include refid="groupBy" />
    </if>
    <choose>
      <when test="example.orderBy != null">
        <include refid="orderByOther" />
      </when>
      <otherwise>
        <include refid="orderBy" />
      </otherwise>
    </choose>
    <include refid="limit" />
  </select>
  <!--查询符合条件的记录(指定字段)-->
  <select id="listFieldsByExample" resultMap="resultMap">
    SELECT
      ${fields}
    FROM `bz_complain_subject`
    <include refid="whereCondition" />
    <if test="example.groupBy != null">
      <include refid="groupBy" />
    </if>
    <choose>
      <when test="example.orderBy != null">
        <include refid="orderByOther" />
      </when>
      <otherwise>
        <include refid="orderBy" />
      </otherwise>
    </choose>
  </select>
  <!--分页查询符合条件的记录(指定字段)-->
  <select id="listFieldsPageByExample" resultMap="resultMap">
    SELECT
      ${fields}
    FROM `bz_complain_subject`
    <include refid="whereCondition" />
    <if test="example.groupBy != null">
      <include refid="groupBy" />
    </if>
    <choose>
      <when test="example.orderBy != null">
        <include refid="orderByOther" />
      </when>
      <otherwise>
        <include refid="orderBy" />
      </otherwise>
    </choose>
    <include refid="limit" />
  </select>
  <!--根据条件删除记录，可多条删除-->
  <update id="deleteByExample">
    update `bz_complain_subject`SET enabled_flag =0
    <include refid="whereCondition" />
  </update>
  <!--根据主键删除记录-->
  <update id="deleteByPrimaryKey">
    update `bz_complain_subject`SET enabled_flag =0
    <include refid="pkWhere" />
  </update>
  <!--插入一条记录-->
  <insert id="insert" keyColumn="complain_subject_id" keyProperty="complainSubjectId" parameterType="com.cfpamf.ms.mallorder.po.ComplainSubject" useGeneratedKeys="true">
    INSERT INTO `bz_complain_subject`(
    <include refid="columns" />
    )
    VALUES(
    <trim suffixOverrides=",">
      <if test="complainSubjectName != null">
        #{complainSubjectName},
      </if>
      <if test="complainSubjectDesc != null">
        #{complainSubjectDesc},
      </if>
      <if test="isShow != null">
        #{isShow},
      </if>
      <if test="sort != null">
        #{sort},
      </if>
      <if test="createTime != null">
        #{createTime},
      </if>
      <if test="createAdminId != null">
        #{createAdminId},
      </if>
    </trim>
    )
  </insert>
  <!--按条件更新记录中不为空的字段-->
  <update id="updateByExampleSelective">
    UPDATE `bz_complain_subject`
    <trim prefix="SET" suffixOverrides=",">
      <if test="record.complainSubjectName != null">
        `complain_subject_name` = #{record.complainSubjectName},
      </if>
      <if test="record.complainSubjectDesc != null">
        `complain_subject_desc` = #{record.complainSubjectDesc},
      </if>
      <if test="record.isShow != null">
        `is_show` = #{record.isShow},
      </if>
      <if test="record.sort != null">
        `sort` = #{record.sort},
      </if>
      <if test="record.createTime != null">
        `create_time` = #{record.createTime},
      </if>
      <if test="record.createAdminId != null">
        `create_admin_id` = #{record.createAdminId},
      </if>
    </trim>
    <include refid="whereCondition" />
  </update>
  <!--按照主键更新记录中不为空的字段-->
  <update id="updateByPrimaryKeySelective">
    UPDATE `bz_complain_subject`
    <trim prefix="SET" suffixOverrides=",">
      <if test="complainSubjectName != null">
        `complain_subject_name` = #{complainSubjectName},
      </if>
      <if test="complainSubjectDesc != null">
        `complain_subject_desc` = #{complainSubjectDesc},
      </if>
      <if test="isShow != null">
        `is_show` = #{isShow},
      </if>
      <if test="sort != null">
        `sort` = #{sort},
      </if>
      <if test="createTime != null">
        `create_time` = #{createTime},
      </if>
      <if test="createAdminId != null">
        `create_admin_id` = #{createAdminId},
      </if>
    </trim>
    WHERE `complain_subject_id` = #{complainSubjectId}
  </update>
</mapper>