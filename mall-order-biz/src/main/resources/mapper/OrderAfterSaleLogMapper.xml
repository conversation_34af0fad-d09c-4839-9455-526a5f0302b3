<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.OrderAfterSaleLogMapper">
  <resultMap id="resultMap" type="com.cfpamf.ms.mallorder.po.OrderAfterSaleLogPO">
    <id column="log_id" property="logId" />
    <result column="create_time" property="createTime" />
    <result column="update_time" property="updateTime" />
    <result column="enabled_flag" property="enabledFlag" />
    <result column="log_role" property="logRole" />
    <result column="log_user_id" property="logUserId" />
    <result column="log_user_name" property="logUserName" />
    <result column="afs_sn" property="afsSn" />
    <result column="afs_type" property="afsType" />
    <result column="state" property="state" />
    <result column="content" property="content" />
    <result column="create_by" property="createBy" />
    <result column="update_by" property="updateBy" />
    <result column="channel" property="channel"/>
  </resultMap>
  <!--除主键外的所有字段，用于插入操作-->
  <sql id="columns">
    <trim suffixOverrides=",">
      <if test="logRole != null">
        `log_role`,
      </if>
      <if test="logUserId != null">
        `log_user_id`,
      </if>
      <if test="logUserName != null">
        `log_user_name`,
      </if>
      <if test="afsSn != null">
        `afs_sn`,
      </if>
      <if test="afsType != null">
        `afs_type`,
      </if>
      <if test="state != null">
        `state`,
      </if>
      <if test="content != null">
        `content`,
      </if>
      <if test="createTime != null">
        `create_time`,
      </if>
      <if test="channel != null">
        `channel`,
      </if>
    </trim>
  </sql>
  <!--按照主键值进行操作-->
  <sql id="pkWhere">
    WHERE `log_id` = #{primaryKey} AND enabled_flag =1
  </sql>
  <!--操作条件-->
  <sql id="whereCondition">
    <if test="example != null">
      <trim prefix="WHERE" prefixOverrides="AND|OR">
        AND enabled_flag =1
        <if test="example.logIdNotEquals != null">
          AND `log_id` != #{example.logIdNotEquals}
        </if>
        <if test="example.logIdIn != null">
          AND `log_id` in (${example.logIdIn})
        </if>
        <if test="example.logRole != null">
          AND `log_role` = #{example.logRole}
        </if>
        <if test="example.logUserId != null">
          AND `log_user_id` = #{example.logUserId}
        </if>
        <if test="example.logUserName != null">
          AND `log_user_name` = #{example.logUserName}
        </if>
        <if test="example.logUserNameLike != null">
          AND `log_user_name` like concat('%',#{example.logUserNameLike},'%')
        </if>
        <if test="example.afsSn != null">
          AND `afs_sn` = #{example.afsSn}
        </if>
        <if test="example.afsSnLike != null">
          AND `afs_sn` like concat('%',#{example.afsSnLike},'%')
        </if>
        <if test="example.afsType != null">
          AND `afs_type` = #{example.afsType}
        </if>
        <if test="example.state != null">
          AND `state` = #{example.state}
        </if>
        <if test="example.content != null">
          AND `content` = #{example.content}
        </if>
        <if test="example.contentLike != null">
          AND `content` like concat('%',#{example.contentLike},'%')
        </if>
        <if test="example.createTimeAfter != null">
          AND `create_time` <![CDATA[ >= ]]> #{example.createTimeAfter}
        </if>
        <if test="example.createTimeBefore != null">
          AND `create_time` <![CDATA[ <= ]]> #{example.createTimeBefore}
        </if>
      </trim>
    </if>
    <if test="example == null">
      where enabled_flag =1
    </if>
  </sql>
  <!--排序条件-->
  <sql id="orderBy">
    ORDER BY `log_id` DESC
  </sql>
  <sql id="orderByOther">
    order by ${example.orderBy}
  </sql>
  <!--分组条件-->
  <sql id="groupBy">
    group by ${example.groupBy}
  </sql>
  <!--分页条件-->
  <sql id="limit">
    <if test="size != null and size &gt; 0">
      limit #{startRow},#{size}
    </if>
  </sql>
  <!--查询符合条件的记录数-->
  <select id="countByExample" parameterType="com.cfpamf.ms.mallorder.request.OrderAfterSaleLogExample" resultType="java.lang.Integer">
    SELECT
    COUNT(*)
    FROM `bz_order_after_sale_log`
    <include refid="whereCondition" />
  </select>
  <!--根据主键查询记录-->
  <select id="getByPrimaryKey" resultMap="resultMap">
    SELECT
    *
    FROM `bz_order_after_sale_log`
    <include refid="pkWhere" />
  </select>
  <!--查询符合条件的记录(所有字段)-->
  <select id="listByExample" resultMap="resultMap">
    SELECT
    *
    FROM `bz_order_after_sale_log`
    <include refid="whereCondition" />
    <if test="example.groupBy != null">
      <include refid="groupBy" />
    </if>
    <choose>
      <when test="example.orderBy != null">
        <include refid="orderByOther" />
      </when>
      <otherwise>
        <include refid="orderBy" />
      </otherwise>
    </choose>
  </select>
  <!--分页查询符合条件的记录(所有字段)-->
  <select id="listPageByExample" resultMap="resultMap">
    SELECT
    *
    FROM `bz_order_after_sale_log`
    <include refid="whereCondition" />
    <if test="example.groupBy != null">
      <include refid="groupBy" />
    </if>
    <choose>
      <when test="example.orderBy != null">
        <include refid="orderByOther" />
      </when>
      <otherwise>
        <include refid="orderBy" />
      </otherwise>
    </choose>
    <include refid="limit" />
  </select>
  <!--查询符合条件的记录(指定字段)-->
  <select id="listFieldsByExample" resultMap="resultMap">
    SELECT
    ${fields}
    FROM `bz_order_after_sale_log`
    <include refid="whereCondition" />
    <if test="example.groupBy != null">
      <include refid="groupBy" />
    </if>
    <choose>
      <when test="example.orderBy != null">
        <include refid="orderByOther" />
      </when>
      <otherwise>
        <include refid="orderBy" />
      </otherwise>
    </choose>
  </select>
  <!--分页查询符合条件的记录(指定字段)-->
  <select id="listFieldsPageByExample" resultMap="resultMap">
    SELECT
    ${fields}
    FROM `bz_order_after_sale_log`
    <include refid="whereCondition" />
    <if test="example.groupBy != null">
      <include refid="groupBy" />
    </if>
    <choose>
      <when test="example.orderBy != null">
        <include refid="orderByOther" />
      </when>
      <otherwise>
        <include refid="orderBy" />
      </otherwise>
    </choose>
    <include refid="limit" />
  </select>
  <!--根据条件删除记录，可多条删除-->
  <update id="deleteByExample">
    update `bz_order_after_sale_log`SET enabled_flag =0
    <include refid="whereCondition" />
  </update>
  <!--根据主键删除记录-->
  <update id="deleteByPrimaryKey">
    update `bz_order_after_sale_log`SET enabled_flag =0
    <include refid="pkWhere" />
  </update>
  <!--插入一条记录-->
  <insert id="insert" keyColumn="log_id" keyProperty="logId" parameterType="com.cfpamf.ms.mallorder.po.OrderAfterSaleLogPO" useGeneratedKeys="true">
    INSERT INTO `bz_order_after_sale_log`(
    <include refid="columns" />
    )
    VALUES(
    <trim suffixOverrides=",">
      <if test="logRole != null">
        #{logRole},
      </if>
      <if test="logUserId != null">
        #{logUserId},
      </if>
      <if test="logUserName != null">
        #{logUserName},
      </if>
      <if test="afsSn != null">
        #{afsSn},
      </if>
      <if test="afsType != null">
        #{afsType},
      </if>
      <if test="state != null">
        #{state},
      </if>
      <if test="content != null">
        #{content},
      </if>
      <if test="createTime != null">
        #{createTime},
      </if>
      <if test="channel != null">
        #{channel},
      </if>
    </trim>
    )
  </insert>
  <!--按条件更新记录中不为空的字段-->
  <update id="updateByExampleSelective">
    UPDATE `bz_order_after_sale_log`
    <trim prefix="SET" suffixOverrides=",">
      <if test="record.logRole != null">
        `log_role` = #{record.logRole},
      </if>
      <if test="record.logUserId != null">
        `log_user_id` = #{record.logUserId},
      </if>
      <if test="record.logUserName != null">
        `log_user_name` = #{record.logUserName},
      </if>
      <if test="record.afsSn != null">
        `afs_sn` = #{record.afsSn},
      </if>
      <if test="record.afsType != null">
        `afs_type` = #{record.afsType},
      </if>
      <if test="record.state != null">
        `state` = #{record.state},
      </if>
      <if test="record.content != null">
        `content` = #{record.content},
      </if>
      <if test="record.createTime != null">
        `create_time` = #{record.createTime},
      </if>
      <if test="record.channel != null">
        `channel` = #{record.channel},
      </if>
    </trim>
    <include refid="whereCondition" />
  </update>
  <!--按照主键更新记录中不为空的字段-->
  <update id="updateByPrimaryKeySelective">
    UPDATE `bz_order_after_sale_log`
    <trim prefix="SET" suffixOverrides=",">
      <if test="logRole != null">
        `log_role` = #{logRole},
      </if>
      <if test="logUserId != null">
        `log_user_id` = #{logUserId},
      </if>
      <if test="logUserName != null">
        `log_user_name` = #{logUserName},
      </if>
      <if test="afsSn != null">
        `afs_sn` = #{afsSn},
      </if>
      <if test="afsType != null">
        `afs_type` = #{afsType},
      </if>
      <if test="state != null">
        `state` = #{state},
      </if>
      <if test="content != null">
        `content` = #{content},
      </if>
      <if test="createTime != null">
        `create_time` = #{createTime},
      </if>
      <if test="channel != null">
        `channel` = #{channel},
      </if>
    </trim>
    WHERE `log_id` = #{logId}
  </update>
</mapper>