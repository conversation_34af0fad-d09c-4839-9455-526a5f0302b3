<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.BzOrderPayWhitelistMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.mallorder.po.BzOrderPayWhitelistPO">
        <result column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="enabled_flag" property="enabledFlag" />
        <result column="pay_id" property="payId" />
        <result column="store_id" property="storeId" />
        <result column="store_name" property="storeName" />
        <result column="is_own_store" property="isOwnStore" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_time,
        update_time,
        enabled_flag,
        pay_id, store_id, store_name, is_own_store, create_by, update_by
    </sql>

    <select id="getPayMethodByStoreId"  resultType="com.cfpamf.ms.mallorder.po.BzOrderPayWhitelistPO">
        select * from bz_order_pay_whitelist where store_id = #{storeId}
    </select>

</mapper>
