<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.PayMethodMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.mallorder.po.PayMethodPO">
        <result column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="enabled_flag" property="enabledFlag"/>
        <result column="pay_method_code" property="payMethodCode"/>
        <result column="pay_method_name" property="payMethodName"/>
        <result column="pay_method_desc" property="payMethodDesc"/>
        <result column="pay_method_status" property="payMethodStatus"/>
        <result column="min_amount" property="minAmount"/>
        <result column="support_order_type" property="supportOrderType"/>
        <result column="support_merchant" property="supportMerchant"/>
        <result column="support_order_channel" property="supportOrderChannel"/>
        <result column="is_loan_pay" property="isLoanPay"/>
        <result column="loan_code" property="loanCode"/>
        <result column="loan_product" property="loanProduct"/>
        <result column="loan_product_name" property="loanProductName"/>
        <result column="sort" property="sort"/>
        <result column="allow_user_flag" property="allowUserFlag"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="pay_icon" property="payIcon"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_time,
        update_time,
        enabled_flag,
        pay_method_code, pay_method_name, pay_method_desc, pay_method_status, min_amount, support_order_type, support_merchant, support_order_channel, is_loan_pay, loan_code, loan_product, loan_product_name, sort, allow_user_flag, create_by, update_by,pay_icon
    </sql>

    <select id="pagePayMethodList" resultMap="BaseResultMap">
        SELECT * FROM bz_pay_method WHERE enabled_flag =1
        <if test="queryDTO.payMethodName !=null and queryDTO.payMethodName !=''">
            and pay_method_name like concat('%',#{queryDTO.payMethodName},'%')
        </if>
        order by sort asc
    </select>
</mapper>
