<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.OrderLogisticMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.mallorder.po.OrderLogisticPO">
        <result column="logistic_id" property="logisticId" />
        <result column="order_sn" property="orderSn" />
        <result column="package_sn" property="packageSn" />
        <result column="deliver_type" property="deliverType" />
        <result column="express_id" property="expressId" />
        <result column="express_name" property="expressName" />
        <result column="express_company_code" property="expressCompanyCode" />
        <result column="express_number" property="expressNumber" />
        <result column="deliver_name" property="deliverName" />
        <result column="deliver_mobile" property="deliverMobile" />
        <result column="deliver_warehouse" property="deliverWarehouse" />
        <result column="deliver_warehouse_name" property="deliverWarehouseName" />
        <result column="deliver_package_state" property="deliverPackageState" />
        <result column="deliver_package_failReason" property="deliverPackageFailReason" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="enabled_flag" property="enabledFlag" />

    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,order_sn,package_sn,deliver_type,express_id,express_name,express_company_code,express_number,deliver_name, deliver_mobile,
        deliver_warehouse,deliver_warehouse_name,deliver_package_state,deliver_package_failReason,update_time, create_time, enabled_flag, create_by, update_by
    </sql>

    <select id="expressNumberIsUsed" resultType="java.lang.String">
        select bol.order_sn
        from bz_order_logistic bol,bz_order bo
        where bol.order_sn = bo.order_sn
            and bo.order_state >= 30
            and bol.express_number = #{expressNumber}

    </select>

    <select id="expressNumberUseCount" resultType="java.lang.String">
        select t.order_sn
        from bz_order_logistic t
        where t.express_number = #{expressNumber}
    </select>

    <select id="getOrderLogisticListByOrderProductId" resultType="com.cfpamf.ms.mallorder.vo.OrderLogisticVO">
        select l.logistic_id logisticId,
               l.order_sn orderSn,
               l.package_sn packageSn,
               l.deliver_type deliverType,
               l.express_id expressId,
               l.express_name expressName,
               l.express_company_code expressCompanyCode,
               l.express_number expressNumber,
               l.deliver_name deliverName,
               l.deliver_mobile deliverMobile,
               l.create_by createBy,
               l.create_time createTime,
               l.update_by updateBy,
               l.update_time updateTime
        from bz_order_logistic l,bz_order_logistic_item li
        where l.package_sn = li.package_sn
          and li.order_product_id = #{orderProductId}
          and l.deliver_package_state = 2
          and l.enabled_flag = 1
    </select>

    <select id="getPackageItemList" resultType="com.cfpamf.ms.mallorder.vo.OrderLogisticItemVO">
        select l.order_sn,
               l.deliver_type,
               l.express_id,
               l.express_name,
               l.express_company_code,
               l.express_number,
               l.deliver_name,
               l.deliver_mobile,
               l.deliver_warehouse,
               l.deliver_warehouse_name,
               l.deliver_package_state,
               l.deliver_package_fail_reason,
               li.package_sn,
               li.order_product_id,
               li.goods_name,
               li.product_image,
               li.spec_values,
               li.product_num,
               li.delivery_num,
               li.create_time,
               li.create_by,
               li.update_time,
               li.update_by
        from bz_order_logistic_item li left join bz_order_logistic l on l.package_sn = li.package_sn
        where l.order_sn = #{orderSn}
    </select>

    <select id="getOrderLogisticListByOrderSn" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from bz_order_logistic
        where order_sn = #{orderSn}
            and deliver_package_state != 3
    </select>

    <select id="getExportOrderLogisticListByOrderProductId" resultType="com.cfpamf.ms.mallorder.dto.OrderDeliveryPackageDTO">
        select l.create_time,
               l.express_name,
               l.express_company_code,
               l.express_number,
               l.deliver_type,
<!--               l.deliver_name,-->
               l.deliver_mobile,
               l.deliver_warehouse,
               l.deliver_warehouse_name,
               l.create_by as deliverName
        from bz_order_logistic l,bz_order_logistic_item li
        where l.package_sn = li.package_sn
            and deliver_package_state != 3
            and li.order_product_id = #{orderProductId}
    </select>
</mapper>
