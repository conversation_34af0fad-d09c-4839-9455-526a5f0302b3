<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.OrderPayRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.mallorder.po.OrderPayRecordPO">
        <result column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="pay_no" property="payNo" />
        <result column="pay_sn" property="paySn" />
        <result column="payment_code" property="paymentCode" />
        <result column="payment_name" property="paymentName" />
        <result column="amount" property="amount" />
        <result column="balance" property="balance" />
        <result column="pay_status" property="payStatus" />
        <result column="pay_time" property="payTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="enabled_flag" property="enabledFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_time,
        update_time,
        pay_no, pay_sn, payment_code, payment_name, amount, pay_status, pay_time, refund_status, refund_time, create_by, update_by, enabled_flag
    </sql>

</mapper>
