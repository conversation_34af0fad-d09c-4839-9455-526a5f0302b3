<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.OrderPriceRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.mallorder.po.OrderPriceRecordPO">
        <id column="price_record_id" property="priceRecordId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="enabled_flag" property="enabledFlag" />
        <result column="order_sn" property="orderSn" />
        <result column="order_product_id" property="orderProductId" />
        <result column="product_show_price" property="productShowPrice" />
        <result column="renewal_product_show_price" property="renewalProductShowPrice" />
        <result column="landing_price" property="landingPrice" />
        <result column="renewal_landing_price" property="renewalLandingPrice" />
        <result column="express_fee" property="expressFee" />
        <result column="renewal_express_fee" property="renewalExpressFee" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        create_time,
        update_time,
        enabled_flag,
        price_record_id, order_sn, order_product_id, product_show_price, renewal_product_show_price, landing_price, renewal_landing_price, express_fee, renewal_express_fee, create_by, update_by
    </sql>

</mapper>
