<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.OrderGroupBuyingRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.mallorder.po.OrderGroupBuyingRecordPO">
        <id column="group_buying_id" property="groupBuyingId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="enabled_flag" property="enabledFlag" />
        <result column="group_buying_code" property="groupBuyingCode" />
        <result column="gift_order_sn" property="giftOrderSn" />
        <result column="group_order_num" property="groupOrderNum" />
        <result column="group_goods_num" property="groupGoodsNum" />
        <result column="gift_goods_num" property="giftGoodsNum" />
        <result column="store_category_id" property="storeCategoryId" />
        <result column="store_category_name" property="storeCategoryName" />
        <result column="branch_code" property="branchCode" />
        <result column="branch_name" property="branchName" />
        <result column="area_code" property="areaCode" />
        <result column="area_name" property="areaName" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        create_time
        ,
        update_time,
        enabled_flag,
        group_buying_id, group_buying_code, gift_order_sn, group_order_num, group_goods_num, gift_goods_num, store_category_id, store_category_name, branch_code, branch_name, area_code, area_name, create_by, update_by
    </sql>

    <sql id="whereCondition">
        <if test="query != null">
            <if test="query.groupBuyingCode != null and query.groupBuyingCode != ''">
                AND gbc.`group_buying_code` = #{query.groupBuyingCode}
            </if>
            <if test="query.giftOrderSn != null and query.giftOrderSn != ''">
                AND gbc.`gift_order_sn` = #{query.giftOrderSn}
            </if>
            <if test="query.storeId != null ">
                AND gbc.`store_id` = #{query.storeId}
            </if>
            <if test="query.storeName != null and query.storeName != ''">
                AND gbc.`store_name` like concat('%',#{query.storeName},'%')
            </if>
            <if test="query.storeCategoryId != null ">
                AND gbc.`store_category_id` = #{query.storeCategoryId}
            </if>
            <if test="query.branchCode != null and query.branchCode != ''">
                AND gbc.`branch_code` = #{query.branchCode}
            </if>
            <if test="query.branchList != null">
                AND gbc.`branch_code` in
                <foreach collection="query.branchList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </if>
    </sql>

    <select id="ListOrderGroupBuyingRecord" resultType="com.cfpamf.ms.mallorder.vo.GroupOrderRecordVO">
        select gbc.group_buying_id,
            gbc.group_buying_code,
            gbc.gift_order_sn,
            gbc.group_order_num,
            gbc.group_goods_num,
            gbc.gift_goods_num,
            gbc.store_category_id,
            gbc.store_category_name,
            gbc.branch_code,
            gbc.branch_name,
            gbc.area_code,
            gbc.area_name,
            gbc.create_time,
            gbc.create_by
        from bz_order_group_buying_record gbc
        where gbc.enabled_flag = 1
        <include refid="whereCondition"/>
        order by gbc.create_time desc
    </select>

    <select id="listWaitingGroupOrderPage" resultType="com.cfpamf.ms.mallorder.vo.GroupOrderVO">
        select bo.order_sn,
               bo.store_id,
               bo.store_name,
               bo.member_id,
               bo.member_name,
               bo.order_state,
               bo.order_type,
               bo.order_pattern,
               bo.performance_modes,
               bo.create_time,
               boe.manager,
               boe.manager_name,
               boe.area_code,
               boe.area_name,
               boe.branch branch_code,
               boe.branch_name
        from bz_order bo,
             bz_order_extend boe
        where bo.order_sn = boe.order_sn
            and bo.order_state in (20,25)
        <if test="query.storeId != null ">
            AND bo.store_id = #{query.storeId}
        </if>
        <if test="query.branchCode != null and query.branchCode != ''">
            AND boe.branch = #{query.branchCode}
        </if>
        <if test="query.orderSn != null and query.orderSn != ''">
            AND bo.order_sn = #{query.orderSn}
        </if>
        <if test="query.managerName != null and query.managerName != ''">
            AND boe.manager_name like concat('%',#{query.managerName},'%')
        </if>
            and exists(select order_product_id
                     from bz_order_product bop
                     where bo.order_sn = bop.order_sn
                        and bop.delivery_state = 0
                        and bop.group_buying_tag = 1
                        and bop.is_gift = 0
                        and bop.product_num > bop.return_number
                    <if test="query.storeCategoryId != null and query.storeCategoryId != ''">
                        and locate (#{query.storeCategoryId}, bop.store_category_id)
                    </if>
                    )
        order by bo.create_time desc
    </select>

    <select id="listWaitingGroupOrderProduct" resultType="com.cfpamf.ms.mallorder.vo.GroupOrderProductVO">
        select bop.order_product_id,
               bop.order_sn,
               bop.goods_id,
               bop.goods_name,
               bop.product_image,
               bop.product_id,
               bop.product_show_price,
               bop.product_num,
               bop.return_number,
               bop.product_num - bop.return_number as validNum,
               bop.channel_sku_id,
               bop.channel_sku_unit,
               bop.supplier_code,
               bop.supplier_name,
               bop.is_gift,
               bop.performance_service,
               bop.spec_values,
               bop.store_category_id,
               bop.delivery_state,
               bop.group_buying_tag
        from bz_order_product bop
        where bop.order_sn = #{orderSn}
            and bop.delivery_state = 0
            and bop.group_buying_tag = 1
            and bop.is_gift = 0
            and bop.product_num > bop.return_number
        <if test="storeCategoryId != null and storeCategoryId != ''">
            and locate (#{storeCategoryId}, bop.store_category_id)
        </if>

        UNION

        select bop.order_product_id,
            bop.order_sn,
            bop.goods_id,
            bop.goods_name,
            bop.product_image,
            bop.product_id,
            bop.product_show_price,
            bop.product_num,
            bop.return_number,
            bop.product_num - bop.return_number as validNum,
            bop.channel_sku_id,
            bop.channel_sku_unit,
            bop.supplier_code,
            bop.supplier_name,
            bop.is_gift,
            bop.performance_service,
            bop.spec_values,
            bop.store_category_id,
            bop.delivery_state,
            bop.group_buying_tag
        from bz_order_product bop
        where bop.order_sn = #{orderSn}
            and bop.is_gift = 1
            and bop.gift_group in (
                select bop.gift_group
                from bz_order_product bop
                where bop.order_sn = #{orderSn}
                    and bop.delivery_state = 0
                    and bop.group_buying_tag = 1
                    and bop.is_gift = 0
                    and bop.product_num > bop.return_number
                <if test="storeCategoryId != null and storeCategoryId != ''">
                    and locate (#{storeCategoryId}, bop.store_category_id)
                </if>
            )
    </select>

    <resultMap id="groupOrderMap" type="com.cfpamf.ms.mallorder.vo.GroupOrderVO">
        <id column="order_sn" property="orderSn"/>
        <result column="member_name" property="memberName"/>
        <result column="member_id" property="memberId"/>
        <result column="store_id" property="storeId"/>
        <result column="store_name" property="storeName"/>
        <result column="manager" property="manager"/>
        <result column="manager_name" property="managerName"/>
        <result column="branch_code" property="branchCode"/>
        <result column="branch_name" property="branchName"/>
        <result column="area_code" property="areaCode"/>
        <result column="area_name" property="areaName"/>
        <result column="order_state" property="orderState"/>
        <result column="order_type" property="orderType"/>
        <result column="order_pattern" property="orderPattern"/>
        <result column="performance_modes" property="performanceModes"/>
        <result column="create_time" property="createTime"/>

        <collection property="orderProductVOs" ofType="com.cfpamf.ms.mallorder.vo.GroupOrderProductVO">
            <id column="order_product_id" property="orderProductId"/>
            <result column="order_sn" property="orderSn"/>
            <result column="goods_id" property="goodsId"/>
            <result column="goods_name" property="goodsName"/>
            <result column="product_image" property="productImage"/>
            <result column="product_id" property="productId"/>
            <result column="product_show_price" property="productShowPrice"/>
            <result column="product_num" property="productNum"/>
            <result column="return_number" property="returnNumber"/>
            <result column="validNum" property="validNum"/>
            <result column="channel_sku_unit" property="channelSkuUnit"/>
            <result column="channel_sku_id" property="channelSkuId"/>
            <result column="supplier_name" property="supplierName"/>
            <result column="supplier_code" property="supplierCode"/>
            <result column="is_gift" property="isGift"/>
            <result column="gift_group" property="giftGroup"/>
            <result column="performance_service" property="performanceService"/>
            <result column="spec_values" property="specValues"/>
            <result column="store_category_id" property="storeCategoryId"/>
            <result column="delivery_state" property="deliveryState"/>
            <result column="group_buying_tag" property="groupBuyingTag"/>
        </collection>
    </resultMap>

    <select id="listGroupOrder" resultMap="groupOrderMap">
        select bo.order_sn,
        bo.store_id,
        bo.store_name,
        bo.member_id,
        bo.member_name,
        bo.order_state,
        bo.order_type,
        bo.order_pattern,
        bo.performance_modes,
        bo.create_time,
        boe.manager,
        boe.manager_name,
        boe.area_code,
        boe.area_name,
        boe.branch branch_code,
        boe.branch_name,
        bop.order_product_id,
        bop.order_sn,
        bop.goods_id,
        bop.goods_name,
        bop.product_image,
        bop.product_id,
        bop.product_show_price,
        bop.product_num,
        bop.return_number,
        bop.product_num - bop.return_number as validNum,
        bop.channel_sku_id,
        bop.channel_sku_unit,
        bop.supplier_code,
        bop.supplier_name,
        bop.is_gift,
        bop.gift_group,
        bop.performance_service,
        bop.spec_values,
        bop.store_category_id,
        bop.delivery_state,
        bop.group_buying_tag
        from bz_order bo
            , bz_order_extend boe
            , bz_order_product bop
        where bo.order_sn = boe.order_sn
            and bo.order_sn = bop.order_sn
        <if test="query.storeId != null ">
            AND bo.store_id = #{query.storeId}
        </if>
        <if test="query.branchCode != null and query.branchCode != ''">
            AND boe.branch = #{query.branchCode}
        </if>
        <if test="query.orderSn != null and query.orderSn != ''">
            AND bo.order_sn = #{query.orderSn}
        </if>
        <if test="query.managerName != null and query.managerName != ''">
            AND boe.manager_name like concat('%',#{query.managerName},'%')
        </if>
        <if test="query.groupOrderProductIdList != null">
            AND bop.order_product_id in
            <foreach collection="query.groupOrderProductIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.storeCategoryId != null and query.storeCategoryId != ''">
            and locate (#{query.storeCategoryId}, bop.store_category_id)
        </if>
        order by bo.create_time desc
    </select>

    <select id="statisticGroupOrderNumber" resultType="com.cfpamf.ms.mallorder.vo.GroupOrderStatisticDTO">
        select bo.store_id,
               bo.store_name,
               boe.branch branch_code,
               boe.branch_name,
               bop.store_category_id,
               sum(bop.product_num)   totalNum,
               sum(bop.return_number) returnNumber
        from bz_order bo
           , bz_order_extend boe
           , bz_order_product bop
        where bo.order_sn = boe.order_sn
          and bo.order_sn = bop.order_sn
          and bo.order_state in (20,25)
          and bop.delivery_state = 0
          and bop.group_buying_tag = 1
          and bop.is_gift = 0
          and bop.product_num > bop.return_number
          and bop.store_category_id is not null
        <if test="query.storeId != null ">
            AND bo.store_id = #{query.storeId}
        </if>
        <if test="query.branchCode != null and query.branchCode != ''">
            AND boe.branch = #{query.branchCode}
        </if>
        <if test="query.orderSn != null and query.orderSn != ''">
            AND bo.order_sn = #{query.orderSn}
        </if>
        <if test="query.managerName != null and query.managerName != ''">
            AND boe.manager_name like concat('%',#{query.managerName},'%')
        </if>
        <if test="query.storeCategoryId != null and query.storeCategoryId != ''">
            and locate (#{query.storeCategoryId}, bop.store_category_id)
        </if>
        group by bo.store_id, boe.branch, bop.store_category_id
    </select>

    <select id="listGroupOrderProduct" resultType="com.cfpamf.ms.mallorder.vo.GroupOrderProductVO">
        select bop.order_product_id,
               bop.order_sn,
               bo.store_id,
               boe.branch branchCode,
               bop.goods_id,
               bop.goods_name,
               bop.product_image,
               bop.product_id,
               bop.product_show_price,
               bop.product_num,
               bop.return_number,
               bop.product_num - bop.return_number as validNum,
               bop.channel_sku_id,
               bop.channel_sku_unit,
               bop.supplier_code,
               bop.supplier_name,
               bop.is_gift,
               bop.performance_service,
               bop.spec_values,
               bop.store_category_id,
               bop.delivery_state,
               bop.group_buying_tag
        from bz_order bo
            , bz_order_extend boe
            , bz_order_product bop
        where bo.order_sn = boe.order_sn
        and bo.order_sn = bop.order_sn
        <if test="query.storeId != null ">
            AND bo.store_id = #{query.storeId}
        </if>
        <if test="query.branchCode != null and query.branchCode != ''">
            AND boe.branch = #{query.branchCode}
        </if>
        <if test="query.orderSn != null and query.orderSn != ''">
            AND bo.order_sn = #{query.orderSn}
        </if>
        <if test="query.managerName != null and query.managerName != ''">
            AND boe.manager_name like concat('%',#{query.managerName},'%')
        </if>
        <if test="query.groupOrderProductIdList != null">
            AND bop.order_product_id in
            <foreach collection="query.groupOrderProductIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.storeCategoryId != null and query.storeCategoryId != ''">
            and locate (#{query.storeCategoryId}, bop.store_category_id)
        </if>
        order by bo.create_time desc
    </select>

    <resultMap id="orderExportDTO" type="com.cfpamf.ms.mallorder.dto.GroupOrderExportDTO">
        <result column="order_id" property="orderId" />
        <result column="orderSn" property="orderSn" />
        <result column="member_id" property="memberId" />
        <result column="member_name" property="memberName" />
        <result column="store_id" property="storeId" />
        <result column="store_name" property="storeName" />
        <result column="recommend_store_id" property="recommendStoreId" />
        <result column="recommend_store_name" property="recommendStoreName" />
        <result column="create_time" property="createTime" />
        <result column="pay_time" property="payTime" />
        <result column="finish_time" property="finishTime" />
        <result column="goods_name" property="goodsName" />
        <result column="spec_values" property="specValues" />
        <result column="product_num" property="productNum" />
        <result column="skuMaterialCode" property="skuMaterialCode" />
        <result column="skuMaterialName" property="skuMaterialName" />
        <result column="validNum" property="validNum" />
        <result column="cost" property="cost" />
        <result column="landing_price" property="landingPrice" />
        <result column="tax_rate" property="taxRate" />
        <result column="tax_price" property="taxPrice" />
        <result column="product_show_price" property="productShowPrice" />
        <result column="order_type" property="orderType" />
        <result column="customer_id" property="customerId" />
        <result column="customer_name" property="customerName" />
        <result column="branch_name" property="branchName" />
        <result column="zone_code" property="zoneCode" />
        <result column="zone_name" property="zoneName" />
        <result column="area_code" property="areaCode" />
        <result column="manager" property="manager" />
        <result column="manager_name" property="managerName" />
        <result column="product_id" property="productId" />
        <result column="supplierName" property="supplierName" />
        <result column="returnState" property="returnState" />
        <result column="deliverState" property="deliverState" />
        <result column="goods_category_path" property="goodsCategoryPath" />
        <result column="performance_channel" property="performanceChannel" />
        <result column="performance_service" property="performanceService" />
        <result column="productEffectivePrice" property="productEffectivePrice" />
        <collection property="orderDeliveryPackageDTOList"
                    ofType="com.cfpamf.ms.mallorder.dto.OrderDeliveryPackageDTO"
                    column="{orderProductId=orderProductId}"
                    select="com.cfpamf.ms.mallorder.mapper.OrderLogisticMapper.getExportOrderLogisticListByOrderProductId">

            <result column="create_time" property="createTime" />
            <result column="express_name" property="expressName" />
            <result column="express_company_code" property="expressCompanyCode" />
            <result column="express_number" property="expressNumber" />
            <result column="deliver_type" property="deliverType" />
            <result column="deliver_name" property="deliverName" />
            <result column="deliver_mobile" property="deliverMobile" />
            <result column="deliver_warehouse" property="deliverWarehouse" />
            <result column="deliver_warehouse_name" property="deliverWarehouseName" />
        </collection>
    </resultMap>

    <select id="exportRecord" resultMap="orderExportDTO">
        select o.order_id,
            gbc.group_buying_code,
            o.order_sn                                           orderSn,
            o.pay_sn,
            o.order_type,
            o.order_pattern,
            o.order_state,
            o.order_amount,
            o.member_id,
            o.member_name,
            o.store_id,
            o.store_name,
            o.recommend_store_id,
            o.recommend_store_name,
            o.create_time,
            o.pay_time,
            o.payment_name,
            o.finish_time,
            o.compose_pay_name,

            p.order_product_id                                   orderProductId,
            p.goods_id,
            p.goods_name,
            p.product_id,
            p.spec_values,
            p.goods_category_path,
            p.supplier_name                 AS                   supplierName,
            p.sku_material_code                                  skuMaterialCode,
            p.sku_material_name                                  skuMaterialName,
            case when p.return_number != 0 then '是' else '否' end returnState,
            p.status                                             productStatus,
            p.delivery_state                                     deliverState,
            p.product_num,
            p.product_num - p.return_number as                   validNum,
            p.delivery_num,
            p.cost,
            p.landing_price,
            p.tax_price,
            p.product_show_price,
            p.goods_amount_total,
            p.money_amount,
            IFNULL(p.product_effective_price, 0)                 productEffectivePrice,
            p.performance_mode                                   performanceMode,
            p.performance_channel,
            p.performance_service,

            e.customer_id,
            e.customer_name,
            e.branch                                             branchCode,
            e.branch_name,
            e.zone_code,
            e.zone_name,
            e.area_code,
            e.area_name,
            e.manager,
            e.manager_name,
            e.user_code

        from bz_order o
            join bz_order_group_buying_record gbc on o.order_sn = gbc.gift_order_sn
            join bz_order_product p on o.order_sn = p.order_sn
            join bz_order_extend e on o.order_sn = e.order_sn
        <include refid="whereCondition"/>
        order by gbc.create_time desc
    </select>

</mapper>
