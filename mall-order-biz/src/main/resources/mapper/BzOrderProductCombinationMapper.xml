<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.BzOrderProductCombinationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.mallorder.po.BzOrderProductCombinationPO">
        <result column="id" property="id" />
        <result column="update_time" property="updateTime" />
        <result column="enabled_flag" property="enabledFlag" />
        <result column="create_time" property="createTime" />
        <result column="order_sn" property="orderSn" />
        <result column="product_id" property="productId" />
        <result column="goods_id" property="goodsId" />
        <result column="goods_name" property="goodsName" />
        <result column="main_product_id" property="mainProductId" />
        <result column="child_product_id" property="childProductId" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="main_image" property="mainImage" />
        <result column="buy_num" property="buyNum" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        update_time,
        enabled_flag,
        create_time,
        main_image,
        buy_num,
        order_sn, product_id, goods_id, goods_name, main_product_id, child_product_id, create_by, update_by
    </sql>

</mapper>
