<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.BzBankPayMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.mallorder.po.BzBankPayPO">
        <result column="id" property="id" />
        <result column="update_time" property="updateTime" />
        <result column="create_time" property="createTime" />
        <result column="enabled_flag" property="enabledFlag" />
        <result column="pay_sn" property="paySn" />
        <result column="order_sn" property="orderSn" />
        <result column="other_trade_no" property="otherTradeNo" />
        <result column="amount" property="amount" />
        <result column="status" property="status" />
        <result column="pay_status" property="payStatus" />
        <result column="remark" property="remark" />
        <result column="account_no" property="accountNo" />
        <result column="account_name" property="accountName" />
        <result column="id_card_no" property="idCardNo" />
        <result column="account_type" property="accountType" />
        <result column="business_type" property="businessType" />
        <result column="loan_org_no" property="loanOrgNo" />
        <result column="plan_loan_dt" property="planLoanDt" />
        <result column="fail_retry" property="failRetry" />
        <result column="call_from" property="callFrom" />
        <result column="reason" property="reason" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        update_time,
        create_time,
        enabled_flag,
        pay_sn, order_sn, other_trade_no, amount, status, pay_status, remark, account_no, account_name, id_card_no, account_type, business_type, loan_org_no, plan_loan_dt, fail_retry, call_from, reason
    </sql>

</mapper>
