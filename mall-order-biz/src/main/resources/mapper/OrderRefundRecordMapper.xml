<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.OrderRefundRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.mallorder.po.OrderRefundRecordPO">
        <result column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="refund_no" property="refundNo" />
        <result column="order_sn" property="orderSn" />
        <result column="afs_sn" property="afsSn" />
        <result column="pay_no" property="payNo" />
        <result column="pay_sn" property="paySn" />
        <result column="refund_type" property="refundType" />
        <result column="refund_type_value" property="refundTypeValue" />
        <result column="payment_code" property="paymentCode" />
        <result column="payment_name" property="paymentName" />
        <result column="amount" property="amount" />
        <result column="actual_amount" property="actualAmount" />
        <result column="channel_service_fee" property="channelServiceFee" />
        <result column="channel_service_rate" property="channelServiceRate" />
        <result column="refund_status" property="refundStatus" />
        <result column="refund_time" property="refundTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="enabled_flag" property="enabledFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_time,
        update_time,
        refund_no, order_sn, afs_sn, pay_no, pay_sn, refund_type, refund_type_value,payment_code,payment_name,amount,
        actual_amount,channel_service_fee,channel_service_rate, refund_status, refund_time, create_by, update_by, enabled_flag
    </sql>

</mapper>
