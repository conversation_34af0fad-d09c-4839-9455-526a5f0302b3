<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.OrderExtendMapper">
  <resultMap id="resultMap" type="com.cfpamf.ms.mallorder.po.OrderExtendPO">
      <id column="extend_id" property="extendId" />
      <result column="create_time" property="createTime" />
      <result column="update_time" property="updateTime" />
      <result column="enabled_flag" property="enabledFlag" />
      <result column="order_sn" property="orderSn" />
      <result column="store_id" property="storeId" />
      <result column="evaluation_time" property="evaluationTime" />
      <result column="order_remark" property="orderRemark" />
      <result column="order_points_count" property="orderPointsCount" />
      <result column="voucher_price" property="voucherPrice" />
      <result column="voucher_code" property="voucherCode" />
      <result column="order_from" property="orderFrom" />
      <result column="deliver_address_id" property="deliverAddressId" />
      <result column="receiver_province_code" property="receiverProvinceCode" />
      <result column="receiver_city_code" property="receiverCityCode" />
      <result column="receiver_district_code" property="receiverDistrictCode" />
      <result column="receiver_town_code" property="receiverTownCode" />
      <result column="receiver_name" property="receiverName"/>
      <result column="receiver_area_info" property="receiverAreaInfo"/>
      <result column="receiver_address" property="receiverAddress"/>
      <result column="receiver_mobile" property="receiverMobile"/>
      <result column="receiver_name" property="receiverName" />
      <result column="receiver_info" property="receiverInfo" />
      <result column="invoice_info" property="invoiceInfo" />
      <result column="promotion_info" property="promotionInfo" />
      <result column="is_dzmd" property="isDzmd" />
      <result column="invoice_status" property="invoiceStatus" />
      <result column="store_voucher_amount" property="storeVoucherAmount" />
      <result column="platform_voucher_amount" property="platformVoucherAmount" />
      <result column="create_by" property="createBy" />
      <result column="update_by" property="updateBy" />
      <result column="customer_id" property="customerId" />
      <result column="customer_name" property="customerName" />
      <result column="manager" property="manager" />
      <result column="manager_name" property="managerName" />
      <result column="branch" property="branch" />
      <result column="branch_name" property="branchName" />
      <result column="area_code" property="areaCode" />
      <result column="area_name" property="areaName" />
      <result column="supervisor" property="supervisor" />
      <result column="supervisor_name" property="supervisorName" />
      <result column="xz_card_list" property="xzCardList" />
      <result column="category_code" property="categoryCode" />
      <result column="category_name" property="categoryName" />
      <result column="settle_channel" property="settleChannel" />
      <result column="manage_type" property="manageType" />
      <result column="receive_code" property="receiveCode" />
      <result column="receive_confirm_doc_type" property="receiveConfirmDocType"/>
      <result column="receive_confirm_doc_url" property="receiveConfirmDocUrl"/>
      <result column="disable_channel_list" property="disableChannelList" javaType="com.alibaba.fastjson.JSONObject"
            typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
  </resultMap>
  <!--除主键外的所有字段，用于插入操作-->
  <sql id="columns">
    <trim suffixOverrides=",">
      <if test="orderSn != null">
        `order_sn`,
      </if>
      <if test="storeId != null">
        `store_id`,
      </if>
      <if test="customerId != null">
        `customer_id`,
      </if>
      <if test="customerName != null">
        `customer_name`,
      </if>
      <if test="branch != null">
        `branch`,
      </if>
      <if test="branchName != null">
        `branch_name`,
      </if>
      <if test="areaCode != null">
        `area_code`,
      </if>
      <if test="areaName != null">
        `area_name`,
      </if>
      <if test="manageType != null">
        `manage_type`,
      </if>
      <if test="manager != null">
        `manager`,
      </if>
      <if test="managerName != null">
        `manager_name`,
      </if>
      <if test="supervisor != null">
        `supervisor`,
      </if>
      <if test="supervisorName != null">
        `supervisor_name`,
      </if>
      <if test="evaluationTime != null">
        `evaluation_time`,
      </if>
      <if test="orderRemark != null">
        `order_remark`,
      </if>
      <if test="orderPointsCount != null">
        `order_points_count`,
      </if>
      <if test="voucherPrice != null">
        `voucher_price`,
      </if>
      <if test="voucherCode != null">
        `voucher_code`,
      </if>
      <if test="orderFrom != null">
        `order_from`,
      </if>
      <if test="deliverAddressId != null">
        `deliver_address_id`,
      </if>
      <if test="receiverProvinceCode != null">
        `receiver_province_code`,
      </if>
      <if test="receiverCityCode != null">
        `receiver_city_code`,
      </if>
      <if test="receiverDistrictCode != null">
        `receiver_district_code`,
      </if>
      <if test="receiverName != null">
        `receiver_name`,
      </if>
      <if test="receiverInfo != null">
        `receiver_info`,
      </if>
      <if test="invoiceInfo != null">
        `invoice_info`,
      </if>
      <if test="promotionInfo != null">
        `promotion_info`,
      </if>
      <if test="isDzmd != null">
        `is_dzmd`,
      </if>
      <if test="invoiceStatus != null">
        `invoice_status`,
      </if>
      <if test="storeVoucherAmount != null">
        `store_voucher_amount`,
      </if>
      <if test="platformVoucherAmount != null">
        `platform_voucher_amount`,
      </if>
      <if test="shareCode != null">
        `share_code`,
      </if>
      <if test="xzCardList != null">
        `xz_card_list`,
      </if>
      <if test="categoryCode != null">
        `category_code`,
      </if>
      <if test="categoryName != null">
        `category_name`,
      </if>
    </trim>
  </sql>
  <!--按照主键值进行操作-->
  <sql id="pkWhere">
    WHERE `extend_id` = #{primaryKey} AND enabled_flag =1
  </sql>
  <!--操作条件-->
  <sql id="whereCondition">
    <if test="example != null">
      <trim prefix="WHERE" prefixOverrides="AND|OR">
        AND enabled_flag =1
        <if test="example.extendIdNotEquals != null">
          AND `extend_id` != #{example.extendIdNotEquals}
        </if>
        <if test="example.extendIdIn != null">
          AND `extend_id` in (${example.extendIdIn})
        </if>
        <if test="example.orderSn != null">
          AND `order_sn` = #{example.orderSn}
        </if>
        <if test="example.orderSnLike != null">
          AND `order_sn` like concat('%',#{example.orderSnLike},'%')
        </if>
        <if test="example.storeId != null">
          AND `store_id` = #{example.storeId}
        </if>
        <if test="example.customerName != null">
          AND `customer_name` = #{example.customerName}
        </if>
        <if test="example.customerId != null">
          AND `customer_id` = #{example.customerId}
        </if>
        <if test="example.branch != null">
          AND `branch` = #{example.branch}
        </if>
        <if test="example.manager != null">
          AND `manager` = #{example.manager}
        </if>
        <if test="example.manageType != null">
          AND `manage_type` = #{example.manageType}
        </if>
        <if test="example.evaluationTimeAfter != null">
          AND `evaluation_time` <![CDATA[ >= ]]> #{example.evaluationTimeAfter}
        </if>
        <if test="example.evaluationTimeBefore != null">
          AND `evaluation_time` <![CDATA[ <= ]]> #{example.evaluationTimeBefore}
        </if>
        <if test="example.orderRemark != null">
          AND `order_remark` = #{example.orderRemark}
        </if>
        <if test="example.orderPointsCount != null">
          AND `order_points_count` = #{example.orderPointsCount}
        </if>
        <if test="example.voucherPrice != null">
          AND `voucher_price` = #{example.voucherPrice}
        </if>
        <if test="example.voucherCode != null">
          AND `voucher_code` = #{example.voucherCode}
        </if>
        <if test="example.orderFrom != null">
          AND `order_from` = #{example.orderFrom}
        </if>
        <if test="example.deliverAddressId != null">
          AND `deliver_address_id` = #{example.deliverAddressId}
        </if>
        <if test="example.receiverProvinceCode != null">
          AND `receiver_province_code` = #{example.receiverProvinceCode}
        </if>
        <if test="example.receiverCityCode != null">
          AND `receiver_city_code` = #{example.receiverCityCode}
        </if>
        <if test="example.receiverDistrictCode != null">
          AND `receiver_district_code` = #{example.receiverDistrictCode}
        </if>
        <if test="example.receiverName != null">
          AND `receiver_name` = #{example.receiverName}
        </if>
        <if test="example.receiverNameLike != null">
          AND `receiver_name` like concat('%',#{example.receiverNameLike},'%')
        </if>
        <if test="example.receiverInfo != null">
          AND `receiver_info` = #{example.receiverInfo}
        </if>
        <if test="example.invoiceInfo != null">
          AND `invoice_info` = #{example.invoiceInfo}
        </if>
        <if test="example.promotionInfo != null">
          AND `promotion_info` = #{example.promotionInfo}
        </if>
        <if test="example.isDzmd != null">
          AND `is_dzmd` = #{example.isDzmd}
        </if>
        <if test="example.invoiceStatus != null">
          AND `invoice_status` = #{example.invoiceStatus}
        </if>
        <if test="example.storeVoucherAmount != null">
          AND `store_voucher_amount` = #{example.storeVoucherAmount}
        </if>
        <if test="example.platformVoucherAmount != null">
          AND `platform_voucher_amount` = #{example.platformVoucherAmount}
        </if>
        <if test="example.xzCardList != null">
          AND `xz_card_list` = #{example.xzCardList}
        </if>
        <if test="example.categoryCode != null">
          AND `category_code` = #{example.categoryCode}
        </if>
        <if test="example.categoryName != null">
          AND `category_name` = #{example.categoryName}
        </if>
      </trim>
    </if>
    <if test="example == null">
      where enabled_flag =1
    </if>
  </sql>
  <!--排序条件-->
  <sql id="orderBy">
    ORDER BY `extend_id` DESC
  </sql>
  <sql id="orderByOther">
    order by ${example.orderBy}
  </sql>
  <!--分组条件-->
  <sql id="groupBy">
    group by ${example.groupBy}
  </sql>
  <!--分页条件-->
  <sql id="limit">
    <if test="size != null and size &gt; 0">
      limit #{startRow},#{size}
    </if>
  </sql>
  <!--查询符合条件的记录数-->
  <select id="countByExample" parameterType="com.cfpamf.ms.mallorder.request.OrderExtendExample" resultType="java.lang.Integer">
    SELECT
      COUNT(*)
    FROM `bz_order_extend`
    <include refid="whereCondition" />
  </select>
  <!--根据主键查询记录-->
  <select id="getByPrimaryKey" resultMap="resultMap">
    SELECT
      *
    FROM `bz_order_extend`
    <include refid="pkWhere" />
  </select>
  <!--查询符合条件的记录(所有字段)-->
  <select id="listByExample" resultMap="resultMap">
    SELECT
      *
    FROM `bz_order_extend`
    <include refid="whereCondition" />
    <if test="example.groupBy != null">
      <include refid="groupBy" />
    </if>
    <choose>
      <when test="example.orderBy != null">
        <include refid="orderByOther" />
      </when>
      <otherwise>
        <include refid="orderBy" />
      </otherwise>
    </choose>
  </select>
  <!--分页查询符合条件的记录(所有字段)-->
  <select id="listPageByExample" resultMap="resultMap">
    SELECT
      *
    FROM `bz_order_extend`
    <include refid="whereCondition" />
    <if test="example.groupBy != null">
      <include refid="groupBy" />
    </if>
    <choose>
      <when test="example.orderBy != null">
        <include refid="orderByOther" />
      </when>
      <otherwise>
        <include refid="orderBy" />
      </otherwise>
    </choose>
    <include refid="limit" />
  </select>
  <!--查询符合条件的记录(指定字段)-->
  <select id="listFieldsByExample" resultMap="resultMap">
    SELECT
      ${fields}
    FROM `bz_order_extend`
    <include refid="whereCondition" />
    <if test="example.groupBy != null">
      <include refid="groupBy" />
    </if>
    <choose>
      <when test="example.orderBy != null">
        <include refid="orderByOther" />
      </when>
      <otherwise>
        <include refid="orderBy" />
      </otherwise>
    </choose>
  </select>
  <!--分页查询符合条件的记录(指定字段)-->
  <select id="listFieldsPageByExample" resultMap="resultMap">
    SELECT
      ${fields}
    FROM `bz_order_extend`
    <include refid="whereCondition" />
    <if test="example.groupBy != null">
      <include refid="groupBy" />
    </if>
    <choose>
      <when test="example.orderBy != null">
        <include refid="orderByOther" />
      </when>
      <otherwise>
        <include refid="orderBy" />
      </otherwise>
    </choose>
    <include refid="limit" />
  </select>

  <select id="listUserNo2Fix" resultType="java.lang.String">
    SELECT DISTINCT(bo.`user_no`) user_no
    FROM `bz_order` bo,
         `bz_order_extend` boe
    where bo.`order_sn` = boe.`order_sn`
      and boe.`customer_id` is not null
      and boe.`branch` is null
  </select>

  <select id="listAllBranch" resultType="java.lang.String">
    select distinct(branch)
    from bz_order_extend
    where branch is not null;
  </select>

  <select id="listSkuMaterialCode" resultType="java.lang.String">
    select distinct(sku_material_code)
    FROM `bz_order_product`
    WHERE `sku_material_name` IS NULL and `sku_material_code` IS NOT NULL
  </select>

  <select id="obtainSelfLiftingPoint" resultType="java.lang.Long">
    select boe.point_id
    from bz_order bo,
         bz_order_extend boe
    where bo.order_sn = boe.order_sn
      and bo.order_pattern = 6
      and bo.member_id = #{memberId}
      and bo.store_id = #{storeId}
      and boe.point_id is not null
    order by bo.create_time desc limit 1
  </select>

  <!--根据条件删除记录，可多条删除-->
  <update id="deleteByExample">
    update `bz_order_extend`SET enabled_flag =0
    <include refid="whereCondition"/>
  </update>
  <!--根据主键删除记录-->
  <update id="deleteByPrimaryKey">
    update `bz_order_extend`SET enabled_flag =0
    <include refid="pkWhere"/>
  </update>

  <!--按条件更新记录中不为空的字段-->
  <update id="updateByExampleSelective">
    UPDATE `bz_order_extend`
    <trim prefix="SET" suffixOverrides=",">
      <if test="record.orderSn != null">
        `order_sn` = #{record.orderSn},
      </if>
      <if test="record.storeId != null">
        `store_id` = #{record.storeId},
      </if>
      <if test="record.customerId != null">
        `customer_id` = #{record.customerId},
      </if>
      <if test="record.customerName != null">
        `customer_name` = #{record.customerName},
      </if>
      <if test="record.branch != null">
        `branch` = #{record.branch},
      </if>
      <if test="record.branchName != null">
        `branch_name` = #{record.branchName},
      </if>
      <if test="record.areaCode != null">
        `area_code` = #{record.areaCode},
      </if>
      <if test="record.areaName != null">
        `area_name` = #{record.areaName},
      </if>
      <if test="record.manager != null">
        `manager` = #{record.manager},
      </if>
      <if test="record.managerName != null">
        `manager_name` = #{record.managerName},
      </if>
      <if test="record.supervisor != null">
        `supervisor` = #{record.supervisor},
      </if>
      <if test="record.supervisorName != null">
        `supervisor_name` = #{record.supervisorName},
      </if>
      <if test="record.evaluationTime != null">
        `evaluation_time` = #{record.evaluationTime},
      </if>
      <if test="record.orderRemark != null">
        `order_remark` = #{record.orderRemark},
      </if>
      <if test="record.orderPointsCount != null">
        `order_points_count` = #{record.orderPointsCount},
      </if>
      <if test="record.voucherPrice != null">
        `voucher_price` = #{record.voucherPrice},
      </if>
      <if test="record.voucherCode != null">
        `voucher_code` = #{record.voucherCode},
      </if>
      <if test="record.orderFrom != null">
        `order_from` = #{record.orderFrom},
      </if>
      <if test="record.deliverAddressId != null">
        `deliver_address_id` = #{record.deliverAddressId},
      </if>
      <if test="record.receiverProvinceCode != null">
        `receiver_province_code` = #{record.receiverProvinceCode},
      </if>
      <if test="record.receiverCityCode != null">
        `receiver_city_code` = #{record.receiverCityCode},
      </if>
      <if test="record.receiverDistrictCode != null">
        `receiver_district_code` = #{record.receiverDistrictCode},
      </if>
      <if test="record.receiverName != null">
        `receiver_name` = #{record.receiverName},
      </if>
      <if test="record.receiverInfo != null">
        `receiver_info` = #{record.receiverInfo},
      </if>
      <if test="record.invoiceInfo != null">
        `invoice_info` = #{record.invoiceInfo},
      </if>
      <if test="record.promotionInfo != null">
        `promotion_info` = #{record.promotionInfo},
      </if>
      <if test="record.isDzmd != null">
        `is_dzmd` = #{record.isDzmd},
      </if>
      <if test="record.invoiceStatus != null">
        `invoice_status` = #{record.invoiceStatus},
      </if>
      <if test="record.storeVoucherAmount != null">
        `store_voucher_amount` = #{record.storeVoucherAmount},
      </if>
      <if test="record.platformVoucherAmount != null">
        `platform_voucher_amount` = #{record.platformVoucherAmount},
      </if>
      <if test="record.xzCardList != null">
        `xz_card_list` = #{record.xzCardList},
      </if>
      <if test="record.categoryCode != null">
        `category_code` = #{record.categoryCode},
      </if>
      <if test="record.categoryName != null">
        `category_name` = #{record.categoryName},
      </if>
      <if test="record.manageType != null">
        `manage_type` = #{record.manageType},
      </if>
    </trim>
    <include refid="whereCondition" />
  </update>
  <!--按照主键更新记录中不为空的字段-->
  <update id="updateByPrimaryKeySelective">
    UPDATE `bz_order_extend`
    <trim prefix="SET" suffixOverrides=",">
      <if test="orderSn != null">
        `order_sn` = #{orderSn},
      </if>
      <if test="storeId != null">
        `store_id` = #{storeId},
      </if>
      <if test="customerId != null">
        `customer_id` = #{customerId},
      </if>
      <if test="customerName != null">
        `customer_name` = #{customerName},
      </if>
      <if test="branch != null">
        `branch` = #{branch},
      </if>
      <if test="branchName != null">
        `branch_name` = #{branchName},
      </if>
      <if test="areaCode != null">
        `area_code` = #{areaCode},
      </if>
      <if test="areaName != null">
        `area_name` = #{areaName},
      </if>
      <if test="manager != null">
        `manager` = #{manager},
      </if>
      <if test="managerName != null">
        `manager_name` = #{managerName},
      </if>
      <if test="supervisor != null">
        `supervisor` = #{supervisor},
      </if>
      <if test="supervisorName != null">
        `supervisor_name` = #{supervisorName},
      </if>
      <if test="evaluationTime != null">
        `evaluation_time` = #{evaluationTime},
      </if>
      <if test="orderRemark != null">
        `order_remark` = #{orderRemark},
      </if>
      <if test="orderPointsCount != null">
        `order_points_count` = #{orderPointsCount},
      </if>
      <if test="voucherPrice != null">
        `voucher_price` = #{voucherPrice},
      </if>
      <if test="voucherCode != null">
        `voucher_code` = #{voucherCode},
      </if>
      <if test="orderFrom != null">
        `order_from` = #{orderFrom},
      </if>
      <if test="deliverAddressId != null">
        `deliver_address_id` = #{deliverAddressId},
      </if>
      <if test="receiverProvinceCode != null">
        `receiver_province_code` = #{receiverProvinceCode},
      </if>
      <if test="receiverCityCode != null">
        `receiver_city_code` = #{receiverCityCode},
      </if>
      <if test="receiverDistrictCode != null">
        `receiver_district_code` = #{receiverDistrictCode},
      </if>
      <if test="receiverName != null">
        `receiver_name` = #{receiverName},
      </if>
      <if test="receiverInfo != null">
        `receiver_info` = #{receiverInfo},
      </if>
      <if test="invoiceInfo != null">
        `invoice_info` = #{invoiceInfo},
      </if>
      <if test="promotionInfo != null">
        `promotion_info` = #{promotionInfo},
      </if>
      <if test="isDzmd != null">
        `is_dzmd` = #{isDzmd},
      </if>
      <if test="invoiceStatus != null">
        `invoice_status` = #{invoiceStatus},
      </if>
      <if test="storeVoucherAmount != null">
        `store_voucher_amount` = #{storeVoucherAmount},
      </if>
      <if test="platformVoucherAmount != null">
        `platform_voucher_amount` = #{platformVoucherAmount},
      </if>
      <if test="xzCardList != null">
        `xz_card_list` = #{xzCardList},
      </if>
      <if test="categoryCode != null">
        `category_code` = #{categoryCode},
      </if>
      <if test="categoryName != null">
        `category_name` = #{categoryName},
      </if>
      <if test="record.manageType != null">
        `manage_type` = #{manageType},
      </if>
    </trim>
    WHERE `extend_id` = #{extendId}
  </update>
</mapper>
