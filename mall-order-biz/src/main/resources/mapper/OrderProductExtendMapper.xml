<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.OrderProductExtendMapper">
    <resultMap id="resultMap" type="com.cfpamf.ms.mallorder.po.OrderProductExtendPO">
        <id column="extend_id" property="extendId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="enabled_flag" property="enabledFlag"/>
        <result column="order_product_id" property="orderProductId"/>
        <result column="order_sn" property="orderSn"/>
        <result column="promotion_grade" property="promotionGrade"/>
        <result column="promotion_type" property="promotionType"/>
        <result column="promotion_id" property="promotionId"/>
        <result column="promotion_amount" property="promotionAmount"/>
        <result column="is_store" property="isStore"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="funder" property="funder"/>
        <result column="retail_price" property="retailPrice"/>
    </resultMap>
    <sql id="columns">
        <trim suffixOverrides=",">
            <if test="orderProductId != null">
                `order_product_id`,
            </if>
            <if test="orderSn != null">
                `order_sn`,
            </if>
            <if test="promotionGrade != null">
                `promotion_grade`,
            </if>
            <if test="promotionType != null">
                `promotion_type`,
            </if>
            <if test="promotionId != null">
                `promotion_id`,
            </if>
            <if test="promotionAmount != null">
                `promotion_amount`,
            </if>
            <if test="createTime != null">
                `create_time`,
            </if>
            <if test="isStore != null">
                `is_store`,
            </if>
        </trim>
    </sql>
    <!--按照主键值进行操作-->
    <sql id="pkWhere">
    WHERE `extend_id` = #{primaryKey} AND enabled_flag =1
  </sql>
    <!--操作条件-->
    <sql id="whereCondition">
        <if test="example != null">
            <trim prefix="WHERE" prefixOverrides="AND|OR">
                AND enabled_flag =1
                <if test="example.extendIdNotEquals != null">
                    AND `extend_id` != #{example.extendIdNotEquals}
                </if>
                <if test="example.extendIdIn != null">
                    AND `extend_id` in (${example.extendIdIn})
                </if>
                <if test="example.orderProductId != null">
                    AND `order_product_id` = #{example.orderProductId}
                </if>
                <if test="example.orderSn != null">
                    AND `order_sn` = #{example.orderSn}
                </if>
                <if test="example.orderSnLike != null">
                    AND `order_sn` like concat('%',#{example.orderSnLike},'%')
                </if>
                <if test="example.promotionGrade != null">
                    AND `promotion_grade` = #{example.promotionGrade}
                </if>
                <if test="example.promotionType != null">
                    AND `promotion_type` = #{example.promotionType}
                </if>
                <if test="example.promotionId != null">
                    AND `promotion_id` = #{example.promotionId}
                </if>
                <if test="example.promotionAmount != null">
                    AND `promotion_amount` = #{example.promotionAmount}
                </if>
                <if test="example.createTimeAfter != null">
                    AND `create_time` <![CDATA[ >= ]]> #{example.createTimeAfter}
                </if>
                <if test="example.createTimeBefore != null">
                    AND `create_time` <![CDATA[ <= ]]> #{example.createTimeBefore}
                </if>
                <if test="example.isStore != null">
                    AND `is_store` = #{example.isStore}
                </if>
            </trim>
        </if>
        <if test="example == null">
            where enabled_flag =1
        </if>
    </sql>
    <!--排序条件-->
    <sql id="orderBy">
    ORDER BY `extend_id` DESC
  </sql>
    <sql id="orderByOther">
    order by ${example.orderBy}
  </sql>
    <!--分组条件-->
    <sql id="groupBy">
    group by ${example.groupBy}
  </sql>
    <!--分页条件-->
    <sql id="limit">
        <if test="size != null and size &gt; 0">
            limit #{startRow},#{size}
        </if>
    </sql>
    <!--查询符合条件的记录数-->
    <select id="countByExample" parameterType="com.cfpamf.ms.mallorder.request.OrderProductExtendExample"
            resultType="java.lang.Integer">
        SELECT
        COUNT(*)
        FROM `bz_order_product_extend`
        <include refid="whereCondition"/>
    </select>
    <!--根据主键查询记录-->
    <select id="getByPrimaryKey" resultMap="resultMap">
        SELECT
        *
        FROM `bz_order_product_extend`
        <include refid="pkWhere"/>
    </select>
    <!--查询符合条件的记录(所有字段)-->
    <select id="listByExample" resultMap="resultMap">
        SELECT
        *
        FROM `bz_order_product_extend`
        <include refid="whereCondition"/>
        <if test="example.groupBy != null">
            <include refid="groupBy"/>
        </if>
        <choose>
            <when test="example.orderBy != null">
                <include refid="orderByOther"/>
            </when>
            <otherwise>
                <include refid="orderBy"/>
            </otherwise>
        </choose>
    </select>
    <!--分页查询符合条件的记录(所有字段)-->
    <select id="listPageByExample" resultMap="resultMap">
        SELECT
        *
        FROM `bz_order_product_extend`
        <include refid="whereCondition"/>
        <if test="example.groupBy != null">
            <include refid="groupBy"/>
        </if>
        <choose>
            <when test="example.orderBy != null">
                <include refid="orderByOther"/>
            </when>
            <otherwise>
                <include refid="orderBy"/>
            </otherwise>
        </choose>
        <include refid="limit"/>
    </select>
    <!--查询符合条件的记录(指定字段)-->
    <select id="listFieldsByExample" resultMap="resultMap">
        SELECT
        ${fields}
        FROM `bz_order_product_extend`
        <include refid="whereCondition"/>
        <if test="example.groupBy != null">
            <include refid="groupBy"/>
        </if>
        <choose>
            <when test="example.orderBy != null">
                <include refid="orderByOther"/>
            </when>
            <otherwise>
                <include refid="orderBy"/>
            </otherwise>
        </choose>
    </select>
    <!--分页查询符合条件的记录(指定字段)-->
    <select id="listFieldsPageByExample" resultMap="resultMap">
        SELECT
        ${fields}
        FROM `bz_order_product_extend`
        <include refid="whereCondition"/>
        <if test="example.groupBy != null">
            <include refid="groupBy"/>
        </if>
        <choose>
            <when test="example.orderBy != null">
                <include refid="orderByOther"/>
            </when>
            <otherwise>
                <include refid="orderBy"/>
            </otherwise>
        </choose>
        <include refid="limit"/>
    </select>
    <!--根据条件删除记录，可多条删除-->
    <update id="deleteByExample">
        update `bz_order_product_extend`SET enabled_flag =0
        <include refid="whereCondition"/>
    </update>
    <!--根据主键删除记录-->
    <update id="deleteByPrimaryKey">
        update `bz_order_product_extend`SET enabled_flag =0
        <include refid="pkWhere"/>
    </update>
    <!--插入一条记录-->
    <insert id="insert" keyColumn="extend_id" keyProperty="extendId"
            parameterType="com.cfpamf.ms.mallorder.po.OrderProductExtendPO" useGeneratedKeys="true">
        INSERT INTO `bz_order_product_extend`(
        <include refid="columns"/>
        )
        VALUES(
        <trim suffixOverrides=",">
            <if test="orderProductId != null">
                #{orderProductId},
            </if>
            <if test="orderSn != null">
                #{orderSn},
            </if>
            <if test="promotionGrade != null">
                #{promotionGrade},
            </if>
            <if test="promotionType != null">
                #{promotionType},
            </if>
            <if test="promotionId != null">
                #{promotionId},
            </if>
            <if test="promotionAmount != null">
                #{promotionAmount},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="isStore != null">
                #{isStore},
            </if>
        </trim>
        )
    </insert>
    <!--按条件更新记录中不为空的字段-->
    <update id="updateByExampleSelective">
        UPDATE `bz_order_product_extend`
        <trim prefix="SET" suffixOverrides=",">
            <if test="record.orderProductId != null">
                `order_product_id` = #{record.orderProductId},
            </if>
            <if test="record.orderSn != null">
                `order_sn` = #{record.orderSn},
            </if>
            <if test="record.promotionGrade != null">
                `promotion_grade` = #{record.promotionGrade},
            </if>
            <if test="record.promotionType != null">
                `promotion_type` = #{record.promotionType},
            </if>
            <if test="record.promotionId != null">
                `promotion_id` = #{record.promotionId},
            </if>
            <if test="record.promotionAmount != null">
                `promotion_amount` = #{record.promotionAmount},
            </if>
            <if test="record.createTime != null">
                `create_time` = #{record.createTime},
            </if>
            <if test="record.isStore != null">
                `is_store` = #{record.isStore},
            </if>
        </trim>
        <include refid="whereCondition"/>
    </update>
    <!--按照主键更新记录中不为空的字段-->
    <update id="updateByPrimaryKeySelective">
        UPDATE `bz_order_product_extend`
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderProductId != null">
                `order_product_id` = #{orderProductId},
            </if>
            <if test="orderSn != null">
                `order_sn` = #{orderSn},
            </if>
            <if test="promotionGrade != null">
                `promotion_grade` = #{promotionGrade},
            </if>
            <if test="promotionType != null">
                `promotion_type` = #{promotionType},
            </if>
            <if test="promotionId != null">
                `promotion_id` = #{promotionId},
            </if>
            <if test="promotionAmount != null">
                `promotion_amount` = #{promotionAmount},
            </if>
            <if test="createTime != null">
                `create_time` = #{createTime},
            </if>
            <if test="isStore != null">
                `is_store` = #{isStore},
            </if>
        </trim>
        WHERE `extend_id` = #{extendId}
    </update>
</mapper>
