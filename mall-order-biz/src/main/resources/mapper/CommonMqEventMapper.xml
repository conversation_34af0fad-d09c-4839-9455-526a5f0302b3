<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.CommonMqEventMapper">

    <select id="getById" resultType="com.cfpamf.ms.mallorder.po.CommonMqEvent">
        select * from cd_mall.`common_mq_event` where id = #{id}
    </select>
    
    <select id="getMqMessage4Resend" resultType="com.cfpamf.ms.mallorder.po.CommonMqEvent">
        SELECT
            *
        FROM
            `common_mq_event` cme
        WHERE
            cme.`send_times` <![CDATA[ <= ]]> 4
            AND TO_DAYS(NOW()) - TO_DAYS( cme.`update_time`  ) <![CDATA[ <= ]]> 30
            AND TIMESTAMPDIFF(MINUTE, cme.`update_time`, NOW()) <![CDATA[ >= ]]> 2
        ORDER BY cme.id
    </select>

</mapper>
