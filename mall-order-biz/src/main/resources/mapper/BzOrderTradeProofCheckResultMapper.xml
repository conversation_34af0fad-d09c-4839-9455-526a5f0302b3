<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.BzOrderTradeProofCheckResultMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.mallorder.po.BzOrderTradeProofCheckResultPO">
        <result column="id" property="id" />
        <result column="update_time" property="updateTime" />
        <result column="create_time" property="createTime" />
        <result column="enabled_flag" property="enabledFlag" />
        <result column="request_no" property="requestNo" />
        <result column="order_sn" property="orderSn" />
        <result column="order_product_id" property="orderProductId" />
        <result column="scene_no" property="sceneNo" />
        <result column="scene_name" property="sceneName" />
        <result column="result_status" property="resultStatus" />
        <result column="interrupt_result" property="interruptResult" />
        <result column="all_result" property="allResult" />
        <result column="request" property="request" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        update_time,
        create_time,
        enabled_flag,
        request_no, order_sn, order_product_id, scene_no, scene_name, result_status, interrupt_result, all_result, request, create_by, update_by
    </sql>

</mapper>
