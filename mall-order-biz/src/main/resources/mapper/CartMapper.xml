<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.CartMapper">
  <resultMap id="resultMap" type="com.cfpamf.ms.mallorder.po.CartPO">
      <id column="cart_id" property="cartId" />
      <result column="create_time" property="createTime" />
      <result column="update_time" property="updateTime" />
      <result column="enabled_flag" property="enabledFlag" />
      <result column="member_id" property="memberId" />
      <result column="store_id" property="storeId" />
      <result column="store_name" property="storeName" />
      <result column="goods_id" property="goodsId" />
      <result column="goods_name" property="goodsName" />
      <result column="product_id" property="productId" />
      <result column="buy_num" property="buyNum" />
      <result column="product_price" property="productPrice" />
      <result column="product_image" property="productImage" />
      <result column="spec_value_ids" property="specValueIds" />
      <result column="spec_values" property="specValues" />
      <result column="is_checked" property="isChecked" />
      <result column="promotion_id" property="promotionId" />
      <result column="promotion_type" property="promotionType" />
      <result column="promotion_description" property="promotionDescription" />
      <result column="off_price" property="offPrice" />
      <result column="product_state" property="productState" />
      <result column="create_by" property="createBy" />
      <result column="update_by" property="updateBy" />
      <result column="area_code" property="areaCode" />
      <result column="finance_rule_code" property="financeRuleCode" />
      <result column="rule_tag" property="ruleTag" />
      <result column="distribute_parent" property="distributeParent" />
  </resultMap>
  <!--除主键外的所有字段，用于插入操作-->
  <sql id="columns">
    <trim suffixOverrides=",">
      <if test="memberId != null">
        `member_id`,
      </if>
      <if test="storeId != null">
        `store_id`,
      </if>
      <if test="storeName != null">
        `store_name`,
      </if>
      <if test="goodsId != null">
        `goods_id`,
      </if>
      <if test="goodsName != null">
        `goods_name`,
      </if>
      <if test="productId != null">
        `product_id`,
      </if>
      <if test="buyNum != null">
        `buy_num`,
      </if>
      <if test="productPrice != null">
        `product_price`,
      </if>
      <if test="productImage != null">
        `product_image`,
      </if>
      <if test="specValueIds != null">
        `spec_value_ids`,
      </if>
      <if test="specValues != null">
        `spec_values`,
      </if>
      <if test="isChecked != null">
        `is_checked`,
      </if>
      <if test="updateTime != null">
        `update_time`,
      </if>
      <if test="promotionId != null">
        `promotion_id`,
      </if>
      <if test="promotionType != null">
        `promotion_type`,
      </if>
      <if test="promotionDescription != null">
        `promotion_description`,
      </if>
      <if test="offPrice != null">
        `off_price`,
      </if>
      <if test="productState != null">
        `product_state`,
      </if>
      <if test="areaCode != null">
        `area_code`,
      </if>
      <if test="financeRuleCode != null">
        `finance_rule_code`,
      </if>
      <if test="ruleTag != null">
        `rule_tag`,
      </if>
      <if test="distributeParent != null">
        `distribute_parent`,
      </if>
    </trim>
  </sql>
  <!--按照主键值进行操作-->
  <sql id="pkWhere">
    WHERE `cart_id` = #{primaryKey}  AND enabled_flag =1
  </sql>
  <!--操作条件-->
  <sql id="whereCondition">
    <if test="example != null">
      <trim prefix="WHERE" prefixOverrides="AND|OR">
        AND enabled_flag =1
        <if test="example.cartId != null">
          AND `cart_id` = #{example.cartId}
        </if>
        <if test="example.cartIdNotEquals != null">
          AND `cart_id` != #{example.cartIdNotEquals}
        </if>
        <if test="example.cartIdList != null">
          AND `cart_id` in
          <foreach collection="example.cartIdList" item="item" open="(" separator="," close=")">
            #{item}
          </foreach>
        </if>
        <if test="example.cartIdIn != null">
          AND `cart_id` in (${example.cartIdIn})
        </if>
        <if test="example.memberId != null">
          AND `member_id` = #{example.memberId}
        </if>
        <if test="example.storeId != null">
          AND `store_id` = #{example.storeId}
        </if>
        <if test="example.storeName != null">
          AND `store_name` = #{example.storeName}
        </if>
        <if test="example.storeNameLike != null">
          AND `store_name` like concat('%',#{example.storeNameLike},'%')
        </if>
        <if test="example.goodsId != null">
          AND `goods_id` = #{example.goodsId}
        </if>
        <if test="example.goodsName != null">
          AND `goods_name` = #{example.goodsName}
        </if>
        <if test="example.goodsNameLike != null">
          AND `goods_name` like concat('%',#{example.goodsNameLike},'%')
        </if>
        <if test="example.productId != null">
          AND `product_id` = #{example.productId}
        </if>
        <if test="example.buyNum != null">
          AND `buy_num` = #{example.buyNum}
        </if>
        <if test="example.productPrice != null">
          AND `product_price` = #{example.productPrice}
        </if>
        <if test="example.productImage != null">
          AND `product_image` = #{example.productImage}
        </if>
        <if test="example.specValueIds != null">
          AND `spec_value_ids` = #{example.specValueIds}
        </if>
        <if test="example.specValues != null">
          AND `spec_values` = #{example.specValues}
        </if>
        <if test="example.isChecked != null">
          AND `is_checked` = #{example.isChecked}
        </if>
        <if test="example.updateTimeAfter != null">
          AND `update_time` <![CDATA[ >= ]]> #{example.updateTimeAfter}
        </if>
        <if test="example.updateTimeBefore != null">
          AND `update_time` <![CDATA[ <= ]]> #{example.updateTimeBefore}
        </if>
        <if test="example.promotionId != null">
          AND `promotion_id` = #{example.promotionId}
        </if>
        <if test="example.promotionType != null">
          AND `promotion_type` = #{example.promotionType}
        </if>
        <if test="example.promotionDescription != null">
          AND `promotion_description` = #{example.promotionDescription}
        </if>
        <if test="example.offPrice != null">
          AND `off_price` = #{example.offPrice}
        </if>
        <if test="example.productState != null">
          AND `product_state` = #{example.productState}
        </if>
        <if test="example.productType != null">
          AND `product_type` = #{example.productType}
        </if>
        <if test="example.financeRuleCode != null">
          AND `finance_rule_code` = #{example.financeRuleCode}
        </if>
        <if test="example.ruleTag != null">
          AND `rule_tag` = #{example.ruleTag}
        </if>
        <if test="example.distributeParent != null">
          AND `distribute_parent` = #{example.distributeParent}
        </if>
      </trim>
    </if>
    <if test="example == null">
      where enabled_flag =1
    </if>
  </sql>
  <!--排序条件-->
  <sql id="orderBy">
    ORDER BY `cart_id` DESC
  </sql>
  <sql id="orderByOther">
    order by ${example.orderBy}
  </sql>
  <!--分组条件-->
  <sql id="groupBy">
    group by ${example.groupBy}
  </sql>
  <!--分页条件-->
  <sql id="limit">
    <if test="size != null and size &gt; 0">
      limit #{startRow},#{size}
    </if>
  </sql>
  <!--查询符合条件的记录数-->
  <select id="countByExample" parameterType="com.cfpamf.ms.mallorder.request.CartExample" resultType="java.lang.Integer">
    SELECT
      COUNT(*)
    FROM `bz_cart`
    <include refid="whereCondition" />
  </select>

  <!--根据主键查询记录-->
  <select id="getByPrimaryKey" resultType="com.cfpamf.ms.mallorder.po.CartPO">
    SELECT
      *
    FROM `bz_cart`
    <include refid="pkWhere" />
  </select>

  <!--查询符合条件的记录(所有字段)-->
  <select id="listByExample" resultType="com.cfpamf.ms.mallorder.po.CartPO">
    SELECT
      *
    FROM `bz_cart`
    <include refid="whereCondition" />
    <if test="example.groupBy != null">
      <include refid="groupBy" />
    </if>
    <choose>
      <when test="example.orderBy != null">
        <include refid="orderByOther" />
      </when>
      <otherwise>
        <include refid="orderBy" />
      </otherwise>
    </choose>
  </select>

  <!--分页查询符合条件的记录(所有字段)-->
  <select id="listPageByExample" resultMap="resultMap">
    SELECT
      *
    FROM `bz_cart`
    <include refid="whereCondition" />
    <if test="example.groupBy != null">
      <include refid="groupBy" />
    </if>
    <choose>
      <when test="example.orderBy != null">
        <include refid="orderByOther" />
      </when>
      <otherwise>
        <include refid="orderBy" />
      </otherwise>
    </choose>
    <include refid="limit" />
  </select>

  <!--查询符合条件的记录(指定字段)-->
  <select id="listFieldsByExample" resultMap="resultMap">
    SELECT
      ${fields}
    FROM `bz_cart`
    <include refid="whereCondition" />
    <if test="example.groupBy != null">
      <include refid="groupBy" />
    </if>
    <choose>
      <when test="example.orderBy != null">
        <include refid="orderByOther" />
      </when>
      <otherwise>
        <include refid="orderBy" />
      </otherwise>
    </choose>
  </select>
  <!--分页查询符合条件的记录(指定字段)-->
  <select id="listFieldsPageByExample" resultMap="resultMap">
    SELECT
      ${fields}
    FROM `bz_cart`
    <include refid="whereCondition" />
    <if test="example.groupBy != null">
      <include refid="groupBy" />
    </if>
    <choose>
      <when test="example.orderBy != null">
        <include refid="orderByOther" />
      </when>
      <otherwise>
        <include refid="orderBy" />
      </otherwise>
    </choose>
    <include refid="limit" />
  </select>
  <!--根据条件删除记录，可多条删除-->
  <update id="deleteByExample">
    update `bz_cart` SET enabled_flag =0
    <include refid="whereCondition" />
  </update>
  <!--根据主键删除记录-->
  <update id="deleteByPrimaryKey">
    update `bz_cart` SET enabled_flag =0
    <include refid="pkWhere" />
  </update>
  <!--插入一条记录-->
<!--  <insert id="insert" keyColumn="cart_id" keyProperty="cartId" parameterType="com.cfpamf.ms.mallorder.po.CartPO" useGeneratedKeys="true">-->
<!--    INSERT INTO `bz_cart`(-->
<!--    <include refid="columns" />-->
<!--    )-->
<!--    VALUES(-->
<!--    <trim suffixOverrides=",">-->
<!--      <if test="memberId != null">-->
<!--        #{memberId},-->
<!--      </if>-->
<!--      <if test="storeId != null">-->
<!--        #{storeId},-->
<!--      </if>-->
<!--      <if test="storeName != null">-->
<!--        #{storeName},-->
<!--      </if>-->
<!--      <if test="goodsId != null">-->
<!--        #{goodsId},-->
<!--      </if>-->
<!--      <if test="goodsName != null">-->
<!--        #{goodsName},-->
<!--      </if>-->
<!--      <if test="productId != null">-->
<!--        #{productId},-->
<!--      </if>-->
<!--      <if test="buyNum != null">-->
<!--        #{buyNum},-->
<!--      </if>-->
<!--      <if test="productPrice != null">-->
<!--        #{productPrice},-->
<!--      </if>-->
<!--      <if test="productImage != null">-->
<!--        #{productImage},-->
<!--      </if>-->
<!--      <if test="specValueIds != null">-->
<!--        #{specValueIds},-->
<!--      </if>-->
<!--      <if test="specValues != null">-->
<!--        #{specValues},-->
<!--      </if>-->
<!--      <if test="isChecked != null">-->
<!--        #{isChecked},-->
<!--      </if>-->
<!--      <if test="updateTime != null">-->
<!--        #{updateTime},-->
<!--      </if>-->
<!--      <if test="promotionId != null">-->
<!--        #{promotionId},-->
<!--      </if>-->
<!--      <if test="promotionType != null">-->
<!--        #{promotionType},-->
<!--      </if>-->
<!--      <if test="promotionDescription != null">-->
<!--        #{promotionDescription},-->
<!--      </if>-->
<!--      <if test="offPrice != null">-->
<!--        #{offPrice},-->
<!--      </if>-->
<!--      <if test="productState != null">-->
<!--        #{productState},-->
<!--      </if>-->
<!--      <if test="areaCode != null">-->
<!--        #{areaCode},-->
<!--      </if>-->
<!--      <if test="financeRuleCode != null">-->
<!--        #{financeRuleCode},-->
<!--      </if>-->
<!--      <if test="ruleTag != null">-->
<!--        #{ruleTag},-->
<!--      </if>-->
<!--      <if test="distributeParent != null">-->
<!--        #{distributeParent},-->
<!--      </if>-->
<!--    </trim>-->
<!--    )-->
<!--  </insert>-->
  <!--按条件更新记录中不为空的字段-->
  <update id="updateByExampleSelective">
    UPDATE `bz_cart`
    <trim prefix="SET" suffixOverrides=",">
      <if test="record.memberId != null">
        `member_id` = #{record.memberId},
      </if>
      <if test="record.storeId != null">
        `store_id` = #{record.storeId},
      </if>
      <if test="record.storeName != null">
        `store_name` = #{record.storeName},
      </if>
      <if test="record.goodsId != null">
        `goods_id` = #{record.goodsId},
      </if>
      <if test="record.goodsName != null">
        `goods_name` = #{record.goodsName},
      </if>
      <if test="record.productId != null">
        `product_id` = #{record.productId},
      </if>
      <if test="record.buyNum != null">
        `buy_num` = #{record.buyNum},
      </if>
      <if test="record.productPrice != null">
        `product_price` = #{record.productPrice},
      </if>
      <if test="record.productImage != null">
        `product_image` = #{record.productImage},
      </if>
      <if test="record.specValueIds != null">
        `spec_value_ids` = #{record.specValueIds},
      </if>
      <if test="record.specValues != null">
        `spec_values` = #{record.specValues},
      </if>
      <if test="record.isChecked != null">
        `is_checked` = #{record.isChecked},
      </if>
      <if test="record.updateTime != null">
        `update_time` = #{record.updateTime},
      </if>
      <if test="record.promotionId != null">
        `promotion_id` = #{record.promotionId},
      </if>
      <if test="record.promotionType != null">
        `promotion_type` = #{record.promotionType},
      </if>
      <if test="record.promotionDescription != null">
        `promotion_description` = #{record.promotionDescription},
      </if>
      <if test="record.offPrice != null">
        `off_price` = #{record.offPrice},
      </if>
      <if test="record.productState != null">
        `product_state` = #{record.productState},
      </if>
      <if test="record.financeRuleCode != null">
        `finance_rule_code` = #{record.financeRuleCode},
      </if>
      <if test="record.ruleTag != null">
        `rule_tag` = #{record.ruleTag},
      </if>
      <if test="record.distributeParent != null">
        `distribute_parent` = #{record.distributeParent},
      </if>
    </trim>
    <include refid="whereCondition" />
  </update>
  <!--按照主键更新记录中不为空的字段-->
  <update id="updateByPrimaryKeySelective">
    UPDATE `bz_cart`
    <trim prefix="SET" suffixOverrides=",">
      <if test="memberId != null">
        `member_id` = #{memberId},
      </if>
      <if test="storeId != null">
        `store_id` = #{storeId},
      </if>
      <if test="storeName != null">
        `store_name` = #{storeName},
      </if>
      <if test="goodsId != null">
        `goods_id` = #{goodsId},
      </if>
      <if test="goodsName != null">
        `goods_name` = #{goodsName},
      </if>
      <if test="productId != null">
        `product_id` = #{productId},
      </if>
      <if test="buyNum != null">
        `buy_num` = #{buyNum},
      </if>
      <if test="productPrice != null">
        `product_price` = #{productPrice},
      </if>
      <if test="productImage != null">
        `product_image` = #{productImage},
      </if>
      <if test="specValueIds != null">
        `spec_value_ids` = #{specValueIds},
      </if>
      <if test="specValues != null">
        `spec_values` = #{specValues},
      </if>
      <if test="isChecked != null">
        `is_checked` = #{isChecked},
      </if>
      <if test="updateTime != null">
        `update_time` = #{updateTime},
      </if>
      <if test="promotionId != null">
        `promotion_id` = #{promotionId},
      </if>
      <if test="promotionType != null">
        `promotion_type` = #{promotionType},
      </if>
      <if test="promotionDescription != null">
        `promotion_description` = #{promotionDescription},
      </if>
      <if test="offPrice != null">
        `off_price` = #{offPrice},
      </if>
      <if test="productState != null">
        `product_state` = #{productState},
      </if>
      <if test="financeRuleCode != null">
        `finance_rule_code` = #{financeRuleCode},
      </if>
      <if test="ruleTag != null">
        `rule_tag` = #{ruleTag},
      </if>
      <if test="distributeParent != null">
        `distribute_parent` = #{distributeParent},
      </if>
    </trim>
    WHERE `cart_id` = #{cartId}
  </update>


  <!--按照主键更新记录中不为空的字段-->
  <update id="updateByPrimaryKeySelectiveNull" >
    UPDATE `bz_cart`
    <trim prefix="SET" suffixOverrides=",">
      <if test="cartPO.memberId != null">
        `member_id` = #{cartPO.memberId},
      </if>
      <if test="cartPO.storeId != null">
        `store_id` = #{cartPO.storeId},
      </if>
      <if test="cartPO.storeName != null">
        `store_name` = #{cartPO.storeName},
      </if>
      <if test="cartPO.goodsId != null">
        `goods_id` = #{cartPO.goodsId},
      </if>
      <if test="cartPO.goodsName != null">
        `goods_name` = #{cartPO.goodsName},
      </if>
      <if test="cartPO.productId != null">
        `product_id` = #{cartPO.productId},
      </if>
      <if test="cartPO.buyNum != null">
        `buy_num` = #{cartPO.buyNum},
      </if>
      <if test="cartPO.productPrice != null">
        `product_price` = #{cartPO.productPrice},
      </if>
      <if test="cartPO.areaCode != null">
        `area_code` = #{cartPO.areaCode},
      </if>
      <if test="cartPO.productImage != null">
        `product_image` = #{cartPO.productImage},
      </if>
      <if test="cartPO.specValueIds != null">
        `spec_value_ids` = #{cartPO.specValueIds},
      </if>
      <if test="cartPO.specValues != null">
        `spec_values` = #{cartPO.specValues},
      </if>
      <if test="cartPO.isChecked != null">
        `is_checked` = #{cartPO.isChecked},
      </if>
      <if test="cartPO.updateTime != null">
        `update_time` = #{cartPO.updateTime},
      </if>
      <if test="cartPO.promotionId != null">
        `promotion_id` = #{cartPO.promotionId},
      </if>
      <if test="cartPO.promotionType != null">
        `promotion_type` = #{cartPO.promotionType},
      </if>
      <if test="cartPO.promotionDescription != null">
        `promotion_description` = #{cartPO.promotionDescription},
      </if>
      <if test="cartPO.offPrice != null">
        `off_price` = #{cartPO.offPrice},
      </if>
      <if test="cartPO.productState != null">
        `product_state` = #{cartPO.productState},
      </if>
      <if test="cartPO.financeRuleCode != null">
        `finance_rule_code` = #{cartPO.financeRuleCode},
      </if>
      <if test="cartPO.ruleTag != null">
        `rule_tag` = #{cartPO.ruleTag},
      </if>
      <if test="cartPO.financeRuleCode == null">
        `finance_rule_code` = '',
      </if>
      <if test="cartPO.ruleTag == null">
        `rule_tag` = '',
      </if>
      <if test="cartPO.distributeParent != null">
        `distribute_parent` = #{cartPO.distributeParent},
      </if>
    </trim>
    WHERE `cart_id` = #{cartPO.cartId}
  </update>
</mapper>
