<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.OrderPromotionSendCouponMapper">
  <resultMap id="resultMap" type="com.cfpamf.ms.mallorder.po.OrderPromotionSendCouponPO">
      <id column="send_coupon_id" property="sendCouponId" />
      <result column="create_time" property="createTime" />
      <result column="update_time" property="updateTime" />
      <result column="enabled_flag" property="enabledFlag" />
      <result column="order_sn" property="orderSn" />
      <result column="promotion_grade" property="promotionGrade" />
      <result column="promotion_type" property="promotionType" />
      <result column="promotion_id" property="promotionId" />
      <result column="is_store" property="isStore" />
      <result column="coupon_id" property="couponId" />
      <result column="number" property="number" />
      <result column="create_by" property="createBy" />
      <result column="update_by" property="updateBy" />
  </resultMap>
  <!--除主键外的所有字段，用于插入操作-->
  <sql id="columns">
    <trim suffixOverrides=",">
      <if test="sendCouponId != null">
        `send_coupon_id`,
      </if>
      <if test="orderSn != null">
        `order_sn`,
      </if>
      <if test="promotionGrade != null">
        `promotion_grade`,
      </if>
      <if test="promotionType != null">
        `promotion_type`,
      </if>
      <if test="promotionId != null">
        `promotion_id`,
      </if>
      <if test="isStore != null">
        `is_store`,
      </if>
      <if test="couponId != null">
        `coupon_id`,
      </if>
      <if test="number != null">
        `number`,
      </if>
    </trim>
  </sql>
  <!--按照主键值进行操作-->
  <sql id="pkWhere">
    WHERE `send_coupon_id` = #{primaryKey} AND enabled_flag =1
  </sql>
  <!--操作条件-->
  <sql id="whereCondition">
    <if test="example != null">
      <trim prefix="WHERE" prefixOverrides="AND|OR">
        AND enabled_flag =1
        <if test="example.sendCouponIdNotEquals != null">
          AND `send_coupon_id` != #{example.sendCouponIdNotEquals}
        </if>
        <if test="example.sendCouponIdIn != null">
          AND `send_coupon_id` in (${example.sendCouponIdIn})
        </if>
        <if test="example.orderSn != null">
          AND `order_sn` = #{example.orderSn}
        </if>
        <if test="example.orderSnLike != null">
          AND `order_sn` like concat('%',#{example.orderSnLike},'%')
        </if>
        <if test="example.promotionGrade != null">
          AND `promotion_grade` = #{example.promotionGrade}
        </if>
        <if test="example.promotionType != null">
          AND `promotion_type` = #{example.promotionType}
        </if>
        <if test="example.promotionId != null">
          AND `promotion_id` = #{example.promotionId}
        </if>
        <if test="example.isStore != null">
          AND `is_store` = #{example.isStore}
        </if>
        <if test="example.couponId != null">
          AND `coupon_id` = #{example.couponId}
        </if>
        <if test="example.number != null">
          AND `number` = #{example.number}
        </if>
      </trim>
    </if>
    <if test="example == null">
      where enabled_flag =1
    </if>
  </sql>
  <!--排序条件-->
  <sql id="orderBy">
    ORDER BY `send_coupon_id` DESC
  </sql>
  <sql id="orderByOther">
    order by ${example.orderBy}
  </sql>
  <!--分组条件-->
  <sql id="groupBy">
    group by ${example.groupBy}
  </sql>
  <!--分页条件-->
  <sql id="limit">
    <if test="size != null and size &gt; 0">
      limit #{startRow},#{size}
    </if>
  </sql>
  <!--查询符合条件的记录数-->
  <select id="countByExample" parameterType="com.cfpamf.ms.mallorder.request.OrderPromotionSendCouponExample" resultType="java.lang.Integer">
    SELECT
      COUNT(*)
    FROM `bz_order_promotion_send_coupon`
    <include refid="whereCondition" />
  </select>
  <!--根据主键查询记录-->
  <select id="getByPrimaryKey" resultMap="resultMap">
    SELECT
      *
    FROM `bz_order_promotion_send_coupon`
    <include refid="pkWhere" />
  </select>
  <!--查询符合条件的记录(所有字段)-->
  <select id="listByExample" resultMap="resultMap">
    SELECT
      *
    FROM `bz_order_promotion_send_coupon`
    <include refid="whereCondition" />
    <if test="example.groupBy != null">
      <include refid="groupBy" />
    </if>
    <choose>
      <when test="example.orderBy != null">
        <include refid="orderByOther" />
      </when>
      <otherwise>
        <include refid="orderBy" />
      </otherwise>
    </choose>
  </select>
  <!--分页查询符合条件的记录(所有字段)-->
  <select id="listPageByExample" resultMap="resultMap">
    SELECT
      *
    FROM `bz_order_promotion_send_coupon`
    <include refid="whereCondition" />
    <if test="example.groupBy != null">
      <include refid="groupBy" />
    </if>
    <choose>
      <when test="example.orderBy != null">
        <include refid="orderByOther" />
      </when>
      <otherwise>
        <include refid="orderBy" />
      </otherwise>
    </choose>
    <include refid="limit" />
  </select>
  <!--查询符合条件的记录(指定字段)-->
  <select id="listFieldsByExample" resultMap="resultMap">
    SELECT
      ${fields}
    FROM `bz_order_promotion_send_coupon`
    <include refid="whereCondition" />
    <if test="example.groupBy != null">
      <include refid="groupBy" />
    </if>
    <choose>
      <when test="example.orderBy != null">
        <include refid="orderByOther" />
      </when>
      <otherwise>
        <include refid="orderBy" />
      </otherwise>
    </choose>
  </select>
  <!--分页查询符合条件的记录(指定字段)-->
  <select id="listFieldsPageByExample" resultMap="resultMap">
    SELECT
      ${fields}
    FROM `bz_order_promotion_send_coupon`
    <include refid="whereCondition" />
    <if test="example.groupBy != null">
      <include refid="groupBy" />
    </if>
    <choose>
      <when test="example.orderBy != null">
        <include refid="orderByOther" />
      </when>
      <otherwise>
        <include refid="orderBy" />
      </otherwise>
    </choose>
    <include refid="limit" />
  </select>
  <!--根据条件删除记录，可多条删除-->
  <update id="deleteByExample">
    update `bz_order_promotion_send_coupon`SET enabled_flag =0
    <include refid="whereCondition" />
  </update>
  <!--根据主键删除记录-->
  <update id="deleteByPrimaryKey">
    update `bz_order_promotion_send_coupon`SET enabled_flag =0
    <include refid="pkWhere" />
  </update>
  <!--插入一条记录-->
  <insert id="insert" keyColumn="send_coupon_id" keyProperty="sendCouponId" parameterType="com.cfpamf.ms.mallorder.po.OrderPromotionSendCouponPO" useGeneratedKeys="true">
    INSERT INTO `bz_order_promotion_send_coupon`(
    <include refid="columns" />
    )
    VALUES(
    <trim suffixOverrides=",">
      <if test="sendCouponId != null">
        #{sendCouponId},
      </if>
      <if test="orderSn != null">
        #{orderSn},
      </if>
      <if test="promotionGrade != null">
        #{promotionGrade},
      </if>
      <if test="promotionType != null">
        #{promotionType},
      </if>
      <if test="promotionId != null">
        #{promotionId},
      </if>
      <if test="isStore != null">
        #{isStore},
      </if>
      <if test="couponId != null">
        #{couponId},
      </if>
      <if test="number != null">
        #{number},
      </if>
    </trim>
    )
  </insert>
  <!--按条件更新记录中不为空的字段-->
  <update id="updateByExampleSelective">
    UPDATE `bz_order_promotion_send_coupon`
    <trim prefix="SET" suffixOverrides=",">
      <if test="record.orderSn != null">
        `order_sn` = #{record.orderSn},
      </if>
      <if test="record.promotionGrade != null">
        `promotion_grade` = #{record.promotionGrade},
      </if>
      <if test="record.promotionType != null">
        `promotion_type` = #{record.promotionType},
      </if>
      <if test="record.promotionId != null">
        `promotion_id` = #{record.promotionId},
      </if>
      <if test="record.isStore != null">
        `is_store` = #{record.isStore},
      </if>
      <if test="record.couponId != null">
        `coupon_id` = #{record.couponId},
      </if>
      <if test="record.number != null">
        `number` = #{record.number},
      </if>
    </trim>
    <include refid="whereCondition" />
  </update>
  <!--按照主键更新记录中不为空的字段-->
  <update id="updateByPrimaryKeySelective">
    UPDATE `bz_order_promotion_send_coupon`
    <trim prefix="SET" suffixOverrides=",">
      <if test="orderSn != null">
        `order_sn` = #{orderSn},
      </if>
      <if test="promotionGrade != null">
        `promotion_grade` = #{promotionGrade},
      </if>
      <if test="promotionType != null">
        `promotion_type` = #{promotionType},
      </if>
      <if test="promotionId != null">
        `promotion_id` = #{promotionId},
      </if>
      <if test="isStore != null">
        `is_store` = #{isStore},
      </if>
      <if test="couponId != null">
        `coupon_id` = #{couponId},
      </if>
      <if test="number != null">
        `number` = #{number},
      </if>
    </trim>
    WHERE `send_coupon_id` = #{sendCouponId}
  </update>
</mapper>
