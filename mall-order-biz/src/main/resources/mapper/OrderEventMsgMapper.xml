<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.OrderEventMsgMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.mallorder.po.OrderEventMsgPO">
        <result column="id" property="id" />
        <result column="enabled_flag" property="enabledFlag" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="pay_sn" property="paySn" />
        <result column="order_sn" property="orderSn" />
        <result column="order_pattern" property="orderPattern" />
        <result column="order_type" property="orderType" />
        <result column="user_no" property="userNo" />
        <result column="store_id" property="storeId" />
        <result column="is_self" property="isSelf" />
        <result column="event_type" property="eventType" />
        <result column="event_type_desc" property="eventTypeDesc" />
        <result column="event_time" property="eventTime" />
        <result column="channel" property="channel" />
        <result column="refund_sn" property="refundSn" />
        <result column="apply_source" property="applySource" />
        <result column="status" property="status" />
        <result column="retry_times" property="retryTimes" />
        <result column="result" property="result" />
        <result column="original" property="original" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        enabled_flag,
        create_time,
        update_time,
        pay_sn, order_sn, order_pattern, order_type, user_no, store_id, is_self, event_type, event_type_desc, event_time, channel, refund_sn, apply_source, status, retry_times, result, original
    </sql>

</mapper>
