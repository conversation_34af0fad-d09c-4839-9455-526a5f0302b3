package com.cfpamf.ms.mallorder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.ms.loan.facade.vo.external.mall.MallContractCodesVo;
import com.cfpamf.ms.loan.facade.vo.external.mall.MallContractContentVO;
import com.cfpamf.ms.mallorder.dto.OrderAutoPayDTO;
import com.cfpamf.ms.mallorder.po.OrderPayPO;
import com.cfpamf.ms.mallorder.req.OrderPayRequest;
import com.cfpamf.ms.mallorder.vo.MallWithholdCheckResponseVO;
import com.cfpamf.ms.mallorder.vo.OrderPayBriefInfoVO;
import com.cfpamf.ms.mallorder.vo.OrderPayInfoVO;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 订单支付  service 接口
 */
public interface IOrderPayService extends IService<OrderPayPO> {

    /**
     * @param
     * @return java.util.List<com.cfpamf.ms.mallorder.dto.OrderAutoPayDTO>
     * @description : 获取待自动支付订单
     */
    List<OrderAutoPayDTO> listOrderAutoPay();

    /**
     * 获取支付单号
     *
     * @param memberId  用户ID
     */
    String getOrderPno(String memberId);

    /**
     * 根据支付单号查询订单
     *
     * @param pno   支付单号
     */
    OrderPayBriefInfoVO getOrderPayBriefInfoByPno(String pno);


    OrderPayPO getByPaySn(String paySn);

    /***
    * @param request
    * @return com.cfpamf.ms.loan.facade.vo.external.mall.MallContractContentVO
    * @description : 预览合同
    */
    MallContractContentVO contractPreview(@NotNull @Valid OrderPayRequest request);


    MallContractCodesVo listLoanContractCode(@NotNull @Valid OrderPayRequest request);

    /**
     * 预付订金订单类型需要特殊处理支付金额
     *
     * @param orderSn
     * @param payType 订单类型，1-订金
     * @param orderPayInfoVO
     * */
    void dealPreSellResult(String orderSn,Integer payType,OrderPayInfoVO orderPayInfoVO);


    /**
     * @param bizId
     * @return void
     * @description :渠道交易流水号补偿
     */
    Boolean orderBankTrxNoMakeUp(Long bizId);

    /**
     * 代扣还款校验
     *
     * @param orderNo 订单编号
     * @return 校验结果
     */
    MallWithholdCheckResponseVO checkWithHold(String orderNo);
}
