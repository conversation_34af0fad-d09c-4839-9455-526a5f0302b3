package com.cfpamf.ms.mallorder.controller.fegin.facade;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.mallorder.dto.DbcTrialRefundCommissionDTO;
import com.cfpamf.ms.mallorder.dto.OrderEventNotifyDTO;
import com.cfpamf.ms.mallorder.dto.OrderItemCommissionDTO;
import com.cfpamf.ms.mallorder.vo.DbcDistributionSaleOrderVO;
import com.cfpamf.ms.mallorder.vo.DbcTrialRefundCommissionVO;
import com.cfpamf.ms.mallorder.vo.FreightCostItemVO;
import com.cfpamf.ms.mallorder.vo.ManagerCommissionItemVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> 2021/10/8.
 */
@FeignClient(name = "dbc-service", url = "${dbc-service.url}")
public interface DbcServiceFeign {

    @Deprecated
    @ApiOperation("根据订单号获取订单佣金")
    @GetMapping({"/cms-dbc-system/order/getOrderCommission"})
    Result<List<OrderItemCommissionDTO>> getOrderCommission(@RequestParam("orderId") String orderId);

    @ApiOperation("根据订单号获取订单佣金V2")
    @GetMapping("/v1/feign/dbc/order/getOrderCommissionV2")
    Result<List<OrderItemCommissionDTO>> getOrderCommissionV2(@RequestParam("orderId") String orderId);
    
    @ApiOperation("获取冲抵后的订单佣金")
    @GetMapping("/v1/feign/dbc/order/getOrderOffsetCommission")
    Result<List<OrderItemCommissionDTO>> getOrderOffsetCommission(@RequestParam("orderId") String orderId);

    @ApiOperation("根据订单号试算总佣金")
    @PostMapping("/v1/feign/dbc/order/orderCommissionTrial")
    Result<BigDecimal> trialOrderCommission(@RequestParam("orderId") String orderId);

    @ApiOperation("退款单退佣金计算")
    @PostMapping("/v1/feign/dbc/refundOrder/trialCalcRefundCommission")
    Result<DbcTrialRefundCommissionVO> trialCalcRefundCommission(@RequestBody DbcTrialRefundCommissionDTO dto);

    @ApiOperation("佣金激励费历史数据处理")
    @PostMapping("/v1/feign/dbc/order/commissionIncentiveFeeHistoryData")
    Result<List<String>> commissionIncentiveFeeHistoryData(@RequestBody List<OrderEventNotifyDTO> orderEventNotifyDTOList);

    @ApiOperation("查询客户经理佣金")
    @PostMapping("/v1/feign/dbc/distributionSalesUser/managerCommission")
    Result<BigDecimal> managerCommission(@RequestParam(value = "orderId") String orderId,
                                         @RequestParam(value = "businessChannel") Integer businessChannel,
                                         @RequestParam(value = "storeId") String storeId);

    @ApiOperation("查询客户经理佣金-商品行")
    @PostMapping("/v1/feign/dbc/distributionSalesUser/managerCommissionByCommodities")
    Result<List<ManagerCommissionItemVO>> managerCommissionByCommodities(@RequestParam(value = "orderId") String orderId,
                                                                         @RequestParam(value = "businessChannel") Integer businessChannel,
                                                                         @RequestParam(value = "storeId") String storeId);

    @ApiOperation("通过订单号获取分销订单列表")
    @GetMapping("/v1/feign/dbc/order/getListByOrderId")
    Result<List<DbcDistributionSaleOrderVO>> getListByOrderId(@RequestParam("orderId") String orderId);

    @ApiOperation("商品行一段运费")
    @PostMapping("/v1/feign/dbc/distributionSalesUser/freightCostByCommodities")
    Result<List<FreightCostItemVO>> freightCostByCommodities(@RequestParam(value = "orderId") String orderId,
                                                             @RequestParam(value = "businessChannel") Integer businessChannel,
                                                             @RequestParam(value = "storeId") String storeId);
}
