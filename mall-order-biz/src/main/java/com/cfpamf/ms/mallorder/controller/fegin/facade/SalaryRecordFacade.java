package com.cfpamf.ms.mallorder.controller.fegin.facade;

import com.cdfinance.hrms.facade.vo.BranchRelationVO;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.mallorder.vo.EmployeePaySubjectRelationVO;
import com.cfpamf.ms.mallorder.vo.hrmsVO.OutBranchLifeAndInteriorInfoVO;
import com.cfpamf.ms.mallorder.vo.hrmsVO.QueryBranchLifeAndInteriorRequest;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @description
 * @author: Binting
 * @create: 2021-09-17 15:00
 **/

@FeignClient(name = "hrms-biz", url = "${hrms-biz.url}")
public interface SalaryRecordFacade {

    /**
     * 查询员工以及发薪主体之间的关系
     */
    @ApiOperation(value = "查询员工以及发薪主体之间的关系")
    @PostMapping("/hrms/salary/system/record/queryEmployeePaySubjectRelation")
    Result<List<EmployeePaySubjectRelationVO>> queryEmployeePaySubjectRelation(@RequestBody List<String> employeeCodeList);


    /**
     * 按分支编码查询分支关联片区，区域信息
     * @param branchCode
     * @return
     */
    @PostMapping("/hrms/organization/mdm/branchRelationByCode")
    Result<BranchRelationVO> queryBranchRelationByCode(@RequestParam("branchCode") String branchCode);

    /**
     * 根据分支编码集合，查询分支最新的生服对接人和分支内务人员信息集合
     * 入参：分支编码集合
     * 出参：分支编码、生服对接人工号、分支内务工号信息集合
     * 使用域：电商
     * @param request
     * @return
     */
    @PostMapping("/hrms/employee/queryBranchLifeAndInteriorList")
    Result<List<OutBranchLifeAndInteriorInfoVO>> queryBranchLifeAndInteriorList(@RequestBody QueryBranchLifeAndInteriorRequest request);

}


