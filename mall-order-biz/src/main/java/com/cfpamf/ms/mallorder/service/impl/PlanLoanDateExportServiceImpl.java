package com.cfpamf.ms.mallorder.service.impl;


import com.cfpamf.ms.mall.filecenter.component.FileComponent;
import com.cfpamf.ms.mall.filecenter.domain.dto.FileDTO;
import com.cfpamf.ms.mall.filecenter.domain.vo.ExcelAsyncExportVO;
import com.cfpamf.ms.mall.filecenter.service.impl.AbstractExcelDataExportServiceImpl;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.PlanLoanDateFailDTO;
import com.cfpamf.ms.mallorder.req.ExchangeApplyListPcReq;
import com.cfpamf.ms.mallorder.service.IPlanLoanDateExportService;
import com.cfpamf.ms.mallorder.vo.OrderExchangeReturnListVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Service
@Slf4j
public class PlanLoanDateExportServiceImpl extends AbstractExcelDataExportServiceImpl<PlanLoanDateFailDTO, List<PlanLoanDateFailDTO>>
        implements IPlanLoanDateExportService {

    @Autowired
    private FileComponent fileComponent;

    @Value("${spring.application.name}")
    private String appName;

    @Override
    public FileDTO asyncExportFailureData(Long userId, String userName, List<PlanLoanDateFailDTO> failureList) throws Exception {
        ExcelAsyncExportVO excelAsyncExportVO = new ExcelAsyncExportVO();
        String bizModule = "计划放款日失败列表";
        excelAsyncExportVO.setBizModule(bizModule);
        excelAsyncExportVO.setUserId(userId);
        excelAsyncExportVO.setUserName(userName);
        excelAsyncExportVO.setUserType(OrderConst.LOG_ROLE_ADMIN);
        return fileComponent.executeAsyncExportExcelWithAnnotation(excelAsyncExportVO, appName, bizModule,
                this, failureList, 1000, new PlanLoanDateFailDTO());
    }

    @Override
    public Integer getDataCounts(List<PlanLoanDateFailDTO> planLoanDateFailDTOSet) {
        return planLoanDateFailDTOSet.size();
    }

    @Override
    public List<PlanLoanDateFailDTO> getDataByPage(List<PlanLoanDateFailDTO> planLoanDateFailDTOSet, Integer pageNum, Integer pageSize) {
        log.info("PlanLoanDateExportServiceImpl getDataByPage planLoanDateFailDTOSet.size() = {}", planLoanDateFailDTOSet.size());
        return new ArrayList<>(planLoanDateFailDTOSet);
    }
}
