package com.cfpamf.ms.mallorder.controller.admin;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.mallorder.common.enums.OrderStatusEnum;
import com.cfpamf.ms.mallorder.dto.OrderDayDTO;
import com.cfpamf.ms.mallorder.dto.OrderSaleInfoDTO;
import com.cfpamf.ms.mallorder.dto.SaleTotalDayDTO;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.model.OrderReportModel;
import com.cfpamf.ms.mallorder.request.OrderExample;
import com.cfpamf.ms.mallorder.vo.AdminIndexVO;
import com.cfpamf.ms.mallorder.vo.SalesVolumeVO;
import com.slodon.bbc.core.controller.BaseController;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import com.slodon.bbc.core.util.TimeUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Api(tags = "admin-首页概况")
@RestController
@RequestMapping("admin/dashboard")
public class AdminIndexController extends BaseController {

    @Resource
    private OrderModel orderModel;
    @Resource
    private OrderReportModel orderReportModel;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 平台端首页概况缓存key
     */
    private static final String INDEX_INFO_CACHE_KEY = "new_mall_admin_index_info";

    @SneakyThrows
    @ApiOperation(value = "首页概况信息")
    @GetMapping("indexInfo")
    public JsonResult<AdminIndexVO> indexInfo(HttpServletRequest request) {
        String cache = stringRedisTemplate.opsForValue().get(INDEX_INFO_CACHE_KEY);
        if (StringUtils.isNotBlank(cache)){
            AdminIndexVO adminIndexVO = JSONObject.parseObject(cache, AdminIndexVO.class);
            return SldResponse.success(adminIndexVO);
        }

        /**
         * 初始化时间参数
         */
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        Date startTime = sdf.parse(TimeUtil.getToday() + " 00:00:00");
        Date endTime = sdf.parse(TimeUtil.getToday() + " 23:59:59");

        /**
         * 创建VO对象
         */
        AdminIndexVO vo = new AdminIndexVO();

        /**
         * 并行获取远程数据 member、shop、goods
         */
        orderReportModel.wrapBoardRemoteData(vo, startTime, endTime);

        /**
         * 封装本地参数
         */
        OrderExample todayOrderQuery = new OrderExample();
        todayOrderQuery.setCreateTimeAfter(startTime);
        todayOrderQuery.setCreateTimeBefore(endTime);
        todayOrderQuery.setOrderStateList(OrderStatusEnum.paidStatus());
        OrderSaleInfoDTO todaySaleInfo = orderReportModel.orderSaleInfo(todayOrderQuery);

        // 1、今日下单数
        vo.setOrderNum(todaySaleInfo.getTotalNum());
        // 2、今日营业额
        vo.setDailySale(todaySaleInfo.getTotalAmount());

        //3、一周概况 订单增长
        OrderExample orderExample1 = new OrderExample();
        orderExample1.setCreateTimeAfter(TimeUtil.getDayAgoDate(new Date(), -6));
        orderExample1.setCreateTimeBefore(TimeUtil.getDayAgoDate(new Date(), 0));
        orderExample1.setOrderStateList(OrderStatusEnum.paidStatus());
        List<OrderDayDTO> orderDayDto = orderModel.getOrderDayDto(orderExample1);
        vo.setOrderWeeklyReport(getWeeklyReport(orderDayDto));

        //4、一周概况 销售总额增长
        vo.setSaleTotalWeeklyReport(getSaleTotalWeeklyReport(orderDayDto));

        //7日内 - 店铺销售排名数据 付款后计算商品销量，商品销量 = 购买数量-退货数量
        vo.setStoreSaleRank(orderReportModel.storeSaleRankV2(orderExample1));

        //7日内 - 商品销售排名数据 付款后即计算商品销量，商品销量 = 购买数量-退货数量
        vo.setGoodsSaleRank(orderReportModel.goodsSaleRankV2(orderExample1));

        //销售额类别占比
        List<SalesVolumeVO> yearSaleCateRate = orderReportModel.getSaleCateStatistics("year");
        List<SalesVolumeVO> monthSaleCateRate = orderReportModel.getSaleCateStatistics("month");
        List<SalesVolumeVO> weekSaleCateRate = orderReportModel.getSaleCateStatistics("week");
        vo.setYearSaleCateRate(yearSaleCateRate);
        vo.setMonthSaleCateRate(monthSaleCateRate);
        vo.setWeekSaleCateRate(weekSaleCateRate);
        stringRedisTemplate.opsForValue().set(INDEX_INFO_CACHE_KEY,JSONObject.toJSONString(vo));
        stringRedisTemplate.expire(INDEX_INFO_CACHE_KEY,1, TimeUnit.HOURS);

        return SldResponse.success(vo);
    }

    private List<SaleTotalDayDTO> getSaleTotalWeeklyReport(List<OrderDayDTO> result) {
        List<SaleTotalDayDTO> list = new ArrayList<>(7);
        if (CollectionUtils.isEmpty(result)) {
            for (int i = 0; i < 7; i++) {
                SaleTotalDayDTO saleTotalDayDTO = new SaleTotalDayDTO();
                saleTotalDayDTO.setDay(TimeUtil.getYesterdayOfWeek(i - 6).toString());
                saleTotalDayDTO.setAmount(BigDecimal.ZERO);
                list.add(saleTotalDayDTO);
            }
        } else {
            for (int i = 0; i < 7; i++) {
                String yesterday = TimeUtil.getYesterday(i - 6);
                SaleTotalDayDTO saleTotalDayDTO = new SaleTotalDayDTO();
                saleTotalDayDTO.setDay(TimeUtil.getYesterdayOfWeek(i - 6).toString());
                saleTotalDayDTO.setAmount(BigDecimal.ZERO);
                for (OrderDayDTO o : result) {
                    if (o.getOrderDay().equals(yesterday)) {
                        saleTotalDayDTO.setAmount(o.getOrderAmount());
                        break;
                    }
                }
                list.add(saleTotalDayDTO);
            }
        }
        return list;
    }

    private List<AdminIndexVO.OrderReportVO> getWeeklyReport(List<OrderDayDTO> result) {
        List<AdminIndexVO.OrderReportVO> list = new ArrayList<>(7);
        if (CollectionUtils.isEmpty(result)) {
            for (int i = 0; i < 7; i++) {
                AdminIndexVO.OrderReportVO vo = new AdminIndexVO.OrderReportVO();
                vo.setDay(TimeUtil.getYesterdayOfWeek(i - 6).toString());
                vo.setNumber(0);
                list.add(vo);
            }
        } else {
            for (int i = 0; i < 7; i++) {
                String yesterday = TimeUtil.getYesterday(i - 6);
                AdminIndexVO.OrderReportVO vo = new AdminIndexVO.OrderReportVO();
                vo.setDay(TimeUtil.getYesterdayOfWeek(i - 6).toString());
                vo.setNumber(0);
                for (OrderDayDTO o : result) {
                    if (o.getOrderDay().equals(yesterday)) {
                        vo.setNumber(o.getCount());
                        break;
                    }
                }
                list.add(vo);
            }
        }
        return list;
    }

}
