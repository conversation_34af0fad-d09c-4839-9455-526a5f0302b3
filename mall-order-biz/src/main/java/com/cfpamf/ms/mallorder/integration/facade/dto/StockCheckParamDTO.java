package com.cfpamf.ms.mallorder.integration.facade.dto;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;

import java.util.List;


/**
 * <AUTHOR>
 * @Create 2023-08-02 13:41
 * @Description :实物库存出库校验参数
 */
@Data
public class StockCheckParamDTO {

    @ApiModelProperty("1-标准电商 2-自研电商 3-乡信助农，必填")
    private Integer channel;

    @ApiModelProperty("店铺ID，必填")
    private Long storeId;

    @ApiModelProperty("业务单号")
    private String businessNo;

    @ApiModelProperty("履约模式，1：自提 2物流")
    private Integer performanceMode;

    @ApiModelProperty("仓库编码")
    private String depotCode;

    @ApiModelProperty("分支编码")
    private String branch;

    @ApiModelProperty("业绩归属类型")
    private Integer performanceType;

    @ApiModelProperty("业绩归属人工号")
    private String belongerEmployeeNo;

    @ApiModelProperty("业绩归属人分支编码")
    private String employeeBranchCode;

    @ApiModelProperty("商品详情")
    private List<ErpStockProductDTO> performanceSkus;

    @ApiModelProperty(value = "(实仓)仓库编码")
    private String actualDepotCode;
    
    @ApiModelProperty(value = "第三方单号")
    private String externalOrderNo;

    @ApiModelProperty("出库单号，物流仓必填，其他非必填")
    private String outboundOrderNo;

    @ApiModelProperty(value = "(实仓)仓库编号")
    private Long actualDepotId;
}
