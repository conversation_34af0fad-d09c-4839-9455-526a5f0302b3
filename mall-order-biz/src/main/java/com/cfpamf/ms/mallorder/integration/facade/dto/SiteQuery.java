package com.cfpamf.ms.mallorder.integration.facade.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <p>
 *     站点列表查询
 * </p>
 *
 * <AUTHOR>
 * @since 2020/8/26
 */
@Data
public class SiteQuery {

    @ApiModelProperty(value = "站点名称")
    private String siteName;

    @ApiModelProperty(value = "站点联系人")
    private String siteContacts;

    @ApiModelProperty(value = "站点类型：1-发货站 2-服务站 3-收货站 4-转运站")
    private Integer siteType;

    @ApiModelProperty(value = "站点编号")
    private String siteCode;

    @ApiModelProperty(value = "站点分类：1-平台站点 2-分支站点")
    private Integer siteCategory = 1;

    @ApiModelProperty(value = "分支编号")
    private String branchCode;

    @ApiModelProperty(value = "分支编号集合")
    private List<String> branchList;

    private Integer number = 1;
    private Integer pageSize = 10;
}
