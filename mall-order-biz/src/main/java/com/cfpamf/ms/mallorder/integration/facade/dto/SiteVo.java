package com.cfpamf.ms.mallorder.integration.facade.dto;

import com.cfpamf.ms.mallorder.integration.wms.contest.SiteTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class SiteVo {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "站点编号")
    private String siteCode;

    @ApiModelProperty(value = "站点名称")
    private String siteName;

    @ApiModelProperty(value = "供应商")
    private String supplier;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "站点类型：1-发货站 2-服务站 3-收货站 4-转运站")
    private SiteTypeEnum siteType;

    @ApiModelProperty(value = "站点联系人")
    private String siteContacts;

    @ApiModelProperty(value = "联系电话")
    private String contactNumber;

    @ApiModelProperty(value = "地址")
    private String address;

    @ApiModelProperty(value = "所属省编码")
    private String province;

    @ApiModelProperty(value = "所属市编码")
    private String city;

    @ApiModelProperty(value = "所属区编码")
    private String county;

    @ApiModelProperty(value = "所属乡镇编码")
    private String town;

    @ApiModelProperty(value = "所属乡镇名称")
    private String townDesc;

    @ApiModelProperty(value = "是否需要查询四级地址标识 0-不需要，1-需要")
    private Integer townFlag;

    @ApiModelProperty(value = "所属省")
    private String provinceDesc;

    @ApiModelProperty(value = "所属市")
    private String cityDesc;

    @ApiModelProperty(value = "所属区")
    private String countyDesc;

    @ApiModelProperty(value = "详细地址")
    private String detailAddress;

    @ApiModelProperty(value = "分支组织树")
    private String branchTree;

    @ApiModelProperty(value = "分支编号")
    private String branchCode;

    @ApiModelProperty(value = "分支名称（所属分支）")
    private String branchName;
}
