package com.cfpamf.ms.mallorder.scheduler;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.mallorder.service.OrderProductErpExtendService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 填充订单物料分类信息job，根据订单编码补充
 */
@Slf4j
@Component
public class FillOrderErpSkuMaterialCodeJob {

    @Autowired
    private OrderProductErpExtendService orderProductErpExtendService;

    @XxlJob("fillOrderErpSkuMaterialCode")
    public ReturnT<String> fillOrderErpSkuMaterialCode() {
        try{
            String param = XxlJobHelper.getJobParam();
            if (StringUtils.isBlank(param)) {
                log.info("fillOrderErpSkuMaterialCode param is empty");
                return ReturnT.SUCCESS;
            }
            log.info("fillOrderErpSkuMaterialCode param:{}", param);
            List<String> orderSnList = JSONObject.parseArray(param, String.class);
            if (CollectionUtils.isEmpty(orderSnList)) {
                log.info("fill orderSnList is empty");
                return ReturnT.SUCCESS;
            }
            orderProductErpExtendService.fillOrderErpSkuMaterialCode(orderSnList);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.warn("fillOrderErpSkuMaterialCode error, errorMsg:{}", e.getMessage(), e);
            return new ReturnT<>(ReturnT.FAIL_CODE, e.getMessage());
        }
    }
}