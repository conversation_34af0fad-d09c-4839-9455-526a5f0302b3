package com.cfpamf.ms.mallorder.checkProof.validator;


import com.cfpamf.ms.mall.filecenter.domain.dto.FileScenesMaterialProofDTO;
import com.cfpamf.ms.mallorder.checkProof.CheckProofValidateResult;
import com.cfpamf.ms.mallorder.checkProof.ICheckProofValidator;
import com.cfpamf.ms.mallorder.common.enums.OrderMaterialTypeEnum;
import com.cfpamf.ms.mallorder.vo.OrderTradeProofVO;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class SameLinkConsistentCheckValidator implements ICheckProofValidator {

    @Override
    public boolean needValidate(OrderTradeProofVO orderTradeProofVO, OrderMaterialTypeEnum materialTypeEnum) {
        return NumberUtils.INTEGER_ONE.equals(orderTradeProofVO.getSameLinkConsistentCheck());
    }

    @Override
    public CheckProofValidateResult validate(FileScenesMaterialProofDTO paramDTO, OrderTradeProofVO orderTradeProofVO, OrderMaterialTypeEnum materialTypeEnum) {


        return null;
    }
}
