package com.cfpamf.ms.mallorder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.ares.aftersale.domain.dto.AfterSaleCreateDTO;
import com.cfpamf.ares.aftersale.domain.dto.AfterSaleRefundNotifyDTO;
import com.cfpamf.ares.trade.domain.dto.OrderStatusSyncDTO;
import com.cfpamf.ares.trade.domain.dto.PayResultNotifyDTO;
import com.cfpamf.ares.trade.domain.dto.TradeDTO;
import com.cfpamf.ms.mallorder.enums.OrderEventEnum;
import com.cfpamf.ms.mallorder.po.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 订单事件消息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
public interface IOrderEventMsgService extends IService<OrderEventMsgPO> {

    List<OrderEventMsgPO> getByMsgIdList(List<String> msgIds);

    OrderEventMsgPO getByOrderSn(String orderSn, String eventType, Integer msgType,String afsSn);

    TradeDTO buildTradeDTO(OrderPO orderPO);

    OrderStatusSyncDTO buildOrderStatusSyncDTO(OrderPO orderPO, OrderEventEnum orderEventEnum);

    PayResultNotifyDTO buildPayResultNotifyDTO(OrderPO orderPO);

    void doExecuteSync(OrderEventEnum orderEventEnum, OrderPO orderPO);

    AfterSaleCreateDTO buildAfterSaleCreateDTO(OrderPO orderPO, OrderReturnPO orderReturnPO, OrderAfterPO orderAfterPO);

    AfterSaleRefundNotifyDTO buildAfterSaleRefundNotifyDTO(OrderPO orderPO, OrderReturnPO orderReturnPO);

    void doExecuteRefundSync(OrderEventEnum orderEventEnum, OrderPO orderPO, OrderReturnPO orderReturnPO, OrderAfterPO orderAfterPO);

    Boolean laterStepExist(String orderSn, String eventType, Integer msgType, Integer executeOrder);

    /**
     * 运费分摊。单品运费 = 总运费 * （单品应付金额 / 订单总金额）
     * @param totalExpressFee
     * @param orderProductList
     * @return
     */
    Map<Long, BigDecimal> buildOrderItemExpressFee(BigDecimal totalExpressFee, List<OrderProductPO> orderProductList);

}
