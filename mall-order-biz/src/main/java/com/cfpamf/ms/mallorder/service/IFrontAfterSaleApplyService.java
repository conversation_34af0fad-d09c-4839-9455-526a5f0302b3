package com.cfpamf.ms.mallorder.service;

import com.cfpamf.ms.mallorder.vo.AfsApplyInfoVO;

import java.math.BigDecimal;

public interface IFrontAfterSaleApplyService {


    /**
     * 退款申请信息
     * @param memberId
     * @param orderSn
     * @param orderProductId
     * @param isValet 是否代客售后标识
     * @return
     */
    AfsApplyInfoVO getAfsApplyInfoVO(Integer memberId, String orderSn, Long orderProductId, boolean isValet);

    /**
     * 根据订单号，获取预付订金类型的退款金额
     * @param orderSn
     * @param amount 退款金额
     * @return
     */
    BigDecimal getDepositProductRefundMoney(String orderSn,BigDecimal amount);
}
