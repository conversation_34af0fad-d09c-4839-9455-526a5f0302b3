package com.cfpamf.ms.mallorder.controller.seller;

import com.cfpamf.ms.mallorder.common.util.ValidUtils;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.ReceiveMaterialResultDTO;
import com.cfpamf.ms.mallorder.dto.TradeProofUploadDTO;
import com.cfpamf.ms.mallorder.integration.filecenter.ProofSceneEnum;
import com.cfpamf.ms.mallorder.service.IOrderTradeProofService;
import com.cfpamf.ms.mallorder.vo.ContractSearchResultVO;
import com.cfpamf.ms.mallorder.vo.OrderTradeProofVO;
import com.cfpamf.ms.mallshop.resp.Vendor;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import com.slodon.bbc.core.util.UserUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;

/**
 * 商家端-交易凭证controller
 *
 * <AUTHOR>
 * @since 2024/10/28
 */
@Api(tags = "seller-交易凭证管理")
@Slf4j
@RestController
@RequestMapping("seller/tradeProof")
public class SellerTradeProofController {
    @Autowired
    private IOrderTradeProofService orderTradeProofService;

    @GetMapping("/getProofList")
    @ApiOperation("根据订单编号、场景编码、资料编码查询交易凭证")
    public JsonResult<List<OrderTradeProofVO>> getOrderTradeProofByOrderSnAndScene(@RequestParam("orderSn")String orderSn, @RequestParam("sceneNo")String sceneNo, @RequestParam("materialNo")String materialNo){
        return SldResponse.success(orderTradeProofService.listSceneMaterial(orderSn, null, materialNo, ProofSceneEnum.getValue(sceneNo)));
    }

    @PostMapping("uploadProof")
    @ApiOperation("上传凭证")
    public JsonResult<Boolean> uploadProof(HttpServletRequest request, @RequestBody TradeProofUploadDTO proofUploadDTO){
        Vendor vendor = UserUtil.getUser(request,Vendor.class);
        Boolean result = orderTradeProofService.uploadProofMall(OrderConst.LOG_ROLE_VENDOR, String.valueOf(vendor.getVendorId()),vendor.getVendorName(),"电商系统",proofUploadDTO);
        return SldResponse.success(result);
    }

    @ApiOperation("校验文件风险等级")
    @PostMapping("/checkReceiveMaterial")
    public JsonResult<ReceiveMaterialResultDTO> checkReceiveMaterial(@RequestParam("orderSn")String orderSn, @RequestParam("fileUrl")String fileUrl) {

        return SldResponse.success(orderTradeProofService.checkReceiveMaterial(orderSn,fileUrl));

    }

    /**
     * 合同url查询
     *
     * @param request 请求实体
     * @param proofId 交易凭证id
     * @param needLoanInfo 是否需要返回信贷信息，1-需要，0-不需要
     * @return 合同信息结果
     */
    @ApiOperation("合同查询")
    @PostMapping(value = "/contractSearch")
    public JsonResult<ContractSearchResultVO> contractSearch(HttpServletRequest request, @RequestParam("proofId")Long proofId, @RequestParam(value = "needLoanInfo",defaultValue = "1")Integer needLoanInfo) {
        Vendor vendor = UserUtil.getUser(request,Vendor.class);
        if (Objects.isNull(vendor) || ValidUtils.isEmpty(vendor.getVendorId())){
            throw new BusinessException("请先登录");
        }
        ContractSearchResultVO result = orderTradeProofService.contractSearch(proofId,needLoanInfo);
        return SldResponse.success(result);
    }
}
