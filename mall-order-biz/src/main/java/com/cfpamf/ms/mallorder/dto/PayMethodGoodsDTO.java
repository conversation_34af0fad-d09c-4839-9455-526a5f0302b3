package com.cfpamf.ms.mallorder.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * <AUTHOR>
 * @Create 2021-08-05 13:41
 * @Description :支付方式商品添加
 */
@Data
public class PayMethodGoodsDTO {

    @ApiModelProperty(value = "支付方式主键")
    @NotNull(message = "支付方式主键不能为空")
    private Long payMethodId;

    @ApiModelProperty(value = "商品集合")
    private List<PayMethodGoodsListDTO> goodsListDTOS;

    @ApiModelProperty(value = "处理人")
    @NotEmpty(message = "处理人不能为空,取当前登录用户名")
    private String operateUserName;
}
