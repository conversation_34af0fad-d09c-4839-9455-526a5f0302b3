package com.cfpamf.ms.mallorder.controller.fegin.facade;

import java.util.List;
import javax.annotation.Resource;

import com.cfpamf.ms.mallorder.model.OrderLogModel;
import com.cfpamf.ms.mallorder.po.OrderLogPO;
import com.cfpamf.ms.mallorder.request.OrderLogExample;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@RestController
@Slf4j
public class OrderLogFeign {

    @Resource
    private OrderLogModel orderLogModel;

    /**
     * 根据logId获取订单操作日志表详情
     *
     * @param logId logId
     * @return
     */
    @GetMapping("/v1/feign/business/orderLog/get")
    public OrderLogPO getOrderLogByLogId(@RequestParam("logId") Integer logId) {
        return orderLogModel.getOrderLogByLogId(logId);
    }

    /**
     * 获取条件获取订单操作日志表列表
     *
     * @param example 查询条件信息
     * @return
     */
    @PostMapping("/v1/feign/business/orderLog/getList")
    public List<OrderLogPO> getOrderLogList(@RequestBody OrderLogExample example) {
        return orderLogModel.getOrderLogList(example, example.getPager());
    }

    /**
     * 新增订单操作日志表
     *
     * @param orderLogPO
     * @return
     */
    @PostMapping("/v1/feign/business/orderLog/addOrderLog")
    public Integer saveOrderLog(@RequestBody OrderLogPO orderLogPO) {
        return orderLogModel.saveOrderLog(orderLogPO);
    }

    /**
     * 根据logId更新订单操作日志表
     *
     * @param orderLogPO
     * @return
     */
    @PostMapping("/v1/feign/business/orderLog/updateOrderLog")
    public Integer updateOrderLog(@RequestBody OrderLogPO orderLogPO) {
        return orderLogModel.updateOrderLog(orderLogPO);
    }

    /**
     * 根据logId删除订单操作日志表
     *
     * @param logId logId
     * @return
     */
    @PostMapping("/v1/feign/business/orderLog/deleteOrderLog")
    public Integer deleteOrderLog(@RequestParam("logId") Integer logId) {
        return orderLogModel.deleteOrderLog(logId);
    }

    @GetMapping("/v1/feign/business/orderLog/testRollback/global")
    public void testRollback(@RequestParam("goodsId") Long goodsId,@RequestParam("goodsName") String goodsName,
                             @RequestParam("logId") Integer logId, @RequestParam("logContent") String logContent) {
        orderLogModel.testRollback(goodsId,goodsName,logId,logContent);
    }

    @GetMapping("/v1/feign/business/orderLog/testRollback/local")
    public void testRollbackLocal(@RequestParam("goodsId") Long goodsId,@RequestParam("goodsName") String goodsName,
                                  @RequestParam("logId") Integer logId, @RequestParam("logContent") String logContent) {
        orderLogModel.testRollbackLocal(goodsId,goodsName,logId,logContent);
    }
}