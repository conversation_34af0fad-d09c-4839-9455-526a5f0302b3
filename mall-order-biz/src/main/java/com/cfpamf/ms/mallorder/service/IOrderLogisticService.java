package com.cfpamf.ms.mallorder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.ms.mallorder.dto.ExpressDeliveryDTO;
import com.cfpamf.ms.mallorder.dto.ExpressNumberDTO;
import com.cfpamf.ms.mallorder.dto.OrderPerformanceDeliveryDTO;
import com.cfpamf.ms.mallorder.po.OrderLogisticPO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.req.OrderDeliveryReq;
import com.cfpamf.ms.mallorder.vo.OrderLogisticItemVO;
import com.cfpamf.ms.mallorder.vo.OrderLogisticVO;
import com.cfpamf.ms.mallshop.resp.Vendor;

import java.util.List;

/**
 * <p>
 * 订单发货记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-18
 */
public interface IOrderLogisticService extends IService<OrderLogisticPO> {

	OrderLogisticPO saveLogistic(OrderPO orderPO, OrderDeliveryReq deliveryReq, String operator,Integer productDeliveryState);

	/**
	 * 根据快递单号查询快递公司信息
	 *
	 * @param expressNumber   快递单号
	 * @param orderSn
	 * @param vendor
	 * @return  Express    物流（快递）公司信息
	 */
	ExpressDeliveryDTO getExpressByExpressNumber(String expressNumber,String orderSn, Vendor vendor);


	/**
	 * 批量根据快递单号查询快递公司信息
	 *
	 * @param expressNumberList   快递单号列表
	 * @return  Express    物流（快递）公司信息
	 */
	List<ExpressDeliveryDTO> getExpressListByExpressNumber(List<ExpressNumberDTO> expressNumberList,Vendor vendor);


	/**
	 * 根据单号查询物流公司
	 * @param expressNumber 快递单号
	 * @return  ExpressDeliveryDTO
	 * */
	ExpressDeliveryDTO getExpressDelivery(String expressNumber);


	/**
	 * 保存供应商发货信息
	 * @param orderPerformanceDeliveryDTO
	 * @return boolean
	 * */
	OrderLogisticPO savePerformanceLogistic(OrderPerformanceDeliveryDTO orderPerformanceDeliveryDTO);

	OrderLogisticPO getOrderLogisticByPackageSn(String packageSn);

	List<OrderLogisticPO> getOrderLogisticByOrderSn(String orderSn,Integer deliverPackageState);


	void updatePackageState(String packageSn,int state,String failReason);


	/**
	 * 根据主键Id获取物流信息（facade）
	 * @param logisticId
	 * @return OrderLogisticVO
	 * */
	OrderLogisticVO getOrderLogisticByLogisticId(Long logisticId);

	List<OrderLogisticVO> getOrderLogisticListByOrderProductId(Long orderProductId);

	OrderLogisticVO getOrderLogisticByOrderProductId(Long orderProductId);

	List<OrderLogisticItemVO> getPackageItemList(String orderSn);
}
