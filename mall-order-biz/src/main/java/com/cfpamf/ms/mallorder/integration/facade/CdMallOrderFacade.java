package com.cfpamf.ms.mallorder.integration.facade;

import com.cdfinance.ms.facade.model.response.signBefore.ContractFindResponse;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.loan.facade.request.external.mall.*;
import com.cfpamf.ms.loan.facade.vo.SetlTryResultVo;
import com.cfpamf.ms.loan.facade.vo.external.mall.*;
import com.cfpamf.ms.mallorder.integration.facade.dto.RepaymentScheduleRequest;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(value = "ms-service-loan",url = "${ms-service-loan.url}")
public interface CdMallOrderFacade {

    /**
     * 保存电商订单
     * @param request
     * @return
     */
    @PostMapping(value = "/loan/externalBusiness/saveCdmallApply")
    Result<Void> saveCdmallApply(@RequestBody SaveCdMallApplyRequest request);

    /**
     * 电商订单退款
     * @param request
     * @return
     */
    @PostMapping(value = "/loan/externalBusiness/orderDetailRefund")
    Result<Void> orderDetailRefund(@RequestBody OrderRefundRequest request);

    /**
     * 电商订单放款
     * @param request
     * @return
     */
    @PostMapping(value = "/loan/externalBusiness/lendingCdMallApply")
    Result<Void> lendingCdMallApply(@RequestBody LendingCdMallApplyRequest request);

    /**
     * 电商订单取消
     * @param request
     * @return
     */
    @PostMapping(value = "/loan/externalBusiness/cancelCdmallApply")
    Result<Void> cancelCdmallApply(@RequestBody CancelCdMallApplyRequest request);

    /**
     * 电商订单重新代付
     * @param request
     * @return
     */
    @PostMapping("/loan/externalBusiness/redrawCdMallApply")
    Result<Void> redrawCdMallApply(@RequestBody RedrawCdMallApplyRequest request);

    /**
     * 决策结果返回
     * @param request
     * @return
     */
    @PostMapping(value = "/loan/externalBusiness/cdmallOrderDecisionResultCallback")
    Result<Void> cdmallOrderDecisionResultCallback(@RequestBody OrderDecisionResultCallbackRequest request);

    /**
     * 退货接口
     * @param request
     * @return
     */
    @PostMapping(value = "/loan/externalBusiness/cdmallOrderReturn")
    Result<String> cdmallOrderReturn(@RequestBody ReturnCdMallOrderRequest request);

    /**
     * 退货接口V2
     * @param request
     * @return
     */
    @PostMapping(value = "/loan/externalBusiness/cdmallOrderReturnV2")
    Result<OrderReturnResultVo> cdmallOrderReturnV2(@RequestBody ReturnCdMallOrderRequest request);

    /**
     * 试算接口
     * @param request
     * @return
     */
    @PostMapping(value = "/loan/externalBusiness/cdmallOrderSetlTry")
    Result<SetlTryResultVo> cdmallOrderSetlTry(@RequestBody CdmallSetlTryRequest request);

    /**
     * 生成合同接口，一次调一个效果最好
     * @param request
     * @return
     */
    @PostMapping(value = "/loan/externalBusiness/autoSignCdmallOrderContract")
    Result<String> autoSignCdmallOrderContract(@RequestBody SignCdmallOrderContractRequest request) throws Exception;

    /**
     * 电商用呗前置条件判断
     * @param request
     * @return
     */
    @PostMapping(value = "/loan/externalBusiness/cdmallPreconditionCheck")
    Result<CdmallPreconditionCheckVo> cdmallPreconditionCheck(@RequestBody CdmallPreconditionCheckRequest request) throws Exception;

    /**
     * 获取用呗合同
     * @param request
     * @return
     */
    @PostMapping("/loan/externalBusiness/getMallPreviewContractCodes")
    Result<MallContractCodesVo> getMallPreviewContractCodes(@RequestBody GetMallContractCodesRequest request);

    /**
     * 预览合同
     * @param contractPreviewRequest
     */
    @GetMapping(value = "/loan/externalBusiness/contractPreview/V3")
    Result<MallContractContentVO> contractPreviewV3(@RequestBody ContractPreviewRequest contractPreviewRequest);

    /**
     * 可用额度列表
     */
    @PostMapping({"/loan/externalBusiness/getAvailablePayMode"})
    Result<PayModeListVO> getAvailablePayMode(@RequestBody OverallPreWithdrawRequest var1);

    @PostMapping("/loan/externalBusiness/updateCdmallOrderPlanLoanDate")
    Result<List<CdmallOrderUpdatePlanLoanVo>> updateCdmallOrderPlanLoanDate(@RequestBody CdmallOrderUpdatePlanLoanRequest request) throws Exception;

    @PostMapping(value = "/loan/externalBusiness/confirmReceipt/preview")
    @ApiOperation(value = "预览还款计划(交易成功起息订单查询)")
    Result<List<ContractFindResponse>> repaymentSchedule(@RequestBody RepaymentScheduleRequest request);

    /**
     * 确认收货 - 合同签订
     * @param request
     * @return
     * @throws Exception
     */
    @PostMapping("/loan/externalBusiness/confirmReceipt/sign")
    Result<Void> signContract(@RequestBody RepaymentScheduleRequest request);

}
