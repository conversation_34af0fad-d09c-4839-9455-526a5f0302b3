package com.cfpamf.ms.mallorder.vo.bean;

import com.github.pagehelper.PageInfo;
import lombok.Data;

import java.util.List;

/**
 * @description: 高级分页
 * @author: zhang<PERSON><PERSON>
 * @create: 2018-07-06 09:19
 **/
@Data
public class OuterPageInfo<T, S> extends PageInfo<T> {

    /**
     * 外层信息1
     */
    private S outer;

    /**
     * 默认构造函数
     */
    public OuterPageInfo() {
        super();
    }

    /**
     * list构造函数
     */
    public OuterPageInfo(List<T> list) {
        super(list);
    }

    /**
     * list构造函数
     */
    public OuterPageInfo(List<T> list, S outer) {
        super(list);
        this.outer = outer;
    }
}
