package com.cfpamf.ms.mallorder.controller.fegin.facade;

import com.cfpamf.ms.mallorder.model.ComplainSubjectModel;

import java.util.List;
import javax.annotation.Resource;

import com.cfpamf.ms.mallorder.po.ComplainSubject;
import com.cfpamf.ms.mallorder.request.ComplainSubjectExample;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@RestController
@Slf4j
public class ComplainSubjectFeign {


    @Resource
    private ComplainSubjectModel complainSubjectModel;

    /**
     * 根据complainSubjectId获取投诉主题表详情
     *
     * @param complainSubjectId complainSubjectId
     * @return
     */
    @GetMapping("/v1/feign/business/complainSubject/get")
    public ComplainSubject getComplainSubjectByComplainSubjectId(@RequestParam("complainSubjectId") Integer complainSubjectId) {
        return complainSubjectModel.getComplainSubjectByComplainSubjectId(complainSubjectId);
    }

    /**
     * 获取条件获取投诉主题表列表
     *
     * @param example 查询条件信息
     * @return
     */
    @PostMapping("/v1/feign/business/complainSubject/getList")
    public List<ComplainSubject> getComplainSubjectList(@RequestBody ComplainSubjectExample example) {
        return complainSubjectModel.getComplainSubjectList(example, example.getPager());
    }

    /**
     * 新增投诉主题表
     *
     * @param complainSubject
     * @return
     */
    @PostMapping("/v1/feign/business/complainSubject/addComplainSubject")
    public Integer saveComplainSubject(@RequestBody ComplainSubject complainSubject) {
        return complainSubjectModel.saveComplainSubject(complainSubject);
    }

    /**
     * 根据complainSubjectId更新投诉主题表
     *
     * @param complainSubject
     * @return
     */
    @PostMapping("/v1/feign/business/complainSubject/updateComplainSubject")
    public Integer updateComplainSubject(@RequestBody ComplainSubject complainSubject) {
        return complainSubjectModel.updateComplainSubject(complainSubject);
    }

    /**
     * 根据complainSubjectId删除投诉主题表
     *
     * @param complainSubjectId complainSubjectId
     * @return
     */
    @PostMapping("/v1/feign/business/complainSubject/deleteComplainSubject")
    public Integer deleteComplainSubject(@RequestParam("complainSubjectId") Integer complainSubjectId) {
        return complainSubjectModel.deleteComplainSubject(complainSubjectId);
    }
}