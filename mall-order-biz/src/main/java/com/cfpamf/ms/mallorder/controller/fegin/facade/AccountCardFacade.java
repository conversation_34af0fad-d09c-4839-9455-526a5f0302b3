//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//

package com.cfpamf.ms.mallorder.controller.fegin.facade;

import com.cfpamf.ms.mall.account.enums.AccountCardTypeEnum;
import com.cfpamf.ms.mall.account.vo.AccountCard;
import com.slodon.bbc.core.response.JsonResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(
    name = "mall-account",
    contextId = "account",
    url = "${mall-account.url:}"
)
public interface AccountCardFacade {
    @GetMapping({"/v1/feign/business/account/card/defaultCard"})
    JsonResult<AccountCard> defaultCard(@RequestParam("storeId") Long var1);

    @ApiOperation("根据店铺ID+cardType查询银行卡信息")
    @GetMapping({"/v1/feign/business/account/card/detail"})
    JsonResult<AccountCard> detail(@RequestParam("storeId") String var1, @RequestParam("cardType") AccountCardTypeEnum var2);
}
