package com.cfpamf.ms.mallorder.integration.logistic;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.mallLogistic.dto.ExpressDTO;
import com.cfpamf.ms.mallLogistic.facade.LogisticFeignClient;
import com.cfpamf.ms.mallLogistic.req.ExpressTrackQuery;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.slodon.bbc.core.constant.ResponseConst;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.response.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class LogisticIntegration {

    @Autowired
    private LogisticFeignClient logisticFeignClient;

    public ExpressDTO getTracks(ExpressTrackQuery query) {
        JsonResult<ExpressDTO> result = null;
        try {
            result = logisticFeignClient.getTracks(query);
        } catch (Exception e) {
            log.error("获取物流中心轨迹信息异常，error:{0}", e);
            if (!CommonConst.TIME_OUT.equals(e.getMessage())) {
                throw e;
            }
        }
        if (result == null) {
            log.warn("获取物流中心轨迹信息返回为null,请求参数:{}", JSONObject.toJSONString(query));
            throw new BusinessException("获取物流中心轨迹信息返回为null");
        }
        if (result.getState().equals(ErrorCodeEnum.U.ILLEGAL_PARAM.getCode())){
            // 单号或物流公司输入有误，返回空
            return null;
        }
        if (result.getState() != ResponseConst.STATE_SUCCESS) {
            log.warn("获取物流中心轨迹信息失败,请求参数:{}，响应参数:{}", JSONObject.toJSONString(query), JSONObject.toJSON(result));
            throw new BusinessException(result.getMsg());
        }
        return result.getData();
    }
}
