package com.cfpamf.ms.mallorder.controller.fegin.facade.workflow.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

@Getter
@Setter
public class ProcessSuspendRequestDto extends ProcessModifyRequestDTO {
    @NotBlank(message = "流程实例ID不能为空")
    @ApiModelProperty(value = "流程实例ID", required = true)
    private String procInstId;

    @ApiModelProperty(value = "业务ID")
    private String bizId;

    @ApiModelProperty(value = "评论")
    private String comment = "拒绝";

}
