package com.cfpamf.ms.mallorder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.ms.mallorder.common.enums.TaskQueueBizTypeEnum;
import com.cfpamf.ms.mallorder.po.TaskQueueLogPO;
import com.cfpamf.ms.mallorder.po.TaskQueuePO;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 定时任务推送队列表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-01-08
 */
public interface ITaskQueueService extends IService<TaskQueuePO> {

    /**
     * 查询待处理任务
     *
     * @param bizType     业务类型
     * @param executeTime 执行时间
     * @return
     */
    List<TaskQueuePO> listTodoTask(TaskQueueBizTypeEnum bizType, String executeTime);

    /**
     * 查询待处理任务，指定执行条数上限
     * @param bizType
     * @param executeTime
     * @param limit
     * @return
     */
    List<TaskQueuePO> listTodoTaskLimit(TaskQueueBizTypeEnum bizType, String executeTime, String limit);

    /**
     * 批量查询待处理任务，指定执行条数上限
     * @param bizType
     * @param executeTime
     * @param limit
     * @return
     */
    List<TaskQueuePO> listTodoTaskLimit(List<Integer> bizType, String executeTime, String limit);

    /**
         * 任务成功处理：记录log 移除task
         *
         * @param task
         */
    void taskSuccess(TaskQueuePO task);

    /**
     * 任务失败处理：记录log 更新task
     *
     * @param task
     */
    void taskFail(TaskQueuePO task, String requestContent, String responseContent);

    void dealTask(TaskQueuePO task, TaskQueueLogPO taskLog);

    TaskQueuePO saveTaskQueue(Long bizId, TaskQueueBizTypeEnum bizType, Date executeTime, String operator);

    void removeTaskQueue(TaskQueuePO taskQueuePo);

    Date getErrorTaskNextExecuteTime();

    int modifyTaskQueue(Long bizId, Integer bizType, Long excludeId);

    TaskQueuePO getTaskQueueByBizIdAndBizType(String bizId, Integer bizType);
}
