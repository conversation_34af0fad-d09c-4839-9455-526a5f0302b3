package com.cfpamf.ms.mallorder.controller.fegin.facade.workflow.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <p>
 * 流程任务记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-02 18:33:44
 */
@Getter
@Setter
@ApiModel(value = "WorkflowTaskDetailPO对象", description = "流程任务记录表")
@Data
//@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class WorkflowTaskDetailVO {

    private Integer id;


    @ApiModelProperty("任务ID")
    private String taskId;

    @ApiModelProperty("业务ID")
    private String bizId;

    @ApiModelProperty("流程定义key")
    private String procDefKey;

    @ApiModelProperty("流程实例ID")
    private String procInstId;

    @ApiModelProperty("全局唯一key")
    private String businessKey;

    @ApiModelProperty("申请人BMS用户ID")
    private String applyUserId;

    @ApiModelProperty("申请时间")
    private Date applyTime;

    @ApiModelProperty("申请人姓名")
    private String applicantName;

    @ApiModelProperty("处理人BMS用户ID")
    private String assigneeUserId;

    @ApiModelProperty("处理时间")
    private Date assigneeTime;

    @ApiModelProperty("处理人姓名")
    private String assigneeName;

    @ApiModelProperty("节点key")
    private String nodeKey;

    @ApiModelProperty("节点名称")
    private String nodeName;

    @ApiModelProperty("审批结果：同意，拒绝，驳回")
    private String auditResult;

    @ApiModelProperty("审批评论")
    private String remark;

    @ApiModelProperty("是否正在执行节点，0:否；1:是")
    private Boolean currentFlag;

    private Integer executeOrder;

    @ApiModelProperty("当前节点的跳转webUrl（只有待办支持返回）")
    private String webUrl;

    @ApiModelProperty("url参数信息")
    private String urlParam;

    @ApiModelProperty("流程名称")
    private String procDefName;

}
