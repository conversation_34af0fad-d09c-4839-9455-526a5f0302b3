package com.cfpamf.ms.mallorder.v2.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.v2.common.enums.OrderSnType;
import com.cfpamf.ms.mallorder.v2.domain.bo.OrderCancelDataBO;
import com.cfpamf.ms.mallorder.v2.domain.vo.OperationUserVO;
import com.cfpamf.ms.mallorder.v2.domain.vo.OrderCancelVO;
import com.cfpamf.ms.mallorder.v2.service.OrderCancelSupportService;
import com.cfpamf.ms.mallorder.v2.service.OrderDetailService;
import com.slodon.bbc.core.constant.PromotionConst;

/**
 * 类NewPeopleOrderCancelSupportService.java的实现描述：系统操作订单取消，不含拼团订单
 *
 * <AUTHOR> 14:58
 */
@Service("newPeopleOrderCancelSupportService")
public class NewPeopleOrderCancelSupportServiceImpl implements OrderCancelSupportService {

    @Autowired
    private OrderDetailService orderDetailService;

    @Override
    public boolean orderCancelFlowOrderState(OrderCancelVO vo, OperationUserVO oprationUser) {
        BizAssertUtil.isTrue(!OrderSnType.isParentOrder(vo.getOrderSnType()), "抱歉，取消订单退款申请，传入订单号类型必须是父订单，请重新操作！");
        OrderPO orderPO = new OrderPO();
        orderPO.setOrderState(OrderConst.ORDER_STATE_0);
        orderPO.setRefuseReason(vo.getReasonDesc());
        orderPO.setRefuseRemark(oprationUser.getOperationRemark());
        UpdateWrapper<OrderPO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("parent_sn", vo.getOrderSn());
        updateWrapper.eq("order_state", OrderConst.ORDER_STATE_10);
        updateWrapper.eq("order_type", PromotionConst.PROMOTION_TYPE_106);//新人活动
        // 批量更新订单状态记录
        return orderDetailService.update(orderPO, updateWrapper);
    }

    @Override
    public OrderCancelDataBO getOrderCancelData(OrderCancelVO vo, OperationUserVO oprationUser) {
        QueryWrapper<OrderPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("parent_sn", vo.getOrderSn());
        queryWrapper.eq("order_state", OrderConst.ORDER_STATE_0);
        queryWrapper.eq("order_type", PromotionConst.PROMOTION_TYPE_106);//新人活动
        queryWrapper.eq("enabled_flag", OrderConst.ENABLED_FLAG_Y);
        List<OrderPO> orderPOList = orderDetailService.list(queryWrapper);
        return new OrderCancelDataBO(orderPOList, OrderConst.ORDER_STATE_10, OrderConst.ORDER_STATE_0);
    }

}
