package com.cfpamf.ms.mallorder.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.event.SyncReadListener;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cfpamf.framework.autoconfigure.redis.lock.SlodonLock;
import com.cfpamf.mallpayment.facade.constant.Status;
import com.cfpamf.ms.customer.facade.vo.CustBaseInfoVo;
import com.cfpamf.ms.mall.filecenter.domain.dto.FileDTO;
import com.cfpamf.ms.mallgoods.facade.api.GoodsFeignClient;
import com.cfpamf.ms.mallgoods.facade.api.ProductFeignClient;
import com.cfpamf.ms.mallgoods.facade.api.ProductFinanceGoodsLabelFeignClient;
import com.cfpamf.ms.mallgoods.facade.dto.AgricFeeCalculateDTO;
import com.cfpamf.ms.mallgoods.facade.dto.ProductPriceDTO;
import com.cfpamf.ms.mallgoods.facade.vo.*;
import com.cfpamf.ms.mallmember.api.MemberFeignClient;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallmember.request.MemberExample;
import com.cfpamf.ms.mallorder.builder.OrderBuilder;
import com.cfpamf.ms.mallorder.builder.OrderOfflineBuilder;
import com.cfpamf.ms.mallorder.common.config.OrderOfflineAgriFeeCalculConfig;
import com.cfpamf.ms.mallorder.common.constant.TaskConstant;
import com.cfpamf.ms.mallorder.common.enums.CustomerTypeEnum;
import com.cfpamf.ms.mallorder.common.enums.OrderPatternEnum;
import com.cfpamf.ms.mallorder.common.enums.OrderStatusEnum;
import com.cfpamf.ms.mallorder.common.enums.TaskQueueBizTypeEnum;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.cfpamf.ms.mallorder.common.handler.OrderSubmitContextHolder;
import com.cfpamf.ms.mallorder.common.strategy.expresscalculate.ExpressCalculateStrategy;
import com.cfpamf.ms.mallorder.common.template.ordersubmitdto.OrderSubmitDTOContext;
import com.cfpamf.ms.mallorder.common.util.*;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.*;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.enums.SettleModeEnum;
import com.cfpamf.ms.mallorder.integration.cust.CustomerIntegration;
import com.cfpamf.ms.mallorder.integration.facade.BmsIntegration;
import com.cfpamf.ms.mallorder.integration.facade.BmsUserFacade;
import com.cfpamf.ms.mallorder.integration.goods.GoodsFeignIntegration;
import com.cfpamf.ms.mallorder.integration.pay.PayIntegration;
import com.cfpamf.ms.mallorder.mapper.OrderMapper;
import com.cfpamf.ms.mallorder.model.CartModel;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.po.CartPO;
import com.cfpamf.ms.mallorder.po.OrderOfflinePO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.req.OrderOfflineExportRequest;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.validation.OrderOfflineValidation;
import com.cfpamf.ms.mallorder.validation.OrderValidation;
import com.cfpamf.ms.mallorder.vo.*;
import com.cfpamf.ms.mallpromotion.api.PromotionCommonFeignClient;
import com.cfpamf.ms.mallpromotion.constant.PromotionConst;
import com.cfpamf.ms.mallshop.api.PurchaserFeign;
import com.cfpamf.ms.mallshop.api.StoreFeignClient;
import com.cfpamf.ms.mallshop.api.StoreIsolateWhiteListFeignClient;
import com.cfpamf.ms.mallshop.api.StoreRegionFeignClient;
import com.cfpamf.ms.mallshop.enums.WhiteListEnum;
import com.cfpamf.ms.mallshop.resp.Vendor;
import com.cfpamf.ms.mallshop.vo.FrontStoreRegionRequestVO;
import com.cfpamf.ms.mallshop.vo.FrontStoreRegionVo;
import com.cfpamf.ms.mallshop.vo.PurchaserFeignResultVO;
import com.cfpamf.ms.mallshop.vo.StoreRegionVO;
import com.cfpamf.smartid.client.utils.sharding.SeqEnum;
import com.cfpamf.smartid.client.utils.sharding.ShardingId;
import com.google.common.collect.Lists;
import com.slodon.bbc.core.constant.GoodsConst;
import com.slodon.bbc.core.constant.RedisConst;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.exception.MallException;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.util.StringUtil;
import com.slodon.bbc.core.util.UserUtil;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 订单正向操作 service
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/5/30 17:02
 */
@Slf4j
@Service
public class PlacingOrderServiceImpl extends ServiceImpl<OrderMapper, OrderPO> implements IOrderPlacingService {

    @Autowired
    private HttpServletRequest request;
    @Autowired
    private OrderSubmitUtil orderSubmitUtil;
    @Autowired
    private MemberFeignClient memberFeignClient;
    @Resource
    private GoodsFeignClient goodsFeignClient;
    @Resource
    private StoreFeignClient storeFeignClient;
    @Autowired
    private OrderModel orderModel;
    @Autowired
    private SlodonLock slodonLock;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private PromotionCommonFeignClient promotionCommonFeignClient;
    @Autowired
    private ITaskQueueService taskQueueService;
    @Autowired
    private PayIntegration payIntegration;
    @Resource
    private OrderMapper orderMapper;
    @Autowired
    private ICartService cartService;
    @Autowired
    private ShardingId shardingId;
    @Autowired
    private IOrderService iOrderService;
    @Resource
    private IPayMethodService payMethodService;
    @Autowired
    private OrderLocalUtils orderLocalUtils;
    @Autowired
    private PromotionUtils promotionUtils;
    @Resource
    private CartModel cartModel;
    @Resource
    private ValidUtils validUtils;
    @Resource
    private OrderCreateHelper orderCreateHelper;

    @Autowired
    private BmsIntegration bmsIntegration;
    @Autowired
    private CustomerIntegration customerIntegration;
    @Autowired
    private IOrderGroupBuyingRecordService orderGroupBuyingRecordService;
    @Autowired
    private UserInfoComponent userInfoComponent;
    @Autowired
    private OrderOfflineService orderOfflineService;
    @Resource
    private IOrderOfflineExtendService orderOfflineExtendService;
    @Autowired
    private StoreRegionFeignClient storeRegionFeignClient;

    @Value("${fastOrder-expired}")
    private long expiredDay;

    @Value("${defaultToBOfflineOrderMemberMobile:19999999999}")
    private String defaultToBOfflineOrderMemberMobile;

    @Resource
    private BmsUserFacade bmsUserFacade;

    @Resource
    private StoreIsolateWhiteListFeignClient storeIsolateWhiteListFeignClient;

    @Autowired
    private GoodsFeignIntegration goodsFeignIntegration;

    @Autowired
    private OrderOfflineAgriFeeCalculConfig orderOfflineAgriFeeCalculConfig;

    @Autowired
    private OrderSubmitAttributesUtils orderSubmitAttributesUtils;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private PrivilegeService privilegeService;
    @Autowired
    private ValidatorUtils validatorUtils;

    @Autowired
    private OrderOfflineImportFailExportService orderOfflineImportFailExportService;

    @Autowired
    private ProductFinanceGoodsLabelFeignClient productFinanceGoodsLabelFeignClient;

    @Autowired
    private ProductFeignClient productFeignClient;

    @Autowired
    private PurchaserFeign purchaserFeign;



    @Override
    public ChannelOrderSubmitVO submitChannelOrder(ChannelOrderSubmitDTO dto) {

        PreOrderDTO preOrderDTO = new PreOrderDTO();

        /**
         * 封装购买商品信息
         */
//        List<CartPO> cartPOList = cartService.buildCartList(dto.getSkuInfoList(), dto.getOrderType());
//
//        Map<String, List<CartPO>> orderMap = new HashMap<>();
//
//        for (CartPO cartPO : cartPOList) {
//            String orderShardingKey = String.valueOf(cartPO.getStoreId());
//            List<CartPO> currentCart = orderMap.get(orderShardingKey);
//            if (currentCart == null){
//                List<CartPO> items = new ArrayList<CartPO>();
//                items.add(cartPO);
//                orderMap.put(orderShardingKey,  items);
//            } else {
//
//            }
//        }


        long pno = shardingId.next(SeqEnum.PNO, null);

//
//        // 创建订单
//        this.createOrder(consumerDTO);


        /**
         * step.1 业务参数校验
         */

        /**
         * step.2 查询商品信息
         */

        /**
         * step.3 查询活动信息
         */

        /**
         * step.4 封装订单信息
         */

        /**
         * step.5 扣除库存、卡券
         */

        /**
         * step.6 写入订单信息
         */


        return null;
    }


    /**
     * 创建订单接口
     *
     * @param req
     * @return long
     * @return 订单编号，不包含父单号
     * <AUTHOR>
     * @date 2021/5/30 17:01
     */
    @Override
    public List<String> createOrder(OrderSubmitMqConsumerDTO req) {

//        Set<String> lock = new TreeSet<>();
        OrderSubmitParamDTO paramDTO = req.getParamDTO();
        List<String> orderSnList = Lists.newArrayList();
        try {
            //构造计算优惠dto
            OrderSubmitDTO orderSubmitDTO = null;
            if (paramDTO.isChannelOrder()
                    || OrderTypeEnum.isOfflineAll(req.getOrderType())
                    || req.isSimulationShoppingCart()) {
                List<CartPO> cartItems = Lists.newArrayList();
                if (Objects.nonNull(req.getOrderPlaceUserDTO())) {
                    // 代客下单支持多店铺
                    for (OrderParamDTO.StoreInfo storeInfo : req.getOrderParamDTO().getStoreInfoList()) {
                        cartItems.addAll(cartService.buildCartList(storeInfo.getSkuInfoList(), req.getOrderType(),
                                req.getMemberId(), storeInfo.getAreaCode(), storeInfo.getFinanceRuleCode(), paramDTO.getChannel(), paramDTO.getOrderAddress()));
                    }
                    // 代客下单支持预付定金
                    orderSubmitDTO = orderSubmitUtil.getOrderSubmitDTOV3(cartItems, paramDTO, Boolean.TRUE.equals(req.isSimulationShoppingCart()), Objects.isNull(paramDTO.getOrderPromotionInfo()));
                } else {
                    cartItems = cartService.buildCartList(req.getSkuInfoList(), req.getOrderType(),
                            req.getMemberId(), req.getAreaCode(), req.getFinanceRuleCode(), paramDTO.getChannel(), paramDTO.getOrderAddress());

                    //补录订单需计算农服费用
                    String areaCode = cartItems.get(0).getAreaCode();
                    Long storeId = cartItems.get(0).getStoreId();
                    calculateAgricFeeDTOList(req,areaCode,storeId);
                    orderSubmitDTO = orderSubmitUtil.getOrderSubmitDTOV2(cartItems, paramDTO, Boolean.TRUE.equals(req.isSimulationShoppingCart()));
                }
                if (SettleModeEnum.BORROW.getCode().equals(orderSubmitDTO.getOrderInfoList().get(0).getSettleMode())) {
                    throw new BusinessException("暂不支持购买现款现货的商品");
                }
            } else if (paramDTO.isShareResource()) {
                List<CartPO> cartItems = cartService.buildCartListV2(req.getSkuInfoList(), req.getMemberId(), req.getOrderType(), paramDTO.getChannel(), paramDTO.getOrderAddress(), paramDTO.getIsCart());
                orderSubmitDTO = orderSubmitUtil.getOrderSubmitDTOV3(cartItems, paramDTO, true, paramDTO.getIsCart());
            } else {
                orderSubmitDTO = orderSubmitUtil.getOrderSubmitDTO(paramDTO, req.getMemberId(), paramDTO.getProductType(),
                        paramDTO.getOrderAddress(), true, true);
            }

            //定金不计算优惠
            if (req.getPreOrderDTO() != null && req.getPreOrderDTO().getIsCalculateDiscount()) {
                //调用活动模块计算优惠：封装活动对象，计算活动金额
                JsonResult<OrderSubmitDTO> result = promotionCommonFeignClient.orderSubmitCalculationDiscountV2(orderSubmitDTO, paramDTO.getSource());
                if (result.getState() != 200) {
                    throw new MallException(result.getMsg());
                }
                orderSubmitDTO = result.getData();
            }

            //计算运费
            OrderAddressDTO orderAddress = req.getParamDTO().getOrderAddress();
//            List<BigDecimal> expressFeeList = new ArrayList<>();
            BigDecimal totalExpressFee = BigDecimal.ZERO;
            boolean containsDealerStore = false;
            Member member = orderSubmitAttributesUtils.getMemberByMemberId(req.getMemberId());
            for (OrderSubmitDTO.OrderInfo orderInfo : orderSubmitDTO.getOrderInfoList()) {
                // 校验用户是否在当前店铺为经销商身份
                if (!containsDealerStore) {
                    try {
                        JsonResult<PurchaserFeignResultVO> dealResult = ExternalApiUtil.callJsonResultApi(() -> purchaserFeign.getPurchaserRelationByUserNoAndSupllierStoreId(member.getUserNo(), orderInfo.getStoreId()), orderInfo.getStoreId(),
                                "/purchaser/isDealer", "判定用户在当前店铺是否为经销商");
                        PurchaserFeignResultVO purchaserFeignResultVO = dealResult.getData();
                        if (Objects.nonNull(purchaserFeignResultVO)) {
                            // 数据存在则表示是经销商
                            containsDealerStore = true;
                        }
                    } catch (Exception e) {
                        log.info("获取经销商身份失败");
                    }
                }
                BigDecimal expressFee;
                //传了地址信息，计算运费
                expressFee = orderLocalUtils.getOrderExpressFee(orderAddress.getCityCode(), orderInfo);
                totalExpressFee = totalExpressFee.add(expressFee);
//                expressFeeList.add(expressFee);
                //立即购买时，orderType会由于单品活动查询接口，预先赋值205满赠类型，该处先还原为普通订单
                if (orderInfo.getOrderType().equals(OrderTypeEnum.FULL_GIFT.getValue())
                        || orderInfo.getOrderType().equals(PromotionConst.PROMOTION_TYPE_205)) {
                    orderInfo.setOrderType(OrderTypeEnum.NORMAL.getValue());
                }
                //存在赠品，则将订单置为满赠订单
                if (!CollectionUtils.isEmpty(orderInfo.getFullGiftPromotionInfoList())) {
                    for (OrderSubmitDTO.PromotionInfo promotionInfo : orderInfo.getFullGiftPromotionInfoList()) {
                        if (!CollectionUtils.isEmpty(promotionInfo.getConsumptionFreebieList())) {
                            orderInfo.setOrderType(OrderTypeEnum.FULL_GIFT.getValue());
                            // 有一些情况下，订单层级的promotionId没有返回
                            orderInfo.setPromotionId(Integer.valueOf(promotionInfo.getPromotionId()));
                            break;
                        }
                    }
                }
                orderInfo.setExpressFee(expressFee);
                orderInfo.setExpressFeeTotal(expressFee);
            }
            orderSubmitDTO.setExpressFee(totalExpressFee);
            //处理已计算好的运费
//            iOrderService.dealExpress(orderSubmitDTO, expressFeeList);

            // 拼单标识校验、赋值
            orderCreateHelper.groupBuyingTagCheck(req.getUserNo(), paramDTO.getGroupBuyingTag(), orderSubmitDTO);
            if (containsDealerStore) {
                if (orderSubmitDTO.getOrderInfoList().size() > 1) {
                    // 多店铺下单
                    throw new BusinessException("经销商客户需按店铺分开下单");
                }
            }

            log.info("============= orderSubmitDTO:{}", JSON.toJSONString(orderSubmitDTO));
            orderSnList = orderModel.submit(orderSubmitDTO,member,req);
        } catch (Exception e) {
            log.info("创建补录订单失败", e);
            // 订单提交失败 活动库存回退
            promotionUtils.deductionStockNumber(req.getPreOrderDTO(), req.getMemberId(), paramDTO.getNumber());

            if (e instanceof BusinessException || e instanceof MallException) {
                throw e;
            }

            throw new MallException(
                    "创建订单失败,请联系管理员！",
                    "创建订单失败, 支付单号:" + req.getPaySn(),
                    ErrorCodeEnum.S.SYSTEM_EXCEPTION.getCode(),
                    e);

        } finally {
            //订单处理成功或失败，都删除redis中的订单处理标识
            stringRedisTemplate.delete(OrderConst.ORDER_SUBMIT_MQ_REDIS_PREFIX + req.getPaySn());
        }
        if (req.getPreOrderDTO() != null
                && req.getPreOrderDTO().getOrderType().equals(PromotionConst.PROMOTION_TYPE_104)) {
            //秒杀订单提交完毕，增加会员购买数量
            String key = RedisConst.REDIS_SECKILL_MEMBER_BUY_NUM_PREFIX + paramDTO.getProductId() + "_" + req.getMemberId();
            String memberAlreadyBuyNum = stringRedisTemplate.opsForValue().get(key);
            if (memberAlreadyBuyNum == null) {
                stringRedisTemplate.opsForValue().set(key, paramDTO.getNumber().toString());
            } else {
                stringRedisTemplate.opsForValue().set(key, (paramDTO.getNumber() + Integer.parseInt(memberAlreadyBuyNum)) + "");
            }
        }
        return orderSnList;
    }

    private void calculateAgricFeeDTOList(OrderSubmitMqConsumerDTO req,String areaCode,Long storeId) {
        log.info("calculateAgricFeeDTOList storeId = {}", storeId);
        if(StringUtils.isEmpty(orderOfflineAgriFeeCalculConfig.getAgriFeeCalStoreId())) {
            return;
        }
        if(!orderOfflineAgriFeeCalculConfig.getAgriFeeCalStoreId().contains(String.valueOf(storeId))) {
            return;
        }
        if (OrderTypeEnum.isOfflineAll(req.getOrderType())
                && (Objects.isNull(req.getOrderOfflineParamDTO().getIsContinueSubmit())
                || !req.getOrderOfflineParamDTO().getIsContinueSubmit())) {
            List<AgricFeeCalculateDTO> agricFeeCalculateDTOList = new ArrayList<>();
            for (OrderSkuInfoDTO orderSkuInfoDTO : req.getSkuInfoList()) {
                AgricFeeCalculateDTO agricFeeCalculateDTO = new AgricFeeCalculateDTO();
                agricFeeCalculateDTO.setBizNo(String.valueOf(orderSkuInfoDTO.getProductId()));
                agricFeeCalculateDTO.setAreaCode(areaCode);
                agricFeeCalculateDTO.setProductNum(orderSkuInfoDTO.getNumber());
                agricFeeCalculateDTO.setFinanceRuleCode(orderSkuInfoDTO.getFinanceRuleCode());
                agricFeeCalculateDTO.setProductId(orderSkuInfoDTO.getProductId());
                agricFeeCalculateDTO.setTaxPrice(orderSkuInfoDTO.getTaxPrice());
                agricFeeCalculateDTOList.add(agricFeeCalculateDTO);
            }
            List<AgricFeeCalculateVO> agricFeeCalculateVOList = goodsFeignIntegration.getAgricFeeCalculateVOList(agricFeeCalculateDTOList);
            if (CollectionUtils.isEmpty(agricFeeCalculateVOList)) {
                throw new BusinessException(OrderConst.ORDER_OFFLINE_AGRIC_FEE_CODE, "订单计算农服费用参数存在缺失，是否无需计算，继续提交订单");
            }
            for (AgricFeeCalculateVO agricFeeCalculateVO : agricFeeCalculateVOList) {
                if (Objects.isNull(agricFeeCalculateVO) || Objects.isNull(agricFeeCalculateVO.getAgricFee())) {
                    throw new BusinessException(OrderConst.ORDER_OFFLINE_AGRIC_FEE_CODE,"订单计算农服费用参数存在缺失，是否无需计算，继续提交订单");
                }
                for(OrderSkuInfoDTO orderSkuInfoDTO : req.getSkuInfoList()) {
                    if(String.valueOf(orderSkuInfoDTO.getProductId()).equals(agricFeeCalculateVO.getBizNo())) {
                        orderSkuInfoDTO.setAgriServiceFees(agricFeeCalculateVO.getAgricFee());
                    }
                }
            }
        }
    }

    @Override
    public void profitWithBackup(String orderSn) {
        try {
            payIntegration.profitSharding(orderSn);
        } catch (Exception e) {
            log.error("profitSharding()订单微信分账失败", e);
            taskQueueService.saveTaskQueue(
                    Long.valueOf(orderSn),
                    TaskQueueBizTypeEnum.PROFIT_COMP,
                    new Date(),
                    TaskConstant.DEFAULT_JOB_NUMBER
            );
        }
    }

    @Override
    public boolean closeOrderStatus(String orderSn) {
        //订单状态-已关闭
        OrderPO orderPO = new OrderPO();
        orderPO.setOrderState(OrderConst.ORDER_STATE_50);

        LambdaQueryWrapper<OrderPO> orderQuery = new LambdaQueryWrapper();
        orderQuery.eq(OrderPO::getOrderSn, orderSn);

        return this.update(orderPO, orderQuery);
    }

    @Override
    public boolean updateOrderStatus(String orderSn, int orderState) {

        OrderStatusEnum orderStatusEnum = OrderStatusEnum.valueOf(orderState);
        BizAssertUtil.isTrue(orderStatusEnum == null, "请传入正确的订单状态!");

        OrderPO orderPO = new OrderPO();
        orderPO.setOrderState(orderState);

        LambdaQueryWrapper<OrderPO> orderQuery = new LambdaQueryWrapper();
        orderQuery.eq(OrderPO::getOrderSn, orderSn);

        return this.update(orderPO, orderQuery);
    }


    @Override
    public OrderPO getByOrderSn(String orderSn) {
        LambdaQueryWrapper<OrderPO> orderQuery = new LambdaQueryWrapper();
        orderQuery.eq(OrderPO::getOrderSn, orderSn);
        orderQuery.eq(OrderPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
        return orderMapper.selectOne(orderQuery);
    }


    //region    模式订单


    /**
     * 确认下单
     */
    @Override
    @SuppressWarnings("all")
    public OrderSubmitPageVO confirmPurchaseOrder(OrderSubmitParamDTO submitParam, Member member) {

        // 1、解析地址信息
        OrderAddressDTO addressDTO = submitParam.getPurchaseOrderAddress();
        if (Objects.isNull(addressDTO)) {
            addressDTO = orderCreateHelper.buildOrderAddressById(submitParam.getAddressId());
        }

        // 2、解析入参
        OrderSubmitDTO orderSubmitDTO = OrderSubmitDTOContext.ORDER_SUBMIT_DTO_CREATOR_MAP
                .get(OrderPatternEnum.valueOf(submitParam.getOrderPattern()))
                .getOrderSubmitDto(submitParam, member, submitParam.getAreaCode(), addressDTO, false);

        // 4、运费计算
        if (OrderPatternEnum.PURCHASE_CENTRE.getValue().equals(submitParam.getOrderPattern())) {
            List<GoodsPurchaseDeliveryVO> purchaseDeliveryVOs = (List<GoodsPurchaseDeliveryVO>) ExpressCalculateStrategy
                    .calculate(OrderPatternEnum.PURCHASE_CENTRE, orderSubmitDTO.getOrderInfoList(), addressDTO);
            this.purchaseOrderDealExpress(orderSubmitDTO, purchaseDeliveryVOs);
        } else {
            List<BigDecimal> expressFeeList = (List<BigDecimal>) ExpressCalculateStrategy
                    .calculate(OrderPatternEnum.SHOP_STREET, orderSubmitDTO.getOrderInfoList(), addressDTO);
            iOrderService.dealExpress(orderSubmitDTO, expressFeeList);
        }

        // 5、构建返回对象
        OrderSubmitPageVO vo = new OrderSubmitPageVO(orderSubmitDTO);

        // 6、是否可以开发票
        vo.setIsVatInvoice(allowDrawInvoice(orderSubmitDTO.getOrderInfoList()));

        return vo;
    }

    /**
     * 订单处理运费信息
     *
     * @param orderSubmitDTO      解析的订单信息
     * @param purchaseDeliveryVOs 运费信息
     */
    private void purchaseOrderDealExpress(OrderSubmitDTO orderSubmitDTO, List<GoodsPurchaseDeliveryVO> purchaseDeliveryVOs) {
        for (int i = 0; i < orderSubmitDTO.getOrderInfoList().size(); i++) {
            OrderSubmitDTO.OrderInfo orderInfo = orderSubmitDTO.getOrderInfoList().get(i);
            GoodsPurchaseDeliveryVO purchaseDeliveryVO = purchaseDeliveryVOs.get(i);
            orderInfo.setDeliverPlace(purchaseDeliveryVO.getDeliveryAreaInfo());
            orderInfo.setExpressFee(purchaseDeliveryVO.getFreightFee());
            orderInfo.setExpressFeeTotal(purchaseDeliveryVO.getFreightFee());
            orderInfo.setEstimateExpressFee(purchaseDeliveryVO.getDeliveryFee());
            orderInfo.setExpressCalculateType(purchaseDeliveryVO.getCalculateDeliveryFeeByDistance() ? 2 : 1);
            orderSubmitDTO.setExpressFee(Optional.ofNullable(orderSubmitDTO.getExpressFee()).orElse(new BigDecimal(0))
                    .add(purchaseDeliveryVO.getFreightFee()));
        }
    }

    /**
     * 下单前校验
     */
    @Override
    @SuppressWarnings("all")
    public OrderSubmitCheckVO checkPurchaseOrder(OrderSubmitParamDTO submitParam, Member member) {
        // 1、解析地址信息
        OrderAddressDTO addressDTO = submitParam.getPurchaseOrderAddress();
        if (Objects.isNull(addressDTO)) {
            addressDTO = orderCreateHelper.buildOrderAddressById(submitParam.getAddressId());
        }
        OrderSubmitDTO orderSubmitDTO = OrderSubmitDTOContext.ORDER_SUBMIT_DTO_CREATOR_MAP
                .get(OrderPatternEnum.valueOf(submitParam.getOrderPattern()))
                .getOrderSubmitDto(submitParam, member, submitParam.getAreaCode(), addressDTO, true);
        return cartModel.checkCart(orderSubmitDTO);
    }

    /**
     * 提交订单
     */
    @Override
    @SuppressWarnings("all")
    public Map<String, String> submitPurchaseOrder(OrderSubmitParamDTO submitParam, Member member) {
        // 1、零元订单验证码校验
        validUtils.checkVerifyCode(submitParam.getVerifyCode(), member, 0);

        // 2、解析地址信息
        OrderAddressDTO addressDTO = submitParam.getPurchaseOrderAddress();
        if (Objects.isNull(addressDTO)) {
            addressDTO = orderCreateHelper.buildOrderAddressById(submitParam.getAddressId());
        }
        submitParam.setOrderAddress(addressDTO);

        // 3、创建支付单号（有传入使用传入的订单号）
        String pno = StringUtils.isEmpty(submitParam.getPno()) ?
                String.valueOf(shardingId.next(SeqEnum.PNO, member.getMemberId().toString())) : submitParam.getPno();

        // 4、构造订单传输对象，用于下单处理
        OrderSubmitMqConsumerDTO consumerDTO = new OrderSubmitMqConsumerDTO();
        consumerDTO.setParamDTO(submitParam);
        consumerDTO.setMemberId(member.getMemberId());
        consumerDTO.setUserNo(member.getUserNo());
        consumerDTO.setPaySn(pno);
        consumerDTO.setAreaCode(submitParam.getAreaCode());

        //将表标识放入redis，key=标识前缀+paySn，mq处理成功或失败后删除标识
        stringRedisTemplate.opsForValue().set(OrderConst.ORDER_SUBMIT_MQ_REDIS_PREFIX + pno, "");

        // 5、解析入参
        OrderSubmitDTO orderSubmitDTO = OrderSubmitDTOContext.ORDER_SUBMIT_DTO_CREATOR_MAP
                .get(OrderPatternEnum.valueOf(submitParam.getOrderPattern()))
                .getOrderSubmitDto(submitParam, member, submitParam.getAreaCode(), addressDTO, true);

        if (SettleModeEnum.BORROW.getCode().equals(orderSubmitDTO.getOrderInfoList().get(0).getSettleMode())) {
            throw new BusinessException("暂不支持购买现款现货的商品");
        }

        // 4、运费计算
        if (OrderPatternEnum.PURCHASE_CENTRE.getValue().equals(submitParam.getOrderPattern())) {
            List<GoodsPurchaseDeliveryVO> purchaseDeliveryVOs = (List<GoodsPurchaseDeliveryVO>) ExpressCalculateStrategy
                    .calculate(OrderPatternEnum.PURCHASE_CENTRE, orderSubmitDTO.getOrderInfoList(), addressDTO);
            this.purchaseOrderDealExpress(orderSubmitDTO, purchaseDeliveryVOs);
        } else {
            List<BigDecimal> expressFeeList = (List<BigDecimal>) ExpressCalculateStrategy
                    .calculate(OrderPatternEnum.SHOP_STREET, orderSubmitDTO.getOrderInfoList(), addressDTO);
            iOrderService.dealExpress(orderSubmitDTO, expressFeeList);
        }

        // 6、执行创建订单
        List<String> orderSnList = Lists.newArrayList();
        try {
            //通用提交订单
            orderSnList = orderModel.submit(orderSubmitDTO, member, consumerDTO);
        } catch (Exception e) {
            log.warn("异常为：{}", e.getClass().getName());
            if (e instanceof BusinessException || e instanceof MallException) {
                throw e;
            }
            if (e instanceof DuplicateKeyException) {
                throw new MallException("创建订单失败,请联系管理员！",
                        "创建订单失败," + ErrorCodeEnum.S.DATA_UNIQUE_CONFLICT.getMsg() + ",支付单号:" + pno,
                        ErrorCodeEnum.S.DATA_UNIQUE_CONFLICT.getCode(), e);
            } else {
                throw new MallException("创建订单失败,请联系管理员！", "创建订单失败, 支付单号:" + pno,
                        ErrorCodeEnum.S.SYSTEM_EXCEPTION.getCode(), e);
            }
        }

        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("paySn", pno);
        dataMap.put("orderList", JSONObject.toJSONString(orderSnList));

        return dataMap;
    }

    @Override
    public ChannelOrderSubmitVO submitCouponOrder(OrderSubmitParamDTO submitParam, Member member, ChannelCouponOrderSubmitDTO channelSubmitDTO) {
        // 1、零元订单验证码校验
        validUtils.checkVerifyCode(submitParam.getVerifyCode(), member, 0);

        // 解析入参
        OrderSubmitDTO orderSubmitDTO = orderSubmitUtil.getOrderSubmitDTO(submitParam, member.getMemberId(),
                submitParam.getProductType(), null, true, true);

        // 收货地址设为空
        OrderAddressDTO addressDTO = new OrderAddressDTO();
        submitParam.setOrderAddress(addressDTO);

        // 查询商品是否参与单品活动
        GoodsPromotion singlePromotion = cartModel.getSinglePromotion(submitParam.getProductId(), member.getMemberId());
        PreOrderDTO preOrderDTO = new PreOrderDTO();
        if (singlePromotion != null && !submitParam.getIsAloneBuy()) {
            // 活动预校验
            preOrderDTO = promotionUtils.preCheckSubmit(singlePromotion, submitParam.getProductId(), submitParam.getNumber(), member.getMemberId());

            // dto活动参数赋值
            promotionUtils.promotionParamSet(singlePromotion, submitParam, orderSubmitDTO);
        }

        if (preOrderDTO.getIsCalculateDiscount()) {
            // 调用活动模块计算优惠：封装活动对象，计算活动金额
            JsonResult<OrderSubmitDTO> result = promotionCommonFeignClient.orderSubmitCalculationDiscountV2(orderSubmitDTO, submitParam.getSource());
            if (result.getState() != 200) {
                throw new MallException(result.getMsg());
            }
            orderSubmitDTO = result.getData();
        }

        // 运费设置为0
        orderSubmitDTO.setExpressFee(BigDecimal.ZERO);
        //子单运费
        for (int i = 0; i < orderSubmitDTO.getOrderInfoList().size(); i++) {
            orderSubmitDTO.getOrderInfoList().get(i).setExpressFee(BigDecimal.ZERO);
            orderSubmitDTO.getOrderInfoList().get(i).setExpressFeeTotal(BigDecimal.ZERO);
        }
        // 创建支付单号（有传入使用传入的订单号）
        String pno = StringUtils.isEmpty(submitParam.getPno()) ?
                String.valueOf(shardingId.next(SeqEnum.PNO, member.getMemberId().toString())) : submitParam.getPno();

        //将表标识放入redis，key=标识前缀+paySn，mq处理成功或失败后删除标识
        stringRedisTemplate.opsForValue().set(OrderConst.ORDER_SUBMIT_MQ_REDIS_PREFIX + pno, "");

        // 构造订单传输对象，用于下单处理
        OrderSubmitMqConsumerDTO consumerDTO = new OrderSubmitMqConsumerDTO();
        consumerDTO.setParamDTO(submitParam);
        consumerDTO.setMemberId(member.getMemberId());
        consumerDTO.setUserNo(member.getUserNo());
        consumerDTO.setPaySn(pno);
        consumerDTO.setPreOrderDTO(preOrderDTO);
        consumerDTO.setAreaCode(submitParam.getAreaCode());
        if (Objects.nonNull(channelSubmitDTO)) {
            // 乡助福袋订单，传乡助订单来源及业务单号
            ChannelOrderSubmitDTO channelOrderSubmitDTO = new ChannelOrderSubmitDTO();
            channelOrderSubmitDTO.setOutBizId(channelSubmitDTO.getOutBizId());
            channelOrderSubmitDTO.setOutBizSource(channelSubmitDTO.getOutBizSource());
            channelOrderSubmitDTO.setChannel(submitParam.getChannel());
            consumerDTO.setChannelOrderSubmitDTO(channelOrderSubmitDTO);
        }
        //代客下单
        if (submitParam.isPlacingCombinationFlag()) {
            OrderValidation.isValidBatchOrderBaseInfo(submitParam);
            OrderPlaceUserDTO orderPlaceUserDTO = OrderBuilder.buildOrderPlaceUser(request, member, submitParam.getChannel(), submitParam.getOrderPlaceUserRole());
            consumerDTO.setOrderPlaceUserDTO(orderPlaceUserDTO);
            consumerDTO.setOrderPlaceUserRole(submitParam.getOrderPlaceUserRole());
        }
        log.info("卡券订单下单参数：{}", JSONObject.toJSONString(orderSubmitDTO));

        // 执行创建订单
        List<String> orderSnList = Lists.newArrayList();
        try {
            //通用提交订单
            orderSnList = orderModel.submit(orderSubmitDTO, member, consumerDTO);
        } catch (Exception e) {
            log.warn("下单失败，异常为：{}", e.getClass().getName());

            // 订单提交失败 redis活动库存回退
            promotionUtils.deductionStockNumber(preOrderDTO, member.getMemberId(), submitParam.getNumber());

            if (e instanceof BusinessException || e instanceof MallException) {
                throw e;
            }

            throw new MallException(
                    "创建订单失败,请联系管理员！",
                    "创建订单失败, 支付单号:" + pno,
                    ErrorCodeEnum.S.SYSTEM_EXCEPTION.getCode(),
                    e);
        }

        ChannelOrderSubmitVO vo = new ChannelOrderSubmitVO();
        vo.setPaySn(pno);
        vo.setOrderSnList(orderSnList);

        return vo;
    }

    /**
     * 是否允许开发票
     *
     * @param orderInfos 订单信息
     * @return true/false
     */
    private Boolean allowDrawInvoice(List<OrderSubmitDTO.OrderInfo> orderInfos) {
        Map<Long, Goods> goodsMap = new HashMap<>();//保存已查询的商品，减少查库次数
        for (OrderSubmitDTO.OrderInfo orderInfo : orderInfos) {
            for (OrderSubmitDTO.OrderInfo.OrderProductInfo orderProductInfo : orderInfo.getOrderProductInfoList()) {
                if (goodsMap.containsKey(orderProductInfo.getGoodsId())) {
                    continue;
                }
                Goods goods = goodsFeignClient.getGoodsByGoodsId(orderProductInfo.getGoodsId());
                if (goods.getIsVatInvoice() != null && goods.getIsVatInvoice() == GoodsConst.IS_VAT_INVOICE_NO) {
                    //商品不允许开增值税发票,跳出循环
                    return false;
                }
                goodsMap.put(goods.getGoodsId(), goods);
            }
        }
        return true;
    }

    //region end


    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public OrderSubmitVO createOfflineOrder(OrderOfflineParamDTO dto) {
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        log.info("【createOfflineOrder】线下补录订单 {}", dto);

        if (Objects.equals(CustomerTypeEnum.ENTERPRISE_CUSTOMER.getCode(), dto.getCustomerType())) {
            // 企业用户补录订单使用默认用户信息
            dto.setUserMobile(defaultToBOfflineOrderMemberMobile);
            BizAssertUtil.notNull(dto.getOrderOfflineCompanyDTO(), "企业用户补录订单企业用户信息不能为空");
            BizAssertUtil.notEmpty(dto.getSellBranchCode(), "企业用户补录订单销售分支不能为空");
        } else {
            BizAssertUtil.notEmpty(dto.getUserMobile(), "乡助会员补录订单客户手机号不能为空");
            // 乡助会员补录订单，客户手机号为内部员工不允许下单
            Boolean isInter = ExternalApiUtil.callResultApi(() -> bmsUserFacade.isInter(dto.getUserMobile()), dto.getUserMobile(),
                    "/user/isInter", "根据手机号判断是否是内部员工");
            if (!Objects.isNull(isInter) && isInter) {
                Boolean whiteList = storeIsolateWhiteListFeignClient.isStoreOnList(WhiteListEnum.ORDER_OFFLINE_INNER_STAFF, vendor.getStoreId());
                BizAssertUtil.isTrue(!whiteList, "手机号为【内部员工】手机号，请重新输入");
            }
        }

        /////////////////////////////出单条件验证/////////////////////////////
        OrderOfflineValidation.isValidOfflineOrderBaseInfo(dto);
        //查询员工信息 20230710修改：获取员工管护分支信息，不从hr获取
        CustInfoVo custInfoVo = orderOfflineService.queryCustInfo(dto.getEmployeeCode());
        OrderOfflineValidation.isValidOfflineOrderEmployeeInfo(custInfoVo, dto.getEmployeeCode());
        //查询用户信息
        MemberExample example = new MemberExample();
        example.setMemberMobile(dto.getUserMobile());
        List<Member> members = memberFeignClient.getMemberList(example);
        OrderOfflineValidation.isValidOfflineOrderMemberInfo(dto.getUserMobile(), members);
        // 自提订单，查询自提点信息
        if (OrderPatternEnum.SELF_LIFT.getValue().equals(dto.getOrderPattern())) {
            OrderAddressDTO orderAddressDTO = orderCreateHelper.buildOrderAddressByPointId(dto.getPointId());
            dto.setAddress(orderAddressDTO);
        }

        //线下补录订单扩展信息校验
        OrderOfflineValidation.validOfflineInfoOrder(dto.getOfflineInfoDTO());

        /////////////////////////////出单参数构建/////////////////////////////
        Member member = members.get(0);
        orderSubmitAttributesUtils.setMember(member);
        long paySn = shardingId.next(SeqEnum.PNO, member.getMemberId().toString());

        OrderSubmitParamDTO orderSubmitParam = orderCreateHelper.buildOrderSubmit(dto, member.getUserNo(), custInfoVo);

        // 构造入mq对象
        OrderSubmitMqConsumerDTO consumerDTO = orderCreateHelper.buildOrderSubmit(orderSubmitParam, dto, member, paySn);
        // 将表标识放入redis，key=标识前缀+paySn，mq处理成功或失败后删除标识
        stringRedisTemplate.opsForValue().set(OrderConst.ORDER_SUBMIT_MQ_REDIS_PREFIX + paySn, "");
        log.info("【createOfflineOrder】线下补录订单，下单前处理：{}", consumerDTO);

        /////////////////////////////创建订单/////////////////////////////
        List<String> orderSnList = this.createOrder(consumerDTO);

        log.info("【createOfflineOrder】线下补录订单--------------paySn:{}", paySn);
        LambdaQueryWrapper<OrderPO> orderQuery = new LambdaQueryWrapper<>();
        //orderQuery.select(OrderPO::getOrderSn);
        orderQuery.eq(OrderPO::getPaySn, consumerDTO.getPaySn());
        List<OrderPO> orderPOList = super.list(orderQuery);
        OrderSubmitVO vo = new OrderSubmitVO();
        vo.setPaySn(String.valueOf(paySn));
        vo.setOrderSnList(orderPOList.stream().map(OrderPO::getOrderSn).collect(Collectors.toList()));

        /////////////////////////////创建线下订单收款信息/////////////////////////////
        if (!CollectionUtils.isEmpty(dto.getOrderOfflineList())) {
            List<OrderOfflinePO> orderOfflines = OrderOfflineBuilder.buildOrderOfflinePOList(
                    vo.getPaySn(), member.getMemberName(), dto.getOrderOfflineList());
            orderOfflineService.saveBatch(orderOfflines);
        }

        /////////////////////////////线下补录扩展信息/////////////////////////////
        if (ObjectUtils.isNotEmpty(dto.getOfflineInfoDTO())) {
            orderOfflineExtendService.saveOfflineOrder(orderPOList, dto, vendor.getVendorName());
        }
        log.info("【createOfflineOrder】线下补录订单--------------paySn:{} vo:{}", paySn, vo);
        return vo;
    }

    @Override
    public ExportingFailedDataToExcelVO importOfflineOrder(Vendor vendor, MultipartFile excelFile) throws Exception {
        BizAssertUtil.isTrue(!privilegeService.getSellerPrivilegeInfo(vendor.getStoreId().intValue()).isOrderOfflinePrivilege(),
                String.format("抱歉，当前店铺编号【%s】，暂无线下补录订单操作权限，请联系管理员，赋予权限！", vendor.getStoreId()));
        List<OrderOfflineImportDTO> failList = new ArrayList<>();
        List<OrderOfflineImportDTO> orderOfflineImportList = getExcelData(excelFile);
        int total = orderOfflineImportList.size();
        checkImportData(orderOfflineImportList, failList, vendor);
        orderOfflineImportList.removeAll(failList);
        createOrderOffline(orderOfflineImportList, failList);

        OrderOfflineExportRequest request = new OrderOfflineExportRequest();
        request.setFailList(failList);
        request.setBizModule("订单补录");
        UserDTO userDTO = new UserDTO(vendor);
        request.setUserDTO(userDTO);
        FileDTO fileDTO = orderOfflineImportFailExportService.executeAsyncExportExcel(request);

        ExportingFailedDataToExcelVO result = new ExportingFailedDataToExcelVO();
        result.setFailureCount(failList.size());
        result.setSuccessCount(total - failList.size());
        result.setFileDTO(fileDTO);
        return result;
    }

    private void checkImportData(List<OrderOfflineImportDTO> excelData, List<OrderOfflineImportDTO> failList, Vendor vendor) {
        List<Long> productIds = excelData.stream().map(OrderOfflineImportDTO::getSkuId).distinct().collect(Collectors.toList());
        Map<Long, Goods> productIdGoodsMap = goodsFeignIntegration.getGoodsByProductIds(productIds);
        for (OrderOfflineImportDTO dto : excelData) {
            if (!validatorUtils.validateOrder(dto)){
                dto.setRemark("导入文件数据校验异常");
                failList.add(dto);
                continue;
            }
            if (BigDecimal.ZERO.compareTo(dto.getTaxPrice()) == 0) {
                dto.setRemark("订单金额不能为0");
                failList.add(dto);
                continue;
            }
            BigDecimal orderAmount = dto.getTaxPrice().multiply(new BigDecimal(dto.getBuyNum()));
            if (dto.getDiscountAmount() != null && orderAmount.compareTo(dto.getDiscountAmount()) < 0) {
                dto.setRemark("订单金额不能小于优惠金额");
                failList.add(dto);
                continue;
            }
            Integer orderPatterValue = OrderPatternEnum.getByDesc(dto.getOrderPattern()).getValue();
            if (OrderPatternEnum.SELF_LIFT.getValue().equals(orderPatterValue)) {
                dto.setRemark("批量导入不支持自提订单");
                failList.add(dto);
                continue;
            }
            Goods goods = productIdGoodsMap.getOrDefault(dto.getSkuId(), null);
            if (goods == null) {
                dto.setRemark("商品SKU不存在");
                failList.add(dto);
                continue;
            }
            if (GoodsConst.GOODS_STATE_UPPER != goods.getState().intValue()) {
                dto.setRemark("非上架商品不支持下单");
                failList.add(dto);
                continue;
            }
            Integer orderPattern = OrderPatternEnum.getByDesc(dto.getOrderPattern()).getValue();
            if (OrderPatternEnum.PURCHASE_CENTRE.getValue().equals(orderPattern) && !OrderPatternEnum.PURCHASE_CENTRE.getValue().equals(goods.getGoodsType())) {
                dto.setRemark("非采购商品不能创建采购订单");
                failList.add(dto);
                continue;
            }
            if (OrderPatternEnum.PURCHASE_CENTRE.getValue().equals(goods.getGoodsType()) && !OrderPatternEnum.PURCHASE_CENTRE.getValue().equals(orderPattern)) {
                dto.setRemark("采购商品不能创建非采购订单");
                failList.add(dto);
            }

            FrontStoreRegionRequestVO requestVO = new FrontStoreRegionRequestVO();
            requestVO.setProvince(dto.getProvince());
            requestVO.setCity(dto.getCity());
            requestVO.setCounty(dto.getDistrict());
            requestVO.setTown(dto.getTown());
            requestVO.setStoreId(vendor.getStoreId().toString());
            FrontStoreRegionVo accurateArea = storeRegionFeignClient.getAccurateAreaByGps(requestVO);
            log.info("获取区域编码返回 {}", JSON.toJSONString(accurateArea));
            if (accurateArea == null || CollectionUtils.isEmpty(accurateArea.getRegionVOList())) {
                dto.setRemark("商品在收货区域不可售");
                failList.add(dto);
                continue;
            }

            ProductPriceDTO productPriceDTO = new ProductPriceDTO();
            productPriceDTO.setProductId(dto.getSkuId());
            productPriceDTO.setAreaCodeList(accurateArea.getRegionVOList().stream().map(StoreRegionVO::getCode).collect(Collectors.toList()));
            JsonResult<ProductSimplePriceVO> productPrice = productFeignClient.getProductPrice(productPriceDTO);
            log.info("获取地区价格返回 {}", JSON.toJSONString(productPrice));
            if (Status.OK.getCode().equals(productPrice.getState()) && productPrice.getData() != null) {
                ProductSimplePriceVO productSimplePriceVO = productPrice.getData();
                if (OrderConst.IS_AREA_PRICE.equals(productSimplePriceVO.getIsAreaPrice()) && CollectionUtils.isEmpty(productSimplePriceVO.getAreaPriceVOList())) {
                    dto.setRemark("商品在收货区域不可售");
                    failList.add(dto);
                }
            }
        }
    }

    private void createOrderOffline(List<OrderOfflineImportDTO> orderOfflineImportList, List<OrderOfflineImportDTO> failList) {
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        for (OrderOfflineImportDTO dto : orderOfflineImportList) {
            try {
                OrderOfflineParamDTO paramDTO = OrderOfflineImportDTO.buildOrderOfflineParam(dto);
                log.info("线下补录订单开始 {}", new Date());
                orderModel.createOfflineOrderLocalTransaction(vendor,paramDTO);
                log.info("线下补录订单结束 {}", new Date());
            } catch (Exception e) {
                log.info("创建线下单失败 {}", JSON.toJSONString(dto), e);
                dto.setRemark("创建线下单失败： " + e.getMessage());
                failList.add(dto);
            }
            // 清空订单提交上下文
            OrderSubmitContextHolder.resetOrderSubmitAttributes();
        }
    }

    private List<OrderOfflineImportDTO> getExcelData(MultipartFile excelFile) {
        try {
            SyncReadListener excelListener = new SyncReadListener();
            EasyExcel.read(excelFile.getInputStream(), OrderOfflineImportDTO.class, excelListener).doReadAll();
            List<Object> list = excelListener.getList();
            if (list.size() > 20) {
                throw new BusinessException("单次导入最多不超过20条，请核查！");
            }

            Set<OrderOfflineImportDTO> OrderOfflineImportList = new HashSet<>();
            for (Object obj : list) {
                OrderOfflineImportDTO orderOfflineImportDTO = (OrderOfflineImportDTO) obj;
                OrderOfflineImportList.add(orderOfflineImportDTO);
            }

            return new ArrayList<>(OrderOfflineImportList);
        }catch (BusinessException businessException){
            throw businessException;
        } catch (Exception e) {
            log.info("线下单导入模板错误", e);
            throw new BusinessException("线下单导入模板错误，请联系管理员！");
        }
    }

    @Override
    public OrderSubmitVO createBatchOrder(OrderParamDTO dto) {
        log.info("【createBatchOrder】批量非购物车订单：{}", dto);
        /////////////////////////////出单条件验证/////////////////////////////
        Member member = UserUtil.getUser(request, Member.class);
        OrderValidation.isValidOrderMemberInfo(member);
        orderSubmitAttributesUtils.setMember(member);
        log.info("【createBatchOrder】member:{}", JSON.toJSONString(member));
        OrderPlaceUserDTO orderPlaceUserDTO = OrderBuilder.buildOrderPlaceUser(request, member, dto.getChannel(), dto.getOrderPlaceUserRole());
        log.info("【createBatchOrder】orderPlaceUserDTO:{}", JSON.toJSONString(orderPlaceUserDTO));
        OrderValidation.isValidBatchOrderBaseInfo(dto);
        /////////////////////////////出单参数构建/////////////////////////////
        long paySn = shardingId.next(SeqEnum.PNO, member.getMemberId().toString());
        OrderSubmitParamDTO orderSubmitParam = orderCreateHelper.buildOrderSubmit(dto, member.getUserNo());
        // 构造入mq对象
        OrderSubmitMqConsumerDTO consumerDTO = orderCreateHelper.buildOrderSubmit(orderSubmitParam, dto, member, paySn, orderPlaceUserDTO);
        // 将表标识放入redis，key=标识前缀+paySn，mq处理成功或失败后删除标识
        stringRedisTemplate.opsForValue().set(OrderConst.ORDER_SUBMIT_MQ_REDIS_PREFIX + paySn, "");
        log.info("【createBatchOrder】批量非购物车订单，下单前处理：{}", consumerDTO);
        /////////////////////////////创建订单/////////////////////////////
        List<String> orderSnList = this.createOrder(consumerDTO);

        log.info("【createBatchOrder】批量非购物车订单--------------paySn:{}", paySn);
        OrderSubmitVO vo = new OrderSubmitVO();
        vo.setPaySn(String.valueOf(paySn));
        vo.setOrderSnList(orderSnList);
        log.info("【createBatchOrder】批量非购物车订单--------------paySn:{} vo:{}", paySn, vo);
        return vo;
    }

    @Override
    public OrderSubmitVO createGroupOrder(GroupBuyingOrderSubmitDTO dto, UserDTO userDTO) {
        /**
         * 赠品订单商品校验
         */
        GroupOrderProductSubmitDTO groupOrderProductSubmitDTO = new GroupOrderProductSubmitDTO();
        BeanUtils.copyProperties(dto, groupOrderProductSubmitDTO);
        orderGroupBuyingRecordService.checkGroupOrder(groupOrderProductSubmitDTO);

        /**
         * 收货地址
         */
        if (Objects.nonNull(dto.getPointId())) {
            OrderAddressDTO orderAddressDTO = orderCreateHelper.buildOrderAddressByPointId(dto.getPointId());
            dto.setAddress(orderAddressDTO);
        } else if (Objects.isNull(dto.getAddress())) {
            throw new BusinessException(ErrorCodeEnum.U.INVALID_BIZ_DATA.getCode(), "订单收货地址缺失");
        }

        /**
         * 查询用户信息
         */
        // 下单人取分支主任
        OrganizationWithDirectorVO directorByHrOrgCode = bmsIntegration.getDirectorByHrOrgCodes(dto.getBranchCode());
        BizAssertUtil.notNull(directorByHrOrgCode, "分支主任不存在");
        // 根据身份证查客户信息
        CustBaseInfoVo custBaseInfoVo = customerIntegration.baseInfoByIdNo(directorByHrOrgCode.getIdCard());
        BizAssertUtil.notNull(custBaseInfoVo, "客户信息不存在");
        //查询用户信息
        MemberExample example = new MemberExample();
        example.setMemberMobile(custBaseInfoVo.getMobile());
        List<Member> members = memberFeignClient.getMemberList(example);
        OrderOfflineValidation.isValidOfflineOrderMemberInfo(custBaseInfoVo.getMobile(), members);
        // 会员信息
        Member member = members.get(0);

        /**
         * 构建下单入参
         */
        OrderSubmitParamDTO paramDTO = new OrderSubmitParamDTO();
        paramDTO.setMemberId(member.getMemberId());
        paramDTO.setUsrNo(member.getUserNo());
        paramDTO.setOrderType(OrderTypeEnum.GROUP_BUYING_GIFT);
        paramDTO.setChannel(dto.getChannel());
        paramDTO.setOrderAddress(dto.getAddress());
        String areaCode = orderCreateHelper.getStoreAreaCodeByAddress(dto.getStoreId().toString(), dto.getAddress(), member);
        paramDTO.setAreaCode(areaCode);
        paramDTO.setSource(3);
        paramDTO.setOrderFrom(1);
        paramDTO.setProductType(1);
        paramDTO.setChannelOrder(false);
        paramDTO.setIsCart(false);
        paramDTO.setIsAloneBuy(false);
        paramDTO.setBankTransferable(false);

        /**
         * 构建订单参数
         */
        List<CartPO> cartItems = cartService.buildCartList(dto.getSkuInfoList(), paramDTO.getOrderType(),
                member.getMemberId(), paramDTO.getAreaCode(), paramDTO.getFinanceRuleCode(), paramDTO.getChannel(), paramDTO.getOrderAddress());

        OrderSubmitDTO orderSubmitDTO = orderSubmitUtil.getOrderSubmitDTOV2(cartItems, paramDTO, true);

        // 运费设置为0
        orderSubmitDTO.setExpressFee(BigDecimal.ZERO);
        //子单运费
        for (int i = 0; i < orderSubmitDTO.getOrderInfoList().size(); i++) {
            orderSubmitDTO.getOrderInfoList().get(i).setExpressFee(BigDecimal.ZERO);
            orderSubmitDTO.getOrderInfoList().get(i).setExpressFeeTotal(BigDecimal.ZERO);
        }

        // 创建支付单号
        String pno = String.valueOf(shardingId.next(SeqEnum.PNO, member.getMemberId().toString()));

        //将表标识放入redis，key=标识前缀+paySn，mq处理成功或失败后删除标识
        stringRedisTemplate.opsForValue().set(OrderConst.ORDER_SUBMIT_MQ_REDIS_PREFIX + pno, "");

        /**
         * 构造订单传输对象，用于下单处理
         */
        OrderSubmitMqConsumerDTO consumerDTO = new OrderSubmitMqConsumerDTO();
        consumerDTO.setParamDTO(paramDTO);
        consumerDTO.setMemberId(member.getMemberId());
        consumerDTO.setUserNo(member.getUserNo());
        consumerDTO.setPaySn(pno);
        consumerDTO.setPreOrderDTO(null);
        consumerDTO.setAreaCode(paramDTO.getAreaCode());
        consumerDTO.setGroupOrderProductSubmitDTO(groupOrderProductSubmitDTO);
        OrderPlaceUserDTO orderPlaceUserDTO = new OrderPlaceUserDTO();
        orderPlaceUserDTO.setOrderPlaceUserId(userDTO.getUserId());
        orderPlaceUserDTO.setOrderPlaceUserName(userDTO.getUserName());
        consumerDTO.setOrderPlaceUserDTO(orderPlaceUserDTO);
        log.info("赠品订单下单参数：{}, pno:{}", JSONObject.toJSONString(orderSubmitDTO), pno);

        // 执行创建订单
        List<String> orderSnList = Lists.newArrayList();
        try {
            //通用提交订单
            orderSnList = orderModel.submit(orderSubmitDTO, member, consumerDTO);
        } catch (Exception e) {

            if (e instanceof BusinessException || e instanceof MallException) {
                throw e;
            }

            throw new MallException(
                    "创建订单失败,请联系管理员！",
                    "创建订单失败, 支付单号:" + pno,
                    ErrorCodeEnum.S.SYSTEM_EXCEPTION.getCode(),
                    e);
        }

        // 查询要返回的信息

        OrderSubmitVO vo = new OrderSubmitVO();
        vo.setPaySn(pno);
        vo.setOrderSnList(orderSnList);

        return vo;
    }


    @Override
    public OrderSubmitVO createRebateGiftOrder(RebateOrderSubmitDTO dto) {
        /**
         * 收货地址
         */
        Long pointId = dto.getPointId();
        if (Objects.nonNull(pointId)) {
            OrderAddressDTO orderAddressDTO = orderCreateHelper.buildOrderAddressByPointId(pointId);
            dto.setAddress(orderAddressDTO);
        } else if (Objects.isNull(dto.getAddress())) {
            throw new BusinessException(ErrorCodeEnum.U.INVALID_BIZ_DATA.getCode(), "订单收货地址缺失");
        }

        /**
         * 查询用户信息
         */
        // 会员信息
        Member member = memberFeignClient.getMemberByMemberId(Math.toIntExact(dto.getMemberId()));
        BizAssertUtil.isTrue(Objects.isNull(member),"用户信息为空");
        /**
         * 构建下单入参
         */
        OrderSubmitParamDTO paramDTO = new OrderSubmitParamDTO();
        paramDTO.setMemberId(member.getMemberId());
        paramDTO.setUsrNo(member.getUserNo());
        paramDTO.setOrderType(OrderTypeEnum.REBATE_GIFT);
        paramDTO.setChannel(dto.getChannel());
        paramDTO.setOrderAddress(dto.getAddress());
        String areaCode = orderCreateHelper.getStoreAreaCodeByAddress(dto.getStoreId().toString(), dto.getAddress(), member);
        paramDTO.setAreaCode(areaCode);
        paramDTO.setSource(3);
        paramDTO.setOrderFrom(1);
        paramDTO.setProductType(1);
        paramDTO.setChannelOrder(false);
        paramDTO.setIsCart(false);
        paramDTO.setIsAloneBuy(false);
        paramDTO.setBankTransferable(false);
        if (ValidUtils.isEmpty(pointId)){
            paramDTO.setOrderPattern(OrderPatternEnum.SHOP_STREET.getValue());
        }else{
            paramDTO.setOrderPattern(OrderPatternEnum.SELF_LIFT.getValue());
        }

        /**
         * 构建订单参数
         */
        List<CartPO> cartItems = cartService.buildCartList(dto.getSkuInfoList(), paramDTO.getOrderType(),
                member.getMemberId(), paramDTO.getAreaCode(), paramDTO.getFinanceRuleCode(), paramDTO.getChannel(), paramDTO.getOrderAddress());

        OrderSubmitDTO orderSubmitDTO = orderSubmitUtil.getOrderSubmitDTOV2(cartItems, paramDTO, true);
        // 运费设置为0
        orderSubmitDTO.setExpressFee(BigDecimal.ZERO);
        //子单运费
        for (int i = 0; i < orderSubmitDTO.getOrderInfoList().size(); i++) {
            OrderSubmitDTO.OrderInfo orderInfo = orderSubmitDTO.getOrderInfoList().get(i);
            orderInfo.setExpressFee(BigDecimal.ZERO);
            orderInfo.setExpressFeeTotal(BigDecimal.ZERO);
            orderInfo.setPromotionId(Integer.valueOf(dto.getPromotionId()));
            List<OrderSubmitDTO.OrderInfo.OrderProductInfo> orderProductInfoList = orderInfo.getOrderProductInfoList();
            for (OrderSubmitDTO.OrderInfo.OrderProductInfo orderProductInfo : orderProductInfoList) {
                OrderSubmitDTO.PromotionInfo promotionInfo = new OrderSubmitDTO.PromotionInfo();
                promotionInfo.setPromotionId(dto.getPromotionId());
                promotionInfo.setPromotionType(PromotionConst.PROMOTION_TYPE_108);
                promotionInfo.setPromotionName("返利活动");
                promotionInfo.setIsStore(Boolean.TRUE);
                // 返利活动信息等于商品价格
                promotionInfo.setDiscount(orderProductInfo.getMoneyAmount());
                orderProductInfo.getPromotionInfoList().add(promotionInfo);
            }
        }

        // 创建支付单号
        String pno = String.valueOf(shardingId.next(SeqEnum.PNO, member.getMemberId().toString()));

        //将表标识放入redis，key=标识前缀+paySn，mq处理成功或失败后删除标识
        stringRedisTemplate.opsForValue().set(OrderConst.ORDER_SUBMIT_MQ_REDIS_PREFIX + pno, "");

        /**
         * 构造订单传输对象，用于下单处理
         */
        OrderSubmitMqConsumerDTO consumerDTO = new OrderSubmitMqConsumerDTO();
        consumerDTO.setParamDTO(paramDTO);
        consumerDTO.setMemberId(member.getMemberId());
        consumerDTO.setUserNo(member.getUserNo());
        consumerDTO.setPaySn(pno);
        consumerDTO.setPreOrderDTO(null);
        consumerDTO.setAreaCode(paramDTO.getAreaCode());
        consumerDTO.setChannelOrderSn(dto.getIdempotent());
        log.info("赠品订单下单参数：{}, pno:{}", JSONObject.toJSONString(orderSubmitDTO), pno);

        // 执行创建订单
        List<String> orderSnList = Lists.newArrayList();
        try {
            // 是否需要分布式锁？
            String idempotent = dto.getIdempotent();
            LambdaQueryWrapper<OrderPO> orderQuery = Wrappers.lambdaQuery(OrderPO.class);
            orderQuery.select(OrderPO::getOrderSn,OrderPO::getPaySn);
            orderQuery.eq(OrderPO::getChannelOrderSn,idempotent);
            orderQuery.eq(OrderPO::getChannel,dto.getChannel());
            OrderPO existOrderPo = iOrderService.getOne(orderQuery);
            if (null != existOrderPo){
                OrderSubmitVO vo = new OrderSubmitVO();
                vo.setPaySn(existOrderPo.getPaySn());
                vo.setOrderSnList(Collections.singletonList(existOrderPo.getOrderSn()));
                log.info("createRebateOrder use exist result:{}",vo);
                return vo;
            }
            //通用提交订单
            orderSnList = orderModel.submit(orderSubmitDTO, member, consumerDTO);
            // 查询要返回的信息
            OrderSubmitVO vo = new OrderSubmitVO();
            vo.setPaySn(pno);
            vo.setOrderSnList(orderSnList);
            log.info("createRebateOrder result:{}",vo);
            return vo;
        } catch (Exception e) {
            log.info("createRebateOrder fail,param:{},exp:{}",dto,e.getMessage());

            if (e instanceof BusinessException || e instanceof MallException) {
                throw e;
            }

            throw new MallException(
                    "创建订单失败,请联系管理员！",
                    "创建订单失败, 支付单号:" + pno,
                    ErrorCodeEnum.S.SYSTEM_EXCEPTION.getCode(),
                    e);
        }

    }

    @Override
    public FastOrderConfirmVO cacheConfirm(OrderCacheConfirmDTO dto) {
        String randomNumeric = RandomStringUtils.randomNumeric(8);
        String key = OrderConst.CACHE_CONFIRM_REDIS_PREFIX + dto.getQrCode() + "_" + randomNumeric;
        stringRedisTemplate.opsForValue().set(key, JSONObject.toJSONString(dto), expiredDay, TimeUnit.HOURS);
        FastOrderConfirmVO vo = new FastOrderConfirmVO();
        vo.setQrCodeResult(dto.getQrCode() + "_" + randomNumeric);
        return vo;
    }


    @Override
    @Transactional
    public FastOrderConfirmVO getCacheConfirm(OrderGetCacheConfirmDTO dto, Integer memberId) {
        String key = OrderConst.CACHE_CONFIRM_REDIS_PREFIX + dto.getQrCode();
        String s = stringRedisTemplate.opsForValue().get(key);
        if (StringUtils.isEmpty(s)) {
            return null;
        }

        List<OrderAddressDTO> addressList = dto.getAddressList();
        List<OrderAddressDTO> addressListAdd = new ArrayList<>();
        FastOrderConfirmVO fastOrderConfirmVO = JSONObject.parseObject(s, FastOrderConfirmVO.class);

        //校验允许被使用的用户
        List<Integer> memberIdList = fastOrderConfirmVO.getMemberIdList();
        if (CollectionUtil.isEmpty(memberIdList)) {
            memberIdList = new ArrayList<>();
            memberIdList.add(memberId);
            fastOrderConfirmVO.setMemberIdList(memberIdList);
        } else {
            if (!memberIdList.contains(memberId)) {
                if (memberIdList.size() < 3) {
                    memberIdList.add(memberId);
                    fastOrderConfirmVO.setMemberIdList(memberIdList);
                } else {
                    return null;
                }
            }
        }
        //刷新缓存
        stringRedisTemplate.opsForValue().set(key, JSONObject.toJSONString(fastOrderConfirmVO), expiredDay, TimeUnit.HOURS);


        OrderAddressDTO orderAddressDTO;
        if (!StringUtil.isNullOrZero(fastOrderConfirmVO.getAddressId())) {
            orderAddressDTO = orderCreateHelper.buildOrderAddressById(fastOrderConfirmVO.getAddressId());
        } else if (Objects.nonNull(fastOrderConfirmVO.getPointId())) {
            orderAddressDTO = orderCreateHelper.buildOrderAddressByPointId(fastOrderConfirmVO.getPointId());
            if (ObjectUtil.isNotNull(orderAddressDTO)) {
                //自提订单直接返回自提点地址
                fastOrderConfirmVO.setAddAddress(false);
                addressListAdd.add(orderAddressDTO);
                fastOrderConfirmVO.setAddressList(addressListAdd);
                fastOrderConfirmVO.setIsSelfLift(true);
                return fastOrderConfirmVO;
            }
        } else {
            throw new BusinessException(ErrorCodeEnum.U.INVALID_BIZ_DATA.getCode(), "订单收货地址缺失");
        }


        //新客户无地址，直接提示一键新增地址 ；自提订单，展示自提点地址
        if (CollectionUtil.isEmpty(addressList)) {
            fastOrderConfirmVO.setAddAddress(true);
            addressListAdd.add(orderAddressDTO);
            fastOrderConfirmVO.setAddressList(addressListAdd);
            return fastOrderConfirmVO;
        }
        //比对分享者的地址与员工的地址，省市区详细地址如果一致，则不提示新增，否则提示一键新增
        boolean flag = true;
        for (OrderAddressDTO address : addressList) {
            //其它订单展示用户收获地址
            if (address.getProvince().equals(orderAddressDTO.getProvince()) && address.getCity().equals(orderAddressDTO.getCity())
                    && address.getDistrict().equals(orderAddressDTO.getDistrict()) && address.getDetailAddress().equals(orderAddressDTO.getDetailAddress())) {
                flag = false;
                addressListAdd.add(address);
            }

        }
        if (flag) {
            fastOrderConfirmVO.setAddAddress(true);
            addressListAdd.add(orderAddressDTO);
            fastOrderConfirmVO.setAddressList(addressListAdd);
        } else {
            fastOrderConfirmVO.setAddAddress(false);
            fastOrderConfirmVO.setAddressList(addressListAdd);
        }
        return fastOrderConfirmVO;
    }
}
