//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//

package com.cfpamf.ms.mallorder.controller.fegin.facade;

import com.cfpamf.cmis.common.base.CommonResult;
import com.cfpamf.cmis.common.base.PageBean;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.bizconfig.facade.request.AuditVisitRequest;
import com.cfpamf.ms.bizconfig.facade.request.QueryUserScopeRequest;
import com.cfpamf.ms.bizconfig.facade.request.UserListRequest;
import com.cfpamf.ms.bizconfig.facade.request.UserRequest;
import com.cfpamf.ms.bizconfig.facade.request.UserRuleRequest;
import com.cfpamf.ms.bizconfig.facade.request.UserScopeRequest;
import com.cfpamf.ms.bizconfig.facade.vo.AuditVisitBranchVo;
import com.cfpamf.ms.bizconfig.facade.vo.EmployeePostVo;
import com.cfpamf.ms.bizconfig.facade.vo.LawsuitAgentVo;
import com.cfpamf.ms.bizconfig.facade.vo.MaxLoanAuditGradeVo;
import com.cfpamf.ms.bizconfig.facade.vo.StaffCreditVo;
import com.cfpamf.ms.bizconfig.facade.vo.UserAcctInfoVo;
import com.cfpamf.ms.bizconfig.facade.vo.UserCardNoVo;
import com.cfpamf.ms.bizconfig.facade.vo.UserConfigVo;
import com.cfpamf.ms.bizconfig.facade.vo.UserDutyVo;
import com.cfpamf.ms.bizconfig.facade.vo.UserRoleVo;
import com.cfpamf.ms.bizconfig.facade.vo.UserRuleVo;
import com.cfpamf.ms.bizconfig.facade.vo.UserVo;
import java.util.List;
import javax.validation.Valid;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(
    value = "ms-service-bizconfig",
    url = "${ms-bizconfig-service.url}"
)
public interface UserFacade {
    @PostMapping({"/bizconfig/user/getUserInfoByUserCode"})
    CommonResult<List<UserVo>> getUserListByUserCodeList(@RequestBody List<String> var1) throws Exception;

    @GetMapping({"/bizconfig/user/getUserInfoByUserCode"})
    CommonResult<UserVo> getUserListByUserCode(@RequestParam("userCode") String var1) throws Exception;

    @GetMapping({"/bizconfig/user/getUserInfoByUserCode/v2"})
    Result<UserVo> getUserListByUserCodeV2(@RequestParam("userCode") String var1, @RequestParam("branchCode") String var2) throws Exception;

    @GetMapping({"/bizconfig/user/getUserInfoByUserCode/v3"})
    Result<UserVo> getUserListByUserCodeV3(@RequestParam("userCode") String var1, @RequestParam("branchCode") String var2) throws Exception;

    @GetMapping({"/bizconfig/user/getUserInfoContainLeaveByUserCde"})
    CommonResult<UserVo> getUserInfoContainLeaveByUserCde(@RequestParam("userCode") String var1) throws Exception;

    @GetMapping({"/bizconfig/user/getUserInfoContainLeaveByUserCde/v2"})
    Result<UserVo> getUserInfoContainLeaveByUserCdeV2(@RequestParam("userCode") String var1, @RequestParam("branchCode") String var2) throws Exception;

    @PostMapping({"/bizconfig/user/getUserInfoContainLeaveByUserCdeList"})
    Result<List<UserVo>> getUserInfoContainLeaveByUserCdeList(@RequestBody List<String> var1) throws Exception;

    @PostMapping({"/bizconfig/user/getUserInfoContainLeaveByUserCdeList/v2"})
    Result<List<UserVo>> getUserInfoContainLeaveByUserCdeListV2(@RequestBody List<UserListRequest> var1) throws Exception;

    @GetMapping({"/bizconfig/user/getUserInfoByBranchCode"})
    CommonResult<List<UserVo>> getUserListByBranchCode(@RequestParam("branchCode") String var1) throws Exception;

    @GetMapping({"/bizconfig/user/getUserInfoByBranchCode/v2"})
    Result<List<UserVo>> getUserListByBranchCodeV2(@RequestParam("branchCode") String var1) throws Exception;

    @GetMapping({"/bizconfig/user/getUserInfoByOrgCode"})
    Result<List<UserVo>> getUserListByOrgCode(@RequestParam("branchCode") String var1) throws Exception;

    @GetMapping({"/bizconfig/user/getUserInfoBySupervisor"})
    CommonResult<List<UserVo>> getUserListBySupervisor(@RequestParam("supervisorCode") String var1) throws Exception;

    @GetMapping({"/bizconfig/user/getUserInfoBySupervisor/v2"})
    Result<List<UserVo>> getUserListBySupervisorV2(@RequestParam("supervisorCode") String var1, @RequestParam("branchCode") String var2) throws Exception;

    @GetMapping({"/bizconfig/role/getUserRole"})
    CommonResult<List<UserRoleVo>> getUserRoleByUserCode(@RequestParam("userCode") String var1) throws Exception;

    @GetMapping({"/bizconfig/role/getUserRole/v2"})
    Result<List<UserRoleVo>> getUserRoleByUserCodeV2(@RequestParam("userCode") String var1, @RequestParam("branchCode") String var2) throws Exception;

    @GetMapping({"/bizconfig/role/getUserMaxRole"})
    CommonResult<UserRoleVo> getUserMaxRoleByUserCode(@RequestParam("userCode") String var1) throws Exception;

    @GetMapping({"/bizconfig/role/getUserMaxRole/v2"})
    Result<UserRoleVo> getUserMaxRoleByUserCodeV2(@RequestParam("userCode") String var1, @RequestParam("branchCode") String var2) throws Exception;

    /** @deprecated */
    @Deprecated
    @GetMapping({"/bizconfig/user/getUserAcctInfoByBankNo"})
    CommonResult<UserAcctInfoVo> getUserAcctInfo(@RequestParam("bankAcctNo") String var1, @RequestParam("bankAcctName") String var2) throws Exception;

    @PostMapping({"/bizconfig/user/getUserList"})
    CommonResult<PageBean<UserVo>> getUserList(@RequestBody UserRequest var1) throws Exception;

    @PostMapping({"/bizconfig/user/getUserList/v2"})
    Result<List<UserVo>> getUserListV2(@RequestBody UserRequest var1) throws Exception;

    @PostMapping({"/bizconfig/user/getUserListAndAreaSpsvr"})
    Result<List<UserVo>> getUserListAndAreaSpsvr(@RequestBody UserRequest var1) throws Exception;

    @GetMapping({"/bizconfig/user/staffCredit"})
    CommonResult<StaffCreditVo> queryStaffCredit(@RequestParam("idNo") String var1, @RequestParam("branchCode") String var2);

    /** @deprecated */
    @Deprecated
    @GetMapping({"/bizconfig/duty/getUserDuty"})
    CommonResult<List<UserDutyVo>> getUserDutyByUserCode(@RequestParam("userCode") String var1) throws Exception;

    @GetMapping({"/bizconfig/user/getUserConfig"})
    CommonResult<UserConfigVo> getUserConfigByUserCode(@RequestParam("userCode") String var1) throws Exception;

    @GetMapping({"/bizconfig/user/getUserConfig/v2"})
    Result<UserConfigVo> getUserConfigByUserCodeV2(@RequestParam("userCode") String var1, @RequestParam("branchCode") String var2) throws Exception;

    @GetMapping({"user/getUserConfig/v3"})
    Result<UserConfigVo> getUserConfigByUserCodeV3(@RequestParam("userCode") String var1, @RequestParam("branchCode") String var2) throws Exception;

    /** @deprecated */
    @Deprecated
    @GetMapping({"/bizconfig/user/getUserInfoByDutyCode"})
    CommonResult<List<UserVo>> getUserListByDutyCode(@RequestParam("dutyCode") String var1, @RequestParam("branchCode") String var2) throws Exception;

    @GetMapping({"/bizconfig/user/getUserCodesByIsFastLoan"})
    CommonResult<List<String>> getUserCodesByIsFastLoan(@RequestParam("isFastLoan") String var1) throws Exception;

    /** @deprecated */
    @Deprecated
    @GetMapping({"/bizconfig/user/getUserCardNoListByIdNo"})
    CommonResult<List<UserCardNoVo>> getUserCardNoListByIdNo(@RequestParam("idNo") String var1) throws Exception;

    @GetMapping({"/bizconfig/user/getUserRepaymentDay"})
    CommonResult<List<String>> getUserRepaymentDayList(@RequestParam("userCode") String var1) throws Exception;

    @GetMapping({"/bizconfig/userscope/getUserListByUserCodeAndBranchCode"})
    CommonResult<List<UserVo>> getUserListByUserCodeAndBranchCode(@RequestParam("userCode") String var1, @RequestParam(value = "branchCode",required = false) String var2) throws Exception;

    @PostMapping({"/bizconfig/userscope/getUserListByUserCodeAndBranchCode/v3"})
    Result<List<UserVo>> getUserListByUserCodeAndBranchCodeV3(@RequestBody UserScopeRequest var1) throws Exception;

    @GetMapping({"/bizconfig/user/getUserListByRoleCode"})
    CommonResult<List<UserVo>> getUserListByRoleCode(@RequestParam("roleCode") String var1) throws Exception;

    @GetMapping({"/bizconfig/user/getUserListByRoleCode/v2"})
    Result<List<UserVo>> getUserListByRoleCodeV2(@RequestParam("roleCode") String var1) throws Exception;

    @GetMapping({"/bizconfig/user/getLoanofficerByBranchCode"})
    Result<List<UserVo>> getLoanofficerByBranchCode(@RequestParam("branchCode") String var1) throws Exception;

    @GetMapping({"/bizconfig/user/getLoanofficerByBranchCode/v2"})
    Result<List<UserVo>> getLoanofficerByBranchCodeV2(@RequestParam("branchCode") String var1) throws Exception;

    @GetMapping({"/bizconfig/user/getAllLoanofficer"})
    Result<List<String>> getAllLoanofficer() throws Exception;

    @GetMapping({"/bizconfig/user/getAllLoanofficer/v2"})
    Result<List<String>> getAllLoanofficerV2() throws Exception;

    @GetMapping({"/bizconfig/user/getLoanofficerContainLeaveByBranchCode"})
    Result<List<UserVo>> getLoanofficerContainLeaveByBranchCode(@RequestParam("branchCode") String var1) throws Exception;

    @GetMapping({"/bizconfig/user/getLoanofficerContainLeaveByBranchCode/v2"})
    Result<List<UserVo>> getLoanofficerContainLeaveByBranchCodeV2(@RequestParam("branchCode") String var1) throws Exception;

    @GetMapping({"/bizconfig/user/getUserMasterByMobile"})
    Result<UserVo> getUserMasterByMobile(@RequestParam("mobile") String var1) throws Exception;

    @GetMapping({"/bizconfig/user/getUserInfoListByMobile"})
    Result<List<UserVo>> getUserInfoListByMobile(String var1) throws Exception;

    @GetMapping({"/bizconfig/user/getUserInfoListByMobile/v2"})
    Result<List<UserVo>> getUserInfoListByMobileV2(String var1) throws Exception;

    @GetMapping({"/bizconfig/user/getUserMasterByIdNo"})
    Result<UserVo> getUserMasterByIdNo(@RequestParam("idNo") String var1) throws Exception;

    @PostMapping({"/bizconfig/user/getUserMasterByIdNos"})
    Result<List<UserVo>> getUserMasterByIdNos(@RequestBody List<String> var1) throws Exception;

    @GetMapping({"/bizconfig/user/getR0002ByUserCode"})
    Result<List<UserVo>> getR0002ByUserCode(@RequestParam("userCode") String var1, @RequestParam("branchCode") String var2);

    @PostMapping({"/bizconfig/user/getR0002ByUserCode/v2"})
    Result<List<UserVo>> getR0002ByUserCodeV2(@RequestBody UserScopeRequest var1);

    @PostMapping({"/bizconfig/user/getR0002ByUserCode/v3"})
    Result<List<UserVo>> getR0002ByUserCodeV3(@RequestBody UserScopeRequest var1) throws Exception;

    @PostMapping({"user/getUserListBySupervisorAndUserCode"})
    Result<List<UserVo>> getUserListBySupervisorAndUserCode(@RequestBody @Valid QueryUserScopeRequest var1) throws Exception;

    @GetMapping({"/bizconfig/user/getManagerListByCounty"})
    Result<List<UserVo>> getManagerListByCounty(@RequestParam("county") String var1);

    @GetMapping({"/bizconfig/user/getManagerListByCounty/v2"})
    Result<List<UserVo>> getManagerListByCountyV2(@RequestParam("county") String var1);

    @GetMapping({"/bizconfig/user/getUserListByRoleCodeAndBranchCode"})
    Result<List<UserVo>> getUserListByRoleCodeAndBranchCode(@RequestParam("roleCode") String var1, @RequestParam("branchCode") String var2);

    @GetMapping({"/bizconfig/user/getUserListByRoleCodeAndBranchCode/v2"})
    Result<List<UserVo>> getUserListByRoleCodeAndBranchCodeV2(@RequestParam("roleCode") String var1, @RequestParam("branchCode") String var2);

    @GetMapping({"/bizconfig/user/getEmployeePost"})
    CommonResult<List<EmployeePostVo>> getEmployeePost(@RequestHeader("authorization") String var1);

    @GetMapping({"/bizconfig/user/getEmployeePost/v2"})
    Result<List<EmployeePostVo>> getEmployeePostV2(@RequestHeader("authorization") String var1);

    @GetMapping({"/bizconfig/user/getUserInfoByDeviceId"})
    Result<UserVo> getUserInfoByDeviceId(@RequestParam("deviceId") String var1) throws Exception;

    @PostMapping({"/bizconfig/user/getUserListByRuleQuery"})
    Result<List<UserRuleVo>> getUserListByRuleQuery(@RequestBody UserRuleRequest var1) throws Exception;

    @PostMapping({"/bizconfig/user/getUserListByRuleQuery/v2"})
    Result<List<UserRuleVo>> getUserListByRuleQueryV2(@RequestBody UserRuleRequest var1) throws Exception;

    @PostMapping({"/bizconfig/user/getUserAuditVisitBranchList"})
    Result<List<AuditVisitBranchVo>> getUserAuditVisitBranchList(@RequestBody AuditVisitRequest var1) throws Exception;

    @GetMapping({"/bizconfig/user/getMaxLoanAuditGrade"})
    Result<MaxLoanAuditGradeVo> getMaxLoanAuditGrade(@RequestParam("branchCode") String var1) throws Exception;

    @PostMapping({"/bizconfig/user/getLawsuitAgentListByBranchCode"})
    Result<List<LawsuitAgentVo>> getLawsuitAgentListByBranchCode(@RequestParam("branchCode") String var1);

    @PostMapping({"/bizconfig/user/disableBranchQrCode"})
    Result disableBranchQrCode(@RequestParam("branchCode") String var1);
}
