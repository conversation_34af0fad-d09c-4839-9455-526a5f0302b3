package com.cfpamf.ms.mallorder.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.cmis.common.base.PageBean;
import com.cfpamf.common.ms.vo.PageVO;
import com.cfpamf.ms.bizconfig.facade.request.ProductEssentialQueryRequet;
import com.cfpamf.ms.bizconfig.facade.request.ProductRequest;
import com.cfpamf.ms.bizconfig.facade.vo.product.ProductVo;
import com.cfpamf.ms.bizconfig.facade.vo.product.RepaymentRelationVo;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.common.enums.OrderCreateChannel;
import com.cfpamf.ms.mallorder.dto.*;
import com.cfpamf.ms.mallorder.po.PayMethodPO;
import com.cfpamf.ms.mallorder.vo.*;
import com.slodon.bbc.core.response.JsonResult;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <p>
 * 支付配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-04
 */
public interface IPayMethodService extends IService<PayMethodPO> {

    /**** @param vendorId
     * @return com.slodon.bbc.core.response.JsonResult
     * @description : 商家审核通过自动补充支付方式
     *
     */
    JsonResult insertPayMethodMerchant(Long vendorId);

    /**** @param payMethodReq
     * @return com.slodon.bbc.core.response.JsonResult<com.baomidou.mybatisplus.extension.plugins.pagination.Page   <   com.cfpamf.ms.mallorder.vo.PayMethodVO>>
     * @description :分页查询支付方式列表
     */
    JsonResult<Page<PayMethodPageVO>> pagePayMethodList(PayMethodReq payMethodReq);

    /**** @param dto
     * @return com.slodon.bbc.core.response.JsonResult
     * @description :保存支付方式
     */
    JsonResult savePayMethod(PayMethodDTO dto);

    /**** @param queryDTO
     * @return com.slodon.bbc.core.response.JsonResult<com.baomidou.mybatisplus.extension.plugins.pagination.Page   <   com.cfpamf.ms.mallorder.vo.PaymethodMerchantVO>>
     * @description :分页查询适用商家列表
     */
    @Deprecated
    JsonResult<Page<PaymethodMerchantVO>> pageMerchantList(PayMethodMerchantReq queryDTO);

    /**** @param dto
     * @return com.slodon.bbc.core.response.JsonResult
     * @description : 新增商家
     */
    @Deprecated
    JsonResult insertMerchant(PayMethodMerchantDTO dto);

    /**** @param vendorIds
     * @return com.slodon.bbc.core.response.JsonResult
     * @description : 删除商家
     * @param vendorIds
     */
    @Deprecated
    JsonResult deleteMerchant(PayMethodMerchantDTO vendorIds);


    /**** @param payMethodId
     * @return com.slodon.bbc.core.response.JsonResult<com.cfpamf.ms.mallorder.vo.BzPayMethodTrackVO>
     * @description : 支付方式修改列表
     */
    JsonResult<List<BzPayMethodTrackVO>> listPayMethodTrack(String payMethodId);


    /**** @param payMethodId
    * @return com.slodon.bbc.core.response.JsonResult<com.cfpamf.ms.mallorder.vo.BzPayMethodTrackVO>
    * @description : 支付方式修改列表
    */
    JsonResult<PageVO<BzPayMethodTrackVO>> listPayMethodTrackV2(String payMethodId, Integer current, Integer pageSize);

    /**** @param request
    * @return com.slodon.bbc.core.response.JsonResult<java.util.List<com.cfpamf.ms.bizconfig.facade.vo.product.ProductVo>>
    * @description :查询贷款产品列表
    */
    JsonResult<List<ProductVo>> getProductListByQuery(ProductRequest request);

    /*** @param queryRequet
    * @return com.slodon.bbc.core.response.JsonResult<com.cfpamf.cmis.common.base.PageBean<com.cfpamf.ms.bizconfig.facade.vo.product.RepaymentRelationVo>>
    * @description :查询产品信息
    */
    JsonResult<PageBean<RepaymentRelationVo>> queryProductEssential(ProductEssentialQueryRequet queryRequet);

    /**
    * @param dto
    * @return com.slodon.bbc.core.response.JsonResult
    * @description :新增商品
    */
    JsonResult insertGoods(PayMethodGoodsDTO dto);

    /***
    * @param dto
    * @return com.slodon.bbc.core.response.JsonResult
    * @description : 删除商品
    */
    JsonResult deleteGoods(PayMethodGoodsDeleteDTO dto);

    JsonResult<String> insertCategory(PayMethodCategoryDTO dto);

    /**
    * @param queryDTO
    * @return com.slodon.bbc.core.response.JsonResult<com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.cfpamf.ms.mallorder.vo.BzOrderPayBlacklistVO>>
    * @description :分页查询商品黑名单列表
    */
    JsonResult<PageVO<BzOrderPayBlacklistVO>> pageGoodsList(PayMethodGoodsReq queryDTO);

    /**
    * @param queryDTO
    * @return com.slodon.bbc.core.response.JsonResult<com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.cfpamf.ms.mallorder.vo.BzOrderPayWhitelistVO>>
    * @description :分页查询商家白名单列表
    */
    JsonResult<PageVO<BzOrderPayWhitelistVO>> pageMerchantV2List(PayMethodMerchantReq queryDTO);

    /**
    * @param dto
    * @return com.slodon.bbc.core.response.JsonResult
    * @description :新增商家V2
    */
    JsonResult insertMerchantV2(PayMethodMerchantV2DTO dto);

    /**
    * @param dto
    * @return com.slodon.bbc.core.response.JsonResult
    * @description :删除商家V2
    */
    JsonResult deleteMerchantV2(PayMethodGoodsDeleteDTO dto);

    /**
    * @param
    * @return com.slodon.bbc.core.response.JsonResult
    * @description : 处理历史商家
    */
    @Deprecated
    JsonResult merchantClean();

    /**
     * @param paySn
     * @param channel
     * @param member
     * @param mobiletype
     * @param utdid
     * @param imei
     * @param request
     * @param wifi
     * @param returnUrl
     * @param orderPattern
     * @return java.util.List<com.cfpamf.ms.mallorder.vo.PayMethodVO>
     * @description :查询可用支付方式列表
     */
    List<PayMethodVO3> getPayMethodVOS3(String paySn, OrderCreateChannel channel, Member member, String mobiletype,
                                        String utdid, String imei, HttpServletRequest request, String wifi, String returnUrl,
                                        String chan_nel, String ctversion, String checkAuthVersion);

    /**
     * 根据支付方式编码批量查询支付方式信息
     *
     * @param payMethodCodes    支付方式编码
     * @return                  支付方式信息
     */
    List<PayMethodVOV2> listPayMethodByCode(List<String> payMethodCodes);

    /**
     * @param paySn
     * @param channel
     * @param member
     * @param mobiletype
     * @param utdid
     * @param imei
     * @param request
     * @return java.util.List<com.cfpamf.ms.mallorder.vo.PayMethodVO>
     * @description :查询可用支付方式列表
     */
    List<PayMethodVO> getPayMethodVOS(String paySn, OrderCreateChannel channel, Member member, String mobiletype, String utdid, String imei, HttpServletRequest request);


    /**
    * @param goodsReq
    * @return java.util.List<? extends java.lang.Object>
    * @description : 查询商品黑名单列表
    */
    List<? extends Object> goodsList(PayMethodGoodsReq goodsReq);

    /**
    * @param
    * @return java.lang.String
    * @description : 初始化商品黑名单
    */
    Boolean initGoods();

    /**
     * 为指定店铺新增指定支付方式
     *
     * @param payMethodStoreDTO     支付方式和商家信息
     * @return                      结果
     */
    JsonResult<String> insertPayMethodStore(PayMethodStoreDTO payMethodStoreDTO);

    /**
     * 为指定店铺新增指定支付方式
     *
     * @param payMethodStoreDTO     支付方式和商家信息
     * @return                      结果
     */
    JsonResult<String> deletePayMethodStore(PayMethodStoreDTO payMethodStoreDTO);

    /**
     * @param
     * @return java.lang.String
     * @description : 初始化商家白名单
     */
    Boolean initStore();

    /**
     * 查询适用分类
     * @param payMethodId
     * @param orderPattern
     * @return
     */
    JsonResult<List<String>> queryCategory(String payMethodId, Integer orderPattern);

    /**
     * 新增分类，临时插入接口
     * @param dto
     * @return
     */
    JsonResult<Boolean> insertCategoryTmp(PayMethodCategoryListDTO dto);

    /**
     * 全量同步用呗、随心取适用商家至支付系统
     * @return
     * @param storeId
     * @param beginDate
     * @param endDate
     */
    JsonResult<Boolean> syncMerchant(String storeId, String beginDate, String endDate);

    /**
     * 增量同步用呗、随心取适用商家至支付系统
     * @return
     */
    boolean syncMerchantIntraday();

    /**
     * 增量增加店铺白名单
     * @param storeId
     * @param channel
     * @return
     */
    boolean addChannelWhiteList(String storeId, Integer channel);

    /**
     * 从白名单中删除指定店铺
     * @param storeId
     * @param channel
     * @return
     */
    boolean deleteChannelWhiteList(String storeId, Integer channel);

    /**
     * 根据店铺id获取可用支付方式
     * @param storeId storeId
     * @return List<PayMethodPO>
     */
    List<PayMethodStoreVO> getPayMethodStoreByStoreId(Long storeId);
}
