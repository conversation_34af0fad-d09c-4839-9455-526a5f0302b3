package com.cfpamf.ms.mallorder.service.payment;

public interface IRefundHandleService {

    /**
     * 校验电子账户余额
     * 
     * @param <T>
     * @param retundHandleType
     * @param t
     */
    <T> void verifyElectronicAccountBalance(RetundHandleType retundHandleType, T t);

    /**
     * 下账
     * 
     * @param <T>
     * @param retundHandleType
     * @param t
     * @return
     */
    <T> Boolean issuedBill(RetundHandleType retundHandleType, T t);

}
