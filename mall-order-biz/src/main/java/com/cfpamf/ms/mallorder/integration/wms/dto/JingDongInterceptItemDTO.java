package com.cfpamf.ms.mallorder.integration.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class JingDongInterceptItemDTO {

    @ApiModelProperty(value = "规格编码")
    @NotBlank(message = "规格编码不能为空")
    private String skuId;

    @ApiModelProperty(value = "规格单位编码")
    @NotBlank(message = "规格单位编码不能为空")
    private String skuUnitCode;

    @ApiModelProperty(value = "退款数量")
    @NotNull(message = "退款数量不能为空")
    private Integer returnNum;

}
