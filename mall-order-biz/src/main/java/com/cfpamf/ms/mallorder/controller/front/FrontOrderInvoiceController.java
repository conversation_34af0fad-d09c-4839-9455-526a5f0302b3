package com.cfpamf.ms.mallorder.controller.front;

import com.cfpamf.ares.trade.common.enums.OrderSourceEnum;
import com.cfpamf.ares.trade.domain.vo.InvoiceBuyerVO;
import com.cfpamf.ares.trade.domain.vo.InvoiceTitleVO;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.common.util.ValidUtils;
import com.cfpamf.ms.mallorder.service.IOrderInvoiceService;
import com.cfpamf.ms.mallorder.vo.CreateInvoiceParamVO;
import com.cfpamf.ms.mallorder.vo.MallInvoiceVO;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import com.slodon.bbc.core.util.UserUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * 前端发票控制器
 *
 * <AUTHOR>
 * @since 2024/12/3
 */
@Api(tags = "front-发票管理")
@Slf4j
@RestController
@RequestMapping("front/invoice")
public class FrontOrderInvoiceController {
    @Autowired
    private IOrderInvoiceService orderInvoiceService;

    @ApiOperation("查询发票信息")
    @GetMapping("/queryInvoiceByOrderSn")
    public JsonResult<MallInvoiceVO> queryInvoice(HttpServletRequest request, @RequestParam("orderSn")String orderSn){
        Member member = UserUtil.getUser(request, Member.class);
        if (!ValidUtils.isMemberLogin(member)){
            throw new BusinessException("请先登录");
        }
        MallInvoiceVO invoiceVO = orderInvoiceService.queryInvoice(member,orderSn);
        return SldResponse.success(invoiceVO);
    }

    @ApiOperation("生成发票")
    @PostMapping("/createInvoice")
    public JsonResult<Boolean> createInvoice(HttpServletRequest request, @RequestBody @Valid CreateInvoiceParamVO createInvoiceParamVO){
        Member member = UserUtil.getUser(request, Member.class);
        if (!ValidUtils.isMemberLogin(member)){
            throw new BusinessException("请先登录");
        }
        Boolean result = orderInvoiceService.createInvoice(member,createInvoiceParamVO);
        return SldResponse.success(result);
    }

    @ApiOperation("发票换开")
    @PostMapping("/undoInvoice")
    public JsonResult<Boolean> undoInvoice(HttpServletRequest request, @RequestBody @Valid CreateInvoiceParamVO createInvoiceParamVO){
        Member member = UserUtil.getUser(request, Member.class);
        if (!ValidUtils.isMemberLogin(member)){
            throw new BusinessException("请先登录");
        }
        Boolean result = orderInvoiceService.undoInvoice(member,createInvoiceParamVO);
        return SldResponse.success(result);
    }

    @ApiOperation("发票换开-售后")
    @PostMapping("/undoInvoiceAfter")
    public JsonResult<String> undoInvoiceAfter(@RequestParam String orderSn){
        orderInvoiceService.undoInvoiceFromAfter(orderSn);
        return SldResponse.success("success");
    }

    @ApiOperation(value = "获取订单可开票金额")
    @GetMapping("/getInvoiceAmount")
    public JsonResult<BigDecimal> getInvoiceAmount(@RequestParam String orderSn){
        BigDecimal amount = orderInvoiceService.getInvoiceAmount(orderSn);
        return SldResponse.success(amount);
    }

    /**
     * 根据名称查询企业抬头信息
     *
     * @param name 名称
     * @param searchType 查询类型，0-模糊查询，1-精确查询
     * @return 抬头信息列表
     */
    @ApiOperation("纳税企业信息查询")
    @GetMapping("/buyerSearch")
    public JsonResult<List<InvoiceBuyerVO>> buyerSearch(@RequestParam("name")String name, @RequestParam(value = "searchType",defaultValue = "0")Integer searchType){
        List<InvoiceBuyerVO> result = orderInvoiceService.buyerSearch(name,searchType);
        return SldResponse.success(result);
    }

    @GetMapping("/queryTitleList")
    @ApiOperation("查询发票抬头列表")
    public JsonResult<List<InvoiceTitleVO>> queryTitleList(HttpServletRequest request) {
        Member member = UserUtil.getUser(request, Member.class);
        if (!ValidUtils.isMemberLogin(member)){
            throw new BusinessException("请先登录");
        }
        List<InvoiceTitleVO> queryTitleList = orderInvoiceService.queryTitleList(member);
        return SldResponse.success(queryTitleList);
    }

    @GetMapping("/queryTitleDetail")
    @ApiOperation("查询发票抬头详情")
    public JsonResult<InvoiceTitleVO> queryTitleDetail(HttpServletRequest request,@RequestParam("id") Long id) {
        Member member = UserUtil.getUser(request, Member.class);
        if (!ValidUtils.isMemberLogin(member)){
            throw new BusinessException("请先登录");
        }
        InvoiceTitleVO result = orderInvoiceService.queryTitleDetail(id);
        return SldResponse.success(result);
    }

    @PostMapping("/updateTitle")
    @ApiOperation("更新发票抬头")
    public JsonResult<Void> updateTitle(HttpServletRequest request,@RequestBody InvoiceTitleVO vo) {
        Member member = UserUtil.getUser(request, Member.class);
        if (!ValidUtils.isMemberLogin(member)){
            throw new BusinessException("请先登录");
        }
        vo.setOwner(member.getMemberId().toString());
        vo.setUpdateBy(member.getMemberId().toString());
        vo.setChannel(OrderSourceEnum.STANDARD_MALL.getOrderSourceCode());
        orderInvoiceService.updateTitle(vo);
        return SldResponse.success();
    }

    @PostMapping("/saveTitle")
    @ApiOperation("新增发票抬头")
    public JsonResult<Void> saveTitle(HttpServletRequest request,@RequestBody InvoiceTitleVO vo) {
        Member member = UserUtil.getUser(request, Member.class);
        if (!ValidUtils.isMemberLogin(member)){
            throw new BusinessException("请先登录");
        }
        vo.setOwner(member.getMemberId().toString());
        vo.setUpdateBy(member.getMemberId().toString());
        vo.setChannel(OrderSourceEnum.STANDARD_MALL.getOrderSourceCode());
        orderInvoiceService.saveTitle(vo);
        return SldResponse.success();
    }

    @GetMapping("/delTitle")
    @ApiOperation("删除发票抬头")
    public JsonResult<Void> delTitle(HttpServletRequest request,@RequestParam("id") Long id) {
        Member member = UserUtil.getUser(request, Member.class);
        if (!ValidUtils.isMemberLogin(member)){
            throw new BusinessException("请先登录");
        }
        orderInvoiceService.delTitle(id);
        return SldResponse.success();
    }
}
