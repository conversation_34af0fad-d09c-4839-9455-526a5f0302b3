package com.cfpamf.ms.mallorder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.ms.mallorder.dto.OrderProductDeliverDTO;
import com.cfpamf.ms.mallorder.po.OrderLogisticItemPO;
import com.cfpamf.ms.mallorder.po.OrderProductPO;

import java.util.List;
import java.util.Map;


public interface IOrderLogisticItemService extends IService<OrderLogisticItemPO> {

    void batchSave(String packageSn, List<OrderProductPO> orderProductPOList, Map<Long, OrderProductDeliverDTO> orderProductDeliverDTOMap, String operator);

    List<OrderLogisticItemPO> getOrderLogisticItemListByPackageSn(String packageSn);

    List<OrderLogisticItemPO> getOrderLogisticItemListByOrderProductId(Long orderProductId);
}
