package com.cfpamf.ms.mallorder.integration.facade;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.mallorder.integration.cashier.request.PushOrderRequest;
import com.cfpamf.ms.mallorder.integration.facade.dto.PayConfigDTO;
import com.cfpamf.ms.mallorder.vo.LargeOrderRecvVO;
import com.cfpamf.ms.mallorder.vo.PayerLargePayacctVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @Create 2022-09-08 10:40
 * @Description :收银台外部接口
 */
@FeignClient(value = "cashier-service", url = "${cashier-service.url}", name = "cashier-service")
public interface CashierFacade {

    /**
     * @param pushOrder
     * @return Result<Boolean>
     * @description : 推送业务订单信息到收银台
     */
    @PostMapping("/cashier/bizorder/dopush")
    Result<Boolean> pushBizOrder(@RequestBody PushOrderRequest pushOrder);

    /**
     * 设置灰度配置开关
     *
     * @return
     */
    @PostMapping("/cashier/pay-config/set")
    Result<Boolean> configSet(@RequestBody PayConfigDTO payConfigDTO);

    /**
     * 删除灰度配置开关
     * @param configKey
     * @param systemCode
     * @return
     */
    @PostMapping("/cashier/pay-config/delete")
    Result<Boolean> configDelete(@RequestParam("configKey") String configKey,
                                 @RequestParam("systemCode") String systemCode);

    /**
     * 查询对应大额订单支付方式默认付款银行卡
     */
    @GetMapping("/cashier/pay/large-payacct")
    Result<PayerLargePayacctVO> queryPayerLargePayacct(@RequestParam(value = "payer") String payer,
                                                       @RequestParam(value = "systemCode") String systemCode);
}
