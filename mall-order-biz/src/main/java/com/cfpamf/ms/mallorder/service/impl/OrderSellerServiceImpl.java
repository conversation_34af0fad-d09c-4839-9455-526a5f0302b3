package com.cfpamf.ms.mallorder.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cfpamf.ms.mallorder.common.enums.OrderReturnStatus;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.common.util.DateUtil;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.StoreLayoutDTO;
import com.cfpamf.ms.mallorder.model.OrderReturnModel;
import com.cfpamf.ms.mallorder.po.Order;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.service.IOrderSellerService;
import com.cfpamf.ms.mallorder.service.IOrderService;
import com.cfpamf.ms.mallorder.vo.StoreLayoutVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

@Service
public class OrderSellerServiceImpl implements IOrderSellerService {


    @Resource
    private IOrderService orderService;

    @Resource
    private OrderReturnModel orderReturnModel;


    /**
     * 判断店铺是否可以注销
     * 1.查询店铺是否有以下的订单
     * a。状态: 待付款、待签收
     * b。最新的订单交易成功时间大于 180天
     * 2，查询店铺是否有处理中的售后单，待商家审核、待平台审核。
     * @param storeLayoutDTO
     * @return
     */
    @Override
    public StoreLayoutVO layoutStore(StoreLayoutDTO storeLayoutDTO) throws Exception {
        StoreLayoutVO storeLayoutVO = new StoreLayoutVO();
        LambdaQueryWrapper<OrderPO> orderPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        orderPOLambdaQueryWrapper.eq(OrderPO::getStoreId, storeLayoutDTO.getStoreId())
                .in(OrderPO::getOrderState, storeLayoutDTO.getOrderStateList())
                .eq(OrderPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);

        int effectiveOrderCount = orderService.count(orderPOLambdaQueryWrapper);
        storeLayoutVO.setOrderCount(effectiveOrderCount);

        LambdaQueryWrapper<OrderPO> orderLambdaQueryWrapper = new LambdaQueryWrapper<>();
        orderLambdaQueryWrapper.eq(OrderPO::getStoreId, storeLayoutDTO.getStoreId())
                .eq(OrderPO::getOrderState, OrderConst.ORDER_STATE_40)
                .eq(OrderPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                .orderByDesc(OrderPO::getFinishTime)
                .last("limit 1");


        OrderPO lastFinishOrder = orderService.getOne(orderLambdaQueryWrapper);
        Order order = new Order();
        if(Objects.nonNull(lastFinishOrder)){
            BeanUtils.copyProperties(lastFinishOrder,order);

        }

        storeLayoutVO.setOrder(order);

        storeLayoutVO.setOrderReturnCount(orderReturnModel.returningOrderCount(storeLayoutDTO.getStoreId()));

        return storeLayoutVO;
    }



}
