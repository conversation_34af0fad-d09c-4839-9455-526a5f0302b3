package com.cfpamf.ms.mallorder.integration.cashier.request;

import com.alibaba.fastjson.annotation.JSONType;
import com.baomidou.mybatisplus.core.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description: 支付方式枚举
 * @Author: wangyingwei
 * @Date: 2019/12/18
 */
@AllArgsConstructor
@Getter
@JSONType(serializeEnumAsJavaBean = true)
public enum PayWayCasierEnum implements IEnum<String> {

    WXPAY("WXPAY", "微信支付"),
    WX_PAY("WXPAY", "微信支付"),
    WX_PAY_V3("WX_PAY_V3", "微信支付V3"),
    ALIPAY("ALIPAY", "支付宝"),
    ALI_PAY("ALIPAY", "支付宝"),
    ENJOY_PAY("ENJOY_PAY", "用呗支付"),
    CREDIT_PAY("CREDIT_PAY", "授信额度"),
    FOLLOW_HEART("FOLLOW_HEART", "随心取"),
    BALANCE("BALANCE", "虚拟账余额"),
    CARD_VOUCHER("CARD_VOUCHER", "卡券支付"),
    CARD("CARD", "乡助卡支付"),
    MALL_BALANCE("MALL_BALANCE", "余额"),
    AGREED_PAY("AGREED_PAY", "协议支付"),
    BANK_PAY("BANK_PAY", "银行卡转账"),
    BANK_TRANSFER("BANK_TRANSFER", "银行卡汇款"),
    COMBINATION_PAY("COMBINATION_PAY", "组合支付");


    String value;
    String desc;

    public static PayWayEnum getValue(String value){
        for(PayWayEnum ps : PayWayEnum.values()){
            if (value.equals(ps.value)){
                return ps;
            }
        }
        return null;
    }

    public static PayWayCasierEnum getValueName(String name){
        for(PayWayCasierEnum ps : PayWayCasierEnum.values()){
            if (name.equals(ps.toString())){
                return ps;
            }
        }
        return null;
    }

    public static boolean isLoanPay(PayWayCasierEnum payWay) {
        return payWay == ENJOY_PAY || payWay == CREDIT_PAY || payWay == FOLLOW_HEART;
    }

    public static boolean isThirdPartyPay(PayWayCasierEnum payWay) {
        return payWay == ALIPAY || payWay == WXPAY;
    }

}
