package com.cfpamf.ms.mallorder.service;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.cfpamf.ms.mallorder.dto.OrderSubmitDTO;
import com.cfpamf.ms.mallorder.enums.SettleModeEnum;
import com.cfpamf.ms.mallorder.integration.filecenter.SceneTypeEnum;
import com.cfpamf.ms.mallorder.po.Cart;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.vo.OrderProductDeliveryVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/*
现款现货业务接口
 */
public interface IFundsBorrowBizService {

    static Result<List<OrderSubmitDTO.OrderInfo.OrderProductInfo>> verifyCartOrder(List<OrderSubmitDTO.OrderInfo> orderInfoList) {
        Result<List<OrderSubmitDTO.OrderInfo.OrderProductInfo>> result = new Result<>();
        if (CollectionUtils.isEmpty(orderInfoList)) {
            result.setSuccess(true);
            result.setData(Collections.emptyList());
            return result;
        }
        boolean haveFundsBorrow = false;
        for (OrderSubmitDTO.OrderInfo itemOrderInfo : orderInfoList) {
            if (SettleModeEnum.BORROW.getCode().equals(itemOrderInfo.getSettleMode())) {
                haveFundsBorrow = true;
                break;
            }
        }
        if (haveFundsBorrow && orderInfoList.size() > 1) {
            result.setSuccess(false);
            result.addError(String.valueOf(ErrorCodeEnum.U.CHECK_FAILURE.getCode()),
                    "现款现货模式的商品不允许加入购物车后进行下单", "");
            return result;
        }
        List<OrderSubmitDTO.OrderInfo.OrderProductInfo> fundsBorrowProductList = new ArrayList<>();
        Result<List<OrderSubmitDTO.OrderInfo.OrderProductInfo>> verifyProductResult;
        for (OrderSubmitDTO.OrderInfo itemOrderInfo : orderInfoList) {
            verifyProductResult = verifyCartProduct(itemOrderInfo.getOrderProductInfoList());
            if (!verifyProductResult.isSuccess()) {
                fundsBorrowProductList.addAll(verifyProductResult.getData());
            }
        }
        if (fundsBorrowProductList.size() < 1) {
            result.setSuccess(true);
            result.setData(Collections.emptyList());
            return result;
        }
        StringBuilder fundsBorrowGoodsStrBuild = new StringBuilder();
        for (OrderSubmitDTO.OrderInfo.OrderProductInfo itemProduct : fundsBorrowProductList) {
            if (NumberUtils.INTEGER_ONE.equals(itemProduct.getFundsBorrowable())) {
                fundsBorrowGoodsStrBuild.append("【").append(itemProduct.getGoodsName()).append("】");
            }
        }
        result.setSuccess(false);
        result.addError(String.valueOf(ErrorCodeEnum.U.CHECK_FAILURE.getCode()),
                String.format("现款现货模式的商品%s仅允许单独购买", fundsBorrowGoodsStrBuild), "");
        result.setData(fundsBorrowProductList);
        return result;
    }

    static Result<List<Cart>> verifyCart(List<Cart> cartList) {
        Result<List<Cart>> result = new Result<>();
        if (CollectionUtils.isEmpty(cartList)) {
            result.setSuccess(true);
            result.setData(Collections.emptyList());
            return result;
        }
        if (cartList.size() == 1) {
            result.setSuccess(true);
            result.setData(cartList);
            return result;
        }
        List<Cart> fundsBorrowProductList = new ArrayList<>();
        StringBuilder fundsBorrowGoodsStrBuild = new StringBuilder();
        for (Cart itemCart : cartList) {
            if (NumberUtils.INTEGER_ONE.equals(itemCart.getFundsBorrowable())) {
                fundsBorrowGoodsStrBuild.append("【").append(itemCart.getGoodsName()).append("】");
                fundsBorrowProductList.add(itemCart);
            }
        }
        if (fundsBorrowProductList.size() < 1) {
            result.setSuccess(true);
            result.setData(Collections.emptyList());
            return result;
        }
        result.setSuccess(false);
        result.addError(String.valueOf(ErrorCodeEnum.U.CHECK_FAILURE.getCode()),
                String.format("现款现货的商品%s仅允许单独购买", fundsBorrowGoodsStrBuild), "");
        result.setData(fundsBorrowProductList);
        return result;
    }

    static Result<List<OrderSubmitDTO.OrderInfo.OrderProductInfo>> verifyCartProduct(List<OrderSubmitDTO.OrderInfo.OrderProductInfo> productList) {
        Result<List<OrderSubmitDTO.OrderInfo.OrderProductInfo>> result = new Result<>();
        if (CollectionUtils.isEmpty(productList)) {
            result.setSuccess(true);
            result.setData(Collections.emptyList());
            return result;
        }
        if (productList.size() == 1) {
            result.setSuccess(true);
            result.setData(productList);
            return result;
        }
        List<OrderSubmitDTO.OrderInfo.OrderProductInfo> fundsBorrowProductList = new ArrayList<>();
        StringBuilder fundsBorrowGoodsStrBuild = new StringBuilder();
        for (OrderSubmitDTO.OrderInfo.OrderProductInfo itemProduct : productList) {
            if (NumberUtils.INTEGER_ONE.equals(itemProduct.getFundsBorrowable())) {
                fundsBorrowGoodsStrBuild.append("【").append(itemProduct.getGoodsName()).append("】");
                fundsBorrowProductList.add(itemProduct);
            }
        }
        if (fundsBorrowProductList.size() < 1) {
            result.setSuccess(true);
            result.setData(Collections.emptyList());
            return result;
        }
        result.setSuccess(false);
        result.addError(String.valueOf(ErrorCodeEnum.U.CHECK_FAILURE.getCode()),
                String.format("现款现货模式的商品%s仅允许单独购买", fundsBorrowGoodsStrBuild), "");
        result.setData(fundsBorrowProductList);
        return result;
    }

    Result<Void> verifyOrderReceive(OrderPO orderPO,SceneTypeEnum sceneTypeEnum);

    Result<Void> verifyOrderDelivery(OrderPO orderPO, SceneTypeEnum sceneTypeEnum);

    Result<Integer> fundsBorrowExpireAlert(List<OrderProductDeliveryVO> orderProductPOList, String alertType);

    Result<Integer> fundsBorrowCloseStoreTrade(List<OrderProductDeliveryVO> orderProductPOList);
}
