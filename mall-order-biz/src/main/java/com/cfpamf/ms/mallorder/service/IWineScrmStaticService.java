package com.cfpamf.ms.mallorder.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cfpamf.ms.mallorder.req.pgrpt.IndicatorsRequest;
import com.cfpamf.ms.mallorder.req.pgrpt.PkMatchOpponentsRequest;
import com.cfpamf.ms.mallorder.vo.pgrpt.IndicatorsResponse;
import com.cfpamf.ms.mallorder.vo.pgrpt.PkMatchOpponentsResponse;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public interface IWineScrmStaticService {
	/**
	 * 随机/手动匹配对手
	 * @param request
	 * @return
	 */
	Page<PkMatchOpponentsResponse> matchOpponents(PkMatchOpponentsRequest request);

	/**
	 * 查询员工的历史战力值
	 * 查询上月业绩
	 * @param request
	 * @return
	 */
	List<IndicatorsResponse> queryPkHistoryIndicators(IndicatorsRequest request);


	/**
	 * 查询员工的实时战力值
	 * 查询当月最新业绩
	 * @param request
	 * @return
	 */
	List<IndicatorsResponse> queryPkRealTimeIndicators(IndicatorsRequest request);

	/**
	 * 查询PK结果
	 * 查询pk时间内当月业绩
	 * @param request
	 * @return
	 */
	List<IndicatorsResponse> pkSettlement(IndicatorsRequest request);
}
