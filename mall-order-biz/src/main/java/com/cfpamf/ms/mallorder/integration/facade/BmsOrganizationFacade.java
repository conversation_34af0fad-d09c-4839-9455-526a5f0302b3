package com.cfpamf.ms.mallorder.integration.facade;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.bms.facade.constants.BmsConstantCore;
import com.cfpamf.ms.bms.facade.constants.BmsOrgLevelEnum;
import com.cfpamf.ms.bms.facade.dto.ElementTreeNodeDTO;
import com.cfpamf.ms.bms.facade.query.FdOrganizationQuery;
import com.cfpamf.ms.bms.facade.query.OrgTimeWindowQuery;
import com.cfpamf.ms.bms.facade.vo.*;
import com.cfpamf.ms.mallorder.vo.BmsOrganizationBaseVO;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 外部服务接入bms组织接口
 **/
@FeignClient(name = "bms-service-biz", url = "${bms.api.url}")
public interface BmsOrganizationFacade {

    @ApiOperation(value = "获取整个组织机构树")
    @GetMapping(value = "/org/getOrgTree")
    Result<List<ElementTreeNodeDTO>> getOrganizationTree(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization);

    @ApiOperation("获取组织机构管理人员信息")
    @PostMapping(value = "/org/list/detail")
    Result<List<FdOrgEmployeeVO>> getOrganizationInfos(@RequestBody FdOrganizationQuery dto);

    @ApiOperation("获取区域分支树信息")
    @GetMapping(value = "/org/regions")
    Result<List<FdOrgVO>> getAllRegionBranches(@RequestParam(value = "includeHeadOrg") boolean includeHeadOrg);

    @ApiOperation("根据OrgId获取组织机构详情")
    @GetMapping(value = "/org/getOrganizationByOrgId/{orgId}")
    Result<OrganizationVO> getOrganizationByOrgId(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization, @PathVariable("orgId") int orgId);

    @ApiOperation("根据hrOrgId获取组织机构详情")
    @GetMapping(value = "/org/getOrganizationByHrOrgId/{hrOrgId}")
    Result<OrganizationVO> getOrganizationByHrOrgId(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization, @PathVariable(value = "hrOrgId") int hrOrgId);

    @ApiOperation("根据orgCode获取组织机构详情")
    @GetMapping(value = "/org/getOrganizationByOrgCode/{orgCode}")
    Result<OrganizationVO> getOrganizationByOrgCode(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization, @PathVariable("orgCode") String orgCode);

    @ApiOperation(value = "判断是否是区域管理部", notes = "/org/isAreaOffice?orgId=xxx")
    @GetMapping(value = "/org/isAreaOffice")
    Result<Boolean> checkOrgIsAreaOffice(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization, @RequestParam(value = "orgId") Integer orgId);

    @ApiOperation("判断业务是否是总部")
    @GetMapping(value = "/org/ishead")
    Result<Boolean> checkOrgIsHead(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization, @RequestParam(value = "treePath") String treePath);

    @ApiOperation("根据区域合作伙伴的employeeId获取所管辖区域的基本信息")
    @GetMapping(value = "/org/areas/partners/{employeeId}/list")
    Result<List<OrganizationBaseVO>> listAreaBaseVOByAreaPartnerEmployeeId(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization, @PathVariable(value = "employeeId") Integer employeeId);

    @ApiOperation("根据名称获取组织机构信息")
    @GetMapping(value = "/org/getOrganizationByName")
    Result<OrganizationBaseVO> getOrganizationByName(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization, @RequestParam(value = "orgName") String orgName);

    @ApiOperation("根据名称模糊匹配组织机构信息列表")
    @GetMapping(value = "/org/listOrganizationByNames")
    Result<List<OrganizationBaseVO>> listOrganizationByNames(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization, @RequestParam(value = "orgNames") List<String> orgNames);

    @ApiOperation(value = "获取HR的区域机构Id下的区域信息")
    @GetMapping(value = "/org/listAreasByAreaHrOrgIds")
    Result<List<OrganizationBaseVO>> listAreasByAreaHrOrgIds(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization, @RequestParam(value = "areaHrOrgIds") List<Integer> areaHrOrgIds);

    @ApiOperation("根据传入的orgIds获取Organization信息列表")
    @GetMapping(value = "/org/listOrganizationBaseVOByOrgIds")
    Result<List<OrganizationBaseVO>> listOrganizationBaseVOByOrgIds(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization, @RequestParam("orgIds") List<Integer> orgIds);

    @ApiOperation(value = "获取传入的orgCodes获取Organization信息列表")
    @GetMapping(value = "/org/listOrganizationBaseVOByOrgCodes")
    Result<List<OrganizationBaseVO>> listOrganizationBaseVOByOrgCodes(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization, @RequestParam("orgCodes") List<String> orgCodes);

    @ApiOperation(value = "获取传入的hrOrgIds获取Organization信息列表")
    @GetMapping(value = "/org/listOrganizationBaseVOByHrOrgIds")
    Result<List<OrganizationBaseVO>> listOrganizationBaseVOByHrOrgIds(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization, @RequestParam("hrOrgIds") List<Integer> hrOrgIds);

    @ApiOperation(value = "获取所有的Organization信息列表")
    @GetMapping(value = "/org/listOrganizationBaseVO")
    Result<List<OrganizationBaseVO>> listOrganizationBaseVO(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization);

    @ApiOperation(value = "根据hrOrgId(可能为分支的，也可能为区域办公室的，也可能直管的)获取区域信息")
    @GetMapping(value = "/org/{hrOrgId}/area")
    Result<OrganizationBaseVO> getAreaByHrOrgId(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization, @PathVariable("hrOrgId") Integer hrOrgId);

    @ApiOperation(value = "根据hrOrgId所对应的级别获取对应下的所有子孙的分支信息")
    @GetMapping(value = "/org/listBranchesByHrOrgIds")
    Result<List<OrganizationBaseVO>> listBranchesByHrOrgIds(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization, @RequestParam(value = "hrOrgIds") List<Integer> hrOrgIds);

    @ApiOperation(value = "根据hrParentId获取对应下的所有子孙的分支信息")
    @GetMapping(value = "/org/{hrParentId}/branches")
    Result<List<OrganizationBaseVO>> listBranchesByHrParentId(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization, @PathVariable(value = "hrParentId") Integer hrParentId);

    @ApiOperation(value = "获取区域列表")
    @GetMapping(value = "/org/getAreaList")
    Result<List<AreaVO>> getAreaList(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization);

    @ApiOperation(value = "获取区域分支树")
    @GetMapping(value = "/org/getAreaBranchTree")
    Result<List<ElementTreeNodeDTO>> getAreaBranchTree(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization, @RequestParam("includeHeadOrg") boolean includeHeadOrg);

    @ApiOperation(value = "获取当前组织机构类型(未知:Unknown,总部:Head,区域:Area,分支:Branch,片区/直管:Zone)")
    @GetMapping(value = "/org/getOrgLevelByOrgId")
    Result<BmsOrgLevelEnum> getOrgLevelByOrgId(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization, @RequestParam("orgId") Integer orgId);

    @ApiOperation(value = "按时间窗口获取组织机构页列表")
    @PostMapping(value = "/org/timewindow/search")
    Result<PageInfo<OrganizationVO>> findOrganizationByTimeWindow(@RequestBody OrgTimeWindowQuery query);

    @ApiOperation(value = "根据orgCode获取当前机构信息,如果当前机构是区域管理部则返回对应的区域信息，其他不变")
    @GetMapping(value = "/org/getBizOrgBaseVOByOrgCode")
    Result<OrganizationBaseVO> getBizOrgBaseVOByOrgCode(@RequestParam("orgCode") String orgCode);

    @ApiOperation(value = "根据机构编码获取以此机构为根节点的区域（总部级区域管理部）、片区、分支树结构信息")
    @GetMapping(value = "/org/listRootHeadBuAreaBranchBySuperOrgCode")
    Result<List<ElementTreeNodeDTO>> listRootHeadBuAreaBranchBySuperOrgCode(@RequestParam("orgCode") String orgCode);

    @ApiOperation(value = "根据机构编码获取以此机构为根节点的区域（分支级区域）、片区、分支树结构信息")
    @GetMapping(value = "/org/listRootBranchBuAreaBranchBySuperOrgCode")
    Result<List<ElementTreeNodeDTO>> listRootBranchBuAreaBranchBySuperOrgCode(@RequestParam("orgCode") String orgCode);

    @ApiOperation(value = "获取整个组织机构树（子级中包含直管部门，如：信息技术部下包含：信息技术部、开发中心等）")
    @GetMapping(value = "/org/getOrgTree/includeDirectDept")
    Result<List<ElementTreeNodeDTO>> getOrganizationTreeIncludeDirectDept(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization);

    @ApiOperation(value = "根据(总部/分支)事业部Id获取所管辖的区域列表")
    @GetMapping(value = "/org/listAreaByBuOrgId/{orgId}")
    @Deprecated
    Result<List<OrganizationBaseVO>> listAreaByBuOrgId(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization, @PathVariable("orgId") Integer orgId);

    @ApiOperation(value = "获取所有的事业部(总部事业部)列表，无权限控制")
    @GetMapping(value = "/org/listRootHeadBu")
    @Deprecated
    Result<List<OrganizationBaseVO>> listRootHeadBu(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization);

    @ApiOperation(value = "获取所有的事业部(分支事业部)列表，无权限控制")
    @GetMapping(value = "/org/listRootBranchBu")
    @Deprecated
    Result<List<OrganizationBaseVO>> listRootBranchBu(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization);

    @ApiOperation(value = "根据登录用户信息获取用户有权限查看的事业部(总部事业部)列表,总部非事业部部门则返回所有事业部")
    @GetMapping(value = "/org/listRootHeadBuByTokenTreePath")
    @Deprecated
    Result<List<OrganizationBaseVO>> listRootHeadBuByTokenTreePath(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization);

    @ApiOperation(value = "根据hrOrgCode所对应的级别获取对应下的分支负责人")
    @PostMapping(value = "/org/listHeadersByHrOrgCodes")
    Result<List<UserListVO>> listHeadersByHrOrgCodes(@RequestBody List<String> hrOrgCodes);

    @PostMapping("/org/hrOrgCode/areas")
    @ApiOperation("根据hrOrgCode(分支)获取分支、所属片区、所属区域信息")
    Result<List<BmsOrganizationBaseVO>> getBmsOrganizationBaseVOByOrgCode(@RequestBody List<String> hrOrgCodes);
}

