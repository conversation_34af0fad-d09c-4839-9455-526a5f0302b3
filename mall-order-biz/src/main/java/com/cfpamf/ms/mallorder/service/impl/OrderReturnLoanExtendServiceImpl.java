package com.cfpamf.ms.mallorder.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cfpamf.mallpayment.facade.vo.CDMallRefundTryResultVO;
import com.cfpamf.ms.mallorder.builder.OrderReturnLoanExtendBuilder;
import com.cfpamf.ms.mallorder.mapper.OrderReturnLoanExtendMapper;
import com.cfpamf.ms.mallorder.po.OrderReturnLoanExtendPO;
import com.cfpamf.ms.mallorder.service.IOrderReturnLoanExtendService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;


/**
 * 订单售后贷款类信息扩展表 SERVICE 实现类
 */
@Slf4j
@Service
public class OrderReturnLoanExtendServiceImpl extends ServiceImpl<OrderReturnLoanExtendMapper, OrderReturnLoanExtendPO> implements IOrderReturnLoanExtendService {

    /**
     * 保存/更新售后贷款类信息
     */
    @Override
    public boolean saveUpdateOrderReturnLoanExtend(String orderSn, String afsSn, String bizSn, CDMallRefundTryResultVO refundTryResult) {

        log.info("##########OrderReturnLoanExtendServiceImpl.saveUpdateOrderReturnLoanExtend, orderSn:{}, afsSn:{}, bizSn:{}, cdMallRefundTryResultVO:{}",
                orderSn, afsSn, bizSn, refundTryResult);

        // 1、判断数据是否存在：不存在插入，存在更新
        Integer count = this.lambdaQuery().eq(OrderReturnLoanExtendPO::getOrderSn, orderSn)
                .eq(OrderReturnLoanExtendPO::getAfsSn, afsSn)
                .eq(OrderReturnLoanExtendPO::getBizSn, bizSn).count();

        log.info("##########OrderReturnLoanExtendServiceImpl.saveUpdateOrderReturnLoanExtend, count:{}", count);

        // 2、存在，则更新
        if (count > 0) {
            return this.lambdaUpdate().eq(OrderReturnLoanExtendPO::getOrderSn, orderSn)
                    .eq(OrderReturnLoanExtendPO::getAfsSn, afsSn)
                    .eq(OrderReturnLoanExtendPO::getBizSn, bizSn)
                    .set(OrderReturnLoanExtendPO::getLoanNo, refundTryResult.getLoanNo())
                    .set(OrderReturnLoanExtendPO::getSumAmount, refundTryResult.getSumAmount())
                    .set(OrderReturnLoanExtendPO::getRepayPrincipal, refundTryResult.getRepayPrincipal())
                    .set(OrderReturnLoanExtendPO::getRepayInterest, refundTryResult.getRepayInterest())
                    .set(OrderReturnLoanExtendPO::getRepayOverdue, refundTryResult.getRepayOverdue())
                    .set(OrderReturnLoanExtendPO::getRepayPrincipalOverdue, refundTryResult.getRepayPrincipalOverdue())
                    .set(OrderReturnLoanExtendPO::getRepayInterestOverdue, refundTryResult.getRepayInterestOverdue())
                    .set(OrderReturnLoanExtendPO::getRepayOverdueInterestOverdue, refundTryResult.getRepayOverdueInterestOverdue())
                    .set(OrderReturnLoanExtendPO::getUpdateTime, new Date())
                    .update();
        }

        // 3、不存在，则插入
        return this.save(OrderReturnLoanExtendBuilder.buildOrderReturnLoanExtendPO(orderSn, afsSn, bizSn, refundTryResult));

    }

    /**
     * 根据售后业务单号查询，售后贷款类信息
     */
    public OrderReturnLoanExtendPO getOrderReturnLoanExtendPOBySn(String orderSn, String afsSn, String bizSn) {
        return this.lambdaQuery().eq(OrderReturnLoanExtendPO::getOrderSn, orderSn)
                .eq(OrderReturnLoanExtendPO::getAfsSn, afsSn)
                .eq(OrderReturnLoanExtendPO::getBizSn, bizSn).one();
    }

}
