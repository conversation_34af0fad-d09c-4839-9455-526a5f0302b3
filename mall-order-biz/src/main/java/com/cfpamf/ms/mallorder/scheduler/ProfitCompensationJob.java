package com.cfpamf.ms.mallorder.scheduler;

import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.mallorder.common.enums.TaskQueueBizTypeEnum;
import com.cfpamf.ms.mallorder.integration.pay.PayIntegration;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.po.TaskQueuePO;
import com.cfpamf.ms.mallorder.service.ITaskQueueService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 分账结算补偿
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/9 16:17
 */
@Component
@Slf4j
public class ProfitCompensationJob {
    @Autowired
    private OrderModel orderModel;
    @Autowired
    private PayIntegration payIntegration;
    @Autowired
    private ITaskQueueService taskQueueService;

    @XxlJob(value = "ProfitCompensationJob")
    public ReturnT<String> execute(String s) {

        long startTime = System.currentTimeMillis();
        XxlJobHelper.log("start job at local time: {}", LocalDateTime.now());

        log.info("profitCompensationJob() start");
        String dateFormat = DateUtil.format(new Date(), DateUtil.CN_LONG_FORMAT);
        // 查询定时任务，进行发起补偿
        List<TaskQueuePO> taskQueuePOS = taskQueueService.listTodoTask(TaskQueueBizTypeEnum.PROFIT_COMP, dateFormat);

        for (TaskQueuePO taskQueuePo : taskQueuePOS) {
            try {
                payIntegration.profitSharding(taskQueuePo.getBizId() + "");
                taskQueueService.taskSuccess(taskQueuePo);
            } catch (Exception e){
                log.error("profitSharding()订单微信分账补偿失败", e);
                taskQueueService.taskFail(taskQueuePo, "订单微信分账补偿失败", e.getMessage());
            }
        }

        XxlJobHelper.log("finish job at local time: {}  cost:{}",
                LocalDateTime.now(),
                (System.currentTimeMillis() - startTime) / 1000);

        return new ReturnT("SUCCESS");
    }
}
