package com.cfpamf.ms.mallorder.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.mallpayment.facade.api.MallPaymentFacade;
import com.cfpamf.mallpayment.facade.request.CDMallRefundTryRequest;
import com.cfpamf.mallpayment.facade.vo.CDMallRefundTryResultVO;
import com.cfpamf.ms.loan.facade.request.external.mall.RedrawCdMallApplyRequest;
import com.cfpamf.ms.loan.facade.request.external.mall.RedrawCdMallItem;
import com.cfpamf.ms.mall.account.constant.AccountConstans;
import com.cfpamf.ms.mall.account.enums.AccountCardTypeEnum;
import com.cfpamf.ms.mall.account.vo.AccountCard;
import com.cfpamf.ms.mallorder.common.enums.LoanCalculateErrorCodeEnum;
import com.cfpamf.ms.mallorder.common.enums.LoanFailureTypeEnum;
import com.cfpamf.ms.mallorder.common.enums.LoanStatusEnum;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.agricorder.LoanListResultQueryDTO;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.integration.agricorder.AgricOrderIntegration;
import com.cfpamf.ms.mallorder.integration.loan.LoanPayIntegration;
import com.cfpamf.ms.mallorder.integration.settlement.BillOperatinIntegration;
import com.cfpamf.ms.mallorder.mapper.LoanResultMapper;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.po.LoanResultPO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderPresellPO;
import com.cfpamf.ms.mallorder.req.admin.LoanListResultRequest;
import com.cfpamf.ms.mallorder.service.ILoanResultService;
import com.cfpamf.ms.mallorder.service.IOrderService;
import com.cfpamf.ms.mallorder.v2.service.OrderPresellService;
import com.cfpamf.ms.mallorder.vo.LoanResultVo;
import com.cfpamf.ms.mallorder.vo.agricorder.AgricLoanResultVo;
import com.cfpamf.ms.mallshop.api.StoreFeignClient;
import com.cfpamf.ms.mallshop.resp.StoreContractReceiptInfoVO;
import com.cfpamf.ms.mallsystem.vo.Admin;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.exception.MallException;
import com.slodon.bbc.core.response.PageVO;
import com.slodon.bbc.core.response.PagerInfo;
import freemarker.core.BugException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-17
 */
@Service
@Slf4j
public class LoanResultServiceImpl extends ServiceImpl<LoanResultMapper, LoanResultPO> implements ILoanResultService {

    @Resource
    private LoanResultMapper loanResultMapper;

    @Resource
    private IOrderService orderService;
    @Autowired
    private OrderPresellService orderPresellService;

    @Resource
    private OrderModel orderModel;

    @Resource
    private LoanPayIntegration loanPayIntegration;

    @Resource
    private BillOperatinIntegration billOperatinIntegration;
    @Autowired
    private MallPaymentFacade mallPaymentFacade;


    @Resource
    private StoreFeignClient storeFeignClient;
    @Autowired
    private ILoanResultService loanResultService;

    @Autowired
    private AgricOrderIntegration agricOrderIntegration;

    public PageVO<LoanResultVo> queryLoanResultPage(PagerInfo pager, LoanListResultRequest request) {
        BizAssertUtil.notNull(pager, "分页信息为空");
        pager.setRowsCount(loanResultMapper.countByRequest(request));
        List<LoanResultVo> loanResultVos = loanResultMapper.listPageByRequest(request, pager.getStart(), pager.getPageSize());
        return new PageVO<>(loanResultVos, pager);
    }

    @Override
    public boolean orderAutoLoanLendingMakeUpJob() {
        Admin admin = new Admin();
        admin.setAdminId(1);
        admin.setAdminName("system");
        List<LoanResultVo> resultVos = loanResultMapper.listAutoLoanLendingMakeUp();
        for (LoanResultVo resultVo : resultVos) {
            try {
                loanResultService.loanRepay(admin, resultVo.getPayNo());
            } catch (Exception e) {
                log.warn("自动处理代付时异常,orderSn:{},message:{}", resultVo.getPayNo(), e.getMessage());
            }
        }
        return true;
    }

    @Override
    @Transactional
    public Boolean loanRepay(Admin admin, String orderSn) {
        // 查询放款结果单
        LambdaQueryWrapper<LoanResultPO> loanResultQueryWrapper = Wrappers.lambdaQuery(LoanResultPO.class);
        loanResultQueryWrapper.eq(LoanResultPO::getPayNo, orderSn)
                .eq(LoanResultPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                .last("limit 1");
        LoanResultPO loanResultPo = super.getOne(loanResultQueryWrapper);

        // 对订单进行校验
        OrderPO orderPo = orderService.getByOrderSn(orderSn);

        // 重新放款校验
        this.loanRepayCheck(orderPo, loanResultPo);

        // 根据失败的类型（区分信贷域是否生成相关的数据信息），做重新代付，或者重新发起
        Result result = new Result();
        result.setSuccess(false);
        if (LoanFailureTypeEnum.CALL_BACK_FAILURE.getCode().equals(loanResultPo.getFailureType())) {
            // 回调失败补偿：重新代付
            try {
                result = this.callbackFailRepay(admin, orderSn);
            } catch (Exception e) {
                String errorMessage = String.format("订单%s发起重新代付执行失败，请联系技术人员~", orderSn);
                log.warn(errorMessage, e);
                throw new BusinessException(errorMessage);
            }
        } else if (LoanFailureTypeEnum.APPLY_FAILURE.getCode().equals(loanResultPo.getFailureType())) {
            // 申请失败补偿：重新发起
            try {
                result = this.applyFailRepay(admin, orderPo);
                if (!result.isSuccess()) {
                    // 非系统异常导致放款失败，记录日志，人工处理
                    orderModel.loanPayFail(orderPo, result.getErrorMsg());
                } else {
                    orderModel.loanPaySuccess(orderPo, OrderConst.LOG_ROLE_MEMBER, Long.valueOf(orderPo.getMemberId()), orderPo.getMemberName());
                }
            } catch (Exception e) {
                String errorMessage = String.format("订单%s发起重新申请执行失败，请联系技术人员~", orderSn);
                log.warn(errorMessage, e);
                throw new BusinessException(errorMessage);
            }
        }
        return result.isSuccess();
    }

    /**
     * 获取单号
     *
     * @param orderPo   订单信息
     * @return          用呗单号
     */
    private String loanRepayDealPayNo(OrderPO orderPo) {

        return orderPo.getOrderSn();
    }

    /**
     * 重新放款校验
     *
     * @param orderPo           订单信息
     * @param loanResultPo      放款单信息
     */
    private void loanRepayCheck(OrderPO orderPo, LoanResultPO loanResultPo) {
        // 对放款结果校验
        BizAssertUtil.notNull(loanResultPo, "放款信息不存在");
        BizAssertUtil.isTrue(!LoanResultPO.LOAN_RESULT_N.equals(loanResultPo.getLoanResult()),
                String.format("单号【%s】状态错误，当前状态" +  loanResultPo.getLoanResult(), loanResultPo.getPayNo()));
        log.info(" >>>>>>>>>>>>>>>>loanRepay, 放款结果对象信息【{}】", loanResultPo.toString());

        // 对订单进行校验
        BizAssertUtil.isTrue(orderPo.getLoanPayState().equals(LoanStatusEnum.DEAL_LENDING.getValue())
                        || orderPo.getLoanPayState().equals(LoanStatusEnum.LENDING_SUCCESS.getValue()),
                String.format("订单放款状态为:%s 不允许放款申请", orderPo.getLoanPayState()));

        // 对放款失败类型校验
        BizAssertUtil.isTrue(!LoanFailureTypeEnum.isLoanFailureTypeCode(loanResultPo.getFailureType()), "未知的放款失败类型");

    }

    /**
     * 对回调告知放款失败的放款单重新发起
     *
     * @param admin     操作人信息
     * @param payNo     操作单
     * @return          处理结果
     */
    private Result callbackFailRepay(Admin admin, String payNo) {
        Result result = this.reLendingByOrderSn(payNo, admin.getAdminName());
        // 更新记录信息
        LambdaUpdateWrapper<LoanResultPO> loanResultUpdateWrapper = Wrappers.lambdaUpdate(LoanResultPO.class);
        loanResultUpdateWrapper.eq(LoanResultPO::getPayNo, payNo)
                .eq(LoanResultPO::getLoanResult, LoanResultPO.LOAN_RESULT_N)
                .set(LoanResultPO::getUpdateBy, admin.getAdminName())
                .set(LoanResultPO::getUpdateTime, new Date())
                .setSql("retry_times = retry_times + 1");
        if (result.isSuccess()) {
            loanResultUpdateWrapper.set(LoanResultPO::getLoanResult, LoanResultPO.LOAN_RESULT_P);
        }
        this.update(loanResultUpdateWrapper);
        return result;
    }

    /**
     * 对发起放款失败的单子重新发起
     *
     * @param admin   操作人信息
     * @param orderPo
     * @return 处理结果
     */
    private Result applyFailRepay(Admin admin, OrderPO orderPo) {
        String preSellPayNo = null;
        if (OrderTypeEnum.isPresell(orderPo.getOrderType())) {
            OrderPresellPO orderPresellPO = orderPresellService.queryBalanceInfoByOrderSn(orderPo.getOrderSn());
            BizAssertUtil.isTrue(orderPresellPO == null || orderPresellPO.getPaySn() == null, "抱歉，预售订单payNo为空，放款异常，请联系管理员！");
            preSellPayNo = orderPresellPO.getPayNo();
        }
        return orderModel.doLoanOperate(orderPo.getOrderSn(), (long) admin.getAdminId(), preSellPayNo);
    }

    @Override
    public Result reLendingByOrderSn(String payNo, String operator) {
        // 重新推送放款
        RedrawCdMallApplyRequest redrawRequest = new RedrawCdMallApplyRequest();
        redrawRequest.setOperator(operator);
        RedrawCdMallItem item = new RedrawCdMallItem();
        item.setOrderBatchId(payNo);
        // 查询订单信息
        OrderPO orderPo = orderService.getByOrderSn(payNo);
        if (OrderTypeEnum.isPresell(orderPo.getOrderType())) {
            OrderPresellPO orderPresellPO = orderPresellService.queryBalanceInfoByOrderSn(orderPo.getOrderSn());
            BizAssertUtil.isTrue(orderPresellPO == null || orderPresellPO.getPaySn() == null, "抱歉，预售订单payNo为空，放款异常，请联系管理员！");
            String preSellPayNo = orderPresellPO.getPayNo();
            item.setOrderBatchId(preSellPayNo);
        }
        // 查询卡号信息
        if (orderPo.getNewOrder()) {
            AccountCard accountCard = billOperatinIntegration.detailByBankAccount(AccountConstans.UNI_PLF_STORE_ID, AccountCardTypeEnum.UNI_JS_PLF_SUP);
            item.setRedrawBankcardId(accountCard.getLoanCardId());
        } else {
            StoreContractReceiptInfoVO storeContractRecommend = storeFeignClient.getStoreContractReciptInfo(orderPo.getRecommendStoreId());
            item.setRedrawBankcardId(storeContractRecommend.getStore().getAcctId());
        }
        redrawRequest.setRedrawCdMallItems(Collections.singletonList(item));
        return loanPayIntegration.reLendingCdMallApply(redrawRequest);
    }


    /**
     * 售后还款金额试算
     */
    @Override
    public Result<CDMallRefundTryResultVO> getTryCaculateResult(CDMallRefundTryRequest cdmallSetlTryRequest) {
        Result<CDMallRefundTryResultVO> setlTryResultVoResult;
        log.info("试算入参:{}", JSON.toJSONString(cdmallSetlTryRequest));
        try {
            setlTryResultVoResult = mallPaymentFacade.paymentLoanSetlTry(cdmallSetlTryRequest);
        } catch (Exception ex) {
            throw new MallException(ex.getMessage());
        }
        if (setlTryResultVoResult == null) {
            throw new MallException("payment-service:试算异常,返回结果为空", ErrorCodeEnum.C.CALL_EXCEPTION.getCode());
        }

        log.info("试算结果setlTryResultVoResult:{}", setlTryResultVoResult);

        // 贷款金额/余额必须大于1元
        if ("002005099".equals(setlTryResultVoResult.getErrorCode())
                || "贷款金额/余额必须大于1元".equals(setlTryResultVoResult.getMessage().trim()) ||
                "贷款金额/余额必须大于1元".contains(setlTryResultVoResult.getMessage().trim())) {
            setlTryResultVoResult = new Result<>();
            setlTryResultVoResult.addError("002005099", "贷款金额/余额必须大于1元", "贷款金额/余额必须大于1元");
            return setlTryResultVoResult;
        }

        if (!setlTryResultVoResult.isSuccess()) {
            // 0024990099：借据已结清
            if (LoanCalculateErrorCodeEnum.ALREADY_SETTLED.getCode().equals(setlTryResultVoResult.getErrorCode())) {
                return setlTryResultVoResult;
            }
            if (LoanCalculateErrorCodeEnum.REPAY_LIMIT_THE_TIME_PERIOD.getCode().equals(setlTryResultVoResult.getErrorCode())) {
                throw new MSBizNormalException(setlTryResultVoResult.getErrorCode(), "抱歉，由于信贷规则限制，" + setlTryResultVoResult.getMessage());
            }
            if (LoanCalculateErrorCodeEnum.REPAY_LIMIT_ON_LOAN_DAY.getCode().equals(setlTryResultVoResult.getErrorCode())) {
                throw new MSBizNormalException(setlTryResultVoResult.getErrorCode(), "抱歉，由于信贷规则限制，当前贷款今日不能进行代还，请明天再试。");
            }
            throw new MSBizNormalException(setlTryResultVoResult.getErrorCode(),
                    "抱歉，由于信贷规则限制，当前贷款不能代还，请联系客服。原因：" + setlTryResultVoResult.getMessage());
        }

        if (setlTryResultVoResult.getData() == null) {
            throw new MallException("payment-service:试算异常,返回数据为空", ErrorCodeEnum.C.CALL_EXCEPTION.getCode());
        }
        return setlTryResultVoResult;
    }

    @Override
    public Page<AgricLoanResultVo> agricLoanResultPage(LoanListResultQueryDTO loanRequest) {
        return agricOrderIntegration.agricLoanResultPage(loanRequest);
    }

    @Override
    public void agricLoanRepay(String paySn, String jobNumber) {
        agricOrderIntegration.loanRepay(paySn, jobNumber);
    }

}
