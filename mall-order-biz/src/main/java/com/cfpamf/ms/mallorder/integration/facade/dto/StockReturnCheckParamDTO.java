package com.cfpamf.ms.mallorder.integration.facade.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Create 2023-08-02 13:41
 * @Description :实物库存出库校验参数
 */
@Data
public class StockReturnCheckParamDTO {

    @ApiModelProperty("业务类型 1-订单号,2-退货单号")
    private Integer businessType;

    @ApiModelProperty("业务单号,必填")
    private String businessNo;

    @ApiModelProperty("商品详情")
    private List<ErpStockProductDTO> detail;
}
