package com.cfpamf.ms.mallorder.service.payment;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.cfpamf.ms.mall.liquidate.request.RevokeReq;

@Service
public class CommonRefundHandleServiceImpl implements IRefundHandleService {
    
    @Autowired
    private IRefundSettlementService refundSettlementService;

    @Override
    public <T> void verifyElectronicAccountBalance(RetundHandleType retundHandleType, T t) {
        refundSettlementService.verifyElectronicAccountBalance((RevokeReq)t);
    }

    @Override
    public <T> Boolean issuedBill(RetundHandleType retundHandleType, T t) {
        return refundSettlementService.issuedBill((RevokeReq)t);
    }

}
