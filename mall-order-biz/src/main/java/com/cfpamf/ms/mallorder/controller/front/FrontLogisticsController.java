package com.cfpamf.ms.mallorder.controller.front;

import com.cfpamf.ms.mallLogistic.constant.LogisticConst;
import com.cfpamf.ms.mallLogistic.dto.ExpressDTO;
import com.cfpamf.ms.mallLogistic.req.ExpressTrackQuery;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.common.util.OrderLocalUtils;
import com.cfpamf.ms.mallorder.integration.logistic.LogisticIntegration;
import com.cfpamf.ms.mallorder.model.OrderAfterServiceModel;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.po.OrderAfterPO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.service.IOrderService;
import com.cfpamf.ms.mallorder.vo.ExpressLogisticsTrackVO;
import com.slodon.bbc.core.express.TracesResult;
import com.slodon.bbc.core.express.TrackUtil;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import com.slodon.bbc.core.util.AssertUtil;
import com.slodon.bbc.core.util.StringUtil;
import com.slodon.bbc.core.util.UserUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Api(tags = "front-物流信息")
@Slf4j
@RestController
@RequestMapping("front/logistics")
public class FrontLogisticsController {

    @Autowired
    private LogisticIntegration logisticIntegration;

    @Resource
    private OrderModel orderModel;
    @Resource
    private OrderAfterServiceModel orderAfterServiceModel;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private IOrderService orderService;

    @ApiOperation("订单查看物流")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderSn", value = "订单号", required = true, paramType = "query"),
            @ApiImplicitParam(name = "orderProductId", value = "订单商品ID",  required = true, paramType = "query")
    })
    @GetMapping("order/getTrace")
    public JsonResult<TracesResult> getOrderTrace(HttpServletRequest request, String orderSn, Long orderProductId) {
        Member member = UserUtil.getUser(request, Member.class);
        //查询订单信息
        OrderPO orderPO = orderModel.getOrderByOrderSn(orderSn);
        log.info("查询订单物流，订单信息：{}", orderPO);
        AssertUtil.notNull(orderPO,"此订单物流信息为空");
        AssertUtil.notNull(member, "用户信息为空，请确认是否经过网关");
        AssertUtil.isTrue(!member.getMemberId().equals(orderPO.getMemberId()), "您无权查看物流");

        List<TracesResult> tracesResultList = orderService.getOrderTrace(orderSn, orderProductId);
        return SldResponse.success(tracesResultList.get(0));

    }

    @ApiOperation("售后查看物流")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "afsSn", value = "退货单号", required = true, paramType = "query")
    })
    @GetMapping("afs/getTrace")
    public JsonResult<TracesResult> getTrace(HttpServletRequest request, String afsSn) {

        Member member = UserUtil.getUser(request, Member.class);
        OrderAfterPO orderAfterServicePO = orderAfterServiceModel.getAfterServiceByAfsSn(afsSn);
        AssertUtil.isTrue(StringUtil.isEmpty(orderAfterServicePO.getBuyerExpressCode()), "物流公司快递代码为空，请重试");
        AssertUtil.isTrue(StringUtil.isEmpty(orderAfterServicePO.getBuyerExpressNumber()), "物流单号为空，请重试");
        AssertUtil.isTrue(!member.getMemberId().equals(orderAfterServicePO.getMemberId()), "您无权查看物流");

        //获取配置表内快递鸟的EBusinessID和AppKey
        String EBusinessID = stringRedisTemplate.opsForValue().get("express_ebusinessid");
        AssertUtil.notNull(EBusinessID, "请完善快递鸟配置信息：");

        String AppKey = stringRedisTemplate.opsForValue().get("express_apikey");
        AssertUtil.notNull(AppKey, "请完善快递鸟配置信息：");

        TracesResult tracesResult = TrackUtil.getKdniaoTrack(orderAfterServicePO.getOrderSn(), orderAfterServicePO.getBuyerExpressCode(),
                orderAfterServicePO.getBuyerExpressName(), orderAfterServicePO.getBuyerExpressNumber(), EBusinessID, AppKey,
                OrderLocalUtils.subMobileLastFour(orderAfterServicePO.getContactPhone()));
        return SldResponse.success(tracesResult);
    }


    @ApiOperation("售后查看物流V2版本")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "afsSn", value = "退货单号", required = true, paramType = "query")
    })
    @GetMapping("afs/getTrace/V2")
    public JsonResult<ExpressLogisticsTrackVO> getTraceV2(HttpServletRequest request, String afsSn) {

        Member member = UserUtil.getUser(request, Member.class);
        OrderAfterPO orderAfterServicePO = orderAfterServiceModel.getAfterServiceByAfsSn(afsSn);
        AssertUtil.isTrue(StringUtil.isEmpty(orderAfterServicePO.getBuyerExpressCode()), "物流公司快递代码为空，请重试");
        AssertUtil.isTrue(StringUtil.isEmpty(orderAfterServicePO.getBuyerExpressNumber()), "物流单号为空，请重试");
        AssertUtil.isTrue(!member.getMemberId().equals(orderAfterServicePO.getMemberId()), "您无权查看物流");

        //获取配置表内快递鸟的EBusinessID和AppKey
        String EBusinessID = stringRedisTemplate.opsForValue().get("express_ebusinessid");
        AssertUtil.notNull(EBusinessID, "请完善快递鸟配置信息： express_ebusinessid");

        String AppKey = stringRedisTemplate.opsForValue().get("express_apikey");
        AssertUtil.notNull(AppKey, "请完善快递鸟配置信息： express_apikey");

        ExpressTrackQuery query = new ExpressTrackQuery();
        query.setBizCode(afsSn);
        query.setSourceChannel(LogisticConst.MALL);
        query.setLogisticCode(orderAfterServicePO.getBuyerExpressNumber());
        query.setShipperCode(orderAfterServicePO.getBuyerExpressCode());
        query.setCustomerName(OrderLocalUtils.subMobileLastFour(orderAfterServicePO.getContactPhone()));
        ExpressDTO tracks = logisticIntegration.getTracks(query);

        ExpressLogisticsTrackVO expressLogisticsTrackVO = new ExpressLogisticsTrackVO();
        expressLogisticsTrackVO.setTracesResult(tracks);
        expressLogisticsTrackVO.setExpressCompanyCode(orderAfterServicePO.getBuyerExpressCode());
        expressLogisticsTrackVO.setExpressNumber(orderAfterServicePO.getBuyerExpressNumber());
        expressLogisticsTrackVO.setExpressName(orderAfterServicePO.getBuyerExpressName());
        expressLogisticsTrackVO.setReceiverAddress(orderAfterServicePO.getStoreAfsAddress());

        return SldResponse.success(expressLogisticsTrackVO);
    }


}
