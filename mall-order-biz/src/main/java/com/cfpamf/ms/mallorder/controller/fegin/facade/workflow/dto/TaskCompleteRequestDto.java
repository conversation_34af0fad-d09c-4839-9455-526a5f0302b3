package com.cfpamf.ms.mallorder.controller.fegin.facade.workflow.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
public class TaskCompleteRequestDto extends ProcessModifyRequestDTO {
    @ApiModelProperty(value = "流程实例ID")
    private String procInstId;
    @ApiModelProperty(value = "废弃不传")
    @Deprecated
    private String processInstanceId;
    @ApiModelProperty("审批任务时提交的批注")
    private String comment;
    @ApiModelProperty("完成任务时传入的流程变量如：下个节点处理人（key为固定值”PROCESSOR_JOB_NO“，value为工号，多个用‘,’号隔开）")
    private Map<String, Object> vars = new HashMap<>();
    @ApiModelProperty("0：普通用户（默认）,1：管理员用户")
    private Integer adminFlag;

    public String getProcInstId() {
        return StringUtils.hasText(procInstId) ? procInstId : processInstanceId;
    }
}
