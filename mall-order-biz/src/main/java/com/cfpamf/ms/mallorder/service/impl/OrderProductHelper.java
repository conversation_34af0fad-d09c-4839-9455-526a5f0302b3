package com.cfpamf.ms.mallorder.service.impl;

import java.math.BigDecimal;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.cfpamf.ms.mallpromotion.api.LadderGroupGoodsFeignClient;
import com.cfpamf.ms.mallpromotion.api.PresellFeignClient;
import com.cfpamf.ms.mallpromotion.api.PresellGoodsFeignClient;
import com.cfpamf.ms.mallpromotion.api.SpellGoodsFeignClient;
import com.cfpamf.ms.mallpromotion.request.LadderGroupGoods;
import com.cfpamf.ms.mallpromotion.request.PresellGoodsExample;
import com.cfpamf.ms.mallpromotion.request.SpellGoodsExample;
import com.cfpamf.ms.mallpromotion.vo.LadderGroupGoodsVO;
import com.cfpamf.ms.mallpromotion.vo.PreSellVO;
import com.cfpamf.ms.mallpromotion.vo.PresellGoodsVO;
import com.cfpamf.ms.mallpromotion.vo.SpellGoodsVO;
import com.slodon.bbc.core.constant.PreSellConst;
import com.slodon.bbc.core.util.AssertUtil;
import com.slodon.bbc.core.util.StringUtil;

@Component
public class OrderProductHelper {

    @Resource
    private SpellGoodsFeignClient spellGoodsFeignClient;

    @Resource
    private PresellFeignClient presellFeignClient;

    @Resource
    private PresellGoodsFeignClient presellGoodsFeignClient;

    @Resource
    private LadderGroupGoodsFeignClient ladderGroupGoodsFeignClient;

    /**
     * 查询商品团购售价
     *
     * @param promotionId
     * @param productId
     * @param spellTeamId
     * @return
     */
    public BigDecimal getSpellGroupPrice(int promotionId, long productId, Long spellTeamId) {
        //查询拼团活动类型
        SpellGoodsExample example = new SpellGoodsExample();
        example.setSpellId(promotionId);
        example.setProductId(productId);
        List<SpellGoodsVO> spellGoodsList = spellGoodsFeignClient.getSpellGoodsList(example);
        AssertUtil.notEmpty(spellGoodsList, "阶梯团商品不存在");
        SpellGoodsVO spellGoods = spellGoodsList.get(0);
        //团长开团
        if (StringUtil.isNullOrZero(spellTeamId)
                && !StringUtil.isNullOrZero(spellGoods.getLeaderPrice())) {
            return spellGoods.getLeaderPrice();
        } else {
            return spellGoods.getSpellPrice();
        }
    }

    /**
     * 查询商品预售售价
     *
     * @param promotionId
     * @param productId
     * @return
     */
    public BigDecimal getPreSellPrice(int promotionId, long productId) {
        //查询预售活动类型
        PreSellVO presell = presellFeignClient.getPresellByPresellId(promotionId);
        AssertUtil.notNull(presell, "预售活动不存在");
        PresellGoodsExample example = new PresellGoodsExample();
        example.setPresellId(promotionId);
        example.setProductId(productId);
        List<PresellGoodsVO> presellGoodsList = presellGoodsFeignClient.getPresellGoodsList(example);
        AssertUtil.notEmpty(presellGoodsList, "预售商品不存在");
        if (presell.getType() == PreSellConst.PRE_SELL_TYPE_1) {
            //定金预售
            return presellGoodsList.get(0).getFirstMoney();
        } else {
            //全款预售
            return presellGoodsList.get(0).getPresellPrice();
        }
    }

    /**
     * 查询商品接阶梯团售价
     *
     * @param promotionId
     * @param productId
     * @return
     */
    public BigDecimal getLadderGroupPrice(int promotionId, long productId) {
        //查询阶梯团活动类型
        LadderGroupGoods example = new LadderGroupGoods();
        example.setGroupId(promotionId);
        example.setProductId(productId);
        List<LadderGroupGoodsVO> groupGoodsList = ladderGroupGoodsFeignClient.getLadderGroupGoodsList(example);
        AssertUtil.notEmpty(groupGoodsList, "阶梯团商品不存在");
        return groupGoodsList.get(0).getAdvanceDeposit();
    }
}
