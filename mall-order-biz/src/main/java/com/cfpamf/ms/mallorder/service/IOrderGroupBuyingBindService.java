package com.cfpamf.ms.mallorder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.ms.mallorder.dto.GroupOrderProductSubmitDTO;
import com.cfpamf.ms.mallorder.po.OrderGroupBuyingBindPO;
import com.cfpamf.ms.mallorder.req.query.OrderGroupBuyingBindQuery;
import com.cfpamf.ms.mallorder.vo.GroupOrderProductVO;

import java.util.List;

/**
 * <p>
 * 拼单满赠关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-16
 */
public interface IOrderGroupBuyingBindService extends IService<OrderGroupBuyingBindPO> {

	/**
	 * 获取满赠关联信息
	 * @param query
	 * @return
	 */
	List<OrderGroupBuyingBindPO> getGroupBuyingBindList(OrderGroupBuyingBindQuery query);

	/**
	 * 获取拼单订单商品信息
	 * @param query
	 * @return
	 */
	List<GroupOrderProductVO> listGroupOrderProduct(OrderGroupBuyingBindQuery query);

	Boolean saveOrderGroupBuyingBind(GroupOrderProductSubmitDTO submitDTO, String groupBuyingCode, String giftOrderSn);
}
