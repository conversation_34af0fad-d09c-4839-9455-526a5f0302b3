package com.cfpamf.ms.mallorder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.ms.mallorder.po.OrderExtendFinancePO;
import com.cfpamf.ms.mallorder.po.OrderPO;

/**
 * <p>
 * 订单金融信息扩展表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-22
 */
public interface IOrderExtendFinanceService extends IService<OrderExtendFinancePO> {

	OrderExtendFinancePO getByOrderSn(String orderSn);

	/**
	 * @param productId
	 * @param areaCode
	 * @param financeRuleCode
	* @return void
	* @description : 保存金融规则信息
	*/
    void insertOrderExtendFinance(Long productId, String areaCode, String financeRuleCode,String orderSn);

	/**
	 * 获取orderExtendFinance实体
	 *
	 * @param productId       货品id
	 * @param areaCode        地区编码
	 * @param financeRuleCode 金融规则
	 * @param orderPo         订单编号
	 * @param autoReceiveDays 自动收货天数
	 * @return 实体
	 */
    OrderExtendFinancePO getOrderExtendFinance(Long productId, String areaCode, String financeRuleCode, OrderPO orderPo, Integer autoReceiveDays);

	void updateAutoReceiveDays(String orderSn, Integer autoReceiveDays);

}
