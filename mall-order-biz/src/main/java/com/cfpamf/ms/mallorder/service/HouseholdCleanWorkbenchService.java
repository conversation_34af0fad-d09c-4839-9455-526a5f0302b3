package com.cfpamf.ms.mallorder.service;

import java.util.List;

import com.cfpamf.ms.mallorder.req.HouseholdCleanSalesReportRequest;
import com.cfpamf.ms.mallorder.vo.HouseholdCleanCategoryProportionSalesAmountVO;
import com.cfpamf.ms.mallorder.vo.HouseholdCleanSalesOrgPerformanceVO;
import com.cfpamf.ms.mallorder.vo.HouseholdCleanSalesReportVO;
import com.cfpamf.ms.mallorder.vo.HouseholdCleanSalesStaffPerformanceVO;
import com.cfpamf.ms.mallorder.vo.HouseholdCleanSalesItemVO;

public interface HouseholdCleanWorkbenchService {

	/**
	 * 销售报表
	 * 
	 * @param request
	 * @return
	 */
	HouseholdCleanSalesReportVO querySalesReport(HouseholdCleanSalesReportRequest request);

	/**
	 * 查询每日销售金额和数量
	 * 
	 * @param request
	 * @return
	 */
	List<HouseholdCleanSalesItemVO> querySalesAmountAndQuantity(HouseholdCleanSalesReportRequest request);

	/**
	 * 查询销售机构TOP10
	 * 
	 * @param request
	 * @return
	 */
	List<HouseholdCleanSalesOrgPerformanceVO> queryTop10SalesOrgPerformance(HouseholdCleanSalesReportRequest request);

	/**
	 * 查询销售人员TOP10
	 * 
	 * @param request
	 * @return
	 */
	List<HouseholdCleanSalesStaffPerformanceVO> queryTop10SalesStaffPerformance(
			HouseholdCleanSalesReportRequest request);

	/**
	 * 查询货品金额占比
	 * 
	 * @param request
	 * @return
	 */
	List<HouseholdCleanCategoryProportionSalesAmountVO> queryProportionSalesAmountByCategory(
			HouseholdCleanSalesReportRequest request);

}
