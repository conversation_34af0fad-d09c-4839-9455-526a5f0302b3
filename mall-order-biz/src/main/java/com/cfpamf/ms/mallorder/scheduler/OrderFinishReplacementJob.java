package com.cfpamf.ms.mallorder.scheduler;

import com.cfpamf.ms.mallorder.model.OrderReplacementModel;
import com.slodon.bbc.core.util.AssertUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 系统自动完成换货单
 * 对已发货状态的换货单发货时间超过15个自然日的换货单进行自动完成处理
 * <p>
 * cron: 0 0/10 * * * ?
 */
@Component
@Slf4j
public class OrderFinishReplacementJob {

    @Autowired
    private OrderReplacementModel orderReplacementModel;

    @XxlJob(value = "OrderFinishReplacementJob")
    public ReturnT<String> execute(String s) {

        long startTime = System.currentTimeMillis();
        XxlJobHelper.log("start job at local time: {0}", LocalDateTime.now());

        log.info("jobSystemCancelOrder() start");
        try {
            boolean jobResult = orderReplacementModel.jobSystemFinishReplacementOrder();
            AssertUtil.isTrue(!jobResult, "[jobSystemFinishReplacementOrder] 系统自动完成换货单时失败");
        } catch (Exception e) {
            log.error("jobSystemFinishReplacementOrder()", e);
        }

        XxlJobHelper.log("finish job at local time: {0}  cost:{1}",
                LocalDateTime.now(),
                (System.currentTimeMillis() - startTime) / 1000);

        return new ReturnT("SUCCESS");
    }
}
