package com.cfpamf.ms.mallorder.service.external.impl;

import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.enums.OrderEventEnum;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderReturnPO;
import com.cfpamf.ms.mallorder.service.IOrderReturnService;
import com.cfpamf.ms.mallorder.service.IOrderService;
import com.cfpamf.ms.mallorder.service.external.IMessageCompensateService;
import com.cfpamf.ms.mallorder.service.impl.OrderCreateHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 消息补偿服务实现类
 */
@Slf4j
@Service
public class MessageCompensateServiceImpl implements IMessageCompensateService {

    @Resource
    private IOrderService orderService;
    @Resource
    private IOrderReturnService orderReturnService;

    @Resource
    private OrderCreateHelper orderCreateHelper;

    @Override
    public void rabbitmqOrderEventCompensate(String orderSn, String orderEvent) {
        BizAssertUtil.isTrue(!OrderEventEnum.isForwardEvent(orderEvent), "请输入正确的正向订单事件");
        OrderPO orderPO = orderService.getByOrderSn(orderSn);
        orderCreateHelper.addOrderChangeEvent(orderPO, OrderEventEnum.valueOf(orderEvent), orderPO.getCreateTime());
    }

    @Override
    public void rabbitmqOrderReturnEventCompensate(String afsSn, String orderEvent) {
        /*BizAssertUtil.isTrue(!OrderEventEnum.isReverseEvent(orderEvent), "请输入正确的逆向订单事件");
        OrderReturnPO orderReturnPO = orderReturnService.getByAfsSn(afsSn);
        // 退款成功，发送退款消息
        orderCreateHelper.addOrderReturnEvent(orderReturnPO, OrderEventEnum.valueOf(orderEvent), orderReturnPO.getCompleteTime());*/
    }
}
