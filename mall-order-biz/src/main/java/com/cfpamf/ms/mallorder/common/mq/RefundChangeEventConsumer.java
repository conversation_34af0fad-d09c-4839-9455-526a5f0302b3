package com.cfpamf.ms.mallorder.common.mq;


import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.mallorder.dto.OrderRefundEventNotifyDTO;
import com.cfpamf.ms.mallorder.enums.OrderEventEnum;
import com.cfpamf.ms.mallorder.service.IOrderExchangeService;
import com.rabbitmq.client.Channel;
import com.slodon.bbc.core.exception.MallException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class RefundChangeEventConsumer {

    @Resource
    private IOrderExchangeService exchangeService;

    /***
     * 消费订单状态变更MQ
     * */
    @RabbitListener(queues = RabbitMqConfig.EXCHANGE_ORDER_REFUND_STATUS_CHANGE_QUEUE)
    public void refundChangeEventOnMessage(Message message, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag, Channel channel) throws Exception {
        Boolean isAck = Boolean.TRUE;
        try {
            String msg = new String(message.getBody());
            if (StringUtils.isBlank(msg)) {
                log.info("获取售后订单状态变更MQ通知内容为空");
                return;
            }
            log.info("=========获取售后订单状态变更消息,json:{}", msg);
            OrderRefundEventNotifyDTO notifyDTO = JSONObject.parseObject(msg, OrderRefundEventNotifyDTO.class);
            if (StringUtils.isBlank(notifyDTO.getAfsSn())) {
                throw new MallException("获取售后订单状态变更消息缺失afsSn");
            }
            if(OrderEventEnum.REFUND.getCode().equals(notifyDTO.getEventType())){
                //处理换货申请单状态
                log.info("RefundChangeEventConsumer notifyDTO============{}",JSONObject.toJSONString(notifyDTO));
                exchangeService.updateExchangeApplyStatus(notifyDTO.getAfsSn());
            }
        } catch (Exception ex) {
            log.error("=========获取售后订单状态变更MQ消息出现未知异常", ex);
            isAck = false;
        } finally {
            //处理失败消息重返队列
            RabbitMQUtils.manualAcknowledgeMode(isAck, deliveryTag, channel, false);
        }
    }

}
