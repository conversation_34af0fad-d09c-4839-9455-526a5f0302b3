package com.cfpamf.ms.mallorder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.ms.mallorder.po.CommonMqEvent;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-16
 */
public interface ICommonMqEventService extends IService<CommonMqEvent> {

    Long saveEvent(String body, String exchange);

    Long saveEvent(Object body, String exchange);

    <T> Long saveEvent(T body,String routeKey, String exchange);

    /**
     * 查询需要重新发送的消息
     *
     * @return      消息集合
     */
    List<CommonMqEvent> getMqMessage4Resend();

}
