package com.cfpamf.ms.mallorder.scheduler;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cfpamf.framework.autoconfigure.redis.lock.DistributeLock;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.enums.transaction.TransactionLogTypeEnum;
import com.cfpamf.ms.mallorder.common.enums.transaction.TransactionStatusEnum;
import com.cfpamf.ms.mallorder.dto.TransactionRollbackDTO;
import com.cfpamf.ms.mallorder.po.TransactionLogPO;
import com.cfpamf.ms.mallorder.service.ITransactionLogService;
import com.cfpamf.ms.mallorder.service.impl.OrderCreateHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 事务log job
 *
 * <AUTHOR>
 * @since 2024/3/11
 */
@Component
@Slf4j
@RestController
public class TransactionLogJob {

    @Autowired
    private ITransactionLogService transactionLogService;

    @Autowired
    private DistributeLock distributeLock;

    @Autowired
    private OrderCreateHelper orderCreateHelper;

    @XxlJob(value = "TransactionLogJob")
    @GetMapping("/TransactionLogJob")
    public ReturnT<String> execute(String s) {
        log.info("TransactionLogJob 开始执行事务回滚job");
        LambdaQueryWrapper<TransactionLogPO> logQuery = Wrappers.lambdaQuery(TransactionLogPO.class);
        logQuery.eq(TransactionLogPO::getStatus, TransactionStatusEnum.READY.getCode());
        // 查询过去30分钟-10分钟内的数据
        logQuery.le(TransactionLogPO::getCreateTime,
                DateUtil.format(DateUtil.offset(new Date(), DateField.MINUTE, -10), "yyyy-MM-dd HH:mm:ss")
        );
        logQuery.ge(TransactionLogPO::getCreateTime,
                DateUtil.format(DateUtil.offset(new Date(), DateField.MINUTE, -30), "yyyy-MM-dd HH:mm:ss"));
        // 根据id倒序，只取10条
        logQuery.last(" limit 10 ");
        logQuery.orderByDesc(TransactionLogPO::getId);
        List<TransactionLogPO> logPoList = transactionLogService.list(logQuery);
        log.info("TransactionLogJob 需要执行的log数据：{}", logPoList);
        for (TransactionLogPO logPo : logPoList) {
            String transactionId = logPo.getTransactionId();
            Integer logType = logPo.getLogType();
            TransactionLogTypeEnum logTypeEnum = TransactionLogTypeEnum.getByCode(logType);
            try {
                distributeLock.lockAndProcess(CommonConst.PREFIX_TRANSACTION_KEY + transactionId, 10, 5, TimeUnit.MINUTES, () -> {
                    // 发送回滚mq消息
                    switch (Objects.requireNonNull(logTypeEnum)) {
                        case ORDER_SUBMIT:
                            // 下单
                            String requestParam = logPo.getRequestParam();
                            TransactionRollbackDTO rollbackDTO = JSONObject.parseObject(requestParam, TransactionRollbackDTO.class);
                            orderCreateHelper.addTransactionLogEvent(logPo.getTransactionId(), logTypeEnum, rollbackDTO.getOrderSnList());
                            break;
                        default:
                            log.warn("不支持的回滚类型");
                            break;
                    }
                    return Boolean.TRUE;
                });
            } catch (Exception e) {
                log.error("TransactionLogJob 处理事务日志失败,logPo:{}",logPo);
            }
        }
        log.info("TransactionLogJob 执行完成");
        return ReturnT.SUCCESS;
    }
}
