package com.cfpamf.ms.mallorder.controller.fegin.facade;

import com.cfpamf.ms.mallorder.common.config.OrderMaterialConfig;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.BzOrderTradeProofCheckResultDTO;
import com.cfpamf.ms.mallorder.dto.TransactionVerifyDTO;
import com.cfpamf.ms.mallorder.service.IBzOrderTradeProofCheckResultService;
import com.cfpamf.ms.mallorder.service.IOrderTradeProofService;
import com.cfpamf.ms.mallorder.vo.AgricTradeProofUploadVO;
import com.cfpamf.ms.mallorder.vo.ContractSimpleVo;
import com.cfpamf.ms.mallorder.vo.ReceiveCheckResultVO;
import com.cfpamf.ms.mallsystem.vo.Admin;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 订单退货表feign
 */
@RestController
@Slf4j
public class OrderTradeProofFeign {

    @Autowired
    private IBzOrderTradeProofCheckResultService orderTradeProofCheckResultService;

    @Autowired
    private IOrderTradeProofService orderTradeProofService;

    @Autowired
    private OrderMaterialConfig materialConfig;


    /**
     * 根据returnId获取订单退货表详情
     *
     * @param orderNo orderNo
     * @return
     */
    @GetMapping("/v1/feign/business/orderTradeProof/queryResult")
    public JsonResult<List<TransactionVerifyDTO>> orderTradeProofQueryResult(@RequestParam("orderNo") String orderNo){

        return SldResponse.success(orderTradeProofCheckResultService.orderTradeProofQueryResult(orderNo));
    }

    @GetMapping("/v1/feign/business/orderTradeProof/batchQueryResult")
    public JsonResult<List<BzOrderTradeProofCheckResultDTO>> orderTradeProofBatchQueryResult(@RequestParam("orderNoList") List<String> orderNoList){

        return SldResponse.success(orderTradeProofCheckResultService.orderTradeProofBatchQueryResult(orderNoList));
    }

    @GetMapping("/v1/feign/business/orderTradeProof/getProofListAgric")
    @ApiOperation("根据订单编号、场景编码、资料编码查询签收凭证")
    public JsonResult<List<ReceiveCheckResultVO>> getOrderTradeProofByOrderSnAndSceneAgric(@RequestParam("orderSn")String orderSn){
        return SldResponse.success(orderTradeProofService.listSceneMaterialForAgric(orderSn));
    }

    @GetMapping("/v1/feign/business/orderTradeProof/getProofListAgricEle")
    @ApiOperation("根据订单编号、场景编码、资料编码查询签收凭证")
    public JsonResult<ContractSimpleVo> getOrderTradeProofByOrderSnAndSceneAgricEle(@RequestParam("orderSn")String orderSn){
        return SldResponse.success(orderTradeProofService.listSceneMaterialForAgricEle(orderSn));
    }

    @PostMapping("/v1/feign/business/orderTradeProof/uploadProofAgric")
    @ApiOperation("上传凭证")
    public JsonResult<Boolean> uploadProof(@RequestBody AgricTradeProofUploadVO uploadVO){
        Admin admin = new Admin();
        admin.setAdminId(NumberUtils.INTEGER_ONE);
        admin.setAdminName(uploadVO.getWmsOperator());
        Boolean result = orderTradeProofService.uploadProofAgric(OrderConst.LOG_ROLE_ADMIN, admin.getAdminId().toString(),admin.getAdminName(),"拼车系统",uploadVO,false);
        return SldResponse.success(result);
    }

    @PostMapping("/v1/feign/business/orderTradeProof/uploadProofEleAgric")
    @ApiOperation("上传凭证")
    public JsonResult<Boolean> uploadEleProof(@RequestBody AgricTradeProofUploadVO uploadVO){
        Admin admin = new Admin();
        admin.setAdminId(NumberUtils.INTEGER_ONE);
        admin.setAdminName(uploadVO.getWmsOperator());
        Boolean result = orderTradeProofService.uploadProofAgric(OrderConst.LOG_ROLE_ADMIN, admin.getAdminId().toString(),admin.getAdminName(),"拼车系统",uploadVO,true);
        return SldResponse.success(result);
    }

    @ApiOperation("校验文件风险等级")
    @PostMapping("/v1/feign/business/orderTradeProof/checkReceiveMaterial")
    public JsonResult<ReceiveCheckResultVO> checkReceiveMaterial(@RequestParam("orderSn")String orderSn, @RequestParam("fileUrl")String fileUrl) {

        return SldResponse.success(orderTradeProofService.checkReceiveMaterialAgric(orderSn,fileUrl));

    }

    @ApiOperation("软删除数据")
    @GetMapping("/v1/feign/business/orderTradeProof/softDel")
    public JsonResult<String> softDel(@RequestParam("ids")String ids){
        orderTradeProofService.softDel(ids);
        return SldResponse.success("success");
    }
}