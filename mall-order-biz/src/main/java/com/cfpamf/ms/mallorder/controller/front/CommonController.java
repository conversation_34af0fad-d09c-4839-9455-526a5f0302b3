package com.cfpamf.ms.mallorder.controller.front;

import com.cfpamf.ms.bms.facade.vo.DictionaryItemVO;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.integration.facade.BmsIntegration;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@RequestMapping("front/common")
@Api(tags = "公共配置查询controller")
@Slf4j
public class CommonController {

    @Autowired
    private BmsIntegration bmsIntegration;

    @GetMapping("/getDictionaryItemsByTypeCode")
    @ApiOperation("查询字典值")
    public JsonResult<List<DictionaryItemVO>> getDictionaryItemsByTypeCode(@RequestParam(value = "typeCode", required = true)
                                                                       @ApiParam("BMS字典key") String typeCode) {
        List<DictionaryItemVO> dictList = bmsIntegration.getDictionaryItemsByTypeCode(CommonConst.NEW_MALL_SYSTEM_ID, typeCode);

        return SldResponse.success(dictList);
    }
}
