package com.cfpamf.ms.mallorder.controller.seller;

import com.cfpamf.ms.mall.settlement.api.SettlementBillFeignClient;
import com.cfpamf.ms.mallgoods.facade.api.GoodsFeignClient;
import com.cfpamf.ms.mallgoods.facade.request.GoodsExample;
import com.cfpamf.ms.mallgoods.facade.vo.Goods;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.constant.OrdersAfsConst;
import com.cfpamf.ms.mallorder.common.util.OrderLocalUtils;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.CategoryLoanDTO;
import com.cfpamf.ms.mallorder.dto.OrderDayDTO;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.model.OrderReturnModel;
import com.cfpamf.ms.mallorder.request.OrderExample;
import com.cfpamf.ms.mallorder.request.OrderReturnExample;
import com.cfpamf.ms.mallorder.service.IBzOrderPayCategoryService;
import com.cfpamf.ms.mallorder.vo.SellerIndexVO;
import com.cfpamf.ms.mallshop.resp.Vendor;
import com.slodon.bbc.core.constant.BillConst;
import com.slodon.bbc.core.constant.GoodsConst;
import com.slodon.bbc.core.controller.BaseController;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.exception.MallException;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.PagerInfo;
import com.slodon.bbc.core.response.SldResponse;
import com.slodon.bbc.core.util.TimeUtil;
import com.slodon.bbc.core.util.UserUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.SneakyThrows;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.*;

@Api(tags = "seller-首页概况")
@RestController
@RequestMapping("seller/dashboard")
public class SellerIndexController extends BaseController {

    @Resource
    private OrderModel orderModel;
    @Resource
    private OrderReturnModel orderReturnModel;
    @Resource
    private GoodsFeignClient goodsFeignClient;
    @Autowired
    private SettlementBillFeignClient settlementBillFeignClient;
    @Autowired
    private IBzOrderPayCategoryService bzOrderPayCategoryService;


    @SneakyThrows
    @ApiOperation(value = "首页概况信息")
    @GetMapping("indexInfo")
    public JsonResult<SellerIndexVO> index(HttpServletRequest request) {
        Vendor vendor = UserUtil.getUser(request, Vendor.class);

        if (Objects.isNull(vendor) || Objects.isNull(vendor.getStoreId())) {
            throw new BusinessException("店铺信息异常，请确认");
        }

        //组装数据
        SellerIndexVO vo = new SellerIndexVO();
        //待付款
        OrderExample orderExamplePay = new OrderExample();
        orderExamplePay.setStoreId(vendor.getStoreId());
        orderExamplePay.setOrderState(OrderConst.ORDER_STATE_10);
        vo.setWaitPayCount(orderModel.getOrderCount(orderExamplePay));
        //待发货
        OrderExample orderExample = new OrderExample();
        orderExample.setStoreId(vendor.getStoreId());
        orderExample.setOrderState(OrderConst.ORDER_STATE_20);
        vo.setToDeliveredNum(orderModel.getOrderCount(orderExample));
        //售后中订单
        OrderReturnExample returnExample = new OrderReturnExample();
        returnExample.setStoreId(vendor.getStoreId());
        returnExample.setStateNotIn(OrdersAfsConst.RETURN_STATE_202 + "," + OrdersAfsConst.RETURN_STATE_300);
        vo.setAfsOrderNum(orderReturnModel.getOrderReturnCount(returnExample));
        //出售中的商品
        GoodsExample goodsExample = new GoodsExample();
        goodsExample.setStoreId(vendor.getStoreId());
        goodsExample.setState(GoodsConst.GOODS_STATE_UPPER);
        vo.setOnSaleGoodsNum(goodsFeignClient.getGoodsCount(goodsExample));
        //待审核的商品
        goodsExample.setState(null);
        goodsExample.setStateIn(GoodsConst.GOODS_STATE_SELL_NOW_TO_AUDIT + "," + GoodsConst.GOODS_STATE_WAREHOUSE_TO_AUDIT);
        vo.setToAuditGoodsNum(goodsFeignClient.getGoodsCount(goodsExample));
        //违规的商品
        goodsExample.setStateIn(null);
        goodsExample.setState(GoodsConst.GOODS_STATE_LOWER_BY_SYSTEM);
        vo.setViolationGoodsNum(goodsFeignClient.getGoodsCount(goodsExample));
        //待确认的结算单
        JsonResult<List<Map<String, Integer>>> result = settlementBillFeignClient.countByState(vendor.getStoreId(), BillConst.STATE_1);
        if (result.getState() == 200 && Objects.nonNull(result.getData())) {
            vo.setToConfirmBillNUm(result.getData().get(0).get("count"));
        } else {
            vo.setToConfirmBillNUm(0);
        }
        //销量排名(默认取前四条)
        PagerInfo pager = new PagerInfo(4, 1);
        goodsExample.setState(null);
        goodsExample.setStateIn(null);
        goodsExample.setQuerySales("notNull");
        goodsExample.setOrderBy("actual_sales desc");
        goodsExample.setPager(pager);
        List<Goods> goodsList = goodsFeignClient.getGoodsList(goodsExample);
        List<SellerIndexVO.GoodsSaleRankVO> vos = new ArrayList<>();
        if (!CollectionUtils.isEmpty(goodsList)) {
            goodsList.forEach(goods -> {
                vos.add(new SellerIndexVO.GoodsSaleRankVO(goods));
            });
        }
        vo.setGoodsSaleRank(vos);

        //七日订单数
        orderExample.setOrderState(null);
        orderExample.setOrderStateIn(OrderConst.ORDER_STATE_20 + "," + OrderConst.ORDER_STATE_30 + "," + OrderConst.ORDER_STATE_40);
        orderExample.setCreateTimeAfter(TimeUtil.getDayAgoDate(new Date(), -6));
        orderExample.setCreateTimeBefore(TimeUtil.getDayAgoDate(new Date(), 0));
        List<OrderDayDTO> orderDayDto = orderModel.getOrderDayDto(orderExample);
        vo.setOrderWeeklyReport(getWeeklyReport(orderDayDto));

        //七日销售额
        vo.setWeekSaleReport(getSaleTotalWeeklyReport(orderDayDto));
        return SldResponse.success(vo);
    }

    private List<SellerIndexVO.SellerSalesVolumeVO> getSaleTotalWeeklyReport(List<OrderDayDTO> result) {
        List<SellerIndexVO.SellerSalesVolumeVO> list = new ArrayList<>(7);
        if (CollectionUtils.isEmpty(result)) {
            for (int i = 0; i < 7; i++) {
                SellerIndexVO.SellerSalesVolumeVO saleTotalDayDTO = new SellerIndexVO.SellerSalesVolumeVO();
                saleTotalDayDTO.setDay(TimeUtil.getYesterdayOfWeek(i - 6).toString());
                saleTotalDayDTO.setAmount(BigDecimal.ZERO);
                list.add(saleTotalDayDTO);
            }
        } else {
            for (int i = 0; i < 7; i++) {
                String yesterday = TimeUtil.getYesterday(i - 6);
                SellerIndexVO.SellerSalesVolumeVO saleTotalDayDTO = new SellerIndexVO.SellerSalesVolumeVO();
                saleTotalDayDTO.setDay(TimeUtil.getYesterdayOfWeek(i - 6).toString());
                saleTotalDayDTO.setAmount(BigDecimal.ZERO);
                for (OrderDayDTO o : result) {
                    if (o.getOrderDay().equals(yesterday)) {
                        saleTotalDayDTO.setAmount(o.getOrderAmount().subtract(o.getRefundAmount()));
                        break;
                    }
                }
                list.add(saleTotalDayDTO);
            }
        }
        return list;
    }

    private List<SellerIndexVO.OrderReportVO> getWeeklyReport(List<OrderDayDTO> result) {
        List<SellerIndexVO.OrderReportVO> list = new ArrayList<>(7);
        if (CollectionUtils.isEmpty(result)) {
            for (int i = 0; i < 7; i++) {
                SellerIndexVO.OrderReportVO vo = new SellerIndexVO.OrderReportVO();
                vo.setDay(TimeUtil.getYesterdayOfWeek(i - 6).toString());
                vo.setNumber(0);
                list.add(vo);
            }
        } else {
            for (int i = 0; i < 7; i++) {
                String yesterday = TimeUtil.getYesterday(i - 6);
                SellerIndexVO.OrderReportVO vo = new SellerIndexVO.OrderReportVO();
                vo.setDay(TimeUtil.getYesterdayOfWeek(i - 6).toString());
                vo.setNumber(0);
                for (OrderDayDTO o : result) {
                    if (o.getOrderDay().equals(yesterday)) {
                        vo.setNumber(o.getCount());
                        break;
                    }
                }
                list.add(vo);
            }
        }
        return list;
    }

    @ApiOperation("判断店铺分类是否允许开通金融支付")
    @PostMapping("isAllowLoan")
    public JsonResult<Boolean> isAllowLoan(@RequestBody @Valid @NotNull CategoryLoanDTO categoryLoanDTO) {
        return SldResponse.success(bzOrderPayCategoryService.isAllowLoan(categoryLoanDTO));
    }

    @Autowired
    private OrderLocalUtils orderLocalUtils;


    @ApiOperation("判断店铺是否存在白名单")
    @GetMapping("isExistWhiteList")
    public JsonResult<Boolean> isExistWhiteList(@RequestParam(value = "storeId") @ApiParam("店铺id") String storeId){
        String openNewOrder = orderLocalUtils.getRedisValueByKey("openNewOrder");
        if (StringUtils.isEmpty(openNewOrder) || CommonConst.FALSE.equals(openNewOrder)) {
            String oldSvcWhite = orderLocalUtils.getRedisValueByKey("svc_white");
            if (StringUtils.isEmpty(oldSvcWhite)) {
                return SldResponse.success(true);
            } else {
                List<String> oldWhiteList = Arrays.asList(oldSvcWhite.split(","));
                boolean exist = oldWhiteList.contains(storeId);
                return SldResponse.success(exist);
            }
        }
        return SldResponse.success(false);
    }

}
