package com.cfpamf.ms.mallorder.validation;


import com.cfpamf.ms.mallorder.common.enums.OrderReturnStatus;
import com.cfpamf.ms.mallorder.common.enums.ReturnByEnum;
import com.cfpamf.ms.mallorder.common.enums.ReturnTypeEnum;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.integration.erp.ERPIntegration;
import com.cfpamf.ms.mallorder.po.OrderAfterPO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderProductPO;
import com.cfpamf.ms.mallorder.po.OrderReturnPO;
import com.cfpamf.ms.mallorder.service.IOrderAfterService;
import com.cfpamf.ms.mallorder.service.IOrderProductService;
import com.cfpamf.ms.mallshop.api.StoreIsolateWhiteListFeignClient;
import com.cfpamf.ms.mallshop.enums.WhiteListEnum;
import com.slodon.bbc.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 售后审核相关校验
 */
@Slf4j
@Component
public class OrderReturnAuditValidation {

    @Autowired
    private IOrderProductService orderProductService;

    @Autowired
    private IOrderAfterService orderAfterService;

    @Autowired
    private StoreIsolateWhiteListFeignClient storeIsolateWhiteListFeignClient;

    @Autowired
    private ERPIntegration erpIntegration;


    /**
     * 商家审批时校验售后单
     */
    public boolean afsStoreAuditValidate(OrderReturnPO orderReturnPO) {
        // 校验售后单状态
        if (!OrderReturnStatus.REFUND_APPLY.getValue().equals(orderReturnPO.getState())
                && !OrderReturnStatus.RETURN_APPLY.getValue().equals(orderReturnPO.getState())
                    && !OrderReturnStatus.WAIT_SELF_PICKUP_POINT_RECEIVED.getValue().equals(orderReturnPO.getState())) {
            throw new BusinessException(String.format("商家审批售后单%s状态异常,当前售后单状态%s", orderReturnPO.getAfsSn(),
                    OrderReturnStatus.getShowDesc(orderReturnPO.getState())));
        }

        return true;
    }

    /**
     * 售后是否支持钉钉审批
     */
    public boolean isSupportDingTalkAudit(OrderPO orderPO, OrderReturnPO orderReturnPO) {
        // 判断是否在售后钉钉审批白名单
        if (!storeIsolateWhiteListFeignClient.isStoreOnList(WhiteListEnum.DING_DING_REFUND_WHITE_LIST, orderPO.getStoreId())) {
            log.warn(String.format("售后店铺%d不在售后钉钉审批白名单中", orderPO.getStoreId()));
            return false;
        }

        // 预付定金不做审批处理
        if (OrderTypeEnum.PRE_SELL_DEPOSIT.getValue().equals(orderPO.getOrderType())) {
            log.warn(String.format("预付定金售后单%s不支持钉钉审批流", orderReturnPO.getAfsSn()));
            return false;
        }
        // 平台强制退款不进行
        if (ReturnByEnum.PLATFORM_FORCE.getValue().equals(orderReturnPO.getReturnBy())) {
            log.warn(String.format("强制退款售后单%s不支持钉钉审批流", orderReturnPO.getAfsSn()));
            return false;
        }
        // 换货不做钉钉审批处理
        if (ReturnTypeEnum.EXCHANGE_AND_REFUND.getValue().equals(orderReturnPO.getReturnType())) {
            log.warn(String.format("换货售后单%s不支持钉钉审批流", orderReturnPO.getAfsSn()));
            return false;
        }

        return true;
    }

    /**
     * 商家确认收货时校验
     */
    public boolean afsStoreReceiveValidate(OrderAfterPO orderAfterServicePO) {

        // 酒水物流订单，需要校验ERP是否实际已经收货
        OrderProductPO orderProductPO = orderProductService.selectOneByOrderProductId(orderAfterServicePO.getOrderProductId());
        if (orderAfterService.isJdInterceptOrderProduct(orderProductPO)) {
            if (!erpIntegration.checkReturnInFlow(orderAfterServicePO)) {
                throw new BusinessException("仓库退货入库单状态：入库失败/未入库，暂不可收货！");
            }
        }

        return true;
    }

}
