package com.cfpamf.ms.mallorder.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cfpamf.ares.aftersale.domain.dto.*;
import com.cfpamf.ares.aftersale.domain.vo.AfterSaleAuditVO;
import com.cfpamf.ares.aftersale.domain.vo.AfterSaleCreateVO;
import com.cfpamf.ares.trade.common.enums.OrderSourceEnum;
import com.cfpamf.ares.trade.common.enums.*;
import com.cfpamf.ares.trade.domain.dto.*;
import com.cfpamf.ares.trade.domain.vo.TradeVO;
import com.cfpamf.athena.common.domain.dto.ResultEntity;
import com.cfpamf.cmis.common.exception.BizException;
import com.cfpamf.framework.autoconfigure.mybatis.BaseRepoServiceImpl;
import com.cfpamf.ms.mallorder.common.constant.OrderPaymentConst;
import com.cfpamf.ms.mallorder.common.enums.*;
import com.cfpamf.ms.mallorder.common.enums.aresAfterSale.OrderReturnStatusEnum;
import com.cfpamf.ms.mallorder.common.enums.aresAfterSale.RefundAmountTypeEnum;
import com.cfpamf.ms.mallorder.common.util.DateUtil;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.PayCallbackNotifyDTO;
import com.cfpamf.ms.mallorder.dto.RefundCallbackNotifyDTO;
import com.cfpamf.ms.mallorder.enums.OrderEventEnum;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.integration.facade.AresAfterSaleFacade;
import com.cfpamf.ms.mallorder.integration.facade.AresTradeFacade;
import com.cfpamf.ms.mallorder.mapper.OrderEventMsgMapper;
import com.cfpamf.ms.mallorder.model.OrderExtendModel;
import com.cfpamf.ms.mallorder.model.OrderProductModel;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.service.IOrderEventMsgService;
import com.google.common.collect.Lists;
import com.slodon.bbc.core.exception.MallException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 订单事件消息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
@Slf4j
@Service
public class OrderEventMsgServiceImpl extends BaseRepoServiceImpl<OrderEventMsgMapper, OrderEventMsgPO> implements IOrderEventMsgService {
    private String NONE_VALUE = "-";
    private String PART_REFUND = "部分退";

    @Resource
    private OrderExtendModel orderExtendModel;

    @Resource
    private OrderProductModel orderProductModel;

    @Resource
    private AresTradeFacade aresTradeFacade;

    @Resource
    private AresAfterSaleFacade aresAfterSaleFacade;
    @Override
    public List<OrderEventMsgPO> getByMsgIdList(List<String> msgIds) {
        if (CollectionUtils.isEmpty(msgIds)) {
            log.warn("MQ消息ID为空");
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<OrderEventMsgPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(OrderEventMsgPO::getMsgId, msgIds);
        return getBaseMapper().selectList(queryWrapper);
    }

    @Override
    public OrderEventMsgPO getByOrderSn(String orderSn, String eventType, Integer msgType,String afsSn) {
        LambdaQueryWrapper<OrderEventMsgPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(OrderEventMsgPO::getOrderSn, orderSn);
        queryWrapper.eq(OrderEventMsgPO::getEventType, eventType);
        queryWrapper.eq(OrderEventMsgPO::getMsgType, msgType);
        queryWrapper.eq(OrderEventMsgPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
        queryWrapper.eq(StringUtils.isNotBlank(afsSn),OrderEventMsgPO::getAfsSn,afsSn);
        return getBaseMapper().selectOne(queryWrapper);
    }

    @Override
    public TradeDTO buildTradeDTO(OrderPO orderPO) {
        TradeDTO tradeDTO = new TradeDTO();
        tradeDTO.setOrderTime(DateUtil.dateToLocalDateTime(orderPO.getCreateTime()));
        tradeDTO.setPayDeadline(DateUtil.dateToLocalDateTime(orderPO.getCreateTime()).plusHours(12));
        tradeDTO.setOrderChannel(orderPO.getChannel());
        tradeDTO.setOrderSourceCode(OrderSourceEnum.STANDARD_MALL.getOrderSourceCode());
        tradeDTO.setOrderSourceDesc(OrderSourceEnum.STANDARD_MALL.getOrderSourceDesc());
        tradeDTO.setTradeSourceNo(orderPO.getPaySn());
        tradeDTO.setPayPatternCode(PayPatternEnum.BUSINESS_PAY.getPayPatternCode());
        tradeDTO.setPayPatternDesc(PayPatternEnum.BUSINESS_PAY.getPayPatternDesc());
        tradeDTO.setOrderPlaceUserId(orderPO.getUserNo());
        tradeDTO.setOrderPlaceUserName(orderPO.getUserMobile());
        tradeDTO.setOrderPlaceAreaCode(orderPO.getAreaCode());
        tradeDTO.setOrderPlaceRole(orderPO.getOrderPlaceUserRoleCode());
        tradeDTO.setOrderPlaceRoleDesc(orderPO.getOrderPlaceUserRoleDesc());
        tradeDTO.setTradeOrders(buildTradeOrder(orderPO));
        tradeDTO.setTradePayApplys(buildTradePayApplyDTO(orderPO));

        return tradeDTO;
    }

    @Override
    public OrderStatusSyncDTO buildOrderStatusSyncDTO(OrderPO orderPO, OrderEventEnum orderEventEnum) {
        OrderStatusSyncDTO orderStatusSyncDTO = new OrderStatusSyncDTO();
        orderStatusSyncDTO.setOrderSourceCode(OrderSourceEnum.STANDARD_MALL.getOrderSourceCode());
        orderStatusSyncDTO.setOrderSourceOrderNo(orderPO.getOrderSn());
        orderStatusSyncDTO.setOrderStatusCode(orderEventEnum.getOrderStateCode());
        orderStatusSyncDTO.setOrderStatusName(orderEventEnum.getOrderStateDesc());
        orderStatusSyncDTO.setCustomerConfirmStatus(orderPO.getCustomerConfirmStatus());
        orderStatusSyncDTO.setCustomerConfirmStatusDesc(orderPO.getCustomerConfirmStatusDesc());
        orderStatusSyncDTO.setOperationRole("admin");
        orderStatusSyncDTO.setOperationTime(LocalDateTime.now());
        orderStatusSyncDTO.setOperationUserCode("admin");
        orderStatusSyncDTO.setOperationUserName("admin");

        return orderStatusSyncDTO;

    }

    private List<TradeOrderDTO> buildTradeOrder(OrderPO orderPO) {
        OrderExtendPO orderExtendPO = orderExtendModel.getOrderExtendByOrderSn(orderPO.getOrderSn());
        List<TradeOrderDTO> tradeOrderList = new ArrayList<>();
        TradeOrderDTO tradeOrderDTO = new TradeOrderDTO();
        tradeOrderDTO.setOrderBusinessTypeCode(OrderTypeEnum.getValue(orderPO.getOrderType()) == null ? OrderTypeEnum.NORMAL.getValue() : OrderTypeEnum.getValue(orderPO.getOrderType()).getValue());
        tradeOrderDTO.setOrderBusinessTypeDesc(OrderTypeEnum.getValue(orderPO.getOrderType()) == null ? OrderTypeEnum.NORMAL.getDesc() : OrderTypeEnum.getValue(orderPO.getOrderType()).getDesc());
        tradeOrderDTO.setOrderSourceCode(OrderSourceEnum.STANDARD_MALL.getOrderSourceCode());
        tradeOrderDTO.setOrderSourceDesc(OrderSourceEnum.STANDARD_MALL.getOrderSourceDesc());
        tradeOrderDTO.setOrderSourceOrderNo(orderPO.getOrderSn());
        tradeOrderDTO.setPayPatternCode(PayPatternEnum.BUSINESS_PAY.getPayPatternCode());
        tradeOrderDTO.setPayPatternDesc(PayPatternEnum.BUSINESS_PAY.getPayPatternDesc());
        tradeOrderDTO.setPayStatus(PayStatusAresTradeEnum.TODO_PAY.getStatus());
        tradeOrderDTO.setPayStatusDesc(PayStatusAresTradeEnum.TODO_PAY.getDesc());

        tradeOrderDTO.setTogetherFlag(2);
        tradeOrderDTO.setPurchaseType(1);
        tradeOrderDTO.setPaymentType(1);
        tradeOrderDTO.setLoanPayer(orderPO.getLoanPayer());

        tradeOrderDTO.setPerformanceModelCode(PerformanceModelEnum.PLATFORM_STORE.getCode());
        tradeOrderDTO.setPerformanceModelDesc(PerformanceModelEnum.PLATFORM_STORE.getDesc());
        tradeOrderDTO.setStoreId(orderPO.getStoreId());
        tradeOrderDTO.setStoreName(orderPO.getStoreName());
        tradeOrderDTO.setRecommendStoreId(orderPO.getRecommendStoreId() == null ? 0L : orderPO.getRecommendStoreId());
        tradeOrderDTO.setRecommendStoreName(orderPO.getRecommendStoreName());
        tradeOrderDTO.setBuyerUserNo(orderPO.getUserNo());
        tradeOrderDTO.setBuyerCustomerNo(orderPO.getMemberId() + "");
        tradeOrderDTO.setBuyerPhone(StringUtils.isBlank(orderPO.getUserMobile()) ? NONE_VALUE : orderPO.getUserMobile());
        tradeOrderDTO.setBuyerName(StringUtils.isBlank(orderExtendPO.getCustomerName()) ? NONE_VALUE : orderExtendPO.getCustomerName());
        tradeOrderDTO.setOrderPlaceAreaCode(StringUtils.isBlank(orderPO.getAreaCode()) ? NONE_VALUE : orderPO.getAreaCode());
        tradeOrderDTO.setOrderPlaceRole(orderPO.getOrderPlaceUserRoleCode());
        tradeOrderDTO.setOrderPlaceRoleDesc(orderPO.getOrderPlaceUserRoleDesc());
        tradeOrderDTO.setAfterSaleDeadline(DateUtil.dateToLocalDateTime(DateUtil.addDays(orderPO.getCreateTime(), 365)));
        tradeOrderDTO.setOrderStatusCode(OrderEventEnum.CREATE.getOrderStateCode());
        tradeOrderDTO.setOrderStatusName(OrderEventEnum.CREATE.getOrderStateDesc());
        tradeOrderDTO.setCustomerConfirmStatus(orderPO.getCustomerConfirmStatus());
        tradeOrderDTO.setCustomerConfirmStatusDesc(orderPO.getCustomerConfirmStatusDesc());
        tradeOrderDTO.setOrderDeliveryAddress(buildOrderDeliveryAddress(orderExtendPO));
        tradeOrderDTO.setOrderLoanCustomerOrganization(buildOrderLoanCustomerOrganization(orderExtendPO));
        tradeOrderDTO.setOrderAmounts(buildOrderCreatedAmountDTOList(orderPO));
        tradeOrderDTO.setOrderItems(buildOrderItemList(orderPO));

        tradeOrderList.add(tradeOrderDTO);
        return tradeOrderList;
    }

    private OrderDeliveryAddressDTO buildOrderDeliveryAddress(OrderExtendPO orderExtendPO) {
        OrderDeliveryAddressDTO deliveryAddress = new OrderDeliveryAddressDTO();
        deliveryAddress.setCountry("中国");
        deliveryAddress.setProvince(orderExtendPO.getReceiverProvinceCode());
        deliveryAddress.setCity(orderExtendPO.getReceiverCityCode());
        deliveryAddress.setDistrict(orderExtendPO.getReceiverDistrictCode());
        deliveryAddress.setStreet(orderExtendPO.getReceiverTownCode());
        deliveryAddress.setConsignee(orderExtendPO.getReceiverName());
        deliveryAddress.setCityCode(orderExtendPO.getReceiverCityCode());
        deliveryAddress.setTelphone(orderExtendPO.getReceiverMobile());
        deliveryAddress.setAddress(orderExtendPO.getReceiverInfo());

        return deliveryAddress;
    }


    private OrderLoanCustomerOrganizationDTO buildOrderLoanCustomerOrganization(OrderExtendPO orderExtendPO) {
        OrderLoanCustomerOrganizationDTO dto = new OrderLoanCustomerOrganizationDTO();
        dto.setCustomerManagerCode(StringUtils.isBlank(orderExtendPO.getManager()) ? NONE_VALUE : orderExtendPO.getManager());
        dto.setCustomerManagerName(StringUtils.isBlank(orderExtendPO.getManagerName()) ? NONE_VALUE : orderExtendPO.getManagerName());
        dto.setSupervisorCode(StringUtils.isBlank(orderExtendPO.getSupervisor()) ? NONE_VALUE : orderExtendPO.getSupervisor());
        dto.setSupervisorName(StringUtils.isBlank(orderExtendPO.getSupervisorName()) ? NONE_VALUE : orderExtendPO.getSupervisorName());
        dto.setBranchCode(StringUtils.isBlank(orderExtendPO.getBranch()) ? NONE_VALUE : orderExtendPO.getBranch());
        dto.setBranchName(StringUtils.isBlank(orderExtendPO.getBranchName()) ? NONE_VALUE : orderExtendPO.getBranchName());
        dto.setZoneCode(StringUtils.isBlank(orderExtendPO.getZoneCode()) ? NONE_VALUE : orderExtendPO.getZoneCode());
        dto.setZoneName(StringUtils.isBlank(orderExtendPO.getZoneName()) ? NONE_VALUE : orderExtendPO.getZoneName());
        dto.setAreaCode(StringUtils.isBlank(orderExtendPO.getAreaCode()) ? NONE_VALUE : orderExtendPO.getAreaCode());
        dto.setAreaName(StringUtils.isBlank(orderExtendPO.getAreaName()) ? NONE_VALUE : orderExtendPO.getAreaName());

        return dto;
    }


    private List<OrderAmountDTO> buildOrderCreatedAmountDTOList(OrderPO orderPO) {
        List<OrderAmountDTO> orderAmountDTOList = new ArrayList<>();

        orderAmountDTOList.add(buildOrderAmountDTO(orderPO, OrderAmountCategoryEnum.PLATFORM_SERVICE_FREE, orderPO.getServiceFee()));
        orderAmountDTOList.add(buildOrderAmountDTO(orderPO, OrderAmountCategoryEnum.THIRDPARTNAR_SERVICE_FREE, orderPO.getThirdpartnarFee()));
        orderAmountDTOList.add(buildOrderAmountDTO(orderPO, OrderAmountCategoryEnum.DEPOSIT_ACTUAL_AMOUNT, orderPO.getOrderAmount()));

        return orderAmountDTOList;
    }

    @Override
    public PayResultNotifyDTO buildPayResultNotifyDTO(OrderPO orderPO) {
        HashMap<String, String> bankPayTrxNos = CollectionUtils.newHashMap();
        bankPayTrxNos.put(orderPO.getPaySn(), StringUtils.isBlank(orderPO.getBankPayTrxNo()) ? NONE_VALUE : orderPO.getBankPayTrxNo());

        PayCallbackNotifyDTO payResultNotifyDTO = new PayCallbackNotifyDTO();
        payResultNotifyDTO.setMainOrderNo(orderPO.getPaySn());
        payResultNotifyDTO.setOrderOn(orderPO.getPaySn());
        payResultNotifyDTO.setPayCode(orderPO.getPaySn());
        payResultNotifyDTO.setPayTradeNo(orderPO.getPaySn());
        payResultNotifyDTO.setRelPayAmt(orderPO.getPayAmount());
        payResultNotifyDTO.setPayUmpAmt(orderPO.getActivityDiscountAmount());
        payResultNotifyDTO.setRateAmt(orderPO.getChannelServiceFee());
        payResultNotifyDTO.setPayStatus(PayStatusAresTradeEnum.PAY_SUCCESS.getStatus());
        payResultNotifyDTO.setPayWayCode(StringUtils.isBlank(orderPO.getPaymentCode()) ? OrderPaymentConst.PAYMENT_CODE_ONLINE : orderPO.getPaymentCode());
        payResultNotifyDTO.setPayWay(StringUtils.isBlank(orderPO.getPaymentName()) ? OrderPaymentConst.PAYMENT_NAME_ONLINE : orderPO.getPaymentName());
        payResultNotifyDTO.setPaySuccessTime(orderPO.getPayTime());
        payResultNotifyDTO.setBankPayTrxNos(bankPayTrxNos);
        payResultNotifyDTO.setPayChannel(PayChannelTradeEnum.getByCode(orderPO.getPayChannel()) == null ? NONE_VALUE : PayChannelTradeEnum.getByCode(orderPO.getPayChannel()).toString());
        payResultNotifyDTO.setSystemCode(OrderSourceEnum.STANDARD_MALL.getOrderSourceCode());

        return payResultNotifyDTO;
    }


    @Override
    public void doExecuteSync(OrderEventEnum orderEventEnum, OrderPO orderPO) {
        switch (orderEventEnum) {
            case CREATE:
                ResultEntity<TradeVO> createResult = aresTradeFacade.create(buildTradeDTO(orderPO));
                if (!createResult.isSuccess()) {
                    log.info("同步订单失败 {} {}", orderPO.getOrderSn(), createResult.getMsg());
                    throw new MallException("同步订单失败：" + createResult.getMsg());
                }
                break;
            case PAY:
                ResultEntity<Void> callbackResult = aresTradeFacade.callback(buildPayResultNotifyDTO(orderPO));
                if (!callbackResult.isSuccess()) {
                    log.info("支付成功回调同步失败 {} {}", orderPO.getOrderSn(), callbackResult.getMsg());
                    throw new BizException("支付成功回调同步失败：" + callbackResult.getMsg());
                }
            case PAYING:
            case PART_DELIVERY:
            case DELIVERY:
            case FINISH:
            case CANCEL:
            case CLOSE:
                ResultEntity<Void> syncStatusResult = aresTradeFacade.orderStatusSync(buildOrderStatusSyncDTO(orderPO, orderEventEnum));
                if (!syncStatusResult.isSuccess()) {
                    log.info("同步订单状态失败 {} {}", orderPO.getOrderSn(), syncStatusResult.getMsg());
                    throw new BizException("同步订单状态失败：" + syncStatusResult.getMsg());
                }
                break;
            default:
                break;
        }
    }

    private OrderAmountDTO buildOrderAmountDTO(OrderPO orderPO, OrderAmountCategoryEnum categoryEnum, BigDecimal amount) {
        OrderAmountDTO orderAmountDTO = new OrderAmountDTO();
        orderAmountDTO.setOrderSourceCode(OrderSourceEnum.STANDARD_MALL.getOrderSourceCode());
        orderAmountDTO.setOrderSourceDesc(OrderSourceEnum.STANDARD_MALL.getOrderSourceDesc());
        orderAmountDTO.setOrderSourceOrderNo(orderPO.getOrderSn());
        orderAmountDTO.setPayOrderSourceNo(orderPO.getPaySn());
        orderAmountDTO.setPayOrderTypeCode(OrderTypeEnum.getValue(orderPO.getOrderType()) == null ? OrderTypeEnum.NORMAL.getValue() : OrderTypeEnum.getValue(orderPO.getOrderType()).getValue());
        orderAmountDTO.setPayOrderTypeDesc(OrderTypeEnum.getValue(orderPO.getOrderType()) == null ? OrderTypeEnum.NORMAL.getDesc() : OrderTypeEnum.getValue(orderPO.getOrderType()).getDesc());
        orderAmountDTO.setType(categoryEnum.getType());
        orderAmountDTO.setTypeDesc(categoryEnum.getTypeDesc());
        orderAmountDTO.setAmount(amount == null ? BigDecimal.ZERO : amount);
        orderAmountDTO.setAmountTime(DateUtil.dateToLocalDateTime(orderPO.getCreateTime()));
        orderAmountDTO.setStatus(EffectiveStatusEnum.TODO_EFFECTIVE.getCode());
        orderAmountDTO.setStatusDesc(EffectiveStatusEnum.TODO_EFFECTIVE.getDesc());

        return orderAmountDTO;

    }

    private List<OrderItemDTO> buildOrderItemList(OrderPO orderPO) {
        BigDecimal expressFee = orderPO.getExpressFee();
        List<OrderProductPO> orderProductList = orderProductModel.getOrderProductListByOrderSn(orderPO.getOrderSn());
        Map<Long, BigDecimal> expressFeeMap = buildOrderItemExpressFee(expressFee, orderProductList);
        return orderProductModel.getOrderProductListByOrderSn(orderPO.getOrderSn())
                .stream()
                .map(i -> buildOrderItemDTO(i, expressFeeMap))
                .collect(Collectors.toList());
    }

    private OrderItemDTO buildOrderItemDTO(OrderProductPO orderProductPO, Map<Long, BigDecimal> expressFeeMap) {
        BigDecimal expressFee = expressFeeMap.getOrDefault(orderProductPO.getProductId(), BigDecimal.ZERO);
        BigDecimal payableTotalAmount = orderProductPO.getMoneyAmount().add(expressFee);
        OrderItemDTO orderItemDTO = new OrderItemDTO();
        orderItemDTO.setSpuCode(orderProductPO.getGoodsId().toString());
        orderItemDTO.setSkuCode(orderProductPO.getProductId().toString());
        orderItemDTO.setSkuName(StringUtils.isBlank(orderProductPO.getGoodsName()) ? NONE_VALUE : orderProductPO.getGoodsName());
        orderItemDTO.setSpuCategoryId(orderProductPO.getGoodsCategoryId());
        orderItemDTO.setSkuCategoryPath(StringUtils.isBlank(orderProductPO.getGoodsCategoryPath()) ? NONE_VALUE : orderProductPO.getGoodsCategoryPath());
        orderItemDTO.setSkuPictureUrl(StringUtils.isBlank(orderProductPO.getProductImage()) ? NONE_VALUE : orderProductPO.getProductImage());
        orderItemDTO.setSupplierCode(StringUtils.isBlank(orderProductPO.getSupplierCode()) ? NONE_VALUE : orderProductPO.getSupplierCode());
        orderItemDTO.setSupplierSku(StringUtils.isBlank(orderProductPO.getChannelSkuId()) ? NONE_VALUE : orderProductPO.getChannelSkuId());
        orderItemDTO.setSpecValues(StringUtils.isBlank(orderProductPO.getSpecValues()) ? NONE_VALUE : orderProductPO.getSpecValues());
        orderItemDTO.setTaxPrice(orderProductPO.getTaxPrice() == null ? BigDecimal.ZERO : orderProductPO.getTaxPrice());
        orderItemDTO.setTaxRate(orderProductPO.getTaxRate() == null ? BigDecimal.ZERO : orderProductPO.getTaxRate());
        orderItemDTO.setCostPrice(orderProductPO.getCost() == null ? BigDecimal.ZERO : orderProductPO.getCost());
        orderItemDTO.setLandingPrice(orderProductPO.getLandingPrice() == null ? BigDecimal.ZERO : orderProductPO.getLandingPrice());
        orderItemDTO.setSalePrice(orderProductPO.getProductShowPrice() == null ? BigDecimal.ZERO : orderProductPO.getProductShowPrice());
        orderItemDTO.setQuantity(orderProductPO.getProductNum() == null ? BigDecimal.ZERO : BigDecimal.valueOf(orderProductPO.getProductNum()));
        orderItemDTO.setExpressFee(expressFee);
        orderItemDTO.setPlatformActivityAmount(orderProductPO.getPlatformActivityAmount() == null ? BigDecimal.ZERO : orderProductPO.getPlatformActivityAmount());
        orderItemDTO.setPlatformCouponAmount(orderProductPO.getPlatformVoucherAmount() == null ? BigDecimal.ZERO : orderProductPO.getPlatformVoucherAmount());
        orderItemDTO.setStoreActivityAmount(orderProductPO.getStoreActivityAmount() == null ? BigDecimal.ZERO : orderProductPO.getStoreActivityAmount());
        orderItemDTO.setStoreCouponAmount(orderProductPO.getStoreVoucherAmount() == null ? BigDecimal.ZERO : orderProductPO.getStoreVoucherAmount());
        orderItemDTO.setDiscountTotalAmount(orderProductPO.getActivityDiscountAmount() == null ? BigDecimal.ZERO : orderProductPO.getActivityDiscountAmount());
        orderItemDTO.setPerformanceStatusCode(PerformanceStatusEnum.NO_NEED_PERFORMANCE.getCode());
        orderItemDTO.setPerformanceStatusDesc(PerformanceStatusEnum.NO_NEED_PERFORMANCE.getDesc());
        orderItemDTO.setSkuTotalAmount(orderProductPO.getGoodsAmountTotal() == null ? BigDecimal.ZERO : orderProductPO.getGoodsAmountTotal());
        orderItemDTO.setSkuPayableTotalAmount(payableTotalAmount);
        orderItemDTO.setIsGift(orderProductPO.getIsGift());
        orderItemDTO.setSkuSnapshootVersion("1.0.0");

        OrderSkuSnapshootDTO orderSkuSnapshootDTO = new OrderSkuSnapshootDTO();
        orderSkuSnapshootDTO.setSnapshootInfo(JSONObject.toJSONString(orderProductPO));

        orderItemDTO.setOrderSkuSnapshoot(orderSkuSnapshootDTO);

        return orderItemDTO;

    }

private List<TradePayApplyDTO> buildTradePayApplyDTO(OrderPO orderPO) {
    List<TradePayApplyDTO> tradePayApplyDTOS = new ArrayList<>();

    TradePayApplyDTO tradePayApplyDTO = new TradePayApplyDTO();
    tradePayApplyDTO.setPayOrderTypeCode(OrderTypeEnum.getValue(orderPO.getOrderType()) == null ? OrderTypeEnum.NORMAL.getValue() : OrderTypeEnum.getValue(orderPO.getOrderType()).getValue());
    tradePayApplyDTO.setPayOrderTypeDesc(OrderTypeEnum.getValue(orderPO.getOrderType()) == null ? OrderTypeEnum.NORMAL.getDesc() : OrderTypeEnum.getValue(orderPO.getOrderType()).getDesc());
    tradePayApplyDTO.setPayOrderSourceNo(orderPO.getPaySn());
    tradePayApplyDTO.setAmount(orderPO.getOrderAmount() == null ? BigDecimal.ZERO : orderPO.getOrderAmount());
    tradePayApplyDTO.setApplyTime(DateUtil.dateToLocalDateTime(orderPO.getCreateTime()));

    // 构建 TradePayLoanApplyDTO
    TradePayLoanApplyDTO tradePayLoanApplyDTO = new TradePayLoanApplyDTO();
    tradePayLoanApplyDTO.setLoanPayMode(orderPO.getLoanPayer());

    tradePayApplyDTO.setTradePayLoanApply(tradePayLoanApplyDTO);

    tradePayApplyDTOS.add(tradePayApplyDTO);

    return tradePayApplyDTOS;
}


    @Override
    public void doExecuteRefundSync(OrderEventEnum orderEventEnum, OrderPO orderPO, OrderReturnPO orderReturnPO, OrderAfterPO orderAfterPO) {
        switch (orderEventEnum) {
            case REFUND_APPLY:
                ResultEntity<AfterSaleCreateVO> createResult = aresAfterSaleFacade.create(buildAfterSaleCreateDTO(orderPO, orderReturnPO, orderAfterPO));
                if (!createResult.isSuccess()) {
                    log.info("同步售后单失败 {} {}", orderReturnPO.getAfsSn(), createResult.getMsg());
                    throw new MallException("同步售后单失败：" + createResult.getMsg());
                }
                break;
            case REFUND:
                ResultEntity<Boolean> notifyResult = aresAfterSaleFacade.refundNotify(buildAfterSaleRefundNotifyDTO(orderPO, orderReturnPO));
                if (!notifyResult.isSuccess()) {
                    log.info("售后结果通知失败 {} {}", orderReturnPO.getAfsSn(), notifyResult.getMsg());
                    throw new MallException("售后结果通知失败：" + notifyResult.getMsg());
                }
            default:
                ResultEntity<AfterSaleAuditVO> auditResult = aresAfterSaleFacade.audit(buildAfterSaleAuditDTO(orderReturnPO));
                if (!auditResult.isSuccess()) {
                    log.info("售后单状态更新失败 {} {}", orderReturnPO.getAfsSn(), auditResult.getMsg());
                    throw new MallException("售后单状态更新失败：" + auditResult.getMsg());
                }
        }
    }

    /**
     * 是否存在后续环节同步成功的事件
     * @return
     */
    @Override
    public Boolean laterStepExist(String orderSn, String eventType, Integer msgType, Integer executeOrder) {
        if (StringUtils.equalsAny(eventType, OrderEventEnum.CREATE.getCode(),
                OrderEventEnum.CANCEL.getCode(), OrderEventEnum.CLOSE.getCode(), OrderEventEnum.REFUND_APPLY.getCode(),
                OrderEventEnum.REFUND.getCode(), OrderEventEnum.REVOKE_REFUND.getCode())) {
            return false;
        }
        LambdaQueryWrapper<OrderEventMsgPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(OrderEventMsgPO::getOrderSn, orderSn);
        queryWrapper.eq(OrderEventMsgPO::getMsgType, msgType);
        queryWrapper.eq(OrderEventMsgPO::getStatus, OrderConst.SYNC_SUCCESS);
        queryWrapper.gt(OrderEventMsgPO::getExecuteOrder, executeOrder);
        queryWrapper.eq(OrderEventMsgPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
        return count(queryWrapper) > 0;
    }

    @Override
    public Map<Long, BigDecimal> buildOrderItemExpressFee(BigDecimal totalExpressFee, List<OrderProductPO> orderProductList) {
        //1、获取实付金额之和
        BigDecimal totalAmount = orderProductList.stream().map(OrderProductPO::getMoneyAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        Map<Long, BigDecimal> expressFeeMap = new HashMap<>();
        orderProductList.stream().peek(p -> {
            if (BigDecimal.ZERO.compareTo(totalAmount) < 0) {
                BigDecimal productAmount = p.getMoneyAmount();
                if (expressFeeMap.containsKey(p.getProductId())) {
                    BigDecimal amount = expressFeeMap.get(p.getProductId());
                    productAmount = amount.add(p.getMoneyAmount());
                }
                expressFeeMap.put(p.getProductId(), totalExpressFee.multiply((productAmount.divide(totalAmount, new MathContext(2, RoundingMode.HALF_UP)))));
            } else {
                expressFeeMap.put(p.getProductId(), BigDecimal.ZERO);
            }
        }).collect(Collectors.toList());
        return expressFeeMap;
    }

    private AfterSaleAuditDTO buildAfterSaleAuditDTO(OrderReturnPO orderReturnPO) {
        AfterSaleAuditDTO afterSaleAuditDTO = new AfterSaleAuditDTO();
        afterSaleAuditDTO.setAfterSaleSourceCode(OrderSourceEnum.STANDARD_MALL.getOrderSourceCode());
        afterSaleAuditDTO.setAfterSaleSourceNo(orderReturnPO.getAfsSn());
        afterSaleAuditDTO.setStatus(OrderReturnStatusEnum.getByCode(orderReturnPO.getState()).getAresAfterSaleCode());
        afterSaleAuditDTO.setStatusDesc(OrderReturnStatusEnum.getByCode(orderReturnPO.getState()).getAresAfterSaleDesc());
        afterSaleAuditDTO.setOperationRole("admin");
        afterSaleAuditDTO.setOperationUserCode("admin");
        afterSaleAuditDTO.setOperationUserName("admin");
        afterSaleAuditDTO.setAuditTime(DateUtil.dateToLocalDateTime(new Date()));

        return afterSaleAuditDTO;
    }


    @Override
    public AfterSaleRefundNotifyDTO buildAfterSaleRefundNotifyDTO(OrderPO orderPO, OrderReturnPO orderReturnPO) {
        RefundCallbackNotifyDTO refundNotifyDTO = new RefundCallbackNotifyDTO();
        refundNotifyDTO.setRefundCode(orderReturnPO.getAfsSn());
        refundNotifyDTO.setPayCode(orderPO.getPaySn());
        refundNotifyDTO.setPayTradeNo(orderPO.getPaySn());
        refundNotifyDTO.setOrderOn(orderPO.getOrderSn());
        refundNotifyDTO.setRefundOn(orderReturnPO.getAfsSn());
        refundNotifyDTO.setRefundChannelNo(NONE_VALUE);
        refundNotifyDTO.setRefundOtherNo(NONE_VALUE);
        refundNotifyDTO.setRefundStatus(OrderReturnStatusEnum.convert2AresAfterSaleRefundStatus(orderReturnPO.getState()));
        refundNotifyDTO.setRefundAmt(orderReturnPO.getReturnMoneyAmount());
        refundNotifyDTO.setRelRefundAmt(orderReturnPO.getActualReturnMoneyAmount());
        refundNotifyDTO.setRateChannel(orderReturnPO.getChannelServiceFee());
        refundNotifyDTO.setRateCompany(BigDecimal.ZERO);
        refundNotifyDTO.setRefundFinishTime(orderReturnPO.getRefundEndTime());
        refundNotifyDTO.setRefundType(Integer.parseInt(ReturnTypeEnum.getByValue(orderReturnPO.getReturnType()).getAresAfterSaleValue()));
        refundNotifyDTO.setRefundChannel(PayChannelSourceEnum.UNKNOWN.getCode());

        return refundNotifyDTO;
    }

    @Override
    public AfterSaleCreateDTO buildAfterSaleCreateDTO(OrderPO orderPO, OrderReturnPO orderReturnPO, OrderAfterPO orderAfterPO) {
        BigDecimal discountTotalAmount = orderReturnPO.getPlatformActivityAmount().add(orderReturnPO.getPlatformVoucherAmount()).add(orderReturnPO.getStoreActivityAmount()).add(orderReturnPO.getStoreVoucherAmount());
        BigDecimal refundTotalAmount = orderPO.getGoodsAmount().add(orderReturnPO.getReturnExpressAmount()).subtract(discountTotalAmount).subtract(orderReturnPO.getRefundPunishAmount());
        AfterSaleCreateDTO afterSaleCreateDTO = new AfterSaleCreateDTO();
        afterSaleCreateDTO.setAfterSaleSourceCode(OrderSourceEnum.STANDARD_MALL.getOrderSourceCode());
        afterSaleCreateDTO.setAfterSaleSourceDesc(OrderSourceEnum.STANDARD_MALL.getOrderSourceDesc());
        afterSaleCreateDTO.setAfterSaleSourceNo(orderReturnPO.getAfsSn());
        afterSaleCreateDTO.setRefundPatternCode(PayPatternEnum.BUSINESS_PAY.getPayPatternCode());
        afterSaleCreateDTO.setRefundPatternDesc(PayPatternEnum.BUSINESS_PAY.getPayPatternDesc());
        afterSaleCreateDTO.setOrderSourceCode(OrderSourceEnum.STANDARD_MALL.getOrderSourceCode());
        afterSaleCreateDTO.setOrderSourceDesc(OrderSourceEnum.STANDARD_MALL.getOrderSourceDesc());
        afterSaleCreateDTO.setOrderSourceOrderNo(orderReturnPO.getOrderSn());
        afterSaleCreateDTO.setChannel(orderPO.getChannel());
        afterSaleCreateDTO.setRefundType(ReturnTypeEnum.getByValue(orderReturnPO.getReturnType()).getAresAfterSaleValue());
        afterSaleCreateDTO.setRefundMode(PART_REFUND);
        afterSaleCreateDTO.setStatus(OrderReturnStatusEnum.getByCode(orderReturnPO.getState()).getAresAfterSaleCode());
        afterSaleCreateDTO.setStatusDesc(OrderReturnStatusEnum.getByCode(orderReturnPO.getState()).getAresAfterSaleDesc());
        afterSaleCreateDTO.setApplicantRole(ReturnByEnum.getByValue(orderReturnPO.getReturnBy()) == null ? NONE_VALUE : ReturnByEnum.getByValue(orderReturnPO.getReturnBy()).getAresAfterSaleValue());
        afterSaleCreateDTO.setApplicantCode(orderPO.getUserNo());
        afterSaleCreateDTO.setApplicantName(orderAfterPO.getContactName());
        afterSaleCreateDTO.setApplicantPhone(StringUtils.isBlank(orderAfterPO.getContactPhone()) ? NONE_VALUE : orderAfterPO.getContactPhone());
        afterSaleCreateDTO.setApplyImage(orderAfterPO.getApplyImage());
        afterSaleCreateDTO.setApplyTime(DateUtil.dateToLocalDateTime(orderReturnPO.getApplyTime()));
        afterSaleCreateDTO.setApplyReason(orderAfterPO.getApplyReasonContent());
        afterSaleCreateDTO.setApplyDescription(orderAfterPO.getAfsDescription());
        afterSaleCreateDTO.setPlatformActivityAmount(orderReturnPO.getPlatformActivityAmount());
        afterSaleCreateDTO.setPlatformCouponAmount(orderReturnPO.getPlatformVoucherAmount());
        afterSaleCreateDTO.setStoreActivityAmount(orderReturnPO.getStoreActivityAmount());
        afterSaleCreateDTO.setStoreCouponAmount(orderReturnPO.getStoreVoucherAmount());
        afterSaleCreateDTO.setDiscountTotalAmount(discountTotalAmount);
        afterSaleCreateDTO.setSkuRefundTotalAmount(orderPO.getGoodsAmount());
        afterSaleCreateDTO.setSkuTotalExpressFee(orderReturnPO.getReturnExpressAmount());
        afterSaleCreateDTO.setRefundPunishAmount(orderReturnPO.getRefundPunishAmount());
        afterSaleCreateDTO.setRefundTotalAmount(refundTotalAmount);
        afterSaleCreateDTO.setAfterSaleItems(buildAfterSaleItemCreateDTOList(orderPO, orderReturnPO, orderAfterPO));
        afterSaleCreateDTO.setAfterSaleAmounts(buildAfterSaleAmountCreateDTOList(orderPO, orderReturnPO));
        afterSaleCreateDTO.setAfterSaleRefundApplys(buildAfterSaleRefundApplyList(orderPO, orderReturnPO));
        afterSaleCreateDTO.setLoanRefundApplys(buildAfterSaleLoanReutnApplyList(orderPO, orderReturnPO));

        return afterSaleCreateDTO;

    }
    private List<AfterSaleLoanRefundApplyDTO> buildAfterSaleLoanReutnApplyList(OrderPO orderPO, OrderReturnPO orderReturnPO) {
        List<AfterSaleLoanRefundApplyDTO> afterSaleLoanRefundApplyList = new ArrayList<>();
        AfterSaleLoanRefundApplyDTO afterSaleLoanRefundApply = new AfterSaleLoanRefundApplyDTO();
        afterSaleLoanRefundApply.setAfterSaleRefundSourceNo(orderReturnPO.getAfsSn());
        afterSaleLoanRefundApply.setPayOrderSourceNo(orderPO.getPaySn());
        afterSaleLoanRefundApply.setPayOrderTypeDesc(OrderTypeEnum.getValue(orderPO.getOrderType()) == null ? OrderTypeEnum.NORMAL.getDesc() : OrderTypeEnum.getValue(orderPO.getOrderType()).getDesc());
        afterSaleLoanRefundApply.setPayOrderTypeCode(OrderTypeEnum.getValue(orderPO.getOrderType()) == null ? OrderTypeEnum.NORMAL.getValue() : OrderTypeEnum.getValue(orderPO.getOrderType()).getValue());
        afterSaleLoanRefundApply.setLoanCustId(NONE_VALUE);
        afterSaleLoanRefundApply.setLoanCustName(NONE_VALUE);
        afterSaleLoanRefundApply.setMerchantId(NONE_VALUE);
        afterSaleLoanRefundApply.setMerchantAcctNo(NONE_VALUE);
        afterSaleLoanRefundApply.setOperatorNo(NONE_VALUE);
        afterSaleLoanRefundApply.setOperatorName(NONE_VALUE);
        afterSaleLoanRefundApply.setOperatorBranch(NONE_VALUE);
        afterSaleLoanRefundApplyList.add(afterSaleLoanRefundApply);
        return afterSaleLoanRefundApplyList;
    }
    private List<AfterSaleRefundApplyCreateDTO> buildAfterSaleRefundApplyList(OrderPO orderPO, OrderReturnPO orderReturnPO) {
        List<AfterSaleRefundApplyCreateDTO> afterSaleRefundApplyList = new ArrayList<>();
        AfterSaleRefundApplyCreateDTO afterSaleRefundApply = new AfterSaleRefundApplyCreateDTO();
        afterSaleRefundApply.setPayOrderTypeCode(OrderTypeEnum.getValue(orderPO.getOrderType()) == null ? OrderTypeEnum.NORMAL.getValue() : OrderTypeEnum.getValue(orderPO.getOrderType()).getValue());
        afterSaleRefundApply.setPayOrderTypeDesc(OrderTypeEnum.getValue(orderPO.getOrderType()) == null ? OrderTypeEnum.NORMAL.getDesc() : OrderTypeEnum.getValue(orderPO.getOrderType()).getDesc());
        afterSaleRefundApply.setAfterSaleRefundSourceNo(orderReturnPO.getAfsSn());
        afterSaleRefundApply.setPayOrderSourceNo(orderPO.getPaySn());
        afterSaleRefundApply.setRefundWay(RefundType.value(orderReturnPO.getRefundType()) == null ? RefundType.OTHER.getAresAfterSaleValue() : RefundType.value(orderReturnPO.getRefundType()).getAresAfterSaleValue());
        afterSaleRefundApply.setRefundWayDesc(RefundType.value(orderReturnPO.getRefundType()) == null ? RefundType.OTHER.getAresAfterSaleDesc() : RefundType.value(orderReturnPO.getRefundType()).getAresAfterSaleDesc());
        afterSaleRefundApply.setRefundApplyTime(DateUtil.dateToLocalDateTime(orderReturnPO.getApplyTime()));
        afterSaleRefundApply.setRefundAmount(orderReturnPO.getActualReturnMoneyAmount());
        afterSaleRefundApply.setRefundSn(orderReturnPO.getAfsSn());

        afterSaleRefundApplyList.add(afterSaleRefundApply);
        return afterSaleRefundApplyList;
    }

    private List<AfterSaleAmountCreateDTO> buildAfterSaleAmountCreateDTOList(OrderPO orderPO, OrderReturnPO orderReturnPO) {
        List<AfterSaleAmountCreateDTO> afterSaleAmountList = new ArrayList<>();
        afterSaleAmountList.add(buildAfterSaleAmount(orderPO, RefundAmountTypeEnum.PLATFORM_SERVICE_FEE, orderReturnPO.getAfsSn(), orderReturnPO.getChannelServiceFee()));
        afterSaleAmountList.add(buildAfterSaleAmount(orderPO, RefundAmountTypeEnum.THIRDPARTNAR_SERVICE_FEE, orderReturnPO.getAfsSn(), orderReturnPO.getThirdpartnarFee()));
        afterSaleAmountList.add(buildAfterSaleAmount(orderPO, RefundAmountTypeEnum.ORDER_COMMISSION, orderReturnPO.getAfsSn(), orderReturnPO.getOrderCommission()));
        afterSaleAmountList.add(buildAfterSaleAmount(orderPO, RefundAmountTypeEnum.PRE_LOAN_INTEREST, orderReturnPO.getAfsSn(), orderReturnPO.getPlanDiscountAmount()));
        afterSaleAmountList.add(buildAfterSaleAmount(orderPO, RefundAmountTypeEnum.CHANNEL_SERVICE_FEE, orderReturnPO.getAfsSn(), orderReturnPO.getChannelServiceFee()));
        BigDecimal discountTotalAmount = orderReturnPO.getPlatformActivityAmount().add(orderReturnPO.getPlatformVoucherAmount());
        afterSaleAmountList.add(buildAfterSaleAmount(orderPO, RefundAmountTypeEnum.PLATFORM_DISCOUNT_AMOUNT, orderReturnPO.getAfsSn(), discountTotalAmount));
        afterSaleAmountList.add(buildAfterSaleAmount(orderPO, RefundAmountTypeEnum.CUSTOMER_ASSUME_AMOUNT, orderReturnPO.getAfsSn(), orderReturnPO.getCustomerAssumeAmount()));
        afterSaleAmountList.add(buildAfterSaleAmount(orderPO, RefundAmountTypeEnum.COMMISSION_SERVICE_AMOUNT, orderReturnPO.getAfsSn(), orderReturnPO.getCommissionServiceFee()));
        return afterSaleAmountList;
    }
    private AfterSaleAmountCreateDTO buildAfterSaleAmount(OrderPO orderPO, RefundAmountTypeEnum amountType, String afsSn, BigDecimal amount) {
        AfterSaleAmountCreateDTO afterSaleAmount = new AfterSaleAmountCreateDTO();
        afterSaleAmount.setAfterSaleRefundSourceNo(afsSn);
        afterSaleAmount.setPayOrderTypeCode(OrderTypeEnum.getValue(orderPO.getOrderType()) == null ? OrderTypeEnum.NORMAL.getValue() : OrderTypeEnum.getValue(orderPO.getOrderType()).getValue());
        afterSaleAmount.setPayOrderTypeCodeDesc(OrderTypeEnum.getValue(orderPO.getOrderType()) == null ? OrderTypeEnum.NORMAL.getDesc() : OrderTypeEnum.getValue(orderPO.getOrderType()).getDesc());
        afterSaleAmount.setAmountType(amountType.getValue());
        afterSaleAmount.setAmountTypeDesc(amountType.getDesc());
        afterSaleAmount.setStatus(EffectiveStatusEnum.EFFECTIVE.getCode());
        afterSaleAmount.setStatusDesc(EffectiveStatusEnum.EFFECTIVE.getDesc());
        afterSaleAmount.setAmount(amount == null ? BigDecimal.ZERO : amount);

        return afterSaleAmount;
    }

    private List<AfterSaleItemCreateDTO> buildAfterSaleItemCreateDTOList(OrderPO orderPO, OrderReturnPO orderReturnPO, OrderAfterPO orderAfterPO) {
        List<AfterSaleItemCreateDTO> afterSaleItemCreateDTOList = new ArrayList<>();
        afterSaleItemCreateDTOList.add(buildAfterSaleItemCreateDTO(orderPO, orderReturnPO, orderAfterPO));
        return afterSaleItemCreateDTOList;
    }

    private AfterSaleItemCreateDTO buildAfterSaleItemCreateDTO(OrderPO orderPO, OrderReturnPO orderReturnPO, OrderAfterPO orderAfterPO) {
        BigDecimal discountTotalAmount = orderReturnPO.getPlatformActivityAmount().add(orderReturnPO.getPlatformVoucherAmount()).add(orderReturnPO.getStoreActivityAmount()).add(orderReturnPO.getStoreVoucherAmount());
        BigDecimal refundTotalAmount = orderPO.getGoodsAmount().add(orderReturnPO.getReturnExpressAmount()).subtract(discountTotalAmount).subtract(orderReturnPO.getRefundPunishAmount());
        OrderProductPO orderProduct = orderProductModel.getOrderProductById(orderAfterPO.getOrderProductId());
        AfterSaleItemCreateDTO afterSaleItem = new AfterSaleItemCreateDTO();
        afterSaleItem.setPerformanceStatusCode(PerformanceStatusEnum.DONE.getCode());
        afterSaleItem.setPerformanceStatusDesc(PerformanceStatusEnum.DONE.getDesc());
        afterSaleItem.setSpuCode(String.valueOf(orderAfterPO.getGoodsId()));
        afterSaleItem.setSkuName(orderProduct == null ? "" : orderProduct.getGoodsName());
        afterSaleItem.setSkuCode(String.valueOf(orderProduct.getProductId()));
        afterSaleItem.setQuantity(new BigDecimal(orderReturnPO.getReturnNum()));
        afterSaleItem.setSkuRefundTotalAmount(orderPO.getGoodsAmount());
        afterSaleItem.setExpressFee(orderReturnPO.getReturnExpressAmount());
        afterSaleItem.setRefundTotalAmount(refundTotalAmount);
        afterSaleItem.setPlatformActivityAmount(orderReturnPO.getPlatformActivityAmount());
        afterSaleItem.setPlatformCouponAmount(orderReturnPO.getPlatformVoucherAmount());
        afterSaleItem.setStoreActivityAmount(orderReturnPO.getStoreActivityAmount());
        afterSaleItem.setStoreCouponAmount(orderReturnPO.getStoreVoucherAmount());
        afterSaleItem.setDiscountTotalAmount(discountTotalAmount);
        return afterSaleItem;
    }
}
