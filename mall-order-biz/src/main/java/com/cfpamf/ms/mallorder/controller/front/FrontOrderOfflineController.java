package com.cfpamf.ms.mallorder.controller.front;

import com.cfpamf.ms.mallorder.po.OrderOfflinePO;
import com.cfpamf.ms.mallorder.service.OrderOfflineService;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * front-线下订单操作controller
 */
@RestController
@RequestMapping("/front/OrderOffline")
@Api(tags = "front-线下订单操作")
public class FrontOrderOfflineController{

	@Autowired
	private OrderOfflineService orderOfflineService;

	@ApiOperation("线下订单额外信息查询")
	@GetMapping("query/detail")
	public JsonResult<List<OrderOfflinePO>> queryByPaySn(String paySn) {
		List<OrderOfflinePO> data = orderOfflineService.queryOrderOfflineList(paySn);
		return SldResponse.success(data);
	}
    

}
