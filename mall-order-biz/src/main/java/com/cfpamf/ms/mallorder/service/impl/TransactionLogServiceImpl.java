package com.cfpamf.ms.mallorder.service.impl;

import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cfpamf.ms.mallorder.common.enums.transaction.TransactionStatusEnum;
import com.cfpamf.ms.mallorder.common.util.OrderSubmitAttributesUtils;
import com.cfpamf.ms.mallorder.common.util.ValidUtils;
import com.cfpamf.ms.mallorder.po.TransactionLogPO;
import com.cfpamf.ms.mallorder.mapper.TransactionLogMapper;
import com.cfpamf.ms.mallorder.service.ITransactionLogService;
import com.cfpamf.framework.autoconfigure.mybatis.BaseRepoServiceImpl;
import com.cfpamf.smartid.client.utils.sharding.SeqEnum;
import com.cfpamf.smartid.client.utils.sharding.ShardingId;
import com.google.api.client.util.Lists;
import com.slodon.bbc.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 事务记录日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-06
 */
@Service
@Slf4j
public class TransactionLogServiceImpl extends BaseRepoServiceImpl<TransactionLogMapper, TransactionLogPO> implements ITransactionLogService {
    /**
     * 订单全局事务前缀
     *
     */
    private static final String TRANSACTION_PREFIX = "mall_order_transaction";
    /**
     * 分隔符
     */
    private static final String SPLIT ="@";

    @Autowired
    private OrderSubmitAttributesUtils orderSubmitAttributesUtils;

    @Autowired
    private ShardingId shardingId;

    /**
     * 生成全局事务id
     *
     * @return 全局事务id
     */
    @Override
    public String generateTransactionId() {
        // 前缀+smartId，直接使用orderProduct的smartId
        return String.valueOf(shardingId.next(SeqEnum.OPO, "system"));
    }

    /**
     * 生成新事务
     *
     * @param logType       事务类型
     * @param role          事务角色
     * @return 事务记录po
     */
    @Override
    @Transactional(rollbackFor = Exception.class,transactionManager = "masterdbTx",propagation = Propagation.REQUIRES_NEW)
    public TransactionLogPO generateNewTransactionLogPO(Integer logType, Integer role) {
        log.info("TransactionLogServiceImpl generateNewTransactionLogPO,logType:{},role:{}",logType,role);
        TransactionLogPO po = new TransactionLogPO();
        po.setLogType(logType);
        po.setRole(role);
        po.setStatus(TransactionStatusEnum.READY.getCode());
        String existTransactionId = orderSubmitAttributesUtils.getTransactionId();
        if (StringUtils.isBlank(existTransactionId)){
            existTransactionId = generateTransactionId();
            orderSubmitAttributesUtils.setTransactionId(existTransactionId);
        }
        po.setTransactionId(existTransactionId);
//        po.setRequestParam(requestParam);
        po.setRequestTime(new Date());
        save(po);
        log.info("TransactionLogServiceImpl generateNewTransactionLogPO result:{}",po);
        return po;
    }

    /**
     * 根据事务id更新事务状态
     *
     * @param transactionId 事务id
     * @param oldStatus     旧状态，可为空
     * @param newStatus     新状态，不可为空
     * @return 更新结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTransactionStatusById(String transactionId, Integer oldStatus, Integer newStatus) {
        log.info("TransactionLogServiceImpl updateTransactionStatusById,transactionId:{},oldStatus:{},newStatus:{}",transactionId,oldStatus,newStatus);
        if (null == TransactionStatusEnum.getByCode(newStatus)){
            log.error("TransactionLogServiceImpl updateTransactionStatusById 事务状态不在指定枚举内,transactionId:{},oldStatus:{},newStatus:{}",transactionId,oldStatus,newStatus);
            throw new BusinessException("事务记录更新失败");
        }
        if (StringUtils.isBlank(transactionId)){
            log.error("事务记录更新失败，事务id为空");
            throw new BusinessException("实物记录更新失败");
        }
        LambdaUpdateWrapper<TransactionLogPO> logUpdate = Wrappers.lambdaUpdate(TransactionLogPO.class);
        logUpdate.eq(TransactionLogPO::getTransactionId,transactionId);
        logUpdate.eq(null != oldStatus,TransactionLogPO::getStatus,oldStatus);
        logUpdate.set(TransactionLogPO::getStatus,newStatus);
        TransactionStatusEnum status = TransactionStatusEnum.getByCode(newStatus);
        if (null != status){
            switch (status){
                case COMMITTED:
                    logUpdate.set(TransactionLogPO::getCommitTime,new Date());
                    break;
                case ROLLBACK:
                    logUpdate.set(TransactionLogPO::getRollbackTime,new Date());
                    break;
                default:
                    break;
            }
        }else{
            log.error("updateTransactionStatusById 错误的状态类型");
        }
        update(logUpdate);
    }

    /**
     * 根据事务id获取事务记录列表
     *
     * @param transactionId 事务id
     * @return 事务记录列表
     */
    @Override
    public List<TransactionLogPO> listTransactionLogByTransactionId(String transactionId) {
        if (StringUtils.isBlank(transactionId)){
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<TransactionLogPO> logQuery = Wrappers.lambdaQuery(TransactionLogPO.class);
        logQuery.eq(TransactionLogPO::getTransactionId,transactionId);
        return list(logQuery);
    }

    /**
     * 更新请求参数
     *
     * @param id         主键
     * @param jsonString 参数
     */
    @Override
    public void updateRequestParam(Long id, String jsonString) {
        LambdaUpdateWrapper<TransactionLogPO> logPoUpdate = Wrappers.lambdaUpdate(TransactionLogPO.class);
        logPoUpdate.eq(TransactionLogPO::getId,id);
        logPoUpdate.set(TransactionLogPO::getRequestParam,jsonString);
        update(logPoUpdate);
    }
}
