package com.cfpamf.ms.mallorder.integration.facade.dto;

import com.cfpamf.framework.autoconfigure.mybatis.page.PageQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ErpActualReturnInFlowQueryDTO extends PageQuery {

    @ApiModelProperty(value = "关联业务单号")
    private String linkNo;

    @ApiModelProperty(value = "货品sku编码", notes = "主单 linkNo 记录的是订单号，不是售后单号，需要基于skuNo才能查到对应的售后退货信息")
    private List<String> skuNoList;

}
