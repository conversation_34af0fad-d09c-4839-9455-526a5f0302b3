package com.cfpamf.ms.mallorder.v2.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description:
 * @Author: zml
 * @CreateTime: 2022-05-19
 */
@Data
public class PreSellPromotionPayDetail {

    @ApiModelProperty(value = "订金总金额")
    private BigDecimal totalDepositAmount;

    @ApiModelProperty(value = "应付总尾款")
    private BigDecimal totalRemainAmount;

    @ApiModelProperty(value = "订金支付详情")
    private PayDetail depositPayDetail;

    @ApiModelProperty(value = "尾款支付详情")
    private PayDetail remainAmountDetail;


    @Data
    public static class PayDetail {

        @ApiModelProperty(value = "截止时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
        private Date deadTime;

        @ApiModelProperty(value = "支付单号")
        private String payNo;

        @ApiModelProperty(value = "支付方式")
        private String paymentCode;

        @ApiModelProperty(value = "支付方式名称")
        private String paymentName;

        @ApiModelProperty(value = "支付金额")
        private BigDecimal payAmount;

        @ApiModelProperty(value = "支付状态：1、待支付 2、支付中 3、已支付")
        private Integer payStatus;

        @ApiModelProperty(value = "支付时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
        private Date payTime;

    }
}
