package com.cfpamf.ms.mallorder.service;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.mallorder.enums.OrderAmountStateEnum;
import com.cfpamf.ms.mallorder.enums.OrderAmountTypeEnum;
import com.cfpamf.ms.mallorder.po.OrderAmountStateRecordPO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.vo.OrderAmountStateRecordVO;


import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 订单金额记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
public interface IOrderAmountStateRecordService extends IService<OrderAmountStateRecordPO> {

    /**
     * 初始化指定的订单资金项
     *
     * @param orderPO               订单信息
     * @param orderAmountTypeEnum   订单资金项
     * @return                      处理结果 true/false
     */
    Boolean initOrderAmountState(OrderPO orderPO, OrderAmountTypeEnum orderAmountTypeEnum);

    Result<Void> initOrderAmountState(OrderPO orderPO);

    Result<Void> modifyOrderAmountState(String orderSn, OrderAmountTypeEnum amountTypeEnum, BigDecimal amount,
                                        OrderAmountStateEnum stateEnum);

    Result<List<OrderAmountStateRecordVO>> listByOrderSn(String orderSn, OrderAmountTypeEnum amountType);

    Result<OrderAmountStateRecordVO> getOneByOrderSn(String orderSn, OrderAmountTypeEnum amountType);

    Result<OrderAmountStateRecordVO> getOneByOrderSn(String orderSn, OrderAmountTypeEnum amountType, OrderAmountStateEnum stateEnum);

    /**
     * 保存平台服务费结算金额
     * @param orderSn
     * @param payNo
     * @param orderPlatformServiceFee
     * @return
     */
    Boolean saveServiceFeeAmount(String orderSn, String payNo, BigDecimal orderPlatformServiceFee);

    /**
     * 更新
     * @param orderAmountStateRecord
     * @return
     */
    boolean updateOrderAmountStateRecord(OrderAmountStateRecordPO orderAmountStateRecord);

}
