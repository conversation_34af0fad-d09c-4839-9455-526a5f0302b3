package com.cfpamf.ms.mallorder.integration.hrms;

import com.alibaba.fastjson.JSONObject;
import com.cdfinance.hrms.facade.request.outer.QueryEmployeeContactInfoRequest;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.framework.autoconfigure.common.exception.MSException;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.cfpamf.ms.mallorder.controller.fegin.facade.HrmsEmployeeFacade;
import com.cfpamf.ms.mallorder.vo.hrmsVO.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Slf4j
@Component
public class HrmsEmployeeFeignIntegration {
    @Autowired
    private HrmsEmployeeFacade hrmsEmployeeFacade;

    public OutEmployeeVO queryOnboardEmployeeByCode(String employeeCode) {
        if (StringUtils.isBlank(employeeCode)) {
            return null;
        }
        Result<OutEmployeeVO> result = null;
        try {
            QueryEmployeeContactInfoRequest request = new QueryEmployeeContactInfoRequest();
            request.setEmployeeCode(employeeCode);
            log.info("根据员工工号查询hrms的个人详情,employeeCode:{}",employeeCode);
            result = hrmsEmployeeFacade.queryEmployeeContactInfo(request);
        } catch (Exception ex) {
            log.error("根据员工工号查询个人详情调用服务异常,employeeCode:{}", employeeCode, ex);
            throw new MSException(String.valueOf(ErrorCodeEnum.C.CALL_EXCEPTION.getCode()), "根据员工工号查询个人详情调用服务异常");
        }
        if (Objects.isNull(result) || !result.isSuccess()) {
            throw new MSException(String.valueOf(ErrorCodeEnum.C.RESULT_INVALID.getCode()), "根据员工工号查询个人详情返回结果为空");
        }
        log.info("根据员工工号查询hrms的个人详情,返回结果:{}", JSONObject.toJSONString(result.getData()));
        return result.getData();
    }

    public EmployeeVO queryEmployeeByIdNo(String idNo) {
        Result<EmployeeVO> result = null;
        try {
            log.info("根据idNo查询hrms的个人详情,idNo:{}",idNo);
            result = hrmsEmployeeFacade.getByIdNo(idNo);
        } catch (Exception ex) {
            log.error("根据id查询个人详情出现未知异常,idNo:{}", idNo, ex);
            throw new MSException(String.valueOf(ErrorCodeEnum.C.CALL_EXCEPTION.getCode()), "根据id查询个人详情调用服务异常");
        }
        if (Objects.isNull(result) || !result.isSuccess()) {
            throw new MSException(String.valueOf(ErrorCodeEnum.C.RESULT_INVALID.getCode()), "根据id查询个人详情返回结果为空");
        }
        log.info("根据idNo查询hrms的个人详情,返回结果:{}", JSONObject.toJSONString(result.getData()));
        return result.getData();
    }

    public OutEmployeeVO queryEmployeeByMobile(String mobile) {
        Result<OutEmployeeVO> result = null;
        try {
            QueryEmployeeContactInfoRequest request = new QueryEmployeeContactInfoRequest();
            request.setMobile(mobile);
            log.info("根据mobile查询hrms的个人详情,mobile:{}",mobile);
            result = hrmsEmployeeFacade.queryEmployeeContactInfo(request);
        } catch (Exception ex) {
            log.error("根据mobile查询个人详情调用服务异常,mobile:{}", mobile, ex);
            throw new MSException(String.valueOf(ErrorCodeEnum.C.CALL_EXCEPTION.getCode()), "根据mobile查询个人详情调用服务异常");
        }
        if (Objects.isNull(result) || !result.isSuccess()) {
            throw new MSException(String.valueOf(ErrorCodeEnum.C.RESULT_INVALID.getCode()), "根据mobile查询个人详情返回结果为空");
        }
        log.info("根据mobile查询hrms的个人详情,返回结果:{}", JSONObject.toJSONString(result.getData()));
        return result.getData();
    }
}
