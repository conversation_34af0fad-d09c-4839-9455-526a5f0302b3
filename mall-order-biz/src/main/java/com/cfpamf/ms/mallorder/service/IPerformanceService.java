package com.cfpamf.ms.mallorder.service;

import com.cfpamf.ms.mallorder.common.enums.OrderCreateChannel;
import com.cfpamf.ms.mallorder.dto.ErpPerformanceStock;
import com.cfpamf.ms.mallorder.dto.UserDTO;
import com.cfpamf.ms.mallorder.po.OrderExtendPO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderProductPO;
import com.cfpamf.ms.mallorder.req.OrderDeliveryReq;
import com.cfpamf.ms.mallorder.vo.hrmsVO.OutBranchLifeAndInteriorInfoVO;
import com.cfpamf.ms.mallshop.resp.Vendor;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface IPerformanceService {

    /**
     * ERP履约渠道,校验出库数量 
     * Outbound
     */
    void erpOutBoundCheck(OrderPO orderPODb, List<OrderProductPO> productPOList, OrderExtendPO orderExtendPO, OrderDeliveryReq deliveryReq);

    /**
     * 判断订单商品是否出库中
     * @return
     */
    boolean outbounding(String orderSn);

    ErpPerformanceStock buildErpOutBoundParam(List<Long> orderProductIds,
                                              OrderDeliveryReq deliveryReq,
                                              OrderPO orderPODb,
                                              Vendor vendor);

    /**
     * 指定店铺的订单需要发送短信签收码
     */
    void sendSmsReceiveCodeForDelivery(String orderSn);

    String sendSmsReceiveCode(String orderSn,String userMobile);

    String sendSmsDeliveryCode(String orderSn,String userMobile);

    String sendSmsReceiveCodeAndInsertLog(String orderSn,UserDTO userDTO, OrderCreateChannel channel);

    /**
     * 获取订单签收验证码
     */
    String getOrderReceiveCode(String orderSn);

    boolean isReceiveCodeStore(Long storeId);
    boolean isExtendReceiveStore(Long storeId);

    List<String> getReceiveCodeStoreList();

    /**
     * 自提订单 自动发货
     * @param orderSn
     * @return
     */
    boolean selfLiftOrderAutoDelivery(String orderSn);


    /**
     * 更新商品行待发货的为出库中
     * @param orderProductIds
     */
    int updateProductForOutbound(Set<Long> orderProductIds) ;
    /**
     * 更新商品行出库中的为待发货
     * @param productIdList
     */
    int outboundToWaitDelivery(String orderSn, List<Long> productIdList);

    void outOfStackDingdingRemind(String orderSn,String skuName,String spuName, String requestStockNum, String currentStockNum);

    /**
     * 线下订单生成签收码
     *
     * @param orderPO 订单信息
     */
    void createReceiveCodeForOffline(OrderPO orderPO);

    /**
     * 根据分支编码查询对应的生服对接人以及分支内务
     *
     * @param branchCodeList 分支编码列表
     * @return 分支对接人、分支内务信息
     */
    Map<String, OutBranchLifeAndInteriorInfoVO> queryBranchLifeAndInteriorList(List<String> branchCodeList);
}
