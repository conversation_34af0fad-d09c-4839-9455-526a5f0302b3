package com.cfpamf.ms.mallorder.integration.filecenter;

import com.cfpamf.ms.mall.filecenter.constant.IScenesMaterialProofConstant;
import com.cfpamf.ms.mall.filecenter.domain.dto.FileScenesMaterialProofDTO;
import com.cfpamf.ms.mall.filecenter.domain.request.MaterialProofRequest;
import com.cfpamf.ms.mall.filecenter.domain.request.ScenesMaterialProofRequest;
import com.cfpamf.ms.mall.filecenter.domain.vo.FileScenesProofVO;
import com.cfpamf.ms.mall.filecenter.domain.vo.FileScenesWithMaterialVO;
import com.cfpamf.ms.mall.filecenter.domain.vo.MaterialVO;
import com.cfpamf.ms.mall.filecenter.facade.FileScenesMaterialProofFacade;
import com.cfpamf.ms.mallorder.common.util.ExternalApiUtil;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.response.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * 文件中心对接
 */
@Slf4j
@Component
public class FileCenterIntegration {

	@Autowired
	private FileScenesMaterialProofFacade fileScenesMaterialProofFacade;

	public List<FileScenesWithMaterialVO> listScenesMaterialProof(String proofNo) {
		ScenesMaterialProofRequest request = new ScenesMaterialProofRequest();
		request.setProofNo(proofNo);
		request.setProofType(IScenesMaterialProofConstant.ProofTypeEnum.MALL_ORDER_ORDER_SN);

		JsonResult<List<FileScenesWithMaterialVO>> result = ExternalApiUtil.callJsonResultApi(() -> fileScenesMaterialProofFacade.listScenesMaterialProof(request),
				request, "/scenesMaterialProof/list", "查询订单所有场景资料");

		return result.getData();
	}

	public FileScenesWithMaterialVO queryScenesProofMaterial(String proofNo, IScenesMaterialProofConstant.ProofTypeEnum proofType, SceneTypeEnum sceneTypeEnum) {
		ScenesMaterialProofRequest proofRequest = new ScenesMaterialProofRequest();
		proofRequest.setProofNo(proofNo);
		proofRequest.setProofType(proofType);
		proofRequest.setSceneNo(sceneTypeEnum.getValue());
		JsonResult<FileScenesWithMaterialVO> jsonResult = fileScenesMaterialProofFacade.queryScenesMaterialProof(proofRequest);
		if (Objects.isNull(jsonResult) || HttpStatus.OK.value() != jsonResult.getState()
				|| Objects.isNull(jsonResult.getData()) || CollectionUtils.isEmpty(jsonResult.getData().getMaterialVOList())) {
			log.info("获取{}的相关资料失败,proofNo:{}",sceneTypeEnum.getDesc(), proofNo);
			throw new BusinessException(String.format("获取%s的相关资料失败", sceneTypeEnum.getDesc()));
		}
		List<MaterialVO> materialVOList = jsonResult.getData().getMaterialVOList();
		Optional<MaterialVO> blankOptional = materialVOList.stream()
				.filter(x -> NumberUtils.INTEGER_ONE.equals(x.getRequisite()) && CollectionUtils.isEmpty(x.getMaterialContentList()))
				.findFirst();
		if (blankOptional.isPresent()) {
			log.info("{}必须上传相关资料,proofNo:{}", sceneTypeEnum.getDesc(), proofNo);
			throw new BusinessException(String.format("%s必须上传相关资料", sceneTypeEnum.getDesc()));
		}
		return jsonResult.getData();
	}

	public Boolean saveScenesMaterialProofV2(FileScenesMaterialProofDTO paramDTO) {
		JsonResult<Boolean> result = ExternalApiUtil.callJsonResultApi(() ->
						fileScenesMaterialProofFacade.saveScenesMaterialProofV2(paramDTO), paramDTO,
				"/scenesMaterialProof/save/v2", "保存订单场景资料");

		return result.getData();

	}

	public List<FileScenesProofVO> queryScenesMaterialProofV2(String proofNo, String subProofNo, String sceneNo,
															  String materialNo, String proofRemark, String picEncrypt) {
		MaterialProofRequest proofRequest = new ScenesMaterialProofRequest();
		proofRequest.setProofNo(proofNo);
		proofRequest.setProofType(IScenesMaterialProofConstant.ProofTypeEnum.MALL_ORDER_ORDER_SN);
		if (StringUtils.isNotBlank(subProofNo)) {
			proofRequest.setSubProofNo(subProofNo);
			proofRequest.setSubProofType(IScenesMaterialProofConstant.ProofTypeEnum.MALL_ORDER_ORDER_PRODUCT);
		}
		proofRequest.setSceneNo(sceneNo);
		proofRequest.setMaterialNo(materialNo);
		proofRequest.setProofRemark(proofRemark);
		proofRequest.setPicEncrypt(picEncrypt);

		JsonResult<List<FileScenesProofVO>> result = ExternalApiUtil.callJsonResultApi(() ->
						fileScenesMaterialProofFacade.queryScenesMaterialProofV2(proofRequest), proofRequest,
				"/scenesMaterialProof/query/v2", "查询订单场景资料");

		return result.getData();
	}

}
