package com.cfpamf.ms.mallorder.controller.fegin.facade.workflow.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 节点信息
 * <AUTHOR>
 * @date 2024-02-20
 * @since JDK 1.8
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FlowNodeInfoVO {

    /**
     * 流程定义key
     */
    private String definitionKey;

    /**
     * 节点key
     */
    private String nodeKey;

    /**
     * 节点名称
     */
    private String nodeName;

    /**
     * 是否逻辑删除
     */
    private Integer enableFlag;

    /**
     * 节点顺序
     */
    private String nodeOrderNo;

}
