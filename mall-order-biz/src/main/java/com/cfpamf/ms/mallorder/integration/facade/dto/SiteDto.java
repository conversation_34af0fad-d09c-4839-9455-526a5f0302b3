package com.cfpamf.ms.mallorder.integration.facade.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
public class SiteDto {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "站点名称")
    private String siteName;

    @ApiModelProperty(value = "商家id")
    private Long merchantId;

    @ApiModelProperty(value = "商家名称")
    private String merchantName;

    @ApiModelProperty(value = "供应商")
    private String supplier;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @NotNull(message = "是否默认站点不能为空")
    @ApiModelProperty(value = "是否为默认站点：0-否 1-是")
    private Integer isDefaultSite;

    @NotNull(message = "站点类型不能为空")
    @ApiModelProperty(value = "站点类型：1-发货站 2-服务站 3-收货站 4-转运站")
    private Integer siteType;

    @ApiModelProperty(value = "站点分类：1-平台站点 2-分支站点")
    private Integer siteCategory = 1;

    @ApiModelProperty(value = "站点联系人")
    private String siteContacts;

    @ApiModelProperty(value = "联系电话")
    private String contactNumber;

    @ApiModelProperty(value = "收货省编码")
    @NotEmpty(message = "收货省编码不能为空")
    private String province;

    @ApiModelProperty(value = "收货市编码")
    @NotEmpty(message = "收货市编码不能为空")
    private String city;

    @ApiModelProperty(value = "收货区编码")
    @NotEmpty(message = "收货区编码不能为空")
    private String county;

    @ApiModelProperty(value = "所属乡镇编码")
    @NotEmpty(message = "乡镇编码不能为空")
    private String town;

    @ApiModelProperty(value = "所属乡镇名称")
    @NotEmpty(message = "乡镇名称不能为空")
    private String townDesc;

    @ApiModelProperty(value = "收货省")
    @NotEmpty(message = "收货省不能为空")
    private String provinceDesc;

    @ApiModelProperty(value = "收货市")
    @NotEmpty(message = "收货市不能为空")
    private String cityDesc;

    @ApiModelProperty(value = "收货区")
    @NotEmpty(message = "收货区不能为空")
    private String countyDesc;

    @ApiModelProperty(value = "详细地址")
    @NotEmpty(message = "详细地址不能为空")
    private String detailAddress;

    @ApiModelProperty(value = "分支组织树")
    private String branchTree;

    @ApiModelProperty(value = "分支编号")
    private String branchCode;

    @ApiModelProperty(value = "分支名称")
    private String branchName;

    @ApiModelProperty(value = "经度")
    private String longitude;

    @ApiModelProperty(value = "纬度")
    private String latitude;

    @ApiModelProperty(value = "物流方式：1-汽运 2-自提 3-火运 4-水运")
    private Integer carType;
}
