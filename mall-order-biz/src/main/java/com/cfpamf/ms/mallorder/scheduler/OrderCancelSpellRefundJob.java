package com.cfpamf.ms.mallorder.scheduler;

import com.cfpamf.framework.autoconfigure.redis.lock.DistributeLock;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.service.IOrderService;
import com.cfpamf.ms.mallorder.v2.config.CommonConfig;
import com.cfpamf.ms.mallorder.v2.domain.vo.OperationUserVO;
import com.cfpamf.ms.mallorder.v2.service.OrderRefundService;
import com.cfpamf.ms.mallpromotion.api.SpellTeamFeignClient;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.util.AssertUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 系统自动发起对拼团失败的订单取消操作（包括已付款和未付款） cron: 0 0/15 * * * ?
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/4 15:22
 */
@Component
@Slf4j
public class OrderCancelSpellRefundJob {

    /**
     * 需要取消订单的key
     */
    public static final String CANCEL_ORDER_SN = "cancelOrderSn";

    /**
     * 需要完成订单的key
     */
    public static final String SIMULATE_ORDER_SN = "simulateOrderSn";

    public static final String SPELL_CANCEL_LOCK_KEY = "newmall:spell:lock";

    @Resource
    private SpellTeamFeignClient spellTeamFeignClient;

    @Autowired
    private IOrderService orderService;

    @Resource
    private DistributeLock distributeLock;

    @Autowired
    private OrderModel orderModel;

    @Autowired
    private OrderRefundService orderRefundService;

    @Autowired
    private CommonConfig commonConfig;

    @XxlJob(value = "OrderCancelSpellRefundJob")
    public ReturnT<String> execute(String s) {

        long startTime = System.currentTimeMillis();
        XxlJobHelper.log("start job at local time: {0}", LocalDateTime.now());

        log.info("jobSystemCancelOrder() start");
        try {
            if (commonConfig.isOpenV2()) {
                log.info("开启V2流程拼团订单取消或退款申请自动审批退款，jobSystemLaunchSpellRefund()");
                boolean jobResult = this.jobSystemLaunchSpellRefund();
                AssertUtil.isTrue(!jobResult, "[jobSystemLaunchSpellRefund] 定时任务系统自动拼团订单取消或退款申请失败");
                XxlJobHelper.log("finish job at local time: {0}  cost:{1}", LocalDateTime.now(),
                    (System.currentTimeMillis() - startTime) / 1000);
                return ReturnT.SUCCESS;
            }
            boolean jobResult = orderModel.handleFailSpellTeam();
            AssertUtil.isTrue(!jobResult, "[jobSystemLaunchSpellRefund] 定时任务自动发起拼团失败的退款时失败");
        } catch (Exception e) {
            log.error("系统自动发起对拼团失败的订单取消操作异常：{}", e.getMessage());
        }

        XxlJobHelper.log("finish job at local time: {0}  cost:{1}", LocalDateTime.now(),
            (System.currentTimeMillis() - startTime) / 1000);

        return ReturnT.SUCCESS;
    }

    private boolean jobSystemLaunchSpellRefund() {
        return distributeLock.lockAndProcess(OrderCancelSpellRefundJob.SPELL_CANCEL_LOCK_KEY, 0, 60, TimeUnit.SECONDS,
            () -> {
                // 获取拼团超过截团时间的订单，包括需要成功的和失败的
                JsonResult<Map<String, List<String>>> orders = spellTeamFeignClient.getSpellTeamNeededCancel();
                if (Objects.isNull(orders.getData())) {
                    log.warn("当前无，拼团超过截团时间的订单，包括需要成功的和失败的订单，无需处理", orders);
                    return false;
                }

                log.info("拼团超时获取的订单信息为：【{}】", orders.getData().toString());
                // 需要完成的订单
                List<String> simulateOrderSn = orders.getData().get(OrderCancelSpellRefundJob.SIMULATE_ORDER_SN);
                // 完成订单
                if (!CollectionUtils.isEmpty(simulateOrderSn)) {
                    orderService.setOrdersDeliverable(simulateOrderSn);
                }
                // 需要取消的订单
                List<String> cancelOrderSns = orders.getData().get(OrderCancelSpellRefundJob.CANCEL_ORDER_SN);
                // 取消订单
                if (!CollectionUtils.isEmpty(cancelOrderSns)) {
                    log.info("V2流程取消订单，job拼团订单取消或退款申请。cancelOrderSns:{}", cancelOrderSns);
                    for (String orderSn : cancelOrderSns) {
                        try {
                            log.info("V2流程取消订单，job拼团订单取消或退款申请。orderSn:{}", orderSn);
                            orderRefundService.orderRefundApplyAndAutoRefundMoney(orderSn,
                                new OperationUserVO(OrderConst.LOG_ROLE_ADMIN, OrderConst.RETURN_BY_0, 0, "system",
                                    "系统自动取消拼团订单"),
                                "系统自动取消拼团订单", "系统自动取消拼团订单", "system");
                        } catch (Exception e) {
                            log.error("开启V2流程取消订单，拼团订单取消或退款申请。orderSn:{}", orderSn, e);
                        }
                    }
                }
                return true;
            });

    }
}
