package com.cfpamf.ms.mallorder.controller.admin;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.cfpamf.cmis.common.utils.DateUtils;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.framework.autoconfigure.ZhnxServiceException;
import com.cfpamf.mallpayment.facade.request.loan.SetlTryResultVo;
import com.cfpamf.mallpayment.facade.vo.CDMallRefundTryResultVO;
import com.cfpamf.ms.mall.filecenter.domain.dto.FileDTO;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.common.constant.SentenceConst;
import com.cfpamf.ms.mallorder.common.enums.*;
import com.cfpamf.ms.mallorder.common.util.OrderLocalUtils;
import com.cfpamf.ms.mallorder.common.util.PoiUtils;
import com.cfpamf.ms.mallorder.constant.ExchangeOrderConst;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.*;
import com.cfpamf.ms.mallorder.mapper.OrderReturnTrackMapper;
import com.cfpamf.ms.mallorder.model.*;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.req.RefundTypeChangeRequest;
import com.cfpamf.ms.mallorder.req.admin.*;
import com.cfpamf.ms.mallorder.req.exportreq.OrderRefundExportRequest;
import com.cfpamf.ms.mallorder.req.seller.SellerAfterSaleAuditRequest;
import com.cfpamf.ms.mallorder.req.seller.SellerAfterSaleConfirmReceiveRequest;
import com.cfpamf.ms.mallorder.request.ComplainExample;
import com.cfpamf.ms.mallorder.request.ComplainTalkExample;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.v2.common.lock.Lock;
import com.cfpamf.ms.mallorder.v2.config.CommonConfig;
import com.cfpamf.ms.mallorder.v2.service.OrderRefundService;
import com.cfpamf.ms.mallorder.vo.*;
import com.cfpamf.ms.mallshop.resp.Vendor;
import com.cfpamf.ms.mallsystem.vo.Admin;
import com.slodon.bbc.core.constant.CommonConst;
import com.slodon.bbc.core.constant.ComplainConst;
import com.slodon.bbc.core.controller.BaseController;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.PageVO;
import com.slodon.bbc.core.response.PagerInfo;
import com.slodon.bbc.core.response.SldResponse;
import com.slodon.bbc.core.util.AssertUtil;
import com.slodon.bbc.core.util.StringUtil;
import com.slodon.bbc.core.util.UserUtil;
import com.slodon.bbc.core.util.WebUtil;
import com.slodon.bbc.starter.mq.entity.AdminLogSendVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.slodon.bbc.core.constant.StarterConfigConst.MQ_EXCHANGE_NAME;
import static com.slodon.bbc.core.constant.StarterConfigConst.MQ_QUEUE_NAME_ADMINLOG_MSG;


@Api(tags = "admin-售后管理")
@RestController
@RequestMapping("admin/after/sale")
@Slf4j
public class AdminAfterSaleController extends BaseController {

    @Resource
    private IOrderAfterService orderAfterService;
    @Resource
    private IOrderReturnService orderReturnService;
    @Resource
    private IOrderReturnTrackService orderReturnTrackService;
    @Resource
    private IOrderAdminReturnService orderAdminReturnService;

    @Resource
    private OrderReturnModel orderReturnModel;
    @Resource
    private OrderAfterServiceModel orderAfterServiceModel;
    @Resource
    private OrderProductModel orderProductModel;
    @Resource
    private ComplainModel complainModel;
    @Resource
    private ComplainTalkModel complainTalkModel;

    @Resource
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private Lock lock;

    @Autowired
    private OrderRefundService orderRefundService;

    @Autowired
    private CommonConfig commonConfig;

    @Resource
    private IProofService proofService;

    @Resource
    private OrderReturnTrackMapper orderReturnTrackMapper;

    @Resource
    private IOrderService orderService;

    @Resource
    private IOrderExchangeDetailService orderExchangeDetailService;

    @Resource
    private IOrderAfterExcelDataExportService orderAfterExcelDataExportService;
    @Autowired
    private OrderLocalUtils orderLocalUtils;

    @ApiOperation("售后列表")
    @PostMapping("list")
    public JsonResult<PageVO<OrderReturnVOV2>> list(HttpServletRequest request, @RequestBody AdminAfterSaleListRequest req) {
        Admin admin = UserUtil.getUser(request, Admin.class);
        DatePermissionDTO datePermissionDTO = orderLocalUtils.getBranchAndManageListByAdmin(req.getBranchCodeList(), req.getManagerName(), admin);
        req.setBranchCodeList(datePermissionDTO.getBranchCodeList());
        req.setManagerNameList(datePermissionDTO.getManageNameList());
        req.setManagerName(null);

        int pageSize = Objects.isNull(req.getPageSize()) || StringUtil.isNullOrZero(Integer.parseInt(req.getPageSize())) ? CommonConst.DEFAULT_PAGE_SIZE : Integer.parseInt(req.getPageSize());
        int pageIndex = Objects.isNull(req.getCurrent()) || StringUtil.isNullOrZero(Integer.parseInt(req.getCurrent())) ? 1 : Integer.parseInt(req.getCurrent());
        PagerInfo pager = new PagerInfo(pageSize, pageIndex);
        PageVO<OrderReturnVOV2> orderReturnVOPageVO = orderAfterService.adminAfterSaleList(pager, req);
        return SldResponse.success(orderReturnVOPageVO);
    }

    @ApiOperation("售后详情")
    @GetMapping("detail")
    public JsonResult<OrderReturnDetailVO> detail(HttpServletRequest request, String afsSn) {
        return SldResponse.success(orderAfterService.adminAfterSaleDetail(afsSn));
    }


    @ApiOperation("换货申请详情")
    @GetMapping("orderExchangeApplyDetail")
    public JsonResult<OrderExchangeDetailVO> orderExchangeApplyDetail(HttpServletRequest request, @RequestParam("exchangeSn") @NotNull String exchangeSn) {
        Admin admin = UserUtil.getUser(request, Admin.class);
        UserDTO userDTO = new UserDTO(admin);
        return SldResponse.success(orderExchangeDetailService.orderExchangeApplyDetail(exchangeSn, userDTO));
    }

    @ApiOperation("退款时分销订单,商户账户余额不足提醒")
    @GetMapping("balanceReminder")
    public JsonResult<DistributionOrderInfoVO> balanceReminder(HttpServletRequest request, String afsSn) {
        return SldResponse.success(orderAfterService.balanceReminder(afsSn));
    }

    @ApiOperation(value = "确认退款", tags = "CORE")
    @PostMapping("confirmRefund")
    public JsonResult<String> confirmRefund(HttpServletRequest request, @RequestBody AdminAfterSaleAuditRequest auditRequest) {
        Admin admin = UserUtil.getUser(request, Admin.class);
        log.info("平台确认退款时候的admin对象信息{} afsSn{}", admin, auditRequest.getAfsSns());
        String result;
        try {
            log.info("admin审批退款单afsSns：{}", auditRequest.getAfsSns());
            MDC.put("isConfirmRefund", "true");

            OrderAfterPO orderAfterPO = orderAfterService.getByAfsSn(auditRequest.getAfsSns());
            String lockKey = "AdminAfterSaleController:confirmRefund:" + (Objects.isNull(orderAfterPO) ? auditRequest.getAfsSns() : orderAfterPO.getOrderSn());

            result = lock.tryLockExecuteFunction(lockKey, 0, 30, TimeUnit.SECONDS,
                    () -> orderReturnModel.adminRefundOperation(admin, auditRequest.getAfsSns(), auditRequest.getRemark(),
                            null, true, auditRequest.getChannel(), auditRequest.getRefundPunishAmount()));
        } catch (ZhnxServiceException ex) {
            throw new BusinessException("点击过快，稍等一下~");
        } finally {
            MDC.remove("isConfirmRefund");
        }

        if (!OrderConst.RESULT_CODE_SUCCESS.equals(result)) {
            return new JsonResult<>(255, result);
        }

        //操作日志
        AdminLogSendVO adminLogSendVO = new AdminLogSendVO(admin.getAdminId(), admin.getAdminName(), request.getRequestURI(),
                String.format("确认退款[确认退款成功][售后单号字符串：%s]", auditRequest.getAfsSns()), WebUtil.getRealIp(request));
        try {
            rabbitTemplate.convertAndSend(MQ_EXCHANGE_NAME, MQ_QUEUE_NAME_ADMINLOG_MSG, adminLogSendVO);
        } catch (Exception e) {
            log.info("发消息异常捕获,{}", JSON.toJSONString(adminLogSendVO), e);
        }

        return SldResponse.success("确认成功");
    }


    @ApiOperation(value = "收付通预付确认退款")
    @PostMapping("confirmRefundBySftPresell")
    public JsonResult<String> confirmRefundBySftPresell(HttpServletRequest request, @RequestBody AdminAfterSaleAuditRequest auditRequest) {
        Admin admin = UserUtil.getUser(request, Admin.class);
        String lockKey = "AdminAfterSaleController:confirmRefund:" + auditRequest.getAfsSns();
        String result;
            try {
                log.info("admin审批退款单afsSns：{}", auditRequest.getAfsSns());
                result = lock.tryLockExecuteFunction(lockKey, 0, 30, TimeUnit.SECONDS,
                    () -> orderReturnModel.adminRefundOperation(admin, auditRequest.getAfsSns(), auditRequest.getRemark(),
                            null, true, auditRequest.getChannel(), auditRequest.getRefundPunishAmount(),Boolean.TRUE));
            } catch (ZhnxServiceException ex) {
                throw new BusinessException("点击过快，稍等一下~");
            }

        if (!OrderConst.RESULT_CODE_SUCCESS.equals(result)) {
            return new JsonResult<>(255, result);
        }

        //操作日志
        String opt = "确认退款[确认退款成功][售后单号字符串：" + auditRequest.getAfsSns() + "]";
        AdminLogSendVO adminLogSendVO = new AdminLogSendVO(admin.getAdminId(), admin.getAdminName(),
            request.getRequestURI(), opt, WebUtil.getRealIp(request));
        try {
            rabbitTemplate.convertAndSend(MQ_EXCHANGE_NAME, MQ_QUEUE_NAME_ADMINLOG_MSG, adminLogSendVO);
        } catch (Exception e) {
            log.info("发消息异常捕获,{}", JSON.toJSONString(adminLogSendVO), e);
        }

        return SldResponse.success("确认成功");
    }


    @ApiOperation("拒绝退款")
    @PostMapping("refuseRefund")
    public JsonResult<String> refuseRefund(HttpServletRequest request, @RequestBody AdminAfterSaleAuditRequest auditRequest) {
        Admin admin = UserUtil.getUser(request, Admin.class);
        log.info("拒绝退款admin{}",admin);
        //参数校验
        AssertUtil.notEmpty(auditRequest.getRefuseReason(), "拒绝理由不能为空");

        String result;
        try {
            OrderAfterPO orderAfterPO = orderAfterService.getByAfsSn(auditRequest.getAfsSns());
            String lockKey = "AdminAfterSaleController:confirmRefund:" + (Objects.isNull(orderAfterPO) ? auditRequest.getAfsSns() : orderAfterPO.getOrderSn());
            result = lock.tryLockExecuteFunction(lockKey, 0, 30, TimeUnit.SECONDS,
                    () -> orderReturnModel.adminRefundOperation(admin, auditRequest.getAfsSns(), auditRequest.getRemark(),
                            auditRequest.getRefuseReason(), false, auditRequest.getChannel(), BigDecimal.ZERO));
        } catch (ZhnxServiceException ex) {
            throw new BusinessException("点击过快，稍等一下~");
        }

        //操作日志
        String opt = "拒绝退款[拒绝退款成功][售后单号字符串：" + auditRequest.getAfsSns() + "]";
        AdminLogSendVO adminLogSendVO = new AdminLogSendVO(admin.getAdminId(), admin.getAdminName(),
            request.getRequestURI(), opt, WebUtil.getRealIp(request));
        try {
            rabbitTemplate.convertAndSend(MQ_EXCHANGE_NAME, MQ_QUEUE_NAME_ADMINLOG_MSG, adminLogSendVO);
        } catch (Exception e) {
            log.info("发消息异常捕获,adminLogSendVO:{}", JSON.toJSONString(adminLogSendVO), e);
        }
        if (!OrderConst.RESULT_CODE_SUCCESS.equals(result)) {
            return SldResponse.success("拒绝异常,原因：" + result);
        }
        return SldResponse.success("拒绝成功");
    }

    @ApiOperation(value = "售后风险提示")
    @GetMapping("riskMessage")
    public JsonResult<OrderReturnRiskMessageVO> riskMessage(HttpServletRequest request, @RequestParam String afsSn) {
        Admin admin = UserUtil.getUser(request, Admin.class);
        log.info("riskMessage with admin:{}", JSON.toJSONString(admin));

        return SldResponse.success(orderAfterService.riskMessage(afsSn));
    }

    @ApiOperation("投诉列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "orderSn", value = "订单号", paramType = "query"),
            @ApiImplicitParam(name = "afsSn", value = "退款编号", paramType = "query"),
            @ApiImplicitParam(name = "memberName", value = "投诉人", paramType = "query"),
            @ApiImplicitParam(name = "storeName", value = "投诉店铺", paramType = "query"),
            @ApiImplicitParam(name = "state", value = "投诉状态，1-待平台处理/2-待商家申诉/3-待双方对话/4-待平台仲裁/5-会员撤销投诉/6-会员胜诉/7-商家胜诉",
                    paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "分页大小", defaultValue = "20", paramType = "query"),
            @ApiImplicitParam(name = "current", value = "当前页面位置", defaultValue = "1", paramType = "query")})
    @GetMapping("complainList")
    public JsonResult<PageVO<ComplainVO>> complainList(HttpServletRequest request, String orderSn, String afsSn,
        String memberName, String storeName, Integer state) {
        PagerInfo pager = WebUtil.handlerPagerInfo(request);
        //查询投诉列表
        ComplainExample example = new ComplainExample();
        example.setOrderSnLike(orderSn);
        example.setAfsSnLike(afsSn);
        example.setComplainMemberNameLike(memberName);
        example.setStoreNameLike(storeName);
        example.setComplainState(state);
        List<ComplainPO> list = complainModel.getComplainList(example, pager);
        List<ComplainVO> vos = new ArrayList<>();

        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(complain -> {
                //查询售后信息
                OrderAfterPO orderAfterServicePO = orderAfterServiceModel.getAfterServiceByAfsSn(complain.getAfsSn());
                // 查询订单货品
                OrderProductPO orderProductPO =
                    orderProductModel.getOrderProductByOrderProductId(orderAfterServicePO.getOrderProductId());
                AssertUtil.notNull(orderProductPO, "获取订单货品信息为空，请重试");
                vos.add(new ComplainVO(complain, orderAfterServicePO, orderProductPO));
            });
        }
        return SldResponse.success(new PageVO<>(vos, pager));
    }

    @ApiOperation("投诉详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "complainId", value = "投诉id", required = true, paramType = "query")
    })
    @GetMapping("complainDetail")
    public JsonResult<ComplainDetailVO> complainDetail(HttpServletRequest request, Integer complainId) {
        AssertUtil.notNullOrZero(complainId, "投诉id不能为空");
        //查询投诉信息
        ComplainPO complainPO = complainModel.getComplainByComplainId(complainId);
        AssertUtil.notNull(complainPO, "查询的投诉信息为空");
        //查询售后信息
        OrderAfterPO orderAfterServicePO = orderAfterServiceModel.getAfterServiceByAfsSn(complainPO.getAfsSn());
        // 查询订单货品
        OrderProductPO orderProductPO =
            orderProductModel.getOrderProductByOrderProductId(orderAfterServicePO.getOrderProductId());
        AssertUtil.notNull(orderProductPO, "获取订单货品信息为空，请重试");
        //查询对话信息列表
        ComplainTalkExample example = new ComplainTalkExample();
        example.setComplainId(complainId);
        example.setOrderBy("complain_talk_id ASC");
        List<ComplainTalk> list = complainTalkModel.getComplainTalkList(example, null);

        ComplainDetailVO vo = new ComplainDetailVO(complainPO, orderAfterServicePO, orderProductPO);

        if (!CollectionUtils.isEmpty(list)) {
            List<ComplainDetailVO.ComplainTalkInfo> complainTalkInfoList = new ArrayList<>();
            list.forEach(complainTalk -> {
                ComplainDetailVO.ComplainTalkInfo complainTalkInfo =
                    new ComplainDetailVO.ComplainTalkInfo(complainTalk);
                complainTalkInfoList.add(complainTalkInfo);
            });
            vo.setComplainTalkInfoList(complainTalkInfoList);
        }

        return SldResponse.success(vo);
    }

    @ApiOperation(value = "投诉审核")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "complainIds", value = "投诉id集合，用逗号隔开", required = true, paramType = "query"),
        @ApiImplicitParam(name = "auditType", value = "投诉审核:1 通过, 2 拒绝", required = true, paramType = "query"),
        @ApiImplicitParam(name = "adminHandleContent", value = "拒绝理由,当auditType为2 拒绝时,必填", paramType = "query")})
    @PostMapping("complainAudit")
    public JsonResult complainAudit(HttpServletRequest request, String complainIds, Integer auditType,
        String adminHandleContent) {
        Admin admin = UserUtil.getUser(request, Admin.class);
        //参数校验
        AssertUtil.notEmpty(complainIds, "请选择要删除的数据");
        AssertUtil.notFormatFrontIds(complainIds, "afsSns格式错误,请重试");
        AssertUtil.notNullOrZero(auditType, "投诉审核不能为空");
        AssertUtil.isTrue(auditType == ComplainConst.AUDIT_TYPE_NO && StringUtil.isEmpty(adminHandleContent),
            "当审核拒绝时,拒绝理由不能为空");

        complainModel.batchUpdateComplain(admin.getAdminId(), complainIds, auditType, adminHandleContent);

        //操作日志
        //操作行为
        StringBuilder opt = new StringBuilder("投诉审核");
        opt.append("[");
        opt.append(auditType == ComplainConst.AUDIT_TYPE_YES ? "投诉审核通过" : "投诉审核拒绝");
        opt.append("]");
        opt.append("[");
        opt.append("投诉id集合字符串：");
        opt.append(complainIds);
        opt.append("]");
        AdminLogSendVO adminLogSendVO = new AdminLogSendVO(admin.getAdminId(), admin.getAdminName(),
            request.getRequestURI(), opt.toString(), WebUtil.getRealIp(request));
        try {

            rabbitTemplate.convertAndSend(MQ_EXCHANGE_NAME, MQ_QUEUE_NAME_ADMINLOG_MSG, adminLogSendVO);
        } catch (Exception e) {
            log.info("发消息异常捕获,{}", JSON.toJSONString(adminLogSendVO), e);
        }
        return SldResponse.success(auditType == ComplainConst.AUDIT_TYPE_YES ? "投诉审核通过成功" : "投诉审核拒绝成功");
    }

    @ApiOperation("仲裁")
    @ApiImplicitParams({@ApiImplicitParam(name = "complainId", value = "投诉id", required = true, paramType = "query"),
        @ApiImplicitParam(name = "state", value = "会员是否胜诉:true 会员胜诉, false 商家胜诉", required = true, paramType = "query"),
        @ApiImplicitParam(name = "content", value = "仲裁意见", paramType = "query")})
    @PostMapping("handle")
    public JsonResult addHandle(HttpServletRequest request, Integer complainId, Boolean state, String content) {
        //参数校验
        AssertUtil.notNullOrZero(complainId, "投诉id不能为空");
        AssertUtil.notNull(state, "投诉id不能为空");
        Admin admin = UserUtil.getUser(request, Admin.class);
        //仲裁
        ComplainPO updateOne = new ComplainPO();
        updateOne.setAdminHandleContent(content);
        updateOne.setAdminHandleTime(new Date());
        updateOne.setAdminHandleId(admin.getAdminId());
        if (state) {
            updateOne.setComplainState(ComplainConst.COMPLAIN_STATE_6);
        } else {
            updateOne.setComplainState(ComplainConst.COMPLAIN_STATE_7);
        }

        ComplainExample example = new ComplainExample();
        example.setComplainId(complainId);
        complainModel.updateByExampleSelective(updateOne, example);

        //操作日志
        //操作行为
        StringBuilder opt = new StringBuilder("仲裁");
        opt.append("[");
        opt.append(state ? "会员胜诉" : "商家胜诉");
        opt.append("]");
        opt.append("[");
        opt.append("投诉id");
        opt.append(complainId);
        opt.append("]");
        AdminLogSendVO adminLogSendVO = new AdminLogSendVO(admin.getAdminId(), admin.getAdminName(),
            request.getRequestURI(), opt.toString(), WebUtil.getRealIp(request));
        try {
            rabbitTemplate.convertAndSend(MQ_EXCHANGE_NAME, MQ_QUEUE_NAME_ADMINLOG_MSG, adminLogSendVO);
        } catch (Exception e) {
            log.info("发消息异常捕获,{}", JSON.toJSONString(adminLogSendVO), e);
        }
        return SldResponse.success(state ? "会员胜诉" : "商家胜诉");
    }

    @ApiOperation("发送对话")
    @ApiImplicitParams({@ApiImplicitParam(name = "complainId", value = "投诉id", required = true, paramType = "query"),
        @ApiImplicitParam(name = "talkContent", value = "投诉对话内容", required = true, paramType = "query")})
    @PostMapping("addTalk")
    public JsonResult addTalk(HttpServletRequest request, Integer complainId, String talkContent) {
        AssertUtil.notNullOrZero(complainId, "投诉id不能为空");
        AssertUtil.notEmpty(talkContent, "投诉对话内容不能为空");
        Admin admin = UserUtil.getUser(request, Admin.class);
        int talkUserType = ComplainConst.TALK_USER_TYPE_3;
        complainTalkModel.saveComplainTalk(complainId, talkContent, Long.valueOf(admin.getAdminId()),
            admin.getAdminName(), talkUserType);

        //添加操作日志
        StringBuilder opt = new StringBuilder("平台发送对话");
        opt.append("[");
        opt.append("新增成功");
        opt.append("]");
        opt.append("[");
        opt.append("投诉id：");
        opt.append(complainId);
        opt.append("]");
        AdminLogSendVO adminLogSendVO = new AdminLogSendVO(admin.getAdminId(), admin.getAdminName(),
            request.getRequestURI(), opt.toString(), WebUtil.getRealIp(request));
        try {
            rabbitTemplate.convertAndSend(MQ_EXCHANGE_NAME, MQ_QUEUE_NAME_ADMINLOG_MSG, adminLogSendVO);
        } catch (Exception e) {
            log.info("发消息异常捕获,{}", JSON.toJSONString(adminLogSendVO), e);
        }
        return SldResponse.success("发送对话成功");
    }

    @ApiOperation("获取试算结果")
    @GetMapping("getTryCaculateResult")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "returnMoneyAmount", value = "试算金额", required = true, paramType = "query"),
        @ApiImplicitParam(name = "afsSn", value = "退款单号", required = true, paramType = "query")})
    public JsonResult<LoanTryResultVO> getTryCaculateResult(HttpServletRequest request, @RequestParam("returnMoneyAmount") @NotNull BigDecimal returnMoneyAmount,
        @RequestParam("afsSn") @NotNull String afsSn) {
        CDMallRefundTryResultVO setlTryResultVo = null;
        try {
            Result<CDMallRefundTryResultVO> tryCaculateResult = orderAfterServiceModel.getTryCaculateResult(returnMoneyAmount, afsSn);
            setlTryResultVo = tryCaculateResult.getData();
        } catch (Exception e) {
            log.info("试算参数：{},{}", JSON.toJSON(returnMoneyAmount), afsSn, e);
        }
        return SldResponse.success(new LoanTryResultVO(setlTryResultVo));
    }

    @ApiOperation("恢复额度退款查询该批次所有退款单号")
    @GetMapping("getAllRestoreLimitOrders")
    @ApiImplicitParams({@ApiImplicitParam(name = "orderSn", value = "订单号", required = true, paramType = "query")})
    public JsonResult<RestoreLimitOrders> getAllRestoreLimitOrders(HttpServletRequest request,
        @RequestParam("orderSn") @NotNull String orderSn) {
        List<String> afsSn = orderReturnService.getAllRestoreLimitOrders(orderSn);
        RestoreLimitOrders restoreLimitOrders = new RestoreLimitOrders();
        restoreLimitOrders.setRestoreLimitOrders(String.join(",", afsSn));
        return SldResponse.success(restoreLimitOrders);
    }

    @ApiOperation("退款订单表导出")
    @PostMapping("orderRefundExport")
    public void orderRefundExport(HttpServletRequest request, HttpServletResponse response,
        @RequestBody OrderRefundExportRequest refundExportRequest) {
        Map<String, List<? extends Object>> sheepMap = new HashMap<>(16);
        sheepMap.put("退款订单表", orderReturnService.adminOrderRefundExport(refundExportRequest));
        PoiUtils.exportByHttp(request, response, "退款订单表" + DateUtils.format(new Date(), "yyyyMMdd"), sheepMap,
            PoiUtils.DEFAULT_DATE_FORMAT, null, false);
    }

    @ApiOperation("平台端退款订单表导出")
    @PostMapping("orderRefundExport/V2")
    public JsonResult<FileDTO> orderRefundExportV2(HttpServletRequest request,@RequestBody OrderRefundExportRequest refundExportRequest)
            throws Exception {
        JsonResult<FileDTO> jsonResult = new JsonResult<>(200,"成功");
        Admin admin = Optional.ofNullable(UserUtil.getUser(request, Admin.class)).orElse(new Admin());
        DatePermissionDTO datePermissionDTO = orderLocalUtils.getBranchAndManageListByAdmin(refundExportRequest.getBranchCodeList(), refundExportRequest.getManagerName(), admin);
        refundExportRequest.setBranchCodeList(datePermissionDTO.getBranchCodeList());
        refundExportRequest.setManagerNameList(datePermissionDTO.getManageNameList());
        refundExportRequest.setManagerName(null);
        UserDTO userDTO = new UserDTO(admin);
        refundExportRequest.setUserDTO(userDTO);
        jsonResult.setData(orderAfterExcelDataExportService.executeAsyncExportExcel(refundExportRequest));
        return jsonResult;
    }

    @ApiOperation("平台端查询退款单轨迹信息")
    @GetMapping("getOrderReturnTrack")
    @ApiImplicitParams({@ApiImplicitParam(name = "afsSn", value = "退款单号", required = true, paramType = "query")})
    public JsonResult<List<OrderReturnTrackVO>> getOrderReturnTrack(@RequestParam("afsSn") @NotNull String afsSn) {
        return SldResponse.success(orderReturnTrackService.getOrderReturnTrackVOByAfsSn(afsSn));
    }

    @ApiOperation(value = "平台端强制退款", tags = "CORE")
    @PostMapping("adminForceRefund")
    public JsonResult<Boolean> adminForceRefund(HttpServletRequest request,
        @RequestBody @Valid AdminForceRefundRequest forceRefundReq) {
        Admin admin = UserUtil.getUser(request, Admin.class);
        String[] orderSns = forceRefundReq.getOrderSns().split(",");
        for (String orderSn : orderSns) {
            OrderPO orderPo = orderService.getByOrderSn(orderSn);
            if(ExchangeOrderConst.ORDER_EXCHANGE_FLAG_2 == orderPo.getExchangeFlag()){
                throw new BusinessException("换货后的订单不允许强制退款");
            }
        }

        log.info("申请平台强制退款的订单号为：{} admin:{}", forceRefundReq.getOrderSns(), admin);
        if (commonConfig.isOpenV2()) {// 开启V2流程取消订单
            log.info("开启V2流程申请平台强制退款。orderSn:{}", forceRefundReq.getOrderSns());
            lock.tryLockExecuteFunction("adminForceRefund:" + forceRefundReq.getOrderSns(), 0, 30, TimeUnit.SECONDS, () -> {
                return orderRefundService.adminForceRefund(admin, forceRefundReq);
            });
            return SldResponse.success("平台端强制退款申请成功");
        }
        String result = orderAdminReturnService.adminForceRefund(admin, forceRefundReq);
        if (SentenceConst.DEAL_SUCCESS.equals(result)) {
            return SldResponse.success(result);
        }
        return SldResponse.fail(result);
    }

    @ApiOperation("平台端强制退款查询")
    @GetMapping("adminForceRefundList")
    public JsonResult<PageVO<OrderReturnVOV2>> adminForceRefundList(HttpServletRequest request,
        AdminForceRefundListRequest forceRefundListReq) {
        PagerInfo pager = WebUtil.handlerPagerInfo(request);
        return SldResponse.success(orderAdminReturnService.adminForceRefundList(pager, forceRefundListReq));
    }

    @ApiOperation("平台端撤销退款")
    @PostMapping("adminRevokeRefund")
    public JsonResult<Boolean> adminRevokeRefund(HttpServletRequest request, @RequestBody @Valid AdminRevokeRefundBaseRequest revokeRequest) {
        Admin admin = UserUtil.getUser(request, Admin.class);
        log.info("平台端撤销退款>>>>>>>>>>>>>>>>>>>admin信息:{}", admin.toString());
        OperatorDTO operator = new OperatorDTO(admin.getAdminId(), admin.getAdminName(), OrderConst.LOG_ROLE_ADMIN, admin.getPhone());
        orderReturnService.revokeRefund(revokeRequest, OrderRefundRevokingPartyEnum.PLATFORM_REJECT, operator);
        return SldResponse.success();
    }


    @ApiOperation("上传凭证")
    @PostMapping("uploadVoucher")
    public JsonResult<Boolean> uploadVoucher(HttpServletRequest request, @RequestBody @Valid ProofRecordDTO proofRecordDTO) {
        Admin admin = UserUtil.getUser(request, Admin.class);
        proofRecordDTO.setProofUploadUserId(admin.getAdminId().longValue());
        proofRecordDTO.setProofUploadUserRole(OrderConst.LOG_ROLE_ADMIN);
        proofRecordDTO.setProofUploadUserName(admin.getAdminName());
        proofService.saveOrUpdateProofRecord(proofRecordDTO);

        Date now = new Date();
        String operator = admin.getAdminName();
        if(!StringUtils.isEmpty(admin.getPhone())){
            operator = operator + "-" + admin.getPhone();
        }
        // 6、记录退款轨迹
        OrderReturnTrackPO orderReturnTrackPO =
                OrderReturnTrackPO.builder().afsSn(proofRecordDTO.getProofValue()).operateType(OrderReturnOperateTypeEnum.CREATE.getValue())
                        .operator(operator).operateTime(now)
                        .operateResult(AuditResultEnum.AUDIT_PASS.getValue()).operateRemark("上传凭证")
                        .channel(OrderCreateChannel.WEB.getValue()).build();
        orderReturnTrackMapper.insert(orderReturnTrackPO);

        return SldResponse.success();
    }

    @ApiOperation(value = "内部员工平台提交退款申请")
    @PostMapping("submitRefund")
    public JsonResult<String> submitRefund(HttpServletRequest request, @RequestBody OrderAfterDTO orderAfterDTO) {
        Admin admin = UserUtil.getUser(request, Admin.class);
        log.info("admin apply after sale, submitRefund, admin info is : {}", JSONObject.toJSONString(admin));

        try {
            lock.tryLockExecuteFunction("AdminAfterSaleController:submitRefund:"
                            + orderAfterDTO.getOrderSn(), 0, 30, TimeUnit.SECONDS,
                    () -> {
                        orderAfterDTO.setReturnBy(ReturnByEnum.ACCOUNT_MANAGER.getValue());
                        Member member = com.cfpamf.ms.mallorder.common.util.UserUtil.convertAdmin2Member4RefundApply(admin);
                        orderAfterServiceModel.submitAfterSaleApply(orderAfterDTO, member);
                        return SldResponse.success("提交成功");
                    });
        } catch (ZhnxServiceException ex) {
            throw new BusinessException("点击过快，稍等一下~");
        }
        return SldResponse.success("提交成功");
    }

    @ApiOperation(value = "内部员工平台审核待商家审核的售后单")
    @PostMapping("adminStoreAudit")
    public JsonResult<String> adminStoreAudit(HttpServletRequest request, @RequestBody SellerAfterSaleAuditRequest auditRequest) {
        Admin admin = UserUtil.getUser(request, Admin.class);
        log.info("admin apply after sale, adminStoreAudit, admin info is : {}", JSONObject.toJSONString(admin));
        Vendor vendor = com.cfpamf.ms.mallorder.common.util.UserUtil.convertAdmin2Vendor4StoreAudit(admin);

        String lockKey = "AdminAfterSaleController:adminStoreAudit:" + auditRequest.getAfsSn();
        String result = lock.tryLockExecuteFunction(lockKey, 0, 10, TimeUnit.SECONDS,
                () -> orderReturnModel.afsStoreAudit(vendor, auditRequest.getAfsSn(), auditRequest.getIsPass(),
                        auditRequest.getRemark(), auditRequest.getStoreAddressId(), auditRequest.getChannel(),
                        auditRequest.getDistribution(), auditRequest.getRefundPunishAmount(), OperationRoleEnum.ACCOUNT_MANAGER,null));

        if (!OrderConst.RESULT_CODE_SUCCESS.equals(result)) {
            return new JsonResult<>(255, result);
        }
        return SldResponse.success("审核成功");
    }

    @ApiOperation(value = "内部员工平台审核待商家确认收货的售后单")
    @PostMapping("adminStoreConfirmReceive")
    public JsonResult<String> adminStoreConfirmReceive(HttpServletRequest request,
                                                       @RequestBody SellerAfterSaleConfirmReceiveRequest receiveRequest) {
        Admin admin = UserUtil.getUser(request, Admin.class);
        log.info("admin apply after sale, adminStoreConfirmReceive, admin info is : {}", JSONObject.toJSONString(admin));
        Vendor vendor = com.cfpamf.ms.mallorder.common.util.UserUtil.convertAdmin2Vendor4StoreAudit(admin);
        String result = orderReturnModel.afsStoreReceive(vendor, receiveRequest.getAfsSn(), receiveRequest.isReceive(), receiveRequest.getRemark(),
                receiveRequest.getChannel(), receiveRequest.getDistribution(), receiveRequest.getRefundPunish(), OperationRoleEnum.ACCOUNT_MANAGER, null);
        if (!OrderConst.RESULT_CODE_SUCCESS.equals(result)){
            return new JsonResult<>(255, result);
        }
        return SldResponse.success("审核成功");
    }


    @ApiOperation(value = "更改退款类型")
    @PostMapping("changeRefundType")
    public JsonResult<String> changeRefundType(HttpServletRequest request,
                                                       @RequestBody @Valid RefundTypeChangeRequest refundTypeChangeRequest) {
        Admin admin = UserUtil.getUser(request, Admin.class);
        log.info("admin apply after sale, adminStoreConfirmReceive, admin info is : {}", JSONObject.toJSONString(admin));

        if (Objects.isNull(admin) || Objects.isNull(admin.getAdminId())) {
            admin = new Admin();
            admin.setAdminId(com.cfpamf.ms.mallorder.common.constant.CommonConst.SYSTEM_ID);
            admin.setAdminName(com.cfpamf.ms.mallorder.common.constant.CommonConst.ADMIN_NAME_EN);
            admin.setPhone(com.cfpamf.ms.mallorder.common.constant.CommonConst.ADMIN_PHONE);
        }
        return SldResponse.success(orderAdminReturnService.changeRefundType(admin, refundTypeChangeRequest));
    }

}
