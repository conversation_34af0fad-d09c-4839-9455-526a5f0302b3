package com.cfpamf.ms.mallorder.integration.facade;

import com.cfpamf.ares.trade.domain.dto.InvoiceApplyDTO;
import com.cfpamf.ares.trade.domain.dto.InvoicePayerDTO;
import com.cfpamf.ares.trade.domain.query.InvoiceQuery;
import com.cfpamf.ares.trade.domain.vo.*;
import com.cfpamf.athena.common.domain.dto.ResultEntity;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 *
 * 交易中心发票facade
 *
 * <AUTHOR>
 * @since 2024/12/3
 */
@FeignClient(
        name = "ares-trade",
        url = "${ares-trade.url:}"
)
public interface AresInvoiceFacade {
    /**
     * 根据渠道和编码查询对应的纳税人信息
     *
     * @param channel 渠道
     * @param code 编码
     * @return 纳税人信息
     */
    @GetMapping("/invoice/payer/queryByChannelAndCode")
    ResultEntity<InvoicePayerVO> queryPayerByChannelAndCode(@RequestParam("channel")String channel, @RequestParam("code")String code);

    /**
     * 创建或更新纳税人信息，根据渠道和编码唯一区分
     *
     * @param payerDTO 纳税人信息vo
     * @return 是否成功
     */
    @PostMapping("/invoice/payer/createOrUpdate")
    ResultEntity<Boolean> createOrUpdatePayer(@RequestBody InvoicePayerDTO payerDTO);

    /**
     * 校验是否对应订单已经存在发票
     *
     * @param channel 渠道
     * @param orderNo 订单号
     * @return 是否存在
     */
    @GetMapping("/invoice/check")
    ResultEntity<Boolean> checkSingleOrderInvoice(@RequestParam("channel")String channel,@RequestParam("orderNo")String orderNo);

    /**
     * 发票查询
     *
     * @param query 查询实体
     * @return 发票信息
     */
    @PostMapping("/invoice/query")
    ResultEntity<InvoiceVO> queryByChannelAndOrderNo(@RequestBody InvoiceQuery query);

    /**
     * 申请开票
     *
     * @param applyDTO 申请开票dto
     * @return 发票信息
     */
    @PostMapping("/invoice/apply")
    ResultEntity<Boolean> applyInvoice(@RequestBody InvoiceApplyDTO applyDTO);

    /**
     * 发票冲红并重开
     *
     * @param applyDTO 冲红dto
     * @return 操作结果
     */
    @PostMapping("/invoice/undo")
    ResultEntity<InvoiceUndoResultVO> undoInvoice(@RequestBody InvoiceApplyDTO applyDTO);

    /**
     * 根据名称查询抬头信息
     *
     * @param name 名称
     * @param searchType 查询类型，0-模糊查询，1-精确查询
     * @return 抬头信息列表
     */
    @GetMapping("/invoice/buyerSearch")
    ResultEntity<List<InvoiceBuyerVO>> buyerSearch(@RequestParam("name")String name, @RequestParam(value = "searchType",defaultValue = "0")Integer searchType);


    @GetMapping("/invoice/queryTitleList")
    @ApiOperation("查询发票抬头列表")
    ResultEntity<List<InvoiceTitleVO>> queryTitleList(@RequestParam("channel") String channel, @RequestParam("owner") String owner);

    @GetMapping("/invoice/queryTitleDetail")
    @ApiOperation("查询发票抬头详情")
    ResultEntity<InvoiceTitleVO> queryTitleDetail(@RequestParam("id") Long id);

    @PostMapping("/invoice/updateTitle")
    @ApiOperation("更新发票抬头")
    ResultEntity<Void> updateTitle(@RequestBody InvoiceTitleVO vo);

    @PostMapping("/invoice/saveTitle")
    @ApiOperation("更新发票抬头")
    ResultEntity<Void> saveTitle(@RequestBody InvoiceTitleVO vo);

    @GetMapping("/invoice/delTitle")
    @ApiOperation("删除发票抬头")
    ResultEntity<Void> delTitle(@RequestParam("id") Long id);
}
