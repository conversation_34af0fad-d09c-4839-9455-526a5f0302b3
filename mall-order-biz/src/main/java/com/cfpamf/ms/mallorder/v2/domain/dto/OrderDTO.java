package com.cfpamf.ms.mallorder.v2.domain.dto;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.cfpamf.ms.mallorder.po.*;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.util.CollectionUtils;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 类OrderDTO.java的实现描述：
 *
 * <AUTHOR> 15:15
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = false)
public class OrderDTO implements java.io.Serializable {

    /**
     * 
     */
    private static final long serialVersionUID = -4494866501348953413L;

    private List<OrderPO> orders;//

    private List<OrderProductPO> orderProducts;

    private List<OrderProductExtendPO> orderProductExtends;

    private List<OrderExtendPO> orderExtends;//

    private List<OrderPromotionDetailPO> orderPromotionDetails;

    private List<OrderPromotionSendCouponPO> orderPromotionSendCoupons;

    private List<OrderPromotionSendProductPO> orderPromotionSendProducts;

    private List<OrderLogPO> orderLogs;//

    /**
     * 预售信息
     */
    private List<OrderPresellPO> orderPresells;

    /**
     * 交易流水信息
     */
    private List<OrderPayRecordPO> orderPayRecords;

    private OrderPayPO orderPay;

    private String parentSn;// 父订单号

    /**
     * 活动类型标识
     */
    private Integer promotionTypeMark;

    public void addOrder(OrderPO order) {
        if (CollectionUtils.isEmpty(this.orders)) {
            this.orders = new ArrayList<>();
        }
        this.orders.add(order);
    }

    public void addOrderProducts(List<OrderProductPO> orderProducts) {
        if (CollectionUtils.isEmpty(orderProducts)) {
            return;
        }
        if (CollectionUtils.isEmpty(this.orderProducts)) {
            this.orderProducts = new ArrayList<>();
        }
        this.orderProducts.addAll(orderProducts);
    }

    public void addOrderProductExtends(List<OrderProductExtendPO> orderProductExtends) {
        if (CollectionUtils.isEmpty(orderProductExtends)) {
            return;
        }
        if (CollectionUtils.isEmpty(this.orderProductExtends)) {
            this.orderProductExtends = new ArrayList<>();
        }
        this.orderProductExtends.addAll(orderProductExtends);
    }

    public void addOrderExtend(OrderExtendPO orderExtend) {
        if (ObjectUtils.isEmpty(orderExtend)) {
            return;
        }
        if (CollectionUtils.isEmpty(this.orderExtends)) {
            this.orderExtends = new ArrayList<>();
        }
        this.orderExtends.add(orderExtend);
    }

    public void addOrderPromotionDetails(List<OrderPromotionDetailPO> orderPromotionDetails) {
        if (CollectionUtils.isEmpty(orderPromotionDetails)) {
            return;
        }
        if (CollectionUtils.isEmpty(this.orderPromotionDetails)) {
            this.orderPromotionDetails = new ArrayList<>();
        }
        this.orderPromotionDetails.addAll(orderPromotionDetails);
    }

    public void addOrderPromotionSendCoupons(List<OrderPromotionSendCouponPO> orderPromotionSendCoupons) {
        if (CollectionUtils.isEmpty(orderPromotionSendCoupons)) {
            return;
        }
        if (CollectionUtils.isEmpty(this.orderPromotionSendCoupons)) {
            this.orderPromotionSendCoupons = new ArrayList<>();
        }
        this.orderPromotionSendCoupons.addAll(orderPromotionSendCoupons);
    }

    public void addOrderPromotionSendProducts(List<OrderPromotionSendProductPO> orderPromotionSendProducts) {
        if (CollectionUtils.isEmpty(orderPromotionSendProducts)) {
            return;
        }
        if (CollectionUtils.isEmpty(this.orderPromotionSendProducts)) {
            this.orderPromotionSendProducts = new ArrayList<>();
        }
        this.orderPromotionSendProducts.addAll(orderPromotionSendProducts);
    }

    public void addOrderLog(OrderLogPO orderLog) {
        if (ObjectUtils.isEmpty(orderLog)) {
            return;
        }
        if (CollectionUtils.isEmpty(this.orderLogs)) {
            this.orderLogs = new ArrayList<>();
        }
        this.orderLogs.add(orderLog);
    }

    public OrderDTO addOrderPresell(OrderPresellPO orderPresell) {
        if (ObjectUtils.isEmpty(orderPresell)) {
            return this;
        }
        if (Objects.isNull(orderPresells)) {
            this.orderPresells = new ArrayList<>();
        }
        this.orderPresells.add(orderPresell);
        return this;
    }

    public OrderDTO addOrderPayRecord(OrderPayRecordPO orderPayRecord) {
        if (ObjectUtils.isEmpty(orderPayRecord)) {
            return this;
        }
        if (Objects.isNull(orderPayRecords)) {
            this.orderPayRecords = new ArrayList<>();
        }
        this.orderPayRecords.add(orderPayRecord);
        return this;
    }

}
