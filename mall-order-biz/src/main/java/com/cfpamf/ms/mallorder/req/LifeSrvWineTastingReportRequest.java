package com.cfpamf.ms.mallorder.req;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.mallorder.contant.LifeSrvIncomeConstants;
import com.cfpamf.ms.mallorder.integration.wms.contest.WmsErrorCodeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

@Data
public class LifeSrvWineTastingReportRequest implements java.io.Serializable {

	private static final long serialVersionUID = 1L;

    @ApiModelProperty("分支督导工号")
    String spvsrId;

    @ApiModelProperty("机构编码")
    String orgCode;

    @ApiModelProperty("分支编码")
    String areaOrgCode;

    @ApiModelProperty("区域编码")
    String areaCode;

    @ApiModelProperty("片区编码")
    String districtCode;

    @ApiModelProperty("权限级别 nation 全国, area 区域, district 片区, bch 分支, spvsr 分支督导, emp 员工")
    String authType;

    @ApiModelProperty("区间 当年：year 当月：month")
    private String interval;

    @ApiModelProperty("pt")
    private String pt;

    DateTimeFormatter FMT_YYYYMMDD = DateTimeFormatter.ofPattern("yyyyMMdd");;

    public String getPt() {
        if (StringUtils.isBlank(pt)) {
           return LocalDate.now().minusDays(1).format(FMT_YYYYMMDD);//昨天日期yyyyMMdd
        }
        return pt;
    }
}
