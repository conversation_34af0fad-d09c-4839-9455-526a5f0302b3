package com.cfpamf.ms.mallorder.integration.settlement;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.mallpayment.facade.api.MallPaymentFacade;
import com.cfpamf.mallpayment.facade.vo.PaymentLoanInfoVO;
import com.cfpamf.ms.mall.account.enums.AccountCardTypeEnum;
import com.cfpamf.ms.mall.account.vo.AccountCard;
import com.cfpamf.ms.mall.liquidate.api.BizActionFacade;
import com.cfpamf.ms.mall.liquidate.enums.ActionEnum;
import com.cfpamf.ms.mall.liquidate.request.ExecuteReq;
import com.cfpamf.ms.mall.settlement.api.BillOperatinFeignClient;
import com.cfpamf.ms.mall.settlement.api.SettlementBillFeignClient;
import com.cfpamf.ms.mall.settlement.dto.BillOperatinDTO;
import com.cfpamf.ms.mall.settlement.dto.BillOperatinDetails;
import com.cfpamf.ms.mall.settlement.po.BillOperatin;
import com.cfpamf.ms.mallorder.common.constant.TaskConstant;
import com.cfpamf.ms.mallorder.common.enums.LoanStatusEnum;
import com.cfpamf.ms.mallorder.common.enums.PayMethodEnum;
import com.cfpamf.ms.mallorder.common.enums.TaskQueueBizTypeEnum;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.controller.fegin.facade.AccountCardFacade;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderReturnPO;
import com.cfpamf.ms.mallorder.service.IOrderPlacingService;
import com.cfpamf.ms.mallorder.service.ITaskQueueService;
import com.cfpamf.ms.mallshop.api.StoreFeignClient;
import com.cfpamf.ms.mallshop.resp.Store;
import com.qiniu.util.Json;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.exception.MallException;
import com.slodon.bbc.core.response.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 结算服务交互
 */
@Slf4j
@Component
public class BillOperatinIntegration {
    @Value("${spring.application.name}")
    private String appName;
    @Resource
    private BillOperatinFeignClient operatinFeignClient;
    @Resource
    private SettlementBillFeignClient settlementBillFeignClient;
    @Resource
    private ITaskQueueService taskQueueService;
    @Autowired
    private AccountCardFacade accountCardFacade;
    @Autowired
    private StoreFeignClient storeFeignClient;
    @Autowired
    private MallPaymentFacade paymentFacade;
    @Autowired
    private BizActionFacade bizActionFacade;
    @Autowired
    private IOrderPlacingService orderPlacingService;





    public Map<String, String> orderBillList(List<String> bizSnList) {

        Map<String, String> orderBillMap = null;
        try {
            orderBillMap = settlementBillFeignClient.orderBillList(bizSnList);
        } catch (Exception e) {
            throw new MallException("settlement 查询订单结算单异常，订单号："
                    + bizSnList, ErrorCodeEnum.C.CALL_EXCEPTION.getCode (), e);
        }

        return orderBillMap;

    }

    /**
     * 获取结算账户
     *
     * @param storeId
     */
    public AccountCard getAccount(String storeId) {
        JsonResult<AccountCard> result = null;
        try {
            result = accountCardFacade.defaultCard(Long.valueOf(storeId));
        } catch (Exception e) {
            log.warn("调用account获取账户信息异常 /v1/feign/business/account/card/defaultCard：", e);
        }

        if (result == null || result.getData() == null) {
            log.warn("调用account获取账户信息为空 /v1/feign/business/account/card/defaultCard");
            return null;
        }

        if (result.getState() != 200) {
            log.warn("调用account获取账户信息为空 /v1/feign/business/account/card/defaultCard");
            return null;
        }
        return result.getData();
    }


    public AccountCard detailByBankAccount(String storeId, AccountCardTypeEnum value) {

        JsonResult<AccountCard> jsonResult = null;
        try {
            jsonResult = accountCardFacade.detail(storeId, value);
        } catch (Exception e) {
            log.error("调用bankAccount+cardType查询银行卡信息接口异常:{}", e);
            throw e;
        }
        if (jsonResult == null) {
            log.warn("调用bankAccount+cardType查询银行卡信息接口未返回信息,请求参数:{}", JSONObject.toJSONString(storeId));
            throw new MallException("调用消费转账接口未返回信息");
        } else if (jsonResult.getState() != 200) {
            log.warn("调用bankAccount+cardType查询银行卡信息接口失败,请求参数:{}，响应参数:{}", JSONObject.toJSONString(storeId), JSONObject.toJSON(jsonResult));
            throw new MallException(jsonResult.getMsg());
        }
        return jsonResult.getData();
    }


    /**
     * 获取店铺信息
     *
     * @param storeId
     */
    public Store getStoreByStoreId(String storeId) {
        Store store = null;
        try {
            store = storeFeignClient.getStoreByStoreId(Long.valueOf(storeId));
        } catch (Exception e) {
            log.warn("调用shop获取店铺信息异常/v1/feign/seller/store/get：", e);
        }

        if (store == null ) {
            log.warn("调用account获取账户信息为空 /v1/feign/seller/store/get");
            return null;
        }
        return store;
    }

}
