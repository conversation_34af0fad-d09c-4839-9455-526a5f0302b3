package com.cfpamf.ms.mallorder.integration.cashier.request;

import com.alibaba.fastjson.annotation.JSONType;
import com.baomidou.mybatisplus.core.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description: 支付方式枚举
 * @Author: wangyingwei
 * @Date: 2019/12/18
 */
@AllArgsConstructor
@Getter
@JSONType(serializeEnumAsJavaBean = true)
public enum PayWayEnum implements IEnum<String> {

    ENJOY_PAY("enjoypay", "用呗"),
    CREDIT_PAY("credit", "授信额度"),
    FOLLOW_HEART("followheart", "随心取"),
    BALANCE("balance", "余额"),
    ALI_PAY("alipay", "支付宝"),
    WXPAY("wxpay", "微信支付"),
    AGREED_PAY("agreedPay", "协议支付"),
    ONLINE("ONLINE", "在线支付"),
    ONLINE_PAY("onlinepay", "在线支付"), // 临时
    CARD_VOUCHER2("cardVoucher", "卡券支付"), // 临时
    CARD_VOUCHER("CARD_VOUCHER", "卡券支付"),
    CARD("CARD", "乡助卡支付"),
    BANK_PAY("BANK_PAY", "银行卡转账"),
    BANK_TRANSFER("BANK_TRANSFER", "银行卡汇款"),
    COMBINATION_PAY("COMBINATION_PAY", "组合支付");

    String value;
    String desc;

    public static PayWayEnum getValue(String value){
        for(PayWayEnum ps : PayWayEnum.values()){
            if (value.equals(ps.value)){
                return ps;
            }
        }
        return null;
    }

    public static PayWayEnum getValueName(String name){
        for(PayWayEnum ps : PayWayEnum.values()){
            if (name.equals(ps.toString())){
                return ps;
            }
        }
        return null;
    }

    public static boolean isLoanPay(PayWayEnum payWay) {
        return payWay == ENJOY_PAY || payWay == CREDIT_PAY || payWay == FOLLOW_HEART;
    }

    public static boolean isThirdPartyPay(PayWayEnum payWay) {
        return payWay == ALI_PAY || payWay == WXPAY;
    }

}
