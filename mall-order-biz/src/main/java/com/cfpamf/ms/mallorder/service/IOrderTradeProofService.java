package com.cfpamf.ms.mallorder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.ms.mall.filecenter.domain.dto.FileScenesMaterialProofDTO;
import com.cfpamf.ms.mall.filecenter.domain.vo.FileScenesProofVO;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.dto.OrderTradeProofQueryDTO;
import com.cfpamf.ms.mallorder.dto.ReceiveMaterialResultDTO;
import com.cfpamf.ms.mallorder.dto.TradeProofUploadDTO;
import com.cfpamf.ms.mallorder.dto.UserBaseInfo;
import com.cfpamf.ms.mallorder.integration.filecenter.ProofSceneEnum;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderTradeProofPO;
import com.cfpamf.ms.mallorder.vo.*;
import com.cfpamf.ms.mallorder.vo.filescene.FileScenesProductWithResultVO;

import java.util.List;

/**
 * <p>
 * 订单交易凭证表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-12
 */
public interface IOrderTradeProofService extends IService<OrderTradeProofPO> {

    /**
     * 保存用户上传资料到文件中心
     * 根据订单进行校验
     *
     * @param paramDTO
     * @return
     */
    Boolean saveScenesMaterial(Integer operateRole, String operateId, String operateName, FileScenesMaterialProofDTO paramDTO);

    /**
     * 校验签收文件风险等级
     *
     * @param fileUrl 文件地址
     * @return 校验结果
     */
    ReceiveMaterialResultDTO checkReceiveMaterial(String orderSn, String fileUrl);

    /**
     * 查询文件中心上传资料内容
     *
     * @param proofNo
     * @param subProofNo
     * @param sceneNo
     * @return
     */
    List<FileScenesProofVO> queryScenesMaterial(String proofNo, String subProofNo, String sceneNo, String materialNo, Boolean showAgreement);

    /**
     * 查询文件中心上传资料内容及校验结果
     *
     * @param proofNo
     * @param subProofNo
     * @param sceneNo
     * @return
     */
    List<FileScenesProductWithResultVO> queryScenesMaterialWithResult(String proofNo, String subProofNo, String sceneNo, Boolean showAgreement);

    /**
     * 查询订单资料信息
     *
     * @param orderSn
     * @param orderProductId
     * @param sceneEnum
     * @return
     */
    List<OrderTradeProofVO> listSceneMaterial(String orderSn, Long orderProductId, String mateiralNo, ProofSceneEnum sceneEnum);

    /**
     * 匹配规则资料信息
     *
     * @param queryDTO
     * @return
     */
    List<OrderTradeProofVO> matchSceneMaterials(OrderTradeProofQueryDTO queryDTO);

    /**
     * 订单规则匹配，查bapp权限
     *
     * @param orderSn
     * @return
     */
    Boolean matchBappPrivilege(String orderSn, ProofSceneEnum sceneEnum);

    /**
     * 校验订单相关场景是否已上传资料内容
     *
     * @param orderSn
     * @param sceneEnum
     * @return
     */
    Boolean checkOrderProofUpload(String orderSn, List<Long> orderProductIdList, ProofSceneEnum sceneEnum);

    /**
     * 根据订单编号查询需要上传的交易凭证信息
     *
     * @param orderSn 订单编号
     * @return 需要上传的交易凭证信息
     */
    List<OrderTradeProofPO> getListByOrderSn(String orderSn);

    /**
     * 直接上传交易凭证
     *
     * @param proofUploadDTO 参数dto
     * @return 上传结果
     */
    Boolean uploadProof(Integer operateRole, String operateId, String operateName, String source, TradeProofUploadDTO proofUploadDTO);

    /**
     * 农服收货确认函资料查询
     *
     * @param orderSn 订单编号
     * @return 收货确认函资料
     */
    List<ReceiveCheckResultVO> listSceneMaterialForAgric(String orderSn);

    /**
     * 上传收货凭证
     *
     * @param logRoleAdmin 操作角色
     * @param operateId    操作人id
     * @param operator     操作人姓名
     * @param source       操作来源
     * @param uploadVO     上传信息
     * @return 上传结果
     */
    Boolean uploadProofAgric(int logRoleAdmin, String operateId, String operator, String source, AgricTradeProofUploadVO uploadVO, Boolean isEle);

    /**
     * 农服资料校验
     *
     * @param orderSn 订单编号
     * @param fileUrl 文件地址
     * @return 校验结果
     */
    ReceiveCheckResultVO checkReceiveMaterialAgric(String orderSn, String fileUrl);

    /**
     * 电商上传凭证
     *
     * @param logRoleType    操作人类型
     * @param operateId      操作人id
     * @param operator       操作人名字
     * @param source         来源
     * @param proofUploadDTO 参数dto
     * @return 上传结果
     */
    Boolean uploadProofMall(int logRoleType, String operateId, String operator, String source, TradeProofUploadDTO proofUploadDTO);

    /**
     * 法大大用户实名+授权检查
     *
     * @param member        用户信息
     * @param userBaseInfo  客户中心的用户信息，可不传
     * @param url           回调url
     * @param genUrl        是否需要生成认证url,0-不需要，1-需要
     * @param isMiniProgram 是否为小程序
     * @return 校验结果
     */
    MallCustomerCheckResultVO customerCheck(Member member, UserBaseInfo userBaseInfo, String url, String genUrl, String isMiniProgram);

    /**
     * 合同默签
     *
     * @param member  用户信息
     * @param paramVO 请求参数
     * @return 默签结果
     */
    ContractSignResultVO contractSign(Member member, ContractSignParamVO paramVO);
    /**
     * 同步生成合同
     *
     * @param orderSn 订单编号
     * @param materialNo 交易真实性资料编码
     */
    List<String> generateContractSync(String orderSn, String materialNo);

    /**
     * 异步生成合同
     *
     * @param orderSn 订单编号
     * @param materialNo 交易真实性资料编码
     */
    void generateContract(String orderSn, String materialNo);

    /**
     * 合同查询
     *
     * @param member 用户信息
     * @param proofId 资料id
     * @param needLoanInfo 是否需要返回信贷信息
     * @return 预览地址
     */
    ContractSearchResultVO contractSearch(Long proofId,Integer needLoanInfo);

    /**
     * 根据订单查询所有交易真实性资料
     *
     * @param orderPO 订单
     */
    List<MallFileScenesProofVO> queryScenesMaterialV2(OrderPO orderPO);

    /**
     * 根据订单查询所有交易真实性资料 - 电子签
     *
     * @param orderSn 订单编号
     */
    ContractSimpleVo listSceneMaterialForAgricEle(String orderSn);

    /**
     * 刷脸认证信息获取
     *
     * @param member 用户
     * @return 刷脸信息
     */
    FaceAuthResultVO faceAuthInfo(Member member);

    /**
     * 重新签署
     *
     * @param proofIdList
     * @return
     */
    void contractReSign(List<Long> proofIdList,Integer force);

    /**
     * 软删除部分数据
     *
     * @param ids 需要删除的id
     */
    void softDel(String ids);

    /**
     * 签单导出
     *
     * @param orderSn 订单编号
     * @return 签单导出文件url
     */
    String paperSignExport(String orderSn);

    /**
     * 获取农服经销商下单合同
     *
     * @param orderSn 订单编号
     * @return 结果vo
     */
    AgricDistributorContractResultVO getAgricDealerContract(String orderSn);

    /**
     * 农服经销商合同签署
     *
     * @param member 用户信息
     * @param paramVO 签署参数
     * @return 签署结果
     */
    ContractSignResultVO agricDealerContractSign(Member member, ContractSignParamVO paramVO);
}
