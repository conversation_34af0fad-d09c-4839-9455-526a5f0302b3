package com.cfpamf.ms.mallorder.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.ms.mallorder.po.OrderProductExtendPO;

/**
 * <AUTHOR> 2021/8/17.
 */
public interface IOrderProductExtendService extends IService<OrderProductExtendPO> {

    /**
     * 订单商品扩展信息
     * 
     * @param orderProductIds
     * @return
     */
    List<OrderProductExtendPO> getOrderProductExtendPOList(List<Long> orderProductIds);

    /**
     * 订单商品扩展信息
     * 
     * @param orderSn
     * @return
     */
    List<OrderProductExtendPO> getOrderProductExtendPOList(String orderSn);

}
