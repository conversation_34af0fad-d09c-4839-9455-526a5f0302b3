package com.cfpamf.ms.mallorder.scheduler;

import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.mallorder.api.feign.OrderAfterServiceFeignClient;
import com.cfpamf.ms.mallorder.model.OrderAfterServiceModel;
import com.cfpamf.ms.mallorder.po.OrderAfterService;
import com.cfpamf.ms.mallorder.po.OrderReturnPO;
import com.cfpamf.ms.mallorder.request.OrderAfterServiceExample;
import com.slodon.bbc.core.config.DomainUrlUtil;
import com.slodon.bbc.core.constant.MemberTplConst;
import com.slodon.bbc.core.constant.StarterConfigConst;
import com.slodon.bbc.core.constant.StoreTplConst;
import com.slodon.bbc.core.util.TimeUtil;
import com.slodon.bbc.starter.mq.entity.MessageSendProperty;
import com.slodon.bbc.starter.mq.entity.MessageSendVO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 系统自动处理售后单
 * 1.商户未审核的退货退款申请
 * 2.商户未审核的换货
 * 3.商户未审核的仅退款申请
 * 4.用户退货发货但是到时间限制商户还未收货
 * <p>
 * cron: 0 0/10 * * * ?
 */
@Component
@Slf4j
public class OrderAfterDealJob {

    @Resource
    private OrderAfterServiceFeignClient orderAfterServiceFeignClient;

    @Autowired
    private OrderAfterServiceModel orderAfterServiceModel;

    @Resource
    private RabbitTemplate rabbitTemplate;

    @XxlJob(value = "OrderAfterDealJob")
    public ReturnT<String> execute(String s) {

        long startTime = System.currentTimeMillis();
        XxlJobHelper.log("start job at local time: {}", LocalDateTime.now());

        log.info("jobSystemCancelOrder() start");
        try {
            Map<String, List<?>> resultMap = orderAfterServiceModel.jobSystemDealAfterService();

            List<OrderReturnPO> returnsReceiveList = (List<OrderReturnPO>) resultMap.get("returnsReceiveList");
            List<OrderReturnPO> returnsApplyList = (List<OrderReturnPO>) resultMap.get("returnsApplyList");
            List<OrderReturnPO> refundApplyList = (List<OrderReturnPO>) resultMap.get("refundApplyList");
//            List<OrderReplacement> replacementsApplyList = (List<OrderReplacement>) resultMap.get("replacementsApplyList");

            //处理退货自动处理消息通知
            if (!CollectionUtils.isEmpty(returnsReceiveList)) {
                for (OrderReturnPO orderReturn : returnsReceiveList) {
                    OrderAfterServiceExample afterServiceExample = new OrderAfterServiceExample();
                    afterServiceExample.setAfsSn(orderReturn.getAfsSn());
                    List<OrderAfterService> orderAfterServiceList = orderAfterServiceFeignClient.getOrderAfterServiceList(afterServiceExample);
                    if (CollectionUtils.isEmpty(orderAfterServiceList)) {
                        continue;
                    }
                    OrderAfterService orderAfterService = orderAfterServiceList.get(0);
                    //添加消息通知
                    List<MessageSendProperty> messageSendPropertyList = new ArrayList<>();
                    messageSendPropertyList.add(new MessageSendProperty("afsSn", orderReturn.getAfsSn()));
                    String msgLinkInfo = "{\"type\":\"return_news\",\"afsSn\":\"" + orderReturn.getAfsSn() + "\"}";
                    MessageSendVO messageSendVO = new MessageSendVO(messageSendPropertyList, null,
                            orderReturn.getStoreId(), StoreTplConst.MEMBER_RETURN_AUTO_RECEIVE_REMINDER, msgLinkInfo);

                    //发送到mq
                    try {
                        rabbitTemplate.convertAndSend(StarterConfigConst.MQ_EXCHANGE_NAME, StarterConfigConst.MQ_QUEUE_NAME_SELLER_MSG, messageSendVO);
                    } catch (Exception e) {
                        log.info("发消息异常捕获,{}", JSON.toJSONString(messageSendVO), e);
                    }
                    //发送会员售后提醒到mq
                    //微信消息通知
                    List<MessageSendProperty> messageSendPropertyList4Wx = new ArrayList<>();
                    messageSendPropertyList4Wx.add(new MessageSendProperty("first", "您的售后单有新的变化。"));
                    messageSendPropertyList4Wx.add(new MessageSendProperty("keyword1", orderReturn.getAfsSn()));
                    messageSendPropertyList4Wx.add(new MessageSendProperty("keyword2", orderReturn.getReturnMoneyAmount().add(orderReturn.getReturnExpressAmount()) + "元"));
                    messageSendPropertyList4Wx.add(new MessageSendProperty("keyword3", orderReturn.getReturnNum() + "件"));
                    messageSendPropertyList4Wx.add(new MessageSendProperty("keyword4", TimeUtil.getDateTimeString(new Date())));
                    messageSendPropertyList4Wx.add(new MessageSendProperty("remark", "服务农村最后一百米"));
                    MessageSendVO sendVO = new MessageSendVO(messageSendPropertyList, messageSendPropertyList4Wx, null, orderReturn.getMemberId(), MemberTplConst.AFTER_SALE_REMINDER, msgLinkInfo);
                    try {
                        rabbitTemplate.convertAndSend(StarterConfigConst.MQ_EXCHANGE_NAME, StarterConfigConst.MQ_QUEUE_NAME_MEMBER_MSG, sendVO);
                    } catch (Exception e) {
                        log.info("发消息异常捕获,{}", JSON.toJSONString(sendVO), e);
                    }
                }
            }
            //处理退货未收货自动处理消息通知
            if (!CollectionUtils.isEmpty(returnsApplyList)) {
                for (OrderReturnPO orderReturn : returnsApplyList) {
                    OrderAfterServiceExample afterServiceExample = new OrderAfterServiceExample();
                    afterServiceExample.setAfsSn(orderReturn.getAfsSn());
                    List<OrderAfterService> orderAfterServiceList = orderAfterServiceFeignClient.getOrderAfterServiceList(afterServiceExample);
                    if (CollectionUtils.isEmpty(orderAfterServiceList)) {
                        continue;
                    }
                    OrderAfterService orderAfterService = orderAfterServiceList.get(0);

                    //添加消息通知
                    List<MessageSendProperty> messageSendPropertyList = new ArrayList<>();
                    messageSendPropertyList.add(new MessageSendProperty("afsSn", orderReturn.getAfsSn()));
                    String msgLinkInfo = "{\"type\":\"return_news\",\"afsSn\":\"" + orderReturn.getAfsSn() + "\"}";
                    MessageSendVO messageSendVO = new MessageSendVO(messageSendPropertyList, null, orderReturn.getStoreId(), StoreTplConst.MEMBER_AUTO_RETURN_REMINDER, msgLinkInfo);

                    //发送到mq
                    try {
                        rabbitTemplate.convertAndSend(StarterConfigConst.MQ_EXCHANGE_NAME, StarterConfigConst.MQ_QUEUE_NAME_SELLER_MSG, messageSendVO);
                    } catch (Exception e) {
                        log.info("发消息异常捕获,{}", JSON.toJSONString(messageSendVO), e);
                    }
                    //发送会员售后提醒到mq
                    //微信消息通知
                    List<MessageSendProperty> messageSendPropertyList4Wx = new ArrayList<>();
                    messageSendPropertyList4Wx.add(new MessageSendProperty("first", "您的售后单有新的变化。"));
                    messageSendPropertyList4Wx.add(new MessageSendProperty("keyword1", orderReturn.getAfsSn()));
                    messageSendPropertyList4Wx.add(new MessageSendProperty("keyword2", orderReturn.getReturnMoneyAmount().add(orderReturn.getReturnExpressAmount()) + "元"));
                    messageSendPropertyList4Wx.add(new MessageSendProperty("keyword3", orderReturn.getReturnNum() + "件"));
                    messageSendPropertyList4Wx.add(new MessageSendProperty("keyword4", orderAfterService.getApplyReasonContent()));
                    messageSendPropertyList4Wx.add(new MessageSendProperty("remark", "服务农村最后一百米"));
                    MessageSendVO sendVO = new MessageSendVO(messageSendPropertyList, messageSendPropertyList4Wx, null, orderReturn.getMemberId(), MemberTplConst.AFTER_SALE_REMINDER, msgLinkInfo);
                    try {
                        rabbitTemplate.convertAndSend(StarterConfigConst.MQ_EXCHANGE_NAME, StarterConfigConst.MQ_QUEUE_NAME_MEMBER_MSG, sendVO);
                    } catch (Exception e) {
                        log.info("发消息异常捕获,{}", JSON.toJSONString(sendVO), e);
                    }
                }
            }
            //处理退款自动处理消息通知
            if (!CollectionUtils.isEmpty(refundApplyList)) {
                for (OrderReturnPO orderReturn : refundApplyList) {
                    OrderAfterServiceExample afterServiceExample = new OrderAfterServiceExample();
                    afterServiceExample.setAfsSn(orderReturn.getAfsSn());
                    List<OrderAfterService> orderAfterServiceList = orderAfterServiceFeignClient.getOrderAfterServiceList(afterServiceExample);
                    if (CollectionUtils.isEmpty(orderAfterServiceList)) {
                        continue;
                    }
                    OrderAfterService orderAfterService = orderAfterServiceList.get(0);

                    //添加消息通知
                    List<MessageSendProperty> messageSendPropertyList = new ArrayList<>();
                    messageSendPropertyList.add(new MessageSendProperty("refundSn", orderReturn.getAfsSn()));
                    String msgLinkInfo = "{\"type\":\"refund_news\",\"refundSn\":\"" + orderReturn.getAfsSn() + "\"}";
                    MessageSendVO messageSendVO = new MessageSendVO(messageSendPropertyList, null, orderReturn.getStoreId(), StoreTplConst.MEMBER_AUTO_REFUND_REMINDER, msgLinkInfo);

                    //发送到mq
                    try {
                        rabbitTemplate.convertAndSend(StarterConfigConst.MQ_EXCHANGE_NAME, StarterConfigConst.MQ_QUEUE_NAME_SELLER_MSG, messageSendVO);
                    } catch (Exception e) {
                        log.info("发消息异常捕获,{}", JSON.toJSONString(messageSendVO), e);
                    }
                    //发送会员售后提醒到mq
                    //微信消息通知
                    List<MessageSendProperty> messageSendPropertyList4Wx = new ArrayList<>();
                    messageSendPropertyList4Wx.add(new MessageSendProperty("first", "您的售后单有新的变化。"));
                    messageSendPropertyList4Wx.add(new MessageSendProperty("keyword1", orderReturn.getAfsSn()));
                    messageSendPropertyList4Wx.add(new MessageSendProperty("keyword2", orderReturn.getReturnMoneyAmount().add(orderReturn.getReturnExpressAmount()) + "元"));
                    messageSendPropertyList4Wx.add(new MessageSendProperty("keyword3", orderReturn.getReturnNum() + "件"));
                    messageSendPropertyList4Wx.add(new MessageSendProperty("keyword4", orderAfterService.getApplyReasonContent()));
                    messageSendPropertyList4Wx.add(new MessageSendProperty("remark", "服务农村最后一百米"));
                    MessageSendVO sendVO = new MessageSendVO(messageSendPropertyList, messageSendPropertyList4Wx, null, orderReturn.getMemberId(), MemberTplConst.AFTER_SALE_REMINDER, msgLinkInfo);
                    try {
                        rabbitTemplate.convertAndSend(StarterConfigConst.MQ_EXCHANGE_NAME, StarterConfigConst.MQ_QUEUE_NAME_MEMBER_MSG, sendVO);
                    } catch (Exception e) {
                        log.info("发消息异常捕获,{}", JSON.toJSONString(sendVO), e);
                    }
                }
            }
            //暂时注释换货部分
//            if (!CollectionUtils.isEmpty(replacementsApplyList)) {
//                for (OrderReplacement replacement : replacementsApplyList) {
//                    //消息通知
//                    List<MessageSendProperty> messageSendPropertyList = new ArrayList<>();
//                    messageSendPropertyList.add(new MessageSendProperty("afsSn", replacement.getAfsSn()));
//                    String msgLinkInfo = "{\"type\":\"replacement_news\",\"afsSn\":\"" + replacement.getAfsSn() + "\"}";
//                    MessageSendVO messageSendVO = new MessageSendVO(messageSendPropertyList, null, replacement.getStoreId(), StoreTplConst.MEMBER_REPLACEMENT_REMINDER, msgLinkInfo);
//
//                    //发送到mq
//                    rabbitTemplate.convertAndSend(MQ_EXCHANGE_NAME, MQ_QUEUE_NAME_SELLER_MSG, messageSendVO);
//                }
//            }
        } catch (Exception e) {
            log.error("jobSystemDealAfterService()", e);
        }

        XxlJobHelper.log("finish job at local time: {}  cost:{}",
                LocalDateTime.now(),
                (System.currentTimeMillis() - startTime) / 1000);

        return new ReturnT("SUCCESS");
    }

}
