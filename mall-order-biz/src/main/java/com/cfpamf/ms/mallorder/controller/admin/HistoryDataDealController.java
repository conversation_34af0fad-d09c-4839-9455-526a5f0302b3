package com.cfpamf.ms.mallorder.controller.admin;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.event.SyncReadListener;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.mq.OrderOutBoundConsumer;
import com.cfpamf.ms.mallorder.common.mq.msg.TransferRefundMsg;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.ErpPerformanceOutBoundNotifyDTO;
import com.cfpamf.ms.mallorder.dto.OrderRefundEventNotifyDTO;
import com.cfpamf.ms.mallorder.dto.ProductCostDTO;
import com.cfpamf.ms.mallorder.model.OrderAfterServiceModel;
import com.cfpamf.ms.mallorder.model.OrderProductModel;
import com.cfpamf.ms.mallorder.model.OrderReturnModel;
import com.cfpamf.ms.mallorder.po.OrderDeliveryRecordPO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderProductPO;
import com.cfpamf.ms.mallorder.scheduler.JsPkDynamicPerformanceJob;
import com.cfpamf.ms.mallorder.service.*;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.query.Param;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;


/**
 * @Author: zml
 * @CreateTime: 2022/9/7 19:18
 */
@RestController
@RequestMapping("admin/history/data/deal")
@Api(tags = "历史数据处理")
@Slf4j
public class HistoryDataDealController {

    @Resource
    private OrderReturnModel orderReturnModel;
    @Autowired
    private IOrderService orderService;
    @Autowired
    private IOrderDatePushService orderDatePushService;
    @Autowired
    private IOrderProductService iOrderProductService;

    @Autowired
    private IHistoryDataDealService historyDataDealService;

    @Autowired
    private IDingTalkFlowService dingTalkFlowService;
    @Autowired
    private OrderAfterServiceModel orderAfterServiceModel;

    @Autowired
    private OrderProductModel orderProductModel;

    @ApiOperation("订单喜报推送")
    @GetMapping("/paySuccessDataPush")
    public JsonResult<String> dataPushMatch(@ApiParam(value = "订单号", required = true)
                                            @RequestParam(value = "orderSn") String orderSn) {
        orderDatePushService.paySuccessDataPush(orderSn);
        return SldResponse.success("处理成功");
    }

    @ApiOperation("订单用户身份历史数据处理")
    @GetMapping("/orderUserIdentity")
    public JsonResult<String> orderUserIdentity() {
        historyDataDealService.orderUserIdentity();
        return SldResponse.success("处理成功");
    }

    @ApiOperation("恢复额度未退库存售后单处理")
    @ApiImplicitParams(@ApiImplicitParam(name = "afsSn", value = "退款单号", required = true, paramType = "query"))
    @GetMapping("afterSale/return/stock")
    public JsonResult afterSaleReturnStock(HttpServletRequest request, String afsSn) {
        orderReturnModel.afterSaleReturnStock(afsSn);
        return SldResponse.success("处理成功");
    }

    @ApiOperation("修复异常平台服务费")
    @GetMapping("serviceFee/fix")
    public JsonResult<Boolean> serviceFee(@ApiParam(value = "订单号", required = true)
                                          @RequestParam(value = "orderSn") String orderSn) {
        OrderPO orderPO = orderService.lambdaQuery().eq(OrderPO::getOrderSn, orderSn).one();
        BigDecimal orderPlatformServiceFee = orderPO.getServiceFee();

        List<OrderProductPO> orderProductPOS = iOrderProductService.lambdaQuery().eq(OrderProductPO::getOrderSn, orderSn)
                .eq(OrderProductPO::getIsGift, OrderConst.IS_GIFT_NO).list();

        BigDecimal fixedServiceFee = BigDecimal.ZERO; // 商品行服务费累计值
        List<OrderProductPO> updateOrderProductS = new ArrayList<>();

        for (int i = 0, s = orderProductPOS.size(); i < s; i++) {
            OrderProductPO orderProductPO = orderProductPOS.get(i);

            OrderProductPO updateProduct = new OrderProductPO();
            updateProduct.setOrderProductId(orderProductPO.getOrderProductId());
            // 只有一行商品
            if (s == 1) {
                updateProduct.setServiceFee(orderPlatformServiceFee);
            } else if (i == s - 1) { // 最后一行商品 钆差
                updateProduct.setServiceFee(orderPlatformServiceFee.subtract(fixedServiceFee));
            } else {
                BigDecimal productPrice = orderProductPO.getProductShowPrice()
                        .multiply(BigDecimal.valueOf(orderProductPO.getProductNum()))
                        .subtract(orderProductPO.getStoreActivityAmount())
                        .subtract(orderProductPO.getStoreVoucherAmount());
                BigDecimal orderPrice = orderPO.getGoodsAmount().subtract(orderPO.getStoreActivityAmount())
                        .subtract(orderPO.getStoreVoucherAmount());
                BigDecimal productServiceFee = BigDecimal.ZERO;
                if (orderPrice.compareTo(BigDecimal.ZERO) > 0) {
                    productServiceFee = productPrice.multiply(orderPO.getServiceFeeRate()).setScale(2, RoundingMode.HALF_UP);
                }
                updateProduct.setServiceFee(productServiceFee);

                fixedServiceFee = fixedServiceFee.add(productServiceFee);

            }
            updateOrderProductS.add(updateProduct);
        }
        return SldResponse.success(iOrderProductService.updateBatchById(updateOrderProductS));
    }

    @ApiOperation("退款单平台服务费处理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "afsSn", value = "退款单号", required = true, paramType = "query"),
            @ApiImplicitParam(name = "serviceFee", value = "平台服务费", required = true, paramType = "query")
    })
    @GetMapping("returnServiceFeeFix")
    public JsonResult<Integer> returnServiceFeeFix(String afsSn, BigDecimal serviceFee) {
        return SldResponse.success(historyDataDealService.returnServiceFeeFix(afsSn, serviceFee));
    }

    @GetMapping("orderProductCostFix")
    public JsonResult<String> orderProductCostFix(@RequestParam(value = "orderSn",required = false) String orderSn) {
        return SldResponse.success(historyDataDealService.orderProductCostFix(orderSn));
    }

    @Resource
    private JsPkDynamicPerformanceJob jsPkDynamicPerformanceJob;

    @GetMapping("test")
    public void test() throws Exception {
        jsPkDynamicPerformanceJob.execute();
    }
    @PostMapping("orderProductCostFix/import")
    public JsonResult<String> orderProductCostFixImport(@Param("file") MultipartFile file) throws IOException {
        SyncReadListener excelListener = new SyncReadListener();
        EasyExcel.read(file.getInputStream(), ProductCostDTO.class, excelListener).doReadAll();
        List<Object> list = excelListener.getList();
        List<ProductCostDTO> costList = new ArrayList<>();
        for (Object o : list) {
            ProductCostDTO productCostDTO = (ProductCostDTO) o;
            costList.add(productCostDTO);
        }
        return SldResponse.success(historyDataDealService.orderProductCostFixImport(costList));
    }


    @ApiOperation("手动进行预付尾款退款")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "afsSn", value = "退款单号", required = true, paramType = "query"),
            @ApiImplicitParam(name = "serviceFee", value = "平台服务费", required = true, paramType = "query")
    })
    @GetMapping("cancelPreSellBalance")
    public JsonResult<Boolean> cancelPreSellBalance(@ApiParam(value = "订单号", required = true)
                                                    @RequestParam(value = "orderSn") String orderSn) {
        return SldResponse.success(historyDataDealService.cancelPreSellBalance(orderSn));
    }

    @ApiOperation("历史订单税率处理")
    @GetMapping("orderProductRateFix")
    public JsonResult<Boolean> orderProductRateFix() {
        return SldResponse.success(historyDataDealService.orderProductRateFix());
    }

    @ApiOperation("自提订单物流信息补充")
    @GetMapping("orderSelfLiftLogisticFix")
    public JsonResult<Boolean> orderSelfLiftLogisticFix() {
        return SldResponse.success(historyDataDealService.orderSelfLiftLogisticFix());
    }

    @ApiOperation("金融订单+线下补录订单物流信息补充")
    @GetMapping("historyOrderLogisticFix")
    public JsonResult<Boolean> historyOrderLogisticFix() {
        return SldResponse.success(historyDataDealService.historyOrderLogisticFix());
    }

    @Resource
    private OrderOutBoundConsumer orderOutBoundConsumer;

    @ApiOperation("test")
    @PostMapping("test")
    public JsonResult<Boolean> tset(@RequestBody ErpPerformanceOutBoundNotifyDTO notifyDTO) {
        orderOutBoundConsumer.dealErpOutBoundResult(notifyDTO);
        return SldResponse.success(Boolean.TRUE);
    }

    @ApiOperation("订单管护分支、客户经理落库手动维护")
    @GetMapping("fixManager")
    public JsonResult<Boolean> fixManager(@ApiParam(value = "订单号", required = true) @RequestParam(value = "orderSn") String orderSn,
                                          @ApiParam(value = "客户经理工号", required = true) @RequestParam(value = "userNo") String userNo,
                                          @ApiParam(value = "分支编码", required = true) @RequestParam(value = "branchCode") String branchCode,
                                          @ApiParam(value = "分支名称", required = true) @RequestParam(value = "branchName") String branchName,
                                          @ApiParam(value = "区域编码", required = true) @RequestParam(value = "areaCode") String areaCode,
                                          @ApiParam(value = "区域名称", required = true) @RequestParam(value = "areaName") String areaName) {
        return SldResponse.success(historyDataDealService.fixManager(orderSn, userNo,branchCode,branchName,areaCode,areaName));
    }

    @ApiOperation("按照店铺手动维护订单管护分支、客户经理落库")
    @GetMapping("fixManagerByStoreId")
    public JsonResult<Boolean> fixManagerByStoreId(@ApiParam(value = "店铺id", required = true)
                                          @RequestParam(value = "storeId") String storeId) {
        return SldResponse.success(historyDataDealService.fixManagerByStoreId(storeId));
    }


    @ApiOperation("线下补录订单，交易成功补发")
    @GetMapping("offlineOrder")
    public JsonResult<Boolean> offlineOrder(@ApiParam(value = "订单号", required = true)
                                          @RequestParam(value = "orderSns") String orderSns) {
        return SldResponse.success(historyDataDealService.offlineOrderV2(orderSns));
    }

    @ApiOperation("线下订单历史数据处理")
    @GetMapping("offlineOrderFix")
    public JsonResult<Boolean> offlineOrderFix(@ApiParam(value = "订单号", required = true)
                                            @RequestParam(value = "orderSns") String orderSns) {
        return SldResponse.success(historyDataDealService.offlineOrderFix(orderSns));
    }


    @ApiOperation("发货历史数据处理")
    @GetMapping("updatePerformanceChannel")
    public JsonResult<Boolean> updatePerformanceChannel() {
        return SldResponse.success(historyDataDealService.updatePerformanceChannel());
    }



    @ApiOperation("成都天杰--包裹唯一键")
    @PostMapping("getPackageUniqueCode")
    public JsonResult<List<OrderDeliveryRecordPO>> getPackageUniqueCode(@RequestBody List<String> bizUniqueNoList) {
        return SldResponse.success(historyDataDealService.getPackageUniqueCode(bizUniqueNoList));
    }

    /**
     * 查询发货信息表，bz_order_logistic对应的订单，对应的商品；通过商品行的logistics_id关联，
     * 将刚商品的信息及发货数量插入到bz_order_logistic_item表，如果没有包裹号，就重新生成包裹号
     * 已发货数量 = 商品数量
     */
    @ApiOperation("包裹商品明细表，发货数量")
    @GetMapping("initLogisticItem")
    public JsonResult<String> initLogisticItem() {
        historyDataDealService.initLogisticItem();
        return SldResponse.success("success");
    }

    @ApiOperation("线下补录订单扩展表历史数据处理")
    @GetMapping("offline/extend/deal")
    public JsonResult<String> offlineExtendDeal() {
        historyDataDealService.offlineExtendDeal();
        return SldResponse.success("success");
    }

    @ApiOperation("商品发货状态处理")
    @GetMapping("updateProductDeliveryState")
    public JsonResult<Boolean> updateProductDeliveryState(@ApiParam(value = "商品主键id", required = true)
                                            @RequestParam(value = "orderProductId") String orderProductIds,
                                                @ApiParam(value = "商品主键id", required = true)
                                                @RequestParam(value = "deliveryState") int deliveryState) {
        return SldResponse.success(historyDataDealService.updateProductDeliveryState(orderProductIds, deliveryState));
    }


    @ApiOperation("线下订单发货数据处理")
    @GetMapping("offlineOrderLogisticsItem")
    public JsonResult<Boolean> offlineOrderLogisticsItem(@ApiParam(value = "订单号", required = true)
                                               @RequestParam(value = "orderSns") String orderSns) {
        return SldResponse.success(historyDataDealService.offlineOrderLogisticsItem(orderSns));
    }

    @ApiOperation("京东拦截")
    @GetMapping("jdIntercept")
    public JsonResult<Boolean> jdIntercept(@ApiParam(value = "订单号", required = true)
                                                         @RequestParam(value = "param") String param) {
        return SldResponse.success(historyDataDealService.jdIntercept(param));
    }


    @ApiOperation("refundSuccess")
    @PostMapping("refundSuccess")
    public JsonResult<Boolean> refundSuccess(@RequestBody TransferRefundMsg refundMsg) {
        return SldResponse.success(orderAfterServiceModel.loanRefundResult(refundMsg));
    }


    @ApiOperation("refundNotifyDingTalkFlow")
    @PostMapping("refundNotifyDingTalkFlow")
    public JsonResult<Boolean> refundNotifyDingTalkFlow(@RequestBody OrderRefundEventNotifyDTO notifyDTO) {
        dingTalkFlowService.orderReturnDingTalkFlowNotify(notifyDTO);
        return SldResponse.success(true);
    }

    @ApiOperation("更新商品行渠道手续费")
    @GetMapping("/saveChannelServiceFee2OrderProduct")
    public JsonResult<String> saveChannelServiceFee2OrderProduct(
            @ApiParam(value = "订单号", required = true)
            @RequestParam(value = "orderSn") String orderSn,
            @ApiParam(value = "订单渠道手续费", required = true)
            @RequestParam(value = "channelServiceFee") String channelServiceFee
            ) {

        BigDecimal channelServiceFeeDec = null;
        if (channelServiceFee != null){
            channelServiceFeeDec = new BigDecimal(channelServiceFee);
        }

        orderProductModel.saveChannelServiceFee2OrderProduct(orderSn, channelServiceFeeDec);
        return SldResponse.success("处理成功");
    }


    @ApiOperation("按照区域更新维护订单管护组织区域、片区、分支信息")
    @GetMapping("fixOrgByArea")
    public JsonResult<Boolean> fixOrgByArea(@ApiParam(value = "区域编号", required = true)
                                           @RequestParam(value = "areaCode") String areaCode) {
        return SldResponse.success(historyDataDealService.fixOrgInfo(areaCode));
    }

    @ApiOperation("按照区域更新维护订单管护组织区域、片区、分支信息")
    @GetMapping("fixOrgByAreaInManager")
    public JsonResult<Boolean> fixOrgByAreaInManager(@ApiParam(value = "区域编号", required = true)
                                            @RequestParam(value = "areaCode") String areaCode) {
        return SldResponse.success(historyDataDealService.fixOrgInfoInManager(areaCode));
    }

    @ApiOperation("按照订单更新维护订单管护组织区域、片区、分支信息")
    @GetMapping("fixOrgInfoInOrderSn")
    public JsonResult<Boolean> fixOrgInfoInOrderSn(@ApiParam(value = "订单号", required = true)
                                                     @RequestParam(value = "orderSn") String orderSn) {
        return SldResponse.success(historyDataDealService.fixOrgInfoInOrderSn(orderSn));
    }

    @ApiOperation("售后主任确认收货操作")
    @GetMapping("directorAuditRefund")
    public JsonResult<Boolean> directorAuditRefund(@RequestParam(value = "afsSn") String afsSn) {
        Member member = new Member();
        member.setMemberId(CommonConst.SYSTEM_ID);
        member.setMemberName(CommonConst.ADMIN_NAME_EN);
        member.setMemberMobile(CommonConst.ADMIN_PHONE);
        return  SldResponse.success(orderAfterServiceModel.selfPointReceiveGoods(new Member(), afsSn));
    }

}
