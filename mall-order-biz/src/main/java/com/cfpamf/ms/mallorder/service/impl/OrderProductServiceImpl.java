package com.cfpamf.ms.mallorder.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cdfinance.ms.facade.model.request.signBefore.ContractFindRequest;
import com.cdfinance.ms.facade.model.response.signBefore.ContractFindResponse;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.mall.filecenter.domain.vo.FileScenesProofVO;
import com.cfpamf.ms.mallgoods.facade.api.GoodsFeignClient;
import com.cfpamf.ms.mallgoods.facade.api.ProductFeignClient;
import com.cfpamf.ms.mallorder.common.config.OrderMaterialConfig;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.enums.*;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.common.util.ExternalApiUtil;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.controller.fegin.facade.MsContractFacade;
import com.cfpamf.ms.mallorder.dto.*;
import com.cfpamf.ms.mallorder.enums.OrderEventEnum;
import com.cfpamf.ms.mallorder.enums.OrderGroupBuyingEnum;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.integration.erp.ERPIntegration;
import com.cfpamf.ms.mallorder.integration.erp.vo.MallProductQuery;
import com.cfpamf.ms.mallorder.integration.erp.vo.ProductVO;
import com.cfpamf.ms.mallorder.integration.facade.AgricHostOrderFacade;
import com.cfpamf.ms.mallorder.integration.facade.OrderQueryFacade;
import com.cfpamf.ms.mallorder.integration.facade.dto.ProductSkuUnitVO;
import com.cfpamf.ms.mallorder.mapper.OrderMapper;
import com.cfpamf.ms.mallorder.mapper.OrderProductMapper;
import com.cfpamf.ms.mallorder.mapper.OrderReturnMapper;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.model.OrderProductModel;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.request.OrderProductExample;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.v2.service.OrderPayRecordService;
import com.cfpamf.ms.mallorder.vo.*;
import com.cfpamf.smartid.client.utils.sharding.SeqEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.exception.MallException;
import com.slodon.bbc.core.util.AssertUtil;
import com.slodon.bbc.core.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 订单正向操作 service
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/07/18 17:02
 */
@Slf4j
@Service
public class OrderProductServiceImpl extends ServiceImpl<OrderProductMapper, OrderProductPO>
        implements IOrderProductService {
    @Resource
    private ProductFeignClient productFeignClient;
    @Resource
    private GoodsFeignClient goodsFeignClient;
    @Resource
    private OrderProductMapper orderProductMapper;
    @Resource
    private OrderReturnMapper orderReturnMapper;
    @Resource
    private OrderMapper orderMapper;
    @Autowired
    private IOrderService iOrderService;
    @Autowired
    private IOrderReturnService orderReturnService;
    @Autowired
    private OrderQueryFacade orderQueryFacade;
    @Autowired
    private OrderPayRecordService orderPayRecordService;
    @Autowired
    private IOrderExtendService iOrderExtendService;
    @Autowired
    private IOrderExtendFinanceService iOrderExtendFinanceService;
    @Autowired
    private AgricHostOrderFacade agricHostOrderFacade;
    @Autowired
    private IFrontAfterSaleApplyService frontAfterSaleApplyService;

    @Autowired
    private OrderProductModel orderProductModel;
    @Autowired
    private IOrderTradeProofService orderTradeProofService;

    @Autowired
    private ERPIntegration erpIntegration;

    @Autowired
    private OrderModel orderModel;

    @Autowired
    private OrderMaterialConfig orderMaterialConfig;

    @Autowired
    private MsContractFacade contractFacade;

    @Override
    public boolean addReturnNumber(Long orderProductId, int num) {
        OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(orderProductId);
        orderProductPO.setReturnNumber(num);
        return orderProductMapper.addReturnNumber(orderProductPO) == 1;
    }

    @Override
    public boolean deductReturnNumber(Long orderProductId, int num) {
        OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(orderProductId);
        orderProductPO.setReturnNumber(num);
        return orderProductMapper.deductReturnNumber(orderProductPO) == 1;
    }

    @Override
    public boolean dealOrderProductReturnStatus(String handleType, Long orderProductId) {
        LambdaQueryWrapper<OrderProductPO> queryProduct = Wrappers.lambdaQuery();
        queryProduct.eq(OrderProductPO::getOrderProductId, orderProductId)
                .select(OrderProductPO::getOrderProductId, OrderProductPO::getProductNum, OrderProductPO::getReturnNumber);
        OrderProductPO orderProductPo = this.getOne(queryProduct);
        log.info("处理订单商品行退款状态，操作类型：{}，商品数量：{}，商品已退数量：{}", handleType,
                orderProductPo.getProductNum(), orderProductPo.getReturnNumber());

        // 允许多次退，最多只有一个退款单在退款中
        LambdaUpdateWrapper<OrderProductPO> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(OrderProductPO::getOrderProductId, orderProductId);
        switch (handleType) {
            case CommonConst.PRODUCT_RETURN_PLATFORM_PASS:
                if (orderProductPo.getReturnNumber().equals(orderProductPo.getProductNum())) {
                    // 退的数量 = 商品数量，全退
                    updateWrapper.set(OrderProductPO::getStatus, OrderProductStatusEnum.ALL_RETURN.getValue());
                } else {
                    // 退的数量 < 商品数量，部分退
                    updateWrapper.set(OrderProductPO::getStatus, OrderProductStatusEnum.PART_RETURN.getValue());
                }
                super.update(updateWrapper);
                break;
            case CommonConst.PRODUCT_RETURN_REJECT:
                // 商家拒绝、平台拒绝
                if (orderProductPo.getReturnNumber() == 0) {
                    // 第一次退款，退款数量归还为0
                    updateWrapper.set(OrderProductPO::getStatus, OrderProductStatusEnum.NO_RETURN.getValue());
                } else {
                    // 多次退款最后一次拒绝，部分退款
                    updateWrapper.set(OrderProductPO::getStatus, OrderProductStatusEnum.PART_RETURN.getValue());
                }
                super.update(updateWrapper);
                break;
            case CommonConst.PRODUCT_RETURN_APPLY:
                // 申请
                updateWrapper.set(OrderProductPO::getStatus, OrderProductStatusEnum.RETURNING.getValue());
                super.update(updateWrapper);
                break;
            default:
                log.error("处理订单商品化退款状态，未识别的操作类型：{}", handleType);
        }
        return Boolean.TRUE;
    }


    @Override
    public boolean returnAll(String orderSn) {
        return orderProductMapper.returnAll(orderSn) > 0;
    }

    @Override
    public Boolean isAllDelivery(String orderSn) {
        // 不存在待发货商品（所有商品都已发货），则发货完成
        LambdaQueryWrapper<OrderProductPO> query = new LambdaQueryWrapper<>();
        query.eq(OrderProductPO::getOrderSn, orderSn)
                .in(OrderProductPO::getDeliveryState, Arrays.asList(OrderProductDeliveryEnum.WAIT_DELIVERY
                        , OrderProductDeliveryEnum.OUTBOUND
                        ,OrderProductDeliveryEnum.PART_DELIVERY))
                .select(OrderProductPO::getOrderProductId);
        List<OrderProductPO> productPOS = list(query);
        if (CollectionUtils.isEmpty(productPOS)) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    public Boolean dealOrderProductGroupBuyingTag(List<Long> orderProductIds) {
        LambdaUpdateWrapper<OrderProductPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(OrderProductPO::getOrderProductId, orderProductIds)
                .eq(OrderProductPO::getDeliveryState, OrderProductDeliveryEnum.WAIT_DELIVERY)
                .eq(OrderProductPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                .eq(OrderProductPO::getGroupBuyingTag, OrderGroupBuyingEnum.WAITING.getValue())
                .set(OrderProductPO::getGroupBuyingTag, OrderGroupBuyingEnum.FINISHED.getValue());

        boolean update = this.update(updateWrapper);
        BizAssertUtil.isTrue(!update, "订单商品拼单状态更新失败，请检查数据是否正确");

        return Boolean.TRUE;
    }

    @Override
    public Boolean updateLogistic(String orderSn, List<Long> orderProductIds, Long logisticId) {
        // 订单商品校验
        LambdaQueryWrapper<OrderProductPO> productQuery = new LambdaQueryWrapper<>();
        productQuery.eq(OrderProductPO::getOrderSn, orderSn)
                .in(OrderProductPO::getOrderProductId, orderProductIds)
                .eq(OrderProductPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y).select(OrderProductPO::getOrderProductId);
        List<OrderProductPO> productPOS = list(productQuery);
        if (CollectionUtils.isEmpty(productPOS)
                || productPOS.size() < orderProductIds.size()) {
            throw new BusinessException("请选择正确的订单商品");
        }

        // 订单商品关联物流
        productPOS.stream().forEach(po -> {
            //po.setLogisticId(logisticId);
            po.setDeliveryState(OrderProductDeliveryEnum.DELIVERED);
        });
        boolean update = updateBatchById(productPOS);
        if (!update) {
            throw new MallException("更新订单商品物流失败");
        }

        return Boolean.TRUE;
    }

    @Override
    public Boolean orderProductDelivery(String orderSn) {
        LambdaUpdateWrapper<OrderProductPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OrderProductPO::getOrderSn, orderSn)
                .eq(OrderProductPO::getDeliveryState, OrderProductDeliveryEnum.WAIT_DELIVERY)
                .eq(OrderProductPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                .set(OrderProductPO::getDeliveryState, OrderProductDeliveryEnum.DELIVERED);

        this.update(updateWrapper);

        return Boolean.TRUE;
    }

    @Override
    public Map<String, OrderProductVO> bizOrderProducts(List<String> bizSnList) {
        Map<String, OrderProductVO> map = new HashMap<>(bizSnList.size());

        Set<String> afsSnList = new HashSet<>();
        Set<String> orderSnList = new HashSet<>();
        for (String bizSn : bizSnList) {
            if (bizSn.startsWith(SeqEnum.RNO.prefix())) {
                afsSnList.add(bizSn);
            } else {
                orderSnList.add(bizSn);
            }
        }

        if (!CollectionUtils.isEmpty(afsSnList)) {
            Map<String, OrderProductVO> orderReturnProducts = this.getOrderReturnProducts(new ArrayList<>(afsSnList));
            map.putAll(orderReturnProducts);
        }
        if (!CollectionUtils.isEmpty(orderSnList)) {
            Map<String, OrderProductVO> orderProducts = getOrderProducts(new ArrayList<>(orderSnList));
            map.putAll(orderProducts);
        }

        return map;
    }

    public Map<String, OrderProductVO> getOrderProducts(List<String> bizSnList) {
        Map<String, OrderProductVO> map = new HashMap<>(bizSnList.size());

        List<Map<String, Object>> productListMap = orderMapper.getProductList(bizSnList);
        for (Map<String, Object> productInfo : productListMap) {

            OrderProductVO vo = new OrderProductVO();
            vo.setBizSn(productInfo.get("orderSn").toString());
            vo.setOrderProductNum(Integer.valueOf(productInfo.get("num").toString()));
            vo.setOrderProducts(productInfo.get("goodsName").toString());
            vo.setOrderProductsSku(productInfo.get("productId").toString());
            map.put(vo.getBizSn(), vo);
        }
        return map;
    }

    public Map<String, OrderProductVO> getOrderReturnProducts(List<String> bizSnList) {
        Map<String, OrderProductVO> map = new HashMap<>(bizSnList.size());

        List<Map<String, Object>> productListMap = orderReturnMapper.getProductList(bizSnList);
        for (Map<String, Object> productInfo : productListMap) {

            OrderProductVO vo = new OrderProductVO();
            vo.setBizSn(productInfo.get("afsSn").toString());
            vo.setOrderProductNum(1);
            vo.setOrderProducts(productInfo.get("goodsName").toString());
            vo.setOrderProductsSku(productInfo.get("productId").toString());
            map.put(vo.getBizSn(), vo);
        }
        return map;
    }

    @Override
    public List<OrderProductPO> listByOrderSn(String orderSn) {
        LambdaQueryWrapper<OrderProductPO> productQuery = new LambdaQueryWrapper<>();
        productQuery.eq(OrderProductPO::getOrderSn, orderSn);
        productQuery.eq(OrderProductPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
        return this.list(productQuery);
    }

    @Override
    public List<OrderProductPO> listByOrderSns(Set<String> orderSns) {
        if (CollectionUtils.isEmpty(orderSns)) {
            return null;
        }
        LambdaQueryWrapper<OrderProductPO> productQuery = new LambdaQueryWrapper<>();
        productQuery.in(OrderProductPO::getOrderSn, orderSns);
        productQuery.eq(OrderProductPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
        return this.list(productQuery);
    }

    /**
     * @return com.cfpamf.ms.mallorder.vo.OrderDetailVO
     * @description : 根据订单号查询自研以及标准电商订单商品信息
     */
    @Override
    public OrderLoanDetailVO orderDetail(String orderNo) {
        AssertUtil.isTrue(StringUtil.isBlank(orderNo), "订单号不能为空");

        // 查询订单信息
        OrderPO orderDb = iOrderService.getOne(Wrappers.lambdaQuery(OrderPO.class)
                .eq(OrderPO::getOrderSn, orderNo)
                .last("limit 1"));
        /**
         * 标准电商普通订单信息不为空处理
         */
        if (Objects.nonNull(orderDb)) {
            String orderSn = orderDb.getOrderSn();
            OrderExtendPO extendPO = iOrderExtendService.lambdaQuery()
                    .eq(OrderExtendPO::getOrderSn, orderSn)
                    .one();
            OrderExtendFinancePO orderExtendFinancePO = iOrderExtendFinanceService.lambdaQuery()
                    .eq(OrderExtendFinancePO::getOrderSn, orderSn)
                    .one();
            BizAssertUtil.notNull(extendPO, "扩展信息为空");
            List<FileScenesProofVO> fileScenesProofVOS = orderTradeProofService.queryScenesMaterial(orderSn, null, null, null,false);
            OrderDetailVO orderDetailVO = buildOrderDetailVO(
                    orderNo,
                    orderDb.getOrderAmount(),
                    orderDb.getStoreName(),
                    orderDb.getOrderSn(),
                    orderDb.getRuleTag(),
                    orderDb.getLoanPayer(),
                    extendPO,
                    orderExtendFinancePO,
                    fileScenesProofVOS
            );
            return dealEleSign(orderDetailVO);
        }
        //查询预付支付单信息
        OrderPayRecordPO orderPayRecordPO = orderPayRecordService.lambdaQuery()
                .eq(OrderPayRecordPO::getPayNo, orderNo)
                .last("limit 1")
                .one();
        /**
         * 标准电商预付订单信息不为空
         */
        if (Objects.nonNull(orderPayRecordPO)) {
            OrderPO orderDbByPrell = iOrderService.getOne(Wrappers.lambdaQuery(OrderPO.class)
                    .eq(OrderPO::getPaySn, orderPayRecordPO.getPaySn())
                    .last("limit 1"));
            if (Objects.nonNull(orderDbByPrell)) {
                String orderSn = orderDbByPrell.getOrderSn();
                OrderExtendPO extendPO = iOrderExtendService.lambdaQuery()
                        .eq(OrderExtendPO::getOrderSn, orderSn)
                        .one();
                OrderExtendFinancePO orderExtendFinancePO = iOrderExtendFinanceService.lambdaQuery()
                        .eq(OrderExtendFinancePO::getOrderSn, orderSn)
                        .one();
                BizAssertUtil.notNull(extendPO, "扩展信息为空");
                List<FileScenesProofVO> fileScenesProofVOS = orderTradeProofService.queryScenesMaterial(orderSn, null, null, null,false);
                //取预付用呗支付单金额
                OrderDetailVO orderDetailVO = buildOrderDetailVO(
                        orderNo,
                        orderPayRecordPO.getAmount(),
                        orderDbByPrell.getStoreName(),
                        orderDbByPrell.getOrderSn(),
                        orderDbByPrell.getRuleTag(),
                        orderDbByPrell.getLoanPayer(),
                        extendPO,
                        orderExtendFinancePO,
                        fileScenesProofVOS
                );
                return dealEleSign(orderDetailVO);
            }
        }
        /**
         * 标准电商订单不存在，查自研电商订单
         */
        Result<OrderLoanDetailVO> orderDetailVOResult = orderQueryFacade.orderDetail(orderNo);
        if (orderDetailVOResult == null || !orderDetailVOResult.isSuccess()) {
            log.error("用呗复核查询订单商品信息时为空，请处理！" + orderNo);
            throw new MallException("订单商品信息为空，请联系管理员");
        }
        if (Objects.nonNull(orderDetailVOResult.getData())) {
            return orderDetailVOResult.getData();
        }
        /**
         * 自研电商不存在，查农服
         */
        Result<OrderLoanDetailVO> result = agricHostOrderFacade.loanOrderQuery(orderNo);
        if (result == null || !result.isSuccess()) {
            log.error("用呗复核查询订单商品信息时为空，请处理！" + orderNo);
            throw new MallException("订单商品信息为空，请联系管理员");
        }
        if (Objects.nonNull(result.getData())) {
            return result.getData();
        }
        return null;
    }

    private OrderLoanDetailVO dealEleSign(OrderDetailVO orderDetailVO) {
        if (Objects.isNull(orderDetailVO)){
            return null;
        }
        OrderLoanDetailVO loanDetailVO = new OrderLoanDetailVO(orderDetailVO);
        List<OrderTradeProofPO> proofList = orderTradeProofService.getListByOrderSn(orderDetailVO.getOrderSn());
        if (CollectionUtils.isEmpty(proofList)){
            return loanDetailVO;
        }
        Map<String, List<OrderTradeProofPO>> proofMap = proofList.stream().collect(Collectors.groupingBy(OrderTradeProofPO::getSceneNo));
        List<FileLoanScenesProofVO> fileScenesProofVOS = loanDetailVO.getFileScenesProofVOS();
        if (CollectionUtils.isEmpty(fileScenesProofVOS)){
            return loanDetailVO;
        }
        for (FileLoanScenesProofVO proofVO : fileScenesProofVOS) {
            List<FileLoanScenesProofMaterialVO> materialVOList = proofVO.getMaterialVOList();
            if (CollectionUtils.isEmpty(materialVOList)){
                continue;
            }
            for (FileLoanScenesProofMaterialVO materialVO : materialVOList) {
                if (orderMaterialConfig.getEleReceiveMaterialNo().equals(materialVO.getMaterialNo()) || orderMaterialConfig.getAgricDealerMaterialNo().equals(materialVO.getMaterialNo())){
                    log.info("dealEleSign,materialVO:{}",materialVO);
                    List<String> contentList = Lists.newArrayList();
                    Map<String,String> map = Maps.newHashMap();
                    List<OrderTradeProofPO> proofPOS = proofMap.get(proofVO.getSceneNo());
                    if (!CollectionUtils.isEmpty(proofPOS)){
                        List<OrderTradeProofPO> eleSignProofPoList = proofPOS.stream().filter(po -> orderMaterialConfig.getEleReceiveMaterialNo().equals(po.getMaterialNo()) || orderMaterialConfig.getAgricDealerMaterialNo().equals(po.getMaterialNo())).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(eleSignProofPoList)){
                            for (OrderTradeProofPO proofPO : eleSignProofPoList) {
                                if (Objects.isNull(proofPO) || StringUtils.isBlank(proofPO.getContractNo())){
                                    continue;
                                }
                                ContractFindRequest findRequest = new ContractFindRequest();
                                findRequest.setContractNo(proofPO.getContractNo());
                                findRequest.setContractExpireHour(1L);
                                List<ContractFindResponse> contractResponse = ExternalApiUtil.callResultApi(() -> contractFacade.findAllContract(findRequest), findRequest, "/contract/common/findAllContract", "合同查询");
                                if (!CollectionUtils.isEmpty(contractResponse)){
                                    ContractFindResponse contractFindResponse = contractResponse.get(0);
                                    String viewUrl = contractFindResponse.getViewURL();
                                    String downloadURL = contractFindResponse.getDownloadURL();
                                    contentList.add(viewUrl);
                                    map.put(viewUrl,downloadURL);
                                }
                                materialVO.setContractNo(proofPO.getContractNo());
                                proofVO.setContractNo(proofPO.getContractNo());
                            }
                        }else{
                            log.info("dealEleSign,materialVO:{}，未找到对应的电签资料",materialVO);
                        }
                    }else{
                        log.info("dealEleSign,materialVO:{},proofPos为空",materialVO);
                    }
                    materialVO.setMaterialContentList(contentList);
                    materialVO.setDownUrlMap(map);
                    log.info("dealEleSign,materialVO:{}，contexntList:{},downUrlMap:{}",materialVO,contentList,map);
                }
            }
        }
        return loanDetailVO;
    }

    /**
     * 将老的订单详情，转换为新的订单详情
     *
     * @param orderDetailVO 老的订单详情
     * @return 新的订单详情
     */
    private OrderLoanDetailVO copyOrderDetail(OrderDetailVO orderDetailVO){
        return new OrderLoanDetailVO(orderDetailVO);
    }


    private OrderDetailVO buildOrderDetailVO(String orderNo,
                                             BigDecimal amount,
                                             String storeName,
                                             String orderSn,
                                             String ruleTag,
                                             String loanPayer,
                                             OrderExtendPO extendPO,
                                             OrderExtendFinancePO orderExtendFinancePO, List<FileScenesProofVO> fileScenesProofVOS) {
        OrderDetailVO orderVO = new OrderDetailVO();
        orderVO.setOrderSn(orderNo);
        // 查询商品信息
        List<OrderProductPO> productPOS = this.lambdaQuery().eq(OrderProductPO::getOrderSn, orderSn).list();
        if (CollectionUtils.isEmpty(productPOS)) {
            log.error("用呗复核查询订单商品信息时，标准电商商品信息为空，请处理！" + orderSn);
            throw new MallException("订单商品信息为空，请联系管理员");
        }
        orderVO.setStoreName(storeName);
        List<ProductsVO> productsVOList = productPOS.stream().map(x -> {
            ProductsVO productsVO = new ProductsVO();
            productsVO.setName(x.getGoodsName());
            productsVO.setSellingPrice(x.getProductShowPrice());
            productsVO.setQty(x.getProductNum());
            return productsVO;
        }).collect(Collectors.toList());
        orderVO.setProductsVOS(productsVOList);
        orderVO.setOrderAmount(amount);
        orderVO.setReceiverAddress(extendPO.getReceiverAddress());
        orderVO.setReceiverName(extendPO.getReceiverName());
        orderVO.setReceiverMobile(extendPO.getReceiverMobile());
        orderVO.setFinanceRuleCode(Objects.nonNull(orderExtendFinancePO) ? orderExtendFinancePO.getFinanceRuleCode() : "");
        orderVO.setFinanceRuleName(ruleTag);
        orderVO.setFileScenesProofVOS(fileScenesProofVOS);
        orderVO.setLoanPayer(loanPayer);
        orderVO.setEleProofUploadStatus(NumberUtils.INTEGER_ZERO);
        if (ReceiveConfirmDocType.ELE_SIGN.getCode().equals(extendPO.getReceiveConfirmDocType()) && NumberUtils.INTEGER_ONE.equals(extendPO.getReceiveMaterialStatus())){
            // 电签且已签收
            orderVO.setEleProofUploadStatus(NumberUtils.INTEGER_ONE);
        }
        orderVO.setReceiveDocStatus(extendPO.getReceiveMaterialStatus());
        return orderVO;
    }

    @Override
    public OrderProductPO selectOneByOrderProductId(Long orderProductId) {
        if (ObjectUtils.isEmpty(orderProductId)) {
            return null;
        }
        return super.getById(orderProductId);
    }

    @Override
    public void updateProductReturnNumAfterCancel(String orderSn) {
        LambdaUpdateWrapper<OrderProductPO> productUpdateWrapper = Wrappers.lambdaUpdate();
        productUpdateWrapper.eq(OrderProductPO::getOrderSn, orderSn)
                .setSql("return_number = product_num");
        super.update(productUpdateWrapper);
    }

    @Override
    public List<AfsOrderProductVOV2> getAfsOrderProductList(String orderSn) {
        // 查询订单信息
        OrderPO order = iOrderService.getByOrderSn(orderSn);
        BizAssertUtil.notNull(order, String.format("查询订单%s记录为空，请确认数据正确性~", orderSn));

        List<OrderProductPO> orderProductPOs = this.listByOrderSn(orderSn);
        BizAssertUtil.notEmpty(orderProductPOs, String.format("查询订单%s商品信息为空，请确认数据正确性~", orderSn));

        List<AfsOrderProductVOV2> vos = new ArrayList<>(orderProductPOs.size());
        for (OrderProductPO orderProductPO : orderProductPOs) {
            AfsOrderProductVOV2 vo = new AfsOrderProductVOV2(orderProductPO);
            if (order.getOrderType() != null && OrderTypeEnum.PRE_SELL_DEPOSIT == OrderTypeEnum.valueOf(order.getOrderType())) {
                vo.setMoneyAmount(frontAfterSaleApplyService.getDepositProductRefundMoney(orderSn, orderProductPO.getMoneyAmount()));
            }
            vos.add(vo);

            // 统计商品已退、退款中数量
            if (orderProductPO.getReturnNumber() > 0) {
                List<OrderReturnVOV2> orderReturnVOList = orderReturnService
                        .listOrderReturnByProductId(orderProductPO.getOrderProductId());
                Integer alreadyReturnNum = 0;
                Integer returningNum = 0;
                for (OrderReturnVOV2 returnVO : orderReturnVOList) {
                    if (OrderReturnStatus.isFinish(returnVO.getState())) {
                        alreadyReturnNum += returnVO.getReturnNum();
                    }
                    if (OrderReturnStatus.duringRefund(returnVO.getState())) {
                        returningNum += returnVO.getReturnNum();
                    }
                }
                vo.setAlreadyReturnNum(alreadyReturnNum);
                vo.setReturningNum(returningNum);
            }
        }

        return vos;
    }

    @Override
    public List<OrderProductInfoVO> getOrderProductInfoVOList(OrderProductExample example) {
        List<OrderProductPO> orderProductPOs = orderProductModel.getOrderProductList(example, example.getPager());
        List<OrderProductInfoVO> orderProductVOs = new ArrayList<>();
        if (CollUtil.isNotEmpty(orderProductPOs)) {
            for (OrderProductPO orderProductPO : orderProductPOs) {
                OrderProductInfoVO orderProductVO = new OrderProductInfoVO();
                orderProductVO.setOrderProductId(orderProductPO.getOrderProductId());
                orderProductVO.setOrderSn(orderProductPO.getOrderSn());
                orderProductVO.setProductId(orderProductPO.getProductId());
                orderProductVO.setDeliveryState(orderProductPO.getDeliveryState().getValue());
                orderProductVO.setPerformanceMode(orderProductPO.getPerformanceMode());
                orderProductVO.setPerformanceChannel(orderProductPO.getPerformanceChannel());
                orderProductVO.setPerformanceService(orderProductPO.getPerformanceService());
                orderProductVOs.add(orderProductVO);
            }
        }
        return orderProductVOs;
    }

    @Override
    public List<OrderProductRebateVO> queryByRebateProduct(OrderProductRebateDTO dto) {
        return orderProductMapper.queryByRebateProduct(dto);
    }

    @Override
    public List<OrderProductRebateVO> queryBuyCommodityRebateProduct(BuyCommodityRebateProductDTO dto) {
        return orderProductMapper.queryBuyCommodityRebateProduct(dto);
    }

    @Override
    public List<OrderProductRebateVO> queryBuyWineGift(BuyWineRebateProductDTO dto) {
        return orderProductMapper.queryBuyWineGift(dto);
    }

    /**
     * 同步销项税率
     */
    @Override
    public void syncTaxRate() {
        LambdaQueryWrapper<OrderProductPO> orderProductQuery = Wrappers.lambdaQuery(OrderProductPO.class);
        orderProductQuery.isNull(OrderProductPO::getTaxRate);
        orderProductQuery.isNotNull(OrderProductPO::getSkuMaterialCode);
        List<OrderProductPO> orderProductList = list(orderProductQuery);
        List<List<OrderProductPO>> partitionList = Lists.partition(orderProductList, 1000);
        List<OrderProductPO> needUpdate = Lists.newArrayList();
        for (List<OrderProductPO> singleList : partitionList) {
            try {
                List<String> materialCode = singleList.stream().map(OrderProductPO::getSkuMaterialCode).collect(Collectors.toList());
                MallProductQuery query = new MallProductQuery();
                query.setSkuMaterialCodes(materialCode);
                List<ProductVO> productVoList = erpIntegration.getProductInfoList(query);
                Map<String, ProductVO> productVOMap = productVoList.stream().collect(Collectors.toMap(ProductVO::getProductNo, p -> p, (k1, k2) -> k1));
                for (OrderProductPO orderProductPO : singleList) {
                    String code = orderProductPO.getSkuMaterialCode();
                    if (productVOMap.containsKey(code)){
                        ProductVO productVO = productVOMap.get(code);
                        orderProductPO.setTaxRate(productVO.getOutTax());
                        orderProductPO.setUpdateTime(new Date());
                        orderProductPO.setUpdateBy("syncTaxRate");
                        needUpdate.add(orderProductPO);
                    }
                }
            } catch (Exception exp) {
                log.info("执行更新历史销项税率失败，exp:{}",exp.getMessage());
            }
        }
        if (!CollectionUtils.isEmpty(needUpdate)){
            updateBatchById(needUpdate);
        }
    }

    /**
     * 历史skuid 处理
     *
     * @param param 参数，可以选定order productId进行更新，如果没有设置，则默认更新全量数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "masterdbTx")
    public void dealHistorySkuId(String param) {
        // 需要转换的skuId列表
        List<String> channelSkuIdList = Lists.newArrayList();
        List<String> orderProductIdList = Lists.newArrayList();
        // 查询orderProdcut中需要更新的数据
        if (StringUtils.isNotBlank(param)){
            // 指定了orderProductId，那么就只查对应的数据
            orderProductIdList = Arrays.asList(param.split(","));
            LambdaQueryWrapper<OrderProductPO> orderProductPoQuery = Wrappers.lambdaQuery(OrderProductPO.class);
            orderProductPoQuery.in(OrderProductPO::getOrderProductId,orderProductIdList);
            List<OrderProductPO> orderProductList = list(orderProductPoQuery);
            channelSkuIdList = orderProductList.stream().map(OrderProductPO::getChannelSkuId).distinct().collect(Collectors.toList());
        }else{
            channelSkuIdList = orderProductMapper.getDistinctChannelSkuIdStartByEight();
        }
        if (CollectionUtils.isEmpty(channelSkuIdList)){
            // 如果需要转换的skuId列表为空，则不进行处理了
            log.info("dealHistorySkuId channelSkuIdList is empty!,param:{}",param);
            return;
        }
        // 分片，避免将erp查挂了,单次查询300条
        List<List<String>> partitions = Lists.partition(channelSkuIdList, 300);
        // 同步执行，不进行异步处理，避免同时处理条数峰值过高
        for (List<String> partition : partitions) {
            List<ProductSkuUnitVO> productVoList = erpIntegration.getListBySkuUnitCode(partition);
            if (CollectionUtils.isEmpty(productVoList)){
                // 在erp中没有查到对应的数据，不进行处理
                log.info("dealHistorySkuId productVoList is empty!,param:{}",param);
                continue;
            }
            // 按照skuId分组更新
            Map<String, String> mappingMap = productVoList.stream().collect(Collectors.toMap(ProductSkuUnitVO::getSkuUnitCode, ProductSkuUnitVO::getSkuId));
            List<String> finalOrderProductIdList = orderProductIdList;
            mappingMap.forEach((oldSkuId, newSkuId)->{
                // 因为每一个skuId对应的值都不一样，所以只能单个进行更新
                LambdaUpdateWrapper<OrderProductPO> productUpdate = Wrappers.lambdaUpdate(OrderProductPO.class);
                productUpdate.in(!CollectionUtils.isEmpty(finalOrderProductIdList),OrderProductPO::getOrderProductId, finalOrderProductIdList);
                productUpdate.eq(OrderProductPO::getChannelSkuId,oldSkuId);
                // 在没有指定orderProductId时，只更新为空的数据
                productUpdate.isNull(CollectionUtils.isEmpty(finalOrderProductIdList),OrderProductPO::getChannelNewSkuId);
                productUpdate.set(OrderProductPO::getChannelNewSkuId,newSkuId);
                update(productUpdate);
            });
        }
        log.info("dealHistorySkuId end,param:{}",param);
    }

    @Override
    public Boolean updateProductDeliveryState(ProductDeliveryDTO dto) {
        OrderProductDeliveryEnum deliverPackageStateEnum = OrderProductDeliveryEnum.valueOf(dto.getDeliveryState());

        BizAssertUtil.notNull(deliverPackageStateEnum, "该发货状态不存在，请检查");

        OrderPO orderPO = orderModel.getOrderByOrderSn(dto.getOrderSn());
        BizAssertUtil.notNull(orderPO, "订单不存在");



        BizAssertUtil.isTrue(!orderPO.getOrderState().equals(OrderStatusEnum.WAIT_DELIVER.getValue())
                , "该订单未处于待发货状态，不能修改该状态");
        LambdaUpdateWrapper<OrderProductPO> updateWrapper = Wrappers.lambdaUpdate(OrderProductPO.class);
        updateWrapper.set(OrderProductPO::getDeliveryState, deliverPackageStateEnum.getValue())
                .eq(OrderProductPO::getOrderSn, dto.getOrderSn())
                .in(OrderProductPO::getProductId, dto.getProductIdList());

        this.update(updateWrapper);

        return Boolean.TRUE;
    }

    @Override
    public List<OrderProductRebateVO> tjBuyNzRebateGift(TjBuyNzRebateProductDTO dto) {
        return orderProductMapper.tjBuyNzRebateGift(dto);
    }

    @Override
    public OrderAddressDTO queryUserAddress(String userNo, Long skuId) {
        return orderProductMapper.queryUserAddress(userNo,skuId);
    }

    /**
     * 查询订单商品列表
     *
     * @param list 查询dto列表
     * @return 订单编号列表
     */
    @Override
    public List<HistoryDealOrderEventNotifyDTO> queryOrderSnByProductIdAndFinanceRule(List<AgricQueryOrderDTO> list) {
        if (CollectionUtils.isEmpty(list)){
            return Lists.newArrayList();
        }
        list.removeIf(dto -> Objects.isNull(dto.getProductId()));
        if (CollectionUtils.isEmpty(list)){
            return Lists.newArrayList();
        }
        List<AgricQueryOrderDTO> with = list.stream().filter(dto -> StringUtils.isNotBlank(dto.getFinanceRuleCode())).collect(Collectors.toList());
        List<AgricQueryOrderDTO> without = list.stream().filter(dto -> StringUtils.isBlank(dto.getFinanceRuleCode())).collect(Collectors.toList());
        List<OrderProductPO> result = orderProductMapper.queryOrderSnByProductIdAndFinanceRule(with,without);
        if (CollectionUtils.isEmpty(result)){
            return Lists.newArrayList();
        }
        List<String> orderSnList = result.stream().map(OrderProductPO::getOrderSn).collect(Collectors.toList());
        LambdaQueryWrapper<OrderPO> orderQuery = Wrappers.lambdaQuery(OrderPO.class);
        orderQuery.in(OrderPO::getOrderSn,orderSnList);
        List<OrderPO> orderList = iOrderService.list(orderQuery);
        Map<String, OrderPO> orderMap = orderList.stream().collect(Collectors.toMap(OrderPO::getOrderSn, Function.identity(), (o1, o2) -> o1));
        LambdaQueryWrapper<OrderExtendPO> orderExtendQuery = Wrappers.lambdaQuery(OrderExtendPO.class);
        orderExtendQuery.in(OrderExtendPO::getOrderSn,orderSnList);
        List<OrderExtendPO> orderExtendList = iOrderExtendService.list(orderExtendQuery);
        Map<String, OrderExtendPO> extendPOMap = orderExtendList.stream().collect(Collectors.toMap(OrderExtendPO::getOrderSn, Function.identity(), (e1, e2) -> e1));
        List<HistoryDealOrderEventNotifyDTO> orderEventNotifyDTOList = Lists.newArrayList();
        for (OrderProductPO orderProductPO : result) {
            String orderSn = orderProductPO.getOrderSn();
            OrderPO order = orderMap.get(orderSn);
            OrderExtendPO orderExtendPO = extendPOMap.get(orderSn);
            if (Objects.isNull(order) || Objects.isNull(orderExtendPO)){
                log.error("queryOrderSnByProductIdAndFinanceRule 结果异常,orderSn:{}查询不到对应的订单和订单拓展表信息",orderSn);
                continue;
            }
            Integer orderState = order.getOrderState();
            if (!OrderStatusEnum.isPaid(orderState)){
                log.info("queryOrderSnByProductIdAndFinanceRule orderSn:{}订单不进行处理，未支付成功",orderSn);
                continue;
            }
            HistoryDealOrderEventNotifyDTO orderEventNotifyDTO = new HistoryDealOrderEventNotifyDTO();
            orderEventNotifyDTO.setPaySn(order.getPaySn());
            orderEventNotifyDTO.setOrderSn(order.getOrderSn());
            orderEventNotifyDTO.setUserNo(order.getUserNo());
            orderEventNotifyDTO.setStoreId(order.getStoreId());
            if (OrderConst.STORE_TYPE_SELF_1.equals(orderExtendPO.getJindieTransFlag())) {
                orderEventNotifyDTO.setIsSelf(OrderConst.SEND_JINDIE_YES_1);
            }
            orderEventNotifyDTO.setEventType(OrderEventEnum.PAY.getCode());
            orderEventNotifyDTO.setEventTime(order.getPayTime());
            orderEventNotifyDTO.setEventTypeDesc(OrderEventEnum.PAY.getDesc());
            orderEventNotifyDTO.setChannel(order.getChannel());
            orderEventNotifyDTO.setOrderPattern(order.getOrderPattern());
            orderEventNotifyDTO.setOrderType(order.getOrderType());
            orderEventNotifyDTO.setProductId(orderProductPO.getProductId());
            orderEventNotifyDTO.setFinanceRuleCode(orderProductPO.getFinanceRuleCode());
            orderEventNotifyDTOList.add(orderEventNotifyDTO);
        }
        return orderEventNotifyDTOList;
    }

    /**
     * 根据订单编号查询订单信息
     *
     * @param orderQueryDTO 查询dto
     * @return 订单信息
     */
    @Override
    public List<HistoryDealOrderEventNotifyDTO> queryOrderInfoByOrderIdList(DbcOrderQueryDTO orderQueryDTO) {
        log.info("queryOrderInfoByOrderIdList orderQueryDTO:{}", JSON.toJSONString(orderQueryDTO));
        List<String> dtoOrderSnList = orderQueryDTO.getOrderSnList();
        String startPayDate = orderQueryDTO.getStartDate() == null ? null: orderQueryDTO.getStartDate();
        if (CollectionUtils.isEmpty(dtoOrderSnList)){
            return Lists.newArrayList();
        }
        List<OrderProductPO> result = orderProductMapper.queryOrderInfoByOrderIdList(dtoOrderSnList,startPayDate);
        if (CollectionUtils.isEmpty(result)){
            return Lists.newArrayList();
        }
        List<String> orderSnList = result.stream().map(OrderProductPO::getOrderSn).collect(Collectors.toList());
        LambdaQueryWrapper<OrderPO> orderQuery = Wrappers.lambdaQuery(OrderPO.class);
        orderQuery.in(OrderPO::getOrderSn,orderSnList);
        List<OrderPO> orderList = iOrderService.list(orderQuery);
        Map<String, OrderPO> orderMap = orderList.stream().collect(Collectors.toMap(OrderPO::getOrderSn, Function.identity(), (o1, o2) -> o1));
        LambdaQueryWrapper<OrderExtendPO> orderExtendQuery = Wrappers.lambdaQuery(OrderExtendPO.class);
        orderExtendQuery.in(OrderExtendPO::getOrderSn,orderSnList);
        List<OrderExtendPO> orderExtendList = iOrderExtendService.list(orderExtendQuery);
        Map<String, OrderExtendPO> extendPOMap = orderExtendList.stream().collect(Collectors.toMap(OrderExtendPO::getOrderSn, Function.identity(), (e1, e2) -> e1));
        List<HistoryDealOrderEventNotifyDTO> orderEventNotifyDTOList = Lists.newArrayList();
        for (OrderProductPO orderProductPO : result) {
            String orderSn = orderProductPO.getOrderSn();
            OrderPO order = orderMap.get(orderSn);
            OrderExtendPO orderExtendPO = extendPOMap.get(orderSn);
            if (Objects.isNull(order) || Objects.isNull(orderExtendPO)){
                log.error("queryOrderSnByProductIdAndFinanceRule 结果异常,orderSn:{}查询不到对应的订单和订单拓展表信息",orderSn);
                continue;
            }
            Integer orderState = order.getOrderState();
            if (!OrderStatusEnum.isPaid(orderState)){
                log.info("queryOrderSnByProductIdAndFinanceRule orderSn:{}订单不进行处理，未支付成功",orderSn);
                continue;
            }
            HistoryDealOrderEventNotifyDTO orderEventNotifyDTO = new HistoryDealOrderEventNotifyDTO();
            orderEventNotifyDTO.setPaySn(order.getPaySn());
            orderEventNotifyDTO.setOrderSn(order.getOrderSn());
            orderEventNotifyDTO.setUserNo(order.getUserNo());
            orderEventNotifyDTO.setStoreId(order.getStoreId());
            if (OrderConst.STORE_TYPE_SELF_1.equals(orderExtendPO.getJindieTransFlag())) {
                orderEventNotifyDTO.setIsSelf(OrderConst.SEND_JINDIE_YES_1);
            }
            orderEventNotifyDTO.setEventType(OrderEventEnum.PAY.getCode());
            orderEventNotifyDTO.setEventTime(order.getPayTime());
            orderEventNotifyDTO.setEventTypeDesc(OrderEventEnum.PAY.getDesc());
            orderEventNotifyDTO.setChannel(order.getChannel());
            orderEventNotifyDTO.setOrderPattern(order.getOrderPattern());
            orderEventNotifyDTO.setOrderType(order.getOrderType());
            orderEventNotifyDTO.setProductId(orderProductPO.getProductId());
            orderEventNotifyDTO.setFinanceRuleCode(orderProductPO.getFinanceRuleCode());
            orderEventNotifyDTOList.add(orderEventNotifyDTO);
        }
        log.info("queryOrderInfoByOrderIdList response orderEventNotifyDTOList.size:{}", orderEventNotifyDTOList.size());
        return orderEventNotifyDTOList;
    }
}
