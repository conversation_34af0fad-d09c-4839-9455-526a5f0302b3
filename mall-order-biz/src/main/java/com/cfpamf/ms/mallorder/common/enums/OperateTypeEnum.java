package com.cfpamf.ms.mallorder.common.enums;

import com.cfpamf.framework.autoconfigure.common.exception.MSException;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;

import java.util.Arrays;

public enum OperateTypeEnum {

    INSERT("insert", "新增"),
    DELETE("delete", "删除"),
    UPDATE("update", "修改"),
    SELECT("select", "查询");

    private final String value;
    private final String desc;

    OperateTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static OperateTypeEnum parseEnum(String value){
        return Arrays.stream(OperateTypeEnum.values()).filter(x -> x.getValue().equals(value)).findFirst()
                .orElseThrow(()->new MSException(String.valueOf(ErrorCodeEnum.S.DATA_NOT_FOUND.getCode()),String.format("操作类型value转换成enum失败,value:%s",value)));
    }
}
