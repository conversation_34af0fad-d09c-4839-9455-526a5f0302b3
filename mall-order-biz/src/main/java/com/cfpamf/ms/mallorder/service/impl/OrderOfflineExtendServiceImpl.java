package com.cfpamf.ms.mallorder.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cfpamf.framework.autoconfigure.mybatis.BaseRepoServiceImpl;
import com.cfpamf.ms.mallorder.common.enums.CustomerTypeEnum;
import com.cfpamf.ms.mallorder.common.util.DateUtil;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.OrderOfflineInfoDTO;
import com.cfpamf.ms.mallorder.dto.OrderOfflineParamDTO;
import com.cfpamf.ms.mallorder.mapper.OrderOfflineExtendMapper;
import com.cfpamf.ms.mallorder.po.OrderOfflineExtendPO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.service.IOrderOfflineExtendService;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class OrderOfflineExtendServiceImpl
        extends BaseRepoServiceImpl<OrderOfflineExtendMapper, OrderOfflineExtendPO>
        implements IOrderOfflineExtendService {

    @Override
    public void saveOfflineOrder(List<OrderPO> orderPOList, OrderOfflineParamDTO offlineParamDTO, String operator) {
        if (CollectionUtils.isEmpty(orderPOList)) {
            return;
        }
        OrderOfflineInfoDTO offlineInfoDTO = offlineParamDTO.getOfflineInfoDTO();
        List<OrderOfflineExtendPO> orderOfflineExtendPOList = new ArrayList<>();
        for(OrderPO orderPO : orderPOList) {
            OrderOfflineExtendPO orderOfflineExtendPO = new OrderOfflineExtendPO();
            orderOfflineExtendPO.setOrderSn(orderPO.getOrderSn());
            orderOfflineExtendPO.setAccountPeriodDays(offlineInfoDTO.getAccountPeriodDays());
            if(StringUtils.isNotEmpty(offlineInfoDTO.getBuyerSupplierCode())) {
                orderOfflineExtendPO.setBuyerSupplierCode(offlineInfoDTO.getBuyerSupplierCode());
                orderOfflineExtendPO.setBuyerSupplierName(offlineInfoDTO.getBuyerSupplierName());
            }
            if(!Objects.isNull(offlineInfoDTO.getOverdueTime())) {
                orderOfflineExtendPO.setOverdueTime(offlineInfoDTO.getOverdueTime());
                orderOfflineExtendPO.setOverdueFlag(OrderConst.OVERDUE_FLAG_0);
                if(DateUtil.isEndDateLessBeginDateByDay(new Date(), offlineInfoDTO.getOverdueTime())) {
                    orderOfflineExtendPO.setOverdueFlag(OrderConst.OVERDUE_FLAG_1);
                    int days = DateUtil.days(DateUtil.format(offlineInfoDTO.getOverdueTime(), DateUtil.FORMAT_DATE),
                            DateUtil.format(new Date(), DateUtil.FORMAT_DATE), DateUtil.FORMAT_DATE);
                    orderOfflineExtendPO.setOverdueDays(days);
                }

            }
            orderOfflineExtendPO.setCreateBy(operator);

            orderOfflineExtendPO.setCustomerType(offlineParamDTO.getCustomerType());
            if (Objects.equals(CustomerTypeEnum.ENTERPRISE_CUSTOMER.getCode(), offlineParamDTO.getCustomerType())
                    && Objects.nonNull(offlineParamDTO.getOrderOfflineCompanyDTO())) {
                // 企业用户补录订单 B端客户信息保存
                orderOfflineExtendPO.setCustomerCode(offlineParamDTO.getOrderOfflineCompanyDTO().getCustomerCode());
                orderOfflineExtendPO.setCustomerName(offlineParamDTO.getOrderOfflineCompanyDTO().getCustomerName());
            }
            orderOfflineExtendPOList.add(orderOfflineExtendPO);
        }
        super.saveBatch(orderOfflineExtendPOList);
    }

    @Override
    public OrderOfflineExtendPO getOrderOfflineExtendByOrderSn(String orderSn) {
        LambdaQueryWrapper<OrderOfflineExtendPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(OrderOfflineExtendPO::getOrderSn, orderSn);
        return this.getOne(lambdaQueryWrapper);
    }

    @Override
    public Map<String, OrderOfflineExtendPO> getOrderOfflineExtendMapByOrderSnList(List<String> bizSnList) {
        if (CollectionUtils.isEmpty(bizSnList)) {
            return Maps.newHashMap();
        }
        LambdaQueryWrapper<OrderOfflineExtendPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(OrderOfflineExtendPO::getOrderSn, bizSnList);
        return this.list(lambdaQueryWrapper).stream().collect(Collectors.toMap(OrderOfflineExtendPO::getOrderSn, item -> item, (v1, v2) -> (v2)));
    }

}
