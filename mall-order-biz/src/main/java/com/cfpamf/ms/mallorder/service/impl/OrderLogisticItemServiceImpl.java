package com.cfpamf.ms.mallorder.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cfpamf.framework.autoconfigure.mybatis.BaseRepoServiceImpl;
import com.cfpamf.ms.mallorder.dto.OrderProductDeliverDTO;
import com.cfpamf.ms.mallorder.mapper.OrderLogisticItemMapper;
import com.cfpamf.ms.mallorder.po.OrderLogisticItemPO;
import com.cfpamf.ms.mallorder.po.OrderProductPO;
import com.cfpamf.ms.mallorder.service.IOrderLogisticItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class OrderLogisticItemServiceImpl extends BaseRepoServiceImpl<OrderLogisticItemMapper, OrderLogisticItemPO> implements IOrderLogisticItemService {

    @Override
    public void batchSave(String packageSn, List<OrderProductPO> orderProductPOList, Map<Long, OrderProductDeliverDTO> orderProductDeliverDTOMap,String operator) {
        List<OrderLogisticItemPO> logisticItemPOList = new ArrayList<>();
        for(OrderProductPO orderProductPO : orderProductPOList) {
            OrderLogisticItemPO orderLogisticItemPO = new OrderLogisticItemPO();
            orderLogisticItemPO.setOrderProductId(orderProductPO.getOrderProductId());
            orderLogisticItemPO.setPackageSn(packageSn);
            orderLogisticItemPO.setGoodsName(orderProductPO.getGoodsName());
            orderLogisticItemPO.setProductImage(orderProductPO.getProductImage());
            int deliveryNum = orderProductDeliverDTOMap.get(orderProductPO.getOrderProductId()).getDeliveryNum();
            orderLogisticItemPO.setDeliveryNum(deliveryNum);
            orderLogisticItemPO.setSpecValues(orderProductPO.getSpecValues());
            orderLogisticItemPO.setProductNum(orderProductPO.getProductNum());
            orderLogisticItemPO.setCreateBy(operator);
            orderLogisticItemPO.setUpdateBy(operator);
            logisticItemPOList.add(orderLogisticItemPO);
        }
        this.saveBatch(logisticItemPOList);
    }


    @Override
    public List<OrderLogisticItemPO> getOrderLogisticItemListByPackageSn(String packageSn) {
        LambdaQueryWrapper<OrderLogisticItemPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(OrderLogisticItemPO::getPackageSn, packageSn);
        return this.list(lambdaQueryWrapper);
    }
    @Override
    public List<OrderLogisticItemPO> getOrderLogisticItemListByOrderProductId(Long orderProductId) {
        LambdaQueryWrapper<OrderLogisticItemPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(OrderLogisticItemPO::getOrderProductId, orderProductId);
        return this.list(lambdaQueryWrapper);
    }

}
