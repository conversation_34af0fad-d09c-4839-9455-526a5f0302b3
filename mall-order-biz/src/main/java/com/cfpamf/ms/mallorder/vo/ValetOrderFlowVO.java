package com.cfpamf.ms.mallorder.vo;

import com.cfpamf.ms.mallorder.common.config.RuleEngineConfig;
import com.cfpamf.ms.mallorder.common.enums.CommonEnum;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "ValetOrderFlowVO对象",description = "代客下单配置返回结果类")
public class ValetOrderFlowVO implements Serializable {

    private static final long serialVersionUID = -2234155632832L;

    @ApiModelProperty("贷款确认方式：READ-完成订单阅读确认(客户端);FACE_DETECTION-完成订单人脸识别（站长端）")
    private String confirmMethodCode = OrderConst.LOAN_CONFIRM_METHOD_READ; // 默认阅读确认支付

    @ApiModelProperty("允许代下单站长贷款代付 0-不允许; 1-允许")
    private Integer allowPlaceUserLoan = 0; // 默认不允许站长贷款代付

    @ApiModelProperty("代下单客户确认无需刷脸 0-无需刷脸; 1-需要刷脸")
    private Integer allowConfirmNoFace = 1; // 默认需要刷脸

}
