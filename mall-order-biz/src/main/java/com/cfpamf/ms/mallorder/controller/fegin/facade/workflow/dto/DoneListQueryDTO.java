package com.cfpamf.ms.mallorder.controller.fegin.facade.workflow.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * 完成列表查询dto
 *
 * <AUTHOR>
 */
@Data
@ToString
public class DoneListQueryDTO {
    @ApiModelProperty("当前用户ID")
    @NotNull(message = "当前用户ID不能为空")
    private String userId;
    @ApiModelProperty("当前用户ID类型：JOB_NO, BMS_USER_ID, DING_USER_ID， OTHERS")
    @NotNull(message = "用户ID类型不能为空")
    private String userIdType;
    @ApiModelProperty("流程定义的key")
    private String processDefKey;
    @ApiModelProperty("当前页")
    private Integer pageNumber = 1;
    private Integer pageSize = 10;
}
