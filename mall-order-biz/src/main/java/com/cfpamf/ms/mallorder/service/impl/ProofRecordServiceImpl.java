package com.cfpamf.ms.mallorder.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cfpamf.ms.mallorder.mapper.ProofRecordMapper;
import com.cfpamf.ms.mallorder.po.ProofRecordPO;
import com.cfpamf.ms.mallorder.service.IProofRecordService;
import com.cfpamf.ms.mallorder.service.IProofService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class ProofRecordServiceImpl extends ServiceImpl<ProofRecordMapper, ProofRecordPO> implements IProofRecordService {


    @Resource
    private ProofRecordMapper proofRecordMapper;

    @Override
    public List<ProofRecordPO> getProofRecords(String proofDefinitionCode, String proofValue) {
        return proofRecordMapper.getProofRecords(proofDefinitionCode, proofValue);
    }

    @Override
    public ProofRecordPO getByKeyValue(String proofKey, String proofValue, String proofDefinitionCode) {
        return proofRecordMapper.getByKeyValue(proofDefinitionCode, proofKey, proofValue);
    }
}
