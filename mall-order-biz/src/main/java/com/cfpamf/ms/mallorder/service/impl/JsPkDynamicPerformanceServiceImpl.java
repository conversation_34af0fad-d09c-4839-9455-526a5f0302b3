package com.cfpamf.ms.mallorder.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.mallorder.common.enums.PkTypeEnum;
import com.cfpamf.ms.mallorder.common.mq.RabbitMQUtils;
import com.cfpamf.ms.mallorder.common.mq.RabbitMqConfig;
import com.cfpamf.ms.mallorder.pgMapper.JsPkDynamicPerformanceMapper;
import com.cfpamf.ms.mallorder.po.JsPkDynamicPerformancePO;
import com.cfpamf.ms.mallorder.service.ICommonMqEventService;
import com.cfpamf.ms.mallorder.service.IJsPkDynamicPerformanceService;
import com.cfpamf.ms.mallorder.vo.RealIndicatorsMqVO;
import com.cfpamf.ms.mallorder.vo.pgrpt.IndicatorsVO;
import com.slodon.bbc.core.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-23
 */

@Service
@Slf4j
public class JsPkDynamicPerformanceServiceImpl implements IJsPkDynamicPerformanceService {
    @Resource
    private JsPkDynamicPerformanceMapper jsPkDynamicPerformanceMapper;
    @Resource
    private ICommonMqEventService commonMqEventService;
    @Resource
    private RabbitMQUtils rabbitMQUtils;

    @Override
    public void queryList(Date rptDateStart, Date rptDateEnd) {
        List<JsPkDynamicPerformancePO> list =  jsPkDynamicPerformanceMapper.listByQuery(rptDateStart,rptDateEnd);
        //AssertUtil.notEmpty(list, "[JsPkDynamicPerformanceJob] 定时任务酒水PK实时业绩推送执行失败-未查询到相关数据");
        if (CollectionUtil.isEmpty(list)){
            return;
        }
        //推送到MQ
        list.stream().forEach(e -> {
            RealIndicatorsMqVO realIndicatorsMqVO = RealIndicatorsMqVO.builder()
                    .userName(e.getRecommendUserName())
                    .userCode(e.getRecommendUserId())
                    .branchCode(e.getBchCode())
                    .branchName(e.getBchName())
                    .indicatorsVO(IndicatorsVO.builder()
                            .indicatorTypeList(PkTypeEnum.getCodeList())
                            .type(PkTypeEnum.VALID_SALE_AMT.getCode())
                            .typeName(PkTypeEnum.VALID_SALE_AMT.getDesc())
                            .value(e.getWineValidSaleAmt())
                            .unit(PkTypeEnum.VALID_SALE_AMT.getUnit())
                            .build())
                    .time(e.getPayTime().toLocalDateTime())
                    .idempotent(e.getMallOrderNo())
                    .build();
            this.sendMessage(realIndicatorsMqVO);
        });
    }


    /**
     * 推MQ
     */
    public void sendMessage(RealIndicatorsMqVO realIndicatorsMqVO){
        Long eventId = commonMqEventService.saveEvent(JSON.toJSON(realIndicatorsMqVO), RabbitMqConfig.SCRM_QUEUE_PK_LIQUOR_MESSAGE_KEY,
                RabbitMqConfig.SCRM_EXCHANGE_PK_BUSINESS_MESSAGE_EXCHANGE_NAME);
        log.info("酒水PK推送消息: {}", JSON.toJSON(realIndicatorsMqVO));
        rabbitMQUtils.sendByEventId(eventId);
    }
}
