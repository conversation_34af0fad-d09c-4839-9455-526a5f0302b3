package com.cfpamf.ms.mallorder.controller.seller;


import com.cfpamf.ms.mall.filecenter.domain.dto.FileScenesMaterialProofDTO;
import com.cfpamf.ms.mall.filecenter.domain.vo.FileScenesProofVO;
import com.cfpamf.ms.mallorder.common.util.ValidUtils;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.OrderTradeProofQueryDTO;
import com.cfpamf.ms.mallorder.service.IOrderTradeProofService;
import com.cfpamf.ms.mallorder.vo.OrderTradeProofVO;
import com.cfpamf.ms.mallshop.resp.Vendor;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import com.slodon.bbc.core.util.UserUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 订单交易凭证表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-12
 */
@RestController
@Api(tags = "seller-订单交易凭证操作")
@RequestMapping("seller/orderTradeProof")
public class SellerOrderTradeProofController {

	@Autowired
	private IOrderTradeProofService orderTradeProofService;

	@ApiOperation(value = "保存订单凭证资料", tags = "CORE")
	@PostMapping("/saveFileMaterial")
	public JsonResult<Boolean> saveFileMaterial(HttpServletRequest request, @RequestBody @Valid @NotNull FileScenesMaterialProofDTO paramDTO) {
		Vendor vendor = UserUtil.getUser(request,Vendor.class);
		if (Objects.isNull(vendor)){
			vendor = new Vendor();
		}
		if (Objects.isNull(vendor.getVendorId())){
			vendor.setVendorId(999999L);
		}
		if (StringUtils.isBlank(vendor.getVendorName())){
			vendor.setVendorName("system");
		}
		return SldResponse.success(orderTradeProofService.saveScenesMaterial(OrderConst.LOG_ROLE_VENDOR,vendor.getVendorId().toString(),vendor.getVendorName(),paramDTO));

	}

	@ApiOperation("查询文件中心凭证资料")
	@GetMapping("/queryFileMaterial")
	public JsonResult<List<FileScenesProofVO>> queryFileMaterial(@RequestParam(value = "proofNo", required = true) String proofNo,
																 @RequestParam(value = "subProofNo", required = false) String subProofNo,
																 @RequestParam(value = "sceneNo", required = false) String sceneNo) {

		return SldResponse.success(orderTradeProofService.queryScenesMaterial(proofNo, subProofNo, sceneNo, null,true));
	}

	@ApiOperation("匹配订单规则资料列表")
	@PostMapping("/matchSceneMaterials")
	public JsonResult<List<OrderTradeProofVO>> matchSceneMaterials(@RequestBody @NotNull OrderTradeProofQueryDTO queryDTO) {

		return SldResponse.success(orderTradeProofService.matchSceneMaterials(queryDTO));
	}

	@GetMapping("/paperSignExport")
	@ApiOperation("签单导出")
	public JsonResult<String> paperSignExport(HttpServletRequest request,@RequestParam("orderSn")String orderSn){
		Vendor vendor = UserUtil.getUser(request,Vendor.class);
		if (Objects.isNull(vendor) || ValidUtils.isEmpty(vendor.getVendorId())) {
			throw new BusinessException("请先登录");
		}
		String exportUrl = orderTradeProofService.paperSignExport(orderSn);
		JsonResult<String> result = SldResponse.success();
		result.setData(exportUrl);
		return result;
	}
}
