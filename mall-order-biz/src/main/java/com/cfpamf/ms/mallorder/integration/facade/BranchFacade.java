package com.cfpamf.ms.mallorder.integration.facade;

import com.cfpamf.cmis.common.base.CommonResult;
import com.cfpamf.cmis.common.base.PageBean;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.bizconfig.facade.request.BranchAreaSupervisorProcessRequest;
import com.cfpamf.ms.bizconfig.facade.request.BranchQueryRequest;
import com.cfpamf.ms.bizconfig.facade.vo.AreaVo;
import com.cfpamf.ms.bizconfig.facade.vo.BranchAreaVo;
import com.cfpamf.ms.bizconfig.facade.vo.BranchContractConfigVo;
import com.cfpamf.ms.bizconfig.facade.vo.BranchInfoVo;
import com.cfpamf.ms.bizconfig.facade.vo.BranchMessageNoticeListVo;
import com.cfpamf.ms.bizconfig.facade.vo.BranchRangeVo;
import com.cfpamf.ms.bizconfig.facade.vo.TreeNodeVo;
import com.cfpamf.ms.bizconfig.facade.vo.TryBranchVo;
import java.util.List;
import javax.validation.Valid;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(
        value = "ms-service-bizconfig",
        url = "${ms-bizconfig-service.url}"
)
@RequestMapping({"/bizconfig"})
public interface BranchFacade {
    @GetMapping({"/branch/getbranchInfo"})
    CommonResult<BranchAreaVo> getBranchAreaInfo(@RequestParam("branchCode") String var1) throws Exception;

    @GetMapping({"/branch/getBaseBranchInfo"})
    CommonResult<BranchAreaVo> getBaseBranchInfo(@RequestParam("branchCode") String var1) throws Exception;

    @GetMapping({"/branch/getTryBranchByCApp"})
    CommonResult<List<String>> getTryBranchByCApp() throws Exception;

    @GetMapping({"/branch/getBranchListByBeisenSuperCode"})
    CommonResult<List<BranchAreaVo>> getBranchListByBeisenSuperCode(@RequestParam("branchCode") String var1) throws Exception;

    @GetMapping({"/branch/getBranchListByBeisenSuperCodeList"})
    CommonResult<List<BranchAreaVo>> getBranchListByBeisenSuperCodeList(@RequestParam("branchCodeList") List<String> var1) throws Exception;

    @GetMapping({"/branch/getList"})
    CommonResult<PageBean<BranchAreaVo>> getBranchList(@RequestParam("pageindex") Integer var1, @RequestParam("pagesize") Integer var2) throws Exception;

    @PostMapping({"/branch/getBranchListByCondition"})
    Result<List<BranchAreaVo>> getBranchListByCondition(@RequestBody BranchQueryRequest var1) throws Exception;

    @PostMapping({"/branch/getBranchListByBatch"})
    Result<List<BranchAreaVo>> getBranchListByBatch(@RequestBody List<String> var1) throws Exception;

    @GetMapping({"/branch/getProvince"})
    CommonResult<List<AreaVo>> getBchProvince();

    @GetMapping({"/branch/getCity"})
    CommonResult<List<AreaVo>> getBchCity(@RequestParam("provinceCode") String var1);

    @GetMapping({"/branch/getCounty"})
    CommonResult<List<AreaVo>> getBchCounty(@RequestParam("cityCode") String var1);

    @GetMapping({"/branch/getBranchListByFinanceOrg"})
    CommonResult<List<BranchAreaVo>> getBranchListByFinanceOrg(@RequestParam("financeOrg") String var1);

    @GetMapping({"/branch/getBranchListBySuperCode"})
    CommonResult<List<BranchAreaVo>> getBranchListBySuperCode(@RequestParam("branchCode") String var1) throws Exception;

    @GetMapping({"/branch/getBranchListBySuperCodeList"})
    CommonResult<List<BranchAreaVo>> getBranchListBySuperCodeList(@RequestParam("branchCodeList") List<String> var1) throws Exception;

    @GetMapping({"/branch/getTryBranchAll"})
    CommonResult<List<TryBranchVo>> getTryBranchAll() throws Exception;

    @GetMapping({"/branchscope/getBranchListByUserCode"})
    CommonResult<List<BranchAreaVo>> getBranchListByUserCode(@RequestParam("userCode") String var1) throws Exception;

    @GetMapping({"/branchscope/getBranchListByUserCode/v2"})
    Result<List<BranchAreaVo>> getBranchListByUserCodeV2(@RequestParam("userCode") String var1, @RequestParam("branchCode") String var2) throws Exception;

    @GetMapping({"/branch/queryBranchRangeList"})
    CommonResult<List<BranchRangeVo>> queryBranchRangeList(@RequestParam("branchCode") String var1) throws Exception;

    @GetMapping({"/branch/getAllBranchRangeList"})
    Result<List<BranchRangeVo>> getAllBranchRangeList() throws Exception;

    @GetMapping({"/branch/getBranchRangeList"})
    Result<PageBean<BranchRangeVo>> getBranchRangeList(@RequestParam("pageindex") Integer var1, @RequestParam("pagesize") Integer var2) throws Exception;

    @GetMapping({"/branch/getRepaymentDay"})
    Result<List<String>> getRepaymentDay(@RequestParam("branchCode") String var1) throws Exception;

    @GetMapping({"/branch/queryBranchContractConfig"})
    CommonResult<BranchContractConfigVo> queryBranchContractConfig(@RequestParam("branchCode") String var1) throws Exception;

    @GetMapping({"/branch/queryBranchInfo"})
    CommonResult<BranchInfoVo> queryBranchInfo(@RequestParam("branchCode") String var1) throws Exception;

    @GetMapping({"/branchscope/getBranchTree"})
    Result<List<TreeNodeVo>> getBranchTree(@RequestParam("userCode") String var1) throws Exception;

    @GetMapping({"/branchscope/getBranchTree/v2"})
    Result<List<TreeNodeVo>> getBranchTreeV2(@RequestParam("userCode") String var1, @RequestParam("branchCode") String var2) throws Exception;

    @GetMapping({"/branch/getBranchMessageNoticeList"})
    Result<List<BranchMessageNoticeListVo>> getBranchMessageNoticeList(@RequestParam("branchCode") String var1, @RequestParam("status") String var2) throws Exception;

    @PostMapping({"/branch/callbackEditAreaSupervisor"})
    Result<Void> callbackEditAreaSupervisor(@RequestBody @Valid BranchAreaSupervisorProcessRequest var1);
}
