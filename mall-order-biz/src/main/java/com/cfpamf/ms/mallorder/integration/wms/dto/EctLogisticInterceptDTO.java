package com.cfpamf.ms.mallorder.integration.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class EctLogisticInterceptDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("发货关联单号")
    private String deliverBusinessNo;

    @ApiModelProperty("拦截关联单号")
    private String interceptBusinessNo;

    @ApiModelProperty("拦截状态 200-拦截成功 300-拦截失败")
    private Integer interceptStatusCode;

}
