package com.cfpamf.ms.mallorder.integration.shop;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.framework.autoconfigure.common.exception.MSException;
import com.cfpamf.ms.mall.account.enums.AccountCardTypeEnum;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.cfpamf.ms.mallorder.common.util.ExternalApiUtil;
import com.cfpamf.ms.mallshop.api.StoreAccountBookFeignClient;
import com.cfpamf.ms.mallshop.api.StoreExpenseFeignClient;
import com.cfpamf.ms.mallshop.resp.StoreAccountBookInfo;
import com.slodon.bbc.core.response.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/*
店铺域对接
 */
@Slf4j
@Component
public class StoreIntegration {

    @Autowired
    private StoreExpenseFeignClient storeExpenseFeignClient;
    @Autowired
    private StoreAccountBookFeignClient storeAccountBookFeignClient;

    public Result<Void> orderAuthorityClose(Long storeId){
        Result<Void> result = new Result<>();
        JsonResult jsonResult;
        try {
            jsonResult = storeExpenseFeignClient.orderAuthorityClose(storeId);
            if (Objects.isNull(jsonResult) || HttpStatus.OK.value() != jsonResult.getState()) {
                result.setSuccess(false);
                result.addError(String.valueOf(ErrorCodeEnum.C.CALL_EXCEPTION.getCode()),
                        String.format("关闭店铺下单权限返回失败,storeId:%s",storeId),"");
                return result;
            }
            result.setSuccess(true);
            result.setData(null);
            return result;
        } catch (Exception ex) {
            result.setSuccess(false);
            result.addError(String.valueOf(ErrorCodeEnum.C.CALL_EXCEPTION.getCode()),
                    String.format("关闭店铺下单权限出现未知异常,storeId:%s",storeId),"");
            return result;
        }
    }

    public List<StoreAccountBookInfo> batchQueryStoreYztIncomeAccount(List<Long> storeIdList) {
        JsonResult<List<StoreAccountBookInfo>> result = ExternalApiUtil.callJsonResultApi(() -> storeAccountBookFeignClient.listStoreAccountBook(storeIdList,AccountCardTypeEnum.UNI_JS_STORE_HEAD.getValue()), storeIdList,
                "/seller/storeAccountBook/listStoreAccountBook", "查询店铺云直通收款账户信息");
        if (result.getData() == null) {
            throw new MSException(String.valueOf(ErrorCodeEnum.C.RESULT_INVALID.getCode()),"查询店铺云直通收款账户信息返回结果为null");
        }
        List<StoreAccountBookInfo> storeAccountInfoList = result.getData();
        for (StoreAccountBookInfo itemInfo : storeAccountInfoList) {
            List<StoreAccountBookInfo.StoreAccountBook> storeAccountBookList = itemInfo.getStoreAccountBookList();
            if (CollectionUtils.isEmpty(storeAccountBookList) || storeAccountBookList.size() != NumberUtils.INTEGER_ONE) {
                throw new MSException(String.valueOf(ErrorCodeEnum.C.RESULT_INVALID.getCode()),"查询店铺云直通收款账户返回收款账簿列表为空或者数量大于1");
            }
            StoreAccountBookInfo.StoreAccountBook storeAccountBook = storeAccountBookList.get(0);
            if (!AccountCardTypeEnum.UNI_JS_STORE_HEAD.getValue().equals(storeAccountBook.getFunctionType())) {
                throw new MSException(String.valueOf(ErrorCodeEnum.C.RESULT_INVALID.getCode()),"查询店铺云直通收款账户返回收款账簿类型不是商户普通户");
            }
        }
        return result.getData();
    }

}
