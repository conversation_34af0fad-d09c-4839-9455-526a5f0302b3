package com.cfpamf.ms.mallorder.controller.admin;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cfpamf.cmis.common.base.PageBean;
import com.cfpamf.cmis.common.utils.DateUtils;
import com.cfpamf.common.ms.vo.PageVO;
import com.cfpamf.ms.bizconfig.facade.request.ProductEssentialQueryRequet;
import com.cfpamf.ms.bizconfig.facade.request.ProductRequest;
import com.cfpamf.ms.bizconfig.facade.vo.product.ProductVo;
import com.cfpamf.ms.bizconfig.facade.vo.product.RepaymentRelationVo;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.util.OrderLocalUtils;
import com.cfpamf.ms.mallorder.common.util.PoiUtils;
import com.cfpamf.ms.mallorder.dto.*;
import com.cfpamf.ms.mallorder.integration.cashier.CashierIntegration;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.PayMethodPO;
import com.cfpamf.ms.mallorder.service.IOrderService;
import com.cfpamf.ms.mallorder.service.IPayMethodService;
import com.cfpamf.ms.mallorder.vo.*;
import com.qiniu.util.Json;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Create 2021-08-04 16:36
 * @Description :支付方式控制类
 */
@RestController
@RequestMapping("admin/payMethod/")
@Api(value = "payMethod", tags = "admin-支付方式管理")
@Slf4j
public class AdminPayMethodController {

    @Autowired
    private IPayMethodService iPayMethodService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private CashierIntegration cashierIntegration;

    @ApiOperation("商家入驻新增默认支付")
    @PostMapping("insertPayMethodMerchant")
    public JsonResult insertPayMethodMerchant(@RequestParam(value = "vendorId") Long vendorId) {
        return iPayMethodService.insertPayMethodMerchant(vendorId);
    }

    @ApiOperation("入驻增加渠道白名单")
    @GetMapping("addChannelWhiteList")
    public JsonResult<Boolean> addChannelWhiteList(@RequestParam(value = "storeId") @ApiParam("店铺id") String storeId,
                                                   @RequestParam(value = "channel") @ApiParam("1-云直通，2-收付通") Integer channel) {
        return SldResponse.success(iPayMethodService.addChannelWhiteList(storeId, channel));
    }

    @ApiOperation("单个店铺删除渠道白名单")
    @GetMapping("deleteChannelWhiteList")
    public JsonResult<Boolean> deleteChannelWhiteList(@RequestParam(value = "storeId") @ApiParam("店铺id") String storeId,
                                                   @RequestParam(value = "channel") @ApiParam("1-云直通，2-收付通") Integer channel) {
        return SldResponse.success(iPayMethodService.deleteChannelWhiteList(storeId, channel));
    }

    @ApiOperation("分页查询支付方式列表")
    @PostMapping("pageList")
    public JsonResult<Page<PayMethodPageVO>> pagePayMethodList(@RequestBody PayMethodReq payMethodReq) {
        return iPayMethodService.pagePayMethodList(payMethodReq);
    }

    @ApiOperation("保存支付方式")
    @PostMapping("save")
    public JsonResult savePayMethod(@RequestBody PayMethodDTO dto) {
        return iPayMethodService.savePayMethod(dto);
    }

    @ApiOperation("分页查询适用商家列表")
    @PostMapping("merchant/pageList")
    public JsonResult<Page<PaymethodMerchantVO>> pageMerchantList(@RequestBody PayMethodMerchantReq queryDTO) {
        return iPayMethodService.pageMerchantList(queryDTO);
    }

    @ApiOperation("处理历史商家数据")
    @GetMapping("merchant/clean")
    public JsonResult merchantClean() {
        return iPayMethodService.merchantClean();
    }

    @ApiOperation("新增商家")
    @PostMapping("merchant/save")
    public JsonResult insertMerchant(@RequestBody PayMethodMerchantDTO dto) {
        return iPayMethodService.insertMerchant(dto);
    }

    @ApiOperation("删除商家")
    @PostMapping("merchant/delete")
    public JsonResult deleteMerchant(@RequestBody PayMethodMerchantDTO dto) {
        return iPayMethodService.deleteMerchant(dto);
    }

    @ApiOperation("分页查询适用商家列表V2")
    @PostMapping("merchantV2/pageList")
    public JsonResult<PageVO<BzOrderPayWhitelistVO>> pageMerchantV2List(@RequestBody PayMethodMerchantReq queryDTO) {
        return iPayMethodService.pageMerchantV2List(queryDTO);
    }

    @ApiOperation("新增商家V2")
    @PostMapping("merchantV2/save")
    public JsonResult insertMerchantV2(@RequestBody PayMethodMerchantV2DTO dto) {
        return iPayMethodService.insertMerchantV2(dto);
    }

    @ApiOperation("删除商家V2")
    @PostMapping("merchantV2/delete")
    public JsonResult deleteMerchantV2(@RequestBody PayMethodGoodsDeleteDTO dto) {
        return iPayMethodService.deleteMerchantV2(dto);
    }


    @ApiOperation("新增商品")
    @PostMapping("goods/save")
    public JsonResult insertGoods(@RequestBody @Valid @NotNull PayMethodGoodsDTO dto) {
        return iPayMethodService.insertGoods(dto);
    }

    @ApiOperation("删除商品")
    @PostMapping("goods/delete")
    public JsonResult deleteGoods(@RequestBody PayMethodGoodsDeleteDTO dto) {
        return iPayMethodService.deleteGoods(dto);
    }


    @ApiOperation("分页查询商品黑名单列表")
    @PostMapping("goods/pageList")
    public JsonResult<PageVO<BzOrderPayBlacklistVO>> pageGoodsList(@RequestBody PayMethodGoodsReq queryDTO) {
        return iPayMethodService.pageGoodsList(queryDTO);
    }

    @ApiOperation("商品黑名单表导出")
    @PostMapping("goods/export")
    public void goodExpot(HttpServletRequest request, HttpServletResponse response,
                          @RequestBody PayMethodGoodsReq goodsReq) {
        Map<String, List<? extends Object>> sheepMap = new HashMap<>(16);
        sheepMap.put("商品黑名单", iPayMethodService.goodsList(goodsReq));
        PoiUtils.exportByHttp(request, response, "商品黑名单" + DateUtils.format(new Date(), "yyyyMMdd"), sheepMap,
                PoiUtils.DEFAULT_DATE_FORMAT, null, false);
    }

    @ApiOperation("初始化商品黑名单历史数据")
    @GetMapping("goods/init")
    public JsonResult<Boolean> initGoods() {
        return SldResponse.success(iPayMethodService.initGoods());
    }

    @ApiOperation("初始化店铺白名单历史数据")
    @GetMapping("store/init")
    public JsonResult<Boolean> initStore() {
        return SldResponse.success(iPayMethodService.initStore());
    }

    @ApiOperation("查询支付方式修改轨迹")
    @GetMapping("track/list")
    public JsonResult<List<BzPayMethodTrackVO>> listPayMethodTrack(@ApiParam(value = "支付方式id", required = true)
                                                                   @RequestParam(value = "payMethodId") String payMethodId) {
        return iPayMethodService.listPayMethodTrack(payMethodId);
    }

    @ApiOperation("查询支付方式修改轨迹-分页")
    @GetMapping("track/list/v2")
    public JsonResult<PageVO<BzPayMethodTrackVO>> listPayMethodTrackV2(@ApiParam(value = "支付方式id", required = true)
                                                                   @RequestParam(value = "payMethodId") String payMethodId,
                                                                   @ApiParam(value = "当前页面", required = true, example = "1")
                                                                   @RequestParam(value = "current") Integer current,
                                                                   @ApiParam(value = "页面大小", required = true,example = "10")
                                                                   @RequestParam(value = "pageSize") Integer pageSize) {

        return iPayMethodService.listPayMethodTrackV2(payMethodId, current, pageSize);
    }

    @ApiOperation("查询贷款产品列表")
    @PostMapping({"product/getProductListByQuery"})
    public JsonResult<List<ProductVo>> getProductListByQuery(@RequestBody ProductRequest request) {
        return iPayMethodService.getProductListByQuery(request);
    }

    @ApiOperation(" 查询产品信息")
    @PostMapping({"product/manage/queryProductEssential"})
    public JsonResult<PageBean<RepaymentRelationVo>> queryProductEssential(@RequestBody ProductEssentialQueryRequet queryRequet){
        return iPayMethodService.queryProductEssential(queryRequet);
    }

    @ApiOperation("查询微信白名单店铺")
    @GetMapping("wx/get")
    public JsonResult<String> wxGet(@ApiParam(value = "redisKey", required = true)
                                    @RequestParam(value = "redisKey") String redisKey) {
        return SldResponse.success(stringRedisTemplate.opsForValue().get(redisKey));
    }

    @ApiOperation("设置微信白名单店铺")
    @PostMapping("wx/put")
    public JsonResult<String> wxPut(@ApiParam(value = "redisKey", required = true)
                                    @RequestParam(value = "redisKey") String redisKey,
                                    @ApiParam(value = "redisValue")
                                    @RequestParam(value = "redisValue") String redisValue) {
        stringRedisTemplate.opsForValue().set(redisKey, redisValue);
        try {
            cashierIntegration.configSet(redisKey, redisValue, CommonConst.MALL_ORDER);
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return SldResponse.success("新增成功");
    }

    @ApiOperation("删除微信白名单店铺")
    @GetMapping("wx/delete")
    public JsonResult<String> wxDelete(@ApiParam(value = "redisKey", required = true)
                                       @RequestParam(value = "redisKey") String redisKey) {
        stringRedisTemplate.delete(redisKey);
        try {
            cashierIntegration.configDelete(redisKey, CommonConst.MALL_ORDER);
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return SldResponse.success("删除成功");
    }

    @ApiOperation("商家新增支付方式")
    @PostMapping("insertPayMethodStore")
    public JsonResult insertPayMethodStore(@RequestBody PayMethodStoreDTO payMethodStoreDTO) {
        return iPayMethodService.insertPayMethodStore(payMethodStoreDTO);
    }

    @ApiOperation("商家删除支付方式")
    @PostMapping("deletePayMethodStore")
    public JsonResult deletePayMethodStore(@RequestBody PayMethodStoreDTO payMethodStoreDTO) {
        return iPayMethodService.deletePayMethodStore(payMethodStoreDTO);
    }

    @ApiOperation("查询商家支付方式")
    @PostMapping("getPayMethodStoreByStoreId")
    public JsonResult<List<PayMethodStoreVO>> getPayMethodStoreByStoreId(@RequestParam(value = "storeId") Long storeId) {
        List<PayMethodStoreVO> payMethodPoList = iPayMethodService.getPayMethodStoreByStoreId(storeId);
        return SldResponse.success(payMethodPoList);
    }

    @ApiOperation("新增分类")
    @PostMapping("category/save")
    public JsonResult<String> insertCategory(@RequestBody @Valid @NotNull PayMethodCategoryDTO dto) {
        return iPayMethodService.insertCategory(dto);
    }

    @ApiOperation("查询分类")
    @GetMapping("category/query")
    public JsonResult<List<String>> queryCategory(@ApiParam(value = "支付方式id", required = true)
                                                  @RequestParam(value = "payMethodId") String payMethodId,
                                                  @ApiParam(value = "订单模式")
                                                  @RequestParam(value = "orderPattern",required = false) Integer orderPattern) {
        return iPayMethodService.queryCategory(payMethodId, orderPattern);
    }


    @ApiOperation("新增分类-临时接口")
    @PostMapping("category/saveTmp")
    public JsonResult<Boolean> insertCategoryTmp(@RequestBody @Valid @NotNull PayMethodCategoryListDTO dto) {
        return iPayMethodService.insertCategoryTmp(dto);
    }

    @ApiOperation("渠道商户同步-临时接口")
    @GetMapping("syncMerchant")
    public JsonResult<Boolean> syncMerchant(@ApiParam(value = "店铺id集合，按照英文逗号分割", required = false) @RequestParam(value = "storeIds",required = false) String storeIds,
                                            @ApiParam(value = "同步时间起 yyyy-MM-dd", required = false) @RequestParam(value = "beginDate",required = false) String beginDate,
                                            @ApiParam(value = "同步时间止 yyyy-MM-dd", required = false) @RequestParam(value = "endDate",required = false) String endDate) {
        iPayMethodService.syncMerchant(storeIds, beginDate, endDate);
        return SldResponse.success(true);
    }


}
