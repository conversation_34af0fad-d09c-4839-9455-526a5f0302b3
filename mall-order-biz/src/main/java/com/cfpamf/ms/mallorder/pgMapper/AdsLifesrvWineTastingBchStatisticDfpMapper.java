package com.cfpamf.ms.mallorder.pgMapper;

import com.cfpamf.framework.autoconfigure.mybatis.MyBaseMapper;
import com.cfpamf.ms.mallorder.po.pgrpt.AdsLifesrvWineTastingBchStatisticDfpPO;
import com.cfpamf.ms.mallorder.req.LifeSrvWineTastingBchDetailRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-30
 */
public interface AdsLifesrvWineTastingBchStatisticDfpMapper extends MyBaseMapper<AdsLifesrvWineTastingBchStatisticDfpPO> {

    /**
     * 获取分支品酒会活动明细
     * @param request 请求参数
     * @return list
     */
    List<AdsLifesrvWineTastingBchStatisticDfpPO> getPageInfo(@Param("request") LifeSrvWineTastingBchDetailRequest request); /**
     * 获取分支品酒会活动明细
     * @param request 请求参数
     * @return list
     */
    Integer getCount(@Param("request") LifeSrvWineTastingBchDetailRequest request);
}
