package com.cfpamf.ms.mallorder.service.external;

/**
 * 消息补偿服务
 */
public interface IMessageCompensateService {

    /**
     * 正向订单rabbitmq消息补偿
     *
     * @param orderSn       订单号
     * @param orderEvent    事件
     */
    void rabbitmqOrderEventCompensate(String orderSn, String orderEvent);

    /**
     * 逆向订单rabbitmq消息补偿
     *
     * @param afsSn         退款单号
     * @param orderEvent    事件
     */
    void rabbitmqOrderReturnEventCompensate(String afsSn, String orderEvent);

}
