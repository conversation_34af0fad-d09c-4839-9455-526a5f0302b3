package com.cfpamf.ms.mallorder.controller.fegin.facade;


import com.cfpamf.ms.mallorder.dto.ErpDeliveryDTO;
import com.cfpamf.ms.mallorder.dto.OrderPerformanceDeliveryDTO;
import com.cfpamf.ms.mallorder.dto.OrderPerformanceOutboundDTO;
import com.cfpamf.ms.mallorder.dto.OrderPerformanceReceiptDTO;
import com.cfpamf.ms.mallorder.service.IOrderExternalPerformanceService;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * @Author: zml
 * @CreateTime: 2022/7/5 15:45
 */
@RestController
@Slf4j
@Api(tags = "外部履约")
public class OrderPerformanceFeignClient {


    @Resource
    private IOrderExternalPerformanceService orderPerformanceService;


    @ApiOperation(value = "外部发货接口", tags = "CORE")
    @PostMapping("/v1/feign/business/order/performance/delivery")
    public JsonResult<Boolean> extDeliver(@RequestBody @Valid @NotNull OrderPerformanceDeliveryDTO orderPerformanceDeliveryDTO) {
        orderPerformanceService.extDeliver(orderPerformanceDeliveryDTO);
        return SldResponse.success(Boolean.TRUE);
    }

    @ApiOperation("外部签收接口")
    @PostMapping("/v1/feign/business/order/performance/receipt")
    public JsonResult<Boolean> extReceipt(@RequestBody @Valid @NotNull OrderPerformanceReceiptDTO orderPerformanceReceiptDTO){        ;
        return SldResponse.success(orderPerformanceService.extReceipt(orderPerformanceReceiptDTO));
    }

    @ApiOperation("外部更新物流信息接口")
    @PostMapping("/v1/feign/business/order/performance/updateDeliveryInfo")
    public JsonResult<Boolean> updateDeliveryInfo(@RequestBody ErpDeliveryDTO erpDeliveryDTO) {
        return SldResponse.success(orderPerformanceService.updateDeliveryInfo(erpDeliveryDTO));
    }

    /**
     * 渠道订单，将商品置为发货中，不允许售后；存在售后则返回特定编码 1000001表示有售后
     * @param orderPerformanceDeliveryDTO
     * @return
     */
    @ApiOperation(value = "将订单置为发货中")
    @PostMapping("/v1/feign/business/order/performance/outbound")
    public JsonResult<Boolean> extOutbound(@RequestBody @Valid @NotNull OrderPerformanceOutboundDTO orderPerformanceDeliveryDTO) {
        return orderPerformanceService.extOutbound(orderPerformanceDeliveryDTO);
    }


    /**
     * 渠道订单，将商品置为待发货，允许售后
     * @param orderPerformanceDeliveryDTO
     * @return
     */
    @ApiOperation(value = "将订单置为待发货")
    @PostMapping("/v1/feign/business/order/performance/outboundToWaitDelivery")
    public JsonResult<Boolean> outboundToWaitDelivery(@RequestBody @Valid @NotNull OrderPerformanceOutboundDTO orderPerformanceDeliveryDTO) {
        return orderPerformanceService.outboundToWaitDelivery(orderPerformanceDeliveryDTO);
    }

}
