package com.cfpamf.ms.mallorder.integration.facade;


import com.alibaba.fastjson.JSONObject;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.bms.facade.dto.ElementTreeNodeDTO;
import com.cfpamf.ms.bms.facade.util.JwtHelper;
import com.cfpamf.ms.bms.facade.vo.*;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.integration.facade.dto.BmsUserInfoVO;
import com.cfpamf.ms.mallorder.vo.BmsOrganizationBaseVO;
import com.cfpamf.ms.mallorder.vo.OrganizationWithDirectorVO;
import com.google.api.client.util.Lists;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.exception.MallException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Create 2022-09-08 10:39
 * @Description :收银台接口
 */
@Slf4j
@Component
public class BmsIntegration {

    private static final String BMS_EXCEPTION_CODE = "999999";
    private static final String BMS_EXCEPTION_MSG = "调用BMS服务异常";

    @Autowired
    private BmsUserFacade bmsUserFacade;

    @Autowired
    private BmsDictionaryFacade bmsDictionaryFacade;

    @Autowired
    private JwtHelper jwtHelper;

    @Autowired
    private BmsOrganizationFacade bmsOrganizationFacade;

    @Autowired
    private BmsEmployeeFacade bmsEmployeeFacade;

    @Value("${bms-director.postId:82}")
    private Integer directorPostId;

    public List<DictionaryItemVO> listDictionaryItemsByTypeCode(String typeCode, Integer systemId) {
        Result<List<DictionaryItemVO>> result = null;
        try {
            result = bmsUserFacade.getDictionaryItemsByTypeCode(jwtHelper.getToken(), systemId, typeCode);
        }catch (Exception e) {
            log.error("bmsUserFacade.getDictionaryItemsByTypeCode /dictionary/items/{typeCode} {}{}{} 接口响应异常：",jwtHelper.getToken(), systemId, typeCode,e);
            throw new MallException("抱歉，获取BMS字典信息，/dictionary/items/{typeCode}异常。参数：" + typeCode);
        }
        if (result == null) {
            throw new MallException("调用dictionaryFacade.getDictionaryItemsByTypeCode返回null。参数：" + typeCode);
        }
        if (!result.isSuccess()) {
            throw new BusinessException("错误码：" + result.getErrorCode() + "错误信息：" + result.getErrorMsg());
        }
        return result.getData();
    }

    public List<OrganizationWithDirectorVO> listDirectorByHrOrgCodes(List<String> hrOrgCodes) {
        Result<List<OrganizationWithDirectorVO>> result = bmsUserFacade.listDirectorByHrOrgCodesV2(hrOrgCodes);

        if (result == null) {
            throw new MallException("/org/listDirectorByHrOrgCodes。参数：" + hrOrgCodes);
        }
        if (!result.isSuccess()) {
            throw new BusinessException("错误码：" + result.getErrorCode() + "错误信息：" + result.getErrorMsg());
        }
        return result.getData();
    }

    public OrganizationWithDirectorVO getDirectorByHrOrgCodes(String hrOrgCodes) {
        List<OrganizationWithDirectorVO> directorVOS = this.listDirectorByHrOrgCodes(Collections.singletonList(hrOrgCodes));
        return directorVOS.stream().filter(x -> x.getOrgCode().equals(hrOrgCodes)).findFirst().orElse(null);
    }


    /**
     * 根据工号查询员工任职信息
     */
    public UserListBaseVO getEmployeesByJobNumbersBase(String userNo) {
        if (StringUtils.isEmpty(userNo)) {
            return null;
        }
        BaseUserDetailQuery query = new BaseUserDetailQuery();
        query.setJobNumbers(Arrays.asList(userNo));
        Result<List<UserListBaseVO>> result;
        try {
            result = bmsUserFacade.getEmployeesByJobNumbersBase(query);
        } catch (Exception e) {
            log.error("调用bmsUserFacade.getEmployeesByJobNumbersBase异常:{}", e.getMessage());
            throw new MallException("获取组织信息异常");
        }
        if (result == null || result.getData() == null) {
            log.error("调用bmsUserFacade.getEmployeesByJobNumbersBase未返回信息:{}", userNo);
            throw new MallException("获取组织信息异常");
        }
        List<UserListBaseVO> userListBaseVOS = result.getData();

        //主职信息
        UserListBaseVO userListBaseVO = userListBaseVOS.stream().filter(x -> x.getServiceType().equals(0)).findFirst().orElse(null);
        if (Objects.isNull(userListBaseVO)) {
            //不存在主职，则返回兼职
            userListBaseVO = userListBaseVOS.stream().filter(x -> x.getServiceType().equals(1)).findFirst().orElse(null);
        }
        return userListBaseVO;
    }

    public String getJobNumberByMobile(String mobile) {

        String[] accounts = {mobile};

        Result<List<UserListVO>> result = bmsUserFacade.getEmployeesByAccounts(accounts);
        if (result == null) {
            throw new MallException("调用BmsUserFacade.detailByAccounts返回null。参数：" + mobile);
        }
        if (!result.isSuccess()) {
            throw new BusinessException("调用BmsUserFacade.detailByAccounts错误码：" + result.getErrorCode() + "错误信息：" + result.getErrorMsg());
        }
        if (CollectionUtils.isEmpty(result.getData())) {
            return null;
        }

        return result.getData().get(0).getJobNumber();
    }

    public String getJobNumberByByIdCard(String idCard) {
        Result<UserDetailVO> result = null;
        try {
            result = bmsUserFacade.getUserDetailVOByIdcard(idCard);
        } catch (Exception e) {
            throw new BusinessException("调用BMS服务异常，接口：根据身份证查询用户基本信息");
        }
        if (result == null) {
            throw new MallException("调用BMS返回null。参数：" + idCard);
        }
        if (!result.isSuccess()) {
            log.warn("调用BMS查询员工信息失败，原因：" + result.getErrorMsg());
//			throw new BusinessException("调用BMS失败，错误码：" + result.getErrorCode() + "错误信息：" + result.getErrorMsg());
        }
        if (Objects.isNull(result.getData())) {
            return null;
        }

        return result.getData().getJobNumber();
    }

    /**
     * 根据手机号码查询用户信息
     */
    public UserListVO getEmployeesByAccount(String account) {
        Result<List<UserListVO>> result;
        String[] accountArr = {account};
        try {
            result = bmsUserFacade.getEmployeesByAccounts(accountArr);
        } catch (Exception e) {
            throw new BusinessException(String.format("调用BMS服务异常, 根据手机号码精确匹配获取所有用户信息列表, account:%s", account));
        }
        if (Objects.isNull(result)) {
            throw new BusinessException(String.format("调用BMS服务返回为null, 根据手机号码精确匹配获取所有用户信息列表, account:%s", account));
        }
        if (!result.isSuccess()) {
            throw new BusinessException(String.format("调用BMS服务返回失败, 根据手机号码精确匹配获取所有用户信息列表, account:%s, 失败原因:%s",
                    account, result.getErrorMsg()));
        }
        if (Objects.isNull(result.getData())) {
            log.warn(String.format("调用BMS服务返回为空, 根据手机号码精确匹配获取所有用户信息列表, account:%s", account));
            return null;
        }
        if (CollectionUtils.isEmpty(result.getData())) {
            log.warn(String.format("调用BMS服务返回用户列表为空, 根据手机号码精确匹配获取所有用户信息列表, account:%s", account));
            return null;
        }
        return result.getData().get(0);
    }

    /**
     * 根据用户ID获取用户信息
     */
    public BmsUserInfoVO getUserInfo(String userId) {
        Result<List<BmsUserInfoVO>> result;
        try {
            result = bmsUserFacade.getUserInfo(userId);
        } catch (Exception e) {
            throw new BusinessException(String.format("调用BMS服务异常, 根据用户ID获取用户信息, userId:%s", userId));
        }
        if (Objects.isNull(result)) {
            throw new BusinessException(String.format("调用BMS服务返回为null, 根据用户ID获取用户信息, userId:%s", userId));
        }
        if (!result.isSuccess()) {
            throw new BusinessException(String.format("调用BMS服务返回失败, 根据用户ID获取用户信息, userId:%s, 失败原因:%s",
                    userId, result.getErrorMsg()));
        }
        if (Objects.isNull(result.getData())) {
            log.warn(String.format("调用BMS服务返回为空, 根据用户ID获取用户信息, userId:%s", userId));
            return null;
        }
        return result.getData().get(0);
    }


    public List<UserListBaseVO> getEmployeeInfo(String jobNumber) {
        BaseUserDetailQuery query = new BaseUserDetailQuery();
        query.setJobNumbers(Arrays.asList(jobNumber));
        Result<List<UserListBaseVO>> result;
        try {
            result = bmsUserFacade.getEmployeesByJobNumbersBase(query);
        } catch (Exception e) {
            log.error("调用bmsUserFacade.getEmployeesByJobNumbersBase /user/list/baseDetail 异常 query:{}",query, e);
            throw new MallException("抱歉，获取BMS组织信息异常");
        }


        if (result == null) {
            throw new BusinessException("查询员工信息异常,jobNumber=" + jobNumber);
        }
        if (!result.isSuccess()) {
            throw new BusinessException("BMS:" + result.getErrorMsg());
        }
        if (org.apache.commons.collections.CollectionUtils.isEmpty(result.getData())) {
            throw new BusinessException("BMS查询无此员工信息:" + jobNumber);
        }

        return result.getData();
    }

    public List<ElementTreeNodeDTO> listRootHeadBuAreaBranchBySuperOrgCode(String orgCode) {
        Result<List<ElementTreeNodeDTO>> result = bmsOrganizationFacade.listRootHeadBuAreaBranchBySuperOrgCode(orgCode);
        if (result == null) {
            throw new BusinessException("listRootHeadBuAreaBranchBySuperOrgCode接口异常");
        } else if (!result.isSuccess()) {
            throw new BusinessException(result.getErrorMsg());
        }
        return result.getData();
    }

    public List<UserListVO> listLoanOfficerUserListVOBySupervisor(String jobNumber) {
        Result<List<UserListVO>> result = bmsEmployeeFacade.listLoanOfficerUserListVOBySupervisor(null, jobNumber);
        if (result == null) {
            throw new BusinessException("listLoanOfficerUserListVOBySupervisor接口异常");
        } else if (!result.isSuccess()) {
            throw new BusinessException(result.getErrorMsg());
        }
        return result.getData();
    }
    public OrganizationBaseVO getAreaByHrOrgId(Integer hrOrgId) {
        Result<OrganizationBaseVO> result = bmsOrganizationFacade.getAreaByHrOrgId(null, hrOrgId);
        if (result == null) {
            throw new BusinessException("getAreaByHrOrgId接口异常");
        } else if (!result.isSuccess()) {
            throw new BusinessException(result.getErrorMsg());
        }
        return result.getData();
    }

    public List<OrganizationBaseVO> listBranchesByHrParentId(Integer hrParentId) {
        Result<List<OrganizationBaseVO>> result = bmsOrganizationFacade.listBranchesByHrParentId(null, hrParentId);
        if (result == null) {
            throw new BusinessException("listBranchesByHrParentId接口异常");
        } else if (!result.isSuccess()) {
            throw new BusinessException(result.getErrorMsg());
        }
        return result.getData();
    }

    public List<OrganizationBaseVO> listAreasByAreaHrOrgIds(List<Integer> areaHrOrgIds) {
        Result<List<OrganizationBaseVO>> result = bmsOrganizationFacade.listAreasByAreaHrOrgIds(null, areaHrOrgIds);
        if (result == null) {
            throw new BusinessException("listAreasByAreaHrOrgIds接口异常");
        } else if (!result.isSuccess()) {
            throw new BusinessException(result.getErrorMsg());
        }
        return result.getData();
    }

    public List<OrganizationBaseVO> listBranchesByHrOrgIds(List<Integer> hrOrgIds) {
        Result<List<OrganizationBaseVO>> result = bmsOrganizationFacade.listBranchesByHrOrgIds(null, hrOrgIds);
        if (result == null) {
            throw new BusinessException("listBranchesByHrOrgIds接口异常");
        } else if (!result.isSuccess()) {
            throw new BusinessException(result.getErrorMsg());
        }
        return result.getData();
    }

    public List<DictionaryItemVO> getDictionaryItemsByTypeCode(Integer systemId, String typeCode) {
        Result<List<DictionaryItemVO>> result = null;
        try {
            result = bmsDictionaryFacade.getDictionaryItemsByTypeCode(null, systemId, typeCode);
        }catch (Exception e) {
            log.error("bmsUserFacade.getDictionaryItemsByTypeCode /dictionary/items/{typeCode} {}{}{} 接口响应异常：", systemId, typeCode, e);
            throw new MallException("抱歉，获取BMS字典信息，/dictionary/items/{typeCode}异常。参数：" + typeCode);
        }
        if(result == null) {
            throw new MSBizNormalException(BMS_EXCEPTION_CODE, BMS_EXCEPTION_MSG);
        } else if(!result.isSuccess()) {
            throw new MSBizNormalException(result.getErrorCode(), "BMS:" + result.getErrorMsg());
        }
        return result.getData();
    }


    /**
     * 获取生服店铺ID
     */
    public List<Long> getLifeServiceStoreId() {
        List<DictionaryItemVO> items = this.getDictionaryItemsByTypeCode(CommonConst.MALL_SUPPORT_SYSTEM_ID, CommonConst.STOREID_SFN);
        if (CollectionUtils.isEmpty(items)) {
            return new ArrayList<>();
        }

        List<Long> storeIds = items.stream().map(DictionaryItemVO::getItemCode).map(Long::parseLong).collect(Collectors.toList());

        log.info("BmsIntegration # getLifeServiceStoreId : {}", storeIds);

        return storeIds;
    }

    public OrganizationBaseVO getBizOrgBaseVOByOrgCode(String orgCode) {
        Result<OrganizationBaseVO> result = bmsOrganizationFacade.getBizOrgBaseVOByOrgCode(orgCode);
        if (result == null) {
            throw new MSBizNormalException(BMS_EXCEPTION_CODE, BMS_EXCEPTION_MSG);
        } else if (!result.isSuccess()) {
            throw new MSBizNormalException(result.getErrorCode(), "BMS:" + result.getErrorMsg());
        }
        return result.getData();
    }

    /**
     * 根据分支编码获取分支的主任
     * @param branchCode
     * @return
     */
    public List<UserListVO> getDirectorByBranchCode(String branchCode) {
        OrganizationBaseVO branchOrg = getBizOrgBaseVOByOrgCode(branchCode);
        if (Objects.isNull(branchOrg) || Objects.isNull(branchOrg.getHrOrgId())) {
            log.warn("根据" + branchCode + "获取机构信息返回为空，请联系管理员！");
            return Lists.newArrayList();
        }

        Result<List<UserListVO>> result = bmsEmployeeFacade.getUserByBranchCodeAndPostId(branchOrg.getHrOrgId(), directorPostId);
        if (result == null) {
            throw new MSBizNormalException(BMS_EXCEPTION_CODE, BMS_EXCEPTION_MSG);
        } else if (!result.isSuccess()) {
            throw new MSBizNormalException(result.getErrorCode(), "BMS:" + result.getErrorMsg());
        }
        return result.getData();
    }


    public List<BmsOrganizationBaseVO> getBmsOrganizationBaseVOByOrgCode(List<String> orgCodes) {
        try {
            Result<List<BmsOrganizationBaseVO>> result = bmsOrganizationFacade.getBmsOrganizationBaseVOByOrgCode(orgCodes);
            if (result == null) {
                throw new BusinessException("根据orgCode查询机构返回为null, 分支编码为:" + JSONObject.toJSONString(orgCodes));
            } else if (!result.isSuccess()) {
                throw new BusinessException(result.getErrorMsg());
            }
            return result.getData();
        } catch (Exception e) {
            throw new BusinessException("根据orgCode查询机构信息异常, 分支编码为:" + JSONObject.toJSONString(orgCodes) + ", 异常信息:" +  e.getMessage());
        }
    }
}