package com.cfpamf.ms.mallorder.controller.fegin.facade;

import java.util.List;
import javax.annotation.Resource;

import com.cfpamf.ms.mallorder.model.OrderProductExtendModel;
import com.cfpamf.ms.mallorder.po.OrderProductExtendPO;
import com.cfpamf.ms.mallorder.request.OrderProductExtendExample;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@RestController
@Slf4j
public class OrderProductExtendFeign {

    @Resource
    private OrderProductExtendModel orderProductExtendModel;

    /**
     * 根据extendId获取活动和订单商品的扩展关系表详情
     *
     * @param extendId extendId
     * @return
     */
    @GetMapping("/v1/feign/business/orderProductExtend/get")
    public OrderProductExtendPO getOrderProductExtendByExtendId(@RequestParam("extendId") Integer extendId) {
        return orderProductExtendModel.getOrderProductExtendByExtendId(extendId);
    }

    /**
     * 获取条件获取活动和订单商品的扩展关系表列表
     *
     * @param example 查询条件信息
     * @return
     */
    @PostMapping("/v1/feign/business/orderProductExtend/getList")
    public List<OrderProductExtendPO> getOrderProductExtendList(@RequestBody OrderProductExtendExample example) {
        return orderProductExtendModel.getOrderProductExtendList(example, example.getPager());
    }

    /**
     * 新增活动和订单商品的扩展关系表
     *
     * @param orderProductExtendPO
     * @return
     */
    @PostMapping("/v1/feign/business/orderProductExtend/addOrderProductExtend")
    public Integer saveOrderProductExtend(@RequestBody OrderProductExtendPO orderProductExtendPO) {
        return orderProductExtendModel.saveOrderProductExtend(orderProductExtendPO);
    }

    /**
     * 根据extendId更新活动和订单商品的扩展关系表
     *
     * @param orderProductExtendPO
     * @return
     */
    @PostMapping("/v1/feign/business/orderProductExtend/updateOrderProductExtend")
    public Integer updateOrderProductExtend(@RequestBody OrderProductExtendPO orderProductExtendPO) {
        return orderProductExtendModel.updateOrderProductExtend(orderProductExtendPO);
    }

    /**
     * 根据extendId删除活动和订单商品的扩展关系表
     *
     * @param extendId extendId
     * @return
     */
    @PostMapping("/v1/feign/business/orderProductExtend/deleteOrderProductExtend")
    public Integer deleteOrderProductExtend(@RequestParam("extendId") Integer extendId) {
        return orderProductExtendModel.deleteOrderProductExtend(extendId);
    }

    /**
     * 根据extendId删除活动和订单商品的扩展关系表
     *
     * @return
     */
    @PostMapping("/v1/feign/business/orderProductExtend/init")
    public Boolean init() {
        return orderProductExtendModel.init();
    }
}