package com.cfpamf.ms.mallorder.integration.cashier;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.customer.facade.vo.user.UserBaseInfoVo;
import com.cfpamf.ms.mall.account.constant.AccountConstans;
import com.cfpamf.ms.mall.account.enums.AccountCardTypeEnum;
import com.cfpamf.ms.mall.account.vo.AccountCard;
import com.cfpamf.ms.mallgoods.facade.vo.Product;
import com.cfpamf.ms.mallgoods.facade.vo.ProductFinanceRuleLabel;
import com.cfpamf.ms.mallgoods.facade.vo.ProductPriceVO;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.enums.*;
import com.cfpamf.ms.mallorder.common.util.ExternalApiUtil;
import com.cfpamf.ms.mallorder.common.util.OrderLocalUtils;
import com.cfpamf.ms.mallorder.common.util.ValidUtils;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.OrderSubmitDTO;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.integration.cashier.request.OrderProduct;
import com.cfpamf.ms.mallorder.integration.cashier.request.*;
import com.cfpamf.ms.mallorder.integration.cust.CustomerIntegration;
import com.cfpamf.ms.mallorder.integration.facade.CashierFacade;
import com.cfpamf.ms.mallorder.integration.facade.dto.PayConfigDTO;
import com.cfpamf.ms.mallorder.integration.pay.PayIntegration;
import com.cfpamf.ms.mallorder.integration.settlement.BillOperatinIntegration;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.model.OrderProductModel;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.v2.service.OrderPayRecordService;
import com.cfpamf.ms.mallorder.v2.service.OrderPresellService;
import com.cfpamf.ms.mallorder.vo.MerchantBaseVo;
import com.cfpamf.ms.mallpromotion.constant.PromotionConst;
import com.cfpamf.ms.mallpromotion.enums.CouponFunder;
import com.cfpamf.ms.mallshop.api.StoreFeignClient;
import com.cfpamf.ms.mallshop.resp.Store;
import com.cfpamf.ms.mallshop.resp.StoreContractReceiptInfoVO;
import com.slodon.bbc.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Create 2022-09-08 10:39
 * @Description :收银台接口
 */
@Slf4j
@Component
public class CashierIntegration {

    @Value("${mall-payment.notify}")
    private String notifyUrl;

    @Value("${mall-payment.refundNotify}")
    private String refundNotifyUrl;

    @Value("${mall-payment.loanNotify}")
    private String loanNotifyUrl;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private CashierFacade cashierFacade;
    @Autowired
    private IOrderPayService iOrderPayService;
    @Autowired
    private IOrderService iOrderService;
    @Autowired
    private IOrderExtendService iOrderExtendService;
    @Autowired
    private IOrderProductService iOrderProductService;
    @Resource
    private OrderProductModel orderProductModel;
    @Autowired
    private StoreFeignClient storeFeignClient;
    @Autowired
    private BillOperatinIntegration billOperatinIntegration;
    @Autowired
    private OrderLocalUtils orderLocalUtils;
    @Autowired
    private PayIntegration payIntegration;
    @Autowired
    private CustomerIntegration customerIntegration;
    @Autowired
    private OrderPayRecordService orderPayRecordService;
    @Autowired
    private OrderPresellService orderPresellService;
    @Autowired
    private IBzOldUserPoolService iBzOldUserPoolService;
    @Autowired
    private IOrderProductExtendService orderProductExtendService;

    @Autowired
    private OrderModel orderModel;

    /**
     * @param pushOrder
     * @return Result<Boolean>
     * @description : 推送业务订单信息到收银台
     */
    public Boolean pushBizOrder(PushOrderRequest pushOrder) {
        return ExternalApiUtil.callResultApi(() -> cashierFacade.pushBizOrder(pushOrder), pushOrder,
                "/cashier/bizorder/dopush", "推送业务订单信息到收银台");
    }

    /**
     * @return Result<Boolean>
     * @description : 推送业务订单信息到收银台
     */
    public Boolean configSet(String configKey, String configValue, String systemCode) {
        PayConfigDTO payConfigDTO = new PayConfigDTO();
        payConfigDTO.setConfigKey(configKey);
        payConfigDTO.setConfigValue(configValue);
        payConfigDTO.setSystemCode(systemCode);

        return ExternalApiUtil.callResultApi(() -> cashierFacade.configSet(payConfigDTO), configKey,
                "/cashier/pay-config/set", "设置灰度配置开关");
    }


    /**
     * @return Result<Boolean>
     * @description : 推送业务订单信息到收银台
     */
    public Boolean configDelete(String configKey, String systemCode) {
        return ExternalApiUtil.callResultApi(() -> cashierFacade.configDelete(configKey, systemCode), configKey,
                "/cashier/pay-config/delete", "删除灰度配置开关");
    }


    public Boolean pushToCashier(String paySn, String returnPage) {

        if (StringUtils.isEmpty(paySn)) {
            return false;
        }
        //查询支付单信息
        OrderPayPO orderPayPO = iOrderPayService.lambdaQuery().eq(OrderPayPO::getPaySn, paySn).last("limit 1").one();
        if (Objects.isNull(orderPayPO)) {
            return false;
        }
        //查询分批支付信息
        List<OrderPayRecordPO> orderPayRecordPOS = orderPayRecordService.lambdaQuery().eq(OrderPayRecordPO::getPaySn, paySn).list();
        //查询子订单信息
        List<OrderPO> orderPOList = iOrderService.lambdaQuery().eq(OrderPO::getPaySn, paySn).list();
        if (CollectionUtils.isEmpty(orderPOList)) {
            return false;
        }
        for (OrderPO orderPO : orderPOList) {
            OrderStatusEnum orderStatus = OrderStatusEnum.valueOf(orderPO.getOrderState());
            if (OrderStatusEnum.WAIT_PAY_DEPOSIT != orderStatus && OrderStatusEnum.WAIT_PAY != orderStatus
                    && OrderStatusEnum.DEAL_PAY != orderStatus) {
                log.warn("订单当前状态不允许进行支付,orderSn:{},orderStatus:{},paySn:{}",
                        orderPO.getOrderSn(), orderStatus.getDesc(), orderPO.getPaySn());
                throw new BusinessException("订单当前状态不允许支付，请返回订单列表查看实时状态！");
            }
        }

        List<String> orderSn = orderPOList.stream().map(OrderPO::getOrderSn).collect(Collectors.toList());
        OrderPO orderPoTmp = orderPOList.get(0);
        //查询客户中心id
        UserBaseInfoVo userBaseInfoVo = customerIntegration.userBaseInfo(orderPoTmp.getUserNo());
        String custId = "";
        if (Objects.nonNull(userBaseInfoVo) &&
                Objects.nonNull(userBaseInfoVo.getCustInfoVo())) {
            custId = userBaseInfoVo.getCustInfoVo().getId().toString();
        }
        //查询商品行
        List<OrderProductPO> orderProductPOList = iOrderProductService.lambdaQuery().in(OrderProductPO::getOrderSn, orderSn).list();
        if (CollectionUtils.isEmpty(orderProductPOList)) {
            return false;
        }
        Map<String, List<OrderProductPO>> orderProductList = orderProductPOList.stream().collect(Collectors.groupingBy(OrderProductPO::getOrderSn));

        Map<Long, Product> longProductMap = orderLocalUtils.getLongProductMap(orderProductPOList);

        //查询扩展信息
        List<OrderExtendPO> orderExtendPOS = iOrderExtendService.lambdaQuery().in(OrderExtendPO::getOrderSn, orderSn).list();
        if (CollectionUtils.isEmpty(orderExtendPOS)) {
            return false;
        }
        //转成map，以便使用
        Map<String, OrderExtendPO> orderExtendPOMap = orderExtendPOS.stream()
                .collect(Collectors.toMap(OrderExtendPO::getOrderSn, item -> item, (v1, v2) -> (v2)));


        OrderExtendPO orderExtendPO = orderExtendPOS.get(0);

        //取出店铺id及引荐商id，用于批量查询店铺信息
        Set<Long> storeIds = new HashSet<>();
        for (OrderPO orderPO : orderPOList) {
            storeIds.add(orderPO.getStoreId());
            storeIds.add(orderPO.getRecommendStoreId());
        }
        List<Long> storeIdList = new ArrayList<>(storeIds);
        List<MerchantBaseVo> merchantBaseInfoBatch = orderProductModel.getMerchantBaseInfoBatch(storeIdList);
        //转成map，以便使用
        Map<Long, MerchantBaseVo> merchantBaseVoMap = merchantBaseInfoBatch.stream()
                .collect(Collectors.toMap(MerchantBaseVo::getId, item -> item, (v1, v2) -> (v2)));

        AccountCard accountCard = null;
        Map<Long, StoreContractReceiptInfoVO> contractReceiptInfoVOMap = null;
        //旧订单中，用呗收款账户为引荐商的收款卡
        if (Boolean.FALSE.equals(orderPoTmp.getNewOrder())) {
            List<StoreContractReceiptInfoVO> contractReciptInfoBatch = storeFeignClient.getStoreContractReciptInfoBatch(storeIdList);
            //转成map，以便使用
            contractReceiptInfoVOMap = contractReciptInfoBatch.stream()
                    .collect(Collectors.toMap(StoreContractReceiptInfoVO::getStoreId, item -> item, (v1, v2) -> (v2)));
        } else {
            //新订单查询平台监管户，用于用呗收款
            accountCard = billOperatinIntegration.detailByBankAccount(AccountConstans.UNI_PLF_STORE_ID, AccountCardTypeEnum.UNI_JS_PLF_SUP);
        }

        PushOrderRequest pushOrderRequest = new PushOrderRequest();
        /**
         * 支付者信息
         */
        this.buildOrderPlayerList(orderPOList, custId, orderExtendPO, pushOrderRequest,userBaseInfoVo.getMobile());
        /**
         * 父单信息
         */
        Integer minutes = orderModel.getAutoCancelMinutes(orderPoTmp.getOrderPattern(), orderPoTmp.getOrderType());
        this.buildPushOrderRequestMainInfo(paySn, returnPage, orderPayPO, pushOrderRequest,minutes);
        /**
         * 分批支付信息
         */
        this.buildOrderPhaseList(paySn, returnPage, orderPayRecordPOS, orderPoTmp, pushOrderRequest);

        /**
         * 组装子单信息
         */
        List<OrderSub> subOrderList = new ArrayList<>();
        for (OrderPO orderPO : orderPOList) {
            OrderSub orderSub = new OrderSub();
            orderSub.setSubOrderNo(orderPO.getOrderSn());
            orderSub.setOrderAmt(orderPO.getOrderAmount());
            orderSub.setDescription("【乡助】订单编号" + orderPayPO.getOrderSn());
            orderSub.setPayAmt(orderPO.getOrderAmount());
            orderSub.setProfitSharing(1);
            //支付通道类型，微信普通商户 WX_MCH, 微信服务商 WX_SVC，目前只支持微信
            orderSub.setPayChannelType(Boolean.TRUE.equals(orderPO.getNewOrder()) ? "WX_MCH" : "WX_SVC");
            orderSub.setPayChannel(orderPO.getPayChannel());
            MerchantBaseVo recommendStoreInfo = merchantBaseVoMap.get(orderPO.getRecommendStoreId());
            MerchantBaseVo merchantBaseInfo = merchantBaseVoMap.get(orderPO.getStoreId());

            String orgCode = "";
            Store storeByStoreId = storeFeignClient.getStoreByStoreId(orderPO.getRecommendStoreId());
            if (Objects.nonNull(storeByStoreId) && Objects.nonNull(storeByStoreId.getStoreCertificate())) {
                orgCode = storeByStoreId.getStoreCertificate().getCertificateRegistrationNum();
            }

            /**
             * 商家信息
             */
            this.buildOrderMerchant(orderPO, orderSub, merchantBaseInfo, orgCode);

            /**
             * 商家注册地址信息
             */
            this.buildReceiveAddress(orderSub, merchantBaseInfo);
            /**
             * 商品行信息
             */
            List<OrderProductPO> productPOS = this.buildOrderProductList(orderProductList, longProductMap, orderPO, orderSub);

            List<SubOrderExt> subOrderExtList = new ArrayList<>();
            /**
             * 子单用呗扩展信息
             */
            this.buildSubEnjoyExtList(orderPO, recommendStoreInfo, merchantBaseInfo, orgCode,
                    productPOS, subOrderExtList, accountCard, contractReceiptInfoVOMap,orderExtendPOMap);

            /**
             * 子单微信支付宝扩展信息
             */
            this.buildSubWxExtJson(orderPO, subOrderExtList);

            orderSub.setSubExtList(subOrderExtList);

            subOrderList.add(orderSub);
        }

        pushOrderRequest.setOrderSubList(subOrderList);
        /**
         * 主单用呗扩展信息
         */
        this.buildMainExtList(orderExtendPO, pushOrderRequest, orderPoTmp);
        log.info("推数参数:{}", JSON.toJSONString(pushOrderRequest));
        return this.pushBizOrder(pushOrderRequest);
    }

    private void buildSubWxExtJson(OrderPO orderPO, List<SubOrderExt> subOrderExtList) {
        //配销订单，收款账户特殊处理

        String ali = "";
        String wx = "";

        StoreContractReceiptInfoVO storeContract =
                storeFeignClient.getStoreContractReciptInfo(orderPO.getStoreId());
        if (OrderConst.ORDER_TYPE_7 == orderPO.getOrderType()) {
            ali = storeContract.getRecommentAliSellerId();
            wx = storeContract.getRecommentWxSellerId();
        } else {
            wx = storeContract.getWxSellerId();
        }

        // 补差金额
        BigDecimal subsidyAmount = orderProductExtendService.lambdaQuery().eq(OrderProductExtendPO::getOrderSn, orderPO.getOrderSn())
                .eq(OrderProductExtendPO::getIsStore, CommonEnum.NO.getCode())
                .eq(OrderProductExtendPO::getPromotionType, PromotionConst.PROMOTION_TYPE_402)
                .eq(OrderProductExtendPO::getFunder, CouponFunder.PLATFORM.getValue())
                .list()
                .stream()
                .map(OrderProductExtendPO::getPromotionAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal platformAmount =
                // 代运营服务费
                orderPO.getThirdpartnarFee()
                        // 平台服务费
                        .add(orderPO.getServiceFee())
                        // 业务佣金
                        .add(orderPO.getBusinessCommission())
                        // 订单佣金
                        .add(orderPO.getOrderCommission());

        /**
         * 子单微信支付扩展信息
         */
        CshPayorderSubExtJsonBo cshPayorderSubExtJsonWxBo = new CshPayorderSubExtJsonBo();
        if (OrderConst.ORDER_TYPE_7 == orderPO.getOrderType()) {
            cshPayorderSubExtJsonWxBo.setBusiReceiveAccount(wx);
        }
        cshPayorderSubExtJsonWxBo.setSubsidyAmount(subsidyAmount);
        cshPayorderSubExtJsonWxBo.setPlatformAmount(platformAmount);
        cshPayorderSubExtJsonWxBo.setOtherAmount(orderPO.getExpressFee());
        cshPayorderSubExtJsonWxBo.setWxSellerId(wx);

        SubOrderExt subOrderExtWx = new SubOrderExt();
        subOrderExtWx.setPayWay(PayWayCasierEnum.WXPAY);
        subOrderExtWx.setExtAttrs(cshPayorderSubExtJsonWxBo);
        subOrderExtList.add(subOrderExtWx);

        /**
         * 子单支付宝支付扩展信息
         */
        CshPayorderSubExtJsonBo cshPayorderSubExtJsonAliBo = new CshPayorderSubExtJsonBo();
        if (OrderConst.ORDER_TYPE_7 == orderPO.getOrderType()) {
            cshPayorderSubExtJsonAliBo.setBusiReceiveAccount(ali);
            SubOrderExt subOrderExtAli = new SubOrderExt();
            subOrderExtAli.setPayWay(PayWayCasierEnum.ALI_PAY);
            subOrderExtAli.setExtAttrs(cshPayorderSubExtJsonAliBo);
            subOrderExtList.add(subOrderExtAli);
        }
    }

    private void buildSubEnjoyExtList(OrderPO orderPO, MerchantBaseVo recommendStoreInfo, MerchantBaseVo merchantBaseInfo,
                                      String orgCode, List<OrderProductPO> productPOS, List<SubOrderExt> subOrderExtList,
                                      AccountCard accountCard, Map<Long, StoreContractReceiptInfoVO> contractReceiptInfoVOMap,
                                      Map<String, OrderExtendPO> orderExtendMap) {
        CshPayorderSubExtJsonBo cshPayorderSubExtJsonBo = new CshPayorderSubExtJsonBo();
        if (Boolean.TRUE.equals(orderPO.getNewOrder()) && Objects.nonNull(accountCard)) {
            cshPayorderSubExtJsonBo.setDrawCardNo(accountCard.getBankAccountNumber());
            cshPayorderSubExtJsonBo.setDrawBankCode(accountCard.getBankCode());
            cshPayorderSubExtJsonBo.setDrawBankName(accountCard.getBankBranch());
            cshPayorderSubExtJsonBo.setDrawCardId(accountCard.getLoanCardId());
            cshPayorderSubExtJsonBo.setDrawBankAccount(accountCard.getBankAccountName());
        } else if (Objects.nonNull(contractReceiptInfoVOMap)) {
            //旧订单，走原有引荐商配置在客户中的的银行卡id
            StoreContractReceiptInfoVO storeContractRecommend = contractReceiptInfoVOMap.get(orderPO.getRecommendStoreId());
            cshPayorderSubExtJsonBo.setBusiReceiveAccount(storeContractRecommend.getStore().getAcctId());

            cshPayorderSubExtJsonBo.setDrawCardNo(recommendStoreInfo.getDrawCardNo());
            cshPayorderSubExtJsonBo.setDrawBankCode(recommendStoreInfo.getDrawBankCode());
            cshPayorderSubExtJsonBo.setDrawBankName(recommendStoreInfo.getDrawBankName());
            cshPayorderSubExtJsonBo.setDrawCardId(recommendStoreInfo.getDrawCardId());
            cshPayorderSubExtJsonBo.setDrawBankAccount(recommendStoreInfo.getDrawBankAccount());
        }
        cshPayorderSubExtJsonBo.setOpeningBank(recommendStoreInfo.getDrawBankName());
        cshPayorderSubExtJsonBo.setOrgCode(orgCode);
        cshPayorderSubExtJsonBo.setSellerId(merchantBaseInfo.getSellerId());
        //金融规则信息
        ProductPriceVO finance = orderLocalUtils.getProductPrice(productPOS.get(0).getProductId(), orderPO.getAreaCode(),
                orderPO.getFinanceRuleCode());
        if (Objects.nonNull(finance) && Objects.nonNull(finance.getProductFinanceRuleLabel())) {
            ProductFinanceRuleLabel productFinanceRuleLabel = finance.getProductFinanceRuleLabel();
            if (productFinanceRuleLabel.getInterestWay().equals(InterestType.N_DAYS_AFTER_PAY.getValue())) {
                cshPayorderSubExtJsonBo.setInterestType(InterestType.N_DAYS_AFTER_PAY.getValue());
                cshPayorderSubExtJsonBo.setPlanInterestStartDays(productFinanceRuleLabel.getPlanInterestStartDays());
            } else {
                cshPayorderSubExtJsonBo.setInterestType(productFinanceRuleLabel.getInterestWay());
                cshPayorderSubExtJsonBo.setPlanLoanDate(productFinanceRuleLabel.getPlanLoanDate());
            }
            cshPayorderSubExtJsonBo.setInterestStartType(productFinanceRuleLabel.getInterestStartType());
            cshPayorderSubExtJsonBo.setInterestStartDays(productFinanceRuleLabel.getInterestStartDays());
            cshPayorderSubExtJsonBo.setInterestStartDate(productFinanceRuleLabel.getEmployeeInterestStartDate());
            cshPayorderSubExtJsonBo.setCouponBatch(productFinanceRuleLabel.getCouponBatch());
            cshPayorderSubExtJsonBo.setFinanceRuleCode(productFinanceRuleLabel.getFinanceRuleCode());
            cshPayorderSubExtJsonBo.setFinanceRuleName(productFinanceRuleLabel.getRuleName());
        }
        OrderExtendPO orderExtendPO = orderExtendMap.get(orderPO.getOrderSn());
        cshPayorderSubExtJsonBo.setStoreAreaCode(orderExtendPO.getStoreAreaCode());
        cshPayorderSubExtJsonBo.setStoreAreaName(orderExtendPO.getStoreAreaName());
        cshPayorderSubExtJsonBo.setPlatformServiceRateFlag(orderExtendPO.getPlatformServiceRateFlag());
        //判断是否首单用户
        Boolean firstLoanOrderByUserNo = iBzOldUserPoolService.isFirstLoanOrderByUserNo(orderPO.getUserNo());
        cshPayorderSubExtJsonBo.setIsFirstLoanOrder(firstLoanOrderByUserNo);
        SubOrderExt subOrderExt = new SubOrderExt();
        subOrderExt.setPayWay(PayWayCasierEnum.ENJOY_PAY);
        subOrderExt.setExtAttrs(cshPayorderSubExtJsonBo);

        subOrderExtList.add(subOrderExt);
    }

    private List<OrderProductPO> buildOrderProductList(Map<String, List<OrderProductPO>> orderProductList, Map<Long, Product> longProductMap, OrderPO orderPO, OrderSub orderSub) {
        List<OrderProductPO> productPOS = orderProductList.get(orderPO.getOrderSn());
        List<OrderProduct> orderProductLists = new ArrayList<>();
        for (OrderProductPO productPO : productPOS) {
            OrderProduct orderProduct = new OrderProduct();
            orderProduct.setGoodsId(productPO.getGoodsId().toString());
            orderProduct.setGoodsName(productPO.getGoodsName());
            orderProduct.setProductId(productPO.getProductId().toString());
            orderProduct.setProductNum(productPO.getProductNum());
            orderProduct.setAmount(productPO.getMoneyAmount());

            List<String> strings = Arrays.asList(productPO.getGoodsCategoryPath().split("->"));
            orderProduct.setFirstItemCategoryName(!strings.isEmpty() ? strings.get(0) : "");
            orderProduct.setSecondItemCategoryName(strings.size() > 1 ? strings.get(1) : "");
            orderProduct.setThreeItemCategoryName(strings.size() > 2 ? strings.get(2) : "");
            //处理类目id
            if (longProductMap.containsKey(productPO.getProductId())) {
                Product product = longProductMap.get(productPO.getProductId());
                orderProduct.setFirstItemCategoryId(product.getCategoryId1().toString());
                orderProduct.setSecondItemCategoryId(product.getCategoryId2().toString());
                orderProduct.setThreeItemCategoryId(product.getCategoryId3().toString());
            }
            orderProduct.setItemCategoryId(productPO.getGoodsCategoryId().toString());
            orderProduct.setItemCategoryTree(productPO.getGoodsCategoryPath());
            orderProductLists.add(orderProduct);
        }
        orderSub.setOrderProductList(orderProductLists);
        return productPOS;
    }

    private void buildReceiveAddress(OrderSub orderSub, MerchantBaseVo merchantBaseInfo) {
        CshPayOrderSubRegisterAddressJsonBo addresJsonBo = new CshPayOrderSubRegisterAddressJsonBo();
        addresJsonBo.setProvinceId(merchantBaseInfo.getProvince());
        addresJsonBo.setCityId(merchantBaseInfo.getCity());
        addresJsonBo.setCountyId(merchantBaseInfo.getCounty());
        addresJsonBo.setTownId(merchantBaseInfo.getTown());
        addresJsonBo.setTown(merchantBaseInfo.getTown());
        addresJsonBo.setTownFlag(ValidUtils.isEmpty(merchantBaseInfo.getTown()) ? 0 : 1);
        addresJsonBo.setAddress(merchantBaseInfo.getAddress());
        orderSub.setReceiveAddress(addresJsonBo);
    }

    private OrderMerchant buildOrderMerchant(OrderPO orderPO, OrderSub orderSub, MerchantBaseVo merchantBaseInfo, String orgCode) {
        //根据店铺查询商家信息
        OrderMerchant orderMerchant = new OrderMerchant();

        orderMerchant.setOrgCode(orgCode);
        orderMerchant.setRcmdMerchant(orderPO.getRecommendStoreId().toString());
        orderMerchant.setMerchantId(orderPO.getStoreId().toString());
        orderMerchant.setVirtualAcctNo(merchantBaseInfo.getVirtualAcctNo());
        orderMerchant.setMerchantName(merchantBaseInfo.getMerchantName());
        orderMerchant.setMerchantType(merchantBaseInfo.getMerchantType());
        orderMerchant.setMerchantMobile(merchantBaseInfo.getMerchantMobile());
        orderMerchant.setOwnStore(orderPO.getStoreIsSelf());
        orderMerchant.setCompanySealCode(merchantBaseInfo.getCompanySealCode());
        orderMerchant.setCorporateName(merchantBaseInfo.getCorporateName());
        orderMerchant.setJoinDate(StringUtils.isEmpty(merchantBaseInfo.getJoinDate()) ? null : DateUtil.toLocalDateTime(DateUtil.parseDate(merchantBaseInfo.getJoinDate())));
        orderMerchant.setEndDate(StringUtils.isEmpty(merchantBaseInfo.getEndDate()) ? null : DateUtil.toLocalDateTime(DateUtil.parseDate(merchantBaseInfo.getEndDate())));
        orderSub.setOrderMerchant(orderMerchant);
        return orderMerchant;
    }

    private void buildMainExtList(OrderExtendPO orderExtendPO, PushOrderRequest pushOrderRequest, OrderPO orderPO) {
        CshPayorderMainExtJsonBo cshPayorderMainExtJsonBo = new CshPayorderMainExtJsonBo();

        cshPayorderMainExtJsonBo.setOrderPatternCode(orderPO.getOrderPattern());
        OrderPatternEnum orderPatternEnum = OrderPatternEnum.valueOf(orderPO.getOrderPattern());
        cshPayorderMainExtJsonBo.setOrderPatternDesc(orderPatternEnum == null ? null : orderPatternEnum.getDesc());
        cshPayorderMainExtJsonBo.setBranchCode(orderExtendPO.getBranch());
        cshPayorderMainExtJsonBo.setBranchName(orderExtendPO.getBranchName());
        cshPayorderMainExtJsonBo.setAreaCode(orderExtendPO.getAreaCode());
        cshPayorderMainExtJsonBo.setAreaName(orderExtendPO.getAreaName());

        MainOrderExt mainOrderExt = new MainOrderExt();
        mainOrderExt.setPayWay(PayWayCasierEnum.ENJOY_PAY);
        mainOrderExt.setExtAttrs(cshPayorderMainExtJsonBo);

        pushOrderRequest.setMianExtList(Collections.singletonList(mainOrderExt));
    }

    private void buildOrderPhaseList(String paySn, String returnPage, List<OrderPayRecordPO> orderPayRecordPOS, OrderPO orderPoTmp, PushOrderRequest pushOrderRequest) {
        if (CollectionUtils.isNotEmpty(orderPayRecordPOS)) {
            List<OrderPhase> orderPhases = new ArrayList<>();
            for (OrderPayRecordPO orderPayRecordPO : orderPayRecordPOS) {
                OrderPhase orderPhase = new OrderPhase();
                orderPhase.setPhaseOrderNo(orderPayRecordPO.getPayNo());
                orderPhase.setPayAmt(orderPayRecordPO.getAmount());
                orderPhase.setReturnUrl(returnPage);
                orderPhase.setNotifyUrl(notifyUrl.concat("?payMethod=COMBINATION_PAY&payNo=" + orderPayRecordPO.getPayNo()
                        + "&paySn=" + paySn
                        + "&orderSn=" + orderPoTmp.getOrderSn()));
                orderPhase.setLoanNotifyUrl(loanNotifyUrl);
                orderPhase.setSequence(orderPayRecordPO.getPayOrder());

                if (orderPayRecordPO.getPayOrder().equals(PresellCapitalTypeEnum.BALANCE.getValue())) {
                    BigDecimal subsidyAmount = this.getSubsidyAmount(orderPoTmp, orderPayRecordPO);
                    orderPhase.setSubsidyAmount(subsidyAmount);
                }
                OrderPresellPO presellPO = orderPresellService.lambdaQuery()
                        .eq(OrderPresellPO::getPayNo, orderPayRecordPO.getPayNo())
                        .last("limit 1").one();
                if (Objects.nonNull(presellPO)) {
                    orderPhase.setOrderPhasePeriod(presellPO.getDeadTime());
                }

                orderPhases.add(orderPhase);
            }
            pushOrderRequest.setOrderPhaseList(orderPhases);
        }
    }

    /**
     * 预付单定金不能使用优惠券，不存在平台优惠券补差。预付单为尾款时，将订单维度上的补差金额全量补差（补差没有比例限制）
     *
     * @param orderPoTmp
     * @param orderPayRecordPO
     * @return
     */
    private BigDecimal getSubsidyAmount(OrderPO orderPoTmp, OrderPayRecordPO orderPayRecordPO) {
        String discountDetail = orderPoTmp.getActivityDiscountDetail();
        if (!StringUtils.isEmpty(discountDetail)) {
            List<OrderSubmitDTO.PromotionInfo> promotionInfos = JSON.parseArray(discountDetail, OrderSubmitDTO.PromotionInfo.class);
            if (CollectionUtils.isNotEmpty(promotionInfos)) {
                for (OrderSubmitDTO.PromotionInfo promotionInfo : promotionInfos) {
                    // 平台优惠券 补差金额
                    if (promotionInfo.getPromotionType() != null
                            && promotionInfo.getPromotionType() == PromotionConst.PROMOTION_TYPE_402
                            && Boolean.TRUE.equals(!promotionInfo.getIsStore())) {
                        // 补差金额，默认
                        BigDecimal subsidyAmount = promotionInfo.getDiscount();
                        if (OrderTypeEnum.isPresell(orderPoTmp.getOrderType())) {
                            //重置
                            subsidyAmount = BigDecimal.ZERO;
                            OrderPresellPO orderPresell = orderPresellService.queryByPayNo(orderPayRecordPO.getPayNo());
                            log.info("预付微信补差金额，paySn{} orderType:{} subsidyAmount：{} orderPresell:{}", orderPoTmp.getPaySn(), orderPoTmp.getOrderType(), subsidyAmount, orderPresell);
                            if (PresellCapitalTypeEnum.BALANCE.getValue().equals(orderPresell.getType())) {
                                // 补差金额，默认
                                subsidyAmount = promotionInfo.getDiscount();
                                log.info("预付微信补差金额，paySn{} orderType:{} subsidyAmount：{}", orderPoTmp.getPaySn(), orderPoTmp.getOrderType(), subsidyAmount);
                            }
                        }
                        log.info("最终微信补差金额，paySn{} orderType:{} subsidyAmount：{}", orderPoTmp.getPaySn(), orderPoTmp.getOrderType(), subsidyAmount);
                        return subsidyAmount;
                    }
                }
            }
        }
        return BigDecimal.ZERO;
    }

    private void buildPushOrderRequestMainInfo(String paySn, String returnPage, OrderPayPO orderPayPO, PushOrderRequest pushOrderRequest,Integer minutes) {
        // 支付倒计时
//        String value = stringRedisTemplate.opsForValue().get("time_limit_of_auto_cancle_order");
//        int autoCancelDay = value == null ? 24 : Integer.parseInt(value);
//        Date autoCancelTime = TimeUtil.getHourAgoDate(orderPayPO.getCreateTime(), autoCancelDay);

        pushOrderRequest.setOrderNo(paySn);
        pushOrderRequest.setOrderAmt(orderPayPO.getPayAmount());
        pushOrderRequest.setOrderCreateTime(DateUtil.toLocalDateTime(orderPayPO.getCreateTime()));
        pushOrderRequest.setBusiSystemCode(CommonConst.MALL_ORDER);
        pushOrderRequest.setDescription(CommonConst.MALL_ORDER + paySn);
        pushOrderRequest.setPayAmt(orderPayPO.getPayAmount());
        pushOrderRequest.setReturnUrl(returnPage);
        pushOrderRequest.setNotifyUrl(notifyUrl);
        pushOrderRequest.setLoanNotifyUrl(loanNotifyUrl);
        LocalDateTime createLocalDateTime = DateUtil.toLocalDateTime(orderPayPO.getCreateTime());
        pushOrderRequest.setOrderPeriod(createLocalDateTime.plusMinutes(minutes));
        pushOrderRequest.setExpirationTime(24 * 60);
    }

    private boolean buildOrderPlayerList(List<OrderPO> orderPOList,
                                         String custId, OrderExtendPO orderExtendPO,
                                         PushOrderRequest pushOrderRequest,String mobile) {

        List<String> loanPlayerList = orderPOList.stream().map(x -> x.getLoanPayer()).collect(Collectors.toList());

        //用呗
        OrderPlayer orderPlayer = new OrderPlayer();
        orderPlayer.setCustId(custId);

        /**
         * 站长代客下单，贷款额度选择
         */
        if(CollectionUtils.isNotEmpty(loanPlayerList) && loanPlayerList.contains(LoanPayerEnum.STATION_MASTER.getCode())) {
            log.info("使用站长额度支付");
            if(StringUtils.isEmpty(orderExtendPO.getStationMaster())) {
                log.warn("站长代客订单，站长信息为空");
                return false;
            }
            try {
                //查询客户中心站长信息
                UserBaseInfoVo userBaseInfoVo = customerIntegration.userBaseInfo(orderExtendPO.getStationMaster());

                orderPlayer.setPlayer(userBaseInfoVo.getCustInfoVo().getCustDetail().getLoanCustId());
                orderPlayer.setName(userBaseInfoVo.getCustInfoVo().getCustName());
                //C开头的贷款用户编号
                orderPlayer.setAccount(orderExtendPO.getStationMaster());
                orderPlayer.setCustId(userBaseInfoVo.getCustInfoVo().getId().toString());
            } catch (Exception e) {
                log.warn("客户中心，获取站长信息为空");
            }


        } else {
            orderPlayer.setPlayer(orderExtendPO.getCustomerId());
            orderPlayer.setName(orderExtendPO.getCustomerName());
            //C开头的贷款用户编号
            orderPlayer.setAccount(orderPOList.get(0).getUserNo());

        }

        orderPlayer.setPayWay(PayWayCasierEnum.ENJOY_PAY);
        orderPlayer.setLoanManager(orderExtendPO.getManager());
        orderPlayer.setMobile(mobile);

        //微信
        //OrderPlayer orderPlayerWx = new OrderPlayer();
        //orderPlayerWx.setPlayer(payOrderParam.getOpenId());
        //orderPlayerWx.setName(payOrderParam.getPayerName());
        //orderPlayerWx.setPayWay(PayWayCasierEnum.WXPAY);
        //orderPlayerList.add(orderPlayerWx);

        pushOrderRequest.setOrderPlayerList(orderPlayer);
        return true;
    }
}
