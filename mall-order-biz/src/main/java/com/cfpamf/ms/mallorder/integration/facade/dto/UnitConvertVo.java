package com.cfpamf.ms.mallorder.integration.facade.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Program: wms-service
 * @Description: 单位换算对象
 * @author: LS
 * @create: 2022-04-20 10:27
 **/
@Data
public class UnitConvertVo {

    @ApiModelProperty("标识ID")
    private String tagId;

    @ApiModelProperty("批次号")
    private String batchNo;

    @ApiModelProperty("sku编码")
    private String skuId;

    @ApiModelProperty("skuUnit编码")
    private String skuUnitCode;

    @ApiModelProperty("数量")
    private BigDecimal packageNum;

    @ApiModelProperty("单位名称")
    private String unitName;

    @ApiModelProperty("价格")
    private BigDecimal price;

    @ApiModelProperty("单位编号")
    private String unitCode;
}
