//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//

package com.cfpamf.ms.mallorder.integration.facade;

import com.cfpamf.cmis.common.base.CommonResult;
import com.cfpamf.cmis.common.base.PageBean;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.bizconfig.facade.request.AddProductConfigRequest;
import com.cfpamf.ms.bizconfig.facade.request.AddProductGroupRequest;
import com.cfpamf.ms.bizconfig.facade.request.EditBranchProductEssentialRequest;
import com.cfpamf.ms.bizconfig.facade.request.ProductEssentialQueryRequet;
import com.cfpamf.ms.bizconfig.facade.request.ProductEssentialRequest;
import com.cfpamf.ms.bizconfig.facade.request.ProductManageRequest;
import com.cfpamf.ms.bizconfig.facade.request.ProductRequest;
import com.cfpamf.ms.bizconfig.facade.request.QueryLoanOptionsRequest;
import com.cfpamf.ms.bizconfig.facade.request.QueryProductGroupBranchRequest;
import com.cfpamf.ms.bizconfig.facade.request.RepaymentCreditQueryRequest;
import com.cfpamf.ms.bizconfig.facade.request.SetRepaymentCreditBranchRequest;
import com.cfpamf.ms.bizconfig.facade.request.SpecialCaseManageRequest;
import com.cfpamf.ms.bizconfig.facade.request.SpecialCaseMatchingRequest;
import com.cfpamf.ms.bizconfig.facade.request.SpecialQueryRequest;
import com.cfpamf.ms.bizconfig.facade.request.UpdateProductGroupBranchRequest;
import com.cfpamf.ms.bizconfig.facade.vo.LoanOptionTemporaryVo;
import com.cfpamf.ms.bizconfig.facade.vo.PLoanMTDVo;
import com.cfpamf.ms.bizconfig.facade.vo.PLoanTypeVo;
import com.cfpamf.ms.bizconfig.facade.vo.increaselimit.BaseIncreaseLimitBranchVo;
import com.cfpamf.ms.bizconfig.facade.vo.product.ApplyBranchVo;
import com.cfpamf.ms.bizconfig.facade.vo.product.ChannelRepaymentTemporaryVo;
import com.cfpamf.ms.bizconfig.facade.vo.product.LoanOptionsVo;
import com.cfpamf.ms.bizconfig.facade.vo.product.ProductConfigInfoVo;
import com.cfpamf.ms.bizconfig.facade.vo.product.ProductGroupBranchVo;
import com.cfpamf.ms.bizconfig.facade.vo.product.ProductGroupVo;
import com.cfpamf.ms.bizconfig.facade.vo.product.ProductInfoVo;
import com.cfpamf.ms.bizconfig.facade.vo.product.ProductSpecialVo;
import com.cfpamf.ms.bizconfig.facade.vo.product.ProductVo;
import com.cfpamf.ms.bizconfig.facade.vo.product.RepaymentConfigVo;
import com.cfpamf.ms.bizconfig.facade.vo.product.RepaymentRelationVo;
import com.cfpamf.ms.bizconfig.facade.vo.product.SingleRuleVo;
import com.cfpamf.ms.bizconfig.facade.vo.product.creditRepayment.RepaymentCreditBranchTrailVo;
import com.cfpamf.ms.bizconfig.facade.vo.product.creditRepayment.RepaymentCreditBranchVo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import java.math.BigDecimal;
import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(
    value = "ms-service-bizconfig",
    url = "${ms-bizconfig-service.url}"
)
@RequestMapping({"/bizconfig"})
public interface LoanProductFacade {
    @GetMapping({"/product/getPloanTypeList"})
    CommonResult<List<PLoanTypeVo>> getPloanTypeList() throws Exception;

    @GetMapping({"/product/getLoanMTDList"})
    CommonResult<List<PLoanMTDVo>> getLoanMTDList() throws Exception;

    @GetMapping({"/product/getLoanMTDListByTypeSeqAndTnr"})
    CommonResult<List<PLoanMTDVo>> getPloanMTDListByLoanTypeAndTnr(@RequestParam("typeSeq") int var1, @RequestParam("tnr") int var2);

    @GetMapping({"/product/getTrnByPloanType"})
    CommonResult<List<Integer>> getTrnByPloanType(@RequestParam("typeSeq") int var1);

    @PostMapping({"/product/getPloanTypeListByQuery"})
    CommonResult<List<PLoanTypeVo>> getPloanTypeListByQuery(@RequestBody ProductRequest var1);

    @GetMapping({"/product/getProductByProductId"})
    CommonResult<ProductVo> getProductByProductId(@RequestParam("productId") Integer var1) throws Exception;

    @GetMapping({"/product/getProductList"})
    CommonResult<List<ProductVo>> getProductList() throws Exception;

    @GetMapping({"/product/getProductListV2"})
    CommonResult<List<ProductVo>> getProductListV2() throws Exception;

    @GetMapping({"/product/getBaseRepaymentList"})
    CommonResult<List<RepaymentRelationVo>> getBaseRepaymentList() throws Exception;

    @GetMapping({"/product/getBaseRepaymentInfoByRepaymentCode"})
    CommonResult<RepaymentRelationVo> getBaseRepaymentInfoByRepaymentCode(@RequestParam("repaymentCode") String var1) throws Exception;

    @ApiOperation(
        value = "查询还款方式信息",
        tags = {"产品业务相关-新"}
    )
    @ApiImplicitParams({@ApiImplicitParam(
    paramType = "query",
    name = "repaymentCode",
    value = "还款方式编号",
    dataType = "string",
    required = true
)})
    @GetMapping({"/product/getRepaymentListByRepmentCode"})
    CommonResult<List<RepaymentRelationVo>> getRepaymentListByRepmentCode(@RequestParam("repaymentCode") String var1) throws Exception;

    @GetMapping({"/product/getRepaymentRelation"})
    CommonResult<RepaymentRelationVo> getRepaymentRelation(@RequestParam("repaymentCode") String var1, @RequestParam("tnr") Integer var2) throws Exception;

    @PostMapping({"/product/getProductListByQuery"})
    CommonResult<List<ProductVo>> getProductListByQuery(@RequestBody ProductRequest var1);

    @GetMapping({"/product/getRepaymentListByQuery"})
    CommonResult<List<RepaymentRelationVo>> getRepaymentListByQuery(@RequestParam("productCode") String var1, @RequestParam("tnr") Integer var2, @RequestParam("branchCode") String var3);

    @GetMapping({"/product/getTrnByProductBranch"})
    CommonResult<List<Integer>> getTrnByProductBranch(@RequestParam("productCode") String var1, @RequestParam("branchCode") String var2);

    @GetMapping({"/product/getSpecialCase"})
    CommonResult<ProductSpecialVo> getSpecialCase(@RequestParam("productCode") String var1, @RequestParam("relationCode") String var2, @RequestParam("branchCode") String var3, @RequestParam("poorFlag") String var4, @RequestParam("isNew") String var5, @RequestParam("isFeeGuarantee") String var6, @RequestParam("amount") BigDecimal var7);

    @PostMapping({"/product/specialCaseMatching"})
    Result<ProductSpecialVo> specialCaseMatching(@RequestBody SpecialCaseMatchingRequest var1);

    @PostMapping({"/product/manage/insertProductEssential"})
    Result<String> insertProductEssential(@RequestBody ProductEssentialRequest var1);

    @PostMapping({"/product/manage/updateProductEssential"})
    Result updateProductEssential(@RequestBody ProductEssentialRequest var1);

    @PostMapping({"/product/manage/queryProductEssential"})
    Result<PageBean<RepaymentRelationVo>> queryProductEssential(@RequestBody ProductEssentialQueryRequet var1);

    @GetMapping({"/product/manage/getProductEssentialInfo"})
    Result<RepaymentRelationVo> getProductEssentialInfo(@RequestParam("relationCode") String var1);

    @GetMapping({"/product/manage/queryProductEssentialBranch"})
    Result<List<ApplyBranchVo>> queryProductEssentialBranch(@RequestParam("relationCode") String var1, @RequestParam("branchCode") String var2);

    @PostMapping({"/product/manage/editBranchProductEssential"})
    Result editBranchProductEssential(@RequestBody EditBranchProductEssentialRequest var1);

    @GetMapping({"/product/manage/queryBranchProductEssential"})
    Result<List<RepaymentRelationVo>> queryBranchProductEssential(@RequestParam("branchCode") String var1, @RequestParam("relationCode") String var2);

    @PostMapping({"/product/manage/insertProduct"})
    Result insertProduct(@RequestBody ProductManageRequest var1);

    @PostMapping({"/product/manage/updateProduct"})
    Result updateProduct(@RequestBody ProductManageRequest var1);

    @PostMapping({"/product/manage/queryProductList"})
    Result<List<ProductVo>> queryProductList(@RequestBody ProductRequest var1);

    @GetMapping({"/product/manage/getProductInfo"})
    Result<ProductVo> getProductInfo(@RequestParam("productCode") String var1);

    @GetMapping({"/product/manage/queryProductBranch"})
    Result<List<ApplyBranchVo>> queryProductBranch(@RequestParam("productCode") String var1, @RequestParam("branchCode") String var2);

    @PostMapping({"/product/manage/insertSpecial"})
    Result<String> insertSpecial(@RequestBody SpecialCaseManageRequest var1);

    @PostMapping({"/product/manage/updateSpecial"})
    Result updateSpecial(@RequestBody SpecialCaseManageRequest var1);

    @PostMapping({"/product/manage/querySpecialList"})
    Result<List<ProductSpecialVo>> querySpecialList(@RequestBody SpecialQueryRequest var1);

    @GetMapping({"/product/manage/getSpecialInfo"})
    Result<ProductSpecialVo> getSpecialInfo(@RequestParam("specialCode") String var1);

    @GetMapping({"/product/manage/getRuleTypeList"})
    Result<List<SingleRuleVo>> getRuleTypeList();

    @GetMapping({"/product/manage/querySpecialProduct"})
    Result<List<ProductVo>> querySpecialProduct(@RequestParam("specialCode") String var1, @RequestParam("keyword") String var2);

    @GetMapping({"/product/manage/querySpecialBranch"})
    Result<List<ApplyBranchVo>> querySpecialBranch(@RequestParam("specialCode") String var1, @RequestParam("branchCode") String var2);

    @PostMapping({"/product/manage/setRepaymentCreditBranch"})
    Result<Void> setRepaymentCreditBranch(@RequestBody SetRepaymentCreditBranchRequest var1);

    @PostMapping({"/product/manage/getRepaymentCreditBranchQueryList"})
    Result<PageBean<RepaymentCreditBranchVo>> getRepaymentCreditBranchQueryList(@RequestBody RepaymentCreditQueryRequest var1);

    @GetMapping({"/product/manage/getRepaymentCreditBranchList"})
    Result<List<RepaymentRelationVo>> getRepaymentCreditBranchList(@RequestParam("branchId") String var1);

    @GetMapping({"/product/manage/getRepaymentRelationBranchDetail"})
    Result<List<RepaymentRelationVo>> getRepaymentRelationBranchDetail(@RequestParam("branchId") String var1, @RequestParam("searchText") String var2);

    @GetMapping({"/product/manage/getRepaymentCreditBranchTrail"})
    Result<List<RepaymentCreditBranchTrailVo>> getRepaymentCreditBranchTrail(@RequestParam("branchId") String var1);

    @GetMapping({"/product/manage/getIncreaseLimitBranch"})
    Result<BaseIncreaseLimitBranchVo> getIncreaseLimitBranch(@RequestParam("branchId") String var1);

    @GetMapping({"/product/manage/getBaseIncreaseLimitMax"})
    Result<BaseIncreaseLimitBranchVo> getBaseIncreaseLimitMax();

    @GetMapping({"/product/manage/syncCreditConfig"})
    Result<Void> syncCreditConfig(String var1);

    @GetMapping({"/product/queryChannelRepayment"})
    CommonResult<List<ChannelRepaymentTemporaryVo>> queryChannelRepayment(@RequestParam("channelCode") String var1);

    @PostMapping({"/product/getLoanOption"})
    CommonResult<LoanOptionsVo> getLoanOption(@RequestBody QueryLoanOptionsRequest var1);

    @PostMapping({"/product/getLoanOptionV2"})
    CommonResult<LoanOptionsVo> getLoanOptionV2(@RequestBody QueryLoanOptionsRequest var1);

    @PostMapping({"/product/getLoanOptions"})
    Result<List<LoanOptionTemporaryVo>> getLoanOptions(@RequestBody QueryLoanOptionsRequest var1);

    @PostMapping({"/product/getLoanOptionsV2"})
    Result<List<LoanOptionTemporaryVo>> getLoanOptionsV2(@RequestBody QueryLoanOptionsRequest var1);

    @PostMapping({"/product/insertProductGroup"})
    CommonResult<Void> insertProductGroup(@RequestBody AddProductGroupRequest var1);

    @PostMapping({"/product/updateProductGroup"})
    CommonResult<Void> updateProductGroup(@RequestBody AddProductGroupRequest var1);

    @GetMapping({"/product/getProductGroupList"})
    CommonResult<PageBean<ProductGroupVo>> getProductGroupList(@RequestParam("pageIndex") Integer var1, @RequestParam("pageSize") Integer var2);

    @GetMapping({"/product/getProductListConfig"})
    CommonResult<List<ProductInfoVo>> getProductListConfig();

    @PostMapping({"/product/updateProductGroupBranch"})
    CommonResult<Void> updateProductGroupBranch(@RequestBody UpdateProductGroupBranchRequest var1);

    @GetMapping({"/product/getProductGroupBranch"})
    CommonResult<PageBean<ProductGroupBranchVo>> getProductGroupBranch(QueryProductGroupBranchRequest var1);

    @GetMapping({"/product/getProductGroupInfo"})
    CommonResult<ProductGroupVo> getProductGroupInfo(@RequestParam("groupOid") String var1);

    @PostMapping({"/product/insertProductConfig"})
    CommonResult<Void> insertProductConfig(@RequestBody AddProductConfigRequest var1);

    @GetMapping({"/product/getProductConfigInfo"})
    CommonResult<ProductConfigInfoVo> getProductConfigInfo(@RequestParam("groupOid") String var1, @RequestParam("productCode") String var2, @RequestParam("guaranteeCode") String var3);

    @GetMapping({"/product/getRepaymentListConfig"})
    CommonResult<List<RepaymentConfigVo>> getRepaymentListConfig();
}
