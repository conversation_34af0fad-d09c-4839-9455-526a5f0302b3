package com.cfpamf.ms.mallorder.scheduler;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cfpamf.ares.aftersale.domain.vo.AfterSaleCreateVO;
import com.cfpamf.athena.common.domain.dto.ResultEntity;
import com.cfpamf.ms.mallorder.common.enums.aresAfterSale.OrderReturnStatusEnum;
import com.cfpamf.ms.mallorder.common.util.DateUtil;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.OrderSync2TradeDTO;
import com.cfpamf.ms.mallorder.enums.OrderEventEnum;
import com.cfpamf.ms.mallorder.integration.facade.AresAfterSaleFacade;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.service.IOrderAfterService;
import com.cfpamf.ms.mallorder.service.IOrderEventMsgService;
import com.cfpamf.ms.mallorder.service.IOrderReturnService;
import com.cfpamf.ms.mallorder.service.ITempOrderSyncFailService;
import com.slodon.bbc.core.exception.MallException;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 历史订单同步交易中心
 */
@Slf4j
@Component
public class OrderRefundHistorySync2TradeJob {
    private String NONE_VALUE = "-";
    private String startTimeStr = "2021-07-05 00:00:00";

    @Resource
    private AresAfterSaleFacade aresAfterSaleFacade;

    @Resource
    private IOrderReturnService orderReturnService;

    @Resource
    private OrderModel orderModel;

    @Resource
    private IOrderAfterService orderAfterService;

    @Resource
    private IOrderEventMsgService orderEventMsgService;

    @Resource
    private ITempOrderSyncFailService orderSyncFailService;


    @XxlJob("orderRefundHistorySync2TradeJob")
    public Boolean execute() {
        log.info("同步历史售后单开始 {}", new Date());
        String jobParam = XxlJobHelper.getJobParam();
        if (StringUtils.isNotBlank(jobParam)) {
            OrderSync2TradeDTO orderSync2TradeDTO = JSON.parseObject(jobParam, OrderSync2TradeDTO.class);
            Integer queryFlag = checkJobParam(orderSync2TradeDTO);
            LambdaQueryWrapper<OrderReturnPO> queryWrapper = Wrappers.lambdaQuery();
            if (queryFlag == 1) {
                queryWrapper.between(OrderReturnPO::getCreateTime, orderSync2TradeDTO.getStartTime(), orderSync2TradeDTO.getEndTime());
            }
            if (queryFlag == 2) {
                queryWrapper.in(OrderReturnPO::getOrderSn, orderSync2TradeDTO.getOrderSnList());
            }
            queryWrapper.eq(OrderReturnPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
            queryWrapper.orderBy(true,true, OrderReturnPO::getReturnId);
            for (int currentPage = 1;;currentPage++) {
                Page<OrderReturnPO> page = new Page<>(currentPage, 100);
                IPage<OrderReturnPO> pageResult = orderReturnService.page(page, queryWrapper);
                List<OrderReturnPO> list = pageResult.getRecords();
                if (CollectionUtils.isEmpty(list)) {
                    break;
                }
                List<String> orderSnList = list.stream().map(OrderReturnPO::getOrderSn).distinct().collect(Collectors.toList());
                Map<String, OrderPO> orderPOMap = orderModel.getOrderByOrderSnList(orderSnList).stream().collect(Collectors.toMap(OrderPO::getOrderSn, v -> v, (v1, v2) -> v1));
                List<String> afsSnList = list.stream().map(OrderReturnPO::getAfsSn).distinct().collect(Collectors.toList());
                Map<String, OrderAfterPO> orderAfterPOMap = orderAfterService.getByAfsSnList(afsSnList).stream().collect(Collectors.toMap(OrderAfterPO::getAfsSn, v -> v, (v1, v2) -> v1));
                for (OrderReturnPO orderReturnPO : list) {
                    OrderPO orderPO = orderPOMap.getOrDefault(orderReturnPO.getOrderSn(), null);
                    OrderAfterPO orderAfterPO = orderAfterPOMap.getOrDefault(orderReturnPO.getAfsSn(), null);
                    doExecuteRefundSync(orderPO, orderReturnPO, orderAfterPO);
                }
            }
        } else {
            Date start = DateUtil.parse(startTimeStr, DateUtil.FORMAT_TIME);
            Date end = new Date();
            for (Date t = start; t.before(end); t = DateUtil.addDays(t, 10)) {
                Date endTime = DateUtil.addDays(t, 10);
                LambdaQueryWrapper<OrderReturnPO> queryWrapper = Wrappers.lambdaQuery();
                queryWrapper.between(OrderReturnPO::getCreateTime, t, endTime);
                queryWrapper.eq(OrderReturnPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
                queryWrapper.orderBy(true,true, OrderReturnPO::getReturnId);
                List<OrderReturnPO> list = orderReturnService.list(queryWrapper);
                List<String> orderSnList = list.stream().map(OrderReturnPO::getOrderSn).distinct().collect(Collectors.toList());
                Map<String, OrderPO> orderPOMap = orderModel.getOrderByOrderSnList(orderSnList).stream().collect(Collectors.toMap(OrderPO::getOrderSn, v -> v, (v1, v2) -> v1));
                List<String> afsSnList = list.stream().map(OrderReturnPO::getAfsSn).distinct().collect(Collectors.toList());
                Map<String, OrderAfterPO> orderAfterPOMap = orderAfterService.getByAfsSnList(afsSnList).stream().collect(Collectors.toMap(OrderAfterPO::getAfsSn, v -> v, (v1, v2) -> v1));
                for (OrderReturnPO orderReturnPO : list) {
                    OrderPO orderPO = orderPOMap.getOrDefault(orderReturnPO.getOrderSn(), null);
                    OrderAfterPO orderAfterPO = orderAfterPOMap.getOrDefault(orderReturnPO.getAfsSn(), null);
                    doExecuteRefundSync(orderPO, orderReturnPO, orderAfterPO);
                }
            }
        }

        log.info("同步历史订单结束 {}", new Date());
        return XxlJobHelper.handleSuccess();
    }

    public void doExecuteRefundSync(OrderPO orderPO, OrderReturnPO orderReturnPO, OrderAfterPO orderAfterPO) {
        try {
            OrderEventEnum orderEventEnum = getByOrderRefundState(orderReturnPO.getState());
            ResultEntity<AfterSaleCreateVO> createResult = aresAfterSaleFacade.create(orderEventMsgService.buildAfterSaleCreateDTO(orderPO, orderReturnPO, orderAfterPO));
            if (!createResult.isSuccess()) {
                log.info("同步售后单失败 {} {}", orderReturnPO.getAfsSn(), createResult.getMsg());
                throw new MallException("同步售后单失败：" + createResult.getMsg());
            }
            if (OrderEventEnum.REFUND.equals(orderEventEnum)) {
                ResultEntity<Boolean> notifyResult = aresAfterSaleFacade.refundNotify(orderEventMsgService.buildAfterSaleRefundNotifyDTO(orderPO, orderReturnPO));
                if (!notifyResult.isSuccess()) {
                    log.info("售后结果通知失败 {} {}", orderReturnPO.getAfsSn(), notifyResult.getMsg());
                    throw new MallException("售后结果通知失败：" + notifyResult.getMsg());
                }
            }
        } catch (Exception e) {
            log.info("同步历史售后单失败 {}", orderReturnPO.getAfsSn(), e);
            OrderSyncFailPO orderSyncFailPO = new OrderSyncFailPO();
            orderSyncFailPO.setBizId(orderReturnPO.getAfsSn());
            orderSyncFailPO.setBizType(OrderConst.MQ_MESSAGE_TYPE_1);
            orderSyncFailPO.setSyncTime(LocalDateTime.now());
            orderSyncFailPO.setFailReason(e.getMessage());
            orderSyncFailService.save(orderSyncFailPO);
        }
    }

    private OrderEventEnum getByOrderRefundState(Integer orderRefundState) {
        if (OrderReturnStatusEnum.RETURN_STATE_400.getCode().equals(orderRefundState)) {
            return OrderEventEnum.REFUND;
        } else {
            return OrderEventEnum.REFUND_APPLY;
        }
    }

    private Integer checkJobParam(OrderSync2TradeDTO jobParam) {
        if (jobParam == null) {
            throw new MallException("参数异常");
        }
        //根据时间范围同步
        if (jobParam.getStartTime() != null && jobParam.getEndTime() != null) {
            return 1;
        }
        //指定订单号同步
        if (jobParam.getStartTime() == null && jobParam.getEndTime() == null && !CollectionUtils.isEmpty(jobParam.getOrderSnList())) {
            return 2;
        }
        throw new MallException("参数异常");
    }
}
