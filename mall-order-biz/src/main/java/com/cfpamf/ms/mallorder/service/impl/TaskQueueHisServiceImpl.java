package com.cfpamf.ms.mallorder.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cfpamf.ms.mallorder.mapper.TaskQueueHisMapper;
import com.cfpamf.ms.mallorder.mapper.TaskQueueLogMapper;
import com.cfpamf.ms.mallorder.po.TaskQueueHisPO;
import com.cfpamf.ms.mallorder.service.ITaskQueueHisService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 定时任务推送队列表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-01-08
 */
@Service
public class TaskQueueHisServiceImpl extends ServiceImpl<TaskQueueHisMapper, TaskQueueHisPO> implements ITaskQueueHisService {

    @Resource
    TaskQueueHisMapper taskQueueHisMapper;

    @Override
    public int modifyTaskQueueHisPO(Long bizId, Integer bizType) {
        return taskQueueHisMapper.modifyTaskQueueHis(bizId,bizType);
    }
}
