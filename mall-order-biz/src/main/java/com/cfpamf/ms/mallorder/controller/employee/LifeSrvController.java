package com.cfpamf.ms.mallorder.controller.employee;

import com.cfpamf.ms.mallorder.req.LifeSrvIncomeReportRequest;
import com.cfpamf.ms.mallorder.req.LifeSrvWineTastingBchDetailRequest;
import com.cfpamf.ms.mallorder.req.LifeSrvWineTastingReportRequest;
import com.cfpamf.ms.mallorder.service.LifeSrvInComeService;
import com.cfpamf.ms.mallorder.service.LifeSrvWineTastingService;
import com.cfpamf.ms.mallorder.vo.bean.OuterPageInfo;
import com.cfpamf.ms.mallorder.vo.pgrpt.LifesrvDepartmentProfitBasicVO;
import com.cfpamf.ms.mallorder.vo.pgrpt.LifesrvEmpProfitBasicVO;
import com.cfpamf.ms.mallorder.vo.pgrpt.LifesrvWineTastingBaseVO;
import com.cfpamf.ms.mallorder.vo.pgrpt.LifesrvWineTastingBchDetailBasicVO;
import com.github.pagehelper.PageInfo;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.PageVO;
import com.slodon.bbc.core.response.SldResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Api(tags = "生服牛工作台-生服数据板块")
@RestController
@RequestMapping("/lifesrv")
public class LifeSrvController {

	@Autowired
	private LifeSrvWineTastingService lifeSrvWineTastingService;

	@ApiOperation(value = "查询品酒会基础指标")
	@PostMapping("wineTasting/basic/metrics")
	public JsonResult<LifesrvWineTastingBaseVO> basicMetrics(
			@RequestBody @Valid LifeSrvWineTastingReportRequest request) {
		return SldResponse.success(lifeSrvWineTastingService.basicMetrics(request));
	}

	@ApiOperation(value = "查询品酒会活动明细")
	@PostMapping("wineTasting/bch/basic/metrics")
	public JsonResult<PageInfo<LifesrvWineTastingBchDetailBasicVO>> bchDetailBasicMetrics(
			@RequestBody @Valid LifeSrvWineTastingBchDetailRequest request) {
		return SldResponse.success(lifeSrvWineTastingService.bchDetailBasicMetrics(request));
	}
}
