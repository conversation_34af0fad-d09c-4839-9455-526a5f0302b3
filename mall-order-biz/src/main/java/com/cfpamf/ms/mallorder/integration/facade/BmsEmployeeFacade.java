package com.cfpamf.ms.mallorder.integration.facade;


import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.bms.facade.constants.BmsConstantCore;
import com.cfpamf.ms.bms.facade.vo.UserListVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "bms-service-biz", url = "${bms.api.url}")
public interface BmsEmployeeFacade {

    @ApiOperation(value = "根据工号查询督导下管护客户经理", notes = "/employee/listLoanOfficerUserListVOBySupervisor?jobNumber=xxx")
    @GetMapping(value = "/employee/listLoanOfficerUserListVOBySupervisor")
    Result<List<UserListVO>> listLoanOfficerUserListVOBySupervisor(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization, @RequestParam("jobNumber") String jobNumber);


    @ApiOperation("根据hrOrgId(机构编码)和 postId(岗位id) 获取该机构该岗位的所有员工信息(包括主职和兼职)")
    @GetMapping(value = "/user/listUserByHrOrgId")
    Result<List<UserListVO>> getUserByBranchCodeAndPostId(@RequestParam("hrOrgId") Integer hrOrgId,
                                                          @RequestParam("postId") Integer postId);
}
