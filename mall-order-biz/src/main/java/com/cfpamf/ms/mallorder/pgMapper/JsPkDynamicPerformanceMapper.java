package com.cfpamf.ms.mallorder.pgMapper;


import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.cfpamf.framework.autoconfigure.mybatis.MyBaseMapper;
import com.cfpamf.ms.mallorder.po.JsPkDynamicPerformancePO;
import com.cfpamf.ms.mallorder.po.OrderPresellPO;

/**
 * <p>
 * 销售业绩查询接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-23
 */
public interface JsPkDynamicPerformanceMapper  extends MyBaseMapper<OrderPresellPO> {

    List<JsPkDynamicPerformancePO> listByQuery(@Param("rptDateStart") Date rptDateStart, @Param("rptDateEnd")Date rptDateEnd);

}
