package com.cfpamf.ms.mallorder.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cfpamf.framework.autoconfigure.common.exception.MSException;
import com.cfpamf.framework.autoconfigure.mybatis.BaseRepoServiceImpl;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.cfpamf.ms.mallorder.mapper.OrderPriceRecordMapper;
import com.cfpamf.ms.mallorder.po.OrderPriceRecordPO;
import com.cfpamf.ms.mallorder.service.IOrderPriceRecordService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 订单改价记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-22
 */
@Service
public class OrderPriceRecordServiceImpl extends BaseRepoServiceImpl<OrderPriceRecordMapper, OrderPriceRecordPO> implements IOrderPriceRecordService {

    @Override
    public List<OrderPriceRecordPO> listPriceRecord(String orderSn) {
        if (StringUtils.isBlank(orderSn)) {
            throw new MSException(String.valueOf(ErrorCodeEnum.U.EMPTY_PARAM.getCode()),"列表查询订单所有的商品改价记录入参不能为空");
        }
        LambdaQueryWrapper<OrderPriceRecordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderPriceRecordPO::getOrderSn,orderSn);
        return this.list(queryWrapper);
    }
}
