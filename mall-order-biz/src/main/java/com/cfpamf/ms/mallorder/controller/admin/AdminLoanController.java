package com.cfpamf.ms.mallorder.controller.admin;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cfpamf.ms.mallorder.dto.agricorder.LoanListResultQueryDTO;
import com.cfpamf.ms.mallorder.req.admin.LoanListResultRequest;
import com.cfpamf.ms.mallorder.service.ILoanResultService;
import com.cfpamf.ms.mallorder.vo.LoanResultVo;
import com.cfpamf.ms.mallorder.vo.agricorder.AgricLoanResultVo;
import com.cfpamf.ms.mallsystem.vo.Admin;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.PageVO;
import com.slodon.bbc.core.response.PagerInfo;
import com.slodon.bbc.core.response.SldResponse;
import com.slodon.bbc.core.util.UserUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Api(tags = "admin-贷款")
@RestController
@Slf4j
@RequestMapping("admin/loan")
public class AdminLoanController {

    @Resource
    private ILoanResultService loanResultService;

    @ApiOperation("放款结果列表")
    @PostMapping("loanResultPage")
    public JsonResult<PageVO<LoanResultVo>> loanResultPage(@RequestBody @NotNull @Valid LoanListResultRequest loanRequest) {
        PagerInfo pager = new PagerInfo(Integer.parseInt(loanRequest.getPageSize()), Integer.parseInt(loanRequest.getCurrent()));
        return SldResponse.success(loanResultService.queryLoanResultPage(pager, loanRequest));
    }

    @ApiOperation("用呗重新代付")
    @GetMapping("loanRepay")
    public JsonResult<Boolean> loanRepay(HttpServletRequest request, @RequestParam @NotNull String paySn) {
        Admin admin = UserUtil.getUser(request, Admin.class);
        log.info(" >>>>>>>>>>>>>>>>loanRepay 用呗重新代付，admin信息:【{}】，单号:【{}】", admin.toString(), paySn);
        return SldResponse.success(loanResultService.loanRepay(admin, paySn));
    }

    @ApiOperation("乡信-放款结果列表")
    @PostMapping("agric/loanResultPage")
    public JsonResult<Page<AgricLoanResultVo>> agricLoanResultPage(@RequestBody @NotNull @Valid LoanListResultQueryDTO loanRequest) {
        return SldResponse.success(loanResultService.agricLoanResultPage(loanRequest));
    }

    @ApiOperation("乡信-重新代付")
    @GetMapping("agric/loanRepay")
    public JsonResult<Void> agricLoanRepay(HttpServletRequest request, @RequestParam @NotNull String paySn) {
        Admin admin = UserUtil.getUser(request, Admin.class);
        log.info(" >>>>>>>>>>>>>>>>loanRepay 乡信重新代付，admin信息:【{}】，单号:【{}】", admin.toString(), paySn);
        loanResultService.agricLoanRepay(paySn, admin.getJobNumber());
        return SldResponse.success();
    }

}
