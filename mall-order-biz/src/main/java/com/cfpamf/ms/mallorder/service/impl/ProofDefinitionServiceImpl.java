package com.cfpamf.ms.mallorder.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cfpamf.ms.mallorder.mapper.ProofDefinitionMapper;
import com.cfpamf.ms.mallorder.po.ProofDefinitionPO;
import com.cfpamf.ms.mallorder.service.IProofDefinitionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class ProofDefinitionServiceImpl extends ServiceImpl<ProofDefinitionMapper,ProofDefinitionPO> implements IProofDefinitionService {


    @Resource
    private ProofDefinitionMapper proofDefinitionMapper;

    @Override
    public ProofDefinitionPO getProofDefinitionByCode(String proofDefinitionCode){
        return proofDefinitionMapper.getProofDefinitionByCode(proofDefinitionCode);
    }
}
