package com.cfpamf.ms.mallorder.controller.fegin.facade;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cfpamf.framework.autoconfigure.common.result.Result;
import com.cfpamf.ms.mallorder.common.base.AgricResult;
import com.cfpamf.ms.mallorder.dto.ProductSkuDetailDTO;
import com.cfpamf.ms.mallorder.dto.ProductSkuQuery;
import com.cfpamf.ms.mallorder.dto.ProductSkuVO;
import com.cfpamf.ms.mallorder.dto.agricorder.LoanListResultQueryDTO;
import com.cfpamf.ms.mallorder.vo.agricorder.AgricLoanResultVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * erp facade
 *
 * <AUTHOR>
 * @since 2024-12-04
 */
@FeignClient(name = "agric-host-order", url = "${agric-host-order.url}")
public interface AgricOrderFacade {
    @PostMapping("/feign/loan/repay/loanResultPage")
    @ApiOperation(value = "乡信放款结果列表")
    AgricResult<Page<AgricLoanResultVo>> loanResultPage(@RequestBody LoanListResultQueryDTO loanRequest);

    @ApiOperation(value = "乡信重新代付")
    @PostMapping("/feign/loan/repay/loanRepay")
    AgricResult<Void> loanRepay(@RequestParam("paySn") String paySn, @RequestParam("jobNumber") String jobNumber);
}
