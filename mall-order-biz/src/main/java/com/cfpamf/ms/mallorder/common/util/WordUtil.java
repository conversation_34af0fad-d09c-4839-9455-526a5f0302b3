package com.cfpamf.ms.mallorder.common.util;

import cn.hutool.core.io.resource.ClassPathResource;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.config.ConfigureBuilder;
import com.deepoove.poi.policy.HackLoopTableRenderPolicy;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * create by z<PERSON><PERSON> on 2019/09/11.
 * word文档生成工具类
 */
public class WordUtil {

    /**
     * word文件生成
     *
     * @param modelfile   模板文件地址
     * @param storagePath 生成文件地址
     * @param datas       普通模板参数
     * @throws IOException
     */
    public static void createWord(String modelfile, String storagePath, Map datas) throws IOException {
        XWPFTemplate template = XWPFTemplate.compile(modelfile).render(datas);
        FileOutputStream outFile = new FileOutputStream(storagePath);   //NOSONAR
        template.write(outFile);
        template.close();
    }

    /**
     * 生成带表格的Wrod文档
     *
     * @param modelfile   模板文件地址
     * @param storagePath 生成文件地址
     * @param datas       普通模板参数
     * @param tableBinds  表格绑定参数
     * @throws IOException
     */
    public static void createTableWord(String modelfile, String storagePath, Map datas,
                                       String[] tableBinds) throws IOException {

        HackLoopTableRenderPolicy policy = new HackLoopTableRenderPolicy();

        ConfigureBuilder builder = Configure.newBuilder();
        if (tableBinds != null && tableBinds.length > 0) {
            for (String tableBind : tableBinds) {
                builder.bind(tableBind, policy);
            }
        }
        Configure config = builder.build();

        ClassPathResource classPathResource = new ClassPathResource(modelfile);
        InputStream file = classPathResource.getStream();
        XWPFTemplate template = XWPFTemplate.compile(file, config).render(datas);
        FileOutputStream outFile = new FileOutputStream(storagePath);   //NOSONAR
        template.write(outFile);
        template.close();
    }


    public static void downloadFile(File file, HttpServletResponse response) throws IOException {
        InputStream fin = null;
        ServletOutputStream out = null;
        try {
            fin = new FileInputStream(file);

            out = response.getOutputStream();
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            response.setContentType("application/x-download");
            String fileNme = URLEncoder.encode(file.getName(), StandardCharsets.UTF_8.name());
            response.setHeader("Content-Disposition", "attachment; filename=" + fileNme);

            byte[] buffer = new byte[1024];
            int bytesToRead = -1;
            // 通过循环将读入的Word文件的内容输出到浏览器中
            while ((bytesToRead = fin.read(buffer)) != -1) {
                out.write(buffer, 0, bytesToRead);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (fin != null) {
                fin.close();
            }
            if (out != null) {
                out.close();
            }

        }
    }


    /**
     * 删除文件，可以是文件或文件夹
     *
     * @param fileName 要删除的文件名
     * @return 删除成功返回true，否则返回false
     */
    public static boolean delete(String fileName) {
        File file = new File(fileName); //NOSONAR
        if (!file.exists()) {
            System.out.println("删除文件失败:" + fileName + "不存在！");
            return false;
        } else {
            if (file.isFile()) {
                return deleteFile(fileName);
            }

        }
        return false;
    }

    /**
     * 删除单个文件
     *
     * @param fileName 要删除的文件的文件名
     * @return 单个文件删除成功返回true，否则返回false
     */
    public static boolean deleteFile(String fileName) {
        File file = new File(fileName); //NOSONAR
        // 如果文件路径所对应的文件存在，并且是一个文件，则直接删除
        if (file.exists() && file.isFile()) {
            if (file.delete()) {
                System.out.println("删除单个文件" + fileName + "成功！");
                return true;
            } else {
                System.out.println("删除单个文件" + fileName + "失败！");
                return false;
            }
        } else {
            System.out.println("删除单个文件失败：" + fileName + "不存在！");
            return false;
        }
    }
}