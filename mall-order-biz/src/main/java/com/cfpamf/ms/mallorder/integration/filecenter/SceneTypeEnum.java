package com.cfpamf.ms.mallorder.integration.filecenter;

import java.util.Arrays;

/**
 * 文件资料场景类型
 */
public enum SceneTypeEnum {

    FUNDS_BORROW_DELIVERY("1","现款现货商品发货"),
    FUNDS_BORROW_RECEIVE("2","现款现货商品签收"),
    MFR_ORDER_DELIVER("4", "农机厂商商品发货"),
    MFR_ORDER_RECEIVE("8", "农机厂商商品签收"),
    LOAN_ORDER_RECEIVE("16", "贷款支付类订单签收"),
    FUNDS_BORROW_AND_MFR_ORDER_DELIVERY("5","现款现货并且农机厂商的商品发货"),
    FUNDS_BORROW_AND_MFR_ORDER_RECEIVE("10","现款现货并且农机厂商的商品签收");

    private final String value;
    private final String desc;

    SceneTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static SceneTypeEnum parseEnum(String value) {
        return Arrays.stream(values()).filter((x) -> x.getValue().equals(value)).findFirst().orElse(null);
    }

    public String getValue() {
        return this.value;
    }

    public String getDesc() {
        return this.desc;
    }
}
