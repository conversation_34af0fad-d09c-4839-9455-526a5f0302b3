package com.cfpamf.ms.mallorder.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 * @Create 2021-08-05 13:41
 * @Description :支付方式商品集合
 */
@Data
public class PayMethodMerchantV2ListDTO {

    @ApiModelProperty(value = "店铺ID，平台统一生成")
    private String storeId;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    @ApiModelProperty(value = "店铺类型：1-自营店铺，2-入驻店铺")
    private Integer isOwnStore;

    @ApiModelProperty(value = "注册手机号")
    private String registerPhone;

    @ApiModelProperty(value = "超管姓名")
    private String identityName;

    @ApiModelProperty(value = "引荐商店铺id")
    private String recommendStoreId;

    @ApiModelProperty(value = "引荐商店铺名称")
    private String recommendStoreName;

}
