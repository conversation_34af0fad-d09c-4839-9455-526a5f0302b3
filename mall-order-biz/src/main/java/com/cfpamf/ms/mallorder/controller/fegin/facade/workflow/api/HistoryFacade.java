package com.cfpamf.ms.mallorder.controller.fegin.facade.workflow.api;

import com.cfpamf.ms.mallorder.controller.fegin.facade.workflow.util.ResultData;
import com.cfpamf.ms.mallorder.controller.fegin.facade.workflow.vo.ActivityVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "flow-manager", url = "${flow-manager.url:}", contextId = "flow-manager-history")
public interface HistoryFacade {
    @GetMapping("/workflow/history/processInstance/nodes")
    ResultData<List<ActivityVo>> getActivityNodes(@RequestParam("bizId") String bizId, @RequestParam("procDefKey") String procDefKey);
}
