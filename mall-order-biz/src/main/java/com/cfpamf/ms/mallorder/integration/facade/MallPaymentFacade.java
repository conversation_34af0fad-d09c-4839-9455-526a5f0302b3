package com.cfpamf.ms.mallorder.integration.facade;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.mallpayment.facade.request.DepositNotify.DepositLoanNotifyRequest;
import com.cfpamf.mallpayment.facade.request.PaymentCancelRequest;
import com.cfpamf.mallpayment.facade.request.loan.*;
import com.cfpamf.mallpayment.facade.request.refund.RefundCheckRequest;
import com.cfpamf.mallpayment.facade.request.refund.RefundRequest;
import com.cfpamf.mallpayment.facade.vo.*;

import com.cfpamf.mallpayment.facade.request.PaymentCreateRequest;
import com.cfpamf.mallpayment.facade.request.PaymentQueryRequest;
import com.cfpamf.mallpayment.facade.request.PaymentRefundRequest;
import com.cfpamf.mallpayment.facade.vo.info.PaymentOrderInfoVO;
import com.cfpamf.mallpayment.facade.vo.info.PaymentRefundInfoVO;
import com.cfpamf.mallpayment.facade.vo.loan.CdmallOrderVo;
import com.cfpamf.mallpayment.facade.vo.loan.RepayCalcVo;
import com.cfpamf.ms.mallorder.req.LargePaymentRecvQueryRequest;
import com.cfpamf.ms.mallorder.req.WayMerchantSyncRequest;
import com.cfpamf.ms.mallorder.vo.PaymentLargeRecvVO;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @Create 2021-07-01 10:00
 * @Description :通用接口-包含用呗
 */
@FeignClient(name = "mall-payment", url = "${mall-payment.url}", contextId = "MallPaymentFacade")
public interface MallPaymentFacade {

    /**
     * 付款
     */
    @PostMapping({"/mallpayment/create"})
    Result<PaymentCreateVO> paymentCreate(@RequestBody PaymentCreateRequest request);

    /**
     * 退款
     */
    @PostMapping({"/mallpayment/refund"})
    Result<PaymentRefundVO> paymentRefund(@RequestBody PaymentRefundRequest request);

    /**
     * 退款
     */
    @PostMapping({"/mallpayment/refund/check"})
    Result<Boolean>  paymentRefundCheck(@RequestBody RefundCheckRequest request);

    /**
     * 退款
     */
    @PostMapping({"/mallpayment/refund/refundV2"})
    Result<PaymentOrderRefundV2VO> paymentRefundV2(@RequestBody RefundRequest request);

    /**
     * 撤销
     */
    @PostMapping({"/mallpayment/cancel"})
    Result<PaymentOrderCancelVO> paymentCancel(@RequestBody PaymentCancelRequest request);

    /**
     * 支付信息查询
     */
    @PostMapping({"/mallpayment/info"})
    Result<List<PaymentOrderInfoVO>> paymentQuery(@RequestBody PaymentQueryRequest request);

    /**
     * 支付订单关闭
     */
    @PostMapping({"/mallpayment/close"})
    Result<PaymentOrderCancelVO> paymentClose(@RequestBody @Valid PaymentCancelRequest request);

    /**
     * 支付信息查询
     */
    @PostMapping({"/mallpayment/order-info"})
    Result<PaymentOrderInfoVO> paymentOrderInfo(@RequestBody PaymentQueryRequest request);
    /**
     * 退款信息查询
     */
    @PostMapping({"/mallpayment/refund/info"})
    Result<List<PaymentRefundInfoVO>> paymentRefundQuery(@RequestBody PaymentQueryRequest request);

    /**
     * 支付方式
     */
    @PostMapping({"/mallpayment/way"})
    Result<PaymentWayVO> paymentWayQuery();

    /**
     *校验支付宝账户是否存在
     */
    @GetMapping("/mallpayment/check")
    Result<Boolean> checkAliPayAccount(@RequestParam(value = "accountName") String accountName);
    //------------------------------以下为用呗专用

    /**
     * 借贷放款（用呗）
     */
    @PostMapping({"/mallpayment/loan/lending"})
    Result<Void> paymentLoanLending(@RequestBody LendingCdMallApplyRequest request);

    /**
     * 根据申请单号查询用呗信息
     */
    @GetMapping({"/mallpayment/loan/getCdmallOrder"})
    Result<CdmallOrderVo> getCdmallOrder(@RequestParam("orderBatchId") @ApiParam("申请单号") String orderBatchId);

    /**
     * 借贷授信取消（用呗）
     */
    @PostMapping({"/mallpayment/loan/cancel"})
    Result<Void> paymentLoanCancel(@RequestBody CancelCdMallApplyRequest request);

    /**
     * 借贷重推放款（用呗）
     */
    @PostMapping({"/mallpayment/loan/redraw"})
    Result<Void> paymentLoanRedraw(@RequestBody RedrawCdMallApplyRequest request);

    /**
     * 借贷试算（用呗）
     */
    @PostMapping({"/mallpayment/loan/setltry"})
    Result<SetlTryResultVo> paymentLoanSetlTry(@RequestBody CdmallSetlTryRequest request);

    /**
     * 本金试算接口 - v2 版本
     */
    @PostMapping("/mallpayment/repay/prcprepaycalc/v2")
    Result<RepayCalcVo> repayCalcByPrincipalV2(@Valid @RequestBody RepayCalcRequest request);

    /**
     * 借贷放款结果回调
     */
    @PostMapping("/callback/loan/notify")
    Result<Void> paymentLoanCallBack(@RequestBody @Valid LoanLendingResult request);

    /**
     * 借贷申请结果回调
     */
    @PostMapping("/callback/loan/apply")
    Result<Boolean> loanApplyNotify(@RequestBody @Valid LoanApplyResult request);

    /**
     * 基础信息
     */
    @GetMapping("/mallpayment/baseDetail")
    Result<MerchantBaseVo> getMerchantBaseInfo(@RequestParam("merchantId") @NotNull Long merchantId, @RequestParam("orderSource") String orderSource);

    /**
     * 根据交易号查询商品名称
     */
    @PostMapping("/mallpayment/listProductsByTno")
    Result<List<OrderProductsVO>> listProductsByTno(@RequestBody @Valid ProductRequest request);

    /**
     * 订单明细查询(决策使用商品信息)
     */
    @GetMapping("/mallpayment/orderInfo")
    Result<OrderProductsVO> orderInfo(@RequestParam("tno") @NotNull Long tno, @RequestParam("orderSource") String orderSource);

    /**
     *放款入金消息通知回调接口
     */
    @PostMapping("/callback/loan/deposit/notify")
    Result<Boolean> loanDepositNotify(@RequestBody @Valid DepositLoanNotifyRequest request);


    /**
     * 根据订单号查询放款信息明细
     */
    @GetMapping("/mallpayment/queryLoanInfo")
    Result<PaymentLoanInfoVO> queryLoanInfo(@RequestParam(value = "orderNo") @ApiParam("订单号") String orderNo);


    @GetMapping("/mallpayment/queryLoanInfoBatch")
    /**
     *批量查询放款信息明细
     */
    Result<List<PaymentLoanInfoVO>> queryLoanInfoBatch(@RequestParam(value = "orderNo") @ApiParam("订单号") String orderNo,
                                                       @RequestParam(value = "startDate") @ApiParam("开始时间 yyyy-MM-dd") String startDate,
                                                       @RequestParam(value = "endDate") @ApiParam("结束时间  yyyy-MM-dd") String endDate,
                                                       @RequestParam(value = "loanOrgNo") @ApiParam("小贷公司编码") String loanOrgNo);

    /**
     * 查询订单支付信息
     * @param orderNo
     * @return
     */
    @GetMapping("/mallpayment/queryPayStatus")
    Result<List<PaymentPayStatusVO>> queryPayStatusBatch(@RequestParam(value = "orderNo") @ApiParam("订单号") String orderNo);


    /**
     * 渠道商户收款信息同步
     */
    @PostMapping("/wayMerchant/sync")
    Result<Boolean> sync(@RequestBody List<WayMerchantSyncRequest> wayMerchantSyncRequestList);


    @ApiOperation(value = "大额订单收款信息查询")
    @PostMapping(value = "/mallpayment/pay-query/get-large-recv")
    Result<PaymentLargeRecvVO> getPaymentLargeRecvInfo(@RequestBody LargePaymentRecvQueryRequest queryRequest);

    @ApiOperation(value = "大额订单收款信息查询or创建(存在失效的大额订单会新向第三方请求创建)")
    @PostMapping(value = "/mallpayment/pay-query/getorcreate-large-recv")
    Result<PaymentLargeRecvVO> getOrCreatePaymentLargeRecvInfo(@RequestBody LargePaymentRecvQueryRequest queryRequest);
}
