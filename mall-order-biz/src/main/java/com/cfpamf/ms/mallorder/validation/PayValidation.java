package com.cfpamf.ms.mallorder.validation;

import java.util.Objects;

import org.apache.commons.lang3.ObjectUtils;

import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.v2.service.OrderPresellService;
import com.slodon.bbc.core.util.AssertUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * PayValidation
 * 
 * <AUTHOR>
 *
 */
@Slf4j
public class PayValidation {

    /**
     * 校验预付支付方式是否支持
     * 
     * @param orderPresellService
     * @param orderType
     * @param paySn
     * @param orderSn
     * @param newOrder
     * @param payMethodValue
     * @param depositPayMethods
     * @param remainPayMethods
     */
    public static void validPresellPayMethodIsSupport(OrderPresellService orderPresellService, Integer orderType,
        String paySn, String orderSn, Boolean newOrder, String payMethodValue, String depositPayMethods,
        String remainPayMethods) {
        boolean isPresell = OrderTypeEnum.isPresell(orderType);// Andy.预付
        log.info("校验预付支付方式是否支持，orderSn:{} payMethod：{} newOrder：{} isPresell：{}", orderSn, payMethodValue, newOrder,
            isPresell);
        // 当前订单为预付订单 && 不是新订单，支付限制。Andy 预付，暂时关闭
        if (isPresell) {
            if (ObjectUtils.isEmpty(newOrder)) {
                // Andy,初始化原本newOrder标识
                newOrder = false;
            }
            // 是否预付尾款支付完成
            boolean presellBalance = orderPresellService.verifyOrderWaitPayBalanceByPaySn(paySn);
            log.info(
                "校验预付支付方式是否支持，newOrder!=ture 拒绝支付 orderSn:{} payMethod：{} newOrder:{} depositPayMethods:{} remainPayMethods:{} presellBalance:{}",
                orderSn, payMethodValue, newOrder, depositPayMethods, remainPayMethods, presellBalance);
             AssertUtil.isTrue(!newOrder, "抱歉，预付订单不支持当前支付方式，请更换其他支付方式！");//Andy.校验规则防控2022.11.21
            log.info("校验预付支付方式是否支持，预付订单具体支付方式验证，newOrder!=ture 拒绝支付 orderSn:{} payMethod：{} newOrder:{}", orderSn,
                payMethodValue, newOrder);
            if (presellBalance) {
                AssertUtil.isTrue(!remainPayMethods.contains(payMethodValue), "抱歉，预付订单尾款不支持当前支付方式，请更换其他支付方式！");
            } else {
                AssertUtil.isTrue(!depositPayMethods.contains(payMethodValue), "抱歉，预付订单订金不支持当前支付方式，请更换其他支付方式！");
            }
        }
    }

    /**
     * 校验预付订单定金尾款分帐规则
     * 
     * @param orderPresellService
     * @param orderType
     * @param paySn
     * @param newOrder1
     * @param newOrder2
     */
    public static void validPresellRoutingRule(OrderPresellService orderPresellService, Integer orderType, String paySn,
        Boolean newOrder1, Boolean newOrder2) {
//        boolean isPresell = OrderTypeEnum.isPresell(orderType);// Andy.预付
//        log.info("【validPresellRoutingRule】校验预付订单定金尾款分帐规则，paySn:{} newOrder1：{} newOrder2：{} isPresell：{}", paySn, newOrder1, newOrder2,
//            isPresell);
//        // 当前订单为预付订单
//        if (isPresell) {
//            // 是否预付订金支付完成
//            boolean presellDeposit = orderPresellService.verifyPayOrderFinishByPaySn(paySn, 1);
//            log.info("【validPresellRoutingRule】校验预付订单定金尾款分帐规则，paySn:{} newOrder1：{} newOrder2：{}  presellDeposit：{}", paySn, newOrder1,
//                newOrder2, presellDeposit);
//            if (!presellDeposit) {
//                // 是否预付尾款支付完成
//                boolean presellBalance = orderPresellService.verifyOrderWaitPayBalanceByPaySn(paySn);
//                log.info("【validPresellRoutingRule】校验预付订单定金尾款分帐规则，paySn:{} newOrder1：{} newOrder2：{}  presellBalance：{}", paySn, newOrder1,
//                    newOrder2, presellBalance);
//                if (!presellDeposit && presellBalance && Objects.nonNull(newOrder2)) {//newOrder2=null表示没改变
//                    AssertUtil.isTrue(!newOrder1.equals(newOrder2), "抱歉，预付订单的尾款不支持当前支付方式，请更换其他支付方式！");
//                }
//            }else {
//                AssertUtil.isTrue(Objects.isNull(newOrder1), "抱歉，预付订单的定金不支持当前支付方式，请更换其他支付方式！");
//            }
//        }

    }

}
