package com.cfpamf.ms.mallorder.scheduler;

import com.cfpamf.ms.mallorder.service.IBzBankPayService;
import com.cfpamf.ms.mallorder.service.ILoanResultService;
import com.slodon.bbc.core.util.AssertUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Create 2021-09-17 14:36
 * @Description :0 0/30 * * * ? 自动转账放款失败接口
 */
@Component
@Slf4j
public class OrderAutoLoanLendingMakeUpJob {

    @Autowired
    private ILoanResultService iLoanResultService;

    @XxlJob(value = "OrderAutoLoanLendingMakeUpJob")
    public ReturnT<String> execute(String s) {
        long startTime = System.currentTimeMillis();
        XxlJobHelper.log("start job at local time: {0}", LocalDateTime.now());
        log.info("OrderAutoLoanLendingMakeUpJob() start");
        try {
            boolean jobResult = iLoanResultService.orderAutoLoanLendingMakeUpJob();
            AssertUtil.isTrue(!jobResult, "[OrderAutoLoanLendingMakeUpJob] 定时任务系统自动付款补偿失败");
        } catch (Exception e) {
            log.error("OrderAutoLoanLendingMakeUpJob()", e);
        }
        XxlJobHelper.log("finish job at local time: {0}  cost:{1}",
                LocalDateTime.now(),
                (System.currentTimeMillis() - startTime) / 1000);

        return new ReturnT("SUCCESS");
    }


}
