package com.cfpamf.ms.mallorder.validation;

import com.cfpamf.ms.mallorder.dto.OrderAfterDTO;

import java.util.List;

/**
 * <AUTHOR> 2023/1/11.
 *
 * 售后申请校验类
 */
public class OrderRefundApplyValidation {

    /**
     * 退款申请商品校验
     *
     * @param productList       退款申请商品信息
     * @return                  校验结果 true/false
     */
    public static Boolean refundApplyProductCheck(List<OrderAfterDTO.AfterProduct> productList) {

        // 校验申请的商品数量是否全部为0
        for (OrderAfterDTO.AfterProduct afterProduct : productList) {
            if (afterProduct.getAfsNum() > 0) {
                return Boolean.TRUE;
            }
        }

        return Boolean.FALSE;
    }

}
