package com.cfpamf.ms.mallorder.common.config;

import io.seata.rm.datasource.DataSourceProxy;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.TransactionManagementConfigurer;

import javax.annotation.Resource;
import javax.sql.DataSource;

/**
 * @Author: oyfm
 * @Description: 指向默认事务管理器
 * @Date: 2020/12/9 14:40
 */
@Configuration
public class DefaultTransactionManagement implements TransactionManagementConfigurer {

    @Resource
    private PlatformTransactionManager masterdbTx;

    @Override
    public PlatformTransactionManager annotationDrivenTransactionManager() {
        return masterdbTx;
    }

    @Bean
    public DataSourceProxy dataSourceProxy(DataSource dataSource) {
        return new DataSourceProxy(dataSource);
    }

}
