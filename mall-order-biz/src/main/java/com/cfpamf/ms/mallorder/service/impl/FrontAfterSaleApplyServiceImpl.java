package com.cfpamf.ms.mallorder.service.impl;

import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.mallorder.common.enums.OrderPatternEnum;
import com.cfpamf.ms.mallorder.common.enums.PayMethodEnum;
import com.cfpamf.ms.mallorder.common.enums.RefundType;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.mapper.OrderMapper;
import com.cfpamf.ms.mallorder.model.OrderAfterServiceModel;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.service.IFrontAfterSaleApplyService;
import com.cfpamf.ms.mallorder.v2.common.constant.PreSellOrderConstant;
import com.cfpamf.ms.mallorder.v2.domain.dto.OrderPresellDTO;
import com.cfpamf.ms.mallorder.v2.service.OrderPresellService;
import com.cfpamf.ms.mallorder.vo.AfsApplyInfoVO;
import com.cfpamf.ms.mallshop.api.StoreIsolateWhiteListFeignClient;
import com.cfpamf.ms.mallshop.enums.WhiteListEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * @Author: zml
 * @CreateTime: 2022/6/6 10:13
 */
@Service
@Slf4j
public class FrontAfterSaleApplyServiceImpl implements IFrontAfterSaleApplyService {

	@Resource
	private OrderAfterServiceModel orderAfterServiceModel;

	@Resource
	private OrderMapper orderMapper;

	@Resource
	private OrderPresellService orderPresellService;

	@Resource
	private StoreIsolateWhiteListFeignClient storeIsolateWhiteListFeignClient;

	@Override
	public AfsApplyInfoVO getAfsApplyInfoVO(Integer memberId, String orderSn, Long orderProductId, boolean isValet) {
		AfsApplyInfoVO vo = orderAfterServiceModel.getAfterSaleApplyInfo(memberId, orderSn, orderProductId, isValet);

		// 设置发起退款时，退款单的退款类型
		RefundType refundType = orderAfterServiceModel.getRefundType(orderSn);
		log.info("apply refund info, the refund type is : {}", JSON.toJSONString(refundType));
		vo.setRefundType(refundType.getValue());

		//根据order_sn号看是什么类型，如果是107类型且是待支付尾款状态，查presell表中已支付订金信息返回给moneyCanReturn字段
		OrderPO order = orderMapper.getOrderAmount(orderSn);
		vo.setOrderPattern(order.getOrderPattern());
		if (order != null && order.getOrderType() != null && order.getOrderType() == OrderTypeEnum.PRE_SELL_DEPOSIT.getValue()) {
			OrderPresellDTO orderPresellDTO = orderPresellService.getDepositOrderDetail(orderSn);
			if (PreSellOrderConstant.PRESELL_ORDER_STATUS_3.equals(orderPresellDTO.getDepositPayStatus())
					&& PreSellOrderConstant.PRESELL_ORDER_STATUS_1.equals(orderPresellDTO.getRemainPayStatus())) {
				vo.setMoneyCanReturn(orderPresellDTO.getDepositPayAmount());
			}

		}

		// 只有自提订单，且在白名单内才展示轨迹
		if (OrderPatternEnum.SELF_LIFT.getValue().equals(order.getOrderPattern())
				&& storeIsolateWhiteListFeignClient.isStoreOnList(WhiteListEnum.DING_DING_REFUND_WHITE_LIST, order.getStoreId())) {
			vo.setSelfLiftReturnTrack(true);
		}

		return vo;
	}


	@Override
	public BigDecimal getDepositProductRefundMoney(String orderSn, BigDecimal amount) {
		OrderPresellDTO orderPresellDTO = orderPresellService.getDepositOrderDetail(orderSn);
		//订单订金已支付，尾款未支付的情况，只退订金
		if (PreSellOrderConstant.PRESELL_ORDER_STATUS_3.equals(orderPresellDTO.getDepositPayStatus())
				&& PreSellOrderConstant.PRESELL_ORDER_STATUS_1.equals(orderPresellDTO.getRemainPayStatus())) {
			return orderPresellDTO.getDepositPayAmount();
		}
		//订单订金已支付，尾款支付中且为银行卡汇款的情况，只退订金
		if (PreSellOrderConstant.PRESELL_ORDER_STATUS_3.equals(orderPresellDTO.getDepositPayStatus())
				&& (PreSellOrderConstant.PRESELL_ORDER_STATUS_2.equals(orderPresellDTO.getRemainPayStatus()) && orderPresellDTO.getRemainPaymentCode().equals(PayMethodEnum.BANK_TRANSFER.getValue()))) {
			return orderPresellDTO.getDepositPayAmount();
		}
		if (PreSellOrderConstant.PRESELL_ORDER_STATUS_3.equals(orderPresellDTO.getDepositPayStatus())
				&& PreSellOrderConstant.PRESELL_ORDER_STATUS_3.equals(orderPresellDTO.getRemainPayStatus())) {
			if (amount.compareTo(orderPresellDTO.getDepositPayAmount().add(orderPresellDTO.getRemainPayAmount())) != 0) {
				log.warn("FrontAfterSaleApplyServiceImpl getDepositProductRefundMoney orderSn = {}," +
						"【退款详情页展示】预付订单实际支付订金金额加尾款金额不等于退款金额", orderSn);
			}
		}
		return amount;

	}
}
