package com.cfpamf.ms.mallorder.controller.seller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cfpamf.cmis.common.utils.DateUtils;
import com.cfpamf.framework.autoconfigure.ZhnxServiceException;
import com.cfpamf.framework.autoconfigure.redis.lock.DistributeLock;
import com.cfpamf.ms.mall.filecenter.domain.dto.FileDTO;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.builder.OrderQueryBuilder;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.constant.OrderProductConst;
import com.cfpamf.ms.mallorder.common.constant.OrdersAfsConst;
import com.cfpamf.ms.mallorder.common.enums.*;
import com.cfpamf.ms.mallorder.common.util.*;
import com.cfpamf.ms.mallorder.constant.ExchangeOrderConst;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.*;
import com.cfpamf.ms.mallorder.enums.OrderPerformanceModeEnum;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.integration.facade.dto.ErpDepotVO;
import com.cfpamf.ms.mallorder.integration.filecenter.SceneTypeEnum;
import com.cfpamf.ms.mallorder.model.BankTransferModel;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.model.OrderProductModel;
import com.cfpamf.ms.mallorder.model.OrderReturnModel;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.req.*;
import com.cfpamf.ms.mallorder.request.OrderExample;
import com.cfpamf.ms.mallorder.request.OrderProductExample;
import com.cfpamf.ms.mallorder.request.OrderReturnExample;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.v2.common.enums.OrderSnType;
import com.cfpamf.ms.mallorder.v2.common.lock.Lock;
import com.cfpamf.ms.mallorder.v2.config.CommonConfig;
import com.cfpamf.ms.mallorder.v2.domain.vo.OperationUserVO;
import com.cfpamf.ms.mallorder.v2.domain.vo.OrderCancelVO;
import com.cfpamf.ms.mallorder.v2.service.OrderCancelService;
import com.cfpamf.ms.mallorder.v2.service.OrderCancelSupportService;
import com.cfpamf.ms.mallorder.vo.*;
import com.cfpamf.ms.mallorder.vo.exportvo.DeliveryFailureVO;
import com.cfpamf.ms.mallshop.api.StoreFeignClient;
import com.cfpamf.ms.mallshop.api.VendorFeignClient;
import com.cfpamf.ms.mallshop.resp.Store;
import com.cfpamf.ms.mallshop.resp.Vendor;
import com.cfpamf.ms.mallsystem.api.ReasonFeignClient;
import com.cfpamf.ms.mallsystem.vo.Reason;
import com.slodon.bbc.core.constant.WebConst;
import com.slodon.bbc.core.controller.BaseController;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.PageVO;
import com.slodon.bbc.core.response.PagerInfo;
import com.slodon.bbc.core.response.SldResponse;
import com.slodon.bbc.core.util.AssertUtil;
import com.slodon.bbc.core.util.ExcelExportUtil;
import com.slodon.bbc.core.util.UserUtil;
import com.slodon.bbc.core.util.WebUtil;
import com.slodon.bbc.starter.mq.entity.VendorLogSendVO;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.slodon.bbc.core.constant.StarterConfigConst.MQ_EXCHANGE_NAME;
import static com.slodon.bbc.core.constant.StarterConfigConst.MQ_QUEUE_NAME_SELLERLOG_MSG;

@Api(tags = "seller-订单管理")
@Slf4j
@RestController
@RequestMapping("seller/orderInfo")
public class SellerOrderInfoController extends BaseController {

    @Resource
    private OrderModel orderModel;
    @Resource
    private IOrderService orderService;
    @Resource
    private OrderProductModel orderProductModel;
    @Resource
    private OrderReturnModel orderReturnModel;
    @Resource
    private ReasonFeignClient reasonFeignClient;
    @Resource
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private IOrderPlacingService orderPlacingService;
    @Autowired
    private IOrderInfoService iOrderInfoService;
    @Resource
    private IOrderReturnService orderReturnService;
    @Resource
    private VendorFeignClient vendorFeignClient;
    @Autowired
    private DistributeLock distributeLock;
    @Autowired
    private Lock lock;
    @Autowired
    private OrderCancelService orderCancelService;
    @Autowired
    private OrderCancelSupportService commonChildOrderOrderCancelSupportService;
    @Autowired
    private CommonConfig commonConfig;
    @Autowired
    private BankTransferModel bankTransferModel;

    @Resource
    private IOrderExchangeService orderExchangeService;

    @Resource
    private IOrderLogisticService orderLogisticService;

    @Resource
    private OrderExcelDataExportService orderExcelDataExportService;

    @Resource
    private IOrderExchangeExcelDataExportService orderExchangeExcelDataExportService;

    @Resource
    private IOrderExternalPerformanceService orderExternalPerformanceService;

    @Autowired
    private StoreFeignClient storeFeignClient;

    @Autowired
    private IOrderPerformanceBelongsService orderPerformanceBelongsService;

    @ApiOperation("获取订单管理状态汇总(小程序)")
    @GetMapping("min/count")
    public JsonResult<OrderStatusCountVO>
    getOrderCountInfo(@ApiParam(value = "店铺id", required = true) @RequestParam(value = "storeId") String storeId) {
        return iOrderInfoService.getOrderCountInfo(storeId);
    }

    @ApiOperation("获取店铺订单搜索历史(小程序)")
    @GetMapping("min/searchHistory")
    public JsonResult<List<BzOrderSearchHistoryVO>>
    getSearchHistory(@ApiParam(value = "店铺id", required = true) @RequestParam(value = "storeId") String storeId) {
        return iOrderInfoService.getSearchHistory(storeId);
    }

    @ApiOperation("清空店铺订单搜索历史(小程序)")
    @GetMapping("min/searchHistory/delete")
    public JsonResult deleteSearchHistory(
            @ApiParam(value = "店铺id", required = true) @RequestParam(value = "storeId") String storeId) {
        return iOrderInfoService.deleteSearchHistory(storeId);
    }

    @ApiOperation(value = "分页查询店铺订单列表(小程序)")
    @PostMapping("min/pageList")
    public JsonResult<Page<OrderListVO>> pageStoreOrderList(@RequestBody @Valid StoreOrderReq storeOrderReq) {
        return iOrderInfoService.pageStoreOrderList(storeOrderReq);
    }

    @ApiOperation(value = "获取订单详情接口(小程序)")
    @GetMapping("min/detail")
    public JsonResult<SellerOrderVO> getMinOrderDetail(HttpServletRequest request,
                                                       @ApiParam(value = "订单id", required = true) @RequestParam(value = "orderSn") String orderSn) {
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        return iOrderInfoService.getMinOrderDetail(orderSn, vendor);
    }

    @ApiOperation("订单列表相关接口")
    @ApiImplicitParams({@ApiImplicitParam(name = "orderSn", value = "订单号", paramType = "query"),
            @ApiImplicitParam(name = "memberName", value = "会员名称", paramType = "query"),
            @ApiImplicitParam(name = "goodsName", value = "商品名称", paramType = "query"),
            @ApiImplicitParam(name = "startTime", value = "下单开始时间", paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "下单结束时间", paramType = "query"),
            @ApiImplicitParam(name = "finishTimeAfter", value = "交易成功开始时间", paramType = "query"),
            @ApiImplicitParam(name = "finishTimeBefore", value = "交易成功结束时间", paramType = "query"),
            @ApiImplicitParam(name = "paymentCode", value = "支付方式编码", paramType = "query"),
            @ApiImplicitParam(name = "orderReturnState", value = "退款状态：1-退款中", paramType = "query"),
            @ApiImplicitParam(name = "orderState", value = "订单状态：0-已取消；10-待付款订单；20-代发货订单；30-待收货订单；40-已完成;50-已关闭",
                    paramType = "query"),
            @ApiImplicitParam(name = "channel",
                    value = "下单操作渠道：H5-浏览器H5；APP-乡助APP；WE_CHAT-微信浏览器；" + "MINI_PRO-小程序；OMS-运管物资", paramType = "query"),
            @ApiImplicitParam(name = "orderType", value = "订单类型：OrderTypeEnum", paramType = "query"),
            @ApiImplicitParam(name = "customerId", value = "客户编码", paramType = "query"),
            @ApiImplicitParam(name = "customerName", value = "客户名称", paramType = "query"),
            @ApiImplicitParam(name = "storeName", value = "店铺名称", paramType = "query"),
            @ApiImplicitParam(name = "recommendStoreName", value = "引荐商名称", paramType = "query"),
            @ApiImplicitParam(name = "recommendStoreId", value = "引荐商ID", paramType = "query"),
            @ApiImplicitParam(name = "branchName", value = "分支名", paramType = "query"),
            @ApiImplicitParam(name = "areaName", value = "区域名", paramType = "query"),
            @ApiImplicitParam(name = "distribution", value = "是否配销订单 0-否 1-是", paramType = "query"),
            @ApiImplicitParam(name = "orderPattern", value = "订单模式：1-C端店铺街，2-B端采购中心", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "分页大小", defaultValue = "20", paramType = "query"),
            @ApiImplicitParam(name = "receiverName", value = "收货人姓名", paramType = "query"),
            @ApiImplicitParam(name = "receiverMobile", value = "收货人手机号", paramType = "query"),
            @ApiImplicitParam(name = "userMobile", value = "买家手机号", paramType = "query"),
            @ApiImplicitParam(name = "current", value = "当前页面位置", defaultValue = "1", paramType = "query")})
    @GetMapping("list")
    @Deprecated
    public JsonResult<PageVO<OrderListVOV2>> getList(HttpServletRequest request, String orderSn, String memberName,
                                                     String goodsName, Date startTime, Date endTime, Date finishTimeAfter, Date finishTimeBefore, String paymentCode,
                                                     Integer orderState, Integer orderReturnState, String channel, @RequestParam(name = "orderType", required = false) List<Integer> orderType, String customerId,
                                                     String customerName, String storeName, String recommendStoreName, String recommendStoreId, String branchName,
                                                     Integer distribution, String areaName, Integer orderPattern, String receiverName, String receiverMobile,
                                                     String userMobile) {
        PagerInfo pager = WebUtil.handlerPagerInfo(request);
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        return iOrderInfoService.listStoreWeb(orderSn, memberName, goodsName, startTime, endTime, finishTimeAfter,
                finishTimeBefore, paymentCode, orderState, orderReturnState, pager, vendor, channel, orderType, customerId,
                customerName, storeName, recommendStoreId, recommendStoreName, branchName, distribution, orderPattern,
                areaName, receiverName, receiverMobile, userMobile);
    }

    @ApiOperation("订单列表相关接口")
    @PostMapping("list/V2")
    public JsonResult<PageVO<OrderListVOV2>> getList(HttpServletRequest request, @RequestBody OrderListQueryRequest orderRequest) {
        PagerInfo pager = new PagerInfo(Integer.parseInt(Optional.ofNullable(orderRequest.getPageSize()).orElse("10")),
                Integer.parseInt(Optional.ofNullable(orderRequest.getCurrent()).orElse("1")));
        //PagerInfo pager = WebUtil.handlerPagerInfo(request);
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        return SldResponse.success(iOrderInfoService.listStoreWebV2(orderRequest, pager, vendor));
    }

    @ApiOperation("线下补录订单列表")
    @PostMapping("offlineOrder/list")
    public JsonResult<PageVO<OfflineOrderListVOV2>> getOfflineOrderList(HttpServletRequest request, @RequestBody OfflineOrderListQueryRequest orderRequest) {
        PagerInfo pager = new PagerInfo(Integer.parseInt(Optional.ofNullable(orderRequest.getPageSize()).orElse("10")),
                Integer.parseInt(Optional.ofNullable(orderRequest.getCurrent()).orElse("1")));
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        return SldResponse.success(iOrderInfoService.getOfflineOrderList(orderRequest, pager, vendor));
    }


    @ApiOperation("换货申请列表")
    @PostMapping("exchangeOrderList")
    public JsonResult<PageVO<OrderExchangeReturnListVO>> getOrderExchangeReturnList(HttpServletRequest request, @RequestBody ExchangeApplyListPcReq exchangeApplyListReq) {
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        PagerInfo pager = new PagerInfo(Integer.parseInt(Optional.ofNullable(exchangeApplyListReq.getPageSize()).orElse("10")),
                Integer.parseInt(Optional.ofNullable(exchangeApplyListReq.getCurrent()).orElse("1")));
        UserDTO userDTO = new UserDTO(vendor);
        exchangeApplyListReq.setUserDTO(userDTO);
        List<OrderExchangeReturnListVO> list = orderExchangeService.getExchangeApplyList(exchangeApplyListReq, pager);
        return SldResponse.success(new PageVO<>(list, pager));
    }

    @ApiOperation("换货申请列表导出")
    @PostMapping("exchangeOrderList/export")
    public JsonResult<FileDTO> orderExchangeReturnListExport(HttpServletRequest request,
                                                             @RequestBody ExchangeApplyListPcReq exchangeApplyListReq) throws Exception {
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        UserDTO userDTO = new UserDTO(vendor);
        exchangeApplyListReq.setUserDTO(userDTO);
        JsonResult<FileDTO> jsonResult = new JsonResult<>(200, "成功");
        exchangeApplyListReq.setStoreId(vendor.getStoreId());
        exchangeApplyListReq.setUserDTO(userDTO);
        jsonResult.setData(orderExchangeExcelDataExportService.executeAsyncExportExcel(exchangeApplyListReq));
        return jsonResult;
    }


    @ApiOperation("获取预付订金尾款支付提醒接口")
    @GetMapping("getDepositRemindCount")
    public JsonResult<Object> getDepositRemindInfo(HttpServletRequest request) {
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        return SldResponse.success(iOrderInfoService.getDepositRemindCount(vendor.getStoreId()));
    }

    @ApiOperation("获取订单详情接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = WebConst.USER_HEADER, value = "商户信息 由网关处理", paramType = "header", dataType = "String"),
            @ApiImplicitParam(name = "orderSn", value = "订单号", required = true),
            @ApiImplicitParam(name = "distribution", value = "是否配销订单 0-否 1-是", paramType = "query")})
    @GetMapping("detail")
    public JsonResult<SellerOrderVO> getOrderDetail(HttpServletRequest request, @RequestParam("orderSn") String orderSn,
                                                    @RequestParam(value = "distribution", required = false) Integer distribution) {
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        log.info("vendor info:{}", JSONObject.toJSONString(vendor));
        return iOrderInfoService.getOrderDetailWeb(orderSn, vendor, distribution, true);
    }


    @ApiOperation("订单分支维护")
    @PostMapping("manageBranch")
    public JsonResult<Void> manageBranch(HttpServletRequest request, @RequestBody OrderManageBranchPramDTO pramDTO) {
        // 获取商户信息
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        BizAssertUtil.notNull(vendor, "商户不存在");
        return SldResponse.success(iOrderInfoService.manageBranch(pramDTO,vendor));
    }


    @ApiOperation("获取订单轨迹")
    @GetMapping("getOrderLogs")
    public JsonResult<List<OrderLogPO>> getOrderLogs(HttpServletRequest request, @RequestParam("orderSn") String orderSn) {
        return SldResponse.success(iOrderInfoService.getOrderLogs(orderSn));
    }

    @ApiOperation(value = "订单改价")
    @PostMapping("renewalPrice")
    public JsonResult<String> renewalPrice(HttpServletRequest request, @RequestBody @NotNull @Valid OrderRenewalPriceReq renewalPriceReq) {

        // 获取商户信息
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        log.info("renewal order price with request:{},orderSn:{}", JSON.toJSONString(renewalPriceReq), renewalPriceReq.getOrderSn());
        orderService.renewalPrice(renewalPriceReq, vendor.getVendorId(), vendor.getVendorName());

        return SldResponse.success("订单改价成功");
    }

    @ApiOperation(value = "订单备注修改")
    @PostMapping("modifyRemark")
    public JsonResult<String> modifyRemark(HttpServletRequest request, @RequestBody @NotNull @Valid OrderInfoModifyReq orderInfoModifyReq) {

        // 获取商户信息
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        orderService.orderInfoModify(orderInfoModifyReq, vendor);

        return SldResponse.success("订单修改成功");
    }

    @ApiOperation("订单商品发货详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = WebConst.USER_HEADER, value = "商户信息 由网关处理", paramType = "header", dataType = "String"),
            @ApiImplicitParam(name = "orderSn", value = "订单号", required = true),
            @ApiImplicitParam(name = "distribution", value = "是否配销订单 0-否 1-是", paramType = "query")})
    @GetMapping("deliveryDetail")
    public JsonResult<List<OrderProductDeliveryVO>> deliveryDetail(HttpServletRequest request,
                                                                   @RequestParam("orderSn") String orderSn,
                                                                   @RequestParam(value = "distribution", required = false) Integer distribution) {
        Vendor vendor = UserUtil.getUser(request, Vendor.class);

        LambdaQueryWrapper<OrderPO> query = new LambdaQueryWrapper<>();
        query.eq(OrderPO::getOrderSn, orderSn).select(OrderPO::getOrderSn, OrderPO::getStoreId,
                OrderPO::getRecommendStoreId, OrderPO::getOrderType);
        OrderPO orderPO = orderService.getOne(query);
        BizAssertUtil.notNull(orderPO, "订单信息为空");

        // 配销订单，引荐商需要看到子店订单，进行权限校验
        OrderLocalUtils.checkOrderPermissions(vendor.getStoreId(), orderPO.getStoreId(), orderPO.getRecommendStoreId(),
                distribution, orderPO.getOrderType());

        return SldResponse.success(orderService.deliveryDetail(orderSn));
    }

    @ApiOperation("订单商品发货详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = WebConst.USER_HEADER, value = "商户信息 由网关处理", paramType = "header", dataType = "String"),
            @ApiImplicitParam(name = "orderSn", value = "订单号", required = true),
            @ApiImplicitParam(name = "distribution", value = "是否配销订单 0-否 1-是", paramType = "query")})
    @GetMapping("deliveryDetailV2")
    public JsonResult<OrderDeliverVo> deliveryDetailV2(HttpServletRequest request,
                                                                   @RequestParam("orderSn") String orderSn,
                                                                   @RequestParam(value = "distribution", required = false) Integer distribution) {
        Vendor vendor = UserUtil.getUser(request, Vendor.class);

        LambdaQueryWrapper<OrderPO> query = new LambdaQueryWrapper<>();
        query.eq(OrderPO::getOrderSn, orderSn).select(OrderPO::getOrderSn, OrderPO::getStoreId,
                OrderPO::getRecommendStoreId, OrderPO::getOrderType);
        OrderPO orderPO = orderService.getOne(query);
        BizAssertUtil.notNull(orderPO, "订单信息为空");

        // 配销订单，引荐商需要看到子店订单，进行权限校验
        OrderLocalUtils.checkOrderPermissions(vendor.getStoreId(), orderPO.getStoreId(), orderPO.getRecommendStoreId(),
                distribution, orderPO.getOrderType());

        return SldResponse.success(orderService.deliveryDetailV2(orderSn));
    }

    @ApiOperation(value = "获取仓库信息")
    @GetMapping("getDepotList")
    public JsonResult<List<ErpDepotVO>> getDepotList(HttpServletRequest request,@Param("skuIdList") String skuIdList) {
        // 获取商户信息
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        BizAssertUtil.notEmpty(skuIdList, "skuIdList不能为空");
        return SldResponse.success(orderExternalPerformanceService.getDepotListWithResult(vendor.getStoreId(), Arrays.asList(skuIdList.split(","))));
    }


    @ApiOperation(value = "发货、修改物流", tags = "CORE")
    @PostMapping("deliver")
    public JsonResult<String> deliver(HttpServletRequest request, @RequestBody @NotNull @Valid OrderDeliveryReq deliveryReq) {
        log.info("deliver====deliveryReq = {}", JSONObject.toJSONString(deliveryReq));

        // 获取商户信息
        Vendor vendor = UserUtil.getUser(request, Vendor.class);

        orderService.deliveryV2(deliveryReq, vendor);
        return SldResponse.success("订单发货成功");
    }


    @ApiOperation(value = "获取发货物流包裹信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderSn", value = "订单号", required = true, paramType = "query")
    })
    @GetMapping("getDeliveryPackageList")
    public JsonResult<List<OrderLogisticItemVO>> getDeliveryPackageList(HttpServletRequest request, @RequestParam("orderSn") String orderSn) {
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        OrderPO orderPO = orderModel.getOrderByOrderSn(orderSn);
        BizAssertUtil.isTrue(Objects.isNull(orderPO), "该订单不存在");
        BizAssertUtil.isTrue(!Objects.equals(vendor.getStoreId(), orderPO.getStoreId()), "您无权查看该订单的物流包裹信息！");
        return SldResponse.success(orderLogisticService.getPackageItemList(orderSn));
    }


    @ApiOperation("订单商品发货列表")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = WebConst.USER_HEADER,
                    value = "商户信息 由网关处理",
                    paramType = "header",
                    dataType = "String"),
            @ApiImplicitParam(name = "orderSn", value = "订单号", required = true)
    })
    @GetMapping("deliveryList")
    public JsonResult<List<OrderFrontDeliveryVO>> deliveryDetail(HttpServletRequest request, @RequestParam("orderSn") String orderSn) {
        Vendor vendor = UserUtil.getUser(request, Vendor.class);

        LambdaQueryWrapper<OrderPO> query = new LambdaQueryWrapper<>();
        query.eq(OrderPO::getOrderSn, orderSn)
                .select(OrderPO::getOrderSn, OrderPO::getStoreId);
        OrderPO orderPO = orderService.getOne(query);
        BizAssertUtil.notNull(orderPO, "订单信息为空");
        BizAssertUtil.isTrue(!Objects.equals(vendor.getStoreId(), orderPO.getStoreId()), "您无权操作此订单");

        return SldResponse.success(orderService.frontDeliveryList(orderSn, null));
    }



    @ApiOperation(value = "批量发货", tags = "CORE")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderSns", value = "订单号，逗号（,）隔开", required = true, paramType = "query"),
            @ApiImplicitParam(name = "channel", value = "下单渠道：H5-浏览器H5，APP-乡助APP，BAPP-掌上中和，WE_CHAT-微信浏览器，MINI_PRO-小程序，WEB-后台浏览器", paramType = "query")
    })
    @PostMapping("batchDelivery")
    public JsonResult batchDelivery(HttpServletRequest request, @RequestBody @NotNull @Valid OrderBatchDeliveryDTO orderBatchDeliveryDTO) {
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        long startTime = System.currentTimeMillis();
        log.info("批量发货 batchDelivery 开始时间：【{}】", startTime);
        String result = orderService.batchDeliveryV2(orderBatchDeliveryDTO, vendor);
        long endTime = System.currentTimeMillis();
        log.info("批量发货 batchDelivery 结束时间：【{}】，耗时【{}】秒", endTime, (endTime - startTime) / 1000);
        return SldResponse.success(result);
    }

    @ApiOperation("导入发货信息")
    @PostMapping("importDeliveryMessage/V2")
    public void importDeliveryMessageV2(HttpServletRequest request, HttpServletResponse response,
                                        @RequestBody @NotNull @Valid List<OrderDeliveryMessageReq> deliveryMessageList) {
        Vendor vendor = UserUtil.getUser(request, Vendor.class);

        long startTime = System.currentTimeMillis();
        log.info("导入发货 importDeliveryMessage/V2 开始时间：【{}】，数据量【{}】", startTime, deliveryMessageList.size());
        List<DeliveryFailureVO> result;
        try {
            String lockKey = "SellerOrderInfoController:importDeliveryMessageV2:" + vendor.getVendorId();
            result = lock.tryLockExecuteFunction(lockKey, 0, 60, TimeUnit.SECONDS,
                    () -> orderService.batchReplenishDeliveryV2(deliveryMessageList, vendor));
        } catch (ZhnxServiceException ex) {
            throw new BusinessException("点击过快，稍等一下~");
        }
        long endTime = System.currentTimeMillis();
        log.info("导入发货 importDeliveryMessage/V2 结束时间：【{}】，数据量【{}】，耗时【{}】秒", endTime, deliveryMessageList.size(),
                (endTime - startTime) / 1000);
        Map<String, List<? extends Object>> sheepMap = new HashMap<>(16);
        sheepMap.put("发货失败记录", result);
        PoiUtils.exportByHttp(request, response, "发货失败记录" + DateUtils.format(new Date(), "yyyyMMdd"), sheepMap,
                PoiUtils.DEFAULT_DATE_FORMAT, null, false);

    }

    @ApiOperation("导入发货信息")
    @PostMapping("importDeliveryMessage")
    @Deprecated
    public JsonResult importDeliveryMessage(HttpServletRequest request,
                                            @RequestBody @NotNull @Valid List<OrderDeliveryMessageReq> deliveryMessageList) {
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        long startTime = System.currentTimeMillis();
        log.info("导入发货 importDeliveryMessage 开始时间：【{}】，数据量【{}】", startTime, deliveryMessageList.size());
        List<String> result = orderService.batchReplenishDelivery(deliveryMessageList, vendor);
        long endTime = System.currentTimeMillis();
        log.info("导入发货 importDeliveryMessage 结束时间：【{}】，耗时【{}】秒", endTime, (endTime - startTime) / 1000);
        return SldResponse.success("发货成功" + (result.size() > 0 ? " 发货冲突数量:" + result.size() : ""));
    }

    @ApiOperation(value = "取消订单")
    @ApiImplicitParams({@ApiImplicitParam(name = "orderSn", value = "订单号", required = true, paramType = "query"),
            @ApiImplicitParam(name = "reasonId", value = "取消原因id", required = true, paramType = "query"),
            @ApiImplicitParam(name = "remark", value = "取消备注", paramType = "query"),
            @ApiImplicitParam(name = "channel",
                    value = "下单操作渠道：H5-浏览器H5；APP-乡助APP；WE_CHAT-微信浏览器；" + "MINI_PRO-小程序；OMS-运管物资", paramType = "query"),
            @ApiImplicitParam(name = "performanceMode", value = "履约模式，0-内部履约模式，1-供应商履约模式", paramType = "query")})
    @PostMapping("cancel")
    public JsonResult<String> cancelOrder(HttpServletRequest request, String orderSn, Integer reasonId, String remark,
                                          String channel, Integer performanceMode) {
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        log.info("商家取消订单，vendor：{}", JSON.toJSONString(vendor));
        OrderPO orderPODb = orderPlacingService.getByOrderSn(orderSn);
        BizAssertUtil.notNull(orderPODb, "订单信息为空");
        AssertUtil.isTrue(!vendor.getStoreId().equals(orderPODb.getStoreId()),
                String.format("订单所属商家信息不一致, 当前登录商家:%s, 订单所属商家:%s, 请校验",
                        vendor.getStore().getStoreName(), orderPODb.getStoreName()));
        log.info("商家取消订单，orderPODb：{}", JSON.toJSONString(orderPODb));

        BizAssertUtil.isTrue(orderPODb.getPerformanceModes().contains(OrderPerformanceModeEnum.PERFORMANCE_MODE_SUPPLIER.getValue().toString())
                        && orderPODb.getOrderState() >= OrderStatusEnum.WAIT_DELIVER.getValue(),
                "【受外部渠道限制，订单不允许取消】");

        BizAssertUtil.isTrue(ExchangeOrderConst.ORDER_EXCHANGE_FLAG_2 == orderPODb.getExchangeFlag(), "换货订单不允许需取消订单");

        List<OrderPO> orderPOList = null;
        if (orderPODb.getOrderState().equals(OrderStatusEnum.WAIT_PAY.getValue())) {
            orderPOList = orderService.listByPaySn(orderPODb.getPaySn());
            BizAssertUtil.isTrue(orderPOList.size() > 1,
                    String.format("商家不允许取消涉及多笔子订单的交易,paySn:%s", orderPODb.getPaySn()));
        }
        BzBankTransferPO bankTransferEntity = null;
        if (PayMethodEnum.BANK_TRANSFER.getValue().equals(orderPODb.getPaymentCode())) {
            orderPOList = orderService.listByPaySn(orderPODb.getPaySn());
            BizAssertUtil.isTrue(orderPOList.size() > 1,
                    String.format("商家不允许取消涉及多笔子订单的银行卡汇款订单,paySn:%s", orderPODb.getPaySn()));
            bankTransferEntity = bankTransferModel.queryByPaySn(orderPODb.getPaySn());
            if (Objects.nonNull(bankTransferEntity) && !TransferStatusEnum.WAIT_TRANSFER.getValue().equals(bankTransferEntity.getTransferState())
                    && !TransferStatusEnum.DEAL_TRANSFER.getValue().equals(bankTransferEntity.getTransferState())) {
                throw new BusinessException(String.format("当前银行卡汇款订单不允许执行取消操作,paySn:%s", orderPODb.getPaySn()));
            }
        } else {
            orderPOList = Collections.singletonList(orderPODb);
        }
        // 配销订单，引荐商需要看到子店订单，进行权限校验
//        OrderLocalUtils.checkOrderPermissions(vendor.getStoreId(), orderPODb.getStoreId(),
//            orderPODb.getRecommendStoreId(), distribution, orderPODb.getOrderType());
        BizAssertUtil.isTrue(OrderPerformanceModeEnum.PERFORMANCE_MODE_SUPPLIER.getValue().equals(performanceMode)
                && orderPODb.getOrderState() >= OrderStatusEnum.DEAL_PAY.getValue(), "渠道已付款订单不支持取消");

        if (commonConfig.isOpenV2()) {// 开启V2流程取消订单
            log.info("开启V2流程取消订单，商家取消订单。orderPODb:{}", orderPODb);
            lock.tryLockExecuteFunction("cancelOrder:" + orderSn, 0, 30, TimeUnit.SECONDS, () -> {
                return orderCancelService.orderCancelOrRefundApply(commonChildOrderOrderCancelSupportService,
                        new OrderCancelVO(OrderSnType.CHILD_ORDER_SN, orderPODb.getOrderSn(), reasonId, channel),
                        new OperationUserVO(OrderConst.LOG_ROLE_VENDOR, OrderConst.RETURN_BY_2, vendor.getVendorId(),
                                vendor.getVendorName(), "商户取消订单"));
            });
            return SldResponse.success("操作成功");
        }

        // 查询取消原因
        Reason reason = reasonFeignClient.getReasonByReasonId(reasonId);
        BizAssertUtil.notNull(reason, "取消原因不存在");

        String lockKey = "cancelOrder:" + orderSn;
        try {
            BzBankTransferPO finalBankTransferEntity = bankTransferEntity;
            List<OrderPO> finalOrderPOList = orderPOList;
            distributeLock.lockAndProcess(lockKey, 0, 30, TimeUnit.SECONDS, () -> {
                if (Objects.nonNull(finalBankTransferEntity)) {
                    bankTransferModel.cancelLargePayment(finalBankTransferEntity);
                }
                orderReturnService.cancelOrder(finalOrderPOList, reason.getContent(), remark, OrderConst.LOG_ROLE_VENDOR,
                        vendor.getVendorId(), vendor.getVendorName(), "商户取消订单", OrderConst.RETURN_BY_2, channel);
                return true;
            });
        } catch (ZhnxServiceException ex) {
            throw new BusinessException("点击过快，稍等一下~");
        }

        // 操作行为
        StringBuilder opt = new StringBuilder("取消订单");
        opt.append("[");
        opt.append("订单取消成功");
        opt.append("]");
        opt.append("[");
        opt.append("订单号：");
        opt.append(orderPODb.getOrderSn());
        opt.append("]");
        VendorLogSendVO vendorLogSendVO = new VendorLogSendVO(vendor.getVendorId(), vendor.getVendorName(),
                request.getRequestURI(), opt.toString(), WebUtil.getRealIp(request));
        try {
            rabbitTemplate.convertAndSend(MQ_EXCHANGE_NAME, MQ_QUEUE_NAME_SELLERLOG_MSG, vendorLogSendVO);
        } catch (Exception e) {
            log.info("发消息异常捕获,{}", JSON.toJSONString(vendorLogSendVO), e);
        }
        return SldResponse.success("订单取消成功");
    }

    @ApiOperation("订单导出")
    @PostMapping("export/V2")
    public JsonResult<Void> exportV2(HttpServletRequest request, HttpServletResponse response, @RequestBody OfflineOrderListQueryRequest orderRequest) throws IOException, InterruptedException {
        Vendor vendor = UserUtil.getUser(request, Vendor.class);

        List<ExportEnum> excludeColumns = new ArrayList<>();
        if (!OrderConst.XZ_STORE_ID.equals(vendor.getStoreId())) {
            excludeColumns.add(ExportEnum.RET_MONEY_MODE);
        }

        OrderExample orderExample = OrderQueryBuilder.buildOfflineOrderExportDataQueryCondition(orderRequest, vendor.getStoreId());
        OrderExportUtil util = new OrderExportUtil(orderModel, orderExample, excludeColumns);

        Map<String, SXSSFWorkbook> excelMap = util.doExport();

        String fileNameNoSuffix = "订单导出-" + ExcelExportUtil.getSystemDate();
        FileUtil.exportFile(request, response, excelMap, fileNameNoSuffix);
        return null;
    }

    @ApiOperation("订单导出")
    @PostMapping("export/V3")
    public JsonResult<FileDTO> exportV3(HttpServletRequest request, @RequestBody OrderListQueryRequest orderListQueryRequest)
            throws Exception {
        JsonResult<FileDTO> jsonResult = new JsonResult<>(200, "成功");
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        UserDTO userDTO = new UserDTO(vendor);
        Vendor searchVendor = vendorFeignClient.getVendorByVendorId(vendor.getVendorId());
        Store storeByStoreId = storeFeignClient.getStoreByStoreId(vendor.getStoreId());
        Integer isSelfLift = storeByStoreId.getIsSelfLift();
        orderListQueryRequest.setRolesName(searchVendor.getRolesName());
        orderListQueryRequest.setStoreId(vendor.getStoreId());
        orderListQueryRequest.setUserDTO(userDTO);
        orderListQueryRequest.setIsSelf(isSelfLift);
        jsonResult.setData(orderExcelDataExportService.executeAsyncExportExcel(orderListQueryRequest));
        return jsonResult;
    }

    /**
     * 处理退货信息
     *
     * @param orderProductPO
     * @param button         没有申请过退换货时展示的状态
     */
    private void dealReturnInfo(OrderProductPO orderProductPO, Integer button) {
        // 有退货,查询退货是否已完成
        OrderReturnExample returnExample = new OrderReturnExample();
        returnExample.setOrderSn(orderProductPO.getOrderSn());
        returnExample.setOrderProductId(orderProductPO.getOrderProductId());
        List<OrderReturnPO> returnList = orderReturnModel.getOrderReturnList(returnExample, null);
        if (CollectionUtils.isEmpty(returnList)) {
            orderProductPO.setAfsButton(button);
            return;
        }
        OrderReturnPO orderReturnPO = returnList.get(0);
        orderProductPO.setAfsSn(orderReturnPO.getAfsSn());
        orderProductPO.setAfsState(orderReturnPO.getState());
        if (orderReturnPO.getState().equals(OrdersAfsConst.RETURN_STATE_300)) {
            // 退款完成
            orderProductPO.setAfsButton(OrderProductConst.AFS_BUTTON_402);
        } else if (orderReturnPO.getState().equals(OrdersAfsConst.RETURN_STATE_202)) {
            // 退款失败
            orderProductPO.setAfsButton(OrderProductConst.AFS_BUTTON_301);
        } else {
            // 没有退款完成的，展示退款中
            orderProductPO.setAfsButton(OrderProductConst.AFS_BUTTON_401);
        }
    }

    @ApiOperation("获取聊天用户订单")
    @ApiImplicitParams({@ApiImplicitParam(name = "memberId", value = "会员id", required = true, paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "分页大小", defaultValue = "20", paramType = "query"),
            @ApiImplicitParam(name = "current", value = "当前页面位置", defaultValue = "1", paramType = "query"),
            @ApiImplicitParam(name = "distribution", value = "是否配销订单 0-否 1-是", paramType = "query")})
    @GetMapping("userOrders")
    public JsonResult<PageVO<ChatOrdersVO>> userOrders(HttpServletRequest request, Integer memberId,
                                                       Integer distribution) {
        Vendor vendor = UserUtil.getUser(request, Vendor.class);

        PagerInfo pager = WebUtil.handlerPagerInfo(request);
        OrderExample example = new OrderExample();
        if (CommonConst.PURCHASE_ORDER_HEADER.equals(request.getHeader(CommonConst.ORDER_PATTERN_HEADER))) {
            example.setOrderPattern(OrderPatternEnum.PURCHASE_CENTRE.getValue());
        } else {
            example.setOrderPatternNotIn(String.valueOf(OrderPatternEnum.PURCHASE_CENTRE.getValue()));
        }
        if (distribution == null || CommonEnum.NO.getCode().equals(distribution)) {
            example.setStoreId(vendor.getStoreId());
        } else {
            example.setRecommendStoreId(vendor.getStoreId().toString());
            example.setOrderType(OrderTypeEnum.ORDER_TYPE_7.getValue());
        }
        example.setMemberId(memberId);
        List<OrderPO> list = orderModel.getOrderList(example, pager);
        ArrayList<ChatOrdersVO> vos = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(order -> {
                // 查询货品列表
                OrderProductExample productExample = new OrderProductExample();
                productExample.setOrderSn(order.getOrderSn());
                List<OrderProductPO> productList = orderProductModel.getOrderProductList(productExample, null);
                vos.add(new ChatOrdersVO(order, productList));
            });
        }
        return SldResponse.success(new PageVO<>(vos, pager));
    }


    @ApiOperation(value = "根据物流单号获取物流公司信息", tags = "CORE")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "expressNumber", value = "物流单号", required = true, paramType = "query")
    })
    @GetMapping("getExpressByExpressNumber")
    public JsonResult<ExpressDeliveryDTO> getExpressByExpressNumber(HttpServletRequest request, String expressNumber, String orderSn) {
        BizAssertUtil.isTrue(StringUtils.isEmpty(expressNumber), "快递单号不能为空");
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        return SldResponse.success(orderLogisticService.getExpressByExpressNumber(expressNumber, orderSn, vendor));
    }

    @ApiOperation("根据物流单号获取物流公司信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "expressNumber", value = "物流单号", required = true, paramType = "query")
    })
    @PostMapping("getExpressListByExpressNumber")
    public JsonResult<List<ExpressDeliveryDTO>> getExpressListByExpressNumber(HttpServletRequest request, @RequestBody BatchExpressNumberRequest batchExpressNumberRequest) {
        BizAssertUtil.isTrue(CollectionUtils.isEmpty(batchExpressNumberRequest.getExpressNumberList()), "快递单号不能为空");
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        return SldResponse.success(orderLogisticService.getExpressListByExpressNumber(batchExpressNumberRequest.getExpressNumberList(), vendor));
    }

    /**
     * 如果快递单号有效，但是该单号通过订单查询已完结发货，或者在全域内被重复使用到不同的订单上≥3次，
     * 则提示【该物流单号已重复使用，涉嫌虚假物流，是否继续提交】，告警但不进行拦截
     * <p>
     * 校验快递单号是否又被使用过
     *
     * @param expressNumber 快递单号
     */
    @ApiOperation("校验快递单号是否又被使用过")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "expressNumber", value = "物流单号", required = true, paramType = "query")
    })
    @GetMapping("validExpressNumber")
    public JsonResult<Boolean> validExpressNumber(HttpServletRequest request, String expressNumber) {
        BizAssertUtil.isTrue(StringUtils.isEmpty(expressNumber), "请输入快递单号");
        boolean isReusedFlag = false;
        try {
            if (StringUtils.isNotEmpty(expressNumber)) {
                isReusedFlag = orderService.validExpressNumberReuseCount(expressNumber);
            }
        } catch (Exception e) {
            log.info("validExpressNumber Exception,", e);
        }
        return SldResponse.success(isReusedFlag);
    }

    /**
     * 超期售后
     */
    @ApiOperation("超期售后")
    @PostMapping("extendedAfterSales")
    public JsonResult<Boolean> extendedAfterSales(HttpServletRequest request, @RequestBody ExtendedAfterSalesDTO extendedAfterSalesDTO) {
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        return SldResponse.success(orderService.extendedAfterSales(extendedAfterSalesDTO, OrderConst.LOG_ROLE_VENDOR, vendor.getVendorId(), vendor.getVendorName()));
    }

    @GetMapping("receive")
    @ApiOperation(value = "确认收货接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderSn", value = "订单号", required = true, paramType = "query"),
            @ApiImplicitParam(name = "channel", value = "下单渠道：H5-浏览器H5，APP-乡助APP，BAPP-掌上中和，WE_CHAT-微信浏览器，MINI_PRO-小程序，WEB-后台浏览器", paramType = "query")
    })
    public JsonResult<Boolean> receive(HttpServletRequest request, String orderSn, OrderCreateChannel channel) {
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        // 订单信息
        OrderPO orderPO = orderModel.getOrderByOrderSn(orderSn);
        BizAssertUtil.isTrue(!orderPO.getStoreId().equals(vendor.getStoreId()), "不能操作非本店铺的订单");
        // 确认收货
        orderModel.receiveOrder(orderPO, OrderConst.LOG_ROLE_VENDOR, Long.valueOf(vendor.getVendorId()),
                vendor.getVendorName(), "会员确认收货", channel);

        return SldResponse.success(Boolean.TRUE);
    }

    @ApiOperation(value = "获取订单的文件场景编号")
    @ApiImplicitParams({@ApiImplicitParam(name = "orderSn", value = "订单号", paramType = "query"),
            @ApiImplicitParam(name = "deliveryState", value = "订单商品发货状态：0-待发货;1-已发货", paramType = "query")})
    @PostMapping("getOrderFileSceneNo")
    public JsonResult<String> getOrderFileSceneNo(HttpServletRequest request,@RequestParam("orderSn") String orderSn,
                                                  @RequestParam(value = "deliveryState",required = false) Integer deliveryState) {
        JsonResult<String> jsonResult = new JsonResult<>();
        // 获取商户信息
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        LambdaQueryWrapper<OrderPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderPO::getOrderSn,orderSn);
        OrderPO orderPO = orderService.getOne(queryWrapper);
        if (Objects.isNull(orderPO)) {
            jsonResult.setState(250);
            jsonResult.setMsg("未找到指定的订单记录");
            return jsonResult;
        }
        OrderProductDeliveryEnum deliveryEnum = null;
        if (Objects.nonNull(deliveryState)) {
            deliveryEnum = OrderProductDeliveryEnum.valueOf(deliveryState);
        }
        SceneTypeEnum sceneTypeEnum = orderService.getOrderFileSceneNo(orderPO, deliveryEnum);
        if (Objects.isNull(sceneTypeEnum)) {
            jsonResult.setData("");
            return jsonResult;
        }
        jsonResult.setData(sceneTypeEnum.getValue());
        return jsonResult;
    }

    /**
     * 根据订单编号获取业绩归属变更轨迹
     *
     * @param orderSn 订单编号
     * @return 变更轨迹
     */
    @ApiOperation(value = "业绩归属信息轨迹查询")
    @PostMapping("/listOperatedTrack")
    public JsonResult<List<EventTraceVO>> listCommissionTransferTrack(@RequestParam("orderSn") String orderSn) {
        List<EventTraceVO> resultList = orderPerformanceBelongsService.listAdjustPerformanceHistory(orderSn);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(resultList)) {
            return SldResponse.success(Collections.emptyList());
        }
        return SldResponse.success(resultList);
    }

    @ApiOperation(value = "业绩归属信息更新-支持通过接口针对单笔订单调整业绩归属人、业绩归属分支、业绩归属类型信息")
    @PostMapping("/modifyPerformanceBelongsInfo")
    public JsonResult<Boolean> modifyPerformanceBelongsInfo(@RequestBody @Valid OrderPerformanceBelongsDTO orderPerformanceBelongsDTO) {
        final OrderPerformanceBelongsPO orderPerformanceBelongs = orderPerformanceBelongsService.modifyPerformanceBelongsInfo(orderPerformanceBelongsDTO);
        if (Objects.isNull(orderPerformanceBelongs)) {
            return SldResponse.success(Boolean.FALSE);
        }
        return SldResponse.success(Boolean.TRUE);
    }
}
