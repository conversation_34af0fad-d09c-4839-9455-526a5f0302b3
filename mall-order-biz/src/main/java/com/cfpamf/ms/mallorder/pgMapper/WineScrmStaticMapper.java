package com.cfpamf.ms.mallorder.pgMapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cfpamf.ms.mallorder.po.pgrpt.WineScrmStaticPO;
import com.cfpamf.ms.mallorder.req.pgrpt.WineScrmStaticQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 酒水PK指标统计
 * </p>
 *
 * <AUTHOR>
 */
@Mapper
public interface WineScrmStaticMapper extends BaseMapper<WineScrmStaticPO> {

	IPage<WineScrmStaticPO> pageList(Page page, @Param("query") WineScrmStaticQuery query);

	List<WineScrmStaticPO> list(@Param("query") WineScrmStaticQuery query);

	WineScrmStaticPO selectByEmpId(@Param("empId") String empId, @Param("jobCode") String jobCode);

	String getLatestStatisticsTime();

}
