package com.cfpamf.ms.mallorder.scheduler;


import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.mapper.OrderMapper;
import com.cfpamf.ms.mallorder.service.IPerformanceService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;


/**
 * 酒水自提订单自动发货
 * <p>
 * cron: 0 0/10 * * * ?
 */
@Component
@Slf4j
public class SelfLiftOrderAutoDeliveryJob {

    @Value("#{'${self-lift-order.auto-delivery-store-list}'.split(',')}")
    private List<Long> autoDeliveryStoreIdList;
    @Resource
    private IPerformanceService performanceService;

    @Resource
    private OrderMapper orderMapper;

    @XxlJob("SelfLiftOrderAutoDeliveryJob")
    public ReturnT<String> execute(String s) throws Exception {
        long startTime = System.currentTimeMillis();
        XxlJobHelper.log("start job at local time: {}", LocalDateTime.now());

        log.info("jobSystemSelfLiftOrderAutoDelivery start");

        List<String> autoDeliveryOrders = orderMapper.getSelfLiftOrdersToDelivery(autoDeliveryStoreIdList);
        for (String orderSn : autoDeliveryOrders) {
            try {
                boolean jobResult = performanceService.selfLiftOrderAutoDelivery(orderSn);
                BizAssertUtil.isTrue(!jobResult, "[jobSystemSelfLiftOrderAutoDelivery] 系统自提订单自动发货时失败, orderSn:{" + orderSn + "}");
            } catch (Exception e) {
                log.error("jobSystemSelfLiftOrderAutoDelivery()", e);
            }
        }

        XxlJobHelper.log("finish jobSystemSelfLiftOrderAutoDelivery job at local time: {}  cost:{}",
                LocalDateTime.now(),
                (System.currentTimeMillis() - startTime) / 1000);

        return ReturnT.SUCCESS;
    }

}
