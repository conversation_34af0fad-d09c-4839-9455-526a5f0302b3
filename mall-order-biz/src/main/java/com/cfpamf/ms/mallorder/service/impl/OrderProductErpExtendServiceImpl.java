package com.cfpamf.ms.mallorder.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cfpamf.ms.mallorder.common.enums.CommonEnum;
import com.cfpamf.ms.mallorder.integration.erp.ERPIntegration;
import com.cfpamf.ms.mallorder.integration.erp.vo.MallProductQuery;
import com.cfpamf.ms.mallorder.integration.erp.vo.ProductVO;
import com.cfpamf.ms.mallorder.mapper.OrderProductErpExtendMapper;
import com.cfpamf.ms.mallorder.model.OrderProductModel;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderProductErpExtendPO;
import com.cfpamf.ms.mallorder.po.OrderProductPO;
import com.cfpamf.ms.mallorder.service.OrderProductErpExtendService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OrderProductErpExtendServiceImpl extends ServiceImpl<OrderProductErpExtendMapper, OrderProductErpExtendPO> implements OrderProductErpExtendService {



    @Autowired
    private OrderProductErpExtendMapper orderProductErpExtendMapper;

    @Autowired
    private OrderProductModel orderProductModel;

    @Autowired
    private ERPIntegration erpIntegration;


    /**
     * 根据商品行id 和订单sn 获取对应的商品erp拓展信息 (如果有的话)
     * @param orderSn
     * @param orderProductId
     * @return
     */
    public OrderProductErpExtendPO selectOrderProductErpExtendByOrderSnAndOrderProductId(String orderSn, Long orderProductId) {
        LambdaQueryWrapper<OrderProductErpExtendPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderProductErpExtendPO::getOrderSn, orderSn)
                .eq(OrderProductErpExtendPO::getOrderProductId, orderProductId).last("limit 1");
        return getOne(queryWrapper);
    }

    /**
     * 生成订单商品erp拓展信息,过滤掉没有物料编码的商品行
     * @param orderPO
     * @param orderProductPoList
     * @return
     */
    public List<OrderProductErpExtendPO> buildOrderProductErpExtendList(OrderPO orderPO, List<OrderProductPO> orderProductPoList) {
        List<OrderProductErpExtendPO> list = Lists.newArrayList();
        if (CollectionUtils.isEmpty(orderProductPoList)) {
            return list;
        }
        log.info("订单货品拓展信息:{}", orderProductPoList);
        for (OrderProductPO orderProductPO : orderProductPoList) {
            // 只保存有物料编码的商品erp拓展信息
            if (StringUtils.isNotBlank(orderProductPO.getSkuMaterialCode())) {
                OrderProductErpExtendPO erpExtendPO = OrderProductErpExtendPO.of(orderPO, orderProductPO);
                list.add(erpExtendPO);
            }
        }
        return list;
    }
    /**
     * 填充订单erp物料编码
     * @param orderSnList
     */
    @Override
    public void fillOrderErpSkuMaterialCode(List<String> orderSnList) {
        if (CollectionUtils.isEmpty(orderSnList)) {
            return;
        }
        // 查询订单货品，并过滤掉没有物料编码的订单商品行数据
        List<OrderProductPO> orderProductPOList = orderProductModel.listByOrderSnList(orderSnList)
                .stream().filter(orderProductPO -> StringUtils.isNotBlank(orderProductPO.getSkuMaterialCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderProductPOList)) {
            log.info("订单货品过滤掉物料编码为空的货品之后没有需要填充物料信息的订单货品");
            return;
        }
        // 分批次处理
        List<List<OrderProductPO>> partitionList = Lists.partition(orderProductPOList, 500);
        partitionList.forEach(orderProductPOs -> {
            // 获取需要查erp的物料编码信息集合(去重)
            List<String> skuMaterialCodeList = orderProductPOs.stream().map(OrderProductPO::getSkuMaterialCode).distinct().collect(Collectors.toList());
            // 封装查询erp参数
            MallProductQuery query = new MallProductQuery();
            query.setSkuMaterialCodes(skuMaterialCodeList);
            // 请求erp获取物料信息
            List<ProductVO> productVOList = erpIntegration.getProductInfoList(query);
            if (CollectionUtils.isEmpty(productVOList)) {
                log.warn("从erp获取到的货品物料信息为空, 参数为{},", JSONObject.toJSONString(query));
                return;
            }
            // 根据物料编码分组 一个编码对应一个订单货品物料信息
            Map<String, ProductVO> skuMaterialCodeProductVOMap = productVOList.stream().collect(Collectors.toMap(ProductVO::getProductNo, Function.identity(), (o, n) -> n));
            // 删掉bz_order_product_erp_extend表中的这批订单货品的物料信息，重新保存新的物料信息
            List<String> orderSns = orderProductPOs.stream().map(OrderProductPO::getOrderSn).distinct().collect(Collectors.toList());
            List<Long> orderProductIds = orderProductPOs.stream().map(OrderProductPO::getOrderProductId).distinct().collect(Collectors.toList());
            lambdaUpdate().in(OrderProductErpExtendPO::getOrderSn, orderSns)
                    .in(OrderProductErpExtendPO::getOrderProductId, orderProductIds)
                    .remove();

            List<String> fillOrderSnList = Lists.newArrayListWithCapacity(orderSns.size());
            List<OrderProductErpExtendPO> newOrderProductErpExtendPOList = Lists.newArrayList();
            orderProductPOs.forEach(orderProductPO -> {
                // 重新保存订单货品erp拓展信息
                OrderProductErpExtendPO erpExtendPO = new OrderProductErpExtendPO();
                erpExtendPO.setSkuMaterialCode(orderProductPO.getSkuMaterialCode());
                erpExtendPO.setOrderSn(orderProductPO.getOrderSn());
                fillOrderSnList.add(orderProductPO.getOrderSn());
                erpExtendPO.setOrderProductId(orderProductPO.getOrderProductId());
                // 保存新的创建和更新时间
                LocalDateTime now = LocalDateTime.now();
                erpExtendPO.setCreateTime(now);
                erpExtendPO.setUpdateTime(now);
                erpExtendPO.setCreateBy("Xxl-Job");
                erpExtendPO.setFillFlag(CommonEnum.NO.getCode());
                ProductVO productVO = skuMaterialCodeProductVOMap.get(orderProductPO.getSkuMaterialCode());
                if (Objects.nonNull(productVO)) {
                    erpExtendPO.setProductCategoryPathName(productVO.getCategoryPath());
                    erpExtendPO.setProductCategoryPath(productVO.getCategoryCodePath());
                    erpExtendPO.setFillFlag(CommonEnum.YES.getCode());
                    log.info("订单货品erp拓展信息填充成功, 订单号为:{}, 订单货品erp拓展信息为:{}", orderProductPO.getOrderSn(), JSONObject.toJSONString(erpExtendPO));
                }
                newOrderProductErpExtendPOList.add(erpExtendPO);
            });
            if (CollectionUtils.isNotEmpty(newOrderProductErpExtendPOList)) {
                log.info("重新保存订单货品erp拓展信息的订单号为:{}", JSONObject.toJSONString(fillOrderSnList));
                saveBatch(newOrderProductErpExtendPOList);
            }
        });
    }

    @Override
    public Map<Long, OrderProductErpExtendPO> getOrderProductErpExtendMapByOrderProductIds(List<Long> orderProductIds) {
        if (CollectionUtils.isEmpty(orderProductIds)) {
            return Maps.newHashMap();
        }
        LambdaQueryWrapper<OrderProductErpExtendPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OrderProductErpExtendPO::getOrderProductId, orderProductIds);
        return list(queryWrapper).stream().collect(Collectors.toMap(OrderProductErpExtendPO::getOrderProductId, Function.identity(), (o, n) -> n));
    }
}
