package com.cfpamf.ms.mallorder.controller.fegin.facade;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cfpamf.cmis.common.constants.Constants;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.mallorder.req.pgrpt.IndicatorsRequest;
import com.cfpamf.ms.mallorder.req.pgrpt.PkMatchOpponentsRequest;
import com.cfpamf.ms.mallorder.service.IWineScrmStaticService;
import com.cfpamf.ms.mallorder.vo.pgrpt.IndicatorsResponse;
import com.cfpamf.ms.mallorder.vo.pgrpt.PkMatchOpponentsResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/v1/feign/business/pk")
@Api(tags = "feign-酒水PK统计")
@Slf4j
@Validated
public class WinePKFacade {

	@Resource
	private IWineScrmStaticService wineScrmStaticService;

	@ApiOperation(value = "随机/手动匹配对手", produces = Constants.CONTENT_TYPE_JSON)
	@PostMapping("/matchOpponents")
	public Result<Page<PkMatchOpponentsResponse>> matchOpponents(@RequestBody @Valid PkMatchOpponentsRequest request) {
		Page<PkMatchOpponentsResponse> responsePage = wineScrmStaticService.matchOpponents(request);
		return new Result<>(true, null, responsePage);
	}

	@ApiOperation(value = "查询员工的历史战力值", produces = Constants.CONTENT_TYPE_JSON)
	@PostMapping("/queryPkHistoryIndicators")
	public Result<List<IndicatorsResponse>> queryPkHistoryIndicators(@RequestBody @Valid IndicatorsRequest request) {
		List<IndicatorsResponse> indicatorsResponses = wineScrmStaticService.queryPkHistoryIndicators(request);
		return new Result<>(true, null, indicatorsResponses);
	}

	@ApiOperation(value = "查询员工的实时战力值", produces = Constants.CONTENT_TYPE_JSON)
	@PostMapping("/queryPkRealTimeIndicators")
	public Result<List<IndicatorsResponse>> queryPkRealTimeIndicators(@RequestBody @Valid IndicatorsRequest request) {
		List<IndicatorsResponse> indicatorsResponses = wineScrmStaticService.queryPkRealTimeIndicators(request);
		return new Result<>(true, null, indicatorsResponses);
	}

	@ApiOperation(value = "查询PK结果", produces = Constants.CONTENT_TYPE_JSON)
	@PostMapping("/pkSettlement")
	public Result<List<IndicatorsResponse>> pkSettlement(@RequestBody @Valid IndicatorsRequest request) {
		List<IndicatorsResponse> indicatorsResponses = wineScrmStaticService.pkSettlement(request);
		return new Result<>(true, null, indicatorsResponses);
	}
}
