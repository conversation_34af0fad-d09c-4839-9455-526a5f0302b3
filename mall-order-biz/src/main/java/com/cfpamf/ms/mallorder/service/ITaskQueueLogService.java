package com.cfpamf.ms.mallorder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.ms.mallorder.po.TaskQueueLogPO;
import com.cfpamf.ms.mallorder.po.TaskQueuePO;

/**
 * <p>
 * 定时任务推送日志表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-01-08
 */
public interface ITaskQueueLogService extends IService<TaskQueueLogPO> {
    void  saveLog(TaskQueuePO task, TaskQueueLogPO taskLog);

    int modifyTaskQueueLog(Long bizId, Integer bizType, Long excludeId);
}
