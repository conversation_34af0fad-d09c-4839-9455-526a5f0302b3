package com.cfpamf.ms.mallorder.dto;

import com.cfpamf.ms.mallorder.po.OrderProductPO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 订单货品
 */
@Data
public class OrderProductAmountDP  {

    @ApiModelProperty("订单货品id")
    private Long orderProductId;

    @ApiModelProperty("订单号")
    private String orderSn;

    @ApiModelProperty("商品id")
    private Long goodsId;

    @ApiModelProperty("商品名称")
    private String goodsName;

    @ApiModelProperty("货品ID")
    private Long productId;

    @ApiModelProperty(value = "落地价")
    private BigDecimal landingPrice;

    @ApiModelProperty(value = "含税售价")
    private BigDecimal taxPrice;

    @ApiModelProperty("成本")
    private BigDecimal cost;

    @ApiModelProperty("税率")
    private BigDecimal taxRate;

    @ApiModelProperty("货品单价，与订单表中goods_amount对应")
    private BigDecimal productShowPrice;

    @ApiModelProperty("商品数量")
    private Integer productNum;

    @ApiModelProperty("货品应付金额 = 货品单价*数量")
    private BigDecimal goodsAmountTotal;

    @ApiModelProperty("货品实付金额 = 货品单价*数量 -活动优惠总金额 -乡助卡抵扣金额")
    private BigDecimal moneyAmount;

    @ApiModelProperty("货品分摊的活动优惠总额，与订单表中activity_discount_amount对应")
    private BigDecimal activityDiscountAmount;

    @ApiModelProperty("店铺活动优惠金额")
    private BigDecimal storeActivityAmount;

    @ApiModelProperty("平台活动优惠金额")
    private BigDecimal platformActivityAmount;

    @ApiModelProperty("店铺优惠券优惠金额")
    private BigDecimal storeVoucherAmount;

    @ApiModelProperty("平台优惠券优惠金额")
    private BigDecimal platformVoucherAmount;

    @ApiModelProperty("乡助卡优惠金额")
    private BigDecimal xzCardAmount;

    @ApiModelProperty("使用积分数量")
    private Integer integral;

    @ApiModelProperty("积分抵扣金额")
    private BigDecimal integralCashAmount;

    @ApiModelProperty("业务佣金比例：用于分销系统计算业务佣金")
    private BigDecimal commissionRate;

    @ApiModelProperty("平台佣金")
    private BigDecimal commissionAmount;

    @ApiModelProperty("平台服务费")
    private BigDecimal serviceFee;

    @ApiModelProperty("代运营服务费")
    private BigDecimal thirdpartnarFee;

    @ApiModelProperty("订单佣金")
    private BigDecimal orderCommission;

    @ApiModelProperty("业务佣金")
    private BigDecimal businessCommission;

    @ApiModelProperty("定金")
    private BigDecimal deposit;
    
    @ApiModelProperty("尾款")
    private BigDecimal balance;

    @ApiModelProperty("商品有效单价，不参与实际业务，仅用于Excel导出和数仓同步")
    private BigDecimal productEffectivePrice;

    public OrderProductAmountDP(){}

    public OrderProductAmountDP(OrderProductPO orderProductPO){
        BeanUtils.copyProperties(orderProductPO, this);
    }

    /**
     * 货品应付金额 = 货品单价*数量
     *
     * @return
     */
    public BigDecimal getGoodsAmountTotal() {
        return this.getProductShowPrice()
                .multiply(new BigDecimal(this.getProductNum()));
    }

    /**
     * 货品实付金额 = 应付金额 -活动优惠总金额 -乡助卡抵扣金额
     *
     * @return
     */
    public BigDecimal getMoneyAmount() {
        return this.getGoodsAmountTotal()
                .subtract(this.getActivityDiscountAmount())
                .max(BigDecimal.ZERO)
                .subtract(this.getXzCardAmount());
    }

    /**
     * 计算优惠总额
     * @return
     */
    public BigDecimal getActivityDiscountAmount() {
        return getStoreActivityAmount()
                .add(getPlatformActivityAmount())
                .add(getPlatformVoucherAmount())
                .add(getStoreVoucherAmount());
    }

    /**
     * 有效单价 = 商品含税售价-（（店铺优惠金额+店铺优惠券优惠金额）/购买数量）
     * @return
     */
    public BigDecimal getProductEffectivePrice() {
        BigDecimal productNum = BigDecimal.valueOf(this.getProductNum());
        return this.getProductShowPrice()
                .subtract((this.getStoreActivityAmount()
                        .add(this.getStoreVoucherAmount()))
                        .divide(productNum, 2, RoundingMode.HALF_UP));
    }

}