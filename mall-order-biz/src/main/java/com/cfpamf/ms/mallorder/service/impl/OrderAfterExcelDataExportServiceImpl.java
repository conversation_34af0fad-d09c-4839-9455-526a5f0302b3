package com.cfpamf.ms.mallorder.service.impl;

import com.cfpamf.ms.mall.filecenter.component.FileComponent;
import com.cfpamf.ms.mall.filecenter.domain.dto.ColumnDTO;
import com.cfpamf.ms.mall.filecenter.domain.dto.FileDTO;
import com.cfpamf.ms.mall.filecenter.domain.vo.ExcelAsyncExportVO;
import com.cfpamf.ms.mall.filecenter.service.impl.AbstractExcelDataExportServiceImpl;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.enums.OrderReturnStatus;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.common.util.DateUtil;
import com.cfpamf.ms.mallorder.common.util.StringUtil;
import com.cfpamf.ms.mallorder.dto.OrderExportDTO;
import com.cfpamf.ms.mallorder.mapper.OrderReturnMapper;
import com.cfpamf.ms.mallorder.req.OrderListQueryRequest;
import com.cfpamf.ms.mallorder.req.exportreq.OrderRefundExportRequest;
import com.cfpamf.ms.mallorder.service.IOrderAfterExcelDataExportService;
import com.cfpamf.ms.mallorder.vo.exportvo.OrderRefundExportVO;
import com.cfpamf.ms.mallsystem.vo.Admin;
import com.slodon.bbc.core.util.UserUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
public class OrderAfterExcelDataExportServiceImpl extends AbstractExcelDataExportServiceImpl<OrderRefundExportVO, OrderRefundExportRequest>
        implements IOrderAfterExcelDataExportService {

    @Autowired
    private FileComponent fileComponent;

    @Value("${spring.application.name}")
    private String appName;

    @Resource
    private OrderReturnMapper orderReturnMapper;

    @Override
    public FileDTO executeAsyncExportExcel(OrderRefundExportRequest refundExportRequest) throws Exception {
        ExcelAsyncExportVO excelAsyncExportVO = new ExcelAsyncExportVO();
        String bizModule = refundExportRequest.getBizModule();
        if (StringUtils.isEmpty(bizModule)) {
            bizModule = "售后列表";
        }
        excelAsyncExportVO.setBizModule(bizModule);
        excelAsyncExportVO.setUserId(Long.valueOf(refundExportRequest.getUserDTO().getUserId()));
        excelAsyncExportVO.setUserName(refundExportRequest.getUserDTO().getUserName());
        excelAsyncExportVO.setUserType(getUserType(refundExportRequest.getUserDTO().getUserRole()));
        excelAsyncExportVO.setApplicationCondition(refundExportRequest.getApplyCondition());
        return fileComponent.executeAsyncExportExcelWithAnnotation(excelAsyncExportVO, appName, CommonConst.ORDER_EXCEL_SHEET_NAME,
                this, refundExportRequest, 10000, new OrderRefundExportVO());
    }

    private Integer getUserType(Integer userRole) {
        int result = 2;
        if (userRole == null) {
            return result;
        }
        if (userRole == 1) {
            return 1;
        } else if (userRole == 2) {
            return 0;
        }
        return result;
    }

    @Override
    public Integer getDataCounts(OrderRefundExportRequest refundExportRequest) {
        return orderReturnMapper.orderRefundExportCount(refundExportRequest);
    }

    @Override
    public List<OrderRefundExportVO> getDataByPage(OrderRefundExportRequest refundExportRequest, Integer pageNum, Integer pageSize) {
        log.info("OrderAfterExcelDataExportServiceImpl getDataByPage pageNum = {}, pageSize= {}", pageNum, pageSize);
        if (OrderReturnStatus.STORE_AGREE_RETURN.getValue().equals(refundExportRequest.getState())) {
            refundExportRequest.setState(null);
            refundExportRequest.setStateIn(OrderReturnStatus.STORE_AGREE_RETURN.getValue() + "," + OrderReturnStatus.SELF_PICKUP_POINT_RETURN_APPLY.getValue());
        }
        int startRow = (pageNum - 1) * pageSize;
        List<OrderRefundExportVO> orderRefundExportList = orderReturnMapper.orderRefundExportByPage(refundExportRequest, startRow, pageSize);
        return orderRefundExportList;
    }


}
