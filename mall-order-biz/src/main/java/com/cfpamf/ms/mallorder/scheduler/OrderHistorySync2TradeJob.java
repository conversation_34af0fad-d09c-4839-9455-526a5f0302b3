package com.cfpamf.ms.mallorder.scheduler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cfpamf.ares.trade.common.enums.*;
import com.cfpamf.ares.trade.domain.dto.*;
import com.cfpamf.ares.trade.domain.vo.TradeVO;
import com.cfpamf.athena.common.domain.dto.ResultEntity;
import com.cfpamf.cmis.common.exception.BizException;
import com.cfpamf.ms.mallorder.common.constant.OrderPaymentConst;
import com.cfpamf.ms.mallorder.common.enums.PayChannelTradeEnum;
import com.cfpamf.ms.mallorder.common.enums.PayStatusAresTradeEnum;
import com.cfpamf.ms.mallorder.common.util.DateUtil;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.OrderSync2TradeDTO;
import com.cfpamf.ms.mallorder.dto.PayCallbackNotifyDTO;
import com.cfpamf.ms.mallorder.enums.OrderEventEnum;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.integration.facade.AresTradeFacade;
import com.cfpamf.ms.mallorder.model.OrderExtendModel;
import com.cfpamf.ms.mallorder.model.OrderProductModel;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.service.IOrderEventMsgService;
import com.cfpamf.ms.mallorder.service.IOrderService;
import com.cfpamf.ms.mallorder.service.ITempOrderSyncFailService;
import com.slodon.bbc.core.exception.MallException;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 历史订单同步交易中心
 */
@Slf4j
@Component
public class OrderHistorySync2TradeJob {
    private String NONE_VALUE = "-";
    private String startTimeStr = "2021-07-03 00:00:00";

    @Resource
    private AresTradeFacade aresTradeFacade;

    @Resource
    private IOrderService orderService;

    @Resource
    private IOrderEventMsgService orderEventMsgService;

    @Resource
    private OrderExtendModel orderExtendModel;

    @Resource
    private OrderProductModel orderProductModel;

    @Resource
    private ITempOrderSyncFailService orderSyncFailService;

    @XxlJob("orderHistorySync2TradeJob")
    public Boolean execute() {
        log.info("同步历史订单开始 {}", new Date());
        String jobParam = XxlJobHelper.getJobParam();
        if (StringUtils.isNotBlank(jobParam)) {
            OrderSync2TradeDTO orderSync2TradeDTO = JSON.parseObject(jobParam, OrderSync2TradeDTO.class);
            Integer queryFlag = checkJobParam(orderSync2TradeDTO);
            LambdaQueryWrapper<OrderPO> queryWrapper = Wrappers.lambdaQuery();
            if (queryFlag == 0) {
                LambdaQueryWrapper<OrderSyncFailPO> syncFailQueryWrapper = Wrappers.lambdaQuery();
                syncFailQueryWrapper.eq(OrderSyncFailPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
                syncFailQueryWrapper.eq(OrderSyncFailPO::getBizType, OrderConst.MQ_MESSAGE_TYPE_0);
                List<String> orderSyncFailPOList = orderSyncFailService.list(syncFailQueryWrapper)
                        .stream()
                        .map(OrderSyncFailPO::getBizId)
                        .distinct()
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(orderSyncFailPOList)) {
                    queryWrapper.in(OrderPO::getOrderSn, orderSyncFailPOList);
                } else {
                    return XxlJobHelper.handleSuccess();
                }
            }

            if (queryFlag == 1) {
                queryWrapper.between(OrderPO::getCreateTime, orderSync2TradeDTO.getStartTime(), orderSync2TradeDTO.getEndTime());
            }
            if (queryFlag == 2) {
                queryWrapper.in(OrderPO::getOrderSn, orderSync2TradeDTO.getOrderSnList());
            }
            queryWrapper.eq(OrderPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
            queryWrapper.orderBy(true,true, OrderPO::getCreateTime);
            for (int currentPage = 1;; currentPage++) {
                Page<OrderPO> page = new Page<>(currentPage, 100);
                IPage<OrderPO> pageResult = orderService.page(page, queryWrapper);
                if (CollectionUtils.isEmpty(pageResult.getRecords())) {
                    break;
                }
                for (OrderPO orderPO : page.getRecords()) {
                    doExecuteSync(orderPO);
                }
            }
        } else {
            Date start = DateUtil.parse(startTimeStr, DateUtil.FORMAT_TIME);
            Date end = new Date();
            for (Date t = start; t.before(end); t = DateUtil.addDays(t, 1)) {
                Date endTime = DateUtil.addDays(t, 1);
                LambdaQueryWrapper<OrderPO> queryWrapper = Wrappers.lambdaQuery();
                queryWrapper.between(OrderPO::getCreateTime, t, endTime);
                queryWrapper.eq(OrderPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
                List<OrderPO> list = orderService.list(queryWrapper);
                for (OrderPO orderPO : list) {
                    doExecuteSync(orderPO);
                }
            }
        }

        log.info("同步历史订单结束 {}", new Date());
        return XxlJobHelper.handleSuccess();
    }

    private void doExecuteSync(OrderPO orderPO) {
        try {
            OrderEventEnum orderEventEnum = OrderEventEnum.getByOrderStatusCode(orderPO.getOrderState());
            ResultEntity<TradeVO> createResult = aresTradeFacade.create(buildTradeDTO(orderPO, orderEventEnum));
            if (createResult != null && !createResult.isSuccess()) {
                log.info("同步订单失败 {} {}", orderPO.getOrderSn(), createResult.getMsg());
                throw new MallException("同步订单失败：" + createResult.getMsg());
            }
            if (!OrderEventEnum.CREATE.equals(orderEventEnum) && !OrderEventEnum.CANCEL.equals(orderEventEnum) && !OrderEventEnum.PAYING.equals(orderEventEnum)) {
                ResultEntity<Void> callbackResult = aresTradeFacade.callback(buildPayResultNotifyDTO(orderPO));
                if (callbackResult != null && !callbackResult.isSuccess()) {
                    log.info("同步订单回调失败 {} {}", orderPO.getOrderSn(), createResult.getMsg());
                    throw new MallException("同步订单回调失败：" + createResult.getMsg());
                }
            }

            ResultEntity<Void> syncStatusResult = aresTradeFacade.orderStatusSync(orderEventMsgService.buildOrderStatusSyncDTO(orderPO, orderEventEnum));
            if (!syncStatusResult.isSuccess()) {
                log.info("同步订单状态失败 {} {}", orderPO.getOrderSn(), syncStatusResult.getMsg());
                throw new BizException("同步订单状态失败：" + syncStatusResult.getMsg());
            }
        } catch (Exception e) {
            log.info("同步订单抛出异常 {}", orderPO.getOrderSn(), e);
            OrderSyncFailPO orderSyncFailPO = new OrderSyncFailPO();
            orderSyncFailPO.setBizId(orderPO.getOrderSn());
            orderSyncFailPO.setBizType(OrderConst.MQ_MESSAGE_TYPE_0);
            orderSyncFailPO.setSyncTime(LocalDateTime.now());
            orderSyncFailPO.setFailReason(e.getMessage());
            orderSyncFailService.save(orderSyncFailPO);
        }
    }

    private Integer checkJobParam(OrderSync2TradeDTO jobParam) {
        if (jobParam.getStartTime() == null && jobParam.getEndTime() == null && CollectionUtils.isEmpty(jobParam.getOrderSnList())) {
            return 0;
        }
        //根据时间范围同步
        if (jobParam.getStartTime() != null && jobParam.getEndTime() != null) {
            return 1;
        }
        //指定订单号同步
        if (jobParam.getStartTime() == null && jobParam.getEndTime() == null && !CollectionUtils.isEmpty(jobParam.getOrderSnList())) {
            return 2;
        }
        throw new MallException("参数异常");
    }

    private TradeDTO buildTradeDTO(OrderPO orderPO, OrderEventEnum orderEventEnum) {
        TradeDTO tradeDTO = new TradeDTO();
        tradeDTO.setOrderTime(DateUtil.dateToLocalDateTime(orderPO.getCreateTime()));
        tradeDTO.setPayDeadline(DateUtil.dateToLocalDateTime(orderPO.getCreateTime()).plusHours(12));
        tradeDTO.setOrderChannel(orderPO.getChannel());
        tradeDTO.setOrderSourceCode(OrderSourceEnum.STANDARD_MALL.getOrderSourceCode());
        tradeDTO.setOrderSourceDesc(OrderSourceEnum.STANDARD_MALL.getOrderSourceDesc());
        tradeDTO.setTradeSourceNo(orderPO.getPaySn());
        tradeDTO.setPayPatternCode(PayPatternEnum.BUSINESS_PAY.getPayPatternCode());
        tradeDTO.setPayPatternDesc(PayPatternEnum.BUSINESS_PAY.getPayPatternDesc());
        tradeDTO.setOrderPlaceUserId(orderPO.getUserNo());
        tradeDTO.setOrderPlaceUserName(StringUtils.isBlank(orderPO.getUserMobile()) ? NONE_VALUE : orderPO.getUserMobile());
        tradeDTO.setOrderPlaceAreaCode(orderPO.getAreaCode());
        tradeDTO.setOrderPlaceRole(orderPO.getOrderPlaceUserRoleCode());
        tradeDTO.setOrderPlaceRoleDesc(orderPO.getOrderPlaceUserRoleDesc());
        tradeDTO.setTradeOrders(buildTradeOrder(orderPO, orderEventEnum));
        tradeDTO.setTradePayApplys(buildTradePayApplyDTO(orderPO));
        return tradeDTO;
    }


    private List<TradeOrderDTO> buildTradeOrder(OrderPO orderPO, OrderEventEnum orderEventEnum) {
        OrderExtendPO orderExtendPO = orderExtendModel.getOrderExtendByOrderSn(orderPO.getOrderSn());
        PayStatusEnum payStatusEnum = getPayStatusByOrderEventEnum(orderEventEnum);
        List<TradeOrderDTO> tradeOrderList = new ArrayList<>();
        TradeOrderDTO tradeOrderDTO = new TradeOrderDTO();
        tradeOrderDTO.setOrderBusinessTypeCode(OrderTypeEnum.getValue(orderPO.getOrderType()) == null ? OrderTypeEnum.NORMAL.getValue() : OrderTypeEnum.getValue(orderPO.getOrderType()).getValue());
        tradeOrderDTO.setOrderBusinessTypeDesc(OrderTypeEnum.getValue(orderPO.getOrderType()) == null ? OrderTypeEnum.NORMAL.getDesc() : OrderTypeEnum.getValue(orderPO.getOrderType()).getDesc());
        tradeOrderDTO.setOrderSourceCode(OrderSourceEnum.STANDARD_MALL.getOrderSourceCode());
        tradeOrderDTO.setOrderSourceDesc(OrderSourceEnum.STANDARD_MALL.getOrderSourceDesc());
        tradeOrderDTO.setOrderSourceOrderNo(orderPO.getOrderSn());
        tradeOrderDTO.setPayPatternCode(PayPatternEnum.BUSINESS_PAY.getPayPatternCode());
        tradeOrderDTO.setPayPatternDesc(PayPatternEnum.BUSINESS_PAY.getPayPatternDesc());
        tradeOrderDTO.setPayStatus(payStatusEnum.getStatus());
        tradeOrderDTO.setPayStatusDesc(payStatusEnum.getDesc());
        tradeOrderDTO.setPerformanceModelCode(PerformanceModelEnum.PLATFORM_STORE.getCode());
        tradeOrderDTO.setPerformanceModelDesc(PerformanceModelEnum.PLATFORM_STORE.getDesc());
        tradeOrderDTO.setStoreId(orderPO.getStoreId());
        tradeOrderDTO.setStoreName(StringUtils.isBlank(orderPO.getStoreName()) ? NONE_VALUE : orderPO.getStoreName());
        tradeOrderDTO.setRecommendStoreId(orderPO.getRecommendStoreId() == null ? 0L : orderPO.getRecommendStoreId());
        tradeOrderDTO.setRecommendStoreName(StringUtils.isBlank(orderPO.getRecommendStoreName()) ? NONE_VALUE : orderPO.getRecommendStoreName());
        tradeOrderDTO.setBuyerUserNo(orderPO.getUserNo());
        tradeOrderDTO.setBuyerCustomerNo(orderPO.getMemberId() == null ? NONE_VALUE : orderPO.getMemberId() + "");
        tradeOrderDTO.setBuyerPhone(StringUtils.isBlank(orderPO.getUserMobile()) ? NONE_VALUE : orderPO.getUserMobile());
        tradeOrderDTO.setBuyerName(StringUtils.isBlank(orderExtendPO.getCustomerName()) ? NONE_VALUE : orderExtendPO.getCustomerName());
        tradeOrderDTO.setOrderPlaceAreaCode(StringUtils.isBlank(orderPO.getAreaCode()) ? NONE_VALUE : orderPO.getAreaCode());
        tradeOrderDTO.setOrderPlaceRole(orderPO.getOrderPlaceUserRoleCode());
        tradeOrderDTO.setOrderPlaceRoleDesc(orderPO.getOrderPlaceUserRoleDesc());
        tradeOrderDTO.setAfterSaleDeadline(DateUtil.dateToLocalDateTime(DateUtil.addDays(orderPO.getCreateTime(), 365)));
        tradeOrderDTO.setOrderStatusCode(orderEventEnum.getOrderStateCode());
        tradeOrderDTO.setOrderStatusName(orderEventEnum.getOrderStateDesc());
        tradeOrderDTO.setPerformanceModelCode(PerformanceModelEnum.PLATFORM_STORE.getCode());
        tradeOrderDTO.setPerformanceModelDesc(PerformanceModelEnum.PLATFORM_STORE.getDesc());
        tradeOrderDTO.setOrderDeliveryAddress(buildOrderDeliveryAddress(orderExtendPO));
        tradeOrderDTO.setOrderLoanCustomerOrganization(buildOrderLoanCustomerOrganization(orderExtendPO));
        tradeOrderDTO.setOrderAmounts(buildOrderAmountDTOList(orderPO, orderEventEnum));
        tradeOrderDTO.setOrderItems(buildOrderItemList(orderPO));

        log.info("转换后的对象 OrderPO: {}，tradeOrderDTO: {}", JSON.toJSONString(orderPO), JSON.toJSONString(tradeOrderDTO));
        tradeOrderList.add(tradeOrderDTO);
        return tradeOrderList;
    }

    private OrderDeliveryAddressDTO buildOrderDeliveryAddress(OrderExtendPO orderExtendPO) {
        OrderDeliveryAddressDTO orderDeliveryAddress = new OrderDeliveryAddressDTO();
        orderDeliveryAddress.setCountry("中国");
        orderDeliveryAddress.setProvince(orderExtendPO.getReceiverProvinceCode());
        orderDeliveryAddress.setCity(orderExtendPO.getReceiverCityCode());
        orderDeliveryAddress.setDistrict(orderExtendPO.getReceiverDistrictCode());
        orderDeliveryAddress.setStreet(orderExtendPO.getReceiverTownCode());
        orderDeliveryAddress.setConsignee(orderExtendPO.getReceiverName());
        orderDeliveryAddress.setCityCode(orderExtendPO.getReceiverCityCode());
        orderDeliveryAddress.setTelphone(orderExtendPO.getReceiverMobile());
        orderDeliveryAddress.setAddress(orderExtendPO.getReceiverInfo());
        return orderDeliveryAddress;
    }


    private OrderLoanCustomerOrganizationDTO buildOrderLoanCustomerOrganization(OrderExtendPO orderExtendPO) {
        OrderLoanCustomerOrganizationDTO loanCustomerOrganization = new OrderLoanCustomerOrganizationDTO();
        loanCustomerOrganization.setCustomerManagerCode(StringUtils.isBlank(orderExtendPO.getManager()) ? NONE_VALUE : orderExtendPO.getManager());
        loanCustomerOrganization.setCustomerManagerName(StringUtils.isBlank(orderExtendPO.getManagerName()) ? NONE_VALUE : orderExtendPO.getManagerName());
        loanCustomerOrganization.setSupervisorCode(StringUtils.isBlank(orderExtendPO.getSupervisor()) ? NONE_VALUE : orderExtendPO.getSupervisor());
        loanCustomerOrganization.setSupervisorName(StringUtils.isBlank(orderExtendPO.getSupervisorName()) ? NONE_VALUE : orderExtendPO.getSupervisorName());
        loanCustomerOrganization.setBranchCode(StringUtils.isBlank(orderExtendPO.getBranch()) ? NONE_VALUE : orderExtendPO.getBranch());
        loanCustomerOrganization.setBranchName(StringUtils.isBlank(orderExtendPO.getBranchName()) ? NONE_VALUE : orderExtendPO.getBranchName());
        loanCustomerOrganization.setZoneCode(StringUtils.isBlank(orderExtendPO.getZoneCode()) ? NONE_VALUE : orderExtendPO.getZoneCode());
        loanCustomerOrganization.setZoneName(StringUtils.isBlank(orderExtendPO.getZoneName()) ? NONE_VALUE : orderExtendPO.getZoneName());
        loanCustomerOrganization.setAreaCode(StringUtils.isBlank(orderExtendPO.getAreaCode()) ? NONE_VALUE : orderExtendPO.getAreaCode());
        loanCustomerOrganization.setAreaName(StringUtils.isBlank(orderExtendPO.getAreaName()) ? NONE_VALUE : orderExtendPO.getAreaName());
        return loanCustomerOrganization;
    }


    private List<OrderAmountDTO> buildOrderAmountDTOList(OrderPO orderPO, OrderEventEnum orderEventEnum) {
        List<OrderAmountDTO> orderAmountDTOList = new ArrayList<>();
        EffectiveStatusEnum effectiveStatusEnum = getEffectiveStatusByOrderEventEnum(orderEventEnum);
        orderAmountDTOList.addAll(buildOrderCreatedAmountDTOList(orderPO, effectiveStatusEnum));
        if (!OrderEventEnum.CREATE.equals(orderEventEnum)) {
            orderAmountDTOList.addAll(buildOrderPayedAmountDTOList(orderPO, effectiveStatusEnum));
        }
        return orderAmountDTOList;
    }

    private List<OrderAmountDTO> buildOrderCreatedAmountDTOList(OrderPO orderPO, EffectiveStatusEnum effectiveStatusEnum) {
        List<OrderAmountDTO> orderAmountDTOList = new ArrayList<>();

        orderAmountDTOList.add(buildOrderAmountDTO(orderPO, OrderAmountCategoryEnum.PLATFORM_SERVICE_FREE, orderPO.getServiceFee(), effectiveStatusEnum));
        orderAmountDTOList.add(buildOrderAmountDTO(orderPO, OrderAmountCategoryEnum.THIRDPARTNAR_SERVICE_FREE, orderPO.getThirdpartnarFee(), effectiveStatusEnum));
        orderAmountDTOList.add(buildOrderAmountDTO(orderPO, OrderAmountCategoryEnum.DEPOSIT_ACTUAL_AMOUNT, orderPO.getOrderAmount(), effectiveStatusEnum));

        return orderAmountDTOList;
    }

    public List<OrderAmountDTO> buildOrderPayedAmountDTOList(OrderPO orderPO, EffectiveStatusEnum effectiveStatusEnum) {
        List<OrderAmountDTO> orderAmountDTOList = new ArrayList<>();

        orderAmountDTOList.add(buildOrderAmountDTO(orderPO, OrderAmountCategoryEnum.ORDER_COMMISSION, orderPO.getOrderCommission(), effectiveStatusEnum));
        orderAmountDTOList.add(buildOrderAmountDTO(orderPO, OrderAmountCategoryEnum.INTEREST_SUBSIDY_LOAN, orderPO.getPlanDiscountAmount(), effectiveStatusEnum));
        orderAmountDTOList.add(buildOrderAmountDTO(orderPO, OrderAmountCategoryEnum.PAY_CHANNEL_PROCEDURE_FREE, orderPO.getChannelServiceFee(), effectiveStatusEnum));

        return orderAmountDTOList;
    }

    public PayResultNotifyDTO buildPayResultNotifyDTO(OrderPO orderPO) {
        BigDecimal invalidAmount = new BigDecimal("0");
        HashMap<String, String> bankPayTrxNos = new HashMap();
        bankPayTrxNos.put(orderPO.getPaySn(), StringUtils.isBlank(orderPO.getBankPayTrxNo()) ? NONE_VALUE : orderPO.getBankPayTrxNo());
        PayCallbackNotifyDTO payResultNotifyDTO = new PayCallbackNotifyDTO();
        payResultNotifyDTO.setMainOrderNo(orderPO.getPaySn());
        payResultNotifyDTO.setOrderOn(orderPO.getPaySn());
        payResultNotifyDTO.setPayCode(orderPO.getPaySn());
        payResultNotifyDTO.setPayTradeNo(orderPO.getPaySn());
        payResultNotifyDTO.setRelPayAmt(orderPO.getPayAmount() == null ? invalidAmount : orderPO.getPayAmount());
        payResultNotifyDTO.setPayUmpAmt(orderPO.getActivityDiscountAmount() == null ? invalidAmount : orderPO.getActivityDiscountAmount());
        payResultNotifyDTO.setRateAmt(orderPO.getChannelServiceFee() == null ? invalidAmount : orderPO.getChannelServiceFee());
        payResultNotifyDTO.setPayStatus(PayStatusAresTradeEnum.PAY_SUCCESS.getStatus());
        payResultNotifyDTO.setPayWayCode(StringUtils.isBlank(orderPO.getPaymentCode()) ? OrderPaymentConst.PAYMENT_CODE_ONLINE : orderPO.getPaymentCode());
        payResultNotifyDTO.setPayWay(StringUtils.isBlank(orderPO.getPaymentName()) ? OrderPaymentConst.PAYMENT_NAME_ONLINE : orderPO.getPaymentName());
        payResultNotifyDTO.setPaySuccessTime(orderPO.getPayTime());
        payResultNotifyDTO.setBankPayTrxNos(bankPayTrxNos);
        payResultNotifyDTO.setPayChannel(PayChannelTradeEnum.getByCode(orderPO.getPayChannel()) == null ? NONE_VALUE : PayChannelTradeEnum.getByCode(orderPO.getPayChannel()).toString());
        payResultNotifyDTO.setSystemCode(OrderSourceEnum.STANDARD_MALL.getOrderSourceCode());
        return payResultNotifyDTO;
    }

    private OrderAmountDTO buildOrderAmountDTO(OrderPO orderPO, OrderAmountCategoryEnum categoryEnum, BigDecimal amount, EffectiveStatusEnum effectiveStatusEnum) {
        OrderAmountDTO orderAmountDTO = new OrderAmountDTO();
        orderAmountDTO.setOrderSourceCode(OrderSourceEnum.STANDARD_MALL.getOrderSourceCode());
        orderAmountDTO.setOrderSourceDesc(OrderSourceEnum.STANDARD_MALL.getOrderSourceDesc());
        orderAmountDTO.setOrderSourceOrderNo(orderPO.getOrderSn());
        orderAmountDTO.setPayOrderSourceNo(orderPO.getPaySn());
        orderAmountDTO.setPayOrderTypeCode(OrderTypeEnum.getValue(orderPO.getOrderType()) == null ? OrderTypeEnum.NORMAL.getValue() : OrderTypeEnum.getValue(orderPO.getOrderType()).getValue());
        orderAmountDTO.setPayOrderTypeDesc(OrderTypeEnum.getValue(orderPO.getOrderType()) == null ? OrderTypeEnum.NORMAL.getDesc() : OrderTypeEnum.getValue(orderPO.getOrderType()).getDesc());
        orderAmountDTO.setType(categoryEnum.getType());
        orderAmountDTO.setTypeDesc(categoryEnum.getTypeDesc());
        orderAmountDTO.setAmount(amount == null ? BigDecimal.ZERO : amount);
        orderAmountDTO.setAmountTime(DateUtil.dateToLocalDateTime(orderPO.getCreateTime()));
        orderAmountDTO.setStatus(effectiveStatusEnum.getCode());
        orderAmountDTO.setStatusDesc(effectiveStatusEnum.getDesc());
        return orderAmountDTO;
    }


    private List<OrderItemDTO> buildOrderItemList(OrderPO orderPO) {
        BigDecimal expressFee = orderPO.getExpressFee();
        List<OrderProductPO> orderProductList = orderProductModel.getOrderProductListByOrderSn(orderPO.getOrderSn());
        Map<Long, BigDecimal> expressFeeMap = orderEventMsgService.buildOrderItemExpressFee(expressFee, orderProductList);
        return orderProductModel.getOrderProductListByOrderSn(orderPO.getOrderSn())
                .stream()
                .map(i -> buildOrderItemDTO(i, expressFeeMap))
                .collect(Collectors.toList());
    }

    private OrderItemDTO buildOrderItemDTO(OrderProductPO orderProductPO, Map<Long, BigDecimal> expressFeeMap) {
        OrderItemDTO orderItemDTO = new OrderItemDTO();
        orderItemDTO.setSpuCode(orderProductPO.getGoodsId().toString());
        orderItemDTO.setSkuCode(orderProductPO.getProductId().toString());
        orderItemDTO.setSkuName(StringUtils.isBlank(orderProductPO.getGoodsName()) ? NONE_VALUE : orderProductPO.getGoodsName());
        orderItemDTO.setSpuCategoryId(orderProductPO.getGoodsCategoryId() == null ? -1 : orderProductPO.getGoodsCategoryId());
        orderItemDTO.setSkuCategoryPath(StringUtils.isBlank(orderProductPO.getGoodsCategoryPath()) ? NONE_VALUE : orderProductPO.getGoodsCategoryPath());
        orderItemDTO.setSkuPictureUrl(StringUtils.isBlank(orderProductPO.getProductImage()) ? NONE_VALUE : orderProductPO.getProductImage());
        orderItemDTO.setSupplierCode(StringUtils.isBlank(orderProductPO.getSupplierCode()) ? NONE_VALUE : orderProductPO.getSupplierCode());
        orderItemDTO.setSupplierSku(StringUtils.isBlank(orderProductPO.getChannelSkuId()) ? NONE_VALUE : orderProductPO.getChannelSkuId());
        orderItemDTO.setSpecValues(StringUtils.isBlank(orderProductPO.getSpecValues()) ? NONE_VALUE : orderProductPO.getSpecValues());
        orderItemDTO.setTaxPrice(orderProductPO.getTaxPrice() == null ? BigDecimal.ZERO : orderProductPO.getTaxPrice());
        orderItemDTO.setTaxRate(orderProductPO.getTaxRate() == null ? BigDecimal.ZERO : orderProductPO.getTaxRate());
        orderItemDTO.setCostPrice(orderProductPO.getCost() == null ? BigDecimal.ZERO : orderProductPO.getCost());
        orderItemDTO.setLandingPrice(orderProductPO.getLandingPrice() == null ? BigDecimal.ZERO : orderProductPO.getLandingPrice());
        orderItemDTO.setSalePrice(orderProductPO.getProductShowPrice() == null ? BigDecimal.ZERO : orderProductPO.getProductShowPrice());
        orderItemDTO.setQuantity(orderProductPO.getProductNum() == null ? BigDecimal.ZERO : BigDecimal.valueOf(orderProductPO.getProductNum()));
        orderItemDTO.setExpressFee(expressFeeMap.getOrDefault(orderProductPO.getProductId(), BigDecimal.ZERO));
        orderItemDTO.setPlatformActivityAmount(orderProductPO.getPlatformActivityAmount() == null ? BigDecimal.ZERO : orderProductPO.getPlatformActivityAmount());
        orderItemDTO.setPlatformCouponAmount(orderProductPO.getPlatformVoucherAmount() == null ? BigDecimal.ZERO : orderProductPO.getPlatformVoucherAmount());
        orderItemDTO.setStoreActivityAmount(orderProductPO.getStoreActivityAmount() == null ? BigDecimal.ZERO : orderProductPO.getStoreActivityAmount());
        orderItemDTO.setStoreCouponAmount(orderProductPO.getStoreVoucherAmount() == null ? BigDecimal.ZERO : orderProductPO.getStoreVoucherAmount());
        orderItemDTO.setDiscountTotalAmount(orderProductPO.getActivityDiscountAmount() == null ? BigDecimal.ZERO : orderProductPO.getActivityDiscountAmount());
        orderItemDTO.setPerformanceStatusCode(PerformanceStatusEnum.NO_NEED_PERFORMANCE.getCode());
        orderItemDTO.setPerformanceStatusDesc(PerformanceStatusEnum.NO_NEED_PERFORMANCE.getDesc());
        orderItemDTO.setSkuTotalAmount(orderProductPO.getGoodsAmountTotal() == null ? BigDecimal.ZERO : orderProductPO.getGoodsAmountTotal());
        orderItemDTO.setSkuPayableTotalAmount(orderProductPO.getMoneyAmount() == null ? BigDecimal.ZERO : orderProductPO.getMoneyAmount());
        orderItemDTO.setSkuSnapshootVersion("1.0.0");
        orderItemDTO.setIsGift(orderProductPO.getIsGift());
        OrderSkuSnapshootDTO orderSkuSnapshoot = new OrderSkuSnapshootDTO();
        orderSkuSnapshoot.setSnapshootInfo(JSONObject.toJSONString(orderProductPO));
        orderItemDTO.setOrderSkuSnapshoot(orderSkuSnapshoot);
        return orderItemDTO;
    }

    private List<TradePayApplyDTO> buildTradePayApplyDTO(OrderPO orderPO) {
        TradePayApplyDTO tradePayApplyDTO = new TradePayApplyDTO();
        tradePayApplyDTO.setPayOrderTypeCode(OrderTypeEnum.getValue(orderPO.getOrderType()) == null ? OrderTypeEnum.NORMAL.getValue() : OrderTypeEnum.getValue(orderPO.getOrderType()).getValue());
        tradePayApplyDTO.setPayOrderTypeDesc(OrderTypeEnum.getValue(orderPO.getOrderType()) == null ? OrderTypeEnum.NORMAL.getDesc() : OrderTypeEnum.getValue(orderPO.getOrderType()).getDesc());
        tradePayApplyDTO.setPayOrderSourceNo(orderPO.getPaySn());
        tradePayApplyDTO.setAmount(orderPO.getOrderAmount() == null ? BigDecimal.ZERO : orderPO.getOrderAmount());
        tradePayApplyDTO.setApplyTime(DateUtil.dateToLocalDateTime(orderPO.getCreateTime()));

        TradePayLoanApplyDTO tradePayLoanApplyDTO = new TradePayLoanApplyDTO();
        tradePayLoanApplyDTO.setLoanPayMode(orderPO.getLoanPayer());

        tradePayApplyDTO.setTradePayLoanApply(tradePayLoanApplyDTO);

        List<TradePayApplyDTO> result = new ArrayList<>();
        result.add(tradePayApplyDTO);
        return result;
    }

    private PayStatusEnum getPayStatusByOrderEventEnum(OrderEventEnum orderEventEnum) {
        switch (orderEventEnum) {
            case CREATE:
                return PayStatusEnum.TODO_PAY;
            case PAYING:
                return PayStatusEnum.PAY;
            case PAY:
            case PART_DELIVERY:
            case DELIVERY:
            case FINISH:
                return PayStatusEnum.PAY_SUCCESS;
            case CANCEL:
            case CLOSE:
                return PayStatusEnum.PAY_FAIL;
            default:
                throw new MallException("订单状态转支付状态失败");
        }
    }

    private EffectiveStatusEnum getEffectiveStatusByOrderEventEnum(OrderEventEnum orderEventEnum) {
        switch (orderEventEnum) {
            case CREATE:
            case CANCEL:
            case PAYING:
                return EffectiveStatusEnum.TODO_EFFECTIVE;
            case PAY:
            case CLOSE:
            case PART_DELIVERY:
            case DELIVERY:
            case FINISH:
                return EffectiveStatusEnum.EFFECTIVE;
            default:
                throw new MallException("订单状态转资金项生效状态失败");
        }
    }
}
