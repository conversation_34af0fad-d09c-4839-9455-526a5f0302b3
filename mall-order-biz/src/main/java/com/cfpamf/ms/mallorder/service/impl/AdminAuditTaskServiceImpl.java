package com.cfpamf.ms.mallorder.service.impl;

import cn.hutool.core.util.StrUtil;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.enums.FlowProcessType;
import com.cfpamf.ms.mallorder.common.enums.OrderPatternEnum;
import com.cfpamf.ms.mallorder.common.enums.RefundDingTalkAuditRoleEnum;
import com.cfpamf.ms.mallorder.common.enums.ReturnTypeEnum;
import com.cfpamf.ms.mallorder.common.enums.aresAfterSale.OrderReturnStatusEnum;
import com.cfpamf.ms.mallorder.common.util.FlowUtil;
import com.cfpamf.ms.mallorder.controller.fegin.facade.workflow.util.MyPage;
import com.cfpamf.ms.mallorder.controller.fegin.facade.workflow.vo.WorkflowTaskDetailVO;
import com.cfpamf.ms.mallorder.dto.AuditTaskSearchVO;
import com.cfpamf.ms.mallorder.integration.facade.BmsIntegration;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderReturnPO;
import com.cfpamf.ms.mallorder.service.AdminAuditTaskService;
import com.cfpamf.ms.mallorder.service.IOrderReturnService;
import com.cfpamf.ms.mallorder.service.IOrderService;
import com.cfpamf.ms.mallorder.vo.AuditTaskVO;
import com.cfpamf.ms.mallorder.vo.TaskTypeVO;
import com.cfpamf.ms.mallshop.api.StoreIsolateWhiteListFeignClient;
import com.cfpamf.ms.mallshop.enums.WhiteListEnum;
import com.cfpamf.ms.mallsystem.vo.Admin;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.response.PageVO;
import com.slodon.bbc.core.response.PagerInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
@Service
@Slf4j
public class AdminAuditTaskServiceImpl implements AdminAuditTaskService {

    @Autowired
    private FlowUtil flowUtil;

    @Autowired
    private IOrderReturnService orderReturnService;

    @Autowired
    private IOrderService orderService;

    @Autowired
    private StoreIsolateWhiteListFeignClient storeIsolateWhiteListFeignClient;

    @Autowired
    private BmsIntegration bmsIntegration;

    /**
     * 获取代办任务列表
     *
     * @param admin    用户
     * @param searchVO 查询参数vo
     * @return 代办任务列表
     */
    @Override
    public PageVO<AuditTaskVO> todoList(Admin admin, AuditTaskSearchVO searchVO) {
        checkEmployeeNumber(admin, "");
        String employeeNumber = admin.getEmployeeNumber();
        PagerInfo pagerInfo = new PagerInfo(searchVO.getPageSize(), searchVO.getCurrent());
        searchVO.setProcessKey("order_return_apply_35");
        MyPage<WorkflowTaskDetailVO> todoList = flowUtil.getTodoList(searchVO, employeeNumber);
        return getAuditTaskVoByTaskDetailVo(pagerInfo, todoList, searchVO);
    }

    /**
     * 获取已完成代办列表
     *
     * @param admin    用户
     * @param searchVO 查询参数vo
     * @return 已完成任务列表
     */
    @Override
    public PageVO<AuditTaskVO> doneList(Admin admin, AuditTaskSearchVO searchVO) {
        checkEmployeeNumber(admin, "");
        String employeeNumber = admin.getEmployeeNumber();
        PagerInfo pagerInfo = new PagerInfo(searchVO.getPageSize(), searchVO.getCurrent());
        searchVO.setProcessKey("order_return_apply_35");
        MyPage<WorkflowTaskDetailVO> doneList = flowUtil.getDoneList(searchVO, employeeNumber);
        return getAuditTaskVoByTaskDetailVo(pagerInfo, doneList, searchVO);
    }

    /**
     * 检查工号
     *
     * @param admin                  当前登陆人
     * @param expectedEmployeeNumber 预期的工号
     */
    private void checkEmployeeNumber(Admin admin, String expectedEmployeeNumber) {
        if (StrUtil.isBlank(expectedEmployeeNumber)) {
            if (null == admin || StrUtil.isBlank(admin.getEmployeeNumber())) {
                throw new BusinessException("请先填写工号再进行操作！");
            }
        } else {
            if (null == admin || !StrUtil.equals(admin.getEmployeeNumber(), expectedEmployeeNumber)) {
                throw new BusinessException("无权操作");
            }
        }
    }

    /**
     * 数据转换
     *
     * @param pagerInfo    分页数据
     * @param taskDetailVo 任务详情vo
     * @return audiTaskVo
     */
    @Override
    public PageVO<AuditTaskVO> getAuditTaskVoByTaskDetailVo(PagerInfo pagerInfo, MyPage<WorkflowTaskDetailVO> taskDetailVo, AuditTaskSearchVO searchVO) {
        if (null == taskDetailVo) {
            return new PageVO<>(Lists.newArrayList(), pagerInfo);
        }
        List<WorkflowTaskDetailVO> taskList = taskDetailVo.getData();
        List<String> instanceIds = taskList.stream().map(WorkflowTaskDetailVO::getProcInstId).collect(Collectors.toList());
        Map<String, String> descMap = Maps.newHashMapWithExpectedSize(instanceIds.size());
        List<AuditTaskVO> auditTaskVoS = taskList.stream().map(t -> {
            AuditTaskVO vo = new AuditTaskVO();
            BeanUtils.copyProperties(t, vo);
            // 转换procDefKey为流程类型枚举
            FlowProcessType flowProcessType = flowUtil.convertType(t.getProcDefKey());
            if (null != flowProcessType) {
                vo.setTaskType(flowProcessType.getName());
            }
            vo.setDesc(descMap.get(t.getProcInstId()));

            // 退货退款包装信息
            if ((FlowProcessType.MALL_ORDER_REFUND_RETURN_APPLY.getCode().equals(t.getProcDefKey())
                    || FlowProcessType.MALL_ORDER_REFUND_APPLY.getCode().equals(t.getProcDefKey())) && searchVO.getSfnFlag()) {
                String bizId = t.getBizId();
                OrderReturnPO orderReturnPO = orderReturnService.getByAfsSn(bizId);
                if (Objects.isNull(orderReturnPO)) {
                    return vo;
                }
                OrderPO orderPO = orderService.getByOrderSn(orderReturnPO.getOrderSn());
                vo.setSelfPointOrderReturn(OrderPatternEnum.SELF_LIFT.getValue().equals(orderPO.getOrderPattern())
                        && storeIsolateWhiteListFeignClient.isStoreOnList(WhiteListEnum.DING_DING_REFUND_WHITE_LIST, orderPO.getStoreId()));
                vo.setOrderSn(orderPO.getOrderSn());
                // 非自提过滤
                if (!OrderPatternEnum.SELF_LIFT.getValue().equals(orderPO.getOrderPattern())) {
                    return null;
                }
                // 非生服店铺的数据，不处理
                if (!bmsIntegration.getLifeServiceStoreId().contains(orderPO.getStoreId())) {
                    return null;
                }
                // 仅退款数据过滤
                if (ReturnTypeEnum.REFUND.getValue().equals(orderReturnPO.getReturnType())) {
                    return null;
                }
                // 待办且非主任审核数据过滤
                if (!RefundDingTalkAuditRoleEnum.DIRECTOR_AUDIT.getRoleCode().equals(t.getNodeKey())
                        && !RefundDingTalkAuditRoleEnum.SELF_POINT_RECEIVE_GOODS.getRoleCode().equals(t.getNodeKey())
                        && CommonConst.FLOW_TASK_TYPE_1 == searchVO.getTaskType()) {
                    return null;
                }
            }
            return vo;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        pagerInfo.setRowsCount((int) taskDetailVo.getTotal());
        return new PageVO<>(auditTaskVoS, pagerInfo);
    }

    /**
     * 获取所有任务类型枚举
     *
     * @return 任务类型vo
     */
    @Override
    public List<TaskTypeVO> getAllTaskType() {
        FlowProcessType[] values = FlowProcessType.values();
        List<TaskTypeVO> result = Lists.newArrayList();
        for (FlowProcessType value : values) {
            TaskTypeVO vo = new TaskTypeVO();
            vo.setCode(value.getCode());
            vo.setName(value.getName());
            result.add(vo);
        }
        return result;
    }

}
