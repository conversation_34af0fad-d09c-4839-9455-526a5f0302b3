package com.cfpamf.ms.mallorder.service.payment;

import com.cfpamf.ms.mall.liquidate.vo.ExecuteVO;
import com.cfpamf.ms.mallorder.po.OrderAfterPO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderReturnPO;
import com.cfpamf.ms.mallsystem.vo.Admin;
import com.slodon.bbc.core.response.JsonResult;

import java.util.Map;

/**
 * <AUTHOR>
 * @Create 2022-05-09 09:57
 * @Description : 支付通用处理器接口
 */
public interface IPayHandleService {

    /**
    * @param
    * @return java.lang.Boolean
    * @description : 退款
    */
    Boolean paymentRefund(OrderPO orderPO, OrderReturnPO orderReturnPO, Map<String, Object> extendParam,
                          String actionType, Admin admin);

    /**
    * @param afsSn
    * @return com.slodon.bbc.core.response.JsonResult<com.cfpamf.ms.mall.liquidate.vo.ExecuteVO>
    * @description : 下账
    */
    //Boolean syncExecute(String afsSn,String orderSn,String actionType);

    Boolean syncExecute(OrderPO orderPO, OrderReturnPO orderReturnPO, String actionType);

    /**
     * @param returnPO
     * @return void
     * @description : 校验电子账户余额
     */
    void verifyExecute(OrderPO orderPO, OrderReturnPO returnPO, String actionType);
}
