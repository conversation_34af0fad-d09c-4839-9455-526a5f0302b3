package com.cfpamf.ms.mallorder.service;

import com.cfpamf.ms.mallorder.po.BzOrderPayWhitelistPO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 支付黑名单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-19
 */
public interface IBzOrderPayWhitelistService extends IService<BzOrderPayWhitelistPO> {

    /**
     * 根据店铺id查询支付黑名单
     * @param storeId storeId
     * @return List<BzOrderPayWhitelistPO>
     */
    List<BzOrderPayWhitelistPO> getPayMethodByStoreId(Long storeId);

}
