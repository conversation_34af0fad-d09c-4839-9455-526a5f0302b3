package com.cfpamf.ms.mallorder.controller.fegin.facade;

import java.util.List;
import javax.annotation.Resource;

import com.cfpamf.ms.mallorder.model.OrderPromotionSendCouponModel;
import com.cfpamf.ms.mallorder.po.OrderPromotionSendCouponPO;
import com.cfpamf.ms.mallorder.request.OrderPromotionSendCouponExample;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 订单活动赠送优惠券表feign
 */
@RestController
@Slf4j
public class OrderPromotionSendCouponFeign {

    @Resource
    private OrderPromotionSendCouponModel orderPromotionSendCouponModel;

    /**
     * 根据sendCouponId获取订单活动赠送优惠券表详情
     *
     * @param sendCouponId sendCouponId
     * @return
     */
    @GetMapping("/v1/feign/business/orderPromotionSendCoupon/get")
    public OrderPromotionSendCouponPO getOrderPromotionSendCouponBySendCouponId(@RequestParam("sendCouponId") Integer sendCouponId) {
        return orderPromotionSendCouponModel.getOrderPromotionSendCouponBySendCouponId(sendCouponId);
    }

    /**
     * 获取条件获取订单活动赠送优惠券表列表
     *
     * @param example 查询条件信息
     * @return
     */
    @PostMapping("/v1/feign/business/orderPromotionSendCoupon/getList")
    public List<OrderPromotionSendCouponPO> getOrderPromotionSendCouponList(@RequestBody OrderPromotionSendCouponExample example) {
        return orderPromotionSendCouponModel.getOrderPromotionSendCouponList(example, example.getPager());
    }

    /**
     * 新增订单活动赠送优惠券表
     *
     * @param orderPromotionSendCouponPO
     * @return
     */
    @PostMapping("/v1/feign/business/orderPromotionSendCoupon/addOrderPromotionSendCoupon")
    public Integer saveOrderPromotionSendCoupon(@RequestBody OrderPromotionSendCouponPO orderPromotionSendCouponPO) {
        return orderPromotionSendCouponModel.saveOrderPromotionSendCoupon(orderPromotionSendCouponPO);
    }

    /**
     * 根据sendCouponId更新订单活动赠送优惠券表
     *
     * @param orderPromotionSendCouponPO
     * @return
     */
    @PostMapping("/v1/feign/business/orderPromotionSendCoupon/updateOrderPromotionSendCoupon")
    public Integer updateOrderPromotionSendCoupon(@RequestBody OrderPromotionSendCouponPO orderPromotionSendCouponPO) {
        return orderPromotionSendCouponModel.updateOrderPromotionSendCoupon(orderPromotionSendCouponPO);
    }

    /**
     * 根据sendCouponId删除订单活动赠送优惠券表
     *
     * @param sendCouponId sendCouponId
     * @return
     */
    @PostMapping("/v1/feign/business/orderPromotionSendCoupon/deleteOrderPromotionSendCoupon")
    public Integer deleteOrderPromotionSendCoupon(@RequestParam("sendCouponId") Integer sendCouponId) {
        return orderPromotionSendCouponModel.deleteOrderPromotionSendCoupon(sendCouponId);
    }
}