package com.cfpamf.ms.mallorder.service.impl;


import com.cfpamf.ms.mallorder.builder.DingTalkMessageTemplateBuilder;
import com.cfpamf.ms.mallorder.common.enums.*;
import com.cfpamf.ms.mallorder.common.util.DateUtil;
import com.cfpamf.ms.mallorder.integration.shop.ShopIntegration;
import com.cfpamf.ms.mallorder.pgMapper.AdsWineWorkDateOrderDetailHfpMapper;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.po.pgrpt.AdsWineWorkDateOrderDetailHfpPO;
import com.cfpamf.ms.mallorder.req.BeverageWorkReportRequest;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.vo.OrderReturnTrackVO;
import com.cfpamf.ms.mallshop.vo.SelfLiftingPointVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
public class BeverageWorkReportServiceImpl implements IBeverageWorkReportService {

    @Autowired
    private IOrderService orderService;

    @Autowired
    private IOrderExtendService orderExtendService;

    @Autowired
    private IOrderProductService orderProductService;

    @Autowired
    private IOrderReturnService orderReturnService;

    @Autowired
    private IOrderAfterService orderAfterService;

    @Autowired
    private IOrderReturnTrackService orderReturnTrackService;

    @Autowired
    private IOrderPerformanceBelongsService orderPerformanceBelongsService;

    @Autowired
    private AdsWineWorkDateOrderDetailHfpMapper adsWineWorkDateOrderDetailHfpMapper;

    @Resource
    private ShopIntegration shopIntegration;

    @Resource
    private DingTalkMessageTemplateBuilder dingTalkMessageTemplateBuilder;

    @Override
    public List<AdsWineWorkDateOrderDetailHfpPO> queryBeverageWorkReport(BeverageWorkReportRequest request) {
        List<AdsWineWorkDateOrderDetailHfpPO> list = adsWineWorkDateOrderDetailHfpMapper.getList(request);
        return null;
    }

    /**
     * 查询流程业务信息详情
     */
    public Map<String, Object> queryFlowMessageDetail(String procDefKey, String nodeKey, String bizId) {

        HashMap<String, Object> detailInfo = new HashMap<>();

        // 电商售后流程
        if (FlowProcessType.isAfterSaleProcess(procDefKey)) {
            // 查询售后单信息
            OrderReturnPO orderReturnPO = orderReturnService.getByAfsSn(bizId);
            if (orderReturnPO == null) {
                log.error("售后单不存在, 售后单号: {}", bizId);
                return null;
            }
            OrderAfterPO orderAfterPO = orderAfterService.getByAfsSn(bizId);
            // 查询订单信息
            OrderPO orderPO = orderService.getByOrderSn(orderReturnPO.getOrderSn());
            // 查询订单扩展信息
            OrderExtendPO orderExtendPO = orderExtendService.getOrderExtendByOrderSn(orderPO.getOrderSn());
            // 查询商品信息
            OrderProductPO orderProductPO = orderProductService.selectOneByOrderProductId(orderAfterPO.getOrderProductId());
            // 查询自提信息
            SelfLiftingPointVo selfLiftingPoint = shopIntegration.getSelfLiftingPointV2(orderExtendPO.getPointId());
            // 业绩归属人信息
            OrderPerformanceBelongsPO orderPerformanceBelongsPO = orderPerformanceBelongsService.getEntityByOrderSn(orderPO.getOrderSn());
            // 轨迹信息
            List<OrderReturnTrackVO> orderReturnTracks = orderReturnTrackService.getOrderReturnTrackVOByAfsSn(bizId);
            detailInfo.put("afsSn", orderReturnPO.getAfsSn());
            String currentPoint = OrderReturnStatus.getShowDesc(orderReturnPO.getState());
            detailInfo.put("currentPoint", currentPoint);
            detailInfo.put("businessType", OrderPatternEnum.getDescByValue(orderPO.getOrderPattern()));
            detailInfo.put("goodsName", orderProductPO.getGoodsName());
            detailInfo.put("afsNum", String.valueOf(orderReturnPO.getReturnNum()) + dingTalkMessageTemplateBuilder.getProductSellUnit(orderProductPO));
            detailInfo.put("orderAmt", orderPO.getOrderAmount().toPlainString() + "元");
            detailInfo.put("discountAmt", orderPO.getActivityDiscountAmount().toPlainString() + "元");
            detailInfo.put("payAmt", orderPO.getPayAmount().toPlainString() + "元");
            detailInfo.put("refundAmt", orderReturnPO.getRefundApplySumAmount().toPlainString() + "元");
            detailInfo.put("deliveryStatus", orderProductPO.getDeliveryState().getDesc());
            detailInfo.put("selfPointBranch", selfLiftingPoint == null ? "" : selfLiftingPoint.getBranchName());
            detailInfo.put("performBelongBranch", orderPerformanceBelongsPO == null ? "" : orderPerformanceBelongsPO.getEmployeeBranchName());
            detailInfo.put("performBelongName", orderPerformanceBelongsPO == null ? "" : orderPerformanceBelongsPO.getBelongerName());
            detailInfo.put("orderSn", orderPO.getOrderSn());
            detailInfo.put("applyName", orderAfterPO.getContactName());
            detailInfo.put("returnType", ReturnTypeEnum.getDesc(orderReturnPO.getReturnType()));
            detailInfo.put("refundReason", orderAfterPO.getAfsDescription());
            detailInfo.put("applyTime", DateUtil.getDateString(orderAfterPO.getBuyerApplyTime(), DateUtil.FORMAT_TIME));
            detailInfo.put("state", orderReturnPO.getState());
            detailInfo.put("orderReturnTracks", orderReturnTracks);
        }

        return detailInfo;
    }


}
