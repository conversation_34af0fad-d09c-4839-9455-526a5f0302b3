package com.cfpamf.ms.mallorder.service.impl;

import com.cfpamf.framework.autoconfigure.mybatis.BaseRepoServiceImpl;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.mapper.BzOrderSearchHistoryMapper;
import com.cfpamf.ms.mallorder.po.BzOrderSearchHistoryPO;
import com.cfpamf.ms.mallorder.service.IBzOrderSearchHistoryService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 订单搜索历史表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
@Service
public class BzOrderSearchHistoryServiceImpl extends BaseRepoServiceImpl<BzOrderSearchHistoryMapper, BzOrderSearchHistoryPO> implements IBzOrderSearchHistoryService {


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrderSearchHistory(BzOrderSearchHistoryPO bzOrderSearchHistoryPO) {
        Long storeId = bzOrderSearchHistoryPO.getStoreId();
        String searchValue = bzOrderSearchHistoryPO.getSearchValue();
        List<BzOrderSearchHistoryPO> historyPOS = lambdaQuery()
                .eq(BzOrderSearchHistoryPO::getStoreId, storeId)
                .orderByAsc(BzOrderSearchHistoryPO::getCreateTime)
                .list();
        List<BzOrderSearchHistoryPO> historyPOList = new ArrayList<>();

        for (BzOrderSearchHistoryPO historyPO : historyPOS) {
            if (searchValue.equals(historyPO.getSearchValue())) {
                removeById(historyPO);
            } else {
                historyPOList.add(historyPO);
            }
        }

        if (historyPOList.size() > CommonConst.MAX_HISTORY) {
            int deleteNum = historyPOList.size() - CommonConst.MAX_HISTORY;
            for (int i = 0; i < deleteNum; i++) {
                BzOrderSearchHistoryPO needDelete = historyPOList.get(i);
                removeById(needDelete);
            }
        }
       return save(bzOrderSearchHistoryPO);
    }

}
