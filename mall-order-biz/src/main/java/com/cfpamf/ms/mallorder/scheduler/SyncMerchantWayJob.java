package com.cfpamf.ms.mallorder.scheduler;

import com.cfpamf.ms.mallorder.service.IBzBankPayService;
import com.cfpamf.ms.mallorder.service.IPayMethodService;
import com.slodon.bbc.core.util.AssertUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Create 2021-09-17 14:36
 * @Description :0 59 23 * * ?  每天23.59同步当天新增的适用商户
 */
@Component
@Slf4j
public class SyncMerchantWayJob {

    @Autowired
    private IPayMethodService iPayMethodService;

    @XxlJob(value = "SyncMerchantWayJob")
    public ReturnT<String> execute(String s) {
        long startTime = System.currentTimeMillis();
        XxlJobHelper.log("start job SyncMerchantWayJob at local time: {0}", LocalDateTime.now());
        log.info("SyncMerchantWayJob() start");
        try {
            boolean jobResult = iPayMethodService.syncMerchantIntraday();
            AssertUtil.isTrue(!jobResult, "[SyncMerchantWayJob] 同步当天新增的适用商户失败");
        } catch (Exception e) {
            log.error("OrderAutoPayJob()", e);
        }
        XxlJobHelper.log("finish job SyncMerchantWayJob at local time: {0}  cost:{1}",
                LocalDateTime.now(),
                (System.currentTimeMillis() - startTime) / 1000);

        return new ReturnT("SUCCESS");
    }


}
