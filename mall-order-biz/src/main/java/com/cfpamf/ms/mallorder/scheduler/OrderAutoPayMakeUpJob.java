package com.cfpamf.ms.mallorder.scheduler;

import com.cfpamf.ms.mallorder.service.IBzBankPayService;
import com.slodon.bbc.core.util.AssertUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Create 2021-09-17 14:36
 * @Description :0 0/30 * * * ? 自动转账补偿接口
 */
@Component
@Slf4j
public class OrderAutoPayMakeUpJob {

    @Autowired
    private IBzBankPayService iBzBankPayService;

    @XxlJob(value = "OrderAutoPayMakeUpJob")
    public ReturnT<String> execute(String s) {
        long startTime = System.currentTimeMillis();
        XxlJobHelper.log("start job at local time: {0}", LocalDateTime.now());
        log.info("jobSystemCancelOrder() start");
        try {
            boolean jobResult = iBzBankPayService.orderAutoPayMakeUpJob();
            AssertUtil.isTrue(!jobResult, "[OrderAutoPayMakeUpJob] 定时任务系统自动付款补偿失败");
        } catch (Exception e) {
            log.error("OrderAutoPayMakeUpJob()", e);
        }
        XxlJobHelper.log("finish job at local time: {0}  cost:{1}",
                LocalDateTime.now(),
                (System.currentTimeMillis() - startTime) / 1000);

        return new ReturnT("SUCCESS");
    }


}
