package com.cfpamf.ms.mallorder.integration.facade.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class StockLackNotifyDto {

    @ApiModelProperty("需求库存")
    private String requestStockNum;

    @ApiModelProperty("当前库存")
    private String currentStockNum;

    @ApiModelProperty("货品id")
    private String spuId;

    @ApiModelProperty("货品名称")
    private String spuName;

    @ApiModelProperty("规格id")
    private String skuId;

    @ApiModelProperty("规格名称")
    private String skuName;

    @ApiModelProperty("仓库名称")
    private String depotName;

    @ApiModelProperty("单位")
    private String unit;

}
