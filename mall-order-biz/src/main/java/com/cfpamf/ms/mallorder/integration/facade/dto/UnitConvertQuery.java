package com.cfpamf.ms.mallorder.integration.facade.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Program: wms-service
 * @Description: 单位换算入参
 * @author: LS
 * @create: 2022-04-20 10:24
 **/
@Data
public class UnitConvertQuery {

    @ApiModelProperty("标识ID")
    private String tagId;

    @ApiModelProperty("批次号")
    private String batchNo;

    @ApiModelProperty("换算类型 1-销售转财务 2-财务转销售 3-销售转库存")
    private Integer convertType;

    @ApiModelProperty("sku编码")
    private String skuId;

    @ApiModelProperty("skuUnit编码")
    private String skuUnitCode;

    @ApiModelProperty("下单数量")
    private BigDecimal buyNum;

    @ApiModelProperty("价格")
    private BigDecimal price;

    @ApiModelProperty("单位编码")
    private String unitCode;
}
