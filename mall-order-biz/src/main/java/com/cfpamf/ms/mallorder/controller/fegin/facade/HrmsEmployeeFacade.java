package com.cfpamf.ms.mallorder.controller.fegin.facade;

import com.cdfinance.hrms.facade.request.outer.QueryEmployeeContactInfoRequest;
import com.cfpamf.common.ms.result.Result;

import com.cfpamf.ms.mallorder.vo.hrmsVO.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(
        name = "hrms-biz",
        url = "${hrms-biz.url}"
)
public interface HrmsEmployeeFacade {

    @PostMapping("/hrms/employee/mdm/queryOnboardEmployeeById")
    Result<OnBoardEmployeeVO> queryOnboardEmployeeById(@RequestParam("employeeId")Long employeeId);

    @GetMapping({"/hrms/employee/getByIdNo"})
    Result<EmployeeVO> getByIdNo(@RequestParam("idNo") String idNo);

    @PostMapping({"/hrms/employee/mdm/queryEmployeeContactInfo"})
    Result<OutEmployeeVO> queryEmployeeContactInfo(@RequestBody QueryEmployeeContactInfoRequest param);
}