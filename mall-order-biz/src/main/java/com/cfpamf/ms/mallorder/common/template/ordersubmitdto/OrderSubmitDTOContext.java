package com.cfpamf.ms.mallorder.common.template.ordersubmitdto;

import com.cfpamf.ms.mallorder.common.enums.OrderPatternEnum;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 *
 */
@Component
public class OrderSubmitDTOContext implements ApplicationContextAware {

    public static Map<OrderPatternEnum, OrderSubmitDTOCreator> ORDER_SUBMIT_DTO_CREATOR_MAP = new HashMap<>();

    private ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @PostConstruct
    public void initCreatorMap() {
        ORDER_SUBMIT_DTO_CREATOR_MAP.put(OrderPatternEnum.PURCHASE_CENTRE, applicationContext.getBean(PurchaseOrderSubmitDTOCreator.class));
        ORDER_SUBMIT_DTO_CREATOR_MAP.put(OrderPatternEnum.EXCHANGE_ORDER, applicationContext.getBean(ExchangeOrderSubmitDTOCreator.class));
        ORDER_SUBMIT_DTO_CREATOR_MAP.put(OrderPatternEnum.FARMERS_SERVICE, applicationContext.getBean(FarmerServiceOrderSubmitDTO.class));
        ORDER_SUBMIT_DTO_CREATOR_MAP.put(OrderPatternEnum.COUPON_CENTRE, applicationContext.getBean(CouponOrderSubmitDTOCreator.class));
    }

}
