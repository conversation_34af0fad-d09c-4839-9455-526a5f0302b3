package com.cfpamf.ms.mallorder.controller.fegin.facade.workflow.api;

import com.cfpamf.ms.mallorder.controller.fegin.facade.workflow.dto.ProcessStartRequestDto;
import com.cfpamf.ms.mallorder.controller.fegin.facade.workflow.dto.ProcessSuspendRequestDto;
import com.cfpamf.ms.mallorder.controller.fegin.facade.workflow.util.ResultData;
import com.cfpamf.ms.mallorder.controller.fegin.facade.workflow.vo.FlowNodeInfoVO;
import com.cfpamf.ms.mallorder.controller.fegin.facade.workflow.vo.ProcessInstanceVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

@FeignClient(name = "flow-manager", url = "${flow-manager.url:}", contextId = "flow-manager-process")
public interface ProcessInstanceFacade {

    @ApiOperation(value = "启动流程实例")
    @PostMapping("/workflow/processInstance/start")
    ResultData<String> startProcess(@RequestBody @Valid ProcessStartRequestDto dto);

    @ApiOperation("根据流程实例ID查询流程实例")
    @GetMapping("/workflow/processInstance/getById")
    ResultData<ProcessInstanceVo> getProcessInstanceByProcessInstanceId(@RequestParam("instanceId") String instanceId);

    @ApiOperation("中止流程")
    @PostMapping("/workflow/processInstance/suspend")
    ResultData<Void> suspendProcess(@RequestBody @Valid ProcessSuspendRequestDto dto);


    @ApiOperation("撤销流程")
    @PostMapping("/workflow/processInstance/revoke")
    ResultData<Void> revokeProcess(@RequestBody @Valid ProcessSuspendRequestDto dto);

    /**
     * 调用本接口查询流程节点列表，仅能查询该流程友哪些节点
     * @param definitionKey 流程定义key
     * @param deleteFlag 是否包含逻辑删除节点：默认包含
     * @return
     */
    @ApiOperation("查询节点列表")
    @GetMapping("/workflow/node/definitionKey/list")
    ResultData<List<FlowNodeInfoVO>> listNodes(@RequestParam("definitionKey") String definitionKey, @RequestParam("deleteFlag") Boolean deleteFlag);
}
