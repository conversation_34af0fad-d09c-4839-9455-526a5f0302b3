package com.cfpamf.ms.mallorder.controller.seller;

import com.alibaba.fastjson.JSON;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.common.help.ResultUtils;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.po.BzOrderProductInstallPO;
import com.cfpamf.ms.mallorder.req.OrderProductInstallSaveRequest;
import com.cfpamf.ms.mallorder.service.IBzOrderProductInstallService;
import com.cfpamf.ms.mallorder.vo.*;
import com.slodon.bbc.core.util.UserUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Api(tags = "front-订单商品安装资料补充")
@RestController
@RequestMapping("seller/order/product/install")
@Slf4j
public class SellerOrderProductInstallController {
    @Autowired
    private IBzOrderProductInstallService orderProductInstallService;

    @PostMapping("/save")
    @ApiOperation(value = "订单商品安装资料补充")
    public Result save(@RequestBody @Valid OrderProductInstallSaveRequest saveRequest) {
        orderProductInstallService.save(saveRequest);
        return ResultUtils.buildSuccessResult(null);
    }

    @GetMapping("/track/list")
    @ApiOperation(value = "查询安装资料轨迹")
    public Result<List<OrderProductInstallLogVO>> save(@RequestParam Long orderProductId) {
        List<OrderProductInstallLogVO> result = orderProductInstallService.queryTrackList(orderProductId);
        return ResultUtils.buildSuccessResult(result);
    }
}
