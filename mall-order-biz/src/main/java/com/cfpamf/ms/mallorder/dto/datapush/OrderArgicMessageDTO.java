package com.cfpamf.ms.mallorder.dto.datapush;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 农服喜报推送消息
 *
 */
@Data
public class OrderArgicMessageDTO {

	@ApiModelProperty("订单号")
	private String orderSn;

	@ApiModelProperty("商家ID")
	private Long storeId;

	@ApiModelProperty("订单实付金额")
	private BigDecimal orderAmount;

	@ApiModelProperty("订单商品金额")
	private BigDecimal goodsAmount;

	@ApiModelProperty("客户经理订单佣金")
	private BigDecimal orderCommission;

	@ApiModelProperty("订单总佣金")
	private BigDecimal orderTotalCommission;

	@ApiModelProperty("推送时间")
	private String pushTime;

	@ApiModelProperty("分支名称")
	private String branchName;

	@ApiModelProperty("区域名称")
	private String areaName;

	@ApiModelProperty("客户经理姓名")
	private String managerName;

	@ApiModelProperty("客户姓名")
	private String customerName;

	@ApiModelProperty(value = "商品分类数量")
	private Integer goodsCategoryNum;

	@ApiModelProperty(value = "商品一级分类")
	private String goodsCategory1;

	@ApiModelProperty(value = "商品二级分类")
	private String goodsCategory2;

	@ApiModelProperty(value = "商品三级分类")
	private String goodsCategory3;

	@ApiModelProperty(value = "二级分类下的商品id集合")
	private List<Long> productIdList;

	@ApiModelProperty("新媒体喜报商品名称 :多个商品取第一个商品")
	@JSONField(name = "goodsName")
	private String newMediaGoodsName;

	@ApiModelProperty("数量")
	private Integer weight;

	@ApiModelProperty("数量")
	private Integer unitNum;

	@ApiModelProperty("单位名称")
	private String unitName;

    @ApiModelProperty("新媒体喜报单位")
	private String newMediaUnitName;

	@ApiModelProperty("吨数")
	private BigDecimal ton;

	@ApiModelProperty("空气能-分支总销量")
	private Integer branchTotalNum = 0;

	@ApiModelProperty("空气能-区域总销量")
	private Integer areaTotalNum = 0;

	@ApiModelProperty("商品数量")
	private Integer productCount;

	@ApiModelProperty("清洁宝瓶数 = 商品数量 * 15")
	private Integer qjbBottleCount;

	public Integer getQjbBottleCount() {
        return productCount == null ? null : productCount * 15;
    }

    @ApiModelProperty("清洁宝订单数量")
    private Integer orderNum;

	@ApiModelProperty(value = "清洁喵瓶数 = 商品数量 * 15")
	private Integer qjmBottleCount;

	public Integer getQjmBottleCount() {
		return productCount == null ? null : productCount * 15;
	}


	@ApiModelProperty("清洁喵订单数量")
	private Integer qjmOrderNum;

	@ApiModelProperty("客户经理头像")
	private String imageUrl;

	@ApiModelProperty("物料名称")
	private String skuMaterialName;

	private String year;
	private String month;
	private String date;
	private Integer count;

	public Integer getCount() {
		return unitNum;
	}

	public String getDate() {
		return String.valueOf(LocalDateTime.now().getDayOfMonth());
	}

	public String getMonth() {
		return String.valueOf(LocalDateTime.now().getMonthValue());
	}

	public String getYear() {
		return String.valueOf(LocalDateTime.now().getYear());
	}
}
