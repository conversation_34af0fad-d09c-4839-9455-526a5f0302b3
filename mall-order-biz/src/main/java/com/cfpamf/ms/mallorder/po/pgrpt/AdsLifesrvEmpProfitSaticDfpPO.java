package com.cfpamf.ms.mallorder.po.pgrpt;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cfpamf.ms.mallorder.common.base.BasePO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wine.ads_lifesrv_emp_profit_static_dfp")
@ApiModel(value="AdsLifesrvEmpProfitSaticDfpPO对象", description="生服牛收益集市_员工日统计")
public class AdsLifesrvEmpProfitSaticDfpPO extends BasePO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "员工工号")
    private String empId;

    @ApiModelProperty(value = "员工姓名")
    private String empName;
    
    @ApiModelProperty(value = "分支编码")
    private String bchCode;

    @ApiModelProperty(value = "分支名称")
    private String bchName;

    @ApiModelProperty(value = "分支负责人工号")
    private String bchLeaderId;

    @ApiModelProperty(value = "分支负责人姓名")
    private String bchLeaderName;

    @ApiModelProperty(value = "片区编码")
    private String districtCode;

    @ApiModelProperty(value = "片区名称")
    private String districtName;

    @ApiModelProperty(value = "区域编码")
    private String areaCode;

    @ApiModelProperty(value = "区域名称")
    private String areaName;

    @ApiModelProperty(value = "岗位编码")
    private String jobName;

    @ApiModelProperty(value = "督导工号")
    private String spvsrId;

    @ApiModelProperty(value = "督导姓名")
    private String spvsrName;

    @ApiModelProperty(value = "是否在职 0否1是")
    private Integer isWork;

    @ApiModelProperty(value = "离职日期")
    private LocalDate leaveDate;

    @ApiModelProperty(value = "当年营收")
    private BigDecimal syAllRevenueAmt;

    @ApiModelProperty(value = "当月营收")
    private BigDecimal smAllRevenueAmt;

    @ApiModelProperty(value = "当年推广费")
    private BigDecimal syAllBaseCommissionAmt;

    @ApiModelProperty(value = "当月推广费")
    private BigDecimal smAllBaseCommissionAmt;
}
