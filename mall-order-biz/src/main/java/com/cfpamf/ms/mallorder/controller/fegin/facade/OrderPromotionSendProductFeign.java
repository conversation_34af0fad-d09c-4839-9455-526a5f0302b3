package com.cfpamf.ms.mallorder.controller.fegin.facade;

import java.util.List;
import javax.annotation.Resource;

import com.cfpamf.ms.mallorder.model.OrderPromotionSendProductModel;
import com.cfpamf.ms.mallorder.po.OrderPromotionSendProductPO;
import com.cfpamf.ms.mallorder.request.OrderPromotionSendProductExample;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 订单活动赠送货品表feign
 */
@RestController
@Slf4j
public class OrderPromotionSendProductFeign {


    @Resource
    private OrderPromotionSendProductModel orderPromotionSendProductModel;

    /**
     * 根据sendProductId获取订单活动赠送货品表详情
     *
     * @param sendProductId sendProductId
     * @return
     */
    @GetMapping("/v1/feign/business/orderPromotionSendProduct/get")
    public OrderPromotionSendProductPO getOrderPromotionSendProductBySendProductId(@RequestParam("sendProductId") Integer sendProductId) {
        return orderPromotionSendProductModel.getOrderPromotionSendProductBySendProductId(sendProductId);
    }

    /**
     * 获取条件获取订单活动赠送货品表列表
     *
     * @param example 查询条件信息
     * @return
     */
    @PostMapping("/v1/feign/business/orderPromotionSendProduct/getList")
    public List<OrderPromotionSendProductPO> getOrderPromotionSendProductList(@RequestBody OrderPromotionSendProductExample example) {
        return orderPromotionSendProductModel.getOrderPromotionSendProductList(example, example.getPager());
    }

    /**
     * 新增订单活动赠送货品表
     *
     * @param orderPromotionSendProductPO
     * @return
     */
    @PostMapping("/v1/feign/business/orderPromotionSendProduct/addOrderPromotionSendProduct")
    public Integer saveOrderPromotionSendProduct(@RequestBody OrderPromotionSendProductPO orderPromotionSendProductPO) {
        return orderPromotionSendProductModel.saveOrderPromotionSendProduct(orderPromotionSendProductPO);
    }

    /**
     * 根据sendProductId更新订单活动赠送货品表
     *
     * @param orderPromotionSendProductPO
     * @return
     */
    @PostMapping("/v1/feign/business/orderPromotionSendProduct/updateOrderPromotionSendProduct")
    public Integer updateOrderPromotionSendProduct(@RequestBody OrderPromotionSendProductPO orderPromotionSendProductPO) {
        return orderPromotionSendProductModel.updateOrderPromotionSendProduct(orderPromotionSendProductPO);
    }

    /**
     * 根据sendProductId删除订单活动赠送货品表
     *
     * @param sendProductId sendProductId
     * @return
     */
    @PostMapping("/v1/feign/business/orderPromotionSendProduct/deleteOrderPromotionSendProduct")
    public Integer deleteOrderPromotionSendProduct(@RequestParam("sendProductId") Integer sendProductId) {
        return orderPromotionSendProductModel.deleteOrderPromotionSendProduct(sendProductId);
    }
}