package com.cfpamf.ms.mallorder.service;


import com.cfpamf.ms.mallorder.dto.StoreLayoutDTO;
import com.cfpamf.ms.mallorder.vo.StoreLayoutVO;

public interface IOrderSellerService {

    /**
     * 判断店铺是否可以注销
     * 1.查询店铺是否有以下的订单
         * a。状态: 待付款、待签收
         * b。最新的订单交易成功时间大于 180天
     * 2，查询店铺是否有处理中的售后单，待商家审核、待平台审核。
     * @param storeLayoutDTO
     * @return
     */
    StoreLayoutVO layoutStore(StoreLayoutDTO storeLayoutDTO) throws Exception;
}
