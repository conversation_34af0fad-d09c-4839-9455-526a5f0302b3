package com.cfpamf.ms.mallorder.integration.erp;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.mallorder.integration.erp.vo.MallProductQuery;
import com.cfpamf.ms.mallorder.integration.erp.vo.ProductVO;
import com.cfpamf.ms.mallorder.integration.facade.ERPFacade;
import com.cfpamf.ms.mallorder.integration.facade.ErpProductAndSkuVo;
import com.cfpamf.ms.mallorder.integration.facade.dto.*;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.po.OrderAfterPO;
import com.cfpamf.ms.mallorder.po.OrderPerformanceBelongsPO;
import com.cfpamf.ms.mallorder.po.OrderProductPO;
import com.cfpamf.ms.mallorder.req.OrderDeliveryReq;
import com.cfpamf.ms.mallorder.service.IOrderProductService;
import com.cfpamf.ms.mallorder.service.IPerformanceService;
import com.cfpamf.ms.mallorder.vo.ErpDepotVo;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.response.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ERPIntegration {

    @Resource
    private ERPFacade erpFacade;

    @Resource
    private OrderModel orderModel;

    @Resource
    private IOrderProductService orderProductService;

    @Resource
    private IPerformanceService performanceService;

    public List<ProductVO> getProductInfoList(MallProductQuery query) {
        List<ProductVO> result = null;
        try {
            result = erpFacade.getProductInfoList(query);
            log.info("query erp productInfoList with materialCode:{}", JSONObject.toJSONString(query));
        } catch (Exception e) {
            log.error("通过物料编码获取货品信息列表异常，error:{0}", e);
        }
        if (result == null) {
            log.warn("调用erp系统返回为null,请求参数:{}", JSONObject.toJSONString(query));
        }
        return result;
    }

    /**
     * 根据unitCode批量获取对应的erp product
     *
     *
     * @param skuUnitCodeList unitCode 列表，8开头
     *
     * @return erp 货品vo
     */
    public List<ProductSkuUnitVO> getListBySkuUnitCode(List<String> skuUnitCodeList) {
        List<ProductSkuUnitVO> result = null;
        try {
            result = erpFacade.getListBySkuUnitCode(skuUnitCodeList);
            log.info("query erp productInfoList with materialCode:{}", JSONObject.toJSONString(skuUnitCodeList));
        } catch (Exception e) {
            log.error("根据unitCode批量获取对应的erp product，error:{0}", e);
        }
        if (result == null) {
            log.warn("调用erp系统返回为null,请求参数:{}", JSONObject.toJSONString(skuUnitCodeList));
        }
        return result;
    }

    /**
     * 根据单位编码，获取对应物料下所有单位编码
     * @param skuUnitCode
     * @return
     */
    public List<String> getSkuUnitCodeListBySkuUnitCode(String skuUnitCode) {

        Map<String, List<ProductSkuUnitVO>> result = null;
        try {
            result = erpFacade.getSkuUnitCodeListBySkuUnitCode(Collections.singletonList(skuUnitCode));
            log.info("connect erp-service SkuUnitCodeListBySkuUnitCode,result:{},skuUnitCode:{}", JSONObject.toJSONString(result), skuUnitCode);
        } catch (Exception e) {
            throw new BusinessException( "调用erp系统获取单位编码返回异常");
        }
        if (result == null) {
            throw new BusinessException("调用erp系统获取单位编码返回为null");
        }

        List<ProductSkuUnitVO> productSkuUnitVOS = result.get(skuUnitCode);
        if (CollectionUtils.isEmpty(productSkuUnitVOS)) {
            throw new BusinessException("调用erp系统获取单位编码返回数据为空");
        }

        List<String> collect = productSkuUnitVOS.stream().map(ProductSkuUnitVO::getSkuUnitCode).collect(Collectors.toList());
        collect.add(productSkuUnitVOS.get(0).getSkuId());
        return collect;
    }

    public List<UnitConvertVo> unitConvert(List<Map<String, Object>> maps) {
        List<UnitConvertQuery> query = new ArrayList<>(maps.size());
        for (Map<String, Object> map : maps) {
            UnitConvertQuery q = new UnitConvertQuery();
            // 3-销售转库存
            Integer convertType = 3;
            q.setConvertType(convertType);
            // map.get("deliveryQty") 实际会转化为一个BigDecimal对象
            q.setBuyNum(new BigDecimal(map.get("deliveryQty").toString()));
            q.setUnitCode(map.get("skuUnit").toString());
            String skuId = map.get("skuId").toString();
            if (skuId.startsWith("5")) {
                q.setSkuId(skuId);
            } else {
                q.setSkuUnitCode(skuId);
            }
            query.add(q);
        }
        List<UnitConvertVo> result;
        try {
            result = erpFacade.unitConvert(query);
            log.info("connect erp-service unitConvert, query:{}, result:{}", JSONObject.toJSONString(query), JSONObject.toJSONString(result));
        } catch (Exception e) {
            throw new BusinessException("调用erp系统进行单位转换异常");
        }
        if (result == null) {
            throw new BusinessException("调用erp系统进行单位转换返回为null");
        }

        return result;
    }

    public void stockOutPreCheck(String orderSn, Long storeId, Integer isSelfLift, String  branch, String depotCode,
                                 List<ErpStockProductDTO> productDetail, OrderPerformanceBelongsPO performanceBelongsPO,String actualWareHouseCode, OrderDeliveryReq deliveryReq) {
        StockCheckParamDTO stockCheckParamDTO = new StockCheckParamDTO();
        stockCheckParamDTO.setChannel(1);
        stockCheckParamDTO.setStoreId(storeId);
        stockCheckParamDTO.setPerformanceMode(isSelfLift);
        stockCheckParamDTO.setBranch(branch);
        stockCheckParamDTO.setDepotCode(depotCode);
        stockCheckParamDTO.setPerformanceSkus(productDetail);
        stockCheckParamDTO.setBusinessNo(orderSn);
        stockCheckParamDTO.setPerformanceType(performanceBelongsPO.getPerformanceType());
        stockCheckParamDTO.setBelongerEmployeeNo(performanceBelongsPO.getBelongerEmployeeNo());
        stockCheckParamDTO.setEmployeeBranchCode(performanceBelongsPO.getEmployeeBranchCode());
        stockCheckParamDTO.setActualDepotCode(actualWareHouseCode);
        stockCheckParamDTO.setActualDepotId(deliveryReq.getActualDepotId());
        stockCheckParamDTO.setOutboundOrderNo(deliveryReq.getOutboundOrderNo());
        stockCheckParamDTO.setExternalOrderNo(deliveryReq.getExternalOrderNo());
        JsonResult<Boolean> jsonResult;
        try {
            jsonResult = erpFacade.stockOutPreCheck(stockCheckParamDTO);
            log.info("connect erp-service stockOutPreCheck, query:{}, result:{}", JSONObject.toJSONString(stockCheckParamDTO), JSONObject.toJSONString(stockCheckParamDTO));
        } catch (Exception e) {
            throw new BusinessException("调用erp系统库存发货前置校验异常");
        }
        if (jsonResult == null || jsonResult.getData() == null) {
            throw new BusinessException("调用erp系统库存发货前置校验返回为null");
        }
        if (!jsonResult.getData()) {
            //库存不足特定编码,做钉钉提醒
            if(ErpConst.ERP_OUT_OF_STOCK_CODE_717 == jsonResult.getState()) {
                StockLackNotifyDto stockLackNotifyDto = JSONObject.parseObject(jsonResult.getMsg(), StockLackNotifyDto.class);
                if (orderModel.isSelfLift(orderSn)){
                    performanceService.outOfStackDingdingRemind(orderSn, stockLackNotifyDto.getSkuName(), stockLackNotifyDto.getSpuName(),
                            stockLackNotifyDto.getRequestStockNum(), stockLackNotifyDto.getCurrentStockNum());
                }
                throw new BusinessException("ERP库存不足,请先补充库存:" + stockLackNotifyDto.getSkuId() );
            } else {
                throw new BusinessException("ERP:" + jsonResult.getMsg());
            }
        }
    }

    /**
     * 校验退货退款出库流水是否出库成功
     */
    public boolean checkReturnInFlow(OrderAfterPO orderAfterPO) {
        // 查询商品channelSkuId，作为erp查询条件skuNo
        OrderProductPO orderProductPO = orderProductService.selectOneByOrderProductId(orderAfterPO.getOrderProductId());
        if (Objects.isNull(orderProductPO)) {
            log.warn("根据商品信息查询订单商品为空, orderProductId:{}", orderAfterPO.getOrderProductId());
            return false;
        }
        Result<Boolean> result;
        try {
            ErpActualReturnInFlowQueryDTO returnInFlowQueryDTO = new ErpActualReturnInFlowQueryDTO();
            returnInFlowQueryDTO.setLinkNo(orderAfterPO.getOrderSn());
            returnInFlowQueryDTO.setSkuNoList(Collections.singletonList(orderProductPO.getChannelNewSkuId()));
            result = erpFacade.checkReturnInFlow(returnInFlowQueryDTO);
        } catch (Exception e) {
            log.error("调用erp系统校验销售退货入库流水是否已入库成功异常,  售后单号:{}", orderAfterPO.getAfsSn(), e);
            throw new BusinessException("调用erp系统校验销售退货入库流水是否已入库成功异常");
        }
        return result.getData();
    }

    /**
     * 售后用户发货
     */
    public boolean orderAfterReturnGoods(String afsSn) {
        Result<Boolean> result;
        try {
            log.info("调用erp系统售后用户发货, afsSn:{}", afsSn);
            result = erpFacade.orderAfterReturnGoods(afsSn);
        } catch (Exception e) {
            log.error("调用erp系统售后用户发货异常, 售后单号:{}", afsSn, e);
            return false;
        }
        return result.getData();
    }

    /**
     * 根据仓库ID查询仓库信息
     */
    public ErpDepotVo getDepotById(Long depotId) {
        Result<ErpDepotVo> result;
        try {
            result = erpFacade.getDepotById(depotId);
        } catch (Exception e) {
            log.error("根据仓库ID查询仓库信息异常, depotId:{}", depotId, e);
            return null;
        }
        return result.getData();
    }

    /**
     * 查询商品单位信息
     */
    public ProductSkuUnitVO getUnitInfoBySkuUnitCode(String skuUnitCode, String unitTypeCode) {
        List<ProductSkuUnitVO> result;
        try {
            log.info("START 根据skuUnitCode查询单位信息, skuUnitCode:{}, unitTypeCode:{}", skuUnitCode, unitTypeCode);
            result = erpFacade.batchFindUnitListBySkuIdAndType(Collections.singletonList(skuUnitCode), unitTypeCode);
            log.info("END 根据skuUnitCode查询单位信息, skuUnitCode:{}, unitTypeCode:{}, result:{}", skuUnitCode, unitTypeCode, result);
        } catch (Exception e) {
            log.error("根据skuUnitCode查询单位信息异常, skuUnitCode:{}", skuUnitCode, e);
            return null;
        }
        return result.get(0);
    }


    public List<ErpProductAndSkuVo> getProductAndSkuListBySkuIds(List<String> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptyList();
        }
        Result<List<ErpProductAndSkuVo>> result;
        try {
            result = erpFacade.getProductAndSkuListBySkuIds(skuIds);
        } catch (Exception e) {
            log.error("根据skuIds查询商品信息异常, skuIds:{}", skuIds, e);
            return Collections.emptyList();
        }
        if (Objects.isNull(result)) {
            log.warn("根据skuIds查询商品信息为空, skuIds:{}", skuIds);
            return Collections.emptyList();
        }
        if (!result.isSuccess()) {
            log.warn("根据skuIds查询商品信息失败, skuIds:{}, result:{}", skuIds, StringUtils.isBlank(result.getErrorMsg()) ? result.getErrorMsg() : result.getMessage());
            return Collections.emptyList();
        }

        return result.getData();
    }

}
