package com.cfpamf.ms.mallorder.controller.fegin.facade.workflow.util;

import java.io.Serializable;

public class ResultData<T> implements Serializable {

    private String code = "200";
    private String msg;
    private T data;

    public ResultData() {
    }

    ;

    public ResultData(String code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public ResultData(String msg, T data) {
        this.msg = msg;
        this.data = data;
    }

    public ResultData(String msg) {
        this.msg = msg;
    }

    public static ResultData<Void> ofSuccess() {
        return new ResultData<>("success");
    }

    public static ResultData<Void> ofMsg(String msg) {
        return new ResultData<>(msg);
    }

    public static <T> ResultData<T> ofData(T data) {
        return new ResultData<>(null, data);
    }

    public static <T> ResultData<T> of(String msg, T data) {
        return new ResultData<>(msg, data);
    }

    public static <T> ResultData<T> of(String code, String msg, T data) {
        return new ResultData<>(code, msg, data);
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}