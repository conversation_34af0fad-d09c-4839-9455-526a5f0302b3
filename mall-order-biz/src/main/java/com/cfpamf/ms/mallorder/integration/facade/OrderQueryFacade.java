package com.cfpamf.ms.mallorder.integration.facade;

import com.cdfinance.mall.facade.vo.OrderDbcVO;
import com.cdfinance.mall.facade.vo.OrderTrackVO;
import com.cdfinance.mall.facade.vo.erp.OrderDetailsPO;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.mallpayment.facade.vo.OrderProductsVO;
import com.cfpamf.ms.mallorder.vo.OrderCountVo;
import com.cfpamf.ms.mallorder.vo.OrderLoanDetailVO;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 订单查询
 *
 * @version V1.0
 * @author: maoliang
 * @date: 2020-10-22
 */
@FeignClient(name = "mall-biz", url = "${mall-biz.url}")
public interface OrderQueryFacade {
    /**
     * 订单明细查询(用呗复核使用商品信息)
     * @param orderSn
     * @return
     */
    @GetMapping("/mallBiz/orderquery/orderDetail")
    Result<OrderLoanDetailVO> orderDetail(@RequestParam("orderSn") String orderSn);

    @GetMapping("/mallBiz/orderquery/countByStatus")
    @ApiOperation("根据订单状态统计数量")
    OrderCountVo countByStatus(@ApiParam("用户编号") @RequestParam(value = "userNo") String userNo);

    /**
     * 放款结果回调
     *
     * @param tnos
     * @return
     */
    @GetMapping("/mallBiz/orderquery/listProductsByTno")
    Result<List<OrderProductsVO>> listProductsByTno(@RequestParam("tnos") @NotNull List<Long> tnos);

    /**
     * 订单明细查询(决策使用商品信息)
     *
     * @param tno
     * @return
     */
    @GetMapping("/mallBiz/orderquery/orderInfo")
    Result<OrderProductsVO> orderInfo(@RequestParam("tno") @NotNull Long tno);

    /**
     * 订单轨迹查询
     *
     * @param expressCompany 快递公司编号
     * @param expressNo 快递单号
     * @return
     */
    @GetMapping("/mallBiz/orderquery/track")
    Result<OrderTrackVO> track(
            @RequestParam(value = "expressCompany") @NotBlank String expressCompany,
            @RequestParam(value = "expressNo") @NotBlank String expressNo
    );

    @GetMapping("/mallBiz/orderquery/dbc")
    Result<List<OrderDbcVO>> dbc(@RequestParam(value = "createTimeBeg") @NotBlank String createTimeBeg,
                                 @RequestParam(value = "createTimeEnd") @NotBlank String createTimeEnd,
                                 @RequestParam(value = "ono", required = false) Long ono);

    @PostMapping(value = "/mallBiz/orderquery/setImages4Erp", produces = "application/json;charset=utf-8")
    Result<List<OrderDetailsPO>> setImages4Erp(@RequestBody @NotNull @Valid List<OrderDetailsPO> orderList);


    @GetMapping("/mallBiz/orderquery/checkIsNewUser")
    @ApiOperation("判断用户是否为新人（未曾下单）,true为新人，false则反")
    Result<Boolean> checkIsNewUser(@ApiParam("用户编号") @RequestParam(value = "userNo") String userNo
            ,@ApiParam("用户手机号") @RequestParam(value = "userMobile") String userMobile);
}
