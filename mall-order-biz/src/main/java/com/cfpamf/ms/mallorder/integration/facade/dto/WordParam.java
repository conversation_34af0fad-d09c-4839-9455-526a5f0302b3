package com.cfpamf.ms.mallorder.integration.facade.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 通用文字识别参数
 *
 * <AUTHOR>
 * @since 2024/10/22
 */
@Data
public class WordParam {

    @ApiModelProperty(value = "文件地址")
    private String fileUrl;

    @ApiModelProperty(value = "是否mock,true - mock，false-不mock")
    private String mock;

    @ApiModelProperty(value = "请求id")
    private String requestId;

    @ApiModelProperty(value = "消费方,固定为newmall即可")
    private String consumer;
}
