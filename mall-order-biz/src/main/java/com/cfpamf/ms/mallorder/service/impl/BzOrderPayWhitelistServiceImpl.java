package com.cfpamf.ms.mallorder.service.impl;

import com.cfpamf.ms.mallorder.po.BzOrderPayWhitelistPO;
import com.cfpamf.ms.mallorder.mapper.BzOrderPayWhitelistMapper;
import com.cfpamf.ms.mallorder.service.IBzOrderPayWhitelistService;
import com.cfpamf.framework.autoconfigure.mybatis.BaseRepoServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 支付黑名单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-19
 */
@Service
public class BzOrderPayWhitelistServiceImpl extends BaseRepoServiceImpl<BzOrderPayWhitelistMapper, BzOrderPayWhitelistPO> implements IBzOrderPayWhitelistService {

    @Autowired
    private BzOrderPayWhitelistMapper bzOrderPayWhitelistMapper;

    /**
     * 根据店铺id查询支付黑名单
     * @param storeId storeId
     * @return
     */
    @Override
    public List<BzOrderPayWhitelistPO> getPayMethodByStoreId(Long storeId) {
        return bzOrderPayWhitelistMapper.getPayMethodByStoreId(storeId);
    }
}
