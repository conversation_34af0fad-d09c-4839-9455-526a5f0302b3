package com.cfpamf.ms.mallorder.integration.facade;


import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.loan.facade.vo.CmisContractFileVo;

/**
 * @Author: zengzhen
 * @Date: 2019-07-29 14:31
 * @Version 1.0
 * @Desc
 */
@FeignClient(value = "ms-service-loan",url = "${ms-service-loan.url}")
public interface ContractFacade {

    /**
     * 获取授信合同号
     * @param applyId
     * @return
     */
    @GetMapping(value = "/loan/contract/getCreditContractNoByApplyId")
    Result<String> getCreditContractNoByApplyId(@RequestParam(value = "applyId") String applyId);

    /**
     * 根据申请单编号/订单号获取所有合同信息
     * @param applyId
     * @return
     * @throws Exception
     */
    @GetMapping(value = "/loan/contract/getAllContractByApplyId")
    Result<List<CmisContractFileVo>> getAllContractByApplyId(@RequestParam(value = "applyId") String applyId) throws Exception;
}
