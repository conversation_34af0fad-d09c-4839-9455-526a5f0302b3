package com.cfpamf.ms.mallorder.controller.employee;

import java.util.List;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.cfpamf.ms.mallorder.req.HouseholdCleanSalesReportRequest;
import com.cfpamf.ms.mallorder.service.HouseholdCleanWorkbenchService;
import com.cfpamf.ms.mallorder.vo.HouseholdCleanCategoryProportionSalesAmountVO;
import com.cfpamf.ms.mallorder.vo.HouseholdCleanSalesOrgPerformanceVO;
import com.cfpamf.ms.mallorder.vo.HouseholdCleanSalesReportVO;
import com.cfpamf.ms.mallorder.vo.HouseholdCleanSalesStaffPerformanceVO;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "生服牛工作台家清API")
@RestController
@RequestMapping("/household/clean/workbench")
public class HouseholdCleanWorkbenchController {

	@Autowired
	private HouseholdCleanWorkbenchService householdCleanWorkbenchService;

	@ApiOperation(value = "查询销售报表数据")
	@PostMapping("/querySalesReport")
	public JsonResult<HouseholdCleanSalesReportVO> querySalesReport(
			@RequestBody @Valid HouseholdCleanSalesReportRequest request) {
		return SldResponse.success(householdCleanWorkbenchService.querySalesReport(request));
	}

	@ApiOperation(value = "查询销售机构TOP10")
	@PostMapping("/queryTop10SalesOrgPerformance")
	public JsonResult<List<HouseholdCleanSalesOrgPerformanceVO>> queryTop10SalesOrgPerformance(
			@RequestBody @Valid HouseholdCleanSalesReportRequest request) {
		return SldResponse.success(householdCleanWorkbenchService.queryTop10SalesOrgPerformance(request));
	}

	@ApiOperation(value = "查询销售人员TOP10")
	@PostMapping("/queryTop10SalesStaffPerformance")
	public JsonResult<List<HouseholdCleanSalesStaffPerformanceVO>> queryTop10SalesStaffPerformance(
			@RequestBody @Valid HouseholdCleanSalesReportRequest request) {
		return SldResponse.success(householdCleanWorkbenchService.queryTop10SalesStaffPerformance(request));
	}

	@ApiOperation(value = "查询货品金额占比")
	@PostMapping("/queryProportionSalesAmountByCategory")
	public JsonResult<List<HouseholdCleanCategoryProportionSalesAmountVO>> queryProportionSalesAmountByCategory(
			@RequestBody @Valid HouseholdCleanSalesReportRequest request) {
		return SldResponse.success(householdCleanWorkbenchService.queryProportionSalesAmountByCategory(request));
	}

}
