package com.cfpamf.ms.mallorder.integration.bms;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.framework.autoconfigure.common.exception.MSException;
import com.cfpamf.ms.bms.facade.vo.OrganizationBaseVO;
import com.cfpamf.ms.bms.facade.vo.OrganizationVO;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.cfpamf.ms.mallorder.integration.facade.BmsOrgFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class BmsOrgFeignIntegration {

    @Resource
    private BmsOrgFacade bmsOrgFacade;

    public OrganizationVO getOrganizationByHrOrgId(Long hrOrgId) {
        if (Objects.isNull(hrOrgId)) {
            return null;
        }
        Result<OrganizationVO> result = null;
        try {
            log.info("根据hr系统的组织机构orgId获取组织机构详情,hrOrgId:{}", hrOrgId);
            result = bmsOrgFacade.getOrganizationByHrOrgId(null, hrOrgId);
        } catch (Exception ex) {
            log.error("根据hrOrgId获取组织机构详情出现未知异常,hrOrgId:{}", hrOrgId, ex);
            throw new MSException(String.valueOf(ErrorCodeEnum.C.CALL_EXCEPTION.getCode()), "根据hrOrgId获取组织机构详情调用BMS系统异常");
        }
        if (result == null || !result.isSuccess() || result.getData() == null) {
            throw new MSException(String.valueOf(ErrorCodeEnum.C.RESULT_INVALID.getCode()), "根据hrOrgId获取组织机构详情调用BMS系统失败");
        }
        log.info("根据hr系统的组织机构orgId获取组织机构详情,返回结果:{}", JSONObject.toJSONString(result.getData()));
        return result.getData();
    }

    public OrganizationVO getOrganizationByOrgCode(String orgCode) {
        if (orgCode == null) {
            return null;
        }
        Result<OrganizationVO> result = null;
        try {
            log.info("根据组织机构编码orgCode获取组织机构详情,orgCode:{}", orgCode);
            result = bmsOrgFacade.getOrganizationByOrgCode(null, orgCode);
        } catch (Exception ex) {
            log.error("根据orgCode获取组织机构详情出现未知异常,orgCode:{}", orgCode, ex);
            throw new MSException(String.valueOf(ErrorCodeEnum.C.CALL_EXCEPTION.getCode()), "根据orgCode获取组织机构详情调用BMS系统异常");
        }
        if (result == null || !result.isSuccess() || result.getData() == null) {
            throw new MSException(String.valueOf(ErrorCodeEnum.C.RESULT_INVALID.getCode()), "根据orgCode获取组织机构详情调用BMS系统失败");
        }
        log.info("根据组织机构编码orgCode获取组织机构详情,返回结果:{}", JSONObject.toJSONString(result.getData()));
        return result.getData();
    }

    public List<OrganizationBaseVO> listBranchesByHrOrgCodesV2(String hrOrgCodes) {
        if (StringUtils.isEmpty(hrOrgCodes)) {
            return Arrays.asList();
        }
        Result<List<OrganizationBaseVO>> result = null;
        try {
            log.info("根据hrOrgCode所对应的级别获取对应下的所有子孙的分支信息,orgCode:{}", hrOrgCodes);
            result = bmsOrgFacade.listBranchesByHrOrgCodesV2(hrOrgCodes);
        } catch (Exception ex) {
            log.error("根据hrOrgCode所对应的级别获取对应下的所有子孙的分支信息,orgCode:{}", hrOrgCodes, ex);
            throw new MSException(String.valueOf(ErrorCodeEnum.C.CALL_EXCEPTION.getCode()), "根据hrOrgCode所对应的级别获取对应下的所有子孙的分支信息调用BMS系统异常");
        }
        if (result == null || !result.isSuccess() || result.getData() == null) {
            throw new MSException(String.valueOf(ErrorCodeEnum.C.RESULT_INVALID.getCode()), "根据hrOrgCode所对应的级别获取对应下的所有子孙的分支信息调用BMS系统失败");
        }
        log.info("根据hrOrgCode所对应的级别获取对应下的所有子孙的分支信息,返回结果:{}", JSONObject.toJSONString(result.getData()));
        return result.getData();
    }

}
