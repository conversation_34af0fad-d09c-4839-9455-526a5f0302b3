package com.cfpamf.ms.mallorder.scheduler;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.enums.OrderStatusEnum;
import com.cfpamf.ms.mallorder.common.enums.PayMethodEnum;
import com.cfpamf.ms.mallorder.common.enums.PresellCapitalTypeEnum;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderPresellPO;
import com.cfpamf.ms.mallorder.service.IOrderService;
import com.cfpamf.ms.mallorder.v2.service.OrderPresellService;
import com.cfpamf.ms.mallpromotion.constant.PromotionConst;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 系统自动取消没有付款的预售订单
 * cron:0 0/5 * * * ?
 */
@Component
@Slf4j
public class OrderCancelPreSellDepositJob {

    @Resource
    private OrderPresellService orderPresellService;

    @Resource
    private IOrderService orderService;

    @Resource
    private OrderModel orderModel;


    @XxlJob(value = "OrderCancelPreSellDepositJob")
    public ReturnT<String> execute(String s) {

        long startTime = System.currentTimeMillis();
        XxlJobHelper.log("start OrderCancelPreSellDepositJob local time: {}", LocalDateTime.now());

        log.info("start OrderCancelPreSellJob local time");
        try {
            // 查询超时的待支付定金的预售订单号，只取最近5天的数据
            LambdaQueryWrapper<OrderPresellPO> presellOrderQuery = Wrappers.lambdaQuery(OrderPresellPO.class);
            presellOrderQuery.eq(OrderPresellPO::getType, PresellCapitalTypeEnum.DEPOSIT.getValue())
                    .eq(OrderPresellPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                    .le(OrderPresellPO::getDeadTime, LocalDateTime.now())
                    .ge(OrderPresellPO::getDeadTime, LocalDateTime.now().plusDays(-5))
                    .and(wrapper -> wrapper.eq(OrderPresellPO::getPayStatus, CommonConst.PAY_STATUS_1).or(
                            wrapper2 -> wrapper2.eq(OrderPresellPO::getPayStatus, CommonConst.PAY_STATUS_2).
                                    eq(OrderPresellPO::getPaymentCode, PayMethodEnum.BANK_TRANSFER.getValue())
                            )
                    );
            List<OrderPresellPO> presellsOrder = orderPresellService.list(presellOrderQuery);
            XxlJobHelper.log("系统自动取消没有付定金的预售订单，查询预付订单未付定金数为：{}", presellsOrder.size());
            log.info("系统自动取消没有付定金的预售订单，查询预付订单未付定金数为：{}", presellsOrder.size());

            if (!CollectionUtils.isEmpty(presellsOrder)) {
                // 查询出对应的订单
                LambdaQueryWrapper<OrderPO> orderQuery = Wrappers.lambdaQuery(OrderPO.class);
                orderQuery.in(OrderPO::getOrderSn, presellsOrder.stream().map(OrderPresellPO::getOrderSn).collect(Collectors.toList()))
                        .eq(OrderPO::getOrderType, PromotionConst.PROMOTION_TYPE_107)
                        .eq(OrderPO::getOrderState, OrderStatusEnum.WAIT_PAY_DEPOSIT.getValue())
                        .eq(OrderPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
                List<OrderPO> orderPos = orderService.list(orderQuery);
                orderModel.cancelOrder(orderPos, OrderConst.WAIT_PAY_ORDER_EXPIRE_REASON, null, OrderConst.LOG_ROLE_ADMIN,
                        OrderConst.LOG_USER_ID, OrderConst.LOG_USER_NAME, "系统自动取消预售订单", OrderConst.RETURN_BY_0);
            }
        } catch (Exception e) {
            log.error("OrderCancelPreSellDepositJob()", e);
        }

        XxlJobHelper.log("end OrderCancelPreSellDepositJob local time: {}", LocalDateTime.now(), (System.currentTimeMillis() - startTime) / 1000);

        return ReturnT.SUCCESS;
    }
}
