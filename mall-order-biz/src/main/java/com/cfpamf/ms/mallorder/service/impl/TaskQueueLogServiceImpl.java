package com.cfpamf.ms.mallorder.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cfpamf.ms.mallorder.mapper.TaskQueueLogMapper;
import com.cfpamf.ms.mallorder.mapper.TaskQueueMapper;
import com.cfpamf.ms.mallorder.po.TaskQueueLogPO;
import com.cfpamf.ms.mallorder.po.TaskQueuePO;
import com.cfpamf.ms.mallorder.service.ITaskQueueLogService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 定时任务推送日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-01-08
 */
@Service
public class TaskQueueLogServiceImpl extends ServiceImpl<TaskQueueLogMapper, TaskQueueLogPO> implements ITaskQueueLogService {


    @Resource
    TaskQueueLogMapper taskQueueLogMapper;

    @Override
    public void saveLog(TaskQueuePO task, TaskQueueLogPO taskLog) {
        taskLog.setTaskId(task.getId());
        taskLog.setExecuteOrder(task.getExecuteCount());
        taskLog.setBizId(task.getBizId());
        taskLog.setBizType(task.getBizType());
        taskLog.setStatus(task.getStatus());
        taskLog.setUpdateBy(task.getUpdateBy());
        taskLog.setCreateBy(task.getCreateBy());
        this.save(taskLog);
    }

    @Override
    public int modifyTaskQueueLog(Long bizId, Integer bizType, Long excludeId) {
        return taskQueueLogMapper.modifyTaskQueueLog(bizId,bizType,excludeId);
    }
}
