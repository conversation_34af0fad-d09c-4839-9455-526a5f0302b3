//package com.cfpamf.ms.mallorder.v2.proxy;
//
//import java.lang.reflect.Method;
//import java.util.Date;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.Lazy;
//
//import com.cfpamf.ms.mallorder.model.OrderPayModel;
//import com.cfpamf.ms.mallorder.po.OrderPO;
//import com.cfpamf.ms.mallorder.v2.config.CommonConfig;
//import com.cfpamf.ms.mallorder.v2.service.OrderPayProcessService;
//
//import lombok.extern.slf4j.Slf4j;
//import net.sf.cglib.proxy.Enhancer;
//import net.sf.cglib.proxy.MethodInterceptor;
//import net.sf.cglib.proxy.MethodProxy;
//
//@Slf4j
//@Configuration
//public class OrderPayModelServiceCglibProxy implements MethodInterceptor {
//
//    @Lazy
//    @Autowired
//    private OrderPayProcessService orderPayProcessService;
//
//    @Autowired
//    private CommonConfig commonConfig;
//
//    @Bean(name = "orderPayModel")
//    public OrderPayModel getInstance() {
//        Enhancer enhancer = new Enhancer();
//        // 设置要增强的类, cglib实际上是 继承委托类
//        enhancer.setSuperclass(OrderPayModel.class);
//        enhancer.setCallback(this);
//        return (OrderPayModel)enhancer.create();
//    }
//
//    @Override
//    public Object intercept(Object obj, Method method, Object[] args, MethodProxy proxy) throws Throwable {
//        // 当V2流程打开，OrderPayModel类中orderPaySuccess，会执行代理类代码
//        if ("orderPaySuccess".equalsIgnoreCase(method.getName()) && commonConfig.isOpenV2()) {
//            log.info("===OrderPayModel.orderPaySuccess方法被代理，执行V2提交订单流程===");
//            orderPayProcessService.orderPaySuccess((OrderPO)args[0], (String)args[1], (String)args[2], (String)args[3],
//                (Date)args[4]);
//            log.info("===OrderPayModel.orderPaySuccess方法被代理，执行V2结束订单流程===");
//            return null;
//        }
//        Object result = proxy.invokeSuper(obj, args);
//        return result;
//    }
//
//}
