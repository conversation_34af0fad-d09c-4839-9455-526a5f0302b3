package com.cfpamf.ms.mallorder.integration.cashier.request;


import com.alibaba.fastjson.annotation.JSONType;
import com.baomidou.mybatisplus.core.enums.IEnum;

/**
 * 记息差开始方式
 *
 * @Author: maoliang
 * @Date: 2021/11/22
 */
@JSONType(serializeEnumAsJavaBean = true)
public enum InterestStartType implements IEnum<Integer> {
    FIXED_DATE(1, "固定日期"),
    CUSTOMER_CONFIRM(2, "确认后N天");
    private Integer value;
    private String desc;

    InterestStartType(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static InterestStartType valueOf(int value){
        for(InterestStartType ps : InterestStartType.values()){
            if (value == ps.value){
                return ps;
            }
        }
        return null;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }

    public String getDesc() {
        return this.desc;
    }
}
