package com.cfpamf.ms.mallorder.controller.fegin.facade.workflow.dto;

import com.cfpamf.ms.mallorder.controller.fegin.facade.workflow.constant.UserIdTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ProcessStartRequestDto {

    @ApiModelProperty(value = "业务系统ID", required = true)
    private Integer systemId = 38;

    @NotBlank(message = "流程定义的key不能为空")
    @ApiModelProperty(value = "流程图定义时的ID", required = true)
    private String procDefKey;

    @NotBlank(message = "业务ID不能为空")
    @ApiModelProperty(value = "业务对象的主键", required = true)
    private String bizId;

    @NotBlank(message = "提交者用户ID不能为空")
    @ApiModelProperty(value = "提交者用户ID", required = true)
    private String submitterId;

    @NotNull(message = "提交者用户ID类型不能为空")
    @ApiModelProperty(value = "提交者用户ID类型：工号--JOB_NO; bms用户ID--BMS_USER_ID；钉钉用户ID--DING_USER_ID", required = true)
    private UserIdTypeEnum submitterIdType;

    @ApiModelProperty(value = "流程变量")
    private Map<String, Object> vars = new HashMap<>();
}
