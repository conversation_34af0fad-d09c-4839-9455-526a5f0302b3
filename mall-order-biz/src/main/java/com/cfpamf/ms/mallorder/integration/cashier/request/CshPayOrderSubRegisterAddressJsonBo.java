package com.cfpamf.ms.mallorder.integration.cashier.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Create 2022-08-29 14:40
 * @Description :店铺注册地址信息
 */
@Data
public class CshPayOrderSubRegisterAddressJsonBo {

    @ApiModelProperty(value = "省id")
    private String provinceId;

    @ApiModelProperty(value = "省")
    private String province;

    @ApiModelProperty(value = "市id")
    private String cityId;

    @ApiModelProperty(value = "市")
    private String city;

    @ApiModelProperty(value = "区Id")
    private String countyId;

    @ApiModelProperty(value = "区")
    private String county;

    @ApiModelProperty(value = "乡镇Id")
    private String townId;

    @ApiModelProperty(value = "乡镇")
    private String town;

    @ApiModelProperty(value = "是否需要查询四级地址标识 0-不需要，1-需要")
    private Integer townFlag;

    @ApiModelProperty(value = "详细地址")
    private String address;

}
