package com.cfpamf.ms.mallorder.service;

import com.cfpamf.ms.mallorder.dto.BzOrderTradeProofCheckResultDTO;
import com.cfpamf.ms.mallorder.dto.TransactionVerifyDTO;
import com.cfpamf.ms.mallorder.po.BzOrderTradeProofCheckResultPO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 订单交易凭证校验结果表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-21
 */
public interface IBzOrderTradeProofCheckResultService extends IService<BzOrderTradeProofCheckResultPO> {

    List<TransactionVerifyDTO> orderTradeProofQueryResult(String orderNo);

    List<BzOrderTradeProofCheckResultDTO> orderTradeProofBatchQueryResult(List<String> orderNoList);
}
