package com.cfpamf.ms.mallorder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.ms.mallorder.dto.*;
import com.cfpamf.ms.mallorder.po.OrderProductPO;
import com.cfpamf.ms.mallorder.request.OrderProductExample;
import com.cfpamf.ms.mallorder.vo.*;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 订单商品 service 接口
 *
 * <AUTHOR>
 * @date 2021/07/18 17:00
 * @return
 */
public interface IOrderProductService extends IService<OrderProductPO> {

    boolean addReturnNumber(Long orderProductId, int num);

    boolean deductReturnNumber(Long orderProductId, int num);

    /**
     * 处理商品的退款状态
     *
     * @param handleType        操作类型
     * @param orderProductId    订单商品行ID
     * @return 处理结果
     */
    boolean dealOrderProductReturnStatus(String handleType, Long orderProductId);

    boolean returnAll(String orderSn);

    Boolean isAllDelivery(String orderSn);

    /**
     * 处理订单商品为已拼单
     * @param orderProductIds
     * @return
     */
    Boolean dealOrderProductGroupBuyingTag(List<Long> orderProductIds);

    /**
     * 订单商品物流
     *
     * @param orderSn
     * @param orderProductIds
     * @param logisticId
     * @return
     */
    Boolean updateLogistic(String orderSn, List<Long> orderProductIds, Long logisticId);

    /**
     * 订单整单发货
     * 
     * @param orderSn
     * @return
     */
    Boolean orderProductDelivery(String orderSn);

    Map<String, OrderProductVO> bizOrderProducts(List<String> bizSnList);

    List<OrderProductPO> listByOrderSn(String orderSn);

    List<OrderProductPO> listByOrderSns(Set<String> orderSns);

    /**
     * @param orderSn
     * @return com.cfpamf.ms.mallorder.vo.OrderDetailVO
     * @description :订单详情（用呗复核使用）
     */
    OrderLoanDetailVO orderDetail(String orderSn);

    /**
     * 根据主键查询订单商品明细
     * 
     * @param orderProductId
     * @return
     */
    OrderProductPO selectOneByOrderProductId(Long orderProductId);

    /**
     * 订单取消后更新商品的已退数量
     *
     * @param orderSn
     */
    void updateProductReturnNumAfterCancel(String orderSn);

    /**
     * 查询售后商品信息
     *
     * @param orderSn   订单号
     * @return          售后商品信息
     */
    List<AfsOrderProductVOV2> getAfsOrderProductList(String orderSn);

    /**
     * 获取条件获取订单货品明细表列表 VO 对象
     *
     * @param example 查询条件信息
     * @return        商品 VO 信息
     */
    List<OrderProductInfoVO> getOrderProductInfoVOList(@RequestBody OrderProductExample example);

    // Boolean saveFullGiftSendOrderProduct(OrderSubmitDTO.OrderInfo orderInfo, Integer memberId, String areaCode, String ruleCode, OrderExtendPO orderExtendPO, OrderPO orderPO);

    /**
     * 按返利活动查询条件查询商品快照信息
     * @param dto
     * @return
     */
    List<OrderProductRebateVO> queryByRebateProduct(OrderProductRebateDTO dto);

    /**
     * 农服购买物料返利活动查询条件查询商品快照信息
     * @param dto
     * @return
     */
    List<OrderProductRebateVO> queryBuyCommodityRebateProduct(BuyCommodityRebateProductDTO dto);


    /**
     * 酒水满十赠一返利活动查询商品信息
     * @param dto
     * @return
     */
    List<OrderProductRebateVO> queryBuyWineGift(BuyWineRebateProductDTO dto);

    /**
     * 同步销项税率
     *
     */
    void syncTaxRate();

    /**
     * 历史skuid 处理
     *
     * @param param 参数，可以选定order productId进行更新，如果没有设置，则默认更新全量数据
     */
    void dealHistorySkuId(String param);

    Boolean updateProductDeliveryState(ProductDeliveryDTO dto);

    /**
     * 天杰满十赠一返利活动查询商品信息
     * @param dto
     * @return
     */
    List<OrderProductRebateVO> tjBuyNzRebateGift(@RequestBody @Valid TjBuyNzRebateProductDTO dto);

    /**
     * 根据userNo和sku查询用户最新订单的收货地址
     * @param userNo
     * @param skuId
     * @return
     */
    OrderAddressDTO queryUserAddress(String userNo, Long skuId);

    /**
     * 查询订单商品列表
     *
     * @param list 查询dto列表
     * @return 订单编号列表
     */
    List<HistoryDealOrderEventNotifyDTO> queryOrderSnByProductIdAndFinanceRule(List<AgricQueryOrderDTO> list);

    /**
     * 根据订单编号查询订单商品列表
     *
     * @param orderQueryDTO 查询dto
     * @return 订单编号列表
     */
    List<HistoryDealOrderEventNotifyDTO> queryOrderInfoByOrderIdList(DbcOrderQueryDTO orderQueryDTO);
}
