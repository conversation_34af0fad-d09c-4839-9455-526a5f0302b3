//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//

package com.cfpamf.ms.mallorder.controller.fegin.facade;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.trade.facade.request.AcctPayStatRequest;
import com.cfpamf.ms.trade.facade.request.BatchPayRequest;
import com.cfpamf.ms.trade.facade.request.ClientRepayRequest;
import com.cfpamf.ms.trade.facade.request.PayAbnormalRequest;
import com.cfpamf.ms.trade.facade.request.PayRequest;
import com.cfpamf.ms.mallorder.req.PayV2Request;
import com.cfpamf.ms.trade.facade.request.QueryBankDirectDTO;
import com.cfpamf.ms.trade.facade.request.StrategyRequest;
import com.cfpamf.ms.trade.facade.vo.AcctPayStatVo;
import com.cfpamf.ms.trade.facade.vo.AcctPendingPayStatVo;
import com.cfpamf.ms.trade.facade.vo.BaseQueryBalanceVo;
import com.cfpamf.ms.trade.facade.vo.ClientRepayVo;
import com.cfpamf.ms.trade.facade.vo.PageResultVo;
import com.cfpamf.ms.trade.facade.vo.PayAbnormalDetailVo;
import com.cfpamf.ms.trade.facade.vo.PayRequestDetailVo;
import com.cfpamf.ms.trade.facade.vo.PayRequestVo;
import com.cfpamf.ms.trade.facade.vo.QueryBankDirectResultVo;
import com.cfpamf.ms.trade.facade.vo.UrgencyPayVo;
import java.util.List;
import javax.validation.Valid;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(
    value = "core-trade-service-biz",
    url = "${core-trade-service.url}"
)
public interface PayFacade {
    @PostMapping(
        path = {"/pay"},
        consumes = {"application/json"},
        produces = {"application/json"}
    )
    Result<String> pay(@Valid @RequestBody PayRequest var1);

    @PostMapping(
        path = {"/batchPay"},
        consumes = {"application/json"},
        produces = {"application/json"}
    )
    Result<String> batchPay(@Valid @RequestBody BatchPayRequest var1);

    @PostMapping(
        path = {"/pay/v2"},
        consumes = {"application/json"},
        produces = {"application/json"}
    )
    Result<String> payV2(@Valid @RequestBody PayV2Request var1);

    @PostMapping(
        path = {"/queryPayResult"},
        consumes = {"application/json"},
        produces = {"application/json"}
    )
    Result<PayRequestVo> queryPayResult(@RequestParam(name = "tradeNo") String var1);

    @PostMapping(
        path = {"/queryBalance"},
        consumes = {"application/json"},
        produces = {"application/json"}
    )
    Result<BaseQueryBalanceVo> queryBalance(@RequestParam(name = "accountNo") String var1, @RequestParam("bankNo") String var2);

    @PostMapping(
        path = {"/queryPayDetailResult"},
        consumes = {"application/json"},
        produces = {"application/json"}
    )
    Result<List<PayRequestDetailVo>> queryPayDetailResult(@RequestParam(name = "tradeNo") String var1);

    @PostMapping(
        path = {"/queryBankDirectResult"},
        consumes = {"application/json"},
        produces = {"application/json"}
    )
    Result<PageResultVo<QueryBankDirectResultVo>> queryBankDirectResult(@RequestBody QueryBankDirectDTO var1);

    @PostMapping(
        path = {"/changeStrategy"},
        consumes = {"application/json"},
        produces = {"application/json"}
    )
    Result<String> changeStrategy(@RequestBody StrategyRequest var1);

    @GetMapping(
        path = {"/getStrategy"},
        produces = {"application/json"}
    )
    Result<String> getStrategy();

    @PostMapping(
        path = {"/urgencyPay"},
        consumes = {"application/json"},
        produces = {"application/json"}
    )
    Result<String> urgencyPay(@RequestParam(name = "clientId") String var1, @RequestParam(name = "cardNo") String var2);

    @PostMapping(
        path = {"/urgency"},
        consumes = {"application/json"},
        produces = {"application/json"}
    )
    Result<UrgencyPayVo> urgency(@RequestParam(name = "applSeq") String var1);

    @PostMapping(
        path = {"/cancelPay"},
        consumes = {"application/json"},
        produces = {"application/json"}
    )
    Result<String> cancelPay(@RequestParam(name = "tradeNo") String var1);

    @PostMapping(
        path = {"/rePay"},
        consumes = {"application/json"},
        produces = {"application/json"}
    )
    Result<ClientRepayVo> rePay(@RequestBody ClientRepayRequest var1);

    @PostMapping(
        path = {"/payAbnormal"},
        consumes = {"application/json"},
        produces = {"application/json"}
    )
    Result<String> payAbnormal(@RequestBody PayAbnormalRequest var1);

    @RequestMapping(
        path = {"/payAbnormalDetail"},
        method = {RequestMethod.GET},
        produces = {"application/json"}
    )
    Result<List<PayAbnormalDetailVo>> queryPayAbnormalDetail();

    @RequestMapping(
        path = {"/payStat"},
        consumes = {"application/json"},
        method = {RequestMethod.POST},
        produces = {"application/json"}
    )
    Result<List<AcctPayStatVo>> payStatByAccountNo(@RequestBody AcctPayStatRequest var1);

    @RequestMapping(
        path = {"/payPendingStat"},
        consumes = {"application/json"},
        method = {RequestMethod.POST},
        produces = {"application/json"}
    )
    Result<List<AcctPendingPayStatVo>> payPendingStatByAccountNo(@RequestBody AcctPayStatRequest var1);
}
