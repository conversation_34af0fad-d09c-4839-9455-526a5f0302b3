package com.cfpamf.ms.mallorder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.ms.mallorder.dto.ExchangeOrderDetailDTO;
import com.cfpamf.ms.mallorder.dto.ExchangeOrderReturnDetailDTO;
import com.cfpamf.ms.mallorder.dto.UserDTO;
import com.cfpamf.ms.mallorder.po.OrderDeliveryRecordPO;
import com.cfpamf.ms.mallorder.po.OrderExchangeDetailPO;
import com.cfpamf.ms.mallorder.req.exchange.OrderExchangeListRequest;
import com.cfpamf.ms.mallorder.request.OrderExchangeDetailExample;
import com.cfpamf.ms.mallorder.request.OrderExchangeListExample;
import com.cfpamf.ms.mallorder.vo.OrderExchangeDetailVO;
import com.cfpamf.ms.mallorder.vo.OrderExchangeListVO;
import com.slodon.bbc.core.response.PagerInfo;

import java.util.List;
import java.util.Map;

public interface IOrderExchangeDetailService extends IService<OrderExchangeDetailPO> {
    List<OrderExchangeListVO> orderExchangeList(OrderExchangeListRequest orderExchangeListRequest, PagerInfo pager);

    List<OrderExchangeDetailVO> productOrderExchangeDetail(Long orderProductId, UserDTO userDTO);

    OrderExchangeDetailVO orderExchangeApplyDetail(String exchangeSn, UserDTO userDTO);

    List<OrderExchangeDetailVO> getOrderExchangeDetail(String Order, UserDTO userDTO);

    ExchangeOrderReturnDetailDTO dealExchangeOrderReturnDetail(String afsSn);


    OrderExchangeDetailPO getExchangeOrderDetailByExample(OrderExchangeDetailExample example);

    ExchangeOrderDetailDTO getOrderExchangeDetailByAfsSn(String afSn);

    ExchangeOrderDetailDTO getOrderExchangeDetailByExchangeSn(String exchangeSn);

    Map<Long, Integer> getProductExchangeCountMap(List<Long> orderProductIdList);
}
