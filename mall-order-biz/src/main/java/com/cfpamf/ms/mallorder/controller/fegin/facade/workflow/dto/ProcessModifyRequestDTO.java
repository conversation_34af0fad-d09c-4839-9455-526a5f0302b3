package com.cfpamf.ms.mallorder.controller.fegin.facade.workflow.dto;

import com.cfpamf.ms.mallorder.controller.fegin.facade.workflow.constant.UserIdTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class ProcessModifyRequestDTO {
    @ApiModelProperty("请求接口的用户ID")
    @NotNull(message = "用户ID必填")
    private String userId;
    @ApiModelProperty("请求接口的用户ID类型：JOB_NO, BMS_USER_ID")
    @NotNull(message = "用户ID类型必填")
    private UserIdTypeEnum userIdType;

    private String name;
    @ApiModelProperty("调用接口来源：1——正常调用；2——卡片消息回调")
    private Integer source = 1;
    @ApiModelProperty("流程节点key")
    private String taskDefKey;
}
