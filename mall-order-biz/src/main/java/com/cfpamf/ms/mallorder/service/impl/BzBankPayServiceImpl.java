package com.cfpamf.ms.mallorder.service.impl;

import java.text.ParseException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cfpamf.ms.mallorder.common.mq.msg.PayResultMessage;
import com.cfpamf.ms.mallorder.v2.common.lock.Lock;
import com.cfpamf.ms.trade.facade.vo.PayRequestVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cfpamf.framework.autoconfigure.mybatis.BaseRepoServiceImpl;
import com.cfpamf.ms.mall.account.vo.AccountCard;
import com.cfpamf.ms.mallorder.common.enums.OrderStatusEnum;
import com.cfpamf.ms.mallorder.common.enums.PayMethodEnum;
import com.cfpamf.ms.mallorder.common.enums.trade.CallFromEnum;
import com.cfpamf.ms.mallorder.common.enums.trade.PayStatusApiEnum;
import com.cfpamf.ms.mallorder.common.enums.trade.TradeBankEnum;
import com.cfpamf.ms.mallorder.common.util.DateUtil;
import com.cfpamf.ms.mallorder.common.util.ThreadPoolUtil;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.controller.fegin.facade.AccountCardFacade;
import com.cfpamf.ms.mallorder.dto.OrderAutoPayDTO;
import com.cfpamf.ms.mallorder.integration.hrms.HrmsIntegration;
import com.cfpamf.ms.mallorder.integration.settlement.BillOperatinIntegration;
import com.cfpamf.ms.mallorder.integration.trade.CoreTradeIntegration;
import com.cfpamf.ms.mallorder.mapper.BzBankPayMapper;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.model.OrderPayModel;
import com.cfpamf.ms.mallorder.model.OrderReturnModel;
import com.cfpamf.ms.mallorder.po.BzBankPayPO;
import com.cfpamf.ms.mallorder.po.OrderExtendPO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderProductPO;
import com.cfpamf.ms.mallorder.po.OrderReturnPO;
import com.cfpamf.ms.mallorder.req.PayV2Request;
import com.cfpamf.ms.mallorder.service.IBzBankPayService;
import com.cfpamf.ms.mallorder.service.IOrderExtendService;
import com.cfpamf.ms.mallorder.service.IOrderPayService;
import com.cfpamf.ms.mallorder.service.IOrderProductService;
import com.cfpamf.ms.mallorder.service.IOrderReturnService;
import com.cfpamf.ms.mallorder.service.IOrderService;
import com.cfpamf.ms.mallorder.vo.EmployeePaySubjectRelationVO;
import com.cfpamf.ms.trade.facade.enums.TradeBusinessTypeEnum;
import com.cfpamf.ms.trade.facade.message.TradeRefundMessage;
import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 转账记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-17
 */
@Service
@Slf4j
public class BzBankPayServiceImpl extends BaseRepoServiceImpl<BzBankPayMapper, BzBankPayPO> implements IBzBankPayService {

    @Autowired
    private IOrderPayService orderPayService;
    @Autowired
    private IOrderReturnService orderReturnService;

    @Autowired
    private OrderModel orderModel;
    @Autowired
    private OrderReturnModel orderReturnModel;

    @Autowired
    private CoreTradeIntegration coreTradeIntegration;
    @Autowired
    private IBzBankPayService iBzBankPayService;
    @Autowired
    private HrmsIntegration hrmsIntegration;
    @Autowired
    private BillOperatinIntegration billOperatinIntegration;
    @Autowired
    private IOrderService iOrderService;
    @Autowired
    private OrderPayModel orderPayModel;
    @Autowired
    private IOrderExtendService iOrderExtendService;
    @Autowired
    private IOrderProductService iOrderProductService;
    @Autowired
    private BzBankPayServiceImpl bzBankPayService;
    @Resource
    private AccountCardFacade accountCardFacade;
    /**
     * @param
     * @return boolean
     * @description : 自动处理job
     */
    @Override
    public boolean orderAutoPayJob() throws ParseException {
        List<OrderAutoPayDTO> waitPayOrder = orderPayService.listOrderAutoPay();
        //处理用户信息
        List<OrderAutoPayDTO> needDeal = dealUserInfo(waitPayOrder);
        if (needDeal.size() < 1) {
            return true;
        }
        for (OrderAutoPayDTO orderAutoPayDTO : needDeal) {
            //只处理未付款订单
            String orderSn = orderAutoPayDTO.getOrderSn();
            OrderPO orderDB = iOrderService.getOne(Wrappers.lambdaQuery(OrderPO.class)
                    .eq(OrderPO::getOrderSn, orderSn)
                    .last("limit 1"));
            if (orderDB.getOrderState() > OrderStatusEnum.WAIT_PAY.getValue()) {
                continue;
            }
            BzBankPayPO bzBankPayPO = new BzBankPayPO();
            PayV2Request payV2Request = new PayV2Request();
            //构建请求参数
            buildRequest(bzBankPayPO, payV2Request, orderAutoPayDTO, 0);
            //发起支付
            iBzBankPayService.dealPay(bzBankPayPO, payV2Request);
            if (orderDB.getOrderState().equals(OrderStatusEnum.WAIT_PAY.getValue())) {
                orderDB.setOrderState(OrderStatusEnum.DEAL_PAY.getValue());
                iOrderService.updateById(orderDB);
            }
        }
        return true;
    }

    @Override
    public boolean executeOrderAutoPaySyncJob() {
        List<BzBankPayPO> waitSync = this.lambdaQuery()
                .eq(BzBankPayPO::getStatus, TradeBankEnum.CREATE_SUCCESS.getValue())
                .eq(BzBankPayPO::getPayStatus, PayStatusApiEnum.Unverified.getEnumCode())
                .le(BzBankPayPO::getCreateTime, DateUtil.add(DateUtil.getNow(), Calendar.HOUR, -10))
                .list();
        for (BzBankPayPO bzBankPayPO : waitSync) {
            PayRequestVo payRequestVo;
            try {
                payRequestVo = coreTradeIntegration.queryPayResult(bzBankPayPO.getOrderSn());
                Integer tradePayStatus = payRequestVo.getPayStatus();
                Integer status = conventPayStatus(tradePayStatus);
                // 状态未发生变化，跳过
                if (status.equals(bzBankPayPO.getStatus())) {
                    continue;
                }
                PayResultMessage payResultMessage = new PayResultMessage();
                BeanUtils.copyProperties(payRequestVo, payResultMessage);
                bzBankPayService.updateTradeResult(payResultMessage);
            } catch (Exception e) {
                log.error("同步物资订单交易状态异常:{}", e.getMessage());
            }

        }
        return true;
    }

    /**
     * @param bzBankPayPO
     * @param payV2Request
     * @return void
     * @description :处理付款
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dealPay(BzBankPayPO bzBankPayPO, PayV2Request payV2Request) {
        //发起转账
        try {
            coreTradeIntegration.payV2(payV2Request);
            //发起成功，改为付款中
            if (bzBankPayPO.getStatus().equals(TradeBankEnum.INIT.getValue())) {
                bzBankPayPO.setStatus(TradeBankEnum.CREATE_SUCCESS.getValue());
            }
        } catch (Exception e) {
            log.error("对公转账失败:{}", JSONObject.toJSONString(payV2Request));
            //发起成功，改为付款中
            if (bzBankPayPO.getStatus().equals(TradeBankEnum.INIT.getValue())) {
                bzBankPayPO.setStatus(TradeBankEnum.FAIL.getValue());
                bzBankPayPO.setReason(e.getMessage().length() > 200 ? e.getMessage().substring(0, 200) : e.getMessage());
            }
        }
        this.updateById(bzBankPayPO);
    }

    /**
     * @param waitPayOrder
     * @return void
     * @description :处理用户信息
     */
    private List<OrderAutoPayDTO> dealUserInfo(List<OrderAutoPayDTO> waitPayOrder) {
        List<OrderAutoPayDTO> autoPayDTOS = new ArrayList<>();

        for (OrderAutoPayDTO orderAutoPayDTO : waitPayOrder) {
            //1.工号为空不处理
            if (StringUtils.isEmpty(orderAutoPayDTO.getUserNumber())) {
                log.warn("处理自动付款时用户工号为空:{}", JSONObject.toJSONString(orderAutoPayDTO));
                continue;
            }
            //2.根据店铺id获取银行卡信息
            AccountCard account = billOperatinIntegration.getAccount(orderAutoPayDTO.getStoreId());
            if (Objects.isNull(account) || StringUtils.isEmpty(account.getBankAccountNumber())) {
                log.warn("处理自动付款时获取客户信息为空:{}", JSONObject.toJSONString(orderAutoPayDTO));
                continue;
            }
            orderAutoPayDTO.setAccountName(account.getBankAccountName());
            orderAutoPayDTO.setAccountNo(account.getBankAccountNumber());
            orderAutoPayDTO.setAccountType(String.valueOf(account.getAccountType()));
            orderAutoPayDTO.setBankNo(account.getBankNo());
            autoPayDTOS.add(orderAutoPayDTO);
        }
        if (autoPayDTOS.size() < 1) {
            return Lists.newArrayListWithCapacity(0);
        }
        //批量查询发薪主体
        List<String> userList = autoPayDTOS.stream().map(OrderAutoPayDTO::getUserNumber).collect(Collectors.toList());
        List<EmployeePaySubjectRelationVO> subjectRelationVOS = hrmsIntegration.queryEmployeePaySubjectRelation(userList);

        List<OrderAutoPayDTO> finalPay = new ArrayList<>();
        for (OrderAutoPayDTO autoPayDTO : autoPayDTOS) {
            EmployeePaySubjectRelationVO subjectRelationVO = subjectRelationVOS.stream()
                    .filter(x -> autoPayDTO.getUserNumber().equals(x.getEmployeeCode()))
                    .findFirst().orElse(null);
            if (Objects.isNull(subjectRelationVO) || StringUtils.isEmpty(subjectRelationVO.getPaySubjectCode())) {
                log.warn("处理自动付款时获取发薪主体为空,支付单号:{}", JSONObject.toJSONString(autoPayDTO));
                continue;
            }
            autoPayDTO.setLoanOrgNo(subjectRelationVO.getPaySubjectCode());
            finalPay.add(autoPayDTO);
        }

        return finalPay;
    }


    /**
     * @param bzBankPayPO
     * @param payV2Request
     * @param orderAutoPayDTO
     * @return void
     * @description : 构建请求参数
     */
    private void buildRequest(BzBankPayPO bzBankPayPO, PayV2Request payV2Request, OrderAutoPayDTO orderAutoPayDTO, Integer makeFlag) throws ParseException {
        if (makeFlag == 0) {
            bzBankPayPO.setPaySn(orderAutoPayDTO.getPaySn());
            bzBankPayPO.setOrderSn(orderAutoPayDTO.getOrderSn());
            bzBankPayPO.setParentSn(orderAutoPayDTO.getParentSn());
            bzBankPayPO.setAmount(orderAutoPayDTO.getOrderAmount());
            bzBankPayPO.setStatus(TradeBankEnum.INIT.getValue());
            bzBankPayPO.setPayStatus(PayStatusApiEnum.Unverified.getEnumCode());
            bzBankPayPO.setRemark("物资申领WZSL：" + orderAutoPayDTO.getOrderSn());
            bzBankPayPO.setAccountNo(orderAutoPayDTO.getAccountNo());
            bzBankPayPO.setAccountName(orderAutoPayDTO.getAccountName());
            bzBankPayPO.setIdCardNo("");
            bzBankPayPO.setAccountType(orderAutoPayDTO.getAccountType());
            bzBankPayPO.setBusinessType(TradeBusinessTypeEnum.APPLY_SUPPLIES.getCode());
            bzBankPayPO.setLoanOrgNo(orderAutoPayDTO.getLoanOrgNo());
            bzBankPayPO.setPlanLoanDt(new Date());
            bzBankPayPO.setFailRetry(0);
            bzBankPayPO.setCallFrom(CallFromEnum.MALL_ORDER.getCode());
            bzBankPayPO.setBankNo(orderAutoPayDTO.getBankNo());
            iBzBankPayService.saveBankPay(bzBankPayPO);
        }
        bzBankPayPO.setStatus(TradeBankEnum.INIT.getValue());
        payV2Request.setTradeNo(bzBankPayPO.getOrderSn());
        //格式化日期
        payV2Request.setPlanLoanDt(bzBankPayPO.getPlanLoanDt());
        payV2Request.setTranAmt(bzBankPayPO.getAmount());
        payV2Request.setIdCardNo("");
        payV2Request.setAccountNo(bzBankPayPO.getAccountNo());
        payV2Request.setAccountName(bzBankPayPO.getAccountName());
        payV2Request.setAccountType(bzBankPayPO.getAccountType());
        payV2Request.setLoanOrgNo(bzBankPayPO.getLoanOrgNo());
        payV2Request.setBusinessType(bzBankPayPO.getBusinessType());
        payV2Request.setCallFrom(bzBankPayPO.getCallFrom());
        payV2Request.setRemark(bzBankPayPO.getRemark());
        payV2Request.setBankNo(bzBankPayPO.getBankNo());
    }

    /**
     * @param
     * @return boolean
     * @description : 失败自动补偿
     */
    @Override
    public boolean orderAutoPayMakeUpJob() throws ParseException {
        //转账失败和转账退票中重试次数小于5的记录进行重试
        List<BzBankPayPO> payPOS = lambdaQuery()
                .in(BzBankPayPO::getStatus, TradeBankEnum.FAIL.getValue())
                .le(BzBankPayPO::getFailRetry, 5)
                .le(BzBankPayPO::getCreateTime, DateUtil.addDate(DateUtil.getNow(), Calendar.MINUTE, -10))
                .list();
        for (BzBankPayPO payPO : payPOS) {
            PayV2Request payV2Request = new PayV2Request();
            buildRequest(payPO, payV2Request, null, 1);
            //发起转账
            iBzBankPayService.dealPay(payPO, payV2Request);
            payPO.setFailRetry(payPO.getFailRetry() + 1);
            this.updateById(payPO);
        }
        return true;
    }


    /**
     * @param
     * @param orderSn
     * @return boolean
     * @description : 失败补偿
     */
    public boolean omsPayMakeUp(String orderSn) throws ParseException {
        BzBankPayPO bzBankPayPO = getOne(Wrappers.lambdaQuery(BzBankPayPO.class)
                .eq(BzBankPayPO::getOrderSn, orderSn)
                .last("limit 1"));

        PayV2Request payV2Request = new PayV2Request();
        buildRequest(bzBankPayPO, payV2Request, null, 1);
        //发起转账
        iBzBankPayService.dealPay(bzBankPayPO, payV2Request);
        bzBankPayPO.setFailRetry(bzBankPayPO.getFailRetry() + 1);
        this.updateById(bzBankPayPO);
        return true;
    }

    @Autowired
    private Lock lock;

    /**
     * @param msg
     * @return void
     * @description : 更新交易状态
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTradeResult(PayResultMessage msg) {
        log.info("收到交易状态消息:{}", JSON.toJSONString(msg));
        String lockKey = "updateTradeResult:oms:" + msg.getTradeNo();
        return lock.tryLockExecuteFunction(lockKey, 0, 20, TimeUnit.SECONDS,
                () -> {
                    String tradeNo = msg.getTradeNo();
                    Integer status = conventPayStatus(msg.getPayStatus());

                    BzBankPayPO bzBankPayPO = this.getOne(Wrappers.lambdaQuery(BzBankPayPO.class)
                            .eq(BzBankPayPO::getOrderSn, tradeNo)
                            .last("limit 1"));
                    if (Objects.isNull(bzBankPayPO)) {
                        log.error("消费交易状态未查询到信息:{}", JSON.toJSONString(msg));
                        return true;
                    }
                    if (bzBankPayPO.getStatus().equals(status)) {
                        log.error("消费状态未变更，不做处理:{}", JSON.toJSONString(msg));
                        return true;
                    }
                    OrderPO orderPO = iOrderService.getOne(Wrappers.lambdaQuery(OrderPO.class)
                            .eq(OrderPO::getOrderSn, tradeNo)
                            .last("limit 1"));
                    if (orderPO == null) {
                        log.error("消费交易状态未查询到信息:{}", JSON.toJSONString(msg));
                        return true;
                    }

                    LambdaUpdateWrapper<BzBankPayPO> wrapper = new LambdaUpdateWrapper<>();
                    wrapper.eq(BzBankPayPO::getOrderSn, tradeNo);
                    wrapper.in(BzBankPayPO::getStatus, Arrays.asList(TradeBankEnum.CREATE_SUCCESS.getValue(), TradeBankEnum.INIT.getValue()));
                    wrapper.set(BzBankPayPO::getPayStatus, msg.getPayStatus());
                    wrapper.set(BzBankPayPO::getStatus, status);
                    if (status.equals(TradeBankEnum.FAIL.getValue()) ||
                            status.equals(TradeBankEnum.REFUND.getValue())) {
                        wrapper.set(BzBankPayPO::getFailRetry, 5);
                    }
                    if (StringUtils.isNotBlank(msg.getDescription())) {
                        wrapper.set(BzBankPayPO::getReason, msg.getDescription());
                    }
                    boolean update = this.update(wrapper);
                    if (!update) {
                        log.error("更新物资订单交易放款失败:{}", JSON.toJSONString(msg));
                        return true;
                    }
                    //订单状态为待支付并支付状态为成功才处理
                    if (status.equals(TradeBankEnum.SUCCESS.getValue())
                            && orderPO.getOrderState().equals(OrderStatusEnum.DEAL_PAY.getValue())) {
                        //订单已支付
                        orderPayModel.orderPaySuccess(orderPO, null, PayMethodEnum.BANK_PAY.getValue(), PayMethodEnum.BANK_PAY.getDesc(), new Date(), null);
                        OrderExtendPO orderExtendPO = iOrderExtendService.getOne(Wrappers.lambdaQuery(OrderExtendPO.class)
                                .eq(OrderExtendPO::getOrderSn, tradeNo)
                                .last("limit 1"));
                        if (OrderConst.BGCL.equals(orderExtendPO.getCategoryCode())) {
                            //办公车辆支付成功发送群通知
                            List<OrderProductPO> orderProductPOS = iOrderProductService.lambdaQuery().eq(OrderProductPO::getOrderSn, orderPO.getOrderSn()).list();
                            List<String> goodsName = orderProductPOS.stream().map(OrderProductPO::getGoodsName).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(goodsName)) {
                                String tmp = StringUtils.join(goodsName, ",");
                                String talkMsg = "【中和农信】" + orderExtendPO.getBranchName() + "申领的商品“" + tmp + "”的乡助订单：" + orderPO.getOrderSn() +
                                        "总额：" + orderPO.getOrderAmount() + "元已自动付款成功。";
                                ThreadPoolUtil.execute(() -> orderPayModel.pushDingTalk(talkMsg, null));
                            }
                        }
                    }
                    //失败直接不重试，进行关单处理
                    if (status.equals(TradeBankEnum.FAIL.getValue()) ||
                            status.equals(TradeBankEnum.REFUND.getValue())) {
                        //订单支付失败
                        orderPayModel.orderPayFail(orderPO);
                    }
                    return true;
                }
        );
    }

    /**
     * @param msg
     * @return void
     * @description : 更换退汇状态
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTradeRefund(TradeRefundMessage msg) {
        log.info("收到退汇状态消息:{}", JSONObject.toJSONString(msg));
        String tradeNo = msg.getTradeNo();
        BzBankPayPO bzBankPayPO = this.getOne(Wrappers.lambdaQuery(BzBankPayPO.class)
                .eq(BzBankPayPO::getOrderSn, tradeNo)
                .last("limit 1"));

        OrderPO orderPO = iOrderService.getOne(Wrappers.lambdaQuery(OrderPO.class)
                .eq(OrderPO::getOrderSn, tradeNo)
                .last("limit 1"));
        if (bzBankPayPO == null || orderPO == null) {
            log.info("消费退汇状态未查询到信息:{}", JSONObject.toJSONString(msg));
            return true;
        }
        //更新转账状态
        bzBankPayPO.setStatus(TradeBankEnum.REFUND.getValue());
        bzBankPayPO.setReason(msg.getRefundReason().length() > 200
                ? msg.getRefundReason().substring(0, 200) : msg.getRefundReason());
        updateById(bzBankPayPO);

        //当前订单状态为“已发货”、“已完成”、“已关闭”，则不修改订单状态
        if (orderPO.getOrderState() > OrderStatusEnum.WAIT_DELIVER.getValue()) {
            return true;
        }

        orderPayModel.orderPayFail(orderPO);

        // 调用自动取消，生成退款记录
        orderModel.cancelOrder(Collections.singletonList(orderPO), "退汇取消OMS订单",
                null, OrderConst.LOG_ROLE_VENDOR, 0L, "system",
                "退汇取消OMS订单", OrderConst.RETURN_BY_0);

        // 再直接调用退款成功
        OrderReturnPO orderReturn = orderReturnService.lambdaQuery()
                .eq(OrderReturnPO::getOrderSn, orderPO.getOrderSn())
                .eq(OrderReturnPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                .last("limit 1")
                .one();
        if (Objects.nonNull(orderReturn)) {
            orderReturnModel.refundSuccess(null,orderReturn.getAfsSn());
        }

        return true;
    }

    /**
     * @param bzBankPayPO
     * @return void
     * @description : 保存转账记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBankPay(BzBankPayPO bzBankPayPO) {
        saveOrUpdate(bzBankPayPO);
    }


    /**
     * @return java.lang.Integer
     * @description : 转换支付状态
     */
    private Integer conventPayStatus(Integer payStatus) {
        Integer status;
        switch (payStatus) {
            case 0:
                status = 1;
                break;
            case 1:
            case 2:
            case 3:
                status = 2;
                break;
            case 4:
            case -1:
            case 9:
                status = 4;
                break;
            case 5:
                status = 3;
                break;
            default:
                status = -1;
                break;
        }
        return status;
    }
}
