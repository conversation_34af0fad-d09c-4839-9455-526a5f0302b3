package com.cfpamf.ms.mallorder.v2.manager.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollectionUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderPromotionDetailPO;
import com.cfpamf.ms.mallorder.service.IOrderPromotionDetailService;
import com.cfpamf.ms.mallorder.v2.builder.OrderReturnCouponBuilder;
import com.cfpamf.ms.mallorder.v2.common.enums.OrderSnType;
import com.cfpamf.ms.mallorder.v2.domain.bo.OrderReturnCouponBO;
import com.cfpamf.ms.mallorder.v2.manager.CouponOrderManager;
import com.cfpamf.ms.mallorder.v2.service.OrderDetailService;
import com.cfpamf.ms.mallpromotion.api.CouponTccFeign;
import com.cfpamf.ms.mallpromotion.vo.CouponUseVO;
import com.slodon.bbc.core.response.JsonResult;

import io.seata.core.context.RootContext;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class CouponOrderManagerImpl implements CouponOrderManager {

    @Autowired
    private CouponTccFeign couponTccFeign;

    @Autowired
    private IOrderPromotionDetailService orderPromotionDetailService;

    @Autowired
    private OrderDetailService orderDetailService;

    /**
     * 店铺优惠券使用
     * 
     * @param memberDb
     * @param couponCode
     * @param orderSn
     * @param storeId
     */
    @Override
    public void storeCouponUse(Member memberDb, String couponCode, String orderSn, Long storeId) {
        log.info("tcc订单使用店铺优惠券， xid:【{}】 memberDb:{} -> couponCode:{} ->orderSn:{}->storeId:{}", RootContext.getXID(),
            memberDb, couponCode, orderSn, storeId);
        if (ObjectUtils.isEmpty(memberDb)) {
            log.warn("tcc订单使用店铺优惠券，memberDb 为空拒绝执行 xid:【{}】", RootContext.getXID());
            return;
        }
        if (ObjectUtils.isEmpty(couponCode)) {
            log.warn("订单使用店铺优惠券，couponCode 为空拒绝执行 xid:【{}】", RootContext.getXID());
            return;
        }
        if (ObjectUtils.isEmpty(orderSn)) {
            log.warn("tcc订单使用店铺优惠券，orderSn 为空拒绝执行 xid:【{}】", RootContext.getXID());
            return;
        }
        if (ObjectUtils.isEmpty(storeId)) {
            log.warn("tcc订单使用店铺优惠券，storeId 为空拒绝执行 xid:【{}】", RootContext.getXID());
            return;
        }
        CouponUseVO couponVO = new CouponUseVO();
        List<String> couponCodeList = new ArrayList<>();
        couponCodeList.add(couponCode);
        couponVO.setCouponCode(couponCodeList);
        couponVO.setStoreId(storeId);
        log.info("tcc订单使用店铺优惠券API调用，xid:【{}】 orderSn:{}  couponVO：{} ", RootContext.getXID(), orderSn, couponVO);
        JsonResult<Boolean> result =
            couponTccFeign.couponUse(couponVO, "mall-order", orderSn, memberDb.getMemberId(), memberDb.getMemberName());
        log.info("tcc订单使用店铺优惠券API结果，xid:【{}】 orderSn:{}  couponVO：{}  result：{}", RootContext.getXID(), orderSn, couponVO,
            result);
        // 订单使用店铺优惠券失败
        BizAssertUtil.isTrue(200 != result.getState(),
            StringUtils.defaultString(result.getMsg(), "抱歉，下单失败，店铺优惠券使用失败，请稍后再试！"));
        // 订单使用店铺优惠券失败
        BizAssertUtil.isTrue(!result.getData(), "抱歉，店铺优惠券使用失败，请稍后再试！");
    }

    /**
     * 平台优惠券使用
     * 
     * @param memberDb
     * @param couponCode
     * @param parentSn
     */
    @Override
    public void platformCouponUse(Member memberDb, String couponCode, String parentSn) {
        log.info("tcc订单使用平台优惠券， xid:【{}】 memberDb:{} -> couponCode:{} ->orderSn:{}->storeId:{}", RootContext.getXID(),
            memberDb, couponCode, parentSn);
        if (ObjectUtils.isEmpty(memberDb)) {
            log.warn("tcc订单使用平台优惠券，memberDb 为空拒绝执行 xid:【{}】", RootContext.getXID());
            return;
        }
        if (ObjectUtils.isEmpty(couponCode)) {
            log.warn("tcc订单使用平台优惠券，couponCode 为空拒绝执行 xid:【{}】", RootContext.getXID());
            return;
        }
        if (ObjectUtils.isEmpty(parentSn)) {
            log.warn("tcc订单使用平台优惠券，parentSn 为空拒绝执行 xid:【{}】", RootContext.getXID());
            return;
        }
        CouponUseVO coupon = new CouponUseVO();
        List<String> couponCodeList = new ArrayList<>();
        couponCodeList.add(couponCode);
        coupon.setCouponCode(couponCodeList);
        coupon.setStoreId(0L);
        log.info("tcc订单使用平台优惠API调用，xid:【{}】 parentSn:{} -> coupon：{} ", RootContext.getXID(), parentSn, coupon);
        JsonResult<Boolean> result =
            couponTccFeign.couponUse(coupon, "mall-order", parentSn, memberDb.getMemberId(), memberDb.getMemberName());
        log.info("tcc订单使用平台优惠API结果，xid:【{}】 parentSn:{} -> coupon：{} result：{}", RootContext.getXID(), parentSn, coupon,
            result);
        // 订单使用平台优惠券失败
        BizAssertUtil.isTrue(200 != result.getState(), "抱歉，下单失败，平台优惠券使用失败，请稍后再试！");
        // 订单使用平台优惠券失败
        BizAssertUtil.isTrue(!result.getData(), "抱歉，平台优惠券使用失败，请稍后再试！");
    }

    @Override
    public void platformCouponReturnBatch(List<OrderPO> orderList) {
        this.couponReturnBatch(orderList, 2);
    }

    @Override
    public void storeCouponReturnBatch(OrderPO order) {
        if (ObjectUtils.isEmpty(order)) {
            log.info("tcc恢复优惠券，xid:【{}】order{} 为空，拒绝执行 ", RootContext.getXID(), order);
            return;
        }
        List<OrderPO> orderList = new ArrayList<>();
        orderList.add(order);
        this.couponReturnBatch(orderList, 1);
    }

    /**
     * 优惠券退还
     * 
     * <pre>
     * 店铺优惠券，直接退还
     * 平台优惠券需要，总订单下的最后一笔子单取消或退款时候才能退还
     * 
     * </pre>
     * 
     * @param orderList
     * @param couponType
     *            0，全部优惠券 1，店铺优惠券 2，平台优惠券
     * @throws Exception
     */
    @Override
    public void couponReturnBatch(List<OrderPO> orderList, int couponType) {
        if (CollectionUtils.isEmpty(orderList)) {
            log.warn("tcc恢复优惠券，orderList为空拒绝执行 xid:【{}】", RootContext.getXID());
            return;
        }
        Set<String> orderSns = orderList.stream().map(OrderPO::getOrderSn).collect(Collectors.toSet());
        List<OrderPO> orderNewList = orderDetailService.getOrderListByOrderSns(OrderSnType.CHILD_ORDER_SN,orderSns);
        // 优惠券订单分组
        OrderReturnCouponBO couponOrder = OrderReturnCouponBuilder.buildOrderReturnCouponBO(orderNewList);
        log.info("tcc恢复优惠券，xid:【{}】优惠券订单分组优惠券订单分组 couponOrder:{}", RootContext.getXID(), couponOrder);
        if (couponType == 0 || couponType == 2) {
            log.warn("tcc恢复优惠券，xid:【{}】couponType：{} 开始退还平台券", RootContext.getXID(), couponType);
            // 退还平台券
            this.platformCouponReturnBatch(couponOrder);
        }
        if (couponType != 1 && couponType != 0) {
            // 退还店铺
            log.warn("tcc恢复优惠券，xid:【{}】couponType：{} 无需退还店铺优惠券", RootContext.getXID(), couponType, orderNewList);
            return;
        }
        // 查询订单使用的优惠券<< -->>>>>>退还店铺券<<<<<<<-- >>
        List<OrderPromotionDetailPO> orderCouponList = orderPromotionDetailService
            .getOrderPromotionDetailList(couponOrder.getOrderSns(), OrderConst.IS_STORE_PROMOTION_YES);
        ////////////////////// 店铺券/////////////////////////////////////
        if (CollectionUtils.isEmpty(orderCouponList)) {
            log.warn("tcc恢复店铺优惠券资源，orderCouponList为空拒绝执行，orderSns:{}", couponOrder.getOrderSns());
            return;
        }
        for (OrderPromotionDetailPO orderPromotionDetail : orderCouponList) {
            log.info("tcc恢复资源，恢复店铺优惠券资源API调用，xid:【{}】 orderSn:{} couponCode:{}", RootContext.getXID(),
                orderPromotionDetail.getOrderSn(), orderPromotionDetail.getPromotionId());
            JsonResult<Boolean> result = couponTccFeign.couponReturn(orderPromotionDetail.getPromotionId(),
                "mall-order", orderPromotionDetail.getOrderSn());
            log.info("tcc恢复资源，恢复店铺优惠券资源API结果，xid:【{}】 orderSn:{} couponCode:{} , result：{}", RootContext.getXID(),
                orderPromotionDetail.getOrderSn(), orderPromotionDetail.getPromotionId(), result);
            // 恢复优惠券失败
            BizAssertUtil.isTrue(200 != result.getState(), "抱歉，恢复店铺优惠券系统开小差，请稍后再试！");
            // 恢复优惠券失败
            BizAssertUtil.isTrue(!result.getData(), "抱歉，恢复店铺优惠券系失败，请稍后再试！");
        }
    }

    /**
     * 平台优惠券退还
     * 
     * <pre>
     * 平台优惠券需要，总订单下的最后一笔子单取消或退款时候才能退还
     * </pre>
     * 
     * @param couponOrder
     * @throws Exception
     */
    private void platformCouponReturnBatch(OrderReturnCouponBO couponOrder) {
        log.info("tcc恢复平台优惠券，couponOrder:{} ", couponOrder);
        if (CollectionUtils.isEmpty(couponOrder.getPlatformOrderSns())) {
            log.warn("tcc恢复平台优惠券，PlatformOrderSns为空拒绝执行.-> {}",couponOrder);
            return;
        }
        // 查询订单使用的优惠券<< -->>>>>>退还平台优惠券的前提是当前总订单下的子订单都已经退款或已取消<<<<<<<-- >>
        List<OrderPromotionDetailPO> orderPlatformCouponList = orderPromotionDetailService
            .getOrderPromotionDetailList(couponOrder.getPlatformOrderSns(), OrderConst.IS_STORE_PROMOTION_NO);
        if (CollectionUtils.isEmpty(orderPlatformCouponList)) {
            log.warn("tcc恢复平台优惠券，orderPlatformCouponList为空拒绝执行，couponOrder:{} ，platformOrderSns:{} ", couponOrder,
                couponOrder.getPlatformOrderSns());
            return;
        }
        //按订单号分组
        Map<String, List<OrderPromotionDetailPO>> orderSnCouponMap = orderPlatformCouponList.stream().collect(Collectors.groupingBy(e -> e.getOrderSn()));
        // orderSn->券码
        Map<String, Set<String>> orderCouponMap = new HashMap<>();
        for (OrderPromotionDetailPO orderCoupon : orderPlatformCouponList) {
            if (orderCouponMap.containsKey(orderCoupon.getOrderSn())) {
                log.warn("tcc恢复平台优惠券，orderCouponMap中存在当前orderSn 跳过当前行，couponOrder:{} ，parentSn:【{}】 ", couponOrder,
                    orderCoupon.getOrderSn());
                continue;
            }
            //订单对应的多个优惠卷code
            List<OrderPromotionDetailPO> pos = orderSnCouponMap.get(orderCoupon.getOrderSn());
            Set<String> orderPromotionIdList = pos.stream().map(OrderPromotionDetailPO::getPromotionId).collect(Collectors.toSet());
            orderCouponMap.put(orderCoupon.getOrderSn(), orderPromotionIdList);
        }
        // parentSn-->orderSns
        Map<String, Set<String>> parentSnPlatformOrderSnsMap = couponOrder.getParentSnPlatformOrderSnsMap();
        Set<String> parentSns = parentSnPlatformOrderSnsMap.keySet();
        // db查询parentSns集合中哪些parentSn具备退还平台优惠券能力
        Set<String> conditionSatisfactionParentSns = orderDetailService.selectClosedOrderParentSn(parentSns);
        if (CollectionUtils.isEmpty(conditionSatisfactionParentSns)) {
            log.warn("tcc恢复平台优惠券，conditionSatisfactionParentSns 为空（有子订单未取消或关闭）拒绝执行，couponOrder:{} ，parentSn:【{}】 ",
                couponOrder, parentSns);
            return;
        }
        // 循环遍历从db中新查询到符合条件的parentSns集合
        for (String parentSn : conditionSatisfactionParentSns) {
            Set<String> orderSns = parentSnPlatformOrderSnsMap.get(parentSn);
            if (CollectionUtils.isEmpty(orderSns)) {
                log.warn("tcc恢复平台优惠券，parentSn对应的orderSns为空跳过当前行，couponOrder:{} ，parentSn:【{}】 ", couponOrder, parentSn);
                continue;
            }
            Set<String> couponCodeSet = null;
            for (String orderSn : orderSns) {
                couponCodeSet = orderCouponMap.get(orderSn);
                break;// 一个父订单号下面其中一个子订单的券码就行，父订单与平台券1：1
            }
            if (CollectionUtil.isEmpty(couponCodeSet)) {
                log.warn("tcc恢复平台优惠券，parentSn对应的couponCode为空跳过当前行，couponOrder:{} ，parentSn:【{}】 ", couponOrder, parentSn);
                continue;
            }
            log.info("tcc恢复资源，恢复平台优惠券资源API调用，xid:【{}】 parentSn:{} couponCodeSet:{}", RootContext.getXID(), parentSn,
                    couponCodeSet);
            for (String couponCode : couponCodeSet) {
                JsonResult<Boolean> result = couponTccFeign.couponReturn(couponCode, "mall-order", parentSn);
                log.info("tcc恢复资源，恢复平台优惠券资源API结果，xid:【{}】 parentSn:{} couponCode:{} result：{}", RootContext.getXID(), parentSn,
                        couponCodeSet, result);
                // 恢复优惠券失败
                BizAssertUtil.isTrue(200 != result.getState(), "抱歉，恢复平台优惠券系统开小差，请稍后再试！");
                // 恢复优惠券失败
                BizAssertUtil.isTrue(!result.getData(), "抱歉，恢复平台优惠券失败，请稍后再试！");
            }
        }
    }

}
