package com.cfpamf.ms.mallorder.scheduler;

import com.cfpamf.ms.mallorder.model.OrderModel;
import com.slodon.bbc.core.util.AssertUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 系统自动取消没有付款的阶梯团订单
 * cron:0 0/5 * * * ?
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/4 15:22
 */
@Component
@Slf4j
public class OrderCancelLadderGroupJob {

    @Autowired
    private OrderModel orderModel;

    @XxlJob(value = "OrderCancelLadderGroupJob")
    public ReturnT<String> execute(String s) {

        long startTime = System.currentTimeMillis();
        XxlJobHelper.log("start job at local time: {0}", LocalDateTime.now());

        log.info("jobSystemCancelOrder() start");
        try {
            boolean jobResult = orderModel.jobSystemCancelLadderGroupOrder();
            AssertUtil.isTrue(!jobResult, "[jobSystemCancelLadderGroupOrder] 定时任务系统自动取消没有付款阶梯团订单时失败");
        } catch (Exception e) {
            log.error("jobSystemCancelOrder()", e);
        }

        XxlJobHelper.log("finish job at local time: {0}  cost:{1}",
                LocalDateTime.now(),
                (System.currentTimeMillis() - startTime) / 1000);

        return new ReturnT("SUCCESS");
    }
}
