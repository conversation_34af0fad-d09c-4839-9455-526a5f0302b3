package com.cfpamf.ms.mallorder.controller.fegin.facade;

import com.cfpamf.ms.mallorder.req.query.OrderGroupBuyingBindQuery;
import com.cfpamf.ms.mallorder.service.IOrderGroupBuyingBindService;
import com.cfpamf.ms.mallorder.service.IOrderGroupBuyingRecordService;
import com.cfpamf.ms.mallorder.vo.GroupOrderProductVO;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;

@RestController
@Slf4j
@Api(tags = "feign-拼单满赠管理")
public class OrderGroupBuyingFeign {

    @Resource
    private IOrderGroupBuyingRecordService groupBuyingRecordService;

    @Resource
    private IOrderGroupBuyingBindService groupBuyingBindService;

    /**
     * 获取拼单满赠信息
     *
     * @param query 查询条件信息
     * @return
     */
    @PostMapping("/v1/feign/business/order/groupBuying/listOrderProduct")
    public JsonResult<List<GroupOrderProductVO>> listGroupOrderProduct(@RequestBody @NotNull OrderGroupBuyingBindQuery query) {

        return SldResponse.success(groupBuyingBindService.listGroupOrderProduct(query));
    }

}