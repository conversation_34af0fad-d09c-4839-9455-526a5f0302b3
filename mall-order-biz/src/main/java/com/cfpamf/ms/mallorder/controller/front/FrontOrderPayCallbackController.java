package com.cfpamf.ms.mallorder.controller.front;

import java.util.Date;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.cfpamf.ms.mallorder.po.OrderReturnPO;
import com.cfpamf.ms.mallorder.service.IOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.alibaba.fastjson.JSON;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.mallpayment.facade.enums.PayWayEnum;
import com.cfpamf.mallpayment.facade.request.loan.LoanLendingResult;
import com.cfpamf.mallpayment.facade.vo.PaymentNotifyVO;
import com.cfpamf.mallpayment.facade.vo.PaymentRefundNotifyVO;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.cfpamf.ms.mallorder.common.help.ResultUtils;
import com.cfpamf.ms.mallorder.model.OrderPayModel;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.service.IOrderReturnService;
import com.cfpamf.ms.mallorder.v2.domain.vo.PayInfoExtraInfoVO;
import com.cfpamf.ms.mallorder.v2.domain.vo.RefundInfoExtraInfoVO;
import com.slodon.bbc.core.controller.BaseController;
import com.slodon.bbc.core.exception.MallException;

import lombok.extern.slf4j.Slf4j;


/**
 * 订单支付回调controller
 */
@RestController
@RequestMapping("front/orderPayCallback")
@Slf4j
public class FrontOrderPayCallbackController extends BaseController {

    @Resource
    private OrderPayModel orderPayModel;

    @Autowired
    private IOrderReturnService orderReturnService;
    @Autowired
    private IOrderService orderService;


    /**
     * 退款结果通知
     *
     * @param refundVO
     * @return void
     * <AUTHOR>
     * @date 2021/6/15 15:35
     */
    @PostMapping("refundNotify")
    public Result refundNotify(RefundInfoExtraInfoVO refundInfoExtraInfoVO,@RequestBody @NotNull @Valid PaymentRefundNotifyVO refundVO) {

        log.info("refundNotify退款通知====>>refundInfoExtraInfoVO:{}==>{}",JSON.toJSONString(refundInfoExtraInfoVO),JSON.toJSONString(refundVO));
        if (refundVO.getRefundStatus() == null) {
            throw new MallException("refundStatus is null");
        }

        Result refundResult = new Result<>();
        refundResult.setSuccess(true);
        try {
            orderReturnService.dealRefundByPaymethod(refundInfoExtraInfoVO,refundVO);
        } catch (Exception e) {
            log.error("退款状态回调异常", e);
            refundResult.setSuccess(false);
        }
        return refundResult;
    }

    /**
     * 放款结果通知
     *
     * @param result
     * @return void
     * <AUTHOR>
     * @date 2021/6/15 15:35
     */
    @PostMapping("loanNotify")
    public Result loanNotify(@RequestBody @NotNull @Valid LoanLendingResult result) {
        log.info("LOAN NOTIFY ================== {}", JSON.toJSON(result));
        Result loanresult = new Result<>();
        loanresult.setSuccess(true);
        try {
            orderPayModel.doLoanOperation(result);
        } catch (Exception e) {
            log.warn("放款回调异常，{}", e);
            loanresult.setSuccess(false);
        }
        return loanresult;
    }


    /**
     * 支付结果通知
     *
     * @param req
     * @return void
     * <AUTHOR>
     * @date 2021/6/15 15:35
     */
    @PostMapping("notify")
    public Result<Void> payNotify(PayInfoExtraInfoVO payInfoExtraInfoVO, @RequestBody @NotNull @Valid PaymentNotifyVO req) {
        try {
            log.info("支付通知====>>payInfoExtraInfoVO:{}==>{}",JSON.toJSONString(payInfoExtraInfoVO),JSON.toJSONString(req));
            switch (PayWayEnum.getValue(req.getPayWay())) {
                case FOLLOW_HEART:
                case ENJOY_PAY:
                    orderPayModel.enjoyPayCallBack(req,payInfoExtraInfoVO);
                    break;
                case WX_PAY_V3:
                case ALI_PAY:
                case BANK_TRANSFER:
                    orderPayModel.wxAlipayCallBack(req,payInfoExtraInfoVO);
                    break;
                default:
                    throw new MallException(
                            "支付回调：未识别的支付方式", JSON.toJSONString(req),
                            ErrorCodeEnum.C.RESULT_INVALID.getCode());
            }
        } catch (Exception ex) {
            log.error("支付状态回调处理异常:{}-{}",payInfoExtraInfoVO, req.getOrderOn(), ex);
            return ResultUtils.buildErrorResult("", ex.getMessage());
        }

        return ResultUtils.buildSuccessResult();
    }

    @GetMapping("loanStatusChange")
    public Result<Boolean> loanStatusNotify(@RequestParam(value = "orderSn") String orderSn,
                                            @RequestParam(value = "paymentCode") String paymentCode) {
        Boolean updateResult = orderService.loanStatusChange(orderSn, paymentCode);
        return ResultUtils.buildSuccessResult(updateResult);
    }


    @GetMapping("loanStatusChangeFix")
    public Result<Boolean> loanStatusNotifyFix(@RequestParam(value = "orderSn") String orderSn,
                                            @RequestParam(value = "paymentCode") String paymentCode,
                                            @RequestParam(value = "paymentName") String paymentName) {
        boolean update = orderService.lambdaUpdate()
                .eq(OrderPO::getOrderSn, orderSn)
                .set(OrderPO::getPaymentCode, paymentCode)
                .set(OrderPO::getPaymentName, paymentName)
                .update();
        return ResultUtils.buildSuccessResult(update);
    }

    @GetMapping("loanStatusChangeFixReturn")
    public Result<Boolean> loanStatusChangeFixReturn(@RequestParam(value = "afsSn") String afsSn,
                                            @RequestParam(value = "paymentMethod") String paymentMethod) {
        boolean update = orderReturnService.lambdaUpdate()
                .eq(OrderReturnPO::getAfsSn, afsSn)
                .set(OrderReturnPO::getPaymentMethod, paymentMethod)
                .update();
        return ResultUtils.buildSuccessResult(update);
    }

    /**
     * @param orderPO
     * @return void
     * @description :
     */
    @PostMapping("orderStateFix")
    public void orderPaySuccess(String payNo,@RequestBody OrderPO orderPO) {
        // 子订单支付成功处理
        orderPayModel.orderPaySuccess(orderPO, "",
                orderPO.getPaymentCode(), orderPO.getPaymentName(),new Date(),payNo);
    }

}
