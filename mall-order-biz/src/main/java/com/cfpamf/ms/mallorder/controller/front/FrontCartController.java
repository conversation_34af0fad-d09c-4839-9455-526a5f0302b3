/**
 * Copyright@Slodon since 2015, All rights reserved.
 * <p>
 * 注意：
 * 本软件为北京商联达科技有限公司开发研制，未经许可不得使用
 * 购买后可获得源代码使用权（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 * 商业使用请联系: <EMAIL> 或 拨打全国统一热线 400-881-0877
 * 网址：http://www.slodon.com
 */
package com.cfpamf.ms.mallorder.controller.front;

import com.cfpamf.ms.mallgoods.facade.enums.GoodsTypeEnum;
import com.cfpamf.ms.mallmember.api.MemberFollowProductFeignClient;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.common.aspect.ThreadLocalRemoveTag;
import com.cfpamf.ms.mallorder.common.constant.CartConst;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.model.CartModel;
import com.cfpamf.ms.mallorder.po.CartPO;
import com.cfpamf.ms.mallorder.request.CartExample;
import com.cfpamf.ms.mallorder.service.ICartService;
import com.cfpamf.ms.mallorder.vo.CartListVO;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import com.slodon.bbc.core.util.UserUtil;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;

/**
 * 购物车controller
 */


@RestController
@RequestMapping("front/cart")
@Api(tags = "front-购物车管理")
@Slf4j
@Validated
public class FrontCartController {

    @Resource
    private CartModel cartModel;
    @Autowired
    private ICartService cartService;
    @Resource
    private MemberFollowProductFeignClient memberFollowProductFeignClient;

    @PostMapping("add")
    @ApiOperation(value = "添加购物车接口", tags = "CORE")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "productId", value = "skuId", required = true, paramType = "query"),
            @ApiImplicitParam(name = "areaCode", value = "下单时店铺的区域编码", required = true, paramType = "query"),
            @ApiImplicitParam(name = "number", value = "购买数量，默认为1", required = true, defaultValue = "1", paramType = "query"),
            @ApiImplicitParam(name = "financeRuleCode", value = "金融规则编号", paramType = "query"),
    })
    @ThreadLocalRemoveTag
    public JsonResult<String> addCart(HttpServletRequest request,
                                      @NotNull(message = "商品skuId不能为空") Long productId, String areaCode,
                                      @NotNull(message = "加购数量不能为空") Integer number, String financeRuleCode) {

        Member member = UserUtil.getUser(request, Member.class);
        BizAssertUtil.isTrue(member == null || member.getMemberId() == null,"用户未登录");

        cartService.addCart(member, productId, areaCode, number, financeRuleCode);

        return SldResponse.success("已加入购物车");
    }

    @GetMapping("countInCart")
    @ApiOperation(value = "购物车内有效商品数量", tags = "CORE")
    public JsonResult<Integer> countInCart(HttpServletRequest request,
                                           @RequestParam(value = "productType", required = false)
                                           @ApiParam("商品类型：1.C端商品 2.采购商品") Integer productType) {
        Member member = UserUtil.getUser(request, Member.class);

        if (member == null || member.getMemberId() == null) {
            return SldResponse.success(0);
        }

        if (Objects.isNull(productType)) {
            productType = GoodsTypeEnum.ORDINARY.getCode();
        }
        Integer count = cartService.countInCart(member.getMemberId(), productType);

        return SldResponse.success(count);
    }

    @GetMapping("cartList")
    @ApiOperation(value = "购物车列表接口", tags = "CORE")
    @ThreadLocalRemoveTag
    public JsonResult<CartListVO> cartList(HttpServletRequest request,
                                           @RequestParam(value = "productType", required = false)
                                           @ApiParam("商品类型：1.C端商品 2.采购商品") Integer productType) {
        Member member = UserUtil.getUser(request, Member.class);

        if (member == null || member.getMemberId() == null) {
            return SldResponse.success(new CartListVO());
        }

        if (Objects.isNull(productType)) {
            productType = GoodsTypeEnum.ORDINARY.getCode();
        }
        CartListVO vo = cartService.getCartList(member.getMemberId(), productType);

        return SldResponse.success(vo);
    }


    @PostMapping("changeNum")
    @ApiOperation(value = "修改购物车数量接口", tags = "CORE" )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "cartId", value = "购物车id", required = true, paramType = "query"),
            @ApiImplicitParam(name = "number", value = "修改后数量", required = true, paramType = "query")
    })
    public JsonResult<String> changeNum(HttpServletRequest request,
                                        @NotNull(message = "cartId不能为空") Integer cartId,
                                        @NotNull(message = "请选择要购买的数量") Integer number) {

        Member member = UserUtil.getUser(request, Member.class);
        if (member == null || member.getMemberId() == null) {
            return SldResponse.success("无用户信息");
        }

        cartService.changeNum(member, cartId, number);
        return SldResponse.success("修改成功");
    }

    @PostMapping("deleteCarts")
    @ApiOperation(value = "批量删除购物车接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "cartIds", value = "购物车id集合", required = true, paramType = "query")
    })
    public JsonResult<String> deleteCarts(HttpServletRequest request,
                                          @NotEmpty(message = "请选择要删除的数据") String cartIds) {
        //参数校验
        BizAssertUtil.notEmpty(cartIds, "请选择要删除的数据");
        BizAssertUtil.notFormatFrontIds(cartIds, "cartIds格式错误,请重试");
        Member member = UserUtil.getUser(request, Member.class);
        BizAssertUtil.isTrue(member == null || member.getMemberId() == null, "用户未登录");
        CartExample cartExample = new CartExample();
        cartExample.setMemberId(member.getMemberId());
        cartExample.setCartIdIn(cartIds);
        cartModel.deleteCartByExample(cartExample);

        return SldResponse.success("删除成功");
    }


    @PostMapping("emptyInvalid")
    @ApiOperation("清空失效购物车")
    public JsonResult<String> emptyInvalid(HttpServletRequest request) {
        Member member = UserUtil.getUser(request, Member.class);
        BizAssertUtil.isTrue(member == null || member.getMemberId() == null,"用户未登录");
        CartExample cartExample = new CartExample();
        cartExample.setProductState(CartConst.STATTE_INVALID);
        cartExample.setMemberId(member.getMemberId());
        cartModel.deleteCartByExample(cartExample);

        return SldResponse.success("清空成功");
    }

    @PostMapping("checkedCarts")
    @ApiOperation(value = "修改购物车选中状态", tags = "CORE")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "cartIds", value = "购物车id集合", required = true, paramType = "query"),
            @ApiImplicitParam(name = "checked", value = "是否选中：0=全不选、1=全选中", required = true, paramType = "query")
    })
    public JsonResult<String> checkedCarts(HttpServletRequest request,
                                           @NotEmpty(message = "请选择要勾选的数据") String cartIds,
                                           @NotNull(message = "选中状态不能为空") Integer checked) {
        //参数校验
        BizAssertUtil.notEmpty(cartIds, "请选择要选中的数据");
        BizAssertUtil.notFormatFrontIds(cartIds, "cartIds格式错误,请重试");

        Member member = UserUtil.getUser(request, Member.class);
        BizAssertUtil.isTrue(member == null || member.getMemberId() == null,"用户未登录");
        CartExample cartExample = new CartExample();
        cartExample.setMemberId(member.getMemberId());
        cartExample.setCartIdIn(cartIds);
        cartExample.setProductState(CartConst.STATTE_NORMAL);
        CartPO cartPO = new CartPO();
        cartPO.setIsChecked(checked);
        cartModel.updateCartByExample(cartPO, cartExample);

        return SldResponse.success("选中成功");
    }

    @PostMapping("invertChecked")
    @ApiOperation("购物车反选接口")
    public JsonResult<String> invertChecked(HttpServletRequest request,
                                            @RequestParam(value = "productType", required = false)
                                            @ApiParam("商品类型：1.C端商品 2.采购商品") Integer productType) {
        Member member = UserUtil.getUser(request, Member.class);
        BizAssertUtil.isTrue(member == null || member.getMemberId() == null, "用户未登录");

        if (Objects.isNull(productType)) {
            productType = GoodsTypeEnum.ORDINARY.getCode();
        }
        CartExample cartExample = new CartExample();
        cartExample.setMemberId(member.getMemberId());
        cartExample.setProductState(CartConst.STATTE_NORMAL);
        cartExample.setProductType(productType);
        List<CartPO> cartPOList = cartModel.getCartList(cartExample, null);
        if (!CollectionUtils.isEmpty(cartPOList)) {
            cartPOList.forEach(cartDb -> {
                CartPO update = new CartPO();
                update.setCartId(cartDb.getCartId());
                update.setIsChecked(Math.abs(cartDb.getIsChecked() - 1));
                cartModel.updateCart(update);
            });
        }

        return SldResponse.success("反选成功");
    }

    @PostMapping("changePromotion")
    @ApiOperation("修改购物车参与活动接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "cartId", value = "购物车id", required = true, paramType = "query"),
            @ApiImplicitParam(name = "promotionId", value = "活动id，0表示不参与活动", required = true, paramType = "query"),
            @ApiImplicitParam(name = "promotionType", value = "活动类型", required = true, paramType = "query"),
            @ApiImplicitParam(name = "promotionDescription", value = "活动描述", required = true, paramType = "query")
    })
    public JsonResult<String> changePromotion(HttpServletRequest request,
                                              @NotNull(message = "请选择要修改的数据") Integer cartId, Integer promotionId,
                                              Integer promotionType, String promotionDescription) {

        Member member = UserUtil.getUser(request, Member.class);
        BizAssertUtil.isTrue(member == null || member.getMemberId() == null,"用户未登录");

        CartExample cartExample = new CartExample();
        cartExample.setMemberId(member.getMemberId());
        cartExample.setCartIdIn(cartId + "");
        CartPO cartPO = new CartPO();
        cartPO.setPromotionType(promotionType);
        cartPO.setPromotionId(promotionId);
        cartPO.setPromotionDescription(promotionDescription);
        cartModel.updateCartByExample(cartPO, cartExample);

        return SldResponse.success("修改活动成功");
    }

    @PostMapping("moveToCollection")
    @ApiOperation("移入收藏夹接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "cartIds", value = "购物车id集合", required = true, paramType = "query"),
    })
    public JsonResult<String> moveToCollection(HttpServletRequest request,
                                               @NotEmpty(message = "请选择要收藏的数据") String cartIds) {
        //参数校验
        BizAssertUtil.notFormatFrontIds(cartIds, "cartIds格式错误,请重试");

        Member member = UserUtil.getUser(request, Member.class);
        BizAssertUtil.isTrue(member == null || member.getMemberId() == null,"用户未登录");

        CartExample cartExample = new CartExample();
        cartExample.setMemberId(member.getMemberId());
        cartExample.setCartIdIn(cartIds);
        List<CartPO> cartPOList = cartModel.getCartList(cartExample, null);
        if (!CollectionUtils.isEmpty(cartPOList)) {
            StringBuilder productIds = new StringBuilder();
            cartPOList.forEach(cart -> {
                productIds.append(",").append(cart.getProductId());
            });
            // 收藏
            memberFollowProductFeignClient.editMemberFollowProduct(productIds.substring(1), true, member.getMemberId());
            //收藏完毕删除购物车
            cartModel.deleteCartByExample(cartExample);
        }

        return SldResponse.success("操作成功");
    }

    @PostMapping("updateCartsGoodsName")
    @ApiOperation("更改购物车商品信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "cartIds", value = "购物车id集合，逗号【,】分隔", required = true, paramType = "query"),
            @ApiImplicitParam(name = "goodsName", value = "商品名称", required = true, paramType = "query"),
    })
    public JsonResult<Boolean> updateCartsGoodsName(String cartIds, String goodsName) {
        return SldResponse.success(cartService.updateCartsGoodsName(cartIds, goodsName));
    }

}
