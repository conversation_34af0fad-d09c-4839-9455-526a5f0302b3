package com.cfpamf.ms.mallorder.controller.seller;

import com.cfpamf.ms.mallorder.api.feign.OrderSellerPrivilegeFeignClient;
import com.cfpamf.ms.mallorder.dto.SellerPrivilegeDTO;
import com.cfpamf.ms.mallorder.service.PrivilegeService;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "seller权限管理")
@RestController
@RequestMapping("/seller/privilege")
public class SellerPrivilegeCotroller implements OrderSellerPrivilegeFeignClient {

	@Autowired
	private PrivilegeService privilegeService;

	@GetMapping("/storePrivilege")
	@ApiOperation("权限")
	public JsonResult<SellerPrivilegeDTO> storePrivilege(Integer storeId) {
		return SldResponse.success(privilegeService.getSellerPrivilegeInfo(storeId));
	}
	
	@GetMapping("/storeDeliveryPrivilege")
    @ApiOperation("店铺发货权限")
    public JsonResult<Boolean> storeDeliveryPrivilege(String orderSn) {
        return SldResponse.success(privilegeService.storeDeliveryPrivilege(orderSn));
    }
}
