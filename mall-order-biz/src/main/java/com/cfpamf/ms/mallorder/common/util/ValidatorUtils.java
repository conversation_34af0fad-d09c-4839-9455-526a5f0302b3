package com.cfpamf.ms.mallorder.common.util;

import com.cfpamf.ms.mallorder.dto.OrderOfflineImportDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.Errors;
import org.springframework.validation.Validator;

/**
 * <AUTHOR>
 * @Date 2024/9/2 16:22
 */
@Component
@Slf4j
public class ValidatorUtils {
    private final Validator validator;

    public ValidatorUtils(Validator validator) {
        this.validator = validator;
    }

    public boolean validateOrder(Object obj) {
        Errors errors = new BeanPropertyBindingResult(obj, obj.getClass().getSimpleName());
        validator.validate(obj, errors);

        if (errors.hasErrors()) {
            log.info("Validation errors: " + errors.getAllErrors());
            return false;
        }
        return true;
    }
}
