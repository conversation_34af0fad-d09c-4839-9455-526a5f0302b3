package com.cfpamf.ms.mallorder.controller.fegin.facade;

import com.cfpamf.ms.mallorder.api.feign.OrderRefundRecordFeignClient;
import com.cfpamf.ms.mallorder.v2.service.OrderRefundRecordService;
import com.cfpamf.ms.mallorder.vo.OrderRefundRecordVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
public class OrderRefundRecordFeign implements OrderRefundRecordFeignClient {

    @Autowired
    private OrderRefundRecordService orderRefundRecordService;

    @Override
    @PostMapping("/v1/feign/business/orderRefundRecord/listByAfsSn")
    public List<OrderRefundRecordVO> listByAfsSn(@RequestParam("afsSn") String afsSn,
                                                 @RequestParam(value = "refundStatus",required = false) Integer refundStatus) {
        return orderRefundRecordService.listByAfsSn(afsSn,refundStatus);
    }

    @Override
    @PostMapping("/v1/feign/business/orderRefundRecord/listByPayNo")
    public List<OrderRefundRecordVO> listByPayNo(@RequestParam("payNo") String payNo,
                                                 @RequestParam(value = "refundStatusList",required = false) List<Integer> refundStatusList){
        return orderRefundRecordService.listByPayNo(payNo,refundStatusList);
    }

    @Override
    @PostMapping("/v1/feign/business/orderRefundRecord/listByOrderSn")
    public List<OrderRefundRecordVO> listByOrderSn(@RequestParam("orderSn") String orderSn,
                                            @RequestParam(value = "refundStatusList",required = false) List<Integer> refundStatusList){
        return orderRefundRecordService.listByOrderSn(orderSn,refundStatusList);
    }

    @Override
    @PostMapping("/v1/feign/business/orderRefundRecord/findByRefundNo")
    public OrderRefundRecordVO findByRefundNo(@RequestParam("refundNo") String refundNo) {
        return orderRefundRecordService.findByRefundNo(refundNo);
    }
}
