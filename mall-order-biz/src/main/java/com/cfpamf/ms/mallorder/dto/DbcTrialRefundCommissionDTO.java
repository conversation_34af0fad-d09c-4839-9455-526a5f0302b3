package com.cfpamf.ms.mallorder.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ApiModel(value="DbcTrialRefundCommissionDTO对象", description="分销退单佣金试算DTO")
public class DbcTrialRefundCommissionDTO implements Serializable {

    private static final long serialVersionUID = 5133204417569168348L;

    @NotBlank(message = "店铺id不能为空")
    @ApiModelProperty(value = "店铺id")
    private String storeId;

    @NotBlank(message = "退款订单编号不能为空")
    @ApiModelProperty(value = "退款订单编号")
    private String refundOrderId;

    @NotBlank(message = "订单编号不能为空")
    @ApiModelProperty(value = "订单编号")
    private String orderId;

    @NotNull(message = "退款金额不能为空")
    @ApiModelProperty(value = "退款金额")
    private BigDecimal refundMoney;

    @NotBlank(message = "商品编号不能为空")
    @ApiModelProperty(value = "商品编号")
    private String commodityCode;
    @NotNull(message = "退款商品数量不能为空")
    @ApiModelProperty(value = "退款商品数量")
    private Integer commodityNum;

    @NotNull(message = "业务渠道不能为空")
    @ApiModelProperty(value = "业务渠道参考枚举BusinessChannelEnum:1-自研电商,3-保险,5-标准电商,7-农服土地托管")
    private Integer businessChannel;

    @NotNull(message = "是否最后一笔退款不能为空")
    @ApiModelProperty(value = "是否最后一笔退款,true-是,false-否")
    private Boolean lastRefund;
    @NotNull(message = "退款类型不能为空")
    @ApiModelProperty(value = "退款类型：1-仅退款 2-退货退款 3-换货退款")
    private Integer returnType;

    @ApiModelProperty(value = "订单商品发货状态：含义见枚举 OrderProductDeliveryEnum")
    private Integer orderProductDelivery;

    @ApiModelProperty(value = "订单模式：含义见枚举 OrderPatternEnum")
    private Integer orderPattern;

}
