package com.cfpamf.ms.mallorder.integration.promotion.facade;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.promotion.facade.vo.discount.CouponLockSummary;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@FeignClient(
		name = "ms-promotion-service",
		url = "${ms-promotion-service.url}"
)
public interface DiscountCouponFacade {

	@PostMapping({"/discount-coupon/package/lockCoupons"})
	Result<Boolean> lockCoupons(@RequestBody CouponLockSummary var1);

	@ApiOperation("按申请单号和优惠券批次号查询总已贴息金额")
	@GetMapping("/discount-coupon/totalDiscountAmount")
	public Result<BigDecimal> queryTotalDiscountAmount(@RequestParam("applSeq") String applSeq,
													   @RequestParam("batchNo") String batchNo);
}

