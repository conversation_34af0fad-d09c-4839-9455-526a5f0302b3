package com.cfpamf.ms.mallorder.integration.facade.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * author:llk2021
 * date:2022/3/7
 **/
@Data
public class ProductSkuUnitVO {

    private Long id;

    @ApiModelProperty("skuId")
    private String skuId;

    @ApiModelProperty(value = "单位名称")
    private String unitName;

    @ApiModelProperty(value = "单位code")
    private String unitCode;

    @ApiModelProperty("skuUnitCode单位编码")
    private String skuUnitCode;

    @ApiModelProperty(value = "单位类型")
    private Integer unitType;
    
    @ApiModelProperty(value = "单位类型")
    private String unitTypeName;

    @ApiModelProperty("包装数")
    private BigDecimal packageNum;

    @ApiModelProperty(value = "重量（kg）")
    private BigDecimal weight;

    @ApiModelProperty(value = "说明")
    private String remark;

    @ApiModelProperty(value = "是否财务系统销售单位")
    private Boolean financeSellFlag;

    @ApiModelProperty(value = "是否财务系统采购单位")
    private Boolean financePurchaseFlag;
}
