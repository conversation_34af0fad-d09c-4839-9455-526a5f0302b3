package com.cfpamf.ms.mallorder.controller.fegin.facade;

import com.cfpamf.ms.mallorder.service.IPayMethodService;
import com.cfpamf.ms.mallorder.vo.PayMethodVOV2;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Slf4j
public class OrderPayMethodFeign {

    @Resource
    private IPayMethodService payMethodService;

    @PostMapping("/v1/feign/business/payMethod/listPayMethodByCode")
    public JsonResult<List<PayMethodVOV2>> listPayMethodByCode(@RequestBody List<String> payMethodCodes) {
        return SldResponse.success(payMethodService.listPayMethodByCode(payMethodCodes));
    }

}
