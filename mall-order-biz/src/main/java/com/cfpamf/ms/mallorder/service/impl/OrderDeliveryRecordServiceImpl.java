package com.cfpamf.ms.mallorder.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cfpamf.ms.mallorder.enums.ChannelEnum;
import com.cfpamf.ms.mallorder.mapper.OrderDeliveryRecordMapper;
import com.cfpamf.ms.mallorder.po.OrderDeliveryRecordPO;
import com.cfpamf.ms.mallorder.req.OrderDeliveryReq;
import com.cfpamf.ms.mallorder.service.IOrderDeliveryRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class OrderDeliveryRecordServiceImpl extends ServiceImpl<OrderDeliveryRecordMapper, OrderDeliveryRecordPO> implements IOrderDeliveryRecordService {

    @Override
    public void addDeliveryRecord(Long logisticId,String operator, OrderDeliveryReq deliveryReq, String bizUniqueNo) {
        OrderDeliveryRecordPO orderDeliveryRecordPO = new OrderDeliveryRecordPO();
        orderDeliveryRecordPO.setLogisticId(logisticId);
        orderDeliveryRecordPO.setOrderSn(deliveryReq.getOrderSn());
        orderDeliveryRecordPO.setDeliverType(deliveryReq.getDeliverType());
        orderDeliveryRecordPO.setExpressNumber(deliveryReq.getExpressNumber());
        orderDeliveryRecordPO.setExpressCompanyCode(deliveryReq.getExpressCompanyCode());
        orderDeliveryRecordPO.setExpressName(deliveryReq.getExpressName());
        orderDeliveryRecordPO.setDeliverName(deliveryReq.getDeliverName());
        orderDeliveryRecordPO.setDeliverMobile(deliveryReq.getDeliverMobile());
        orderDeliveryRecordPO.setBizUniqueNo(bizUniqueNo);
        //
        orderDeliveryRecordPO.setCreateBy(operator);
        this.save(orderDeliveryRecordPO);
    }
}
