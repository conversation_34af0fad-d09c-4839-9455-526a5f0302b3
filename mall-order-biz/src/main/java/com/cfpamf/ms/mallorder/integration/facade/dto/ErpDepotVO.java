package com.cfpamf.ms.mallorder.integration.facade.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;


@Data
public class ErpDepotVO {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("仓库名称")
    private String name;

    @ApiModelProperty("仓库编码")
    private String deptCode;

    @ApiModelProperty("库存类型 1-销售库存 2-实物库存")
    private Integer stockType;

    @ApiModelProperty("库存类型名称")
    private String stockTypeName;

    @ApiModelProperty("启用状态 0-正常-默认 1-禁用）")
    private Integer state;

    @ApiModelProperty(value = "所属省编码")
    private String province;

    @ApiModelProperty(value = "所属市编码")
    private String city;

    @ApiModelProperty(value = "所属区编码")
    private String county;

    @ApiModelProperty(value = "所属省")
    private String provinceDesc;

    @ApiModelProperty(value = "所属市")
    private String cityDesc;

    @ApiModelProperty(value = "所属区")
    private String countyDesc;

    @ApiModelProperty("仓库详细地址")
    private String address;

    @ApiModelProperty("仓储费")
    private BigDecimal warehousing;

    @ApiModelProperty("搬运费")
    private BigDecimal truckage;

    @ApiModelProperty("类型 1-销售池 2-店铺仓 3-中转仓 4-分支仓 5-区域仓")
    private Integer type;

    @ApiModelProperty("仓库类型名称")
    private String typeName;

    @ApiModelProperty("排序")
    private String sort;

    @ApiModelProperty("描述")
    private String remark;

    @ApiModelProperty("负责人姓名")
    private String principalName;

    @ApiModelProperty("负责人手机号")
    private String mobile;

    @ApiModelProperty("商家编码")
    private String merchantCode;

    @ApiModelProperty("商家编码")
    private String merchantName;

    @ApiModelProperty("租户id")
    private Long tenantId;

    @ApiModelProperty("创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    @ApiModelProperty("区域/分支编码")
    private String branchCode;

    @ApiModelProperty("员工工号")
    private String employeeNo;
}
