package com.cfpamf.ms.mallorder.integration.facade;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cfpamf.framework.autoconfigure.common.result.Result;
import com.cfpamf.ms.mallorder.integration.facade.dto.*;
import com.cfpamf.ms.mallorder.integration.wms.dto.JingDongInterceptDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

@FeignClient(name = "wms-service", url = "${wms-service.url}")
public interface WmsFacade {
	/**
	 * 查询发货总数
	 * @param query 入参
	 * @return Long
	 */
	@ApiOperation(value = "查询发货总数")
	@PostMapping(value = "/feign/mall/deliver/total/query")
	Result<Long> getDeliverTotalQty(@RequestBody MallDeliverQtyQuery query);

	@ApiOperation(value = "分页列表")
	@PostMapping(value = "/feign/site/pageList")
	Result<Page<SiteVo>> pageList(@RequestBody SiteQuery query) ;

	@ApiOperation(value = "新建站点")
	@PostMapping("/feign/site/create")
	Result<Long> create(@RequestBody @Valid SiteDto request);

	@ApiOperation(value = "京东拦截预校验接口,false-需要拦截，ture无需拦截")
	@PostMapping("/feign/deliverGoods/jingDongInterceptCheck")
	Result<Boolean> jingDongInterceptCheck(@RequestBody @Valid JingDongInterceptDTO query);

	@ApiOperation(value = "京东拦截接口")
	@PostMapping("/feign/deliverGoods/jingDongIntercept")
	Result<Integer> jingDongIntercept(@RequestBody @Valid JingDongInterceptDTO query);

}
