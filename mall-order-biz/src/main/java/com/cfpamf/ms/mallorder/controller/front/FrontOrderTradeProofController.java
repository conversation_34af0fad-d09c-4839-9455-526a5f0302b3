package com.cfpamf.ms.mallorder.controller.front;


import com.cfpamf.ms.mall.filecenter.domain.dto.FileScenesMaterialProofDTO;
import com.cfpamf.ms.mall.filecenter.domain.vo.FileScenesProofVO;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.builder.AgreementBuilder;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.util.ValidUtils;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.OrderTradeProofQueryDTO;
import com.cfpamf.ms.mallorder.dto.ReceiveMaterialResultDTO;
import com.cfpamf.ms.mallorder.dto.TradeProofUploadDTO;
import com.cfpamf.ms.mallorder.service.IOrderTradeProofService;
import com.cfpamf.ms.mallorder.vo.*;
import com.cfpamf.ms.mallsystem.vo.Agreement;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import com.slodon.bbc.core.util.UserUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 订单交易凭证表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-12
 */
@RestController
@Api(tags = "front-订单交易凭证操作")
@RequestMapping("front/orderTradeProof")
public class FrontOrderTradeProofController {

    @Autowired
    private IOrderTradeProofService orderTradeProofService;
    @Autowired
    private AgreementBuilder agreementBuilder;

    @ApiOperation("保存订单凭证资料")
    @PostMapping("/saveFileMaterial")
    public JsonResult<Boolean> saveFileMaterial(HttpServletRequest request, @RequestBody @Valid @NotNull FileScenesMaterialProofDTO paramDTO) {
        Member member = UserUtil.getUser(request, Member.class);
        return SldResponse.success(orderTradeProofService.saveScenesMaterial(OrderConst.LOG_ROLE_MEMBER, member.getMemberId().toString(), member.getMemberName(), paramDTO));

    }

    @ApiOperation("校验文件风险等级")
    @PostMapping("/checkReceiveMaterial")
    public JsonResult<ReceiveMaterialResultDTO> checkReceiveMaterial(@RequestParam("orderSn") String orderSn, @RequestParam("fileUrl") String fileUrl) {

        return SldResponse.success(orderTradeProofService.checkReceiveMaterial(orderSn, fileUrl));

    }

    @ApiOperation("查询文件中心凭证资料")
    @GetMapping("/queryFileMaterial")
    public JsonResult<List<FileScenesProofVO>> queryFileMaterial(@RequestParam(value = "proofNo", required = true) String proofNo,
                                                                 @RequestParam(value = "subProofNo", required = false) String subProofNo,
                                                                 @RequestParam(value = "sceneNo", required = false) String sceneNo) {

        return SldResponse.success(orderTradeProofService.queryScenesMaterial(proofNo, subProofNo, sceneNo, null, false));
    }

    @ApiOperation("匹配订单规则资料列表")
    @PostMapping("/matchSceneMaterials")
    public JsonResult<List<OrderTradeProofVO>> matchSceneMaterials(@RequestBody @NotNull OrderTradeProofQueryDTO queryDTO) {

        return SldResponse.success(orderTradeProofService.matchSceneMaterials(queryDTO));
    }

    @ApiOperation("获取协议内容")
    @GetMapping("/getAgreement")
    public JsonResult<Agreement> getAgreement(@RequestParam("agreementCode") @NotBlank String agreementCode,
                                              @RequestParam("userName") @NotBlank String userName) {

        return SldResponse.success(agreementBuilder.getAgreement(userName, agreementCode));
    }

    @ApiOperation("法大大客户实名+签约授权检查")
    @PostMapping("/customerCheck")
    public JsonResult<MallCustomerCheckResultVO> customerCheck(HttpServletRequest request, @RequestBody @Valid CustomerCheckParamVO checkParamVO) {
        Member member = UserUtil.getUser(request, Member.class);
        if (Objects.isNull(member) || ValidUtils.isEmpty(member.getMemberId())) {
            throw new BusinessException("请先登录");
        }
        MallCustomerCheckResultVO resultVO = orderTradeProofService.customerCheck(member, null, checkParamVO.getUrl(), CommonConst.CONTRACT_CHECK_GEN_URL_YES,checkParamVO.getIsMiniProgram());
        return SldResponse.success(resultVO);
    }

    /**
     * 合同默签
     *
     * @param request 请求
     * @param paramVO 合同默签vo
     * @return 合同默签结果vo
     */
    @ApiOperation("合同默签")
    @PostMapping("/contractSign")
    public JsonResult<ContractSignResultVO> contractSign(HttpServletRequest request, @RequestBody @Valid ContractSignParamVO paramVO) {
        Member member = UserUtil.getUser(request, Member.class);
        if (Objects.isNull(member) || ValidUtils.isEmpty(member.getMemberId())) {
            throw new BusinessException("请先登录");
        }

        ContractSignResultVO result = orderTradeProofService.contractSign(member, paramVO);
        return SldResponse.success(result);
    }

    @ApiOperation("同步生成合同")
    @GetMapping("/generateContract")
    public JsonResult<List<String>> generateContract(@RequestParam("orderSn") String orderSn, @RequestParam("materialNo") String materialNo) {
        List<String> viewUrls = orderTradeProofService.generateContractSync(orderSn, materialNo);
        return SldResponse.success(viewUrls);
    }

    /**
     * 合同url查询
     *
     * @param request      请求实体
     * @param proofId      交易凭证id
     * @param needLoanInfo 是否需要返回信贷信息，1-需要，0-不需要
     * @return 合同信息结果
     */
    @ApiOperation("合同查询")
    @PostMapping(value = "/contractSearch")
    public JsonResult<ContractSearchResultVO> contractSearch(HttpServletRequest request, @RequestParam("proofId") Long proofId, @RequestParam(value = "needLoanInfo", defaultValue = "1") Integer needLoanInfo) {
        Member member = UserUtil.getUser(request, Member.class);
        if (Objects.isNull(member) || ValidUtils.isEmpty(member.getMemberId())) {
            throw new BusinessException("请先登录");
        }
        ContractSearchResultVO result = orderTradeProofService.contractSearch(proofId, needLoanInfo);
        return SldResponse.success(result);
    }

    @ApiOperation("刷脸信息查询")
    @PostMapping("/faceAuthInfo")
    public JsonResult<FaceAuthResultVO> faceAuthInfo(HttpServletRequest request) {
        Member member = UserUtil.getUser(request, Member.class);
        if (Objects.isNull(member) || ValidUtils.isEmpty(member.getMemberId())) {
            throw new BusinessException("请先登录");
        }
        FaceAuthResultVO resultVO = orderTradeProofService.faceAuthInfo(member);
        return SldResponse.success(resultVO);
    }

    /**
     * 合同默签
     *
     * @return 合同默签结果vo
     */
    @ApiOperation("合同默签")
    @PostMapping("/contractReSign")
    public JsonResult<List<Long>> contractReSign(@RequestBody List<Long> proofIdList,@RequestParam(value = "force",defaultValue = "0")Integer force) {
        orderTradeProofService.contractReSign(proofIdList,force);
        return SldResponse.success(proofIdList);
    }

    @PostMapping("uploadProof")
    @ApiOperation("上传凭证")
    public JsonResult<Boolean> uploadProof(HttpServletRequest request, @RequestBody @Valid TradeProofUploadDTO proofUploadDTO){
        Member member = UserUtil.getUser(request,Member.class);
        Boolean result = orderTradeProofService.uploadProofMall(OrderConst.LOG_ROLE_ADMIN, String.valueOf(member.getMemberId()),member.getMemberName(),"电商系统",proofUploadDTO);
        return SldResponse.success(result);
    }

    @GetMapping("/paperSignExport")
    @ApiOperation("签单导出")
    public JsonResult<String> paperSignExport(HttpServletRequest request,@RequestParam("orderSn")String orderSn){
        Member member = UserUtil.getUser(request,Member.class);
        if (Objects.isNull(member) || ValidUtils.isEmpty(member.getMemberId())) {
            throw new BusinessException("请先登录");
        }
        String exportUrl = orderTradeProofService.paperSignExport(orderSn);
        JsonResult<String> result = SldResponse.success();
        result.setData(exportUrl);
        return result;
    }


    @ApiOperation("获取农服经销商合同")
    @GetMapping("/getAgricDealerContract")
    public JsonResult<AgricDistributorContractResultVO> getAgricDealerContract(@RequestParam("orderSn")String orderSn){
        AgricDistributorContractResultVO result = orderTradeProofService.getAgricDealerContract(orderSn);
        return SldResponse.success(result);
    }

    /**
     * 合同默签
     *
     * @param request 请求
     * @param paramVO 合同默签vo
     * @return 合同默签结果vo
     */
    @ApiOperation("合同默签")
    @PostMapping("/agricDealerContractSign")
    public JsonResult<ContractSignResultVO> agricDealerContractSign(HttpServletRequest request, @RequestBody @Valid ContractSignParamVO paramVO) {
        Member member = UserUtil.getUser(request, Member.class);
        if (Objects.isNull(member) || ValidUtils.isEmpty(member.getMemberId())) {
            throw new BusinessException("请先登录");
        }

        ContractSignResultVO result = orderTradeProofService.agricDealerContractSign(member, paramVO);
        return SldResponse.success(result);
    }

}
