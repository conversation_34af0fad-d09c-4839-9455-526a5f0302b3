package com.cfpamf.ms.mallorder.integration.goods;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cfpamf.framework.autoconfigure.common.exception.MSException;
import com.cfpamf.ms.mallgoods.facade.api.GoodsBusDistriFeignClient;
import com.cfpamf.ms.mallgoods.facade.api.GoodsFeignClient;
import com.cfpamf.ms.mallgoods.facade.api.ProductFeignClient;
import com.cfpamf.ms.mallgoods.facade.dto.AgricFeeCalculateDTO;
import com.cfpamf.ms.mallgoods.facade.request.GoodsLandingPricePramaDTO;
import com.cfpamf.ms.mallgoods.facade.vo.*;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.cfpamf.ms.mallorder.po.OrderProductPO;
import com.slodon.bbc.core.constant.ResponseConst;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.response.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class GoodsFeignIntegration {

    //商品未开启业务分销
    public final static int BUSINESS_DISTRIBUTION_EXCEPTION_STATE_89991 = 89991;
    //业务分销商品不存在地区价格
    public final static int BUSINESS_DISTRIBUTION_EXCEPTION_STATE_89992 = 89992;

    @Autowired
    private ProductFeignClient productFeignClient;
    @Autowired
    private GoodsFeignClient goodsFeignClient;
    @Autowired
    private GoodsBusDistriFeignClient goodsBusDistriFeignClient;

    public List<AgricFeeCalculateVO> getAgricFeeCalculateVOList(List<AgricFeeCalculateDTO> agricFeeCalculateDTOList) {
        List<AgricFeeCalculateVO> agricFeeCalculateList = new ArrayList<>();
        try {
            JsonResult<List<AgricFeeCalculateVO>> result = productFeignClient.listProductAgricFee(agricFeeCalculateDTOList);
            if (result == null || CollectionUtils.isEmpty(result.getData())) {
                return null;
            }
            return result.getData();
        } catch (Exception e) {
            log.warn("获取商品农服费用失败");
        }
        return agricFeeCalculateList;
    }

    public GoodsLandingPriceResponse getGoodsDistributionLandingPrice(GoodsLandingPricePramaDTO paramDTO) {
        if (Objects.isNull(paramDTO)) {
            throw new BusinessException(ErrorCodeEnum.C.CALL_EXCEPTION.getCode(), "试算业务分销商品的落地价入参不能为空");
        }
        JsonResult<GoodsLandingPriceResponse> landingPriceResult;
        try {
            log.info("试算业务分销商品的落地价,paramDTO:{}",JSONObject.toJSONString(paramDTO));
            landingPriceResult = goodsBusDistriFeignClient.getGoodsDistributionLandingPrice(paramDTO);
        } catch (Exception ex) {
            log.error("试算业务分销商品的落地价出现未知异常",ex);
            throw new BusinessException(ErrorCodeEnum.C.CALL_EXCEPTION.getCode(), "试算业务分销商品的落地价调用商品服务出现未知异常");
        }
        if (Objects.isNull(landingPriceResult)) {
            throw new BusinessException(ErrorCodeEnum.C.CALL_EXCEPTION.getCode(), "试算业务分销商品的落地价调用商品服务返回报文异常");
        }
        if (BUSINESS_DISTRIBUTION_EXCEPTION_STATE_89991 == landingPriceResult.getState()) {
            log.info("试算业务分销商品的落地价,返回该商品未开启业务分销,返回结果:{}",JSONObject.toJSONString(landingPriceResult));
            return null;
        }
        if (BUSINESS_DISTRIBUTION_EXCEPTION_STATE_89992 == landingPriceResult.getState()) {
            log.info("试算业务分销商品的落地价,返回该商品未开启地区价格,返回结果:{}",JSONObject.toJSONString(landingPriceResult));
            return null;
        }
        if (ResponseConst.STATE_SUCCESS != landingPriceResult.getState()) {
            throw new BusinessException(ErrorCodeEnum.C.CALL_EXCEPTION.getCode(), "试算业务分销商品的落地价调用商品服务返回报文异常");
        }
        log.info("试算业务分销商品的落地价,返回结果:{}", JSONObject.toJSONString(landingPriceResult.getData()));
        return landingPriceResult.getData();
    }

    public Map<Long, Goods> getGoodsByProductIds(List<Long> productIds) {
        List<Product> productList = productFeignClient.getProductListByProductIds(productIds);
        List<Long> goodsIdList = productList.stream().map(Product::getGoodsId).distinct().collect(Collectors.toList());
        Map<Long, Goods> goodsMap = goodsFeignClient.getGoodsListByGoodsIds(goodsIdList).stream()
                .collect(Collectors.toMap(Goods::getGoodsId, goods -> goods, (k1, k2) -> k2));
        Map<Long, Goods> productIdGoodsMap = new HashMap();
        for (Product product : productList) {
            Goods goods = goodsMap.getOrDefault(product.getGoodsId(), null);
            if (goods != null) {
                productIdGoodsMap.put(product.getProductId(), goods);
            }
        }
        return productIdGoodsMap;
    }
}
