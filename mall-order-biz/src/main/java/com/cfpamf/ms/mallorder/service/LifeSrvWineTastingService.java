package com.cfpamf.ms.mallorder.service;

import com.cfpamf.ms.mallorder.req.LifeSrvWineTastingBchDetailRequest;
import com.cfpamf.ms.mallorder.req.LifeSrvWineTastingReportRequest;
import com.cfpamf.ms.mallorder.vo.pgrpt.LifesrvWineTastingBaseVO;
import com.cfpamf.ms.mallorder.vo.pgrpt.LifesrvWineTastingBchDetailBasicVO;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;

import javax.validation.Valid;

@Service
public interface LifeSrvWineTastingService {

	LifesrvWineTastingBaseVO basicMetrics(@Valid LifeSrvWineTastingReportRequest request);

    PageInfo<LifesrvWineTastingBchDetailBasicVO> bchDetailBasicMetrics(@Valid LifeSrvWineTastingBchDetailRequest request);
}
