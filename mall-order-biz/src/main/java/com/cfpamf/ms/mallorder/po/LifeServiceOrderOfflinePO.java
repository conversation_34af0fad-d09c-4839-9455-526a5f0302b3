package com.cfpamf.ms.mallorder.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cfpamf.ms.mallorder.common.base.BasePO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 生活服务线下订单表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bz_life_service_order_offline")
@ApiModel(value = "LifeServiceOrderOfflinePO对象", description = "生活服务线下订单表")
public class LifeServiceOrderOfflinePO extends BasePO {
    @ApiModelProperty("支付日期")
    private Date payDate;
    
    @ApiModelProperty("上级部门")
    private String parentDeptName;
    
    @ApiModelProperty("上级部门编码")
    private String parentDeptCode;
    
    @ApiModelProperty("部门名称")
    private String deptName;
    
    @ApiModelProperty("部门名称编码")
    private String deptCode;
    
    @ApiModelProperty("员工工号")
    private String employeeCode;
    
    @ApiModelProperty("员工姓名")
    private String employeeName;
    
    @ApiModelProperty("店铺id")
    private Long storeId;
    
    @ApiModelProperty("店铺名称")
    private String storeName;
    
    @ApiModelProperty("订单来源")
    private String orderSource;
    
    @ApiModelProperty("订单")
    private String orderSn;
    
    @ApiModelProperty("子单")
    private String subOrderSn;
    
    @ApiModelProperty("支付时间")
    private Date payTime;
    
    @ApiModelProperty("完成时间")
    private Date completeTime;
    
    @ApiModelProperty("一级分类名称")
    private String categoryLevel1Name;
    
    @ApiModelProperty("二级分类名称")
    private String categoryLevel2Name;
    
    @ApiModelProperty("三级分类名称")
    private String categoryLevel3Name;
    
    @ApiModelProperty("物料名称")
    private String materialName;
    
    @ApiModelProperty("订单数量")
    private BigDecimal orderQuantity;
    
    @ApiModelProperty("单价")
    private BigDecimal unitPrice;
    
    @ApiModelProperty("有效单价")
    private BigDecimal effectiveUnitPrice;
    
    @ApiModelProperty("退款数量")
    private BigDecimal refundQuantity;
    
    @ApiModelProperty("营收")
    private BigDecimal revenue;
} 