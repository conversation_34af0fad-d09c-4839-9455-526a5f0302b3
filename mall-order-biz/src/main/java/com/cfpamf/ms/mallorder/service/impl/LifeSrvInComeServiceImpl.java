package com.cfpamf.ms.mallorder.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.bms.facade.vo.OrganizationBaseVO;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.cfpamf.ms.mallorder.integration.facade.BmsIntegration;
import com.cfpamf.ms.mallorder.pgMapper.AdsLifesrvDepartmentProfitStaticDfpMapper;
import com.cfpamf.ms.mallorder.pgMapper.AdsLifesrvEmpProfitStaticDfpMapper;
import com.cfpamf.ms.mallorder.po.pgrpt.AdsLifesrvDepartmentProfitStaticDfpPO;
import com.cfpamf.ms.mallorder.po.pgrpt.AdsLifesrvEmpProfitSaticDfpPO;
import com.cfpamf.ms.mallorder.req.LifeSrvIncomeReportRequest;
import com.cfpamf.ms.mallorder.service.LifeSrvInComeService;
import com.cfpamf.ms.mallorder.vo.bean.OuterPageInfo;
import com.cfpamf.ms.mallorder.vo.pgrpt.LifesrvDepartmentProfitBasicVO;
import com.cfpamf.ms.mallorder.vo.pgrpt.LifesrvEmpProfitBasicVO;
import com.cfpamf.ms.mallorder.vo.pgrpt.LifesrvEmpProfitSummaryVO;
import com.slodon.bbc.core.exception.BusinessException;
import org.springframework.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class LifeSrvInComeServiceImpl implements LifeSrvInComeService {

    @Autowired
    private AdsLifesrvDepartmentProfitStaticDfpMapper adsLifesrvDepartmentProfitStaticDfpMapper;

    @Autowired
    private BmsIntegration bmsIntegration;

    @Autowired
    private AdsLifesrvEmpProfitStaticDfpMapper adsLifesrvEmpProfitStaticDfpMapper;

    @Override
    public OuterPageInfo<LifesrvEmpProfitBasicVO, LifesrvDepartmentProfitBasicVO> basicMetrics(LifeSrvIncomeReportRequest request) {
        log.info("查询生服收益指标 request{}" ,JSONObject.toJSONString(request));
        if (!"nation".equals(request.getAuthType()) && StringUtils.isEmpty(request.getOrgCode())) {
            throw new BusinessException(ErrorCodeEnum.U.INVALID_BIZ_DATA.getCode(),"机构编码不能为空");
        }
        /*
          区域权限时，特殊处理区域管理部编码
         */
        if(!StringUtils.isEmpty(request.getOrgCode())){
            OrganizationBaseVO org = bmsIntegration.getBizOrgBaseVOByOrgCode(request.getOrgCode());
            request.setOrgCode(org.getOrgCode());
        }

        if (!StringUtils.isEmpty(request.getSpvsrId())) {
            List<String> empCodeList = CollectionUtils.isEmpty(request.getEmpCodeList()) ? new ArrayList<>() : request.getEmpCodeList();
            empCodeList.add(request.getSpvsrId());
            request.setEmpCodeList(empCodeList);
        }

        AdsLifesrvDepartmentProfitStaticDfpPO outer = adsLifesrvDepartmentProfitStaticDfpMapper.getBasicMetrics(request);
        List<AdsLifesrvEmpProfitSaticDfpPO> innerList = adsLifesrvEmpProfitStaticDfpMapper.getBasicMetrics(request);
        LifesrvEmpProfitSummaryVO summaryVO = adsLifesrvEmpProfitStaticDfpMapper.getSummary(request);
        int count = Objects.isNull(summaryVO) ? 0 : summaryVO.getCount();

        if (Objects.isNull(outer)) {
            OuterPageInfo<LifesrvEmpProfitBasicVO,LifesrvDepartmentProfitBasicVO> outerPageInfo = new OuterPageInfo<>();
            outerPageInfo.setPageNum(request.getPageIndex());
            outerPageInfo.setSize(request.getPageSize());
            outerPageInfo.setTotal(count);
            outerPageInfo.setHasNextPage(request.getPageIndex() * request.getPageSize() < count);
            return outerPageInfo;
        }
        OuterPageInfo<LifesrvEmpProfitBasicVO,LifesrvDepartmentProfitBasicVO> outerPageInfo = getOuterPageInfo(outer, innerList, summaryVO);
        outerPageInfo.setPageNum(request.getPageIndex());
        outerPageInfo.setSize(request.getPageSize());
        outerPageInfo.setTotal(count);
        outerPageInfo.setHasNextPage(request.getPageIndex() * request.getPageSize() < count);
        return outerPageInfo;
    }

    private OuterPageInfo<LifesrvEmpProfitBasicVO, LifesrvDepartmentProfitBasicVO> getOuterPageInfo(AdsLifesrvDepartmentProfitStaticDfpPO outer, List<AdsLifesrvEmpProfitSaticDfpPO> innerList,LifesrvEmpProfitSummaryVO summaryVO) {
        LifesrvDepartmentProfitBasicVO outerVO = convertOuter(outer,summaryVO);
        List<LifesrvEmpProfitBasicVO> innerVOList = innerList.stream().map(inner -> {
            LifesrvEmpProfitBasicVO innerVO = new LifesrvEmpProfitBasicVO();
            BeanUtils.copyProperties(inner, innerVO);
            innerVO.setSyAllBaseCommissionAmt(formatBigAmount(inner.getSyAllBaseCommissionAmt(),2));
            innerVO.setSmAllBaseCommissionAmt(formatBigAmount(inner.getSmAllBaseCommissionAmt(),2));
            innerVO.setSyAllRevenueAmt(formatBigAmount(inner.getSyAllRevenueAmt(),2));
            innerVO.setSmAllRevenueAmt(formatBigAmount(inner.getSmAllRevenueAmt(),2));
            return innerVO;
        }).collect(Collectors.toList());
        return new OuterPageInfo<>(innerVOList,outerVO);
    }

    private LifesrvDepartmentProfitBasicVO convertOuter(AdsLifesrvDepartmentProfitStaticDfpPO outer,LifesrvEmpProfitSummaryVO summaryVO) {
        LifesrvDepartmentProfitBasicVO outerVO = new LifesrvDepartmentProfitBasicVO();
        LifesrvEmpProfitSummaryVO summary = Objects.isNull(summaryVO) ? new LifesrvEmpProfitSummaryVO() : summaryVO;
        BeanUtils.copyProperties(outer, outerVO);
        outerVO.setSyAllBaseCommissionAmt(formatBigAmount(summary.getSyAllBaseCommissionAmt(),2));
        outerVO.setSmAllBaseCommissionAmt(formatBigAmount(summary.getSmAllBaseCommissionAmt(),2));
        outerVO.setSyAllRevenueAmt(formatBigAmount(summary.getSyAllRevenueAmt(),2));
        outerVO.setSmAllRevenueAmt(formatBigAmount(summary.getSmAllRevenueAmt(),2));
        return outerVO;
    }

    private String formatBigAmount(BigDecimal amount, int newScale) {
        BigDecimal formatAmount = Objects.isNull(amount)
                ?BigDecimal.ZERO.setScale(newScale, RoundingMode.HALF_UP)
                :amount.setScale(newScale, RoundingMode.HALF_UP);

        //大于10000时，转成万元
        String unit = "";
        if (formatAmount.compareTo(BigDecimal.valueOf(10000))>=0) {
            formatAmount = formatAmount.divide(BigDecimal.valueOf(10000), newScale, RoundingMode.HALF_UP);
            unit = "万";
        }
        // 创建一个 DecimalFormat 实例
        DecimalFormat decimalFormat = new DecimalFormat("#,###.00");

        // 格式化 BigDecimal
        return formatAmount.compareTo(BigDecimal.valueOf(1))>=0 || formatAmount.compareTo(BigDecimal.valueOf(-1))<=0
                ?decimalFormat.format(formatAmount)+unit
                :formatAmount.toString()+unit;
    }
}
