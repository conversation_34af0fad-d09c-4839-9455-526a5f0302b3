package com.cfpamf.ms.mallorder.scheduler;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cfpamf.ms.mallgoods.facade.api.ProductFeignClient;
import com.cfpamf.ms.mallgoods.facade.dto.AgricFeeCalculateDTO;
import com.cfpamf.ms.mallgoods.facade.request.ProductAreaDTO;
import com.cfpamf.ms.mallgoods.facade.vo.AgricFeeCalculateVO;
import com.cfpamf.ms.mallorder.dto.OrderOfflineAgriFeeDTO;
import com.cfpamf.ms.mallorder.mapper.OrderOfflineMapper;
import com.cfpamf.ms.mallorder.po.OrderOfflineExtendPO;
import com.cfpamf.ms.mallorder.po.OrderProductPO;
import com.cfpamf.ms.mallorder.service.IOrderOfflineExtendService;
import com.cfpamf.ms.mallorder.service.IOrderProductService;
import com.cfpamf.ms.mallorder.service.impl.OrderOfflineExtendServiceImpl;
import com.slodon.bbc.core.response.JsonResult;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class OrderOfflineAgriServiceFeesJob {

    @Autowired
    private ProductFeignClient productFeignClient;

    @Autowired
    private IOrderOfflineExtendService orderOfflineExtendService;

    @Autowired
    private OrderOfflineMapper orderOfflineMapper;

    @Autowired
    private IOrderProductService orderProductService;

    @XxlJob("OrderOfflineAgriServiceFeesJob")
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobHelper.log("OrderOfflineAgriServiceFeesJob===============start, DATE :{}", LocalDateTime.now());
        List<AgricFeeCalculateDTO> agricFeeCalculateDTOList = new ArrayList<>();
        //一次最多100 条
        List<OrderOfflineAgriFeeDTO> resultList = orderOfflineMapper.getOrderOfflineForAgriFee();

        if (CollectionUtils.isEmpty(resultList)) {
            return ReturnT.SUCCESS;
        }

        log.info("OrderOfflineAgriServiceFeesJob resultList.size = {}", resultList);
        for (OrderOfflineAgriFeeDTO orderOfflineAgriFeeDTO : resultList) {
            AgricFeeCalculateDTO agricFeeCalculateDTO = new AgricFeeCalculateDTO();
            agricFeeCalculateDTO.setBizNo(orderOfflineAgriFeeDTO.getOrderProductId());
            agricFeeCalculateDTO.setAreaCode(orderOfflineAgriFeeDTO.getAreaCode());
            agricFeeCalculateDTO.setFinanceRuleCode(orderOfflineAgriFeeDTO.getFinanceRuleCode());
            agricFeeCalculateDTO.setProductId(orderOfflineAgriFeeDTO.getProductId());
            agricFeeCalculateDTO.setTaxPrice(orderOfflineAgriFeeDTO.getTaxPrice());
            agricFeeCalculateDTO.setProductNum(orderOfflineAgriFeeDTO.getProductNum());
            agricFeeCalculateDTOList.add(agricFeeCalculateDTO);
        }

        JsonResult<List<AgricFeeCalculateVO>> result = productFeignClient.listProductAgricFee(agricFeeCalculateDTOList);
        if (result != null && !CollectionUtils.isEmpty(result.getData())) {
            for (AgricFeeCalculateVO agricFeeCalculateVO : result.getData()) {
                if (Objects.isNull(agricFeeCalculateVO) || Objects.isNull(agricFeeCalculateVO.getAgricFee())) {
                    log.info("agricFeeCalculateVO = {}", agricFeeCalculateVO);
                    continue;
                }
                try {
                    LambdaUpdateWrapper<OrderProductPO> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.set(OrderProductPO::getAgriServiceFees, agricFeeCalculateVO.getAgricFee())
                            .eq(OrderProductPO::getOrderProductId, Long.valueOf(agricFeeCalculateVO.getBizNo()))
                            .eq(OrderProductPO::getOrderSn, agricFeeCalculateVO.getBizNo());
                    orderProductService.update(updateWrapper);
                } catch (Exception e) {
                    log.warn("OrderOfflineAgriServiceFeesJob update error", agricFeeCalculateVO);
                }
            }

        }
        log.info("OrderOfflineAgriServiceFeesJob end");
        XxlJobHelper.log("OrderOfflineAgriServiceFeesJob===============end, DATE :{}", LocalDateTime.now());
        return ReturnT.SUCCESS;
    }

}
