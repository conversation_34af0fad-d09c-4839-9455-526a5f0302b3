package com.cfpamf.ms.mallorder.service.payment;

import com.cfpamf.ms.mall.liquidate.request.ExecuteReq;
import com.cfpamf.ms.mall.liquidate.request.RevokeReq;

public interface IRefundSettlementService {

    /**
     * 校验电子账户余额
     * 
     * @param executeReq
     */
    void verifyElectronicAccountBalance(RevokeReq executeReq);

    /**
     * 下账
     * 
     * @param req
     * @return
     */
    Boolean issuedBill(RevokeReq req);

    /**
     * 贷款下账
     * 
     * @param req
     * @return
     */
    Boolean loanIssuedBill(ExecuteReq req);

    /**
     * 校验贷款电子账户余额
     * 
     * @param executeReq
     */
    void verifyLoanElectronicAccountBalance(ExecuteReq executeReq);

}
