package com.cfpamf.ms.mallorder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.ms.mallorder.dto.OrderOfflineInfoDTO;
import com.cfpamf.ms.mallorder.dto.OrderOfflineParamDTO;
import com.cfpamf.ms.mallorder.po.OrderOfflineExtendPO;
import com.cfpamf.ms.mallorder.po.OrderPO;

import java.util.List;
import java.util.Map;

public interface IOrderOfflineExtendService extends IService<OrderOfflineExtendPO> {

    void saveOfflineOrder(List<OrderPO> orderPOList, OrderOfflineParamDTO offlineParamDTO, String operator);

    OrderOfflineExtendPO getOrderOfflineExtendByOrderSn(String orderSn);

    Map<String, OrderOfflineExtendPO> getOrderOfflineExtendMapByOrderSnList(List<String> bizSnList);
}
