package com.cfpamf.ms.mallorder.service;

import com.cfpamf.ms.mallorder.dto.ProductCostDTO;
import com.cfpamf.ms.mallorder.po.OrderDeliveryRecordPO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> 2023/4/13.
 */
public interface IHistoryDataDealService {

    int returnServiceFeeFix(String afsSn, BigDecimal serviceFee);

    /**
     * 刷成本价为null的数据
     * @param orderSn
     * @return
     */
    String orderProductCostFix(String orderSn);

    Boolean cancelPreSellBalance(String orderSn);

    Boolean orderProductRateFix();

    Boolean orderSelfLiftLogisticFix();
    
    Boolean historyOrderLogisticFix();

    String orderProductCostFixImport(List<ProductCostDTO> costList);

    Boolean fixManager(String orderSn, String userNo, String branchCode, String branchName, String areaCode, String areaName);

    Boolean fixOrgInfo(String areaCode);

    Boolean fixOrgInfoInManager(String areaCode);
    public Boolean fixOrgInfoInOrderSn(String orderSn);

    Boolean fixManagerByStoreId(String storeId);

    Boolean offlineOrder(String orderSns);

    Boolean offlineOrderV2(String orderSns);

    Boolean offlineOrderFix(String orderSns);

    Boolean updatePerformanceChannel();

    List<OrderDeliveryRecordPO> getPackageUniqueCode(List<String> bizUniqueNoList);

    void initLogisticItem();

    Boolean orderUserIdentity();

    void offlineExtendDeal();

    Boolean updateProductDeliveryState(String orderProductId, int deliveryState);

    Boolean offlineOrderLogisticsItem(String orderSns);

    Boolean jdIntercept(String param);
}
