package com.cfpamf.ms.mallorder.integration.facade;

import com.cfpamf.ares.aftersale.domain.dto.AfterSaleAuditDTO;
import com.cfpamf.ares.aftersale.domain.dto.AfterSaleCreateDTO;
import com.cfpamf.ares.aftersale.domain.dto.AfterSaleRefundNotifyDTO;
import com.cfpamf.ares.aftersale.domain.vo.AfterSaleAuditVO;
import com.cfpamf.ares.aftersale.domain.vo.AfterSaleCreateVO;
import com.cfpamf.athena.common.domain.dto.ResultEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;


@FeignClient(
        name = "ares-after-sale",
        url = "${ares-after-sale.url}"
)
public interface AresAfterSaleFacade {
    @PostMapping({"/aftersale/create"})
    ResultEntity<AfterSaleCreateVO> create(@RequestBody @Valid AfterSaleCreateDTO createDTO);

    @PostMapping({"/aftersale/refundNotify"})
    ResultEntity<Boolean> refundNotify(@RequestBody @Valid AfterSaleRefundNotifyDTO refundNotifyDTO);

    @PostMapping({"/aftersale/audit"})
    ResultEntity<AfterSaleAuditVO> audit(@RequestBody @Valid AfterSaleAuditDTO auditDTO);
}

