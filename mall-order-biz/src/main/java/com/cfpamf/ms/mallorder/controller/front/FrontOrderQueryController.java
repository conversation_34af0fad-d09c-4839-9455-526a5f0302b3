package com.cfpamf.ms.mallorder.controller.front;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.mallorder.common.help.ResultUtils;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.dto.FinanceRuleRequest;
import com.cfpamf.ms.mallorder.po.OrderExtendPO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderPayPO;
import com.cfpamf.ms.mallorder.po.OrderProductPO;
import com.cfpamf.ms.mallorder.service.IOrderExtendService;
import com.cfpamf.ms.mallorder.service.IOrderPayService;
import com.cfpamf.ms.mallorder.service.IOrderProductService;
import com.cfpamf.ms.mallorder.service.IOrderService;
import com.cfpamf.ms.mallorder.vo.*;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 订单查询 controller
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/25 11:02
 */
@RestController
@RequestMapping("front/orderQuery")
@Api(tags = "front-订单查询")
@Slf4j
public class FrontOrderQueryController {

    @Autowired
    private IOrderPayService orderPayService;

    @Autowired
    private IOrderService orderService;

    @Autowired
    private IOrderExtendService orderExtendService;

    @Autowired
    private IOrderProductService orderProductService;

    @ApiOperation("订单信息同步接口")
    @PostMapping("trans/detail")
    public Result<OrderTransVO> getOrderDetail(
            @NotBlank(message = "订单号orderSn 不能为空") @RequestParam("orderSn") String orderSn) {

        LambdaQueryWrapper<OrderPO> orderQuery = new LambdaQueryWrapper();
        orderQuery.eq(OrderPO::getOrderSn, orderSn);
        OrderPO orderPO = orderService.getOne(orderQuery);
        BizAssertUtil.isTrue(orderPO == null, "订单不存在：" + orderSn);

        LambdaQueryWrapper<OrderExtendPO> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(OrderExtendPO::getOrderSn, orderSn);
        OrderExtendPO orderExtendPO = orderExtendService.getOne(queryWrapper);
        BizAssertUtil.isTrue(orderExtendPO == null, "订单扩展信息不存在：" + orderPO.getPaySn());

        LambdaQueryWrapper<OrderPayPO> orderPayQuery = new LambdaQueryWrapper();
        orderPayQuery.eq(OrderPayPO::getPaySn, orderPO.getPaySn());
        OrderPayPO orderPayPO = orderPayService.getOne(orderPayQuery);
        BizAssertUtil.isTrue(orderPayPO == null, "支付单不存在：" + orderPO.getPaySn());

        LambdaQueryWrapper<OrderProductPO> productQuery = new LambdaQueryWrapper();
        productQuery.eq(OrderProductPO::getOrderSn, orderSn);
        List<OrderProductPO> productList = orderProductService.list(productQuery);
        BizAssertUtil.isTrue(productList == null, "订单商品不存在：" + orderSn);

        OrderTransVO vo = new OrderTransVO();
        BeanUtils.copyProperties(orderPO, vo);

        OrderExtendTransVO extendTransVO = new OrderExtendTransVO();
        BeanUtils.copyProperties(orderExtendPO, extendTransVO);
        vo.setReceiveMaterialStatus(orderExtendPO.getReceiveMaterialStatus());

        OrderPayTransVO payTransVO = new OrderPayTransVO();
        BeanUtils.copyProperties(orderPayPO, payTransVO);

        List<OrderProductTransVO> productVOS = new ArrayList<>(productList.size());
        for (OrderProductPO orderProductPO : productList) {
            OrderProductTransVO productTransVO = new OrderProductTransVO();
            BeanUtils.copyProperties(orderProductPO, productTransVO);
            productVOS.add(productTransVO);
        }

        vo.setExtendTransVO(extendTransVO);
        vo.setOrderPayVO(payTransVO);
        vo.setOrderProducts(productVOS);

        return ResultUtils.buildSuccessResult(vo);
    }


    @ApiOperation("批量查询金融规则信息")
    @PostMapping("financeRule/batchQuery")
    public JsonResult<List<FinanceRuleVO>> batchQuery(@RequestBody FinanceRuleRequest financeRuleRequest) {
        List<OrderPO> orderPOList = orderService.lambdaQuery()
                .in(OrderPO::getOrderSn, financeRuleRequest.getOrderSnList())
                .select(OrderPO::getFinanceRuleCode, OrderPO::getRuleTag, OrderPO::getOrderSn)
                .list();
        List<FinanceRuleVO> financeRuleVOS = orderPOList.stream().map(x -> {
            FinanceRuleVO financeRuleVO = new FinanceRuleVO();
            financeRuleVO.setFinanceRuleCode(x.getFinanceRuleCode());
            financeRuleVO.setRuleTag(x.getRuleTag());
            financeRuleVO.setOrderSn(x.getOrderSn());
            return financeRuleVO;
        }).collect(Collectors.toList());
        return SldResponse.success(financeRuleVOS);
    }
}
