package com.cfpamf.ms.mallorder.integration.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class JingDongInterceptDTO {

    @ApiModelProperty(value = "订单号")
    @NotBlank(message = "订单号不能为空")
    private String deliverBusinessNo;

    @ApiModelProperty(value = "售后单号")
    @NotBlank(message = "售后单号不能为空")
    private String interceptBusinessNo;

    @ApiModelProperty("操作人")
    private String operate;

    @ApiModelProperty(value = "系统来源")
    private String systemSource;

    @ApiModelProperty(value = "明细集合")
    @NotNull(message = "明细不能为空")
    @Valid
    private List<JingDongInterceptItemDTO> jingDongInterceptItemDTOList;
}

