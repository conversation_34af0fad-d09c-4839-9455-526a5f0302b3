package com.cfpamf.ms.mallorder.integration.cashier.request;

import com.cfpamf.ms.mallorder.request.req.OrderDeviceInfo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;


/**
 * 业务系统订单信息推送
 * <AUTHOR>
 */
@Data
public class PushOrderRequest {

    @ApiModelProperty("业务订单号")
    private String orderNo;

    @ApiModelProperty("订单金额（元）")
    private BigDecimal orderAmt;

    @ApiModelProperty("订单创建时间")
    @JsonFormat(timezone = "GMT+8")
    private LocalDateTime orderCreateTime;

    @ApiModelProperty("订单失效时间")
    @JsonFormat(timezone = "GMT+8")
    private LocalDateTime orderPeriod;

    @ApiModelProperty("验签失效时间")
    @JsonFormat(timezone = "GMT+8")
    private Integer expirationTime;

    @ApiModelProperty("业务系统编码")
    private String busiSystemCode;

    @ApiModelProperty("订单描述")
    private String description;

    @ApiModelProperty("支付金额（元）")
    private BigDecimal payAmt;

    @ApiModelProperty("订单来源渠道（app支付,h5支付,公众号支付,小程序支）")
    private String busiChannel;

    @ApiModelProperty(value = "支付人信息")
    private OrderPlayer orderPlayerList;

    @ApiModelProperty("同步返回地址")
    private String returnUrl;

    @ApiModelProperty("异步回调地址")
    private String notifyUrl;

    @ApiModelProperty(value = "放款通知url")
    private String loanNotifyUrl;

    @ApiModelProperty("设备信息")
    private OrderDeviceInfo orderDeviceInfo;

    @ApiModelProperty(value = "引荐商户ID")
    private Long recommendStoreId;

    @ApiModelProperty("子订单信息")
    private List<OrderSub> orderSubList;

    @ApiModelProperty("主单拓展信息")
    private List<MainOrderExt> mianExtList;


    /**
     * 针对一比订单需要分批支付情况
     * 例如预付订单场景
     * 不存在的情况下不需要传
     */
    @ApiModelProperty("支付阶段单信息")
    private List<OrderPhase> orderPhaseList;
}
