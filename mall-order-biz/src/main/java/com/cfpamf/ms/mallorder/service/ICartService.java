package com.cfpamf.ms.mallorder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.dto.OrderAddressDTO;
import com.cfpamf.ms.mallorder.dto.OrderSkuInfoDTO;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.po.CartPO;
import com.cfpamf.ms.mallorder.vo.CartListVO;

import java.util.List;

/**
 * <p>
 * 购物车 service
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-22
 */
public interface ICartService extends IService<CartPO> {

    /**
     * 添加购物车
     *
     * @param member          用户
     * @param productId       商品sku
     * @param areaCode        售卖区域
     * @param number          加购数量
     * @param financeRuleCode 金融规则编码
     * @return
     */
    Boolean addCart(Member member, Long productId, String areaCode, Integer number, String financeRuleCode);

    /**
     * 购物车列表
     * @param memberId 用户
     * @return
     */
    CartListVO getCartList(Integer memberId, Integer productType);

    /**
     * 购物车商品数量
     *
     * @param memberId
     * @return
     */
    Integer countInCart(Integer memberId, Integer productType);

    Boolean changeNum(Member member, Integer cartId, Integer number);

    List<CartPO> buildCartList(List<OrderSkuInfoDTO> skuInfoList, OrderTypeEnum orderType, Integer memberId, String areaCode, String financeRuleCode, String channel, OrderAddressDTO addressDTO);

    List<CartPO> buildCartListV2(List<OrderSkuInfoDTO> skuInfoList, Integer memberId, OrderTypeEnum orderType,
                                 String channel, OrderAddressDTO addressDTO, Boolean isCart);

    CartPO getCartByProduct(Long productId, Integer memberId);

    /**
     * 批量更新购物车商品名称
     *
     * @param cartIds   购物车id，用【,】分割
     * @param goodsName 商品名称
     * @return 修改结果 true/false
     */
    Boolean updateCartsGoodsName(String cartIds, String goodsName);
}
