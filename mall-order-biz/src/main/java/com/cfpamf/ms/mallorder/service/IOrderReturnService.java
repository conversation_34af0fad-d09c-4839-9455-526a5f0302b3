package com.cfpamf.ms.mallorder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.mallpayment.facade.vo.PaymentRefundNotifyVO;
import com.cfpamf.ms.mallorder.common.enums.OrderRefundRevokingPartyEnum;
import com.cfpamf.ms.mallorder.dto.OperatorDTO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderReturn;
import com.cfpamf.ms.mallorder.po.OrderReturnPO;
import com.cfpamf.ms.mallorder.req.BappOrderReturnRequest;
import com.cfpamf.ms.mallorder.req.base.RevokeRefundBaseRequest;
import com.cfpamf.ms.mallorder.req.exportreq.OrderRefundExportRequest;
import com.cfpamf.ms.mallorder.v2.domain.vo.RefundInfoExtraInfoVO;
import com.cfpamf.ms.mallorder.vo.OrderRefundMoneyDetail;
import com.cfpamf.ms.mallorder.vo.OrderReturnInfoForDbcVO;
import com.cfpamf.ms.mallorder.vo.OrderReturnInfoVO;
import com.cfpamf.ms.mallorder.vo.OrderReturnVOV2;
import com.cfpamf.ms.mallorder.vo.exportvo.OrderRefundExportVO;
import com.cfpamf.ms.mallorder.vo.refund.RestoreLimitRefundVO;
import com.slodon.bbc.core.response.PageVO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 订单正向操作 service 接口
 *
 * <AUTHOR>
 * @date 2021/5/30 17:00
 * @return
 */
public interface IOrderReturnService extends IService<OrderReturnPO> {

    static OrderReturn entity2OrderReturn(OrderReturnPO entity) {
        OrderReturn orderReturn = new OrderReturn();
        orderReturn.setReturnId(entity.getReturnId());
        orderReturn.setAfsSn(entity.getAfsSn());
        orderReturn.setOrderSn(entity.getOrderSn());
        orderReturn.setStoreId(entity.getStoreId());
        orderReturn.setStoreIsSelf(entity.getStoreIsSelf());
        orderReturn.setStoreName(entity.getStoreName());
        orderReturn.setMemberId(entity.getMemberId());
        orderReturn.setMemberName(entity.getMemberName());
        orderReturn.setReturnMoneyType(entity.getReturnMoneyType());
        orderReturn.setReturnType(entity.getReturnType());
        orderReturn.setReturnNum(entity.getReturnNum());
        orderReturn.setReturnMoneyAmount(entity.getReturnMoneyAmount());
        orderReturn.setReturnIntegralAmount(entity.getReturnIntegralAmount());
        orderReturn.setDeductIntegralAmount(entity.getDeductIntegralAmount());
        orderReturn.setReturnExpressAmount(entity.getReturnExpressAmount());
        orderReturn.setReturnVoucherCode(entity.getReturnVoucherCode());
        orderReturn.setCommissionRate(entity.getCommissionRate());
        orderReturn.setCommissionAmount(entity.getCommissionAmount());
        orderReturn.setPlatformVoucherAmount(entity.getPlatformVoucherAmount());
        orderReturn.setPlatformActivityAmount(entity.getPlatformActivityAmount());
        orderReturn.setServiceFee(entity.getServiceFee());
        orderReturn.setThirdpartnarFee(entity.getThirdpartnarFee());        // 退还的乡助卡优惠金额
        orderReturn.setXzCardAmount(entity.getXzCardAmount());        // 退还的乡助卡运费优惠金额
        orderReturn.setXzCardExpressFeeAmount(entity.getXzCardExpressFeeAmount());
        orderReturn.setOrderCommission(entity.getOrderCommission());
        orderReturn.setBusinessCommission(entity.getBusinessCommission());
        orderReturn.setState(entity.getState());
        orderReturn.setRefundFailTimes(entity.getRefundFailTimes());
        orderReturn.setApplyTime(entity.getApplyTime());
        orderReturn.setCompleteTime(entity.getCompleteTime());
        orderReturn.setRefuseReason(entity.getRefuseReason());
        orderReturn.setPaymentMethod(entity.getPaymentMethod());
        orderReturn.setChannelServiceFee(entity.getChannelServiceFee());        //  #######新增########
        orderReturn.setActualReturnMoneyAmount(entity.getActualReturnMoneyAmount());
        orderReturn.setCustomerAssumeAmount(entity.getCustomerAssumeAmount());
        orderReturn.setRefundType(entity.getRefundType());
        orderReturn.setRefundStartTime(entity.getRefundStartTime());
        orderReturn.setRefundEndTime(entity.getRefundEndTime());
        orderReturn.setOtherCompensationAmount(entity.getOtherCompensationAmount());
        orderReturn.setPlanDiscountAmount(entity.getPlanDiscountAmount());
        orderReturn.setInterestPayer(entity.getInterestPayer());
        orderReturn.setPlatformAutoAuditFailTimes(entity.getPlatformAutoAuditFailTimes());
        orderReturn.setRemark(entity.getRemark());
        orderReturn.setReversalDate(entity.getReversalDate());
        orderReturn.setCreateTime(entity.getCreateTime());
        orderReturn.setUpdateTime(entity.getUpdateTime());
        orderReturn.setCreateBy(entity.getCreateBy());
        orderReturn.setUpdateBy(entity.getUpdateBy());
        orderReturn.setReturnBy(entity.getReturnBy());
        orderReturn.setBusinessType(entity.getBusinessType());
        orderReturn.setBusinessDescription(entity.getBusinessDescription());
        orderReturn.setEnabledFlag(entity.getEnabledFlag());
        return orderReturn;
    }

    default OrderReturn entityToOrderReturn(OrderReturnPO entity) {
        return entity2OrderReturn(entity);
    }

    BigDecimal sumReturnedExpressFee(String orderSn);

    OrderReturnPO getByAfsSn(String afsSn);

    /**
     * 判断是否有退款中的退订单
     *
     * @param orderSn
     * @return boolean
     * <AUTHOR>
     * @date 2021/08/04 17:01
     */
    boolean hasDuringRefund(String orderSn);

    /**
     * 判断是否有退款中的退订单(不包含换货退款单)
     */
    boolean hasDuringRefundNoExchange(String orderSn);

    /**
     * 判断订单是否全部退款完成
     *
     * @param afsSn
     * @return boolean
     * <AUTHOR>
     * @date 2021/08/04 17:01
     */
    boolean isAllReturnedFinish(String afsSn);

    /**
     * 判断该订单商品行是否全部退完
     *
     * @param orderProductId    订单商品id
     * @return                  true/false
     */
    boolean isOrderProductAllReturn(Long orderProductId);

    /**
     * 判断当前退订单是否未最后一笔
     *
     * @param afsSn
     * @return boolean
     * <AUTHOR>
     * @date 2021/08/04 17:01
     */
    boolean isLastReturn(String afsSn);


    /**
     * 查询分销所需退款信息
     *
     * @param orderSn 订单号
     * @param afsSns  退款单集合
     * @return 退款信息
     */
    OrderReturnInfoForDbcVO orderAfterSaleDetailInfoFordbc(String orderSn, List<String> afsSns);

    /**
     * 查询分销所需退款信息
     *
     * @param orderSn 订单号
     * @return 退款信息
     */
    OrderReturnInfoForDbcVO orderAfterSaleDetailInfoForDbcWithOrderSn(String orderSn);
    /**
     * 取消订单
     *
     * @param orderPOList  订单
     * @param cancelReason 取消理由
     * @param cancelRemark 取消备注
     * @param optRole      取消者角色
     * @param optUserId    取消者ID
     * @param optUserName  取消者姓名
     * @param optRemark    取消者备注
     * @param returnBy     取消途径
     * @param channel      渠道
     */
    void cancelOrder(List<OrderPO> orderPOList, String cancelReason, String cancelRemark, Integer optRole,
                     Long optUserId, String optUserName, String optRemark, Integer returnBy, String channel);

    /**
     * admin 端退款导出
     *
     * @param exportRequest 请求参数
     * @return 导出信息
     */
    List<OrderRefundExportVO> adminOrderRefundExport(OrderRefundExportRequest exportRequest);

    /**
     * seller 端退款导出
     *
     * @param exportRequest 请求参数
     * @return 导出信息
     */
    List<OrderRefundExportVO> sellerOrderRefundExport(OrderRefundExportRequest exportRequest);

    /**
     * 退款状态回调通知
     *
     * @param refundVO
     */
    void dealRefundByPaymethod(RefundInfoExtraInfoVO refundInfoExtraInfoVO, PaymentRefundNotifyVO refundVO);

    void orderCancelWithoutRefund(OrderPO orderDb, String cancelReason, String cancelRemark, Long optUserId,
                                  String optUserName);

    /**
     * 获取平台超时未审核的数量
     *
     * @param overtime 设置的超时时间
     * @return 超时未审核数量
     */
    int getAfsCountInPlatformAuditOvertime(Integer overtime);

    /**
     * 获取店铺超时未审核的数量
     *
     * @param storeId          商户ID
     * @param recommendStoreId
     * @return 超时未审核数量
     */
    int getAfsCountInStoreAuditOvertime(Long storeId, Integer recommendStoreId);

    /**
     * 恢复额度退款查询该批次所有未关闭退款单号
     *
     * @param orderSn 订单号
     * @return 退款单号集
     */
    List<String> getAllRestoreLimitOrders(String orderSn);

    /**
     * 恢复额度退款查询该批次所有未关闭退款单信息
     */
    List<RestoreLimitRefundVO> getAllRestoreLimitRefundOrderInfos(String orderSn);

    /**
     * 查询退款信息
     *
     * @param afsSn 退款单号
     * @return 退款信息
     */
    OrderReturnInfoVO getOrderReturnDetailInfo(String afsSn);

    /**
     * 取消拼团订单
     *
     * @param orderSns 订单号
     */
    void cancelSpellOrders(List<String> orderSns);

    /**
     * 查询待平台审批退款单
     *
     * @param orderSn
     * @return
     */
    List<String> getApprovedMerchantOrderReturnList(String orderSn);

    /**
     * 退款撤销发起
     *
     * @param revokeRequest 撤销退款请求类
     * @param revokingParty 发起方
     * @param operator      操作人信息
     */
    void revokeRefund(RevokeRefundBaseRequest revokeRequest, OrderRefundRevokingPartyEnum revokingParty, OperatorDTO operator);

    /**
     * 获取云中鹤退款结果
     *
     * @param orderSn
     * @param afsSn
     * @param productId
     * @return boolean
     */
    Integer getYzhIsRefunded(String orderSn, String afsSn, String productId);

    /**
     * Bapp客户经理获取管护关系用户售后列表(不包含换货订单)
     * @param request
     * @return
     */
    PageVO<OrderReturnVOV2> getBappOrderReturnList(BappOrderReturnRequest request);

    /**
     * 重置退款扣罚金额处理
     *
     * @param orderReturnPo      退款单
     * @param refundPunishAmount 重置金额
     * @return 处理结果 true/false
     */
    boolean resetRefundPunishAmount(OrderReturnPO orderReturnPo, BigDecimal refundPunishAmount);

    /**
     * 查询退款单退款金额详情
     *
     * @param afsSn 退款单号
     * @return 退款单退款金额详情
     */
    OrderRefundMoneyDetail refundMoneyDetail(String afsSn);

    List<OrderReturnPO> getPlatformAutoAuditOrder();

    Map<String, BigDecimal> getSumAmountByProductId(Long orderProductId);

    List<OrderReturnVOV2> listOrderReturnByProductId( Long orderProductId);

    /**
     * 根据订单编号查询售后单信息
     *
     * @param orderSn 订单编号
     * @return 售后单信息
     */
    List<OrderReturnPO> getByOrderSn(String orderSn);

    /**
     * 是否有在途的换货单
     *
     * @param orderSn 订单编号
     * @return 是否有在途换货单
     */
    boolean hasDuringExchangeRefund(String orderSn);
}

