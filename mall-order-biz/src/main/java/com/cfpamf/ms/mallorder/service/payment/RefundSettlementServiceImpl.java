package com.cfpamf.ms.mallorder.service.payment;

import com.cfpamf.ms.mall.liquidate.api.BizActionFacade;
import com.cfpamf.ms.mall.liquidate.request.ExecuteReq;
import com.cfpamf.ms.mall.liquidate.request.RevokeReq;
import com.netflix.hystrix.exception.HystrixRuntimeException;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.exception.MallException;
import com.slodon.bbc.core.response.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.net.SocketTimeoutException;

@Slf4j
@Service
public class RefundSettlementServiceImpl implements IRefundSettlementService {

    @Value("${spring.application.name}")
    private String appName;
    @Autowired
    private BizActionFacade bizActionFacade;

    /**
     * 校验电子帐号余额
     * @param executeReq
     */
    @Override
    public void verifyElectronicAccountBalance(RevokeReq executeReq) {
        JsonResult<Void> jsonResult = null;
        try {
            executeReq.setCreateBy(appName);
            jsonResult = bizActionFacade.balanceSurplus(executeReq);
        } catch (Exception e) {
            log.error("liquidate 调用结算校验用户资金余额异常：{}：", e.getMessage());
            throw e;
        }
        if (jsonResult == null) {
            log.warn("调用liquidate 调用结算校验用户资金余额为空，退款单号：{}", executeReq.getBizNo());
            throw new MallException("调用liquidate 调用结算校验用户资金余额未返回任何信息:" + executeReq.getBizNo());
        }
        if (200 != jsonResult.getState()) {
            throw new BusinessException(jsonResult.getMsg());
        }
    }

    /**
     * 下账
     * 
     * @param req
     * @return
     */
    @Override
    public Boolean issuedBill(RevokeReq req) {
        try {
            req.setCreateBy(appName);
            JsonResult<Void> result = bizActionFacade.asyncRevoke(req);
            if (null == result) {
                log.warn("调用下账接口失败,bizSn: {}", req.getBizNo());
                throw new MallException("调用下账接口未返回任何信息,bizSn:" + req.getBizNo());
            }
            if (200 != result.getState()) {
                throw new MallException(result.getMsg());
            }
            return Boolean.TRUE;
        } catch (Exception e) {
            if (e.getCause() instanceof SocketTimeoutException) {
                log.error("调用下账接口超时，请排查BizNo:{}", req.getBizNo());
                return Boolean.TRUE;
            } else {
                throw e;
            }
        }
    }

    /**
     * 贷款下账
     * 
     * @param req
     * @return
     */
    @Override
    public Boolean loanIssuedBill(ExecuteReq req) {
        JsonResult<Void> result = null;
        try {
            req.setCreateBy(appName);
            result = bizActionFacade.asyncExecute(req);
            if (null == result) {
                log.warn("liquidate 调用贷款类赔偿退款接口失败,bizSn: {}", req.getBizNo());
                throw new MallException("调用贷款类赔偿退款接口未返回任何信息,bizSn:" + req.getBizNo());
            }
            if (200 != result.getState()) {
                throw new MallException(result.getMsg());
            }
        } catch (Exception e) {
            if (e.getCause() instanceof SocketTimeoutException || e.getCause() instanceof HystrixRuntimeException) {
                log.error("调用贷款类赔偿退款接口超时，请排查:{}", req.getBizNo());
            } else {
                throw e;
            }
        }
        return Boolean.TRUE;
    }

    /**
     * 校验贷款电子账户余额
     * 
     * @param executeReq
     */
    @Override
    public void verifyLoanElectronicAccountBalance(ExecuteReq executeReq) {
        JsonResult<Void> jsonResult = null;
        try {
            executeReq.setCreateBy(appName);
            jsonResult = bizActionFacade.balanceSurplus(executeReq);
        } catch (Exception e) {
            log.error("liquidate 调用结算校验用户资金余额异常：{}：", e.getMessage());
            throw e;
        }
        if (jsonResult == null) {
            log.warn("调用liquidate 调用结算校验用户资金余额为空，退款单号：{}", executeReq.getBizNo());
            throw new MallException("调用liquidate 调用结算校验用户资金余额未返回任何信息:{}" + executeReq.getBizNo());
        }
        if (200 != jsonResult.getState()) {
            throw new BusinessException(jsonResult.getMsg());
        }
    }

}
