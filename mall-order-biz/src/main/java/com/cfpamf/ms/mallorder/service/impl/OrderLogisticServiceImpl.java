package com.cfpamf.ms.mallorder.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cfpamf.framework.autoconfigure.mybatis.BaseRepoServiceImpl;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.enums.DeliverPackageStateEnum;
import com.cfpamf.ms.mallorder.common.enums.OrderSeqEnum;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.common.util.ExpressDeliveryUtil;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.ExpressDeliveryDTO;
import com.cfpamf.ms.mallorder.dto.ExpressNumberDTO;
import com.cfpamf.ms.mallorder.dto.KdnExpressDTO;
import com.cfpamf.ms.mallorder.dto.OrderPerformanceDeliveryDTO;
import com.cfpamf.ms.mallorder.mapper.OrderLogisticMapper;
import com.cfpamf.ms.mallorder.po.OrderLogisticPO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.req.OrderDeliveryReq;
import com.cfpamf.ms.mallorder.service.IOrderLogisticService;
import com.cfpamf.ms.mallorder.vo.OrderLogisticItemVO;
import com.cfpamf.ms.mallorder.vo.OrderLogisticVO;
import com.cfpamf.ms.mallshop.resp.Vendor;
import com.cfpamf.ms.mallsystem.api.ExpressFeignClient;
import com.cfpamf.ms.mallsystem.request.ExpressExample;
import com.cfpamf.ms.mallsystem.vo.Express;
import com.cfpamf.smartid.client.utils.sharding.ShardingId;
import com.slodon.bbc.core.exception.MallException;
import com.slodon.bbc.core.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 订单发货记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-18
 */
@Service
@Slf4j
public class OrderLogisticServiceImpl extends BaseRepoServiceImpl<OrderLogisticMapper, OrderLogisticPO> implements IOrderLogisticService {

	@Resource
	private ExpressFeignClient expressFeignClient;

	@Resource
	private ExpressDeliveryUtil expressDeliveryUtil;

	@Resource
	private OrderLogisticMapper orderLogisticMapper;

	@Resource
	private ShardingId shardingId;

	@Override
	public OrderLogisticPO saveLogistic(OrderPO orderPO,OrderDeliveryReq deliveryReq, String operator,Integer productDeliveryState) {
		long packageSnLong = shardingId.next(OrderSeqEnum.PGNO.getName(), OrderSeqEnum.PGNO.prefix(), orderPO.getMemberId().toString(), new Date());


		OrderLogisticPO logisticPO = new OrderLogisticPO(deliveryReq);
		logisticPO.setCreateBy(operator);
		logisticPO.setUpdateBy(operator);
		logisticPO.setPackageSn(String.valueOf(packageSnLong));
		logisticPO.setDeliverPackageState(productDeliveryState);

		if (deliveryReq.getDeliverType().equals(OrderConst.DELIVER_TYPE_0)) {
			// 查询快递公司信息
			Express express;
			if (deliveryReq.getExpressId() != null && deliveryReq.getExpressId() != 0) {
				express = expressFeignClient.getExpressByExpressId(deliveryReq.getExpressId());
				AssertUtil.notNull(express, "获取快递公司信息为空，请重试");
			} else {
				ExpressExample expressExample = new ExpressExample();
				expressExample.setExpressCode(deliveryReq.getExpressCompanyCode());
				List<Express> expressList = expressFeignClient.getExpressList(expressExample);
				AssertUtil.notEmpty(expressList, "获取快递公司信息为空，请重试");
				express = expressList.get(0);
			}
			logisticPO.setExpressId(express.getExpressId());
			logisticPO.setExpressName(express.getExpressName());
			logisticPO.setExpressCompanyCode(express.getExpressCode());
		}

		boolean save = this.save(logisticPO);
		if (!save){
			throw new MallException("保存订单物流失败");
		}

		return logisticPO;
	}

	public void updatePackageState(String packageSn,int state,String failReason) {
		LambdaUpdateWrapper<OrderLogisticPO> logisticPOLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
		logisticPOLambdaUpdateWrapper.eq(OrderLogisticPO::getPackageSn, packageSn)
				.eq(OrderLogisticPO::getDeliverPackageState, DeliverPackageStateEnum.OUTBOUND.getValue())
				.set(OrderLogisticPO::getDeliverPackageState, state)
				.set(OrderLogisticPO::getDeliverPackageFailReason,failReason);
				this.update(logisticPOLambdaUpdateWrapper);
	}

	/**
	 * 保存供应商发货信息
	 * @param orderPerformanceDeliveryDTO
	 * @return boolean
	 * */
	@Override
	public OrderLogisticPO savePerformanceLogistic(OrderPerformanceDeliveryDTO orderPerformanceDeliveryDTO) {
		OrderLogisticPO logisticPO = new OrderLogisticPO(orderPerformanceDeliveryDTO);
		//物流发货
		logisticPO.setDeliverType(OrderConst.DELIVER_TYPE_0);
		// 查询快递公司信息
		Express express = new Express();

		ExpressExample expressExample = new ExpressExample();
		expressExample.setExpressCode(orderPerformanceDeliveryDTO.getExpressCompanyCode());
		List<Express> expressList = expressFeignClient.getExpressList(expressExample);
		if(CollectionUtils.isEmpty(expressList)){
			express.setExpressCode(orderPerformanceDeliveryDTO.getExpressCompanyCode());
			express.setExpressName(orderPerformanceDeliveryDTO.getExpressCompanyName());
		}else {
			express = expressList.get(0);
		}
		logisticPO.setExpressId(express.getExpressId());
		logisticPO.setExpressName(express.getExpressName());
		logisticPO.setExpressCompanyCode(express.getExpressCode());
		this.save(logisticPO);
		return logisticPO;
	}



	/**
	 * 根据单号查询物流公司
	 * @param expressNumber 快递单号
	 * @return  ExpressDeliveryDTO
	 * */
	@Override
	public ExpressDeliveryDTO getExpressDelivery(String expressNumber) {
		ExpressDeliveryDTO expressDeliveryDTO = new ExpressDeliveryDTO();
		expressDeliveryDTO.setExpressNumber(expressNumber);
		String requestData = "{'LogisticCode':'" + expressNumber + "'}";
		String jsonResult = "";
		try {
			jsonResult = expressDeliveryUtil.queryKdniaoInterface(requestData,CommonConst.KDNIAO_REQUEST_TYPE_2002);
		}catch (Exception e) {
			log.error("根据快递单号调用快递鸟查询快递公司接口异常", e);
		}
		if(StringUtils.isEmpty(jsonResult)){
			return expressDeliveryDTO;
		}
		KdnExpressDTO kdnExpressDTO = JSONObject.parseObject(jsonResult, KdnExpressDTO.class);
		if(ObjectUtil.isNotNull(kdnExpressDTO)){
			kdnExpressDTO.setBusinessId(kdnExpressDTO.getBusinessId());
			List<KdnExpressDTO.KdnShipperDTO> kdnShipperDTOList = kdnExpressDTO.getKdnShipperDTOList();
			if(CollectionUtils.isEmpty(kdnShipperDTOList)){
				log.error("【标准电商】-【供应商履约】根据快递单号获取不到快递公司");
			}else if(kdnShipperDTOList.size() > 1){
				log.error("【标准电商】-【供应商履约】根据快递单号获取多个快递公司");
			}else {
				expressDeliveryDTO.setShipperName(kdnShipperDTOList.get(0).getShipperName());
				expressDeliveryDTO.setShipperCode(kdnShipperDTOList.get(0).getShipperCode());
			}
		}
		return expressDeliveryDTO;
	}


	@Override
	public ExpressDeliveryDTO getExpressByExpressNumber(String expressNumber,String orderSn, Vendor vendor){
		ExpressDeliveryDTO expressDeliveryDTO = new ExpressDeliveryDTO();
		expressDeliveryDTO.setExpressNumber(expressNumber);
		String requestData = "{'LogisticCode':'" + expressNumber + "'}";
		String result = "";
		try {
			result = expressDeliveryUtil.queryKdniaoInterface(requestData, CommonConst.KDNIAO_REQUEST_TYPE_2002);
		}catch (Exception e) {
			log.error("根据快递单号调用快递鸟查询快递公司接口异常", e);
		}


		if(StringUtils.isEmpty(result)){
			return expressDeliveryDTO;
		}
		JSONObject resultJson = JSONObject.parseObject(result);
		if(ObjectUtil.isNotNull(resultJson)){
			JSONArray shippers = resultJson.getJSONArray("Shippers");
			expressDeliveryDTO.setBusinessId(resultJson.getString("EBusinessID"));
			if(shippers == null || shippers.size() == 0 ){
				return expressDeliveryDTO;
			}
			JSONObject shipper = shippers.getJSONObject(0);
			expressDeliveryDTO.setShipperCode(shipper.getString("ShipperCode"));
			expressDeliveryDTO.setShipperName(shipper.getString("ShipperName"));
		}

		return expressDeliveryDTO;
	}

	@Override
	public List<ExpressDeliveryDTO> getExpressListByExpressNumber(List<ExpressNumberDTO> expressNumberList,Vendor vendor) {
		List<ExpressDeliveryDTO> list = new ArrayList<>();
		for(ExpressNumberDTO expressNumberDTO : expressNumberList){
			ExpressDeliveryDTO expressDeliveryDTO = getExpressByExpressNumber(expressNumberDTO.getExpressNumber(),expressNumberDTO.getOrderSn(), vendor);
            list.add(expressDeliveryDTO);
		}
		return list;
	}

	@Override
	public OrderLogisticVO getOrderLogisticByLogisticId(Long logisticId) {
		OrderLogisticVO orderLogisticVO = new OrderLogisticVO();
		OrderLogisticPO orderLogisticPO = this.getById(logisticId);
		BeanUtils.copyProperties(orderLogisticPO, orderLogisticVO);
		return orderLogisticVO;
	}

	@Override
	public List<OrderLogisticVO> getOrderLogisticListByOrderProductId(Long orderProductId) {
		return orderLogisticMapper.getOrderLogisticListByOrderProductId(orderProductId);
	}

	@Override
	public OrderLogisticVO getOrderLogisticByOrderProductId(Long orderProductId) {
		List<OrderLogisticVO> list = getOrderLogisticListByOrderProductId(orderProductId);
		if(CollectionUtils.isEmpty(list)){
			return null;
		}
		return list.get(0);
	}

	@Override
	public 	OrderLogisticPO getOrderLogisticByPackageSn(String packageSn) {
		LambdaQueryWrapper<OrderLogisticPO> orderLogisticPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
		orderLogisticPOLambdaQueryWrapper.eq(OrderLogisticPO::getPackageSn, packageSn);
		List<OrderLogisticPO> orderLogisticPOList = this.list(orderLogisticPOLambdaQueryWrapper);
		BizAssertUtil.notEmpty(orderLogisticPOList, "查询物流包裹信息为空，请检查物流包裹单号：" + packageSn);
		return orderLogisticPOList.get(0);
	}

	@Override
	public 	List<OrderLogisticPO> getOrderLogisticByOrderSn(String orderSn,Integer deliverPackageState) {
		LambdaQueryWrapper<OrderLogisticPO> orderLogisticPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
		orderLogisticPOLambdaQueryWrapper.eq(OrderLogisticPO::getOrderSn, orderSn);
		if(deliverPackageState != null) {
			orderLogisticPOLambdaQueryWrapper.eq(OrderLogisticPO::getDeliverPackageState, deliverPackageState);
		}
		List<OrderLogisticPO> orderLogisticPOList = this.list(orderLogisticPOLambdaQueryWrapper);
		return orderLogisticPOList;
	}

	@Override
	public List<OrderLogisticItemVO> getPackageItemList(String orderSn) {
		List<OrderLogisticItemVO> logisticItemVOList = orderLogisticMapper.getPackageItemList(orderSn);
		return logisticItemVOList;
	}


}
