package com.cfpamf.ms.mallorder.controller.fegin.facade;

import java.util.List;
import javax.annotation.Resource;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.mallorder.common.help.ResultUtils;
import com.cfpamf.ms.mallorder.model.OrderPayModel;
import com.cfpamf.ms.mallorder.po.OrderPayPO;
import com.cfpamf.ms.mallorder.request.OrderPayExample;
import com.cfpamf.ms.mallorder.service.IOrderPayService;
import com.cfpamf.ms.mallorder.vo.OrderPayBriefInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@RestController
@Slf4j
public class OrderPayFeign {


    @Resource
    private OrderPayModel orderPayModel;

    @Resource
    private IOrderPayService orderPayService;

    /**
     * 根据paySn获取订单支付表详情
     *
     * @param paySn paySn
     * @return
     */
    @GetMapping("/v1/feign/business/orderPay/get")
    public OrderPayPO getOrderPayByPaySn(@RequestParam("paySn") String paySn) {
        return orderPayModel.getOrderPayByPaySn(paySn);
    }

    /**
     * 获取条件获取订单支付表列表
     *
     * @param example 查询条件信息
     * @return
     */
    @PostMapping("/v1/feign/business/orderPay/getList")
    public List<OrderPayPO> getOrderPayList(@RequestBody OrderPayExample example) {
        return orderPayModel.getOrderPayList(example, example.getPager());
    }

    /**
     * 新增订单支付表
     *
     * @param orderPayPO
     * @return
     */
    @PostMapping("/v1/feign/business/orderPay/addOrderPay")
    public Integer saveOrderPay(@RequestBody OrderPayPO orderPayPO) {
        return orderPayModel.saveOrderPay(orderPayPO);
    }

    /**
     * 根据paySn更新订单支付表
     *
     * @param orderPayPO
     * @return
     */
    @PostMapping("/v1/feign/business/orderPay/updateOrderPay")
    public Integer updateOrderPay(@RequestBody OrderPayPO orderPayPO) {
        return orderPayModel.updateOrderPay(orderPayPO);
    }

    /**
     * 根据paySn删除订单支付表
     *
     * @param paySn paySn
     * @return
     */
    @PostMapping("/v1/feign/business/orderPay/deleteOrderPay")
    public Integer deleteOrderPay(@RequestParam("paySn") String paySn) {
        return orderPayModel.deleteOrderPay(paySn);
    }

    /**
     * 生成支付单号
     * @param memberId 用户ID
     */
    @GetMapping("/v1/feign/business/orderPay/getOrderPno")
    public Result<String> getOrderPno(@RequestParam("memberId") String memberId) {
        return ResultUtils.buildSuccessResult(orderPayService.getOrderPno(memberId));
    }

    /**
     * 根据支付单号查订单信息
     *
     * @param pno 支付单号
     */
    @GetMapping("/v1/feign/business/orderPay/getOrderPayBriefInfoByPno")
    Result<OrderPayBriefInfoVO> getOrderPayBriefInfoByPno(@RequestParam("pno") String pno) {
        return ResultUtils.buildSuccessResult(orderPayService.getOrderPayBriefInfoByPno(pno));
    }
}