package com.cfpamf.ms.mallorder.common.advice;

import com.alibaba.fastjson.JSON;
import com.cfpamf.framework.autoconfigure.common.result.Result;
import com.cfpamf.framework.autoconfigure.common.utils.ResultUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

///**
// * @Author: oyfm
// * @Description:  全局返回处理
// * @Date: 2020/1/2 17:08
// */
//@Slf4j
//@ControllerAdvice(annotations = RestController.class)
//public class GlobalResponseBodyHandler implements ResponseBodyAdvice {
//    @Override
//    public boolean supports(MethodParameter methodParameter, Class aClass) {
////        return true;
//        return false;
//    }
//
//    @Override
//    public Object beforeBodyWrite(Object object, MethodParameter methodParameter, MediaType mediaType, Class aClass, ServerHttpRequest serverHttpRequest, ServerHttpResponse serverHttpResponse) {
//        if (!mediaType.equals(MediaType.APPLICATION_JSON)) {
//            return object;
//        }
//
//        Result result = ResultUtils.resultSuccess(object);
//        if (object instanceof String) {
//            return JSON.toJSONString(result);
//        }
//        return result;
//    }
//}
