package com.cfpamf.ms.mallorder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.ms.mallorder.dto.OrderAddressDTO;
import com.cfpamf.ms.mallorder.po.OrderExtendPO;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> 2021/10/18.
 */
public interface IOrderExtendService extends IService<OrderExtendPO> {

    /**
     * 根据订单号查询订单扩展信息
     *
     * @param orderSn   订单号
     * @return          订单扩展信息
     */
    OrderExtendPO getOrderExtendByOrderSn(String orderSn);
    
    /**
     * 修改订单备注
     * @param orderSns
     * @param remark
     * @return
     */
    boolean updateOrderRemark(Set<String> orderSns,String remark) ;

    /**
     * 构建收货信息
     * @param orderExtendPO
     * @return
     */
    OrderAddressDTO buildReceiveInfo(OrderExtendPO orderExtendPO);

    void updateOrderReceiveCode(String orderSn, String receiveCode);

    /**
     * 处理历史实仓数据
     *
     * @param ignoreOrderSn 需要忽略的订单编号
     */
    void dealHistoryActualWareHouse(String ignoreOrderSn);

    /**
     * 处理历史无自提点的自提订单
     * @param orderSnList 订单编号列表，传空时处理全量
     */
    void orderPointFix(List<String> orderSnList);
}
