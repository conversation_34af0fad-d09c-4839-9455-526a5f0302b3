package com.cfpamf.ms.mallorder.integration.shop;

import com.cfpamf.framework.autoconfigure.common.exception.MSException;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.cfpamf.ms.mallshop.api.VendorFeignClient;
import com.cfpamf.ms.mallshop.resp.Vendor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Slf4j
@Component
public class VendorFeignIntegration {
    @Autowired
    private VendorFeignClient vendorFeignClient;

    public Vendor getVendorByStoreId(Long storeId) {
        if (Objects.isNull(storeId)) {
            return null;
        }
        Vendor result = null;
        try {
            result = vendorFeignClient.getVendorByStoreId(storeId);
        } catch (Exception ex) {
            log.error("根据店铺id查询店铺详情出现未知异常,storeId:{}",storeId,ex);
            throw new MSException(String.valueOf(ErrorCodeEnum.C.CALL_EXCEPTION.getCode()), "根据店铺id查询店铺详情调用服务异常");
        }
        if (Objects.isNull(result)) {
            throw new MSException(String.valueOf(ErrorCodeEnum.C.RESULT_INVALID.getCode()), "根据店铺id查询店铺详情返回结果为空");
        }
        return result;
    }
}
