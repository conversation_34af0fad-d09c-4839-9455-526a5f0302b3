package com.cfpamf.ms.mallorder.scheduler;

import com.cfpamf.ms.mallorder.integration.shop.ShopIntegration;
import com.cfpamf.ms.mallorder.mapper.OrderMapper;
import com.cfpamf.ms.mallorder.service.IOrderExtendService;
import com.google.api.client.util.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 订单自提点修复job
 *
 * <AUTHOR>
 * @since 2024/11/29
 */
@Component
@Slf4j
public class OrderPointFixJob {

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private ShopIntegration shopIntegration;

    @Autowired
    private IOrderExtendService extendService;

    @XxlJob("OrderPointFixJob")
    public ReturnT<String> execute(String s) throws Exception {
        List<String> orderSnList = Lists.newArrayList();
        if (StringUtils.isNotBlank(s)){
            String[] split = s.split(",");
            orderSnList.addAll(Arrays.asList(split));
        }
        extendService.orderPointFix(orderSnList);
        return new ReturnT("SUCCESS");
    }
}
