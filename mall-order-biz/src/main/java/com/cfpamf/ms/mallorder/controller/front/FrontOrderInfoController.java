package com.cfpamf.ms.mallorder.controller.front;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.builder.OrderBuilder;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.enums.OrderPatternEnum;
import com.cfpamf.ms.mallorder.common.help.ResultUtils;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.common.util.CurrentUserUtil;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.model.OrderProductModel;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.request.OrderExample;
import com.cfpamf.ms.mallorder.service.IOrderInfoService;
import com.cfpamf.ms.mallorder.service.IOrderProductService;
import com.cfpamf.ms.mallorder.service.IOrderService;
import com.cfpamf.ms.mallorder.v2.service.OrderPresellService;
import com.cfpamf.ms.mallorder.vo.*;
import com.slodon.bbc.core.constant.WebConst;
import com.slodon.bbc.core.controller.BaseController;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.PageVO;
import com.slodon.bbc.core.response.PagerInfo;
import com.slodon.bbc.core.response.SldResponse;
import com.slodon.bbc.core.util.UserUtil;
import com.slodon.bbc.core.util.WebUtil;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;

@Api(tags = "front-订单管理")
@Slf4j
@RestController
@RequestMapping("front/orderInfo")
public class FrontOrderInfoController extends BaseController {

    @Resource
    private OrderProductModel orderProductModel;

    @Autowired
    private IOrderInfoService iOrderInfoService;

    @Resource
    private IOrderService orderService;

    @Autowired
    private IOrderProductService iOrderProductService;

    @Autowired
    private OrderPresellService orderPresellService;

    @Resource
    private OrderModel orderModel;

    @ApiOperation(value = "订单列表相关接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderSn", value = "订单号", paramType = "query"),
            @ApiImplicitParam(name = "orderState", value = "订单状态：0-已取消；10-待付款订单；20-代发货订单；30-待收货订单；40-已完成;50-已关闭", paramType = "query"),
            @ApiImplicitParam(name = "evaluateState", value = "订单评价状态：1.未评价", paramType = "query"),
            @ApiImplicitParam(name = "paySn", value = "支付单号", paramType = "query"),
            @ApiImplicitParam(name = "isSpelling", value = "拼团订单是否需要正在拼团中：1-是，2-否", paramType = "query"),
            @ApiImplicitParam(name = "orderType", value = "订单类型：普通-1；拼团-102", paramType = "query"),
            @ApiImplicitParam(name = "channel", value = "下单操作渠道：H5-浏览器H5；APP-乡助APP；WE_CHAT-微信浏览器；" +
                    "MINI_PRO-小程序；OMS-运管物资", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "分页大小", defaultValue = "20", paramType = "query"),
            @ApiImplicitParam(name = "current", value = "当前页面位置", defaultValue = "1", paramType = "query")
    })
    @GetMapping("list")
    public JsonResult<PageVO<MemberOrderListVO>> getList(HttpServletRequest request, String orderSn, Integer orderState,
                                                         Integer evaluateState, String paySn, Integer orderType,
                                                         Integer isSpelling, String channel) {
        PagerInfo pager = WebUtil.handlerPagerInfo(request);
        Member member = CurrentUserUtil.getUser(request, Member.class);
        return iOrderInfoService.listPageOrder(orderSn, orderState, evaluateState, pager,
                member, paySn, orderType, isSpelling, channel);
    }



    @ApiOperation("获取银行卡转账汇款订单详情接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderSn", value = "订单号" ),
            @ApiImplicitParam(name = "paySn", value = "支付单号"),
            @ApiImplicitParam(
                    name = WebConst.USER_HEADER,
                    value = "客户信息 由网关处理",
                    paramType = "header",
                    dataType = "String")
    })
    @GetMapping("bankTransferdetail")
    public JsonResult<BankTransferOrderDetailVO> getBankTransferDetail(HttpServletRequest request,
                                                                       @RequestParam(name = "paySn",required = false) String paySn,
                                                                       @RequestParam(name = "orderSn",required = false) String orderSn) {
        Member member = CurrentUserUtil.getUser(request, Member.class);
        return iOrderInfoService.getBankTransferDetail(paySn,orderSn, member);
    }

    @ApiOperation("校验预付订单是否存在银行卡转账待汇款")
    @GetMapping("checkPresellPayMethod")
    public JsonResult<Boolean> checkPresellPayMethod(@RequestParam(name = "paySn", required = false) String paySn) {
        return orderPresellService.checkPresellPayMethod(paySn);
    }


    @ApiOperation(value = "获取订单详情接口", tags = "CORE")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderSn", value = "订单号", required = true),
            @ApiImplicitParam(
                    name = WebConst.USER_HEADER,
                    value = "客户信息 由网关处理",
                    paramType = "header",
                    dataType = "String")
    })
    @PostMapping("detail")
    public JsonResult<MemberOrderDetailVO> getOrderDetail(HttpServletRequest request, @RequestParam("orderSn") String orderSn) {
        Member member = CurrentUserUtil.getUser(request, Member.class);
        return iOrderInfoService.getOrderDetail(orderSn, member);
    }


    @ApiOperation("订单商品发货列表")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = WebConst.USER_HEADER,
                    value = "商户信息 由网关处理",
                    paramType = "header",
                    dataType = "String"),
            @ApiImplicitParam(name = "orderSn", value = "订单号", required = true)
    })
    @GetMapping("deliveryList")
    public JsonResult<List<OrderFrontDeliveryVO>> deliveryDetail(HttpServletRequest request, @RequestParam("orderSn") String orderSn) {
        Member member = UserUtil.getUser(request, Member.class);

        LambdaQueryWrapper<OrderPO> query = new LambdaQueryWrapper<>();
        query.eq(OrderPO::getOrderSn, orderSn)
                .select(OrderPO::getOrderSn, OrderPO::getMemberId);
        OrderPO orderPO = orderService.getOne(query);
        BizAssertUtil.notNull(orderPO, "订单信息为空");
        BizAssertUtil.isTrue(!member.getMemberId().equals(orderPO.getMemberId()), "您无权操作此订单");

        return SldResponse.success(orderService.frontDeliveryList(orderSn, null));
    }

    @ApiOperation("批量订单发货信息")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = WebConst.USER_HEADER,
                    value = "商户信息 由网关处理",
                    paramType = "header",
                    dataType = "String"),
            @ApiImplicitParam(name = "orderSnList", value = "订单号列表", required = true)
    })
    @GetMapping("batchOrderDeliveryInfo")
    public JsonResult<List<OrderDeliveryInfoVO>> batchOrderDeliveryInfo(HttpServletRequest request, @RequestParam("orderSnList") List<String> orderSnList) {
        Member member = UserUtil.getUser(request, Member.class);

        return SldResponse.success(orderService.batchOrderDeliveryInfo(orderSnList, member.getMemberId()));
    }

    @ApiOperation("获取聊天界面我的订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "storeId", value = "店铺id", required = true, paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "分页大小", defaultValue = "20", paramType = "query"),
            @ApiImplicitParam(name = "current", value = "当前页面位置", defaultValue = "1", paramType = "query")
    })
    @GetMapping("myOrders")
    public JsonResult<PageVO<ChatOrdersVO>> myOrders(HttpServletRequest request, Long storeId) {
        Member member = UserUtil.getUser(request, Member.class);
        OrderExample example = new OrderExample();
        example.setStoreId(storeId);
        example.setMemberId(member.getMemberId());
        if (CommonConst.PURCHASE_ORDER_HEADER.equals(request.getHeader(CommonConst.ORDER_PATTERN_HEADER))) {
            example.setOrderPattern(OrderPatternEnum.PURCHASE_CENTRE.getValue());
        } else {
            example.setOrderPatternNotIn(String.valueOf(OrderPatternEnum.PURCHASE_CENTRE.getValue()));
        }

        PagerInfo pager = WebUtil.handlerPagerInfo(request);
        return iOrderInfoService.pageMyOrderList(example, pager);
    }

    @ApiOperation(value = "根据交易号查询商品名称")
    @GetMapping("/listProductsByTno")
    public Result<List<OrderProductsVO>> listProductsByTno(@RequestParam("tnos") @NotNull List<Long> tnos) {

        return ResultUtils.buildSuccessResult(orderProductModel.listProductsByTno(tnos));
    }

    @ApiOperation(value = "商家基础信息 用于用呗支付协议")
    @GetMapping("/baseDetail")
    public Result<MerchantBaseVo> getMerchantBaseInfo(@RequestParam("merchantId") @NotNull Long merchantId) {
        return ResultUtils.buildSuccessResult(orderProductModel.getMerchantBaseInfo(merchantId));
    }

    @ApiOperation(value = "订单明细查询(决策使用商品信息)")
    @GetMapping("/orderInfo")
    public Result<OrderProductsVO> orderInfo(@RequestParam("tno") @NotNull Long tno) {

        return ResultUtils.buildSuccessResult(orderProductModel.orderInfo(tno));
    }

    @ApiOperation(value = "订单商品明细查询(用呗复核使用商品信息)")
    @GetMapping("/detail/enjoy")
    public Result<OrderLoanDetailVO> orderDetail(@RequestParam("orderSn") @ApiParam("订单号") @NotEmpty String orderSn) {
        return ResultUtils.buildSuccessResult(iOrderProductService.orderDetail(orderSn));
    }

    @ApiOperation(value = "获取到家服务订单待发货总单数")
    @GetMapping("/installInfo")
    public JsonResult<Integer> installInfo(HttpServletRequest request) {
        Member member = UserUtil.getUser(request, Member.class);
        return SldResponse.success(orderService.getInstallOrderCountByUserNo(member.getUserNo()));
    }

    @GetMapping("/countStatusByStationMaster")
    @ApiOperation("根据订单状态统计数量")
    public JsonResult<OrderCountVo> countStatusByStationMaster(HttpServletRequest request) {
        Member stationMater = OrderBuilder.getStationMasterUser(request);
        BizAssertUtil.isTrue(Objects.isNull(stationMater),"获取站长信息失败，请重新登录！");
        return SldResponse.success(orderModel.countStatusByStationMaster(stationMater.getUserNo()));
    }
}
