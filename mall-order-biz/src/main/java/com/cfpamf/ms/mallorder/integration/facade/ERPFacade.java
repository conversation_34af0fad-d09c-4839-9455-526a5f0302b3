package com.cfpamf.ms.mallorder.integration.facade;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.mallorder.integration.erp.vo.MallProductQuery;
import com.cfpamf.ms.mallorder.integration.erp.vo.ProductVO;
import com.cfpamf.ms.mallorder.integration.facade.dto.*;
import com.cfpamf.ms.mallorder.vo.ErpDepotVo;
import com.slodon.bbc.core.response.JsonResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description erp
 **/
@FeignClient(name = "erp-service", url = "${erp-service.url}")
public interface ERPFacade {

	@ApiOperation("电商-通过物料编码获取货品信息列表")
	@PostMapping("/feign/product/getProductInfoList/mall")
	List<ProductVO> getProductInfoList(@RequestBody MallProductQuery query);

	@ApiOperation("查询skuUnitCode所属sku的所有skuUnitCode")
	@PostMapping({"/feign/product/getSkuUnitCodeListBySkuUnitCode"})
	Map<String, List<ProductSkuUnitVO>> getSkuUnitCodeListBySkuUnitCode(@RequestBody List<String> skuUnitCodeList);

	@ApiOperation(value = "单位换算")
	@PostMapping(value = "/feign/product/unitConvert")
	List<UnitConvertVo> unitConvert(@RequestBody @Valid List<UnitConvertQuery> unitConvertQueryList);


	@ApiOperation(value = "库存发货前置校验")
	@PostMapping(value = "/feign/real/stock/out/preCheck")
	JsonResult<Boolean> stockOutPreCheck(@RequestBody @Valid StockCheckParamDTO stockCheckParamDTO);

	@ApiOperation(value = "库存入库前置校验")
	@PostMapping(value = "/feign/real/stock/return/preCheck")
	JsonResult<Boolean> stockReturnPreCheck(@RequestBody @Valid StockReturnCheckParamDTO stockReturnCheckParamDTO);


	@ApiOperation(value = "仓库信息获取")
	@PostMapping("/feign/depot/getDepotStockList")
	Result<List<ErpDepotVO>> getDepotStockList(@RequestBody @Valid ErpDepotStockQuery query);

	@ApiOperation("sku编码统一转换 skuUnitCode转换skuId")
	@PostMapping("/feign/product/getListBySkuUnitCode")
	List<ProductSkuUnitVO> getListBySkuUnitCode(@RequestBody List<String> skuUnitCodeList);

	@ApiOperation(value = "校验销售退货入库流水是否已入库成功")
	@PostMapping(value = "/feign/actualReturnInFlow/checkReturnInFlow")
	Result<Boolean> checkReturnInFlow(@RequestBody ErpActualReturnInFlowQueryDTO queryDTO);

	@GetMapping(value = "/feign/actualReturnInFlow/orderAfterReturnGoods")
	@ApiOperation(value = "售后用户发货，向erp发起入库")
	Result<Boolean> orderAfterReturnGoods(@RequestParam(value = "afsSn")String afsSn);

	@ApiOperation(value = "分支仓库信息获取")
	@GetMapping("/feign/depot/getDepotById")
	Result<ErpDepotVo> getDepotById(@RequestParam(value = "depotId")Long depotId);

	@ApiOperation("通过skuId及规格分类获取货品规格单位信息列表")
	@PostMapping("feign/product/batchFindUnitListBySkuIdAndType")
	List<ProductSkuUnitVO> batchFindUnitListBySkuIdAndType(@RequestBody List<String> skuIds, @RequestParam("type") String unitTypeCode);

	@ApiOperation(value = "根据skuIds获取货品信息")
	@PostMapping("/feign/product/getProductAndSkuListBySkuIds")
	Result<List<ErpProductAndSkuVo>> getProductAndSkuListBySkuIds(@RequestBody List<String> skuIds);

}