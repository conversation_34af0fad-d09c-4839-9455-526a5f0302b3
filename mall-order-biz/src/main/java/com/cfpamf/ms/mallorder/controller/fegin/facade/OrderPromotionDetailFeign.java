package com.cfpamf.ms.mallorder.controller.fegin.facade;


import com.cfpamf.ms.mallorder.model.OrderPromotionDetailModel;
import com.cfpamf.ms.mallorder.po.OrderPromotionDetailPO;
import com.cfpamf.ms.mallorder.request.OrderPromotionDetailExample;
import com.cfpamf.ms.mallorder.service.IOrderPromotionDetailService;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 订单活动优惠明细feign
 */
@RestController
@Slf4j
public class OrderPromotionDetailFeign {

    @Resource
    private OrderPromotionDetailModel orderPromotionDetailModel;

    @Resource
    private IOrderPromotionDetailService promotionDetailService;

    @GetMapping("/v1/feign/business/orderPromotionDetail/listCouponNoByOrderSn")
    JsonResult<List<String>> listByOrderSn(@RequestParam("orderSn") String orderSn){
        List<String> result = promotionDetailService.listCouponNoByOrderSn(orderSn);
        return SldResponse.success(result);
    }

    /**
     * 根据detailId获取订单活动优惠明细详情
     *
     * @param detailId detailId
     * @return
     */
    @GetMapping("/v1/feign/business/orderPromotionDetail/get")
    public OrderPromotionDetailPO getOrderPromotionDetailByDetailId(@RequestParam("detailId") Integer detailId) {
        return orderPromotionDetailModel.getOrderPromotionDetailByDetailId(detailId);
    }

    /**
     * 获取条件获取订单活动优惠明细列表
     *
     * @param example 查询条件信息
     * @return
     */
    @PostMapping("/v1/feign/business/orderPromotionDetail/getList")
    public List<OrderPromotionDetailPO> getOrderPromotionDetailList(@RequestBody OrderPromotionDetailExample example) {
        return orderPromotionDetailModel.getOrderPromotionDetailList(example, example.getPager());
    }

    /**
     * 新增订单活动优惠明细
     *
     * @param orderPromotionDetailPO
     * @return
     */
    @PostMapping("/v1/feign/business/orderPromotionDetail/addOrderPromotionDetail")
    public Integer saveOrderPromotionDetail(@RequestBody OrderPromotionDetailPO orderPromotionDetailPO) {
        return orderPromotionDetailModel.saveOrderPromotionDetail(orderPromotionDetailPO);
    }

    /**
     * 根据detailId更新订单活动优惠明细
     *
     * @param orderPromotionDetailPO
     * @return
     */
    @PostMapping("/v1/feign/business/orderPromotionDetail/updateOrderPromotionDetail")
    public Integer updateOrderPromotionDetail(@RequestBody OrderPromotionDetailPO orderPromotionDetailPO) {
        return orderPromotionDetailModel.updateOrderPromotionDetail(orderPromotionDetailPO);
    }

    /**
     * 根据detailId删除订单活动优惠明细
     *
     * @param detailId detailId
     * @return
     */
    @PostMapping("/v1/feign/business/orderPromotionDetail/deleteOrderPromotionDetail")
    public Integer deleteOrderPromotionDetail(@RequestParam("detailId") Integer detailId) {
        return orderPromotionDetailModel.deleteOrderPromotionDetail(detailId);
    }


    /**
     * 根据detailId更新订单活动优惠明细
     *
     */
    @PostMapping("/v1/feign/business/orderPromotionDetail/initFunder")
    public Boolean initFunder() {
        return orderPromotionDetailModel.initFunder();
    }
}