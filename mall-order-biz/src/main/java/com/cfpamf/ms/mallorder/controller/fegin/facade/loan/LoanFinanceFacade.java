package com.cfpamf.ms.mallorder.controller.fegin.facade.loan;

import com.cfpamf.common.ms.result.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 信贷facade
 *
 * <AUTHOR>
 * @since 2024/11/25
 */
@FeignClient(
        name = "ms-finance",
        url = "${ms-finance.url}"
)
public interface LoanFinanceFacade {
    /**
     * 用户唯一码查询
     *
     * @param queryUniqueCodeParam 唯一码查询参数
     * @return 唯一码
     */
    @PostMapping("/query/queryMallPaySummary")
    Result<String> queryUniqueCode(@RequestParam("applyId") String applyId,@RequestParam("loanCustId")String loanCustId);

}
