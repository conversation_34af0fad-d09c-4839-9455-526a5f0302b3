package com.cfpamf.ms.mallorder.v2.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:
 * @Author: zml
 * @CreateTime: 2022-05-18
 */
@Data
public class ProductPromotionParamDTO {
    @ApiModelProperty("活动类型")
    private Integer promotionType;
    @ApiModelProperty("活动编码")
    private String promotionId;
    @ApiModelProperty("SPU编码")
    private Long goodsId;
    @ApiModelProperty("SKU编码")
    private Long productId;
    @ApiModelProperty("一个产品的订金金额")
    private BigDecimal deposit;
    @ApiModelProperty("地区名编码")
    private String areaCode;
    @ApiModelProperty("金融规则编码")
    private String financeRuleCode;
}
