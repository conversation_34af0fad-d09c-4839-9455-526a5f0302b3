package com.cfpamf.ms.mallorder.integration.facade;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.mallorder.integration.facade.dto.SpyhunterEngineDTO;
import com.cfpamf.ms.mallorder.integration.facade.dto.SpyhunterEngineQuery;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

@FeignClient(
        name = "spyhunter-engine",
        url = "${spyhunter-engine.url}"
)
public interface SpyhunterEngineFacade {


    @PostMapping(value = {"/services/v1/queryRuleHitResult"})
    SpyhunterEngineDTO queryRuleHitResult(@RequestBody @Valid SpyhunterEngineQuery spyhunterEngineQuery);
}
