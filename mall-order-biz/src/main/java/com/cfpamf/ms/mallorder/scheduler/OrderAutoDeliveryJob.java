package com.cfpamf.ms.mallorder.scheduler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.common.enums.OrderCreateChannel;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.OrderProductDeliverDTO;
import com.cfpamf.ms.mallorder.mapper.OrderMapper;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderProductPO;
import com.cfpamf.ms.mallorder.req.OrderDeliveryReq;
import com.cfpamf.ms.mallorder.service.IOrderProductService;
import com.cfpamf.ms.mallorder.service.IOrderService;
import com.cfpamf.ms.mallshop.resp.Vendor;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 订单自动流程job
 */
@Component
@Slf4j
public class OrderAutoDeliveryJob {

    @Resource
    private IOrderService orderService;

    @Resource
    private OrderMapper orderMapper;

    @Resource
    private OrderModel orderModel;

    @Value("${order.offline.delivery.name:admin}")
    private String orderOfflineDeliveryName;

    @Value("${order.offline.delivery.mobile:18890909090}")
    private String orderOfflineDeliveryMobile;

    @Resource
    private IOrderProductService orderProductService;

    @XxlJob("OrderAutoDeliveryJob")
    public ReturnT<String> execute(String s) throws Exception {

        XxlJobHelper.log("=============== START Automates the order process , DATE :{}", LocalDateTime.now());

        String dateFormat = DateUtil.format(new Date(), DateUtil.CN_LONG_FORMAT);

        /**
         * 查询带金融规则订单，自动发货订单
         */
        List<String> autoDeliverOrders = orderMapper.getAutoDeliverOrder(dateFormat);
        if (!CollectionUtils.isEmpty(autoDeliverOrders)){
            String orderSns = StringUtils.join(autoDeliverOrders, ",");
            Vendor vendor = new Vendor();
            vendor.setVendorId(OrderConst.LOG_USER_ID);
            vendor.setVendorName(OrderConst.LOG_USER_NAME);
            //orderService.batchDelivery(orderSns, vendor, OrderCreateChannel.WEB);
            String[] orderArr = orderSns.split(",");
            Arrays.stream(orderArr).forEach(orderSn -> {
                try {



                    LambdaQueryWrapper<OrderProductPO> orderProductQuery = new LambdaQueryWrapper<>();
                    orderProductQuery.eq(OrderProductPO::getOrderSn, orderSn).eq(OrderProductPO::getEnabledFlag, 1)
                            .select(OrderProductPO::getOrderProductId);
                    List<OrderProductPO> orderProductPOS = orderProductService.list(orderProductQuery);
                    List<Long> orderProductIds =
                            orderProductPOS.stream().map(OrderProductPO::getOrderProductId).collect(Collectors.toList());

                    List<Long> productIds =
                            orderProductPOS.stream().map(OrderProductPO::getProductId).collect(Collectors.toList());

                    OrderDeliveryReq deliveryReq = new OrderDeliveryReq(orderSn, orderProductIds, productIds,Boolean.TRUE,
                            orderOfflineDeliveryName, orderOfflineDeliveryMobile, OrderCreateChannel.WEB);
                    //获取可发货数量，根据orderProductId组装参数 orderProductDetail
                    //可发货数量 = 购买数量 - 已发货数量 - 仅退款数量
                    List<OrderProductDeliverDTO> orderProductDeliveryList = orderService.getProductDeliveryListByProductId(deliveryReq.getOrderSn(), deliveryReq.getProductIds());
                    deliveryReq.setOrderProductDetail(orderProductDeliveryList);

                    deliveryReq.setOrderProductIds(orderProductPOS.stream()
                            .map(OrderProductPO::getOrderProductId).collect(Collectors.toList()));
                    orderService.deliveryV2(deliveryReq, vendor);
                } catch (Exception e) {
                    log.warn("{}订单自动发货失败:{}",orderSn,e.getMessage());
                }
            });
        }

        /**
         * 查询带金融规则订单，自动收货订单（过滤换货后的销售单,过滤线下补录订单）
         */
        List<OrderPO> autoReceiveOrders = orderMapper.getAutoReceiveOrder();
        if (!CollectionUtils.isEmpty(autoReceiveOrders)) {
            for (OrderPO orderPO : autoReceiveOrders) {
                try {
                    // 确认收货
                    orderModel.receiveOrder(orderPO, OrderConst.LOG_ROLE_MEMBER,
                            OrderConst.LOG_USER_ID, OrderConst.LOG_USER_NAME, "系统自动收货", OrderCreateChannel.WEB);

                    // 收货消息
                    Member member = new Member();
                    member.setMemberId(OrderConst.LOG_ROLE);
                    member.setMemberName(OrderConst.LOG_USER_NAME);
                } catch (Exception e) {
                    log.warn("自动处理订单发货失败，orderSn：{}", orderPO.getOrderSn());
                }
            }
        }

        XxlJobHelper.log("=============== FINISHED Automates the order process, DATE : {}，COUNT: {}",
                LocalDateTime.now(), autoDeliverOrders.size() + autoReceiveOrders.size());

        return ReturnT.SUCCESS;
    }

}
