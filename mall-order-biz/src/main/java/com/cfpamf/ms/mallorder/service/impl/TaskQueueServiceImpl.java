package com.cfpamf.ms.mallorder.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cfpamf.ms.mallorder.common.constant.TaskConstant;
import com.cfpamf.ms.mallorder.common.enums.TaskQueueBizTypeEnum;
import com.cfpamf.ms.mallorder.common.enums.TaskQueueStatusEnum;
import com.cfpamf.ms.mallorder.mapper.TaskQueueMapper;
import com.cfpamf.ms.mallorder.po.TaskQueueHisPO;
import com.cfpamf.ms.mallorder.po.TaskQueueLogPO;
import com.cfpamf.ms.mallorder.po.TaskQueuePO;
import com.cfpamf.ms.mallorder.service.ITaskQueueHisService;
import com.cfpamf.ms.mallorder.service.ITaskQueueLogService;
import com.cfpamf.ms.mallorder.service.ITaskQueueService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <p>
 * 定时任务推送队列表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-01-08
 */
@Slf4j
@Service
public class TaskQueueServiceImpl extends ServiceImpl<TaskQueueMapper, TaskQueuePO> implements ITaskQueueService {

    static Lock taskQueueLock = new ReentrantLock();

    @Autowired
    ITaskQueueHisService taskQueueHisService;

    @Autowired
    ITaskQueueLogService taskQueueLogService;

    @Resource
    TaskQueueMapper taskQueueMapper;

    @Override
    public TaskQueuePO saveTaskQueue(Long bizId, TaskQueueBizTypeEnum bizType, Date executeTime, String operator){
        TaskQueuePO taskQueuePo = new TaskQueuePO();
        taskQueuePo.setBizId(bizId);
        taskQueuePo.setBizType(bizType);
        taskQueuePo.setExecuteCount(0);
        taskQueuePo.setNextExecuteTime(executeTime);
        taskQueuePo.setStatus(TaskQueueStatusEnum.INIT);
        taskQueuePo.setCreateBy(operator);
        taskQueuePo.setUpdateBy(operator);
        save(taskQueuePo);
        return taskQueuePo;
    }

    @Override
    public void removeTaskQueue(TaskQueuePO taskQueuePo) {
//        taskQueueLock.lock();
        try {
            TaskQueueHisPO hisPo = new TaskQueueHisPO();
            BeanUtils.copyProperties(taskQueuePo, hisPo);
            taskQueueHisService.save(hisPo);
            removeById(taskQueuePo.getId());
        } catch (Exception ex) {
            log.error("task任务处理异常,bizId-{}", taskQueuePo.getBizId());
        }
//        finally {
//            taskQueueLock.unlock();
//        }
    }

    @Override
    public Date getErrorTaskNextExecuteTime() {
        return DateUtils.addMilliseconds(new Date(),TaskConstant.TASK_NEXT_EXECUTE_INTERVAL.intValue());
    }

    @Override
    public List<TaskQueuePO> listTodoTask(TaskQueueBizTypeEnum bizType, String executeTime) {

        LambdaQueryWrapper<TaskQueuePO> taskQuery = new LambdaQueryWrapper();
        taskQuery.eq(TaskQueuePO::getBizType, bizType.getValue());
        taskQuery.in(TaskQueuePO::getStatus, TaskQueueStatusEnum.INIT, TaskQueueStatusEnum.FAIL);
        taskQuery.le(TaskQueuePO::getNextExecuteTime, executeTime);
        taskQuery.orderByDesc(TaskQueuePO::getBizId);
        taskQuery.last("limit 10000");
        return list(taskQuery);
    }

    @Override
    public List<TaskQueuePO> listTodoTaskLimit(TaskQueueBizTypeEnum bizType, String executeTime, String limit) {
        if (StringUtils.isEmpty(limit)) {
            return listTodoTask(bizType, executeTime);
        }

        LambdaQueryWrapper<TaskQueuePO> taskQuery = new LambdaQueryWrapper();
        taskQuery.eq(TaskQueuePO::getBizType, bizType.getValue());
        taskQuery.in(TaskQueuePO::getStatus, TaskQueueStatusEnum.INIT, TaskQueueStatusEnum.FAIL);
        taskQuery.le(TaskQueuePO::getNextExecuteTime, executeTime);
        taskQuery.orderByDesc(TaskQueuePO::getBizId);
        taskQuery.last("limit " + limit);
        return list(taskQuery);
    }

    @Override
    public List<TaskQueuePO> listTodoTaskLimit(List<Integer> bizType, String executeTime, String limit) {
        LambdaQueryWrapper<TaskQueuePO> taskQuery = new LambdaQueryWrapper();
        taskQuery.in(TaskQueuePO::getBizType, bizType);
        taskQuery.in(TaskQueuePO::getStatus, TaskQueueStatusEnum.INIT, TaskQueueStatusEnum.FAIL);
        taskQuery.le(TaskQueuePO::getNextExecuteTime, executeTime);
        taskQuery.orderByDesc(TaskQueuePO::getBizId);
        taskQuery.last("limit " + limit);
        return list(taskQuery);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void taskSuccess(TaskQueuePO task) {
        TaskQueueLogPO taskLog = new TaskQueueLogPO();
        task.setStatus(TaskQueueStatusEnum.SUCCESS);
        taskLog.setResponseCode("000000");
        taskLog.setRequestContent("SUCCESS");
        taskLog.setResponseContent("SUCCESS");
        dealTask(task, taskLog);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void taskFail(TaskQueuePO task, String requestContent, String responseContent) {
        TaskQueueLogPO taskLog = new TaskQueueLogPO();
        task.setStatus(TaskQueueStatusEnum.FAIL);
        taskLog.setResponseCode(TaskConstant.JOB_EXCEPTION_CODE);
        taskLog.setRequestContent(requestContent);
        taskLog.setResponseContent(responseContent);
        dealTask(task, taskLog);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void dealTask(TaskQueuePO task, TaskQueueLogPO taskLog) {
        task.setExecuteCount(task.getExecuteCount() + 1);
        // 记录日志
        BeanUtils.copyProperties(task, taskLog);
        taskLog.setCreateTime(null);
        taskLog.setUpdateTime(null);
        taskLog.setTaskId(task.getId());
        taskLog.setExecuteOrder(task.getExecuteCount());
        taskQueueLogService.save(taskLog);
        // 失败：更新状态
        if (TaskQueueStatusEnum.FAIL == task.getStatus()) {
            task.setNextExecuteTime(this.getErrorTaskNextExecuteTime());
            task.setUpdateTime(null);
            this.updateById(task);
        }
        // 成功：归档
        if (TaskQueueStatusEnum.SUCCESS == task.getStatus()) {
            this.removeTaskQueue(task);
        }
    }

    @Override
    public int modifyTaskQueue(Long bizId, Integer bizType, Long excludeId) {
        log.info("修改TaskQueue,入参,bizId:{},bizType:{},excludeId:{}",bizId,bizType,excludeId);
        if(bizId==null || bizType == null){
            return 0;
        }
        int queueCount = taskQueueMapper.modifyTaskQueue(bizId, bizType, excludeId);
        log.info("修改TaskQueue,结果:{}",queueCount);
        int logCount = taskQueueLogService.modifyTaskQueueLog(bizId,bizType,excludeId);
        log.info("修改TaskQueueLog,结果:{}",logCount);
        int hisCount = taskQueueHisService.modifyTaskQueueHisPO(bizId, bizType);
        log.info("修改TaskQueueLog,结果:{}",hisCount);
        return queueCount;
    }

    @Override
    public TaskQueuePO getTaskQueueByBizIdAndBizType(String bizId, Integer bizType) {
        LambdaQueryWrapper<TaskQueuePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaskQueuePO::getBizId, bizId);
        queryWrapper.eq(TaskQueuePO::getBizType, bizType);
        return this.getOne(queryWrapper);
    }
}
