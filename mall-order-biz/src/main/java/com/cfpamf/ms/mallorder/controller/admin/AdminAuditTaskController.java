package com.cfpamf.ms.mallorder.controller.admin;

import com.cfpamf.ms.mallorder.dto.AuditTaskSearchVO;
import com.cfpamf.ms.mallorder.service.AdminAuditTaskService;
import com.cfpamf.ms.mallorder.vo.AuditTaskVO;
import com.cfpamf.ms.mallorder.vo.TaskTypeVO;
import com.cfpamf.ms.mallsystem.vo.Admin;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.PageVO;
import com.slodon.bbc.core.response.SldResponse;
import com.slodon.bbc.core.util.UserUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 审批任务
 *
 * <AUTHOR>
 * @since 2022/3/9
 */
@Api(tags = "admin-审批任务")
@RestController
@Slf4j
@RequestMapping("admin/auditTask")
public class AdminAuditTaskController {

    @Autowired
    private AdminAuditTaskService adminAuditTaskService;

    @ApiOperation(value = "查询代办任务列表")
    @PostMapping("todoList")
    public JsonResult<PageVO<AuditTaskVO>> todoList(HttpServletRequest request, @RequestBody AuditTaskSearchVO searchVO) {
        Admin admin = UserUtil.getUser(request, Admin.class);
        PageVO<AuditTaskVO> result = adminAuditTaskService.todoList(admin, searchVO);
        return SldResponse.success(result);
    }

    @ApiOperation("查询已完成任务列表")
    @PostMapping("doneList")
    public JsonResult<PageVO<AuditTaskVO>> doneList(HttpServletRequest request, @RequestBody AuditTaskSearchVO searchVO) {
        Admin admin = UserUtil.getUser(request, Admin.class);
        PageVO<AuditTaskVO> result = adminAuditTaskService.doneList(admin, searchVO);
        return SldResponse.success(result);
    }
    @ApiOperation("获取所有任务类型")
    @GetMapping("getAllTaskType")
    public JsonResult<List<TaskTypeVO>> getAllTaskType() {
        List<TaskTypeVO> result = adminAuditTaskService.getAllTaskType();
        return SldResponse.success(result);
    }
}
