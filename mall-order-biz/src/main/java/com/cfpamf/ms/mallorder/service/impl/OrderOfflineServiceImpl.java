package com.cfpamf.ms.mallorder.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.framework.autoconfigure.mybatis.BaseRepoServiceImpl;
import com.cfpamf.ms.bizconfig.facade.vo.UserVo;
import com.cfpamf.ms.bms.facade.vo.DictionaryItemVO;
import com.cfpamf.ms.bms.facade.vo.DingTalkUserBmsVO;
import com.cfpamf.ms.customer.facade.vo.CustBaseInfoVo;
import com.cfpamf.ms.mallorder.builder.OrderOfflineBuilder;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.enums.*;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.common.util.DateUtil;
import com.cfpamf.ms.mallorder.common.util.ExternalApiUtil;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.*;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.integration.cust.BizConfigIntegration;
import com.cfpamf.ms.mallorder.integration.cust.CustomerIntegration;
import com.cfpamf.ms.mallorder.integration.facade.BmsDingTalkFacade;
import com.cfpamf.ms.mallorder.integration.facade.BmsIntegration;
import com.cfpamf.ms.mallorder.integration.facade.MallExternalAdapterFacade;
import com.cfpamf.ms.mallorder.integration.facade.dto.SiteQuery;
import com.cfpamf.ms.mallorder.integration.facade.dto.SiteVo;
import com.cfpamf.ms.mallorder.integration.shop.ShopIntegration;
import com.cfpamf.ms.mallorder.integration.wms.WmsIntegration;
import com.cfpamf.ms.mallorder.mapper.OrderOfflineMapper;
import com.cfpamf.ms.mallorder.model.OrderLogModel;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.model.OrderPayModel;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.req.OrderDeliveryReq;
import com.cfpamf.ms.mallorder.request.OrderOfflineRequest;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.validation.OrderOfflineValidation;
import com.cfpamf.ms.mallorder.vo.CustInfoVo;
import com.cfpamf.ms.mallorder.vo.OrderOfflineInfoVO;
import com.cfpamf.ms.mallorder.vo.kingdee.KingdeeCustomerVo;
import com.cfpamf.ms.mallshop.api.StoreFeignClient;
import com.cfpamf.ms.mallshop.resp.Store;
import com.cfpamf.ms.mallshop.resp.Vendor;
import com.cfpamf.ms.mallsystem.vo.Admin;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.exception.MallException;
import com.slodon.bbc.core.response.JsonResult;
import com.xxl.job.core.context.XxlJobHelper;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class OrderOfflineServiceImpl extends BaseRepoServiceImpl<OrderOfflineMapper, OrderOfflinePO>
		implements OrderOfflineService {

	@Resource
	private OrderOfflineMapper orderOfflineMapper;

	@Resource
	private OrderOfflineService orderOfflineService;

	@Resource
	private IOrderOfflineExtendService orderOfflineExtendService;

	@Autowired
	private OrderPayModel orderPayModel;

	@Autowired
	private IOrderService orderService;

	@Autowired
	private IOrderPayService orderPayService;

	@Autowired
	private IOrderExtendService orderExtendService;

	@Autowired
	private OrderCreateHelper orderCreateHelper;

	@Resource
	private OrderLogModel orderLogModel;

	@Resource
	private OrderModel orderModel;

	@Autowired
	private BizConfigIntegration bizConfigIntegration;

	@Autowired
	private CustomerIntegration customerIntegration;

	@Autowired
	private IOrderProductService orderProductService;

	@Value("${order.offline.delivery.name:admin}")
	private String orderOfflineDeliveryName;

	@Value("${order.offline.delivery.mobile:18890909090}")
	private String orderOfflineDeliveryMobile;

	@Value("${spring.profiles.active}")
	private String profilesActive;

	@Resource
	private WmsIntegration wmsIntegration;

	@Autowired
	private BmsDingTalkFacade bmsDingTalkFacade;
	@Autowired
	private BmsIntegration bmsIntegration;

	@Autowired
	private StoreFeignClient storeFeignClient;

	@Autowired
	private MallExternalAdapterFacade mallExternalAdapterFacade;


	@Override
	public List<OrderOfflinePO> queryOrderOfflineList(String paySn) {
		if (ObjectUtils.isEmpty(paySn)) {
			return null;
		}
		LambdaQueryWrapper<OrderOfflinePO> orderOfflineQuery = new LambdaQueryWrapper<>();
		orderOfflineQuery.eq(OrderOfflinePO::getPaySn, paySn)
				.eq(OrderOfflinePO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
		return orderOfflineMapper.selectList(orderOfflineQuery);
	}

	@Override
	public CustInfoVo queryCustInfo(String jobNumber) {
		// 根据工号查询用户信息
		UserVo userVo = bizConfigIntegration.getDefaultUserByUserCode(jobNumber);
		if (Objects.isNull(userVo)){
			return null;
		}

		// 根据身份证查客户信息
		CustBaseInfoVo custBaseInfoVo = customerIntegration.baseInfoByIdNo(userVo.getUserIdNo());
		if (Objects.isNull(custBaseInfoVo)){
			return  null;
		}

		// 组装返回参数
		CustInfoVo custInfoVo = new CustInfoVo();
		custInfoVo.setUserCode(userVo.getUserCode());
		custInfoVo.setUserName(userVo.getUserName());
		custInfoVo.setIdNo(custBaseInfoVo.getIdNo());
		custInfoVo.setMobile(custBaseInfoVo.getMobile());
		if (Objects.nonNull(custBaseInfoVo.getCustDetail())){
			custInfoVo.setBranchCode(custBaseInfoVo.getCustDetail().getLoanBranch());
			custInfoVo.setManager(custBaseInfoVo.getCustDetail().getLoanManager());

			// 客户经理信息
			UserVo managerInfo = customerIntegration.getUserInfoByUserCode(
					custBaseInfoVo.getCustDetail().getLoanManager(), custBaseInfoVo.getCustDetail().getLoanBranch());
			custInfoVo.setBranchName(managerInfo.getBranchName());
			custInfoVo.setManagerName(managerInfo.getUserName());
		}

		return custInfoVo;
	}

	@Override
	@Transactional
	public void saveBatch(List<OrderOfflinePO> list) {
		super.saveBatch(list);
	}

	@Override
	@Transactional
	public void payOrder(OrderPO orderPO) {
		OrderPayPO orderPay = orderPayService.getByPaySn(orderPO.getPaySn());
		BizAssertUtil.isTrue(OrderConst.API_PAY_STATE_1.equals(orderPay.getApiPayState()), "该支付单号已完成支付，请勿重复支付");

		log.info("【OrderOfflineServiceImpl payOrder】订单待支付：{}，开始处理后续流程。", orderPay.getPaySn());
		//订单支付状态处理,补录订单支付方式只能是协议支付
		orderPayModel.wxAlipayCallBack(OrderOfflineBuilder.buildPaymentNotifyVO(orderPay), null);
	}

	/**
	 * 线下订单手动发货
	 */
	@Override
	@Transactional
	public void deliveryOrder(String orderSn, Vendor vendor,String deliveryWarehouse,String deliveryWarehouseName) {
		OrderPO orderPO = orderService.getByOrderSn(orderSn);
		log.info("deliveryOrder orderPO ==== {}", JSONObject.toJSONString(orderPO));
		BizAssertUtil.isTrue(!OrderStatusEnum.isWaitDelivery(orderPO.getOrderState()),"该订单处于非待发货状态，不能进行发货");

		LambdaQueryWrapper<OrderProductPO> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(OrderProductPO::getOrderSn, orderPO.getOrderSn())
				.eq(OrderProductPO::getEnabledFlag, 1);

		if(Objects.equals(OrderStatusEnum.PART_DELIVERED.getValue(), orderPO.getOrderState())) {
			queryWrapper.eq(OrderProductPO::getDeliveryState,OrderProductDeliveryEnum.WAIT_DELIVERY);
		}
		List<OrderProductPO>  orderProductPOList = orderProductService.list(queryWrapper);
		List<Long> orderProductIds = orderProductPOList.stream().map(OrderProductPO::getOrderProductId).collect(Collectors.toList());
		List<Long> productIds = orderProductPOList.stream().map(OrderProductPO::getProductId).collect(Collectors.toList());

		OrderDeliveryReq deliveryReq = new OrderDeliveryReq();
		deliveryReq.setAllowNoLogistics(true);
		deliveryReq.setChannel(OrderCreateChannel.WEB);
		deliveryReq.setOrderSn(orderPO.getOrderSn());
		deliveryReq.setDeliverType(1);
		deliveryReq.setDeliverName(orderOfflineDeliveryName);
		deliveryReq.setDeliverMobile(orderOfflineDeliveryMobile);
		deliveryReq.setOrderProductIds(orderProductIds);
		deliveryReq.setOffLineOrder(Boolean.TRUE);
		deliveryReq.setDeliveryWarehouse(deliveryWarehouse);
		deliveryReq.setDeliveryWarehouseName(deliveryWarehouseName);
		deliveryReq.setProductIds(productIds);

		orderService.deliveryV2(deliveryReq, vendor);
	}

	@Override
	@GlobalTransactional
	@Deprecated
	public void manualSettlement(Vendor vendor, OrderOfflineManualSettlementDTO orderOfflineManualSettlement) {
		log.info("【manualSettlement】orderOfflineManualSettlement:{}", orderOfflineManualSettlement);
		OrderOfflineValidation.isValidOfflineManualSettlementInfo(orderOfflineManualSettlement);
		List<OrderPO> data = orderService.listByPaySn(orderOfflineManualSettlement.getPaySn());
		OrderOfflineValidation.isValidOfflineManualSettlementOrderInfo(data);
		for (OrderPO orderPO : data) {
			// 订单手动结算完成，拒绝订单继续更新
			OrderOfflineValidation.isValidOfflineManualSettlementFinish(orderPO);
			OrderOfflineValidation.isValidOfflineManualSettlement(orderPO);
		}

		Set<String> orderSns = data.stream().map(OrderPO::getOrderSn).collect(Collectors.toSet());
		OrderStatusEnum orderPreState = OrderStatusEnum.TRADE_SUCCESS;
		if (OrderOfflineSettlementEnum.isTransactionFinish(orderOfflineManualSettlement.getOrderOfflineSettlement())) {
			log.info("【manualSettlement】////////////////////手动结算，交易完成&&订单类型为线下订单，处理////////////////////////paySn:{}",
					orderOfflineManualSettlement.getPaySn());
			OrderPayPO orderPay = orderPayService.getByPaySn(orderOfflineManualSettlement.getPaySn());
			if (!OrderConst.API_PAY_STATE_1.equals(orderPay.getApiPayState())) {
				log.info("【manualSettlement】订单待支付：{}，开始处理后续流程。", orderPay.getPaySn());
				// 提交，交易完成，订单和支付状态处理
				orderPayModel.wxAlipayCallBack(OrderOfflineBuilder.buildPaymentNotifyVO(orderPay), null);
				orderPreState = OrderStatusEnum.WAIT_PAY;
			}
		}
		log.info("【manualSettlement】////////////////////手动结算，!（交易完成&&订单类型为线下订单）处理////////////////////////paySn:{}",
				orderOfflineManualSettlement.getPaySn());
		// 更新订单信息，待支付
		LambdaUpdateWrapper<OrderPO> updateWrapper = new LambdaUpdateWrapper<>();
		updateWrapper.eq(OrderPO::getPaySn, orderOfflineManualSettlement.getPaySn());
		updateWrapper.inSql(OrderPO::getOrderId, String.format(
				"SELECT order_id FROM ( SELECT order_id FROM bz_order bo1 WHERE pay_sn = %s AND NOT EXISTS( "
						+ "SELECT order_id FROM bz_order bo2 WHERE order_state IN(0,50,40) "
						+ "AND order_type=6 AND  bo1.order_id=bo2.order_id ))AS order_temp ",
				orderOfflineManualSettlement.getPaySn()));
		OrderPO updateOrderPO = OrderOfflineBuilder.buildOrderPO(orderOfflineManualSettlement);
		boolean status = orderService.update(updateOrderPO, updateWrapper);
		BizAssertUtil.isTrue(!status, "抱歉，线下补录订单，手动结算失败！");
		// 更新订单备注
		orderExtendService.updateOrderRemark(orderSns, orderOfflineManualSettlement.getRemark());
		// 重新查询订单对象
		data = orderService.listByPaySn(orderOfflineManualSettlement.getPaySn());
		OrderOperationEventEnum manualSettlementEvent = OrderOperationEventEnum.MANUAL_SETTLEMENT_EVENT;
		for (OrderPO order : data) {// 遍历当前所有订单
			// -bz_order_log 记录订单日志
			orderLogModel.insertOrderLog(OrderConst.LOG_ROLE_VENDOR, vendor.getVendorId(), vendor.getVendorName(),
					order.getOrderSn(), orderPreState.getValue(), updateOrderPO.getOrderState(),
					LoanStatusEnum.DEFAULT.getValue(), "商家手动结算订单", OrderCreateChannel.SELLER_WEB);
			// 订单状态为交易成果，且订单类型为线下订单
			if (OrderStatusEnum.isTradeSuccess(order.getOrderState())
					&& OrderTypeEnum.isOffline(order.getOrderType())) {
				log.info(
						"【manualSettlement】////////////////////手动结算，发送手动结算MQ消息////////////////////////orderSn:{} paySn:{}",
						order.getOrderSn(), order.getPaySn());
				OrderOperationEventNotifyDTO message = new OrderOperationEventNotifyDTO(manualSettlementEvent.getCode(),
						manualSettlementEvent.getDesc(), order.getOrderSn());
				orderCreateHelper.sendMqEventMessageByTransactionCommit(message,
						RabbitMqEventEnum.EXCHANGE_ORDER_OPERATION.getExchang());
			}
		}

		/////////////////////////////创建线下订单额外信息/////////////////////////////
		if (!CollectionUtils.isEmpty(orderOfflineManualSettlement.getOrderOfflineList())){
			List<OrderOfflinePO> orderOfflines = OrderOfflineBuilder.buildOrderOfflinePOList(
					orderOfflineManualSettlement.getPaySn(), vendor.getVendorName(), orderOfflineManualSettlement.getOrderOfflineList());
			this.saveBatch(orderOfflines);
		}

	}

	@Override
	@Transactional
	public void manualSettlementV2(Vendor vendor, OrderOfflineManualSettlementDTO orderOfflineManualSettlement) {
		log.info("【manualSettlementV2】orderOfflineManualSettlement:{}", orderOfflineManualSettlement);
		OrderOfflineValidation.isValidOfflineManualSettlementInfo(orderOfflineManualSettlement);
		List<OrderPO> data = orderService.listByPaySn(orderOfflineManualSettlement.getPaySn());
		OrderOfflineValidation.isValidOfflineManualSettlementOrderInfo(data);
		for (OrderPO orderPO : data) {
			// 订单手动结算完成，拒绝订单继续更新
			OrderOfflineValidation.isValidOfflineManualSettlementFinish(orderPO);
			OrderOfflineValidation.isValidOfflineManualSettlement(orderPO);
		}

		Set<String> orderSns = data.stream().map(OrderPO::getOrderSn).collect(Collectors.toSet());
		OrderStatusEnum orderPreState = OrderStatusEnum.valueOf(data.get(0).getOrderState());
		if (OrderOfflineSettlementEnum.isTransactionFinish(orderOfflineManualSettlement.getOrderOfflineSettlement())) {
			BizAssertUtil.isTrue(!OrderStatusEnum.WAIT_RECEIPT.getValue().equals(orderPreState.getValue()),"待签收状态才能结算为交易成功");
		}
		// 更新订单信息
		LambdaUpdateWrapper<OrderPO> updateWrapper = new LambdaUpdateWrapper<>();
		updateWrapper.eq(OrderPO::getPaySn, orderOfflineManualSettlement.getPaySn());
		updateWrapper.inSql(OrderPO::getOrderId, String.format(
				"SELECT order_id FROM ( SELECT order_id FROM bz_order bo1 WHERE pay_sn = %s AND NOT EXISTS( "
						+ "SELECT order_id FROM bz_order bo2 WHERE order_state IN(0,50,40) "
						+ "AND order_type=6 AND  bo1.order_id=bo2.order_id ))AS order_temp ",
				orderOfflineManualSettlement.getPaySn()));
		OrderPO updateOrderPO = OrderOfflineBuilder.buildOrderPO(orderOfflineManualSettlement);
		boolean status = orderService.update(updateOrderPO, updateWrapper);
		BizAssertUtil.isTrue(!status, "抱歉，线下补录订单，手动结算失败！");
		// 更新订单备注
		orderExtendService.updateOrderRemark(orderSns, orderOfflineManualSettlement.getRemark());
		// 重新查询订单对象
		data = orderService.listByPaySn(orderOfflineManualSettlement.getPaySn());
		OrderOperationEventEnum manualSettlementEvent = OrderOperationEventEnum.MANUAL_SETTLEMENT_EVENT;
		for (OrderPO order : data) {// 遍历当前所有订单
			// -bz_order_log 记录订单日志
			orderLogModel.insertOrderLog(OrderConst.LOG_ROLE_VENDOR, vendor.getVendorId(), vendor.getVendorName(),
					order.getOrderSn(), orderPreState.getValue(), updateOrderPO.getOrderState(),
					LoanStatusEnum.DEFAULT.getValue(), "商家手动结算订单", OrderCreateChannel.SELLER_WEB);
			// 订单状态为交易成果，且订单类型为线下订单
			if (OrderStatusEnum.isTradeSuccess(order.getOrderState())
					&& OrderTypeEnum.isOffline(order.getOrderType())) {
				log.info(
						"【manualSettlementV2】////////////////////手动结算，发送手动结算MQ消息////////////////////////orderSn:{} paySn:{}",
						order.getOrderSn(), order.getPaySn());
				OrderOperationEventNotifyDTO message = new OrderOperationEventNotifyDTO(manualSettlementEvent.getCode(),
						manualSettlementEvent.getDesc(), order.getOrderSn());
				orderCreateHelper.sendMqEventMessageByTransactionCommit(message,
						RabbitMqEventEnum.EXCHANGE_ORDER_OPERATION.getExchang());
			}
		}

		/////////////////////////////创建线下订单额外信息/////////////////////////////
		if (!CollectionUtils.isEmpty(orderOfflineManualSettlement.getOrderOfflineList())){
			List<OrderOfflinePO> orderOfflines = OrderOfflineBuilder.buildOrderOfflinePOList(
					orderOfflineManualSettlement.getPaySn(), vendor.getVendorName(), orderOfflineManualSettlement.getOrderOfflineList());
			this.saveOrUpdateBatch(orderOfflines);
		}

	}


	private void updateOrSaveOrderOfflinePO(String paySn, String vendorName, List<OrderOfflineDTO> orderOfflineList) {
		if(!CollectionUtils.isEmpty(orderOfflineList)) {
			OrderOfflineValidation.validOrderOfflineList(orderOfflineList);
			List<OrderOfflinePO> orderOfflines = OrderOfflineBuilder.buildOrderOfflinePOList(
					paySn, vendorName, orderOfflineList);
			orderOfflineService.saveOrUpdateBatch(orderOfflines);
			//删除
			List<Long> deleteOrderOfflineIds = orderOfflines.stream().filter(x->x.getEnabledFlag().equals(OrderConst.ENABLED_FLAG_N)).map(OrderOfflinePO::getId).collect(Collectors.toList());
			orderOfflineService.removeByIds(deleteOrderOfflineIds);
		}
	}

	@Override
	public OrderOfflineManualDTO getManualInfo(String orderSn) {
		OrderOfflineManualDTO orderOfflineManualDTO = new OrderOfflineManualDTO();
		OrderPO orderPO = orderService.getByOrderSn(orderSn);
		OrderExtendPO extendPO = orderExtendService.getOrderExtendByOrderSn(orderSn);
		BizAssertUtil.notNull(orderPO,"该订单不存在");
		orderOfflineManualDTO.setOrderSn(orderSn);
		orderOfflineManualDTO.setOrderType(orderPO.getOrderType());
		orderOfflineManualDTO.setPaymentTag(orderPO.getPaymentTag());
		orderOfflineManualDTO.setCustomerContract(extendPO.getCustomerContract());
		orderOfflineManualDTO.setSignInImageUrl(extendPO.getSignInImageUrl());
		orderOfflineManualDTO.setInvoiceStatus(extendPO.getInvoiceStatus());
		orderOfflineManualDTO.setInvoiceAmount(extendPO.getInvoiceAmount());
		orderOfflineManualDTO.setInvoiceTime(extendPO.getInvoiceTime());

		List<OrderOfflineDTO> orderOfflineDTOList = new ArrayList<>();
		List<OrderOfflinePO> orderOfflinePOList = queryOrderOfflineList(orderPO.getPaySn());

		if(!CollectionUtils.isEmpty(orderOfflinePOList)) {
			for(OrderOfflinePO offlinePO : orderOfflinePOList) {
				OrderOfflineDTO orderOfflineDTO = new OrderOfflineDTO();
				BeanUtils.copyProperties(offlinePO, orderOfflineDTO);
				orderOfflineDTOList.add(orderOfflineDTO);
			}
		}
		orderOfflineManualDTO.setOrderOfflineList(orderOfflineDTOList);

		OrderOfflineExtendPO orderOfflineExtendPO = orderOfflineExtendService.getOrderOfflineExtendByOrderSn(orderSn);
		if(Objects.nonNull(orderOfflineExtendPO)) {
			orderOfflineManualDTO.setAccountPeriodDays(orderOfflineExtendPO.getAccountPeriodDays());
			orderOfflineManualDTO.setOverdueTime(orderOfflineExtendPO.getOverdueTime());
			orderOfflineManualDTO.setBuyerSupplierCode(orderOfflineExtendPO.getBuyerSupplierCode());
			orderOfflineManualDTO.setBuyerSupplierName(orderOfflineExtendPO.getBuyerSupplierName());
		}
		return orderOfflineManualDTO;
	}

	@Override
	@Transactional
	public void manualPay(OrderOfflineManualDTO orderOfflineManualDTO,Vendor vendor) {
		OrderPO orderPO = orderService.getByOrderSn(orderOfflineManualDTO.getOrderSn());

		List<OrderPO> data = orderService.listByPaySn(orderPO.getPaySn());
		for (OrderPO order : data) {
			//数据校验
			OrderOfflineValidation.validOfflineOrder(order,orderOfflineManualDTO.getPaymentTag());
		}

		updateOrSaveOrderOfflinePO(orderPO.getPaySn(),vendor.getVendorName(),orderOfflineManualDTO.getOrderOfflineList());

		//将预占改成线下
		LambdaUpdateWrapper<OrderPO> updateWrapper = new LambdaUpdateWrapper<>();
		updateWrapper.eq(OrderPO::getPaySn, orderPO.getPaySn());
		updateWrapper.set(OrderPO::getOrderType, OrderTypeEnum.ORDER_TYPE_6.getValue());
		if(!orderOfflineManualDTO.getPaymentTag().equals(orderPO.getPaymentTag())) {
			updateWrapper.set(OrderPO::getPaymentTag, orderOfflineManualDTO.getPaymentTag());
		}
		orderService.update(updateWrapper);

		orderLogModel.insertOrderLog(OrderConst.LOG_ROLE_VENDOR, Long.valueOf(vendor.getVendorId()),
				vendor.getVendorName(), orderPO.getOrderSn(), orderPO.getOrderState(), OrderConst.ORDER_STATE_20,
				LoanStatusEnum.APPLY_SUCCESS.getValue(), "线下补录订单手动支付",
				OrderCreateChannel.H5);

		//协议支付
		orderOfflineService.payOrder(orderPO);
	}

	@Override
	@Transactional
	public void manualPayAdmin(OrderOfflineManualDTO orderOfflineManualDTO, Admin admin) {
		OrderPO orderPO = orderService.getByOrderSn(orderOfflineManualDTO.getOrderSn());

		List<OrderPO> data = orderService.listByPaySn(orderPO.getPaySn());
		for (OrderPO order : data) {
			//数据校验
			OrderOfflineValidation.validOfflineOrder(order,orderOfflineManualDTO.getPaymentTag());
		}

		updateOrSaveOrderOfflinePO(orderPO.getPaySn(),admin.getAdminName(),orderOfflineManualDTO.getOrderOfflineList());

		//将预占改成线下
		LambdaUpdateWrapper<OrderPO> updateWrapper = new LambdaUpdateWrapper<>();
		updateWrapper.eq(OrderPO::getPaySn, orderPO.getPaySn());
		updateWrapper.set(OrderPO::getOrderType, OrderTypeEnum.ORDER_TYPE_6.getValue());
		if(!orderOfflineManualDTO.getPaymentTag().equals(orderPO.getPaymentTag())) {
			updateWrapper.set(OrderPO::getPaymentTag, orderOfflineManualDTO.getPaymentTag());
		}
		orderService.update(updateWrapper);

		orderLogModel.insertOrderLog(OrderConst.LOG_ROLE_VENDOR, Long.valueOf(admin.getAdminId()),
				admin.getAdminName(), orderPO.getOrderSn(), orderPO.getOrderState(), OrderConst.ORDER_STATE_20,
				LoanStatusEnum.APPLY_SUCCESS.getValue(), "线下补录订单运营端手动支付",
				OrderCreateChannel.H5);

		//协议支付
		orderOfflineService.payOrder(orderPO);
	}

	@Override
	public void manualDelivery(OrderOfflineManualDTO orderOfflineManualDTO, Vendor vendor) {

		OrderPO orderPO = orderService.getByOrderSn(orderOfflineManualDTO.getOrderSn());
		//数据校验
		OrderOfflineValidation.validOfflineOrder(orderPO,orderOfflineManualDTO.getPaymentTag());
		BizAssertUtil.isTrue(!OrderTypeEnum.isOfflineAll(orderPO.getOrderType()),"该订单非线下补录订单，不能进行手动发货");

		updateOrSaveOrderOfflinePO(orderPO.getPaySn(),vendor.getVendorName(),orderOfflineManualDTO.getOrderOfflineList());


		//更新收款标签信息
		if(!orderOfflineManualDTO.getPaymentTag().equals(orderPO.getPaymentTag())) {
			LambdaUpdateWrapper<OrderPO> updateWrapper = new LambdaUpdateWrapper<>();
			updateWrapper.eq(OrderPO::getPaySn, orderPO.getPaySn());
			updateWrapper.set(OrderPO::getPaymentTag, orderOfflineManualDTO.getPaymentTag());
			orderService.update(updateWrapper);
		}

		if(StringUtils.isNotEmpty(orderOfflineManualDTO.getCustomerContract())
				|| StringUtils.isNotEmpty(orderOfflineManualDTO.getSignInImageUrl())) {
			LambdaUpdateWrapper<OrderExtendPO> extendPOLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
			extendPOLambdaUpdateWrapper.eq(OrderExtendPO::getOrderSn, orderPO.getOrderSn())
					.set(OrderExtendPO::getCustomerContract, orderOfflineManualDTO.getCustomerContract())
					.set(OrderExtendPO::getSignInImageUrl, orderOfflineManualDTO.getSignInImageUrl());
			orderExtendService.update(extendPOLambdaUpdateWrapper);
		}

		orderOfflineService.deliveryOrder(orderOfflineManualDTO.getOrderSn(),  vendor, orderOfflineManualDTO.getDeliveryWarehouse(), orderOfflineManualDTO.getDeliveryWarehouseName());
	}

	@Override
	@Transactional
	public void manualReceive(OrderOfflineManualDTO orderOfflineManualDTO, Vendor vendor) {
		OrderPO orderPO = orderService.getByOrderSn(orderOfflineManualDTO.getOrderSn());
		//数据校验
		OrderOfflineValidation.validOfflineOrder(orderPO,orderOfflineManualDTO.getPaymentTag());
		if(PaymentTagEnum.isPayment(orderOfflineManualDTO.getPaymentTag())) {
			LambdaQueryWrapper<OrderOfflinePO> offlinePOLambdaQueryWrapper = new LambdaQueryWrapper<>();
			offlinePOLambdaQueryWrapper.eq(OrderOfflinePO::getPaySn, orderPO.getPaySn())
					.eq(OrderOfflinePO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);

			List<OrderOfflineDTO> enabledOrderOfflineList =  orderOfflineManualDTO.getOrderOfflineList().stream().filter(x->x.getEnabledFlag().equals(1)).collect(Collectors.toList());

			BizAssertUtil.isTrue(CollectionUtils.isEmpty(this.list(offlinePOLambdaQueryWrapper))
					&& CollectionUtils.isEmpty(enabledOrderOfflineList),
					"抱歉，请填写回执单信息！");
		}

		updateOrSaveOrderOfflinePO(orderPO.getPaySn(),vendor.getVendorName(),orderOfflineManualDTO.getOrderOfflineList());


		//更新收款标签信息
		if(!orderOfflineManualDTO.getPaymentTag().equals(orderPO.getPaymentTag())) {
			LambdaUpdateWrapper<OrderPO> updateWrapper = new LambdaUpdateWrapper<>();
			updateWrapper.eq(OrderPO::getPaySn, orderPO.getPaySn());
			updateWrapper.set(OrderPO::getPaymentTag, orderOfflineManualDTO.getPaymentTag());
			orderService.update(updateWrapper);
		}

		// 确认收货
		orderModel.receiveOrder(orderPO, OrderConst.LOG_ROLE_VENDOR, vendor.getVendorId(),
				vendor.getVendorName(), "线下补录订单手动签收", OrderCreateChannel.WEB);
	}

	@Override
	@Transactional
	public void infoSupplement(OrderOfflineManualDTO orderOfflineManualDTO, Vendor vendor) {
		String content = "资料补充";

		OrderPO orderPO = orderService.getByOrderSn(orderOfflineManualDTO.getOrderSn());
		OrderExtendPO orderExtendPO = orderExtendService.getOrderExtendByOrderSn(orderOfflineManualDTO.getOrderSn());

		BigDecimal receiptAmount = BigDecimal.ZERO;

		//根据收款便签判断，是否删除原有回执单信息
		if(PaymentTagEnum.isPayment(orderOfflineManualDTO.getPaymentTag())) {
			LambdaQueryWrapper<OrderOfflinePO> offlinePOLambdaQueryWrapper = new LambdaQueryWrapper<>();
			offlinePOLambdaQueryWrapper.eq(OrderOfflinePO::getPaySn, orderPO.getPaySn())
					.eq(OrderOfflinePO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);

			List<OrderOfflineDTO> enabledOrderOfflineList =  orderOfflineManualDTO.getOrderOfflineList().stream().filter(x->x.getEnabledFlag().equals(1)).collect(Collectors.toList());

			BizAssertUtil.isTrue(CollectionUtils.isEmpty(this.list(offlinePOLambdaQueryWrapper))
							&& CollectionUtils.isEmpty(enabledOrderOfflineList),
					"抱歉，请填写回执单信息！");


			//更新回执单信息
			if(!CollectionUtils.isEmpty(orderOfflineManualDTO.getOrderOfflineList())) {
				orderOfflineManualDTO.getOrderOfflineList().forEach( x-> {
					if(OrderOfflineReceiptTypeEnum.DEBT_OFFSE.getCode().equals(x.getReceiptType())) {
						BizAssertUtil.isTrue(StringUtils.isEmpty(x.getSupplierCode()) || StringUtils.isEmpty(x.getSupplierName()),"请填写债权供应商");
					}
				});
				updateOrSaveOrderOfflinePO(orderPO.getPaySn(),vendor.getVendorName(),orderOfflineManualDTO.getOrderOfflineList());
				receiptAmount = orderOfflineManualDTO.getOrderOfflineList().stream().filter(x -> x.getId() != null && x.getEnabledFlag() == 1 )
						.map(OrderOfflineDTO::getReceiptAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
			}

		} else {
			//未收款时，收款信息进行软删除
			LambdaUpdateWrapper<OrderOfflinePO> offlinePOLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
			offlinePOLambdaUpdateWrapper.set(OrderOfflinePO::getEnabledFlag, OrderConst.ENABLED_FLAG_N)
					.eq(OrderOfflinePO::getPaySn, orderPO.getPaySn());
			this.update(offlinePOLambdaUpdateWrapper);
		}

		String paymentTagRemark = "收款标签（「原标签」" + PaymentTagEnum.valueOf(orderPO.getPaymentTag()).getDesc()
				+ "—>「新标签」"+PaymentTagEnum.valueOf(orderOfflineManualDTO.getPaymentTag()).getDesc()
				+ "），收款金额（" + receiptAmount + "）";
		//添加轨迹
		orderLogModel.insertOrderLog(OrderConst.LOG_ROLE_VENDOR, vendor.getVendorId(),
				vendor.getVendorName(), orderPO.getOrderSn(), orderPO.getOrderState(), orderPO.getOrderState(),
				LoanStatusEnum.APPLY_SUCCESS.getValue(), content, OrderCreateChannel.WEB, paymentTagRemark);


		if(StringUtils.isNotEmpty(orderOfflineManualDTO.getCustomerContract())
				|| StringUtils.isNotEmpty(orderOfflineManualDTO.getSignInImageUrl())) {
			LambdaUpdateWrapper<OrderExtendPO> extendPOLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
			extendPOLambdaUpdateWrapper.eq(OrderExtendPO::getOrderSn, orderPO.getOrderSn())
					.set(OrderExtendPO::getCustomerContract, orderOfflineManualDTO.getCustomerContract())
					.set(OrderExtendPO::getSignInImageUrl, orderOfflineManualDTO.getSignInImageUrl());
			orderExtendService.update(extendPOLambdaUpdateWrapper);
		}
		//开票标签
		if(OrderConst.INVOICE_STATE_1 == orderOfflineManualDTO.getInvoiceStatus()) {
			LambdaUpdateWrapper<OrderExtendPO> extendPOLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
			extendPOLambdaUpdateWrapper.eq(OrderExtendPO::getOrderSn, orderPO.getOrderSn())
					.set(OrderExtendPO::getInvoiceAmount, orderOfflineManualDTO.getInvoiceAmount())
					.set(OrderExtendPO::getInvoiceTime, orderOfflineManualDTO.getInvoiceTime())
					.set(OrderExtendPO::getInvoiceStatus, OrderConst.INVOICE_STATE_1);
			orderExtendService.update(extendPOLambdaUpdateWrapper);

			//开票标签（「原标签」未开票—>「新标签」已开票），开票金额（「原金额」891.00—>「新金额」1190.00）
			String invoiceRemark = "开票标签（「原标签」" + InvoiceStatusEnum.valueOf(orderExtendPO.getInvoiceStatus()).getValue()
					+ "—>「新标签」" + InvoiceStatusEnum.valueOf(orderOfflineManualDTO.getInvoiceStatus()).getValue()
					+ "），开票金额（「原金额」"
					+ orderExtendPO.getInvoiceAmount() + "—>「新金额」"
					+ orderOfflineManualDTO.getInvoiceAmount() + "）";
			//添加轨迹
			orderLogModel.insertOrderLog(OrderConst.LOG_ROLE_VENDOR, vendor.getVendorId(),
					vendor.getVendorName(), orderPO.getOrderSn(), orderPO.getOrderState(), orderPO.getOrderState(),
					LoanStatusEnum.APPLY_SUCCESS.getValue(), content, OrderCreateChannel.WEB, invoiceRemark);
		}



		//更新收款标签信息
		if(!orderOfflineManualDTO.getPaymentTag().equals(orderPO.getPaymentTag())) {
			LambdaUpdateWrapper<OrderPO> updateWrapper = new LambdaUpdateWrapper<>();
			updateWrapper.eq(OrderPO::getPaySn, orderPO.getPaySn());
			updateWrapper.set(OrderPO::getPaymentTag, orderOfflineManualDTO.getPaymentTag());
			orderService.update(updateWrapper);
		}




		OrderOfflineExtendPO orderOfflineExtendPO = orderOfflineExtendService.getOrderOfflineExtendByOrderSn(orderPO.getOrderSn());

		//账期天数、回款截止日期
		LambdaUpdateWrapper<OrderOfflineExtendPO> offlineExtendPOLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
		offlineExtendPOLambdaUpdateWrapper.eq(OrderOfflineExtendPO::getOrderSn,orderPO.getOrderSn())
				.set(OrderOfflineExtendPO::getAccountPeriodDays, orderOfflineManualDTO.getAccountPeriodDays())
				.set(OrderOfflineExtendPO::getOverdueTime, orderOfflineManualDTO.getOverdueTime());

		if(PaymentTagEnum.RECEIVED.getValue().equals(orderOfflineManualDTO.getPaymentTag())) {
			offlineExtendPOLambdaUpdateWrapper.set(OrderOfflineExtendPO::getOverdueFlag, OrderConst.OVERDUE_FLAG_0);
		} else {
			//需要更新是否逾期，及逾期天数；如果逾期，则更新逾期天数，如果未逾期，则不更新逾期天数
			if(Objects.nonNull(orderOfflineManualDTO.getOverdueTime())) {
				if(DateUtil.isEndDateLessBeginDateByDay(new Date(), orderOfflineManualDTO.getOverdueTime())) {
					int days = DateUtil.days(DateUtil.format(orderOfflineManualDTO.getOverdueTime(), DateUtil.FORMAT_DATE),
							DateUtil.format(new Date(), DateUtil.FORMAT_DATE), DateUtil.FORMAT_DATE);
					offlineExtendPOLambdaUpdateWrapper.set(OrderOfflineExtendPO::getOverdueFlag, OrderConst.OVERDUE_FLAG_1)
							.set(OrderOfflineExtendPO::getOverdueDays, days);
				} else {
					offlineExtendPOLambdaUpdateWrapper.set(OrderOfflineExtendPO::getOverdueFlag, OrderConst.OVERDUE_FLAG_0);
				}
			}
		}



		String originOverTime = orderOfflineExtendPO.getOverdueTime() == null ? "无" : DateUtil.getDateString(orderOfflineExtendPO.getOverdueTime(), DateUtil.FORMAT_TIME);
		String currentOverTime = orderOfflineManualDTO.getOverdueTime() == null ? "无" : DateUtil.getDateString(orderOfflineManualDTO.getOverdueTime(), DateUtil.FORMAT_TIME);
		String accountRemark = "账期天数（「原账期」" + orderOfflineExtendPO.getAccountPeriodDays() + "—>「新账期」"
				+ orderOfflineManualDTO.getAccountPeriodDays() + "），"
				+ "回款截止日期（「原日期」" + originOverTime
				+ "—>「新日期」" + currentOverTime + "）";
		//添加轨迹
		orderLogModel.insertOrderLog(OrderConst.LOG_ROLE_VENDOR, vendor.getVendorId(),
				vendor.getVendorName(), orderPO.getOrderSn(), orderPO.getOrderState(), orderPO.getOrderState(),
				LoanStatusEnum.APPLY_SUCCESS.getValue(), content, OrderCreateChannel.WEB, accountRemark);

		if(StringUtils.isNotEmpty(orderOfflineManualDTO.getBuyerSupplierCode())
				&& !orderOfflineManualDTO.getBuyerSupplierCode().equals(orderOfflineExtendPO.getBuyerSupplierCode())) {
			String buyerSupplierCodeRemark = "供应商标签(" + orderOfflineExtendPO.getBuyerSupplierCode() + "->" + orderOfflineManualDTO.getBuyerSupplierCode() + "）";
			offlineExtendPOLambdaUpdateWrapper.set(OrderOfflineExtendPO::getBuyerSupplierCode, orderOfflineManualDTO.getBuyerSupplierCode())
					.set(OrderOfflineExtendPO::getBuyerSupplierName, orderOfflineManualDTO.getBuyerSupplierName());
			//添加轨迹
			orderLogModel.insertOrderLog(OrderConst.LOG_ROLE_VENDOR, vendor.getVendorId(),
					vendor.getVendorName(), orderPO.getOrderSn(), orderPO.getOrderState(), orderPO.getOrderState(),
					LoanStatusEnum.APPLY_SUCCESS.getValue(), content, OrderCreateChannel.WEB, buyerSupplierCodeRemark);
		}

		orderOfflineExtendService.update(offlineExtendPOLambdaUpdateWrapper);


	}
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void infoSupplementAdmin(OrderOfflineManualDTO orderOfflineManualDTO, Admin admin) {
		String content = "资料补充";

		OrderPO orderPO = orderService.getByOrderSn(orderOfflineManualDTO.getOrderSn());
		OrderExtendPO orderExtendPO = orderExtendService.getOrderExtendByOrderSn(orderOfflineManualDTO.getOrderSn());
		BigDecimal receiptAmount = BigDecimal.ZERO;

		if(PaymentTagEnum.isPayment(orderOfflineManualDTO.getPaymentTag())) {
			LambdaQueryWrapper<OrderOfflinePO> offlinePOLambdaQueryWrapper = new LambdaQueryWrapper<>();
			offlinePOLambdaQueryWrapper.eq(OrderOfflinePO::getPaySn, orderPO.getPaySn())
					.eq(OrderOfflinePO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);

			List<OrderOfflineDTO> enabledOrderOfflineList =  orderOfflineManualDTO.getOrderOfflineList().stream().filter(x->x.getEnabledFlag().equals(1)).collect(Collectors.toList());

			BizAssertUtil.isTrue(CollectionUtils.isEmpty(this.list(offlinePOLambdaQueryWrapper))
							&& CollectionUtils.isEmpty(enabledOrderOfflineList),
					"抱歉，请填写回执单信息！");



			//更新回执单信息
			if(!CollectionUtils.isEmpty(orderOfflineManualDTO.getOrderOfflineList())) {
				orderOfflineManualDTO.getOrderOfflineList().forEach( x-> {
					if(OrderOfflineReceiptTypeEnum.DEBT_OFFSE.getCode().equals(x.getReceiptType())) {
						BizAssertUtil.isTrue(StringUtils.isEmpty(x.getSupplierCode()) || StringUtils.isEmpty(x.getSupplierName()),"请填写债权供应商");
					}
				});
				updateOrSaveOrderOfflinePO(orderPO.getPaySn(),admin.getAdminName(),orderOfflineManualDTO.getOrderOfflineList());
				receiptAmount = orderOfflineManualDTO.getOrderOfflineList().stream().filter(x-> x.getId() != null && x.getEnabledFlag() == 1)
						.map(OrderOfflineDTO::getReceiptAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
			}


		} else {
			//未收款时，收款信息进行软删除
			LambdaUpdateWrapper<OrderOfflinePO> offlinePOLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
			offlinePOLambdaUpdateWrapper.set(OrderOfflinePO::getEnabledFlag, OrderConst.ENABLED_FLAG_N)
					.eq(OrderOfflinePO::getPaySn, orderPO.getPaySn());
			this.update(offlinePOLambdaUpdateWrapper);
		}
		String paymentTagRemark = "收款标签（「原标签」" + PaymentTagEnum.valueOf(orderPO.getPaymentTag()).getDesc()
				+ "—>「新标签」"+PaymentTagEnum.valueOf(orderOfflineManualDTO.getPaymentTag()).getDesc()
				+ "），收款金额（" + receiptAmount + "）";

		//添加轨迹
		orderLogModel.insertOrderLog(OrderConst.LOG_ROLE_ADMIN, Long.valueOf(admin.getAdminId()),
				admin.getAdminName(), orderPO.getOrderSn(), orderPO.getOrderState(), orderPO.getOrderState(),
				LoanStatusEnum.APPLY_SUCCESS.getValue(), content, OrderCreateChannel.WEB, paymentTagRemark);


		if(StringUtils.isNotEmpty(orderOfflineManualDTO.getCustomerContract())
				|| StringUtils.isNotEmpty(orderOfflineManualDTO.getSignInImageUrl())) {
			LambdaUpdateWrapper<OrderExtendPO> extendPOLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
			extendPOLambdaUpdateWrapper.eq(OrderExtendPO::getOrderSn, orderPO.getOrderSn())
					.set(OrderExtendPO::getCustomerContract, orderOfflineManualDTO.getCustomerContract())
					.set(OrderExtendPO::getSignInImageUrl, orderOfflineManualDTO.getSignInImageUrl());
			orderExtendService.update(extendPOLambdaUpdateWrapper);
		}
		//开票标签
		if(OrderConst.INVOICE_STATE_1 == orderOfflineManualDTO.getInvoiceStatus()) {
			LambdaUpdateWrapper<OrderExtendPO> extendPOLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
			extendPOLambdaUpdateWrapper.eq(OrderExtendPO::getOrderSn, orderPO.getOrderSn())
					.set(OrderExtendPO::getInvoiceAmount, orderOfflineManualDTO.getInvoiceAmount())
					.set(OrderExtendPO::getInvoiceTime, orderOfflineManualDTO.getInvoiceTime())
					.set(OrderExtendPO::getInvoiceStatus, OrderConst.INVOICE_STATE_1);
			orderExtendService.update(extendPOLambdaUpdateWrapper);

			//开票标签（「原标签」未开票—>「新标签」已开票），开票金额（「原金额」891.00—>「新金额」1190.00）
			String invoiceRemark = "开票标签（「原标签」" + InvoiceStatusEnum.valueOf(orderExtendPO.getInvoiceStatus()).getValue()
					+ "—>「新标签」" + InvoiceStatusEnum.valueOf(orderOfflineManualDTO.getInvoiceStatus()).getValue()
					+ "），开票金额（「原金额」"
					+ orderExtendPO.getInvoiceAmount() + "—>「新金额」"
					+ orderOfflineManualDTO.getInvoiceAmount() + "）";
			//添加轨迹
			orderLogModel.insertOrderLog(OrderConst.LOG_ROLE_VENDOR, Long.valueOf(admin.getAdminId()),
					admin.getAdminName(), orderPO.getOrderSn(), orderPO.getOrderState(), orderPO.getOrderState(),
					LoanStatusEnum.APPLY_SUCCESS.getValue(), content, OrderCreateChannel.WEB, invoiceRemark);
		}



		//更新收款标签信息
		if(!orderOfflineManualDTO.getPaymentTag().equals(orderPO.getPaymentTag())) {
			LambdaUpdateWrapper<OrderPO> updateWrapper = new LambdaUpdateWrapper<>();
			updateWrapper.eq(OrderPO::getPaySn, orderPO.getPaySn());
			updateWrapper.set(OrderPO::getPaymentTag, orderOfflineManualDTO.getPaymentTag());
			orderService.update(updateWrapper);
		}


		OrderOfflineExtendPO orderOfflineExtendPO = orderOfflineExtendService.getOrderOfflineExtendByOrderSn(orderPO.getOrderSn());

		//账期天数、回款截止日期
		LambdaUpdateWrapper<OrderOfflineExtendPO> offlineExtendPOLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
		offlineExtendPOLambdaUpdateWrapper.eq(OrderOfflineExtendPO::getOrderSn,orderPO.getOrderSn())
				.set(OrderOfflineExtendPO::getAccountPeriodDays, orderOfflineManualDTO.getAccountPeriodDays())
				.set(OrderOfflineExtendPO::getOverdueTime, orderOfflineManualDTO.getOverdueTime());

		if(PaymentTagEnum.RECEIVED.getValue().equals(orderOfflineManualDTO.getPaymentTag())) {
			offlineExtendPOLambdaUpdateWrapper.set(OrderOfflineExtendPO::getOverdueFlag, OrderConst.OVERDUE_FLAG_0);
		} else {
			//需要更新是否逾期，及逾期天数；如果逾期，则更新逾期天数，如果未逾期，则不更新逾期天数
			if(Objects.nonNull(orderOfflineManualDTO.getOverdueTime())) {
				if(DateUtil.isEndDateLessBeginDateByDay(new Date(), orderOfflineManualDTO.getOverdueTime())) {
					int days = DateUtil.days(DateUtil.format(orderOfflineManualDTO.getOverdueTime(), DateUtil.FORMAT_DATE),
							DateUtil.format(new Date(), DateUtil.FORMAT_DATE), DateUtil.FORMAT_DATE);
					offlineExtendPOLambdaUpdateWrapper.set(OrderOfflineExtendPO::getOverdueFlag, OrderConst.OVERDUE_FLAG_1)
							.set(OrderOfflineExtendPO::getOverdueDays, days);
				} else {
					offlineExtendPOLambdaUpdateWrapper.set(OrderOfflineExtendPO::getOverdueFlag, OrderConst.OVERDUE_FLAG_0);
				}
			}
		}



		String originOverTime = orderOfflineExtendPO.getOverdueTime() == null ? "无" : DateUtil.getDateString(orderOfflineExtendPO.getOverdueTime(), DateUtil.FORMAT_TIME);
		String currentOverTime = orderOfflineManualDTO.getOverdueTime() == null ? "无" : DateUtil.getDateString(orderOfflineManualDTO.getOverdueTime(), DateUtil.FORMAT_TIME);
		String accountRemark = "账期天数（「原账期」" + orderOfflineExtendPO.getAccountPeriodDays() + "—>「新账期」"
				+ orderOfflineManualDTO.getAccountPeriodDays() + "），"
				+ "回款截止日期（「原日期」" + originOverTime
				+ "—>「新日期」" + currentOverTime + "）";
		//添加轨迹
		orderLogModel.insertOrderLog(OrderConst.LOG_ROLE_ADMIN, Long.valueOf(admin.getAdminId()),
				admin.getAdminName(), orderPO.getOrderSn(), orderPO.getOrderState(), orderPO.getOrderState(),
				LoanStatusEnum.APPLY_SUCCESS.getValue(), content, OrderCreateChannel.WEB, accountRemark);

		if(StringUtils.isNotEmpty(orderOfflineManualDTO.getBuyerSupplierCode())
				&& !orderOfflineManualDTO.getBuyerSupplierCode().equals(orderOfflineExtendPO.getBuyerSupplierCode())) {
			String buyerSupplierCodeRemark = "供应商标签(" + orderOfflineExtendPO.getBuyerSupplierCode() + "->" + orderOfflineManualDTO.getBuyerSupplierCode() + "）";
			offlineExtendPOLambdaUpdateWrapper.set(OrderOfflineExtendPO::getBuyerSupplierCode, orderOfflineManualDTO.getBuyerSupplierCode())
					.set(OrderOfflineExtendPO::getBuyerSupplierName, orderOfflineManualDTO.getBuyerSupplierName());
			//添加轨迹
			orderLogModel.insertOrderLog(OrderConst.LOG_ROLE_ADMIN, Long.valueOf(admin.getAdminId()),
					admin.getAdminName(), orderPO.getOrderSn(), orderPO.getOrderState(), orderPO.getOrderState(),
					LoanStatusEnum.APPLY_SUCCESS.getValue(), content, OrderCreateChannel.WEB, buyerSupplierCodeRemark);
		}

		orderOfflineExtendService.update(offlineExtendPOLambdaUpdateWrapper);


	}

	@Override
	public Page<SiteVo> getSiteByPage(WmsSiteDTO wmsSiteDTO) {
		SiteQuery siteQuery = new SiteQuery();
		BeanUtils.copyProperties(wmsSiteDTO, siteQuery);
		siteQuery.setSiteType(3);
		siteQuery.setSiteCategory(2);
		return wmsIntegration.getSiteByPage(siteQuery);

	}

	@Override
	public void batchSaveInvoiceLabel(OrderOfflineInvoiceLabelDTO orderOfflineInvoiceLabelDTO, Vendor vendor) {

		LambdaQueryWrapper<OrderPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
		lambdaQueryWrapper.in(OrderPO::getOrderSn, orderOfflineInvoiceLabelDTO.getOrderList());
		List<OrderPO> orderPOList = orderService.list(lambdaQueryWrapper);
		orderPOList.forEach( x -> {
			LambdaUpdateWrapper<OrderExtendPO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
			lambdaUpdateWrapper.eq(OrderExtendPO::getOrderSn, x.getOrderSn())
					.set(OrderExtendPO::getInvoiceStatus, orderOfflineInvoiceLabelDTO.getInvoiceStatus());

			String remark = "开票标签（已开票——>未开票）";

			//开票金额 = 订单实付金额，开票时间 = 系统当前时间
			if(OrderConst.INVOICE_STATE_1 == orderOfflineInvoiceLabelDTO.getInvoiceStatus()) {
				lambdaUpdateWrapper.set(OrderExtendPO::getInvoiceTime,new Date())
								.set(OrderExtendPO::getInvoiceAmount, x.getOrderAmount());
				remark = "开票标签（未开票——>已开票），开票金额（" + x.getOrderAmount() + "）";
			}
			orderExtendService.update(lambdaUpdateWrapper);

			//添加轨迹
			orderLogModel.insertOrderLog(OrderConst.LOG_ROLE_VENDOR, vendor.getVendorId(),
					vendor.getVendorName(), x.getOrderSn(), x.getOrderState(), x.getOrderState(),
					LoanStatusEnum.APPLY_SUCCESS.getValue(), "批量设置开票标签", OrderCreateChannel.WEB, remark);


		});

	}

	@Override
	public void batchSaveAccountPeriodDays(OrderOfflineAccountPeriodDaysDTO orderOfflineAccountPeriodDaysDTO, Vendor vendor) {
		LambdaQueryWrapper<OrderOfflineExtendPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
		lambdaQueryWrapper.in(OrderOfflineExtendPO::getOrderSn, orderOfflineAccountPeriodDaysDTO.getOrderList());
		List<OrderOfflineExtendPO> orderOfflineExtendPOList = orderOfflineExtendService.list(lambdaQueryWrapper);

		LambdaUpdateWrapper<OrderOfflineExtendPO> orderOfflineExtendPOLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
		orderOfflineExtendPOLambdaUpdateWrapper.set(OrderOfflineExtendPO::getAccountPeriodDays, orderOfflineAccountPeriodDaysDTO.getAccountPeriodDays())
				.in(OrderOfflineExtendPO::getOrderSn, orderOfflineAccountPeriodDaysDTO.getOrderList());
		orderOfflineExtendService.update(orderOfflineExtendPOLambdaUpdateWrapper);

		orderOfflineExtendPOList.forEach( x -> {
			String remark = "账期天数（" + x.getAccountPeriodDays() + "——>" + orderOfflineAccountPeriodDaysDTO.getAccountPeriodDays() + "）";
			OrderPO orderPO = orderModel.getOrderByOrderSn(x.getOrderSn());
			//添加轨迹
			orderLogModel.insertOrderLog(OrderConst.LOG_ROLE_VENDOR, vendor.getVendorId(),
					vendor.getVendorName(), x.getOrderSn(), orderPO.getOrderState(), orderPO.getOrderState(),
					LoanStatusEnum.APPLY_SUCCESS.getValue(), "批量设置账期", OrderCreateChannel.WEB, remark);
		});
	}

	@Override
	public 	void batchSaveSupplierCode(OrderOfflineSupplierCodeDTO orderOfflineSupplierCodeDTO, Vendor vendor) {
		LambdaQueryWrapper<OrderOfflineExtendPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
		lambdaQueryWrapper.in(OrderOfflineExtendPO::getOrderSn, orderOfflineSupplierCodeDTO.getOrderSnList());
		List<OrderOfflineExtendPO> orderOfflineExtendPOList = orderOfflineExtendService.list(lambdaQueryWrapper);

		LambdaUpdateWrapper<OrderOfflineExtendPO> orderOfflineExtendPOLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
		orderOfflineExtendPOLambdaUpdateWrapper.set(OrderOfflineExtendPO::getBuyerSupplierCode, orderOfflineSupplierCodeDTO.getBuyerSupplierCode())
				.set(OrderOfflineExtendPO::getBuyerSupplierName, orderOfflineSupplierCodeDTO.getBuyerSupplierName())
				.in(OrderOfflineExtendPO::getOrderSn, orderOfflineSupplierCodeDTO.getOrderSnList());
		orderOfflineExtendService.update(orderOfflineExtendPOLambdaUpdateWrapper);

		orderOfflineExtendPOList.forEach( x -> {
			String remark = "供应商标签(" + x.getBuyerSupplierCode() + "->" + orderOfflineSupplierCodeDTO.getBuyerSupplierCode() + ")";
			OrderPO orderPO = orderModel.getOrderByOrderSn(x.getOrderSn());
			//添加轨迹
			orderLogModel.insertOrderLog(OrderConst.LOG_ROLE_VENDOR, vendor.getVendorId(),
					vendor.getVendorName(), x.getOrderSn(), orderPO.getOrderState(), orderPO.getOrderState(),
					LoanStatusEnum.APPLY_SUCCESS.getValue(), "批量关联供应商", OrderCreateChannel.WEB, remark);
		});
	}


	@Override
	public String getEmployeeBranchCode(String employeeCode) {
		if(StringUtils.isNotEmpty(employeeCode)) {
			CustInfoVo custInfoVo = orderOfflineService.queryCustInfo(employeeCode);
			if(Objects.nonNull(custInfoVo)) {
				return custInfoVo.getBranchCode();
			}
		}
		return null;
	}

	@Override
	public Boolean notifyByDingTalk(String orderSn) {
		BizAssertUtil.notEmpty(orderSn, "订单号不能为空");
		OrderPO orderPO = orderService.lambdaQuery().eq(OrderPO::getOrderSn, orderSn).one();
		BizAssertUtil.notNull(orderPO, "订单不存在");
		String storeId = orderPO.getStoreId().toString();

		List<DictionaryItemVO> offlineNotifyList = bmsIntegration.listDictionaryItemsByTypeCode(
				CommonConst.OFFLINE_NOTIFY_LIST, CommonConst.MALL_SYSTEM_ID);

		List<String> phoneList = new ArrayList<>();
		//取出需要通知的列表
		for (DictionaryItemVO dictionaryItemVO : offlineNotifyList) {
			if (dictionaryItemVO.getItemStatus() != 1) {
				continue;
			}
			String notifyStoreList = dictionaryItemVO.getItemName();
			// 订单店铺，不在配置的通知店铺范围，则寻下一个配置
			List<String> storeList = Arrays.asList(notifyStoreList.split(","));
			if (CollectionUtils.isEmpty(storeList) || !storeList.contains(storeId)) {
				continue;
			}
			// 取出该店铺需要通知的财务人员手机号
			String itemCode = dictionaryItemVO.getItemCode();
			phoneList.addAll(Arrays.asList(itemCode.split(",")));
		}
		if (CollectionUtils.isEmpty(phoneList)) {
			throw new BusinessException("该店铺未配置通知列表");
		}

		String messageText = "线下补录订单[" + orderSn + "]需要您进行手动支付，请登录【电商运营中心-订单管理】进行操作。";
		if (!"prod".equalsIgnoreCase(profilesActive)) {
			messageText = "测试通知：" + messageText;
		}
		// 根据手机号查询钉钉用户id
		Set<String> dingTalkUserIds = new TreeSet<>();
		Result<List<DingTalkUserBmsVO>> listResult = bmsDingTalkFacade.listUserDetailVOByMobiles(phoneList);
		listResult.getData().forEach(dingTalkUserBmsVO -> {
			Optional.ofNullable(dingTalkUserBmsVO.getDeptList()).ifPresent(deptList -> {
				deptList.forEach(dingTalkUserDeptBmsVO -> {
					dingTalkUserIds.add(dingTalkUserDeptBmsVO.getDingTalkUserId());
				});
			});
			Optional.ofNullable(dingTalkUserBmsVO.getRoleList()).ifPresent(roleList -> {
				roleList.forEach(dingTalkUserRoleBmsVO -> {
					dingTalkUserIds.add(dingTalkUserRoleBmsVO.getDingTalkUserId());
				});
			});
		});
		// 根据手机号未找到钉钉用户
		if (CollectionUtils.isEmpty(dingTalkUserIds)) {
			throw new BusinessException("通知列表配置有误");
		}
		// 发送钉钉消息
		bmsDingTalkFacade.sendPlatformTextMessage(new ArrayList<>(dingTalkUserIds), messageText);
		return Boolean.TRUE;
	}

	@Override
	public List<OrderOfflineInfoVO> getReceiptInfoList(OrderOfflineRequest request) {
		return orderOfflineMapper.getReceiptInfoList(request);
	}

	@Override
	public Page<KingdeeCustomerVo> getKingdeeCustomerPage(KingdeeCustomerQuery query, Long storeId) {
		BizAssertUtil.isTrue(query.getCurrent() < 1, "当前页码不能小于1");
		BizAssertUtil.isTrue(query.getPageSize() > 2000 || query.getPageSize() < 1, "当前分页大小不能超过2000或者小于1");

		// 获取店铺信息, 用于判断店铺是否开启金蝶对接
		Store store = ExternalApiUtil.callSimpleApi(() -> storeFeignClient.getStoreByStoreId(storeId), storeId,
				"店铺feign接口", "根据店铺id获取店铺信息");
		BizAssertUtil.notNull(store, "店铺信息为空");

		if (!Objects.equals(CommonEnum.YES.getCode(), store.getWmsTransFlag())) {
			throw new BusinessException("店铺未开启金蝶对接，无法补录企业订单");
		}

		// 获取店铺对应金蝶组织编码
		List<DictionaryItemVO> dictionaryList = bmsIntegration.getDictionaryItemsByTypeCode(CommonConst.MALL_SYSTEM_MANAGE_ID, OrderConst.STORE_KINGDEE_ORG_DICTIONARY_TYPE);
		if (CollectionUtils.isEmpty(dictionaryList)) {
			log.info("店铺配置金蝶组织字典获取为空");
			return new Page<>();
		}
		DictionaryItemVO dictionaryItemVo = dictionaryList.stream().filter(item -> Objects.equals(item.getItemCode(), storeId.toString())).findFirst().orElse(null);
        if (Objects.isNull(dictionaryItemVo)) {
			throw new BusinessException("店铺没有对应的金蝶组织，无法补录企业订单");
		}
		String itemDesc = dictionaryItemVo.getItemDesc();
		if (StringUtils.isBlank(itemDesc)) {
			throw new BusinessException("店铺没有对应的金蝶组织，无法补录企业订单");
		}
		try {
			JSONObject jsonObject = JSONObject.parseObject(itemDesc, JSONObject.class);
			if (StringUtils.isBlank(jsonObject.getString("organizationCode"))) {
				throw new BusinessException("店铺配置金蝶组织字典格式有误");
			}
			query.setOrgNumber(jsonObject.getString("organizationCode"));
		} catch (Exception e) {
			throw new BusinessException("店铺配置金蝶组织字典格式有误");
		}
        Page<KingdeeCustomerVo> page;
		try {
			JsonResult<Page<KingdeeCustomerVo>> jsonResult = mallExternalAdapterFacade.getCustomerPage(query);
			if (Objects.isNull(jsonResult)) {
				throw new BusinessException("金蝶客户列表查询响应为null");
			}
			if (jsonResult.getState() != 200) {
				throw new BusinessException("金蝶客户列表查询失败，原因为:" + jsonResult.getMsg());
			}
			page = jsonResult.getData();
		} catch (Exception e) {
			log.warn("金蝶客户列表查询失败", e);
			throw new BusinessException("金蝶客户列表查询异常, 异常原因:" + e.getMessage());
		}
		if (!CollectionUtils.isEmpty(page.getRecords())) {
			page.getRecords().forEach(kingdeeCustomerVo -> {
				// 状态位转换
               if (Objects.equals(kingdeeCustomerVo.getDocumentStatusStr(), "C")) {
				   kingdeeCustomerVo.setDocumentStatus(1);
			   } else {
				   kingdeeCustomerVo.setDocumentStatus(0);
			   }

			   if (Objects.equals(kingdeeCustomerVo.getForbidStatusStr(), "A")) {
				   kingdeeCustomerVo.setForbidStatus(0);
			   } else {
				   kingdeeCustomerVo.setForbidStatus(1);
			   }
			});
		}
		return page;
	}


}
