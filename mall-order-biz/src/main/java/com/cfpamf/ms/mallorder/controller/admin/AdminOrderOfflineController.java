package com.cfpamf.ms.mallorder.controller.admin;

import java.util.List;

import com.cfpamf.ms.mallorder.dto.OrderOfflineManualDTO;
import com.cfpamf.ms.mallshop.resp.Vendor;
import com.cfpamf.ms.mallsystem.vo.Admin;
import com.slodon.bbc.core.util.UserUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.cfpamf.ms.mallorder.po.OrderOfflinePO;
import com.cfpamf.ms.mallorder.service.OrderOfflineService;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * admin-线下订单操作controller
 */
@RestController
@RequestMapping("/admin/OrderOffline")
@Api(tags = "admin-线下订单操作")
public class AdminOrderOfflineController{

	@Autowired
	private OrderOfflineService orderOfflineService;

	@Autowired
	private HttpServletRequest request;

	@ApiOperation("线下订单额外信息查询")
	@GetMapping("query/detail")
	public JsonResult<List<OrderOfflinePO>> queryByPaySn(String paySn) {
		List<OrderOfflinePO> data = orderOfflineService.queryOrderOfflineList(paySn);
		return SldResponse.success(data);
	}

	@ApiOperation("手动支付")
	@PostMapping("/manual/pay")
	public JsonResult<Void> manualPay(@RequestBody @Valid OrderOfflineManualDTO orderOfflineManualDTO) {
		Admin admin = UserUtil.getUser(request, Admin.class);
		orderOfflineService.manualPayAdmin(orderOfflineManualDTO, admin);
		return SldResponse.success();
	}

	@ApiOperation("资料补充")
	@PostMapping("/info/supplement")
	public JsonResult<Void> infoSupplement(@RequestBody @Valid OrderOfflineManualDTO orderOfflineManualDTO) {
		Admin admin = UserUtil.getUser(request, Admin.class);
		orderOfflineService.infoSupplementAdmin(orderOfflineManualDTO,  admin);
		return SldResponse.success();
	}

	@ApiOperation("获取补录弹框信息接口")
	@GetMapping("/manual/info")
	public JsonResult<OrderOfflineManualDTO> getManualInfo(String orderSn) {
		return SldResponse.success(orderOfflineService.getManualInfo(orderSn));
	}


}
