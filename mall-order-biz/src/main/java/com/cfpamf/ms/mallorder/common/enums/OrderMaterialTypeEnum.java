package com.cfpamf.ms.mallorder.common.enums;

import com.alibaba.fastjson.annotation.JSONType;
import com.cfpamf.ms.mallorder.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * 订单凭证资料类型
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
@JSONType(serializeEnumAsJavaBean = true)
public enum OrderMaterialTypeEnum implements IEnum<String> {

	TEXT("1", "文本"),

	IMG("2", "图片"),

	AGREEMENT("3", "协议"),

	ELE_AGREEMENT("6","电签协议")
	;

	private final String code;

	private final String name;

	@Override
	public String getValue() {
		return getCode();
	}

	public static OrderMaterialTypeEnum getValue(String code) {
		if (StringUtils.isBlank(code)) {
			return null;
		}
		for (OrderMaterialTypeEnum e : OrderMaterialTypeEnum.values()) {
			if (Objects.equals(code, e.code)) {
				return e;
			}
		}
		return null;
	}
}
