package com.cfpamf.ms.mallorder.service;

import com.cfpamf.ms.mallorder.po.pgrpt.AdsWineWorkDateOrderDetailHfpPO;
import com.cfpamf.ms.mallorder.req.BeverageWorkReportRequest;

import java.util.List;
import java.util.Map;

public interface IBeverageWorkReportService {

    List<AdsWineWorkDateOrderDetailHfpPO> queryBeverageWorkReport(BeverageWorkReportRequest request);


    /**
     * 查询流程业务信息详情
     */
    Map<String, Object> queryFlowMessageDetail(String procDefKey, String nodeKey, String bizId);

}
