package com.cfpamf.ms.mallorder.integration.filecenter;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * 凭证场景类型
 */
@AllArgsConstructor
@Getter
public enum ProofSceneEnum {

    SUBMIT("submit", "用户下单凭证"),
    DELIVERY("delivery", "商家发货凭证"),
    RECEIVE("receive", "客户签收凭证"),
    RECEIVED("received", "补充资料凭证");

    String code;
    String desc;

    public static ProofSceneEnum getValue(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (ProofSceneEnum e : ProofSceneEnum.values()) {
            if (Objects.equals(code, e.code)) {
                return e;
            }
        }
        return null;
    }

}

