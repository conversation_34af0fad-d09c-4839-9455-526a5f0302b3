package com.cfpamf.ms.mallorder.controller.fegin.facade;


import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.mallorder.dto.ExtAfterSalesApplyDTO;
import com.cfpamf.ms.mallorder.dto.OrderForceRefundDTO;
import com.cfpamf.ms.mallorder.req.admin.AdminForceRefundRequest;
import com.cfpamf.ms.mallorder.service.IOrderAdminReturnService;
import com.cfpamf.ms.mallorder.service.IOrderAfterSaleService;
import com.cfpamf.ms.mallsystem.vo.Admin;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Optional;

/**
 * @Author: zml
 * @CreateTime: 2022/7/5 15:45
 */
@RestController
@Slf4j
@Api(tags = "feign-订单售后")
public class OrderAfterSalesFeignClient {


    @Resource
    private IOrderAdminReturnService orderAdminReturnService;

    @Resource
    private IOrderAfterSaleService orderAfterSaleService;

    @ApiOperation("强制退款")
    @PostMapping("/v1/feign/business/performance/forceRefund")
    public JsonResult<Boolean> extOrderOperate(@RequestBody @Valid @NotNull OrderForceRefundDTO orderForceRefundDTO) {
        Admin admin = new Admin();
        AdminForceRefundRequest adminForceRefundRequest = new AdminForceRefundRequest();
        adminForceRefundRequest.setOrderSns(orderForceRefundDTO.getOrderSn());
        adminForceRefundRequest.setRemark(orderForceRefundDTO.getRemark());
        adminForceRefundRequest.setProductIds(orderForceRefundDTO.getProductIds());
        adminForceRefundRequest.setReason(orderForceRefundDTO.getRemark());
        orderAdminReturnService.adminForceRefund(admin, adminForceRefundRequest);
        return SldResponse.success(Boolean.TRUE);
    }

    @ApiOperation("商家售后审批")
    @PostMapping("/v1/feign/business/performance/afterSales/extSellerAudit")
    public JsonResult<Boolean> extSellerAudit(@RequestBody @Valid @NotNull ExtAfterSalesApplyDTO extAfterSalesApplyDTO) {
        log.info("【feign-OrderAfterSalesFeignClient.extAfsApplyCallBack】afsSn = {},extAfterSalesApplyDTO = {}", extAfterSalesApplyDTO.getAfsSn(), JSONObject.toJSONString(extAfterSalesApplyDTO));
        orderAfterSaleService.extSellerAudit(extAfterSalesApplyDTO);
        return SldResponse.success(Boolean.TRUE);
    }

}
