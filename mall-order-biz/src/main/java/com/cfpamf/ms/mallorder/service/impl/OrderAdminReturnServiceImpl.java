package com.cfpamf.ms.mallorder.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cfpamf.ms.mallorder.common.constant.SentenceConst;
import com.cfpamf.ms.mallorder.common.enums.OrderCreateChannel;
import com.cfpamf.ms.mallorder.common.enums.RefundType;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.mapper.OrderReturnMapper;
import com.cfpamf.ms.mallorder.model.OrderAfterServiceModel;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.model.OrderReturnModel;
import com.cfpamf.ms.mallorder.po.OrderAfterPO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderReturnPO;
import com.cfpamf.ms.mallorder.req.RefundTypeChangeRequest;
import com.cfpamf.ms.mallorder.req.admin.AdminForceRefundListRequest;
import com.cfpamf.ms.mallorder.req.admin.AdminForceRefundRequest;
import com.cfpamf.ms.mallorder.request.OrderReturnExample;
import com.cfpamf.ms.mallorder.service.IOrderAdminReturnService;
import com.cfpamf.ms.mallorder.service.IOrderService;
import com.cfpamf.ms.mallorder.validation.OrderReturnValidation;
import com.cfpamf.ms.mallorder.vo.OrderReturnVOV2;
import com.cfpamf.ms.mallsystem.api.ReasonFeignClient;
import com.cfpamf.ms.mallsystem.vo.Admin;
import com.cfpamf.ms.mallsystem.vo.Reason;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.response.PageVO;
import com.slodon.bbc.core.response.PagerInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ArrayUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 平台端退款serviceImpl
 */
@Slf4j
@Service
public class OrderAdminReturnServiceImpl extends ServiceImpl<OrderReturnMapper, OrderReturnPO> implements IOrderAdminReturnService {

    @Resource
    private IOrderService orderService;

    @Resource
    private OrderReturnModel orderReturnModel;

    @Resource
    private OrderAfterServiceModel orderAfterServiceModel;

    @Resource
    private OrderModel orderModel;

    @Resource
    private ReasonFeignClient reasonFeignClient;
    @Resource
    private OrderReturnValidation orderReturnValidation;

    @Override
    public String adminForceRefund(Admin admin, AdminForceRefundRequest forceRefundReq) {
        String[] orderSns = forceRefundReq.getOrderSns().split(",");
        BizAssertUtil.isTrue(ArrayUtils.isEmpty(orderSns), "订单号不能为空");
        String refundReason = forceRefundReq.getReason();
        String remark = forceRefundReq.getRemark();
        if(forceRefundReq.getReasonId() == null) {
            if(StringUtils.isEmpty(forceRefundReq.getReason())){
                throw new BusinessException("退款原因不能为空");
            }
        } else {
            Reason reason = reasonFeignClient.getReasonByReasonId(forceRefundReq.getReasonId());
            BizAssertUtil.isTrue(Objects.isNull(reason) || StringUtils.isEmpty(reason.getContent()), "退款原因为空");
            refundReason = reason.getContent();
        }

        StringBuilder resultMsg = new StringBuilder();
        for (String orderSn : orderSns) {
            OrderPO orderPo = orderService.getByOrderSn(orderSn);
            // 校验订单是否支持强制退款
            String checkResult = orderReturnValidation.orderForceRefundValidate(orderPo);
            if (!OrderConst.CHECK_SUCCESS.equals(checkResult)) {
                this.buildForceRefundFailInfo(resultMsg, orderSn, checkResult);
                continue;
            }
            try {
                orderModel.orderCancelInsertAfterServiceAndReturn(orderPo, refundReason, remark, OrderConst.RETURN_BY_3, OrderConst.LOG_ROLE_ADMIN,
                        Optional.ofNullable(admin.getAdminId()).orElse(OrderConst.LOG_ROLE_ADMIN).longValue(),
                        Optional.ofNullable(admin.getAdminName()).orElse(OrderConst.LOG_USER_NAME), forceRefundReq.getProductIds());
            } catch (Exception e) {
                log.warn("平台强制退款异常，订单号{}", orderSn, e);
                this.buildForceRefundFailInfo(resultMsg, orderSn, e.getMessage());
            }
        }
        return StringUtils.isEmpty(resultMsg.toString()) ? SentenceConst.DEAL_SUCCESS : resultMsg.toString();
    }

    /**
     * 构建错误信息
     *
     * @param resultMsg     错误信息
     * @param orderSn       订单号
     * @param checkResult   错误结果
     */
    private void buildForceRefundFailInfo(StringBuilder resultMsg, String orderSn, String checkResult) {
        resultMsg.append("订单号【");
        resultMsg.append(orderSn);
        resultMsg.append("】强制退款失败，");
        resultMsg.append("原因：【");
        resultMsg.append(checkResult);
        resultMsg.append("】；");
    }

    @Override
    public PageVO<OrderReturnVOV2> adminForceRefundList(PagerInfo pager, AdminForceRefundListRequest request) {
        OrderReturnExample example = new OrderReturnExample();
        example.setOrderSn(request.getOrderSn());
        example.setAfsSn(request.getAfsSn());
        example.setStoreNameLike(request.getStoreName());
        example.setApplyTimeAfter(request.getApplyTimeStart());
        example.setApplyTimeBefore(request.getApplyTimeEnd());
        example.setContactName(request.getOperatorName());
        example.setState(request.getState());
        example.setReturnBy(request.getReturnBy());
        example.setUserMobile(request.getContactPhone());
        List<OrderReturnVOV2> vos = orderReturnModel.getOrderReturnListWithJoin(example, pager);
        return new PageVO<>(vos, pager);
    }

    @Override
    public String changeRefundType(Admin admin, RefundTypeChangeRequest refundTypeChangeRequest) {
        OrderReturnPO orderReturnPO = orderReturnModel.getOrderReturnByAfsSn(refundTypeChangeRequest.getAfsSn());
        OrderAfterPO orderAfterPO = orderAfterServiceModel.getAfterServiceByAfsSn(refundTypeChangeRequest.getAfsSn());
        LambdaUpdateWrapper<OrderReturnPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OrderReturnPO::getAfsSn, refundTypeChangeRequest.getAfsSn())
                .set(OrderReturnPO::getRefundType, refundTypeChangeRequest.getRefundType())
                .set(OrderReturnPO::getUpdateBy, admin.getAdminId());
        this.update(updateWrapper);
        //操作：operateType 1、创建；2、店铺审核；3、平台审核；4、退款成功；5、退款失败；6、售后关闭
        Integer operateType = 3;
        orderReturnModel.insertReturnLog(refundTypeChangeRequest.getAfsSn(), operateType, OrderConst.LOG_ROLE_ADMIN, Long.valueOf(admin.getAdminId())
                , admin.getAdminName(), orderAfterPO.getAfsType(), orderReturnPO.getState().toString(),
                "修改退款类型为【" + RefundType.value(refundTypeChangeRequest.getRefundType()).getDesc() + "】", OrderCreateChannel.WEB.getValue());
        return "退款类型修改成功!";
    }
}
