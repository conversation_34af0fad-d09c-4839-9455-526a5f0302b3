package com.cfpamf.ms.mallorder.service.impl;

import com.cfpamf.framework.autoconfigure.mybatis.BaseRepoServiceImpl;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.dto.GroupOrderProductSubmitDTO;
import com.cfpamf.ms.mallorder.mapper.OrderGroupBuyingBindMapper;
import com.cfpamf.ms.mallorder.po.OrderGroupBuyingBindPO;
import com.cfpamf.ms.mallorder.req.query.OrderGroupBuyingBindQuery;
import com.cfpamf.ms.mallorder.service.IOrderGroupBuyingBindService;
import com.cfpamf.ms.mallorder.vo.GroupOrderProductVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 拼单满赠关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-16
 */
@Service
public class OrderGroupBuyingBindServiceImpl extends BaseRepoServiceImpl<OrderGroupBuyingBindMapper, OrderGroupBuyingBindPO> implements IOrderGroupBuyingBindService {

	@Autowired
	private OrderGroupBuyingBindMapper groupBuyingBindMapper;

	@Override
	public List<OrderGroupBuyingBindPO> getGroupBuyingBindList(OrderGroupBuyingBindQuery query) {
		if (Objects.isNull(query)){
			return null;
		}

		return groupBuyingBindMapper.listGroupBuyingBind(query);
	}

	@Override
	public List<GroupOrderProductVO> listGroupOrderProduct(OrderGroupBuyingBindQuery query) {
		if (Objects.isNull(query)){
			return null;
		}

		return groupBuyingBindMapper.listBindOrderProduct(query);
	}

	@Override
	public Boolean saveOrderGroupBuyingBind(GroupOrderProductSubmitDTO submitDTO, String groupBuyingCode, String giftOrderSn) {
		List<GroupOrderProductVO> groupOrderProductVOList = submitDTO.getGroupOrderProductVOList();
		List<OrderGroupBuyingBindPO> orderGroupBuyingBindPOList = new ArrayList<>(groupOrderProductVOList.size());
		for (GroupOrderProductVO groupOrderProductVO : groupOrderProductVOList) {
			OrderGroupBuyingBindPO orderGroupBuyingBindPO = new OrderGroupBuyingBindPO();
			orderGroupBuyingBindPO.setGroupBuyingCode(groupBuyingCode);
			orderGroupBuyingBindPO.setGiftOrderSn(giftOrderSn);
			orderGroupBuyingBindPO.setGroupOrderSn(groupOrderProductVO.getOrderSn());
			orderGroupBuyingBindPO.setOrderProductId(groupOrderProductVO.getOrderProductId());
			orderGroupBuyingBindPO.setCreateTime(new Date());
			orderGroupBuyingBindPO.setUpdateTime(new Date());
			orderGroupBuyingBindPO.setEnabledFlag(1);

			orderGroupBuyingBindPOList.add(orderGroupBuyingBindPO);
		}

		boolean saveBatch = this.saveBatch(orderGroupBuyingBindPOList);
		BizAssertUtil.isTrue(!saveBatch, "保存拼单满赠关联表失败");
		return true;
	}
}
