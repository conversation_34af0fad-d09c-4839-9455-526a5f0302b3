package com.cfpamf.ms.mallorder.common.enums;

import com.alibaba.fastjson.annotation.JSONType;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 信贷试算错误码
 */
@AllArgsConstructor
@Getter
@JSONType(serializeEnumAsJavaBean = true)
public enum LoanCalculateErrorCodeEnum {

    REPAY_LIMIT_THE_TIME_PERIOD("200001001", "当天放款借据不允许当天还款"),
    REPAY_LIMIT_ON_LOAN_DAY("200001002", "当天放款借据不允许当天还款"),
    ALREADY_SETTLED("0024990099", "借据已结清");

    /**
     * 错误码
     */
    private final String code;
    /**
     * 错误信息
     */
    private final String msg;

}
