package com.cfpamf.ms.mallorder.controller.front;

import com.alibaba.fastjson.JSON;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.framework.autoconfigure.ZhnxServiceException;
import com.cfpamf.mallpayment.facade.request.loan.SetlTryResultVo;
import com.cfpamf.mallpayment.facade.vo.CDMallRefundTryResultVO;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.common.enums.ReturnByEnum;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.common.util.OrderLocalUtils;
import com.cfpamf.ms.mallorder.dto.OrderAfterDTO;
import com.cfpamf.ms.mallorder.model.OrderAfterServiceModel;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.model.OrderProductModel;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderProductPO;
import com.cfpamf.ms.mallorder.service.IFrontAfterSaleApplyService;
import com.cfpamf.ms.mallorder.service.IOrderAfterService;
import com.cfpamf.ms.mallorder.service.IOrderReturnService;
import com.cfpamf.ms.mallorder.v2.common.lock.Lock;
import com.cfpamf.ms.mallorder.validation.OrderReturnValidation;
import com.cfpamf.ms.mallorder.vo.*;
import com.cfpamf.ms.mallorder.vo.refund.RestoreLimitRefundVO;
import com.slodon.bbc.core.controller.BaseController;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import com.slodon.bbc.core.util.AssertUtil;
import com.slodon.bbc.core.util.UserUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Api(tags = "front-售后申请")
@RestController
@RequestMapping("front/after/sale/apply")
@Slf4j
public class FrontAfterSaleApplyController extends BaseController {

    @Resource
    private OrderModel orderModel;
    @Resource
    private OrderProductModel orderProductModel;
    @Resource
    private OrderAfterServiceModel orderAfterServiceModel;

    @Resource
    private IFrontAfterSaleApplyService frontAfterSaleApplyService;
    @Autowired
    private IOrderAfterService orderAfterService;
    @Resource
    private IOrderReturnService orderReturnService;

    @Autowired
    private OrderReturnValidation orderReturnValidation;

    @Resource
    private OrderLocalUtils orderLocalUtils;

    @Autowired
    private Lock lock;

    @ApiOperation("用户端退款申请检验")
    @ApiImplicitParams({@ApiImplicitParam(name = "orderSn", value = "订单号", required = true, paramType = "query"),
                        @ApiImplicitParam(name = "isVale", value = "是否代客申请", defaultValue = "false", paramType = "query")})
    @GetMapping("applyCheck")
    public JsonResult<Boolean> applyCheck(HttpServletRequest request,
                                          String orderSn,
                                          @RequestParam(value = "isValet", defaultValue = "false") Boolean isValet) {
        Member member = UserUtil.getUser(request, Member.class);
        log.info("front applyCheck, member info is : {}", com.alibaba.fastjson.JSON.toJSONString(member));

        OrderPO orderPO = orderModel.getOrderByOrderSn(orderSn);
        // 根据请求头判断是否 bapp端请求
        String bappFlag = request.getHeader("x-cfpamf-mall-web-identify");
        if ("b-proxy-front".equals(StringUtils.isBlank(bappFlag) ? "" : bappFlag.trim())) {
            isValet = true;
        }
        // 商家是否仅支持内部退款，仅支持内部退款的需要客户经理申请
        boolean innerRefundStore = orderReturnValidation.isInnerRefundStore(orderPO.getStoreId());
        if (isValet) {
            // 非内部退款店铺不允许 代客申请售后
            BizAssertUtil.isTrue(!innerRefundStore, "请客户在客户端自主发起退单申请");
        } else {
            // 内部退款店铺不允许 客户自主申请售后
            BizAssertUtil.isTrue(innerRefundStore, "抱歉，请联系客户经理或站长协助申请退款！");
        }
        return SldResponse.success(Boolean.TRUE);
    }

    @ApiOperation("退款申请信息")
    @ApiImplicitParams({@ApiImplicitParam(name = "orderSn", value = "订单号", required = true, paramType = "query"),
        @ApiImplicitParam(name = "orderProductId", value = "订单货品id", required = true, paramType = "query")})
    @GetMapping("applyInfo")
    public JsonResult<AfsApplyInfoVO> applyInfo(HttpServletRequest request, String orderSn,
                                                Long orderProductId,@RequestParam(value = "isValet", defaultValue = "false") Boolean isValet) {
        Member member = UserUtil.getUser(request, Member.class);

        // 根据请求头判断是否 bapp端请求
        String bappFlag = request.getHeader("x-cfpamf-mall-web-identify");
        if ("b-proxy-front".equals(StringUtils.isBlank(bappFlag) ? "" : bappFlag.trim())) {
            isValet = true;
        }
        AfsApplyInfoVO vo = frontAfterSaleApplyService.getAfsApplyInfoVO(member.getMemberId(), orderSn, orderProductId, isValet);
        return SldResponse.success(vo);
    }

    @ApiOperation(value = "售后订单货品详情")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "orderProductId", value = "订单货品id", required = true, paramType = "query")})
    @GetMapping("getOrderProductDetail")
    public JsonResult<AfsOrderProductVO> getOrderProductDetail(HttpServletRequest request, Long orderProductId) {
        Member member = UserUtil.getUser(request, Member.class);
        OrderProductPO orderProductPO = orderProductModel.getOrderProductByOrderProductId(orderProductId);
        // 商家是否仅支持内部退款，仅支持内部退款的需要客户经理申请
        BizAssertUtil.isTrue(orderReturnValidation.isInnerRefundStore(orderProductPO.getStoreId()), "抱歉，请联系客户经理协助申请退款！");
        AssertUtil.notNull(orderProductPO, "无此订单货品");
        BizAssertUtil.isTrue(!member.getMemberId().equals(orderProductPO.getMemberId()), "您无权操作此订单货品");
        return SldResponse.success(new AfsOrderProductVO(orderProductPO));
    }

    @ApiOperation(value = "计算售后退款信息")
    @ApiImplicitParams({@ApiImplicitParam(name = "orderSn", value = "订单号", required = true, paramType = "query"),
            @ApiImplicitParam(name = "orderProductInfos", value = "退换的订单货品列表，格式为：id1-num1,id2-num2...num为空时表示此订单货品全部退换",
                    required = true, paramType = "query")})
    @GetMapping("countReturnMoney")
    public JsonResult<AfsCountVO> countReturnMoney(HttpServletRequest request, String orderSn,
                                                   String orderProductInfos) {
        Member member = UserUtil.getUser(request, Member.class);

        AfsCountVO vo = orderAfterService.refundCountMoneyInfo(orderSn, orderProductInfos, member);
        return SldResponse.success(vo);
    }

    @ApiOperation("可以申请售后订单货品列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "orderSn", value = "订单号", required = true, paramType = "query"),
        @ApiImplicitParam(name = "orderProductId", value = "订单货品id，默认列表第一个元素，默认选中", required = true,
            paramType = "query")})
    @GetMapping("getOrderProductList")
    public JsonResult<List<AfsOrderProductVO>> getOrderProductList(HttpServletRequest request, String orderSn,
        Long orderProductId) {
        Member member = UserUtil.getUser(request, Member.class);

        List<AfsOrderProductVO> vos = orderAfterService.getRefundableOrderProduct(orderSn, orderProductId, member);
        return SldResponse.success(vos);
    }

    @ApiOperation(value = "退款申请提交", tags = "CORE")
    @PostMapping("submit")
    public JsonResult<String> returnedGoods(HttpServletRequest request,
                                            @RequestBody @Valid @NotNull OrderAfterDTO orderAfterDTO) {
        Member member = UserUtil.getUser(request, Member.class);
        BizAssertUtil.isTrue(!orderLocalUtils.checkMemberValid(member),
                String.format("用户已失效，用户号码：%s", member.getMemberMobile()));
        // 返回单号
        return lock.tryLockExecuteFunction("returnedGoods-" + orderAfterDTO.getOrderSn(), 0, 30, TimeUnit.SECONDS,
                () -> {
                    try {
                        orderAfterDTO.setReturnBy(ReturnByEnum.CUSTOMER.getValue());
                        Map<Long, String> resultMap = orderAfterServiceModel.submitAfterSaleApply(orderAfterDTO, member);
                        List<String> afsSnList = new ArrayList<>();
                        orderAfterDTO.getProductList().forEach(product -> {
                            String afsSn = resultMap.get(product.getOrderProductId());
                            afsSnList.add(afsSn);
                        });
                        String afsSns = String.join(",", afsSnList);
                        JsonResult<String> result = SldResponse.success("提交成功");
                        result.setData(afsSns);
                        return result;
                    } catch (ZhnxServiceException e) {
                        throw new BusinessException(ErrorCodeEnum.U.REPEATED_REQUEST.getCode(), "抱歉，退款申请系统开小差了，请稍后再试",
                                "退款申请提交方法执行异常", e);
                    }
                });
    }

//
//    @ApiOperation("根据申请件数获取退款金额")
//    @ApiImplicitParams({
//        @ApiImplicitParam(name = "orderProductId", value = "订单货品id", required = true, paramType = "query"),
//        @ApiImplicitParam(name = "applyNum", value = "applyNum", required = true, paramType = "query")})
//    @GetMapping("getReturnMoney")
//    public JsonResult<BigDecimal> getReturnMoney(String orderProductId, Integer applyNum) {
//        BigDecimal moneyCanReturn = orderAfterServiceModel.getReturnMoney(orderProductId, applyNum);
//        return SldResponse.success(moneyCanReturn);
//    }

    @ApiOperation("获取试算结果")
    @GetMapping("getTryCaculateResult")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "returnMoneyAmount", value = "试算金额", required = true, paramType = "query"),
        @ApiImplicitParam(name = "afsSn", value = "退款单号", required = true, paramType = "query")})
    public JsonResult<LoanTryResultVO> getTryCaculateResult(HttpServletRequest request,
                                                            @RequestParam("returnMoneyAmount") @NotNull BigDecimal returnMoneyAmount, @RequestParam("afsSn") @NotNull String afsSn) {
        CDMallRefundTryResultVO setlTryResultVo = null;
        try {
            Result<CDMallRefundTryResultVO> tryCaculateResult = orderAfterServiceModel.getTryCaculateResult(returnMoneyAmount, afsSn);
            setlTryResultVo = tryCaculateResult.getData();
        } catch (Exception e) {
            log.info("试算参数：{}，{}", JSON.toJSON(returnMoneyAmount), afsSn, e);
        }
        return SldResponse.success(new LoanTryResultVO(setlTryResultVo));
    }

    @ApiOperation("用户端恢复额度退款查询该批次所有退款单号")
    @GetMapping("getAllRestoreLimitOrders")
    @ApiImplicitParams({@ApiImplicitParam(name = "orderSn", value = "订单号", required = true, paramType = "query")})
    public JsonResult<List<RestoreLimitRefundVO>> getAllRestoreLimitOrders(@RequestParam("orderSn") @NotNull String orderSn) {
        return SldResponse.success(orderReturnService.getAllRestoreLimitRefundOrderInfos(orderSn));
    }

}
