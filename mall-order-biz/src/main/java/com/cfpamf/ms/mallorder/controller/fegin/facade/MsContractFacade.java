package com.cfpamf.ms.mallorder.controller.fegin.facade;

import com.cdfinance.ms.facade.model.request.signBefore.ContractFindRequest;
import com.cdfinance.ms.facade.model.request.signBefore.ContractGenerateRequest;
import com.cdfinance.ms.facade.model.request.signing.ContractESignAutoRequest;
import com.cdfinance.ms.facade.model.response.signBefore.ContractFindResponse;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.mallorder.vo.ContractCustomerCheckParamVO;
import com.cfpamf.ms.mallorder.vo.ContractCustomerCheckResultVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 信贷合同facade
 *
 * <AUTHOR>
 * @since 2024-11-20
 */
@FeignClient(name = "ms-contract", url = "${ms-contract.url:}")
public interface MsContractFacade {
    /**
     * 法大大实名+授权校验
     *
     * @param paramVO 校验参数，包含手机号、姓名、身份证等
     * @return 校验结果
     */
    @PostMapping("/contract/customer/checkV2")
    Result<ContractCustomerCheckResultVO> customerCheck(@RequestBody ContractCustomerCheckParamVO paramVO);

    /**
     * 法大大授权状态授权校验
     *
     * @param paramVO
     * @return
     */
    @PostMapping("/contract/customer/checkStatus")
    Result<ContractCustomerCheckResultVO> checkStatus(@RequestBody ContractCustomerCheckParamVO paramVO);

    /**
     * 生成合同
     *
     * @param contractGenerateRequest 生成合同参数
     * @return 生成结果
     */
    @PostMapping("/contract/before/generate")
    Result<ContractFindResponse> generateContract(@RequestBody ContractGenerateRequest contractGenerateRequest);

    /**
     * 合同签署
     *
     * @param eSignAutoRequest 签署请求
     * @return 签署结果
     */
    @PostMapping("/contract/signing/eSign/asynAutoV2")
    Result<ContractFindResponse> contractSign(@RequestBody ContractESignAutoRequest eSignAutoRequest);

    /**
     * 合同查询
     *
     * @param contractFindRequest 查询参数
     * @return 响应结果
     */
    @PostMapping("/contract/common/findAllContract")
    Result<List<ContractFindResponse>> findAllContract(@RequestBody ContractFindRequest contractFindRequest);

}
