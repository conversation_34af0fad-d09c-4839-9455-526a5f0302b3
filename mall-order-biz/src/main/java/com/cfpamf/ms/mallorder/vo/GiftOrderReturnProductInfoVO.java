package com.cfpamf.ms.mallorder.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> 2023/11/8
 */
@Data
public class GiftOrderReturnProductInfoVO {

    @ApiModelProperty("退货单号")
    private String afsSn;

    @ApiModelProperty("退款数量")
    private Integer afsNum;

    @ApiModelProperty("sku ID")
    private Long productId;

    @ApiModelProperty("退款时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date completeTime;

    @ApiModelProperty("退款方式：1-仅退款 2-退货退款")
    private Integer returnType;

    @ApiModelProperty(value = "sku物料编码")
    private String skuMaterialCode;

    @ApiModelProperty(value = "商品渠道来源：1、电商，3、农服")
    private Integer channelSource;

    @ApiModelProperty(value = "拼车sku货品库存单位编码")
    private String channelSkuUnit;

    @ApiModelProperty(value = "渠道商品Sku ID")
    private String channelSkuId;

}
