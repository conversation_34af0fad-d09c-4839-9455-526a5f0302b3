package com.cfpamf.ms.mallorder.validation;

import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.common.enums.OrderOfflineReceiptTypeEnum;
import com.cfpamf.ms.mallorder.common.enums.OrderPatternEnum;
import com.cfpamf.ms.mallorder.common.enums.OrderStatusEnum;
import com.cfpamf.ms.mallorder.common.enums.PaymentTagEnum;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.common.util.ValidUtils;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.OrderOfflineDTO;
import com.cfpamf.ms.mallorder.dto.OrderOfflineInfoDTO;
import com.cfpamf.ms.mallorder.dto.OrderOfflineManualSettlementDTO;
import com.cfpamf.ms.mallorder.dto.OrderOfflineParamDTO;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderProductPO;
import com.cfpamf.ms.mallorder.vo.CustInfoVo;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;


/**
 * 
 * <AUTHOR> 
 * 线下补录订单校验
 *
 */
public class OrderOfflineValidation {

    public static void isValidOfflineOrderBaseInfo(OrderOfflineParamDTO dto) {
		BizAssertUtil.isTrue(!OrderTypeEnum.isOfflineAll(dto.getOrderType()), String.format("抱歉，订单类型错误。请先校准订单类型【%s】，再来下单！", dto.getOrderType()));
		BizAssertUtil.notEmpty(dto.getSkuInfoList(), "抱歉，尚未选择要下单的商品规格，请选中要购买的具体商品！");
		// 收货地址不为空
		if (OrderPatternEnum.SELF_LIFT.getValue().equals(dto.getOrderPattern())) {
			BizAssertUtil.notNull(dto.getPointId(), "自提订单未选择自提点！");
		} else {
			BizAssertUtil.isTrue(ObjectUtils.isNotEmpty(dto.getAddress()) && !ValidUtils.isAddressValid(dto.getAddress()),
					"抱歉，收货地址不完善，请填写正确的收货地址！");
		}
		if(PaymentTagEnum.isPayment(dto.getPaymentTag())) {
			BizAssertUtil.notEmpty(dto.getOrderOfflineList(), "抱歉，请填写回执单信息！");
		}
		if (!CollectionUtils.isEmpty(dto.getOrderOfflineList())) {
			BizAssertUtil.isTrue(dto.getOrderOfflineList().size() > 20, "抱歉，回执单信息，一个订单最多上传20笔！");
			BizAssertUtil.notNull(dto.getOrderOfflineList().get(0), "抱歉，请填写回执单信息！");
			String receiptAccount = dto.getOrderOfflineList().get(0).getReceiptAccount();
			BizAssertUtil.isTrue(StringUtils.isEmpty(receiptAccount), "抱歉，请填写回执单收款帐号！");
			for (OrderOfflineDTO item : dto.getOrderOfflineList()) {
				BizAssertUtil.isTrue(!receiptAccount.equals(item.getReceiptAccount()), "抱歉，一次下单，收款帐号必须相同，请核对回执单收款帐号！");
				if(OrderOfflineReceiptTypeEnum.DEBT_OFFSE.getCode().equals(item.getReceiptType())) {
					BizAssertUtil.isTrue(StringUtils.isEmpty(item.getSupplierCode()) || StringUtils.isEmpty(item.getSupplierName()),
							"抱歉，请填写回执单债权供应商！");
				}
			}
		}
	}

	public static void isValidOfflineOrderEmployeeInfo(CustInfoVo custInfoVo,String jobNumber) {
		BizAssertUtil.notNull(custInfoVo, String.format("抱歉，%s，未查询到客户经理信息，请核对正确后，再来下单！", jobNumber));
		BizAssertUtil.notNull(custInfoVo.getMobile(), String.format("抱歉，%s，客户经理信息中手机号缺失，请到HR系统维护后，再来下单！", jobNumber));
	}


	public static void isValidOfflineOrderMemberInfo(String memberMobile, List<Member> members) {
		BizAssertUtil.notEmpty(members, String.format("抱歉，会员信息不存在。请先用【%s】手机号，在乡助APP上注册会员，再来下单！", memberMobile));
		BizAssertUtil.isTrue(members.size() > 1,
				String.format("抱歉，【%s】手机号，在乡助APP上注册的会员出现多个帐号，不允许下单，请联系技术小哥哥处理！", memberMobile));
		//获取最终下单用户信息
		Member member = members.get(0);
		BizAssertUtil.isTrue(ObjectUtils.isEmpty(member) || StringUtils.isEmpty(member.getUserNo()),
				String.format("抱歉，会员信息不存在。请先用【%s】手机号，在乡助APP上注册会员，再来下单！", memberMobile));
	}


	public static void isValidOfflineManualSettlementInfo(OrderOfflineManualSettlementDTO orderOfflineManualSettlement) {
		BizAssertUtil.notNull(orderOfflineManualSettlement.getPaySn(), "抱歉，请选择要结算的订单！");
		BizAssertUtil.notNull(orderOfflineManualSettlement.getOrderOfflineSettlement(), "抱歉，请选择结算类型!");
		BizAssertUtil.notNull(orderOfflineManualSettlement.getOrderType(), "抱歉，请选择的结算订单类型!");
		BizAssertUtil.isTrue(!OrderTypeEnum.isOfflineAll(orderOfflineManualSettlement.getOrderType()),
				String.format("抱歉，订单类型错误。请先校准订单类型【%s】，再来下单！", orderOfflineManualSettlement.getOrderType()));
		if (!CollectionUtils.isEmpty(orderOfflineManualSettlement.getOrderOfflineList())) {
			BizAssertUtil.isTrue(orderOfflineManualSettlement.getOrderOfflineList().size() > 20, "抱歉，回执单信息，一个订单最多上传20笔！");
			String receiptAccount = orderOfflineManualSettlement.getOrderOfflineList().get(0).getReceiptAccount();
			for (OrderOfflineDTO item : orderOfflineManualSettlement.getOrderOfflineList()) {
				BizAssertUtil.isTrue(!receiptAccount.equals(item.getReceiptAccount()), "抱歉，一次下单，收款帐号必须相同，请核对回执单收款帐号！");
			}
		}
	}
    
    public static void isValidOfflineManualSettlementOrderInfo(List<OrderPO> orders) {
    	BizAssertUtil.notEmpty(orders,"抱歉，请选择要结算的订单！");
    	BizAssertUtil.notNull(orders.get(0),"抱歉，请选择要结算的订单！");
    }
    
    /**
     * 收动结算完成，拒绝重复结算
     * @param order
     */
    public static void isValidOfflineManualSettlementFinish(OrderPO order) {
    	BizAssertUtil.isTrue(OrderStatusEnum.isTradeSuccess(order.getOrderState()) && OrderTypeEnum.isOffline(order.getOrderType()),"抱歉，订单已是线下订单，且完成结算完成，拒绝重复操作！");
    }
    
    /**
     * 取消或关闭
     * @param order
     */
    public static void isValidOfflineManualSettlement(OrderPO order) {
    	BizAssertUtil.isTrue(OrderStatusEnum.isClosed(order.getOrderState()),"抱歉，订单已取消或关闭，拒绝操作！");
    }



	/**
	 * 线下补录订单通用校验
	 */
	public static void validOfflineOrder(OrderPO orderPO, Integer paymentTag) {
		BizAssertUtil.notNull(orderPO,"查询不到该订单信息，请联系管理员");
		BizAssertUtil.isTrue(OrderStatusEnum.isClosed(orderPO.getOrderState()),"抱歉，订单已取消或关闭，拒绝操作！");
		BizAssertUtil.isTrue(!OrderTypeEnum.isOfflineAll(orderPO.getOrderType()),"该订单不属于线下补录订单，请检查后再操作！");
		PaymentTagEnum paymentTagEnum = PaymentTagEnum.valueOf(paymentTag);
		BizAssertUtil.isTrue(paymentTagEnum==null || paymentTagEnum.equals(PaymentTagEnum.UNKOWN),"未知收款标签，请检查收款标签后再操作");
	}
	/**
	 * 线下补录订单通用校验
	 */
	public static void validOrderOfflineList(List<OrderOfflineDTO> orderOfflineList) {
		if (!CollectionUtils.isEmpty(orderOfflineList)) {
			BizAssertUtil.isTrue(orderOfflineList.size() > 20, "抱歉，回执单信息，一个订单最多上传20笔！");
			String receiptAccount = orderOfflineList.get(0).getReceiptAccount();
			for (OrderOfflineDTO item : orderOfflineList) {
				BizAssertUtil.isTrue(!receiptAccount.equals(item.getReceiptAccount()), "抱歉，一次下单，收款帐号必须相同，请核对回执单收款帐号！");
				BizAssertUtil.notNull(item.getReceiptTime(),"抱歉，收款时间不能为空");
				BizAssertUtil.notNull(item.getReceiptAmount(),"抱歉，收款金额");
			}
		}
	}

	public static void validOfflineInfoOrder(OrderOfflineInfoDTO offlineInfoDTO) {
		if (ObjectUtils.isEmpty(offlineInfoDTO)) {
			return;
		}
		if(offlineInfoDTO.getInvoiceStatus() != 0 && OrderConst.INVOICE_STATE_1 == offlineInfoDTO.getInvoiceStatus()) {
			BizAssertUtil.notNull(offlineInfoDTO.getInvoiceAmount(),"抱歉，开票金额不能为空");
			BizAssertUtil.notNull(offlineInfoDTO.getInvoiceTime(),"抱歉，开票时间不能为空");
		}
	}
}
