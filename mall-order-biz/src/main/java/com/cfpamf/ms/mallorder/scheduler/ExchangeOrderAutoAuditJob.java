package com.cfpamf.ms.mallorder.scheduler;

import com.cfpamf.ms.mallorder.service.IOrderExchangeService;
import com.cfpamf.ms.mallorder.service.IOrderService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * @Author: zml
 * @CreateTime: 2022/8/17 16:42
 */
@Component
@Slf4j
public class ExchangeOrderAutoAuditJob {

    @Resource
    private IOrderExchangeService orderExchangeService;

    @XxlJob("ExchangeOrderAutoAuditJob")
    public ReturnT<String> execute(String s) throws Exception {

        XxlJobHelper.log("ExchangeOrderAutoAuditJob===============start, DATE :{}", LocalDateTime.now());
        orderExchangeService.exchangeOrderAutoAuditJob();
        XxlJobHelper.log("ExchangeOrderAutoAuditJob===============end, DATE :{}", LocalDateTime.now());
        return ReturnT.SUCCESS;
    }
}
