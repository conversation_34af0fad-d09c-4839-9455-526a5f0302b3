package com.cfpamf.ms.mallorder.scheduler;

import com.cfpamf.ms.mallorder.common.mq.RabbitMQUtils;
import com.cfpamf.ms.mallorder.po.CommonMqEvent;
import com.cfpamf.ms.mallorder.service.ICommonMqEventService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * rabbitmq 未发生的消息补偿
 *
 * 0 0/5 * * * ?    5分钟执行一次
 */
@Component
@Slf4j
public class RabbitMessageCompensateJob {

    @Resource
    private ICommonMqEventService mqEventService;

    @Resource
    private RabbitMQUtils rabbitMQUtils;

    @XxlJob("RabbitMessageCompensateJob")
    public ReturnT<String> execute(String s) throws Exception {

        XxlJobHelper.log("=============== START RabbitMessageCompensateJob , DATE :{}", LocalDateTime.now());
        try {
            // 1.查询近一月未发送，且发送次数小于3次的消息
            List<CommonMqEvent> mqMessage4Resend = mqEventService.getMqMessage4Resend();
            XxlJobHelper.log("RabbitMessageCompensateJob: 重发消息的数量为【{}】", mqMessage4Resend.size());

            // 2.重新发送
            mqMessage4Resend.forEach(message -> {
                rabbitMQUtils.sendByEventId(message.getId());
            });

        } catch (Exception e) {
            log.error("RabbitMessageCompensateJob", e);
        }

        XxlJobHelper.log("=============== END RabbitMessageCompensateJob , DATE :{}", LocalDateTime.now());
        return ReturnT.SUCCESS;
    }

}
