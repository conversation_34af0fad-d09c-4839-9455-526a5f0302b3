package com.cfpamf.ms.mallorder.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.cfpamf.ares.trade.common.enums.*;
import com.cfpamf.ares.trade.domain.dto.InvoiceApplyDTO;
import com.cfpamf.ares.trade.domain.dto.InvoiceGoodsDTO;
import com.cfpamf.ares.trade.domain.dto.InvoiceOrderDTO;
import com.cfpamf.ares.trade.domain.query.InvoiceQuery;
import com.cfpamf.ares.trade.domain.vo.*;
import com.cfpamf.athena.common.domain.dto.ResultEntity;
import com.cfpamf.framework.autoconfigure.common.result.Result;
import com.cfpamf.ms.mallmember.api.MemberFeignClient;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.common.config.InvoiceConfig;
import com.cfpamf.ms.mallorder.common.enums.CommonEnum;
import com.cfpamf.ms.mallorder.common.enums.OrderStatusEnum;
import com.cfpamf.ms.mallorder.common.util.ExternalApiUtil;
import com.cfpamf.ms.mallorder.common.util.ValidUtils;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.controller.fegin.facade.ErpFacade;
import com.cfpamf.ms.mallorder.dto.ProductSkuDetailDTO;
import com.cfpamf.ms.mallorder.integration.facade.AresInvoiceFacade;
import com.cfpamf.ms.mallorder.po.OrderExtendPO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderProductPO;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.vo.ChildOrdersVO;
import com.cfpamf.ms.mallorder.vo.CreateInvoiceParamVO;
import com.cfpamf.ms.mallorder.vo.MallInvoiceVO;
import com.cfpamf.ms.mallorder.vo.OrderProductListVO;
import com.cfpamf.ms.mallshop.api.StoreFeignClient;
import com.cfpamf.ms.mallshop.api.StoreIsolateWhiteListFeignClient;
import com.cfpamf.ms.mallshop.enums.WhiteListEnum;
import com.cfpamf.ms.mallshop.resp.Store;
import com.google.api.client.util.Lists;
import com.slodon.bbc.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 发票实现类
 *
 * <AUTHOR>
 * @since 2024/12/3
 */
@Slf4j
@Service
public class IOrderInvoiceServiceImpl implements IOrderInvoiceService {

    @Autowired
    private IOrderService orderService;

    @Autowired
    private AresInvoiceFacade aresInvoiceFacade;

    @Resource
    private StoreIsolateWhiteListFeignClient storeIsolateWhiteListFeignClient;

    @Autowired
    private IOrderAfterService afterService;

    @Autowired
    private IOrderProductService orderProductService;

    @Autowired
    private IOrderExtendService orderExtendService;

    @Autowired
    private IOrderReturnService orderReturnService;

    @Autowired
    private MemberFeignClient memberFeignClient;

    @Autowired
    private StoreFeignClient storeFeignClient;

    @Autowired
    private ErpFacade erpFacade;

    @Autowired
    private InvoiceConfig invoiceConfig;

    /**
     * 根据订单编号查询发票信息
     *
     * @param member  用户信息
     * @param orderSn 订单编号
     * @return 发票信息
     */
    @Override
    public MallInvoiceVO queryInvoice(Member member, String orderSn) {
        if (Objects.isNull(member) || ValidUtils.isEmpty(member.getMemberId())) {
            throw new BusinessException("请先登录");
        }
        OrderPO orderPO = orderService.getByOrderSn(orderSn);
        if (Objects.isNull(orderPO)) {
            throw new BusinessException("未找到对应订单");
        }
        Integer memberId = orderPO.getMemberId();
        if (!memberId.equals(member.getMemberId())) {
            throw new BusinessException("您无权查看该发票");
        }
        String channelCode = OrderSourceEnum.STANDARD_MALL.getOrderSourceCode();
        InvoiceQuery invoiceQuery = new InvoiceQuery();
        invoiceQuery.setOrderNo(orderSn);
        invoiceQuery.setChannel(channelCode);
        InvoiceVO invoiceVO = ExternalApiUtil.callResultEntityApi(() -> aresInvoiceFacade.queryByChannelAndOrderNo(invoiceQuery), invoiceQuery, "/invoice/query", "发票查询");
        if (Objects.isNull(invoiceVO)) {
            return null;
        }
        return buildMallInvoiceVO(orderPO, invoiceVO);
    }


    private MallInvoiceVO buildMallInvoiceVO(OrderPO orderPO, InvoiceVO invoiceVO) {
        MallInvoiceVO mallInvoiceVO = new MallInvoiceVO();
        mallInvoiceVO.setOrderSn(orderPO.getOrderSn());
        InvoiceOrderVO orderVO = invoiceVO.getOrderVO();
        if (Objects.isNull(orderVO)) {
            return null;
        }
        mallInvoiceVO.setOrderState(orderPO.getOrderState());
        mallInvoiceVO.setOrderStateDesc(OrderStatusEnum.parseDesc(orderPO.getOrderState()));
        mallInvoiceVO.setOrderAmountTotal(orderPO.getOrderAmountTotal());
        mallInvoiceVO.setOrderAmount(orderVO.getTotalAmountAndTax());
        mallInvoiceVO.setMarginOrderAmount(orderPO.getMarginOrderAmount());
        mallInvoiceVO.setGoodsAmount(orderPO.getGoodsAmount());
        mallInvoiceVO.setExpressFee(orderPO.getExpressFee());
        mallInvoiceVO.setExpressFeeTotal(orderPO.getExpressFeeTotal());
        mallInvoiceVO.setActivityDiscountAmount(orderPO.getActivityDiscountAmount());
        mallInvoiceVO.setStoreVoucherAmount(orderPO.getStoreVoucherAmount());
        mallInvoiceVO.setActivityDiscountDetail(orderPO.getActivityDiscountDetail());
        mallInvoiceVO.setStoreVoucherAmount(orderPO.getStoreVoucherAmount());
        mallInvoiceVO.setStoreActivityAmount(orderPO.getStoreActivityAmount());
        mallInvoiceVO.setPlatformVoucherAmount(orderPO.getPlatformVoucherAmount());
        mallInvoiceVO.setPlatformActivityAmount(orderPO.getPlatformActivityAmount());
        mallInvoiceVO.setXzCardAmount(orderPO.getXzCardAmount());
        mallInvoiceVO.setXzCardExpressFeeAmount(orderPO.getXzCardExpressFeeAmount());
        // 设置流水号
        mallInvoiceVO.setFlowNo(invoiceVO.getFlowNo());

        // 设置销货方信息
        mallInvoiceVO.setTaxPayerName(invoiceVO.getTaxPayerName());
        mallInvoiceVO.setTaxPayerAddress(invoiceVO.getTaxPayerAddress());
        mallInvoiceVO.setTaxPayerMobile(invoiceVO.getTaxPayerMobile());
        mallInvoiceVO.setTaxPayerBankName(invoiceVO.getTaxPayerBankName());
        mallInvoiceVO.setTaxPayerBankAccount(invoiceVO.getTaxPayerBankAccount());

        // 设置开票人信息
        mallInvoiceVO.setIssuerName(invoiceVO.getIssuerName());
        mallInvoiceVO.setIssuerCard(invoiceVO.getIssuerCard());

        // 设置购买方信息
        mallInvoiceVO.setBuyerCode(invoiceVO.getBuyerCode());
        mallInvoiceVO.setBuyerName(invoiceVO.getBuyerName());
        mallInvoiceVO.setBuyerAddress(invoiceVO.getBuyerAddress());
        mallInvoiceVO.setBuyerMobile(invoiceVO.getBuyerMobile());
        mallInvoiceVO.setBuyerBankName(invoiceVO.getBuyerBankName());
        mallInvoiceVO.setBuyerBankAccount(invoiceVO.getBuyerBankAccount());
        Integer orderStatus = orderVO.getOrderStatus();
        InvoiceStatusEnum invoiceStatusEnum = InvoiceStatusEnum.getByCode(orderStatus);
        if (!Objects.isNull(invoiceStatusEnum)) {
            mallInvoiceVO.setInvoiceOrderStatusDesc(invoiceStatusEnum.getDesc());
        }
        mallInvoiceVO.setInvoiceOrderStatus(orderStatus);
        mallInvoiceVO.setTotalAmountAndTax(orderVO.getTotalAmountAndTax());
        // 抬头类型
        String buyerType = invoiceVO.getBuyerType();
        mallInvoiceVO.setBuyerType(buyerType);
        InvoiceBuyerTypeEnum buyerTypeEnum = InvoiceBuyerTypeEnum.getByCode(buyerType);
        if (!Objects.isNull(buyerTypeEnum)) {
            mallInvoiceVO.setBuyerTypeDesc(buyerTypeEnum.getDesc());
        }
        // 发票类型
        String invoiceType = invoiceVO.getInvoiceType();
        mallInvoiceVO.setInvoiceTypeCode(invoiceType);
        InvoiceTypeEnum invoiceTypeEnum = InvoiceTypeEnum.getByCode(String.valueOf(invoiceType));
        if (!Objects.isNull(invoiceTypeEnum)) {
            mallInvoiceVO.setInvoiceTypeDesc(invoiceTypeEnum.getDesc());
        }
        // 开票方式
        Integer billingMethod = orderVO.getBillingMethod();
        mallInvoiceVO.setBillingMethodCode(billingMethod);
        InvoiceBillMethodEnum billMethodEnum = InvoiceBillMethodEnum.getByCode(String.valueOf(billingMethod));
        if (!Objects.isNull(billMethodEnum)) {
            mallInvoiceVO.setBillingMethodDesc(billMethodEnum.getDesc());
        }
        // 发票类型
        String orderType = orderVO.getOrderType();
        mallInvoiceVO.setOrderType(orderType);
        InvoiceOrderTypeEnum orderTypeEnum = InvoiceOrderTypeEnum.getByCode(orderType);
        if (!Objects.isNull(orderTypeEnum)) {
            mallInvoiceVO.setOrderTypeDesc(orderTypeEnum.getDesc());
        }
        mallInvoiceVO.setTaxPayerCode(invoiceVO.getTaxPayerCode());
        mallInvoiceVO.setBuyerEmail(invoiceVO.getBuyerEmail());
        mallInvoiceVO.setCreateTime(invoiceVO.getCreateTime());
        mallInvoiceVO.setOpenTime(orderVO.getOpenTime());
        mallInvoiceVO.setFirstApplyTime(orderVO.getFirstApplyTime());
        mallInvoiceVO.setRedApplyTime(orderVO.getRedApplyTime());
        mallInvoiceVO.setRedOpenTime(orderVO.getRedOpenTime());
        List<InvoiceGoodsVO> goodsVOList = orderVO.getGoodsVOList();
        mallInvoiceVO.setGoodsVOList(goodsVOList);
        mallInvoiceVO.setInvoiceOrderFileVOList(orderVO.getInvoiceOrderFileVOList());
        mallInvoiceVO.setOldInvoiceOrderFileVOList(orderVO.getOldInvoiceOrderFileVOList());
        mallInvoiceVO.setNewBlueInvoiceId(orderVO.getNewBlueInvoiceId());
        mallInvoiceVO.setOldInvoiceOrderId(orderVO.getOldInvoiceOrderId());
        mallInvoiceVO.setStoreId(orderPO.getStoreId());
        mallInvoiceVO.setStoreName(orderPO.getStoreName());
        mallInvoiceVO.setOrderDate(DateUtil.format(orderPO.getCreateTime(), DatePattern.NORM_DATETIME_PATTERN));
        Store store = storeFeignClient.getStoreByStoreId(orderPO.getStoreId());
        OrderExtendPO extendByOrderSn = orderExtendService.getOrderExtendByOrderSn(orderPO.getOrderSn());
        List<OrderProductPO> orderProductList = orderProductService.listByOrderSn(orderPO.getOrderSn());
        List<Long> productIdList = goodsVOList.stream().map(InvoiceGoodsVO::getOwnNo).map(Long::valueOf).collect(Collectors.toList());
        // 移除不在发票商品内的数据
        orderProductList.removeIf(op -> !productIdList.contains(op.getProductId()));
        ChildOrdersVO ordersVO = new ChildOrdersVO(orderPO,extendByOrderSn,store.getStoreLogo());
        if (!CollectionUtils.isEmpty(orderProductList)){
            List<OrderProductListVO> productVoList = orderProductList.stream().map(OrderProductListVO::new).collect(Collectors.toList());
            ordersVO.setOrderProductListVOList(productVoList);
        }
        mallInvoiceVO.setOrdersVO(ordersVO);
        return mallInvoiceVO;
    }

    /**
     * 申请开票
     *
     * @param member               用户信息
     * @param createInvoiceParamVO 申请开票的参数
     * @return 申请开票的结果vo
     */
    @Override
    public Boolean createInvoice(Member member, CreateInvoiceParamVO createInvoiceParamVO) {
        String orderSn = createInvoiceParamVO.getOrderSn();
        OrderPO orderPO = orderService.getByOrderSn(orderSn);
        if (Objects.isNull(orderPO)){
            throw new BusinessException("未找到对应订单");
        }
        Boolean whiteList = storeIsolateWhiteListFeignClient.isStoreOnList(WhiteListEnum.INVOICE_WHITE_LIST, orderPO.getStoreId());
        if (!whiteList) {
            // 必须在指定白名单内
            throw new BusinessException("该店铺无法开具发票");
        }
        Integer storeIsSelf = orderPO.getStoreIsSelf();
        if (!OrderConst.STORE_TYPE_SELF_1.equals(storeIsSelf)) {
            // 非自营店铺不允许开票
            throw new BusinessException("入驻店铺无法开具发票");
        }
        boolean hasDuringRefund = orderReturnService.hasDuringRefund(orderSn);
        if (hasDuringRefund){
            throw new BusinessException("该订单有进行中的售后单，无法开具发票");
        }
        Integer orderState = orderPO.getOrderState();
        boolean tradeSuccess = OrderStatusEnum.isTradeSuccess(orderState);
        if (!tradeSuccess) {
            throw new BusinessException("交易成功后才允许开具发票");
        }
        String invoiceStart = invoiceConfig.getStart();
        if (StringUtils.isNotBlank(invoiceStart)){
            // 校验订单创建时间是否在指定时间之后
            Date createTime = orderPO.getCreateTime();
            DateTime date = DateUtil.parse(invoiceStart, "yyyyMMdd");
            if (createTime.before(date)){
                throw new BusinessException(invoiceStart+"之后的订单才允许开具发票");
            }
        }
        // 开具发票
        InvoiceApplyDTO applyDTO = buildInvoiceApplyDTO(member, orderPO, createInvoiceParamVO);
        ResultEntity<Boolean> invoiceResult = aresInvoiceFacade.applyInvoice(applyDTO);
        log.info("invoiceResult:{}",invoiceResult);
        if (invoiceResult.getState() != 200){
            throw new BusinessException("申请失败, "+invoiceResult.getMsg());
        }
        return true;
    }

    /**
     * 发票冲冲
     *
     * @param member             用户信息
     * @return 冲红结果
     */
    @Override
    public Boolean undoInvoice(Member member, CreateInvoiceParamVO createInvoiceParamVO) {
        log.info("start undo inovice,param:{}",createInvoiceParamVO);
        String orderSn = createInvoiceParamVO.getOrderSn();
        OrderPO orderPO = orderService.getByOrderSn(orderSn);
        if (Objects.isNull(orderPO)){
            throw new BusinessException("未找到对应订单");
        }
        // 先查询是否有发票了
        MallInvoiceVO mallInvoiceVO = queryInvoice(member, orderSn);
        if (Objects.isNull(mallInvoiceVO)) {
            throw new BusinessException("该订单未开具发票，无法进行换开");
        }
        if (!createInvoiceParamVO.getIsAfterInvoke()){
            // 校验是否已经冲过红
            Long oldInvoiceOrderId = mallInvoiceVO.getOldInvoiceOrderId();
            if (Objects.nonNull(oldInvoiceOrderId)){
                throw new BusinessException("该发票已经进行过冲红，无法再次冲红");
            }
        }
        Integer storeIsSelf = orderPO.getStoreIsSelf();
        if (!OrderConst.STORE_TYPE_SELF_1.equals(storeIsSelf)) {
            // 非自营店铺不允许开票
            throw new BusinessException("入驻店铺无法开具发票");
        }
        boolean hasDuringRefund = orderReturnService.hasDuringRefund(orderSn);
        if (hasDuringRefund && !createInvoiceParamVO.getIsAfterInvoke()){
            throw new BusinessException("该订单有进行中的售后单，无法开具发票");
        }
        Integer orderState = orderPO.getOrderState();
        boolean tradeSuccess = OrderStatusEnum.isTradeSuccess(orderState);
        if (!tradeSuccess && !createInvoiceParamVO.getIsAfterInvoke()) {
            throw new BusinessException("交易成功后才允许开具发票");
        }
        Boolean whiteList = storeIsolateWhiteListFeignClient.isStoreOnList(WhiteListEnum.INVOICE_WHITE_LIST, orderPO.getStoreId());
        if (!whiteList) {
            // 必须在指定白名单内
            throw new BusinessException("该店铺无法开具发票");
        }
        InvoiceApplyDTO applyDTO = buildInvoiceApplyDTO(member, orderPO, createInvoiceParamVO);

        ResultEntity<InvoiceUndoResultVO> invoiceUndoResult;
        try {
            invoiceUndoResult = aresInvoiceFacade.undoInvoice(applyDTO);
        } catch (Exception e) {
            log.error("invoice undo error", e);
            throw new BusinessException("申请失败, " + e.getMessage());
        }
        if (invoiceUndoResult.getState() != 200){
            throw new BusinessException("申请失败, "+invoiceUndoResult.getMsg());
        }
        if (!invoiceUndoResult.getData().getSuccessOrderNoList().contains(orderSn)){
            throw new BusinessException("换开失败，原因："+invoiceUndoResult.getData().getFailReason().get(orderSn));
        }

        return true;
    }

    /**
     * 根据订单编号判断是否存在发票
     *
     * @param orderSn 订单编号
     * @return 是否存在
     */
    @Override
    public Boolean checkExist(String orderSn) {
        Boolean checkResult = null;
        try {
            checkResult = ExternalApiUtil.callResultEntityApi(() -> aresInvoiceFacade.checkSingleOrderInvoice(OrderSourceEnum.STANDARD_MALL.getOrderSourceCode(), orderSn), orderSn, "/invoice/check", "检查是否有发票");
        } catch (Exception e) {
            return false;
        }
        return checkResult;
    }

    /**
     * 售后冲红
     *
     * @param orderSn 订单编号
     */
    @Override
    public void undoInvoiceFromAfter(String orderSn) {
        CreateInvoiceParamVO createInvoiceParamVO = new CreateInvoiceParamVO();
        createInvoiceParamVO.setOrderSn(orderSn);
        createInvoiceParamVO.setIsAfterInvoke(Boolean.TRUE);
        OrderPO orderPO = orderService.getByOrderSn(orderSn);
        Integer memberId = orderPO.getMemberId();
        Member member = memberFeignClient.getMemberByMemberId(memberId);
        log.info("undoInvoiceFromAfter,orderSn:{}",orderSn);
        undoInvoice(member,createInvoiceParamVO);
    }

    /**
     * 构建开票申请dto
     *
     * @param member  用户信息
     * @param orderPO 电商订单
     * @return 发票申请dto
     */
    private InvoiceApplyDTO buildInvoiceApplyDTO(Member member, OrderPO orderPO, CreateInvoiceParamVO paramVO) {
        InvoiceApplyDTO applyDTO = new InvoiceApplyDTO();
        applyDTO.setChannel(OrderSourceEnum.STANDARD_MALL.getOrderSourceCode());
        applyDTO.setOrderChannel(orderPO.getChannel());
        // 发票类型
        String buyerType = paramVO.getBuyerType();
        // 默认为普通发票
        applyDTO.setInvoiceType(InvoiceTypeEnum.DIGITAL_NORMAL);
        String invoiceType = paramVO.getInvoiceType();
        InvoiceTypeEnum invoiceTypeEnum = InvoiceTypeEnum.getByCode(invoiceType);
        if (Objects.nonNull(invoiceTypeEnum)){
            // 有传值则取传入的值
            applyDTO.setInvoiceType(invoiceTypeEnum);
        }
        if (InvoiceBuyerTypeEnum.COMPANY.getCode().equals(buyerType)) {
            // 企业
            applyDTO.setBuyerType(InvoiceBuyerTypeEnum.COMPANY);
        } else {
            // 其它
            applyDTO.setBuyerType(InvoiceBuyerTypeEnum.INDIVIDUAL);
        }
        applyDTO.setBuyerCode(paramVO.getBuyerCode());
        applyDTO.setBuyerName(paramVO.getBuyerName());
        applyDTO.setBuyerAddress(paramVO.getBuyerAddress());
        applyDTO.setBuyerMobile(paramVO.getBuyerMobile());
        applyDTO.setBuyerBankName(paramVO.getBuyerBankName());
        applyDTO.setBuyerBankAccount(paramVO.getBuyerBankAccount());
        applyDTO.setBuyerEmail(paramVO.getBuyerEmail());
        applyDTO.setOperator(member.getMemberId().toString());
        applyDTO.setPayerCode(orderPO.getStoreId().toString());
        applyDTO.setOrderDTOList(buildOrderDTOList(orderPO,paramVO));
        return applyDTO;
    }

    private List<InvoiceOrderDTO> buildOrderDTOList(OrderPO orderPO,CreateInvoiceParamVO paramVO) {
        List<OrderProductPO> orderProductPOS = orderProductService.listByOrderSn(orderPO.getOrderSn());
        InvoiceOrderDTO orderDTO = new InvoiceOrderDTO();
        orderDTO.setBusinessType(OrderSourceEnum.STANDARD_MALL.getOrderSourceCode());
        orderDTO.setBillingMethod(InvoiceBillMethodEnum.WHOLE);
        orderDTO.setOrderTypeEnum(InvoiceOrderTypeEnum.BLUE);
        // 冲红原因默认为发票有误，后续会再次发起蓝票开具
        orderDTO.setDeprecateReason(InvoiceDeprecateReasonEnum.INVOICE_ERROR);
        // 合计金额固定传0
        orderDTO.setTotalAmount(BigDecimal.ZERO);
        // 合计税额传0
        orderDTO.setTotalTax(BigDecimal.ZERO);
        orderDTO.setRemark(orderPO.getOrderSn());
        orderDTO.setChannelOrderSn(orderPO.getOrderSn());
        orderDTO.setStoreId(orderPO.getStoreId().toString());
        orderDTO.setStoreName(orderPO.getStoreName());
        if (paramVO.getIsAfterInvoke()){
            // 如果是售后发起，则冲红原因是销售退回
            orderDTO.setDeprecateReason(InvoiceDeprecateReasonEnum.SALES_RETURN);
        }
        Boolean isAfterInvoke = paramVO.getIsAfterInvoke();
        List<InvoiceGoodsDTO> goodsDtoList = getGoodsDTOList(orderProductPOS, isAfterInvoke);
        BigDecimal amount = BigDecimal.ZERO;
        for (InvoiceGoodsDTO invoiceGoodsDTO : goodsDtoList) {
            Integer type = invoiceGoodsDTO.getType();
            if (NumberUtils.INTEGER_ONE.equals(type)){
                // 折扣行
                amount = amount.subtract(invoiceGoodsDTO.getAmount());
            }else{
                // 非折扣行
                amount = amount.add(invoiceGoodsDTO.getAmount());
            }
        }
        if (!isAfterInvoke && CollectionUtils.isEmpty(goodsDtoList)){
            throw new BusinessException("没有可以开票的商品，请联系管理员");
        }
        orderDTO.setTotalAmountAndTax(amount);
        orderDTO.setGoodsVOList(goodsDtoList);
        return Collections.singletonList(orderDTO);
    }

    /**
     * 获取商品dto列表
     *
     * @param orderProductPOS 商品信息列表
     * @param isAfterInvoke 是否由售后发起
     * @return 商品dto列表
     */
    @Override
    public List<InvoiceGoodsDTO> getGoodsDTOList(List<OrderProductPO> orderProductPOS, Boolean isAfterInvoke) {
        List<InvoiceGoodsDTO> goodsDtoList = Lists.newArrayList();
        for (OrderProductPO orderProductPO : orderProductPOS) {
            BigDecimal productNum = new BigDecimal(orderProductPO.getProductNum());
            BigDecimal returnNumber = new BigDecimal(orderProductPO.getReturnNumber());
            if (isAfterInvoke || productNum.compareTo(returnNumber) == 0) {
                // 已经全退了
                log.info("buildOrderDTOList continue,商品全退,orderProductPo:{}",orderProductPO);
                continue;
            }
            if (StringUtils.isBlank(orderProductPO.getChannelSkuUnit())){
                // 没有绑定物料，就没有办法获取规格，跳过处理
                log.info("buildOrderDTOList continue,没有物料id,orderProductPo:{}",orderProductPO);
                continue;
            }
            Result<List<ProductSkuDetailDTO>> productSkuInfo = erpFacade.getProductSkuInfo(Collections.singletonList(orderProductPO.getChannelNewSkuId()));
            boolean success = productSkuInfo.isSuccess();
            if (!success){
                log.info("buildOrderDTOList continue,调用erp失败,orderProductPo:{}",orderProductPO);
                throw new BusinessException("获取商品信息失败，请稍后重试");
            }
            List<ProductSkuDetailDTO> skuDetailDtoList = productSkuInfo.getData();
            if (CollectionUtils.isEmpty(skuDetailDtoList)){
                // 没有获取到商品信息
                log.info("buildOrderDTOList continue,erp没有查到对应的商品信息,orderProductPo:{}",orderProductPO);
                continue;
            }
            // 取第一个
            ProductSkuDetailDTO productSkuDetailDTO = skuDetailDtoList.get(0);
            // 计算系数,默认等于1
            BigDecimal percent = BigDecimal.ONE;
            if (returnNumber.compareTo(BigDecimal.ZERO) != 0){
                // 存在退款了的,系数等于（商品数量-退货数量）/商品数量
                percent = (productNum.subtract(returnNumber)).divide(productNum,2, RoundingMode.CEILING);
            }
            List<InvoiceGoodsDTO> invoiceGoodsDTOS = buildInvoiceGoodsDTO(orderProductPO, productSkuDetailDTO, percent);
            goodsDtoList.addAll(invoiceGoodsDTOS);
        }
        return goodsDtoList;
    }

    private List<InvoiceGoodsDTO> buildInvoiceGoodsDTO(OrderProductPO productPO,ProductSkuDetailDTO productSkuDetailDTO,BigDecimal percent){
        List<InvoiceGoodsDTO> result = Lists.newArrayList();
        InvoiceGoodsDTO goodsDTO = new InvoiceGoodsDTO();
        // 默认为正常行
        goodsDTO.setType(InvoiceGoodTypeEnum.NORMAL.getCode());
//        goodsDTO.setProjectName(productPO.getGoodsName());
//        goodsDTO.setSpecName(productSkuDetailDTO.getSkuName());
        // 自行编码使用productId
        goodsDTO.setOwnNo(productPO.getProductId().toString());
//        goodsDTO.setUnit(productSkuDetailDTO.getUnitName());
        goodsDTO.setNumber((long) (productPO.getProductNum() - productPO.getReturnNumber()));
        goodsDTO.setProjectName(productPO.getGoodsName());
        goodsDTO.setSinglePrice(productPO.getProductShowPrice());
        // 商品原价乘以系数
        goodsDTO.setAmount(productPO.getGoodsAmountTotal().multiply(percent));
        goodsDTO.setGoodsId(String.valueOf(productPO.getGoodsId()));
        goodsDTO.setTaxFlag(NumberUtils.INTEGER_ONE);
        BigDecimal activityDiscountAmount = productPO.getActivityDiscountAmount();
        // 存在优惠
        if (activityDiscountAmount.compareTo(BigDecimal.ZERO) != 0){
            // 修改商品行为被折扣行
            goodsDTO.setType(InvoiceGoodTypeEnum.BE_DISCOUNT.getCode());
            // 新增一个折扣行
            InvoiceGoodsDTO discountDTO = new InvoiceGoodsDTO();
            discountDTO.setType(InvoiceGoodTypeEnum.DISCOUNT.getCode());
            discountDTO.setOwnNo(productPO.getProductId().toString());
            discountDTO.setNumber((long) (productPO.getProductNum() - productPO.getReturnNumber()));
            discountDTO.setProjectName(productPO.getGoodsName());
            // 折扣金额 = 原折扣金额乘以系数
            discountDTO.setAmount(activityDiscountAmount.multiply(percent));
            discountDTO.setTaxFlag(NumberUtils.INTEGER_ONE);
            discountDTO.setSinglePrice(productPO.getProductShowPrice());
            discountDTO.setGoodsId(productPO.getGoodsId().toString());
            result.add(discountDTO);
        }
        result.add(goodsDTO);
        return result;
    }

    /**
     * 发票抬头查询企业抬头
     *
     * @param name
     * @param searchType
     * @return
     */
    @Override
    public List<InvoiceBuyerVO> buyerSearch(String name, Integer searchType) {
        return ExternalApiUtil.callResultEntityApi(() -> aresInvoiceFacade.buyerSearch(name, searchType), name, "/invoice/buyerSearch", "根据名称查询抬头信息");
    }

    /**
     * 查询发票抬头
     *
     * @param member
     * @return
     */
    @Override
    public List<InvoiceTitleVO> queryTitleList(Member member) {
        if (Objects.isNull(member)){
            throw new BusinessException("请先登录");
        }
        return ExternalApiUtil.callResultEntityApi(() -> aresInvoiceFacade.queryTitleList(OrderSourceEnum.STANDARD_MALL.getOrderSourceCode(), member.getMemberId().toString()), member.getMemberId(), "queryTitleList", "查询发票抬头");
    }

    /**
     * 查询发票抬头详情
     *
     * @param id 发票抬头主键
     * @return 发票抬头详情
     */
    @Override
    public InvoiceTitleVO queryTitleDetail(Long id) {
        if (Objects.isNull(id)){
            throw new BusinessException("id为空");
        }
        return ExternalApiUtil.callResultEntityApi(() -> aresInvoiceFacade.queryTitleDetail(id), id, "invoice/queryTitleDetail", "查询发票抬头详情");
    }

    /**
     * 更新发票抬头
     *
     * @param vo 发票抬头vo
     */
    @Override
    public void updateTitle(InvoiceTitleVO vo) {
        invoiceTitleCheck(vo);
        if (Objects.isNull(vo.getId())){
            throw new BusinessException("数据已发生变更，请刷新后重试");
        }
        ExternalApiUtil.callResultEntityApi(() -> aresInvoiceFacade.updateTitle(vo), vo, "invoice/updateTitle", "更新发票抬头");
    }

    /**
     * 保存发票抬头
     *
     * @param vo 发票抬头vo
     */
    @Override
    public void saveTitle(InvoiceTitleVO vo) {
        invoiceTitleCheck(vo);
        if (Objects.nonNull(vo.getId())){
            throw new BusinessException("数据已发生变更，请刷新后重试");
        }
        ExternalApiUtil.callResultEntityApi(() -> aresInvoiceFacade.saveTitle(vo), vo, "invoice/saveTitle", "新增发票抬头");
    }

    /**
     * 删除发票抬头
     *
     * @param id 发票抬头id
     */
    @Override
    public void delTitle(Long id) {
        if (Objects.isNull(id)){
            throw new BusinessException("请选择需要删除的数据");
        }
        ExternalApiUtil.callResultEntityApi(() -> aresInvoiceFacade.delTitle(id), id, "invoice/delTitle", "删除发票抬头");
    }

    public void invoiceTitleCheck(InvoiceTitleVO vo){
        if (Objects.isNull(vo)){
            throw new BusinessException("请输入正确的发票抬头数据");
        }
        if (StringUtils.isBlank(vo.getOwner())){
            throw new BusinessException("归属人为空");
        }
        if (StringUtils.isBlank(vo.getBuyerType())){
            throw new BusinessException("抬头类型为空");
        }
        if (StringUtils.isBlank(vo.getInvoiceType())){
            throw new BusinessException("发票类型为空");
        }
        if (StringUtils.isBlank(vo.getBuyerName())){
            throw new BusinessException("购买方不能为空");
        }
        if (InvoiceBuyerTypeEnum.COMPANY.getCode().equals(vo.getBuyerType())){
            // 发票抬头类型为公司时，校验纳税识别号等信息
            if (StringUtils.isBlank(vo.getBuyerCode())){
                throw new BusinessException("纳税人识别号不能为空");
            }
//            if (StringUtils.isBlank(vo.getBuyerAddress())){
//                throw new BusinessException("纳税人地址不能为空");
//            }
//            if (StringUtils.isBlank(vo.getBuyerMobile())){
//                throw new BusinessException("纳税人电话不能为空");
//            }
        }
        if (Objects.isNull(vo.getIsDefault())){
            // 默认为非默认
            vo.setIsDefault(CommonEnum.NO.getCode());
        }
    }

    /**
     * 获取订单可开票金额
     *
     * @param orderSn 订单编号
     * @return 可开票金额
     */
    @Override
    public BigDecimal getInvoiceAmount(String orderSn) {
        OrderPO orderPO = orderService.getByOrderSn(orderSn);
        if (Objects.isNull(orderPO)){
            throw new BusinessException("订单不存在");
        }
        List<OrderProductPO> orderProductPOS = orderProductService.listByOrderSn(orderSn);
        List<InvoiceGoodsDTO> goodsDTOList = getGoodsDTOList(orderProductPOS, false);
        BigDecimal amount = BigDecimal.ZERO;
        for (InvoiceGoodsDTO invoiceGoodsDTO : goodsDTOList) {
            Integer type = invoiceGoodsDTO.getType();
            if (NumberUtils.INTEGER_ONE.equals(type)){
                // 折扣行
                amount = amount.subtract(invoiceGoodsDTO.getAmount());
            }else{
                // 非折扣行
                amount = amount.add(invoiceGoodsDTO.getAmount());
            }
        }
        return amount;
    }

    /**
     * 校验是否可以进行售后平台审核
     *
     * @param orderPO
     */
    @Override
    public boolean checkPlatformAudit(OrderPO orderPO) {
        Boolean whiteList = storeIsolateWhiteListFeignClient.isStoreOnList(WhiteListEnum.INVOICE_WHITE_LIST, orderPO.getStoreId());
        if (!whiteList) {
            // 必须在指定白名单内
            return true;
        }
        Integer storeIsSelf = orderPO.getStoreIsSelf();
        if (!OrderConst.STORE_TYPE_SELF_1.equals(storeIsSelf)) {
            // 非自营店铺不允许开票
            return true;
        }
        Integer orderState = orderPO.getOrderState();
        boolean tradeSuccess = OrderStatusEnum.isTradeSuccess(orderState);
        if (!tradeSuccess) {
            return true;
        }
        try {
            String channelCode = OrderSourceEnum.STANDARD_MALL.getOrderSourceCode();
            InvoiceQuery invoiceQuery = new InvoiceQuery();
            invoiceQuery.setOrderNo(orderPO.getOrderSn());
            invoiceQuery.setChannel(channelCode);
            InvoiceVO invoiceVO = ExternalApiUtil.callResultEntityApi(() -> aresInvoiceFacade.queryByChannelAndOrderNo(invoiceQuery), invoiceQuery, "/invoice/query", "发票查询");
            if (Objects.isNull(invoiceVO)){
                return true;
            }
            InvoiceOrderVO orderVO = invoiceVO.getOrderVO();
            if (Objects.isNull(orderVO)){
                return true;
            }
            Integer orderStatus = orderVO.getOrderStatus();
            InvoiceStatusEnum invoiceStatusEnum = InvoiceStatusEnum.getByCode(orderStatus);
            if (InvoiceStatusEnum.APPLYING == invoiceStatusEnum){
                // 开票中时，不能平台审核
                return false;
            }
        } catch (Exception ignore) {
            return false;
        }
        return true;
    }
}
