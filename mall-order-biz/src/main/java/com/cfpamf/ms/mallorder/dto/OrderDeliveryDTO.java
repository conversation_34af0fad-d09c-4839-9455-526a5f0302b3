package com.cfpamf.ms.mallorder.dto;

import com.cfpamf.ms.mallorder.common.annotation.ExcelImportProperty;
import lombok.Data;

/**
 * 订单发货解析结果
 */
@Data
public class OrderDeliveryDTO {

    @ExcelImportProperty(name = "订单编号")
    private String orderSn;

    @ExcelImportProperty(name = "货品ID")
    private Long productId;

    @ExcelImportProperty(name = "发货方式")
    private Integer deliveryType;

    @ExcelImportProperty(name = "物流公司编码")
    private String expressCompanyCode;

    @ExcelImportProperty(name = "物流单号")
    private String expressNumber;

    @ExcelImportProperty(name = "物流公司")
    private String expressName;

    @ExcelImportProperty(name = "联系人")
    private String deliverName;

    @ExcelImportProperty(name = "联系方式")
    private String deliverMobile;

}
