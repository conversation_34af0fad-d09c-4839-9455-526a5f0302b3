package com.cfpamf.ms.mallorder.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cfpamf.framework.autoconfigure.mybatis.BaseRepoServiceImpl;
import com.cfpamf.ms.mallgoods.facade.vo.ProductActivityGoodsBindVO;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.dto.CouponSendNotifyDto;
import com.cfpamf.ms.mallorder.enums.CouponSendStateEnum;
import com.cfpamf.ms.mallorder.mapper.OrderProductCouponMapper;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.po.OrderProductCouponPO;
import com.cfpamf.ms.mallorder.po.OrderProductPO;
import com.cfpamf.ms.mallorder.service.IOrderProductCouponService;
import com.slodon.bbc.core.exception.MallException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 订单货品优惠券信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-07
 */
@Service
@Slf4j
public class OrderProductCouponServiceImpl extends BaseRepoServiceImpl<OrderProductCouponMapper, OrderProductCouponPO> implements IOrderProductCouponService {

	@Autowired
	private OrderModel orderModel;

	@Override
	public Boolean insertOrderProductCoupon(OrderProductPO orderProductPO, List<ProductActivityGoodsBindVO> couponList) {
		List<OrderProductCouponPO> insetPOs = new ArrayList<>(couponList.size());
		for (ProductActivityGoodsBindVO coupon : couponList) {
			OrderProductCouponPO orderProductCouponPO = new OrderProductCouponPO();
			orderProductCouponPO.setOrderSn(orderProductPO.getOrderSn());
			orderProductCouponPO.setOrderProductId(orderProductPO.getOrderProductId());
			orderProductCouponPO.setCouponId(coupon.getActivityId());
			orderProductCouponPO.setCouponName(coupon.getActivityTitle());
			orderProductCouponPO.setCouponType(coupon.getActivityType());
			orderProductCouponPO.setCouponChannel(coupon.getActivityChannel());
			orderProductCouponPO.setState(CouponSendStateEnum.VALUE_1.getValue());
			orderProductCouponPO.setCreateBy(orderProductPO.getCreateBy());
			orderProductCouponPO.setUpdateBy(orderProductPO.getUpdateBy());
			// 保存商品返回的出资方和出售价
			orderProductCouponPO.setFunder(coupon.getFunder());
			orderProductCouponPO.setRetailPrice(coupon.getRetailPrice());
			insetPOs.add(orderProductCouponPO);
		}
		saveBatch(insetPOs);
		return Boolean.TRUE;
	}

	/**
	 * 封装订单优惠券实体
	 *
	 * @param orderProductPO          orderProductPo
	 * @param activityGoodsBindVOList 活动绑定vo列表
	 * @return 订单优惠券实体
	 */
	@Override
	public List<OrderProductCouponPO> buildOrderProductCoupon(OrderProductPO orderProductPO, List<ProductActivityGoodsBindVO> activityGoodsBindVOList) {
		List<OrderProductCouponPO> insetPOs = new ArrayList<>(activityGoodsBindVOList.size());
		Integer productNum = orderProductPO.getProductNum();
		for (ProductActivityGoodsBindVO coupon : activityGoodsBindVOList) {
			OrderProductCouponPO orderProductCouponPO = new OrderProductCouponPO();
			orderProductCouponPO.setOrderSn(orderProductPO.getOrderSn());
			orderProductCouponPO.setOrderProductId(orderProductPO.getOrderProductId());
			orderProductCouponPO.setCouponId(coupon.getActivityId());
			orderProductCouponPO.setCouponName(coupon.getActivityTitle());
			orderProductCouponPO.setCouponType(coupon.getActivityType());
			orderProductCouponPO.setCouponChannel(coupon.getActivityChannel());
			orderProductCouponPO.setState(CouponSendStateEnum.VALUE_1.getValue());
			orderProductCouponPO.setCreateBy(orderProductPO.getCreateBy());
			orderProductCouponPO.setUpdateBy(orderProductPO.getUpdateBy());
			// 保存商品返回的出资方和出售价
			orderProductCouponPO.setFunder(coupon.getFunder());
			BigDecimal retailPrice = null == coupon.getRetailPrice() ? BigDecimal.ZERO : coupon.getRetailPrice();
			orderProductCouponPO.setRetailPrice(retailPrice.multiply(BigDecimal.valueOf(productNum)));
			insetPOs.add(orderProductCouponPO);
		}
		return insetPOs;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, transactionManager = "masterdbTx")
	public Boolean couponSendNotify(CouponSendNotifyDto notifyDto) {

		BizAssertUtil.isTrue(!CouponSendStateEnum.contains(notifyDto.getState()), "优惠券发放状态码值错误！");

		if (isAllCouponSend(notifyDto.getOrderSn(), notifyDto.getCouponChannel())) {
			return Boolean.TRUE;
		}

		LambdaUpdateWrapper<OrderProductCouponPO> updateWrapper = new LambdaUpdateWrapper<>();
		updateWrapper.eq(OrderProductCouponPO::getOrderSn, notifyDto.getOrderSn())
				.eq(OrderProductCouponPO::getState, 1)
				.eq(OrderProductCouponPO::getCouponChannel, notifyDto.getCouponChannel())
				.set(OrderProductCouponPO::getState, notifyDto.getState());
		boolean update = update(updateWrapper);
		if (!update) {
			throw new MallException("卡券订单更新优惠券发放状态失败，orderSn：" + notifyDto.getOrderSn());
		}

		return Boolean.TRUE;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, transactionManager = "masterdbTx")
	public Boolean isAllCouponSend(String orderSn, Integer couponChannel) {
		LambdaQueryWrapper<OrderProductCouponPO> query = new LambdaQueryWrapper<>();
		query.eq(OrderProductCouponPO::getOrderSn, orderSn)
				.ne(OrderProductCouponPO::getState, 2)
				.select(OrderProductCouponPO::getOrderProductCouponId);
		if (couponChannel != null) {
			query.eq(OrderProductCouponPO::getCouponChannel, couponChannel);
		}
		List<OrderProductCouponPO> couponPOList = list(query);
		log.info("query OrderProductCouponPO with orderSn:{},couponChannel:{},Result;{}",
				orderSn, couponChannel, JSON.toJSONString(couponChannel));
		boolean empty = CollectionUtils.isEmpty(couponPOList);
		if (empty && Objects.isNull(couponChannel)) {
			orderModel.finishOrder(orderSn);
		}
		return empty;
	}

}
