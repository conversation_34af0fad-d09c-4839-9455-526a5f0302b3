package com.cfpamf.ms.mallorder.integration.loan;


import com.alibaba.fastjson.JSON;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.loan.facade.request.external.mall.*;
import com.cfpamf.ms.loan.facade.vo.external.mall.*;
import com.cfpamf.ms.loan.facade.vo.loanBefore.apply.credit.RepaymentVo;
import com.cfpamf.ms.mallorder.common.base.ResultT;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.cfpamf.ms.mallorder.common.util.ExternalApiUtil;
import com.cfpamf.ms.mallorder.integration.facade.CdMallOrderFacade;
import com.cfpamf.ms.mallorder.integration.facade.CdMallOrderQueryFacade;
import com.cfpamf.ms.mallorder.vo.WithholdCdMallCheckRequest;
import com.cfpamf.ms.mallorder.vo.WithholdCheckResponseVO;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.exception.MallException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import java.util.List;

/**
 * 信贷支付 交互接口
 *
 * <AUTHOR>
 * @Create 2021-11-03 19:16
 * @Description 信贷支付 交互接口
 */
@Slf4j
@Component
public class LoanPayIntegration {

    @Autowired
    private CdMallOrderQueryFacade cdMallOrderQueryFacade;
    @Resource
    private CdMallOrderFacade cdMallOrderFacade;

    /**
     * 校验是否支持用呗
     *
     * @param request
     * @return
     */
    public boolean isPreconditionCheckAllow(CdmallPreconditionCheckRequest request) {
        return this.loanPreconditionCheck(request).isWdAllowed();
    }

    /**
     * 获取当前订单可用支付列表
     *
     * @param request
     * @return
     */
    public List<RepaymentVo> listLoanRepayment(CdmallPreconditionCheckRequest request) {
        return this.loanPreconditionCheck(request).getRepaymentTypeList();
    }

    /**
     * 电商用呗前置条件判断
     *
     * @param request
     * @return
     */
    private CdmallPreconditionCheckVo loanPreconditionCheck(@RequestBody CdmallPreconditionCheckRequest request) {
        Result<CdmallPreconditionCheckVo> result = null;
        try {
            result = cdMallOrderQueryFacade.cdmallPreconditionCheck(request);
        } catch (Exception e) {
            log.warn("用呗决策前置校验 异常:{}", request.getBizId(), e);
            return new CdmallPreconditionCheckVo();
        }

        if (result == null || !result.isSuccess()) {
            log.warn("用呗决策前置校验 失败:{}", request.getBizId());
            return new CdmallPreconditionCheckVo();
        }

        return result.getData();
    }

    /**
     * @param request
     * @return com.cfpamf.common.ms.result.Result<com.cfpamf.ms.loan.facade.vo.external.mall.PayModeListVO>
     * @description :可用额度列表
     */
    public PayModeListVO getAvailablePayMode(OverallPreWithdrawRequest request) {
        return ExternalApiUtil.callResultApi(() -> cdMallOrderFacade.getAvailablePayMode(request), request,
                "/loan/externalBusiness/getAvailablePayMode", "可用额度列表");
    }

    /**
     * @param request
     * @return com.cfpamf.ms.loan.facade.vo.external.mall.MallContractContentVO
     * @description : 预览合同接口
     */
    public MallContractContentVO contractPreviewV3(ContractPreviewRequest request) {
        ResultT<MallContractContentVO> result = cdMallOrderQueryFacade.contractPreviewV3(request);
        log.info("预览合同接口，返回参数:{}", JSON.toJSONString(result));
        if (result == null) {
            throw new BusinessException("预览合同信贷未返回信息");
        }
        if (!result.isSuccess()) {
            throw new BusinessException(result.getMessage());
        }
        return result.getData();
    }

    /**
     * 获取合同模板列表
     *
     * @param request
     */
    public MallContractCodesVo listLoanContractCode(GetMallContractCodesRequest request) {
        ResultT<MallContractCodesVo> result;
        try {
            result = cdMallOrderQueryFacade.getMallPreviewContractCodes(request);
        } catch (Exception e) {
            throw new MSBizNormalException("99999", "获取合同列表-信贷服务异常:" + e.getMessage());
        }
        if (result == null) {
            throw new MSBizNormalException("99999", "获取合同列表-信贷服务null");
        } else if (!result.isSuccess()) {
            throw new MSBizNormalException("99999", result.getMessage());
        }
        return result.getData();
    }

    /**
     * 校验能否进行代扣（电商）
     *
     * @param orderId 信贷申请单号 - 对应电商订单编号
     */
    public WithholdCheckResponseVO checkCdMallCanDoWithhold(String orderId) {
        Result<WithholdCheckResponseVO> result;
        try {
            WithholdCdMallCheckRequest request = new WithholdCdMallCheckRequest();
            request.setOrderId(orderId);
            result = cdMallOrderQueryFacade.checkCdMallCanDoWithhold(request);
        } catch (Exception e) {
            throw new MSBizNormalException("99999", "校验能否进行代扣（电商）-信贷服务异常:" + e.getMessage());
        }
        if (result == null) {
            throw new MSBizNormalException("99999", "校验能否进行代扣（电商）-信贷服务null");
        } else if (!result.isSuccess()) {
            throw new MSBizNormalException("99999", result.getMessage());
        }
        return result.getData();
    }

    /**
     * @param orderSn
     * @return com.cfpamf.ms.loan.facade.vo.external.mall.CdmallOrderVo
     * @description :根据订单号获取贷款信息
     */
    public CdmallOrderVo getLoanInfoByOrderSn(String orderSn) {
        CdmallOrderVo cdmallOrderVo = ExternalApiUtil.callResultApi(() -> cdMallOrderQueryFacade.getCdmallOrder(orderSn),
                orderSn, "/loan/externalBusiness/getCdmallOrder", "查询贷款类信息");
        return cdmallOrderVo;
    }

    /**
     * 用呗重新代付
     *
     * @param redrawRequest 请求参数
     * @return 处理结果
     */
    public Result reLendingCdMallApply(RedrawCdMallApplyRequest redrawRequest) {
        Result result;
        try {
            result = cdMallOrderFacade.redrawCdMallApply(redrawRequest);
        } catch (Exception e) {
            throw new BusinessException(ErrorCodeEnum.S.SYSTEM_EXCEPTION.getCode(), "请求 ms-loan 异常:" + e.getMessage() + " - 请重试");
        }
        if (result == null) {
            throw new MallException("重新代付未返回任何信息");
        }
        if (!result.isSuccess()) {
            throw new BusinessException(result.getMessage());
        }
        return result;

    }
    public List<CdmallOrderUpdatePlanLoanVo> updateCdmallOrderPlanLoanDate(List<CdmallOrderUpdatePlanLoan> requestList,String updateName) {
        Result<List<CdmallOrderUpdatePlanLoanVo>> result;
        try {
            CdmallOrderUpdatePlanLoanRequest request = new CdmallOrderUpdatePlanLoanRequest();
            request.setCdmallOrderUpdatePlanLoanList(requestList);
            request.setUpdateUser(updateName);
            result = cdMallOrderFacade.updateCdmallOrderPlanLoanDate(request);
        } catch (Exception e) {
            throw new BusinessException("批量更新计划放款日期调用信贷接口异常");
        }
        if (result == null) {
            throw new MallException("批量更新计划放款日期调用信贷接口异常");
        }
        if (!result.isSuccess()) {
            throw new BusinessException(result.getMessage());
        }
        return result.getData();

    }


}
