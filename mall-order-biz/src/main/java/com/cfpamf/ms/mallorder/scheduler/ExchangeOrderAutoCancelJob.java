package com.cfpamf.ms.mallorder.scheduler;


import com.cfpamf.ms.mallorder.service.IOrderExchangeService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

@Component
@Slf4j
public class ExchangeOrderAutoCancelJob {
    @Resource
    private IOrderExchangeService orderExchangeService;

    @XxlJob("ExchangeOrderAutoCancelJob")
    public ReturnT<String> execute(String s) throws Exception {

        XxlJobHelper.log("ExchangeOrderAutoCancelJob===============start, DATE :{}", LocalDateTime.now());
        orderExchangeService.exchangeOrderAutoCancelJob();
        XxlJobHelper.log("ExchangeOrderAutoCancelJob===============end, DATE :{}", LocalDateTime.now());
        return ReturnT.SUCCESS;
    }

}
