package com.cfpamf.ms.mallorder.common.enums;

import com.alibaba.fastjson.annotation.JSONType;
import com.baomidou.mybatisplus.annotation.IEnum;
import com.cfpamf.mallpayment.facade.enums.PayWayEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 支付方式
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/2 14:07
 */
@AllArgsConstructor
@Getter
@JSONType(serializeEnumAsJavaBean = true)
public enum PayMethodEnum implements IEnum<String> {

    WXPAY("WXPAY", "微信支付", PayWayEnum.WX_PAY_V3),
    ALIPAY("ALIPAY", "支付宝", PayWayEnum.ALI_PAY),
    ENJOY_PAY("ENJOY_PAY", "用呗支付", PayWayEnum.ENJOY_PAY),
    CREDIT_PAY("CREDIT_PAY", "授信额度", PayWayEnum.CREDIT_PAY),
    FOLLOW_HEART("FOLLOW_HEART", "随心取", PayWayEnum.FOLLOW_HEART),
    BALANCE("BALANCE", "虚拟账余额", PayWayEnum.BALANCE),
    CARD_VOUCHER("CARD_VOUCHER", "卡券支付",null),
    CARD("CARD", "乡助卡支付",null),
    MALL_BALANCE("MALL_BALANCE", "余额", null),
    AGREED_PAY("AGREED_PAY", "协议支付", null),
    BANK_PAY("BANK_PAY", "银行卡转账", null),
    BANK_TRANSFER("BANK_TRANSFER", "银行卡汇款", PayWayEnum.BANK_TRANSFER),
    COMBINATION_PAY("COMBINATION_PAY", "组合支付", null),
    ONLINE("ONLINE", "在线支付", null);

    String value;
    String desc;
    PayWayEnum payWay;

    public static PayMethodEnum getValue(String value) {
        for (PayMethodEnum ps : PayMethodEnum.values()) {
            if (value.equals(ps.value)) {
                return ps;
            }
        }
        return null;
    }

    public static PayMethodEnum getByPaymentCode(String value) {

        PayWayEnum payWayEnum = PayWayEnum.getValue(value);
        for (PayMethodEnum ps : PayMethodEnum.values()) {
        	if (ObjectUtils.isNotEmpty(ps.payWay) && (ps.payWay == payWayEnum)) {
        		return ps;
        	}
        	if (ps.getValue() == value) {
        		return ps;
        	}
        }
        return null;
    }

    public static boolean isLoanPay(String value) {
        return isLoanPay(getByPaymentCode(value));
    }

    public static boolean isLoanPay(PayMethodEnum payWay) {
        return payWay == ENJOY_PAY || payWay == CREDIT_PAY || payWay == FOLLOW_HEART;
    }

    public static List<String> getLoanPay(){
        return Arrays.asList(PayMethodEnum.ENJOY_PAY.getValue(),
        PayMethodEnum.CREDIT_PAY.getValue(), PayMethodEnum.FOLLOW_HEART.getValue());
    }

    public static boolean isThirdPartyPay(PayMethodEnum payWay) {
        return payWay == ALIPAY || payWay == WXPAY;
    }

    /**
     * 是否是微信支付宝支付方式
     *
     * @param payWay    支付方式
     * @return          true/false
     */
    public static boolean isWxAliPay(PayMethodEnum payWay) {
        if (Objects.isNull(payWay)) {
            return Boolean.FALSE;
        }
        return payWay == ALIPAY || payWay == WXPAY;
    }

    public static boolean isWxAliPay(String payMethodCode) {
        return isWxAliPay(getByPaymentCode(payMethodCode));
    }

    /**
     * 是否是微信支付
     *
     * @param payWay    支付方式
     * @return          true/false
     */
    public static boolean isWxPay(PayMethodEnum payWay) {
        if (Objects.isNull(payWay)) {
            return Boolean.FALSE;
        }
        return WXPAY == payWay;
    }

    public static boolean isWxPay(String payMethodCode) {
        return isWxPay(getByPaymentCode(payMethodCode));
    }

    /**
     * 是否是支付宝支付
     *
     * @param payWay    支付方式
     * @return          true/false
     */
    public static boolean isAliPay(PayMethodEnum payWay) {
        if (Objects.isNull(payWay)) {
            return Boolean.FALSE;
        }
        return ALIPAY == payWay;
    }

    public static boolean isAliPay(String payMethodCode) {
        return isAliPay(getByPaymentCode(payMethodCode));
    }

    public static boolean isBankTransfer(PayMethodEnum payWay) {
        return payWay == BANK_TRANSFER;
    }

    /**
     * 是否组合支付
     *
     * @param value
     * @return
     */
    public static boolean isCombinationPay(String value) {
       return COMBINATION_PAY.value.equals(value);
    }

    @Override
    public String getValue(){
        return this.value;
    }

    public boolean isTrue(String payWay){
        if (StringUtils.isBlank(payWay)){
            return false;
        }
        return this.getValue().equals(payWay.trim());
    }
}
