package com.cfpamf.ms.mallorder.v2.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cfpamf.ms.mallgoods.facade.api.ProductStockFeignClient;
import com.cfpamf.ms.mallgoods.facade.request.StockCutRequest;
import com.cfpamf.ms.mallgoods.facade.vo.StockCutVO;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.dto.OrderSubmitDTO;
import com.cfpamf.ms.mallorder.dto.OrderSubmitDTO.OrderInfo;
import com.cfpamf.ms.mallorder.dto.OrderSubmitDTO.OrderInfo.OrderProductInfo;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.request.OrderPromotionSendCouponExample;
import com.cfpamf.ms.mallorder.service.IOrderProductExtendService;
import com.cfpamf.ms.mallorder.v2.builder.StockBuilder;
import com.cfpamf.ms.mallorder.v2.domain.dto.OrderDTO;
import com.cfpamf.ms.mallorder.v2.service.OrderDeductionService;
import com.cfpamf.ms.mallorder.v2.service.OrderPromotionSendCouponService;
import com.cfpamf.ms.mallpromotion.api.CouponTccFeign;
import com.cfpamf.ms.mallpromotion.api.PromotionActivitiesTccFeign;
import com.cfpamf.ms.mallpromotion.api.PromotionCommonFeignClient;
import com.cfpamf.ms.mallpromotion.vo.CouponSendVO;
import com.cfpamf.ms.mallpromotion.vo.CouponUseVO;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.util.StringUtil;
import io.seata.core.context.RootContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class OrderDeductionServiceImpl implements OrderDeductionService {

    @Autowired
    private ProductStockFeignClient productStockFeignClient;

    @Autowired
    private CouponTccFeign couponTccFeign;

    @Autowired
    private PromotionActivitiesTccFeign promotionActivitiesTccFeign;

    @Autowired
    private PromotionCommonFeignClient promotionCommonFeignClient;

    @Autowired
    private OrderPromotionSendCouponService orderPromotionSendCouponService;

    @Autowired
    private IOrderProductExtendService orderProductExtendService;

    /**
     * 扣减订单资源
     * 
     * @param orderSubmitDTO
     * @param member
     * @param order
     */
    @Override
    public void deductionOrderResource(OrderSubmitDTO orderSubmitDTO, Member member, OrderDTO order) {
        // 提交营销活动订单
        this.submitPromotionActivitiesOrder(orderSubmitDTO, member, order);
        // 扣减库存
        this.deductionStockBatch(order);
        // 使用优惠券
        this.couponUseBatch(orderSubmitDTO, member, order);
    }

    /**
     * 订单支付成功营销活动提交订单
     * 
     * @param orderSn
     * @param paySn
     * @param tradeSn
     * @param paymentName
     * @param paymentCode
     */
    @Override
    public void orderPaySuccessSubmitPromotionActivities(String orderSn, String paySn, String tradeSn,
        String paymentName, String paymentCode) {
        // 订单产品营销活动资源对应商品
        List<OrderProductExtendPO> orderProductExtendList =
            orderProductExtendService.getOrderProductExtendPOList(orderSn);
        if (CollectionUtils.isEmpty(orderProductExtendList)) {
            log.warn("订单支付成功营销活动提交订单，orderProductExtendList 为空拒绝执行，orderSn：{} paySn：{}", orderSn, paySn);
            return;
        }
        log.info("订单支付成功营销活动提交订单API调用，xid:【{}】 orderSn:{} -> paySn:{}  tradeSn:{}->  paymentName:{}->  paymentCode:{} ",
            RootContext.getXID(), orderSn, paySn, tradeSn, paymentName, paymentCode);
        JsonResult<Boolean> result = promotionActivitiesTccFeign.orderPaySuccess(orderSn,
            orderProductExtendList.get(0).getPromotionType(), paySn, tradeSn, paymentName, paymentCode, "mall-order");
        log.info(
            "订单支付成功营销活动提交订单API结果，xid:【{}】 orderSn:{} -> paySn:{}  tradeSn:{}-> paymentName:{}-> paymentCode:{} result：{}",
            RootContext.getXID(), orderSn, paySn, tradeSn, paymentName, paymentCode, result);
        // 扣减库存失败
        BizAssertUtil.isTrue(200 != result.getState(), "抱歉，下单失败，提交营销活动订单失败，请稍后再试！");
        // 扣减库存失败
        BizAssertUtil.isTrue(!result.getData(), "抱歉，下单失败，提交营销活动订单失败，请稍后再试！");
    }

    /**
     * 订单支付成功优惠券赠送
     * 
     * @param orderSn
     * @param memberId
     * @param memberName
     */
    @Override
    public void orderPayCouponGive(String orderSn, Integer memberId, String memberName) {
        log.info("订单支付成功优惠券赠送，orderSn:{} -> couponSendVOs:{}-> memberId:{}  memberName:{}", orderSn, memberId,
            memberName);
        OrderPromotionSendCouponExample sendCouponExample = new OrderPromotionSendCouponExample();
        sendCouponExample.setOrderSn(orderSn);
        LambdaQueryWrapper<OrderPromotionSendCouponPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(OrderPromotionSendCouponPO::getOrderSn, orderSn);
        List<OrderPromotionSendCouponPO> orderPromotionSendCouponList =
            orderPromotionSendCouponService.list(queryWrapper);
        if (CollectionUtils.isEmpty(orderPromotionSendCouponList)) {
            log.warn("订单未赠送优惠券，无需操作。 orderSn:{} -> couponSendVOs:{}-> memberId:{}  memberName:{}", orderSn, memberId,
                memberName);
            return;
        }
        List<CouponSendVO> couponSendVOs = new ArrayList<>();
        for (OrderPromotionSendCouponPO orderPromotionSendCouponPO : orderPromotionSendCouponList) {// 查询优惠券信息
            if (orderPromotionSendCouponPO.getNumber() <= 0) {
                // 优惠券赠送优惠券张数<= 0
                log.error("优惠券赠送优惠券张数<= 0，不赠送，couponId:{} 赠与数量：{}" + orderPromotionSendCouponPO.getCouponId(),
                    orderPromotionSendCouponPO.getNumber());
                continue;
            }
            CouponSendVO couponSendVO = new CouponSendVO();// 此对象中，只需要传入couponId，和赠与数量
            couponSendVO.setCouponId(orderPromotionSendCouponPO.getCouponId());
            couponSendVO.setReceiveNum(orderPromotionSendCouponPO.getNumber());
            couponSendVOs.add(couponSendVO);
        }
        if (CollectionUtils.isEmpty(couponSendVOs)) {
            log.error("couponSendVOs为空，无需操作。 orderSn:{} -> couponSendVOs:{}-> memberId:{}  memberName:{}", orderSn,
                memberId, memberName);
            return;
        }
        log.info("订单赠送优惠券，xid:【{}】 orderSn:{} -> couponSendVOs:{}-> memberId:{}  memberName:{}", RootContext.getXID(),
            orderSn, memberId, memberName);
        JsonResult<Boolean> result =
            couponTccFeign.couponSend(couponSendVOs, "mall-order", orderSn, memberId, memberName);
        log.info("订单支付成功优惠券赠送，xid:【{}】 orderSn:{} -> couponSendVOs:{}-> memberId:{}  memberName:{}，result：{}",
            RootContext.getXID(), orderSn, couponSendVOs, memberId, memberName, result);
    }

    /**
     * 扣减库存
     * 
     * @param order
     */
    private void deductionStockBatch(OrderDTO order) {
        log.info("扣减订单库存，orderList:{} -> orderProductList:{}", order.getOrders(), order.getOrderProducts());
        if (CollectionUtils.isEmpty(order.getOrders())) {
            log.warn("扣减订单库存，orderList 为空拒绝执行");
            return;
        }
        if (CollectionUtils.isEmpty(order.getOrderProducts())) {
            log.warn("扣减订单库存，orderProductList 为空拒绝执行");
            return;
        }
        Map<String, OrderPO> orderMap =
            order.getOrders().stream().collect(HashMap::new, (k, v) -> k.put(v.getOrderSn(), v), HashMap::putAll);
        log.info("扣减库存orderMap：{}，", orderMap);
        for (OrderProductPO orderProductPO : order.getOrderProducts()) {
            StockCutRequest stockCutRequest = StockBuilder.buildDeductionStock(orderMap, orderProductPO);
            log.info("扣减订单库存API调用，xid:【{}】 productId:{}  stockCutRequest:{} ", RootContext.getXID(),
                stockCutRequest.getProductId(), stockCutRequest);
            JsonResult<List<StockCutVO>> result = productStockFeignClient.reduceStock(stockCutRequest);
            log.info("扣减订单库存API结果，xid:【{}】 productId:{}  stockCutRequest:{} result:{}", RootContext.getXID(),
                stockCutRequest.getProductId(), stockCutRequest, result);
            // 扣减库存失败
            BizAssertUtil.isTrue(200 != result.getState(), "抱歉，下单失败，当前库存不足，请稍后再试！");

        }
    }

    /**
     * 提交营销活动订单
     * 
     * @param orderSubmitDTO
     * @param member
     * @param order
     */
    private void submitPromotionActivitiesOrder(OrderSubmitDTO orderSubmitDTO, Member member, OrderDTO order) {
        log.info("提交营销活动订单，parentSn:{} -> order:{}", order.getParentSn(), order);
        if (CollectionUtils.isEmpty(orderSubmitDTO.getOrderInfoList())) {
            log.warn("提交营销活动订单，orderInfoList 为空拒绝执行");
            return;
        }
        // 订单商品信息key->orderSn
        Map<String, OrderProductPO> orderProductMap = order.getOrderProducts().stream().collect(HashMap::new,
            (k, v) -> k.put(v.getOrderSn(), v), HashMap::putAll);
        for (OrderInfo orderInfo : orderSubmitDTO.getOrderInfoList()) {
            boolean isSpecial = !StringUtil.isNullOrZero(orderInfo.getPromotionType())
                && promotionCommonFeignClient.specialOrder(orderInfo.getPromotionType());
            if (orderInfo.getOrderProductInfoList().size() != 1 || !isSpecial) {
                log.info(
                    "提交营销活动订单，商品数量【orderProductSize:{}】不为1，isSpecial【{}】不为true跳过当前调用， parentSn:{} -> orderSn:{}  promotionType：{} ",
                    order.getOrderProducts().size(), isSpecial, order.getParentSn(), orderInfo.getOrderSn(),
                    orderInfo.getPromotionType());
                continue;
            }
            OrderProductPO orderProductPO = orderProductMap.get(orderInfo.getOrderSn());
            for (OrderProductInfo orderProductInfo : orderInfo.getOrderProductInfoList()) {
                // 拼团、秒杀 提交活动订单+扣除活动库存
                OrderProduct product = new OrderProduct();
                product.setOrderSn(orderInfo.getOrderSn());
                product.setMemberId(member.getMemberId());
                product.setGoodsId(orderProductInfo.getGoodsId());
                product.setGoodsName(orderProductInfo.getGoodsName());
                product.setProductImage(orderProductInfo.getProductImage());
                product.setProductId(orderProductInfo.getProductId());
                product.setProductNum(orderProductInfo.getBuyNum());
                product.setOrderProductId(orderProductPO.getOrderProductId());
                product.setSpellTeamId(orderProductInfo.getSpellTeamId());
                product.setTriggerSource("tcc");
                log.info("提交营销活动订单API调用，xid:【{}】 parentSn:{} -> orderSn:{}  promotionType：{} ", RootContext.getXID(),
                    order.getParentSn(), orderInfo.getOrderSn(), orderInfo.getPromotionType());
                JsonResult<Boolean> result = promotionActivitiesTccFeign.submitOrder(orderInfo.getPromotionType(),
                    orderInfo.getOrderSn(), "mall-order", product);
                log.info("提交营销活动订单API结果，xid:【{}】 parentSn:{} -> orderSn:{}  promotionType：{} result：{}",
                    RootContext.getXID(), order.getParentSn(), orderInfo.getOrderSn(), orderInfo.getPromotionType(),
                    result);
                // 扣减库存失败
                BizAssertUtil.isTrue(200 != result.getState(), "抱歉，下单失败，营销活动订单失败，请稍后再试！");
                // 扣减库存失败
                BizAssertUtil.isTrue(!result.getData(), "抱歉，下单失败，提交营销活动订单失败，请稍后再试！");
            }
        }
    }

    /**
     * 使用优惠券
     * 
     * @param orderSubmitDTO
     * @param member
     * @param order
     */
    private void couponUseBatch(OrderSubmitDTO orderSubmitDTO, Member member, OrderDTO order) {
        log.info("订单使用店铺优惠券， xid:【{}】 parentSn:{} -> orderList:{}", RootContext.getXID(), order.getParentSn(),
            orderSubmitDTO.getOrderInfoList());
        if (CollectionUtils.isEmpty(orderSubmitDTO.getOrderInfoList())) {
            log.warn("订单使用店铺优惠券，orderInfoList 为空拒绝执行 xid:【{}】", RootContext.getXID());
            return;
        }
        for (OrderInfo orderInfo : orderSubmitDTO.getOrderInfoList()) {// 按子订单循环->店铺维度
            log.info("订单使用店铺优惠券，orderSn:{} couponCode:{}", orderInfo.getOrderSn(), orderInfo.getVoucherCodeList());
            if (CollectionUtils.isEmpty(orderInfo.getVoucherCodeList())) {
                log.warn("订单使用店铺优惠券，couponCode 为空拒绝执行 parentSn:{} orderSn:{} ", order.getParentSn(),
                    orderInfo.getOrderSn());
                continue;
            }
            CouponUseVO couponVO = new CouponUseVO();
            couponVO.setCouponCode(orderInfo.getVoucherCodeList());
            couponVO.setStoreId(orderInfo.getStoreId());
            log.info("订单使用店铺优惠券API调用，xid:【{}】 parentSn:{} -> orderSn:{}  couponVO：{} ", RootContext.getXID(),
                order.getParentSn(), orderInfo.getOrderSn(), couponVO);
            JsonResult<Boolean> result = couponTccFeign.couponUse(couponVO, "mall-order", orderInfo.getOrderSn(),
                member.getMemberId(), member.getMemberName());
            log.info("订单使用店铺优惠券API结果，xid:【{}】 parentSn:{} -> orderSn:{}  couponVO：{} result：{}", RootContext.getXID(),
                order.getParentSn(), orderInfo.getOrderSn(), couponVO, result);
            // 订单使用店铺优惠券失败
            BizAssertUtil.isTrue(200 != result.getState(),
                StringUtils.defaultString(result.getMsg(), "抱歉，下单失败，店铺优惠券使用失败，请稍后再试！"));
            // 订单使用店铺优惠券失败
            BizAssertUtil.isTrue(!result.getData(), "抱歉，店铺优惠券使用失败，请稍后再试！");
        }
        // 使用平台优惠券
        this.platformCouponUse(orderSubmitDTO, member, order);
    }

    private void platformCouponUse(OrderSubmitDTO orderSubmitDTO, Member member, OrderDTO order) {
        log.info("订单使用平台优惠券，parentSn:{}", order.getParentSn());
        if (CollectionUtil.isEmpty(orderSubmitDTO.getCouponCodeList())) {
            log.warn("订单使用平台优惠券，couponCode 为空拒绝执行 parentSn:{}", order.getParentSn());
            return;
        }
        CouponUseVO coupon = new CouponUseVO();
        coupon.setCouponCode(orderSubmitDTO.getCouponCodeList());
        coupon.setStoreId(0L);
        log.info("订单使用平台优惠API调用，xid:【{}】 parentSn:{} -> coupon：{} ", RootContext.getXID(), order.getParentSn(), coupon);
        JsonResult<Boolean> result = couponTccFeign.couponUse(coupon, "mall-order", order.getParentSn(),
            member.getMemberId(), member.getMemberName());
        log.info("订单使用平台优惠API结果，xid:【{}】 parentSn:{} -> coupon：{} result：{}", RootContext.getXID(), order.getParentSn(),
            coupon, result);
        // 订单使用平台优惠券失败
        BizAssertUtil.isTrue(200 != result.getState(), "抱歉，下单失败，平台优惠券使用失败，请稍后再试！");
        // 订单使用平台优惠券失败
        BizAssertUtil.isTrue(!result.getData(), "抱歉，平台优惠券使用失败，请稍后再试！");
    }

}
