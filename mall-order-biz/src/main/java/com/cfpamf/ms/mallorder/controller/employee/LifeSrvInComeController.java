package com.cfpamf.ms.mallorder.controller.employee;

import com.cfpamf.ms.mallorder.req.LifeSrvIncomeReportRequest;
import com.cfpamf.ms.mallorder.service.LifeSrvInComeService;
import com.cfpamf.ms.mallorder.vo.bean.OuterPageInfo;
import com.cfpamf.ms.mallorder.vo.pgrpt.LifesrvDepartmentProfitBasicVO;
import com.cfpamf.ms.mallorder.vo.pgrpt.LifesrvEmpProfitBasicVO;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Api(tags = "生服牛工作台-生服收益板块")
@RestController
@RequestMapping("/lifesrv/income")
public class LifeSrvInComeController {

	@Autowired
	private LifeSrvInComeService inComeService;

	@ApiOperation(value = "查询生服收益基础指标")
	@PostMapping("/basic/metrics")
	public JsonResult<OuterPageInfo<LifesrvEmpProfitBasicVO, LifesrvDepartmentProfitBasicVO>> basicMetrics(
			@RequestBody @Valid LifeSrvIncomeReportRequest request) {
		return SldResponse.success(inComeService.basicMetrics(request));
	}
}
