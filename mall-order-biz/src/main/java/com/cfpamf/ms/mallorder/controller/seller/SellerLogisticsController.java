package com.cfpamf.ms.mallorder.controller.seller;

import com.cfpamf.ms.mallorder.common.enums.CommonEnum;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.common.util.OrderLocalUtils;
import com.cfpamf.ms.mallorder.model.OrderAfterServiceModel;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.po.OrderAfterPO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.service.IOrderExtendService;
import com.cfpamf.ms.mallorder.service.IOrderService;
import com.cfpamf.ms.mallshop.resp.Vendor;
import com.slodon.bbc.core.controller.BaseController;
import com.slodon.bbc.core.express.TracesResult;
import com.slodon.bbc.core.express.TrackUtil;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import com.slodon.bbc.core.util.AssertUtil;
import com.slodon.bbc.core.util.StringUtil;
import com.slodon.bbc.core.util.UserUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Slf4j
@Api(tags = "seller-物流信息")
@RestController
@RequestMapping("seller/logistics")
public class SellerLogisticsController extends BaseController {

    @Resource
    private IOrderExtendService orderExtendService;

    @Resource
    private OrderModel orderModel;
    @Resource
    private OrderAfterServiceModel orderAfterServiceModel;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private IOrderService orderService;

    @ApiOperation("订单查看物流")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderSn", value = "订单号", required = true, paramType = "query"),
            @ApiImplicitParam(name = "orderProductId", value = "订单商品ID",  required = true, paramType = "query"),
            @ApiImplicitParam(name = "distribution", value = "是否配销订单 0-否 1-是", paramType = "query")
    })
    @GetMapping("order/getTrace")
    public JsonResult<TracesResult> getOrderTrace(HttpServletRequest request, String orderSn, Long orderProductId,Integer distribution) {
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        OrderPO orderPO = orderModel.getOrderByOrderSn(orderSn);
        log.info("查询订单物流，订单信息：{}", orderPO);
        AssertUtil.notNull(orderPO,"此订单物流信息为空");
        AssertUtil.notNull(vendor, "商户信息为空，请确认是否经过网关");
        //配销订单，引荐商需要看到子店订单，进行权限校验
        OrderLocalUtils.checkOrderPermissions(vendor.getStoreId(),orderPO.getStoreId(),orderPO.getRecommendStoreId(),distribution,orderPO.getOrderType());

        return SldResponse.success(orderService.getOrderTrace(orderSn, orderProductId).get(0));

    }

    @ApiOperation("售后查看物流")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "afsSn", value = "退货单号", required = true, paramType = "query"),
            @ApiImplicitParam(name = "distribution", value = "是否配销订单 0-否 1-是", paramType = "query")
    })
    @GetMapping("afs/getTrace")
    public JsonResult<TracesResult> getTrace(HttpServletRequest request, String afsSn,Integer distribution) {
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        OrderAfterPO orderAfterServicePO = orderAfterServiceModel.getAfterServiceByAfsSn(afsSn);
        BizAssertUtil.isTrue(StringUtil.isEmpty(orderAfterServicePO.getBuyerExpressCode()), "物流公司快递代码为空，请重试");
        BizAssertUtil.isTrue(StringUtil.isEmpty(orderAfterServicePO.getBuyerExpressNumber()), "物流单号为空，请重试");
        if (CommonEnum.NO.getCode().equals(distribution)) {
            BizAssertUtil.isTrue(!vendor.getStoreId().equals(orderAfterServicePO.getStoreId()), "无权限");
        }

        //获取配置表内快递鸟的EBusinessID和AppKey
        String EBusinessID = stringRedisTemplate.opsForValue().get("express_ebusinessid");
        AssertUtil.notNull(EBusinessID, "请完善快递鸟配置信息：");

        String AppKey = stringRedisTemplate.opsForValue().get("express_apikey");
        AssertUtil.notNull(AppKey, "请完善快递鸟配置信息：");

        TracesResult tracesResult = TrackUtil.getKdniaoTrack(orderAfterServicePO.getOrderSn(), orderAfterServicePO.getBuyerExpressCode(),
                orderAfterServicePO.getBuyerExpressName(), orderAfterServicePO.getBuyerExpressNumber(), EBusinessID, AppKey,
                OrderLocalUtils.subMobileLastFour(orderAfterServicePO.getContactPhone()));
        return SldResponse.success(tracesResult);
    }

}
