package com.cfpamf.ms.mallorder.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cdfinance.hrms.facade.vo.BranchRelationVO;
import com.cfpamf.ms.bizconfig.facade.vo.UserVo;
import com.cfpamf.ms.mallgoods.facade.api.ProductFeignClient;
import com.cfpamf.ms.mallgoods.facade.dto.ProductOutTaxQueryDTO;
import com.cfpamf.ms.mallgoods.facade.vo.ProductMaterialCodeOutTaxVO;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.enums.*;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.common.util.ExternalApiUtil;
import com.cfpamf.ms.mallorder.common.util.UserUtil;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.controller.fegin.facade.MessagePushFacade;
import com.cfpamf.ms.mallorder.dto.OrderOperationEventNotifyDTO;
import com.cfpamf.ms.mallorder.dto.ProductCostDTO;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.enums.OrderUserIdentityEnum;
import com.cfpamf.ms.mallorder.integration.cust.BizConfigIntegration;
import com.cfpamf.ms.mallorder.integration.facade.BmsIntegration;
import com.cfpamf.ms.mallorder.integration.facade.BmsUserFacade;
import com.cfpamf.ms.mallorder.integration.hrms.HrmsIntegration;
import com.cfpamf.ms.mallorder.integration.wms.WmsConst;
import com.cfpamf.ms.mallorder.integration.wms.dto.EctLogisticInterceptDTO;
import com.cfpamf.ms.mallorder.mapper.OrderLogMapper;
import com.cfpamf.ms.mallorder.mapper.OrderMapper;
import com.cfpamf.ms.mallorder.mapper.OrderProductMapper;
import com.cfpamf.ms.mallorder.mapper.OrderReturnMapper;
import com.cfpamf.ms.mallorder.model.OrderLogModel;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.model.OrderReturnModel;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.req.OrderDeliveryReq;
import com.cfpamf.ms.mallorder.req.msg.MessageRequest;
import com.cfpamf.ms.mallorder.req.msg.SendAllMessageRequest;
import com.cfpamf.ms.mallorder.request.OrderLogExample;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.vo.OrganizationWithDirectorVO;
import com.cfpamf.ms.mallshop.api.StoreFeignClient;
import com.cfpamf.ms.mallshop.resp.Vendor;
import com.cfpamf.ms.mallshop.vo.StoreInfoVo;
import com.cfpamf.ms.mallstock.facade.api.StockBalanceFeignClient;
import com.cfpamf.ms.mallstock.facade.dto.ProcurementCostPriceQueryDTO;
import com.cfpamf.ms.mallstock.facade.vo.ProcurementCostPriceVO;
import com.cfpamf.smartid.client.utils.sharding.ShardingId;
import com.slodon.bbc.core.exception.MallException;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2023/4/13.
 */
@Slf4j
@Service
public class HistoryDataDealServiceImpl implements IHistoryDataDealService {

    @Resource
    private OrderReturnMapper orderReturnMapper;

    @Resource
    private StockBalanceFeignClient stockBalanceFeignClient;

    @Resource
    private OrderModel orderModel;

    @Resource
    private OrderReturnModel orderReturnModel;

    @Resource
    private OrderProductMapper orderProductMapper;

    @Resource
    private IOrderProductService productService;
    @Autowired
    private IOrderService orderService;
    @Resource
    private IOrderReturnService orderReturnService;
    @Resource
    private MessagePushFacade messagePushFacade;

    @Autowired
    private ProductFeignClient productFeignClient;
    @Autowired
    private BizConfigIntegration bizConfigIntegration;
    @Autowired
    private IOrderExtendService orderExtendService;
    @Autowired
    private HrmsIntegration hrmsIntegration;
    @Autowired
    private OrderLogModel orderLogModel;
    @Autowired
    private StoreFeignClient storeFeignClient;
    @Autowired
    private BmsIntegration bmsIntegration;
    @Resource
    private OrderMapper orderMapper;
    @Autowired
    private IOrderLogisticService orderLogisticService;

    @Resource
    private IOrderLogisticItemService orderLogisticItemService;

    @Resource
    private OrderLogMapper orderLogMapper;
    @Autowired
    private IOrderProductService orderProductService;

    @Resource
    private OrderOfflineService orderOfflineService;

    @Autowired
    private OrderCreateHelper orderCreateHelper;

    @Autowired
    private BmsUserFacade bmsUserFacade;


    @Value("${order.offline.delivery.name:admin}")
    private String orderOfflineDeliveryName;

    @Value("${order.offline.delivery.mobile:18890909090}")
    private String orderOfflineDeliveryMobile;

    @Resource
    private IOrderDeliveryRecordService orderDeliveryRecordService;

    @Resource
    private ShardingId shardingId;

    @Resource
    private IOrderOfflineExtendService orderOfflineExtendService;


    @Override
    public int returnServiceFeeFix(String afsSn, BigDecimal serviceFee) {

        LambdaUpdateWrapper<OrderReturnPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OrderReturnPO::getAfsSn, afsSn);
        updateWrapper.set(OrderReturnPO::getServiceFee, serviceFee);
        int updateCount = orderReturnMapper.update(null, updateWrapper);

        return updateCount;
    }

    @Override
    public String orderProductCostFixImport(List<ProductCostDTO> costList) {
        Set<ProcurementCostPriceQueryDTO> queryDTOSet = new HashSet<>();
        List<ProcurementCostPriceQueryDTO> queryDTOList = new ArrayList<>();
        for (ProductCostDTO productCostDTO : costList) {
            ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
            procurementCostPriceQueryDTO.setBatchNumber(productCostDTO.getBatchNo());
            procurementCostPriceQueryDTO.setSkuId(productCostDTO.getSkuId());
            queryDTOSet.add(procurementCostPriceQueryDTO);
        }
        queryDTOList.addAll(queryDTOSet);
        log.info("orderProductCostFixImport queryCostPriceByBatchNumber param : {}", JSONObject.toJSONString(queryDTOSet));
        JsonResult<List<ProcurementCostPriceVO>> jsonResult = stockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList);
        log.info("orderProductCostFixImport queryCostPriceByBatchNumber result : {}", JSONObject.toJSONString(jsonResult));

        if (jsonResult == null || jsonResult.getData() == null) {
            return "error";
        }

        for (ProcurementCostPriceVO procurementCostPriceVO : jsonResult.getData()) {
            log.info("orderProductCostFixImport = {}", JSONObject.toJSONString(procurementCostPriceVO));
            LambdaUpdateWrapper<OrderProductPO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(OrderProductPO::getBatchNo, procurementCostPriceVO.getBatchNumber())
                    .eq(OrderProductPO::getChannelSkuId, procurementCostPriceVO.getSkuId())
                    .set(OrderProductPO::getCost, procurementCostPriceVO.getCostPrice());
            productService.update(updateWrapper);
        }

        return "success";
    }

    @Override
    public String orderProductCostFix(String orderSn) {
        log.info("orderProductCostFix orderSn = {}", orderSn);
        List<OrderProductPO> orderProductPOList = orderProductMapper.selectCostByOrderSn(orderSn);
        log.info("orderProductCostFix orderProductPOList : {}", JSONObject.toJSONString(orderProductPOList));

        Set<ProcurementCostPriceQueryDTO> queryDTOSet = new HashSet<>();
        List<ProcurementCostPriceQueryDTO> queryDTOList = new ArrayList<>();

        for (OrderProductPO orderProductPO : orderProductPOList) {
            ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
            procurementCostPriceQueryDTO.setBatchNumber(orderProductPO.getBatchNo());
            procurementCostPriceQueryDTO.setSkuId(orderProductPO.getChannelSkuId());
            queryDTOSet.add(procurementCostPriceQueryDTO);
        }
        queryDTOList.addAll(queryDTOSet);
        log.info("orderProductCostFix queryCostPriceByBatchNumber param : {}", JSONObject.toJSONString(queryDTOSet));
        JsonResult<List<ProcurementCostPriceVO>> jsonResult = stockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList);
        log.info("orderProductCostFix queryCostPriceByBatchNumber result : {}", JSONObject.toJSONString(jsonResult));

        if (jsonResult == null || jsonResult.getData() == null) {
            return "error";
        }

        for (ProcurementCostPriceVO procurementCostPriceVO : jsonResult.getData()) {
            log.info("procurementCostPriceVO = {}", JSONObject.toJSONString(procurementCostPriceVO));
            LambdaUpdateWrapper<OrderProductPO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(OrderProductPO::getBatchNo, procurementCostPriceVO.getBatchNumber())
                    .eq(OrderProductPO::getChannelSkuId, procurementCostPriceVO.getSkuId())
                    .set(OrderProductPO::getCost, procurementCostPriceVO.getCostPrice());
            productService.update(updateWrapper);
        }
        return "success";
    }

    @Override
    public Boolean cancelPreSellBalance(String orderSn) {
        // 查询超时的待支付尾款的预售订单
        OrderPO orderPO = orderService.lambdaQuery()
                .eq(OrderPO::getOrderSn, orderSn)
                .eq(OrderPO::getOrderType, OrderTypeEnum.PRE_SELL_DEPOSIT.getValue())
                .one();
        try {
            if (this.dealBalanceReturn(orderPO, "系统手动取消尾款支付超时的预售订单")) {
                this.sendMessage(String.format("订单%s因尾款长时间未支付，系统自动关闭，订金已原路退回", orderPO.getOrderSn()), orderPO.getUserMobile());
            }
        } catch (Exception e) {
            log.error("系统自动取消尾款支付超时的预售订单失败，订单号：{}", orderPO.getOrderSn(), e);
        }
        return null;
    }

    private Boolean dealBalanceReturn(OrderPO orderPo, String cancelReason) {
        // 该订单已经有退款中的退款单时，不做处理
        if (orderReturnModel.whetherHasReturningProduct(orderPo.getOrderSn())) {
            return Boolean.FALSE;
        }

        // 生成退款单
        orderModel.orderCancelInsertAfterServiceAndReturn(orderPo, cancelReason, cancelReason, OrderConst.RETURN_BY_0,
                OrderConst.ADMIN_ROLE, (long) OrderConst.LOG_ROLE_ADMIN, OrderConst.LOG_USER_NAME, null);

        // 查询订单对应的退款单
        LambdaQueryWrapper<OrderReturnPO> returnQueryWrapper = Wrappers.lambdaQuery();
        returnQueryWrapper.select(OrderReturnPO::getAfsSn)
                .eq(OrderReturnPO::getOrderSn, orderPo.getOrderSn())
                .in(OrderReturnPO::getState, Arrays.asList(OrderReturnStatus.STORE_AGREE_REFUND.getValue(), OrderReturnStatus.STORE_RECEIVED.getValue()))
                .eq(OrderReturnPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                .last("limit 1");
        OrderReturnPO orderReturnPo = orderReturnService.getOne(returnQueryWrapper);
        log.info("系统自动取消尾款支付放款失败的预售订单, 对退款单：{} 进行退款", orderReturnPo.getAfsSn());

        // 自动审批通过
        orderReturnModel.adminRefundOperation(UserUtil.moocAdmin(), orderReturnPo.getAfsSn(), CommonConst.SYSTEM_PASS,
                null, true, OrderCreateChannel.WEB.getValue(), BigDecimal.ZERO);

        return Boolean.TRUE;
    }


    /**
     * 发送短信消息
     *
     * @param message   消息内容
     * @param telephone 接收号码
     */
    private void sendMessage(String message, String telephone) {
        try {
            SendAllMessageRequest sendRequest = new SendAllMessageRequest();
            sendRequest.setMqMsgId(UUID.randomUUID().toString());
            sendRequest.setMobile(telephone);
            sendRequest.setTargetPeople("Customer");
            sendRequest.setBusinessType("sldMallSms");
            sendRequest.setMessageRequest(new MessageRequest());
            sendRequest.getMessageRequest().setAppId("SldMall");
            sendRequest.getMessageRequest().setBody(JSONObject.toJSONString(Collections.singletonList(message)));
            messagePushFacade.sendAllMessageToChannels(sendRequest);
        } catch (Exception e) {
            log.error("预售订单尾款支付超时，发送短信消息失败，消息内容【{}】", message, e);
        }
    }

    @Override
    public Boolean orderProductRateFix() {
        List<String> materialCodeList = orderProductMapper.listSkuMaterialCode();

        ProductOutTaxQueryDTO query = new ProductOutTaxQueryDTO();
        query.setSkuMaterialCodes(materialCodeList);
        JsonResult<List<ProductMaterialCodeOutTaxVO>> skuMaterialTaxList = productFeignClient.getProductOutTaxBySkuMaterialCodes(query);
        log.info("orderProductRateFix getProductOutTaxBySkuMaterialCodes result : {}", JSONObject.toJSONString(skuMaterialTaxList));

        if (CollectionUtils.isEmpty(skuMaterialTaxList.getData())) {
            return true;
        }

        for (ProductMaterialCodeOutTaxVO vo : skuMaterialTaxList.getData()) {
            LambdaUpdateWrapper<OrderProductPO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(OrderProductPO::getSkuMaterialCode, vo.getSkuMaterialCode())
                    .set(OrderProductPO::getTaxRate, vo.getOutTax());
            productService.update(updateWrapper);
        }

        return true;
    }

    @Override
    public Boolean orderSelfLiftLogisticFix() {
        // 查询缺失物流信息自提订单
        List<String> missingLogisticOrder = orderMapper.getMissingLogisticOrder();
        if (CollectionUtils.isEmpty(missingLogisticOrder)) {
            return true;
        }

        for (String orderSn : missingLogisticOrder) {
            // 查询实际发货时间
            OrderLogExample orderLogExample = new OrderLogExample();
            orderLogExample.setOrderSn(orderSn);
            orderLogExample.setOrderPreState(OrderStatusEnum.WAIT_DELIVER.getValue());
            List<OrderLogPO> orderLogPOS = orderLogMapper.listByExample(orderLogExample);
            if (CollectionUtils.isEmpty(orderLogPOS)) {
                continue;
            }

            // 自提订单物流信息取收货联系人，既自提点联系人
            LambdaQueryWrapper<OrderExtendPO> orderExtendQuery = new LambdaQueryWrapper<>();
            orderExtendQuery.eq(OrderExtendPO::getOrderSn, orderSn)
                    .select(OrderExtendPO::getExtendId, OrderExtendPO::getReceiverName, OrderExtendPO::getReceiverMobile);
            OrderExtendPO orderExtendPO = orderExtendService.getOne(orderExtendQuery);

            OrderLogisticPO orderLogisticPO = new OrderLogisticPO();
            orderLogisticPO.setOrderSn(orderSn);
            orderLogisticPO.setDeliverType(1);
            orderLogisticPO.setDeliverName(orderExtendPO.getReceiverName());
            orderLogisticPO.setDeliverMobile(orderExtendPO.getReceiverMobile());
            orderLogisticPO.setCreateBy(OrderConst.LOG_USER_NAME);
            orderLogisticPO.setUpdateBy(OrderConst.LOG_USER_NAME);
            orderLogisticPO.setCreateTime(orderLogPOS.get(0).getCreateTime());
            orderLogisticPO.setUpdateTime(orderLogPOS.get(0).getCreateTime());

            // 保存物流信息
            orderLogisticService.save(orderLogisticPO);

            // 更新订单商品关联物流ID
            /*LambdaUpdateWrapper<OrderProductPO> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.eq(OrderProductPO::getOrderSn, orderSn)
                    .set(OrderProductPO::getLogisticId, orderLogisticPO.getLogisticId());
            orderProductService.update(updateWrapper);*/
        }

        return true;
    }

    @Override
    public Boolean historyOrderLogisticFix() {
        List<String> orderSnList = orderMapper.getMissingHistoryLogisticOrder();
        for (String orderSn : orderSnList) {

            OrderLogExample orderLogExample = new OrderLogExample();
            orderLogExample.setOrderSn(orderSn);
            orderLogExample.setOrderPreState(OrderStatusEnum.WAIT_DELIVER.getValue());
            List<OrderLogPO> orderLogPOS = orderLogMapper.listByExample(orderLogExample);
            if (CollectionUtils.isEmpty(orderLogPOS)) {
                log.info("historyOrderLogisticFix wait delivery time , orderSn = {}", orderSn);
                continue;
            }

            OrderLogisticPO orderLogisticPO = new OrderLogisticPO();
            orderLogisticPO.setOrderSn(orderSn);
            orderLogisticPO.setDeliverType(1);
            orderLogisticPO.setDeliverName(orderOfflineDeliveryName);
            orderLogisticPO.setDeliverMobile(orderOfflineDeliveryMobile);
            orderLogisticPO.setCreateBy(OrderConst.LOG_USER_NAME);
            orderLogisticPO.setUpdateBy(OrderConst.LOG_USER_NAME);
            //取发货时间或交易完成时间
            orderLogisticPO.setCreateTime(orderLogPOS.get(0).getCreateTime());

            // 保存物流信息
            orderLogisticService.save(orderLogisticPO);

            // 更新订单商品关联物流ID
            /*LambdaUpdateWrapper<OrderProductPO> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.eq(OrderProductPO::getOrderSn, orderSn)
                    .ne(OrderProductPO::getLogisticId,0)
                    .set(OrderProductPO::getLogisticId, orderLogisticPO.getLogisticId());
            orderProductService.update(updateWrapper);*/
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean fixManager(String orderSn, String userNo, String branchCode, String branchName, String areaCode, String areaName) {
        OrderPO orderPO = orderService.lambdaQuery().eq(OrderPO::getOrderSn, orderSn).one();
        BizAssertUtil.notNull(orderPO, "订单不存在");
        //根据工号查询客户经理信息
        UserVo byUserCode = bizConfigIntegration.getDefaultUserByUserCode(userNo);
        BizAssertUtil.notNull(byUserCode, "客户经理信息不存在");

        String userCode = byUserCode.getUserCode();
        String userName = byUserCode.getUserName();


        LambdaUpdateWrapper<OrderExtendPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OrderExtendPO::getOrderSn, orderSn);

        updateWrapper.set(OrderExtendPO::getManager, userCode);
        updateWrapper.set(OrderExtendPO::getManagerName, userName);
        updateWrapper.set(OrderExtendPO::getBranch, branchCode);
        updateWrapper.set(OrderExtendPO::getBranchName, branchName);
        updateWrapper.set(OrderExtendPO::getAreaCode, areaCode);
        updateWrapper.set(OrderExtendPO::getAreaName, areaName);
        updateWrapper.set(OrderExtendPO::getManageType, ManageTypeEnum.FIX.getValue());

        // 区域、片区信息
        if (!StringUtils.isEmpty(branchCode)) {
            BranchRelationVO branchRelationVO = hrmsIntegration.getBranchRelation(branchCode);
            if (branchRelationVO != null) {
                updateWrapper.set(OrderExtendPO::getZoneCode, branchRelationVO.getZoneCode());
                updateWrapper.set(OrderExtendPO::getZoneName, branchRelationVO.getZoneName());
                updateWrapper.set(OrderExtendPO::getAreaCode, branchRelationVO.getRegionCode());
                updateWrapper.set(OrderExtendPO::getAreaName, branchRelationVO.getRegionName());
                updateWrapper.set(OrderExtendPO::getZoneCode, branchRelationVO.getZoneCode());
                updateWrapper.set(OrderExtendPO::getZoneName, branchRelationVO.getZoneName());
            }
        }
        orderExtendService.update(updateWrapper);
        //落库订单日志
        orderLogModel.insertOrderLog(OrderConst.LOG_ROLE, OrderConst.LOG_USER_ID, OrderConst.LOG_USER_NAME,
                orderSn, orderPO.getOrderState(), orderPO.getOrderState(),
                LoanStatusEnum.DEFAULT.getValue(), "手动补偿管护信息", OrderCreateChannel.WEB);
        return true;
    }

    @Override
    public Boolean fixManagerByStoreId(String storeId) {
        JsonResult<StoreInfoVo> storeInfoByStoreId = storeFeignClient.getStoreInfoByStoreId(Long.valueOf(storeId));

        log.info("封装店铺区域信息，查询结果:{}", JSON.toJSONString(storeInfoByStoreId));
        if (storeInfoByStoreId == null || !storeInfoByStoreId.getState().equals(CommonConst.SLD_SUCCESS)
                || storeInfoByStoreId.getData() == null) {
            throw new MallException("查询店铺信息出错");
        }
        OrganizationWithDirectorVO directorByHrOrgCodes = null;
        if (Objects.nonNull(storeInfoByStoreId.getData()) && Objects.nonNull(storeInfoByStoreId.getData().getBranchCode())) {
            directorByHrOrgCodes = bmsIntegration.getDirectorByHrOrgCodes(storeInfoByStoreId.getData().getBranchCode());
        }
        if (Objects.isNull(directorByHrOrgCodes) || StringUtils.isEmpty(directorByHrOrgCodes.getDirectorJobNumber())) {
            return true;
        }

        List<OrderPO> orderPOS = orderService.lambdaQuery().eq(OrderPO::getStoreId, storeId).list();
        List<String> orderSns = orderPOS.stream().map(OrderPO::getOrderSn).collect(Collectors.toList());
        List<OrderExtendPO> orderExtendPOS = orderExtendService.lambdaQuery()
                .in(OrderExtendPO::getOrderSn, orderSns)
                .isNull(OrderExtendPO::getManager)
                .list();

        List<OrderExtendPO> updateList = new ArrayList<>();
        for (OrderExtendPO orderExtendDb : orderExtendPOS) {
            OrderExtendPO extendPO = new OrderExtendPO();
            extendPO.setExtendId(orderExtendDb.getExtendId());
            fixBranchInfo(storeInfoByStoreId, extendPO, directorByHrOrgCodes);
            updateList.add(extendPO);
        }
        return orderExtendService.updateBatchById(updateList);
    }

    private void fixBranchInfo(JsonResult<StoreInfoVo> storeInfoVoJsonResult, OrderExtendPO orderExtendPO,
                               OrganizationWithDirectorVO directorByHrOrgCodes) {
        StoreInfoVo byStoreIdData = storeInfoVoJsonResult.getData();
        String branchCode = byStoreIdData.getBranchCode();
        String branchName = byStoreIdData.getBranchName();

        orderExtendPO.setBranch(branchCode);
        orderExtendPO.setBranchName(branchName);
        orderExtendPO.setManageType(ManageTypeEnum.FIX.getValue());

        // 区域、片区信息
        if (!StringUtils.isEmpty(branchCode)) {
            BranchRelationVO branchRelationVO = hrmsIntegration.getBranchRelation(branchCode);
            if (branchRelationVO != null) {
                orderExtendPO.setAreaCode(branchRelationVO.getRegionCode());
                orderExtendPO.setAreaName(branchRelationVO.getRegionName());
                orderExtendPO.setZoneCode(branchRelationVO.getZoneCode());
                orderExtendPO.setZoneName(branchRelationVO.getZoneName());
            }
        }
        //主任信息
        if (Objects.nonNull(directorByHrOrgCodes)) {
            orderExtendPO.setManager(directorByHrOrgCodes.getDirectorJobNumber());
            orderExtendPO.setManagerName(directorByHrOrgCodes.getEmployeeName());
        }
    }

    @Override
    public Boolean fixOrgInfo(String areaCode) {

        QueryWrapper<OrderExtendPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("DISTINCT branch").lambda().eq(OrderExtendPO::getAreaCode, areaCode);
        List<OrderExtendPO> extendPOS = orderExtendService.list(queryWrapper);
        for (OrderExtendPO extendPO : extendPOS) {
            if (extendPO == null) {
                continue;
            }

            if (StringUtils.isEmpty(extendPO.getBranch())) {
                continue;
            }

            String branchCode = extendPO.getBranch();

            BranchRelationVO branchRelationVO = hrmsIntegration.getBranchRelation(branchCode);
            if (branchRelationVO == null) {
                continue;
            }

            LambdaUpdateWrapper<OrderExtendPO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(OrderExtendPO::getBranch, branchCode);
            updateWrapper.set(OrderExtendPO::getBranch, branchRelationVO.getBranchCode());
            updateWrapper.set(OrderExtendPO::getBranchName, branchRelationVO.getBranchName());
            updateWrapper.set(OrderExtendPO::getZoneCode, branchRelationVO.getZoneCode());
            updateWrapper.set(OrderExtendPO::getZoneName, branchRelationVO.getZoneName());
            updateWrapper.set(OrderExtendPO::getAreaCode, branchRelationVO.getRegionCode());
            updateWrapper.set(OrderExtendPO::getAreaName, branchRelationVO.getRegionName());
            orderExtendService.update(updateWrapper);
        }
        return true;
    }


    @Override
    public Boolean fixOrgInfoInOrderSn(String orderSn) {

        QueryWrapper<OrderExtendPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("DISTINCT manager").lambda().eq(OrderExtendPO::getOrderSn, orderSn);
        List<OrderExtendPO> extendPOS = orderExtendService.list(queryWrapper);

        for (OrderExtendPO extendPO : extendPOS) {
            if (extendPO == null) {
                continue;
            }
            if (StringUtils.isEmpty(extendPO.getManager())) {
                continue;
            }
            UserVo byUserCode = null;
            try {
                //根据工号查询客户经理信息
                byUserCode = bizConfigIntegration.getDefaultUserByUserCode(extendPO.getManager());
            } catch (Exception e) {
                log.warn("获取客户经理信息失败，" + extendPO.getManager(), e);
                continue;
            }
            if (byUserCode == null) {
                continue;
            }

            String branchCode = byUserCode.getBranchCode();
            if (StringUtils.isEmpty(branchCode)) {
                continue;
            }

            // 区域、片区信息
            BranchRelationVO branchRelationVO = hrmsIntegration.getBranchRelation(branchCode);
            if (branchRelationVO == null) {
                continue;
            }

            LambdaUpdateWrapper<OrderExtendPO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(OrderExtendPO::getOrderSn, orderSn);
            updateWrapper.set(OrderExtendPO::getBranch, branchRelationVO.getBranchCode());
            updateWrapper.set(OrderExtendPO::getBranchName, branchRelationVO.getBranchName());
            updateWrapper.set(OrderExtendPO::getZoneCode, branchRelationVO.getZoneCode());
            updateWrapper.set(OrderExtendPO::getZoneName, branchRelationVO.getZoneName());
            updateWrapper.set(OrderExtendPO::getAreaCode, branchRelationVO.getRegionCode());
            updateWrapper.set(OrderExtendPO::getAreaName, branchRelationVO.getRegionName());

            orderExtendService.update(updateWrapper);
        }

        return true;
    }

    @Override
    public Boolean fixOrgInfoInManager(String areaCode) {

        QueryWrapper<OrderExtendPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("DISTINCT manager").lambda().eq(OrderExtendPO::getAreaCode, areaCode);
        List<OrderExtendPO> extendPOS = orderExtendService.list(queryWrapper);

        for (OrderExtendPO extendPO : extendPOS) {
            if (extendPO == null) {
                continue;
            }
            if (StringUtils.isEmpty(extendPO.getManager())) {
                continue;
            }
            UserVo byUserCode = null;
            try {
                //根据工号查询客户经理信息
                byUserCode = bizConfigIntegration.getDefaultUserByUserCode(extendPO.getManager());
            } catch (Exception e) {
                log.warn("获取客户经理信息失败，" + extendPO.getManager(), e);
                continue;
            }
            if (byUserCode == null) {
                continue;
            }

            String branchCode = byUserCode.getBranchCode();
            if (StringUtils.isEmpty(branchCode)) {
                continue;
            }

            // 区域、片区信息
            BranchRelationVO branchRelationVO = hrmsIntegration.getBranchRelation(branchCode);
            if (branchRelationVO == null) {
                continue;
            }

            LambdaUpdateWrapper<OrderExtendPO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(OrderExtendPO::getManager, extendPO.getManager());
            updateWrapper.set(OrderExtendPO::getBranch, branchRelationVO.getBranchCode());
            updateWrapper.set(OrderExtendPO::getBranchName, branchRelationVO.getBranchName());
            updateWrapper.set(OrderExtendPO::getZoneCode, branchRelationVO.getZoneCode());
            updateWrapper.set(OrderExtendPO::getZoneName, branchRelationVO.getZoneName());
            updateWrapper.set(OrderExtendPO::getAreaCode, branchRelationVO.getRegionCode());
            updateWrapper.set(OrderExtendPO::getAreaName, branchRelationVO.getRegionName());

            orderExtendService.update(updateWrapper);
        }

        return true;
    }

    /**
     * 线下补录订单，交易成功补发
     *
     * @param orderSns
     * @return
     */
    @Override
    public Boolean offlineOrderV2(String orderSns) {
        String[] orderSnArr = orderSns.split(",");
        OrderOperationEventEnum manualSettlementEvent = OrderOperationEventEnum.MANUAL_SETTLEMENT_EVENT;
        for (String orderSn : orderSnArr) {
            OrderOperationEventNotifyDTO message = new OrderOperationEventNotifyDTO(manualSettlementEvent.getCode(),
                    manualSettlementEvent.getDesc(), orderSn);
            orderCreateHelper.sendMqEventMessageByTransactionCommit(message,
                    RabbitMqEventEnum.EXCHANGE_ORDER_OPERATION.getExchang());
        }

        return Boolean.TRUE;
    }

    @Override
    public Boolean offlineOrderFix(String orderSns) {
        List<String> orderList = Arrays.asList(orderSns.split(","));
        //String[] orderSnArr = orderSns.split(",");
        LambdaUpdateWrapper<OrderPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(OrderPO::getOrderType, 6)
                .in(OrderPO::getOrderSn, orderList)
                .eq(OrderPO::getOrderType, 5)
                .eq(OrderPO::getOrderState, 40);
        orderService.update(updateWrapper);
        return Boolean.TRUE;
    }


    @Override
    public Boolean offlineOrder(String orderSns) {
        String[] orderSnArr = orderSns.split(",");
        for (String orderSn : orderSnArr) {
            log.info("offlineOrder orderSn = {}", orderSn);
            OrderPO orderPO = orderModel.getOrderByOrderSn(orderSn);
            BizAssertUtil.isTrue(orderPO == null, "线下订单历史数据处理，查询不到" + orderSn + "的订单信息");

            //线下补录订单--支付+发货
            if (OrderTypeEnum.isOfflineAll(orderPO.getOrderType()) && orderPO.getOrderState().equals(OrderStatusEnum.WAIT_PAY.getValue())) {
                //支付
                orderOfflineService.payOrder(orderPO);

                Vendor vendor = new Vendor();
                vendor.setVendorId(OrderConst.LOG_USER_ID);
                vendor.setVendorName(OrderConst.LOG_USER_NAME);
                vendor.setStoreId(orderPO.getStoreId());
                //发货
                orderOfflineService.deliveryOrder(orderPO.getOrderSn(), vendor, null, null);
            }
        }

        return Boolean.TRUE;
    }

    @Override
    public Boolean updatePerformanceChannel() {
        orderProductMapper.updatePerformanceChannel();
        return Boolean.TRUE;
    }

    @Override
    public Boolean orderUserIdentity() {
        // 查询用户手机号
        List<String> userMobile = orderMapper.getUserMobile();
        if (CollectionUtils.isEmpty(userMobile)) {
            return Boolean.FALSE;
        }

        for (String mobile : userMobile) {
            Boolean isInter = ExternalApiUtil.callResultApi(() -> bmsUserFacade.isInter(mobile), mobile,
                    "/user/isInter", "根据手机号判断是否是内部员工");
            // 默认为客户，客户身份不更新
            if (Objects.isNull(isInter) || !isInter) {
                continue;
            }

            // 员工身份更新
            LambdaUpdateWrapper<OrderPO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(OrderPO::getUserMobile, mobile)
                    .set(OrderPO::getUserIdentity, OrderUserIdentityEnum.EMPLOYEE.getValue());
            orderService.update(updateWrapper);
        }

        return Boolean.TRUE;
    }

    @Override
    public List<OrderDeliveryRecordPO> getPackageUniqueCode(List<String> bizUniqueNoList) {
        LambdaQueryWrapper<OrderDeliveryRecordPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(OrderDeliveryRecordPO::getBizUniqueNo, bizUniqueNoList)
                .select(OrderDeliveryRecordPO::getId, OrderDeliveryRecordPO::getBizUniqueNo);
        return orderDeliveryRecordService.list(lambdaQueryWrapper);
    }

    @Override
    public void initLogisticItem() {
        log.info("initLogisticItem start");
        //1、获取订单商品行，logistics_id 不为空的数据
        LambdaQueryWrapper<OrderProductPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.ne(OrderProductPO::getLogisticId, 0);
        int count = orderProductService.count(lambdaQueryWrapper);
        log.info("initLogisticItem orderProductPOList count = {}", count);

        int pageSize = 1000;
        int pages = 1;
        if (count > pageSize) {
            pages = (count % pageSize) == 0 ? (count / pageSize) : (count / pageSize + 1);
        }
        for (int i = 0; i < pages; i++) {
            List<OrderProductPO> list;
            if (i == pages - 1) {
                list = orderProductMapper.getOrderProductToInitPackageItem(i * pageSize, count);
            } else {
                list = orderProductMapper.getOrderProductToInitPackageItem(i * pageSize, (i + 1) * pageSize);
            }

            for (OrderProductPO orderProductPO : list) {
                //2、获取logistics_id对应的包裹号，包裹号为空，则生成新的包裹号;
                OrderLogisticPO orderLogisticPO = orderLogisticService.getById(orderProductPO.getLogisticId());
                if (Objects.isNull(orderLogisticPO)) {
                    continue;
                }
                if (Strings.isEmpty(orderLogisticPO.getPackageSn())) {
                    long packageSnLong = shardingId.next(OrderSeqEnum.PGNO.getName(), OrderSeqEnum.PGNO.prefix(), orderProductPO.getMemberId().toString(), new Date());
                    orderLogisticPO.setPackageSn(String.valueOf(packageSnLong));
                    LambdaUpdateWrapper<OrderLogisticPO> logisticPOUpdateWrapper = new LambdaUpdateWrapper<>();
                    logisticPOUpdateWrapper.set(OrderLogisticPO::getPackageSn, String.valueOf(packageSnLong))
                            .eq(OrderLogisticPO::getLogisticId, orderLogisticPO.getLogisticId());
                }
                //3、获取该包裹号对应的上发货详情，如果没有商品信息，则添加，有的不添加
                LambdaQueryWrapper<OrderLogisticItemPO> logisticItemPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
                logisticItemPOLambdaQueryWrapper.eq(OrderLogisticItemPO::getOrderProductId, orderProductPO.getOrderProductId());

                OrderLogisticItemPO orderLogisticItemPO = orderLogisticItemService.getOne(logisticItemPOLambdaQueryWrapper);

                if (Objects.isNull(orderLogisticItemPO)) {
                    OrderLogisticItemPO orderLogisticItemPO1 = new OrderLogisticItemPO();
                    orderLogisticItemPO1.setOrderProductId(orderProductPO.getOrderProductId());
                    orderLogisticItemPO1.setPackageSn(orderLogisticPO.getPackageSn());
                    orderLogisticItemPO1.setGoodsName(orderProductPO.getGoodsName());
                    orderLogisticItemPO1.setProductImage(orderProductPO.getProductImage());
                    orderLogisticItemPO1.setDeliveryNum(orderProductPO.getProductNum());
                    orderLogisticItemPO1.setSpecValues(orderProductPO.getSpecValues());
                    orderLogisticItemPO1.setProductNum(orderProductPO.getProductNum());
                    orderLogisticItemPO1.setCreateBy("zml");
                    orderLogisticItemPO1.setUpdateBy("zml");
                    orderLogisticItemService.save(orderLogisticItemPO1);
                } else {
                    if (StringUtils.isEmpty(orderLogisticItemPO.getPackageSn())) {
                        orderLogisticItemPO.setPackageSn(orderLogisticPO.getPackageSn());
                        orderLogisticItemService.updateById(orderLogisticItemPO);
                    }
                }
                log.info("initLogisticItem save");
            }
        }


        log.info("initLogisticItem end");


    }


    @Override
    public void offlineExtendDeal() {
        LambdaQueryWrapper<OrderPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(OrderPO::getOrderType, Arrays.asList(5, 6));
        List<OrderPO> list = orderService.list(lambdaQueryWrapper);
        List<OrderOfflineExtendPO> orderOfflineExtendPOList = new ArrayList<>();
        list.stream().forEach(x -> {
            OrderOfflineExtendPO orderOfflineExtendPO = orderOfflineExtendService.getOrderOfflineExtendByOrderSn(x.getOrderSn());
            if (Objects.isNull(orderOfflineExtendPO)) {
                orderOfflineExtendPO = new OrderOfflineExtendPO();
                orderOfflineExtendPO.setOrderSn(x.getOrderSn());
                orderOfflineExtendPO.setCreateBy(x.getCreateBy());
                orderOfflineExtendPO.setCreateTime(x.getCreateTime());
                orderOfflineExtendPO.setUpdateBy("zml-init");
                orderOfflineExtendPOList.add(orderOfflineExtendPO);
            }
        });
        orderOfflineExtendService.saveBatch(orderOfflineExtendPOList);
    }

    @Override
    public Boolean updateProductDeliveryState(String orderProductIds, int deliveryState) {
        List<String> orderProductIdStringList = Arrays.asList(orderProductIds.split(","));
        List<Long> orderProductIdList = orderProductIdStringList.stream().map(Long::parseLong).collect(Collectors.toList());
        orderProductMapper.updateDeliveryState(orderProductIdList, deliveryState);
        return Boolean.TRUE;
    }

    @Override
    public Boolean offlineOrderLogisticsItem(String orderSns) {
        List<String> orderSnList = Arrays.asList(orderSns.split(","));
        for (String orderSn : orderSnList) {
            OrderPO orderPO = orderModel.getOrderByOrderSn(orderSn);
            BizAssertUtil.notNull(orderPO, "该订单不存在");
            if (OrderConst.ORDER_STATE_30 == orderPO.getOrderState() || OrderConst.ORDER_STATE_40 == orderPO.getOrderState()) {
                List<OrderLogisticPO> orderLogisticPOList = orderLogisticService.getOrderLogisticByOrderSn(orderSn, null);
                if (CollectionUtils.isEmpty(orderLogisticPOList)) {
                    //添加包裹信息
                    OrderDeliveryReq deliveryReq = new OrderDeliveryReq();
                    deliveryReq.setAllowNoLogistics(true);
                    deliveryReq.setChannel(OrderCreateChannel.WEB);
                    deliveryReq.setOrderSn(orderPO.getOrderSn());
                    deliveryReq.setDeliverType(1);
                    deliveryReq.setDeliverName(orderOfflineDeliveryName);
                    deliveryReq.setDeliverMobile(orderOfflineDeliveryMobile);
                    deliveryReq.setOffLineOrder(Boolean.TRUE);
                    Date createTime = orderPO.getFinishTime() == null ? new Date() : orderPO.getFinishTime();
                    OrderLogisticPO orderLogisticPO = saveLogistic(orderPO, deliveryReq, "zml-offline", 2, createTime);
                    saveOrUpdateLogisticItem(orderLogisticPO, orderSn);

                } else {
                    OrderLogisticPO orderLogisticPO = orderLogisticPOList.get(0);
                    saveOrUpdateLogisticItem(orderLogisticPO, orderSn);
                }
            }
        }

        return Boolean.TRUE;
    }

    private OrderLogisticPO saveLogistic(OrderPO orderPO, OrderDeliveryReq deliveryReq, String operator, Integer productDeliveryState, Date createTime) {
        long packageSnLong = shardingId.next(OrderSeqEnum.PGNO.getName(), OrderSeqEnum.PGNO.prefix(), orderPO.getMemberId().toString(), new Date());


        OrderLogisticPO logisticPO = new OrderLogisticPO(deliveryReq);
        logisticPO.setCreateBy(operator);
        logisticPO.setUpdateBy(operator);
        logisticPO.setPackageSn(String.valueOf(packageSnLong));
        logisticPO.setDeliverPackageState(productDeliveryState);
        logisticPO.setCreateTime(createTime);

        boolean save = orderLogisticService.save(logisticPO);
        if (!save) {
            throw new MallException("保存订单物流失败");
        }

        return logisticPO;
    }


    private void saveOrUpdateLogisticItem(OrderLogisticPO orderLogisticPO, String orderSn) {
        List<OrderProductPO> orderProductPOList = orderProductService.listByOrderSn(orderSn);
        OrderPO orderPO = orderModel.getOrderByOrderSn(orderSn);
        for (OrderProductPO orderProductPO : orderProductPOList) {
            LambdaQueryWrapper<OrderLogisticItemPO> logisticItemPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            logisticItemPOLambdaQueryWrapper.eq(OrderLogisticItemPO::getOrderProductId, orderProductPO.getOrderProductId());

            OrderLogisticItemPO orderLogisticItemPO = orderLogisticItemService.getOne(logisticItemPOLambdaQueryWrapper);

            if (Objects.isNull(orderLogisticItemPO)) {
                OrderLogisticItemPO orderLogisticItemPO1 = new OrderLogisticItemPO();
                orderLogisticItemPO1.setOrderProductId(orderProductPO.getOrderProductId());
                orderLogisticItemPO1.setPackageSn(orderLogisticPO.getPackageSn());
                orderLogisticItemPO1.setGoodsName(orderProductPO.getGoodsName());
                orderLogisticItemPO1.setProductImage(orderProductPO.getProductImage());
                orderLogisticItemPO1.setDeliveryNum(orderProductPO.getProductNum());
                orderLogisticItemPO1.setSpecValues(orderProductPO.getSpecValues());
                orderLogisticItemPO1.setProductNum(orderProductPO.getProductNum());
                if (orderPO.getFinishTime() != null) {
                    orderLogisticItemPO1.setCreateTime(orderPO.getFinishTime());
                }
                orderLogisticItemPO1.setCreateBy("zml-offline");
                orderLogisticItemPO1.setUpdateBy("zml-offline");
                orderLogisticItemService.save(orderLogisticItemPO1);
            } else {
                if (StringUtils.isEmpty(orderLogisticItemPO.getPackageSn())) {
                    orderLogisticItemPO.setDeliveryNum(orderProductPO.getProductNum());
                    orderLogisticItemPO.setPackageSn(orderLogisticPO.getPackageSn());
                    orderLogisticItemService.updateById(orderLogisticItemPO);
                }
            }

            LambdaUpdateWrapper<OrderProductPO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(OrderProductPO::getLogisticId, orderLogisticPO.getLogisticId())
                    .set(OrderProductPO::getDeliveryState, OrderConst.DELIVER_STATE_1)
                    .set(OrderProductPO::getDeliveryNum, orderProductPO.getProductNum())
                    .eq(OrderProductPO::getOrderProductId, orderProductPO.getOrderProductId());
            productService.update(updateWrapper);
        }
    }

    @Override
    public Boolean jdIntercept(String param) {
        //String bodyJsonStr = param.substring(1, param.length() - 1);
        String bodyJsonStr = JSON.parse(param).toString();
        EctLogisticInterceptDTO notifyDTO = JSONObject.parseObject(bodyJsonStr, EctLogisticInterceptDTO.class);

        JdInterceptStatusEnum jdInterceptStatusEnum = JdInterceptStatusEnum.JD_INTERCEPT_STATE_DEFAULT;

        AssertUtil.isTrue(WmsConst.JD_INTERCEPT_STATE_200 != notifyDTO.getInterceptStatusCode()
                && WmsConst.JD_INTERCEPT_STATE_300 != notifyDTO.getInterceptStatusCode(), "ERP-拦截状态同步MQ返回码异常,请检查！");

        if (WmsConst.JD_INTERCEPT_STATE_200 == notifyDTO.getInterceptStatusCode()) {
            jdInterceptStatusEnum = JdInterceptStatusEnum.JD_INTERCEPT_STATE_INTERCEPTED_SUCCESS;
        } else if (WmsConst.JD_INTERCEPT_STATE_300 == notifyDTO.getInterceptStatusCode()) {
            jdInterceptStatusEnum = JdInterceptStatusEnum.JD_INTERCEPT_STATE_INTERCEPTING;
        }

        OrderReturnPO orderReturnPO = orderReturnModel.getOrderReturnByAfsSn(notifyDTO.getInterceptBusinessNo());
        //更新拦截状态
        orderReturnModel.updateJdInterceptStatus(orderReturnPO.getAfsSn(), jdInterceptStatusEnum.getValue());
        return Boolean.TRUE;
    }

}
