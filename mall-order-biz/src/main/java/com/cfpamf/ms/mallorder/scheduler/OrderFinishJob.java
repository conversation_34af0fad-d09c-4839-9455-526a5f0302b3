package com.cfpamf.ms.mallorder.scheduler;

import com.cfpamf.ms.mallorder.model.OrderModel;
import com.slodon.bbc.core.util.AssertUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 订单自动交易完成
 * <p>
 * cron: 0 0/10 * * * ?
 */
@Component
@Slf4j
public class OrderFinishJob {

    @Autowired
    private OrderModel orderModel;

    @XxlJob(value = "OrderFinishJob")
    public ReturnT<String> execute(String s) {

        long startTime = System.currentTimeMillis();
        XxlJobHelper.log("start job at local time: {}", LocalDateTime.now());

        log.info("jobSystemFinishOrder() start");
        try {
            boolean jobResult = orderModel.jobSystemFinishOrder();
            AssertUtil.isTrue(!jobResult, "[jobSystemFinishOrder] 系统自动完成订单时失败");
        } catch (Exception e) {
            log.error("jobSystemFinishOrder()", e);
        }

        XxlJobHelper.log("finish job at local time: {}  cost:{}",
                LocalDateTime.now(),
                (System.currentTimeMillis() - startTime) / 1000);

        return ReturnT.SUCCESS;
    }
}
