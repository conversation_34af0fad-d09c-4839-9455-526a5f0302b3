package com.cfpamf.ms.mallorder.service.payment;

import com.cfpamf.mallpayment.facade.request.PaymentRefundRequest;
import com.cfpamf.ms.mall.liquidate.vo.ExecuteVO;
import com.cfpamf.ms.mallorder.common.enums.PayMethodEnum;
import com.cfpamf.ms.mallorder.common.enums.RefundType;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderReturnPO;
import com.cfpamf.ms.mallsystem.vo.Admin;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.response.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Create 2022-05-09 13:59
 * @Description : 处理器实例化类，根据actionType进行动作分发
 */
@Service
@Primary
@Slf4j
public class IPayHandleServiceImpl extends AbstractPayHandleServiceImpl {

    @Autowired
    private Map<String, IPayHandleService> beanMap;

    /**
     * 获取接口的实际操作实例
     * 从Autowired的Map中，根据 ${beanName} + ServiceImpl组成的键值对进行查询
     *
     * @param actionType 候选值是PayMethodEnum中的value
     * @return IPayHandleService的操作实例Bean
     */
    public IPayHandleService getInstance(String actionType) {
        if (StringUtils.isBlank(actionType)) {
            throw new BusinessException("业务操作服务获取操作实例Bean，入参为空");
        }
        if (PayMethodEnum.isLoanPay(PayMethodEnum.getValue(actionType))) {
            actionType = "Loan";
        }
        IPayHandleService statementBean = beanMap.get(actionType + "_ServiceImpl");
        if (null == statementBean) {
            throw new BusinessException("业务操作服务获取操作实例Bean,结果为空");
        }
        return statementBean;
    }

    @Override
    public Boolean paymentRefund(OrderPO orderPO, OrderReturnPO orderReturnPO, Map<String, Object> extendParam, String actionType, Admin admin) {
        log.info("处理器退款初始化");
        return getInstance(actionType).paymentRefund(orderPO, orderReturnPO, extendParam, actionType, admin);
    }

    @Override
    public Boolean syncExecute(OrderPO orderPO, OrderReturnPO orderReturnPO, String actionType) {
        log.info("处理器下账初始化");
        return getInstance(actionType).syncExecute(orderPO, orderReturnPO, actionType);
    }

    @Override
    public void verifyExecute(OrderPO orderPO, OrderReturnPO returnPO, String actionType) {
        log.info("处理器校验余额初始化");
        getInstance(actionType).verifyExecute(orderPO, returnPO, actionType);
    }
}
