package com.cfpamf.ms.mallorder.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.framework.autoconfigure.mybatis.BaseRepoServiceImpl;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.enums.*;
import com.cfpamf.ms.mallorder.mapper.OrderAmountStateRecordMapper;
import com.cfpamf.ms.mallorder.po.OrderAmountStateRecordPO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderPresellPO;
import com.cfpamf.ms.mallorder.service.IOrderAmountStateRecordService;
import com.cfpamf.ms.mallorder.v2.service.OrderPresellService;
import com.cfpamf.ms.mallorder.validation.OrderAmountValidation;
import com.cfpamf.ms.mallorder.vo.OrderAmountStateRecordVO;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.exception.MallException;
import com.slodon.bbc.core.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 订单金额记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
@Slf4j
@Service
public class OrderAmountStateRecordServiceImpl extends BaseRepoServiceImpl<OrderAmountStateRecordMapper, OrderAmountStateRecordPO> implements IOrderAmountStateRecordService {

    @Autowired
    private OrderPresellService orderPresellService;

    /**
     * 初始化指定的订单资金项
     *
     * @param orderPO               订单信息
     * @param orderAmountTypeEnum   订单资金项
     * @return                      处理结果 true/false
     */
    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "masterdbTx")
    public Boolean initOrderAmountState(OrderPO orderPO, OrderAmountTypeEnum orderAmountTypeEnum) {
        AssertUtil.notNull(orderPO, String.format("订单 %s 信息为空", orderPO.getOrderSn()));
        AssertUtil.notNull(orderAmountTypeEnum, "订单费用类型不能为空");
        this.insertOrderAmountState(orderPO.getOrderSn(), orderAmountTypeEnum);
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "masterdbTx")
    public Result<Void> initOrderAmountState(OrderPO orderPO) {
        if (Objects.isNull(orderPO)) {
            Result<Void> result = new Result<>();
            result.setSuccess(false);
            result.setCode(String.valueOf(ErrorCodeEnum.U.EMPTY_PARAM.getCode()));
            result.setMessage(ErrorCodeEnum.U.EMPTY_PARAM.getMsg());
            return result;
        }
        if (StringUtils.isAnyBlank(orderPO.getOrderSn(), orderPO.getChannel(), orderPO.getPaymentCode())
                || Objects.isNull(orderPO.getOrderType())) {
            Result<Void> result = new Result<>();
            result.setSuccess(false);
            result.setCode(String.valueOf(ErrorCodeEnum.U.CHECK_FAILURE.getCode()));
            result.setMessage(ErrorCodeEnum.U.CHECK_FAILURE.getMsg());
            return result;
        }
        String paymentCode = orderPO.getPaymentCode();
        if (OrderTypeEnum.PRE_SELL_DEPOSIT.getValue() == orderPO.getOrderType()) {
            LambdaQueryWrapper<OrderPresellPO> presellQueryWrapper = new LambdaQueryWrapper<>();
            presellQueryWrapper.eq(OrderPresellPO::getOrderSn,orderPO.getOrderSn());
            presellQueryWrapper.eq(OrderPresellPO::getType,NumberUtils.INTEGER_TWO);
            presellQueryWrapper.eq(OrderPresellPO::getPayStatus, CommonConst.PAY_STATUS_3);
            OrderPresellPO presellPO = orderPresellService.getOne(presellQueryWrapper);
            //预售订单不是尾款支付时，不需要初始化订单关联金额
            if (Objects.isNull(presellPO)) {
                return Result.ok(null);
            }
            paymentCode = presellPO.getPaymentCode();
        }

        FacadePayMethodEnum payMethodEnum = FacadePayMethodEnum.getValue(paymentCode);
        if (Objects.isNull(payMethodEnum)) {
            Result<Void> result = new Result<>();
            result.setSuccess(false);
            result.setCode(String.valueOf(ErrorCodeEnum.U.ILLEGAL_PARAM.getCode()));
            result.setMessage(ErrorCodeEnum.U.ILLEGAL_PARAM.getMsg());
            return result;
        }
        Result<Void> insertResult = Result.ok(null);
        for (OrderAmountTypeEnum itemEnum : OrderAmountTypeEnum.values()) {
            // 订单佣金初始化
            if (OrderAmountTypeEnum.ORDER_COMMISSION == itemEnum) {
                if (!OrderAmountValidation.isOrderContainOrderCommission(orderPO)) {
                    continue;
                }
                insertResult = this.insertOrderAmountState(orderPO.getOrderSn(), OrderAmountTypeEnum.ORDER_COMMISSION);
            }
            // 佣金服务费初始化
            else if (OrderAmountTypeEnum.COMMISSION_SERVICE_FEE == itemEnum) {
                if (!OrderAmountValidation.isOrderContainCommissionServiceFee(orderPO)) {
                    continue;
                }
                insertResult = this.insertOrderAmountState(orderPO.getOrderSn(), OrderAmountTypeEnum.COMMISSION_SERVICE_FEE);
            }
            // 预贴息金额初始化
            else if (OrderAmountTypeEnum.PLAN_DISCOUNT_AMOUNT == itemEnum) {
                // 非贷款类支付的订单没有预贴息金额这项费用
                if (!FacadePayMethodEnum.isLoanPay(payMethodEnum)) {
                    continue;
                }
                insertResult = this.insertOrderAmountState(orderPO.getOrderSn(), OrderAmountTypeEnum.PLAN_DISCOUNT_AMOUNT);
            }

            if (!insertResult.isSuccess()) {
                throw new BusinessException(String.format("初始化订单金额状态记录失败,orderSn:%s,原因:%s", orderPO.getOrderSn(),
                        insertResult.getMessage()));
            }
        }
        return Result.ok(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "masterdbTx")
    public Result<Void> modifyOrderAmountState(String orderSn, OrderAmountTypeEnum amountTypeEnum, BigDecimal amount,
                                               OrderAmountStateEnum stateEnum) {
        if (StringUtils.isBlank(orderSn) || Objects.isNull(amountTypeEnum) || Objects.isNull(stateEnum)) {
            Result<Void> result = new Result<>();
            result.setSuccess(false);
            result.setCode(String.valueOf(ErrorCodeEnum.U.EMPTY_PARAM.getCode()));
            result.setMessage(ErrorCodeEnum.U.EMPTY_PARAM.getMsg());
            return result;
        }

        Result<Void> updateResult = Result.ok(null);
        //修改订单佣金
        if (OrderAmountTypeEnum.ORDER_COMMISSION == amountTypeEnum) {
            updateResult = this.updateOrderAmountState(orderSn, amountTypeEnum, amount, stateEnum);
        }
        // 修改订单佣金服务费
        if (OrderAmountTypeEnum.COMMISSION_SERVICE_FEE == amountTypeEnum) {
            updateResult = this.updateOrderAmountState(orderSn, amountTypeEnum, amount, stateEnum);
        }
        //修改订单佣金激励费
        if (OrderAmountTypeEnum.ORDER_COMMISSION_INCENTIVE_FEE == amountTypeEnum) {
            updateResult = this.updateOrderAmountState(orderSn, amountTypeEnum, amount, stateEnum);
        }
        // 修改订单佣金激励服务费
        if (OrderAmountTypeEnum.COMMISSION_INCENTIVE_SERVICE_FEE == amountTypeEnum) {
            updateResult = this.updateOrderAmountState(orderSn, amountTypeEnum, amount, stateEnum);
        }
        //修改预贴息金额
        else if (OrderAmountTypeEnum.PLAN_DISCOUNT_AMOUNT == amountTypeEnum) {
            updateResult = this.updateOrderAmountState(orderSn, amountTypeEnum, amount, stateEnum);
        }

        if (!updateResult.isSuccess()) {
            throw new BusinessException(String.format("更新订单金额状态记录失败,orderSn:%s,原因:%s", orderSn, updateResult.getMessage()));
        }
        return Result.ok(null);
    }

    private Result<Void> insertOrderAmountState(String orderSn, OrderAmountTypeEnum amountTypeEnum) {
        LambdaQueryWrapper<OrderAmountStateRecordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderAmountStateRecordPO::getOrderSn, orderSn);
        queryWrapper.eq(OrderAmountStateRecordPO::getAmountType, amountTypeEnum.getName());
        OrderAmountStateRecordPO entity = this.getOne(queryWrapper);
        if (!Objects.isNull(entity)) {
            Result<Void> result = new Result<>();
            result.setSuccess(false);
            result.setCode(String.valueOf(ErrorCodeEnum.S.DATA_UNIQUE_CONFLICT.getCode()));
            result.setMessage(ErrorCodeEnum.S.DATA_UNIQUE_CONFLICT.getMsg());
            return result;
        }
        log.info("插入订单金额状态记录,orderSn:{},amountType:{}", orderSn, amountTypeEnum.getName());
        OrderAmountStateRecordPO recordPO = new OrderAmountStateRecordPO(orderSn, amountTypeEnum.getName(),
                BigDecimal.ZERO, OrderAmountStateEnum.INIT.getName());

        if (!this.save(recordPO)) {
            throw new BusinessException(String.format("插入订单金额状态记录失败,orderSn:%s,amountType:%s", orderSn, amountTypeEnum.getMemo()));
        }
        return Result.ok(null);
    }


    private Result<Void> updateOrderAmountState(String orderSn, OrderAmountTypeEnum amountTypeEnum, BigDecimal amount, OrderAmountStateEnum stateEnum) {
        if (Objects.isNull(amount)) {
            throw new BusinessException(ErrorCodeEnum.U.EMPTY_PARAM.getMsg());
        }
        LambdaQueryWrapper<OrderAmountStateRecordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderAmountStateRecordPO::getOrderSn, orderSn);
        queryWrapper.eq(OrderAmountStateRecordPO::getAmountType, amountTypeEnum.getName());
        OrderAmountStateRecordPO entity = this.getOne(queryWrapper);
        if (Objects.isNull(entity)) {
            Result<Void> result = new Result<>();
            result.setSuccess(false);
            result.setCode(String.valueOf(ErrorCodeEnum.S.DATA_NOT_FOUND.getCode()));
            result.setMessage(ErrorCodeEnum.S.DATA_NOT_FOUND.getMsg());
            return result;
        }
        log.info("更新订单金额状态记录,orderSn:{},amountType:{},amount:{},amountState:{}",
                orderSn, amountTypeEnum.getName(), amount, stateEnum.getName());
        //变更状态与数据库状态一致,金额也一致时,不需要重复更新了
        if (StringUtils.equals(entity.getRecordState(), stateEnum.getName())) {
            //变更金额与数据库金额不一致，报错&告警
            if (entity.getAmount().compareTo(amount) != 0) {
                throw new MallException(String.format("更新订单金额状态记录,状态相同但金额不一致,orderSn:%s,amountType:%s"
                        , orderSn, amountTypeEnum.getMemo()));
            }
            return Result.ok(null);
        }

        OrderAmountStateRecordPO recordPO = new OrderAmountStateRecordPO(orderSn, amountTypeEnum.getName(), amount, stateEnum.getName());
        recordPO.setId(entity.getId());
        recordPO.setEffectTime(LocalDateTime.now());
        if (!this.updateById(recordPO)) {
            throw new BusinessException(ErrorCodeEnum.S.SYSTEM_EXCEPTION.getMsg());
        }
        return Result.ok(null);
    }

    @Override
    public Result<List<OrderAmountStateRecordVO>> listByOrderSn(String orderSn, OrderAmountTypeEnum amountType) {
        LambdaQueryWrapper<OrderAmountStateRecordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderAmountStateRecordPO::getOrderSn, orderSn);
        if (!Objects.isNull(amountType)) {
            queryWrapper.eq(OrderAmountStateRecordPO::getAmountType, amountType.getName());
        }
        List<OrderAmountStateRecordPO> entityList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(entityList)) {
            return Result.ok(Collections.emptyList());
        }
        List<OrderAmountStateRecordVO> voList = new ArrayList<>(entityList.size());
        for (OrderAmountStateRecordPO itemPO : entityList) {
            voList.add(this.entity2VO(itemPO));
        }
        return Result.ok(voList);
    }

    @Override
    public Result<OrderAmountStateRecordVO> getOneByOrderSn(String orderSn, OrderAmountTypeEnum amountType) {
        return this.getOneByOrderSn(orderSn,amountType,null);
    }

    @Override
    public Result<OrderAmountStateRecordVO> getOneByOrderSn(String orderSn, OrderAmountTypeEnum amountType, OrderAmountStateEnum stateEnum) {
        if (StringUtils.isBlank(orderSn) || Objects.isNull(amountType)) {
            Result<OrderAmountStateRecordVO> result = new Result<>();
            result.setSuccess(Boolean.FALSE);
            result.setCode(String.valueOf(ErrorCodeEnum.U.EMPTY_PARAM.getCode()));
            result.setMessage("查询订单关联费用金额记录条件不能为空");
            return result;
        }
        LambdaQueryWrapper<OrderAmountStateRecordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderAmountStateRecordPO::getOrderSn, orderSn);
        queryWrapper.eq(OrderAmountStateRecordPO::getAmountType, amountType.getName());
        if (!Objects.isNull(stateEnum)) {
            queryWrapper.eq(OrderAmountStateRecordPO::getRecordState, stateEnum.getName());
        }
        OrderAmountStateRecordPO entity = this.getOne(queryWrapper);
        if (Objects.isNull(entity)) {
            Result<OrderAmountStateRecordVO> result = new Result<>();
            result.setSuccess(Boolean.FALSE);
            result.setCode(String.valueOf(ErrorCodeEnum.S.DATA_NOT_FOUND.getCode()));
            result.setMessage("未找到指定的订单关联费用金额记录");
            return result;
        }
        OrderAmountStateRecordVO vo = this.entity2VO(entity);
        return Result.ok(vo);
    }

    @Override
    public Boolean saveServiceFeeAmount(String orderSn, String payNo, BigDecimal orderPlatformServiceFee) {
        OrderAmountStateRecordPO amountStateRecordPO = new OrderAmountStateRecordPO();
        amountStateRecordPO.setOrderSn(orderSn);
        amountStateRecordPO.setPayNo(payNo);
        amountStateRecordPO.setAmountType(OrderAmountTypeEnum.SERVICE_FEE.getName());
        amountStateRecordPO.setAmount(orderPlatformServiceFee);
        amountStateRecordPO.setRecordState(OrderAmountStateEnum.EFFECT.getName());
        amountStateRecordPO.setEffectTime(LocalDateTime.now());
        return this.save(amountStateRecordPO);
    }

    private OrderAmountStateRecordVO entity2VO(OrderAmountStateRecordPO entity) {
        OrderAmountStateRecordVO vo = new OrderAmountStateRecordVO();
        vo.setOrderSn(entity.getOrderSn());
        vo.setPayNo(entity.getPayNo());
        vo.setAmountTypeEnum(OrderAmountTypeEnum.parseEnum(entity.getAmountType()));
        vo.setAmount(entity.getAmount());
        vo.setRecordState(OrderAmountStateEnum.parseEnum(entity.getRecordState()));
        vo.setEffectTime(entity.getEffectTime());
        return vo;
    }

    @Override
    public boolean updateOrderAmountStateRecord(OrderAmountStateRecordPO orderAmountStateRecord) {
        LambdaQueryWrapper<OrderAmountStateRecordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderAmountStateRecordPO::getOrderSn, orderAmountStateRecord.getOrderSn());
        queryWrapper.eq(OrderAmountStateRecordPO::getAmountType, orderAmountStateRecord.getAmountType());
        queryWrapper.eq(OrderAmountStateRecordPO::getRecordState, OrderAmountStateEnum.EFFECT.getName());
        return super.update(orderAmountStateRecord, queryWrapper);
    }
    
    
}
