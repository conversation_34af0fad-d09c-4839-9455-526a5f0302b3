package com.cfpamf.ms.mallorder.v2.common.traceId;

import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.MDC;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * 类JobTraceIdAspect.java的实现描述：针对job增加一个项目内的链路id
 *
 * <AUTHOR> 14:36
 */
@Aspect
@Order(Integer.MIN_VALUE)
@Component
@Slf4j
public class JobTraceIdAspect {

    final String TRACE_ID = "X-B3-TraceId";

    @Pointcut("execution(* com.cfpamf.ms.mallorder.scheduler.*Job.*(..))")
    public void traceIdAspect() {}

    /**
     * traceId
     * 
     * @param joinPoint
     * @return
     * @throws Throwable
     */
    @Around("traceIdAspect()")
    public Object arround(ProceedingJoinPoint joinPoint) throws Throwable {
        try {
            ///////////////////////// traceId///////////////////////////////
            if (StringUtils.isEmpty(MDC.get(TRACE_ID))) {
                MDC.put(TRACE_ID, UUID.randomUUID().toString().replaceAll("-", ""));
            }
            Object result = joinPoint.proceed();
            return result;
        } catch (Throwable e) {
            log.error("【JobTraceIdAspect】joinPoint：{} arround异常", joinPoint, e);
            throw e;
        } finally {
            MDC.remove(TRACE_ID);
        }
    }
}