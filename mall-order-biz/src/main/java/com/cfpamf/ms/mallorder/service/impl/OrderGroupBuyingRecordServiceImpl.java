package com.cfpamf.ms.mallorder.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cfpamf.cmis.common.utils.DateUtils;
import com.cfpamf.framework.autoconfigure.mybatis.BaseRepoServiceImpl;
import com.cfpamf.ms.mallorder.builder.OrderQueryBuilder;
import com.cfpamf.ms.mallorder.common.enums.OrderProductDeliveryEnum;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.common.util.PoiUtils;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.*;
import com.cfpamf.ms.mallorder.enums.OrderGroupBuyingEnum;
import com.cfpamf.ms.mallorder.mapper.OrderGroupBuyingBindMapper;
import com.cfpamf.ms.mallorder.mapper.OrderGroupBuyingRecordMapper;
import com.cfpamf.ms.mallorder.po.OrderExtendPO;
import com.cfpamf.ms.mallorder.po.OrderGroupBuyingRecordPO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.req.query.GroupOrderQuery;
import com.cfpamf.ms.mallorder.req.query.GroupOrderRecordQuery;
import com.cfpamf.ms.mallorder.req.query.OrderGroupBuyingBindQuery;
import com.cfpamf.ms.mallorder.service.IOrderExtendService;
import com.cfpamf.ms.mallorder.service.IOrderGroupBuyingRecordService;
import com.cfpamf.ms.mallorder.vo.*;
import com.cfpamf.ms.mallshop.api.StoreLabelBindGoodsFeignClient;
import com.cfpamf.ms.mallshop.request.StoreLabelBindGoodsExample;
import com.cfpamf.ms.mallshop.resp.StoreLabelBindGoods;
import com.slodon.bbc.core.response.PageVO;
import com.slodon.bbc.core.response.PagerInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 拼单满赠记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-16
 */
@Service
@Slf4j
public class OrderGroupBuyingRecordServiceImpl extends BaseRepoServiceImpl<OrderGroupBuyingRecordMapper, OrderGroupBuyingRecordPO> implements IOrderGroupBuyingRecordService {

	@Resource
	private OrderGroupBuyingRecordMapper groupBuyingRecordMapper;

	@Resource
	private OrderGroupBuyingBindMapper groupBuyingBindMapper;

	@Autowired
	private IOrderExtendService orderExtendService;

	@Autowired
	private StoreLabelBindGoodsFeignClient storeLabelBindGoodsFeignClient;

	@Override
	public PageVO<GroupOrderRecordVO> ListOrderGroupBuyingRecord(GroupOrderRecordQuery query) {
		PagerInfo pager = new PagerInfo(query.getPageSize(), query.getCurrent());

		Page<GroupOrderRecordVO> page = new Page<>(query.getCurrent(), query.getPageSize());
		IPage<GroupOrderRecordVO> pageResult = groupBuyingRecordMapper.ListOrderGroupBuyingRecord(page, query);
		if (pageResult == null || CollectionUtils.isEmpty(pageResult.getRecords())) {
			pager.setRowsCount(0);
			return new PageVO<>(null, pager);
		}

		pager.setRowsCount((int) pageResult.getTotal());
		return new PageVO<>(pageResult.getRecords(), pager);
	}

	@Override
	public GroupOrderRecordDetailVO orderGroupBuyingDetail(String groupBuyingCode) {
		// 查询拼单满赠信息
		OrderGroupBuyingRecordPO groupBuyingRecordPO = this.getRecordByGroupBuyingCode(groupBuyingCode);
		BizAssertUtil.notNull(groupBuyingRecordPO, "拼单满赠信息不存在");

		GroupOrderRecordDetailVO groupOrderRecordDetailVO = new GroupOrderRecordDetailVO();
		BeanUtils.copyProperties(groupBuyingRecordPO, groupOrderRecordDetailVO);

		// 查询拼单订单信息
		OrderGroupBuyingBindQuery query = new OrderGroupBuyingBindQuery();
		query.setGroupBuyingCode(groupBuyingCode);
		List<GroupOrderVO> groupOrderVOS = groupBuyingBindMapper.listBindGroupOrder(query);
		groupOrderRecordDetailVO.setGroupOrderInfoList(groupOrderVOS);

		// 查询赠品订单信息
		GroupOrderQuery giftQuery = new GroupOrderQuery();
		giftQuery.setOrderSn(groupBuyingRecordPO.getGiftOrderSn());
		List<GroupOrderVO> giftOrderVO = groupBuyingRecordMapper.listGroupOrder(giftQuery);
		if (CollectionUtils.isNotEmpty(giftOrderVO)) {
			groupOrderRecordDetailVO.setGiftOrderInfo(giftOrderVO.get(0));
		}

		// 查询赠品订单收货地址
		OrderExtendPO giftOrderExtend = orderExtendService.getOrderExtendByOrderSn(groupBuyingRecordPO.getGiftOrderSn());
		OrderAddressDTO orderReceiveInfoVO = orderExtendService.buildReceiveInfo(giftOrderExtend);
		groupOrderRecordDetailVO.setGiftReceiveInfo(orderReceiveInfoVO);

		return groupOrderRecordDetailVO;
	}

	@Override
	public void export(HttpServletRequest request, HttpServletResponse response, GroupOrderRecordQuery query) {
		List<GroupOrderExportDTO> groupOrderExportDTOS = groupBuyingRecordMapper.exportRecord(query);

		for (GroupOrderExportDTO orderExportDTO : groupOrderExportDTOS) {
			//组装包裹信息
			if (CollectionUtils.isNotEmpty(orderExportDTO.getOrderDeliveryPackageDTOList())) {
				StringBuilder deliverTime = new StringBuilder();
				StringBuilder expressName = new StringBuilder();
				StringBuilder expressCode = new StringBuilder();
				StringBuilder expressNumber = new StringBuilder();
				StringBuilder deliverType = new StringBuilder();
				StringBuilder deliverName = new StringBuilder();
				StringBuilder deliverMobile = new StringBuilder();
				StringBuilder deliverWarehouseName = new StringBuilder();
				for (int i = 0; i < orderExportDTO.getOrderDeliveryPackageDTOList().size(); i++) {
					OrderDeliveryPackageDTO orderDeliveryPackageDTO = orderExportDTO.getOrderDeliveryPackageDTOList().get(i);
					if (i == orderExportDTO.getOrderDeliveryPackageDTOList().size() - 1) {
						deliverTime.append(OrderQueryBuilder.getNullDate(orderDeliveryPackageDTO.getCreateTime()));
						expressName.append(OrderQueryBuilder.getNullString(orderDeliveryPackageDTO.getExpressName()));
						expressCode.append(OrderQueryBuilder.getNullString(orderDeliveryPackageDTO.getExpressCompanyCode()));
						expressNumber.append(OrderQueryBuilder.getNullString(orderDeliveryPackageDTO.getExpressNumber()));
						deliverType.append(OrderQueryBuilder.getNullString(OrderQueryBuilder.getDeliverType(orderDeliveryPackageDTO.getDeliverType())));
						deliverName.append(OrderQueryBuilder.getNullString(orderDeliveryPackageDTO.getDeliverName()));
						deliverMobile.append(OrderQueryBuilder.getNullString(orderDeliveryPackageDTO.getDeliverMobile()));
						deliverWarehouseName.append(OrderQueryBuilder.getNullString(orderDeliveryPackageDTO.getDeliverWarehouseName()));
					} else {
						deliverTime.append(OrderQueryBuilder.getNullDate(orderDeliveryPackageDTO.getCreateTime())).append(",");
						expressName.append(OrderQueryBuilder.getNullString(orderDeliveryPackageDTO.getExpressName())).append(",");
						expressCode.append(OrderQueryBuilder.getNullString(orderDeliveryPackageDTO.getExpressCompanyCode())).append(",");
						expressNumber.append(OrderQueryBuilder.getNullString(orderDeliveryPackageDTO.getExpressNumber())).append(",");
						deliverType.append(OrderQueryBuilder.getNullString(OrderQueryBuilder.getDeliverType(orderDeliveryPackageDTO.getDeliverType()))).append(",");
						deliverName.append(OrderQueryBuilder.getNullString(orderDeliveryPackageDTO.getDeliverName())).append(",");
						deliverMobile.append(OrderQueryBuilder.getNullString(orderDeliveryPackageDTO.getDeliverMobile())).append(",");
						deliverWarehouseName.append(OrderQueryBuilder.getNullString(orderDeliveryPackageDTO.getDeliverWarehouseName())).append(",");
					}
				}
				orderExportDTO.setProductDeliverTime(deliverTime.toString());
				orderExportDTO.setExpressName(expressName.toString());
				orderExportDTO.setExpressCode(expressCode.toString());
				orderExportDTO.setExpressNumber(expressNumber.toString());
				orderExportDTO.setDeliverType(deliverType.toString());
				orderExportDTO.setDeliverName(deliverName.toString());
				orderExportDTO.setDeliverMobile(deliverMobile.toString());
				orderExportDTO.setDeliverWarehouseName(deliverWarehouseName.toString());
			}
		}

		Map<String, List<? extends Object>> sheepMap = new HashMap<>(16);
		sheepMap.put("拼单满赠列表", groupOrderExportDTOS);
		PoiUtils.exportByHttp(request, response, "拼单满赠列表" + DateUtils.format(new Date(), "yyyyMMdd"),
				sheepMap, PoiUtils.DEFAULT_DATE_FORMAT, null, false);
	}

	@Override
	public OrderGroupBuyingRecordPO getRecordByGroupBuyingCode(String groupBuyingCode) {
		LambdaQueryWrapper<OrderGroupBuyingRecordPO> query = Wrappers.lambdaQuery();
		query.eq(OrderGroupBuyingRecordPO::getGroupBuyingCode, groupBuyingCode)
				.eq(OrderGroupBuyingRecordPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
		return groupBuyingRecordMapper.selectOne(query);
	}

	@Override
	public PageVO<GroupOrderVO> groupOrderList(GroupOrderQuery query) {
		PagerInfo pager = new PagerInfo(query.getPageSize(), query.getCurrent());

		Page<GroupOrderVO> page = new Page<>(query.getCurrent(), query.getPageSize());
		IPage<GroupOrderVO> pageResult = groupBuyingRecordMapper.listWaitingGroupOrderPage(page, query);
		if (pageResult == null || CollectionUtils.isEmpty(pageResult.getRecords())) {
			pager.setRowsCount(0);
			return new PageVO<>(null, pager);
		}

		// 查询订单商品
		for (GroupOrderVO groupOrderVO : pageResult.getRecords()) {
			List<GroupOrderProductVO> groupOrderProductVOS =
					groupBuyingRecordMapper.listWaitingGroupOrderProduct(groupOrderVO.getOrderSn(), query.getStoreCategoryId());
			groupOrderVO.setOrderProductVOs(groupOrderProductVOS);
		}
		pager.setRowsCount((int) pageResult.getTotal());
		return new PageVO<>(pageResult.getRecords(), pager);
	}

	@Override
	public List<GroupOrderStatisticVO> groupOrderStatistic(GroupOrderQuery query) {
		// 查询拼单订单数量统计：以店铺、分支、分类为一个维度
		List<GroupOrderStatisticDTO> groupOrderStatisticDTOS = groupBuyingRecordMapper.statisticGroupOrderNumber(query);
		List<GroupOrderStatisticVO> groupOrderStatisticVOS = new ArrayList<>();
		if (CollectionUtils.isEmpty(groupOrderStatisticDTOS)) {
			return groupOrderStatisticVOS;
		}

		Map<String, GroupOrderStatisticVO> groupOrderStatisticVOMap = new HashMap<>();
		Map<String, GroupOrderStatisticVO.BranchStatisticVO> branchStatisticVOMap = new HashMap<>();

		for (GroupOrderStatisticDTO groupOrderStatisticDTO : groupOrderStatisticDTOS) {
			// 商品店铺分类有多个
			List<String> storeCategoryIds = Arrays.stream(groupOrderStatisticDTO.getStoreCategoryId().split(","))
					.collect(Collectors.toList());
			if (CollectionUtils.isEmpty(storeCategoryIds)) {
				continue;
			}

			/**
			 * 遍历分类，根据店铺和分类的不同组合，在Map中查找对应的VO对象。
			 * 如果不存在，则创建新的VO对象，并添加到Map和列表中。
			 * 如果VO对象已存在，则根据分支统计数量，更新相应的字段值。
			 */
			for (String storeCategoryId : storeCategoryIds) {
				String key = groupOrderStatisticDTO.getStoreId() + "_" + storeCategoryId;
				String branchKey = groupOrderStatisticDTO.getStoreId() + "_" + storeCategoryId + "_" + groupOrderStatisticDTO.getBranchCode();

				GroupOrderStatisticVO groupOrderStatisticVO = groupOrderStatisticVOMap.get(key);
				if (Objects.isNull(groupOrderStatisticVO)) {
					groupOrderStatisticVO = new GroupOrderStatisticVO();
					groupOrderStatisticVO.setStoreId(groupOrderStatisticDTO.getStoreId());
					groupOrderStatisticVO.setStoreName(groupOrderStatisticDTO.getStoreName());
					groupOrderStatisticVO.setStoreCategoryId(Integer.valueOf(storeCategoryId));
					// 分类名称查询
					StoreLabelBindGoodsExample example = new StoreLabelBindGoodsExample();
					example.setInnerLabelId(Integer.valueOf(storeCategoryId));
					List<StoreLabelBindGoods> storeLabelBindGoodsList = storeLabelBindGoodsFeignClient.getStoreLabelBindGoodsList(example);
					if (CollectionUtils.isNotEmpty(storeLabelBindGoodsList)) {
						groupOrderStatisticVO.setStoreCategoryName(storeLabelBindGoodsList.get(0).getInnerLabelName());
					}
					GroupOrderStatisticVO.BranchStatisticVO branchStatisticVO = new GroupOrderStatisticVO.BranchStatisticVO();
					branchStatisticVO.setBranchName(groupOrderStatisticDTO.getBranchName());
					branchStatisticVO.setBranchCode(groupOrderStatisticDTO.getBranchCode());
					branchStatisticVO.setTotalNum(groupOrderStatisticDTO.getTotalNum());
					branchStatisticVO.setValidNum(groupOrderStatisticDTO.getValidNum());
					branchStatisticVO.setReturnNumber(groupOrderStatisticDTO.getReturnNumber());
					groupOrderStatisticVO.setBranchStatisticVOs(new ArrayList<>());
					groupOrderStatisticVO.getBranchStatisticVOs().add(branchStatisticVO);

					branchStatisticVOMap.put(branchKey, branchStatisticVO);
					groupOrderStatisticVOMap.put(key, groupOrderStatisticVO);
					groupOrderStatisticVOS.add(groupOrderStatisticVO);
				} else {
					if (branchStatisticVOMap.containsKey(branchKey)) {
						GroupOrderStatisticVO.BranchStatisticVO branchStatisticVO = branchStatisticVOMap.get(branchKey);
						branchStatisticVO.setTotalNum(branchStatisticVO.getTotalNum() + groupOrderStatisticDTO.getTotalNum());
						branchStatisticVO.setValidNum(branchStatisticVO.getValidNum() + groupOrderStatisticDTO.getValidNum());
						branchStatisticVO.setReturnNumber(branchStatisticVO.getReturnNumber() + groupOrderStatisticDTO.getReturnNumber());
					} else {
						GroupOrderStatisticVO.BranchStatisticVO branchStatisticVO = new GroupOrderStatisticVO.BranchStatisticVO();
						branchStatisticVO.setBranchName(groupOrderStatisticDTO.getBranchName());
						branchStatisticVO.setBranchCode(groupOrderStatisticDTO.getBranchCode());
						branchStatisticVO.setTotalNum(groupOrderStatisticDTO.getTotalNum());
						branchStatisticVO.setValidNum(groupOrderStatisticDTO.getValidNum());
						branchStatisticVO.setReturnNumber(groupOrderStatisticDTO.getReturnNumber());
						groupOrderStatisticVO.getBranchStatisticVOs().add(branchStatisticVO);

						branchStatisticVOMap.put(branchKey, new GroupOrderStatisticVO.BranchStatisticVO());
					}
				}
			}
		}

		return groupOrderStatisticVOS;
	}

	@Override
	public OrderAddressDTO orderReceiveInfo(String orderSn) {
		OrderExtendPO orderExtend = orderExtendService.getOrderExtendByOrderSn(orderSn);
		BizAssertUtil.notNull(orderExtend, "订单信息不存在");
		return orderExtendService.buildReceiveInfo(orderExtend);
	}

	@Override
	public Boolean checkGroupOrder(GroupOrderProductSubmitDTO dto) {
		// 拼单订单商品去重
		List<Long> groupOrderProductIds = dto.getGroupOrderProductIdList().stream()
				.distinct().collect(Collectors.toList());
		dto.setGroupOrderProductIdList(groupOrderProductIds);

		// 查询拼单订单
		GroupOrderQuery query = new GroupOrderQuery();
		query.setGroupOrderProductIdList(groupOrderProductIds);
//		query.setStoreId(dto.getStoreId());
//		query.setStoreCategoryId(dto.getStoreCategoryId().toString());
//		query.setBranchCode(dto.getBranchCode());
		List<GroupOrderProductVO> groupOrderProductVOS = groupBuyingRecordMapper.listGroupOrderProduct(query);
		BizAssertUtil.isTrue(groupOrderProductIds.size() > groupOrderProductVOS.size(), "拼单订单商品不存在，请确认");

		// 校验拼单商品筛选条件：店铺、分支、分类 （赠品商品不受限制）
		// 校验拼单商品条件：拼单标识：1-未拼单 、商品发货状态：0-待发货
		groupOrderProductVOS.forEach(orderProductVO -> {
			BizAssertUtil.isTrue(orderProductVO.getIsGift() == 0 && !orderProductVO.getStoreId().equals(dto.getStoreId()),
					String.format("【%s】中【%s】不属于当前店铺，请重新选择拼单订单", orderProductVO.getOrderSn(), orderProductVO.getGoodsName()));
			BizAssertUtil.isTrue(orderProductVO.getIsGift() == 0 && !orderProductVO.getStoreCategoryId().contains(dto.getStoreCategoryId().toString()),
					String.format("【%s】中【%s】不属于当前分类，请重新选择拼单订单", orderProductVO.getOrderSn(), orderProductVO.getGoodsName()));
			BizAssertUtil.isTrue(orderProductVO.getIsGift() == 0 && !orderProductVO.getBranchCode().equals(dto.getBranchCode()),
					String.format("【%s】中【%s】不属于当前分支，请重新选择拼单订单", orderProductVO.getOrderSn(), orderProductVO.getGoodsName()));
			BizAssertUtil.isTrue(orderProductVO.getDeliveryState() != OrderProductDeliveryEnum.WAIT_DELIVERY,
					String.format("【%s】中【%s】已发货，请重新选择拼单订单", orderProductVO.getOrderSn(), orderProductVO.getGoodsName()));
			BizAssertUtil.isTrue(!OrderGroupBuyingEnum.WAITING.getValue().equals(orderProductVO.getGroupBuyingTag()),
					String.format("【%s】中【%s】已关联其他拼单满赠，请重新选择拼单订单", orderProductVO.getOrderSn(), orderProductVO.getGoodsName()));
		});

		dto.setGroupOrderProductVOList(groupOrderProductVOS);
		return true;
	}

	@Override
	public OrderGroupBuyingRecordPO buildOrderGroupBuyingRecord(GroupOrderProductSubmitDTO dto, OrderExtendPO orderExtendPO, OrderPO giftOrder) {
		OrderGroupBuyingRecordPO orderGroupBuyingRecordPO = new OrderGroupBuyingRecordPO();

		// 生成拼单满赠编码
		orderGroupBuyingRecordPO.setGroupBuyingCode("PDMZ" + giftOrder.getOrderSn().substring(2));
		orderGroupBuyingRecordPO.setGiftOrderSn(giftOrder.getOrderSn());
		orderGroupBuyingRecordPO.setStoreId(giftOrder.getStoreId());
		orderGroupBuyingRecordPO.setStoreName(giftOrder.getStoreName());
		// 拼单订单统计
		List<GroupOrderProductVO> groupOrderProductVOList = dto.getGroupOrderProductVOList();
		Set<String> orderSnSet = new HashSet<>(groupOrderProductVOList.size());
		Integer groupGoodsNum = 0;
		for (GroupOrderProductVO orderProductVO : groupOrderProductVOList) {
			orderSnSet.add(orderProductVO.getOrderSn());
			// 排除赠品数量
			if (!orderProductVO.getIsGift().equals(OrderConst.IS_GIFT_YES)) {
				groupGoodsNum += orderProductVO.getValidNum();
			}
		}
		orderGroupBuyingRecordPO.setGroupOrderNum(orderSnSet.size());
		orderGroupBuyingRecordPO.setGroupGoodsNum(groupGoodsNum);
		// 赠品商品数统计
		Integer giftGoodsNum = dto.getSkuInfoList().stream().mapToInt(OrderSkuInfoDTO::getNumber).sum();
		orderGroupBuyingRecordPO.setGiftGoodsNum(giftGoodsNum);
		// 店铺分类信息
		orderGroupBuyingRecordPO.setStoreCategoryId(dto.getStoreCategoryId());
		StoreLabelBindGoodsExample example = new StoreLabelBindGoodsExample();
		example.setInnerLabelId(dto.getStoreCategoryId());
		List<StoreLabelBindGoods> storeLabelBindGoodsList = storeLabelBindGoodsFeignClient.getStoreLabelBindGoodsList(example);
		BizAssertUtil.notEmpty(storeLabelBindGoodsList, "分类信息不存在");
		orderGroupBuyingRecordPO.setStoreCategoryName(storeLabelBindGoodsList.get(0).getInnerLabelName());
		// 区域信息
		orderGroupBuyingRecordPO.setBranchCode(orderExtendPO.getBranch());
		orderGroupBuyingRecordPO.setBranchName(orderExtendPO.getBranchName());
		orderGroupBuyingRecordPO.setAreaCode(orderExtendPO.getAreaCode());
		orderGroupBuyingRecordPO.setAreaName(orderExtendPO.getAreaName());
		orderGroupBuyingRecordPO.setCreateTime(new Date());
		orderGroupBuyingRecordPO.setUpdateTime(new Date());
		orderGroupBuyingRecordPO.setCreateBy(giftOrder.getCreateBy());
		orderGroupBuyingRecordPO.setUpdateBy(giftOrder.getCreateBy());
		orderGroupBuyingRecordPO.setEnabledFlag(1);

		return orderGroupBuyingRecordPO;
	}


}
