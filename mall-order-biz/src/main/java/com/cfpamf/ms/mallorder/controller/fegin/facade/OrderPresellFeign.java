package com.cfpamf.ms.mallorder.controller.fegin.facade;

import com.cfpamf.ms.mallorder.api.feign.OrderPresellFeignClient;
import com.cfpamf.ms.mallorder.v2.service.OrderPresellService;
import com.cfpamf.ms.mallorder.vo.OrderPresellVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
public class OrderPresellFeign implements OrderPresellFeignClient {

    @Autowired
    private OrderPresellService orderPresellService;

    @Override
    @PostMapping("/v1/feign/business/orderPresell/listByOrderSn")
    public List<OrderPresellVO> listByOrderSn(@RequestParam("orderSn") String orderSn,
                                              @RequestParam(value = "type",required = false) Integer type) {
         return orderPresellService.listByOrderSn(orderSn, type);
    }

    @Override
    @PostMapping("/v1/feign/business/orderPresell/findByPayNo")
    public OrderPresellVO findByPayNo(@RequestParam("payNo") String payNo) {
        return orderPresellService.findByPayNo(payNo);
    }
}
