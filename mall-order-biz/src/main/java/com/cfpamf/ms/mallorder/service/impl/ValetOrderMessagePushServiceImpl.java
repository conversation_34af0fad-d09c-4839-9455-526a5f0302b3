package com.cfpamf.ms.mallorder.service.impl;

import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.cfpamf.ms.mallorder.builder.ValetOrderMessagePushBuilder;
import com.cfpamf.ms.mallorder.common.messagepush.MessagePushComponent;
import com.cfpamf.ms.mallorder.po.OrderExtendPO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.service.IOrderExtendService;
import com.cfpamf.ms.mallorder.service.IOrderService;
import com.cfpamf.ms.mallorder.service.ValetOrderMessagePushService;
import com.cfpamf.msgpush.facade.request.MessagePushV2Request;

import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

@Slf4j
@Service
public class ValetOrderMessagePushServiceImpl implements ValetOrderMessagePushService {

	@Autowired
	private MessagePushComponent messagePushComponent;

	@Autowired
	private IOrderService orderService;

	@Autowired
	private IOrderExtendService orderExtendService;

	@Resource
	private ValetOrderMessagePushBuilder valetOrderMessagePushBuilder;

	@Override
	public boolean customerUnConfirmMessagePush(String orderSn) throws Exception {
		log.info("【customerUnConfirmMessagePush】orderSn：{}", orderSn);
		OrderPO orderPO = orderService.getByOrderSn(orderSn);
		if (Objects.isNull(orderPO)) {
			log.info("【customerUnConfirmMessagePush】orderSn：{} 查询订单为空，拒绝消息通知", orderSn);
			return false;
		}
		OrderExtendPO orderExtendPO = orderExtendService.getOrderExtendByOrderSn(orderSn);
		if (Objects.isNull(orderExtendPO)) {
			log.info("【customerUnConfirmMessagePush】orderSn：{} 查询orderExtendPO为空，拒绝消息通知", orderSn);
			return false;
		}
		MessagePushV2Request message2Customer = valetOrderMessagePushBuilder.buildMessage2Customer(
				orderExtendPO.getCustomerId(), orderExtendPO.getManager(), orderSn, orderPO.getOrderAmount());
		return messagePushComponent.send(message2Customer);
	}

	@Override
	public boolean customerConfirmMessagePush(String orderSn) throws Exception {
		log.info("【customerConfirmMessagePush】orderSn：{}", orderSn);
		OrderPO orderPO = orderService.getByOrderSn(orderSn);
		if (Objects.isNull(orderPO)) {
			log.info("【customerConfirmMessagePush】orderSn：{} 查询订单为空，拒绝消息通知", orderSn);
			return false;
		}
		OrderExtendPO orderExtendPO = orderExtendService.getOrderExtendByOrderSn(orderSn);
		if (Objects.isNull(orderExtendPO)) {
			log.info("【customerConfirmMessagePush】orderSn：{} 查询orderExtendPO为空，拒绝消息通知", orderSn);
			return false;
		}
		MessagePushV2Request message2Manager = valetOrderMessagePushBuilder.buildMessage2Manager(
				orderExtendPO.getCustomerId(), orderExtendPO.getManager(), orderSn, orderPO.getOrderAmount());
		return messagePushComponent.send(message2Manager);
	}

	@Override
	public boolean customerConfirmRefuseMessagePush(String orderSn) throws Exception {
		log.info("【customerConfirmRefuseMessagePush】orderSn：{}", orderSn);
		OrderPO orderPO = orderService.getByOrderSn(orderSn);
		if (Objects.isNull(orderPO)) {
			log.info("【customerConfirmRefuseMessagePush】orderSn：{} 查询订单为空，拒绝消息通知", orderSn);
			return false;
		}
		OrderExtendPO orderExtendPO = orderExtendService.getOrderExtendByOrderSn(orderSn);
		if (Objects.isNull(orderExtendPO)) {
			log.info("【customerConfirmRefuseMessagePush】orderSn：{} 查询orderExtendPO为空，拒绝消息通知", orderSn);
			return false;
		}
		MessagePushV2Request message2ManagerByCustomerRefuse = valetOrderMessagePushBuilder
				.buildMessage2ManagerByCustomerRefuse(orderExtendPO.getCustomerId(), orderExtendPO.getManager(),
						orderSn, orderPO.getOrderAmount());
		return messagePushComponent.send(message2ManagerByCustomerRefuse);
	}

}
