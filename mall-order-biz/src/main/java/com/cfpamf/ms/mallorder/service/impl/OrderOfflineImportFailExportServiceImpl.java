package com.cfpamf.ms.mallorder.service.impl;

import com.cfpamf.ms.mall.filecenter.component.FileComponent;
import com.cfpamf.ms.mall.filecenter.domain.dto.ColumnDTO;
import com.cfpamf.ms.mall.filecenter.domain.dto.FileDTO;
import com.cfpamf.ms.mall.filecenter.domain.vo.ExcelAsyncExportVO;
import com.cfpamf.ms.mall.filecenter.service.impl.AbstractExcelDataExportServiceImpl;
import com.cfpamf.ms.mallorder.builder.OrderQueryBuilder;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.enums.CommonEnum;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.common.util.DateUtil;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.OrderExportDTO;
import com.cfpamf.ms.mallorder.dto.OrderOfflineImportDTO;
import com.cfpamf.ms.mallorder.dto.UserDTO;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.po.BzOrderProductInstallPO;
import com.cfpamf.ms.mallorder.po.OrderPromotionDetailPO;
import com.cfpamf.ms.mallorder.req.OrderListQueryRequest;
import com.cfpamf.ms.mallorder.req.OrderOfflineExportRequest;
import com.cfpamf.ms.mallorder.request.OrderExample;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.v2.domain.dto.OrderPresellDTO;
import com.cfpamf.ms.mallorder.vo.exportvo.OrderRefundExportVO;
import com.cfpamf.ms.mallshop.api.StoreFeignClient;
import com.google.common.collect.Maps;
import com.slodon.bbc.core.response.PagerInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 线下单导入失败数据导出
 * @Author: zhoucs
 */
@Slf4j
@Service
public class OrderOfflineImportFailExportServiceImpl
		extends AbstractExcelDataExportServiceImpl<OrderOfflineImportDTO, OrderOfflineExportRequest>
		implements OrderOfflineImportFailExportService {

	public final static String ORDER_OFFLINE_EXCEL_SHEET_NAME = "线下单补录";
	@Autowired
	private FileComponent fileComponent;

	@Value("${spring.application.name}")
	private String appName;


	@Override
	public FileDTO executeAsyncExportExcel(OrderOfflineExportRequest request) throws Exception {
		ExcelAsyncExportVO excelAsyncExportVO = new ExcelAsyncExportVO();
		excelAsyncExportVO.setBizModule(request.getBizModule());
		excelAsyncExportVO.setUserId(Long.valueOf(request.getUserDTO().getUserId()));
		excelAsyncExportVO.setUserName(request.getUserDTO().getUserName());
		excelAsyncExportVO.setUserType(getUserType(request.getUserDTO().getUserRole()));
		excelAsyncExportVO.setApplicationCondition(request.getApplyCondition());
		return fileComponent.executeAsyncExportExcelWithAnnotation(excelAsyncExportVO, appName, ORDER_OFFLINE_EXCEL_SHEET_NAME,
				this, request, 10000, new OrderOfflineImportDTO());
	}

	private Integer getUserType(Integer userRole) {
		int result = 2;
		if (userRole == null) {
			return result;
		}
		if (userRole == 1) {
			return 1;
		} else if (userRole == 2) {
			return 0;
		}
		return result;
	}

	@Override
	public Integer getDataCounts(OrderOfflineExportRequest request) {
		Integer total = request.getFailList().size();
		log.info("【线下单导入失败】getDataCounts，total：{}", total);
		return total;
	}

	@Override
	public List<ColumnDTO> createTemplate(OrderOfflineExportRequest request) {
		List<ColumnDTO> columnDTOS = new ArrayList<>();
		// 创建模板 columnName - 列标题， columnProperties - 列属性值， index - 列顺序
		columnDTOS.add(new ColumnDTO("创建时间", "createTime", 0));
		columnDTOS.add(new ColumnDTO("订单类型", "orderType", 1));
		columnDTOS.add(new ColumnDTO("订单来源", "orderPattern", 2));
		columnDTOS.add(new ColumnDTO("用户手机号", "userMobile", 3));
		columnDTOS.add(new ColumnDTO("客户经理工号", "managerCode", 4));
		columnDTOS.add(new ColumnDTO("省", "province", 5));
		columnDTOS.add(new ColumnDTO("市", "city", 6));
		columnDTOS.add(new ColumnDTO("区", "district", 7));
		columnDTOS.add(new ColumnDTO("县/街道", "town", 8));
		columnDTOS.add(new ColumnDTO("详细地址", "detailAddress", 9));
		columnDTOS.add(new ColumnDTO("商品SKU", "skuId", 10));
		columnDTOS.add(new ColumnDTO("含税售价", "taxPrice", 11));
		columnDTOS.add(new ColumnDTO("下单数量", "buyNum", 12));
		columnDTOS.add(new ColumnDTO("收款标签", "paymentTag", 13));
		columnDTOS.add(new ColumnDTO("优惠金额", "discountAmount", 14));
		columnDTOS.add(new ColumnDTO("收件人姓名", "receiverName", 15));
		columnDTOS.add(new ColumnDTO("收件人手机号码", "receiverMobile", 16));
		columnDTOS.add(new ColumnDTO("开票标签", "invoiceStatus", 17));
		columnDTOS.add(new ColumnDTO("失败原因", "remark", 18));

		return columnDTOS;
	}

	@Override
	public List<OrderOfflineImportDTO> getData(OrderOfflineExportRequest request) {
		if (!CollectionUtils.isEmpty(request.getFailList())) {
			return request.getFailList();
		}
		return null;
	}

	@Override
	public List<OrderOfflineImportDTO> getDataByPage(OrderOfflineExportRequest request, Integer pageNum, Integer pageSize) {
		if (!CollectionUtils.isEmpty(request.getFailList())) {
			return request.getFailList();
		}
		return null;
	}
}
