package com.cfpamf.ms.mallorder.vo;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.loan.facade.vo.CmisContractFileVo;
import com.cfpamf.ms.mallmember.po.MemberInvoice;
import com.cfpamf.ms.mallorder.constant.DomainUrlConstant;
import com.cfpamf.ms.mallorder.dto.OfflineOrderInfoDTO;
import com.cfpamf.ms.mallorder.dto.OrderMaterialDTO;
import com.cfpamf.ms.mallorder.dto.OrderSubmitDTO;
import com.cfpamf.ms.mallorder.enums.OrderPerformanceModeEnum;
import com.cfpamf.ms.mallorder.integration.filecenter.SceneTypeEnum;
import com.cfpamf.ms.mallorder.po.OrderExtendPO;
import com.cfpamf.ms.mallorder.po.OrderLogPO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.v2.domain.dto.OrderPresellDTO;
import com.cfpamf.ms.mallorder.vo.basevo.OrderDetailBaseVO;
import com.cfpamf.ms.mallorder.vo.filescene.FileScenesProductWithResultVO;
import com.slodon.bbc.core.constant.OrderConst;
import com.slodon.bbc.core.i18n.Language;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 */
@Data
public class OrderVO extends OrderDetailBaseVO {

    @ApiModelProperty("订单id")
    private Integer orderId;

    @ApiModelProperty("订单号")
    private String orderSn;

    @ApiModelProperty("商家ID")
    private Long storeId;

    @ApiModelProperty("商家名称")
    private String storeName;

    @ApiModelProperty("订单状态：0-已取消；10-未付款订单；20-已付款；30-已发货；40-已完成;50-已关闭")
    private Integer orderState;

    @ApiModelProperty("订单状态值：0-已取消；10-未付款订单；20-已付款；30-已发货；40-已完成;50-已关闭")
    private String orderStateValue;

    @ApiModelProperty("订单模式：1-B端采购中心，2-C端店铺街")
    private Integer orderPattern;

    @ApiModelProperty("支付方式名称，参考OrderPaymentConst类")
    private String paymentName;

    @ApiModelProperty("支付方式code, 参考OrderPaymentConst类")
    private String paymentCode;

    @ApiModelProperty("会员名称")
    private String memberName;

    @ApiModelProperty("用户号码")
    private String userMobile;

    @ApiModelProperty("收货人")
    private String receiverName;

    @ApiModelProperty("省市区组合")
    private String receiverAreaInfo;

    @ApiModelProperty("收货地址")
    private String receiverAddress;

    @ApiModelProperty("收货省")
    private String receiverProvince;

    @ApiModelProperty("收货市")
    private String receiverCity;

    @ApiModelProperty("收货区")
    private String receiverDistrict;

    @ApiModelProperty("收货县/镇")
    private String receiverTown;

    @ApiModelProperty("收货人手机号")
    private String receiverMobile;

    @ApiModelProperty("自提点名称")
    private String pointName;

    @ApiModelProperty("订单创建时间")
    private Date createTime;

    @ApiModelProperty("发货时间")
    private Date deliverTime;

    @ApiModelProperty("订单完成时间")
    private Date finishTime;

    @ApiModelProperty("锁定状态:0是正常,大于0是锁定,默认是0")
    private Integer lockState;

    @ApiModelProperty("订单应付金额 = 商品总金额 +总运费")
    private BigDecimal orderAmountTotal;

    @ApiModelProperty("订单实付金额 = 商品总金额 ＋运费 -优惠总金额 -乡助卡抵扣金额")
    private BigDecimal orderAmount;

    @ApiModelProperty("改价差额实付金额，调低为负，调高为正")
    private BigDecimal marginOrderAmount;

    @ApiModelProperty("商品总金额，等于订单中所有的商品的单价乘以数量之和")
    private BigDecimal goodsAmount;

    @ApiModelProperty("总运费 = 支付运费 +乡助卡抵扣运费")
    private BigDecimal expressFeeTotal;

    @ApiModelProperty("乡助卡优惠金额")
    private BigDecimal xzCardAmount;

    @ApiModelProperty("乡助卡运费优惠金额")
    private BigDecimal xzCardExpressFeeAmount;

    @ApiModelProperty("物流费用")
    private BigDecimal expressFee;

    @ApiModelProperty("三方支付金额")
    private BigDecimal payAmount;

    @ApiModelProperty("余额账户支付总金额")
    private BigDecimal balanceAmount;

    @ApiModelProperty("积分抵扣金额")
    private BigDecimal integralCashAmount;

    @ApiModelProperty("活动优惠总金额 （= 店铺优惠券 + 平台优惠券 + 活动优惠【店铺活动 + 平台活动】 + 积分抵扣金额）")
    private BigDecimal activityDiscountAmount;

    @ApiModelProperty("订单佣金")
    private BigDecimal commission;

    @ApiModelProperty("取消原因")
    private String refuseReason;

    @ApiModelProperty("取消备注")
    private String refuseRemark;

    @ApiModelProperty("会员邮箱")
    private String memberEmail;

    @ApiModelProperty("订单备注")
    private String orderRemark;

    @ApiModelProperty("优惠券面额")
    private BigDecimal voucherPrice;

    @ApiModelProperty("发票信息")
    private MemberInvoice invoiceInfo;

    @ApiModelProperty("发票状态0-未开、1-已开")
    private Integer invoiceStatus;

    @ApiModelProperty("发票状态值0-未开、1-已开")
    private String invoiceStatusValue;

    @ApiModelProperty("发货类型：0-物流发货，1-无需物流")
    private Integer deliverType;

    @ApiModelProperty("发货人")
    private String deliverName;

    @ApiModelProperty("发货人电话")
    private String deliverMobile;

    @ApiModelProperty(value = "下单渠道：H5-浏览器H5，APP-乡助APP，WE_CHAT-微信浏览器，MINI_PRO-小程序")
    private String channel;

    @ApiModelProperty("订单商品信息")
    private List<OrderProductAdminVO> orderProductPOList = new ArrayList<>();

    @ApiModelProperty("订单日志信息")
    private List<OrderLogPO> orderLogs;

    @ApiModelProperty("促销信息")
    private List<OrderSubmitDTO.PromotionInfo> promotionInfo;

    @ApiModelProperty("组合支付方式名")
    private String composePayName;

    @ApiModelProperty("贷款分期信息")
    private LoanInfoVo loanInfoVo;

    @ApiModelProperty("预付订金信息")
    private OrderPresellDTO orderPresellDTO;

    @ApiModelProperty("展示银行卡汇款信息")
    private Boolean showBankTransfer;

    @ApiModelProperty("大额订单的过期时间")
    private String expireDatetime;

    @ApiModelProperty(value = "付款账号")
    private String payerAccount;

    @ApiModelProperty(value = "付款人姓名")
    private String payerName;

    @ApiModelProperty(value = "收款账号")
    private String receiptAccount;

    @ApiModelProperty(value = "收款人姓名")
    private String receiptName;

    @ApiModelProperty(value = "收款银行名称")
    private String receiptBankName;

    @ApiModelProperty("定金展示银行卡汇款信息")
    private Boolean depositShowBankTransfer;

    @ApiModelProperty("定金大额订单的过期时间")
    private String depositExpireDatetime;

    @ApiModelProperty(value = "定金付款账号")
    private String depositPayerAccount;

    @ApiModelProperty(value = "定金付款人姓名")
    private String depositPayerName;

    @ApiModelProperty(value = "定金收款账号")
    private String depositReceiptAccount;

    @ApiModelProperty(value = "定金收款人姓名")
    private String depositReceiptName;

    @ApiModelProperty(value = "定金收款银行名称")
    private String depositReceiptBankName;

    @ApiModelProperty("尾款展示银行卡汇款信息")
    private Boolean balanceShowBankTransfer;

    @ApiModelProperty("尾款大额订单的过期时间")
    private String balanceExpireDatetime;

    @ApiModelProperty(value = "尾款付款账号")
    private String balancePayerAccount;

    @ApiModelProperty(value = "尾款付款人姓名")
    private String balancePayerName;

    @ApiModelProperty(value = "尾款收款账号")
    private String balanceReceiptAccount;

    @ApiModelProperty(value = "尾款收款人姓名")
    private String balanceReceiptName;

    @ApiModelProperty(value = "尾款收款银行名称")
    private String balanceReceiptBankName;
    
    @ApiModelProperty(value = "订单类型")
    private Integer orderType;
    
    @ApiModelProperty("支付单号")
    private String paySn;

    @ApiModelProperty("履约模式，0-常规，1-供应商，2-到家服务")
    private List<Integer> performanceModeList;

    @ApiModelProperty("履约模式描述")
    private List<String> performanceModeValueList;

    @ApiModelProperty("供应商名称")
    private String supplierName;
    
    @ApiModelProperty("客户确认状态code：0-无需确认，1-草稿，2-待确认，3-已确认 4-拒绝 见枚举CustomerConfirmStatusEnum")
    private Integer customerConfirmStatus;
    
    @ApiModelProperty("客户确认状态描述：0-无需确认，1-草稿，2-待确认，3-已确认 4-拒绝 见枚举CustomerConfirmStatusEnum")
    private String customerConfirmStatusDesc;
    
    @ApiModelProperty("贷款合同文件集合")
    private List<CmisContractFileVo> contractFiles;

    @ApiModelProperty("发货资料集合")
    private List<OrderMaterialDTO> deliverMaterialImageUrlList;

    @ApiModelProperty("收货资料集合")
    private List<OrderMaterialDTO> receiveMaterialImageUrlList;

    @ApiModelProperty("订单资料集合：发货资料、收货资料、贷款类资料")
    private Map<SceneTypeEnum, List<OrderMaterialDTO>> orderMaterialMap;

    @ApiModelProperty("订单各场景凭证资料集合")
    private List<FileScenesProductWithResultVO> fileScenesProofVOS;

    @ApiModelProperty("换货列表")
    private List<OrderExchangeDetailVO> exchangeDetailVOList;

    @ApiModelProperty("补录订单信息")
    private OfflineOrderInfoDTO offlineOrderInfo;

    @ApiModelProperty("订单业绩归属信息表")
    private OrderPerformanceBelongsVO performanceBelongsVO;

    @ApiModelProperty(value = "交易凭证信息")
    private List<MaterialSceneDTO> proofList;

    @ApiModelProperty("客户姓名")
    private String customerName;


    public OrderVO(OrderPO orderPO, OrderExtendPO orderExtendPO) {
        super(orderPO, orderExtendPO);
        orderId = orderPO.getOrderId();
        orderSn = orderPO.getOrderSn();
        storeId = orderPO.getStoreId();
        storeName = orderPO.getStoreName();
        orderState = orderPO.getOrderState();
        orderStateValue = OrderListVO.getRealOrderStateValue(orderState);
        orderPattern = orderPO.getOrderPattern();
        paymentName = orderPO.getPaymentName();
        paymentCode = orderPO.getPaymentCode();
        memberName = orderPO.getMemberName();
        userMobile = orderPO.getUserMobile();
        receiverName = orderExtendPO.getReceiverName();
        receiverAreaInfo = orderExtendPO.getReceiverAreaInfo();
        receiverAddress = orderExtendPO.getReceiverAddress();
        receiverProvince = orderExtendPO.getReceiverProvinceCode();
        receiverCity = orderExtendPO.getReceiverCityCode();
        receiverDistrict = orderExtendPO.getReceiverDistrictCode();
        receiverTown = orderExtendPO.getReceiverTownCode();
        receiverMobile = dealMemberMobile(orderExtendPO.getReceiverMobile());
        pointName = orderExtendPO.getPointName();
        createTime = orderPO.getCreateTime();
        finishTime = orderPO.getFinishTime();
        deliverTime = orderPO.getDeliverTime();
        lockState = orderPO.getLockState();
        orderAmountTotal = orderPO.getOrderAmountTotal();
        marginOrderAmount = orderPO.getMarginOrderAmount();
        expressFeeTotal = orderPO.getExpressFeeTotal();
        goodsAmount = orderPO.getGoodsAmount();
        orderAmount = orderPO.getOrderAmount();
        payAmount = orderPO.getPayAmount();
        expressFee = orderPO.getExpressFee();
        balanceAmount = orderPO.getBalanceAmount();
        integralCashAmount = orderPO.getIntegralCashAmount();
        activityDiscountAmount = orderPO.getActivityDiscountAmount();
        commission = orderPO.getOrderCommission().add(orderPO.getBusinessCommission());
        refuseReason = orderPO.getRefuseReason();
        refuseRemark = orderPO.getRefuseRemark();
        channel = orderPO.getChannel();
        orderRemark = orderExtendPO.getOrderRemark();
        voucherPrice = orderExtendPO.getVoucherPrice();
        invoiceInfo = StringUtils.isEmpty(orderExtendPO.getInvoiceInfo()) ? null : JSONObject.parseObject(orderExtendPO.getInvoiceInfo(), MemberInvoice.class);
        invoiceStatus = orderExtendPO.getInvoiceStatus();
        invoiceStatusValue = dealRealInvoiceStatusValue(invoiceStatus);
        if (StringUtils.isEmpty(orderExtendPO.getPromotionInfo())) {
            promotionInfo = null;
        } else {
            promotionInfo = JSONObject.parseArray(orderExtendPO.getPromotionInfo(), OrderSubmitDTO.PromotionInfo.class);
        }
        xzCardAmount = orderPO.getXzCardAmount();
        xzCardExpressFeeAmount = orderPO.getXzCardExpressFeeAmount();
        composePayName = orderPO.getComposePayName();
        this.orderType = orderPO.getOrderType();
        this.paySn = orderPO.getPaySn();
        performanceModeList = JSONObject.parseArray(orderPO.getPerformanceModes(), Integer.class);
        performanceModeValueList = dealPerformanceModeValue(performanceModeList);
        this.customerConfirmStatus = orderPO.getCustomerConfirmStatus();
        this.customerConfirmStatusDesc = orderPO.getCustomerConfirmStatusDesc();
        this.customerName = orderExtendPO.getCustomerName();
    }

    public static List<String> dealPerformanceModeValue(List<Integer> performanceModes){
        List<String> performanceModeValueList =  new ArrayList<>();
        if(CollectionUtils.isEmpty(performanceModes)){
            return performanceModeValueList;
        }
        performanceModes.forEach(x -> performanceModeValueList.add(OrderPerformanceModeEnum.valueOf(x).getDesc()));

        return performanceModeValueList;
    }

    public static String dealRealInvoiceStatusValue(Integer invoiceStatus) {
        String value = null;
        if (StringUtils.isEmpty(invoiceStatus)) {
            return Language.translate("未知");
        }
        switch (invoiceStatus) {
            case OrderConst.INVOICE_STATE_0:
                value = "未开发票";
                break;
            case OrderConst.INVOICE_STATE_1:
                value = "已开发票";
                break;
            default:
                break;
        }
        //翻译
        value = Language.translate(value);
        return value;
    }

    /**
     * 处理会员手机号，隐藏中间四位
     *
     * @return
     */
    private static String dealMemberMobile(String memberMobile) {
        if (StringUtils.isEmpty(memberMobile)) {
            return memberMobile;
        }
        List<String> hiddenHosts = Arrays.asList("https://jbbcadmin.slodon.cn", "http://jbbcs-admin.slodon.cn");
        if (hiddenHosts.contains(DomainUrlConstant.SLD_ADMIN_URL)) {
            return memberMobile.replaceFirst("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
        }
        return memberMobile;
    }
}
