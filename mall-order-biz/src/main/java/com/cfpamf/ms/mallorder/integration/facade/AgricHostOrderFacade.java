package com.cfpamf.ms.mallorder.integration.facade;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.mallorder.vo.OrderLoanDetailVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "agric-host-order", url = "${agric-host-order.url}")
public interface AgricHostOrderFacade {

	/*
	提供给信贷接口-查询申请单信息
	 */
	@GetMapping("/feign/order/orderQuery")
	Result<OrderLoanDetailVO> loanOrderQuery(@RequestParam("paySn") String paySn);

}
