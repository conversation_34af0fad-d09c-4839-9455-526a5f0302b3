package com.cfpamf.ms.mallorder.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.mallpayment.facade.request.CDMallRefundTryRequest;
import com.cfpamf.mallpayment.facade.vo.CDMallRefundTryResultVO;
import com.cfpamf.ms.mallorder.dto.agricorder.LoanListResultQueryDTO;
import com.cfpamf.ms.mallorder.po.LoanResultPO;
import com.cfpamf.ms.mallorder.req.admin.LoanListResultRequest;
import com.cfpamf.ms.mallorder.vo.LoanResultVo;
import com.cfpamf.ms.mallorder.vo.agricorder.AgricLoanResultVo;
import com.cfpamf.ms.mallsystem.vo.Admin;
import com.slodon.bbc.core.response.PageVO;
import com.slodon.bbc.core.response.PagerInfo;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-17
 */
public interface ILoanResultService extends IService<LoanResultPO> {

    /**
     * 用呗重新代付
     *
     * @param paySn     单号
     * @param admin     处理人
     * @return          处理结果
     */
    Boolean loanRepay(Admin admin, String paySn);

    /**
     * 对指定单号发起重新代付
     *
     * @param payNo         订单号
     * @param operator      操作人
     * @return              操作结果
     */
    Result reLendingByOrderSn(String payNo, String operator);

    /**
     * 查询放款结果列表
     *
     * @param request   放款结果查询条件
     * @param pager     分页信息
     * @return          结果信息
     */
    PageVO<LoanResultVo> queryLoanResultPage(PagerInfo pager, LoanListResultRequest request);

    /**
     * 自动放款补偿
     * @return
     */
    boolean orderAutoLoanLendingMakeUpJob();



    /**
     * 售后还款试算
     */
    Result<CDMallRefundTryResultVO> getTryCaculateResult(CDMallRefundTryRequest cdmallSetlTryRequest);

    /**
     * 乡信-放款结果列表
     * @param loanRequest
     * @return
     */
    Page<AgricLoanResultVo> agricLoanResultPage(LoanListResultQueryDTO loanRequest);

    /**
     * 乡信-重新代付
     * @param paySn
     * @param jobNumber
     */
    void agricLoanRepay(String paySn, String jobNumber);


}
