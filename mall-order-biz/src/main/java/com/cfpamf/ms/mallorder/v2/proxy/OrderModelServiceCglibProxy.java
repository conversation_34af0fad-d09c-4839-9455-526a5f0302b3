//package com.cfpamf.ms.mallorder.v2.proxy;
//
//import java.lang.reflect.Method;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.Lazy;
//
//import com.cfpamf.ms.mallmember.po.Member;
//import com.cfpamf.ms.mallorder.dto.OrderSubmitDTO;
//import com.cfpamf.ms.mallorder.dto.OrderSubmitMqConsumerDTO;
//import com.cfpamf.ms.mallorder.model.OrderModel;
//import com.cfpamf.ms.mallorder.v2.config.CommonConfig;
//import com.cfpamf.ms.mallorder.v2.service.PlaceOrderService;
//
//import lombok.extern.slf4j.Slf4j;
//import net.sf.cglib.proxy.Enhancer;
//import net.sf.cglib.proxy.MethodInterceptor;
//import net.sf.cglib.proxy.MethodProxy;
//
//@Slf4j
//@Configuration
//public class OrderModelServiceCglibProxy implements MethodInterceptor {
//
//    @Lazy
//    @Autowired
//    private PlaceOrderService placeOrderService;
//
//    @Autowired
//    private CommonConfig commonConfig;
//
//    @Bean(name = "orderModel")
//    public OrderModel getInstance() {
//        Enhancer enhancer = new Enhancer();
//        // 设置要增强的类, cglib实际上是 继承委托类
//        enhancer.setSuperclass(OrderModel.class);
//        enhancer.setCallback(this);
//        return (OrderModel)enhancer.create();
//    }
//
//    @Override
//    public Object intercept(Object obj, Method method, Object[] args, MethodProxy proxy) throws Throwable {
//        // 当V2流程打开，OrderModel类中submitOrder，会执行代理类代码
//        if ("submitOrder".equalsIgnoreCase(method.getName()) && commonConfig.isOpenV2()) {
//            log.info("===OrderModel.submitOrder方法被代理，执行V2提交订单流程，开始===");
//            Object result = placeOrderService.submitOrder((OrderSubmitDTO)args[0], (Member)args[1],
//                (OrderSubmitMqConsumerDTO)args[2]);
//            log.info("===OrderModel.submitOrder方法被代理，执行V2提交订单流程，结束===");
//            return result;
//        }
//        log.info("===OrderModel.class代理开始===");
//        Object result = proxy.invokeSuper(obj, args);
//        log.info("===OrderModel.class代理结束===");
//        return result;
//    }
//
//}
