package com.cfpamf.ms.mallorder.common.mq;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.dto.OrderEventNotifyDTO;
import com.cfpamf.ms.mallorder.enums.OrderEventEnum;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.model.OrderProductModel;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.service.*;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class OrderChangeEventConsumer {

    @Resource
    private IOrderExchangeService exchangeService;

    @Resource
    private OrderOfflineService orderOfflineService;

    @Resource
    private OrderModel orderModel;

    @Resource
    private OrderProductModel orderProductModel;

    @Autowired
    private IOrderDatePushService orderDatePushService;

    @Resource
    private IPerformanceService iPerformanceService;

    @Resource
    private IOrderService orderService;

    /***
     * 消费订单状态变更MQ
     * */
    @RabbitListener(queues = RabbitMqConfig.EXCHANGE_ORDER_STATUS_CHANGE_QUEUE)
    public void orderChangeEventOnMessage(Message message, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag, Channel channel) throws Exception {
        Boolean isAck = Boolean.TRUE;
        try {
            String msg = new String(message.getBody());
            if (StringUtils.isBlank(msg)) {
                log.info("获取订单状态变更MQ通知内容为空");
                return;
            }
            log.info("=========获取订单状态变更消息,json:{}", msg);
            OrderEventNotifyDTO notifyDTO = JSONObject.parseObject(msg, OrderEventNotifyDTO.class);
            if (StringUtils.isBlank(notifyDTO.getOrderSn())) {
                log.error("获取订单状态变更消息缺失orderSn");
                return;
            }
            log.info("OrderChangeEventConsumer notifyDTO============{}", JSONObject.toJSONString(notifyDTO));

            OrderPO orderPO = orderModel.getOrderByOrderSn(notifyDTO.getOrderSn());
            BizAssertUtil.isTrue(orderPO == null, "orderChangeEventOnMessage，查询不到" + notifyDTO.getOrderSn() + "的订单信息");

            if (OrderEventEnum.CREATE.getCode().equals(notifyDTO.getEventType())) {
                // 线下补录订单--支付 || 赠品订单
                if (OrderTypeEnum.GROUP_BUYING_GIFT.getValue().equals(orderPO.getOrderType())) {
                    //支付
                    orderOfflineService.payOrder(orderPO);
                }
            } else if (OrderEventEnum.PART_DELIVERY.getCode().equals(notifyDTO.getEventType()) || OrderEventEnum.DELIVERY.getCode().equals(notifyDTO.getEventType())) {
                //发货成功 && 无物流发货 && 指定店铺的订单需要发送短信签收码
                iPerformanceService.sendSmsReceiveCodeForDelivery(notifyDTO.getOrderSn());
            } else if (OrderEventEnum.FINISH.getCode().equals(notifyDTO.getEventType())) {
                //处理换货订单完成后的逻辑
                exchangeService.dealExchangeOrderFinish(notifyDTO.getOrderSn());
            } else if (OrderEventEnum.PAY.getCode().equals(notifyDTO.getEventType())) {

                // 给线下订单生成签收码
                try {
                    iPerformanceService.createReceiveCodeForOffline(orderPO);
                } catch (Exception e) {
                    log.error("线下订单生成签收码失败, orderSn:{}", orderPO.getOrderSn(), e);
                }

                // 更新渠道手续费到商品行
                try {
                    orderProductModel.saveChannelServiceFee2OrderProduct(orderPO.getOrderSn(), orderPO.getChannelServiceFee());
                } catch (Exception e) {
                    log.error("分摊渠道手续费到商品行失败, orderSn:{}, channelServiceFee{}",
                            orderPO.getOrderSn(), orderPO.getChannelServiceFee(), e);
                }
                //店铺风控预警
                orderService.tradingRiskWareNing(orderPO);
            }
        } catch (Exception ex) {
            log.error("=========获取订单状态变更MQ消息出现未知异常", ex);
            isAck = false;
        } finally {
            //处理失败消息重返队列
            RabbitMQUtils.manualAcknowledgeMode(isAck, deliveryTag, channel, false);
        }
    }
}
