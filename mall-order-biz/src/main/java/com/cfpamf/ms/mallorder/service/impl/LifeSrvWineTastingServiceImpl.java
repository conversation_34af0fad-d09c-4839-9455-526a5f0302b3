package com.cfpamf.ms.mallorder.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.cdfinance.hrms.facade.vo.BranchRelationVO;
import com.cfpamf.ms.bms.facade.vo.OrganizationBaseVO;
import com.cfpamf.ms.mallorder.integration.facade.BmsIntegration;
import com.cfpamf.ms.mallorder.integration.hrms.HrmsIntegration;
import com.cfpamf.ms.mallorder.pgMapper.AdsLifesrvDepartmentProfitStaticDfpMapper;
import com.cfpamf.ms.mallorder.pgMapper.AdsLifesrvWineTastingAreaStatisticDfpMapper;
import com.cfpamf.ms.mallorder.pgMapper.AdsLifesrvWineTastingBchStatisticDfpMapper;
import com.cfpamf.ms.mallorder.po.pgrpt.AdsLifesrvWineTastingAreaStatisticDfpPO;
import com.cfpamf.ms.mallorder.po.pgrpt.AdsLifesrvWineTastingBchStatisticDfpPO;
import com.cfpamf.ms.mallorder.req.LifeSrvWineTastingBchDetailRequest;
import com.cfpamf.ms.mallorder.req.LifeSrvWineTastingReportRequest;
import com.cfpamf.ms.mallorder.service.LifeSrvWineTastingService;
import com.cfpamf.ms.mallorder.vo.pgrpt.*;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class LifeSrvWineTastingServiceImpl implements LifeSrvWineTastingService {

    @Autowired
    private AdsLifesrvDepartmentProfitStaticDfpMapper adsLifesrvDepartmentProfitStaticDfpMapper;

    @Autowired
    private BmsIntegration bmsIntegration;

    @Autowired
    private AdsLifesrvWineTastingAreaStatisticDfpMapper adsLifesrvWineTastingAreaStatisticDfpMapper;

    @Autowired
    private AdsLifesrvWineTastingBchStatisticDfpMapper adsLifesrvWineTastingBchStatisticDfpMapper;

    @Autowired
    private HrmsIntegration hrmsIntegration;

    @Override
    public LifesrvWineTastingBaseVO basicMetrics(LifeSrvWineTastingReportRequest request) {
        log.info("查询品酒会基础指标 request{}" ,JSONObject.toJSONString(request));
        LifesrvWineTastingBaseVO baseVO = new LifesrvWineTastingBaseVO();

        AdsLifesrvWineTastingAreaStatisticDfpPO basicPO = adsLifesrvWineTastingAreaStatisticDfpMapper.getBasicMetrics(request);

        List<AdsLifesrvWineTastingAreaStatisticDfpPO> nationalTop10RankPOList = adsLifesrvWineTastingAreaStatisticDfpMapper.getTOP10RankList(request,"nation");
        if (!StringUtils.isEmpty(request.getAreaOrgCode())) {
            BranchRelationVO branchRelationVO = hrmsIntegration.getBranchRelation(request.getAreaOrgCode());
            String areaCode = "";
            if (!Objects.isNull(branchRelationVO)) {
                areaCode = branchRelationVO.getRegionCode();
            }
            request.setAreaCode(StringUtils.isEmpty(request.getAreaCode())?areaCode:request.getAreaCode());
        }
        List<AdsLifesrvWineTastingAreaStatisticDfpPO> areaTop10RankPOList = adsLifesrvWineTastingAreaStatisticDfpMapper.getTOP10RankList(request,"area");

        LifesrvWineTastingBasicVO basicVO = convertBasicVO(basicPO);
        List<LifesrvWineTastingBasicVO> nationalTop10RankList = nationalTop10RankPOList.stream().map(this::convertBasicVO).collect(Collectors.toList());
        List<LifesrvWineTastingBasicVO> areaTop10RankList = areaTop10RankPOList.stream().map(this::convertBasicVO).collect(Collectors.toList());
        baseVO.setBasicVO(basicVO);
        baseVO.setNationalTop10RankList(nationalTop10RankList);
        baseVO.setAreaTop10RankList(areaTop10RankList);
        return baseVO;
    }

    @Override
    public PageInfo<LifesrvWineTastingBchDetailBasicVO> bchDetailBasicMetrics(LifeSrvWineTastingBchDetailRequest request) {

        /*
          筛选机构编码不为空时，特殊处理区域管理部编码
         */
        if(!StringUtils.isEmpty(request.getBchCode())){
            OrganizationBaseVO org = bmsIntegration.getBizOrgBaseVOByOrgCode(request.getBchCode());
            if (!Objects.isNull(org)) {
                request.setBchCode(org.getOrgCode());
            }
        }

        List<AdsLifesrvWineTastingBchStatisticDfpPO> poList = adsLifesrvWineTastingBchStatisticDfpMapper.getPageInfo(request);
        Integer count = adsLifesrvWineTastingBchStatisticDfpMapper.getCount(request);
        if (Objects.isNull(count) || count == 0) {
            return new PageInfo<>();
        }
        List<LifesrvWineTastingBchDetailBasicVO> bchDetailBasicVOList = poList.stream().map(x->{
            LifesrvWineTastingBchDetailBasicVO bchDetailBasicVO = new LifesrvWineTastingBchDetailBasicVO();
            BeanUtils.copyProperties(x, bchDetailBasicVO);
            bchDetailBasicVO.setCdWineActivityRevenueAmt(formatBigAmount(x.getCdWineActivityRevenueAmt(),2));
            bchDetailBasicVO.setAcmWineTastingInvitedUserCnt(x.getInviteeCustomerCnt());
            return bchDetailBasicVO;
        }).collect(Collectors.toList());
        PageInfo<LifesrvWineTastingBchDetailBasicVO> pageInfo = new PageInfo<>(bchDetailBasicVOList);
        pageInfo.setPageNum(request.getPageIndex());
        pageInfo.setSize(request.getPageSize());
        pageInfo.setTotal(count);
        pageInfo.setHasNextPage(request.getPageIndex() * request.getPageSize() < count);
        return pageInfo;
    }

    private LifesrvWineTastingBasicVO convertBasicVO(AdsLifesrvWineTastingAreaStatisticDfpPO basicPO) {
        LifesrvWineTastingBasicVO basicVO = new LifesrvWineTastingBasicVO();
        if (Objects.isNull(basicPO)) {
            return basicVO;
        }
        BeanUtils.copyProperties(basicPO, basicVO);
        basicVO.setSmWineTastingRevenueAmt(formatBigAmount(basicPO.getSmWineTastingRevenueAmt(),2));
        basicVO.setSyWineTastingRevenueAmt(formatBigAmount(basicPO.getSyWineTastingRevenueAmt(),2));
        basicVO.setSyWineTastingAvgRevenueAmt(formatBigAmount(basicPO.getSyWineTastingAvgRevenueAmt(),2));
        basicVO.setSmWineTastingAvgRevenueAmt(formatBigAmount(basicPO.getSmWineTastingAvgRevenueAmt(),2));
        return basicVO;
    }

    private String formatBigAmount(BigDecimal amount, int newScale) {
        BigDecimal formatAmount = Objects.isNull(amount)
                ?BigDecimal.ZERO.setScale(newScale, RoundingMode.HALF_UP)
                :amount.setScale(newScale, RoundingMode.HALF_UP);

        //大于10000时，转成万元
        String unit = "";
        if (formatAmount.compareTo(BigDecimal.valueOf(10000))>=0) {
            formatAmount = formatAmount.divide(BigDecimal.valueOf(10000), newScale, RoundingMode.HALF_UP);
            unit = "万";
        }
        // 格式化 BigDecimal
        return formatAmount + unit;
    }
}
