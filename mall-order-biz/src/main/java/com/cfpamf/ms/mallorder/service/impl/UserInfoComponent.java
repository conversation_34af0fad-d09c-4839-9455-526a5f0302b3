package com.cfpamf.ms.mallorder.service.impl;

import com.cfpamf.ms.bizconfig.facade.vo.LoanOrgVo;
import com.cfpamf.ms.bizconfig.facade.vo.UserVo;
import com.cfpamf.ms.customer.facade.vo.CustInfoVo;
import com.cfpamf.ms.customer.facade.vo.user.UserBaseInfoVo;
import com.cfpamf.ms.mallorder.dto.UserBaseInfo;
import com.cfpamf.ms.mallorder.integration.cust.CustomerIntegration;
import com.cfpamf.ms.mallorder.po.OrderExtendPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <p>
 * 用户属性获取
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-22
 */
@Slf4j
@Component
public class UserInfoComponent {
    @Resource
    private CustomerIntegration customerIntegration;

    /**
     * 根据用户ID获取用户信息
     *
     * @param userNo
     * @return
     * @throws Exception
     */
    public UserBaseInfo userBaseInfoByUserNo(String userNo) {

        UserBaseInfoVo userBaseInfo = customerIntegration.userBaseInfo(userNo);

        UserBaseInfo result = new UserBaseInfo();
        result.setUserNo(userNo);
        result.setCustPhone(userBaseInfo.getMobile());

        /**
         * 普通用户
         */
        CustInfoVo custInfoVo = userBaseInfo.getCustInfoVo();
        if (custInfoVo == null) {
            return result;
        }

        String customerId = custInfoVo.getCustDetail().getLoanCustId();
        String custName = custInfoVo.getCustName();
        String branchCode = custInfoVo.getCustDetail().getLoanBranch();
        String managerCode = custInfoVo.getCustDetail().getLoanManager();
        String idNo = custInfoVo.getIdNo();

        result.setCustomerId(customerId);
        result.setCustomerName(custName);
        result.setBranchCode(branchCode);
        result.setManager(managerCode);
        result.setIdNo(idNo);
        // 设置联系电话
        result.setContactPhone(custInfoVo.getMobile());

        return userBaseInfoByCustId(result);
    }

    /**
     * 判断是否为分支
     * @param branchCode
     * @return
     */
    private Boolean isBranch(String branchCode){
        return customerIntegration.isBranchLevel(branchCode);
    }


    public UserBaseInfo userBaseInfoByCustId(UserBaseInfo userBaseInfo) {

        // 无管护 实名用户
        if (!this.isBranch(userBaseInfo.getBranchCode())) {
            return userBaseInfo;
        }
        return wrapLoanUserInfo(userBaseInfo);
    }

    /**
     * 封装用户信贷信息: 督导、小贷公司、客户经理姓名、分支名称
     *
     * @param userBaseInfo
     * @return
     */
    private UserBaseInfo wrapLoanUserInfo(UserBaseInfo userBaseInfo) {

        String branchCode = userBaseInfo.getBranchCode();
        // 2、客户经理信息
        UserVo managerInfo = customerIntegration.getUserInfoByUserCode(
                userBaseInfo.getManager(), branchCode);
        userBaseInfo.setManagePhone(managerInfo.getUserTel());

        // 3、督导信息
        String supervisor = null;
        String supervisorName = null;
        // 督导信息不存在时 客户经理即为督导
        if (managerInfo.getUserConfigVo() == null
                || StringUtils.isBlank(managerInfo.getUserConfigVo().getSupervisor())) {
            supervisor = managerInfo.getUserCode();
            supervisorName = managerInfo.getUserName();
        } else {
            UserVo supervisorVo = customerIntegration.getUserInfoByUserCode(
                    managerInfo.getUserConfigVo().getSupervisor(), branchCode);
            supervisor = managerInfo.getUserConfigVo().getSupervisor();
            supervisorName = supervisorVo.getUserName();
        }
        userBaseInfo.setSupervisor(supervisor);
        userBaseInfo.setSupervisorName(supervisorName);

        userBaseInfo.setManagerName(managerInfo.getUserName());
        userBaseInfo.setBranchName(managerInfo.getBranchName());

        // 4、小贷公司信息
        LoanOrgVo loanOrgVo = customerIntegration.getLoanCompany(branchCode);

        userBaseInfo.setCompanyCode(loanOrgVo.getCompanyCode());
        userBaseInfo.setCompanyName(loanOrgVo.getCompanyName());
        return userBaseInfo;
    }
    
    
    
    /**
     * 封装用户信贷信息: 督导、客户经理姓名、分支名称
     *
     * @param orderExtendPO
     * @return
     */
    public void orderExtendPOReplenish(OrderExtendPO orderExtendPO, String userNo) {

        try {
            UserBaseInfoVo userBaseInfo = customerIntegration.userBaseInfo(userNo);
            if (Objects.nonNull(userBaseInfo) && Objects.nonNull(userBaseInfo.getCustInfoVo())
                    && Objects.nonNull(userBaseInfo.getCustInfoVo().getCustDetail())) {
                String customerId = userBaseInfo.getCustInfoVo().getCustDetail().getLoanCustId();
                String custName = userBaseInfo.getCustInfoVo().getCustName();
                orderExtendPO.setCustomerId(customerId);
                orderExtendPO.setCustomerName(custName);
            }
        } catch (Exception e) {
            log.error("查询客户中的客户Id处理异常，UserFacade.baseInfoByUserNo userNo:{}", userNo, e);
        }

        // 1、客户经理信息
        UserVo managerInfo = customerIntegration.getUserInfoByUserCode(orderExtendPO.getManager(), orderExtendPO.getBranch());
        // 2、督导信息
        String supervisor = null;
        String supervisorName = null;
        // 督导信息不存在时 客户经理即为督导
        if (managerInfo.getUserConfigVo() == null
                || StringUtils.isBlank(managerInfo.getUserConfigVo().getSupervisor())) {
            supervisor = managerInfo.getUserCode();
            supervisorName = managerInfo.getUserName();
        } else {
            UserVo supervisorVo = customerIntegration.getUserInfoByUserCode(
                    managerInfo.getUserConfigVo().getSupervisor(), orderExtendPO.getBranch());
            supervisor = managerInfo.getUserConfigVo().getSupervisor();
            supervisorName = supervisorVo.getUserName();
        }
        orderExtendPO.setSupervisor(supervisor);
        orderExtendPO.setSupervisorName(supervisorName);
    }
}
