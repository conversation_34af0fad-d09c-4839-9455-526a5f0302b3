package com.cfpamf.ms.mallorder.integration.pay;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cfpamf.cmis.common.utils.DateUtils;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.mallpayment.facade.api.MallPaymentFacade;
import com.cfpamf.mallpayment.facade.enums.PayWayEnum;
import com.cfpamf.mallpayment.facade.request.PaymentCancelRequest;
import com.cfpamf.mallpayment.facade.request.PaymentCreateRequest;
import com.cfpamf.mallpayment.facade.request.PaymentRefundRequest;
import com.cfpamf.mallpayment.facade.request.loan.LoanProductInfo;
import com.cfpamf.mallpayment.facade.request.loan.OrderDetailInfo;
import com.cfpamf.mallpayment.facade.request.loan.SaveCdMallApplyRequest;
import com.cfpamf.mallpayment.facade.request.wxalipay.AlipayRefundRequest;
import com.cfpamf.mallpayment.facade.request.wxalipay.WxAliPayPayRequest;
import com.cfpamf.mallpayment.facade.request.wxalipay.WxPayV3CombineRequest;
import com.cfpamf.mallpayment.facade.vo.*;
import com.cfpamf.ms.bms.facade.vo.DictionaryItemVO;
import com.cfpamf.ms.customer.facade.vo.CustCreditLimitVo;
import com.cfpamf.ms.mall.account.constant.AccountConstans;
import com.cfpamf.ms.mall.account.enums.AccountCardTypeEnum;
import com.cfpamf.ms.mall.account.vo.AccountCard;
import com.cfpamf.ms.mallgoods.facade.vo.Product;
import com.cfpamf.ms.mallgoods.facade.vo.ProductFinanceRuleLabel;
import com.cfpamf.ms.mallgoods.facade.vo.ProductPriceVO;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.enums.*;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.common.util.ExternalApiUtil;
import com.cfpamf.ms.mallorder.common.util.OrderBizUtils;
import com.cfpamf.ms.mallorder.common.util.OrderLocalUtils;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.controller.fegin.facade.AccountCardFacade;
import com.cfpamf.ms.mallorder.controller.fegin.facade.CustomerServiceFeign;
import com.cfpamf.ms.mallorder.enums.OrderEventEnum;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.integration.cashier.request.InterestType;
import com.cfpamf.ms.mallorder.integration.cust.CustomerIntegration;
import com.cfpamf.ms.mallorder.integration.facade.BmsIntegration;
import com.cfpamf.ms.mallorder.integration.settlement.BillOperatinIntegration;
import com.cfpamf.ms.mallorder.mapper.OrderMapper;
import com.cfpamf.ms.mallorder.mapper.OrderPayMapper;
import com.cfpamf.ms.mallorder.mapper.PayMethodMapper;
import com.cfpamf.ms.mallorder.model.*;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.req.EnjoyPayOrderRequest;
import com.cfpamf.ms.mallorder.req.PayOrderRequest;
import com.cfpamf.ms.mallorder.request.OrderExample;
import com.cfpamf.ms.mallorder.request.OrderPayExample;
import com.cfpamf.ms.mallorder.request.OrderProductExample;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.service.impl.OrderCreateHelper;
import com.cfpamf.ms.mallorder.v2.common.enums.PayOrderOnType;
import com.cfpamf.ms.mallorder.v2.domain.bo.PayInfoBO;
import com.cfpamf.ms.mallorder.v2.domain.dto.PayOrderOnConditionDTO;
import com.cfpamf.ms.mallorder.v2.service.OrderPresellService;
import com.cfpamf.ms.mallorder.v2.service.PayService;
import com.cfpamf.ms.mallorder.validation.PayValidation;
import com.cfpamf.ms.mallorder.vo.LoanProductVO;
import com.cfpamf.ms.mallorder.vo.OrderPayInfoVO;
import com.cfpamf.ms.mallorder.vo.PayRequestVO;
import com.cfpamf.ms.mallpromotion.constant.PromotionConst;
import com.cfpamf.ms.mallpromotion.enums.CouponFunder;
import com.cfpamf.ms.mallshop.api.StoreFeignClient;
import com.cfpamf.ms.mallshop.resp.StoreContractReceiptInfoVO;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.exception.MallException;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import com.slodon.bbc.core.util.AssertUtil;
import com.slodon.bbc.core.util.StringUtil;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/1 17:50
 */
@Component
@Slf4j
public class PayIntegration {

    /**
     * 支付回调：1-成功
     */
    public static final int PAY_CALLBACK_SUCCESS = 1;
    /**
     * 支付回调：3-失败
     */
    public static final int PAY_CALLBACK_FAIL = 3;

    public static final String SYSTEM_CODE = "mallOrder";

    @Value("${mall-payment.notify}")
    private String notifyUrl;

    @Value("${mall-payment.refundNotify}")
    private String refundNotifyUrl;

    @Value("${mall-payment.loanNotify}")
    private String loanNotifyUrl;

    @Value("${platform-collection.account}")
    private String platformCollectionAccount;

    @Value("${platform-collection.name}")
    private String platformCollectionAccountName;

    @Autowired
    private MallPaymentFacade mallPaymentFacade;
    @Resource
    private AccountCardFacade accountCardFacade;

    @Autowired
    private OrderModel orderModel;
    @Autowired
    private OrderPayModel orderPayModel;
    @Autowired
    private OrderProductModel orderProductModel;
    @Resource
    private OrderPayMapper orderPayMapper;
    @Autowired
    private StoreFeignClient storeFeignClient;
    @Resource
    private OrderMapper orderMapper;

    @Autowired
    private CustomerServiceFeign customerServiceFeign;

    @Autowired
    private OrderLogModel orderLogModel;
    @Resource
    private PayMethodMapper payMethodMapper;
    @Autowired
    private IOrderPlacingService orderPlacingService;
    @Autowired
    private CustomerIntegration customerIntegration;

    @Autowired
    private OrderLocalUtils orderLocalUtils;
    @Resource
    private IOrderReturnService orderReturnService;
    @Autowired
    private IOrderProductService orderProductService;
    @Autowired
    private IOrderProductExtendService orderProductExtendService;
    @Autowired
    private IOrderExtendService orderExtendService;
    @Autowired
    private BillOperatinIntegration billOperatinIntegration;
    @Autowired
    private PayService payService;
    @Autowired
    private OrderCreateHelper orderCreateHelper;
    
    @Autowired
    private OrderPresellService orderPresellService;
    @Autowired
    private IOrderPayService orderPayService;

    @Autowired
    private BmsIntegration bmsIntegration;

    @Resource
    private OrderAfterServiceModel orderAfterServiceModel;

    @Value("${presell.paymethods.deposit}")
    private String depositPayMethods;
    @Value("${presell.paymethods.remain}")
    private String remainPayMethods;

    @Transactional(rollbackFor = Exception.class, transactionManager = "masterdbTx")
    public Boolean profitSharding(String orderSn) {
        return Boolean.TRUE;
    }

    /**
     * 获取支付验签
     *
     * @param payRequest
     * @param ip
     * @return com.cfpamf.ms.mallorder.vo.PayRequestVO
     * <AUTHOR>
     * @date 2021/6/15 14:43
     */
    public PayRequestVO getToken(PayOrderRequest payRequest, String ip) {

        AssertUtil.isTrue(!PayMethodEnum.isThirdPartyPay(payRequest.getPayMethod()), "支付方式有误");

        OrderPayPO orderPayPO = orderPayModel.getOrderPayByPaySn(payRequest.getPaySn());

        OrderExample example = new OrderExample();
        example.setPaySn(payRequest.getPaySn());
        List<OrderPO> orderPOList = orderModel.getOrderList(example, null);

        OrderPO orderPO = orderPOList.get(0);
        // 是否存在乡助卡支付
        boolean isCard = orderPO.getXzCardAmount().compareTo(BigDecimal.ZERO) > 0;

        // 封装基础参数
        PaymentCreateRequest paymentRequest = this.buildWxPayRequest(payRequest, orderPayPO, orderPO, ip);

        // 微信支付 封装分账信息
        if (payRequest.getPayMethod() == PayMethodEnum.WXPAY) {

            paymentRequest.setCombineFlag(1);
            paymentRequest.setProfitSharing(1);
            List<WxPayV3CombineRequest> v3CombineRequests = buildCombineRequest(paymentRequest, payRequest.getOpenId(),
                paymentRequest.getWxAliPayReq().getProductName(), orderPOList, isCard);
            paymentRequest.setWxPayV3CombineRequest(v3CombineRequests);
        }

        Result<PaymentCreateVO> paymentCreateVO;
        try {
            paymentCreateVO = mallPaymentFacade.paymentCreate(paymentRequest);
        } catch (Exception ex) {
            log.error("获取支付验签异常:{}，支付单号:{}", ex.getMessage(), payRequest.getPaySn());
            throw new BusinessException("商户收单验签异常，请联系管理人员！");
        }

        if (paymentCreateVO == null) {
            log.error("获取支付验签返回为空,支付单号:{}", payRequest.getPaySn());
            throw new BusinessException("商户收单验签异常，请联系管理人员！");
        }

        if (!paymentCreateVO.isSuccess() || paymentCreateVO.getData() == null) {
            log.error("获取支付验签异常:{}，支付单号:{}", paymentCreateVO.getMessage(), payRequest.getPaySn());
            throw new BusinessException("商户收单验签异常，请联系管理人员！");
        }

        String token = paymentCreateVO.getData().getPayToken();

        PayRequestVO vo = new PayRequestVO();
        vo.setBody(token);
        return vo;
    }

    @GlobalTransactional
    public JsonResult<String> enjoyPay(EnjoyPayOrderRequest payRequest, OrderPayPO orderPayPO, List<OrderPO> orderPOList) {
        if (payRequest.getEnjoyPayVipFlag() == null) {
            payRequest.setEnjoyPayVipFlag(0);
        }

        // 修改orderPay支付信息
        OrderPayPO updateOrderPay = new OrderPayPO();
        // 组合支付，订单支付方式不允许修改。预售Andy修改
        if (!PayMethodEnum.isCombinationPay(orderPayPO.getPaymentCode())) {
            updateOrderPay.setPaymentCode(payRequest.getPayMethod().getValue());
            updateOrderPay.setPaymentName(payRequest.getPayMethod().getDesc());
        }
        updateOrderPay.setEnjoyPayVipFlag(payRequest.getEnjoyPayVipFlag());
        // 用呗支付信息
        OrderPayInfoVO.EnjoyPayExtraInfo enjoyPayExtraInfo =
                new OrderPayInfoVO.EnjoyPayExtraInfo(payRequest.getDueDay(), payRequest.getRepaymentMode(),
                        payRequest.getRepaymentMode(), payRequest.getLoanPeriod(), payRequest.getEnjoyPayVipFlag());
        JSONObject enjoyPayInfo = orderPayModel.enjoyPayInfo(enjoyPayExtraInfo);
        updateOrderPay.setPayWayExtraInfo(enjoyPayInfo);

        LambdaUpdateWrapper<OrderPayPO> payUpdateWrapper = new LambdaUpdateWrapper<>();
        payUpdateWrapper.eq(OrderPayPO::getPayId, orderPayPO.getPayId());
        AssertUtil.isTrue(!orderPayService.update(updateOrderPay, payUpdateWrapper),
                String.format("更新支付订单失败,paySn:%s", orderPayPO.getPaySn()));
        List<DictionaryItemVO> dictionary = bmsIntegration.listDictionaryItemsByTypeCode(CommonConst.AUTO_RECEIVE_DAYS, CommonConst.MALL_SYSTEM_MANAGE_ID);
        Integer autoReceiveDays = null;
        if (!org.springframework.util.CollectionUtils.isEmpty(dictionary)){
            DictionaryItemVO dictionaryItemVO = dictionary.get(0);
            autoReceiveDays = Integer.valueOf(dictionaryItemVO.getItemCode());
        }

        for (OrderPO orderPO : orderPOList) {
            OrderPO orderPoUpdate = new OrderPO();
            orderPoUpdate.setOrderId(orderPO.getOrderId());

            Boolean newOrder =orderPO.getNewOrder();//读取订单支付分账方式

            // 不为待支付 不需要支付
            if (OrderStatusEnum.WAIT_PAY != OrderStatusEnum.valueOf(orderPO.getOrderState())) {
                continue;
            }

            // 0元订单 不需要支付
            if (OrderBizUtils.isZeroOrder(orderPO)) {
                orderPoUpdate.setOrderState(OrderStatusEnum.DEAL_PAY.getValue());
                orderPlacingService.updateById(orderPoUpdate);
                continue;
            }

            // 组合支付，订单支付方式不允许修改。预售Andy修改
            if (!PayMethodEnum.isCombinationPay(orderPayPO.getPaymentCode())) {
                // 将order 的paymentCOde置为ENJOY_PAY
                orderPoUpdate.setPaymentCode(payRequest.getPayMethod().getValue());
                orderPoUpdate.setPaymentName(payRequest.getPayMethod().getDesc());
            }
            // 判断是否开通银联账户，开通且不为配销订单时重新调整为新订单
            AccountCard bankAccount = billOperatinIntegration.detailByBankAccount(orderPO.getStoreId().toString(),
                    AccountCardTypeEnum.UNI_JS_STORE_HEAD);
            if (Objects.nonNull(bankAccount) && orderPO.getOrderType() != OrderTypeEnum.ORDER_TYPE_7.getValue()) {
                orderPoUpdate.setNewOrder(true);
                orderPO.setNewOrder(true);
            }

            if(OrderTypeEnum.isPresell(orderPO.getOrderType())) {//预付订单校验支付规则
                log.info("paySn：{}  orderType：{} newOrder1:{} newOrder2:{} 预付订单用呗支付，校验支付规则重置newOrder2=newOrder1",orderPO.getPaySn(),orderPO.getOrderType(),newOrder, orderPO.getNewOrder());
                orderPO.setNewOrder(newOrder);
                orderPoUpdate.setNewOrder(newOrder);
                PayValidation.validPresellRoutingRule(orderPresellService, orderPO.getOrderType(),orderPO.getPaySn(),newOrder, orderPO.getNewOrder());
            }
            orderPlacingService.updateById(orderPoUpdate);

            //Andy 预付，支付限制
            PayValidation.validPresellPayMethodIsSupport(orderPresellService, orderPO.getOrderType(), orderPO.getPaySn(), orderPO.getOrderSn(),
                    orderPO.getNewOrder(), payRequest.getPayMethod().getValue(), depositPayMethods, remainPayMethods);
            
            // 查询金融规则信息,用于创建申请单及保存
            String orderSn = orderPO.getOrderSn();
            OrderProductPO one = orderProductService.getOne(
                Wrappers.lambdaQuery(OrderProductPO.class).eq(OrderProductPO::getOrderSn, orderSn).last("limit 1"));
            ProductPriceVO finance = orderLocalUtils.getProductPrice(one.getProductId(), orderPO.getAreaCode(),
                orderPO.getFinanceRuleCode());
            log.info("贷款支付，查询金融规则信息：{}", finance);

            // 创建申请单
            PaymentCreateRequest paymentRequest = buildEnjoyPayRequest(payRequest, orderPO, finance);
            Result<PaymentCreateVO> paymentCreateVO = null;
            try {
                log.info("enjoyPay()调用payment服务请求参数，{}", paymentRequest);
                paymentCreateVO = mallPaymentFacade.paymentCreate(paymentRequest);
            } catch (Exception ex) {
                // pay服务异常，order服务抛出异常，不做其他业务处理
                throw new MallException("payment-service：用呗创建支付申请异常", ErrorCodeEnum.C.CALL_EXCEPTION.getCode());
            }
            if (paymentCreateVO == null || !paymentCreateVO.isSuccess() || paymentCreateVO.getData() == null) {
                // pay服务异常，order服务抛出异常，不做其他业务处理
                String errorMessage = Objects.nonNull(paymentCreateVO) ? paymentCreateVO.getErrorMsg() : "";
                log.warn("用呗创建收单异常:{},支付单号:{}", errorMessage, payRequest.getPaySn());
                throw new BusinessException("用呗创建收单异常，" + errorMessage);
            }
            log.info("用呗参数， paySn:{} ", payRequest.getPaySn());
            // 保存金融规则信息
            if (!StringUtils.isEmpty(orderPO.getFinanceRuleCode()) && Objects.nonNull(finance)) {
                orderModel.saveOrderExtendFinance(finance, orderPO,autoReceiveDays);
            }
            log.info("用呗参数，开始更新订单状态为付款中 paySn:{} ", payRequest.getPaySn());
            // 更新订单状态为付款中
            OrderPO orderPOUpdate = new OrderPO();
            orderPOUpdate.setOrderState(OrderConst.ORDER_STATE_15);
            orderPOUpdate.setLoanPayState(LoanStatusEnum.DEAL_APPLY.getValue());
            OrderExample orderExample = new OrderExample();
            orderExample.setOrderSn(orderPO.getOrderSn());
            int total=orderMapper.updateByExampleSelective(orderPOUpdate, orderExample);
            log.info("用呗参数，更新订单状态为付款中 paySn:{} 更新数量total:{}", payRequest.getPaySn(),total);

            //发送支付中的消息
            orderCreateHelper.addOrderChangeEvent(orderPO, OrderEventEnum.PAYING, orderPO.getCreateTime());

            // 2.记录订单日志order_log,创建支付申请单成功
            orderLogModel.insertOrderLog(1, 0L, "system", orderPO.getOrderSn(),
                    orderPO.getOrderState(),
                OrderConst.ORDER_STATE_15, LoanStatusEnum.DEAL_APPLY.getValue(),
                    "创建支付申请单成功", payRequest.getChannel());
        }
        return SldResponse.success("支付成功");

    }

    PaymentCreateRequest buildWxPayRequest(PayOrderRequest payRequest, OrderPayPO payInfo, OrderPO orderPO, String ip) {
        PayOrderOnConditionDTO payOrderOnConditionDTO = PayOrderOnConditionDTO.builder().notifyUrl(notifyUrl)
            .payOrderOnType(PayOrderOnType.PAY_SN).paySn(payInfo.getPaySn()).paymentCode(orderPO.getPaymentCode())
            .payAmount(payInfo.getPayAmount()).build();
        PayInfoBO pay = payService.buildPayInfoParam(payOrderOnConditionDTO); // Andy.2022.05.18
        log.info("构建支付参数，PayInfoBO：{}", pay);
        PaymentCreateRequest paymentRequest = new PaymentCreateRequest();
        paymentRequest.setNewOrder(orderPO.getNewOrder());
        paymentRequest.setOrderOn(pay.getOrderOn());// 修改：Andy.2022-05-18
        //预付订单，新增预付单号和支付顺序传输，兼容收银台新老订单
        paymentRequest.setPhaseOrderNo(payInfo.getPaySn());
        paymentRequest.setSequence(pay.getSequence());
        paymentRequest.setPayWay(payRequest.getPayMethod().getPayWay());
        paymentRequest.setPayer(payRequest.getPayer());
        paymentRequest.setPayerName(payRequest.getPayerName());
        paymentRequest.setMerchantId(orderPO.getStoreId() + "");
        paymentRequest.setMerchantName(orderPO.getStoreName());
        if (payRequest.getPayMethod() == PayMethodEnum.WXPAY) {
            paymentRequest.setSystemCode(SYSTEM_CODE);
        } else {
            paymentRequest.setSystemCode(SYSTEM_CODE);
        }

        // 付款来源渠道,CAPP、BAPP、H5、小程序、PC
        paymentRequest.setChannelSource(payRequest.getChannel().getValue());
        paymentRequest.setOrderAmt(pay.getPayAmount());
        paymentRequest.setPayAmt(pay.getPayAmount());// Andy.支付金额
        paymentRequest.setOrderCreateTime(System.currentTimeMillis());
        paymentRequest.setNotifyUrl(pay.getNotifyUrl());// 修改：Andy.2022-05-18
        paymentRequest.setCallbackUrl(payRequest.getReturnPage());
        // req.setComposePayOrder();
        OrderProductExample orderProductExample = new OrderProductExample();
        orderProductExample.setOrderSn(orderPO.getOrderSn());
        OrderProductPO orderProductPO = orderProductModel.getOrderProductList(orderProductExample, null).get(0);

        WxAliPayPayRequest wxAliPayReq = new WxAliPayPayRequest();
        wxAliPayReq.setProductId(orderProductPO.getGoodsId() + "");
        wxAliPayReq.setProductName(orderProductPO.getGoodsName());
        wxAliPayReq.setProductType("中和电商购物:" + orderProductPO.getGoodsCategoryId() + "");
        wxAliPayReq.setPayMethod(payRequest.getPaySource());
        wxAliPayReq.setOpenId(payRequest.getOpenId());
        wxAliPayReq.setOrderIp(ip);
        wxAliPayReq.setType(payRequest.getType());
        wxAliPayReq.setOrderPeriod(24 * 60); // 验签有效时间 单位：分钟
        StoreContractReceiptInfoVO storeContract =
            storeFeignClient.getStoreContractReciptInfo(orderProductPO.getStoreId());
        String ali;
        String wx;
        // 订单类型为配销订单，则取连锁总店付款账户
        if (OrderConst.ORDER_TYPE_7 == orderPO.getOrderType()) {
            ali = storeContract.getRecommentAliSellerId();
            wx = storeContract.getRecommentWxSellerId();
        } else {
            ali = storeContract.getAliSellerId();
            wx = storeContract.getWxSellerId();
        }
        if (payRequest.getPayMethod() == PayMethodEnum.ALIPAY) {
            wxAliPayReq.setSellerId(ali);
        } else {
            wxAliPayReq.setSellerId(wx);
        }

        paymentRequest.setWxAliPayReq(wxAliPayReq);
        return paymentRequest;
    }

    /**
     * 封装合单支付参数
     *
     * @param openId
     * @param desc
     * @param orderPOList
     * @param isCard
     * @return void
     * <AUTHOR>
     * @date 2021/7/8 11:25
     */
    private List<WxPayV3CombineRequest> buildCombineRequest(PaymentCreateRequest paymentRequest, String openId,
        String desc, List<OrderPO> orderPOList, boolean isCard) {
        List<WxPayV3CombineRequest> wxPayV3CombineRequest = new ArrayList<>(orderPOList.size());
        for (OrderPO orderPO : orderPOList) {

            if (orderPO.getOrderAmount().compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            String sellerName;
            String wx;
            StoreContractReceiptInfoVO storeContract =
                storeFeignClient.getStoreContractReciptInfo(orderPO.getStoreId());
            // 订单类型为配销订单，则取连锁总店付款账户
            if (OrderConst.ORDER_TYPE_7 == orderPO.getOrderType()) {
                wx = storeContract.getRecommentWxSellerId();
                sellerName = storeContract.getRecommentBusinessName();
            } else {
                wx = storeContract.getWxSellerId();
                sellerName = storeContract.getStore().getStoreName();
            }
            WxPayV3CombineRequest v3CombineRequest = new WxPayV3CombineRequest();
            v3CombineRequest.setAmount(orderPO.getOrderAmount());
            v3CombineRequest.setCombineOrderNo(orderPO.getOrderSn());
            if (PayMethodEnum.isCombinationPay(orderPO.getPaymentCode())) {
                BizAssertUtil.isTrue(orderPOList.size() != 1, "抱歉，订单组合支付，暂时不支持拆单支付！");
                v3CombineRequest.setCombineOrderNo(paymentRequest.getOrderOn());// Andy.2022.05.28 组合支付，当前预付组合只有一笔
                v3CombineRequest.setAmount(paymentRequest.getPayAmt());// Andy.2022.05.28 组合支付，当前预付组合只有一笔
            }
            v3CombineRequest.setSellerId(wx);
            v3CombineRequest.setSellerName(sellerName);
            v3CombineRequest.setDescription("【乡助】订单编号" + orderPO.getOrderSn());
            v3CombineRequest.setProfitSharing(1);

            if (!isCard) {
                // BigDecimal commission = orderPO.getBusinessCommission().add(orderPO.getOrderCommission());
                // v3CombineRequest.setPlatformAmount(orderPO.getThirdpartnarFee().add(orderPO.getServiceFee()));
                // if (commission.compareTo(BigDecimal.ZERO) > 0) {
                // v3CombineRequest.setPlatformAmount(v3CombineRequest.getPlatformAmount().add(commission));
                // }
                // 平台服务费修订。Andy
                BigDecimal platformServiceFee = payService.platformServiceFee(orderPO,paymentRequest.getOrderOn());
                log.info("平台服务费 paySn{} {}",orderPO.getPaySn(),platformServiceFee);
                v3CombineRequest.setPlatformAmount(platformServiceFee);
            }
            // otherAmount已经未使用了
            v3CombineRequest.setOtherAmount(orderPO.getExpressFee());
            if (!isCard) {
                v3CombineRequest.setSubsidyAmount(this.getV3CombineSubsidyAmount(paymentRequest, orderPO));
            }
            v3CombineRequest.setAttach(orderPO.getOrderSn());
            v3CombineRequest.setOpenId(openId);
            wxPayV3CombineRequest.add(v3CombineRequest);
        }

        return wxPayV3CombineRequest;
    }

    /**
     *  微信支付请求的【营销补差金额】
     */
    public BigDecimal getV3CombineSubsidyAmount(PaymentCreateRequest paymentRequest, OrderPO orderPO) {
        if (OrderTypeEnum.isPresell(orderPO.getOrderType())) {
            OrderPresellPO orderPresell = orderPresellService.queryByPayNo(paymentRequest.getOrderOn());
            if(!PresellCapitalTypeEnum.BALANCE.getValue().equals(orderPresell.getType())) {
                return BigDecimal.ZERO;
            }
        }
        // 查询订单营销信息
        BigDecimal subsidyAmount = orderProductExtendService.lambdaQuery().eq(OrderProductExtendPO::getOrderSn, orderPO.getOrderSn())
                .eq(OrderProductExtendPO::getIsStore, CommonEnum.NO.getCode())
                .eq(OrderProductExtendPO::getPromotionType, PromotionConst.PROMOTION_TYPE_402)
                .eq(OrderProductExtendPO::getFunder, CouponFunder.PLATFORM.getValue())
                .list()
                .stream()
                .map(OrderProductExtendPO::getPromotionAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        log.info("最终微信补差金额，paySn{} orderType:{} subsidyAmount：{}", orderPO.getPaySn(), orderPO.getOrderType(), subsidyAmount);
        return subsidyAmount;
    }


    PaymentCreateRequest buildEnjoyPayRequest(EnjoyPayOrderRequest payRequest, OrderPO orderPO,
        ProductPriceVO finance) {
        PayOrderOnConditionDTO payOrderOnConditionDTO = PayOrderOnConditionDTO.builder().notifyUrl(notifyUrl)
            .payOrderOnType(PayOrderOnType.ORDER_SN).paySn(orderPO.getPaySn()).orderSn(orderPO.getOrderSn())
            .paymentCode(orderPO.getPaymentCode()).payAmount(orderPO.getOrderAmount()).build();
        PayInfoBO pay = payService.buildPayInfoParam(payOrderOnConditionDTO); // Andy.2022.05.18
        log.info("构建用呗支付参数，PayInfoBO：{}", pay);
        OrderExtendPO orderExtendPo = orderExtendService.getOrderExtendByOrderSn(orderPO.getOrderSn());

        PayWayEnum payWay = payRequest.getPayMethod().getPayWay();
        String storeId = orderPO.getStoreId().toString();
        // 构建用呗请求信息
        PaymentCreateRequest paymentRequest = new PaymentCreateRequest();
        //预付订单，新增预付单号和支付顺序传输，兼容收银台新老订单
        paymentRequest.setPhaseOrderNo(orderPO.getPaySn());
        paymentRequest.setSequence(pay.getSequence());
        paymentRequest.setNewOrder(orderPO.getNewOrder());
        paymentRequest.setOrderOn(pay.getOrderOn());// 修改：Andy.2022-05-18
        paymentRequest.setPayWay(payWay);
        paymentRequest.setPayer(orderExtendPo.getCustomerId());
        paymentRequest.setPayerName(StringUtils.isBlank(orderExtendPo.getCustomerName()) ? orderPO.getUserMobile()
            : orderExtendPo.getCustomerName());
        paymentRequest.setMerchantId(String.valueOf(orderPO.getStoreId()));
        paymentRequest.setMerchantName(orderPO.getStoreName());
        paymentRequest.setSystemCode(SYSTEM_CODE);
        // 付款来源渠道,CAPP、BAPP、H5、小程序、PC
        paymentRequest.setChannelSource(payRequest.getChannel().getValue());
        // 构建用呗支付请求时，校验金额是否大于用呗可用额度
//        checkEnjoyPayLimit(orderExtendPo.getCustomerId(), payOrderOnConditionDTO.getPayAmount());
        paymentRequest.setOrderAmt(pay.getPayAmount());
        paymentRequest.setPayAmt(pay.getPayAmount());// 支付金额 Andy.2022.05.30
        paymentRequest.setOrderCreateTime(orderPO.getCreateTime().getTime());
        paymentRequest.setNotifyUrl(pay.getNotifyUrl());// 修改：Andy.2022-05-18
        paymentRequest.setLoanNotifyUrl(loanNotifyUrl);
        // 默认
        paymentRequest.setCallbackUrl("-");
        SaveCdMallApplyRequest saveCdMallApplyReq = new SaveCdMallApplyRequest();
        //商家收款卡信息
        String merchantPaymentCard;
        //新订单走银联收款账户，旧订单走客户中心配置的银行卡id
        if (orderPO.getNewOrder()) {
            AccountCard accountCard = billOperatinIntegration.detailByBankAccount(AccountConstans.UNI_PLF_STORE_ID,
                AccountCardTypeEnum.UNI_JS_PLF_SUP);
            merchantPaymentCard = accountCard.getLoanCardId();
        } else {
            StoreContractReceiptInfoVO storeContractRecommend =
                storeFeignClient.getStoreContractReciptInfo(orderPO.getRecommendStoreId());
            merchantPaymentCard = storeContractRecommend.getStore().getAcctId();
        }

        // 构建MallApplicationInfoList信息
        List<OrderDetailInfo> mallApplicationInfoList = new ArrayList<>();

        //查询订单商品信息
        List<OrderProductPO> orderProductPOS = orderProductService.lambdaQuery()
                .eq(OrderProductPO::getOrderSn, orderPO.getOrderSn())
                .list();
        Map<Long, Product> longProductMap = orderLocalUtils.getLongProductMap(orderProductPOS);

        for (OrderProductPO orderProductPO : orderProductPOS) {
            OrderDetailInfo orderDetailInfo = new OrderDetailInfo();
            orderDetailInfo.setMerchantPaymentCard(merchantPaymentCard);
            orderDetailInfo.setMerchantId(storeId);
            orderDetailInfo.setOrderDetailAmount(pay.getPayAmount());//Andy.预售
            orderDetailInfo.setOrderDetailId(pay.getOrderOn());//Andy.预售
            orderDetailInfo.setOrderDetailSts(String.valueOf(orderPO.getOrderState()));
            // 贷款用途 默认为01
            orderDetailInfo.setLoanPurpose("01");
            orderDetailInfo.setMerchantName(orderPO.getStoreName());
            orderDetailInfo.setMerchantType(orderPO.getStoreIsSelf().toString());
            orderDetailInfo.setRecommendStoreId(orderPO.getRecommendStoreId());
            //商品信息
            List<String> strings = Arrays.asList(orderProductPO.getGoodsCategoryPath().split("->"));
            orderDetailInfo.setCommodityId(orderProductPO.getGoodsId().toString());
            orderDetailInfo.setCommodityName(orderProductPO.getGoodsName());
            orderDetailInfo.setFirstItemCategoryName(strings.size() > 0 ? strings.get(0) : "");
            orderDetailInfo.setSecondItemCategoryName(strings.size() > 1 ? strings.get(1) : "");
            orderDetailInfo.setThreeItemCategoryName(strings.size() > 2 ? strings.get(2) : "");

            //处理类目id
            if (longProductMap.containsKey(orderProductPO.getProductId())) {
                Product product = longProductMap.get(orderProductPO.getProductId());
                orderDetailInfo.setFirstItemCategoryId(product.getCategoryId1().toString());
                orderDetailInfo.setSecondItemCategoryId(product.getCategoryId2().toString());
                orderDetailInfo.setThreeItemCategoryId(product.getCategoryId3().toString());
            }
            mallApplicationInfoList.add(orderDetailInfo);
        }
        saveCdMallApplyReq.setMallApplicationInfoList(mallApplicationInfoList);
        saveCdMallApplyReq.setChannel(payRequest.getChannel().getValue());
        saveCdMallApplyReq.setDeviceInfo(payRequest.getDeviceInfo());
        saveCdMallApplyReq.setDueDay(payRequest.getDueDay());
        saveCdMallApplyReq.setLoanCustId(orderExtendPo.getCustomerId());
        saveCdMallApplyReq.setBranchId(orderExtendPo.getBranch());

        // 构造LoanProductInfo信息
        LoanProductInfo loanProductInfo = new LoanProductInfo();
        // 贷款产品 loanProduct
        LambdaQueryWrapper<PayMethodPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PayMethodPO::getPayMethodCode, payWay.getValue());
        PayMethodPO payMethodPoDb = payMethodMapper.selectOne(queryWrapper);
        loanProductInfo
            .setLoanProductId(StringUtil.isBlank(payMethodPoDb.getLoanProduct()) ? "" : payMethodPoDb.getLoanProduct());

        loanProductInfo.setPayMode(payWay.getValue());
        loanProductInfo.setLoanPeriod(payRequest.getLoanPeriod());
        // enjoypay followheart credit
        String payMode = null;
        if (PayWayEnum.ENJOY_PAY == payWay && payRequest.getEnjoyPayVipFlag() == 0) {
            payMode = "enjoypay";
            //    区分用呗专享
        } else if (PayWayEnum.ENJOY_PAY == payWay && payRequest.getEnjoyPayVipFlag() == 1) {
            payMode = "enjoypay_vip";
        } else if (PayWayEnum.FOLLOW_HEART == payWay) {
            payMode = "followheart";
        } else if (PayWayEnum.CREDIT_PAY == payWay) {
            payMode = "credit";
        } else {
            throw new MallException("支付方式暂未开通: " + payWay, ErrorCodeEnum.U.INVALID_BIZ_DATA.getCode());
        }
        loanProductInfo.setPayMode(payMode);
        loanProductInfo.setRepaymentMode(payRequest.getRepaymentMode());

        // 计息差开始日：为交易成功日/起息日 当前时间+15天
        loanProductInfo.setHrRateDate("");

        // 起息方式：固定交易成功起息 (important:为1的时候，计息差开始日传空，为2的时候，计息差开始日必传，已和产品、信贷确认)
        loanProductInfo.setIntRateMode("1");

        // 设置金融规则计息差日期
        if (Objects.nonNull(finance) && Objects.nonNull(finance.getProductFinanceRuleLabel())) {
            ProductFinanceRuleLabel financeRuleLabel = finance.getProductFinanceRuleLabel();

            /**
             * 处理记息差开始日
             */
            Date interestStartDate = financeRuleLabel.getEmployeeInterestStartDate();
            if (OrderConst.INTEREST_START_TYPE_AFTER_CONFIRM == financeRuleLabel.getInterestStartType()) {
                interestStartDate = org.apache.commons.lang.time.DateUtils.addDays(orderPO.getCreateTime(),
                    financeRuleLabel.getInterestStartDays());
            }

            if (Objects.nonNull(interestStartDate)) {
                String dateStr = DateUtils.format(interestStartDate, "yyyy-MM-dd");
                loanProductInfo.setHrRateDate(dateStr);
            } else {
                log.warn("用呗申请单-员工记息差开始日为null orderSn:{}", orderPO.getOrderSn());
            }

            if (Objects.nonNull(financeRuleLabel.getInterestWay()) && Objects.nonNull(financeRuleLabel.getInterestWay())) {
                if (financeRuleLabel.getInterestWay().equals(InterestType.N_DAYS_AFTER_PAY.getValue())) {
                    loanProductInfo.setIntRateMode(InterestType.PLAN_DATE_LOAN.getValue().toString());
                } else {
                    loanProductInfo.setIntRateMode(financeRuleLabel.getInterestWay().toString());
                }
            }
        }

        saveCdMallApplyReq.setLoanProductInfo(loanProductInfo);

        // 默认为个人
        saveCdMallApplyReq.setOrderType("个人");
        saveCdMallApplyReq.setOperator(orderExtendPo.getCustomerId());
        saveCdMallApplyReq.setOrderAmount(pay.getPayAmount());//预售.Andy
        saveCdMallApplyReq.setOrderBatchId(pay.getOrderOn());//预售.Andy
        paymentRequest.setLoanReq(saveCdMallApplyReq);
        log.info("buildEnjoyPayRequest的request参数，{}", paymentRequest);
        return paymentRequest;
    }

    private void checkEnjoyPayLimit(String custNo, BigDecimal payAmount) {
        /**
         * 查询客户中心 信贷支付状态和余额
         */
        if (StringUtils.isNotBlank(custNo)) {
            List<CustCreditLimitVo> creditLimitVos = customerIntegration.queryLimitList(custNo);
            if (CollectionUtils.isNotEmpty(creditLimitVos)) {
                for (CustCreditLimitVo creditLimitVo : creditLimitVos) {
                    if ("E".equals(creditLimitVo.getSts())) {
                        continue;
                    }

                    // 02为用呗，判断可用额度与支付金额大小
                    if (creditLimitVo.getSts() == "S" && creditLimitVo.getCreditType() == "02") {
                        Boolean result = creditLimitVo.getBalanceAmt().compareTo(payAmount) < 0;
                        if (result) {
                            throw new MallException("当前支付金额大于您的用呗额度，请更换支付方式!");
                        }
                    }
                }
            }
        }
    }

    public LoanProductVO getLoanProductInfo(String paymentCode, String paySn) {
        OrderExample example = new OrderExample();
        example.setPaySn(paySn);
        List<OrderPO> orderPOList = orderModel.getOrderList(example, null);
        // 以后可能一笔订单会有多个商家的商品
        List<Long> StoreIds = new ArrayList<>();
        for (OrderPO orderPO : orderPOList) {
            StoreIds.add(orderPO.getStoreId());
        }
        OrderPO orderPO = orderPOList.get(0);
        Long storeId = orderPO.getStoreId();
        PayMethodPO payMethodPO = new PayMethodPO();
        payMethodPO.setPayMethodCode(paymentCode);
        LambdaQueryWrapper<PayMethodPO> updateWrapper = new LambdaQueryWrapper();
        updateWrapper.eq(PayMethodPO::getPayMethodCode, paymentCode);
        PayMethodPO payMethodPoDb = payMethodMapper.selectOne(updateWrapper);
        String loanProduct = StringUtil.isBlank(payMethodPoDb.getLoanCode()) ? "" : payMethodPoDb.getLoanCode();
        LoanProductVO loanProductVO = new LoanProductVO();
        loanProductVO.setLoanProduct(loanProduct);
        loanProductVO.setStoreId(StoreIds);
        return loanProductVO;
    }

    /**
     * 退款
     *
     * @param orderPO
     * @param refundAmount
     * @param operator
     * @return java.lang.Boolean True:成功 False:失败 throws Exception:结果未决
     * <AUTHOR>
     * @date 2021/6/15 14:43
     */
    public Boolean refund(OrderPO orderPO, String afsSn, BigDecimal refundAmount, String operator) throws Exception {
        OrderPayExample example = new OrderPayExample();
        example.setPaySn(orderPO.getPaySn());
        OrderPayPO orderPayPO = orderPayMapper.listByExample(example).get(0);

        PaymentRefundRequest refundRequest = new PaymentRefundRequest();
        refundRequest.setPayCode(orderPayPO.getTradeSn());
        refundRequest.setOrderOn(orderPO.getPaySn());
        if (PayMethodEnum.getValue(orderPayPO.getPaymentCode()) == PayMethodEnum.WXPAY) {
            refundRequest.setCombineFlag(1);
            refundRequest.setCombineOrderOn(orderPO.getOrderSn());
        }
        refundRequest.setRefundOn(afsSn);
        refundRequest.setSystemCode(SYSTEM_CODE);
        if (PayMethodEnum.getValue(orderPO.getPaymentCode()) == PayMethodEnum.WXPAY) {
            refundRequest.setSystemCode(SYSTEM_CODE);
        } else {
            refundRequest.setSystemCode(SYSTEM_CODE);
        }
        refundRequest.setRefundAmt(refundAmount);
        refundRequest.setRefundCreateTime(System.currentTimeMillis());
        refundRequest.setRefundReason("整单取消");
        refundRequest.setNotifyUrl(refundNotifyUrl);

        LambdaQueryWrapper<OrderReturnPO> orderReturnQuery = new LambdaQueryWrapper<>();
        orderReturnQuery.eq(OrderReturnPO::getAfsSn, afsSn);
        OrderReturnPO orderReturnPO = orderReturnService.getOne(orderReturnQuery);
        if (orderReturnPO != null) {
            refundRequest.setPlatformAmount(orderReturnPO.getCommissionAmount().add(orderReturnPO.getServiceFee())
                .add(orderReturnPO.getThirdpartnarFee()));
            refundRequest.setPlatformFlag(1);
            refundRequest.setOtherAmount(orderReturnPO.getReturnExpressAmount());
            refundRequest.setSubsidyAmount(orderReturnPO.getPlatformVoucherAmount());
        }
        AlipayRefundRequest alipayRefundRequest = new AlipayRefundRequest();
        alipayRefundRequest.setRefundOperator(operator);
        alipayRefundRequest.setIsFullRefund("1");
        alipayRefundRequest.setRequestId("");

        StoreContractReceiptInfoVO storeContract = storeFeignClient.getStoreContractReciptInfo(orderPO.getStoreId());

        if (orderPO.getPaymentCode().contains(PayMethodEnum.ALIPAY.getValue())) {
            alipayRefundRequest.setSellerId(storeContract.getAliSellerId());
        } else {
            alipayRefundRequest.setSellerId(storeContract.getWxSellerId());
        }
        refundRequest.setAlipayRefundRequest(alipayRefundRequest);

        return this.refund(refundRequest, afsSn);
    }

    /**
     * 退款
     *
     * @param refundRequest
     * @return
     * @throws Exception
     */
    public Boolean refund(PaymentRefundRequest refundRequest,String afsSn) throws Exception {
        Result<PaymentRefundVO> refundVO = null;
        try {
        	log.info("开始调用统一支付退款，退款单号:{}，refundVO:{}", refundRequest.getRefundOn());
            refundVO = mallPaymentFacade.paymentRefund(refundRequest);
            log.info("调用微信退款结果，退款单号:{}，refundVO:{}", refundRequest.getRefundOn(),refundVO);
        } catch (Exception ex) {
            log.error("调用调用统一支付退款异常:{}，退款单号:{}，请及时排查", ex.getMessage(), refundRequest.getRefundOn());
            orderAfterServiceModel.updateRefundFailReasonByAfsSn(afsSn, "调用退款异常->:{}" + ex.getMessage().substring(0,200));
            throw new BusinessException("退款失败，请联系管理人员！");
        }

        if (refundVO == null || refundVO.getData() == null) {
            orderAfterServiceModel.updateRefundFailReasonByAfsSn(afsSn, "调用退款返回结果为空异常");
            throw new BusinessException("退款失败，请联系管理人员！");
        }

        if (!refundVO.isSuccess()) {
            log.error("调用调用统一支付退款异常:{}，退款单号:{}，请及时排查", refundVO.getMessage(), refundRequest.getRefundOn());
            String failReason = "调用退款接口isSuccess返回不为true，" + refundVO.getMessage();
            orderAfterServiceModel.updateRefundFailReasonByAfsSn(afsSn, failReason);
            throw new BusinessException("退款失败，请联系管理人员！");
        }
        return Boolean.TRUE;
    }

    /**
     * @param orderPO
     * @param refundRequest
     * @return void
     * @description :调用支付服务进行用呗退款
     */
    public void loanRefundByPayment(OrderPO orderPO, PaymentRefundRequest refundRequest,String afsSn) {
        Result<PaymentRefundVO> refundVO = null;
        try {
            log.info("###正在向payment服务发起贷款退款，refundRequest：{}", JSON.toJSONString(refundRequest));
            refundVO = mallPaymentFacade.paymentRefund(refundRequest);
            log.info("###payment服务退款结果，refundRequest：{} refundVO:{}", JSON.toJSONString(refundRequest),refundVO);
        } catch (Exception ex) {
        	log.warn("###payment服务贷款退款异常，refundRequest：{} refundVO:{}", JSON.toJSONString(refundRequest),refundVO,ex);
            orderAfterServiceModel.updateRefundFailReasonByAfsSn(afsSn, "payment服务贷款退款异常->:{}" + ex.getMessage().substring(0,200));
            throw new MallException("payment-service：退款异常", ErrorCodeEnum.C.CALL_EXCEPTION.getCode(), ex);
        }

        if (refundVO == null) {
            orderAfterServiceModel.updateRefundFailReasonByAfsSn(afsSn, "payment服务贷款退款申请接口返回值null，异常");
            throw new MallException("退款失败：null", "orderSn:" + orderPO.getOrderSn() + " store:" + orderPO.getStoreName(),
                ErrorCodeEnum.C.CALL_EXCEPTION.getCode());
        }

        if (!refundVO.isSuccess() || refundVO.getData() == null) {
        	log.warn("###payment服务退款异常，refundRequest：{} refundVO:{}", JSON.toJSONString(refundRequest),refundVO);
            orderAfterServiceModel.updateRefundFailReasonByAfsSn(afsSn, refundVO.getMessage());
            throw new MallException("退款失败：" + refundVO.getErrorMsg(),
                "orderSn:" + orderPO.getOrderSn() + " store:" + orderPO.getStoreName(),
                ErrorCodeEnum.C.CALL_EXCEPTION.getCode());
        }
    }

    public String getRefundNotifyUrl() {
        return refundNotifyUrl;
    }

    public String getLoanNotifyUrl() {
        return loanNotifyUrl;
    }

    /**
     * @param orderSn
     * @return com.cfpamf.mallpayment.facade.vo.PaymentLoanInfoVO
     * @description : 根据订单号查询放款明细信息
     */
    public PaymentLoanInfoVO queryLoanInfo(String orderSn) {
        return ExternalApiUtil.callResultApi(() -> mallPaymentFacade.queryLoanInfo(orderSn), orderSn,
                "/mallpayment/queryLoanInfo", "根据订单号查询放款信息明细");
    }

    public Boolean queryPayStatus(String paySn){
        List<PaymentPayStatusVO> payStatusVOS = ExternalApiUtil.callResultApi(() -> mallPaymentFacade.queryPayStatusBatch(paySn), paySn,
                "/mallpayment/queryPayStatus", "查询支付单支付状态");

        return CollectionUtils.isNotEmpty(payStatusVOS);
    }

    /**
     * 支付单关单，当前只支持微信支付类型的关单
     *
     * @param paymentCancelRequest  关单入参
     * @return                      true/false
     */
    public Boolean paymentClose(PaymentCancelRequest paymentCancelRequest) {

        log.info("PayIntegration paymentClose, PaymentCancelRequest: {}", JSON.toJSONString(paymentCancelRequest));

        Result<PaymentOrderCancelVO> result = null;
        try {
            result = mallPaymentFacade.paymentClose(paymentCancelRequest);
        } catch (Exception e) {
            throw new MallException("paymentClose fail, PaymentCancelRequest", ErrorCodeEnum.C.CALL_EXCEPTION.getCode(), e);
        }

        log.info("PayIntegration paymentClose, PaymentOrderCancelVO: {}", JSON.toJSONString(result));

        return result.isSuccess() && result.getData().getCancelResult();

    }

}
