package com.cfpamf.ms.mallorder.integration.promotion;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.mallorder.integration.promotion.facade.DiscountCouponFacade;
import com.cfpamf.ms.promotion.facade.vo.discount.CouponLockSummary;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.exception.MallException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 乡助优惠券服务交互
 * <AUTHOR>
 */
@Slf4j
@Component
public class MsCouponPkgIntegration {

    @Autowired
    private DiscountCouponFacade discountCouponFacade;

    public void lockCoupons(CouponLockSummary couponLockSummary) {
        try {
            Result<Boolean> result = discountCouponFacade.lockCoupons(couponLockSummary);
            if (result == null || !result.isSuccess()) {
                log.warn("锁定乡助优惠券异常, error: {}", result == null ? "" : result.getErrorMsg());
                throw new MallException(result == null ? "锁定乡助优惠券异常" : result.getErrorMsg());
            }
            if (!result.getData()) {
                log.info("锁定乡助优惠券校验不通过");
                throw new BusinessException("您不符合该商品下单要求～");
            }
        } catch (Exception ex) {
            log.warn("锁定乡助优惠券异常，error: {}", ex.getMessage());
            throw ex;
        }
    }

}
