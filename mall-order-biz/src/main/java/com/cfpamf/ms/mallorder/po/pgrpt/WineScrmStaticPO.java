package com.cfpamf.ms.mallorder.po.pgrpt;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value = "WineScrmStaticPO对象", description = "酒水指标统计表")
public class WineScrmStaticPO {

    @ApiModelProperty(value = "日期")
    private String rptDate;

    @ApiModelProperty(value = "员工工号")
    private String empId;

    @ApiModelProperty(value = "员工姓名")
    private String empName;

    @ApiModelProperty(value = "区域编码")
    private String areaCode;

    @ApiModelProperty(value = "区域名称")
    private String areaName;

    @ApiModelProperty(value = "片区编码")
    private String districtCode;

    @ApiModelProperty(value = "片区名称")
    private String districtName;

    @ApiModelProperty(value = "分支编码")
    private String bchCode;

    @ApiModelProperty(value = "分支名称")
    private String bchName;

    @ApiModelProperty(value = "司龄")
    private Integer joinMonths;

    @ApiModelProperty(value = "岗位编码")
    private String jobCode;

    @ApiModelProperty(value = "岗位名称")
    private String jobName;

    @ApiModelProperty(value = "管辖在岗人数")
    private Integer acmWorkEmpCnt;

    @ApiModelProperty(value = "当月酒水营收")
    private BigDecimal smWineRevenueAmt;

    @ApiModelProperty(value = "上月酒水营收")
    private BigDecimal lmWineRevenueAmt;

    @ApiModelProperty(value = "当月有效销售额")
    private BigDecimal smWineValidSaleAmt;

    @ApiModelProperty(value = "上月有效销售额")
    private BigDecimal lmWineValidSaleAmt;

    @ApiModelProperty(value = "当月人均有效销售额")
    private BigDecimal smWineAvgValidSaleAmt;

    @ApiModelProperty(value = "上月人均有效销售额")
    private BigDecimal lmWineAvgValidSaleAmt;

    @ApiModelProperty(value = "当月年度目标完成率")
    private BigDecimal smSyWineValidSalesAmtTargetFinishRadio;

    @ApiModelProperty(value = "上月年度目标完成率")
    private BigDecimal lmSyWineValidSalesAmtTargetFinishRadio;

}
