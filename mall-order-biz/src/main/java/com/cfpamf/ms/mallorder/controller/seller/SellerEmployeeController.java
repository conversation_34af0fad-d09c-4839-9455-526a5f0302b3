package com.cfpamf.ms.mallorder.controller.seller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.cdfinance.hrms.facade.vo.OnBoardEmployeeVO;
import com.cfpamf.ms.mallorder.service.EmployeeService;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;

import io.swagger.annotations.Api;

@Api(tags = "seller-员工操作")
@RestController
@RequestMapping("/seller/employee")
public class SellerEmployeeController {

	@Autowired
	private EmployeeService employeeService;

	@GetMapping("query/detail")
	public JsonResult<OnBoardEmployeeVO> queryByEmployeeCode(String employeeCode) {
		OnBoardEmployeeVO data = employeeService.queryByEmployeeCode(employeeCode);
		return SldResponse.success(data);
	}

}
