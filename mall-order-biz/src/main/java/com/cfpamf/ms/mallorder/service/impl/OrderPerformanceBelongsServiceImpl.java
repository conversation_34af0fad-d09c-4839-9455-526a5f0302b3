package com.cfpamf.ms.mallorder.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cfpamf.framework.autoconfigure.common.exception.MSException;
import com.cfpamf.framework.autoconfigure.web.bms.JwtUserInfo;
import com.cfpamf.ms.bms.facade.vo.OrganizationVO;
import com.cfpamf.ms.customer.facade.vo.CustInfoVo;
import com.cfpamf.ms.customer.facade.vo.UserInfoVo;
import com.cfpamf.ms.customer.facade.vo.user.UserBaseInfoVo;
import com.cfpamf.ms.mallgoods.facade.vo.GoodsSharecodeBindUser;
import com.cfpamf.ms.mallorder.common.config.OrderPerformanceStoreConfig;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.constant.PerformanceBelongsConst;
import com.cfpamf.ms.mallorder.common.enums.ManageTypeEnum;
import com.cfpamf.ms.mallorder.common.enums.OperateTypeEnum;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.cfpamf.ms.mallorder.common.mq.RabbitMQUtils;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.*;
import com.cfpamf.ms.mallorder.enums.*;
import com.cfpamf.ms.mallorder.integration.bms.BmsOrgFeignIntegration;
import com.cfpamf.ms.mallorder.integration.cust.CustomerIntegration;
import com.cfpamf.ms.mallorder.integration.facade.vo.DbcDistributionSaleUserVO;
import com.cfpamf.ms.mallorder.integration.goods.GoodsShareFeignIntegration;
import com.cfpamf.ms.mallorder.integration.hrms.HrmsEmployeeFeignIntegration;
import com.cfpamf.ms.mallorder.integration.shop.VendorFeignIntegration;
import com.cfpamf.ms.mallorder.integration.userPubService.UpsServiceFeignIntegration;
import com.cfpamf.ms.mallorder.po.OrderPerformanceBelongsPO;
import com.cfpamf.ms.mallorder.mapper.OrderPerformanceBelongsMapper;
import com.cfpamf.ms.mallorder.po.OrderExtendPO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.framework.autoconfigure.mybatis.BaseRepoServiceImpl;
import com.cfpamf.ms.mallorder.vo.CustRelateManagerVO;
import com.cfpamf.ms.mallorder.vo.EventTraceVO;
import com.cfpamf.ms.mallorder.vo.OrderPerformanceBelongsVO;
import com.cfpamf.ms.mallorder.vo.custVO.SimpleUserRecommendVo;
import com.cfpamf.ms.mallorder.vo.hrmsVO.EmployeeVO;
import com.cfpamf.ms.mallorder.vo.hrmsVO.OutEmployeeVO;
import com.cfpamf.ms.mallshop.resp.Vendor;
import com.slodon.bbc.core.exception.MallException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 订单业绩归属信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-18
 */
@Slf4j
@Service
public class OrderPerformanceBelongsServiceImpl extends BaseRepoServiceImpl<OrderPerformanceBelongsMapper, OrderPerformanceBelongsPO> implements IOrderPerformanceBelongsService {

    @Autowired
    private OrderPerformanceStoreConfig orderPerformanceStoreConfig;
    @Autowired
    private IOrderService orderService;
    @Autowired
    private IOrderExtendService orderExtendService;
    @Autowired
    private ICommonMqEventService commonMqEventService;
    @Autowired
    private IEventTraceService eventTraceService;
    @Autowired
    private GoodsShareFeignIntegration goodsShareFeignIntegration;
    @Autowired
    private CustomerIntegration customerIntegration;
    @Autowired
    private HrmsEmployeeFeignIntegration hrmsEmployeeFeignIntegration;
    @Autowired
    private BmsOrgFeignIntegration bmsOrgFeignIntegration;
    @Autowired
    private UpsServiceFeignIntegration upsServiceFeignIntegration;
    @Autowired
    private VendorFeignIntegration vendorFeignIntegration;
    @Autowired
    private RabbitMQUtils rabbitMQUtils;

    @Override
    public Boolean bindPerformanceToOrder(CommissionAffiliationUpdateDTO notifyDTO) {
        if (Objects.isNull(notifyDTO) || StringUtils.isBlank(notifyDTO.getOrderSn())) {
            throw new MallException(String.valueOf(ErrorCodeEnum.U.EMPTY_PARAM.getCode()), "订单绑定业绩归属信息的入参为空");
        }
        OrderPO orderPO = orderService.getByOrderSn(notifyDTO.getOrderSn());
        if (Objects.isNull(orderPO)) {
            log.info("未找到指定orderSn的订单详情,orderSn:{}", notifyDTO.getOrderSn());
            return Boolean.TRUE;
        }
        if (PerformanceBelongsConst.COMMISSION_AFFILIATION_CREATE.equals(notifyDTO.getEventCode())) {
            log.info("开始处理订单绑定业绩归属信息,orderSn:{}", notifyDTO.getOrderSn());
            if (!notifyDTO.getDistributionOrderExistible()) {
                return noneDistributionTypeBindCreate(orderPO);
            }
            return distributionTypeBindCreate(orderPO, notifyDTO);
        } else if (PerformanceBelongsConst.COMMISSION_AFFILIATION_UPDATE.equals(notifyDTO.getEventCode())) {
            log.info("开始处理订单更新业绩归属信息,orderSn:{}", notifyDTO.getOrderSn());
            if (!notifyDTO.getDistributionOrderExistible()) {
                OrderPerformanceBelongsPO performanceBelongsPO = noneDistributionTypeBindUpdate(orderPO,OrderPerformanceBindStateEnum.EFFECT);
                if (Objects.isNull(performanceBelongsPO)) {
                    return false;
                }
                this.sendPerformanceBoundNotifyMsg(performanceBelongsPO);
                return true;
            }
            OrderPerformanceBelongsPO performanceBelongsPO = distributionTypeBindUpdate(orderPO, notifyDTO,OrderPerformanceBindStateEnum.EFFECT);
            if (Objects.isNull(performanceBelongsPO)) {
                return false;
            }
            this.sendPerformanceBoundNotifyMsg(performanceBelongsPO);
            return true;
        }else if (PerformanceBelongsConst.COMMISSION_AFFILIATION_ADJUST.equals(notifyDTO.getEventCode())) {
            log.info("开始处理订单调整业绩归属信息,orderSn:{}", notifyDTO.getOrderSn());
            LambdaQueryWrapper<OrderPerformanceBelongsPO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(OrderPerformanceBelongsPO::getOrderSn,orderPO.getOrderSn());
            OrderPerformanceBelongsPO beforeEntity = this.getOne(queryWrapper);
            if (Objects.isNull(beforeEntity)) {
                log.warn("订单在调整业绩归属信息前无绑定记录,orderSn:{}",orderPO.getOrderSn());
                return false;
            }
            if (!notifyDTO.getDistributionOrderExistible()) {
                OrderPerformanceBelongsPO afterEntity = noneDistributionTypeBindUpdate(orderPO,OrderPerformanceBindStateEnum.ADJUST);
                if (Objects.isNull(afterEntity)) {
                    return false;
                }
                this.trackPerformanceAdjust(beforeEntity,afterEntity,notifyDTO);
                return true;
            }
            OrderPerformanceBelongsPO afterEntity = distributionTypeBindUpdate(orderPO, notifyDTO,OrderPerformanceBindStateEnum.ADJUST);
            if (Objects.isNull(afterEntity)) {
                return false;
            }
            this.trackPerformanceAdjust(beforeEntity,afterEntity,notifyDTO);
            return true;
        }else {
            throw new MallException(String.valueOf(ErrorCodeEnum.U.ILLEGAL_PARAM.getCode()),"暂不支持此事件类型的订单绑定业绩归属处理");
        }
    }

    private Boolean noneDistributionTypeBindCreate(OrderPO orderPO) {
        log.info("未产生分销的订单进行绑定业绩归属信息,orderSn:{}", orderPO.getOrderSn());
        BasicUserInfoDTO basicUserDTO = this.getEmployeeUserInfo(orderPO.getUserNo());
        if (Objects.nonNull(basicUserDTO) && UserIdentityTypeEnum.COMPANY_STAFF == basicUserDTO.getUserIdentityType()) {
            return this.generatePerformanceBelongs(basicUserDTO, orderPO);
        }
        if (orderPerformanceStoreConfig.getFollowRegisterRecommendStore().contains(String.valueOf(orderPO.getRecommendStoreId()))) {
            basicUserDTO = this.getShareReferrer(orderPO.getOrderSn());
            if (Objects.nonNull(basicUserDTO)) {
                return this.generatePerformanceBelongs(basicUserDTO, orderPO);
            }
            basicUserDTO = this.getLoanManager(orderPO.getOrderSn());
            if (Objects.nonNull(basicUserDTO)) {
                return this.generatePerformanceBelongs(basicUserDTO, orderPO);
            }
            basicUserDTO = this.packFollowRegisterGetUser(orderPO);
            return this.generatePerformanceBelongs(basicUserDTO, orderPO);
        }
        basicUserDTO = this.getAgricManager(orderPO.getUserNo());
        if (Objects.nonNull(basicUserDTO)) {
            return this.generatePerformanceBelongs(basicUserDTO, orderPO);
        }
        basicUserDTO = this.getLoanManager(orderPO.getOrderSn());
        if (Objects.nonNull(basicUserDTO)) {
            return this.generatePerformanceBelongs(basicUserDTO, orderPO);
        }
        basicUserDTO = this.getDefaultUser(orderPO.getOrderSn());
        return this.generatePerformanceBelongs(basicUserDTO, orderPO);
    }

    private Boolean distributionTypeBindCreate(OrderPO orderPO, CommissionAffiliationUpdateDTO notifyDTO) {
        log.info("已产生分销的订单进行绑定业绩归属信息,orderSn:{}", orderPO.getOrderSn());
        if (CollectionUtils.isEmpty(notifyDTO.getItemList())) {
            log.info("分销订单无实体分销用户的订单进行绑定业绩归属信息,orderSn:{}", orderPO.getOrderSn());
            if (!orderPerformanceStoreConfig.getFollowRegisterRecommendStore().contains(String.valueOf(orderPO.getRecommendStoreId()))) {
                BasicUserInfoDTO basicUserDTO = this.getDefaultUser(orderPO.getOrderSn());
                return this.generatePerformanceBelongs(basicUserDTO, orderPO);
            }
            BasicUserInfoDTO basicUserDTO = this.packFollowRegisterGetUser(orderPO);
            return this.generatePerformanceBelongs(basicUserDTO, orderPO);
        }
        log.info("订单开始绑定分销类型的业绩归属信息,orderSn:{}",orderPO.getOrderSn());
        CommissionAffiliationItemDTO commissionItemDTO = notifyDTO.getItemList().get(0);
        BasicUserInfoDTO basicUserDTO = this.getDistributionUserInfo(commissionItemDTO);
        return this.generatePerformanceBelongs(basicUserDTO, orderPO);
    }

    private OrderPerformanceBelongsPO noneDistributionTypeBindUpdate(OrderPO orderPO, OrderPerformanceBindStateEnum bindStateEnum) {
        log.info("未产生分销的订单进行更新业绩归属信息,orderSn:{}", orderPO.getOrderSn());
        BasicUserInfoDTO basicUserDTO = this.getEmployeeUserInfo(orderPO.getUserNo());
        if (Objects.nonNull(basicUserDTO) && UserIdentityTypeEnum.COMPANY_STAFF == basicUserDTO.getUserIdentityType()) {
            return this.modifyPerformanceBelongs(basicUserDTO, orderPO, bindStateEnum);
        }
        if (orderPerformanceStoreConfig.getFollowRegisterRecommendStore().contains(String.valueOf(orderPO.getRecommendStoreId()))) {
            basicUserDTO = this.getShareReferrer(orderPO.getOrderSn());
            if (Objects.nonNull(basicUserDTO)) {
                return this.modifyPerformanceBelongs(basicUserDTO, orderPO, bindStateEnum);
            }
            basicUserDTO = this.getLoanManager(orderPO.getOrderSn());
            if (Objects.nonNull(basicUserDTO)) {
                return this.modifyPerformanceBelongs(basicUserDTO, orderPO, bindStateEnum);
            }
            basicUserDTO = this.packFollowRegisterGetUser(orderPO);
            return this.modifyPerformanceBelongs(basicUserDTO, orderPO, bindStateEnum);
        }
        basicUserDTO = this.getAgricManager(orderPO.getUserNo());
        if (Objects.nonNull(basicUserDTO)) {
            return this.modifyPerformanceBelongs(basicUserDTO, orderPO, bindStateEnum);
        }
        basicUserDTO = this.getLoanManager(orderPO.getOrderSn());
        if (Objects.nonNull(basicUserDTO)) {
            return this.modifyPerformanceBelongs(basicUserDTO, orderPO, bindStateEnum);
        }
        basicUserDTO = this.getDefaultUser(orderPO.getOrderSn());
        return this.modifyPerformanceBelongs(basicUserDTO, orderPO, bindStateEnum);
    }

    private OrderPerformanceBelongsPO distributionTypeBindUpdate(OrderPO orderPO, CommissionAffiliationUpdateDTO notifyDTO,
                                               OrderPerformanceBindStateEnum bindStateEnum) {
        log.info("已产生分销的订单进行更新业绩归属信息,orderSn:{}", orderPO.getOrderSn());
        if (CollectionUtils.isEmpty(notifyDTO.getItemList())) {
            log.info("分销订单无实体分销用户的订单进行更新业绩归属信息,orderSn:{}", orderPO.getOrderSn());
            if (!orderPerformanceStoreConfig.getFollowRegisterRecommendStore().contains(String.valueOf(orderPO.getRecommendStoreId()))) {
                BasicUserInfoDTO basicUserDTO = this.getDefaultUser(orderPO.getOrderSn());
                return this.modifyPerformanceBelongs(basicUserDTO, orderPO, bindStateEnum);
            }
            BasicUserInfoDTO basicUserDTO = this.packFollowRegisterGetUser(orderPO);
            return this.modifyPerformanceBelongs(basicUserDTO, orderPO, bindStateEnum);
        }
        log.info("订单开始更新分销类型的业绩归属信息,orderSn:{}",orderPO.getOrderSn());
        CommissionAffiliationItemDTO commissionItemDTO = notifyDTO.getItemList().get(0);
        BasicUserInfoDTO basicUserDTO = this.getDistributionUserInfo(commissionItemDTO);
        return this.modifyPerformanceBelongs(basicUserDTO, orderPO, bindStateEnum);
    }

    private BasicUserInfoDTO getDistributionUserInfo(CommissionAffiliationItemDTO commissionItemDTO) {
        BasicEmployeeInfoDTO resultEmployee = new BasicEmployeeInfoDTO();
        resultEmployee.setUserCode(commissionItemDTO.getDistributionUserCode());
        resultEmployee.setUserName(commissionItemDTO.getDistributionUserName());
        resultEmployee.setUserIdentity(commissionItemDTO.getDistributionUserIdentity());

        resultEmployee.setEmployeeNo(commissionItemDTO.getDistributionUserCode());
        resultEmployee.setEmployeeName(commissionItemDTO.getDistributionUserName());
        resultEmployee.setEmployeeIdentity(commissionItemDTO.getDistributionUserIdentity());


        if (PerformanceBelongsConst.STORE_VIRTUAL_USER_ORG_ID.equals(commissionItemDTO.getDistributionBranchCode()) || PerformanceBelongsConst.UNORGANIZED_USER_ORG_ID.equals(commissionItemDTO.getDistributionBranchCode())) {
            BasicBranchDTO resultBranch = new BasicBranchDTO();
            resultBranch.setBranchNo(Long.valueOf(PerformanceBelongsConst.STORE_VIRTUAL_USER_ORG_ID));
            resultBranch.setBranchCode(PerformanceBelongsConst.STORE_VIRTUAL_USER_ORG_ID);
            resultBranch.setBranchName(PerformanceBelongsConst.DEFAULT_ORG_NAME);
            resultEmployee.setBranchDTO(resultBranch);
        } else {
            OrganizationVO orgInfoVo = bmsOrgFeignIntegration.getOrganizationByHrOrgId(Long.parseLong(commissionItemDTO.getDistributionBranchCode()));
            BasicBranchDTO superiorResultBranch = new BasicBranchDTO();
            superiorResultBranch.setBranchNo(Long.valueOf(orgInfoVo.getHrOrgId()));
            superiorResultBranch.setBranchCode(orgInfoVo.getOrgCode());
            superiorResultBranch.setBranchName(orgInfoVo.getOrgName());
            resultEmployee.setBranchDTO(superiorResultBranch);
        }

        resultEmployee.setOrderPerformanceTypeEnum(OrderPerformanceTypeEnum.DISTRIBUTION);
        return resultEmployee;
    }

    public BasicUserInfoDTO getEmployeeUserInfo(String userNo) {
        log.info("根据用户userNo查找员工信息,userNo:{}",userNo);
        UserBaseInfoVo userBaseInfoVo = customerIntegration.baseInfoByUserNo(userNo);
        if (Objects.isNull(userBaseInfoVo)) {
            return null;
        }
        BasicUserInfoDTO resultUser = new BasicUserInfoDTO();
        resultUser.setUserCode(userBaseInfoVo.getUserNo());
        resultUser.setUserName(userBaseInfoVo.getUserName());
        resultUser.setUserPhoneNum(userBaseInfoVo.getMobile());
        if (Objects.nonNull(userBaseInfoVo.getCustInfoVo())) {
            CustInfoVo custInfoVo = userBaseInfoVo.getCustInfoVo();
            resultUser.setUserName(custInfoVo.getCustName());
            resultUser.setUserIdentity(custInfoVo.getIdNo());
        }
        if (StringUtils.isBlank(resultUser.getUserIdentity())) {
            OutEmployeeVO outEmployeeVO = hrmsEmployeeFeignIntegration.queryEmployeeByMobile(resultUser.getUserPhoneNum().trim());
            if (Objects.isNull(outEmployeeVO) || !EmployeeStatusEnum.isReferralEmployee(outEmployeeVO.getEmployeeStatus())) {
                return null;
            }
            resultUser.setUserIdentity(outEmployeeVO.getIdNo());

            BasicEmployeeInfoDTO resultEmployee = new BasicEmployeeInfoDTO(resultUser);
            resultEmployee.setEmployeeNo(outEmployeeVO.getEmployeeCode());
            resultEmployee.setEmployeeName(outEmployeeVO.getName());
            resultEmployee.setEmployeeIdentity(outEmployeeVO.getIdNo());
            resultEmployee.setEmployeePhoneNum(outEmployeeVO.getMobile());

            OrganizationVO employeeOrgInfoVo = bmsOrgFeignIntegration.getOrganizationByHrOrgId(outEmployeeVO.getOrgId());
            BasicBranchDTO employeeResultBranch = new BasicBranchDTO();
            employeeResultBranch.setBranchNo(Long.valueOf(employeeOrgInfoVo.getHrOrgId()));
            employeeResultBranch.setBranchCode(employeeOrgInfoVo.getOrgCode());
            employeeResultBranch.setBranchName(employeeOrgInfoVo.getOrgName());
            resultEmployee.setBranchDTO(employeeResultBranch);

            resultEmployee.setOrderPerformanceTypeEnum(OrderPerformanceTypeEnum.EMPLOYEE_BUY);
            return resultEmployee;
        }
        EmployeeVO employeeVO = hrmsEmployeeFeignIntegration.queryEmployeeByIdNo(resultUser.getUserIdentity());
        if (Objects.nonNull(employeeVO) && EmployeeStatusEnum.isReferralEmployee(employeeVO.getEmployeeStatus())) {
            BasicEmployeeInfoDTO resultEmployee = new BasicEmployeeInfoDTO(resultUser);
            resultEmployee.setEmployeeNo(employeeVO.getEmployeeCode());
            resultEmployee.setEmployeeName(employeeVO.getName());
            resultEmployee.setEmployeeIdentity(employeeVO.getIdNo());
            resultEmployee.setEmployeePhoneNum(employeeVO.getMobile());

            OrganizationVO employeeOrgInfoVo = bmsOrgFeignIntegration.getOrganizationByHrOrgId(employeeVO.getOrgId());
            BasicBranchDTO employeeResultBranch = new BasicBranchDTO();
            employeeResultBranch.setBranchNo(Long.valueOf(employeeOrgInfoVo.getHrOrgId()));
            employeeResultBranch.setBranchCode(employeeOrgInfoVo.getOrgCode());
            employeeResultBranch.setBranchName(employeeOrgInfoVo.getOrgName());
            resultEmployee.setBranchDTO(employeeResultBranch);

            resultEmployee.setOrderPerformanceTypeEnum(OrderPerformanceTypeEnum.EMPLOYEE_BUY);
            return resultEmployee;
        }
        return resultUser;
    }

    public BasicUserInfoDTO getSuperiorEmployeeUserInfo(String idNo) {
        log.info("根据实名用户idNo查找上级员工信息,idNo:{}",idNo);
        DbcDistributionSaleUserVO upsUser = upsServiceFeignIntegration.getSaleUserInfo(idNo);
        if (Objects.isNull(upsUser)) {
            return null;
        }
        if (NumberUtils.INTEGER_ONE.equals(upsUser.getDistributionLevel())) {
            throw new MallException(String.format("用户推广中心为员工层级的用户在hrms系统中并不存在,idNo:%s", idNo));
        }
        if (!NumberUtils.INTEGER_TWO.equals(upsUser.getDistributionLevel())) {
            return null;
        }
        log.info("根据站长用户的推荐码查找其上级员工,idNo:{}",idNo);
        DbcDistributionSaleUserVO superiorUpsUser = upsServiceFeignIntegration.queryUserByMyReferenceCode(upsUser.getReferenceCode());
        if (Objects.isNull(superiorUpsUser)) {
            return null;
        }
        if (!NumberUtils.INTEGER_ONE.equals(superiorUpsUser.getDistributionLevel())) {
            return null;
        }
        BasicEmployeeInfoDTO resultEmployee = new BasicEmployeeInfoDTO();
        resultEmployee.setEmployeeNo(superiorUpsUser.getCustCode());
        resultEmployee.setEmployeeName(superiorUpsUser.getCustName());
        resultEmployee.setEmployeeIdentity(superiorUpsUser.getIdentityNum());
        resultEmployee.setEmployeePhoneNum(String.valueOf(superiorUpsUser.getCustPhoneNum()));

        resultEmployee.setEmployeeNo(superiorUpsUser.getCustCode());
        resultEmployee.setEmployeeName(superiorUpsUser.getCustName());
        resultEmployee.setEmployeeIdentity(superiorUpsUser.getIdentityNum());
        resultEmployee.setEmployeePhoneNum(String.valueOf(superiorUpsUser.getCustPhoneNum()));

        if (PerformanceBelongsConst.STORE_VIRTUAL_USER_ORG_ID.equals(superiorUpsUser.getOrgId()) || PerformanceBelongsConst.UNORGANIZED_USER_ORG_ID.equals(superiorUpsUser.getOrgId())) {
            BasicBranchDTO resultBranch = new BasicBranchDTO();
            resultBranch.setBranchNo(Long.valueOf(PerformanceBelongsConst.STORE_VIRTUAL_USER_ORG_ID));
            resultBranch.setBranchCode(PerformanceBelongsConst.STORE_VIRTUAL_USER_ORG_ID);
            resultBranch.setBranchName(PerformanceBelongsConst.DEFAULT_ORG_NAME);
            resultEmployee.setBranchDTO(resultBranch);
        } else {
            log.info("根据分销用户的orgId查询机构详细信息,idNo:{}",idNo);
            OrganizationVO superiorOrgInfoVo = bmsOrgFeignIntegration.getOrganizationByHrOrgId(Long.parseLong(superiorUpsUser.getOrgId()));
            BasicBranchDTO superiorResultBranch = new BasicBranchDTO();
            superiorResultBranch.setBranchNo(Long.valueOf(superiorOrgInfoVo.getHrOrgId()));
            superiorResultBranch.setBranchCode(superiorOrgInfoVo.getOrgCode());
            superiorResultBranch.setBranchName(superiorOrgInfoVo.getOrgName());
            resultEmployee.setBranchDTO(superiorResultBranch);
        }
        resultEmployee.setOrderPerformanceTypeEnum(OrderPerformanceTypeEnum.EMPLOYEE_BUY);
        return resultEmployee;
    }

    public BasicUserInfoDTO getLoanManager(String orderSn) {
        log.info("根据用户userNo查询其所属的信贷管护客户经理,orderSn:{}",orderSn);
        if (StringUtils.isBlank(orderSn)) {
            throw new MallException("订单号不能为空");
        }
        OrderExtendPO extendEntity = orderExtendService.getOrderExtendByOrderSn(orderSn);
        if (Objects.isNull(extendEntity)) {
            return null;
        }
        if (!ManageTypeEnum.isLoanManagerType(extendEntity.getManageType())) {
            return null;
        }
        OutEmployeeVO managerVo = hrmsEmployeeFeignIntegration.queryOnboardEmployeeByCode(extendEntity.getManager());
        if (Objects.isNull(managerVo) || !EmployeeStatusEnum.isReferralEmployee(managerVo.getEmployeeStatus())) {
            return null;
        }
        BasicManagerInfoDTO resultManager = new BasicManagerInfoDTO();
        resultManager.setUserCode(managerVo.getEmployeeCode());
        resultManager.setUserName(managerVo.getName());
        resultManager.setUserIdentity(managerVo.getIdNo());
        resultManager.setUserPhoneNum(managerVo.getMobile());

        resultManager.setManagerNo(managerVo.getEmployeeCode());
        resultManager.setManagerName(managerVo.getName());
        resultManager.setManagerIdentity(managerVo.getIdNo());
        resultManager.setManagerPhoneNum(managerVo.getMobile());
        resultManager.setManagerBusinessModelEnum(ManagerBusinessModelEnum.LOAN_MANAGER);

        OrganizationVO employeeOrgInfoVo = bmsOrgFeignIntegration.getOrganizationByHrOrgId(managerVo.getOrgId());
        BasicBranchDTO employeeResultBranch = new BasicBranchDTO();
        employeeResultBranch.setBranchNo(Long.valueOf(employeeOrgInfoVo.getHrOrgId()));
        employeeResultBranch.setBranchCode(employeeOrgInfoVo.getOrgCode());
        employeeResultBranch.setBranchName(employeeOrgInfoVo.getOrgName());
        resultManager.setBranchDTO(employeeResultBranch);

        resultManager.setOrderPerformanceTypeEnum(OrderPerformanceTypeEnum.LOAN_MANAGE);
        return resultManager;
    }

    public BasicUserInfoDTO getAgricManager(String userNo) {
        log.info("根据用户userNo查询其所属的农服管护客户经理,userNo:{}",userNo);
        if (StringUtils.isBlank(userNo)) {
            throw new MallException("客户编号不能为空");
        }
        CustRelateManagerVO bindRelateVo = upsServiceFeignIntegration.queryAgricManager(userNo);
        if (Objects.isNull(bindRelateVo) || StringUtils.isBlank(bindRelateVo.getRelateManagerCode())) {
            return null;
        }
        DbcDistributionSaleUserVO managerVo = upsServiceFeignIntegration.queryUserByMyReferenceCode(bindRelateVo.getRelateManagerCode());

        BasicManagerInfoDTO resultManager = new BasicManagerInfoDTO();
        resultManager.setUserCode(managerVo.getCustCode());
        resultManager.setUserName(managerVo.getCustName());
        resultManager.setUserIdentity(managerVo.getIdentityNum());
        resultManager.setUserPhoneNum(String.valueOf(managerVo.getCustPhoneNum()));

        resultManager.setManagerNo(managerVo.getCustCode());
        resultManager.setManagerName(managerVo.getCustName());
        resultManager.setManagerIdentity(managerVo.getIdentityNum());
        resultManager.setManagerPhoneNum(String.valueOf(managerVo.getCustPhoneNum()));
        resultManager.setManagerBusinessModelEnum(ManagerBusinessModelEnum.AGRIC_MANAGER);

        if (PerformanceBelongsConst.STORE_VIRTUAL_USER_ORG_ID.equals(managerVo.getOrgId()) || PerformanceBelongsConst.UNORGANIZED_USER_ORG_ID.equals(managerVo.getOrgId())) {
            BasicBranchDTO resultBranch = new BasicBranchDTO();
            resultBranch.setBranchNo(Long.valueOf(PerformanceBelongsConst.STORE_VIRTUAL_USER_ORG_ID));
            resultBranch.setBranchCode(PerformanceBelongsConst.STORE_VIRTUAL_USER_ORG_ID);
            resultBranch.setBranchName(PerformanceBelongsConst.DEFAULT_ORG_NAME);
            resultManager.setBranchDTO(resultBranch);
        } else {
            OrganizationVO superiorOrgInfoVo = bmsOrgFeignIntegration.getOrganizationByHrOrgId(Long.parseLong(managerVo.getOrgId()));
            BasicBranchDTO superiorResultBranch = new BasicBranchDTO();
            superiorResultBranch.setBranchNo(Long.valueOf(superiorOrgInfoVo.getHrOrgId()));
            superiorResultBranch.setBranchCode(superiorOrgInfoVo.getOrgCode());
            superiorResultBranch.setBranchName(superiorOrgInfoVo.getOrgName());
            resultManager.setBranchDTO(superiorResultBranch);
        }
        resultManager.setOrderPerformanceTypeEnum(OrderPerformanceTypeEnum.AGRIC_MANAGE);
        return resultManager;
    }

    public BasicUserInfoDTO getShareReferrer(String orderSn) {
        log.info("根据订单上的商品分享码查找推荐客户购买的用户详情,orderSn:{}",orderSn);
        if (StringUtils.isBlank(orderSn)) {
            throw new MallException("订单号不能为空");
        }
        OrderExtendPO extendEntity = orderExtendService.getOrderExtendByOrderSn(orderSn);
        if (Objects.isNull(extendEntity) || StringUtils.isEmpty(extendEntity.getShareCode())) {
            return null;
        }
        GoodsSharecodeBindUser boundUser = goodsShareFeignIntegration.getGoodsShareBoundUser(extendEntity.getShareCode());
        if (Objects.nonNull(boundUser)) {
            BasicUserInfoDTO userInfo = this.getEmployeeUserInfo(boundUser.getUserNo());
            if (Objects.nonNull(userInfo) && UserIdentityTypeEnum.COMPANY_STAFF == userInfo.getUserIdentityType()) {
                userInfo.setOrderPerformanceTypeEnum(OrderPerformanceTypeEnum.COMMODITY_SHARING);
                return userInfo;
            }
            if (Objects.nonNull(userInfo) && StringUtils.isNotBlank(userInfo.getUserIdentity())) {
                BasicUserInfoDTO superiorEmployee = this.getSuperiorEmployeeUserInfo(userInfo.getUserIdentity());
                if (Objects.nonNull(superiorEmployee)) {
                    superiorEmployee.setOrderPerformanceTypeEnum(OrderPerformanceTypeEnum.COMMODITY_SHARING);
                    return superiorEmployee;
                }
            }
        }
        UserInfoVo platformShareUser = customerIntegration.queryPlatformShareCodeRecommendUser(extendEntity.getShareCode());
        if (Objects.nonNull(platformShareUser)) {
            BasicUserInfoDTO userInfo = this.getEmployeeUserInfo(platformShareUser.getUserNo());
            if (Objects.nonNull(userInfo) && UserIdentityTypeEnum.COMPANY_STAFF == userInfo.getUserIdentityType()) {
                userInfo.setOrderPerformanceTypeEnum(OrderPerformanceTypeEnum.COMMODITY_SHARING);
                return userInfo;
            }
            if (Objects.nonNull(userInfo) && StringUtils.isNotBlank(userInfo.getUserIdentity())) {
                BasicUserInfoDTO superiorEmployee = this.getSuperiorEmployeeUserInfo(userInfo.getUserIdentity());
                if (Objects.nonNull(superiorEmployee)) {
                    superiorEmployee.setOrderPerformanceTypeEnum(OrderPerformanceTypeEnum.COMMODITY_SHARING);
                    return superiorEmployee;
                }
            }
        }
        return null;
    }

    public BasicUserInfoDTO getRegisterReferrer(String userNo) {
        log.info("根据用户userNo查找推荐其注册的用户,userNo:{}",userNo);
        SimpleUserRecommendVo registerRecommender = customerIntegration.queryRegisterRecommender(userNo);
        if (Objects.isNull(registerRecommender) || StringUtils.isBlank(registerRecommender.getRecommendUserNo())) {
            return null;
        }
        OutEmployeeVO employeeVO = hrmsEmployeeFeignIntegration.queryOnboardEmployeeByCode(registerRecommender.getRecommendUserNo());
        if (Objects.nonNull(employeeVO) && EmployeeStatusEnum.isReferralEmployee(employeeVO.getEmployeeStatus())) {
            BasicEmployeeInfoDTO resultEmployee = new BasicEmployeeInfoDTO();
            resultEmployee.setUserCode(employeeVO.getEmployeeCode());
            resultEmployee.setUserName(employeeVO.getName());
            resultEmployee.setUserIdentity(employeeVO.getIdNo());
            resultEmployee.setUserPhoneNum(employeeVO.getMobile());

            resultEmployee.setEmployeeNo(employeeVO.getEmployeeCode());
            resultEmployee.setEmployeeName(employeeVO.getName());
            resultEmployee.setEmployeeIdentity(employeeVO.getIdNo());
            resultEmployee.setEmployeePhoneNum(employeeVO.getMobile());

            OrganizationVO employeeOrgInfoVo = bmsOrgFeignIntegration.getOrganizationByHrOrgId(employeeVO.getOrgId());
            BasicBranchDTO employeeResultBranch = new BasicBranchDTO();
            employeeResultBranch.setBranchNo(Long.valueOf(employeeOrgInfoVo.getHrOrgId()));
            employeeResultBranch.setBranchCode(employeeOrgInfoVo.getOrgCode());
            employeeResultBranch.setBranchName(employeeOrgInfoVo.getOrgName());
            resultEmployee.setBranchDTO(employeeResultBranch);

            resultEmployee.setOrderPerformanceTypeEnum(OrderPerformanceTypeEnum.REGISTER_RECOMMEND);
            return resultEmployee;
        }
        BasicUserInfoDTO basicUserInfo = this.getEmployeeUserInfo(registerRecommender.getRecommendUserNo());
        if (Objects.nonNull(basicUserInfo) && UserIdentityTypeEnum.COMPANY_STAFF == basicUserInfo.getUserIdentityType()) {
            basicUserInfo.setOrderPerformanceTypeEnum(OrderPerformanceTypeEnum.REGISTER_RECOMMEND);
            return basicUserInfo;
        }
        if (Objects.nonNull(basicUserInfo) && StringUtils.isNotBlank(basicUserInfo.getUserIdentity())) {
            BasicUserInfoDTO superiorEmployee = this.getSuperiorEmployeeUserInfo(basicUserInfo.getUserIdentity());
            if (Objects.nonNull(superiorEmployee)) {
                superiorEmployee.setOrderPerformanceTypeEnum(OrderPerformanceTypeEnum.REGISTER_RECOMMEND);
                return superiorEmployee;
            }
        }
        return null;
    }

    public BasicUserInfoDTO getStoreEnterReferrer(Long storeId) {
        log.info("根据店铺id查找推荐该店铺入驻的用户,storeId:{}",storeId);
        Vendor vendor = vendorFeignIntegration.getVendorByStoreId(storeId);
        if (Objects.isNull(vendor) || StringUtils.isBlank(vendor.getRefrenceCode())) {
            return null;
        }
        DbcDistributionSaleUserVO referrerUser = upsServiceFeignIntegration.queryUserByMyReferenceCode(vendor.getRefrenceCode());
        if (Objects.isNull(referrerUser)) {
            return null;
        }
        if (!NumberUtils.INTEGER_ONE.equals(referrerUser.getDistributionLevel())) {
            return null;
        }
        BasicEmployeeInfoDTO resultEmployee = new BasicEmployeeInfoDTO();
        resultEmployee.setUserCode(referrerUser.getCustCode());
        resultEmployee.setUserName(referrerUser.getCustName());
        resultEmployee.setUserIdentity(referrerUser.getIdentityNum());
        resultEmployee.setUserPhoneNum(String.valueOf(referrerUser.getCustPhoneNum()));

        resultEmployee.setEmployeeNo(referrerUser.getCustCode());
        resultEmployee.setEmployeeName(referrerUser.getCustName());
        resultEmployee.setEmployeeIdentity(referrerUser.getIdentityNum());
        resultEmployee.setEmployeePhoneNum(String.valueOf(referrerUser.getCustPhoneNum()));

        if (PerformanceBelongsConst.STORE_VIRTUAL_USER_ORG_ID.equals(referrerUser.getOrgId()) || PerformanceBelongsConst.UNORGANIZED_USER_ORG_ID.equals(referrerUser.getOrgId())) {
            BasicBranchDTO resultBranch = new BasicBranchDTO();
            resultBranch.setBranchNo(Long.valueOf(PerformanceBelongsConst.STORE_VIRTUAL_USER_ORG_ID));
            resultBranch.setBranchCode(PerformanceBelongsConst.STORE_VIRTUAL_USER_ORG_ID);
            resultBranch.setBranchName(PerformanceBelongsConst.DEFAULT_ORG_NAME);
            resultEmployee.setBranchDTO(resultBranch);
        } else {
            OrganizationVO superiorOrgInfoVo = bmsOrgFeignIntegration.getOrganizationByHrOrgId(Long.parseLong(referrerUser.getOrgId()));
            BasicBranchDTO superiorResultBranch = new BasicBranchDTO();
            superiorResultBranch.setBranchNo(Long.valueOf(superiorOrgInfoVo.getHrOrgId()));
            superiorResultBranch.setBranchCode(superiorOrgInfoVo.getOrgCode());
            superiorResultBranch.setBranchName(superiorOrgInfoVo.getOrgName());
            resultEmployee.setBranchDTO(superiorResultBranch);
        }

        resultEmployee.setOrderPerformanceTypeEnum(OrderPerformanceTypeEnum.SETTLED_RECOMMEND);
        return resultEmployee;
    }

    public BasicUserInfoDTO getStoreLocateVirtualUser(Long storeId) {
        log.info("根据店铺id查找店铺所在地的业绩归属信息,storeId:{}",storeId);
        Vendor vendor = vendorFeignIntegration.getVendorByStoreId(storeId);
        if (Objects.isNull(vendor) || StringUtils.isBlank(vendor.getRefrenceBranchCode())) {
            return null;
        }
        OrganizationVO branchOrgInfo = bmsOrgFeignIntegration.getOrganizationByOrgCode(vendor.getRefrenceBranchCode());
        if (Objects.isNull(branchOrgInfo)) {
            return null;
        }
        BasicUserInfoDTO resultVirtualUser = new BasicUserInfoDTO();
        resultVirtualUser.setUserCode(PerformanceBelongsConst.DEFAULT_EMPLOYEE_CODE);
        resultVirtualUser.setUserName(PerformanceBelongsConst.DEFAULT_EMPLOYEE_NAME);

        BasicBranchDTO referenceBranch = new BasicBranchDTO();
        referenceBranch.setBranchNo(Long.valueOf(branchOrgInfo.getHrOrgId()));
        referenceBranch.setBranchCode(branchOrgInfo.getOrgCode());
        referenceBranch.setBranchName(branchOrgInfo.getOrgName());
        resultVirtualUser.setBranchDTO(referenceBranch);

        resultVirtualUser.setOrderPerformanceTypeEnum(OrderPerformanceTypeEnum.STORE_LOCATION);
        return resultVirtualUser;
    }

    public BasicUserInfoDTO packFollowRegisterGetUser(OrderPO orderPO) {
        log.info("开始走用户注册推荐后统一流程进行业绩归属绑定,orderSn:{}",orderPO.getOrderSn());
        BasicUserInfoDTO basicUserDTO = this.getRegisterReferrer(orderPO.getUserNo());
        if (Objects.nonNull(basicUserDTO)) {
            return basicUserDTO;
        }
        basicUserDTO = this.getStoreEnterReferrer(orderPO.getStoreId());
        if (Objects.nonNull(basicUserDTO)) {
            return basicUserDTO;
        }
        basicUserDTO = this.getStoreLocateVirtualUser(orderPO.getStoreId());
        if (Objects.nonNull(basicUserDTO)) {
            return basicUserDTO;
        }
        return this.getDefaultUser(orderPO.getOrderSn());
    }

    public BasicUserInfoDTO getDefaultUser(String orderSn) {
        log.info("订单开始绑定默认类型的业绩归属信息,orderSn:{}",orderSn);
        BasicUserInfoDTO resultVirtualUser = new BasicUserInfoDTO();
        resultVirtualUser.setUserCode(PerformanceBelongsConst.DEFAULT_EMPLOYEE_CODE);
        resultVirtualUser.setUserName(PerformanceBelongsConst.DEFAULT_EMPLOYEE_NAME);

        BasicBranchDTO resultBranch = new BasicBranchDTO();
        resultBranch.setBranchNo(Long.valueOf(PerformanceBelongsConst.STORE_VIRTUAL_USER_ORG_ID));
        resultBranch.setBranchCode(PerformanceBelongsConst.STORE_VIRTUAL_USER_ORG_ID);
        resultBranch.setBranchName(PerformanceBelongsConst.DEFAULT_ORG_NAME);
        resultVirtualUser.setBranchDTO(resultBranch);

        resultVirtualUser.setOrderPerformanceTypeEnum(OrderPerformanceTypeEnum.DEFAULT);
        return resultVirtualUser;
    }
    
    @Override
    public boolean verifyBoundBelongerByOrderSn(String orderSn) {
        LambdaQueryWrapper<OrderPerformanceBelongsPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderPerformanceBelongsPO::getOrderSn,orderSn);
        List<OrderPerformanceBelongsPO> entityList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(entityList)) {
            log.info("订单未绑定业绩归属信息,orderSn:{}",orderSn);
            return false;
        }
        if (entityList.size() > 1) {
            log.error("订单已绑定多个业绩归属信息,orderSn:{}",orderSn);
            throw new MSException(String.valueOf(ErrorCodeEnum.S.DATA_UNIQUE_CONFLICT.getCode()),
                    String.format("订单已绑定多个业绩归属信息,orderSn:%s",orderSn));
        }
        OrderPerformanceBelongsPO entity = entityList.get(0);
        return OrderPerformanceBindStateEnum.isAvailable(entity.getBindStateCode());
    }

    @Override
    public OrderPerformanceBelongsPO getEntityByOrderSn(String orderSn) {
        LambdaQueryWrapper<OrderPerformanceBelongsPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderPerformanceBelongsPO::getOrderSn,orderSn);
        queryWrapper.in(OrderPerformanceBelongsPO::getBindStateCode, Stream.of(OrderPerformanceBindStateEnum.EFFECT.getCode(), OrderPerformanceBindStateEnum.ADJUST.getCode()).collect(Collectors.toList()));
        return this.getOne(queryWrapper);
    }

    @Override
    public boolean generatePerformanceBelongs(BasicUserInfoDTO basicUserDTO, OrderPO orderPO) {
        LambdaQueryWrapper<OrderPerformanceBelongsPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderPerformanceBelongsPO::getOrderSn,orderPO.getOrderSn());
        OrderPerformanceBelongsPO entity = this.getOne(queryWrapper);
        if (Objects.nonNull(entity)) {
            log.info("已存在指定订单号对应的绑定业绩归属记录,orderSn:{}",orderPO.getOrderSn());
            return true;
        }

        OrderPerformanceBelongsPO insertPO = new OrderPerformanceBelongsPO();
        insertPO.setOrderSn(orderPO.getOrderSn());

        OrderPerformanceTypeEnum performanceTypeEnum = basicUserDTO.getOrderPerformanceTypeEnum();
        insertPO.setPerformanceType(performanceTypeEnum.getCode());
        insertPO.setPerformanceDesc(performanceTypeEnum.getDesc());

        insertPO.setBelongerName(basicUserDTO.getBelongerName());
        insertPO.setBelongerEmployeeNo(basicUserDTO.getBelongerEmployeeNo());

        insertPO.setEmployeeBranchOrgId(basicUserDTO.getBranchDTO().getBranchNo());
        insertPO.setEmployeeBranchCode(basicUserDTO.getBranchDTO().getBranchCode());
        insertPO.setEmployeeBranchName(basicUserDTO.getBranchDTO().getBranchName());

        insertPO.setBindStateCode(OrderPerformanceBindStateEnum.INIT.getCode());
        insertPO.setBindStateDesc(OrderPerformanceBindStateEnum.INIT.getDesc());
        if (!this.save(insertPO)) {
            log.error("新增订单绑定业绩归属信息失败,orderSn:{}",orderPO.getOrderSn());
            return false;
        }
        return true;
    }

    @Override
    public OrderPerformanceBelongsPO modifyPerformanceBelongs(BasicUserInfoDTO basicUserDTO, OrderPO orderPO, OrderPerformanceBindStateEnum bindStateEnum) {
        LambdaQueryWrapper<OrderPerformanceBelongsPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderPerformanceBelongsPO::getOrderSn,orderPO.getOrderSn());
        OrderPerformanceBelongsPO entity = this.getOne(queryWrapper);
        if (Objects.isNull(entity)) {
            log.warn("订单未绑定业绩归属信息,orderSn:{}",orderPO.getOrderSn());
            return null;
        }
        if (!OrderPerformanceBindStateEnum.canConvertible(entity.getBindStateCode(),bindStateEnum.getCode())) {
            log.info("订单的业绩归属绑定状态不能转换,orderSn:{}",orderPO.getOrderSn());
            return entity;
        }

        OrderPerformanceBelongsPO updatePO = new OrderPerformanceBelongsPO();
        updatePO.setId(entity.getId());
        updatePO.setOrderSn(entity.getOrderSn());

        OrderPerformanceTypeEnum performanceTypeEnum = basicUserDTO.getOrderPerformanceTypeEnum();
        updatePO.setPerformanceType(performanceTypeEnum.getCode());
        updatePO.setPerformanceDesc(performanceTypeEnum.getDesc());

        updatePO.setBelongerName(basicUserDTO.getBelongerName());
        updatePO.setBelongerEmployeeNo(basicUserDTO.getBelongerEmployeeNo());

        updatePO.setEmployeeBranchOrgId(basicUserDTO.getBranchDTO().getBranchNo());
        updatePO.setEmployeeBranchCode(basicUserDTO.getBranchDTO().getBranchCode());
        updatePO.setEmployeeBranchName(basicUserDTO.getBranchDTO().getBranchName());

        updatePO.setBindStateCode(bindStateEnum.getCode());
        updatePO.setBindStateDesc(bindStateEnum.getDesc());
        updatePO.setEffectTime(LocalDateTime.now());

        if (!this.updateById(updatePO)) {
            log.error("保存订单绑定业绩归属信息失败,orderSn:{}",orderPO.getOrderSn());
            return null;
        }
        return updatePO;
    }

    @Override
    public OrderPerformanceBelongsPO modifyPerformanceBelongsInfo(OrderPerformanceBelongsDTO orderPerformanceBelongsDTO) {
        LambdaQueryWrapper<OrderPerformanceBelongsPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderPerformanceBelongsPO::getOrderSn,orderPerformanceBelongsDTO.getOrderSn());
        OrderPerformanceBelongsPO entity = this.getOne(queryWrapper);
        if (Objects.isNull(entity)) {
            log.warn("订单未绑定业绩归属信息,orderSn:{}",orderPerformanceBelongsDTO.getOrderSn());
            return null;
        }
        OrderPerformanceBelongsPO updatePO = new OrderPerformanceBelongsPO();
        updatePO.setId(entity.getId());
        updatePO.setOrderSn(entity.getOrderSn());

        OrderPerformanceTypeEnum performanceTypeEnum = OrderPerformanceTypeEnum.parseEnum(orderPerformanceBelongsDTO.getPerformanceType());
        if (performanceTypeEnum != null){
            updatePO.setPerformanceType(performanceTypeEnum.getCode());
            updatePO.setPerformanceDesc(performanceTypeEnum.getDesc());
        }
        updatePO.setBelongerName(orderPerformanceBelongsDTO.getBelongerName());
        updatePO.setBelongerEmployeeNo(orderPerformanceBelongsDTO.getBelongerEmployeeNo());
        updatePO.setEmployeeBranchOrgId(orderPerformanceBelongsDTO.getEmployeeBranchOrgId());
        updatePO.setEmployeeBranchCode(orderPerformanceBelongsDTO.getEmployeeBranchCode());
        updatePO.setEmployeeBranchName(orderPerformanceBelongsDTO.getEmployeeBranchName());
        if (!this.updateById(updatePO)) {
            log.error("保存订单绑定业绩归属信息失败,orderSn:{}",orderPerformanceBelongsDTO.getOrderSn());
            return null;
        }
        return updatePO;
    }

    private void sendPerformanceBoundNotifyMsg(OrderPerformanceBelongsPO performanceBelongsPO) {
        if (!OrderPerformanceBindStateEnum.isEffect(performanceBelongsPO.getBindStateCode())) {
            log.info("订单业绩归属绑定状态未生效,不发送mq消息,orderSn:{}",performanceBelongsPO.getOrderSn());
            return;
        }
        OrderPerformanceNotifyDTO notifyDTO = new OrderPerformanceNotifyDTO(performanceBelongsPO);

        Long eventId = commonMqEventService.saveEvent(notifyDTO, StringUtils.EMPTY, PerformanceBelongsConst.ORDER_PERFORMANCE_BOUND_FANOUT_EXCHANGE);
        rabbitMQUtils.sendByEventId(eventId);
    }

    private void trackPerformanceAdjust(OrderPerformanceBelongsPO beforeEntity,OrderPerformanceBelongsPO afterEntity, CommissionAffiliationUpdateDTO notifyDTO) {
        JwtUserInfo userInfo = JwtUserInfo.builder().jobNumber(notifyDTO.getOperatorNo()).userName(notifyDTO.getOperatorName()).account(notifyDTO.getOperatorMobile()).build();

        List<EventTraceDTO> eventTraceList = new ArrayList<>();
        EventTraceDTO tempDTO = new EventTraceDTO();
        tempDTO.setNodeName("业绩归属人变更");
        tempDTO.setEventChannel("WEB");
        tempDTO.setTargetDomain("OrderPerformanceBelongsPO::Id");
        tempDTO.setOperateTime(LocalDateTime.now());
        tempDTO.setOperateType(OperateTypeEnum.UPDATE);
        tempDTO.setOperateRemark("佣金发生转移，业绩归属人同步变更");
        if (!beforeEntity.getPerformanceType().equals(afterEntity.getPerformanceType())) {
            eventTraceList.add(eventTraceService.remakeEventTraceDTO(tempDTO, String.valueOf(beforeEntity.getId()),
                    "业绩类型编码", String.valueOf(beforeEntity.getPerformanceType()), String.valueOf(afterEntity.getPerformanceType())));
        }
        if (!StringUtils.equals(beforeEntity.getPerformanceDesc(), afterEntity.getPerformanceDesc())) {
            eventTraceList.add(eventTraceService.remakeEventTraceDTO(tempDTO, String.valueOf(beforeEntity.getId()),
                    "业绩类型描述", beforeEntity.getPerformanceDesc(), afterEntity.getPerformanceDesc()));
        }
        if (!StringUtils.equals(beforeEntity.getBelongerName(), afterEntity.getBelongerName())) {
            eventTraceList.add(eventTraceService.remakeEventTraceDTO(tempDTO, String.valueOf(beforeEntity.getId()),
                    "归属人姓名", beforeEntity.getBelongerName(), afterEntity.getBelongerName()));
        }
        if (!StringUtils.equals(beforeEntity.getBelongerEmployeeNo(), afterEntity.getBelongerEmployeeNo())) {
            eventTraceList.add(eventTraceService.remakeEventTraceDTO(tempDTO, String.valueOf(beforeEntity.getId()),
                    "归属人工号", beforeEntity.getBelongerEmployeeNo(), afterEntity.getBelongerEmployeeNo()));
        }
        if (!StringUtils.equals(beforeEntity.getEmployeeBranchName(), afterEntity.getEmployeeBranchName())) {
            eventTraceList.add(eventTraceService.remakeEventTraceDTO(tempDTO, String.valueOf(beforeEntity.getId()),
                    "归属人分支名称", beforeEntity.getEmployeeBranchName(), afterEntity.getEmployeeBranchName()));
        }
        if (!beforeEntity.getEmployeeBranchOrgId().equals(afterEntity.getEmployeeBranchOrgId())) {
            eventTraceList.add(eventTraceService.remakeEventTraceDTO(tempDTO, String.valueOf(beforeEntity.getId()),
                    "归属人分支ID", String.valueOf(beforeEntity.getEmployeeBranchOrgId()), String.valueOf(afterEntity.getEmployeeBranchOrgId())));
        }
        if (!StringUtils.equals(beforeEntity.getEmployeeBranchCode(), afterEntity.getEmployeeBranchCode())) {
            eventTraceList.add(eventTraceService.remakeEventTraceDTO(tempDTO, String.valueOf(beforeEntity.getId()),
                    "归属人分支编码", beforeEntity.getEmployeeBranchCode(), afterEntity.getEmployeeBranchCode()));
        }
        if (!beforeEntity.getBindStateCode().equals(afterEntity.getBindStateCode())) {
            eventTraceList.add(eventTraceService.remakeEventTraceDTO(tempDTO, String.valueOf(beforeEntity.getId()),
                    "绑定状态编码", String.valueOf(beforeEntity.getBindStateCode()), String.valueOf(afterEntity.getBindStateCode())));
        }
        if (!StringUtils.equals(beforeEntity.getBindStateDesc(), afterEntity.getBindStateDesc())) {
            eventTraceList.add(eventTraceService.remakeEventTraceDTO(tempDTO, String.valueOf(beforeEntity.getId()),
                    "绑定状态描述", beforeEntity.getBindStateDesc(), afterEntity.getBindStateDesc()));
        }
        if (!beforeEntity.getEffectTime().isEqual(afterEntity.getEffectTime())) {
            eventTraceList.add(eventTraceService.remakeEventTraceDTO(tempDTO, String.valueOf(beforeEntity.getId()),
                    "业绩绑定时间",beforeEntity.getEffectTime().format(EventTraceServiceImpl.FORMATTER) , afterEntity.getEffectTime().format(EventTraceServiceImpl.FORMATTER)));
        }
        eventTraceService.batchTrack(userInfo, eventTraceList, OrderConst.RESULT_CODE_SUCCESS);
    }

    @Override
    public List<EventTraceVO> listAdjustPerformanceHistory(String orderSn) {
        LambdaQueryWrapper<OrderPerformanceBelongsPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderPerformanceBelongsPO::getOrderSn,orderSn);
        OrderPerformanceBelongsPO entity = this.getOne(queryWrapper);
        if (Objects.isNull(entity)) {
            log.info("未找到指定订单的已绑定业绩归属信息,orderSn:{}",orderSn);
            return Collections.emptyList();
        }
        EventTraceDTO eventTraceDTO = new EventTraceDTO();
        eventTraceDTO.setTargetDomain("OrderPerformanceBelongsPO::Id");
        eventTraceDTO.setTargetObject(String.valueOf(entity.getId()));
        return eventTraceService.listHistoryDiffContent(eventTraceDTO);
    }

    @Override
    public OrderPerformanceBelongsVO getVoByOrderSn(String orderSn) {
        LambdaQueryWrapper<OrderPerformanceBelongsPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderPerformanceBelongsPO::getOrderSn,orderSn);
        OrderPerformanceBelongsPO entity = this.getOne(queryWrapper);
        if (Objects.isNull(entity)) {
            log.info("未找到指定订单的已绑定业绩归属信息,orderSn:{}",orderSn);
            return null;
        }
        return this.entityConvertVO(entity);
    }
}
