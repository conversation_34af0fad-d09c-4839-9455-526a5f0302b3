package com.cfpamf.ms.mallorder.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.bms.facade.dto.ElementTreeNodeDTO;
import com.cfpamf.ms.bms.facade.vo.DictionaryItemVO;
import com.cfpamf.ms.bms.facade.vo.OrganizationBaseVO;
import com.cfpamf.ms.bms.facade.vo.UserListBaseVO;
import com.cfpamf.ms.bms.facade.vo.UserListVO;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.cfpamf.ms.mallorder.dto.EmployeePrivilegeConditionDTO;
import com.cfpamf.ms.mallorder.integration.facade.BmsIntegration;
import com.cfpamf.ms.mallorder.query.EmployeePrivilegeQuery;
import com.cfpamf.ms.mallorder.service.EmployeePrivilegeService;

@Service
public class EmployeePrivilegeServiceImpl implements EmployeePrivilegeService {

	@Autowired
	private BmsIntegration bmsIntegration;

	public EmployeePrivilegeConditionDTO authByBmsDictionary(EmployeePrivilegeQuery query) {
		EmployeePrivilegeConditionDTO privilegeConditionDTO = this.getBranchAndUserList(query);
		// 没有分支才设置人员权限
		if (CollectionUtils.isNotEmpty(privilegeConditionDTO.getOrgCodeList())) {
			privilegeConditionDTO.setUserList(null);
		}
		return privilegeConditionDTO;
	}

	EmployeePrivilegeConditionDTO getBranchAndUserList(EmployeePrivilegeQuery query) {
		EmployeePrivilegeConditionDTO branchAndUserVo = new EmployeePrivilegeConditionDTO();
		if (StringUtils.isEmpty(query.getJobNumber()) || StringUtils.isEmpty(query.getOrgCode())) {
			return branchAndUserVo;
		}
		// 获取员工岗位基本信息
		List<UserListBaseVO> users = bmsIntegration.getEmployeeInfo(query.getJobNumber());
		List<UserListBaseVO> postUserList = users.stream().filter(o -> o.getOrgCode().equals(query.getOrgCode()))
				.collect(Collectors.toList());
		// 校验员工岗位在组织架构上是否匹配
		if (CollectionUtils.isEmpty(postUserList)) {
			throw new MSBizNormalException(ErrorCodeEnum.S.AUTH_EXCEPTION.getCode() + "",
					ErrorCodeEnum.S.AUTH_EXCEPTION.getMsg() + "：员工 " + query.getJobNumber() + " 在组织 "
							+ query.getOrgCode() + " 无岗位");
		}
		// BMS字典，获取岗位配置表
		List<DictionaryItemVO> dictList = bmsIntegration.getDictionaryItemsByTypeCode(
				query.getPrivilegeBmsEnum().getSystemId(), query.getPrivilegeBmsEnum().getBmsCode());
		if (CollectionUtils.isEmpty(dictList)) {
			throw new MSBizNormalException(ErrorCodeEnum.S.AUTH_EXCEPTION.getCode() + "",
					ErrorCodeEnum.S.AUTH_EXCEPTION.getMsg() + "：通用岗位权限数据字典未配置");
		}

		List<String> hrPostIds = new LinkedList<>();
		for (UserListBaseVO userVo : postUserList) {
			Integer hrOrgId = userVo.getHrOrgId();
			String hrPostId = userVo.getHrPostId().toString();
			hrPostIds.add(hrPostId);

			if (dictList.get(0).getItemCode().contains(hrPostId)) {
				// 总部
				return branchAndUserVo;
			} else if (dictList.get(1).getItemCode().contains(hrPostId)) {
				// 事业部
				throw new MSBizNormalException(ErrorCodeEnum.S.AUTH_EXCEPTION.getCode() + "",
						ErrorCodeEnum.S.AUTH_EXCEPTION.getMsg() + " 事业部已过期 员工 " + query.getJobNumber() + " 在组织 "
								+ query.getOrgCode() + " 岗位 " + String.join(",", hrPostIds) + " 无数据权限");
			} else if (dictList.get(2).getItemCode().contains(hrPostId)) {
				// 区域
				branchAndUserVo.getOrgCodeList().addAll(listCodes(query.getOrgCode()));
			} else if (dictList.get(3).getItemCode().contains(hrPostId)) {
				// 片区
				// 获取片区所属区域
				OrganizationBaseVO area = bmsIntegration.getAreaByHrOrgId(hrOrgId);
				// 获取片区下的所有分支
				List<OrganizationBaseVO> branchList = bmsIntegration.listBranchesByHrParentId(hrOrgId);
				List<Integer> branchOrgIdList = branchList.stream().map(OrganizationBaseVO::getHrOrgId)
						.collect(Collectors.toList());
				branchAndUserVo.getOrgCodeList().addAll(getBranchList(
						area == null ? null : Collections.singletonList(area.getHrOrgId()), branchOrgIdList));

			} else if (dictList.get(4).getItemCode().contains(hrPostId)) {
				// 分支
				branchAndUserVo.getOrgCodeList().add(query.getOrgCode());
			} else if (dictList.get(5).getItemCode().contains(hrPostId)) {
				// 督导
				List<UserListVO> userList = bmsIntegration.listLoanOfficerUserListVOBySupervisor(query.getJobNumber());
				List<String> userCodeList = userList.stream().filter(o -> o.getOrgCode().equals(query.getOrgCode()))
						.map(UserListVO::getJobNumber).collect(Collectors.toList());
				if (userCodeList == null) {
					userCodeList = new ArrayList<>();
				}

				userCodeList.add(query.getJobNumber());

				branchAndUserVo.getUserList().addAll(userCodeList);

			} else if (dictList.get(6).getItemCode().contains(hrPostId)) {
				// 信贷员=客户经理
				branchAndUserVo.getUserList().addAll(Collections.singletonList(query.getJobNumber()));
			}
		}
		if (CollectionUtils.isEmpty(branchAndUserVo.getUserList())
				&& CollectionUtils.isEmpty(branchAndUserVo.getOrgCodeList())) {
			throw new MSBizNormalException(ErrorCodeEnum.S.AUTH_EXCEPTION.getCode() + "",
					ErrorCodeEnum.S.AUTH_EXCEPTION.getMsg() + "：员工 " + query.getJobNumber() + " 在组织 "
							+ query.getOrgCode() + " 岗位 " + String.join(",", hrPostIds) + " 无数据权限");
		}
		return branchAndUserVo;
	}

	private List<String> listCodes(String orgCode) {
		List<String> list = new LinkedList<>();
		List<ElementTreeNodeDTO> areas = bmsIntegration.listRootHeadBuAreaBranchBySuperOrgCode(orgCode);
		if (areas != null) {
			// 区域
			areas.forEach(o -> {
				if (o.getChildren() == null) {
					list.add(o.getOrgCode());
				} else {
					// 片区
					o.getChildren().forEach(c -> {
						if (c.getChildren() == null) {
							list.add(c.getOrgCode());
						} else {
							if (c.getChildren() != null) {
								// 分支
								c.getChildren().forEach(cc -> list.add(cc.getOrgCode()));
							}
						}
					});
				}
			});
		}
		return list;
	}

	private List<String> getBranchList(List<Integer> areaHrOrgIds, List<Integer> branchHrOrgIds) {
		List<String> branchVos = new LinkedList<>();
		List<OrganizationBaseVO> areaList = bmsIntegration.listAreasByAreaHrOrgIds(areaHrOrgIds);
		List<OrganizationBaseVO> branchList = bmsIntegration.listBranchesByHrOrgIds(branchHrOrgIds);
		if (areaList != null && areaList.size() > 0) {
			for (OrganizationBaseVO a : areaList) {
				for (OrganizationBaseVO b : branchList) {
					if (b.getTreePath().contains(a.getTreePath())) {
						branchVos.add(b.getOrgCode());
					}
				}
			}
		}
		return branchVos;
	}

}
