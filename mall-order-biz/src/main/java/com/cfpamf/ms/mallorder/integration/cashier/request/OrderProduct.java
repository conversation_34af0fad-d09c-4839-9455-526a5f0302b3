package com.cfpamf.ms.mallorder.integration.cashier.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 子单对应商品信息表
 * </p>
 * <AUTHOR>
 */
@Data
public class OrderProduct {

    @ApiModelProperty("商品名称")
    private String goodsId;

    @ApiModelProperty("产品名称")
    private String goodsName;

    @ApiModelProperty("产品id--sku")
    private String productId;

    @ApiModelProperty("商品数量")
    private Integer productNum;

    @ApiModelProperty("是否分账")
    private Integer productType;

    @ApiModelProperty("货品单价")
    private BigDecimal amount;

    @ApiModelProperty("商品一级类目id")
    private String firstItemCategoryId;

    @ApiModelProperty("商品一级类目")
    private String firstItemCategoryName;

    @ApiModelProperty("商品二级类目id")
    private String secondItemCategoryId;

    @ApiModelProperty("商品二级类目")
    private String secondItemCategoryName;

    @ApiModelProperty("商品三级类目id")
    private String threeItemCategoryId;

    @ApiModelProperty("商品三级类目")
    private String threeItemCategoryName;

    @ApiModelProperty("全类目id")
    private String itemCategoryId;

    @ApiModelProperty("全类目路径")
    private String itemCategoryTree;

    @ApiModelProperty("其他信息 json格式")
    private String extAttrs;
}
