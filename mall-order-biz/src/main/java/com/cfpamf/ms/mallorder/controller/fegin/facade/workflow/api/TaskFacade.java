package com.cfpamf.ms.mallorder.controller.fegin.facade.workflow.api;

import com.cfpamf.ms.mallorder.controller.fegin.facade.workflow.dto.TaskCompleteRequestDto;
import com.cfpamf.ms.mallorder.controller.fegin.facade.workflow.util.MyPage;
import com.cfpamf.ms.mallorder.controller.fegin.facade.workflow.util.ResultData;
import com.cfpamf.ms.mallorder.controller.fegin.facade.workflow.vo.TaskVo;
import com.cfpamf.ms.mallorder.controller.fegin.facade.workflow.vo.WorkflowTaskDetailVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;

@FeignClient(name = "flow-manager", url = "${flow-manager.url:}", contextId = "flow-manager-task")
public interface TaskFacade {

    @ApiOperation(value = "查询待办列表", notes = "/workflow/task/getTodoList")
    @GetMapping("/workflow/task/getTodoList")
    ResultData<MyPage<WorkflowTaskDetailVO>> getTodoList(@RequestParam("userId") String userId,
                                                         @RequestParam("userIdType") String userIdType,
                                                         @RequestParam("processDefKey") String processDefKey,
                                                         @RequestParam("applicantName") String applicantName,
                                                         @RequestParam("startTime") String startTime,
                                                         @RequestParam("endTime") String endTime,
                                                         @RequestParam("processDefKeyList") List<String> processDefKeyList,
                                                         @RequestParam("pageNumber") Integer pageNumber,
                                                         @RequestParam("pageSize") Integer pageSize);

    @ApiOperation(value = "查询已办列表", notes = "/workflow/task/getDoneList")
    @GetMapping("/workflow/task/getDoneList")
    ResultData<MyPage<WorkflowTaskDetailVO>> getDoneList(@RequestParam("userId") String userId,
                                                         @RequestParam("userIdType") String userIdType,
                                                         @RequestParam("processDefKey") String processDefKey,
                                                         @RequestParam("applicantName") String applicantName,
                                                         @RequestParam("startTime") String startTime,
                                                         @RequestParam("endTime") String endTime,
                                                         @RequestParam("processDefKeyList") List<String> processDefKeyList,
                                                         @RequestParam("pageNumber") Integer pageNumber,
                                                         @RequestParam("pageSize") Integer pageSize);


    @ApiOperation(value = "查询列表", notes = "/workflow/task/getSubmittedList")
    @GetMapping("/workflow/task/getSubmittedList")
    ResultData<MyPage<WorkflowTaskDetailVO>> getSubmittedList(@RequestParam("userId") String userId,
                                                         @RequestParam("userIdType") String userIdType,
                                                         @RequestParam("processDefKey") String processDefKey,
                                                         @RequestParam("applicantName") String applicantName,
                                                         @RequestParam("startTime") String startTime,
                                                         @RequestParam("endTime") String endTime,
                                                         @RequestParam("processDefKeyList") List<String> processDefKeyList,
                                                         @RequestParam("pageNumber") Integer pageNumber,
                                                         @RequestParam("pageSize") Integer pageSize);

    @ApiOperation(value = "审批通过任务")
    @PostMapping("/workflow/task/complete")
    ResultData<Void> completeTask(@RequestBody @Valid TaskCompleteRequestDto dto);

    @ApiOperation(value = "审批驳回任务")
    @PostMapping("/workflow/task/reject")
    ResultData<Void> rejectTask(@RequestBody @Valid TaskCompleteRequestDto dto);

    @GetMapping("/workflow/task/getCurrentTask/userId")
    ResultData<TaskVo> getCurrentTaskByUserId(@RequestParam("businessKey") String businessKey, @RequestParam("userId") String userId);

    @ApiOperation(value = "通过processInstanceId获取当前任务")
    @GetMapping("/workflow/getCurrentTask/processInstanceId")
    ResultData<TaskVo> getCurrentTaskByInstanceId(@RequestParam("businessKey") String businessKey, @RequestParam("processInstanceId") String processInstanceId);

    @PostMapping("/workflow/task/addComment")
    ResultData<Void> addComment(@RequestParam("taskId") String taskId, @RequestParam("comment") String comment, @RequestParam("userId") String userId);

    @ApiOperation(value = "查询流程执行轨迹列表", notes = "/workflow/task/getTrackList")
    @GetMapping("/workflow/task/getTrackList")
    ResultData<MyPage<WorkflowTaskDetailVO>> getTrackList(@RequestParam("procDefKey") String procDefkey,
                                                          @RequestParam("bizId") String bizId,
                                                          @RequestParam("systemId") Integer systemId,
                                                          @RequestParam("pageNumber") Integer pageNumber,
                                                          @RequestParam("pageSize") Integer pageSize);

    @ApiOperation(value = "查询流程预览接口", notes = "/workflow/task/getTrackList")
    @GetMapping("/workflow/task/getPreviewList")
    ResultData<MyPage<WorkflowTaskDetailVO>> getPreviewList(@RequestParam("processId")String processId);

    @ApiOperation(value = "审批驳回任务")
    @PostMapping("/workflow/processInstance/suspend")
    ResultData<Void> suspend(@RequestBody @Valid TaskCompleteRequestDto dto);
}
