package com.cfpamf.ms.mallorder.controller.fegin.facade;


import com.cdfinance.hrms.facade.request.EmployeeMobileBatchRequest;
import com.cdfinance.hrms.facade.request.outer.*;
import com.cdfinance.hrms.facade.vo.*;
import com.cfpamf.common.ms.result.Result;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "hrms-biz", url = "${hrms-biz.url}")
public interface EmployeeFacade {
    /**
     * 查找所有在职员工信息
     * @return
     */
    @PostMapping("/hrms/employee/mdm/queryOnboardEmployees")
    Result<List<OnBoardEmployeeVO>> queryOnboardEmployees();

    /**
     * 根据工号查询员工信息
     * @param employeeCode
     * @return
     */
    @PostMapping("/hrms/employee/mdm/queryOnboardEmployee")
    Result<OnBoardEmployeeVO> queryOnboardEmployees(@RequestParam("employeeCode")String employeeCode);

    /**
     * 根据员工ID查询员信息
     * @param employeeId
     * @return
     */
    @PostMapping("/hrms/employee/mdm/queryOnboardEmployeeById")
    Result<OnBoardEmployeeVO> queryOnboardEmployeeById(@RequestParam("employeeId")Long employeeId);

    /**
     * 根据督导工号查询管辖客户经理信息
     * @param employeeCode
     * @return
     */
    @PostMapping("/hrms/employee/mdm/queryLoanOfcrBySpvrCode")
    Result<List<OnBoardEmployeeVO>> queryLoanOfcrBySpvrCode(@RequestParam("employeeCode")String employeeCode);

    /**
     * 离职保险交接人
     * @param employeeId
     * @return
     */
    @PostMapping("/hrms/employee/mdm/queryInsTake")
    Result<InsuranceTakeVO> queryInsuranceTakeInfo(@RequestParam("employeeId")Long employeeId);

    /**
     * 创建电商外部用户
     * @param merchantName
     * @return
     */
    @PostMapping("/hrms/employee/mdm/createMallExternal")
    Result<String> createMallExternal(@RequestParam("merchantName")String merchantName);

    /**
     * 根据员工ID查询员信息
     * @param idNo
     * @return
     */
    @GetMapping("/hrms/employee/getByIdNo")
     Result<EmployeeVO> getByIdNo(@RequestParam("idNo") String idNo);

    /**
     * 根据工号集合获得员工基本信息集合
     * @param request
     * @return
     */
    @PostMapping("/hrms/employee/mdm/queryEmployeeMobiles")
    Result<List<OutEmployeeMobileVO>> queryEmployeeMobiles(@RequestBody EmployeeMobileBatchRequest request);

    /**
     * 根据手机号集合获得员工基本信息集合
     * @param request
     * @return
     */
    @PostMapping("/hrms/employee/mdm/queryEmployeeDetailByMobiles")
    Result<List<OutEmployeeDetailVO>> queryEmployeeDetailByMobiles(@RequestBody QueryEmpDetailsByMobileRequest request);

    /**
     * 根据工号集合获得员工基本信息集合
     * @param request
     * @return
     */
    @PostMapping("/hrms/employee/mdm/queryEmployeeDetailByCodes")
    Result<List<OutEmployeeDetailVO>> queryEmployeeDetailByCodes(@RequestBody QueryEmpDetailsByCodeRequest request);

    /**
     * 提供分销方 -- 根据手机号或身份证号，返回员工所属分支信息
     * 入参：手机号/身份证号；出参：工号、姓名、所属区域、所属片区、所属分支；
     * @param request
     * @return
     */
    @PostMapping("/hrms/employee/mdm/queryEmployeeBranchDetailByMobilesOrIdNo")
    Result<List<OutEmployeeBranchDetailVO>> queryEmployeeBranchDetailByMobilesOrIdNo(@RequestBody QueryEmpBranchDetailsByMobileRequest request);

    /**
     * 提供乡助方 -- 基于工号返回该员工是否离职的批量查询接口
     * 入参：工号；出参：工号、姓名、手机号,是否离职；
     * @param request
     * @return
     */
    @PostMapping("/hrms/employee/mdm/queryEmployeeLeaveDetailByCode")
    Result<List<OutEmployeeLeaveDetailVO>> queryEmployeeLeaveDetailByCode(@RequestBody QueryEmpLeaveDetailsByCodeRequest request);

    /**
     * 提供分销方 -- 查询工号和姓名是否匹配
     * 入参：工号、姓名；出参：工号、姓名、工号姓名是否匹配；
     * @param requestList
     * @return
     */
    @PostMapping("/hrms/employee/mdm/queryEmployeeCodeAndNameMatch")
    Result<List<OutEmployeeInfoVO>> queryEmployeeCodeAndNameMatch(@RequestBody List<QueryEmployeeInfoRequest> requestList);

}

