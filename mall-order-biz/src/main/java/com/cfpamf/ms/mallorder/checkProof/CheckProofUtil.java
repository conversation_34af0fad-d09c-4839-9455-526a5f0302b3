package com.cfpamf.ms.mallorder.checkProof;

import com.cfpamf.ms.mall.filecenter.domain.dto.FileScenesMaterialProofDTO;
import com.cfpamf.ms.mallorder.common.enums.OrderMaterialTypeEnum;
import com.cfpamf.ms.mallorder.vo.OrderTradeProofVO;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class CheckProofUtil {
    private List<ICheckProofValidator> validatorList = new ArrayList<>();

    public List<CheckProofValidateResult> checkProofValidate(FileScenesMaterialProofDTO paramDTO, OrderTradeProofVO orderTradeProofVO, OrderMaterialTypeEnum materialTypeEnum){
        List<CheckProofValidateResult> list = new ArrayList<>();
        for (ICheckProofValidator validator : validatorList){
            if(validator.needValidate(orderTradeProofVO, materialTypeEnum)){
                CheckProofValidateResult validate = validator.validate(paramDTO, orderTradeProofVO, materialTypeEnum);
                list.add(validate);
            }
        }
        return list;
    }

}
