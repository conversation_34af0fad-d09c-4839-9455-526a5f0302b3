package com.cfpamf.ms.mallorder.controller.fegin.facade.workflow.dto;

import com.cfpamf.ms.mallorder.controller.fegin.facade.workflow.constant.UserIdTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
public class TaskListQueryDTO {
    @ApiModelProperty("登录用户ID")
    @NotNull(message = "userId不能为空")
    private String userId;
    @ApiModelProperty("流程定义的key")
    private String processDefKey;
    @ApiModelProperty("用户ID类型: JOB_NO, BMS_USER_ID, DING_USER_ID")
    @NotNull(message = "userId类型不能为空")
    private UserIdTypeEnum userIdType;
    @ApiModelProperty("申请人姓名")
    private String applicantName;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
    @ApiModelProperty("当前页，默认1")
    private Integer pageNumber = 1;
    @ApiModelProperty("分页size，默认10")
    private Integer pageSize = 10;

    public TaskListQueryDTO(String userId, String processDefKey, UserIdTypeEnum userIdType, String applicantName, LocalDateTime startTime, LocalDateTime endTime, Integer pageNumber, Integer pageSize) {
        this.userId = userId;
        this.processDefKey = processDefKey;
        this.userIdType = userIdType;
        this.applicantName = applicantName;
        this.startTime = startTime;
        this.endTime = endTime;
        this.pageNumber = pageNumber == null ? 1 : pageNumber;
        this.pageSize = pageSize == null ? 10 : pageSize;
    }
}
