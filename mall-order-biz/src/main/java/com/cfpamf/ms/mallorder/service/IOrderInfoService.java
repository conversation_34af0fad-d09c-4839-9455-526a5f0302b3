package com.cfpamf.ms.mallorder.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.dto.OrderManageBranchPramDTO;
import com.cfpamf.ms.mallorder.dto.StoreOrderReq;
import com.cfpamf.ms.mallorder.po.OrderLogPO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.req.OfflineOrderListQueryRequest;
import com.cfpamf.ms.mallorder.req.OrderListQueryRequest;
import com.cfpamf.ms.mallorder.request.OrderExample;
import com.cfpamf.ms.mallorder.vo.*;
import com.cfpamf.ms.mallshop.resp.Vendor;
import com.cfpamf.ms.mallsystem.vo.Admin;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.PageVO;
import com.slodon.bbc.core.response.PagerInfo;

import java.util.Date;
import java.util.List;

/**
 * 订单  service 接口
 */
public interface IOrderInfoService extends IService<OrderPO> {

    /***
     * @param orderSn
     * @param orderState
     * @param evaluateState
     * @param pager
     * @param member
     * @return com.slodon.bbc.core.response.JsonResult<com.slodon.bbc.core.response.PageVO < com.cfpamf.ms.mallorder.vo.MemberOrderListVO>>
     * @description : 查询用户订单列表
     */
    JsonResult<PageVO<MemberOrderListVO>> listPageOrder(String orderSn, Integer orderState, Integer evaluateState,
                                                        PagerInfo pager, Member member, String paySn, Integer orderType,
                                                        Integer isSpelling, String channel);

    /***
     * @param orderSn
     * @param member
     * @return com.slodon.bbc.core.response.JsonResult<com.cfpamf.ms.mallorder.vo.MemberOrderDetailVO>
     * @description :查询订单详情
     */
    JsonResult<MemberOrderDetailVO> getOrderDetail(String orderSn, Member member);

    /***
     * @param paySn  支付单号
     * @param orderSn 订单号
     * @param member 用户
     * @return com.slodon.bbc.core.response.JsonResult<com.cfpamf.ms.mallorder.vo.BankTransferOrderDetailVO>
     * @description :查询银行转账汇款订单详情
     */
    JsonResult<BankTransferOrderDetailVO> getBankTransferDetail(String paySn, String orderSn, Member member);

    /**
     *   :分页查询聊天界面我的订单
     */
    JsonResult<PageVO<ChatOrdersVO>> pageMyOrderList(OrderExample example, PagerInfo pager);

    /**
     * @param storeId
     * @return com.slodon.bbc.core.response.JsonResult<com.cfpamf.ms.mallorder.vo.OrderStatusCountVO>
     * @description : 获取订单管理状态汇总(小程序)
     */
    JsonResult<OrderStatusCountVO> getOrderCountInfo(String storeId);

    /**** @param storeOrderReq
     * @return com.slodon.bbc.core.response.JsonResult<com.baomidou.mybatisplus.extension.plugins.pagination.Page < com.cfpamf.ms.mallorder.vo.PayMethodPageVO>>
     * @description :分页查询店铺订单列表(小程序)
     */
    JsonResult<Page<OrderListVO>> pageStoreOrderList(StoreOrderReq storeOrderReq);

    /***
     * @param orderSn
     * @param memberName
     * @param goodsName
     * @param date
     * @param time
     * @param startTime
     * @param endTime
     * @param paymentCode
     * @param state
     * @param orderState
     * @param pager
     * @param vendor
     * @param name
     * @param receiverName
     * @param areaName
     * @return com.slodon.bbc.core.response.JsonResult<com.slodon.bbc.core.response.PageVO < com.cfpamf.ms.mallorder.vo.OrderListVO>>
     * @description :分页查询店铺订单列表(web端)
     */
    JsonResult<PageVO<OrderListVOV2>> listStoreWeb(String orderSn, String memberName, String goodsName, Date date, Date time,
                                                   Date startTime, Date endTime, String paymentCode, Integer state,
                                                   Integer orderState, PagerInfo pager, Vendor vendor, String channel,
                                                   List<Integer> orderType, String customerId, String customerName,
                                                   String storeName, String recommendStoreId, String recommendStoreName,
                                                   String branchName, Integer distribution, Integer orderPattern, String name,
                                                   String receiverName, String areaName,String userMobile);


    /**
     * 预付订金
     *
     * 有X单未支付尾款将自动关闭（距离自动关闭时间 <= 3）,关闭后订金将会返回，请尽快联系用户支付
     * */
    int getDepositRemindCount(Long storeId);

    /**
     * admin 查询订单详情信息
     *
     * @param orderSn 订单号
     * @return 订单详情
     */
    OrderVO adminOrderDetailInfo(String orderSn, Admin admin);

    /***
     * @param orderSn
     * @param vendor
     * @param distribution
     * @return com.slodon.bbc.core.response.JsonResult<com.cfpamf.ms.mallorder.vo.SellerOrderVO>
     * @description : 获取订单详情(web端)
     */
    JsonResult<SellerOrderVO> getOrderDetailWeb(String orderSn, Vendor vendor, Integer distribution, Boolean showAgreement);

    /***
     * @param storeId
     * @param vendor
    * @return com.slodon.bbc.core.response.JsonResult<com.cfpamf.ms.mallorder.vo.SellerOrderVO>
    * @description : 获取订单详情(小程序端)
    */
    JsonResult<SellerOrderVO> getMinOrderDetail(String storeId, Vendor vendor);

    /**** @param storeId
    * @return com.slodon.bbc.core.response.JsonResult<java.util.List<com.cfpamf.ms.mallorder.vo.BzOrderSearchHistoryVO>>
    * @description : 获取店铺订单搜索历史
    */
    JsonResult<List<BzOrderSearchHistoryVO>> getSearchHistory(String storeId);

    /**** @param storeId
    * @return com.slodon.bbc.core.response.JsonResult
    * @description :清空店铺订单搜索历史
    */
    JsonResult deleteSearchHistory(String storeId);

    /**
     * 处理订单支付剩余时间
     *
     * @param key          redis存储的key
     * @param createTime   订单创建时间
     * @param defaultValue 默认值
     * @return
     */
    long dealRemainTime(String key, Date createTime, Integer defaultValue);

    /**
     * 查询分销所需订单信息
     *
     * @param orderSn 订单号
     * @return 订单信息
     */
    OrderInfoForDbcVO orderDetailInfoFordbc(String orderSn);

    /**
     * 查询订单信息
     *
     * @param orderSn 订单号
     * @return 订单简单信息
     */
    OrderBriefInfoVO orderBriefInfo(String orderSn);


    /**
     * 反查订单信息
     *
     * @param orderSn 订单号
     * @return 订单信息
     */
    OrderInfoVO getOrderDetailInfo(String orderSn);

    int updateData(String tableName, String columnName, String columnValue, String idColumn, String idValue);

    Boolean updateProductInfo(List<Long> productIds);

    Boolean updateOrderBranch();

    Boolean updateOrderZone();

    /**
     * 补充订单的userCode数据，单次查询1000条
     *
     * @return      修改的订单量
     */
    Integer replenishOrderUserCode();

    Integer replenishSkuMaterialName();

    /**
     * 查询订单实体的映射信息
     *
     * @param orderSn   订单号
     * @return          订单信息
     */
    OrderInfoVOV2 getOrderSnapshot(String orderSn);

    /**
     * 更新订单锁定状态
     *
     * @param orderSn   订单号
     * @param value     修改的锁定值：正为增加，负为减少
     * @return          处理结果
     */
    boolean dealOrderLock(String orderSn, Integer value);

    /**
     * 更新订单锁定状态
     *
     * @param orderSn       订单号
     * @param lockTimes     更新的锁定值，正数为增加，负数为减少
     */
    boolean updateOrderLockStates(String orderSn, Integer lockTimes);


    PageVO<OrderListVOV2> listStoreWebV2(OrderListQueryRequest orderRequest, PagerInfo pager, Vendor vendor);

    PageVO<OfflineOrderListVOV2> getOfflineOrderList(OfflineOrderListQueryRequest orderRequest, PagerInfo pager, Vendor vendor);

    List<OrderLogPO> getOrderLogs(String orderSn);

    List<String> getReceiveCodeStoreIdList();
    Void manageBranch(OrderManageBranchPramDTO pramDTO,Vendor vendor);
}
