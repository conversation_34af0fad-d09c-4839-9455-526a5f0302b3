package com.cfpamf.ms.mallorder.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@RefreshScope
@EnableConfigurationProperties
@Configuration
@ConfigurationProperties(prefix = "funds-borrow-day")
@Data
public class FundsBorrowDayConfig {

    private Integer days = 30;

    private Integer closeStoreDays = 15;

    private Integer preAlertDays = 1;

    private String factoryNo = "100101001";
}
