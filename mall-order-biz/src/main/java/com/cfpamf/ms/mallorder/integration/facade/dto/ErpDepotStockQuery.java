package com.cfpamf.ms.mallorder.integration.facade.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class ErpDepotStockQuery {


    @ApiModelProperty(value = "店铺ID")
    @NotNull(message = "店铺ID不能为空")
    private Long storeId;

    @ApiModelProperty("业务来源 agric_host-乡信助农 norm_mall-标准电商 self_study_mall-自研电商")
    @NotNull(message = "业务来源不能为空")
    private String bizSource;

    @ApiModelProperty(value = "租户ID", hidden = true)
    private Long tenantId;

    @ApiModelProperty(value = "仓库类型：1-销售池 2-店铺仓 3-中转仓 4-分支仓 5-区域仓")
    private List<Integer> typeList;

    @ApiModelProperty(value = "sku编码集合")
    private List<String> skuIdList;
}
