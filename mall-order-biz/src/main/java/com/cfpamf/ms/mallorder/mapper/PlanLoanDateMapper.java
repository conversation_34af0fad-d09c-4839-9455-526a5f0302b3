package com.cfpamf.ms.mallorder.mapper;

import com.cfpamf.framework.autoconfigure.mybatis.MyBaseMapper;
import com.cfpamf.ms.mallorder.po.PlanLoanDatePO;
import com.cfpamf.ms.mallorder.req.admin.AdminPlanLoanDateRequest;
import com.cfpamf.ms.mallorder.vo.PlanLoanDateVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PlanLoanDateMapper extends MyBaseMapper<PlanLoanDatePO> {

    int getPlanLoanDateListCount(@Param("req") AdminPlanLoanDateRequest req);

    List<PlanLoanDateVO> getPlanLoanDateListByPage(@Param("req") AdminPlanLoanDateRequest req,
                                                   @Param("startRow") Integer startRow,
                                                   @Param("size") Integer size);
}
