package com.cfpamf.ms.mallorder.integration.facade.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class SpyhunterEngineQuery {

    @ApiModelProperty("事件内容(json格式)")
    private String jsonInfo;

    @ApiModelProperty("规则引擎模型guid")
    @NotBlank(message = "modelGuid不能为空")
    private String modelGuid;

    @ApiModelProperty("请求流水号")
    @NotBlank(message = "请求流水号不能为空")
    private String reqId;


}
