package com.cfpamf.ms.mallorder.service;

import com.cfpamf.ms.mallorder.controller.fegin.facade.workflow.util.MyPage;
import com.cfpamf.ms.mallorder.controller.fegin.facade.workflow.vo.WorkflowTaskDetailVO;
import com.cfpamf.ms.mallorder.dto.AuditTaskSearchVO;
import com.cfpamf.ms.mallorder.vo.AuditTaskVO;
import com.cfpamf.ms.mallorder.vo.TaskTypeVO;
import com.cfpamf.ms.mallsystem.vo.Admin;
import com.slodon.bbc.core.response.PageVO;
import com.slodon.bbc.core.response.PagerInfo;

import java.util.List;

public interface AdminAuditTaskService {
    /**
     * 获取代办任务列表
     *
     * @param admin    用户
     * @param searchVO 查询参数vo
     * @return 代办任务列表
     */
    PageVO<AuditTaskVO> todoList(Admin admin, AuditTaskSearchVO searchVO);

    /**
     * 获取已完成代办列表
     *
     * @param admin    用户
     * @param searchVO 查询参数vo
     * @return 已完成任务列表
     */
    PageVO<AuditTaskVO> doneList(Admin admin, AuditTaskSearchVO searchVO);

    PageVO<AuditTaskVO> getAuditTaskVoByTaskDetailVo(PagerInfo pagerInfo, MyPage<WorkflowTaskDetailVO> taskDetailVo, AuditTaskSearchVO searchVO);

    /**
     * 获取所有任务类型枚举
     *
     * @return 任务类型vo
     */
    List<TaskTypeVO> getAllTaskType();
}
