package com.cfpamf.ms.mallorder.validation;

import java.util.Objects;

import com.cfpamf.ms.mallorder.dto.OrderSubmitParamDTO;
import com.cfpamf.ms.mallorder.enums.OrderPlaceUserRole;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.util.CollectionUtils;

import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.common.util.ValidUtils;
import com.cfpamf.ms.mallorder.dto.OrderOfflineDTO;
import com.cfpamf.ms.mallorder.dto.OrderOfflineParamDTO;
import com.cfpamf.ms.mallorder.dto.OrderParamDTO;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;

/**
 * 
 * <AUTHOR> 订单校验
 *
 */
public class OrderValidation {

	public static void isValidOrderMemberInfo(Member member) {
		BizAssertUtil.isTrue(Objects.isNull(member) || Objects.isNull(member.getUserNo()),
				"抱歉，会员信息不存在。请先在乡助APP上注册会员，再来下单！");
	}

	public static void isValidOrderBaseInfo(OrderOfflineParamDTO dto) {
		BizAssertUtil.isTrue(!OrderTypeEnum.isOfflineAll(dto.getOrderType()),
				String.format("抱歉，订单类型错误。请先校准订单类型【%s】，再来下单！", dto.getOrderType()));
		BizAssertUtil.notEmpty(dto.getSkuInfoList(), "抱歉，尚未选择要下单的商品规格，请选中要购买的具体商品！");
		BizAssertUtil.isTrue(ObjectUtils.isNotEmpty(dto.getAddress()) && !ValidUtils.isAddressValid(dto.getAddress()),
				"抱歉，收货地址不完善，请填写正确的收货地址！");
		BizAssertUtil.notEmpty(dto.getOrderOfflineList(), "抱歉，请填写回执单信息！");
		BizAssertUtil.isTrue(dto.getOrderOfflineList().size() > 5, "抱歉，回执单信息，一个订单最多上传5笔！");
		BizAssertUtil.notNull(dto.getOrderOfflineList().get(0), "抱歉，请填写回执单信息！");
		String receiptAccount = dto.getOrderOfflineList().get(0).getReceiptAccount();
		BizAssertUtil.isTrue(StringUtils.isEmpty(receiptAccount), "抱歉，请填写回执单收款帐号！");
		for (OrderOfflineDTO item : dto.getOrderOfflineList()) {
			BizAssertUtil.isTrue(!receiptAccount.equals(item.getReceiptAccount()), "抱歉，一次下单，收款帐号必须相同，请核对回执单收款帐号！");
		}
	}

	public static void isValidBatchOrderBaseInfo(OrderParamDTO dto) {
		BizAssertUtil.isTrue(!CollectionUtils.isEmpty(dto.getAttachmentUrls()) && dto.getAttachmentUrls().size() > 10,"抱歉，订单附件最多只允许上传10个文件！");
		if(("XXAPP".equals(dto.getChannel()) || "XX_MINI_PRO".equals(dto.getChannel()))
				&& OrderPlaceUserRole.isStationMasterOrderPlace(dto.getOrderPlaceUserRole())) {
			BizAssertUtil.isTrue(StringUtils.isEmpty(dto.getLoanPayer()), "支付人不能为空");
			BizAssertUtil.isTrue(StringUtils.isEmpty(dto.getLoanConfirmMethod()), "贷款确认方式不能为空");
		}
		BizAssertUtil.isTrue(Objects.isNull(dto.getAddress()) && Objects.isNull(dto.getPointId()), "收件地址不能为空");
	}

	public static void isValidBatchOrderBaseInfo(OrderSubmitParamDTO dto) {
		BizAssertUtil.isTrue(!CollectionUtils.isEmpty(dto.getAttachmentUrls()) && dto.getAttachmentUrls().size() > 10,"抱歉，订单附件最多只允许上传10个文件！");
		if(("XXAPP".equals(dto.getChannel()) || "XX_MINI_PRO".equals(dto.getChannel()))
				&& OrderPlaceUserRole.isStationMasterOrderPlace(dto.getOrderPlaceUserRole())) {
			BizAssertUtil.isTrue(StringUtils.isEmpty(dto.getLoanPayer()), "支付人不能为空");
			BizAssertUtil.isTrue(StringUtils.isEmpty(dto.getLoanConfirmMethod()), "贷款确认方式不能为空");
		}
	}

}
