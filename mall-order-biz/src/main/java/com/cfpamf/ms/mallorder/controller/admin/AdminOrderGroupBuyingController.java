package com.cfpamf.ms.mallorder.controller.admin;

import com.cfpamf.ms.mallorder.common.aspect.ThreadLocalRemoveTag;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.dto.GroupBuyingOrderSubmitDTO;
import com.cfpamf.ms.mallorder.dto.OrderAddressDTO;
import com.cfpamf.ms.mallorder.dto.UserDTO;
import com.cfpamf.ms.mallorder.req.query.GroupOrderQuery;
import com.cfpamf.ms.mallorder.req.query.GroupOrderRecordQuery;
import com.cfpamf.ms.mallorder.service.IOrderGroupBuyingRecordService;
import com.cfpamf.ms.mallorder.service.impl.PlacingOrderServiceImpl;
import com.cfpamf.ms.mallorder.vo.*;
import com.cfpamf.ms.mallsystem.vo.Admin;
import com.slodon.bbc.core.constant.WebConst;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.PageVO;
import com.slodon.bbc.core.response.SldResponse;
import com.slodon.bbc.core.util.UserUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

@RestController
@RequestMapping("/admin/order/groupBuying")
@Api(tags = "admin-拼单满赠操作")
@Slf4j
public class AdminOrderGroupBuyingController {

	@Autowired
	private IOrderGroupBuyingRecordService groupBuyingRecordService;
	@Autowired
	private PlacingOrderServiceImpl placingOrderService;

	@ApiOperation("拼单满赠列表")
	@PostMapping("recordPageList")
	public JsonResult<PageVO<GroupOrderRecordVO>> orderGroupBuyingList(@RequestBody @NotNull @Valid GroupOrderRecordQuery query) {
		PageVO<GroupOrderRecordVO> groupOrderRecordVOPageVO = groupBuyingRecordService.ListOrderGroupBuyingRecord(query);
		return SldResponse.success(groupOrderRecordVOPageVO);
	}

	@ApiOperation("拼单满赠详情")
	@GetMapping("recordDetail")
	public JsonResult<GroupOrderRecordDetailVO> orderGroupBuyingDetail(@RequestParam(name = "groupBuyingCode") String groupBuyingCode) {

		return SldResponse.success(groupBuyingRecordService.orderGroupBuyingDetail(groupBuyingCode));
	}

	@ApiOperation("导出")
	@PostMapping("export")
	public void export(HttpServletRequest request, HttpServletResponse response,
					   @RequestBody @NotNull @Valid GroupOrderRecordQuery query) {

		groupBuyingRecordService.export(request, response, query);
	}

	@ApiOperation("可拼单订单列表")
	@PostMapping("groupOrderList")
	public JsonResult<PageVO<GroupOrderVO>> groupOrderList(@RequestBody @NotNull @Valid GroupOrderQuery query) {

		return SldResponse.success(groupBuyingRecordService.groupOrderList(query));
	}

	@ApiOperation("可拼单订单数量统计")
	@PostMapping("groupOrderStatistic")
	public JsonResult<List<GroupOrderStatisticVO>> groupOrderStatistic(@RequestBody @NotNull @Valid GroupOrderQuery query) {

		return SldResponse.success(groupBuyingRecordService.groupOrderStatistic(query));
	}

	@ApiOperation("订单收货地址")
	@GetMapping("orderReceiveInfo")
	public JsonResult<OrderAddressDTO> orderReceiveInfo(@RequestParam(name = "orderSn") String orderSn) {

		return SldResponse.success(groupBuyingRecordService.orderReceiveInfo(orderSn));
	}


	@PostMapping("submitGroupOrder")
	@ApiOperation("提交拼单满赠订单接口")
	@ApiImplicitParams({
			@ApiImplicitParam(name = WebConst.USER_HEADER, value = "客户信息 由网关处理", paramType = "header", dataType = "String")
	})
	@ThreadLocalRemoveTag
	public JsonResult<OrderSubmitVO> submitGroupOrder(HttpServletRequest request, @RequestBody @Valid GroupBuyingOrderSubmitDTO dto) {

		// 获取商户信息
		Admin admin = UserUtil.getUser(request, Admin.class);
		BizAssertUtil.notNull(admin, "用户信息获取失败");
		UserDTO userDTO = new UserDTO(Long.valueOf(admin.getUserId()), admin.getUserName(), dto.getOrderPlaceUserRole().getValue());
		log.info("====== submitGroupOrder admin info：{},userDTO info:{}", admin, userDTO);

		return SldResponse.success(placingOrderService.createGroupOrder(dto, userDTO));
	}
}
