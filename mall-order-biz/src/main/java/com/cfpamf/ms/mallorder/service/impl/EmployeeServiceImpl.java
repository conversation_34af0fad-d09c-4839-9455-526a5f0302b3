package com.cfpamf.ms.mallorder.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.bms.facade.dto.ElementTreeNodeDTO;
import com.cfpamf.ms.bms.facade.vo.DictionaryItemVO;
import com.cfpamf.ms.bms.facade.vo.OrganizationBaseVO;
import com.cfpamf.ms.bms.facade.vo.UserListBaseVO;
import com.cfpamf.ms.bms.facade.vo.UserListVO;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.dto.BeverageWorkBranchSortDTO;
import com.cfpamf.ms.mallorder.dto.BeverageWorkProportionOfGoodsDTO;
import com.cfpamf.ms.mallorder.dto.BeverageWorkRevenueAmtDTO;
import com.cfpamf.ms.mallorder.enums.BeverageWorkReportEnum;
import com.cfpamf.ms.mallorder.integration.facade.BmsIntegration;
import com.cfpamf.ms.mallorder.pgMapper.AdsWineWorkDateOrderDetailHfpMapper;
import com.cfpamf.ms.mallorder.po.pgrpt.AdsWineWorkDateOrderDetailHfpPO;
import com.cfpamf.ms.mallorder.req.BeverageWorkReportRequest;
import com.cfpamf.ms.mallorder.vo.AuthBranchAndUserVO;
import com.cfpamf.ms.mallorder.vo.BeverageWorkReportVO;
import com.slodon.bbc.core.exception.BusinessException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.cdfinance.hrms.facade.vo.OnBoardEmployeeVO;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.mallorder.controller.fegin.facade.EmployeeFacade;
import com.cfpamf.ms.mallorder.service.EmployeeService;

import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.Year;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EmployeeServiceImpl implements EmployeeService {

    @Autowired
    private EmployeeFacade employeeFacade;

    @Autowired
    private BmsIntegration bmsIntegration;


    @Autowired
    private AdsWineWorkDateOrderDetailHfpMapper adsWineWorkDateOrderDetailHfpMapper;

    @Override
    public OnBoardEmployeeVO queryByEmployeeCode(String employeeCode) {
        if (ObjectUtils.isEmpty(employeeCode)) {
            return null;
        }
        Result<OnBoardEmployeeVO> data = employeeFacade.queryOnboardEmployees(employeeCode);
        if (ObjectUtils.isEmpty(data) || !data.isSuccess()) {
            log.warn("queryByEmployeeCode客户经理编码查询 {} {}", employeeCode, data);
            return null;
        }
        return data.getData();
    }

    @Override
    public BeverageWorkReportVO queryBeverageWorkReport(BeverageWorkReportRequest request) {
        BeverageWorkReportVO beverageWorkReportVO = new BeverageWorkReportVO();
        AuthBranchAndUserVO authBranchAndUserVO = getBranchAndUserList(request.getJobNumber(), request.getOrgCode());
        if (CollectionUtils.isEmpty(request.getOrgCodeList())) {
            request.setFinalSearchBranchList(authBranchAndUserVO.getBranchList());
        } else {
        	List<String> finalSearchBranchList = request.getOrgCodeList();
        	if(!CollectionUtils.isEmpty(authBranchAndUserVO.getBranchList())){
        		finalSearchBranchList = authBranchAndUserVO.getBranchList().stream().filter(o -> request.getOrgCodeList().contains(o)).collect(Collectors.toList());
        	}
            //分支权限和用户筛选条件取交集
            request.setFinalSearchBranchList(finalSearchBranchList);
        }
        if(CollectionUtils.isEmpty(request.getFinalSearchBranchList())) {
        	request.setManagerList(authBranchAndUserVO.getUserList());
        }

        log.info("queryBeverageWorkReport beverageWorkReportVO = {}", JSONObject.toJSONString(beverageWorkReportVO));


        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime todayStart = LocalDateTime.now().toLocalDate().atStartOfDay();

        LocalDate today = LocalDate.now();
        switch (BeverageWorkReportEnum.getValue(request.getTimeRange())) {
            case TODAY:
                LocalDateTime now = LocalDateTime.now();
                request.setBeginTime(todayStart.format(formatter));
                request.setEndTime(now.format(formatter));
                break;

            case PAST_7_DAYS:
                LocalDate sevenDaysAgo = today.minusDays(6);
                LocalDateTime sevenDaysAgoStart = sevenDaysAgo.atStartOfDay();
                LocalDateTime sevenDaysEnd = today.atTime(23, 59, 59);
                request.setBeginTime(sevenDaysAgoStart.format(formatter));
                request.setEndTime(sevenDaysEnd.format(formatter));
                break;
            case PAST_30_DAYS:
                LocalDate thirtyDaysAgo = today.minusDays(29);
                LocalDateTime thirtyDaysAgoStart = thirtyDaysAgo.atStartOfDay();
                LocalDateTime thirtyDaysEnd = today.atTime(23, 59, 59);
                request.setBeginTime(thirtyDaysAgoStart.format(formatter));
                request.setEndTime(thirtyDaysEnd.format(formatter));
                break;

            case PAST_6_MONTHS:
                LocalDate sixMonthsAgo = today.minusMonths(6);
                LocalDateTime sixMonthsAgoStart = sixMonthsAgo.atStartOfDay();
                LocalDateTime sixMonthsEnd = today.atTime(23, 59, 59);
                request.setBeginTime(sixMonthsAgoStart.format(formatter));
                request.setEndTime(sixMonthsEnd.format(formatter));
                break;
            case THIS_YEAR:
                // 获取当前年份
                int currentYear = Year.now().getValue();
                // 使用当前年份和1月1日创建LocalDate对象
                LocalDateTime firstDayOfYear = LocalDateTime.of(currentYear, 1, 1,0, 0, 0);
                LocalDateTime thisYearEnd = today.atTime(23, 59, 59);
                request.setBeginTime(firstDayOfYear.format(formatter));
                request.setEndTime(thisYearEnd.format(formatter));
                break;
            case THIS_MONTHS:
                // 获取当前年份
                int year = Year.now().getValue();
                int month = LocalDate.now().getMonthValue();
                // 使用当前年份和1月1日创建LocalDate对象
                LocalDateTime firstDayOfMonth = LocalDateTime.of(year, month, 1,0, 0, 0);
                LocalDateTime thisMonthEnd = today.atTime(23, 59, 59);
                request.setBeginTime(firstDayOfMonth.format(formatter));
                request.setEndTime(thisMonthEnd.format(formatter));
                break;
            case PAST_1_YEAR:
            	LocalDate oneYearAgo = today.minusYears(1);
                LocalDateTime oneYearAgoStart = oneYearAgo.atStartOfDay();
                LocalDateTime oneYearEnd = today.atTime(23, 59, 59);
                request.setBeginTime(oneYearAgoStart.format(formatter));
                request.setEndTime(oneYearEnd.format(formatter));
                break;
            default:
                break;
        }
        log.info("queryBeverageWorkReport sql start");
        List<AdsWineWorkDateOrderDetailHfpPO> list = adsWineWorkDateOrderDetailHfpMapper.getList(request);
        log.info("queryBeverageWorkReport sql end");

        if (CollectionUtils.isEmpty(list)) {
            beverageWorkReportVO.setRevenueAmt(BigDecimal.ZERO);
            beverageWorkReportVO.setValidTradeBoxNum(BigDecimal.ZERO);
            beverageWorkReportVO.setRevenueAmtList(new ArrayList<>());
            beverageWorkReportVO.setBranchSortList(new ArrayList<>());
            beverageWorkReportVO.setProportionOfGoodsList(new ArrayList<>());
            return beverageWorkReportVO;
        }

        //有效箱数及营收
        BigDecimal revenueAmt = list.stream().map(AdsWineWorkDateOrderDetailHfpPO::getRevenueAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal validTradeBoxNum = list.stream().map(AdsWineWorkDateOrderDetailHfpPO::getValidTradeBoxNum).reduce(BigDecimal.ZERO, BigDecimal::add);

        beverageWorkReportVO.setRevenueAmt(revenueAmt);
        beverageWorkReportVO.setValidTradeBoxNum(validTradeBoxNum);

        log.info("queryBeverageWorkReport switch start");
        //分组取数据 （有效箱数及营收）
        switch (BeverageWorkReportEnum.getValue(request.getTimeRange())) {
            //按每四个小时分组
            case TODAY:
                dealDataBy4Hours(beverageWorkReportVO, list);
                break;
            //按每天分组
            case PAST_7_DAYS:
                dealDataByDay(beverageWorkReportVO, list, 6);
                break;
            //按每天分组
            case PAST_30_DAYS:
                dealDataByDay(beverageWorkReportVO, list, 29);
                break;
            //按每月分组
            case PAST_6_MONTHS:
                dealDataByMonth(beverageWorkReportVO, list, 6);
                break;
            //按每月分组
            case THIS_YEAR:
                int currentMonthNumber = LocalDate.now().getMonth().getValue();
                dealDataByMonth(beverageWorkReportVO, list, currentMonthNumber);
                break;
            //按每天分组
            case THIS_MONTHS:
                dealDataByDay(beverageWorkReportVO, list, LocalDate.now().getDayOfMonth()-1);
                break;
            //按每月分组
            case PAST_1_YEAR:
                dealDataByMonth(beverageWorkReportVO, list, 12);
                break;
            default:
                break;
        }
        log.info("queryBeverageWorkReport switch end");

        log.info("queryBeverageWorkReport dealPromotionOfGoods start");
        //扇形图
        dealPromotionOfGoods(beverageWorkReportVO, list);
        log.info("queryBeverageWorkReport dealPromotionOfGoods end");


        log.info("queryBeverageWorkReport dealBranchSort start");
        //机构和个人排名 只取分支层级机构数据排名
        List<AdsWineWorkDateOrderDetailHfpPO> sortOrgList = list.stream().filter(x-> 1 == x.getIsNormalBch()).collect(Collectors.toList());

        //机构排名（取前10名，包含并列的）
        dealBranchSort(beverageWorkReportVO, sortOrgList);
        log.info("queryBeverageWorkReport dealBranchSort end");

        log.info("queryBeverageWorkReport dealEmployeeSort start");
        //个人排名（取前10名，包含并列的）
        dealEmployeeSort(beverageWorkReportVO, sortOrgList);
        log.info("queryBeverageWorkReport dealEmployeeSort end");

        return beverageWorkReportVO;
    }

    private void dealEmployeeSort(BeverageWorkReportVO beverageWorkReportVO, List<AdsWineWorkDateOrderDetailHfpPO> list) {

        log.info("dealEmployeeSort start");
        Map<String, Map<String, BeverageWorkBranchSortDTO.BeverageWorkBranchSummary2>> groupedByEmpIdAndName = new LinkedHashMap<>();
        Map<String, BeverageWorkBranchSortDTO.BeverageWorkBranchSummary2> tempMap;
        BeverageWorkBranchSortDTO.BeverageWorkBranchSummary2 summary;

        for (AdsWineWorkDateOrderDetailHfpPO po : list) {
            if (StringUtils.isEmpty(po.getAchievementEmpName())) {
                continue;
            }

            tempMap = groupedByEmpIdAndName.computeIfAbsent(po.getAchievementEmpName(), k -> new LinkedHashMap<>());
            summary = tempMap.computeIfAbsent(po.getAchievementEmpId(), k -> new BeverageWorkBranchSortDTO.BeverageWorkBranchSummary2(
                    BigDecimal.ZERO, BigDecimal.ZERO, po.getBchName(), po.getAreaName()));

            summary.revenueAmt = summary.revenueAmt.add(po.getRevenueAmt());
            summary.validTradeBoxNum = summary.validTradeBoxNum.add(po.getValidTradeBoxNum());
        }
        log.info("dealEmployeeSort for end");

        List<BeverageWorkBranchSortDTO> dtoList = new ArrayList<>();
        for (Map.Entry<String, Map<String, BeverageWorkBranchSortDTO.BeverageWorkBranchSummary2>> entry : groupedByEmpIdAndName.entrySet()) {
            for (Map.Entry<String, BeverageWorkBranchSortDTO.BeverageWorkBranchSummary2> summaryEntry : entry.getValue().entrySet()) {
                BeverageWorkBranchSortDTO dto = new BeverageWorkBranchSortDTO();
                dto.setEmployeeName(entry.getKey());
                dto.setEmployeeId(summaryEntry.getKey());
                dto.setAreaName(summaryEntry.getValue().areaName);
                dto.setBchName(summaryEntry.getValue().bchName);
                dto.setRevenueAmt(summaryEntry.getValue().revenueAmt);
                dto.setValidTradeBoxNum(summaryEntry.getValue().validTradeBoxNum);
                dtoList.add(dto);
            }
        }
        log.info("dealEmployeeSort sort start");

        dtoList.sort(Comparator.comparing(BeverageWorkBranchSortDTO::getRevenueAmt, Comparator.reverseOrder()));
        log.info("dealEmployeeSort sort end");


        List<BeverageWorkBranchSortDTO> top10List = new ArrayList<>();
        int rank = 0;
        BigDecimal previousRevenue = BigDecimal.valueOf(-1);
        BigDecimal previousTradeBoxNum = BigDecimal.valueOf(-1);

        for (BeverageWorkBranchSortDTO summary1 : dtoList) {
            if (rank >= 10 && (!summary1.getRevenueAmt().equals(previousRevenue) || !summary1.getValidTradeBoxNum().equals(previousTradeBoxNum))) {
                break;
            }
            top10List.add(summary1);
            if (!summary1.getRevenueAmt().equals(previousRevenue) || !summary1.getValidTradeBoxNum().equals(previousTradeBoxNum)) {
                rank++;
                previousRevenue = summary1.getRevenueAmt();
                previousTradeBoxNum = summary1.getValidTradeBoxNum();
            }
        }

        beverageWorkReportVO.setBranchEmployeeSortList(top10List);
    }

    private void dealBranchSort(BeverageWorkReportVO beverageWorkReportVO, List<AdsWineWorkDateOrderDetailHfpPO> list) {

        // 使用Stream API进行分组和汇总
        Map<String, Map<String, BeverageWorkBranchSortDTO.BeverageWorkBranchSummary>> groupedByBchAndArea = list.stream()
                .filter(po -> !StringUtils.isEmpty(po.getBchName()))
                .collect(Collectors.groupingBy(
                        AdsWineWorkDateOrderDetailHfpPO::getBchName,
                        Collectors.groupingBy(
                                AdsWineWorkDateOrderDetailHfpPO::getAreaName,
                                Collectors.reducing(
                                        new BeverageWorkBranchSortDTO.BeverageWorkBranchSummary(BigDecimal.ZERO, BigDecimal.ZERO),
                                        po -> new BeverageWorkBranchSortDTO.BeverageWorkBranchSummary(po.getRevenueAmt(), po.getValidTradeBoxNum()),
                                        (s1, s2) -> new BeverageWorkBranchSortDTO.BeverageWorkBranchSummary(s1.revenueAmt.add(s2.revenueAmt), s1.validTradeBoxNum.add(s2.validTradeBoxNum))
                                )
                        )
                ));


        // 将分组和汇总的结果转换为BeverageWorkBranchSortDTO列表
        List<BeverageWorkBranchSortDTO> dtoList = new ArrayList<>();
        groupedByBchAndArea.forEach((bchName, areaMap) -> {
            areaMap.forEach((areaName, summary) -> {
                BeverageWorkBranchSortDTO dto = new BeverageWorkBranchSortDTO();
                dto.setBchName(bchName);
                dto.setAreaName(areaName);
                dto.setRevenueAmt(summary.revenueAmt);
                dto.setValidTradeBoxNum(summary.validTradeBoxNum);
                dtoList.add(dto);
            });
        });

        // 按revenueAmt降序排序，并返回前5名（包括并列）
        dtoList.sort(Comparator.comparing(BeverageWorkBranchSortDTO::getRevenueAmt, Comparator.reverseOrder()));


        // 取前5名，如果并列算同一名
        List<BeverageWorkBranchSortDTO> top10List = new ArrayList<>();
        int rank = 0;
        BigDecimal previousRevenue = BigDecimal.valueOf(-1);
        BigDecimal previousTradeBoxNum = BigDecimal.valueOf(-1);

        for (BeverageWorkBranchSortDTO summary : dtoList) {
            if (rank >= 10 && (!summary.getRevenueAmt().equals(previousRevenue) || !summary.getValidTradeBoxNum().equals(previousTradeBoxNum))) {
                break;
            }
            top10List.add(summary);
            if (!summary.getRevenueAmt().equals(previousRevenue) || !summary.getValidTradeBoxNum().equals(previousTradeBoxNum)) {
                rank++;
                previousRevenue = summary.getRevenueAmt();
                previousTradeBoxNum = summary.getValidTradeBoxNum();
            }
        }

        beverageWorkReportVO.setBranchSortList(top10List);
    }


    private void dealPromotionOfGoods(BeverageWorkReportVO beverageWorkReportVO, List<AdsWineWorkDateOrderDetailHfpPO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        // 按一级类目名称分组，并计算每个分组的营收总和  
        Map<String, BigDecimal> revenueByCategoryName = list.stream()
                .collect(Collectors.groupingBy(
                        AdsWineWorkDateOrderDetailHfpPO::getErpMaterialLvl1CategoryName,
                        Collectors.mapping(AdsWineWorkDateOrderDetailHfpPO::getRevenueAmt, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))
                ));

        // 创建BeverageWorkProportionOfGoodsDTO对象列表  
        List<BeverageWorkProportionOfGoodsDTO> dtoList = new ArrayList<>();
        revenueByCategoryName.forEach((categoryName, categoryRevenue) -> {
            BigDecimal proportion = categoryRevenue.divide(beverageWorkReportVO.getRevenueAmt(), 10, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
            BeverageWorkProportionOfGoodsDTO dto = new BeverageWorkProportionOfGoodsDTO();
            dto.setErpMaterialLvl1CategoryName(categoryName);
            dto.setRevenueAmt(categoryRevenue);
            dto.setProportionOfGoods(proportion);
            dtoList.add(dto);
        });

        // 按占比降序排列  
        dtoList.sort(Comparator.comparing(BeverageWorkProportionOfGoodsDTO::getProportionOfGoods).reversed());

        beverageWorkReportVO.setProportionOfGoodsList(dtoList);
    }

    private void dealDataByMonth(BeverageWorkReportVO beverageWorkReportVO, List<AdsWineWorkDateOrderDetailHfpPO> list, int month) {
        // 计算n个月前的下一个月的第一天
        LocalDate sixMonthsAgoNextMonth = LocalDate.now().minusMonths(month).plusMonths(1).withDayOfMonth(1);

        // 获取当前月份的第一天
        LocalDate currentMonthFirstDay = LocalDate.now().withDayOfMonth(1);

        // 创建一个包含最近n个月所有月份的列表
        List<String> allMonths = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        for (LocalDate date = sixMonthsAgoNextMonth; !date.isAfter(currentMonthFirstDay); date = date.plusMonths(1)) {
            allMonths.add(date.format(formatter));
        }


        // 按月分组
        Map<String, List<AdsWineWorkDateOrderDetailHfpPO>> groupedByMonth = list.stream()
                .collect(Collectors.groupingBy(r -> r.getPayTime().toInstant()
                        .atZone(ZoneId.systemDefault()).format(formatter)));

        // 确保每个月都有数据（如果没有数据，则用空列表代替）
        Map<String, List<AdsWineWorkDateOrderDetailHfpPO>> completeGroupedByMonth = new LinkedHashMap<>();
        for (String perMonth : allMonths) {
            completeGroupedByMonth.put(perMonth, groupedByMonth.getOrDefault(perMonth, Collections.emptyList()));
        }

        List<Map.Entry<String, List<AdsWineWorkDateOrderDetailHfpPO>>> sortedGroups = new ArrayList<>(completeGroupedByMonth.entrySet());

        List<BeverageWorkRevenueAmtDTO> revenueAmtList = new ArrayList<>();

        sortedGroups.forEach(entry -> {
            BeverageWorkRevenueAmtDTO beverageWorkRevenueAmtDTO = new BeverageWorkRevenueAmtDTO();
            if (entry.getValue().isEmpty()) {
                beverageWorkRevenueAmtDTO.setRevenueAmt(BigDecimal.ZERO);
                beverageWorkRevenueAmtDTO.setValidTradeBoxNum(BigDecimal.ZERO);
            } else {
                beverageWorkRevenueAmtDTO.setRevenueAmt(entry.getValue().stream().map(AdsWineWorkDateOrderDetailHfpPO::getRevenueAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
                beverageWorkRevenueAmtDTO.setValidTradeBoxNum(entry.getValue().stream().map(AdsWineWorkDateOrderDetailHfpPO::getValidTradeBoxNum).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
            beverageWorkRevenueAmtDTO.setXDateTime(entry.getKey());
            revenueAmtList.add(beverageWorkRevenueAmtDTO);
        });

        beverageWorkReportVO.setRevenueAmtList(revenueAmtList);
    }

    private void dealDataByDay(BeverageWorkReportVO beverageWorkReportVO, List<AdsWineWorkDateOrderDetailHfpPO> list,int days) {
        LocalDate sevenDaysAgo = LocalDate.now().minusDays(days); // 7天前，因为包含今天
        List<LocalDate> allDays = new ArrayList<>();

        // 按天分组
        Map<LocalDate, List<AdsWineWorkDateOrderDetailHfpPO>> groupedByDay = list.stream()
                .collect(Collectors.groupingBy(e -> e.getPayTime().toInstant()
                        .atZone(ZoneId.systemDefault()).toLocalDate()));

        // 循环遍历从n天前到今天
        for (LocalDate date = sevenDaysAgo; date.isBefore(LocalDate.now().plusDays(1)); date = date.plusDays(1)) {
            allDays.add(date);
        }


        // 确保每一天都有数据（如果没有数据，则用空列表代替）
        Map<LocalDate, List<AdsWineWorkDateOrderDetailHfpPO>> completeGroupedByDay = new LinkedHashMap<>();
        for (LocalDate day : allDays) {
            completeGroupedByDay.put(day, groupedByDay.getOrDefault(day, Collections.emptyList()));
        }

        // 转换为List
        List<Map.Entry<LocalDate, List<AdsWineWorkDateOrderDetailHfpPO>>> sortedGroups = new ArrayList<>(completeGroupedByDay.entrySet());

        List<BeverageWorkRevenueAmtDTO> revenueAmtList = new ArrayList<>();

        sortedGroups.forEach(entry -> {
            BeverageWorkRevenueAmtDTO beverageWorkRevenueAmtDTO = new BeverageWorkRevenueAmtDTO();
            if (entry.getValue().isEmpty()) {
                beverageWorkRevenueAmtDTO.setRevenueAmt(BigDecimal.ZERO);
                beverageWorkRevenueAmtDTO.setValidTradeBoxNum(BigDecimal.ZERO);
            } else {
                beverageWorkRevenueAmtDTO.setRevenueAmt(entry.getValue().stream().map(AdsWineWorkDateOrderDetailHfpPO::getRevenueAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
                beverageWorkRevenueAmtDTO.setValidTradeBoxNum(entry.getValue().stream().map(AdsWineWorkDateOrderDetailHfpPO::getValidTradeBoxNum).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
            beverageWorkRevenueAmtDTO.setXDateTime(entry.getKey().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            revenueAmtList.add(beverageWorkRevenueAmtDTO);
        });

        beverageWorkReportVO.setRevenueAmtList(revenueAmtList);
    }

    private void dealDataBy4Hours(BeverageWorkReportVO beverageWorkReportVO, List<AdsWineWorkDateOrderDetailHfpPO> list) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        Map<LocalDateTime, List<AdsWineWorkDateOrderDetailHfpPO>> groupedBy4Hours = list.stream()
                .collect(Collectors.groupingBy(obj -> {
                    LocalDateTime dateTime = obj.getPayTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                    int hourGroup = dateTime.getHour() / 4 * 4;
                    return dateTime.truncatedTo(ChronoUnit.DAYS).withHour(hourGroup);
                }));

        // 获取当前时间和起始时间（24小时前）
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime start = now.truncatedTo(ChronoUnit.DAYS);


        // 确保每个时间段都有数据
        Map<LocalDateTime, List<AdsWineWorkDateOrderDetailHfpPO>> completeGroupedBy4Hours = new HashMap<>();
        for (LocalDateTime time = start; time.isBefore(now); time = time.plusHours(4)) {
            completeGroupedBy4Hours.put(time, groupedBy4Hours.getOrDefault(time, new ArrayList<>()));
        }


        // 按时间顺序输出分组
        List<Map.Entry<LocalDateTime, List<AdsWineWorkDateOrderDetailHfpPO>>> sortedGroups = completeGroupedBy4Hours.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .collect(Collectors.toList());


        List<BeverageWorkRevenueAmtDTO> revenueAmtList = new ArrayList<>();

        sortedGroups.forEach(entry -> {
            BeverageWorkRevenueAmtDTO beverageWorkRevenueAmtDTO = new BeverageWorkRevenueAmtDTO();

            LocalDateTime startTime = entry.getKey();
            LocalDateTime endTime = startTime.plusHours(4).minusSeconds(1);
            beverageWorkRevenueAmtDTO.setXDateTime(endTime.format(formatter));
            if (entry.getValue().isEmpty()) {
                beverageWorkRevenueAmtDTO.setRevenueAmt(BigDecimal.ZERO);
                beverageWorkRevenueAmtDTO.setValidTradeBoxNum(BigDecimal.ZERO);
            } else {
                beverageWorkRevenueAmtDTO.setRevenueAmt(entry.getValue().stream().map(AdsWineWorkDateOrderDetailHfpPO::getRevenueAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
                beverageWorkRevenueAmtDTO.setValidTradeBoxNum(entry.getValue().stream().map(AdsWineWorkDateOrderDetailHfpPO::getValidTradeBoxNum).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
            revenueAmtList.add(beverageWorkRevenueAmtDTO);
        });
        beverageWorkReportVO.setRevenueAmtList(revenueAmtList);
    }


    @Override
    public AuthBranchAndUserVO getBranchAndUserList(String jobNumber, String orgCode) {

        AuthBranchAndUserVO branchAndUserVO = new AuthBranchAndUserVO();
        if (StringUtils.isEmpty(jobNumber) || StringUtils.isEmpty(orgCode)) {
            return branchAndUserVO;
        }

        /**
         * 1. 获取员工岗位基本信息
         */
        List<UserListBaseVO> users = bmsIntegration.getEmployeeInfo(jobNumber);
        List<UserListBaseVO> postUserList = users.stream().filter(o -> o.getOrgCode().equals(orgCode)
        ).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(postUserList)) {
            throw new BusinessException("员工 " + jobNumber + " 在组织 " + orgCode + ",无岗位");
        }
        /**
         * 获取岗位配置表
         */
        List<DictionaryItemVO> dictList = bmsIntegration.getDictionaryItemsByTypeCode(CommonConst.NEW_MALL_SYSTEM_ID,CommonConst.NEW_MALL_RIGHT_TYPE_CODE);


        if (CollectionUtils.isEmpty(dictList)) {
            throw new BusinessException("通用岗位权限数据字典未配置");
        }

        List<String> hrPostIds = new LinkedList<>();

        for (UserListBaseVO userVo : postUserList) {
            Integer orgId = userVo.getOrgId();
            Integer hrOrgId = userVo.getHrOrgId();
            String hrPostId = userVo.getHrPostId().toString();
            hrPostIds.add(hrPostId);

            if (dictList.get(0).getItemCode().contains(hrPostId)) {
                // 总部
                return branchAndUserVO;
            } else if (dictList.get(1).getItemCode().contains(hrPostId)) {
                // 事业部
                throw new BusinessException(" 事业部已过期 员工 " + jobNumber +
                        " 在组织 " + orgCode +
                        " 岗位 " + String.join(",", hrPostIds) + " 无数据权限");
            } else if (dictList.get(2).getItemCode().contains(hrPostId)) {
                // 区域
                branchAndUserVO.getBranchList().addAll(listCodes(orgCode));
            } else if (dictList.get(3).getItemCode().contains(hrPostId)) {
                // 片区
                // 获取片区所属区域
                OrganizationBaseVO area = bmsIntegration.getAreaByHrOrgId(hrOrgId);
                // 获取片区下的所有分支
                List<OrganizationBaseVO> branchList = bmsIntegration.listBranchesByHrParentId(hrOrgId);
                List<Integer> branchOrgIdList = branchList.stream().map(OrganizationBaseVO::getHrOrgId).collect(Collectors.toList());

                branchAndUserVO.getBranchList().addAll(
                        getBranchList(area == null ? null : Collections.singletonList(area.getHrOrgId()), branchOrgIdList));

            } else if (dictList.get(4).getItemCode().contains(hrPostId)) {
                // 分支
                branchAndUserVO.getBranchList().add(orgCode);
            } else if (dictList.get(5).getItemCode().contains(hrPostId)) {
                // 督导
                List<UserListVO> userList = bmsIntegration.listLoanOfficerUserListVOBySupervisor(jobNumber);
                List<String> userCodeList = userList.stream().filter(o -> o.getOrgCode().equals(orgCode))
                        .map(UserListVO::getJobNumber).collect(Collectors.toList());
                if (userCodeList == null) {
                    userCodeList = new ArrayList<>();
                }

                userCodeList.add(jobNumber);

                branchAndUserVO.getUserList().addAll(userCodeList);

            } else if (dictList.get(6).getItemCode().contains(hrPostId)) {
                // 信贷员
                branchAndUserVO.getUserList().addAll(Collections.singletonList(jobNumber));
            }
        }

        if (CollectionUtils.isEmpty(branchAndUserVO.getUserList()) &&
                CollectionUtils.isEmpty(branchAndUserVO.getBranchList())) {
            throw new BusinessException("员工 " + jobNumber + " 在组织 " + orgCode + " 岗位 " + String.join(",", hrPostIds) + " 无数据权限");
        }

        return branchAndUserVO;
    }

    List<String> listCodes(String orgCode) {
        List<String> list = new LinkedList<>();
        List<ElementTreeNodeDTO> areas = bmsIntegration.listRootHeadBuAreaBranchBySuperOrgCode(orgCode);
        if (areas != null) {
            // 区域
            areas.forEach(o -> {
                if (o.getChildren() == null) {
                    list.add(o.getOrgCode());
                } else {
                    // 片区
                    o.getChildren().forEach(c -> {
                        if (c.getChildren() == null) {
                            list.add(c.getOrgCode());
                        } else {
                            if (c.getChildren() != null) {
                                // 分支
                                c.getChildren().forEach(cc -> list.add(cc.getOrgCode()));
                            }
                        }
                    });
                }
            });
        }
        return list;
    }

    public List<String> getBranchList(List<Integer> areaHrOrgIds, List<Integer> branchHrOrgIds) {
        List<String> branchVos = new LinkedList<>();
        List<OrganizationBaseVO> areaList = bmsIntegration.listAreasByAreaHrOrgIds(areaHrOrgIds);
        List<OrganizationBaseVO> branchList = bmsIntegration.listBranchesByHrOrgIds(branchHrOrgIds);
        if (areaList != null && areaList.size() > 0) {
            for (OrganizationBaseVO a : areaList) {
                for (OrganizationBaseVO b : branchList) {
                    if (b.getTreePath().contains(a.getTreePath())) {
                        branchVos.add(b.getOrgCode());
                    }
                }
            }
        }
        return branchVos;
    }


}
