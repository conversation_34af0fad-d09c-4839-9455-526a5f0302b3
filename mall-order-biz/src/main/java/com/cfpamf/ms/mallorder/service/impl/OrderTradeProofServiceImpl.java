package com.cfpamf.ms.mallorder.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cdfinance.ms.facade.model.enums.BusinessResourceEnum;
import com.cdfinance.ms.facade.model.enums.SignMethodEnum;
import com.cdfinance.ms.facade.model.enums.SignTypeEnum;
import com.cdfinance.ms.facade.model.request.signBefore.ContractFindRequest;
import com.cdfinance.ms.facade.model.request.signBefore.ContractGenerateRequest;
import com.cdfinance.ms.facade.model.request.signing.ContractESignAutoRequest;
import com.cdfinance.ms.facade.model.response.signBefore.ContractFindResponse;
import com.cdfinance.ms.facade.model.vo.ContractCompanySignVO;
import com.cdfinance.ms.facade.model.vo.ContractCustomerSignVO;
import com.cdfinance.ms.facade.model.vo.ContractParamVo;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.framework.autoconfigure.mybatis.BaseRepoServiceImpl;
import com.cfpamf.framework.autoconfigure.redis.lock.DistributeLock;
import com.cfpamf.ms.loan.facade.vo.external.mall.CdmallOrderVo;
import com.cfpamf.ms.mall.filecenter.constant.IScenesMaterialProofConstant;
import com.cfpamf.ms.mall.filecenter.domain.dto.FileMaterialDTO;
import com.cfpamf.ms.mall.filecenter.domain.dto.FileScenesMaterialProofDTO;
import com.cfpamf.ms.mall.filecenter.domain.vo.FileScenesProofMaterialVO;
import com.cfpamf.ms.mall.filecenter.domain.vo.FileScenesProofVO;
import com.cfpamf.ms.mallgoods.facade.api.ProductFeignClient;
import com.cfpamf.ms.mallgoods.facade.vo.Product;
import com.cfpamf.ms.mallmember.api.MemberFeignClient;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.builder.AgreementBuilder;
import com.cfpamf.ms.mallorder.common.config.OrderMaterialConfig;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.constant.OcrConstant;
import com.cfpamf.ms.mallorder.common.enums.*;
import com.cfpamf.ms.mallorder.common.mq.RabbitMQUtils;
import com.cfpamf.ms.mallorder.common.util.*;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.common.util.CosineSimilarityUtil;
import com.cfpamf.ms.mallorder.common.util.ExpressDeliveryUtil;
import com.cfpamf.ms.mallorder.common.util.ExternalApiUtil;
import com.cfpamf.ms.mallorder.constant.ExchangeOrderConst;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.controller.fegin.facade.MsContractFacade;
import com.cfpamf.ms.mallorder.controller.fegin.facade.loan.LoanFinanceFacade;
import com.cfpamf.ms.mallorder.dto.*;
import com.cfpamf.ms.mallorder.enums.checkresult.BzOrderTradeProofCheckResultEnum;
import com.cfpamf.ms.mallorder.integration.cashier.request.InterestType;
import com.cfpamf.ms.mallorder.integration.crawler.CrawlerIntegration;
import com.cfpamf.ms.mallorder.integration.facade.AgricCrmFacade;
import com.cfpamf.ms.mallorder.integration.facade.AresTradeFacade;
import com.cfpamf.ms.mallorder.integration.facade.CdMallOrderFacade;
import com.cfpamf.ms.mallorder.integration.facade.OCRFacade;
import com.cfpamf.ms.mallorder.integration.facade.dto.OCRWordResult;
import com.cfpamf.ms.mallorder.integration.facade.dto.OcrResult;
import com.cfpamf.ms.mallorder.integration.facade.dto.RepaymentScheduleRequest;
import com.cfpamf.ms.mallorder.integration.facade.dto.WordResult;
import com.cfpamf.ms.mallorder.integration.filecenter.FileCenterIntegration;
import com.cfpamf.ms.mallorder.integration.filecenter.ProofSceneEnum;
import com.cfpamf.ms.mallorder.integration.loan.LoanPayIntegration;
import com.cfpamf.ms.mallorder.integration.system.TradeDocumentIntegration;
import com.cfpamf.ms.mallorder.mapper.OrderMapper;
import com.cfpamf.ms.mallorder.mapper.OrderTradeProofMapper;
import com.cfpamf.ms.mallorder.model.OrderLogModel;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.v2.service.OrderPayRecordService;
import com.cfpamf.ms.mallorder.v2.service.OrderPresellService;
import com.cfpamf.ms.mallorder.vo.*;
import com.cfpamf.ms.mallorder.vo.filescene.BzOrderTradeProofCheckResultVO;
import com.cfpamf.ms.mallorder.vo.filescene.FileScenesProductWithResultVO;
import com.cfpamf.ms.mallshop.api.PurchaserFeign;
import com.cfpamf.ms.mallshop.api.StoreFeignClient;
import com.cfpamf.ms.mallshop.resp.StoreContractReceiptInfoVO;
import com.cfpamf.ms.mallshop.vo.PurchaserFeignResultVO;
import com.cfpamf.ms.mallsystem.vo.DocumentVo;
import com.cfpamf.ms.mallsystem.vo.TradeDocumentVo;
import com.cfpamf.smartid.client.utils.sharding.SeqEnum;
import com.google.api.client.util.Lists;
import com.google.common.collect.Maps;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 订单交易凭证表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-12
 */
@Slf4j
@Service
public class OrderTradeProofServiceImpl extends BaseRepoServiceImpl<OrderTradeProofMapper, OrderTradeProofPO> implements IOrderTradeProofService {

    public static final String CHECK_GROUP_FACTORY_NO = "出厂编号";
    /**
     * ocr识别结果成功编码
     */
    public static final String OCR_SUCCESS_STATUS = "00000";
    @Autowired
    private IOrderService orderService;

    @Autowired
    private OrderPresellService orderPresellService;

    @Autowired
    private IOrderProductService orderProductService;

    @Autowired
    private TradeDocumentIntegration tradeDocumentIntegration;

    @Autowired
    private ProductFeignClient productFeignClient;

    @Resource
    private OrderTradeProofMapper orderTradeProofMapper;

    @Autowired
    private FileCenterIntegration fileCenterIntegration;

    @Autowired
    private AgreementBuilder agreementBuilder;

    @Autowired
    private IBzOrderTradeProofCheckResultService proofCheckResultService;

    @Autowired
    private CrawlerIntegration crawlerIntegration;
    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private IOrderExtendService orderExtendService;

    @Autowired
    private OrderMaterialConfig orderMaterialConfig;

    @Autowired
    private OCRFacade ocrFacade;

    @Autowired
    private OrderModel orderModel;

    @Autowired
    private OrderLogModel orderLogModel;

    @Autowired
    private RabbitMQUtils rabbitMQUtils;

    @Autowired
    private ICommonMqEventService commonMqEventService;

    @Autowired
    private MsContractFacade contractFacade;

    @Autowired
    private UserInfoComponent userInfoComponent;

    @Autowired
    private StoreFeignClient storeFeignClient;

    @Autowired
    private IOrderPerformanceBelongsService performanceBelongsService;

    @Resource
    private LoanPayIntegration loanPayIntegration;

    @Autowired
    private AresTradeFacade aresTradeFacade;

    @Autowired
    private LoanFinanceFacade loanFinanceFacade;

    @Autowired
    private OrderCreateHelper orderCreateHelper;

    @Autowired
    private MemberFeignClient memberFeignClient;

    @Autowired
    private DistributeLock distributeLock;

    /**
     * oss 关键词
     */
    private static final String KEY_WORD_OSS = "oss-cn-beijing";

    @Autowired
    private CdMallOrderFacade cdMallOrderFacade;

    @Autowired
    private IOrderExtendFinanceService extendFinanceService;

    @Autowired
    private OrderPayRecordService orderPayRecordService;

    @Autowired
    private IOrderExchangeDetailService orderExchangeDetailService;

    @Autowired
    private PoiWordHelper poiWordHelper;

    @Autowired
    private PurchaserFeign purchaserFeign;

    @Autowired
    private AgricCrmFacade agricCrmFacade;

    @Override
    public Boolean saveScenesMaterial(Integer operateRole, String operateId, String operateName, FileScenesMaterialProofDTO paramDTO) {
        if (CollectionUtils.isEmpty(paramDTO.getMaterialDTOList())) {
            return Boolean.FALSE;
        }
        BizAssertUtil.notNull(ProofSceneEnum.getValue(paramDTO.getSceneNo()), "匹配订单节点不能为空");

        // 查询订单需上传的资料列表，以订单商品维度匹配
        OrderTradeProofQueryDTO queryDTO = new OrderTradeProofQueryDTO();
        queryDTO.setOrderSn(paramDTO.getProofNo());
        queryDTO.setSceneNo(paramDTO.getSceneNo());
        if (StringUtils.isNotBlank(paramDTO.getSubProofNo())) {
            List<Long> orderProductIds = Arrays.stream(paramDTO.getSubProofNo().split(","))
                    .map(Long::valueOf).sorted().collect(Collectors.toList());
            queryDTO.setOrderProductIdList(orderProductIds);
        }
        queryDTO.setDuplicateRemove(Boolean.FALSE);
        List<OrderTradeProofVO> orderTradeProofVOS = this.matchSceneMaterials(queryDTO);
        BizAssertUtil.notEmpty(orderTradeProofVOS, "该订单需上传的资料为空，请检查！");

        // 校验资料项
        Map<String, FileMaterialDTO> materialDTOMap = paramDTO.getMaterialDTOList().stream()
                .collect(Collectors.toMap(FileMaterialDTO::getMaterialNo, Function.identity()));
        List<OrderTradeProofPO> updateUpload = new ArrayList<>(orderTradeProofVOS.size());
        Set<String> agreementNo = new HashSet<>();

        //结果数据
        BzOrderTradeProofCheckResultPO resultPO = new BzOrderTradeProofCheckResultPO();
        //需要提示的结果
        Set<String> remindResultSet = new HashSet<>();
        //所有校验的结果
        Set<String> allResultSet = new HashSet<>();
        //所有结果状态
        Set<String> resultCodeSet = new HashSet<>();
        //重复订单号
        Set<String> repeatOrderNoSet = new HashSet<>();
        // 是否上传了签收确认函，不管是纸质签还是电签都可以，上传其中之一后，另一个不是必须上传
        boolean uploadReceive = false;
        List<OrderTradeProofVO> paperReceiveDocList = orderTradeProofVOS.stream().filter(vo -> orderMaterialConfig.getReceiveMaterialNo().equals(vo.getMaterialNo())).collect(Collectors.toList());
        Map<Long, String> viewUrlMap = Maps.newHashMap();
        OrderPO orderPo = orderModel.getOrderByOrderSn(paramDTO.getProofNo());
        OrderExtendPO orderExtendPo = orderModel.getOrderExtendByOrderSn(paramDTO.getProofNo());
        if (!CollectionUtils.isEmpty(paperReceiveDocList)) {
            for (OrderTradeProofVO paperReceiveDoc : paperReceiveDocList) {
                FileMaterialDTO materialDTO = materialDTOMap.get(paperReceiveDoc.getMaterialNo());
                if (Objects.isNull(materialDTO)) {
                    continue;
                }
                List<String> materialContentList = materialDTO.getMaterialContentList();
                if (!CollectionUtils.isEmpty(materialContentList)) {
                    uploadReceive = true;
                }
            }
        }
        List<OrderTradeProofVO> eleReceiveDocList = orderTradeProofVOS.stream().filter(vo -> orderMaterialConfig.getEleReceiveMaterialNo().equals(vo.getMaterialNo())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(eleReceiveDocList)) {
            // 电签
            for (OrderTradeProofVO eleReceiveDoc : eleReceiveDocList) {
                FileMaterialDTO materialDTO = materialDTOMap.get(eleReceiveDoc.getMaterialNo());
                if (Objects.isNull(materialDTO)) {
                    continue;
                }
                ContractSearchResultVO resultVO = contractSearch(eleReceiveDoc.getId(), NumberUtils.INTEGER_ZERO);
                String viewUrl = resultVO.getViewUrl();
                if (StringUtils.isBlank(viewUrl) || viewUrl.contains(KEY_WORD_OSS)) {
                    // 必填且未上传纸质签时才需要报错
                    if (NumberUtils.INTEGER_ONE.equals(eleReceiveDoc.getRequisite()) && !uploadReceive) {
                        throw new BusinessException("尚未完成电子确认函签署，请稍候重试");
                    }
                } else {
                    viewUrlMap.put(eleReceiveDoc.getId(), viewUrl);
                }
                uploadReceive = true;
            }
        }
        for (OrderTradeProofVO orderTradeProofVO : orderTradeProofVOS) {
            String materialNo = orderTradeProofVO.getMaterialNo();
            FileMaterialDTO materialDTO = materialDTOMap.get(materialNo);

            OrderTradeProofPO update = new OrderTradeProofPO();
            // 协议内容，转换协议内容为PDF，存到文件中心
            if (OrderMaterialTypeEnum.AGREEMENT == OrderMaterialTypeEnum.getValue(orderTradeProofVO.getMaterialType())) {
                // 更新上传标记
                update.setId(orderTradeProofVO.getId());
                update.setIsUpload(Boolean.TRUE);
                updateUpload.add(update);

                // 已经生成pdf，不重复生成
                if (agreementNo.contains(materialNo)) {
                    continue;
                }
                agreementNo.add(materialNo);

                String agreementLink = agreementBuilder.agreement2PdfBuilder(orderTradeProofVO.getOrderSn(), orderTradeProofVO.getMaterialRemark());
                log.info("协议【{}】转PDF，链接为{}", orderTradeProofVO.getMaterialName(), agreementLink);
                if (StringUtils.isBlank(agreementLink)) {
                    continue;
                }
                materialDTO = new FileMaterialDTO();
                materialDTO.setMaterialNo(materialNo);
                materialDTO.setMaterialName(orderTradeProofVO.getMaterialName());
                materialDTO.setMaterialType(orderTradeProofVO.getMaterialType());
                materialDTO.setProofRemark(orderTradeProofVO.getMaterialRemark());
                materialDTO.setProviderId(paramDTO.getMaterialDTOList().get(0).getProviderId());
                materialDTO.setProviderName(paramDTO.getMaterialDTOList().get(0).getProviderName());
                materialDTO.setProviderType(paramDTO.getMaterialDTOList().get(0).getProviderType());
                materialDTO.setMaterialContentList(Collections.singletonList(agreementLink));
                paramDTO.getMaterialDTOList().add(materialDTO);
                continue;
            }

            // 校验是否必传
            if (NumberUtils.INTEGER_ONE.equals(orderTradeProofVO.getRequisite()) && (Objects.isNull(materialDTO) || CollectionUtils.isEmpty(materialDTO.getMaterialContentList()))) {
                boolean isReceiveDoc = orderMaterialConfig.getReceiveMaterialNo().equals(materialNo) || orderMaterialConfig.getEleReceiveMaterialNo().equals(materialNo);
                // 是签收确认函资料且已经有上传了某一个确认函时，不校验是否必填，且当前资料改为非必填。其他情况均需要校验必填
                if (isReceiveDoc && uploadReceive) {
                    // 修改为非必填
                    update.setId(orderTradeProofVO.getId());
                    update.setRequisite(NumberUtils.INTEGER_ZERO);
                    updateUpload.add(update);
                    continue;
                } else {
                    throw new BusinessException(String.format("资料【%s】为必传，请检查！", orderTradeProofVO.getMaterialName()));
                }
            }
            if (Objects.isNull(materialDTO)) {
                continue;
            }

            // 校验最大上传数
            if (orderTradeProofVO.getMaxUploads() < materialDTO.getMaterialContentList().size()) {
                throw new BusinessException(String.format("资料【%s】最大可上传数为%d，已超出！",
                        orderTradeProofVO.getMaterialName(), orderTradeProofVO.getMaxUploads()));
            }


            if (CHECK_GROUP_FACTORY_NO.equals(orderTradeProofVO.getCheckGroup())) {
                log.info("existFactoryNo 出厂编号：{}", materialDTO.getProofRemark());
                Result<Boolean> existFactoryNo = crawlerIntegration.existFactoryNo(materialDTO.getProofRemark());
                if (existFactoryNo.getData() != null && existFactoryNo.getData()) {
                    resultCodeSet.add(BzOrderTradeProofCheckResultEnum.SUSPECTED_SECOND_HAND.getValue());
                    allResultSet.add("经查验，该机器出厂编号已使用。");
                }
            }

            List<String> materialContentList = orderTradeProofVO.getMaterialContentList();
            if (CollectionUtils.isEmpty(materialContentList)) {
                materialContentList = Lists.newArrayList();
            }
            if (orderMaterialConfig.getReceiveMaterialNo().equals(materialNo)) {
                log.info("saveScenesMaterial 处理收货确认书start,orderTradeProofVO:{}", orderTradeProofVO);
                // 收货确认书
                updateOrderReceiveMaterialStatus(paramDTO.getProofNo(), NumberUtils.INTEGER_ONE, ReceiveConfirmDocType.PAPER_SIGN.getCode(), materialContentList.toString());
                orderLogModel.insertOrderLog(operateRole, Long.valueOf(operateId),
                        operateName, paramDTO.getProofNo(), orderPo.getOrderState(), orderPo.getOrderState(),
                        orderPo.getLoanPayState(), "收货凭证上传", OrderCreateChannel.valueOf(orderPo.getChannel()), "来源：电商系统。");
            }
            if (orderMaterialConfig.getEleReceiveMaterialNo().equals(materialNo)) {
//                ContractSearchResultVO resultVO = contractSearch(orderTradeProofVO.getId(), NumberUtils.INTEGER_ZERO);
//                String viewUrl = resultVO.getViewUrl();
//                if (StringUtils.isBlank(viewUrl) || viewUrl.contains(KEY_WORD_OSS)) {
//                    throw new BusinessException("尚未完成电子确认函签署，请稍候重试");
//                }
                String viewUrl = viewUrlMap.get(orderTradeProofVO.getId());
                if (StringUtils.isBlank(viewUrl)) {
                    log.info("{}电签未上传,不做处理", orderTradeProofVO.getId());
                    // 更新为无须上传
                    if (uploadReceive) {
                        // 修改为非必填
                        update.setId(orderTradeProofVO.getId());
                        update.setRequisite(NumberUtils.INTEGER_ZERO);
                        updateUpload.add(update);
                    }
                    continue;
                }
                // 重新生成文件dto
                FileMaterialDTO newMaterialDTO = new FileMaterialDTO();
                newMaterialDTO.setMaterialNo(materialNo);
                newMaterialDTO.setMaterialName(orderTradeProofVO.getMaterialName());
                newMaterialDTO.setMaterialType(orderTradeProofVO.getMaterialType());
                newMaterialDTO.setProofRemark(orderTradeProofVO.getMaterialRemark());
                newMaterialDTO.setProviderId(paramDTO.getMaterialDTOList().get(0).getProviderId());
                newMaterialDTO.setProviderName(paramDTO.getMaterialDTOList().get(0).getProviderName());
                newMaterialDTO.setProviderType(paramDTO.getMaterialDTOList().get(0).getProviderType());
                newMaterialDTO.setMaterialContentList(Collections.singletonList(viewUrl));
                paramDTO.getMaterialDTOList().remove(materialDTO);
                paramDTO.getMaterialDTOList().add(newMaterialDTO);
                update.setFileOriginUrl(viewUrl);
                log.info("saveScenesMaterial 处理收货确认书start,orderTradeProofVO:{}", orderTradeProofVO);
                // 收货确认书
                updateOrderReceiveMaterialStatus(paramDTO.getProofNo(), NumberUtils.INTEGER_ONE, ReceiveConfirmDocType.ELE_SIGN.getCode(), materialContentList.toString());
                orderLogModel.insertOrderLog(operateRole, Long.valueOf(operateId),
                        operateName, paramDTO.getProofNo(), orderPo.getOrderState(), orderPo.getOrderState(),
                        orderPo.getLoanPayState(), "收货凭证上传", OrderCreateChannel.valueOf(orderPo.getChannel()), "来源：电商系统。");
            }
            //将界面值传入orderTradeProofVO
            orderTradeProofVO.setProofRemark(materialDTO.getProofRemark());
            orderTradeProofVO.setMaterialContentList(materialDTO.getMaterialContentList());
            orderTradeProofVO.setOcrValue(materialDTO.getOcrValue());
            // 校验上传内容
            String[] materialType = orderTradeProofVO.getMaterialType().split(",");

            for (String type : materialType) {
                OrderMaterialTypeEnum typeEnum = OrderMaterialTypeEnum.getValue(type);
                BizAssertUtil.isTrue(NumberUtils.INTEGER_ONE.equals(orderTradeProofVO.getRequisite())
                                && OrderMaterialTypeEnum.TEXT == typeEnum && StringUtils.isBlank(materialDTO.getProofRemark()),
                        String.format("资料【%s】内容为空，请检查！", orderTradeProofVO.getMaterialName()));
                BizAssertUtil.isTrue(NumberUtils.INTEGER_ONE.equals(orderTradeProofVO.getRequisite()) &&
                                OrderMaterialTypeEnum.TEXT != typeEnum && CollectionUtils.isEmpty(materialDTO.getMaterialContentList()),
                        String.format("资料【%s】内容为空，请检查！", orderTradeProofVO.getMaterialName()));
                //图片MD5加密
                if (OrderMaterialTypeEnum.IMG == typeEnum) {
                    List<String> md5List = new ArrayList<>();
                    for (String content : materialDTO.getMaterialContentList()) {
                        String md5 = null;
                        try {
                            md5 = ExpressDeliveryUtil.calculateMD5(content);
                        } catch (NoSuchAlgorithmException e) {
                            log.error("转换MD5 NoSuchAlgorithmException", e);
                        } catch (IOException e) {
                            log.error("转换MD5 IOException", e);
                        } catch (Exception e) {
                            log.error("转换MD5 Exception", e);
                        } finally {
                            md5List.add(md5);
                        }
                        // 历史图片MD5一致性校验 需要去掉同一个订单
                        if (NumberUtils.INTEGER_ONE.equals(orderTradeProofVO.getHisPicRepeatCheck())) {
                            List<FileScenesProofVO> fileScenesProofVOMD5 = fileCenterIntegration.queryScenesMaterialProofV2(
                                    null, null, null, materialDTO.getMaterialNo(), null, md5);
                            if (!CollectionUtils.isEmpty(fileScenesProofVOMD5)) {
                                fileScenesProofVOMD5 = fileScenesProofVOMD5.stream().filter(fileScenesProofVO -> !paramDTO.getProofNo().equals(fileScenesProofVO.getProofNo())).collect(Collectors.toList());
                            }

                            if (!CollectionUtils.isEmpty(fileScenesProofVOMD5)) {
                                List<String> orderNoList = fileScenesProofVOMD5.stream().map(FileScenesProofVO::getProofNo).collect(Collectors.toList());
                                orderNoList = filterInvalidOrder(orderNoList);
                                if (!CollectionUtils.isEmpty(orderNoList)) {
                                    if (orderNoList.size() > 10) {
                                        orderNoList = orderNoList.subList(0, 9);
                                    }
                                    String result = String.format("【%s】图片与平台内过往订单【" + String.join(",", new HashSet<>(orderNoList)) + "】重复。", orderTradeProofVO.getMaterialName());
                                    repeatOrderNoSet.addAll(orderNoList);
                                    resultCodeSet.add(BzOrderTradeProofCheckResultEnum.SUSPECTED_ORDER_REPEAT.getValue());
                                    allResultSet.add(result);
                                }
                            }
                        }
                    }
                    String md5ListStr = String.join(",", md5List);
                    materialDTO.setPicEncrypt(md5ListStr);
                    orderTradeProofVO.setPicEncrypt(md5ListStr);
                }


                //历史相同的 需要去掉同一个订单
                List<FileScenesProofVO> fileScenesProofVOS = fileCenterIntegration.queryScenesMaterialProofV2(
                        null, null, null, materialDTO.getMaterialNo(), materialDTO.getProofRemark(), null);
                if (!CollectionUtils.isEmpty(fileScenesProofVOS)) {
                    fileScenesProofVOS = fileScenesProofVOS.stream().filter(fileScenesProofVO ->
                            !paramDTO.getProofNo().equals(fileScenesProofVO.getProofNo())).collect(Collectors.toList());
                }
                // 文本类型校验唯一性，下单时不校验
                if (NumberUtils.INTEGER_ONE.equals(orderTradeProofVO.getUniqueCheck())
                        && OrderMaterialTypeEnum.TEXT == typeEnum
                        && StringUtils.isNotBlank(materialDTO.getProofRemark())
                        && !ProofSceneEnum.SUBMIT.getCode().equals(paramDTO.getSceneNo())) {
//					List<FileScenesProofVO> fileScenesProofVOS = fileCenterIntegration.queryScenesMaterialProofV2(
//							null, null, null, materialDTO.getMaterialNo(), materialDTO.getProofRemark());
                    BizAssertUtil.isTrue(!CollectionUtils.isEmpty(fileScenesProofVOS),
                            String.format("资料【%s】文本内容已被使用，不可重复使用！", orderTradeProofVO.getMaterialName()));
                }
                // 历史数据一致性校验
                if (NumberUtils.INTEGER_ONE.equals(orderTradeProofVO.getHisConsistentCheck()) && !CollectionUtils.isEmpty(fileScenesProofVOS)) {
                    List<String> orderNoList = fileScenesProofVOS.stream().map(FileScenesProofVO::getProofNo).collect(Collectors.toList());
                    orderNoList = filterInvalidOrder(orderNoList);
                    if (!CollectionUtils.isEmpty(orderNoList)) {
                        if (orderNoList.size() > 10) {
                            orderNoList = orderNoList.subList(0, 9);
                        }
                        String result = String.format("【%s】-【%s】与平台内过往订单【" + String.join(",", new HashSet<>(orderNoList)) + "】重复。",
                                orderTradeProofVO.getMaterialName(), orderTradeProofVO.getCheckGroup());
                        repeatOrderNoSet.addAll(orderNoList);
                        resultCodeSet.add(BzOrderTradeProofCheckResultEnum.SUSPECTED_ORDER_REPEAT.getValue());
                        allResultSet.add(result);
                    }
                }
            }


            // 更新上传标记
            update.setId(orderTradeProofVO.getId());
            update.setIsUpload(Boolean.TRUE);
            updateUpload.add(update);
        }

        log.info("orderTradeProofVOS :{}", orderTradeProofVOS);

        //同环节其他
        Map<String, List<OrderTradeProofVO>> checkGroupMap = orderTradeProofVOS.stream()
                .filter(orderTradeProofVO -> StringUtils.isNotEmpty(orderTradeProofVO.getCheckGroup()) && StringUtils.isNotEmpty(orderTradeProofVO.getProofRemark()))
                .collect(Collectors.groupingBy(OrderTradeProofVO::getCheckGroup));
        checkGroupMap.forEach((key, list) -> {
            //下单环节 同环节 还需校验Ocr的值与实际传的字段不一致场景
            if (ProofSceneEnum.SUBMIT.getCode().equals(paramDTO.getSceneNo())) {
                sameLinkOCRCheck(list, allResultSet);
            }
            //同环节字段是否一致
            String result = sameLinkCheck(list);
            if (StringUtils.isNotBlank(result)) {
                remindResultSet.add(result);
                allResultSet.add(result);
            }
        });
        //同环节图片
        String sameLinkCheckPicResult = sameLinkCheckPic(orderTradeProofVOS);
        if (StringUtils.isNotBlank(sameLinkCheckPicResult)) {
            remindResultSet.add(sameLinkCheckPicResult);
            allResultSet.add(sameLinkCheckPicResult);
        }

        //不同环节
        List<OrderTradeProofVO> orderTradeProofVOAll = this.listSceneMaterial(queryDTO.getOrderSn(), null, null, null);
        //把本环节数据过滤掉，再添加重新添加本环节数据
        orderTradeProofVOAll = orderTradeProofVOAll.stream().filter(orderTradeProofVO -> !paramDTO.getSceneNo().equals(orderTradeProofVO.getSceneNo())).collect(Collectors.toList());
        orderTradeProofVOAll.addAll(orderTradeProofVOS);
        Map<String, List<OrderTradeProofVO>> checkGroupMapAll = orderTradeProofVOAll.stream()
                .filter(orderTradeProofVO -> StringUtils.isNotEmpty(orderTradeProofVO.getCheckGroup()) && StringUtils.isNotEmpty(orderTradeProofVO.getProofRemark()))
                .collect(Collectors.groupingBy(OrderTradeProofVO::getCheckGroup));
        checkGroupMapAll.forEach((key, list) -> {
            String result = difLinkCheck(list);

            if (StringUtils.isNotBlank(result)) {
                allResultSet.add(result);
                resultCodeSet.add(BzOrderTradeProofCheckResultEnum.SUSPECTED_ORDER_REPEAT.getValue());
            }

        });

        //其他场景编码
        if (CollectionUtils.isEmpty(resultCodeSet)) {
            resultPO.setResultCode(JSON.toJSONString(Collections.singletonList(BzOrderTradeProofCheckResultEnum.NORMAL.getValue())));
        } else {
            resultPO.setResultCode(JSON.toJSONString(resultCodeSet));
        }
        if (repeatOrderNoSet.size() > 10) {
            List<String> repeatOrderNolist = new ArrayList<>(repeatOrderNoSet);
            //取前十个
            List<String> aheadTenItems = repeatOrderNolist.subList(0, 9);
            resultPO.setRepeatOrderNo(JSON.toJSONString(aheadTenItems));
        } else {
            resultPO.setRepeatOrderNo(JSON.toJSONString(repeatOrderNoSet));
        }

        resultPO.setOrderSn(paramDTO.getProofNo());
        resultPO.setSceneNo(paramDTO.getSceneNo());
        resultPO.setSceneName(ProofSceneEnum.getValue(paramDTO.getSceneNo()).getDesc());
        resultPO.setRequest(JSON.toJSONString(paramDTO));
        resultPO.setInterruptResult(JSON.toJSONString(remindResultSet));
        resultPO.setAllResult(JSON.toJSONString(allResultSet));
        resultPO.setResultStatus(CollectionUtils.isEmpty(allResultSet) ? NumberUtils.INTEGER_ONE : NumberUtils.INTEGER_ZERO);
        proofCheckResultService.save(resultPO);

        if (!CollectionUtils.isEmpty(remindResultSet)) {
            throw new BusinessException(String.join(";", remindResultSet));
        }
        List<FileMaterialDTO> materialDTOList = paramDTO.getMaterialDTOList();
        materialDTOList.forEach(file -> {
            List<String> materialContentList = file.getMaterialContentList();
            if (!orderMaterialConfig.getReceiveMaterialNo().equals(file.getMaterialNo())) {
                return;
            }
            Map<String, Integer> riskLevel = Maps.newHashMap();
            Map<String, String> riskReason = Maps.newHashMap();
            if (CollectionUtils.isEmpty(materialContentList)) {
                return;
            }
            materialContentList.removeIf(Objects::isNull);
            if (!CollectionUtils.isEmpty(materialContentList)) {
                for (String content : materialContentList) {
                    if (orderMaterialConfig.getReceiveMaterialNo().equals(file.getMaterialNo())) {
                        Integer risk = NumberUtils.INTEGER_ZERO;
                        String reason = "";
                        try {
                            ReceiveMaterialResultDTO result = checkReceiveMaterial(paramDTO.getProofNo(), content);
                            risk = result.getLevel();
                            reason = StringUtils.join(result.getRiskReason(), ",");
                        } catch (Exception exp) {
                            log.warn("判定资料风险等级异常,exp:{}", exp.getMessage());
                        }
                        riskLevel.put(content, risk);
                        riskReason.put(content, reason);
                    } else {
                        riskLevel.put(content, NumberUtils.INTEGER_ZERO);
                        riskReason.put(content, "");
                    }
                }
            }
            file.setRiskLevelMap(riskLevel);
            file.setRiskReason(riskReason);
        });

        log.info("saveScenesMaterial final paramDTO:{}", paramDTO);
        // 调用文件中心上传资料
        fileCenterIntegration.saveScenesMaterialProofV2(paramDTO);

        // 更新上传标记
        this.updateBatchById(updateUpload);

        return Boolean.TRUE;
    }

    /**
     * 收货确认书状态更新
     *
     * @param proofNo 编号
     * @param status  状态
     */
    private void updateOrderReceiveMaterialStatus(String proofNo, Integer status, Integer signDocType, String signDocUrl) {
        if (StringUtils.isBlank(proofNo) || Objects.isNull(status)) {
            log.warn("updateOrderReceiveMaterialStatus 失败，订单编号或者资料为空");
            return;
        }
        log.info("updateOrderReceiveMaterialStatus 匹配成功，开始更新订单收货确认书提交状态");
        LambdaUpdateWrapper<OrderExtendPO> orderExtendUpdate = Wrappers.lambdaUpdate(OrderExtendPO.class);
        orderExtendUpdate.eq(OrderExtendPO::getOrderSn, proofNo);
        orderExtendUpdate.set(OrderExtendPO::getReceiveMaterialStatus, status);
        orderExtendUpdate.set(OrderExtendPO::getReceiveConfirmDocType, signDocType);
        orderExtendUpdate.set(StringUtils.isNotBlank(signDocUrl), OrderExtendPO::getReceiveConfirmDocUrl, signDocUrl);
        boolean update = orderExtendService.update(orderExtendUpdate);
        try {
            // 同步交易中心
            aresTradeFacade.receiveConfirmStatusSync(com.cfpamf.ares.trade.common.enums.OrderSourceEnum.STANDARD_MALL.getOrderSourceCode(), proofNo, status);
        } catch (Exception e) {
            log.warn("updateOrderReceiveMaterialStatus 同步交易中心失败");
        }
        log.info("updateOrderReceiveMaterialStatus result :{}", update);
    }

    /**
     * 收货确认书风险识别
     *
     * @param fileUrl 文件地址
     * @return 风险识别结果
     */
    @Override
    public ReceiveMaterialResultDTO checkReceiveMaterial(String orderSn, String fileUrl) {
        OrderPO orderPo = orderModel.getOrderByOrderSn(orderSn);
        OrderExtendPO orderExtendPo = orderModel.getOrderExtendByOrderSn(orderSn);
        return checkReceiveMaterialWithOrder(orderPo, orderExtendPo, fileUrl);

    }

    private ReceiveMaterialResultDTO checkReceiveMaterialWithOrder(OrderPO orderPo, OrderExtendPO orderExtendPo, String fileUrl) {
        if (Objects.isNull(orderPo) || Objects.isNull(orderExtendPo)) {
            throw new BusinessException("未找到对应订单");
        }
        ReceiveMaterialResultDTO resultDTO = new ReceiveMaterialResultDTO();
        List<String> riskReason = Lists.newArrayList();
        Integer riskLevel = NumberUtils.INTEGER_ZERO;
        OcrResult<OCRWordResult> result = ocrFacade.normalWordCheck(fileUrl, "false", System.currentTimeMillis() + "_newmall", "newmall");
        if (!result.getStatus().equals(OCR_SUCCESS_STATUS)) {
            resultDTO.setLevel(OcrConstant.HIGH_RISK);
            riskReason.add("ocr解析失败");
            resultDTO.setRiskReason(riskReason);
            return resultDTO;
        }
        OCRWordResult data = result.getData();
        List<WordResult> wordsResult = data.getWordsResult();
        if (CollectionUtils.isEmpty(wordsResult)) {
            resultDTO.setLevel(OcrConstant.HIGH_RISK);
            riskReason.add("ocr解析失败");
            resultDTO.setRiskReason(riskReason);
            return resultDTO;
        }
        StringBuilder sb = new StringBuilder();
        boolean matchOrderSn = false;
        boolean matchAddress = false;
        for (int i = 0; i < wordsResult.size(); i++) {
            WordResult wordResult = wordsResult.get(i);
            // 拼接结果，与预期结果进行对比
            String word = wordResult.getWords();
            sb.append(word);
            if (!matchAddress && CosineSimilarityUtil.computeCosineSimilarity(orderExtendPo.getReceiverInfo(), word) > 0.7) {
                // 与地址匹配达到0.7视为地址正确
                matchAddress = true;
            }
            if (!matchOrderSn && CosineSimilarityUtil.computeCosineSimilarity(orderPo.getOrderSn(), word) > 0.9) {
                // 与订单编号匹配达到0.9视为订单编号正确
                matchOrderSn = true;
            }
        }
        if (!matchAddress) {
            riskReason.add("地址不匹配");
            riskLevel++;
        }
        if (!matchOrderSn) {
            riskReason.add("订单编号不匹配");
            riskLevel++;
        }
        double matchRatio = CosineSimilarityUtil.computeCosineSimilarity(sb.toString(), orderMaterialConfig.getOriginContext());
        if (orderMaterialConfig.getMatchRatio().compareTo(new BigDecimal(matchRatio)) > 0) {
            riskReason.add("文本整体匹配度较低");
            riskLevel++;
        }
        resultDTO.setLevel(riskLevel);
        resultDTO.setRiskReason(riskReason);
        log.info("checkReceiveMaterial success,result:{}", resultDTO);
        return resultDTO;
    }

    private static void sameLinkOCRCheck(List<OrderTradeProofVO> list, Set<String> allResultSet) {
        for (OrderTradeProofVO item : list) {
            String checkResult = null;
            //不需要校验的 不参与校验
            if (!NumberUtils.INTEGER_ONE.equals(item.getSameLinkConsistentCheck())) {
                continue;
            }
            if (StringUtils.isNotEmpty(item.getOcrValue()) && !Objects.equals(item.getProofRemark(), item.getOcrValue())) {
                checkResult = String.format("%s资料-%s用户有修改，请核查与图片是否相符", item.getMaterialName(), item.getCheckGroup());
            }
            if (StringUtils.isNotBlank(checkResult)) {
                allResultSet.add(checkResult);
            }
        }
    }

    private List<String> filterInvalidOrder(List<String> orderNoList) {
        LambdaQueryWrapper<OrderPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(OrderPO::getOrderSn, orderNoList);
        List<OrderPO> orderPOList = orderMapper.selectList(queryWrapper);
        orderNoList = orderPOList.stream()
                .filter(orderPO -> !OrderStatusEnum.isClosed(orderPO.getOrderState())
                        && !OrderStatusEnum.WAIT_PAY.getValue().equals(orderPO.getOrderState())
                        && !OrderStatusEnum.WAIT_PAY_DEPOSIT.getValue().equals(orderPO.getOrderState()))
                .map(OrderPO::getOrderSn).collect(Collectors.toList());
        return orderNoList;
    }

    private String difLinkCheck(List<OrderTradeProofVO> list) {
        String oriValue = null;
        if (list.size() <= 1) {
            return null;
        }
        List<String> materialShowNameList = list.stream().map(orderTradeProofVO -> orderTradeProofVO.getSceneName() + "-" + orderTradeProofVO.getMaterialName())
                .collect(Collectors.toList());
        for (OrderTradeProofVO item : list) {
            //不需要校验的 不参与校验
            if (!NumberUtils.INTEGER_ONE.equals(item.getDifLinkConsistentCheck())) {
                continue;
            }
            String proofRemark = item.getProofRemark();
            if (oriValue == null) {
                oriValue = proofRemark;
            } else if (!oriValue.equals(proofRemark)) {
                return String.join("、", materialShowNameList) + "不一致";

            }
        }
        return null;
    }

    private static String sameLinkCheck(List<OrderTradeProofVO> list) {
        String oriValue = null;
        if (list.size() <= 1) {
            return null;
        }
        List<String> materialNameList = list.stream()
                .map(orderTradeProofVO -> orderTradeProofVO.getMaterialName() + "-" + orderTradeProofVO.getCheckGroup() + "-" + orderTradeProofVO.getProofRemark())
                .collect(Collectors.toList());
        for (OrderTradeProofVO item : list) {
            //不需要校验的 不参与校验
            if (!NumberUtils.INTEGER_ONE.equals(item.getSameLinkConsistentCheck())) {
                continue;
            }
            String proofRemark = item.getProofRemark();
            if (oriValue == null) {
                oriValue = proofRemark;
            } else if (!oriValue.equals(proofRemark)) {
                return String.join("、", materialNameList) + "不一致，请重新上传！";
            }
        }
        return null;
    }

    private static String sameLinkCheckPic(List<OrderTradeProofVO> list) {
        if (list.size() <= 1) {
            return null;
        }

        List<OrderTradeProofVO> orderTradeProofVOList = list.stream().filter(orderTradeProofVO -> StringUtils.isNotEmpty(orderTradeProofVO.getPicEncrypt())
                        && NumberUtils.INTEGER_ONE.equals(orderTradeProofVO.getSameLinkPicRepeatCheck()))
                .collect(Collectors.toList());
        Map<String, List<OrderTradeProofVO>> compareValue = new HashMap<>();
        orderTradeProofVOList.forEach(vo -> {
            String picEncrypt = vo.getPicEncrypt();
            if (StringUtils.isNotEmpty(picEncrypt)) {
                String[] split = picEncrypt.split(",");
                for (int i = 0; i < split.length; i++) {
                    String picKey = split[i] + vo.getOrderProductId();
                    if (!compareValue.containsKey(picKey)) {
                        compareValue.put(picKey, new ArrayList<>());
                    }
                    compareValue.get(picKey).add(vo);
                }
            }

        });
        for (Map.Entry<String, List<OrderTradeProofVO>> entry : compareValue.entrySet()) {
            List<OrderTradeProofVO> v = entry.getValue();
            if (!CollectionUtils.isEmpty(v) && v.size() > 1) {
                List<String> materialNameList = v.stream().map(OrderTradeProofVO::getMaterialName).collect(Collectors.toList());
                return String.join("、", materialNameList) + "图片一致，请重新上传！";
            }
        }
        return null;
    }

    @Override
    public List<FileScenesProofVO> queryScenesMaterial(String proofNo, String subProofNo, String sceneNo, String materialNo, Boolean showAgreement) {
        BizAssertUtil.isTrue(StringUtils.isBlank(proofNo), "凭证编号不能为空");
        // 查询文件中心资料
        List<FileScenesProofVO> fileScenesProofVOS = fileCenterIntegration.queryScenesMaterialProofV2(proofNo, subProofNo, sceneNo, materialNo, null, null);
        if (CollectionUtils.isEmpty(fileScenesProofVOS)) {
            return fileScenesProofVOS;
        }
        // 不展示协议，移出
        if (!showAgreement) {
            for (FileScenesProofVO fileScenesProofVO : fileScenesProofVOS) {
                for (FileScenesProofMaterialVO scenesProofMaterialVO : fileScenesProofVO.getMaterialVOList()) {
                    if (OrderMaterialTypeEnum.AGREEMENT == OrderMaterialTypeEnum.getValue(scenesProofMaterialVO.getMaterialType())) {
                        fileScenesProofVO.getMaterialVOList().remove(scenesProofMaterialVO);
                        break;
                    }
                }
            }
        }
        return fileScenesProofVOS;
    }

    @Override
    public List<FileScenesProductWithResultVO> queryScenesMaterialWithResult(String proofNo, String subProofNo, String sceneNo, Boolean showAgreement) {
        List<FileScenesProofVO> fileScenesProofVOS = this.queryScenesMaterial(proofNo, null, null, null, true);

        List<FileScenesProductWithResultVO> fileScenesProductWithResultVOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(fileScenesProofVOS)) {
            LambdaQueryWrapper<BzOrderTradeProofCheckResultPO> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(BzOrderTradeProofCheckResultPO::getOrderSn, proofNo);
            queryWrapper.eq(BzOrderTradeProofCheckResultPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
            queryWrapper.orderByDesc(BzOrderTradeProofCheckResultPO::getId);
            List<BzOrderTradeProofCheckResultPO> resultPOList = proofCheckResultService.list(queryWrapper);

            rebuildFileScenesProofWithResultVO(resultPOList, fileScenesProofVOS, fileScenesProductWithResultVOList);

        }
        return fileScenesProductWithResultVOList;
    }

    private static void rebuildFileScenesProofWithResultVO(List<BzOrderTradeProofCheckResultPO> resultPOList, List<FileScenesProofVO> fileScenesProofVOS, List<FileScenesProductWithResultVO> fileScenesProductWithResultVOList) {
        if (resultPOList == null) {
            resultPOList = new ArrayList<>();
        }
        Map<String, List<BzOrderTradeProofCheckResultPO>> sceneMap = resultPOList.stream().collect(Collectors.groupingBy(BzOrderTradeProofCheckResultPO::getSceneNo));
        fileScenesProofVOS.forEach(proof -> {
            FileScenesProductWithResultVO withResultVO = new FileScenesProductWithResultVO();
            List<BzOrderTradeProofCheckResultVO> checkResultVOList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(sceneMap.get(proof.getSceneNo()))) {
                BzOrderTradeProofCheckResultPO checkResultPO = sceneMap.get(proof.getSceneNo()).get(0);
                BzOrderTradeProofCheckResultVO checkResultVO = new BzOrderTradeProofCheckResultVO();
                BeanUtils.copyProperties(checkResultPO, checkResultVO);
                checkResultVO.setAllResult(JSON.parseArray(checkResultPO.getAllResult(), String.class));
                checkResultVO.setInterruptResult(JSON.parseArray(checkResultPO.getInterruptResult(), String.class));
                checkResultVO.setRepeatOrderNoList(JSON.parseArray(checkResultPO.getRepeatOrderNo(), String.class));
                checkResultVOList.add(checkResultVO);
            }
            BeanUtils.copyProperties(proof, withResultVO);
            withResultVO.setProofCheckResultVOList(checkResultVOList);
            fileScenesProductWithResultVOList.add(withResultVO);
        });
    }

    @Override
    public List<OrderTradeProofVO> listSceneMaterial(String orderSn, Long orderProductId, String materialNo, ProofSceneEnum sceneEnum) {
        // 查询订单需上传的资料列表
        String sceneNo = sceneEnum == null ? null : sceneEnum.getCode();
        List<OrderTradeProofVO> orderTradeProofVOS =
                orderTradeProofMapper.queryOrderProofList(orderSn, Objects.isNull(orderProductId) ?
                        null : Collections.singletonList(orderProductId), sceneNo, null, materialNo);
        if (CollectionUtils.isEmpty(orderTradeProofVOS)) {
            return null;
        }
        Map<String, Integer> riskLevel = Maps.newHashMap();
        // 判定是否需要判断风险，目前只需要判断收货确认书的风险
        for (OrderTradeProofVO orderTradeProofVO : orderTradeProofVOS) {
            if (orderMaterialConfig.getReceiveMaterialNo().equals(orderTradeProofVO.getMaterialNo())) {
                orderTradeProofVO.setNeedCheckRisk(Boolean.TRUE);
            } else {
                orderTradeProofVO.setNeedCheckRisk(Boolean.FALSE);
            }
        }
        // 固定查询签收场景是否有纸签资料，用于决定是否生成纸质签资料
        List<OrderTradeProofVO> temp = orderTradeProofMapper.queryOrderProofList(orderSn, null, ProofSceneEnum.RECEIVE.getCode(), null, null);
        boolean containsEle = temp.stream().anyMatch(vo -> vo.getMaterialNo().equals(orderMaterialConfig.getEleReceiveMaterialNo()));
        boolean containsPaper = temp.stream().anyMatch(vo -> vo.getMaterialNo().equals(orderMaterialConfig.getReceiveMaterialNo()));
        if (containsEle && StringUtils.isNotBlank(orderSn) && !containsPaper){
            OrderPO orderPO = orderService.getByOrderSn(orderSn);
            if (checkProofLoanPay(orderPO)){
                log.info("开始增加纸签文件,orderSn:{},orderProductId:{},materialNo:{},sceneEnum:{}",orderSn,orderProductId,materialNo,sceneEnum);
                // 贷款类订单，有电签，没有纸签，添加纸签
                OrderTradeProofPO paperProofPO = new OrderTradeProofPO();
                paperProofPO.setOrderSn(orderSn);
                if (Objects.nonNull(orderProductId)){
                    paperProofPO.setOrderProductId(orderProductId);
                }
                paperProofPO.setMaterialNo(orderMaterialConfig.getReceiveMaterialNo());
                paperProofPO.setMaterialName("收货确认书");
                paperProofPO.setMaterialType("2");
                paperProofPO.setMaterialRemark("请打印收货函模版后拍照上传");
                paperProofPO.setExample(orderMaterialConfig.getPaperExample());
                paperProofPO.setRequisite(0);
                paperProofPO.setUniqueCheck(0);
                paperProofPO.setSameLinkPicRepeatCheck(0);
                paperProofPO.setDifLinkConsistentCheck(0);
                paperProofPO.setHisPicRepeatCheck(0);
                paperProofPO.setSceneNo(sceneNo);
                paperProofPO.setSceneName(ProofSceneEnum.RECEIVE.getDesc());
                if (Objects.nonNull(sceneEnum)){
                    paperProofPO.setSceneName(sceneEnum.getDesc());
                }
                paperProofPO.setIsUpload(false);
                paperProofPO.setCreateBy("auto-");
                paperProofPO.setUpdateBy("auto-");
                paperProofPO.setEleSignInfo("");
                paperProofPO.setNeedFaceAuth(0);
                save(paperProofPO);
                // 重新查一次
                orderTradeProofVOS =
                        orderTradeProofMapper.queryOrderProofList(orderSn, Objects.isNull(orderProductId) ?
                                null : Collections.singletonList(orderProductId), sceneNo, null, materialNo);
            }
        }
        // 查询文件中心资料
        List<FileScenesProofVO> fileScenesProofVOS = fileCenterIntegration.queryScenesMaterialProofV2(orderSn, Objects.isNull(orderProductId) ?
                null : orderProductId.toString(), sceneNo, materialNo, null, null);
        if (CollectionUtils.isEmpty(fileScenesProofVOS)) {
            return orderTradeProofVOS;
        }

        // 已上传资料，返回资料内容
        Map<String, FileScenesProofMaterialVO> materialVOMap = fileScenesProofVOS.get(0).getMaterialVOList().stream()
                .collect(Collectors.toMap(FileScenesProofMaterialVO::getMaterialNo, Function.identity()));
        for (OrderTradeProofVO orderTradeProofVO : orderTradeProofVOS) {
            FileScenesProofMaterialVO scenesProofMaterialVO = materialVOMap.get(orderTradeProofVO.getMaterialNo());
            if (Objects.isNull(scenesProofMaterialVO)) {
                continue;
            }
            // 电签需要实时查询合同地址
            if (orderTradeProofVO.getMaterialNo().equals(orderMaterialConfig.getEleReceiveMaterialNo()) || orderTradeProofVO.getMaterialNo().equals(orderMaterialConfig.getAgricDealerMaterialNo())) {
                String contractNo = orderTradeProofVO.getContractNo();
                // 查询合同
                ContractFindRequest findRequest = new ContractFindRequest();
                findRequest.setContractNo(contractNo);
                findRequest.setContractExpireHour(1L);
                try {
                    List<ContractFindResponse> contractResponse = ExternalApiUtil.callResultApi(() -> contractFacade.findAllContract(findRequest), findRequest, "/contract/common/findAllContract", "合同查询");
                    String viewURL = contractResponse.get(0).getViewURL();
                    List<String> materialContentList = scenesProofMaterialVO.getMaterialContentList();
                    materialContentList.clear();
                    materialContentList.add(viewURL);
                    scenesProofMaterialVO.setMaterialContentList(materialContentList);
                } catch (Exception e) {
                    log.error("listSceneMaterial 调用合同服务失败,orderTradeProofVO:{}", orderTradeProofVO);
                }
            }
            orderTradeProofVO.setMaterialContentList(scenesProofMaterialVO.getMaterialContentList());
            orderTradeProofVO.setProofRemark(scenesProofMaterialVO.getProofRemark());
            orderTradeProofVO.setPicEncrypt(scenesProofMaterialVO.getPicEncrypt());
            Map<String, Integer> riskLevelMap = scenesProofMaterialVO.getRiskLevelMap();
            Map<String, String> riskReason = scenesProofMaterialVO.getRiskReason();
            List<String> materialContentList = scenesProofMaterialVO.getMaterialContentList();
            List<MaterialContentDTO> materialContentDTOList = materialContentList.stream().map(content -> {
                MaterialContentDTO contentDTO = new MaterialContentDTO();
                contentDTO.setUrl(content);
                if (Objects.isNull(riskLevelMap)) {
                    contentDTO.setRiskLevel(NumberUtils.INTEGER_ZERO);
                } else {
                    contentDTO.setRiskLevel(riskLevelMap.getOrDefault(content, NumberUtils.INTEGER_ZERO));
                }
                if (Objects.isNull(riskReason)) {
                    contentDTO.setRiskReason(Lists.newArrayList());
                } else {
                    if (riskReason.containsKey(content)) {
                        String reason = riskReason.get(content);
                        if (StringUtils.isBlank(reason)) {
                            contentDTO.setRiskReason(Lists.newArrayList());
                        } else {
                            List<String> reasonList = Arrays.asList(reason.split(","));
                            contentDTO.setRiskReason(reasonList);
                        }
                    } else {
                        contentDTO.setRiskReason(Lists.newArrayList());
                    }
                }
                return contentDTO;
            }).collect(Collectors.toList());
            orderTradeProofVO.setMaterialContentDTOList(materialContentDTOList);
        }

        return orderTradeProofVOS;
    }

    @Override
    public List<OrderTradeProofVO> matchSceneMaterials(OrderTradeProofQueryDTO queryDTO) {
        String orderSn = queryDTO.getOrderSn();
        if (StringUtils.isNotBlank(orderSn)) {
            String lockKey = "matchSceneMaterials_" + orderSn;
            log.info("matchSceneMaterials 加锁,lockKey:{}", lockKey);
            return distributeLock.lockAndProcess(lockKey, 10, 10, TimeUnit.SECONDS, () -> realMatchMaterials(queryDTO));
        } else {
            return realMatchMaterials(queryDTO);
        }
    }

    /**
     * 资料匹配
     *
     * @param queryDTO
     * @return
     */
    private List<OrderTradeProofVO> realMatchMaterials(OrderTradeProofQueryDTO queryDTO) {
        // 参数校验
        BizAssertUtil.notNull(ProofSceneEnum.getValue(queryDTO.getSceneNo()), "匹配订单节点不能为空");
        BizAssertUtil.isTrue(StringUtils.isBlank(queryDTO.getOrderSn())
                && CollectionUtils.isEmpty(queryDTO.getProductIdList()), "商品集合不能为空");

        Map<Long, OrderProductPO> orderProductPOMap = new HashMap<>();

        String orderSn = queryDTO.getOrderSn();
        OrderPO orderPO = null;
        if (StringUtils.isNotBlank(queryDTO.getOrderSn())) {

            // 适配预付订单尾款用呗支付，预售订单需要转成订单号
            if (queryDTO.getOrderSn().startsWith(SeqEnum.PONO.prefix())) {
                LambdaQueryWrapper<OrderPresellPO> presellQuery = Wrappers.lambdaQuery();
                presellQuery.eq(OrderPresellPO::getPayNo, queryDTO.getOrderSn());
                OrderPresellPO orderPresellPO = orderPresellService.getOne(presellQuery);
                BizAssertUtil.notNull(orderPresellPO, "支付单号对应的预付单不存在：" + queryDTO.getOrderSn());

                orderSn = orderPresellPO.getOrderSn();
            }

            // 订单维度查询已有资料
            if (CollectionUtils.isEmpty(queryDTO.getOrderProductIdList())) {
                List<OrderTradeProofVO> proofVOS = this.listSceneMaterial(orderSn,
                        null,
                        null,
                        Objects.requireNonNull(ProofSceneEnum.getValue(queryDTO.getSceneNo())));
                // 已匹配保存
                if (!CollectionUtils.isEmpty(proofVOS)) {
                    // 资料去重返回
                    if (queryDTO.getDuplicateRemove()) {
                        Set<OrderTradeProofVO> proofVOSetSet = new TreeSet<>(Comparator.comparing(OrderTradeProofVO::getMaterialNo));
                        proofVOSetSet.addAll(proofVOS);
                        return new ArrayList<>(proofVOSetSet);
                    }
                    return proofVOS;
                }
            }

            // 根据订单号查询订单信息
            LambdaQueryWrapper<OrderPO> queryOrder = Wrappers.lambdaQuery();
            queryOrder.eq(OrderPO::getOrderSn, orderSn)
                    .select(OrderPO::getOrderSn, OrderPO::getStoreId, OrderPO::getPaymentCode,
                            OrderPO::getOrderAmount, OrderPO::getOrderPattern, OrderPO::getPerformanceModes,OrderPO::getPaySn);
            orderPO = orderService.getOne(queryOrder);

            OrderExtendPO extendByOrderSn = orderExtendService.getOrderExtendByOrderSn(orderSn);
            BizAssertUtil.notNull(orderPO, "订单信息为空");
            queryDTO.setOrderAmount(orderPO.getOrderAmount());
            queryDTO.setOrderPattern(orderPO.getOrderPattern());
            queryDTO.setPaymentCode(orderPO.getPaymentCode());
            queryDTO.setStoreId(orderPO.getStoreId());
            queryDTO.setPerformanceModes(orderPO.getPerformanceModes());
            queryDTO.setZone(extendByOrderSn.getZoneCode());
            queryDTO.setBranch(extendByOrderSn.getBranch());

            // 查询订单商品信息
            LambdaQueryWrapper<OrderProductPO> orderProductQuery = Wrappers.lambdaQuery();
            orderProductQuery.eq(OrderProductPO::getOrderSn, orderSn)
                    .eq(OrderProductPO::getIsGift, OrderConst.IS_GIFT_NO)
                    .select(OrderProductPO::getOrderProductId, OrderProductPO::getProductId);
            if (!CollectionUtils.isEmpty(queryDTO.getOrderProductIdList())) {
                orderProductQuery.in(OrderProductPO::getOrderProductId, queryDTO.getOrderProductIdList());
            }
            List<OrderProductPO> orderProductPOS = orderProductService.list(orderProductQuery);
            BizAssertUtil.notEmpty(orderProductPOS, "订单商品为空");
            // 商品ID
            List<Long> productIdList = orderProductPOS.stream().map(OrderProductPO::getProductId).collect(Collectors.toList());
            queryDTO.setProductIdList(productIdList);
            // 订单商品map
            orderProductPOMap = orderProductPOS.stream()
                    .collect(Collectors.toMap(OrderProductPO::getProductId, item -> item, (v1, v2) -> (v1)));
        } else {
            queryDTO.setPaymentCode(PayMethodEnum.ONLINE.getValue());
        }

        // 查询商品分类
        List<Product> productList = productFeignClient.getProductListByProductIds(queryDTO.getProductIdList());
        BizAssertUtil.notEmpty(productList, "商品为空，请检查");
        Map<Long, Product> productMap = productList.stream()
                .collect(Collectors.toMap(Product::getProductId, Function.identity()));

        // 以商品行调用，取返回资料的并集
        List<OrderTradeProofVO> orderTradeProofVOS = new ArrayList<>();
        List<OrderTradeProofPO> orderTradeProofPOS = new ArrayList<>();
        Set<String> materialNoSet = new HashSet<>();
        boolean hasEle = Boolean.FALSE;
        if (StringUtils.isNotBlank(queryDTO.getOrderSn())) {
            LambdaQueryWrapper<OrderTradeProofPO> proofQuery = Wrappers.lambdaQuery(OrderTradeProofPO.class);
            proofQuery.eq(OrderTradeProofPO::getOrderSn, orderSn);
            proofQuery.eq(OrderTradeProofPO::getMaterialNo, orderMaterialConfig.getEleReceiveMaterialNo());
            List<OrderTradeProofPO> list = list(proofQuery);
            if (!CollectionUtils.isEmpty(list)) {
                hasEle = Boolean.TRUE;
            }
        }
        boolean eleReceive = Boolean.FALSE;
        boolean paperReceive = Boolean.FALSE;
        for (Long productId : queryDTO.getProductIdList()) {
            // 订单、商品维度查询已有资料
            OrderProductPO orderProductPO = orderProductPOMap.get(productId);
            if (Objects.nonNull(orderProductPO) && !CollectionUtils.isEmpty(queryDTO.getOrderProductIdList())) {
                List<OrderTradeProofVO> existProofVOs = this.listSceneMaterial(orderSn,
                        orderProductPO.getOrderProductId(), null, Objects.requireNonNull(ProofSceneEnum.getValue(queryDTO.getSceneNo())));
                log.info("matchSceneMaterials query existProofVOs:{}", JSONObject.toJSONString(existProofVOs));
                // 已匹配保存
                if (!CollectionUtils.isEmpty(existProofVOs)) {
                    orderTradeProofVOS.addAll(existProofVOs);
                    continue;
                }
            }

            Product product = productMap.get(productId);
            BizAssertUtil.notNull(product, "商品不存在请检查，商品ID：" + productId);
            queryDTO.setGoodsCategoryId1(product.getCategoryId1().toString());
            queryDTO.setGoodsCategoryId2(product.getCategoryId2().toString());
            queryDTO.setGoodsCategoryId3(product.getCategoryId3().toString());

            // 调用system匹配规则
            TradeDocumentVo tradeDocumentVo = tradeDocumentIntegration.matchOrderSceneMaterials(queryDTO);
            log.info("matchSceneMaterials with query:{},result:{}",
                    JSONObject.toJSONString(queryDTO), JSONObject.toJSONString(tradeDocumentVo));
            if (Objects.isNull(tradeDocumentVo) || CollectionUtils.isEmpty(tradeDocumentVo.getDocumentList())) {
                continue;
            }
            List<DocumentVo> documentList = tradeDocumentVo.getDocumentList();
            // 互斥规则，因为没有优先级，所以无论互斥规则在哪个资料配置中，统一收集即可
            List<String> mutualRule = documentList.stream().map(DocumentVo::getMutualRule).filter(StringUtils::isNotBlank).map(rule -> rule.split(",")).flatMap(Arrays::stream).collect(Collectors.toList());
            for (DocumentVo documentVo : tradeDocumentVo.getDocumentList()) {
                // 订单维度的资料校验不重复保存，订单商品维度的资料保存
                if (materialNoSet.contains(documentVo.getCode()) && (orderMaterialConfig.getEleReceiveMaterialNo().equals(documentVo.getCode()) || orderMaterialConfig.getReceiveMaterialNo().equals(documentVo.getCode()) || CollectionUtils.isEmpty(queryDTO.getOrderProductIdList()))) {
                    continue;
                }
                if (mutualRule.contains(documentVo.getCode())) {
                    // 互斥
                    log.warn("matchSceneMaterials 互斥生效，{}被排除", documentVo);
                    continue;
                }
                materialNoSet.add(documentVo.getCode());
                if (orderMaterialConfig.getEleReceiveMaterialNo().equals(documentVo.getCode())) {
                    eleReceive = true;
                    if (hasEle) {
                        // 避免产生多个
                        continue;
                    }
                    hasEle = true;
                    // 生成合同
//                    generateContract(orderSn, documentVo.getCode());
                }
                if (orderMaterialConfig.getReceiveMaterialNo().equals(documentVo.getCode())) {
                    paperReceive = true;
                }

                // 资料信息校验
                String[] types = documentVo.getTypes().split(",");
                for (String type : types) {
                    BizAssertUtil.isTrue(OrderMaterialTypeEnum.getValue(type) == null, "资料类型配置有误，请确认");
                }

                // 封装返回资料信息
                OrderTradeProofVO orderTradeProofVO = new OrderTradeProofVO();
                orderTradeProofVO.setOrderSn(orderSn);
                orderTradeProofVO.setMaterialNo(documentVo.getCode());
                orderTradeProofVO.setMaterialName(documentVo.getName());
                orderTradeProofVO.setMaterialType(documentVo.getTypes());
                orderTradeProofVO.setMaterialRemark(documentVo.getInstruction());
                orderTradeProofVO.setExample(documentVo.getExample());
                orderTradeProofVO.setRequisite(new Integer(documentVo.getRequired()));
                orderTradeProofVO.setUniqueCheck(new Integer(documentVo.getUniqueCheck()));
                orderTradeProofVO.setSceneNo(queryDTO.getSceneNo());
                orderTradeProofVO.setSceneName(ProofSceneEnum.getValue(queryDTO.getSceneNo()).getDesc());
                orderTradeProofVO.setIsUpload(false);
                orderTradeProofVO.setMaxUploads(9);
                orderTradeProofVO.setNeedFaceAuth(documentVo.getNeedFaceAuth());
                orderTradeProofVO.setPaymentCode(documentVo.getPaymentCode());
                orderTradeProofVO.setPaymentName(documentVo.getPaymentName());
                orderTradeProofVO.setEleSignInfo(documentVo.getEleSignInfo());

                BeanUtils.copyProperties(documentVo, orderTradeProofVO);
                orderTradeProofVOS.add(orderTradeProofVO);

                // 保存资料信息
                if (StringUtils.isNotBlank(queryDTO.getOrderSn())) {
                    OrderTradeProofPO orderTradeProofPO = new OrderTradeProofPO();
                    BeanUtils.copyProperties(orderTradeProofVO, orderTradeProofPO);
                    if (!CollectionUtils.isEmpty(queryDTO.getOrderProductIdList())) {
                        orderTradeProofPO.setOrderProductId(orderProductPO.getOrderProductId());
                    }
                    orderTradeProofPO.setCreateBy(OrderConst.LOG_USER_NAME);
                    orderTradeProofPO.setUpdateBy(OrderConst.LOG_USER_NAME);
                    orderTradeProofPOS.add(orderTradeProofPO);
                }
            }
        }
        if (ProofSceneEnum.RECEIVE.getCode().equals(queryDTO.getSceneNo()) && eleReceive && !paperReceive){
            List<OrderTradeProofVO> temp = orderTradeProofMapper.queryOrderProofList(orderSn, null, ProofSceneEnum.RECEIVE.getCode(), null, null);
            boolean containsPaper = temp.stream().anyMatch(vo -> vo.getMaterialNo().equals(orderMaterialConfig.getReceiveMaterialNo()));
            if (!containsPaper && checkProofLoanPay(orderPO)){
                // 命中电签，但是没有纸签，增加纸签
                OrderTradeProofPO paperProofPO = new OrderTradeProofPO();
                paperProofPO.setOrderSn(orderSn);
                paperProofPO.setMaterialNo(orderMaterialConfig.getReceiveMaterialNo());
                paperProofPO.setMaterialName("收货确认书");
                paperProofPO.setMaterialType("2");
                paperProofPO.setMaterialRemark("请打印收货函模版后拍照上传");
                paperProofPO.setExample(orderMaterialConfig.getPaperExample());
                paperProofPO.setRequisite(0);
                paperProofPO.setUniqueCheck(0);
                paperProofPO.setSameLinkPicRepeatCheck(0);
                paperProofPO.setDifLinkConsistentCheck(0);
                paperProofPO.setHisPicRepeatCheck(0);
                paperProofPO.setSceneNo(ProofSceneEnum.RECEIVE.getCode());
                paperProofPO.setSceneName(ProofSceneEnum.RECEIVE.getDesc());
                paperProofPO.setIsUpload(false);
                paperProofPO.setCreateBy("auto");
                paperProofPO.setUpdateBy("auto");
                paperProofPO.setEleSignInfo("");
                paperProofPO.setNeedFaceAuth(0);
                orderTradeProofPOS.add(paperProofPO);
            }
        }

//        if (eleReceive && paperReceive) {
//            log.error("交易真实性资料存在异常，同一订单出现既有电子签又有纸质签，需要调整规则引擎！,orderSn:{}", queryDTO.getOrderSn());
//        }
        // 优先视为电子签
        if (eleReceive) {
            LambdaUpdateWrapper<OrderExtendPO> extendUpdate = Wrappers.lambdaUpdate(OrderExtendPO.class);
            extendUpdate.eq(OrderExtendPO::getOrderSn, orderSn);
            extendUpdate.set(OrderExtendPO::getReceiveConfirmDocType, ReceiveConfirmDocType.ELE_SIGN.getCode());
            orderExtendService.update(extendUpdate);
        } else if (paperReceive) {
            LambdaUpdateWrapper<OrderExtendPO> extendUpdate = Wrappers.lambdaUpdate(OrderExtendPO.class);
            extendUpdate.eq(OrderExtendPO::getOrderSn, orderSn);
            // 比电子签增加一个条件，必须原来为未指定
            extendUpdate.eq(OrderExtendPO::getReceiveConfirmDocType, ReceiveConfirmDocType.NONE.getCode());
            extendUpdate.set(OrderExtendPO::getReceiveConfirmDocType, ReceiveConfirmDocType.ELE_SIGN.getCode());
            orderExtendService.update(extendUpdate);
        }

        if (queryDTO.getSceneNo().equals(ProofSceneEnum.DELIVERY.getCode())) {
            queryDTO.setSceneNo(ProofSceneEnum.RECEIVE.getCode());
            // 发货时生成签收的资料
            matchSceneMaterials(queryDTO);
        }
        // 保存资料信息
        if (!CollectionUtils.isEmpty(orderTradeProofPOS)) {
            this.saveBatch(orderTradeProofPOS);
            // 订单提交时，返回的vo里面会没有id，导致保存失败
            orderTradeProofVOS = orderTradeProofPOS.stream().map(po -> {
                OrderTradeProofVO proofVO = new OrderTradeProofVO();
                BeanUtils.copyProperties(po, proofVO);
                return proofVO;
            }).collect(Collectors.toList());
        }
        // 资料去重返回
        if (queryDTO.getDuplicateRemove()) {
            Set<OrderTradeProofVO> proofVOSetSet = new TreeSet<>(Comparator.comparing(OrderTradeProofVO::getMaterialNo));
            proofVOSetSet.addAll(orderTradeProofVOS);
            return new ArrayList<>(proofVOSetSet);
        }
        return orderTradeProofVOS;
    }

    @Override
    public Boolean matchBappPrivilege(String orderSn, ProofSceneEnum sceneEnum) {
        // 根据订单号查询订单信息
        LambdaQueryWrapper<OrderPO> queryOrder = Wrappers.lambdaQuery();
        queryOrder.eq(OrderPO::getOrderSn, orderSn)
                .select(OrderPO::getOrderSn, OrderPO::getStoreId, OrderPO::getPaymentCode,
                        OrderPO::getOrderAmount, OrderPO::getOrderPattern, OrderPO::getPerformanceModes);
        OrderPO orderPO = orderService.getOne(queryOrder);
        OrderExtendPO extendByOrderSn = orderExtendService.getOrderExtendByOrderSn(orderPO.getOrderSn());
        BizAssertUtil.notNull(orderPO, "订单信息为空");
        OrderTradeProofQueryDTO queryDTO = new OrderTradeProofQueryDTO();
        queryDTO.setSceneNo(sceneEnum.getCode());
        queryDTO.setOrderAmount(orderPO.getOrderAmount());
        queryDTO.setOrderPattern(orderPO.getOrderPattern());
        queryDTO.setPaymentCode(orderPO.getPaymentCode());
        queryDTO.setStoreId(orderPO.getStoreId());
        queryDTO.setPerformanceModes(orderPO.getPerformanceModes());
        queryDTO.setZone(extendByOrderSn.getZoneCode());
        queryDTO.setBranch(extendByOrderSn.getBranch());

        // 查询订单商品信息
        LambdaQueryWrapper<OrderProductPO> orderProductQuery = Wrappers.lambdaQuery();
        orderProductQuery.eq(OrderProductPO::getOrderSn, orderSn)
                .select(OrderProductPO::getOrderProductId, OrderProductPO::getProductId);
        List<OrderProductPO> orderProductPOS = orderProductService.list(orderProductQuery);
        queryDTO.setProductIdList(orderProductPOS.stream().map(OrderProductPO::getProductId).collect(Collectors.toList()));

        // 查询商品分类
        List<Product> productList = productFeignClient.getProductListByProductIds(queryDTO.getProductIdList());
        BizAssertUtil.notEmpty(productList, "商品为空，请检查");

        // 以商品行调用，取返回资料的并集
        for (Product product : productList) {
            queryDTO.setGoodsCategoryId1(product.getCategoryId1().toString());
            queryDTO.setGoodsCategoryId2(product.getCategoryId2().toString());
            queryDTO.setGoodsCategoryId3(product.getCategoryId3().toString());

            // 调用system匹配规则
            TradeDocumentVo tradeDocumentVo = tradeDocumentIntegration.matchOrderSceneMaterials(queryDTO);
            log.info("storeDeliveryPrivilege with query:{},result:{}",
                    JSONObject.toJSONString(queryDTO), JSONObject.toJSONString(tradeDocumentVo));
            if (Objects.isNull(tradeDocumentVo)) {
                continue;
            }
            if (ProofSceneEnum.DELIVERY == sceneEnum && OrderConst.API_PAY_STATE_1.equals(tradeDocumentVo.getBappDelivery())) {
                return true;
            }
            if (ProofSceneEnum.RECEIVE == sceneEnum && OrderConst.API_PAY_STATE_1.equals(tradeDocumentVo.getBappReceive())) {
                return true;
            }
        }
        return false;
    }

    @Override
    public Boolean checkOrderProofUpload(String orderSn, List<Long> orderProductIdList, ProofSceneEnum sceneEnum) {
        // 查询订单需上传的资料列表
        List<OrderTradeProofVO> orderTradeProofVOS =
                orderTradeProofMapper.queryOrderProofList(orderSn, orderProductIdList, sceneEnum.getCode(), null, null);
        if (CollectionUtils.isEmpty(orderTradeProofVOS)) {
            return true;
        }

        // 筛选已上传的资料数量
        long unUploadCount = orderTradeProofVOS.stream()
                .filter(vo -> NumberUtils.INTEGER_ONE.equals(vo.getRequisite()) && !vo.getIsUpload()).count();
        return unUploadCount == 0;
    }

    /**
     * 根据订单编号查询需要上传的交易凭证信息
     *
     * @param orderSn 订单编号
     * @return 需要上传的交易凭证信息
     */
    @Override
    public List<OrderTradeProofPO> getListByOrderSn(String orderSn) {
        // 匹配签收的资料
        OrderTradeProofQueryDTO proofQueryDTO = new OrderTradeProofQueryDTO();
        proofQueryDTO.setOrderSn(orderSn);
        proofQueryDTO.setSceneNo(ProofSceneEnum.RECEIVE.getCode());
        matchSceneMaterials(proofQueryDTO);
        AssertUtil.isTrue(StringUtils.isBlank(orderSn), "订单编号不能为空");
        LambdaQueryWrapper<OrderTradeProofPO> proofQuery = Wrappers.lambdaQuery(OrderTradeProofPO.class);
        proofQuery.eq(OrderTradeProofPO::getOrderSn, orderSn);
        return list(proofQuery);
    }

    /**
     * 直接上传交易凭证
     *
     * @param proofUploadDTO 参数dto
     * @return 上传结果
     */
    @Override
    public Boolean uploadProof(Integer operateRole, String operateId, String operateName, String source, TradeProofUploadDTO proofUploadDTO) {
        if (StringUtils.isBlank(proofUploadDTO.getOrderSn())) {
            throw new BusinessException("订单编号不能为空");
        }
        if (StringUtils.isBlank(proofUploadDTO.getMaterialNo())) {
            throw new BusinessException("资料编码不能为空");
        }
        if (StringUtils.isBlank(proofUploadDTO.getSceneNo())) {
            throw new BusinessException("场景编码不能为空");
        }
        // 调用文件中心上传资料
        FileScenesMaterialProofDTO paramDTO = new FileScenesMaterialProofDTO();
        paramDTO.setProofNo(proofUploadDTO.getOrderSn());
        paramDTO.setProofType(IScenesMaterialProofConstant.ProofTypeEnum.MALL_ORDER_ORDER_SN);
        paramDTO.setSceneNo(proofUploadDTO.getSceneNo());
        List<FileMaterialDTO> fileList = proofUploadDTO.getFileUrlList();
        OrderPO orderPo = orderModel.getOrderByOrderSn(paramDTO.getProofNo());
        boolean isReceiveMaterial = orderMaterialConfig.getReceiveMaterialNo().equals(proofUploadDTO.getMaterialNo());
        boolean isEleReceiveMaterial = orderMaterialConfig.getEleReceiveMaterialNo().equals(proofUploadDTO.getMaterialNo());
        if (isEleReceiveMaterial) {
            // 电签资料不允许上传
            if (PayMethodEnum.isLoanPay(orderPo.getPaymentCode())) {
                // 支付方式为贷款类
                throw new BusinessException("贷款类支付暂不支持，请提醒客户完成收货电签");
            } else if (PayMethodEnum.isCombinationPay(orderPo.getPaymentCode())) {
                // 组合支付，判断尾款是否为贷款类支付
                String paySn = orderPo.getPaySn();
                if (StringUtils.isNotBlank(paySn)) {
                    List<OrderPayRecordPO> orderPayRecordPOS = orderPayRecordService.queryOrderPayByPaySn(paySn);
                    if (!CollectionUtils.isEmpty(orderPayRecordPOS)) {
                        OrderPayRecordPO balanceRecord = orderPayRecordPOS.stream().filter(p -> PresellCapitalTypeEnum.BALANCE.getValue().equals(p.getPayOrder()) && PayMethodEnum.isLoanPay(p.getPaymentCode())).findFirst().orElse(null);
                        if (Objects.nonNull(balanceRecord)) {
                            throw new BusinessException("贷款类支付暂不支持，请提醒客户完成收货电签");
                        }
                    }
                }
            }
        }
        fileList.forEach(file -> {
            file.setProviderId(operateId);
            file.setProviderName(operateName);
            file.setProviderType(IScenesMaterialProofConstant.ProviderTypeEnum.parseEnum(operateRole));
            List<String> materialContentList = file.getMaterialContentList();
            List<String> md5List = Lists.newArrayList();
            Map<String, Integer> riskLevel = Maps.newHashMap();
            Map<String, String> riskReason = Maps.newHashMap();
            materialContentList.removeIf(Objects::isNull);
            if (!CollectionUtils.isEmpty(materialContentList)) {
                for (String content : materialContentList) {
                    if (isReceiveMaterial) {
                        Integer risk = NumberUtils.INTEGER_ZERO;
                        String reason = "";
                        try {
                            ReceiveMaterialResultDTO result = checkReceiveMaterial(proofUploadDTO.getOrderSn(), content);
                            risk = result.getLevel();
                            reason = StringUtils.join(result.getRiskReason(), ",");
                        } catch (Exception exp) {
                            log.warn("判定资料风险等级异常,exp:{}", exp.getMessage());
                        }
                        riskLevel.put(content, risk);
                        riskReason.put(content, reason);
                    } else {
                        riskLevel.put(content, NumberUtils.INTEGER_ZERO);
                        riskReason.put(content, "");
                    }
                    String md5 = null;
                    try {
                        md5 = ExpressDeliveryUtil.calculateMD5(content);
                    } catch (NoSuchAlgorithmException e) {
                        log.error("转换MD5 NoSuchAlgorithmException", e);
                    } catch (IOException e) {
                        log.error("转换MD5 IOException", e);
                    } catch (Exception e) {
                        log.error("转换MD5 Exception", e);
                    } finally {
                        md5List.add(md5);
                    }
                }
            }
            file.setRiskLevelMap(riskLevel);
            file.setRiskReason(riskReason);
            String md5ListStr = String.join(",", md5List);
            file.setPicEncrypt(md5ListStr);
        });
        paramDTO.setMaterialDTOList(fileList);
        Boolean result = fileCenterIntegration.saveScenesMaterialProofV2(paramDTO);
        List<String> fileUrl = fileList.stream().filter(file -> !CollectionUtils.isEmpty(file.getMaterialContentList())).flatMap(file -> file.getMaterialContentList().stream()).collect(Collectors.toList());
        // 更新上传结果
        LambdaUpdateWrapper<OrderTradeProofPO> proofUpdate = Wrappers.lambdaUpdate(OrderTradeProofPO.class);
        proofUpdate.eq(OrderTradeProofPO::getOrderSn, proofUploadDTO.getOrderSn());
        proofUpdate.eq(OrderTradeProofPO::getSceneNo, proofUploadDTO.getSceneNo());
        proofUpdate.eq(OrderTradeProofPO::getMaterialNo, proofUploadDTO.getMaterialNo());
        proofUpdate.set(OrderTradeProofPO::getIsUpload, !CollectionUtils.isEmpty(fileUrl) ? NumberUtils.INTEGER_ONE : NumberUtils.INTEGER_ZERO);
        // 此接口上传的均视为其他
        proofUpdate.set(OrderTradeProofPO::getUserType, CommonConst.PROOF_USER_TYPE_OTHER);
        update(proofUpdate);
        if (isReceiveMaterial) {
            // 收货确认书
            Integer status = NumberUtils.INTEGER_ONE;
            if (CollectionUtils.isEmpty(fileList)) {
                status = NumberUtils.INTEGER_ZERO;
            } else {
                if (CollectionUtils.isEmpty(fileUrl)) {
                    status = NumberUtils.INTEGER_ZERO;
                }
            }
            updateOrderReceiveMaterialStatus(paramDTO.getProofNo(), status, ReceiveConfirmDocType.PAPER_SIGN.getCode(), fileUrl.toString());
            orderLogModel.insertOrderLog(operateRole, Long.valueOf(operateId),
                    operateName, paramDTO.getProofNo(), orderPo.getOrderState(), orderPo.getOrderState(),
                    orderPo.getLoanPayState(), "收货凭证上传", OrderCreateChannel.valueOf(orderPo.getChannel()), "来源：" + source);
        }
        if (isEleReceiveMaterial) {
            // 电子收货确认书
            Integer status = NumberUtils.INTEGER_ONE;
            if (CollectionUtils.isEmpty(fileList)) {
                status = NumberUtils.INTEGER_ZERO;
            } else {
                if (CollectionUtils.isEmpty(fileUrl)) {
                    status = NumberUtils.INTEGER_ZERO;
                }
            }
            updateOrderReceiveMaterialStatus(paramDTO.getProofNo(), status, ReceiveConfirmDocType.ELE_SIGN.getCode(), fileUrl.toString());
            orderLogModel.insertOrderLog(operateRole, Long.valueOf(operateId),
                    operateName, paramDTO.getProofNo(), orderPo.getOrderState(), orderPo.getOrderState(),
                    orderPo.getLoanPayState(), "收货凭证上传", OrderCreateChannel.valueOf(orderPo.getChannel()), "来源：" + source);
        }
        return result;
    }

    /**
     * 农服收货确认函资料查询
     *
     * @param orderSn 订单编号
     * @return 收货确认函资料
     */
    @Override
    public List<ReceiveCheckResultVO> listSceneMaterialForAgric(String orderSn) {
        // 查询收货确认函资料
        List<OrderTradeProofVO> orderTradeProofVOS = listSceneMaterial(orderSn, null, orderMaterialConfig.getReceiveMaterialNo(), ProofSceneEnum.RECEIVE);
        if (CollectionUtils.isEmpty(orderTradeProofVOS)) {
            return Lists.newArrayList();
        }
        // 取第一个,理论上也只会有一个
        OrderTradeProofVO proofVO = orderTradeProofVOS.get(0);
        List<MaterialContentDTO> materialContentDTOList = proofVO.getMaterialContentDTOList();
        if (CollectionUtils.isEmpty(materialContentDTOList)) {
            return Lists.newArrayList();
        }
        // 类型转换
        return materialContentDTOList.stream().map(dto -> {
            ReceiveCheckResultVO resultVO = new ReceiveCheckResultVO();
            resultVO.setUploadImageUrl(dto.getUrl());
            resultVO.setLevel(dto.getRiskLevel());
            resultVO.setRiskReason(dto.getRiskReason());
            return resultVO;
        }).collect(Collectors.toList());
    }

    /**
     * 上传收货凭证
     *
     * @param logRoleAdmin 操作角色
     * @param operateId    操作人id
     * @param operator     操作人姓名
     * @param source       操作来源
     * @param uploadVO     上传信息
     * @return 上传结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean uploadProofAgric(int logRoleAdmin, String operateId, String operator, String source, AgricTradeProofUploadVO uploadVO, Boolean isEle) {
        log.info("uploadProofAgric param,uploadVO:{}", uploadVO);
        if (Objects.isNull(uploadVO)) {
            log.info("uploadProofAgric 参数为空");
            return Boolean.FALSE;
        }
        String orderSn = uploadVO.getOrderSn();
        if (StringUtils.isBlank(orderSn)) {
            log.info("uploadProofAgric 订单号为空");
            return Boolean.FALSE;
        }
        String wmsOperator = uploadVO.getWmsOperator();
        if (StringUtils.isBlank(wmsOperator)) {
            log.info("uploadProofAgric 操作人姓名为空");
            return Boolean.FALSE;
        }
        String materialNo = orderMaterialConfig.getReceiveMaterialNo();
        if (isEle) {
            materialNo = orderMaterialConfig.getEleReceiveMaterialNo();
        }
        // 先查询是否有签收确认函类型的上传凭证，如果没有则需要插入一条
        LambdaQueryWrapper<OrderTradeProofPO> proofQuery = Wrappers.lambdaQuery(OrderTradeProofPO.class);
        proofQuery.eq(OrderTradeProofPO::getOrderSn, uploadVO.getOrderSn());
        proofQuery.eq(OrderTradeProofPO::getSceneNo, ProofSceneEnum.RECEIVE.getCode());
        proofQuery.eq(OrderTradeProofPO::getMaterialNo, materialNo);
        List<OrderTradeProofPO> list = list(proofQuery);
        if (CollectionUtils.isEmpty(list)) {
            OrderTradeProofPO proofPO = new OrderTradeProofPO();
            proofPO.setOrderSn(uploadVO.getOrderSn());
            proofPO.setIsUpload(Boolean.FALSE);
            proofPO.setMaterialNo(materialNo);
            proofPO.setMaterialName("收货确认书");
            proofPO.setMaterialType("2");
            if (isEle) {
                proofPO.setMaterialType("6");
            }
            proofPO.setMaterialRemark("请上传收货确认书照片");
            proofPO.setExample(orderMaterialConfig.getExample());
            proofPO.setRequisite(NumberUtils.INTEGER_ZERO);
            proofPO.setUniqueCheck(NumberUtils.INTEGER_ZERO);
            proofPO.setSameLinkConsistentCheck(NumberUtils.INTEGER_ZERO);
            proofPO.setDifLinkConsistentCheck(NumberUtils.INTEGER_ZERO);
            proofPO.setSameLinkPicRepeatCheck(NumberUtils.INTEGER_ZERO);
            proofPO.setHisConsistentCheck(NumberUtils.INTEGER_ZERO);
            proofPO.setMaxUploads(9);
            proofPO.setSceneNo(ProofSceneEnum.RECEIVE.getCode());
            proofPO.setSceneName(ProofSceneEnum.RECEIVE.getDesc());
            proofPO.setCreateBy(operator);
            proofPO.setUpdateBy(operator);
            save(proofPO);
        }
        TradeProofUploadDTO proofUploadDTO = new TradeProofUploadDTO();
        proofUploadDTO.setMaterialNo(materialNo);
        proofUploadDTO.setOrderSn(orderSn);
        proofUploadDTO.setSceneNo(ProofSceneEnum.RECEIVE.getCode());
        FileMaterialDTO dto = new FileMaterialDTO();
        dto.setMaterialNo(materialNo);
        dto.setMaterialName("收货确认书");
        dto.setMaterialType("2");
        List<String> urlList = Lists.newArrayList();
        List<ReceiveCheckResultVO> receiveImageUploadList = uploadVO.getReceiveImageUploadList();
        if (!CollectionUtils.isEmpty(receiveImageUploadList)) {
            receiveImageUploadList.removeIf(Objects::isNull);
            if (!CollectionUtils.isEmpty(receiveImageUploadList)) {
                urlList = receiveImageUploadList.stream().map(ReceiveCheckResultVO::getUploadImageUrl).collect(Collectors.toList());
            }
        }
        dto.setMaterialContentList(urlList);
        dto.setProofRemark("请上传收货确认书照片");
        dto.setProviderId(operateId);
        dto.setProviderName(operator);
        dto.setProviderType(IScenesMaterialProofConstant.ProviderTypeEnum.PLAT_ADMIN);
        proofUploadDTO.setFileUrlList(Collections.singletonList(dto));
        try {
            uploadProof(logRoleAdmin, operateId, operator, source, proofUploadDTO);
        } catch (Exception e) {
            log.warn("uploadProofAgric 失败，异常：{}", e.getMessage());
            throw e;
        }
        log.info("uploadProofAgric 更新完成");
        return Boolean.TRUE;
    }

    /**
     * 农服资料校验
     *
     * @param orderSn 订单编号
     * @param fileUrl 文件地址
     * @return 校验结果
     */
    @Override
    public ReceiveCheckResultVO checkReceiveMaterialAgric(String orderSn, String fileUrl) {
        ReceiveMaterialResultDTO resultDTO = checkReceiveMaterial(orderSn, fileUrl);
        ReceiveCheckResultVO resultVO = new ReceiveCheckResultVO();
        resultVO.setUploadImageUrl(fileUrl);
        resultVO.setLevel(resultDTO.getLevel());
        resultVO.setRiskReason(resultDTO.getRiskReason());
        return resultVO;
    }

    /**
     * 电商上传凭证
     *
     * @param logRoleType    操作人类型
     * @param operateId      操作人id
     * @param operator       操作人名字
     * @param source         来源
     * @param proofUploadDTO 参数dto
     * @return 上传结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean uploadProofMall(int logRoleType, String operateId, String operator, String source, TradeProofUploadDTO proofUploadDTO) {
        Boolean res = uploadProof(logRoleType, operateId, operator, source, proofUploadDTO);
        String materialNo = proofUploadDTO.getMaterialNo();
        if (orderMaterialConfig.getReceiveMaterialNo().equals(materialNo)) {
            // 收货确认函,发送mq消息给农服，进行农服订单签收函上传状态的更新
            ModifyReceiveImageFlagDTO flagDTO = new ModifyReceiveImageFlagDTO();
            flagDTO.setOrderSn(proofUploadDTO.getOrderSn());
            flagDTO.setOperator(operator);
            flagDTO.setChannel(CommonConst.TRADE_PROOF_CHANNEL);
            flagDTO.setReceiveConfirmDocType(ReceiveConfirmDocType.PAPER_SIGN.getCode());
            List<FileMaterialDTO> fileUrlList = proofUploadDTO.getFileUrlList();
            if (CollectionUtils.isEmpty(fileUrlList) || CollectionUtils.isEmpty(fileUrlList.get(0).getMaterialContentList())) {
                // 没有上传文件，状态设置为未上传
                flagDTO.setReceiveImageFlag(NumberUtils.INTEGER_ZERO);
            } else {
                // 上传了文件，状态设置为已上传
                flagDTO.setReceiveImageFlag(NumberUtils.INTEGER_ONE);
            }
            // 保存日志并发送
            Long eventId = commonMqEventService.saveEvent(flagDTO, StringUtils.EMPTY, CommonConst.TRADE_PROOF_QUQUE);
            rabbitMQUtils.sendByEventId(eventId);
        }
        return res;
    }

    /**
     * 法大大用户实名+授权检查
     *
     * @param member        用户信息
     * @param url           回调url
     * @param genUrl        是否需要生成认证url,0-不需要，1-需要
     * @param isMiniProgram
     * @return 校验结果
     */
    @Override
    public MallCustomerCheckResultVO customerCheck(Member member, UserBaseInfo userBaseInfo, String url, String genUrl, String isMiniProgram) {
        String userNo = member.getUserNo();
        if (StringUtils.isBlank(userNo)) {
            throw new BusinessException("请先登录");
        }
        if (Objects.isNull(userBaseInfo)) {
            userBaseInfo = userInfoComponent.userBaseInfoByUserNo(userNo);
        }
        if (Objects.isNull(userBaseInfo)) {
            throw new BusinessException("获取用户信息异常，请稍后重试");
        }
        if (StringUtils.isBlank(userBaseInfo.getCustPhone())) {
            throw new BusinessException("获取用户手机号异常，请联系管理员");
        }
        if (StringUtils.isBlank(userBaseInfo.getIdNo())) {
            throw new BusinessException("未获取到身份证信息，请先在CAPP进行实名");
        }
        if (StringUtils.isBlank(userBaseInfo.getCustomerName())) {
            throw new BusinessException("未获取到姓名信息，请先在CAPP进行实名");
        }
        ContractCustomerCheckParamVO checkParamVO = new ContractCustomerCheckParamVO();
        String checkPhone = userBaseInfo.getContactPhone();
        if (StringUtils.isBlank(checkPhone)) {
            log.info("customerCheck contactPhone为空，使用custPhone");
            checkPhone = userBaseInfo.getCustPhone();
        }
        // 需要返回认证url
        checkParamVO.setAsycGenUrl("1");
        checkParamVO.setCustName(userBaseInfo.getCustomerName());
        checkParamVO.setIdNo(userBaseInfo.getIdNo());
        // 用户编号，为了避免因为没有实名，没有custId，报错，这里传userNo
        checkParamVO.setLoanCustId(userNo);
        checkParamVO.setMobile(checkPhone);
        checkParamVO.setReturnUrl(url);
        // 是否需要手签
        checkParamVO.setIsOpenHandSign(NumberUtils.INTEGER_ONE.toString());
        checkParamVO.setIsMiniProgram(isMiniProgram);
        Result<ContractCustomerCheckResultVO> contractCustomerCheckResultVOResult = contractFacade.customerCheck(checkParamVO);
        if (!contractCustomerCheckResultVOResult.isSuccess()) {
            throw new BusinessException("用户信息校验失败," + contractCustomerCheckResultVOResult.getMessage());
        }
        ContractCustomerCheckResultVO resultVO = contractCustomerCheckResultVOResult.getData();
        Result<ContractCustomerCheckResultVO> checkResultVOResult = contractFacade.checkStatus(checkParamVO);
        if (!checkResultVOResult.isSuccess()) {
            throw new BusinessException("用户信息校验失败," + checkResultVOResult.getMessage());
        }
        ContractCustomerCheckResultVO signStateVO = checkResultVOResult.getData();
        String status = resultVO.getStatus();
        String signState = signStateVO.getStatus();
        ContractCustomerStatus contractCustomerStatus = ContractCustomerStatus.getByCode(status);
        if (Objects.isNull(contractCustomerStatus)) {
            log.error("调用信贷法大大实名+授权检查，出现了枚举之外的结果,resultVo:{}", resultVO);
            // 兜底返回未认证
            contractCustomerStatus = ContractCustomerStatus.UNVERIFY;
        }
        MallCustomerCheckResultVO result = new MallCustomerCheckResultVO();
        result.setStatusCode(contractCustomerStatus.getStatus());
        result.setStatusName(contractCustomerStatus.getDesc());
        result.setCertificated(ContractCustomerStatus.isCertificated(contractCustomerStatus));
        result.setAuthed(ContractCustomerStatus.isAuthed(contractCustomerStatus));
        result.setUrl(resultVO.getUrl());
        result.setSignState(signState);
        return result;
    }

    /**
     * 合同默签
     *
     * @param member  用户信息
     * @param paramVO 请求参数
     * @return 默签结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ContractSignResultVO contractSign(Member member, ContractSignParamVO paramVO) {
        UserBaseInfo userBaseInfo = userInfoComponent.userBaseInfoByUserNo(member.getUserNo());
        MallCustomerCheckResultVO customerCheckResultVO = this.customerCheck(member, userBaseInfo, "chongho.net", CommonConst.CONTRACT_CHECK_GEN_URL_NO, null);
        if (Objects.isNull(customerCheckResultVO)) {
            throw new BusinessException("认证失败，请稍后重试");
        }
        if (!customerCheckResultVO.getAuthed()) {
            throw new BusinessException("您尚未授权自动签署，请登录乡助APP进行签署授权");
        }
        Long proofId = paramVO.getProofId();
        OrderTradeProofPO proof = getById(proofId);
        if (Objects.isNull(proof)) {
            throw new BusinessException("未找到对应交易凭证");
        }
        OrderPO orderPO = orderService.getByOrderSn(proof.getOrderSn());
        if (Objects.isNull(orderPO)) {
            throw new BusinessException("未找到对应订单");
        }
        if (!orderPO.getMemberId().equals(member.getMemberId())) {
            throw new BusinessException("您无权操作此订单");
        }
        String fileOriginUrl = proof.getFileOriginUrl();
        if (StringUtils.isNotBlank(fileOriginUrl) && fileOriginUrl.contains("fadada.com")) {
            // 线上很奇怪的点是法大大可能没有返回合同编号，因此只能通过域名来判断是否签署成功,正常应该通过法大大合同编号判断
            throw new BusinessException("您已完成签署，请刷新后重试");
        }
        List<String> contractNoList = Lists.newArrayList();
        // 合同信息
        ContractESignAutoRequest eSignAutoRequest = new ContractESignAutoRequest();
        eSignAutoRequest.setResource(BusinessResourceEnum.LOAN.getCode());
        eSignAutoRequest.setSignType(SignMethodEnum.CUSTOMER.getCode());
        //合同编号,取资料中配置的合同编号
        eSignAutoRequest.setContractNo(proof.getContractNo());

        //设置公司签约信息
        ContractCompanySignVO companySignVO = new ContractCompanySignVO();
        // 签收确认函此处公司信息无意义，因为不需要公司章
        companySignVO.setCompanyCode("ZHNX");
        companySignVO.setSignKeyword("中和农信项目管理有限公司");
        eSignAutoRequest.setCompanySignList(Arrays.asList(companySignVO));

        //设置客户签约信息
        String userMobile = userBaseInfo.getContactPhone();
        if (StringUtils.isBlank(userMobile)) {
            log.info("contractSign contactPhone为空，使用custPhone");
            userMobile = userBaseInfo.getCustPhone();
        }
        ContractCustomerSignVO coborrowerSign = new ContractCustomerSignVO();
        coborrowerSign.setCustId(userBaseInfo.getCustomerId());
        coborrowerSign.setCustName(userBaseInfo.getCustomerName());
        coborrowerSign.setIdCardNo(userBaseInfo.getIdNo());
        coborrowerSign.setMobile(userMobile);
        coborrowerSign.setSignKeyword("signLocation");
        eSignAutoRequest.setCustomerSignList(Arrays.asList(coborrowerSign));
        eSignAutoRequest.setContractSignToken("mall-d8824b88-35d9-48e2-b298-295b2345e672");

        // 签署还款计划合同
        OrderExtendFinancePO financePO = extendFinanceService.getByOrderSn(orderPO.getOrderSn());
        if (financePO == null || InterestType.TRADE_SUCCESS.getValue().equals(financePO.getInterestWay())) {
            String orderBatchId = null;
            log.info("contractSign 需要签署还款计划书 orderSn:{}", orderPO.getOrderSn());
            if (PayMethodEnum.isLoanPay(orderPO.getPaymentCode())) {
                // 支付方式为贷款类，直接使用订单编号
                orderBatchId = orderPO.getOrderSn();
            } else if (PayMethodEnum.isCombinationPay(orderPO.getPaymentCode())) {
                // 组合支付，判断尾款是否为贷款类支付
                String paySn = orderPO.getPaySn();
                if (StringUtils.isNotBlank(paySn)) {
                    List<OrderPayRecordPO> orderPayRecordPOS = orderPayRecordService.queryOrderPayByPaySn(paySn);
                    if (!CollectionUtils.isEmpty(orderPayRecordPOS)) {
                        OrderPayRecordPO balanceRecord = orderPayRecordPOS.stream().filter(p -> PresellCapitalTypeEnum.BALANCE.getValue().equals(p.getPayOrder()) && PayMethodEnum.isLoanPay(p.getPaymentCode())).findFirst().orElse(null);
                        if (Objects.nonNull(balanceRecord)) {
                            // 组合支付使用支付编号
                            orderBatchId = balanceRecord.getPayNo();
                        }
                    }
                }
            }
            if (orderPO.getExchangeFlag() == ExchangeOrderConst.ORDER_EXCHANGE_FLAG_2) {
                log.info("换货订单获取还款计划使用的订单号,orderSn:{}", orderPO.getOrderSn());
                // 换货后的订单，进行还款计划获取时，必须使用换货前的订单号
                LambdaQueryWrapper<OrderExchangeDetailPO> exchangeQuery = Wrappers.lambdaQuery(OrderExchangeDetailPO.class);
                exchangeQuery.eq(OrderExchangeDetailPO::getExchangeOrderSn, orderPO.getOrderSn());
                exchangeQuery.orderByDesc(OrderExchangeDetailPO::getCreateTime);
                List<OrderExchangeDetailPO> list = orderExchangeDetailService.list(exchangeQuery);
                if (!CollectionUtils.isEmpty(list)) {
                    // 取第一个
                    OrderExchangeDetailPO detailPO = list.get(0);
                    OrderPO preOrder = orderService.getByOrderSn(detailPO.getOrderSn());
                    if (Objects.nonNull(preOrder)) {
                        if (PayMethodEnum.isLoanPay(preOrder.getPaymentCode())) {
                            // 支付方式为贷款类，直接使用订单编号
                            orderBatchId = preOrder.getOrderSn();
                        } else if (PayMethodEnum.isCombinationPay(preOrder.getPaymentCode())) {
                            // 组合支付，判断尾款是否为贷款类支付
                            String paySn = preOrder.getPaySn();
                            if (StringUtils.isNotBlank(paySn)) {
                                List<OrderPayRecordPO> orderPayRecordPOS = orderPayRecordService.queryOrderPayByPaySn(paySn);
                                if (!CollectionUtils.isEmpty(orderPayRecordPOS)) {
                                    OrderPayRecordPO balanceRecord = orderPayRecordPOS.stream().filter(p -> PresellCapitalTypeEnum.BALANCE.getValue().equals(p.getPayOrder()) && PayMethodEnum.isLoanPay(p.getPaymentCode())).findFirst().orElse(null);
                                    if (Objects.nonNull(balanceRecord)) {
                                        // 组合支付使用支付编号
                                        orderBatchId = balanceRecord.getPayNo();
                                    }
                                }
                            }
                        }
                    }
                } else {
                    log.warn("换货订单获取还款计划使用的订单号失败，未找到对应的换货详情");
                }
            }
            if (StringUtils.isNotBlank(orderBatchId)) {
                // 交易成功后起息，需要查询还款计划合同
                RepaymentScheduleRequest request = new RepaymentScheduleRequest();
                request.setOrderBatchId(orderBatchId);
                request.setOrderSource("CDMALL");
                request.setLoanCustId(userBaseInfo.getCustomerId());
                Result<Void> scheduleResult = cdMallOrderFacade.signContract(request);
                log.info("调用还款计划签署接口参数和结果:param:{},result:{}", request, scheduleResult);
                if (!scheduleResult.isSuccess()) {
                    log.error("还款计划签署失败,需要人工介入处理, orderSn:{},errorMsg:{}", orderPO.getOrderSn(), scheduleResult.getErrorMsg());
                    //                throw new BusinessException("签署失败，请稍后重试");
                }
            } else {
                log.info("contractSign 未命中贷款类支付 orderSn:{}", orderPO.getOrderSn());
            }
        }
        //调用签约方法
        Result<ContractFindResponse> contractESignAutoResponseResult = contractFacade.contractSign(eSignAutoRequest);
        log.info("asynAutoV2 ,param:{},result:{}", eSignAutoRequest, contractESignAutoResponseResult);
        if (!contractESignAutoResponseResult.isSuccess()) {
            throw new BusinessException("签署失败，失败原因：" + contractESignAutoResponseResult.getMessage());
        }
        ContractFindResponse response = contractESignAutoResponseResult.getData();
        String viewURL = response.getViewURL();
        proof.setFileOriginUrl(viewURL);
        contractNoList.add(viewURL);
//        proof.setFddContractNo(viewURL);
        // 修改为已上传
        proof.setIsUpload(Boolean.TRUE);
        // 上传者为用户
        proof.setUserType(CommonConst.PROOF_USER_TYPE_USER);
        // 更新签署状态
        updateOrderReceiveMaterialStatus(proof.getOrderSn(), NumberUtils.INTEGER_ONE, ReceiveConfirmDocType.ELE_SIGN.getCode(), proof.getFileOriginUrl());
        // 保存日志
        orderLogModel.insertOrderLog(OrderConst.LOG_ROLE_MEMBER, Long.valueOf(member.getMemberId()),
                userBaseInfo.getCustomerName(), proof.getOrderSn(), orderPO.getOrderState(), orderPO.getOrderState(),
                orderPO.getLoanPayState(), "收货凭证上传", OrderCreateChannel.valueOf(orderPO.getChannel()), "来源：电商系统。");
        // 发送mq消息，mall-biz更新签署状态
        OrderOperationEventEnum confirmReceive = OrderOperationEventEnum.CUSTOMER_CONFIRM_RECEIVE;
        OrderOperationEventNotifyDTO message =
                new OrderOperationEventNotifyDTO(confirmReceive.getCode(), confirmReceive.getDesc(), orderPO.getOrderSn());
        orderCreateHelper.sendMqEventMessageByTransactionCommit(message,
                RabbitMqEventEnum.EXCHANGE_ORDER_OPERATION.getExchang());
        // 更新交易真实性资料情况
        updateById(proof);
        ContractSignResultVO resultVO = new ContractSignResultVO();
        resultVO.setFddContractNoList(contractNoList);
        resultVO.setViewUrl(viewURL);
        resultVO.setDownloadURL(response.getDownloadURL());
        try {
            // 收货确认函,发送mq消息给农服，进行农服订单签收函上传状态的更新
            ModifyReceiveImageFlagDTO flagDTO = new ModifyReceiveImageFlagDTO();
            flagDTO.setOrderSn(orderPO.getOrderSn());
            flagDTO.setOperator(member.getMemberId().toString());
            flagDTO.setChannel(CommonConst.TRADE_PROOF_CHANNEL);
            flagDTO.setReceiveImageFlag(NumberUtils.INTEGER_ONE);
            flagDTO.setReceiveConfirmDocType(ReceiveConfirmDocType.ELE_SIGN.getCode());
            // 保存日志并发送
            Long eventId = commonMqEventService.saveEvent(flagDTO, StringUtils.EMPTY, CommonConst.TRADE_PROOF_QUQUE);
            rabbitMQUtils.sendByEventId(eventId);
        } catch (Exception e) {
            log.warn("contractSign 发送mq失败");
        }
        // 完成电签时，将纸质签设置为非必上传(同场景)
        LambdaUpdateWrapper<OrderTradeProofPO> proofUpdate = Wrappers.lambdaUpdate(OrderTradeProofPO.class);
        proofUpdate.eq(OrderTradeProofPO::getOrderSn, proof.getOrderSn());
        proofUpdate.eq(OrderTradeProofPO::getSceneNo, proof.getSceneNo());
        proofUpdate.eq(OrderTradeProofPO::getMaterialNo, orderMaterialConfig.getReceiveMaterialNo());
        proofUpdate.set(OrderTradeProofPO::getRequisite, NumberUtils.INTEGER_ZERO);
        update(proofUpdate);
        return resultVO;
    }

    /**
     * 同步生成合同
     *
     * @param orderSn    订单编号
     * @param materialNo 交易真实性资料编码
     */
    @Override
    @Transactional
    public List<String> generateContractSync(String orderSn, String materialNo) {
        return realGenerateContract(orderSn, materialNo, null);
    }

    /**
     * 异步生成合同
     *
     * @param orderSn    订单编号
     * @param materialNo 交易真实性资料编码
     */
    @Async
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generateContract(String orderSn, String materialNo) {
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                /*事务提交后*/
                @Override
                public void afterCommit() {
                    log.info("SPRING事务提交后================================");
                    realGenerateContract(orderSn, materialNo, null);
                }
            });
        } else {
            generateContractSync(orderSn, materialNo);
        }
    }


    /**
     * 生成电签合同
     *
     * @param materialNo 资料编码
     * @return 生成的合同编码, 后续进行电签时需要使用该编码进行签署
     */
    public List<String> realGenerateContract(String orderSn, String materialNo, Long proofId) {
        log.info("start generateEleDoc orderSn:{},materialNo:{}", orderSn, materialNo);
        LambdaQueryWrapper<OrderTradeProofPO> proofQuery = Wrappers.lambdaQuery(OrderTradeProofPO.class);
        proofQuery.eq(OrderTradeProofPO::getOrderSn, orderSn);
        proofQuery.eq(OrderTradeProofPO::getMaterialNo, materialNo);
        proofQuery.eq(Objects.nonNull(proofId), OrderTradeProofPO::getId, proofId);
        List<OrderTradeProofPO> proofPoList = list(proofQuery);
        if (CollectionUtils.isEmpty(proofPoList)) {
            log.warn("generateEleDoc 未找到交易真实性资料信息");
            return Lists.newArrayList();
        }
        OrderPO orderPO = orderService.getByOrderSn(orderSn);
        if (Objects.isNull(orderPO)) {
            log.warn("generateEleDoc 未找到订单信息");
            return Lists.newArrayList();
        }
        String userNo = orderPO.getUserNo();
        UserBaseInfo userBaseInfo = userInfoComponent.userBaseInfoByUserNo(userNo);
        if (Objects.isNull(userBaseInfo)) {
            log.warn("generateEleDoc 未找到用户信息");
            return Lists.newArrayList();
        }
        StoreContractReceiptInfoVO storeContract =
                storeFeignClient.getStoreContractReciptInfo(orderPO.getStoreId());
        JsonResult<String> contractSellerNameByStoreIdResult = storeFeignClient.getContractSellerNameByStoreId(orderPO.getStoreId());
        String contractSellerName = contractSellerNameByStoreIdResult.getData();
        if (StringUtils.isBlank(contractSellerName)) {
            throw new BusinessException("获取合同卖家名失败，请联系管理员");
        }
        if (Objects.isNull(storeContract)) {
            log.warn("generateEleDoc 未找到店铺合同信息");
        }
        OrderExtendPO orderExtendPO = orderExtendService.getOrderExtendByOrderSn(orderSn);
        if (Objects.isNull(orderExtendPO)) {
            log.warn("generateEleDoc 未找到订单拓展信息");
            return Lists.newArrayList();
        }
        List<OrderProductPO> orderProductPOS = orderProductService.listByOrderSn(orderSn);
        if (CollectionUtils.isEmpty(orderProductPOS)) {
            log.warn("generateEleDoc 未找到订单商品信息");
            return Lists.newArrayList();
        }
//        OrderPerformanceBelongsVO performanceBelongsVO = performanceBelongsService.getVoByOrderSn(orderSn);
//        if (Objects.isNull(performanceBelongsVO)) {
//            log.warn("generateEleDoc 未找到业绩归属信息");
//            return Lists.newArrayList();
//        }
        List<String> viewUrls = Lists.newArrayList();
        QueryUniqueCodeParam queryUniqueCodeParam = new QueryUniqueCodeParam();
        queryUniqueCodeParam.setApplyId(orderSn);
        queryUniqueCodeParam.setLoanCustId(userBaseInfo.getCustomerId());
        String uniqueCode = ExternalApiUtil.callResultApi(() -> loanFinanceFacade.queryUniqueCode(orderSn, userBaseInfo.getCustomerId()), queryUniqueCodeParam, "/query/queryMallPaySummary", "查询信贷唯一码");
        for (OrderTradeProofPO proofPO : proofPoList) {
            try {
                String viewUrl = createContract(orderPO, orderExtendPO, orderProductPOS, proofPO, contractSellerName, userBaseInfo, uniqueCode);
                // 调用文件中心上传资料
                FileScenesMaterialProofDTO paramDTO = new FileScenesMaterialProofDTO();
                paramDTO.setProofNo(proofPO.getOrderSn());
                paramDTO.setProofType(IScenesMaterialProofConstant.ProofTypeEnum.MALL_ORDER_ORDER_SN);
                paramDTO.setSceneNo(proofPO.getSceneNo());
                FileMaterialDTO materialDTO = new FileMaterialDTO();
                materialDTO.setMaterialNo(proofPO.getMaterialNo());
                materialDTO.setMaterialName(proofPO.getMaterialName());
                materialDTO.setMaterialType(proofPO.getMaterialType());
                materialDTO.setMaterialContentList(Collections.singletonList(viewUrl));
                materialDTO.setProofRemark(proofPO.getMaterialRemark());
                materialDTO.setRiskLevelMap(Maps.newHashMap());
                materialDTO.setRiskReason(Maps.newHashMap());
                String md5 = null;
                List<String> md5List = Lists.newArrayList();
                try {
                    md5 = ExpressDeliveryUtil.calculateMD5(viewUrl);
                } catch (NoSuchAlgorithmException e) {
                    log.error("转换MD5 NoSuchAlgorithmException", e);
                } catch (IOException e) {
                    log.error("转换MD5 IOException", e);
                } catch (Exception e) {
                    log.error("转换MD5 Exception", e);
                } finally {
                    md5List.add(md5);
                }
                materialDTO.setPicEncrypt(md5);
                materialDTO.setProviderId("0");
                materialDTO.setProviderName("system");
                materialDTO.setProviderType(IScenesMaterialProofConstant.ProviderTypeEnum.PLAT_OPERATOR);
                paramDTO.setMaterialDTOList(Collections.singletonList(materialDTO));
                fileCenterIntegration.saveScenesMaterialProofV2(paramDTO);
                viewUrls.add(viewUrl);
            } catch (Exception e) {
                log.warn("generateEleDoc 执行失败，proofPO:{}", proofPO);
            }
        }
        updateBatchById(proofPoList);
        orderExtendService.updateById(orderExtendPO);
        return viewUrls;
    }

    /**
     * 创建文件
     *
     * @param orderPO              订单信息
     * @param orderExtendPO        订单拓展信息
     * @param performanceBelongsVO 业绩归属
     * @param orderProductPOList   商品信息列表
     */
    private String createContract(OrderPO orderPO, OrderExtendPO orderExtendPO, List<OrderProductPO> orderProductPOList, OrderTradeProofPO proofPO, String sellerName, UserBaseInfo userBaseInfo, String uniqueCode) {
        List<OrderProductBuildParamDTO> productParamList = Lists.newArrayList();
        for (int i = 0; i < orderProductPOList.size(); i++) {
            OrderProductPO p = orderProductPOList.get(i);
            OrderProductBuildParamDTO paramDTO = new OrderProductBuildParamDTO();
            paramDTO.setGoodsName(p.getGoodsName());
            paramDTO.setSpec(p.getSpecValues());
            paramDTO.setNum(p.getProductNum().toString());
            paramDTO.setPrice(p.getProductShowPrice().toString());
            paramDTO.setGoodsAmount(p.getGoodsAmountTotal().toString());
            paramDTO.setIndex((i + 1) + "");
            productParamList.add(paramDTO);
        }
        //参数可以为任意对象,会自动转换为生成合同需要的格式
        LocalDate ld = LocalDate.now();
        ContractBuildParamDTO params = new ContractBuildParamDTO();
        params.setOrderSn(orderPO.getOrderSn());
        params.setProductInfoList(productParamList);
        params.setBusinessLicenseName(sellerName);
        // 没有卖方名称时，报错
        if (StringUtils.isBlank(params.getBusinessLicenseName())) {
            throw new BusinessException("生成合同失败，请联系管理员");
        }
        params.setOrderDate(DateUtil.format(orderPO.getCreateTime(), DatePattern.NORM_DATETIME_PATTERN));
        params.setReceiver(orderExtendPO.getReceiverName());
        params.setReceiveAddress(orderExtendPO.getReceiverAreaInfo() + orderExtendPO.getReceiverAddress());
        params.setContactMobile(orderExtendPO.getReceiverMobile());
//        params.setPerformBelong(performanceBelongsVO.getBelongerName());
        params.setCustName(userBaseInfo.getCustomerName());
        params.setCustIdNo(userBaseInfo.getIdNo());
        params.setTotalAmount(orderPO.getOrderAmount().toString());
        params.setUniqueCode(uniqueCode);
        params.setCustPhone(userBaseInfo.getCustPhone());
        params.setYear(ld.getYear() + "");
        params.setMonth(ld.getMonthValue() + "");
        params.setDay(ld.getDayOfMonth() + "");
        ContractGenerateRequest generateRequest = new ContractGenerateRequest();
        //设置业务流水号
        String flowNo = orderPO.getOrderSn() + "_" + proofPO.getId() + RandomUtil.randomNumbers(2);
        generateRequest.setBuzNo(flowNo);
        //设置业务合同编号,可于业务流水号一致
        generateRequest.setBuzContractNo(flowNo);
        //模板编号,即需要生成的合同模板
        generateRequest.setTemplateNo(proofPO.getEleSignInfo());
        //设置合同生成参数,直接通过构造函数创建ContractParamVo类就行
        generateRequest.setContractParamVo(new ContractParamVo(params));
        //设置contractType:01-进件, 02-授信, 03-放款， 04-外部合同， 05-其它
        generateRequest.setContractType("05");
        //设置签约类型:参考SignTypeEnum, paper-纸质合同,eSignH-客户手签,eSignA-客户默签
        generateRequest.setSignType(SignTypeEnum.E_SIGN_A.getCode());
        generateRequest.setRemark("乡助电商订单:" + orderPO.getOrderSn());
        //设置业务来源,参考BusinessResourceEnum枚举类,LOAN-信贷，FRONT-前端，HR-hr系统，OTHER-其它
        generateRequest.setResource(BusinessResourceEnum.OTHER.getCode());
        ContractFindResponse contractResponse = ExternalApiUtil.callResultApi(() -> contractFacade.generateContract(generateRequest), generateRequest, "/contract/before/generate", "合同生成");
        log.info("generateEleDoc contractResponse:{}", contractResponse);
        proofPO.setContractNo(contractResponse.getContractNo());
        proofPO.setFileOriginUrl(contractResponse.getViewURL());
        orderExtendPO.setReceiveConfirmDocUrl(contractResponse.getViewURL());
        return proofPO.getFileOriginUrl();
    }

    /**
     * 合同查询
     *
     * @param member  用户信息
     * @param proofId 资料id
     * @return 预览地址
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ContractSearchResultVO contractSearch(Long proofId, Integer needLoanInfo) {
        OrderTradeProofPO proofPO = getById(proofId);
        if (Objects.isNull(proofPO)) {
            throw new BusinessException("未找到对应的凭证，请稍后重试");
        }
        OrderPO orderPO = orderService.getByOrderSn(proofPO.getOrderSn());
        if (Objects.isNull(orderPO)) {
            throw new BusinessException("未找到对应订单");
        }
        ContractSearchResultVO resultVO = new ContractSearchResultVO();
        resultVO.setOrderSn(orderPO.getOrderSn());
        resultVO.setFinanceRuleName(orderPO.getRuleTag());
        resultVO.setPayMethodName(orderPO.getPaymentName());
        resultVO.setOrderAmount(orderPO.getOrderAmount());
        UserBaseInfo userBaseInfo = userInfoComponent.userBaseInfoByUserNo(orderPO.getUserNo());
        // 设置客户姓名
        resultVO.setUserName(userBaseInfo.getCustomerName());
        OrderExtendFinancePO financePO = extendFinanceService.getByOrderSn(orderPO.getOrderSn());
        if (financePO == null || InterestType.TRADE_SUCCESS.getValue().equals(financePO.getInterestWay())) {
            String orderBatchId = null;
            log.info("contractSearch 需要签署还款计划书 orderSn:{}", orderPO.getOrderSn());
            if (PayMethodEnum.isLoanPay(orderPO.getPaymentCode())) {
                // 支付方式为贷款类，直接使用订单编号
                orderBatchId = orderPO.getOrderSn();
            } else if (PayMethodEnum.isCombinationPay(orderPO.getPaymentCode())) {
                // 组合支付，判断尾款是否为贷款类支付
                List<OrderPayRecordPO> orderPayRecordPOS = orderPayRecordService.queryOrderPayByPaySn(orderPO.getPaySn());
                if (!CollectionUtils.isEmpty(orderPayRecordPOS)) {
                    OrderPayRecordPO balanceRecord = orderPayRecordPOS.stream().filter(p -> PresellCapitalTypeEnum.BALANCE.getValue().equals(p.getPayOrder()) && PayMethodEnum.isLoanPay(p.getPaymentCode())).findFirst().orElse(null);
                    if (Objects.nonNull(balanceRecord)) {
                        orderBatchId = balanceRecord.getPayNo();
                    }
                }
            }
            boolean exchangePlan = true;
            if (orderPO.getExchangeFlag() == ExchangeOrderConst.ORDER_EXCHANGE_FLAG_2) {
                log.info("换货订单获取还款计划使用的订单号,orderSn:{}", orderPO.getOrderSn());
                // 换货后的订单，进行还款计划获取时，必须使用换货前的订单号
                LambdaQueryWrapper<OrderExchangeDetailPO> exchangeQuery = Wrappers.lambdaQuery(OrderExchangeDetailPO.class);
                exchangeQuery.eq(OrderExchangeDetailPO::getExchangeOrderSn, orderPO.getOrderSn());
                exchangeQuery.orderByDesc(OrderExchangeDetailPO::getCreateTime);
                List<OrderExchangeDetailPO> list = orderExchangeDetailService.list(exchangeQuery);
                if (!CollectionUtils.isEmpty(list)) {
                    // 取第一个
                    OrderExchangeDetailPO detailPO = list.get(0);
                    OrderExtendFinancePO exchangeFinancePO = extendFinanceService.getByOrderSn(detailPO.getOrderSn());
                    // 获取老的金融规则,判断换货订单是否需要获取还款计划
                    exchangePlan = exchangeFinancePO == null || InterestType.TRADE_SUCCESS.getValue().equals(exchangeFinancePO.getInterestWay());
                    OrderPO preOrder = orderService.getByOrderSn(detailPO.getOrderSn());
                    if (Objects.nonNull(preOrder)) {
                        if (PayMethodEnum.isLoanPay(preOrder.getPaymentCode())) {
                            // 支付方式为贷款类，直接使用订单编号
                            orderBatchId = preOrder.getOrderSn();
                        } else if (PayMethodEnum.isCombinationPay(preOrder.getPaymentCode())) {
                            // 组合支付，判断尾款是否为贷款类支付
                            String paySn = preOrder.getPaySn();
                            if (StringUtils.isNotBlank(paySn)) {
                                List<OrderPayRecordPO> orderPayRecordPOS = orderPayRecordService.queryOrderPayByPaySn(paySn);
                                if (!CollectionUtils.isEmpty(orderPayRecordPOS)) {
                                    OrderPayRecordPO balanceRecord = orderPayRecordPOS.stream().filter(p -> PresellCapitalTypeEnum.BALANCE.getValue().equals(p.getPayOrder()) && PayMethodEnum.isLoanPay(p.getPaymentCode())).findFirst().orElse(null);
                                    if (Objects.nonNull(balanceRecord)) {
                                        // 组合支付使用支付编号
                                        orderBatchId = balanceRecord.getPayNo();
                                    }
                                }
                            }
                        }
                    }
                } else {
                    log.warn("换货订单获取还款计划使用的订单号失败，未找到对应的换货详情");
                }
            }
            if (!exchangePlan){
                log.info("换货订单不需要获取还款计划");
            }
            if (exchangePlan && StringUtils.isNotBlank(orderBatchId)) {
                // 交易成功后起息，需要查询还款计划合同
                log.info("contractSearch 需要获取还款计划");
                // 交易成功后起息，需要查询还款计划合同
                RepaymentScheduleRequest request = new RepaymentScheduleRequest();
                request.setOrderBatchId(orderBatchId);
                request.setOrderSource("CDMALL");
                request.setLoanCustId(userBaseInfo.getCustomerId());
                Result<List<ContractFindResponse>> scheduleResult = cdMallOrderFacade.repaymentSchedule(request);
                log.info("调用还款计划获取接口参数和结果:param:{},result:{}", request, scheduleResult);
                if (!scheduleResult.isSuccess()) {
                    log.info("获取还款计划调用失败");
                    throw new BusinessException("获取还款计划失败，请稍后重试");
                }
                List<ContractFindResponse> scheduleList = scheduleResult.getData();
                if (CollectionUtils.isEmpty(scheduleList)) {
                    log.info("获取还款计划结果为空");
                    throw new BusinessException("获取还款计划失败，请稍后重试");
                }
                ContractFindResponse shedule = scheduleList.get(0);
                String viewURL = shedule.getViewURL();
                resultVO.setRepaymentSchedule(viewURL);
            } else {
                log.info("contractSearch 未命中贷款类支付 orderSn:{}", orderPO.getOrderSn());
            }
        }
        // 非用户上传，直接查文件中心
        if (CommonConst.PROOF_USER_TYPE_OTHER.equals(proofPO.getUserType())) {
            List<FileScenesProofVO> fileScenesProofVOS = fileCenterIntegration.queryScenesMaterialProofV2(proofPO.getOrderSn(), null, proofPO.getSceneNo(), proofPO.getMaterialNo(), null, null);
            if (CollectionUtils.isEmpty(fileScenesProofVOS)) {
                return resultVO;
            }
            FileScenesProofVO fileScenesProofVO = fileScenesProofVOS.get(0);
            FileScenesProofMaterialVO materialVOList = fileScenesProofVO.getMaterialVOList().get(0);
            List<String> materialContentList = materialVOList.getMaterialContentList();
            if (!CollectionUtils.isEmpty(materialContentList)) {
                String url = materialContentList.get(0);
                resultVO.setViewUrl(url);
                resultVO.setDownUrl(url);
            }
            return resultVO;
        }
        String contractNo = proofPO.getContractNo();
        if (StringUtils.isBlank(contractNo)) {
            // 尚未生成合同，尝试生成合同
            List<String> urlList = generateContractSync(proofPO.getOrderSn(), proofPO.getMaterialNo());
            if (CollectionUtils.isEmpty(urlList)) {
                throw new BusinessException("生成合同失败，请稍后重试");
            }
            // 重新查一次
            proofPO = getById(proofPO.getId());
            contractNo = proofPO.getContractNo();
        }
        // 查询合同
        ContractFindRequest findRequest = new ContractFindRequest();
        findRequest.setContractNo(contractNo);
        findRequest.setContractExpireHour(1L);
        List<ContractFindResponse> contractResponse = ExternalApiUtil.callResultApi(() -> contractFacade.findAllContract(findRequest), findRequest, "/contract/common/findAllContract", "合同查询");
        if (CollectionUtils.isEmpty(contractResponse)) {
            throw new BusinessException("未找到对应的合同,请稍后重试");
        }
        ContractFindResponse contractFindResponse = contractResponse.get(0);
        proofPO.setFileOriginUrl(contractFindResponse.getViewURL());
        updateById(proofPO);
        resultVO.setViewUrl(contractFindResponse.getViewURL());
        resultVO.setDownUrl(contractResponse.get(0).getDownloadURL());
        PayMethodEnum methodEnum = PayMethodEnum.getValue(orderPO.getPaymentCode());
        // 调用信贷获取贷款详情
        if (NumberUtils.INTEGER_ONE.equals(needLoanInfo) && methodEnum != null && PayMethodEnum.isLoanPay(methodEnum)) {
            CdmallOrderVo loanInfoByOrderSn = loanPayIntegration.getLoanInfoByOrderSn(orderPO.getOrderSn());
            if (ObjectUtil.isNotNull(loanInfoByOrderSn)) {
                LoanInfoVo loanInfoVo = new LoanInfoVo();
                BeanUtils.copyProperties(loanInfoByOrderSn, loanInfoVo);
                loanInfoVo.setFinanceRuleCode(orderPO.getFinanceRuleCode());
                loanInfoVo.setRuleTag(orderPO.getRuleTag());
                resultVO.setLoanInfoVo(loanInfoVo);
            }
        }
        return resultVO;
    }

    /**
     * 根据订单查询所有交易真实性资料
     *
     * @param orderSn 订单编号
     */
    @Override
    public List<MallFileScenesProofVO> queryScenesMaterialV2(OrderPO orderPO) {
        if (Objects.isNull(orderPO)) {
            throw new BusinessException("订单信息为空");
        }
        String orderSn = orderPO.getOrderSn();
        BizAssertUtil.isTrue(StringUtils.isBlank(orderSn), "凭证编号不能为空");
        // 查询文件中心资料
        List<FileScenesProofVO> fileScenesProofVOS = fileCenterIntegration.queryScenesMaterialProofV2(orderSn, null, null, null, null, null);
        LambdaQueryWrapper<OrderTradeProofPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(OrderTradeProofPO::getOrderSn, orderSn);
        List<OrderTradeProofPO> resultPOList = list(queryWrapper);
        if (PayMethodEnum.isLoanPay(orderPO.getPaymentCode()) && Objects.equals(orderPO.getOrderState(), OrderStatusEnum.TRADE_SUCCESS.getValue())) {
            // 贷款类支付且交易完成，确保有电签资料
            boolean match = resultPOList.stream().anyMatch(po -> po.getMaterialNo().equals(orderMaterialConfig.getEleReceiveMaterialNo()));
            if (!match) {
                OrderTradeProofPO proofPO = new OrderTradeProofPO();
                proofPO.setOrderSn(orderPO.getOrderSn());
                proofPO.setMaterialNo(orderMaterialConfig.getEleReceiveMaterialNo());
                proofPO.setMaterialName("收货确认函电签");
                proofPO.setMaterialType("6");
                proofPO.setMaterialRemark("eleSign");
                proofPO.setExample("https://mall-sld-test.oss-cn-beijing.aliyuncs.com/local/order/img-e1.png");
                proofPO.setRequisite(0);
                proofPO.setUniqueCheck(0);
                proofPO.setSameLinkPicRepeatCheck(0);
                proofPO.setDifLinkConsistentCheck(0);
                proofPO.setHisPicRepeatCheck(0);
                proofPO.setSceneNo(ProofSceneEnum.RECEIVE.getCode());
                proofPO.setSceneName(ProofSceneEnum.RECEIVE.getDesc());
                proofPO.setIsUpload(false);
                proofPO.setCreateBy("auto2");
                proofPO.setUpdateBy("auto2");
                proofPO.setEleSignInfo("mall_ele_sign");
                proofPO.setNeedFaceAuth(1);
                save(proofPO);
                log.info("queryScenesMaterialV2 生成资料po,proofPo:{}", proofPO);
                resultPOList.add(proofPO);
            }
            // 判断是否有纸签，没有纸签，也需要赛一条
            boolean paperMatch = resultPOList.stream().anyMatch(po -> po.getMaterialNo().equals(orderMaterialConfig.getReceiveMaterialNo()));
            if (!paperMatch) {
                OrderTradeProofPO paperProofPO = new OrderTradeProofPO();
                paperProofPO.setOrderSn(orderPO.getOrderSn());
                paperProofPO.setMaterialNo(orderMaterialConfig.getReceiveMaterialNo());
                paperProofPO.setMaterialName("收货确认书");
                paperProofPO.setMaterialType("2");
                paperProofPO.setMaterialRemark("请打印收货函模版后拍照上传");
                paperProofPO.setExample(orderMaterialConfig.getPaperExample());
                paperProofPO.setRequisite(0);
                paperProofPO.setUniqueCheck(0);
                paperProofPO.setSameLinkPicRepeatCheck(0);
                paperProofPO.setDifLinkConsistentCheck(0);
                paperProofPO.setHisPicRepeatCheck(0);
                paperProofPO.setSceneNo(ProofSceneEnum.RECEIVE.getCode());
                paperProofPO.setSceneName(ProofSceneEnum.RECEIVE.getDesc());
                paperProofPO.setIsUpload(false);
                paperProofPO.setCreateBy("auto2");
                paperProofPO.setUpdateBy("auto2");
                paperProofPO.setEleSignInfo("");
                paperProofPO.setNeedFaceAuth(0);
                save(paperProofPO);
                resultPOList.add(paperProofPO);
            }
        }
        if (CollectionUtils.isEmpty(fileScenesProofVOS)) {
            List<MallFileScenesProofVO> res = Lists.newArrayList();
            List<OrderTradeProofPO> signProofList = resultPOList.stream().filter(p -> p.getMaterialNo().equals(orderMaterialConfig.getEleReceiveMaterialNo()) || p.getMaterialNo().equals(orderMaterialConfig.getReceiveMaterialNo())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(signProofList)) {
                Map<String, List<OrderTradeProofPO>> proofMap = signProofList.stream().collect(Collectors.groupingBy(OrderTradeProofPO::getSceneNo));
                proofMap.forEach((sceneNo, proofList) -> {
                    OrderTradeProofPO proofPO = proofList.get(0);
                    // 转换为电商实体
                    MallFileScenesProofVO proofVO = new MallFileScenesProofVO();
                    proofVO.setProofNo(proofPO.getOrderSn());
                    proofVO.setProofType(proofPO.getMaterialType());
                    proofVO.setSceneNo(proofPO.getSceneNo());
                    proofVO.setSceneName(proofPO.getSceneName());
                    proofVO.setCreateTime(proofPO.getCreateTime());
                    List<MallFileScenesProofMaterialVO> materialVOS = proofList.stream().map(p -> {
                        MallFileScenesProofMaterialVO mallFileScenesProofMaterialVO = new MallFileScenesProofMaterialVO();
                        BeanUtils.copyProperties(p, mallFileScenesProofMaterialVO);
                        mallFileScenesProofMaterialVO.setId(p.getId());
                        mallFileScenesProofMaterialVO.setIsUpload(p.getIsUpload());
                        return mallFileScenesProofMaterialVO;
                    }).collect(Collectors.toList());
                    proofVO.setMaterialVOList(materialVOS);
                    res.add(proofVO);
                });
            }
//            List<OrderTradeProofPO> paperProofList = resultPOList.stream().filter(p -> p.getMaterialNo().equals(orderMaterialConfig.getReceiveMaterialNo())).collect(Collectors.toList());
//            if (!CollectionUtils.isEmpty(paperProofList)) {
//                Map<String, List<OrderTradeProofPO>> proofMap = paperProofList.stream().collect(Collectors.groupingBy(OrderTradeProofPO::getSceneNo));
//                proofMap.forEach((sceneNo, proofList) -> {
//                    OrderTradeProofPO proofPO = proofList.get(0);
//                    // 转换为电商实体
//                    MallFileScenesProofVO proofVO = new MallFileScenesProofVO();
//                    proofVO.setProofNo(proofPO.getOrderSn());
//                    proofVO.setProofType(proofPO.getMaterialType());
//                    proofVO.setSceneNo(proofPO.getSceneNo());
//                    proofVO.setSceneName(proofPO.getSceneName());
//                    proofVO.setCreateTime(proofPO.getCreateTime());
//                    List<MallFileScenesProofMaterialVO> materialVOS = proofList.stream().map(eleProof -> {
//                        MallFileScenesProofMaterialVO mallFileScenesProofMaterialVO = new MallFileScenesProofMaterialVO();
//                        BeanUtils.copyProperties(eleProof, mallFileScenesProofMaterialVO);
//                        mallFileScenesProofMaterialVO.setId(eleProof.getId());
//                        mallFileScenesProofMaterialVO.setIsUpload(eleProof.getIsUpload());
//                        return mallFileScenesProofMaterialVO;
//                    }).collect(Collectors.toList());
//                    proofVO.setMaterialVOList(materialVOS);
//                    res.add(proofVO);
//                });
//            }
            return res;
        }

        // 不展示协议，移出
        for (FileScenesProofVO fileScenesProofVO : fileScenesProofVOS) {
            for (FileScenesProofMaterialVO scenesProofMaterialVO : fileScenesProofVO.getMaterialVOList()) {
                if (OrderMaterialTypeEnum.AGREEMENT == OrderMaterialTypeEnum.getValue(scenesProofMaterialVO.getMaterialType())) {
                    fileScenesProofVO.getMaterialVOList().remove(scenesProofMaterialVO);
                    break;
                }
            }
        }
        return fileScenesProofVOS.stream().map(vo -> {
            // 转换为电商实体
            MallFileScenesProofVO proofVO = new MallFileScenesProofVO();
            proofVO.setProofNo(vo.getProofNo());
            proofVO.setProofType(vo.getProofType());
            proofVO.setSubProofNo(vo.getSubProofNo());
            proofVO.setSubProofType(vo.getSubProofType());
            proofVO.setSceneNo(vo.getSceneNo());
            proofVO.setSceneName(vo.getSceneName());
            proofVO.setCreateTime(vo.getCreateTime());
            List<FileScenesProofMaterialVO> materialVOList = vo.getMaterialVOList();
            List<OrderTradeProofPO> sameScene = resultPOList.stream().filter(p -> p.getSceneNo().equals(vo.getSceneNo())).collect(Collectors.toList());
            boolean containsEle = sameScene.stream().anyMatch(p -> p.getMaterialNo().equals(orderMaterialConfig.getEleReceiveMaterialNo()));
            boolean containsPaper = sameScene.stream().anyMatch(p -> p.getMaterialNo().equals(orderMaterialConfig.getReceiveMaterialNo()));
            boolean eleHasUpload = false;
            boolean paperHasUpload = false;
            List<MallFileScenesProofMaterialVO> materialVOS = Lists.newArrayList();
            for (FileScenesProofMaterialVO materialVO : materialVOList) {
                MallFileScenesProofMaterialVO mallFileScenesProofMaterialVO = new MallFileScenesProofMaterialVO();
                BeanUtils.copyProperties(materialVO, mallFileScenesProofMaterialVO);
                OrderTradeProofPO proofPO = sameScene.stream().filter(p -> p.getMaterialNo().equals(materialVO.getMaterialNo())).findFirst().orElse(null);
                if (!Objects.isNull(proofPO)) {
                    mallFileScenesProofMaterialVO.setId(proofPO.getId());
                    mallFileScenesProofMaterialVO.setIsUpload(proofPO.getIsUpload());
                    mallFileScenesProofMaterialVO.setNeedFaceAuth(proofPO.getNeedFaceAuth());
                    mallFileScenesProofMaterialVO.setPaymentCode(proofPO.getPaymentCode());
                    mallFileScenesProofMaterialVO.setPaymentName(proofPO.getPaymentName());
                    mallFileScenesProofMaterialVO.setContractNo(proofPO.getContractNo());
                    mallFileScenesProofMaterialVO.setFddContractNo(proofPO.getFddContractNo());
                    mallFileScenesProofMaterialVO.setUserType(proofPO.getUserType());
                    mallFileScenesProofMaterialVO.setExample(proofPO.getExample());
                    mallFileScenesProofMaterialVO.setRequisite(proofPO.getRequisite());
                }
                materialVOS.add(mallFileScenesProofMaterialVO);
                if (materialVO.getMaterialNo().equals(orderMaterialConfig.getEleReceiveMaterialNo())) {
                    eleHasUpload = true;
                }
                if (materialVO.getMaterialNo().equals(orderMaterialConfig.getReceiveMaterialNo())) {
                    paperHasUpload = true;
                }
            }
            // 如果包含了电签，但是电签没有上传，需要塞一条记录进来，让前端能够上传
            if (containsEle && !eleHasUpload) {
                OrderTradeProofPO eleProof = resultPOList.stream().filter(p -> p.getMaterialNo().equals(orderMaterialConfig.getEleReceiveMaterialNo())).findFirst().orElse(null);
                if (!Objects.isNull(eleProof)) {
                    MallFileScenesProofMaterialVO mallFileScenesProofMaterialVO = new MallFileScenesProofMaterialVO();
                    BeanUtils.copyProperties(eleProof, mallFileScenesProofMaterialVO);
                    mallFileScenesProofMaterialVO.setId(eleProof.getId());
                    mallFileScenesProofMaterialVO.setIsUpload(eleProof.getIsUpload());
                    materialVOS.add(mallFileScenesProofMaterialVO);
                }
            }
            // 如果包含了纸签，也需要塞一条记录进来，无论是否上传，让前端能上传
            if (containsPaper && !paperHasUpload) {
                OrderTradeProofPO paperProof = resultPOList.stream().filter(p -> p.getMaterialNo().equals(orderMaterialConfig.getReceiveMaterialNo())).findFirst().orElse(null);
                if (!Objects.isNull(paperProof)) {
                    MallFileScenesProofMaterialVO mallFileScenesProofMaterialVO = new MallFileScenesProofMaterialVO();
                    BeanUtils.copyProperties(paperProof, mallFileScenesProofMaterialVO);
                    mallFileScenesProofMaterialVO.setId(paperProof.getId());
                    mallFileScenesProofMaterialVO.setIsUpload(paperProof.getIsUpload());
                    materialVOS.add(mallFileScenesProofMaterialVO);
                }
            }
            proofVO.setMaterialVOList(materialVOS);
            return proofVO;
        }).collect(Collectors.toList());
    }

    /**
     * 根据订单查询所有交易真实性资料 - 电子签
     *
     * @param orderSn 订单编号
     */
    @Override
    public ContractSimpleVo listSceneMaterialForAgricEle(String orderSn) {
        // 查询收货确认函资料
        List<OrderTradeProofVO> orderTradeProofVOS = listSceneMaterial(orderSn, null, orderMaterialConfig.getEleReceiveMaterialNo(), ProofSceneEnum.RECEIVE);
        if (CollectionUtils.isEmpty(orderTradeProofVOS)) {
            return null;
        }
        // 取第一个,理论上也只会有一个
        OrderTradeProofVO proofVO = orderTradeProofVOS.get(0);
        ContractSearchResultVO resultVO = contractSearch(proofVO.getId(), 0);
        ContractSimpleVo vo = new ContractSimpleVo();
        vo.setContractNo(proofVO.getContractNo());
        vo.setFileName(proofVO.getMaterialName());
        vo.setViewUrl(resultVO.getViewUrl());
        vo.setDownloadUrl(resultVO.getDownUrl());
        return vo;
    }

    /**
     * 刷脸认证信息获取
     *
     * @param member 用户
     * @return 刷脸信息
     */
    @Override
    public FaceAuthResultVO faceAuthInfo(Member member) {
        if (Objects.isNull(member)) {
            return null;
        }
        UserBaseInfo userBaseInfo = userInfoComponent.userBaseInfoByUserNo(member.getUserNo());
        if (Objects.isNull(userBaseInfo)) {
            throw new BusinessException("请先登录");
        }
        FaceAuthResultVO vo = new FaceAuthResultVO();
        vo.setName(userBaseInfo.getCustomerName());
        vo.setIdNo(userBaseInfo.getIdNo());
        return vo;
    }

    /**
     * 重新签署
     *
     * @param proofIdList
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void contractReSign(List<Long> proofIdList, Integer force) {
        List<OrderTradeProofPO> proofPOS = this.listByIds(proofIdList);
        // 清空合同编码,后续重新生成
        for (OrderTradeProofPO proofPO : proofPOS) {
            proofPO.setContractNo(null);
        }
        updateBatchById(proofPOS);
        // 重新生成合同并默签
        for (OrderTradeProofPO proofPO : proofPOS) {
            log.info("contractReSign 开始处理,proofPO:{}", proofPO);
            try {
                String orderSn = proofPO.getOrderSn();
                OrderPO orderPO = orderService.getByOrderSn(orderSn);
                Member member = memberFeignClient.getOrRegisterByCappUserNo(orderPO.getUserNo());
                // 生成合同
                realGenerateContract(orderSn, proofPO.getMaterialNo(), proofPO.getId());
                // force = 1 或者原本就已经签署 时，进行默签
                if (NumberUtils.INTEGER_ONE.equals(force) || proofPO.getIsUpload()) {
                    // 如果之前已经签署了，需要同步签署
                    ContractSignParamVO paramVO = new ContractSignParamVO();
                    paramVO.setProofId(proofPO.getId());
                    // 默签
                    contractSign(member, paramVO);
                }
            } catch (Exception e) {
                log.info("contractReSign 失败,e:{}", e.getMessage());
            }
        }
    }

    /**
     * 软删除部分数据
     *
     * @param ids 需要删除的id
     */
    @Override
    public void softDel(String ids) {
        if (StringUtils.isBlank(ids)) {
            return;
        }
        String[] split = ids.split(",");
        List<Long> idList = Lists.newArrayList();
        for (String s : split) {
            idList.add(Long.valueOf(s));
        }
        LambdaUpdateWrapper<OrderTradeProofPO> updateWrapper = Wrappers.lambdaUpdate(OrderTradeProofPO.class);
        updateWrapper.in(OrderTradeProofPO::getId, idList);
        updateWrapper.set(OrderTradeProofPO::getEnabledFlag, CommonEnum.NO.getCode());
        update(updateWrapper);
    }

    /**
     * 交易真实性判定是否为贷款类支付
     *
     * @param orderPO 订单实体
     * @return
     */
    public boolean checkProofLoanPay(OrderPO orderPO) {
        if (Objects.isNull(orderPO)) {
            return false;
        }
        if (PayMethodEnum.isLoanPay(orderPO.getPaymentCode())) {
            // 支付方式为贷款类
            return true;
        } else if (PayMethodEnum.isCombinationPay(orderPO.getPaymentCode())) {
            // 组合支付，判断尾款是否为贷款类支付
            String paySn = orderPO.getPaySn();
            if (StringUtils.isNotBlank(paySn)) {
                List<OrderPayRecordPO> orderPayRecordPOS = orderPayRecordService.queryOrderPayByPaySn(paySn);
                if (!CollectionUtils.isEmpty(orderPayRecordPOS)) {
                    OrderPayRecordPO balanceRecord = orderPayRecordPOS.stream().filter(p -> PresellCapitalTypeEnum.BALANCE.getValue().equals(p.getPayOrder()) && PayMethodEnum.isLoanPay(p.getPaymentCode())).findFirst().orElse(null);
                    if (Objects.nonNull(balanceRecord)) {
                        return true;
                    }
                } else {
                    log.info("checkProofLoanPay 组合支付没有order pay记录，orderSn：{},paySn:{}", orderPO.getOrderSn(), orderPO.getPaySn());
                }
            } else {
                log.info("checkProofLoanPay 组合支付没有支付单号？，orderSn：{},paySn:{}", orderPO.getOrderSn(), orderPO.getPaySn());
            }
        }
        return false;
    }

    /**
     * 签单导出
     *
     * @param orderSn 订单编号
     * @return 签单导出文件url
     */
    @Override
    public String paperSignExport(String orderSn) {
        OrderPO orderPO = orderService.getByOrderSn(orderSn);
        if (Objects.isNull(orderPO)){
            throw new BusinessException("订单不存在");
        }
        String userNo = orderPO.getUserNo();
        UserBaseInfo userBaseInfo = userInfoComponent.userBaseInfoByUserNo(userNo);
        if (Objects.isNull(userBaseInfo)) {
            log.warn("generateEleDoc 未找到用户信息");
            throw new BusinessException("用户信息异常，请联系管理员");
        }
        StoreContractReceiptInfoVO storeContract =
                storeFeignClient.getStoreContractReciptInfo(orderPO.getStoreId());
        JsonResult<String> contractSellerNameByStoreIdResult = storeFeignClient.getContractSellerNameByStoreId(orderPO.getStoreId());
        String contractSellerName = contractSellerNameByStoreIdResult.getData();
        if (StringUtils.isBlank(contractSellerName)) {
            throw new BusinessException("获取合同卖家名失败，请联系管理员");
        }
        if (Objects.isNull(storeContract)) {
            log.warn("generateEleDoc 未找到店铺合同信息");
        }
        OrderExtendPO orderExtendPO = orderExtendService.getOrderExtendByOrderSn(orderSn);
        if (Objects.isNull(orderExtendPO)) {
            log.warn("generateEleDoc 未找到订单拓展信息");
            throw new BusinessException("订单信息异常，请联系管理员");
        }
        List<OrderProductPO> orderProductPOS = orderProductService.listByOrderSn(orderSn);
        if (CollectionUtils.isEmpty(orderProductPOS)) {
            log.warn("generateEleDoc 未找到订单商品信息");
            throw new BusinessException("订单商品信息异常，请联系管理员");
        }
        QueryUniqueCodeParam queryUniqueCodeParam = new QueryUniqueCodeParam();
        queryUniqueCodeParam.setApplyId(orderSn);
        queryUniqueCodeParam.setLoanCustId(userBaseInfo.getCustomerId());
        String uniqueCode = null;
        try {
            uniqueCode = ExternalApiUtil.callResultApi(() -> loanFinanceFacade.queryUniqueCode(orderSn, userBaseInfo.getCustomerId()), queryUniqueCodeParam, "/query/queryMallPaySummary", "查询信贷唯一码");
        } catch (Exception e) {
            log.warn("获取信贷唯一码失败,原因:{}",e.getMessage());
        }

        // 获取订单对应信息，生成word文件
        Map<String, Object> data = Maps.newHashMap();
        List<OrderProductBuildParamDTO> productParamList = Lists.newArrayList();
        for (int i = 0; i < orderProductPOS.size(); i++) {
            OrderProductPO p = orderProductPOS.get(i);
            OrderProductBuildParamDTO paramDTO = new OrderProductBuildParamDTO();
            paramDTO.setGoodsName(p.getGoodsName());
            paramDTO.setSpec(p.getSpecValues());
            paramDTO.setNum(p.getProductNum().toString());
            paramDTO.setPrice(p.getProductShowPrice().toString());
            paramDTO.setGoodsAmount(p.getGoodsAmountTotal().toString());
            paramDTO.setIndex((i + 1) + "");
            productParamList.add(paramDTO);
        }
        data.put("orderSn",orderPO.getOrderSn());
        data.put("productInfoList",productParamList);
        data.put("businessLicenseName",contractSellerName);
        data.put("receiveAddress",orderExtendPO.getReceiverAreaInfo() + orderExtendPO.getReceiverAddress());
        data.put("orderDate",DateUtil.format(orderPO.getCreateTime(), DatePattern.NORM_DATETIME_PATTERN));
        data.put("receiver",orderExtendPO.getReceiverName());
        data.put("contactMobile",orderExtendPO.getReceiverMobile());
        data.put("custName",userBaseInfo.getCustomerName());
        data.put("custIdNo",userBaseInfo.getIdNo());
        data.put("totalAmount",orderPO.getOrderAmount().toString());
        data.put("uniqueCode",uniqueCode);
        data.put("custPhone",userBaseInfo.getCustPhone());
        // 生成导出签单
        try {
            return poiWordHelper.exportWordReturnUrl(WordModelEnum.ORDER_SIGN_CONTRACT_WORD, data, orderSn);
        } catch (Exception e) {
            log.warn("批量导出客户收货确认函异常，eMessage:{}", e.getMessage(), e);
            throw new BusinessException("导出客户收货确认函异常");
        }
    }

    /**
     * 获取农服经销商下单合同
     *
     * @param orderSn 订单编号
     * @return 结果vo
     */
    @Override
    public AgricDistributorContractResultVO getAgricDealerContract(String orderSn) {
        if (StringUtils.isBlank(orderSn)) {
            throw new BusinessException("订单号为空");
        }
        OrderPO orderPO = orderService.getByOrderSn(orderSn);
        if (Objects.isNull(orderPO)) {
            throw new BusinessException("订单号有误");
        }
        AgricDistributorContractResultVO vo = new AgricDistributorContractResultVO();
        JsonResult<PurchaserFeignResultVO> dealResult = ExternalApiUtil.callJsonResultApi(() -> purchaserFeign.getPurchaserRelationByUserNoAndSupllierStoreId(orderPO.getUserNo(), orderPO.getStoreId()), orderPO.getStoreId(),
                "/purchaser/isDealer", "判定用户在当前店铺是否为经销商");
        if (Objects.isNull(dealResult.getData())) {
            vo.setNeedSign(NumberUtils.INTEGER_ZERO);
            return vo;
        }
        // 默认需要签署
        vo.setNeedSign(NumberUtils.INTEGER_ONE);
        LambdaQueryWrapper<OrderTradeProofPO> proofQuery = Wrappers.lambdaQuery(OrderTradeProofPO.class);
        proofQuery.eq(OrderTradeProofPO::getOrderSn, orderSn);
        proofQuery.eq(OrderTradeProofPO::getSceneNo, ProofSceneEnum.SUBMIT.getCode());
        proofQuery.eq(OrderTradeProofPO::getMaterialNo, orderMaterialConfig.getAgricDealerMaterialNo());
        List<OrderTradeProofPO> proofList = list(proofQuery);
        OrderTradeProofPO agricDealProof = null;
        if (CollectionUtils.isEmpty(proofList)) {
            // 生成
            agricDealProof = new OrderTradeProofPO();
            agricDealProof.setOrderSn(orderSn);
            agricDealProof.setMaterialNo(orderMaterialConfig.getAgricDealerMaterialNo());
            agricDealProof.setMaterialName("销售合同");
            agricDealProof.setMaterialType("6");
            agricDealProof.setMaterialRemark("");
            agricDealProof.setExample(orderMaterialConfig.getPaperExample());
            agricDealProof.setRequisite(0);
            agricDealProof.setUniqueCheck(0);
            agricDealProof.setSameLinkPicRepeatCheck(0);
            agricDealProof.setDifLinkConsistentCheck(0);
            agricDealProof.setHisPicRepeatCheck(0);
            agricDealProof.setSceneNo(ProofSceneEnum.SUBMIT.getCode());
            agricDealProof.setSceneName(ProofSceneEnum.SUBMIT.getDesc());
            agricDealProof.setIsUpload(false);
            agricDealProof.setCreateBy("getAgricDealerContract");
            agricDealProof.setUpdateBy("getAgricDealerContract");
            agricDealProof.setEleSignInfo(orderMaterialConfig.getAgricEleInfo());
            agricDealProof.setNeedFaceAuth(1);
            save(agricDealProof);
        } else {
            agricDealProof = proofList.get(0);
            if (agricDealProof.getIsUpload()) {
                // 已签署不需要再签署
                vo.setNeedSign(NumberUtils.INTEGER_ZERO);
            }
        }
        if (StringUtils.isBlank(agricDealProof.getContractNo())) {
            // 生成合同
            try {
                String result = generateAgricDealContract(agricDealProof, orderPO);
                log.info("generateAgricDealContract result:{},orderSn:{}",result,orderPO.getOrderSn());
                // 重新查一次
                agricDealProof = getById(agricDealProof.getId());
            } catch (Exception e) {
                // 不阻塞下单流程,进行异常提示
                log.warn("generateAgricDealContract 异常，原因：{}",e.getMessage());
                vo.setNeedSign(NumberUtils.INTEGER_ZERO);
            }
        }
        vo.setProofId(agricDealProof.getId());
        if (StringUtils.isNotBlank(agricDealProof.getContractNo())){
            // 查询合同
            ContractFindRequest findRequest = new ContractFindRequest();
            findRequest.setContractNo(agricDealProof.getContractNo());
            findRequest.setContractExpireHour(1L);
            List<ContractFindResponse> contractResponse = ExternalApiUtil.callResultApi(() -> contractFacade.findAllContract(findRequest), findRequest, "/contract/common/findAllContract", "合同查询");
            ContractFindResponse response = contractResponse.get(0);
            String viewURL = response.getViewURL();
            vo.setViewUrl(viewURL);
        } else {
            // 合同编号为空，不阻塞，但是提示
            log.warn("getAgricDealerContract 合同编号为空");
            vo.setNeedSign(NumberUtils.INTEGER_ZERO);
        }
        return vo;
    }

    /**
     * 生成电签合同
     *
     * @param materialNo 资料编码
     * @return 生成的合同编码, 后续进行电签时需要使用该编码进行签署
     */
    public String generateAgricDealContract(OrderTradeProofPO proofPO, OrderPO orderPO) {
        log.info("start generateAgricDealContract proofPo:{}", proofPO);
        if (Objects.isNull(orderPO)) {
            log.warn("generateAgricDealContract 未找到订单信息");
            return null;
        }
        List<OrderProductPO> productList = orderProductService.listByOrderSn(proofPO.getOrderSn());
        String userNo = orderPO.getUserNo();
        UserBaseInfo userBaseInfo = userInfoComponent.userBaseInfoByUserNo(userNo);
        if (Objects.isNull(userBaseInfo)) {
            log.warn("generateAgricDealContract 未找到用户信息");
            return null;
        }
        JsonResult<String> contractSellerNameByStoreIdResult = storeFeignClient.getContractSellerNameByStoreId(orderPO.getStoreId());
        String contractSellerName = contractSellerNameByStoreIdResult.getData();
        if (StringUtils.isBlank(contractSellerName)) {
            throw new BusinessException("获取合同卖家名失败，请联系管理员");
        }
        ContractVariableDTO variableDTO = null;
        try {
            Result<ContractDetailVO> contractDetail = agricCrmFacade.getByTemplateNameAndType(contractSellerName, 4);
            if (contractDetail.isSuccess()){
                ContractDetailVO data = contractDetail.getData();
                if (Objects.nonNull(data)) {
                    variableDTO = data.getContractVariableDto();
                }else{
                    throw new BusinessException("根据合同模板名称、类型获取合同模板信息结果为空                                                                                                                                                                                                  ");
                }
            }
        }catch (BusinessException businessException){
            throw businessException;
        } catch (Exception e) {
            throw new BusinessException("获取合同模板信息失败，请联系管理员");
        }
        JsonResult<PurchaserFeignResultVO> purchaserRelationResult = purchaserFeign.getPurchaserRelationByUserNoAndSupllierStoreId(orderPO.getUserNo(), orderPO.getStoreId());
        if (Objects.isNull(purchaserRelationResult) || !purchaserRelationResult.getState().equals(200)){
            throw new BusinessException("获取采购用户信息失败");
        }
        PurchaserFeignResultVO purchaserFeignResultVO = purchaserRelationResult.getData();
        String orderSn = orderPO.getOrderSn();
        OrderExtendPO orderExtendPO = orderExtendService.getOrderExtendByOrderSn(orderSn);
        if (Objects.isNull(orderExtendPO)) {
            log.warn("generateAgricDealContract 未找到订单拓展信息");
            return null;
        }
        List<OrderProductPO> orderProductPOS = orderProductService.listByOrderSn(orderSn);
        if (CollectionUtils.isEmpty(orderProductPOS)) {
            log.warn("generateAgricDealContract 未找到订单商品信息");
            return null;
        }
        try {
            List<OrderProductBuildParamDTO> productParamList = Lists.newArrayList();
            for (int i = 0; i < productList.size(); i++) {
                OrderProductPO p = productList.get(i);
                OrderProductBuildParamDTO paramDTO = new OrderProductBuildParamDTO();
                paramDTO.setGoodsName(p.getGoodsName());
                paramDTO.setSpec(p.getSpecValues());
                paramDTO.setNum(p.getProductNum().toString());
                paramDTO.setPrice(p.getProductShowPrice().toString());
                paramDTO.setGoodsAmount(p.getGoodsAmountTotal().toString());
                paramDTO.setIndex((i + 1) + "");
                productParamList.add(paramDTO);
            }
            //参数可以为任意对象,会自动转换为生成合同需要的格式
            LocalDate ld = LocalDate.now();
            AgricDealerContractBuildParamDTO params = new AgricDealerContractBuildParamDTO();
            params.setTenantStoreName(variableDTO.getTenantStoreName());
            params.setOrderSn(orderSn);
            params.setTenantManagerName(variableDTO.getTenantManagerName());
            params.setTenantManagerIdCard(variableDTO.getTenantManagerIdCard());
            params.setTenantAddress(variableDTO.getTenantAddress());
            params.setTenantManagerPhone(variableDTO.getTenantManagerPhone());
            params.setTenantSignetCode(variableDTO.getTenantSignetCode());
            params.setPurchaserName(purchaserFeignResultVO.getUserName());
            params.setPurchaserPhone(purchaserFeignResultVO.getMobile());
            StringBuilder purchaserAddress = new StringBuilder();
            if (StringUtils.isNotBlank(purchaserFeignResultVO.getProviceName())){
                purchaserAddress.append(purchaserFeignResultVO.getProviceName());
            }
            if (StringUtils.isNotBlank(purchaserFeignResultVO.getCityName())){
                purchaserAddress.append(purchaserFeignResultVO.getCityName());
            }
            if (StringUtils.isNotBlank(purchaserFeignResultVO.getCountyName())){
                purchaserAddress.append(purchaserFeignResultVO.getCountyName());
            }
            if (StringUtils.isNotBlank(purchaserFeignResultVO.getTownName())){
                purchaserAddress.append(purchaserFeignResultVO.getTownName());
            }
            if (StringUtils.isNotBlank(purchaserFeignResultVO.getVillageName())){
                purchaserAddress.append(purchaserFeignResultVO.getVillageName());
            }
            if (StringUtils.isNotBlank(purchaserFeignResultVO.getAddr())){
                purchaserAddress.append(purchaserFeignResultVO.getAddr());
            }
            params.setPurchaserAddress(purchaserAddress.toString());
            params.setProductInfoList(productParamList);
            params.setYear(ld.getYear() + "");
            params.setMonth(ld.getMonthValue() + "");
            params.setDay(ld.getDayOfMonth() + "");
            String remark = "商品总金额(¥%s)+ 运费(¥%s) - 优惠总金额(¥%s) = 订单金额(¥%s)";
            params.setRemark(String.format(remark, orderPO.getOrderAmountTotal(), orderPO.getExpressFeeTotal(), orderPO.getActivityDiscountAmount(), orderPO.getOrderAmount()));
            ContractGenerateRequest generateRequest = new ContractGenerateRequest();
            //设置业务流水号
            String flowNo = orderPO.getOrderSn() + "_" + proofPO.getId() + RandomUtil.randomNumbers(2);
            generateRequest.setBuzNo(flowNo);
            //设置业务合同编号,可于业务流水号一致
            generateRequest.setBuzContractNo(flowNo);
            //模板编号,即需要生成的合同模板
            generateRequest.setTemplateNo(proofPO.getEleSignInfo());
            //设置合同生成参数,直接通过构造函数创建ContractParamVo类就行
            generateRequest.setContractParamVo(new ContractParamVo(params));
            //设置contractType:01-进件, 02-授信, 03-放款， 04-外部合同， 05-其它
            generateRequest.setContractType("05");
            //设置签约类型:参考SignTypeEnum, paper-纸质合同,eSignH-客户手签,eSignA-客户默签
            generateRequest.setSignType(SignTypeEnum.E_SIGN_A.getCode());
            generateRequest.setRemark("乡助电商订单:" + orderPO.getOrderSn());
            //设置业务来源,参考BusinessResourceEnum枚举类,LOAN-信贷，FRONT-前端，HR-hr系统，OTHER-其它
            generateRequest.setResource(BusinessResourceEnum.OTHER.getCode());
            ContractFindResponse contractResponse = ExternalApiUtil.callResultApi(() -> contractFacade.generateContract(generateRequest), generateRequest, "/contract/before/generate", "合同生成");
            log.info("generateAgricDealContract contractResponse:{}", contractResponse);
            proofPO.setContractNo(contractResponse.getContractNo());
            String viewUrl = contractResponse.getViewURL();
            proofPO.setFileOriginUrl(viewUrl);
            // 调用文件中心上传资料
            FileScenesMaterialProofDTO paramDTO = new FileScenesMaterialProofDTO();
            paramDTO.setProofNo(proofPO.getOrderSn());
            paramDTO.setProofType(IScenesMaterialProofConstant.ProofTypeEnum.MALL_ORDER_ORDER_SN);
            paramDTO.setSceneNo(proofPO.getSceneNo());
            FileMaterialDTO materialDTO = new FileMaterialDTO();
            materialDTO.setMaterialNo(proofPO.getMaterialNo());
            materialDTO.setMaterialName(proofPO.getMaterialName());
            materialDTO.setMaterialType(proofPO.getMaterialType());
            materialDTO.setMaterialContentList(Collections.singletonList(viewUrl));
            materialDTO.setProofRemark(proofPO.getMaterialRemark());
            materialDTO.setRiskLevelMap(Maps.newHashMap());
            materialDTO.setRiskReason(Maps.newHashMap());
            String md5 = null;
            List<String> md5List = Lists.newArrayList();
            try {
                md5 = ExpressDeliveryUtil.calculateMD5(viewUrl);
            } catch (NoSuchAlgorithmException e) {
                log.error("转换MD5 NoSuchAlgorithmException", e);
            } catch (IOException e) {
                log.error("转换MD5 IOException", e);
            } catch (Exception e) {
                log.error("转换MD5 Exception", e);
            } finally {
                md5List.add(md5);
            }
            materialDTO.setPicEncrypt(md5);
            materialDTO.setProviderId("0");
            materialDTO.setProviderName("system");
            materialDTO.setProviderType(IScenesMaterialProofConstant.ProviderTypeEnum.PLAT_OPERATOR);
            paramDTO.setMaterialDTOList(Collections.singletonList(materialDTO));
            fileCenterIntegration.saveScenesMaterialProofV2(paramDTO);
            updateById(proofPO);
            return viewUrl;
        } catch (Exception e) {
            log.warn("generateAgricDealContract 执行失败，proofPO:{},原因:{}", proofPO,e.getMessage());
        }
        return null;
    }

    /**
     * 农服经销商合同签署
     *
     * @param member  用户信息
     * @param paramVO 签署参数
     * @return 签署结果
     */
    @Override
    public ContractSignResultVO agricDealerContractSign(Member member, ContractSignParamVO paramVO) {
        UserBaseInfo userBaseInfo = userInfoComponent.userBaseInfoByUserNo(member.getUserNo());
        MallCustomerCheckResultVO customerCheckResultVO = this.customerCheck(member, userBaseInfo, "chongho.net", CommonConst.CONTRACT_CHECK_GEN_URL_NO, null);
        if (Objects.isNull(customerCheckResultVO)) {
            throw new BusinessException("认证失败，请稍后重试");
        }
        if (!customerCheckResultVO.getAuthed()) {
            throw new BusinessException("您尚未授权自动签署，请登录乡助APP进行签署授权");
        }
        Long proofId = paramVO.getProofId();
        OrderTradeProofPO proof = getById(proofId);
        if (Objects.isNull(proof)) {
            throw new BusinessException("未找到对应交易凭证");
        }
        OrderPO orderPO = orderService.getByOrderSn(proof.getOrderSn());
        if (Objects.isNull(orderPO)) {
            throw new BusinessException("未找到对应订单");
        }
        if (!orderPO.getMemberId().equals(member.getMemberId())) {
            throw new BusinessException("您无权操作此订单");
        }
        String fileOriginUrl = proof.getFileOriginUrl();
        if (StringUtils.isNotBlank(fileOriginUrl) && fileOriginUrl.contains("fadada.com")) {
            // 线上很奇怪的点是法大大可能没有返回合同编号，因此只能通过域名来判断是否签署成功,正常应该通过法大大合同编号判断
            throw new BusinessException("您已完成签署，请刷新后重试");
        }
        List<String> contractNoList = Lists.newArrayList();
        // 合同信息
        ContractESignAutoRequest eSignAutoRequest = new ContractESignAutoRequest();
        eSignAutoRequest.setResource(BusinessResourceEnum.OTHER.getCode());
        eSignAutoRequest.setSignType(SignMethodEnum.CUSTOMER_AND_COMPANY.getCode());
        //合同编号,取资料中配置的合同编号
        eSignAutoRequest.setContractNo(proof.getContractNo());

        //设置公司签约信息
        ContractCompanySignVO companySignVO = new ContractCompanySignVO();
        companySignVO.setCompanyCode("ZHNF");
        companySignVO.setSignKeyword("中和农服(北京)农业科技");
        eSignAutoRequest.setCompanySignList(Arrays.asList(companySignVO));

        //设置客户签约信息
        String userMobile = userBaseInfo.getContactPhone();
        if (StringUtils.isBlank(userMobile)) {
            log.info("contractSign contactPhone为空，使用custPhone");
            userMobile = userBaseInfo.getCustPhone();
        }
        ContractCustomerSignVO coborrowerSign = new ContractCustomerSignVO();
        coborrowerSign.setCustId(userBaseInfo.getCustomerId());
        coborrowerSign.setCustName(userBaseInfo.getCustomerName());
        coborrowerSign.setIdCardNo(userBaseInfo.getIdNo());
        coborrowerSign.setMobile(userMobile);
        coborrowerSign.setSignKeyword("signLocation");
        eSignAutoRequest.setCustomerSignList(Arrays.asList(coborrowerSign));
        eSignAutoRequest.setContractSignToken("mall-d8824b88-35d9-48e2-b298-295b2345e672");
        //调用签约方法
        Result<ContractFindResponse> contractESignAutoResponseResult = contractFacade.contractSign(eSignAutoRequest);
        log.info("asynAutoV2 ,param:{},result:{}", eSignAutoRequest, contractESignAutoResponseResult);
        if (!contractESignAutoResponseResult.isSuccess()) {
            throw new BusinessException("签署失败，失败原因：" + contractESignAutoResponseResult.getMessage());
        }
        ContractFindResponse response = contractESignAutoResponseResult.getData();
        String viewURL = response.getViewURL();
        proof.setFileOriginUrl(viewURL);
        contractNoList.add(viewURL);
        // 修改为已上传
        proof.setIsUpload(Boolean.TRUE);
        // 更新交易真实性资料情况
        updateById(proof);
        ContractSignResultVO resultVO = new ContractSignResultVO();
        resultVO.setFddContractNoList(contractNoList);
        resultVO.setViewUrl(viewURL);
        resultVO.setDownloadURL(response.getDownloadURL());
        FileScenesMaterialProofDTO paramDTO = new FileScenesMaterialProofDTO();
        paramDTO.setProofNo(proof.getOrderSn());
        paramDTO.setProofType(IScenesMaterialProofConstant.ProofTypeEnum.MALL_ORDER_ORDER_SN);
        paramDTO.setSceneNo(proof.getSceneNo());
        FileMaterialDTO materialDTO = new FileMaterialDTO();
        materialDTO.setMaterialNo(proof.getMaterialNo());
        materialDTO.setMaterialName(proof.getMaterialName());
        materialDTO.setMaterialType(proof.getMaterialType());
        materialDTO.setMaterialContentList(Collections.singletonList(viewURL));
        materialDTO.setProofRemark(proof.getMaterialRemark());
        materialDTO.setRiskLevelMap(Maps.newHashMap());
        materialDTO.setRiskReason(Maps.newHashMap());
        String md5 = null;
        List<String> md5List = Lists.newArrayList();
        try {
            md5 = ExpressDeliveryUtil.calculateMD5(viewURL);
        } catch (NoSuchAlgorithmException e) {
            log.error("转换MD5 NoSuchAlgorithmException", e);
        } catch (IOException e) {
            log.error("转换MD5 IOException", e);
        } catch (Exception e) {
            log.error("转换MD5 Exception", e);
        } finally {
            md5List.add(md5);
        }
        materialDTO.setPicEncrypt(md5);
        materialDTO.setProviderId("0");
        materialDTO.setProviderName("system");
        materialDTO.setProviderType(IScenesMaterialProofConstant.ProviderTypeEnum.PLAT_OPERATOR);
        paramDTO.setMaterialDTOList(Collections.singletonList(materialDTO));
        fileCenterIntegration.saveScenesMaterialProofV2(paramDTO);
        return resultVO;
    }
}
