package com.cfpamf.ms.mallorder.integration.facade.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value="DbcDistributionSaleUserVO对象", description="分销用户表")
public class DbcDistributionSaleUserVO {
    @ApiModelProperty(value = "机构ID")
    private String orgId;
    @ApiModelProperty(value = "机构名称")
    private String orgName;
    @ApiModelProperty(value = "客户编号")
    private String custCode;
    @ApiModelProperty(value = "员工id")
    private String employeeId;
    @ApiModelProperty(value = "直接上级员工id")
    private String managerEmployeeId;
    @ApiModelProperty(value = "岗位id")
    private String jobPositionId;
    @ApiModelProperty(value = "客户姓名")
    private String custName;
    @ApiModelProperty(value = "用户手机号")
    private Long custPhoneNum;
    @ApiModelProperty(value = "身份证号码")
    private String identityNum;
    @ApiModelProperty(value = "职业类型")
    private Integer occupationType;
    @ApiModelProperty(value = "客户状态(0:注销，1:正常)")
    private Integer custStatus;
    @ApiModelProperty(value = "自身推荐码")
    private String myReferenceCode;
    @ApiModelProperty(value = "分销层级")
    private Integer distributionLevel;
    @ApiModelProperty(value = "推荐人编号")
    private String referenceCode;
    @ApiModelProperty(value = "推荐人姓名")
    private String referenceName;
    @ApiModelProperty(value = "自提点地址")
    private String pickUpAddress;
    @ApiModelProperty(value = "定位地址")
    private String locationAddress;
    @ApiModelProperty(value = "是否为内部员工（0否，1是）")
    private Integer isInnerStaff;
    @ApiModelProperty(value = "是否是推广大使（0否，1是）")
    private Integer isPromotionAmbassador;
    @ApiModelProperty(value = "是否小鲸宣传员（0否，1是）")
    private Integer isPropagandist;
    @ApiModelProperty(value = "农服土地托管站长（0否，1是）")
    private Integer isAgricHost;
    @ApiModelProperty(value = "是否资源型村代（0否，1是）")
    private Integer isResourceAgency;
    @ApiModelProperty(value = "是否亲属村代（0否，1是）")
    private Integer isRelativeAgency;
    @ApiModelProperty(value = "来源渠道")
    private Integer channel;
    @ApiModelProperty(value = "是否同意注册服务协议.1->是，0->否")
    private Integer isRegistrationAgreement;
    @ApiModelProperty(value = "是否分支负责人")
    private Integer isBranchDirector;
    @ApiModelProperty(value = "业务分销本月分支负责人已获取佣金总和")
    private BigDecimal branchDirectorCommission;
    @ApiModelProperty(value = "是否最后一个分支负责人")
    private Boolean isLastBranchDirector = Boolean.FALSE;
    @ApiModelProperty(value = "分支负责人业务分销订单所属客户经理名称")
    private String managerName;
    @ApiModelProperty(value = "分支负责人业务分销订单所属客户经理工号")
    private String managerJobNum;
    @ApiModelProperty(value = "分支负责人业务分销订单所属客户经理身份证号")
    private String managerIdNum;
    @ApiModelProperty(value = "钱包id")
    private String walletAccountId;
    @ApiModelProperty(value = "用户类型:1普通、2平台虚拟、3亲属虚拟")
    private Integer userType;

}
