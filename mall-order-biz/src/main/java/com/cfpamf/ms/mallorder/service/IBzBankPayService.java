package com.cfpamf.ms.mallorder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.ms.mallorder.po.BzBankPayPO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.common.mq.msg.PayResultMessage;
import com.cfpamf.ms.trade.facade.message.TradeRefundMessage;
import com.cfpamf.ms.mallorder.req.PayV2Request;

import java.text.ParseException;

/**
 * <p>
 * 转账记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-17
 */
public interface IBzBankPayService extends IService<BzBankPayPO> {

    /**
     * @param
     * @return boolean
     * @description : 自动转账付款
     */
    boolean orderAutoPayJob() throws ParseException;

    /**
     * @param bzBankPayPO
     * @return void
     * @description : 保存转账记录
     */
    void saveBankPay(BzBankPayPO bzBankPayPO);

    /**
     * @param
     * @return boolean
     * @description : 自动转账付款补偿
     */
    boolean orderAutoPayMakeUpJob() throws ParseException;

    /**
     * @param msg
     * @return void
     * @description ：更新交易状态
     */
    boolean updateTradeResult(PayResultMessage msg);

    /**
     * @param msg
     * @return void
     * @description : 处理退汇状态
     */
    boolean updateTradeRefund(TradeRefundMessage msg);

    /**
     * @param bzBankPayPO
     * @param payV2Request
     * @return void
     * @description :执行付款
     */
    void dealPay(BzBankPayPO bzBankPayPO, PayV2Request payV2Request);


    /**
     * 同步银行卡汇款状态
     * @return
     */
    boolean executeOrderAutoPaySyncJob();
}
