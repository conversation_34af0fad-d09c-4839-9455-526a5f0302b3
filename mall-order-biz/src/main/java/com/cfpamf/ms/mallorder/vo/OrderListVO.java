package com.cfpamf.ms.mallorder.vo;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.mallorder.common.enums.OrderCreateChannel;
import com.cfpamf.ms.mallorder.common.enums.OrderPatternEnum;
import com.cfpamf.ms.mallorder.common.enums.OrderStatusEnum;
import com.cfpamf.ms.mallorder.constant.DomainUrlConstant;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.po.OrderExtendPO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallshop.enums.StoreJindieStockFlagEnum;
import com.slodon.bbc.core.i18n.Language;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 */
@Data
@Slf4j
public class OrderListVO {

    @ApiModelProperty("订单id")
    private Integer orderId;

    @ApiModelProperty("订单号")
    private String orderSn;

    @ApiModelProperty("商家名称")
    private String storeName;

    @ApiModelProperty("订单创建时间")
    private Date createTime;

    @ApiModelProperty("买家name")
    private String memberName;

    @ApiModelProperty("订单总金额(用户需要支付的金额)，等于商品总金额＋运费-活动优惠金额总额activity_discount_amount")
    private BigDecimal orderAmount;

    @ApiModelProperty("是否免运费，1-免运费，0-有运费")
    private Integer isFreeShipping;

    @ApiModelProperty("物流费用")
    private BigDecimal expressFee;

    @ApiModelProperty("支付方式名称，参考OrderPaymentConst类")
    private String paymentName;

    @ApiModelProperty("支付方式code, 参考OrderPaymentConst类")
    private String paymentCode;

    @ApiModelProperty("订单状态：0-已取消；10-未付款订单；20-已付款；30-已发货；40-已完成;50-已关闭")
    private Integer orderState;

    @ApiModelProperty("订单状态值：0-已取消；10-未付款订单；20-已付款；30-已发货；40-已完成;50-已关闭")
    private String orderStateValue;

    @ApiModelProperty("收货人")
    private String receiverName;

    @ApiModelProperty("省市区组合")
    private String receiverAreaInfo;

    @ApiModelProperty("收货人详细地址")
    private String receiverAddress;

    @ApiModelProperty("收货省")
    private String receiverProvince;

    @ApiModelProperty("收货市")
    private String receiverCity;

    @ApiModelProperty("收货区")
    private String receiverDistrict;

    @ApiModelProperty("收货县/镇")
    private String receiverTown;

    @ApiModelProperty("收货人手机号")
    private String receiverMobile;

    @ApiModelProperty("锁定状态：0-是正常, 大于0是锁定状态，用户申请退款或退货时锁定状态加1，处理完毕减1。锁定后不能操作订单")
    private Integer lockState;

    @ApiModelProperty("订单类型：1-普通订单；其他直接存活动类型（具体类型查看ActivityConst）")
    private Integer orderType;

    @ApiModelProperty("订单类型：1-普通订单；其他直接存活动类型（具体类型查看ActivityConst）")
    private String orderTypeValue;

    @ApiModelProperty(value = "订单模式：1-C端店铺街，2-B端采购中心")
    private Integer orderPattern;

    @ApiModelProperty(value = "订单模式：1-C端店铺街，2-B端采购中心")
    private String orderPatternValue;

    @ApiModelProperty("订单子状态：101-待付定金；102-待付尾款；103-已付全款")
    private Integer orderSubState;

    @ApiModelProperty("是否显示发货按钮")
    private Boolean isShowDeliverButton;

    @ApiModelProperty(value = "是否可以发货：0-否，1-是")
    private Integer isDelivery;

    @ApiModelProperty(value = "退款状态：1-退款中")
    private Integer orderReturnState;

    @ApiModelProperty(value = "退款状态值：1-退款中")
    private Integer orderReturnStateValue;

    @ApiModelProperty("订单货品列表")
    private List<OrderProductListVO> orderProductListVOList;

    @ApiModelProperty("活动优惠总金额 （= 店铺优惠券 + 平台优惠券 + 活动优惠【店铺活动 + 平台活动】 + 积分抵扣金额）")
    private BigDecimal activityDiscountAmount;

    @ApiModelProperty(value = "金融规则编号")
    private String financeRuleCode;

    @ApiModelProperty("金融规则标签")
    private String ruleTag;

    @ApiModelProperty("自动取消时间")
    private Date autoCancelTime;

    @ApiModelProperty(value = "下单渠道：H5-浏览器H5，APP-乡助APP，WE_CHAT-微信浏览器，MINI_PRO-小程序，OMS-运管物资")
    private String channel;

    @ApiModelProperty("下单渠道描述，详见 channel 字段")
    private String channelDesc;

    @ApiModelProperty("组合支付方式")
    private String composePayName;

    @ApiModelProperty("乡助卡优惠金额")
    private BigDecimal xzCardAmount;

    @ApiModelProperty("乡助卡运费优惠金额")
    private BigDecimal xzCardExpressFeeAmount;

    @ApiModelProperty("履约模式以逗号分割，0-常规，1-供应商，2-服务到家")
    private List<Integer> performanceModeList;

    @ApiModelProperty("金蝶库存推送方式")
    private Integer kingdeeStockPushMode;

    @ApiModelProperty("金蝶库存推送方式名称")
    private String kingdeeStockPushModeValue;


    public OrderListVO(OrderPO orderPO, OrderExtendPO orderExtendPO) {
        orderId = orderPO.getOrderId();
        orderSn = orderPO.getOrderSn();
        storeName = orderPO.getStoreName();
        createTime = orderPO.getCreateTime();
        memberName = orderPO.getMemberName();
        orderAmount = orderPO.getOrderAmount();
        isFreeShipping = orderPO.getExpressFee().compareTo(BigDecimal.ZERO) <= 0 ? 1 : 0;
        expressFee = orderPO.getExpressFee();
        paymentName = orderPO.getPaymentName();
        paymentCode = orderPO.getPaymentCode();
        orderState = orderPO.getOrderState();
        orderStateValue = getRealOrderStateValue(orderState);
        receiverName = orderExtendPO.getReceiverName();
        receiverAddress = orderExtendPO.getReceiverAreaInfo() + orderExtendPO.getReceiverAddress();
        receiverAreaInfo = orderExtendPO.getReceiverAreaInfo();
        receiverAddress = orderExtendPO.getReceiverAddress();
        receiverProvince = orderExtendPO.getReceiverProvinceCode();
        receiverCity = orderExtendPO.getReceiverCityCode();
        receiverDistrict = orderExtendPO.getReceiverDistrictCode();
        receiverTown = orderExtendPO.getReceiverTownCode();
        receiverMobile = dealMemberMobile(orderExtendPO.getReceiverMobile());
        lockState = orderPO.getLockState();
        orderType = orderPO.getOrderType();
        orderPattern = orderPO.getOrderPattern();
        orderPatternValue = OrderPatternEnum.valueOf(orderPattern).getDesc();
        isShowDeliverButton = (orderPO.getOrderState() == OrderConst.ORDER_STATE_20 || OrderStatusEnum.PART_DELIVERED.getValue().equals(orderPO.getOrderState()));
        financeRuleCode = orderPO.getFinanceRuleCode();
        ruleTag = orderPO.getRuleTag();
        channel = orderPO.getChannel();
        composePayName = orderPO.getComposePayName();
        xzCardAmount = orderPO.getXzCardAmount();
        xzCardExpressFeeAmount = orderPO.getXzCardExpressFeeAmount();
        kingdeeStockPushMode = orderExtendPO.getKingdeeStockPushMode();
        try {
            performanceModeList = JSONObject.parseArray(orderPO.getPerformanceModes(), Integer.class);
        } catch (Exception e) {
            log.error("【获取订单履约模式异常，orderSn={}】,exception= {}", orderPO.getOrderSn(), e.getMessage());
        }
    }

    public String getKingdeeStockPushModeValue() {
        if(kingdeeStockPushMode == null) {
            return null;
        }
        if(StoreJindieStockFlagEnum.getEnum(kingdeeStockPushMode) == null) {
            return null;
        }
        return StoreJindieStockFlagEnum.getEnum(kingdeeStockPushMode).getDesc();
    }

    public static String getRealOrderStateValue(Integer orderState) {
        if (StringUtils.isEmpty(orderState)) {
            return Language.translate("未知");
        }
        String value = OrderStatusEnum.valueOf(orderState).getDesc();
        //翻译
        value = Language.translate(value);
        return value;
    }

    /**
     * 处理会员手机号，隐藏中间四位
     *
     * @return
     */
    private static String dealMemberMobile(String memberMobile) {
        if (StringUtils.isEmpty(memberMobile)) {
            return memberMobile;
        }
        List<String> hiddenHosts = Arrays.asList("https://jbbcadmin.slodon.cn", "http://jbbcs-admin.slodon.cn");
        if (hiddenHosts.contains(DomainUrlConstant.SLD_ADMIN_URL)) {
            return memberMobile.replaceFirst("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
        }
        return memberMobile;
    }

    public String getChannelDesc() {
        return OrderCreateChannel.getDescByValue(this.channel);
    }

    public String getOrderReturnStateValue() {
        if (Objects.isNull(this.orderReturnState)) {
            return "";
        }
        if (this.orderReturnState == 1) {
            return "退款中";
        }
        return "";
    }
}
