package com.cfpamf.ms.mallorder.controller.seller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cfpamf.ms.mallorder.common.aspect.ThreadLocalRemoveTag;
import com.cfpamf.ms.mallorder.common.enums.OrderCreateChannel;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.*;
import com.cfpamf.ms.mallorder.integration.facade.dto.SiteVo;
import com.cfpamf.ms.mallorder.po.OrderOfflinePO;
import com.cfpamf.ms.mallorder.service.IOrderPlacingService;
import com.cfpamf.ms.mallorder.service.IPerformanceService;
import com.cfpamf.ms.mallorder.service.OrderOfflineService;
import com.cfpamf.ms.mallorder.service.PrivilegeService;
import com.cfpamf.ms.mallorder.vo.CustInfoVo;
import com.cfpamf.ms.mallorder.vo.ExportingFailedDataToExcelVO;
import com.cfpamf.ms.mallorder.vo.OrderSubmitVO;
import com.cfpamf.ms.mallorder.vo.kingdee.KingdeeCustomerVo;
import com.cfpamf.ms.mallshop.resp.Vendor;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import com.slodon.bbc.core.util.UserUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * seller-线下订单操作controller
 */
@Api(tags = "seller-线下订单操作")
@Slf4j
@RestController
@RequestMapping("/seller/OrderOffline")
public class SellerOrderOfflineController {

	@Autowired
	private OrderOfflineService orderOfflineService;

	@Autowired
	private IOrderPlacingService orderPlacingService;

	@Autowired
	private HttpServletRequest request;

	@Autowired
	private PrivilegeService privilegeService;

	@Resource
	private IPerformanceService performanceService;

	@GetMapping("/privilege")
	@ApiOperation("权限")
	public JsonResult<Boolean> privilege(Integer storeId) {
		BizAssertUtil.isTrue(!privilegeService.getSellerPrivilegeInfo(storeId).isOrderOfflinePrivilege(),
				String.format("抱歉，当前店铺编号【%s】铺，暂无线下补录订单操作权限，请联系技术小哥哥，赋予权限！", storeId));
		return SldResponse.success(true);
	}

	@ApiOperation("根据工号查管护分支")
	@GetMapping("/queryCustInfo")
	public JsonResult<CustInfoVo> queryCustInfo(String userCode) {

		return SldResponse.success(orderOfflineService.queryCustInfo(userCode));
	}

	/**
	 * 渠道订单 外部接口调用、无购物车
	 *
	 * @param dto
	 * @return
	 */
	@PostMapping("/submit")
	@ApiOperation("线下订单提交接口 200-下单成功")
	@ThreadLocalRemoveTag
	public JsonResult<OrderSubmitVO> submitOfflineOrder(@Valid @RequestBody OrderOfflineParamDTO dto) {
		log.info("【submitOfflineOrder】storeId：{}", dto.getStoreId());
		// 权限
		BizAssertUtil.isTrue(!privilegeService.getSellerPrivilegeInfo(dto.getStoreId()).isOrderOfflinePrivilege(),
				String.format("抱歉，当前店铺编号【%s】，暂无线下补录订单操作权限，请联系技术小哥哥，赋予权限！", dto.getStoreId()));
		OrderSubmitVO vo = null;
		// 创建订单
		try {
			vo = orderPlacingService.createOfflineOrder(dto);
		} catch (BusinessException e) {
			if(e.getCode() == OrderConst.ORDER_OFFLINE_AGRIC_FEE_CODE) {
				return SldResponse.fail(OrderConst.ORDER_OFFLINE_AGRIC_FEE_CODE, e.getMessage());
			}
			throw e;
		}
		return SldResponse.success(vo);
	}

	/**
	 * 线下单导入
	 * @return
	 */
	@PostMapping("/import")
	@ApiOperation("线下订单导入接口 200-下单成功")
	public JsonResult<ExportingFailedDataToExcelVO> importOfflineOrder(HttpServletRequest request, @RequestPart("excelFile") MultipartFile excelFile) throws Exception {
		log.info("批量导入线下单开始");
		Vendor vendor = UserUtil.getUser(request, Vendor.class);
		return SldResponse.success(orderPlacingService.importOfflineOrder(vendor, excelFile));
	}

	@ApiOperation("手动结算接口")
	@PostMapping("/manual/settlement")
	public JsonResult<Void> manualSettlement(
			@RequestBody @NotNull @Valid OrderOfflineManualSettlementDTO orderOfflineManualSettlement) {
		Vendor vendor = UserUtil.getUser(request, Vendor.class);
		orderOfflineService.manualSettlementV2(vendor, orderOfflineManualSettlement);
		return SldResponse.success();
	}

	@ApiOperation("获取补录弹框信息接口")
	@GetMapping("/manual/info")
	public JsonResult<OrderOfflineManualDTO> getManualInfo(String orderSn) {
		return SldResponse.success(orderOfflineService.getManualInfo(orderSn));
	}

	@ApiOperation("钉钉通知财务")
	@GetMapping("/manual/notifyByDingTalk")
	public JsonResult<Boolean> notifyByDingTalk(@RequestParam("orderSn")  String orderSn) {
		return SldResponse.success(orderOfflineService.notifyByDingTalk(orderSn));
	}


	@ApiOperation("手动支付")
	@PostMapping("/manual/pay")
	@Deprecated
	public JsonResult<Void> manualPay(@RequestBody @Valid OrderOfflineManualDTO orderOfflineManualDTO) {
		Vendor vendor = UserUtil.getUser(request, Vendor.class);
		orderOfflineService.manualPay(orderOfflineManualDTO, vendor);
		return SldResponse.success();
	}

	@ApiOperation("手动发货")
	@PostMapping("/manual/delivery")
	public JsonResult<Void> manualDelivery(@RequestBody @Valid OrderOfflineManualDTO orderOfflineManualDTO) {
		Vendor vendor = UserUtil.getUser(request, Vendor.class);
		orderOfflineService.manualDelivery(orderOfflineManualDTO,  vendor);
		return SldResponse.success();
	}

	@ApiOperation("手动签收")
	@PostMapping("/manual/receive")
	public JsonResult<Void> manualReceive(@RequestBody @Valid OrderOfflineManualDTO orderOfflineManualDTO) {
		Vendor vendor = UserUtil.getUser(request, Vendor.class);
		orderOfflineService.manualReceive(orderOfflineManualDTO,  vendor);
		return SldResponse.success();
	}


	@GetMapping("/manual/getReceiveCode")
	@ApiOperation(value = "获取签收码")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "orderSn", value = "订单号", required = true, paramType = "query"),
			@ApiImplicitParam(name = "channel", value = "下单渠道：H5-浏览器H5，APP-乡助APP，BAPP-掌上中和，WE_CHAT-微信浏览器，MINI_PRO-小程序，WEB-后台浏览器", paramType = "query")
	})
	public JsonResult<String> getReceiveCode(HttpServletRequest request, String orderSn, OrderCreateChannel channel) {
		Vendor vendor = UserUtil.getUser(request, Vendor.class);
		UserDTO userDTO = new UserDTO(vendor);
		String receiveCode = performanceService.sendSmsReceiveCodeAndInsertLog(orderSn, userDTO, channel);
		JsonResult<String> jsonResult = new JsonResult<>(200,"成功");
		jsonResult.setData(receiveCode);
		return jsonResult;
	}

	@ApiOperation("资料补充")
	@PostMapping("/info/supplement")
	@Deprecated
	public JsonResult<Void> infoSupplement(@RequestBody @Valid OrderOfflineManualDTO orderOfflineManualDTO) {
		Vendor vendor = UserUtil.getUser(request, Vendor.class);
		orderOfflineService.infoSupplement(orderOfflineManualDTO,  vendor);
		return SldResponse.success();
	}

	@ApiOperation("线下订单额外信息查询")
	@GetMapping("/query/detail")
	public JsonResult<List<OrderOfflinePO>> queryByPaySn(String paySn) {
		List<OrderOfflinePO> data = orderOfflineService.queryOrderOfflineList(paySn);
		return SldResponse.success(data);
	}

	@ApiOperation(value = "批量设置开票标签")
	@PostMapping("batchSaveInvoiceLabel")
	public JsonResult<Boolean> batchSaveInvoiceLabel (HttpServletRequest request, @RequestBody @Valid OrderOfflineInvoiceLabelDTO orderOfflineInvoiceLabelDTO) {
		Vendor vendor = UserUtil.getUser(request, Vendor.class);
		orderOfflineService.batchSaveInvoiceLabel(orderOfflineInvoiceLabelDTO, vendor);
		return SldResponse.success(Boolean.TRUE);
	}

	@ApiOperation(value = "批量设置账期天数")
	@PostMapping("batchSaveAccountPeriodDays")
	public JsonResult<Boolean> batchSaveAccountPeriodDays (HttpServletRequest request, @RequestBody @Valid OrderOfflineAccountPeriodDaysDTO orderOfflineInvoiceLabelDTO) {
		Vendor vendor = UserUtil.getUser(request, Vendor.class);
		orderOfflineService.batchSaveAccountPeriodDays(orderOfflineInvoiceLabelDTO, vendor);
		return SldResponse.success(Boolean.TRUE);
	}

	@ApiOperation(value = "批量设置买方供应商编码")
	@PostMapping("batchSaveSupplierCode")
	public JsonResult<Boolean> batchSaveSupplierCode (HttpServletRequest request, @RequestBody @Valid OrderOfflineSupplierCodeDTO orderOfflineInvoiceLabelDTO) {
		Vendor vendor = UserUtil.getUser(request, Vendor.class);
		orderOfflineService.batchSaveSupplierCode(orderOfflineInvoiceLabelDTO, vendor);
		return SldResponse.success(Boolean.TRUE);
	}

	@ApiOperation(value = "获取WMS收货站")
	@PostMapping("getSiteByPage")
	public JsonResult<Page<SiteVo>> getSiteByPage (@RequestBody WmsSiteDTO wmsSiteDTO) {
		return SldResponse.success(orderOfflineService.getSiteByPage(wmsSiteDTO));
	}

	@ApiOperation(value = "获取分支编码")
	@PostMapping("getEmployeeBranchCode")
	public JsonResult<String> getEmployeeBranchCode (@RequestBody @Param("employeeCode") String employeeCode) {
		return SldResponse.success(orderOfflineService.getEmployeeBranchCode(employeeCode));
	}

    @ApiOperation(value = "分页获取金蝶客户信息")
	@PostMapping("getKingdeeCustomerPage")
	public JsonResult<Page<KingdeeCustomerVo>> getKindeeCustomerPage(@RequestBody KingdeeCustomerQuery query) {
		Vendor vendor = UserUtil.getUser(request, Vendor.class);
		BizAssertUtil.notNull(vendor.getStoreId(), "商户信息获取为空，请登录");
		return SldResponse.success(orderOfflineService.getKingdeeCustomerPage(query, vendor.getStoreId()));
	}

}
