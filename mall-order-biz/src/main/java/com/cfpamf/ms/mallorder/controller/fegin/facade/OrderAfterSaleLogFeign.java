package com.cfpamf.ms.mallorder.controller.fegin.facade;

import java.util.List;
import javax.annotation.Resource;

import com.cfpamf.ms.mallorder.model.OrderAfterSaleLogModel;
import com.cfpamf.ms.mallorder.po.OrderAfterSaleLogPO;
import com.cfpamf.ms.mallorder.request.OrderAfterSaleLogExample;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@RestController
@Slf4j
public class OrderAfterSaleLogFeign     {



    @Resource
    private OrderAfterSaleLogModel orderAfterSaleLogModel;

    /**
     * 根据logId获取售后服务操作日志表详情
     *
     * @param logId logId
     * @return
     */
    @GetMapping("/v1/feign/business/orderAfterSaleLog/get")
    public OrderAfterSaleLogPO getOrderAfterSaleLogByLogId(@RequestParam("logId") Integer logId) {
        return orderAfterSaleLogModel.getOrderAfterSaleLogByLogId(logId);
    }

    /**
     * 获取条件获取售后服务操作日志表列表
     *
     * @param example 查询条件信息
     * @return
     */
    @PostMapping("/v1/feign/business/orderAfterSaleLog/getList")
    public List<OrderAfterSaleLogPO> getOrderAfterSaleLogList(@RequestBody OrderAfterSaleLogExample example) {
        return orderAfterSaleLogModel.getOrderAfterSaleLogList(example, example.getPager());
    }

    /**
     * 新增售后服务操作日志表
     *
     * @param orderAfterSaleLogPO
     * @return
     */
    @PostMapping("/v1/feign/business/orderAfterSaleLog/addOrderAfterSaleLog")
    public Integer saveOrderAfterSaleLog(@RequestBody OrderAfterSaleLogPO orderAfterSaleLogPO) {
        return orderAfterSaleLogModel.saveOrderAfterSaleLog(orderAfterSaleLogPO);
    }

    /**
     * 根据logId更新售后服务操作日志表
     *
     * @param orderAfterSaleLogPO
     * @return
     */
    @PostMapping("/v1/feign/business/orderAfterSaleLog/updateOrderAfterSaleLog")
    public Integer updateOrderAfterSaleLog(@RequestBody OrderAfterSaleLogPO orderAfterSaleLogPO) {
        return orderAfterSaleLogModel.updateOrderAfterSaleLog(orderAfterSaleLogPO);
    }

    /**
     * 根据logId删除售后服务操作日志表
     *
     * @param logId logId
     * @return
     */
    @PostMapping( "/v1/feign/business/orderAfterSaleLog/deleteOrderAfterSaleLog")
    public Integer deleteOrderAfterSaleLog(@RequestParam("logId") Integer logId) {
        return orderAfterSaleLogModel.deleteOrderAfterSaleLog(logId);
    }
}