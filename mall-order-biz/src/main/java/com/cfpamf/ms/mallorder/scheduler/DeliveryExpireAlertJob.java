package com.cfpamf.ms.mallorder.scheduler;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.mallorder.common.config.FundsBorrowDayConfig;
import com.cfpamf.ms.mallorder.common.enums.OrderProductDeliveryEnum;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.mapper.OrderProductMapper;
import com.cfpamf.ms.mallorder.request.OrderProductExample;
import com.cfpamf.ms.mallorder.service.IFundsBorrowBizService;
import com.cfpamf.ms.mallorder.vo.OrderProductDeliveryVO;
import com.cfpamf.ms.mallshop.api.StoreExpenseFeignClient;
import com.slodon.bbc.core.response.PagerInfo;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 银行卡转账汇款记录对账任务
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-24
 */
@Slf4j
@Component
public class DeliveryExpireAlertJob {

    public static final Integer PAGE_QUERY_SIZE = 50;

    @Autowired
    private FundsBorrowDayConfig fundsBorrowDayConfig;
    @Autowired
    private IFundsBorrowBizService fundsBorrowBizService;
    @Autowired
    private OrderProductMapper orderProductMapper;
    @Autowired
    private StoreExpenseFeignClient storeExpenseFeignClient;

    public static final String DATE_FORMATTER_PATTERN = "yyyyMMdd";

    @XxlJob("fundsBorrowDeliveryExpireAlert")
    public ReturnT<String> fundsBorrowDeliveryExpireAlert() {
        String param = XxlJobHelper.getJobParam();
        LocalDate thatDay = LocalDate.now();
        if (StringUtils.isNotBlank(param)) {
            try {
                thatDay = LocalDate.parse(param, DateTimeFormatter.ofPattern(DATE_FORMATTER_PATTERN));
            } catch (Exception ex) {
                log.info("入参格式非[yyyyMMdd],将默认为今天");
                thatDay = LocalDate.now();
            }
        }
        log.info("=============== START JOB fundsBorrowDeliveryExpireAlert , DATE :{}", LocalDateTime.now());
        log.info("执行现款现货逾期发货提醒Job,提醒日:{}", thatDay);
        Date alertBefore = Date.from(thatDay.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date alertAfter = Date.from(thatDay.minusDays(fundsBorrowDayConfig.getCloseStoreDays()).atStartOfDay(ZoneId.systemDefault()).toInstant());
        Result<Integer> deliveryResult = this.fundsBorrowExpireAlert(alertAfter, alertBefore, OrderConst.FUNDS_BORROW_STORE_DELIVERY_EXPIRE_ALERT);
        if (!deliveryResult.isSuccess()) {
            log.info("=============== FAILURE JOB fundsBorrowDeliveryExpireAlert , DATE : {}，MESSAGE: {}",
                    LocalDateTime.now(), deliveryResult.getMessage());
            return ReturnT.FAIL;
        }
        log.info("=============== FINISHED JOB fundsBorrowDeliveryExpireAlert , DATE : {}，SUCCESS: {}",
                LocalDateTime.now(), deliveryResult.getData());
        return ReturnT.SUCCESS;
    }

    @XxlJob("fundsBorrowStoreCloseAlert")
    public ReturnT<String> fundsBorrowStoreCloseAlert() {
        String param = XxlJobHelper.getJobParam();
        LocalDate thatDay = LocalDate.now();
        if (StringUtils.isNotBlank(param)) {
            try {
                thatDay = LocalDate.parse(param, DateTimeFormatter.ofPattern(DATE_FORMATTER_PATTERN));
            } catch (Exception ex) {
                log.info("入参格式非[yyyyMMdd],将默认为今天");
                thatDay = LocalDate.now();
            }
        }
        log.info("=============== START JOB fundsBorrowStoreCloseAlert , DATE :{}", LocalDateTime.now());
        thatDay = thatDay.minusDays(fundsBorrowDayConfig.getCloseStoreDays());
        Date alertAfter = Date.from(thatDay.atStartOfDay(ZoneId.systemDefault()).toInstant());
        thatDay = thatDay.plusDays(fundsBorrowDayConfig.getPreAlertDays());
        log.info("执行现款现货关闭店铺下单权限提醒Job,提醒日:{}", thatDay);
        Date alertBefore = Date.from(thatDay.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Result<Integer> deliveryResult = this.fundsBorrowExpireAlert(alertAfter, alertBefore, OrderConst.FUNDS_BORROW_STORE_CLOSE_ALERT);
        if (!deliveryResult.isSuccess()) {
            log.info("=============== FAILURE JOB fundsBorrowStoreCloseAlert , DATE : {}，MESSAGE: {}",
                    LocalDateTime.now(), deliveryResult.getMessage());
            return ReturnT.FAIL;
        }
        log.info("=============== FINISHED JOB fundsBorrowStoreCloseAlert , DATE : {}，SUCCESS: {}",
                LocalDateTime.now(), deliveryResult.getData());
        return ReturnT.SUCCESS;
    }

    @XxlJob("fundsBorrowCloseStoreTrade")
    public ReturnT<String> fundsBorrowCloseStoreTrade() {
        String param = XxlJobHelper.getJobParam();
        LocalDate thatDay = LocalDate.now();
        if (StringUtils.isNotBlank(param)) {
            try {
                thatDay = LocalDate.parse(param, DateTimeFormatter.ofPattern(DATE_FORMATTER_PATTERN));
            } catch (Exception ex) {
                log.info("入参格式非[yyyyMMdd],将默认为今天");
                thatDay = LocalDate.now();
            }
        }
        log.info("=============== START JOB fundsBorrowCloseStoreTrade , DATE :{}", LocalDateTime.now());
        thatDay = thatDay.minusDays(fundsBorrowDayConfig.getCloseStoreDays());
        log.info("执行现款现货关闭店铺下单权限Job,关闭:{}", thatDay);
        Date closeBeforeDay = Date.from(thatDay.atStartOfDay(ZoneId.systemDefault()).toInstant());
        thatDay = thatDay.minusDays(1L);
        Date closeAfterDay = Date.from(thatDay.atStartOfDay(ZoneId.systemDefault()).toInstant());
        OrderProductExample orderProductExample = new OrderProductExample();
        orderProductExample.setDeliveryDeadlineAfter(closeAfterDay);
        orderProductExample.setDeliveryDeadlineBefore(closeBeforeDay);
        orderProductExample.setDeliveryStateNotEquals(OrderProductDeliveryEnum.DELIVERED.getValue());
        List<OrderProductDeliveryVO> orderProductPOList = orderProductMapper.listProductDeliveryInfo(orderProductExample);
        Result<Integer> closeResult = fundsBorrowBizService.fundsBorrowCloseStoreTrade(orderProductPOList);
        if (!closeResult.isSuccess()) {
            log.info("=============== FAILURE JOB fundsBorrowCloseStoreTrade , DATE : {}，MESSAGE: {}",
                    LocalDateTime.now(), closeResult.getMessage());
            return ReturnT.FAIL;
        }
        log.info("=============== FINISHED JOB fundsBorrowCloseStoreTrade , DATE : {}，SUCCESS: {}",
                LocalDateTime.now(), closeResult.getData());
        return ReturnT.SUCCESS;
    }

    private Result<Integer> fundsBorrowExpireAlert(Date alertAfter, Date alertBefore, String alertType) {
        Result<Integer> result = new Result<>();
        OrderProductExample orderProductExample = new OrderProductExample();
        orderProductExample.setDeliveryDeadlineAfter(alertAfter);
        orderProductExample.setDeliveryDeadlineBefore(alertBefore);
        orderProductExample.setDeliveryState(OrderProductDeliveryEnum.WAIT_DELIVERY.getValue());
        orderProductExample.setLimit(PAGE_QUERY_SIZE);
        Long lastOrderProductId = 0L;
        List<OrderProductDeliveryVO> orderProductPOList;
        int count = 0;
        do {
            if (!lastOrderProductId.equals(0L)) {
                orderProductExample.setOrderProductIdLessThan(lastOrderProductId);
            }
            orderProductPOList = orderProductMapper.listProductDeliveryInfo(orderProductExample);
            if (CollectionUtils.isEmpty(orderProductPOList)) {
                lastOrderProductId = 0L;
                continue;
            }
            Optional<Long> optional = orderProductPOList.stream().map(OrderProductDeliveryVO::getOrderProductId).min(Long::compareTo);
            if (!optional.isPresent()) {
                lastOrderProductId = 0L;
                continue;
            }
            lastOrderProductId = optional.get();
            Result<Integer> alertResult = fundsBorrowBizService.fundsBorrowExpireAlert(orderProductPOList, alertType);
            if (alertResult.isSuccess()) {
                count += alertResult.getData();
            }
        } while (lastOrderProductId > 0);
        result.setSuccess(true);
        result.setData(count);
        return result;
    }
}
