package com.cfpamf.ms.mallorder.controller.fegin.facade;

import com.cfpamf.ms.mallorder.request.OrderOfflineRequest;
import com.cfpamf.ms.mallorder.service.OrderOfflineService;
import com.cfpamf.ms.mallorder.vo.OrderOfflineInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Slf4j
public class OrderOfflineFeign {


    @Resource
    private OrderOfflineService orderOfflineService;

    /**
     * 更新条件获取线下补录收款信息
     *
     * @param request 查询条件信息
     * @return
     */
    @PostMapping("/v1/feign/business/orderOffline/getReceiptInfoList")
    public List<OrderOfflineInfoVO> getReceiptInfoList(@RequestBody OrderOfflineRequest request) {
        return orderOfflineService.getReceiptInfoList(request);
    }

}
