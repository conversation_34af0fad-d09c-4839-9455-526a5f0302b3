package com.cfpamf.ms.mallorder.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.cfpamf.ms.mallorder.common.enums.InvoiceStatusEnum;
import com.cfpamf.ms.mallorder.common.enums.OrderPatternEnum;
import com.cfpamf.ms.mallorder.common.enums.PaymentTagEnum;
import com.cfpamf.ms.mallorder.enums.OrderCreateChannel;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 线下单导入接受参数
 * <AUTHOR>
 */
@Data
@Component
@Validated
public class OrderOfflineImportDTO implements Serializable {
	private static final long serialVersionUID = 683011422865812319L;

	@ApiModelProperty(value = "创建时间")
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@ExcelProperty(value = "创建时间",index = 0)
	private Date createTime;

	@NotBlank(message = "订单类型不能为空")
	@ApiModelProperty(value = "订单类型：5预占订单（传入值：ORDER_TYPE_5），6线下订单（传入值：ORDER_TYPE_6）", required = true)
	@ExcelProperty(value = "订单类型",index = 1)
	private String orderType;

	@ApiModelProperty(value = "订单来源：1、C端订单（默认值），2、采购订单，6-自提订单")
	@ExcelProperty(value = "订单来源",index = 2)
	@NotBlank(message = "订单来源不能为空")
	private String orderPattern;

	@ApiModelProperty("用户手机号")
	@NotBlank(message = "用户手机号不能为空")
	@ExcelProperty(value = "用户手机号",index = 3)
	private String userMobile;

	@NotBlank(message = "客户经理工号，不能为空")
	@ApiModelProperty(name = "客户经理工号", required = true)
	@ExcelProperty(value = "客户经理工号",index = 4)
	private String managerCode;

	@ApiModelProperty(value = "省", example = "湖南省")
	@ExcelProperty(value = "省",index = 5)
	@NotBlank(message = "省不能为空")
	private String province;

	@ApiModelProperty(value = "市", example = "长沙市")
	@ExcelProperty(value = "市",index = 6)
	@NotBlank(message = "市不能为空")
	private String city;

	@ApiModelProperty(value = "区", example = "岳麓区")
	@ExcelProperty(value = "区",index = 7)
	@NotBlank(message = "区不能为空")
	private String district;

	@ApiModelProperty(value = "县/街道", example = "麓谷街道")
	@ExcelProperty(value = "县/街道",index = 8)
	@NotBlank(message = "县/街道不能为空")
	private String town;

	@ApiModelProperty(value = "详细地址", example = "芯城科技园110栋 120楼")
	@ExcelProperty(value = "详细地址",index = 9)
	@NotBlank(message = "详细地址不能为空")
	private String detailAddress;

	@NotNull(message = "skuId不能为空")
	@ApiModelProperty(value = "skuId", required = true)
	@ExcelProperty(value = "商品SKU",index = 10)
	private Long skuId;

	@ApiModelProperty(value = "含税售价")
	@ExcelProperty(value = "含税售价",index = 11)
	@NotNull(message = "含税售价不能为空")
	@Min(value = 0, message = "含税售价不能小于0")
	private BigDecimal taxPrice;

	@ApiModelProperty("下单数量")
	@ExcelProperty(value = "下单数量",index = 12)
	@NotNull(message = "下单数量不能为空")
	@Min(value = 1, message = "下单数量不能小于1")
	private Integer buyNum;

	@ApiModelProperty("优惠金额")
	@ExcelProperty(value = "优惠金额",index = 13)
	private BigDecimal discountAmount;

	@ApiModelProperty(value = "收件人姓名")
	@ExcelProperty(value = "收件人姓名",index = 14)
	@NotBlank(message = "收件人姓名不能为空")
	private String receiverName;

	@ApiModelProperty(value = "收件人手机号码")
	@ExcelProperty(value = "收件人手机号码",index = 15)
	@NotBlank(message = "收件人手机号码不能为空")
	private String receiverMobile;

	@ApiModelProperty(value = "备注失败原因")
	@ExcelIgnore
	@ExcelProperty(value = "失败原因",index = 16)
	private String remark;

	public static OrderOfflineParamDTO buildOrderOfflineParam(OrderOfflineImportDTO dto) {
		if (dto == null) {
			return null;
		}
		OrderOfflineParamDTO paramDTO = new OrderOfflineParamDTO();
		paramDTO.setChannel(OrderCreateChannel.WEB.getValue());
		paramDTO.setCreateTime(dto.getCreateTime() == null ? new Date() : dto.getCreateTime());
		paramDTO.setOrderType(OrderTypeEnum.getByDesc(dto.getOrderType()));
		paramDTO.setOrderPattern(OrderPatternEnum.getByDesc(dto.getOrderPattern()).getValue());
		paramDTO.setUserMobile(dto.getUserMobile());
		paramDTO.setManager(dto.getManagerCode());
		paramDTO.setEmployeeCode(dto.getManagerCode());
		paramDTO.setSkuInfoList(buildOrderSkuInfo(dto));
		paramDTO.setAddress(buildOrderAddress(dto));
		paramDTO.setPaymentTag(PaymentTagEnum.UNPAID.getValue());
		paramDTO.setOfflineInfoDTO(buildOrderOfflineInfoDTO());
		paramDTO.setIsContinueSubmit(true);
		return paramDTO;
	}

	private static OrderOfflineInfoDTO buildOrderOfflineInfoDTO() {
		OrderOfflineInfoDTO orderOfflineInfoDTO = new OrderOfflineInfoDTO();
		orderOfflineInfoDTO.setInvoiceStatus(InvoiceStatusEnum.INVOICED.getValue());
		return orderOfflineInfoDTO;
	}

	private static List<OrderSkuInfoDTO> buildOrderSkuInfo(OrderOfflineImportDTO dto) {
		OrderSkuInfoDTO orderSkuInfoDTO = new OrderSkuInfoDTO();
		orderSkuInfoDTO.setProductId(dto.getSkuId());
		orderSkuInfoDTO.setNumber(dto.getBuyNum().intValue());
		orderSkuInfoDTO.setTaxPrice(dto.getTaxPrice());
		orderSkuInfoDTO.setProductPrice(dto.getTaxPrice());
		orderSkuInfoDTO.setProductDiscountAmount(dto.getDiscountAmount());
		return Arrays.asList(orderSkuInfoDTO);
	}
	private static OrderAddressDTO buildOrderAddress(OrderOfflineImportDTO dto) {
		OrderAddressDTO orderAddressDTO = new OrderAddressDTO();
		orderAddressDTO.setReceiverMobile(dto.getReceiverMobile());
		orderAddressDTO.setReceiverName(dto.getReceiverName());
		orderAddressDTO.setProvince(dto.getProvince());
		orderAddressDTO.setCity(dto.getCity());
		orderAddressDTO.setDistrict(dto.getDistrict());
		orderAddressDTO.setTown(dto.getTown());
		orderAddressDTO.setDetailAddress(dto.getDetailAddress());
		return orderAddressDTO;
	}

	public static OrderOfflineImportDTO copy(OrderOfflineImportDTO dto) {
		OrderOfflineImportDTO copy = new OrderOfflineImportDTO();
		copy.setCreateTime(dto.getCreateTime());
		copy.setOrderType(dto.getOrderType());
		copy.setOrderPattern(dto.getOrderPattern());
		copy.setUserMobile(dto.getUserMobile());
		copy.setManagerCode(dto.getManagerCode());
		copy.setProvince(dto.getProvince());
		copy.setCity(dto.getCity());
		copy.setDistrict(dto.getDistrict());
		copy.setTown(dto.getTown());
		copy.setDetailAddress(dto.getDetailAddress());
		copy.setSkuId(dto.getSkuId());
		copy.setTaxPrice(dto.getTaxPrice());
		copy.setBuyNum(dto.getBuyNum());
		copy.setDiscountAmount(dto.getDiscountAmount());
		copy.setReceiverName(dto.getReceiverName());
		copy.setReceiverMobile(dto.getReceiverMobile());
		copy.setRemark(dto.getRemark());
		return copy;
	}
}
