package com.cfpamf.ms.mallorder.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.dts.biz.api.channel.SkyCraneFeignClient;
import com.cfpamf.dts.biz.request.channel.skycrane.SendBackGoodsRequest;
import com.cfpamf.mallpayment.facade.api.MallPaymentFacade;
import com.cfpamf.mallpayment.facade.enums.PayWayEnum;
import com.cfpamf.mallpayment.facade.request.CDMallRefundTryRequest;
import com.cfpamf.mallpayment.facade.request.PaymentRefundRequest;
import com.cfpamf.mallpayment.facade.request.loan.CancelCdMallApplyRequest;
import com.cfpamf.mallpayment.facade.request.loan.OrderRefundRequest;
import com.cfpamf.mallpayment.facade.request.loan.ReturnCdMallOrderRequest;
import com.cfpamf.mallpayment.facade.vo.CDMallRefundTryResultVO;
import com.cfpamf.ms.bms.facade.vo.DictionaryItemVO;
import com.cfpamf.ms.customer.facade.request.user.QueryPushInfoReq;
import com.cfpamf.ms.customer.facade.request.user.QueryUserBaseInfoReq;
import com.cfpamf.ms.customer.facade.vo.CustDetailVo;
import com.cfpamf.ms.customer.facade.vo.UserInfoVo;
import com.cfpamf.ms.customer.facade.vo.user.UserBaseInfoVo;
import com.cfpamf.ms.mall.settlement.enums.BillAccountTypeEnum;
import com.cfpamf.ms.mallmember.api.MemberFeignClient;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.builder.OrderBuilder;
import com.cfpamf.ms.mallorder.common.calculate.RefundCalculator;
import com.cfpamf.ms.mallorder.common.config.ChannelFeeRateConfig;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.constant.OrdersAfsConst;
import com.cfpamf.ms.mallorder.common.enums.*;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.cfpamf.ms.mallorder.common.mq.msg.TransferRefundDetailMsg;
import com.cfpamf.ms.mallorder.common.mq.msg.TransferRefundMsg;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.common.util.DateUtil;
import com.cfpamf.ms.mallorder.constant.ExchangeOrderConst;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.controller.fegin.facade.CustomerServiceFeign;
import com.cfpamf.ms.mallorder.dto.OrderAfterDTO;
import com.cfpamf.ms.mallorder.dto.OrderPlaceUserDTO;
import com.cfpamf.ms.mallorder.dto.OrderRefundCountDTO;
import com.cfpamf.ms.mallorder.dto.UserDTO;
import com.cfpamf.ms.mallorder.enums.InterestPayerEnum;
import com.cfpamf.ms.mallorder.enums.OrderEventEnum;
import com.cfpamf.ms.mallorder.enums.OrderPerformanceModeEnum;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.integration.erp.ERPIntegration;
import com.cfpamf.ms.mallorder.integration.facade.BmsIntegration;
import com.cfpamf.ms.mallorder.integration.pay.PayIntegration;
import com.cfpamf.ms.mallorder.integration.promotion.facade.DiscountCouponFacade;
import com.cfpamf.ms.mallorder.mapper.*;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.request.*;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.service.impl.OrderCreateHelper;
import com.cfpamf.ms.mallorder.service.payment.IPayHandleService;
import com.cfpamf.ms.mallorder.v2.service.OrderPayRecordService;
import com.cfpamf.ms.mallorder.v2.service.OrderPresellService;
import com.cfpamf.ms.mallorder.v2.service.OrderRefundRecordService;
import com.cfpamf.ms.mallorder.v2.service.PresellRefundService;
import com.cfpamf.ms.mallorder.validation.OrderCancelValidation;
import com.cfpamf.ms.mallorder.validation.OrderRefundApplyValidation;
import com.cfpamf.ms.mallorder.validation.OrderReturnValidation;
import com.cfpamf.ms.mallorder.vo.AfsApplyInfoVO;
import com.cfpamf.ms.mallorder.vo.AfsProductVO;
import com.cfpamf.ms.mallorder.vo.OrderProductListVO;
import com.cfpamf.ms.mallpromotion.enums.CouponFunder;
import com.cfpamf.ms.mallshop.api.StoreFeignClient;
import com.cfpamf.ms.mallshop.api.StoreIsolateWhiteListFeignClient;
import com.cfpamf.ms.mallshop.enums.StoreStateEnum;
import com.cfpamf.ms.mallshop.enums.WhiteListEnum;
import com.cfpamf.ms.mallshop.resp.Store;
import com.cfpamf.ms.mallshop.resp.StoreContractReceiptInfoVO;
import com.cfpamf.ms.mallsystem.api.ExpressFeignClient;
import com.cfpamf.ms.mallsystem.vo.Admin;
import com.cfpamf.ms.mallsystem.vo.Express;
import com.cfpamf.smartid.client.utils.sharding.SeqEnum;
import com.cfpamf.smartid.client.utils.sharding.ShardingId;
import com.google.common.collect.Maps;
import com.slodon.bbc.core.constant.StoreTplConst;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.exception.MallException;
import com.slodon.bbc.core.response.PagerInfo;
import com.slodon.bbc.core.util.AssertUtil;
import com.slodon.bbc.core.util.StringUtil;
import com.slodon.bbc.core.util.TimeUtil;
import com.slodon.bbc.starter.mq.entity.MessageSendProperty;
import com.slodon.bbc.starter.mq.entity.MessageSendVO;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.slodon.bbc.core.constant.StarterConfigConst.MQ_EXCHANGE_NAME;
import static com.slodon.bbc.core.constant.StarterConfigConst.MQ_QUEUE_NAME_SELLER_MSG;

@Component
@Slf4j
public class OrderAfterServiceModel {

    @Resource
    private OrderAfterMapper orderAfterMapper;
    @Resource
    private OrderReturnMapper orderReturnMapper;
    @Resource
    private OrderReplacementMapper orderReplacementMapper;
    @Resource
    private OrderMapper orderMapper;
    @Resource
    private OrderProductMapper orderProductMapper;
    @Resource
    private OrderExtendMapper orderExtendMapper;
    @Resource
    private OrderAfterSaleLogMapper orderAfterSaleLogMapper;
    @Resource
    private OrderReturnTrackMapper orderReturnTrackMapper;
    @Autowired
    private ChannelFeeRateConfig channelFeeRateConfig;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private ExpressFeignClient expressFeignClient;
    @Resource
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private ShardingId shardingId;
    @Autowired
    private PayIntegration payIntegration;
    @Autowired
    private OrderModel orderModel;
    @Autowired
    private StoreFeignClient storeFeignClient;
    @Autowired
    private MemberFeignClient memberFeignClient;
    @Autowired
    private CustomerServiceFeign customerServiceFeign;
    @Autowired
    private MallPaymentFacade mallPaymentFacade;
    @Autowired
    private DiscountCouponFacade discountCouponFacade;
    @Autowired
    private OrderAfterServiceModel orderAfterServiceModel;
    @Autowired
    private OrderReturnModel orderReturnModel;

    @Autowired
    private ILoanResultService loanResultService;
    @Autowired
    private HttpServletRequest request;
    @Value("${mall-payment.refundNotify}")
    private String refundNotifyUrl;
    @Resource
    private IOrderService orderService;
    @Resource
    private IOrderReturnService orderReturnService;
    @Autowired
    private IOrderPlacingService orderPlacingService;
    @Autowired
    private IOrderProductService orderProductService;
    @Autowired
    private IOrderAfterService orderAfterService;
    @Autowired
    private IPayHandleService iPayHandleService;
    @Autowired
    private IOrderExtendFinanceService orderExtendFinanceService;
    @Autowired
    private PresellRefundService presellRefundService;
    @Autowired
    private OrderRefundRecordService orderRefundRecordService;
    @Autowired
    private IOrderProductExtendService orderProductExtendService;
    @Autowired
    private OrderPayRecordService orderPayRecordService;
    @Resource
    private OrderCreateHelper orderCreateHelper;
    @Resource
    private SkyCraneFeignClient skyCraneFeignClient;

    @Autowired
    private RefundCalculator refundCalculator;

    @Autowired
    private OrderReturnValidation orderReturnValidation;

    @Autowired
    private StoreIsolateWhiteListFeignClient storeIsolateWhiteListFeignClient;

    @Autowired
    private OrderCancelValidation orderCancelValidation;

    @Autowired
    private OrderPresellService orderPresellService;

    @Autowired
    private OrderProductModel orderProductModel;

    @Autowired
    private ERPIntegration erpIntegration;

    @Autowired
    private BmsIntegration bmsIntegration;


    /**
     * 新增订单售后服务表
     *
     * @param orderAfterServicePO
     * @return
     */
    public Integer saveOrderAfterService(OrderAfterPO orderAfterServicePO) {
        int count = orderAfterMapper.insert(orderAfterServicePO);
        if (count == 0) {
            throw new MallException("添加订单售后服务表失败，请重试");
        }
        return count;
    }

    /**
     * 根据afsId删除订单售后服务表
     *
     * @param afsId
     *            afsId
     * @return
     */
    public Integer deleteOrderAfterService(Integer afsId) {
        if (StringUtils.isEmpty(afsId)) {
            throw new MallException("请选择要删除的数据");
        }
        int count = orderAfterMapper.deleteByPrimaryKey(afsId);
        if (count == 0) {
            log.error("根据afsId：" + afsId + "删除订单售后服务表失败");
            throw new MallException("删除订单售后服务表失败,请重试");
        }
        return count;
    }

    /**
     * 根据afsId更新订单售后服务表
     *
     * @param orderAfterServicePO
     * @return
     */
    public Integer updateOrderAfterService(OrderAfterPO orderAfterServicePO) {
        if (StringUtils.isEmpty(orderAfterServicePO.getAfsId())) {
            throw new MallException("请选择要修改的数据");
        }
        int count = orderAfterMapper.updateByPrimaryKeySelective(orderAfterServicePO);
        if (count == 0) {
            log.error("根据afsId：" + orderAfterServicePO.getAfsId() + "更新订单售后服务表失败");
            throw new MallException("更新订单售后服务表失败,请重试");
        }
        return count;
    }

    public int updateByAfsSn(OrderAfterPO orderAfterServicePO) {
        if (StringUtils.isEmpty(orderAfterServicePO.getAfsSn())) {
            throw new MallException("请选择要修改的数据");
        }
        int count = orderAfterMapper.updateByAfsSn(orderAfterServicePO);
        if (count == 0) {
            log.error("根据afsId：" + orderAfterServicePO.getAfsId() + "更新订单售后服务表失败");
            throw new MallException("更新订单售后服务表失败,请重试");
        }
        return count;
    }

    public int updateRefundFailReasonByAfsSn(String afsSn, String failReason) {
        if (StringUtils.isEmpty(afsSn)) {
            throw new MallException("请选择要修改的数据");
        }
        OrderAfterPO orderAfterPO = new OrderAfterPO();
        orderAfterPO.setAfsSn(afsSn);
        orderAfterPO.setRefundFailReason(failReason);
        int count = orderAfterMapper.updateByAfsSn(orderAfterPO);
        if (count == 0) {
            log.error("根据afsSn：" + orderAfterPO.getAfsId() + "，更新退款失败原因失败");
            throw new MallException("更新退款失败原因失败,请重试");
        }
        return count;
    }

    /**
     * 根据afsId获取订单售后服务表详情
     *
     * @param afsId
     *            afsId
     * @return
     */
    public OrderAfterPO getOrderAfterServiceByAfsId(Integer afsId) {
        return orderAfterMapper.getByPrimaryKey(afsId);
    }

    /**
     * 根据条件获取订单售后服务表列表
     *
     * @param example
     *            查询条件信息
     * @param pager
     *            分页信息
     * @return
     */
    public List<OrderAfterPO> getOrderAfterServiceList(OrderAfterServiceExample example, PagerInfo pager) {
        List<OrderAfterPO> orderAfterServicePOList;
        if (pager != null) {
            pager.setRowsCount(orderAfterMapper.countByExample(example));
            orderAfterServicePOList =
                orderAfterMapper.listPageByExample(example, pager.getStart(), pager.getPageSize());
        } else {
            orderAfterServicePOList = orderAfterMapper.listByExample(example);
        }
        return orderAfterServicePOList;
    }

    /**
     * 根据afsSn获取订单售后服务信息
     *
     * @param afsSn
     * @return
     */
    public OrderAfterPO getAfterServiceByAfsSn(String afsSn) {
        OrderAfterServiceExample example = new OrderAfterServiceExample();
        example.setAfsSn(afsSn);
        List<OrderAfterPO> afterServiceList = orderAfterMapper.listByExample(example);
        AssertUtil.notEmpty(afterServiceList, "获取售后服务单信息为空，请重试");

        return afterServiceList.get(0);
    }

    /**
     * 根据售后单号获取退货信息
     *
     * @param afsSn
     * @return
     */
    private OrderReturnPO getOrderReturnByAfsSn(String afsSn) {
        OrderReturnExample example = new OrderReturnExample();
        example.setAfsSn(afsSn);
        List<OrderReturnPO> list = orderReturnMapper.listByExample(example);
        AssertUtil.notEmpty(list, "获取退货信息为空，请重试");
        return list.get(0);
    }

    /**
     * 根据售后单号获取换货信息
     *
     * @param afsSn
     * @return
     */
    private OrderReplacementPO getOrderReplacementByAfsSn(String afsSn) {
        OrderReplacementExample example = new OrderReplacementExample();
        example.setAfsSn(afsSn);
        List<OrderReplacementPO> list = orderReplacementMapper.listByExample(example);
        AssertUtil.notEmpty(list, "获取换货信息为空，请重试");
        return list.get(0);
    }

    /**
     * 退款申请信息
     *
     * @param memberId
     * @param orderSn
     * @param orderProductId
     * @param isValet 是否代客售后标识
     * @return
     */
    public AfsApplyInfoVO getAfterSaleApplyInfo(Integer memberId, String orderSn, Long orderProductId, boolean isValet) {
        /*
         *货品可退金额计算方法（平台优惠券不退的情况,每次退款都按比例退现金和积分）（RMB采用四舍五入计算，积分采用向下取整方式）
         * 1.订单总共可退RMB = 现金支付金额 + 余额支付金额 - 运费 ；   订单总共可退积分 = 订单使用的积分数量；订单购物赠送积分：扩展信息获取
         * 2.计算出本货品总共可退金额（金额包括积分、RMB）、总共需要扣除的购物赠送的积分：（为了保证展示的可退金额，与实际退款金额一致，采用如下算法，保证同一货品在展示与打款时，分配的可退金额相同）
         *      2.1 查询订单下的货品列表，按照数据库中的顺序
         *      2.2 如果不是最后一个货品，可退金额 = 订单总可退金额 * 货品明细金额/（订单总金额-运费）
         *      2.3 如果是最后一个货品，可退金额 = 订单总可退金额 - 其他订单分配的总额
         * 3.计算本货品剩余可退金额（金额包括积分、RMB） 、 剩余要扣除的购物赠送的积分 :
         *      3.1 本货品剩余可退金额 = 本货品总共可退金额 - 本货品总共可退金额 * 换货数量总和 / 货品总数量 - 所有退货金额总和
         */

        // 获取数据库中的订单,订单货品，数据检测
        OrderExample ordersExample = new OrderExample();
        ordersExample.setMemberId(memberId);
        ordersExample.setOrderSn(orderSn);
        OrderPO orderPODb = orderMapper.listByExample(ordersExample).get(0);
        AssertUtil.notNull(orderPODb, "订单不存在");
        OrderProductPO orderProductPODb = orderProductMapper.getByPrimaryKey(orderProductId);
        AssertUtil.notNull(orderProductPODb, "订单货品不存在");
        BizAssertUtil.isTrue(!memberId.equals(orderProductPODb.getMemberId()), "您无权操作此订单货品");

        // 检测订单信息，校验是否可以发起退款
        this.checkOrderInfo(memberId, orderPODb, orderProductPODb, isValet);

        // 舍入类型
        MathContext amountContext = new MathContext(3, RoundingMode.HALF_UP); // 四舍五入，保留2位小数
        MathContext integralContext = new MathContext(1, RoundingMode.DOWN); // 舍弃所有小数

        OrderExtendExample extendExample = new OrderExtendExample();
        extendExample.setOrderSn(orderPODb.getOrderSn());
        OrderExtendPO orderExtendPODb = orderExtendMapper.listByExample(extendExample).get(0);
        // 2.计算出本货品总共可退金额：
        Map<String, BigDecimal> orderProductTotalReturnMap = this.getOrderProductTotalReturn(orderPODb, orderExtendPODb,
            orderProductPODb, amountContext, integralContext);
        // 货品总共可退RMB
        BigDecimal productTotalReturnAmount = orderProductTotalReturnMap.get("productTotalReturnAmount");
        // 货品总共可退积分
        BigDecimal productTotalReturnIntegral = orderProductTotalReturnMap.get("productTotalReturnIntegral");
        // 货品购物赠送总积分
        BigDecimal productTotalSendIntegral = orderProductTotalReturnMap.get("productTotalSendIntegral");
        // 返回数据
        AfsApplyInfoVO afsApplyInfoVO = this.getOrderProductCanReturnAmount(orderProductPODb, productTotalReturnAmount,
            productTotalReturnIntegral, productTotalSendIntegral, amountContext, integralContext);
        afsApplyInfoVO.setNumber(orderProductPODb.getProductNum() - orderProductPODb.getReplacementNumber()
            - orderProductPODb.getReturnNumber());
        OrderProductListVO orderProductListVO = new OrderProductListVO(orderProductPODb);
        afsApplyInfoVO.setOrderProduct(orderProductListVO);
        //是满赠订单，并存在赠品关联关系
        if (orderPODb.getOrderType().equals(OrderTypeEnum.FULL_GIFT.getValue()) && orderProductPODb.getGiftGroup() > 0) {
            List<OrderProductPO> orderGiftProductPOS = orderProductService.lambdaQuery()
                    .eq(OrderProductPO::getOrderSn, orderPODb.getOrderSn())
                    .eq(OrderProductPO::getGiftGroup, orderProductPODb.getGiftGroup())
                    .eq(OrderProductPO::getIsGift, OrderConst.IS_GIFT_YES).list();
            List<OrderProductListVO> orderGiftProductListVOS = orderGiftProductPOS.stream().map(x -> {
                //赠品未退情况下，统一查询进行展示，已退则不展示
                if (x.getReturnNumber() >= x.getProductNum()) {
                    return null;
                }
                return new OrderProductListVO(x);
            }).collect(Collectors.toList());
            afsApplyInfoVO.setOrderGiftProductList(orderGiftProductListVOS);
        }
        return afsApplyInfoVO;
    }

    /**
     * 检测订单信息是否可用
     *
     * @param orderPODb
     * @param orderProductPODb
     * @param isValet 是否代客售后标识
     */
    private void checkOrderInfo(Integer memberId, OrderPO orderPODb, OrderProductPO orderProductPODb, boolean isValet) {
        BizAssertUtil.isTrue(!orderPODb.getMemberId().equals(memberId), "您无权操作此订单");

        // 商家是否仅支持内部退款，仅支持内部退款的需要客户经理申请
        boolean innerRefundStore = orderReturnValidation.isInnerRefundStore(orderPODb.getStoreId());
        if (isValet) {
            // 非内部退款店铺不允许 代客申请售后
            BizAssertUtil.isTrue(!innerRefundStore, "请客户在客户端自主发起退单申请");
        } else {
            // 内部退款店铺不允许 客户自主申请售后
            BizAssertUtil.isTrue(innerRefundStore, "抱歉，请联系客户经理或站长协助申请退款！");
        }

        if (OrderStatusEnum.TRADE_SUCCESS.isTrue(orderPODb.getOrderState())) {
            if(orderPODb.getAfterSalesDeadline() != null){
                BizAssertUtil.isTrue(orderPODb.getAfterSalesDeadline().before(new Date()), "很抱歉，售后服务时间已过");
            }else {
                int limitDay = Integer.parseInt(Objects.requireNonNull(stringRedisTemplate.opsForValue().get("time_limit_of_after_sale")));
                Date date = TimeUtil.getDateApartDay(-limitDay);
                BizAssertUtil.isTrue(orderPODb.getFinishTime().before(date), "很抱歉，售后服务时间已过");
            }
        }
        AssertUtil.isTrue(!orderProductPODb.getOrderSn().equals(orderPODb.getOrderSn()), "订单货品与订单信息不匹配");

        int num = orderProductPODb.getProductNum() - orderProductPODb.getReplacementNumber()
            - orderProductPODb.getReplacementNumber();
        AssertUtil.isTrue(num <= 0, "退换货信息有误");
    }

    /**
     * 计算订单中某一货品，总共可退的RMB、积分，购物赠送的总积分 计算规则为：按订单中所有货品的顺序，按照货品明细金额占订单金额（除运费）的比例计算，最后一个货品为总额-其他货品可退额之和
     *
     * @param orderPODb
     * @param orderExtendPODb
     * @param orderProductPODb
     * @param amountContext    RMB舍入方式
     * @param integralContent  积分舍入方式
     * @return
     */
    private Map<String, BigDecimal> getOrderProductTotalReturn(OrderPO orderPODb, OrderExtendPO orderExtendPODb,
        OrderProductPO orderProductPODb, MathContext amountContext, MathContext integralContent) {
        // 订单总共可退RMB = 三方支付金额 + 余额支付金额 - 运费
        BigDecimal orderTotalReturnAmount =
            orderPODb.getBalanceAmount().add(orderPODb.getPayAmount()).subtract(orderPODb.getExpressFee());
        // 订单总共可退积分
        BigDecimal orderTotalReturnIntegral = BigDecimal.valueOf(orderPODb.getIntegral());
        // 订单购物赠送的总积分
        BigDecimal orderTotalSendIntegral = BigDecimal.valueOf(orderExtendPODb.getOrderPointsCount());

        // 2.1 查询订单下的货品列表，按照数据库中的顺序
        OrderProductExample productExample = new OrderProductExample();
        productExample.setOrderSn(orderPODb.getOrderSn());
        List<OrderProductPO> orderProductPOList = orderProductMapper.listByExample(productExample);
        AssertUtil.notEmpty(orderProductPOList, "订单信息有误");

        BigDecimal productTotalReturnAmount;// 货品总共可退RMB
        BigDecimal productTotalReturnIntegral;// 货品总共可退积分
        BigDecimal productTotalSendIntegral;// 货品购物赠送总积分

        // 2.2 如果不是最后一个货品，可退金额 = 订单总可退金额 * 货品明细金额/（订单总金额-运费）
        if (!orderProductPOList.get(orderProductPOList.size() - 1).getProductId()
            .equals(orderProductPODb.getOrderProductId())) {
            if ((orderPODb.getOrderAmount().subtract(orderPODb.getExpressFee())).compareTo(BigDecimal.ZERO) == 0) {
                productTotalReturnAmount = BigDecimal.ZERO;
                productTotalReturnIntegral = BigDecimal.ZERO;
                productTotalSendIntegral = BigDecimal.ZERO;
            } else {
                productTotalReturnAmount = orderTotalReturnAmount // 订单总可退金额
                    .multiply(orderProductPODb.getMoneyAmount()) // *货品明细金额
                    .divide(orderPODb.getOrderAmount().subtract(orderPODb.getExpressFee()), amountContext); // /（订单总金额-运费）
                productTotalReturnIntegral = orderTotalReturnIntegral // 订单总可退积分
                    .multiply(orderProductPODb.getMoneyAmount()) // *货品明细金额
                    .divide(orderPODb.getOrderAmount().subtract(orderPODb.getExpressFee()), integralContent); // /（订单总金额-运费）

                productTotalSendIntegral = orderTotalSendIntegral // 订单购物赠送积分总额
                    .multiply(orderProductPODb.getMoneyAmount()) // *货品明细金额
                    .divide(orderPODb.getOrderAmount().subtract(orderPODb.getExpressFee()), integralContent); // /（订单总金额-运费）
            }
        } else {
            // 2.3 如果是最后一个货品，可退金额 = 订单总可退金额 - 其他订单分配的总额
            BigDecimal totalAssignAmount = BigDecimal.ZERO;// 已经分配的RMB
            BigDecimal totalAssignIntegral = BigDecimal.ZERO;// 已经分配的可退积分
            BigDecimal totalAssignSendIntegral = BigDecimal.ZERO;// 已经分配的购物赠送积分
            for (int i = 0; i < orderProductPOList.size() - 1; i++) {
                OrderProductPO op = orderProductPOList.get(i);
                // 当前货品分配RMB
                BigDecimal currentAssignAmount = BigDecimal.ZERO;
                if ((orderPODb.getOrderAmount().subtract(orderPODb.getExpressFee())).compareTo(BigDecimal.ZERO) != 0) {
                    currentAssignAmount = orderTotalReturnAmount // 订单总可退金额
                        .multiply(op.getMoneyAmount()) // *货品明细金额
                        .divide(orderPODb.getOrderAmount().subtract(orderPODb.getExpressFee()), amountContext); // /（订单总金额-运费）
                }
                // 累加
                totalAssignAmount = totalAssignAmount.add(currentAssignAmount);
                // 当前货品分配积分
                BigDecimal currentAssignIntegral = BigDecimal.ZERO;
                if ((orderPODb.getOrderAmount().subtract(orderPODb.getExpressFee())).compareTo(BigDecimal.ZERO) != 0) {
                    currentAssignIntegral = orderTotalReturnIntegral // 订单总可退积分
                        .multiply(op.getMoneyAmount()) // *货品明细金额
                        .divide(orderPODb.getOrderAmount().subtract(orderPODb.getExpressFee()), integralContent); // /（订单总金额-运费）
                }
                // 累加
                totalAssignIntegral = totalAssignIntegral.add(currentAssignIntegral);

                // 当前货品分配积分
                BigDecimal currentAssignSendIntegral = BigDecimal.ZERO;
                if ((orderPODb.getOrderAmount().subtract(orderPODb.getExpressFee())).compareTo(BigDecimal.ZERO) != 0) {
                    currentAssignSendIntegral = orderTotalSendIntegral // 订单总可退积分
                        .multiply(op.getMoneyAmount()) // *货品明细金额
                        .divide(orderPODb.getOrderAmount().subtract(orderPODb.getExpressFee()), integralContent); // /（订单总金额-运费）
                }
                // 累加
                totalAssignSendIntegral = totalAssignSendIntegral.add(currentAssignSendIntegral);
            }
            productTotalReturnAmount = orderTotalReturnAmount.subtract(totalAssignAmount);
            productTotalReturnIntegral = orderTotalReturnIntegral.subtract(totalAssignIntegral);
            productTotalSendIntegral = orderTotalSendIntegral.subtract(totalAssignSendIntegral);
        }

        // 返回数据
        Map<String, BigDecimal> result = new HashMap<>();
        result.put("productTotalReturnAmount", productTotalReturnAmount);
        result.put("productTotalReturnIntegral", productTotalReturnIntegral);
        result.put("productTotalSendIntegral", productTotalSendIntegral);
        return result;
    }

    /**
     * 计算订单货品剩余可退金额 本货品剩余可退金额 = 本货品总共可退金额 - 本货品总共可退金额 * 换货数量总和 / 货品总数量 - 所有退货金额总和
     *
     * @param orderProductPODb
     * @param productTotalReturnAmount
     *            货品总可退RMB
     * @param productTotalReturnIntegral
     *            货品总可退积分
     * @param productTotalSendIntegral
     *            货品总共购物赠送的积分
     * @param amountContext
     *            RMB舍入方式
     * @param integralContext
     *            积分舍入方式
     * @return
     */
    private AfsApplyInfoVO getOrderProductCanReturnAmount(OrderProductPO orderProductPODb,
        BigDecimal productTotalReturnAmount, BigDecimal productTotalReturnIntegral, BigDecimal productTotalSendIntegral,
        MathContext amountContext, MathContext integralContext) {
        AfsApplyInfoVO afsApplyInfoVO = new AfsApplyInfoVO();
        Map<String, BigDecimal> canReturnMap = new HashMap<>();// 返回数据
        BigDecimal moneyCanReturn;// 剩余可退RMB
        BigDecimal integralCanReturn;// 剩余可退积分
        BigDecimal integralCanDeduct;// 剩余可扣积分（购物赠送积分）

        if (orderProductPODb.getReplacementNumber() + orderProductPODb.getReturnNumber() == 0) {
            // 没有退换货记录，直接返回该货品的总可退金额
            moneyCanReturn = productTotalReturnAmount;
            integralCanReturn = productTotalReturnIntegral;
            integralCanDeduct = productTotalSendIntegral;
        } else {
            // 3.计算本货品剩余可退金额 :
            // 3.1 本货品剩余可退金额 = 本货品总共可退金额 - 本货品总共可退金额 * 换货数量总和 / 货品总数量 - 所有退货金额总和
            // 查询售后列表
            OrderAfterServiceExample afterServiceExample = new OrderAfterServiceExample();
            afterServiceExample.setOrderSn(orderProductPODb.getOrderSn());
            afterServiceExample.setOrderProductId(orderProductPODb.getOrderProductId());
            List<OrderAfterPO> afterServiceList = orderAfterMapper.listByExample(afterServiceExample);
            BigDecimal replacementNum = BigDecimal.ZERO;// 换货总数
            BigDecimal returnMoney = BigDecimal.ZERO;// 退款总数
            BigDecimal returnIntegral = BigDecimal.ZERO;// 退积分总数
            BigDecimal deductIntegral = BigDecimal.ZERO;// 扣减积分总数
            for (OrderAfterPO afterService : afterServiceList) {
                if (afterService.getAfsType() == OrdersAfsConst.AFS_TYPE_REPLACEMENT) {
                    // 换货，累加换货数量
                    replacementNum = replacementNum.add(BigDecimal.valueOf(afterService.getAfsNum()));
                } else {
                    OrderReturnExample example = new OrderReturnExample();
                    example.setAfsSn(afterService.getAfsSn());
                    List<OrderReturnPO> list = orderReturnMapper.listByExample(example);
                    AssertUtil.notEmpty(list, "获取退货记录信息失败");
                    // 退货，查询退货表，退款金额累加
                    OrderReturnPO orderReturnPO = list.get(0);
                    returnMoney = returnMoney.add(orderReturnPO.getReturnMoneyAmount());
                    returnIntegral = returnIntegral.add(BigDecimal.valueOf(orderReturnPO.getReturnIntegralAmount()));
                    deductIntegral = deductIntegral.add(BigDecimal.valueOf(orderReturnPO.getDeductIntegralAmount()));
                }
            }
            // 按比例计算换货的退款、退积分、扣积分数
            // 计算换货RMB
            BigDecimal replaceMoney = productTotalReturnAmount // 本货品总共可退金额
                .multiply(replacementNum) // * 换货数量
                .divide(BigDecimal.valueOf(orderProductPODb.getProductNum()), amountContext); // / 货品总数量

            // 计算换货积分
            BigDecimal replaceIntegral = productTotalReturnIntegral // 本货品总共可退金额
                .multiply(replacementNum) // * 换货数量
                .divide(BigDecimal.valueOf(orderProductPODb.getProductNum()), integralContext); // / 货品总数量

            // 换货扣除的购物赠送积分
            BigDecimal replaceDeductIntegral = productTotalSendIntegral // 货品购物赠送总积分
                .multiply(replacementNum) // * 换货数量
                .divide(BigDecimal.valueOf(orderProductPODb.getProductNum()), integralContext); // / 货品总数量

            // 计算返回数据 = 总数据 - 退货数据 - 换货数据
            moneyCanReturn = productTotalReturnAmount.subtract(returnMoney).subtract(replaceMoney);
            integralCanReturn = productTotalReturnIntegral.subtract(returnIntegral).subtract(replaceIntegral);
            integralCanDeduct = productTotalSendIntegral.subtract(deductIntegral).subtract(replaceDeductIntegral);
        }
        afsApplyInfoVO.setMoneyCanReturn(moneyCanReturn);
        afsApplyInfoVO.setIntegralCanReturn(integralCanReturn);
        afsApplyInfoVO.setIntegralCanDeduct(integralCanDeduct);
        return afsApplyInfoVO;
    }

    public RefundType getCombinationRefundRefundType(OrderPO orderPODb,OrderPayRecordPO orderPayRecord) {
        if (ObjectUtils.isEmpty(orderPayRecord)) {
            throw new MallException("组合支付未匹配到待退款的组合支付明细:"+orderPODb.getOrderSn() + ErrorCodeEnum.S.DATA_NOT_FOUND.getCode());
        }
        // 把组合支付，替换为支付明细上的具体支付方式
        orderPODb.setPaymentCode(orderPayRecord.getPaymentCode());
        return this.getRefundType(orderPODb,null);
    }

    public RefundType getRefundType(String orderSn) {
        return getRefundType(null, orderSn);
    }

    public RefundType getRefundType(OrderPO orderPODb) {
        return getRefundType(orderPODb, null);
    }

    private RefundType getRefundType(OrderPO orderPODb,String orderSn) {
        if(ObjectUtils.isEmpty(orderPODb)){
            OrderExample example = new OrderExample();
            example.setOrderSn(orderSn);
            orderPODb = orderMapper.listByExample(example).get(0);
        }
        log.info("##当前订单的退款类型的code：{},PayWayEnum.ENJOY_PAY.getValue():{},compare:{}", orderPODb.getPaymentCode(),
            PayWayEnum.ENJOY_PAY.getValue(),
            JSON.toJSONString(PayWayEnum.ENJOY_PAY.getValue() == orderPODb.getPaymentCode()));
        if(ExchangeOrderConst.ORDER_EXCHANGE_FLAG_2 == orderPODb.getExchangeFlag()) {
            return RefundType.ZERO_YUAN_REFUND;
        }
        if (PayWayEnum.ALI_PAY.getValue().equals(orderPODb.getPaymentCode())) {
            return RefundType.APLIPAY_REFUND;
        }
        if (PayWayEnum.WX_PAY.getValue().equals(orderPODb.getPaymentCode())) {
            return RefundType.WXPAY_REFUND;
        }
        if (PayWayEnum.BALANCE.getValue().equals(orderPODb.getPaymentCode())) {
            return RefundType.ACCOUNT_REGULATION;
        }
        if (PayMethodEnum.COMBINATION_PAY.getValue().equals(orderPODb.getPaymentCode())) {
            return RefundType.COMBINATION_REFUND;
        }
        if (PayMethodEnum.isLoanPay(PayMethodEnum.getValue(orderPODb.getPaymentCode()))) {
            if (orderPODb.getLoanPayState().equals(LoanStatusEnum.WAIT_LENDING.getValue())
                || orderPODb.getLoanPayState().equals(LoanStatusEnum.LENDING_FAIL.getValue())
                || orderPODb.getLoanPayState().equals(LoanStatusEnum.APPLY_SUCCESS.getValue())) {
                // 支付成功、待起息、起息失败 -->恢复额度
                return RefundType.RESTORE_LIMIT;
            } else if (orderPODb.getLoanPayState().equals(LoanStatusEnum.LENDING_SUCCESS.getValue())) {
                return RefundType.ASSIST_PAYMENT;
            } else {
                throw new BusinessException("售后单所属订单正在放款中，不能申请退款!");
            }
        }
        if (PayMethodEnum.CARD_VOUCHER.getValue().equals(orderPODb.getPaymentCode())) {
            return RefundType.CARD_VOUCHER_REFUND;
        }
        if (PayMethodEnum.BANK_PAY.getValue().equals(orderPODb.getPaymentCode())) {
            return RefundType.BANK_REFUND;
        }
        if (PayMethodEnum.CARD.getValue().equals(orderPODb.getPaymentCode())) {
            return RefundType.CARD_REFUND;
        }
        if (PayMethodEnum.BANK_TRANSFER.getValue().equals(orderPODb.getPaymentCode())) {
            return RefundType.TRANSFER_REFUND;
        }
        if (PayMethodEnum.AGREED_PAY.getValue().equals(orderPODb.getPaymentCode())) {
            return RefundType.AGREED_REFUND;
        }
        throw new MallException("未匹配到退款类型:" + orderPODb.getOrderSn() + ErrorCodeEnum.S.DATA_NOT_FOUND.getCode());
    }

    /**
     * 退款申请提交
     *
     * @param orderAfterDTO
     * @param member
     * @return productId作为key，售后单号为value的map
     */
    @GlobalTransactional
    public Map<Long,String> submitAfterSaleApply(OrderAfterDTO orderAfterDTO, Member member) {
        Map<Long,String> result = Maps.newHashMap();

        BizAssertUtil.notEmpty(orderAfterDTO.getProductList(), "请选择申请售后的订单货品");
        // 客户经理、站长代客售后参数处理
        OrderPlaceUserDTO userPlaceDTO = preDealParams(orderAfterDTO, member);

        OrderPO orderPODb = orderPlacingService.getByOrderSn(orderAfterDTO.getOrderSn());

        // 商家是否仅支持内部退款，仅支持内部退款的需要客户经理申请
        boolean innerRefundStore = orderReturnValidation.isInnerRefundStore(orderPODb.getStoreId());
        Integer returnBy = orderAfterDTO.getReturnBy();
        log.info("订单:{}发起退款, 发起者身份类型:{}", orderAfterDTO.getOrderSn(), ReturnByEnum.getDesc(returnBy));
        if (ReturnByEnum.isValet(returnBy)) {
            //非内部退款商家，不允许客户经理、站长代客发起售后
            BizAssertUtil.isTrue(!innerRefundStore, "请客户在客户端自主发起退单申请");
        } else {
            BizAssertUtil.isTrue(innerRefundStore, "抱歉，请联系客户经理或站长协助申请退款！");
            BizAssertUtil.isTrue(!orderPODb.getMemberId().equals(member.getMemberId()), "您无权操作此订单");
        }

        BizAssertUtil.isTrue(ExchangeOrderConst.ORDER_EXCHANGE_FLAG_2 == orderPODb.getExchangeFlag(), "换货后的订单不允许强制退款");
        BizAssertUtil.isTrue(!OrderRefundApplyValidation.refundApplyProductCheck(orderAfterDTO.getProductList()), "商品售后申请数量全为0，请确认~");
        BizAssertUtil.isTrue(OrderPatternEnum.PURCHASE_CENTRE.getValue().equals(orderPODb.getOrderPattern()) &&
                orderReturnService.hasDuringRefund(orderAfterDTO.getOrderSn()), "采购订单存在处理中的退订单,请处理完原退订单再申请");
        BizAssertUtil.isTrue(OrderPatternEnum.COUPON_CENTRE.getValue().equals(orderPODb.getOrderPattern()),
                "抱歉，卡券订单不允许进行退款");
        BizAssertUtil.isTrue(orderPODb.getOrderType().equals(OrderTypeEnum.REBATE_GIFT.getValue()) && ReturnByEnum.CUSTOMER.getValue().equals(returnBy),"很抱歉，赠品订单暂不支持售后～");
        Store store = storeFeignClient.getStoreByStoreId(orderPODb.getStoreId());
        BizAssertUtil.isTrue(StoreStateEnum.LOGOUTING.getCode().equals(store.getState())
                        || StoreStateEnum.LOGOUT.getCode().equals(store.getState()), "店铺已注销，不允许退款");
        BizAssertUtil.isTrue(orderReturnService.hasDuringExchangeRefund(orderAfterDTO.getOrderSn()),"订单正在换货处理，请换货完成后再发起退单处理");
        BizAssertUtil.isTrue(!tianjieOrderSnCheck(orderPODb.getOrderSn()), "该订单不允许发起售后，请联系管理员");

        // 参数校验
        this.checkAfsApplyInfo(orderPODb, orderAfterDTO);

        // 校验支付方式是否为ENJOY_PAY&未起息 -- 必须整单退
        this.checkRundByEnjoy(orderAfterDTO, orderPODb);

        //好食期商品必须整单退
        this.checkHsqRefund(orderAfterDTO, orderPODb);

        // 1.处理售后货品，统计各个货品退款信息 ,处理货品退款信息时会设置支付方式和退款方式
        List<AfsProductVO> afsProductVOS = this.dealAfsProduct(orderPODb, orderAfterDTO);

        afsProductVOS.forEach(afsProductVO -> {

            BizAssertUtil.isTrue(!orderReturnValidation.refundProductCheck(afsProductVO.getOrderProductId()),
                    "商品存在退款中的退款单，不允许再次发起退款~");
            int refundType = afsProductVO.getRefundType();
            if (RefundType.RESTORE_LIMIT.getValue() == refundType && orderPODb.getExchangeFlag() == ExchangeOrderConst.ORDER_EXCHANGE_FLAG_1){
                // 恢复额度,如果该订单有换货单，则不允许发起售后
                throw new BusinessException("订单存在换货处理，请放款完成后再发起退单申请");
            }

            //商品的退货退款数量，不能超过发货数量
            if(OrdersAfsConst.AFS_TYPE_RETURN == orderAfterDTO.getAfsType()) {
                BizAssertUtil.isTrue(orderReturnValidation.validReturnNumGtDeliverNum(afsProductVO.getOrderProductId(), afsProductVO.getAfsNum()),
                        "商品的退货退款数量，不能超过发货数量~");
            }



            // 1. 生成退订单号
            String afsSn = shardingId.next(SeqEnum.RNO, member.getMemberId().toString()) + "";
            result.put(afsProductVO.getProductId(),afsSn);
            result.put(afsProductVO.getOrderProductId(), afsSn);

            // 2. 插入售后申请表
            this.insertAfterService(afsSn, afsProductVO, orderAfterDTO, orderPODb);

            // 3. 插入退订申请表
            OrderReturnPO orderReturnPODb = this.insertReturnOrder(afsSn, orderAfterDTO, afsProductVO, orderPODb);

            // 4. 更新订单货品中的退换货数量
            // 这里维护了退货数量（售后数量），没有维护换货数量！！！数据未更新没有做处理
            orderProductService.addReturnNumber(afsProductVO.getOrderProductId(), afsProductVO.getAfsNum());

            // 处理订单商品行退款状态
            orderProductService.dealOrderProductReturnStatus(CommonConst.PRODUCT_RETURN_APPLY,
                    afsProductVO.getOrderProductId());

            //批量保存退款记录 Andy.2022.05.27 预售
            orderRefundRecordService.saveBatch(orderReturnPODb, orderPODb);

            // 计算扣罚金额，实际退款金额需要减去扣罚金额 并更新
            if (orderReturnValidation.refundPunishAmountSupport(orderReturnPODb)) {
                this.recalculateRefundAmount(orderPODb, orderReturnPODb);
            }

            // 5. 记录退订日志（步骤3已经插入了日志）
            // this.insertAfsLog(afsSn, orderAfterDTO.getAfsType(), orderPODb, OrderConst.RETURN_BY_1);

            Date now = new Date();
            // 6、记录退款轨迹
            OrderReturnTrackPO orderReturnTrackPO =
                OrderReturnTrackPO.builder().afsSn(afsSn).operateType(OrderReturnOperateTypeEnum.CREATE.getValue())
                    .operator(userPlaceDTO.getOrderPlaceUserName() + "-" + userPlaceDTO.getMemberMobile()).operateTime(now)
                    .operateResult(AuditResultEnum.AUDIT_PASS.getValue()).operateRemark("发起退款")
                    .channel(orderAfterDTO.getChannel()).build();
            if (ReturnByEnum.isValet(returnBy)) {
                orderReturnTrackPO.setOperatorRole(OperationRoleEnum.getValueByReturnBy(returnBy));
            } else {
                orderReturnTrackPO.setOperatorRole(OperationRoleEnum.CUSTOMER.getValue());
            }
            orderReturnTrackMapper.insert(orderReturnTrackPO);

            // 6. 发送消息
            this.afterSaleApplyMessageHandle(orderReturnPODb, orderAfterDTO);

            //售后申请MQ
            orderCreateHelper.addOrderReturnEvent(orderPODb, orderPODb.getXzCardAmount(), orderReturnPODb,
                    OrderEventEnum.REFUND_APPLY, now,null);
        });
        // 5.更新订单信息（锁定订单）
        this.updateOrder(orderPODb, afsProductVOS.size());
        // 6.预付订单特殊处理
        Integer orderType = orderPODb.getOrderType();
        Integer orderState = orderPODb.getOrderState();
        // 预付定金付款中，且支付状态为未支付，支付方式为银行卡汇款，取消银行卡汇款
        if (OrderTypeEnum.PRE_SELL_DEPOSIT.getValue().equals(orderType) && OrderStatusEnum.DEAL_PAY.getValue().equals(orderState)){
            BizAssertUtil.isTrue(!orderCancelValidation.invokePresellClosePayment(orderPODb), "订单暂不支持取消，请稍后~");
            // 订单状态此时就需要更新订单状态
            LambdaUpdateWrapper<OrderPO> orderUpdate = Wrappers.lambdaUpdate(OrderPO.class);
            orderUpdate.eq(OrderPO::getOrderSn,orderPODb.getOrderSn());
            orderUpdate.eq(OrderPO::getOrderState, OrderStatusEnum.DEAL_PAY.getValue());
            orderUpdate.set(OrderPO::getOrderState, OrderStatusEnum.WAIT_PAY.getValue());
            boolean update = orderPlacingService.update(orderUpdate);
            BizAssertUtil.isTrue(!update, "订单暂不支持取消，请稍后~");
            // 更新尾款支付状态
            // List<OrderPresellPO> list = lambdaQuery()
            //                .eq(OrderPresellPO::getPaySn, paySn)
            //                .eq(OrderPresellPO::getPayStatus, CommonConst.PAY_STATUS_2)
            //                .eq(OrderPresellPO::getPaymentCode, PayMethodEnum.BANK_TRANSFER.getValue())
            //                .list();
            LambdaUpdateWrapper<OrderPresellPO> presellUpdate = Wrappers.lambdaUpdate(OrderPresellPO.class);
            presellUpdate.eq(OrderPresellPO::getOrderSn,orderPODb.getOrderSn());
            presellUpdate.eq(OrderPresellPO::getType, PresellCapitalTypeEnum.BALANCE.getValue());
            presellUpdate.eq(OrderPresellPO::getPaymentCode,PayMethodEnum.BANK_TRANSFER.getValue());
            presellUpdate.set(OrderPresellPO::getPayStatus,CommonConst.PAY_STATUS_1);
            update = orderPresellService.update(presellUpdate);
            BizAssertUtil.isTrue(!update, "订单暂不支持取消，请稍后~");
        }
        return result;
    }

    /**
     * 预处理参数
     * @param orderAfterDTO
     * @param member
     * @return
     */
    private OrderPlaceUserDTO preDealParams(OrderAfterDTO orderAfterDTO, Member member) {
        if ((OrderCreateChannel.XXAPP.getValue().equals(orderAfterDTO.getChannel()) || OrderCreateChannel.XX_MINI_PRO.getValue().equals(orderAfterDTO.getChannel()))
                && CommonEnum.YES.getCode().equals(orderAfterDTO.getValetFlag())) {
            orderAfterDTO.setReturnBy(ReturnByEnum.SITE_MONSTER.getValue());
        } else if (OrderCreateChannel.BAPP.getValue().equals(orderAfterDTO.getChannel()) && CommonEnum.YES.getCode().equals(orderAfterDTO.getValetFlag())) {
            orderAfterDTO.setReturnBy(ReturnByEnum.ACCOUNT_MANAGER.getValue());
        }
        OrderPlaceUserDTO userDTO = OrderBuilder.buildAfterSalesPlaceUser(request, member, orderAfterDTO.getChannel(), orderAfterDTO.getValetFlag());
        // 保存售后操作人
        orderAfterDTO.setContactName(userDTO.getOrderPlaceUserName() + "-" + userDTO.getMemberMobile());
        orderAfterDTO.setOperatorUserId(userDTO.getOrderPlaceUserId());
        return userDTO;
    }

    /**
     * 好食期订单必须整单退
     * @param orderPO
     */
    private void checkHsqRefund(OrderAfterDTO orderAfterDTO, OrderPO orderPO) {
        if(orderProductModel.isContainHsfOrder(orderPO.getOrderSn())) {
            log.info("checkHsqRefund orderSn = {}", orderPO.getOrderSn());
            List<OrderAfterPO> afterPOS = new ArrayList<>(orderAfterDTO.getProductList().size());
            for (OrderAfterDTO.AfterProduct afterProduct : orderAfterDTO.getProductList()) {
                OrderAfterPO orderAfterPO = new OrderAfterPO();
                orderAfterPO.setOrderProductId(afterProduct.getOrderProductId());
                orderAfterPO.setAfsNum(afterProduct.getAfsNum());
                afterPOS.add(orderAfterPO);
            }

            List<OrderProductPO> orderProductPOList = orderProductModel.getOrderProductListByOrderSn(orderPO.getOrderSn());
            if (!orderAfterService.isAllReturnApplied(afterPOS, orderProductPOList)) {
                throw new BusinessException("抱歉，当前订单不支持部分退款，请重新申请！");
            }
        }
    }

    /**
     * 售后单用户申请发送消息
     *
     * @param orderReturnPO     退款单资金信息
     * @param orderAfterDTO     退款单申请信息
     */
    private void afterSaleApplyMessageHandle(OrderReturnPO orderReturnPO, OrderAfterDTO orderAfterDTO) {
        // 平台申请的，不需要通知店铺
        if (ReturnByEnum.ACCOUNT_MANAGER.getValue().equals(orderAfterDTO.getReturnBy())) {
            return;
        }

        List<MessageSendProperty> messageSendProperties = new ArrayList<>();
        String msgLinkInfo = "";
        // 模板类型
        String tplType = "";
        if (orderAfterDTO.getAfsType() == OrdersAfsConst.AFS_TYPE_RETURN) {
            messageSendProperties.add(new MessageSendProperty("returnSn", orderReturnPO.getAfsSn()));
            msgLinkInfo = "{\"afsSn\":\"" + orderReturnPO.getAfsSn() + "\",\"type\":\"return_news\"}";
            tplType = StoreTplConst.MEMBER_RETURN_REMINDER;
        } else if (orderAfterDTO.getAfsType() == OrdersAfsConst.AFS_TYPE_REFUND) {
            messageSendProperties.add(new MessageSendProperty("refundSn", orderReturnPO.getAfsSn()));
            msgLinkInfo = "{\"afsSn\":\"" + orderReturnPO.getAfsSn() + "\",\"type\":\"refund_news\"}";
            tplType = StoreTplConst.MEMBER_REFUND_REMINDER;
        } else {
            messageSendProperties.add(new MessageSendProperty("replaceSn", orderReturnPO.getAfsSn()));
            msgLinkInfo = "{\"afsSn\":\"" + orderReturnPO.getAfsSn() + "\",\"type\":\"replacement_news\"}";
            tplType = StoreTplConst.MEMBER_REPLACEMENT_REMINDER;
        }

        // 微信消息通知
        List<MessageSendProperty> messageSendPropertyList4Wx = new ArrayList<>();
        String result = String.format("客户发起售后，单号%s，请前往商家端查看详情。", orderReturnPO.getAfsSn());
        messageSendPropertyList4Wx.add(new MessageSendProperty("first", "【用户发起退款通知】"));
        messageSendPropertyList4Wx.add(new MessageSendProperty("keyword1", orderReturnPO.getMemberName()));
        messageSendPropertyList4Wx.add(new MessageSendProperty("keyword2", "售后"));
        messageSendPropertyList4Wx.add(new MessageSendProperty("keyword3", result));
        messageSendPropertyList4Wx.add(new MessageSendProperty("keyword4", TimeUtil.getDateTimeString(new Date())));
        messageSendPropertyList4Wx.add(new MessageSendProperty("remark", "服务农村最后一百米"));

        MessageSendVO messageSendVO = new MessageSendVO(messageSendProperties, messageSendPropertyList4Wx,null,
                orderReturnPO.getStoreId().intValue(), tplType, msgLinkInfo);
        // 发送到mq
        try {
            rabbitTemplate.convertAndSend(MQ_EXCHANGE_NAME, MQ_QUEUE_NAME_SELLER_MSG, messageSendVO);
        } catch (Exception e) {
            log.info("发消息异常捕获,{}", JSON.toJSONString(messageSendVO), e);
        }
    }

    /**
     * 退款申请时重置相关金额信息（退款扣罚金额、实际退款金额）
     *
     * @param orderPo           订单信息
     * @param orderReturnPo     退款单信息
     */
    public void recalculateRefundAmount(OrderPO orderPo, OrderReturnPO orderReturnPo) {
        // 更新退款单上的金额
        BigDecimal refundPunishAmount = refundCalculator.refundPunishCalculate(orderReturnPo, orderPo);
        log.info("recalculateRefundAmount , afs {},  the refundPunishAmount is {}", orderReturnPo.getAfsSn(), refundPunishAmount);
        if (BigDecimal.ZERO.compareTo(refundPunishAmount) >= 0) {
            return;
        }

        LambdaUpdateWrapper<OrderReturnPO> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(OrderReturnPO::getRefundPunishAmount, refundPunishAmount)
                .set(OrderReturnPO::getActualReturnMoneyAmount, orderReturnPo.getActualReturnMoneyAmount().subtract(refundPunishAmount))
                .eq(OrderReturnPO::getAfsSn, orderReturnPo.getAfsSn());
        orderReturnService.update(updateWrapper);

        // 组合退款时，更新退款记录上的实际退款金额
        if (RefundType.COMBINATION_REFUND.getValue().equals(orderReturnPo.getRefundType())) {
            LambdaQueryWrapper<OrderRefundRecordPO> refundRecordQueryWrapper = Wrappers.lambdaQuery();
            refundRecordQueryWrapper.eq(OrderRefundRecordPO::getAfsSn, orderReturnPo.getAfsSn())
                    .eq(OrderRefundRecordPO::getRefundType, RefundType.ASSIST_PAYMENT.getValue())
                    .eq(OrderRefundRecordPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
            OrderRefundRecordPO assistRefundRecord = orderRefundRecordService.getOne(refundRecordQueryWrapper);
            if (Objects.nonNull(assistRefundRecord)) {
                LambdaUpdateWrapper<OrderRefundRecordPO> refundRecordUpdateWrapper = Wrappers.lambdaUpdate();
                refundRecordUpdateWrapper.set(OrderRefundRecordPO::getActualAmount,
                        assistRefundRecord.getActualAmount().subtract(refundPunishAmount))
                        .eq(OrderRefundRecordPO::getAfsSn, orderReturnPo.getAfsSn())
                        .eq(OrderRefundRecordPO::getRefundType, RefundType.ASSIST_PAYMENT.getValue())
                        .eq(OrderRefundRecordPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
                orderRefundRecordService.update(refundRecordUpdateWrapper);
            }
        }

    }

    /**
     * 校验支付方式是否为ENJOY_PAY&未起息 -- 必须整单退
     */

    public void checkRundByEnjoy(OrderAfterDTO orderAfterDTO, OrderPO orderPO) {
    	//Andy.预付增加校验
    	String paymentCode=orderPO.getPaymentCode();
    	if (PayMethodEnum.isCombinationPay(orderPO.getPaymentCode())) {
            //预付尾款是否参与贷款支付，是否在本次还款
            List<OrderPayRecordPO> orderPayRecordPO = orderPayRecordService.queryLoanNoRefundPayOrderByPaySn(orderPO.getPaySn());
            if(ObjectUtils.isNotEmpty(orderPayRecordPO)) {
            	orderPO.setPaymentCode(orderPayRecordPO.get(0).getPaymentCode());
            	log.info("【checkRundByEnjoy】paySn:{} orderSn:{}验证预付定金恢复额度。",orderPO.getPaySn());
            }
        }
        // 校验order订单支付方式是否为ENJOY_PAY&未起息。|| 订单退定金必须整单退还.Andy
        RefundType refundType = this.getRefundType(orderPO);
    	orderPO.setPaymentCode(paymentCode);
        if (RefundType.RESTORE_LIMIT.equals(refundType) || OrderStatusEnum.isPresellDeposit(orderPO.getOrderState(), orderPO.getOrderType()) ||
        		OrderStatusEnum.isPresellDeposit(orderPO)) {//Andy
        	log.info("【checkRundByEnjoy】整单退还验证 paySn:{} orderSn:{} ",orderPO.getPaySn());
            // 查询订单货品

            LambdaQueryWrapper<OrderProductPO> orderProductQuery = new LambdaQueryWrapper<>();
            orderProductQuery.eq(OrderProductPO::getOrderSn, orderPO.getOrderSn());
            orderProductQuery.eq(OrderProductPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
            orderProductQuery.eq(OrderProductPO::getIsGift, OrderConst.IS_GIFT_NO);
            List<OrderProductPO> orderProductPOList = orderProductService.list(orderProductQuery);

            // 货品品种&对应品种退款件数要相同

            List<OrderAfterPO> afterPOS = new ArrayList<>(orderAfterDTO.getProductList().size());
            for (OrderAfterDTO.AfterProduct afterProduct : orderAfterDTO.getProductList()) {
                OrderAfterPO orderAfterPO = new OrderAfterPO();
                orderAfterPO.setOrderProductId(afterProduct.getOrderProductId());
                orderAfterPO.setAfsNum(afterProduct.getAfsNum());
                afterPOS.add(orderAfterPO);
            }

            if (!orderAfterService.isAllReturnApplied(afterPOS, orderProductPOList)) {
            	log.warn("【checkRundByEnjoy】整单退还验证 paySn:{} orderSn:{} refundType:{}",orderPO.getPaySn(),orderPO.getOrderSn(),refundType);
                throw new BusinessException("抱歉，当前订单不支持部分退款，请重新申请！");
            }

            BizAssertUtil.isTrue(OrderConst.ORDER_STATE_25 == orderPO.getOrderState(),
                    "【未放款起息的贷款支付订单需全部商品都发货后才能申请退款】");

        }
    }

    /**
     * 校验售后申请信息
     *
     * @param orderPO
     * @param orderAfterDTO
     */
    private void checkAfsApplyInfo(OrderPO orderPO, OrderAfterDTO orderAfterDTO) {
        // 20240613判断是否允许退款这里不判断预付订单，预付订单单独判断
        AssertUtil.isTrue(!OrderStatusEnum.isAllowedReturnV2(orderPO.getOrderState(),orderPO.getOrderType()), "订单状态有误");
        AssertUtil.isTrue(!presellRefundService.checkPresellOrder(orderPO),"订单状态有误");

        // 获取售后限制时间
        if (OrderStatusEnum.TRADE_SUCCESS.isTrue(orderPO.getOrderState())) {
            if(orderPO.getAfterSalesDeadline() != null){
                BizAssertUtil.isTrue(orderPO.getAfterSalesDeadline().before(new Date()), "很抱歉，售后服务时间已过");
            }else {
                int limitDay = Integer.parseInt(stringRedisTemplate.opsForValue().get("time_limit_of_after_sale"));
                Date date = TimeUtil.getDateApartDay(-limitDay);
                BizAssertUtil.isTrue(orderPO.getFinishTime().before(date), "很抱歉，售后服务时间已过");
            }
        }

        // 各售后类型参数校验
        switch (orderAfterDTO.getAfsType()) {
            case OrdersAfsConst.AFS_TYPE_RETURN:// 退货退款
                break;
            case OrdersAfsConst.AFS_TYPE_REPLACEMENT:
                throw new MallException("暂不支持换货申请");
            case OrdersAfsConst.AFS_TYPE_REFUND:// 仅退款
                if (OrderStatusEnum.WAIT_RECEIPT.isTrue(orderPO.getOrderState())
                    || OrderStatusEnum.TRADE_SUCCESS.isTrue(orderPO.getOrderState())) {
                    // 已发货申请仅退款，货物状态必填
                    AssertUtil.notNull(orderAfterDTO.getGoodsState(), "货物状态不能为空");
                    if (orderAfterDTO.getGoodsState() == OrdersAfsConst.GOODS_STATE_YES) {
                        // 仅退款，已收到货，可以修改退款金额，退款金额必传
                        AssertUtil.notNull(orderAfterDTO.getFinalReturnAmount(), "退款金额不能为空");
                    }
                }
                break;
            default:
                throw new MallException("暂不支持该类型的售后");
        }

        // 校验是否是ENJOY_PAY支付 && 起息中 -->拒绝退款
        if (PayMethodEnum.isLoanPay(PayMethodEnum.valueOf(orderPO.getPaymentCode()))) {
            if (LoanStatusEnum.DEAL_LENDING.isTrue(orderPO.getLoanPayState())) {
                throw new BusinessException("正在起息中，请稍后发起用呗退款！");
            }
            if (LoanStatusEnum.LENDING_FAIL.isTrue(orderPO.getLoanPayState())) {
                throw new BusinessException("起息失败，不可发起用呗退款！");
            }
        }
    }

    /**
     * 设置支付类型和退款类型
     */
    public AfsProductVO initPayMethodAndRefundType(AfsProductVO afsProductVO, OrderPO orderPO,
        OrderProductPO orderProductPO) {
        switch (PayMethodEnum.getValue(orderPO.getPaymentCode())) {
            case ENJOY_PAY:
                // 设置支付方式、退款方式、计算退款金额
                afsProductVO.setPaymentMethod(PayMethodEnum.ENJOY_PAY.getValue());
                afsProductVO.setRefundType(getRefundType(orderPO.getOrderSn()).getValue());
                break;
            case FOLLOW_HEART:
                // 设置支付方式、退款方式、计算退款金额
                afsProductVO.setPaymentMethod(PayMethodEnum.FOLLOW_HEART.getValue());
                afsProductVO.setRefundType(getRefundType(orderPO.getOrderSn()).getValue());
                break;
            case WXPAY:
                afsProductVO.setPaymentMethod(PayMethodEnum.WXPAY.getValue());
                afsProductVO.setRefundType(RefundType.WXPAY_REFUND.getValue());
                break;
            case ALIPAY:
                afsProductVO.setPaymentMethod(PayMethodEnum.ALIPAY.getValue());
                afsProductVO.setRefundType(RefundType.APLIPAY_REFUND.getValue());
                break;
            case CARD_VOUCHER:
                afsProductVO.setPaymentMethod(PayMethodEnum.CARD_VOUCHER.getValue());
                afsProductVO.setRefundType(RefundType.CARD_VOUCHER_REFUND.getValue());
                break;
            case BANK_PAY:
                afsProductVO.setPaymentMethod(PayMethodEnum.BANK_PAY.getValue());
                afsProductVO.setRefundType(RefundType.BANK_REFUND.getValue());
                break;
            case CARD:
                afsProductVO.setPaymentMethod(PayMethodEnum.CARD.getValue());
                afsProductVO.setRefundType(RefundType.CARD_REFUND.getValue());
                break;
            case BANK_TRANSFER:
                afsProductVO.setPaymentMethod(PayMethodEnum.BANK_TRANSFER.getValue());
                afsProductVO.setRefundType(RefundType.TRANSFER_REFUND.getValue());
                break;
            case COMBINATION_PAY://Andy.预售
                afsProductVO.setPaymentMethod(PayMethodEnum.COMBINATION_PAY.getValue());
                afsProductVO.setRefundType(RefundType.COMBINATION_REFUND.getValue());
                break;
            case AGREED_PAY://Andy.协议退款
                afsProductVO.setPaymentMethod(PayMethodEnum.AGREED_PAY.getValue());
                afsProductVO.setRefundType(RefundType.AGREED_REFUND.getValue());
                break;
            default:
                break;
        }
        if(ExchangeOrderConst.ORDER_EXCHANGE_FLAG_2 == orderPO.getExchangeFlag()) {
            afsProductVO.setRefundType(RefundType.ZERO_YUAN_REFUND.getValue());
        }
        log.info("----->orderSn:{} 退款支付方式，PaymentMethod:{}",orderPO.getOrderSn(),afsProductVO.getPaymentMethod());
        return afsProductVO;
    }


    public Result<CDMallRefundTryResultVO> getTryCaculateResult(BigDecimal returnMoneyAmount, String afsSn) {
        // 查询售后信息
        OrderAfterPO orderAfterServicePO = orderAfterServiceModel.getAfterServiceByAfsSn(afsSn);
        if (ObjectUtils.isEmpty(orderAfterServicePO)) {
            throw new MallException("退款单号不存在，请检查重试!");
        }

        // 不再处理0元结清的逻辑
        if (BigDecimal.ZERO.compareTo(returnMoneyAmount) == 0) {
            throw new MallException("因还款试算金额不足1元，系统无法进行在线退款，可返回调整”退款扣罚金额”。");
        }
        OrderRefundRecordPO  orderRefundRecordPO = orderRefundRecordService.queryLoanRefundByAfsSn(afsSn);

        // 试算
        CDMallRefundTryRequest cdMallRefundTryRequest = new CDMallRefundTryRequest();
        //Andy.预售。传递预售支付记录单号到信贷那边
        cdMallRefundTryRequest.setOrderBatchId(ObjectUtils.isEmpty(orderRefundRecordPO)?orderAfterServicePO.getOrderSn():orderRefundRecordPO.getPayNo());
        cdMallRefundTryRequest.setAmount(returnMoneyAmount);
        cdMallRefundTryRequest.setTryDate(DateUtil.format(new Date()));
        /*if (BigDecimal.ZERO.compareTo(returnMoneyAmount) == 0) {
            log.info("售后还款金额为0,结清处理,还款模式FS");
            cdMallRefundTryRequest.setRepayMode("FS");
        } else {*/
        cdMallRefundTryRequest.setRepayMode("");
        //}
        cdMallRefundTryRequest.setOperator("system");

        log.info("###-试算参数:{}", JSON.toJSONString(cdMallRefundTryRequest));

        return loanResultService.getTryCaculateResult(cdMallRefundTryRequest);
    }

    public Result<CDMallRefundTryResultVO> getTryCaculateResult(CDMallRefundTryRequest cdmallSetlTryRequest) {
        Result<CDMallRefundTryResultVO> setlTryResultVoResult;
        log.info("###试算参数：{}", JSON.toJSONString(cdmallSetlTryRequest));
        try {
            setlTryResultVoResult = mallPaymentFacade.paymentLoanSetlTry(cdmallSetlTryRequest);
        } catch (Exception ex) {
            throw new MallException(ex.getMessage());
        }
        if (setlTryResultVoResult == null) {
            throw new MallException("payment-service：试算异常", ErrorCodeEnum.C.CALL_EXCEPTION.getCode());
        }

        log.info("试算信贷日志cdmallSetlTryRequest:{} setlTryResultVoResult:{}",cdmallSetlTryRequest,setlTryResultVoResult);
        // 贷款金额/余额必须大于1元
        if ("002005099".equals(setlTryResultVoResult.getErrorCode())
        		|| "贷款金额/余额必须大于1元".equals(setlTryResultVoResult.getMessage().trim()) ||
        		"贷款金额/余额必须大于1元".contains(setlTryResultVoResult.getMessage().trim())) {
        	setlTryResultVoResult = new Result<>();
        	setlTryResultVoResult.addError("002005099", "贷款金额/余额必须大于1元", "贷款金额/余额必须大于1元");
        	return setlTryResultVoResult;
        }

        if (!setlTryResultVoResult.isSuccess()) {
            // 0024990099：借据已结清
            if ("0024990099".equals(setlTryResultVoResult.getErrorCode())) {
                return setlTryResultVoResult;
            }
            throw new MSBizNormalException(setlTryResultVoResult.getErrorCode(),
                "试算失败 core-loan-service:" + setlTryResultVoResult.getMessage());
        }


        if (setlTryResultVoResult.getData() == null) {
            throw new MallException("payment-service：试算异常", ErrorCodeEnum.C.CALL_EXCEPTION.getCode());
        }
        return setlTryResultVoResult;
    }

    /**
     * 构造售后货品信息列表，计算各个货品退款金额
     *
     * @param orderPO
     * @param orderAfterDTO
     * @return
     */
    public List<AfsProductVO> dealAfsProduct(OrderPO orderPO, OrderAfterDTO orderAfterDTO) {

//        if (!orderReturnValidation.checkOrderCommissionAmount(orderPO)) {
//            throw new BusinessException("系统处理中，请一分钟后再试~");
//        }

        List<AfsProductVO> vos = new ArrayList<>();

        BigDecimal maxReturnMoney = new BigDecimal("0.00");// 最大可退金额(不包含运费)
        boolean isAllReturned =
            orderAfterService.isAllReturnApplied(orderAfterDTO.getProductList(), orderAfterDTO.getOrderSn());

        for (int i = 0, s = orderAfterDTO.getProductList().size(); i < s; i++) {
            OrderAfterDTO.AfterProduct afterProduct = orderAfterDTO.getProductList().get(i);
            if (afterProduct.getAfsNum() == 0) {
                continue;
            }
            OrderProductPO orderProductPO = orderProductMapper.getByPrimaryKey(afterProduct.getOrderProductId());

            AfsProductVO vo = new AfsProductVO(orderProductPO);
            vo.setReturnBy(orderAfterDTO.getReturnBy());
            vo.setAfsNum(afterProduct.getAfsNum());
            // 设置支付方式、退款方式
            vo = initPayMethodAndRefundType(vo, orderPO, orderProductPO);

            // 退还优惠券
            OrderExtendExample extendExample = new OrderExtendExample();
            extendExample.setOrderSn(orderPO.getOrderSn());
            OrderExtendPO orderExtendPODb = orderExtendMapper.listByExample(extendExample).get(0);
            vo.setContactPhone(orderExtendPODb.getReceiverMobile());
            vo.setProductDeliveryState(orderProductPO.getDeliveryState().getValue());
            // 商品行全退
            if (afterProduct.getAfsNum().intValue() == orderProductPO.getProductNum()) {
                if(ExchangeOrderConst.ORDER_EXCHANGE_FLAG_2 == orderPO.getExchangeFlag()) {
                    vo.setReturnMoneyAmount(BigDecimal.ZERO);
                } else {
                    // 要退换的数量大于等于商品数量，默认计算所有商品数量
                    maxReturnMoney = maxReturnMoney.add(orderProductPO.getMoneyAmount());
                    vo.setReturnMoneyAmount(orderProductPO.getMoneyAmount());// 订单商品下单总金额
                    //Andy.预售需求增加，退定金金额设置，2022-05-25
                    if(OrderStatusEnum.isPresellDeposit(orderPO.getOrderState(),orderPO.getOrderType())){
                        vo.setReturnMoneyAmount(orderProductPO.getDeposit().multiply(BigDecimal.valueOf(orderProductPO.getProductNum())));//定金*数量=要退款的定金金额
                    }
                    if (OrderStatusEnum.isPresellDeposit(orderPO)) {
                        vo.setReturnMoneyAmount(orderProductPO.getDeposit().multiply(BigDecimal.valueOf(orderProductPO.getProductNum())));//定金*数量=要退款的定金金额
                    }
                    if (this.checkBalancePayMethod(orderPO)) {
                        vo.setReturnMoneyAmount(orderProductPO.getDeposit().multiply(BigDecimal.valueOf(orderProductPO.getProductNum())));//定金*数量=要退款的定金金额
                    }
                }
                vo.setActualReturnMoneyAmount(vo.getReturnMoneyAmount());// 订单商品下单总金额
                vo.setReturnIntegralAmount(orderProductPO.getIntegral());
                vo.setServiceFee(orderProductPO.getServiceFee());
                vo.setThirdpartnarFee(orderProductPO.getThirdpartnarFee());
                vo.setOrderCommission(orderProductPO.getOrderCommission());
                vo.setBusinessCommission(orderProductPO.getBusinessCommission());
                vo.setCommissionAmount(orderProductPO.getCommissionAmount());
                vo.setPlatformActivityAmount(orderProductPO.getPlatformActivityAmount());
                vo.setPlatformVoucherAmount(orderProductPO.getPlatformVoucherAmount());
                vo.setPlatformVoucherAmountPlt(refundCalculator.calculateRefundPlatformVoucherFunderAmount(afterProduct.getOrderProductId(), CouponFunder.PLATFORM));
                vo.setPlatformVoucherAmountStore(refundCalculator.calculateRefundPlatformVoucherFunderAmount(afterProduct.getOrderProductId(), CouponFunder.STORE));
                vo.setPlatformVoucherRetailAmountPlt(refundCalculator.calculateOrderProductPlatformVoucherRetailAmount(afterProduct.getOrderProductId(), CouponFunder.PLATFORM));
                vo.setPlatformVoucherRetailAmountStore(refundCalculator.calculateOrderProductPlatformVoucherRetailAmount(afterProduct.getOrderProductId(), CouponFunder.STORE));
                vo.setStoreActivityAmount(orderProductPO.getStoreActivityAmount());
                vo.setStoreVoucherAmount(orderProductPO.getStoreVoucherAmount());
                vo.setXzCardAmount(orderProductPO.getXzCardAmount());
                if (!StringUtil.isEmpty(orderExtendPODb.getVoucherCode())) {
                    vo.setReturnVoucherCode(orderExtendPODb.getVoucherCode());
                }
            }
            else if (afterProduct.getAfsNum() + orderProductPO.getReturnNumber() < orderProductPO.getProductNum()) {
                // 根据传来的数量计算要退的金额
                int actualNum = afterProduct.getAfsNum();
                BigDecimal returnGoodsPay = orderProductPO.getMoneyAmount().multiply(BigDecimal.valueOf(actualNum))
                        .divide(BigDecimal.valueOf(orderProductPO.getProductNum()), 2, RoundingMode.DOWN);// 舍去第三位小数
                vo.setReturnMoneyAmount(returnGoodsPay);// 订单商品单价*退货数量
                //Andy.预售需求增加，退定金金额设置，2022-05-25
                if (OrderStatusEnum.isPresellDeposit(orderPO.getOrderState(), orderPO.getOrderType())) {
                    vo.setReturnMoneyAmount(orderProductPO.getDeposit().multiply(BigDecimal.valueOf(orderProductPO.getProductNum())));//定金*数量=要退款的定金金额
                }
                if (OrderStatusEnum.isPresellDeposit(orderPO)) {
                    vo.setReturnMoneyAmount(orderProductPO.getDeposit().multiply(BigDecimal.valueOf(orderProductPO.getProductNum())));//定金*数量=要退款的定金金额
                }
                vo.setActualReturnMoneyAmount(vo.getReturnMoneyAmount());// 订单商品单价*退货数量

                Integer returnIntegral = orderProductPO.getIntegral() * actualNum / orderProductPO.getProductNum();// 只保留整数位
                BigDecimal serviceFee = orderProductPO.getServiceFee().multiply(BigDecimal.valueOf(actualNum))
                        .divide(BigDecimal.valueOf(orderProductPO.getProductNum()), 2, RoundingMode.HALF_UP);
                BigDecimal thirdpartnarFee = orderProductPO.getThirdpartnarFee().multiply(BigDecimal.valueOf(actualNum))
                        .divide(BigDecimal.valueOf(orderProductPO.getProductNum()), 2, RoundingMode.HALF_UP);
                BigDecimal orderCommission = orderProductPO.getOrderCommission().multiply(BigDecimal.valueOf(actualNum))
                        .divide(BigDecimal.valueOf(orderProductPO.getProductNum()), 2, RoundingMode.HALF_UP);
                BigDecimal businessCommission =
                        orderProductPO.getBusinessCommission().multiply(BigDecimal.valueOf(actualNum))
                                .divide(BigDecimal.valueOf(orderProductPO.getProductNum()), 2, RoundingMode.HALF_UP);
                BigDecimal platformVoucherAmount =
                        orderProductPO.getPlatformVoucherAmount().multiply(BigDecimal.valueOf(actualNum))
                                .divide(BigDecimal.valueOf(orderProductPO.getProductNum()), 2, RoundingMode.HALF_UP);
                BigDecimal platformVoucherAmountPlt =
                        refundCalculator.calculateRefundPlatformVoucherFunderAmount(afterProduct.getOrderProductId(), CouponFunder.PLATFORM)
                                .multiply(BigDecimal.valueOf(actualNum))
                                .divide(BigDecimal.valueOf(orderProductPO.getProductNum()), 2, RoundingMode.HALF_UP);
                BigDecimal platformVoucherAmountStore =
                        refundCalculator.calculateRefundPlatformVoucherFunderAmount(afterProduct.getOrderProductId(), CouponFunder.STORE)
                                .multiply(BigDecimal.valueOf(actualNum))
                                .divide(BigDecimal.valueOf(orderProductPO.getProductNum()), 2, RoundingMode.HALF_UP);
                BigDecimal platformVoucherRetailAmountPlt =
                        refundCalculator.calculateOrderProductPlatformVoucherRetailAmount(afterProduct.getOrderProductId(), CouponFunder.PLATFORM)
                        .multiply(BigDecimal.valueOf(actualNum))
                        .divide(BigDecimal.valueOf(orderProductPO.getProductNum()), 2, RoundingMode.HALF_UP);
                BigDecimal platformVoucherRetailAmountStore =
                        refundCalculator.calculateOrderProductPlatformVoucherRetailAmount(afterProduct.getOrderProductId(), CouponFunder.STORE)
                                .multiply(BigDecimal.valueOf(actualNum))
                                .divide(BigDecimal.valueOf(orderProductPO.getProductNum()), 2, RoundingMode.HALF_UP);
                BigDecimal platformActivityAmount =
                        orderProductPO.getPlatformActivityAmount().multiply(BigDecimal.valueOf(actualNum))
                                .divide(BigDecimal.valueOf(orderProductPO.getProductNum()), 2, RoundingMode.HALF_UP);
                BigDecimal storeActivityAmount =
                        orderProductPO.getStoreActivityAmount().multiply(BigDecimal.valueOf(actualNum))
                                .divide(BigDecimal.valueOf(orderProductPO.getProductNum()), 2, RoundingMode.HALF_UP);
                BigDecimal storeVoucherAmount =
                        orderProductPO.getStoreVoucherAmount().multiply(BigDecimal.valueOf(actualNum))
                                .divide(BigDecimal.valueOf(orderProductPO.getProductNum()), 2, RoundingMode.HALF_UP);
                BigDecimal xzCardAmount = orderProductPO.getXzCardAmount().multiply(BigDecimal.valueOf(actualNum))
                        .divide(BigDecimal.valueOf(orderProductPO.getProductNum()), 2, RoundingMode.HALF_UP);
                vo.setReturnIntegralAmount(returnIntegral);
                vo.setServiceFee(serviceFee);
                vo.setThirdpartnarFee(thirdpartnarFee);
                vo.setOrderCommission(orderCommission);
                vo.setXzCardAmount(xzCardAmount);
                vo.setBusinessCommission(businessCommission);
                vo.setCommissionAmount(orderCommission.add(businessCommission));
                vo.setPlatformVoucherAmount(platformVoucherAmount);
                vo.setPlatformVoucherAmountPlt(platformVoucherAmountPlt);
                vo.setPlatformVoucherAmountStore(platformVoucherAmountStore);
                vo.setPlatformVoucherRetailAmountPlt(platformVoucherRetailAmountPlt);
                vo.setPlatformVoucherRetailAmountStore(platformVoucherRetailAmountStore);
                vo.setPlatformActivityAmount(platformActivityAmount);
                vo.setStoreActivityAmount(storeActivityAmount);
                vo.setStoreVoucherAmount(storeVoucherAmount);
            }
            else {
                // 最后一次退款，进行钆差
                Map<String, BigDecimal> sumAmountByProduct = orderReturnService.getSumAmountByProductId(orderProductPO.getOrderProductId());
                BigDecimal returnMoneyAmount = orderProductPO.getMoneyAmount()
                        .subtract(sumAmountByProduct.get("returnMoneyAmount"));
                Integer returnIntegral = orderProductPO.getIntegral()
                        - sumAmountByProduct.get("returnIntegralAmount").intValue();
                BigDecimal serviceFee = orderProductPO.getServiceFee()
                        .subtract(sumAmountByProduct.get("serviceFee"));
                BigDecimal thirdpartnarFee = orderProductPO.getThirdpartnarFee()
                        .subtract(sumAmountByProduct.get("thirdpartnarFee"));
                BigDecimal xzCardAmount = orderProductPO.getXzCardAmount()
                        .subtract(sumAmountByProduct.get("xzCardAmount"));
                BigDecimal orderCommission = orderProductPO.getOrderCommission()
                        .subtract(sumAmountByProduct.get("orderCommission"));
                BigDecimal businessCommission = orderProductPO.getBusinessCommission()
                        .subtract(sumAmountByProduct.get("businessCommission"));
                BigDecimal platformVoucherAmountPlt = refundCalculator
                        .calculateRefundPlatformVoucherFunderAmount(afterProduct.getOrderProductId(), CouponFunder.PLATFORM)
                        .subtract(sumAmountByProduct.get("platformVoucherAmountPlt"));
                BigDecimal platformVoucherAmountStore = refundCalculator
                        .calculateRefundPlatformVoucherFunderAmount(afterProduct.getOrderProductId(), CouponFunder.STORE)
                        .subtract(sumAmountByProduct.get("platformVoucherAmountStore"));
                BigDecimal platformVoucherRetailAmountPlt = refundCalculator
                        .calculateOrderProductPlatformVoucherRetailAmount(afterProduct.getOrderProductId(), CouponFunder.PLATFORM)
                        .subtract(sumAmountByProduct.get("platformVoucherRetailAmountPlt"));
                BigDecimal platformVoucherRetailAmountStore = refundCalculator
                        .calculateOrderProductPlatformVoucherRetailAmount(afterProduct.getOrderProductId(), CouponFunder.STORE)
                        .subtract(sumAmountByProduct.get("platformVoucherRetailAmountStore"));
                BigDecimal platformVoucherAmount = orderProductPO.getPlatformVoucherAmount()
                        .subtract(sumAmountByProduct.get("platformVoucherAmount"));
                BigDecimal platformActivityAmount = orderProductPO.getPlatformActivityAmount()
                        .subtract(sumAmountByProduct.get("platformActivityAmount"));
                BigDecimal storeActivityAmount = orderProductPO.getStoreActivityAmount()
                        .subtract(sumAmountByProduct.get("storeActivityAmount"));
                BigDecimal storeVoucherAmount = orderProductPO.getStoreVoucherAmount()
                        .subtract(sumAmountByProduct.get("storeVoucherAmount"));

                vo.setReturnMoneyAmount(returnMoneyAmount);
                vo.setActualReturnMoneyAmount(vo.getReturnMoneyAmount());
                vo.setReturnIntegralAmount(returnIntegral);
                vo.setServiceFee(serviceFee);
                vo.setThirdpartnarFee(thirdpartnarFee);
                vo.setOrderCommission(orderCommission);
                vo.setXzCardAmount(xzCardAmount);
                vo.setBusinessCommission(businessCommission);
                vo.setCommissionAmount(orderCommission.add(businessCommission));
                vo.setPlatformVoucherAmount(platformVoucherAmount);
                vo.setPlatformVoucherAmountPlt(platformVoucherAmountPlt);
                vo.setPlatformVoucherAmountStore(platformVoucherAmountStore);
                vo.setPlatformVoucherRetailAmountPlt(platformVoucherRetailAmountPlt);
                vo.setPlatformVoucherRetailAmountStore(platformVoucherRetailAmountStore);
                vo.setPlatformActivityAmount(platformActivityAmount);
                vo.setStoreActivityAmount(storeActivityAmount);
                vo.setStoreVoucherAmount(storeVoucherAmount);
            }
            vos.add(vo);
        }

        // 运费处理：发货前，仅出现"整单"退款标记，才退还整单的运费；否则不退还运费，运费归于第一个退款商品上
        // Nmall-8776：恢复额度退款直接退运费，不考虑是否发货
        if ((orderPO.getOrderState().equals(OrderConst.ORDER_STATE_20) && isAllReturned)
                || RefundType.RESTORE_LIMIT == this.getRefundType(orderPO.getOrderSn())) {
            vos.get(0).setReturnExpressAmount(orderPO.getExpressFee());
            vos.get(0).setXzCardExpressFeeAmount(orderPO.getXzCardExpressFeeAmount());
            vos.get(0).setXzCardAmount(vos.get(0).getXzCardAmount().add(orderPO.getXzCardExpressFeeAmount()));
        }

        return vos;
    }


    /**
     *  判断尾款是不是银行卡汇款，汇款中，该情况下只退定金
     */
    private boolean checkBalancePayMethod(OrderPO orderPO) {
        if (Objects.equals(orderPO.getOrderType(), OrderTypeEnum.PRE_SELL_DEPOSIT.getValue())
                &&orderPO.getOrderState().equals(OrderStatusEnum.DEAL_PAY.getValue())) {
            List<OrderPayRecordPO> orderPayRecordPOS = orderPayRecordService.lambdaQuery()
                    .eq(OrderPayRecordPO::getPayOrder, PresellCapitalTypeEnum.BALANCE.getValue())
                    .eq(OrderPayRecordPO::getPaySn, orderPO.getPaySn())
                    .eq(OrderPayRecordPO::getPayStatus, CommonConst.PAY_STATUS_2)
                    .eq(OrderPayRecordPO::getPaymentCode, PayMethodEnum.BANK_TRANSFER.getValue())
                    .list();
            return !CollectionUtils.isEmpty(orderPayRecordPOS);
        }
        return false;
    }

    /**
     * 计算退订商品行分摊运费
     *
     * @param orderPO            订单信息
     * @param returnProductMoney 退款金额
     * @param refundType         退款类型
     * @return 分摊运费
     */
    private BigDecimal getExpressFee(OrderPO orderPO, BigDecimal returnProductMoney, RefundType refundType) {
        // 分摊运费
        if (orderPO.getExpressFee().compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }

        if (RefundType.RESTORE_LIMIT == refundType || OrderStatusEnum.WAIT_DELIVER.isTrue(orderPO.getOrderState())) {
            // 订单商品实付金额 (商品金额 - 优惠)
            BigDecimal oderGoodsPay = orderPO.getGoodsAmount().subtract(orderPO.getActivityDiscountAmount());
            // 实付金额为0，分摊全部运费
            if (oderGoodsPay.compareTo(BigDecimal.ZERO) == 0) {
                return orderPO.getExpressFee();
            }
            return returnProductMoney.divide(oderGoodsPay, 2, RoundingMode.DOWN).multiply(orderPO.getExpressFee());
        }

        return BigDecimal.ZERO;
    }

    @GlobalTransactional
    public void refundApply2Payment(OrderPO orderPO, OrderReturnPO orderAfterServicePO,
        BigDecimal actualReturnMoneyAmount, Admin admin) {
        // 校验支付方式为ENJOY_PAY 未放款->恢复额度  已放款->代还退款
        PaymentRefundRequest refundRequest = new PaymentRefundRequest();

        // 获取退款类型
        RefundType refundType = orderAfterServiceModel.getRefundType(orderPO.getOrderSn());
        if (refundType == RefundType.RESTORE_LIMIT) {
            // 恢复额度退款操作之前，实时查询放款状态，如果放款成功，则走代还退款
            OrderExample example = new OrderExample();
            example.setOrderSn(orderPO.getOrderSn());
            OrderPO orderPOLatest = orderMapper.listByExample(example).get(0);
            if (orderPOLatest.getLoanPayState().equals(LoanStatusEnum.LENDING_SUCCESS.getValue())) {
                orderAfterServiceModel.refundApply2Payment(orderPOLatest, orderAfterServicePO, actualReturnMoneyAmount,
                    admin);
            }
            // 恢复额度 1.调用CMIS 恢复用户额度 2.作废贷款申请单
            refundRequest.setRefundOn(orderPO.getOrderSn());
            refundRequest.setRefundAmt(orderPO.getOrderAmount());
            refundRequest.setRefundReason("恢复额度：整单取消");
            refundRequest.setNotifyUrl(refundNotifyUrl);
            // 构造信贷渠道取消参数
            CancelCdMallApplyRequest cancelCdMallApplyRequest = buildCancelCdMallApplyRequest(orderPO, refundRequest);
            refundRequest.setLoanCancel(cancelCdMallApplyRequest);
        }

        if (refundType == RefundType.ASSIST_PAYMENT && !orderPO.getNewOrder()) {
            // 构造信贷渠道退款参数
            ReturnCdMallOrderRequest returnCdMallOrderRequest =
                buildReturnCdMallOrderRequest(orderPO, refundRequest, orderAfterServicePO.getAfsSn(), admin);
            returnCdMallOrderRequest.setReturnAmount(actualReturnMoneyAmount);
            refundRequest.setRefundOn(orderAfterServicePO.getAfsSn());
            refundRequest.setRefundAmt(actualReturnMoneyAmount);
            refundRequest.setRefundReason("代还退款");
            refundRequest.setNotifyUrl(refundNotifyUrl);
            refundRequest.setLoanReturn(returnCdMallOrderRequest);

            // 补充代还退款备注信息
            this.recordAssistPaymentRefundMsg(orderPO, orderAfterServicePO);
        }
        // 调用支付服务进行退款
        payIntegration.loanRefundByPayment(orderPO, refundRequest, orderAfterServicePO.getAfsSn());
    }

    /**
     * @return boolean
     * @description :结算平台退款消息
     */
    @GlobalTransactional
    public boolean loanRefundResult(TransferRefundMsg refundMsg) {
        if (CollectionUtils.isEmpty(refundMsg.getList())) {
            throw new MallException("结算平台退款消息为空");
        }
        TransferRefundDetailMsg transferRefundMsg = refundMsg.getList().get(0);
        if (!transferRefundMsg.getRefundStatus().equals(OrderReturnStatus.REFUND_SUCCESS.getValue())) {
            throw new MallException("结算返回退款状态未成功，暂不处理：{}", transferRefundMsg.getAfsSn());
        }

        if (Objects.nonNull(transferRefundMsg.getSystemCode()) && !"standard-mall".equals(transferRefundMsg.getSystemCode())) {
            log.info("收到非电商平台的下账回调结果,afsSn:{}",transferRefundMsg.getAfsSn());
            return true;
        }

        String afsSn = transferRefundMsg.getAfsSn();
        BizAssertUtil.notNull(afsSn, "退款单号不能为空");

        //获取售后服务信息
        OrderAfterPO orderAfterServicePO = orderReturnModel.getOrderAfterServiceByAfsSn(afsSn);
        AssertUtil.notNull(orderAfterServicePO, "消费结算平台时退款单不存在:" + afsSn);

        // 查询订单信息
        OrderPO orderPO = orderModel.getOrderByOrderSn(orderAfterServicePO.getOrderSn());
        AssertUtil.notNull(orderPO, "消费结算平台时订单信息不存在:" + orderAfterServicePO.getOrderSn());

        // 获取退货信息
        OrderReturnPO orderReturnPODb = orderReturnService.getByAfsSn(afsSn);
        AssertUtil.notNull(orderReturnPODb, "消费结算平台时退货信息不存在:" + afsSn);

        if (orderReturnPODb.getState().equals(OrderReturnStatus.REFUND_SUCCESS.getValue())) {
        	log.warn("退款单状态400无需重复退款，afsSn：{} refundNo:{} state:{}",afsSn,transferRefundMsg.getBatchNo(),orderReturnPODb.getState());
            return true;
        }
        
        //退款发起前，更新退款备注
        orderAfterServiceModel.updateRefundFailReasonByAfsSn(orderReturnPODb.getAfsSn(), "无");

        //如果是预售订单，退款退款走新的流程，Andy.2022.06.21
        if(OrderTypeEnum.isPresell(orderPO.getOrderType())) {
        	 Admin admin =new Admin();
             admin.setAdminId(orderPO.getMemberId());
             admin.setAdminName(orderPO.getMemberName());
             admin.setPhone(orderPO.getUserMobile());
             log.info("开始预付订单下账成功，开始退款，afsSn：{} refundNo:{}",afsSn,transferRefundMsg.getBatchNo());
             presellRefundService.refund(transferRefundMsg.getRefundAmount(),orderReturnPODb, orderAfterServicePO ,transferRefundMsg.getBatchNo(),admin,transferRefundMsg.getPaymentId());
             return true;
        }


        AssertUtil.isTrue(orderReturnPODb.getActualReturnMoneyAmount().add(orderReturnPODb.getReturnExpressAmount())
            .compareTo(transferRefundMsg.getRefundAmount()) != 0, "退款金额不一致");

        //线下退款，不需要调用退款接口
        if(RefundType.OFFLINE_REFUND == RefundType.value(orderReturnPODb.getRefundType())
            || RefundType.SQUARE_OFFLINE_REFUND == RefundType.value(orderReturnPODb.getRefundType())
        ) {
            orderReturnModel.refundSuccess(null, afsSn);
            return Boolean.TRUE;
        }

        Map<String, Object> map = new HashMap<>();
        map.put("paymentId", transferRefundMsg.getPaymentId());
        PayMethodEnum paymentCode = PayMethodEnum.getValue(orderPO.getPaymentCode());
        if (Objects.isNull(paymentCode)) {
            throw new MallException("未匹配的支付方式");
        }
        return iPayHandleService.paymentRefund(orderPO, orderReturnPODb, map, paymentCode.getValue(), null);
    }


    /**
     * 记录代还退款的业务备注信息
     *
     * @param orderPO
     *            订单信息
     * @param orderAfterServicePO
     *            退款单信息
     */
    public void recordAssistPaymentRefundMsg(OrderPO orderPO, OrderReturnPO orderAfterServicePO) {
        LambdaUpdateWrapper<OrderReturnPO> orderReturnUpdate = Wrappers.lambdaUpdate(OrderReturnPO.class);
        orderReturnUpdate.eq(OrderReturnPO::getAfsSn, orderAfterServicePO.getAfsSn())
            .set(OrderReturnPO::getBusinessType,
                OrderConst.STORE_TYPE_SELF_1.equals(orderPO.getStoreIsSelf()) ? "自营代还退款" : "引荐代还退款")
            .set(OrderReturnPO::getBusinessDescription,
                orderAfterServicePO.getStoreId() + "-" + orderAfterServicePO.getStoreName());
        orderReturnService.update(orderReturnUpdate);
    }


    public ReturnCdMallOrderRequest buildReturnCdMallOrderRequest(OrderPO orderPO, PaymentRefundRequest refundRequest, String afsSn, Admin admin) {
        ReturnCdMallOrderRequest returnCdMallOrderRequest = new ReturnCdMallOrderRequest();

        refundRequest.setOrderOn(orderPO.getOrderSn());
        refundRequest.setSystemCode(PayIntegration.SYSTEM_CODE);
        refundRequest.setRefundCreateTime(System.currentTimeMillis());
        // 构造信贷渠道退款参数
        returnCdMallOrderRequest.setTradNo(afsSn);
        returnCdMallOrderRequest.setMerchantId(orderPO.getStoreId() + "");
        returnCdMallOrderRequest.setOperator(orderPO.getUserNo());
        returnCdMallOrderRequest.setOperatorName(orderPO.getMemberName());
        // 自营商户自行代还退款时，从自营商户虚拟账户直接出资；入驻商户申请代还退款时，从引荐商户虚拟账户直接出资
        Long payStoreId = OrderConst.STORE_TYPE_SELF_1.equals(orderPO.getStoreIsSelf()) ? orderPO.getStoreId()
            : orderPO.getRecommendStoreId();
        // 从客户中心获取 custId、branchId
        StoreContractReceiptInfoVO storeContract = storeFeignClient.getStoreContractReciptInfo(payStoreId);
        returnCdMallOrderRequest.setMerchantAcctNo(storeContract.getStore().getVirtualAccount());
        returnCdMallOrderRequest.setOrderBatchId(orderPO.getOrderSn());
        returnCdMallOrderRequest.setReturnDate(DateUtil.format(new Date()));
        returnCdMallOrderRequest.setOperator(Optional.ofNullable(admin.getPhone()).orElse(CommonConst.ADMIN_PHONE));
        returnCdMallOrderRequest
            .setOperatorName(Optional.ofNullable(admin.getPhone()).orElse(CommonConst.ADMIN_NAME_EN));
        returnCdMallOrderRequest.setOperatorBranch(CommonConst.ADMIN_BRANCH);
        return returnCdMallOrderRequest;
    }

    private CancelCdMallApplyRequest buildCancelCdMallApplyRequest(OrderPO orderPO,
        PaymentRefundRequest refundRequest) {
        refundRequest.setOrderOn(orderPO.getOrderSn());
        refundRequest.setSystemCode("mallOrder");
        refundRequest.setRefundCreateTime(System.currentTimeMillis());

        CancelCdMallApplyRequest cancelCdMallApplyRequest = new CancelCdMallApplyRequest();
        cancelCdMallApplyRequest.setOrderBatchId(orderPO.getOrderSn());
        cancelCdMallApplyRequest.setChannel("CAPP");
        cancelCdMallApplyRequest.setOperator(OrderConst.LOG_USER_NAME);
        cancelCdMallApplyRequest.setReason("协调退款");
        // 构造信贷渠道退款参数
        // 从客户中心获取 custId、branchId
        CustDetailVo custDetail = null;
        try {
            Member member = memberFeignClient.getMemberByMemberId(orderPO.getMemberId());
            QueryUserBaseInfoReq queryUserBaseInfoReq = new QueryUserBaseInfoReq();
            queryUserBaseInfoReq.setUserNo(member.getUserNo());
            Result<UserBaseInfoVo> userBaseInfoVoResult = customerServiceFeign.baseInfoByUserNo(queryUserBaseInfoReq);
            UserBaseInfoVo data = userBaseInfoVoResult.getData();
            if (data.getCustInfoVo() != null && data.getCustInfoVo().getCustDetail() != null) {
                custDetail = data.getCustInfoVo().getCustDetail();
                cancelCdMallApplyRequest.setOperator(custDetail.getLoanCustId());
                cancelCdMallApplyRequest.setLoanCustId(custDetail.getLoanCustId());
            }
        } catch (Exception e) {
            log.error("调用客户中心异常", e);
            throw new MallException("服务内部调用超时，请重试！");
        }
        List<OrderRefundRequest> refundReqList = new ArrayList<>();
        // 查询订单号下对应的所有有效退款单（平台审核通过）
        LambdaQueryWrapper<OrderReturnPO> orderReturnQueryWrapper = Wrappers.lambdaQuery(OrderReturnPO.class);
        orderReturnQueryWrapper.eq(OrderReturnPO::getOrderSn, orderPO.getOrderSn()).in(OrderReturnPO::getState,
            OrderReturnStatus.PLATFORM_AGREE.getValue());
        List<OrderReturnPO> orderReturns = orderReturnService.list(orderReturnQueryWrapper);
        for (int i = 0; i < orderReturns.size(); i++) {
            OrderRefundRequest orderRefundRequest = new OrderRefundRequest();
            orderRefundRequest.setLoanCustId(custDetail.getLoanBranch());
            orderRefundRequest.setOrderBatchId(orderPO.getOrderSn());
            orderRefundRequest.setOperator(custDetail.getLoanCustId());
            orderRefundRequest.setOrderDetailId(orderPO.getOrderSn());
            orderRefundRequest.setRefundTransactionId(orderReturns.get(i).getAfsSn());
            // 取明细金额，外面会设置退款总金额refundAmt
            OrderReturnPO orderReturnPOByAfsSn = orderReturnModel.getOrderReturnByAfsSn(orderReturns.get(i).getAfsSn());
            orderRefundRequest.setRefundAmount(
                orderReturnPOByAfsSn.getReturnMoneyAmount().add(orderReturnPOByAfsSn.getReturnExpressAmount()));
            // 查询审核通过时的备注信息
            OrderAfterPO OrderAfterPo =
                orderAfterService.lambdaQuery().eq(OrderAfterPO::getAfsSn, orderReturns.get(i).getAfsSn())
                    .eq(OrderAfterPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y).last("limit 1").one();
            String remark =
                StringUtils.isEmpty(OrderAfterPo.getPlatformRemark()) ? "同意" : OrderAfterPo.getPlatformRemark();
            orderRefundRequest.setRemark(remark);
            orderRefundRequest.setOrderDetailAmount(orderPO.getOrderAmount());
            refundReqList.add(orderRefundRequest);
        }
        cancelCdMallApplyRequest.setRefundReqList(refundReqList);
        return cancelCdMallApplyRequest;
    }

    /**
     * 保存售后信息
     *
     * @param afsSn
     * @param afsProductVO
     * @param orderAfterDTO
     * @param orderPO
     */
    public void insertAfterService(String afsSn, AfsProductVO afsProductVO, OrderAfterDTO orderAfterDTO,
        OrderPO orderPO) {

        // 保存afsSn 与 afsProductVO的关系
        OrderAfterPO orderAfterServicePO = new OrderAfterPO();
        orderAfterServicePO.setAfsSn(afsSn);
        orderAfterServicePO.setStoreId(orderPO.getStoreId());
        orderAfterServicePO.setStoreName(orderPO.getStoreName());
        orderAfterServicePO.setOrderSn(orderAfterDTO.getOrderSn());
        orderAfterServicePO.setMemberId(orderPO.getMemberId());
        orderAfterServicePO.setMemberName(orderPO.getMemberName());
        orderAfterServicePO.setGoodsId(afsProductVO.getGoodsId());
        orderAfterServicePO.setOrderProductId(afsProductVO.getOrderProductId());
        orderAfterServicePO.setAfsNum(afsProductVO.getAfsNum());
        orderAfterServicePO.setApplyImage(orderAfterDTO.getApplyImage());
        if (!StringUtils.isEmpty(orderAfterDTO.getContactName())) {
            orderAfterServicePO.setContactName(orderAfterDTO.getContactName());
        } else {
            orderAfterServicePO.setContactName(orderPO.getMemberName());
        }
        orderAfterServicePO.setContactPhone(afsProductVO.getContactPhone());
        orderAfterServicePO.setAfsType(orderAfterDTO.getAfsType());
        orderAfterServicePO.setApplyReasonContent(orderAfterDTO.getApplyReasonContent());
        orderAfterServicePO.setAfsDescription(orderAfterDTO.getAfsDescription());
        orderAfterServicePO.setBuyerApplyTime(new Date());
        orderAfterServicePO.setGoodsState(orderAfterDTO.getGoodsState());
        orderAfterServicePO.setStoreIsSelf(orderPO.getStoreIsSelf());
        orderAfterServicePO.setPerformanceMode(afsProductVO.getPerformanceMode());
        orderAfterServicePO.setProductDeliveryState(afsProductVO.getProductDeliveryState());
        orderAfterServicePO.setSnapshotInformation(
            StringUtils.isEmpty(JSON.toJSONString(orderAfterDTO)) ? "" : JSON.toJSONString(orderAfterDTO));
        int result = orderAfterMapper.insert(orderAfterServicePO);
        AssertUtil.isTrue(result == 0, "保存售后工单信息失败");
    }

    /**
     * 保存退换货信息和售后日志
     *
     * @param afsSn
     * @param orderAfterDTO
     * @param afsProductVO
     * @param orderPO
     */
    public OrderReturnPO insertReturnOrder(String afsSn, OrderAfterDTO orderAfterDTO, AfsProductVO afsProductVO, OrderPO orderPO) {
        // 仅退款或退货退款
        OrderReturnPO orderReturnPO = new OrderReturnPO();
        orderReturnPO.setAfsSn(afsSn);
        orderReturnPO.setOrderSn(orderPO.getOrderSn());
        orderReturnPO.setReturnNum(afsProductVO.getAfsNum());
        orderReturnPO.setStoreId(orderPO.getStoreId());
        orderReturnPO.setStoreIsSelf(orderPO.getStoreIsSelf());
        orderReturnPO.setStoreName(orderPO.getStoreName());
        orderReturnPO.setMemberId(orderPO.getMemberId());
        orderReturnPO.setMemberName(orderPO.getMemberName());
        // 退款方式取系统设置
        orderReturnPO
            .setReturnMoneyType(Integer.valueOf(stringRedisTemplate.opsForValue().get("refund_setting_switch")));
        orderReturnPO.setReturnIntegralAmount(afsProductVO.getReturnIntegralAmount());
        orderReturnPO.setDeductIntegralAmount(afsProductVO.getDeductIntegralAmount());
        orderReturnPO.setReturnMoneyAmount(afsProductVO.getReturnMoneyAmount());
        orderReturnPO.setReturnExpressAmount(BigDecimal.ZERO);
        if(Objects.nonNull(afsProductVO.getReturnExpressAmount())){
            orderReturnPO.setReturnExpressAmount(afsProductVO.getReturnExpressAmount());
        }
        orderReturnPO.setRefundApplySumAmount(orderReturnPO.getReturnMoneyAmount().add(orderReturnPO.getReturnExpressAmount()));
        orderReturnPO.setReturnVoucherCode(afsProductVO.getReturnVoucherCode());
        orderReturnPO.setCommissionRate(afsProductVO.getCommissionRate());
        orderReturnPO.setCommissionAmount(afsProductVO.getCommissionAmount());
        orderReturnPO.setPlatformActivityAmount(afsProductVO.getPlatformActivityAmount());
        orderReturnPO.setPlatformVoucherAmount(afsProductVO.getPlatformVoucherAmount());
        orderReturnPO.setPlatformVoucherAmountPlt(afsProductVO.getPlatformVoucherAmountPlt());
        orderReturnPO.setPlatformVoucherAmountStore(afsProductVO.getPlatformVoucherAmountStore());
        orderReturnPO.setPlatformVoucherRetailAmountPlt(afsProductVO.getPlatformVoucherRetailAmountPlt());
        orderReturnPO.setPlatformVoucherRetailAmountStore(afsProductVO.getPlatformVoucherRetailAmountStore());
        orderReturnPO.setStoreActivityAmount(afsProductVO.getStoreActivityAmount());
        orderReturnPO.setStoreVoucherAmount(afsProductVO.getStoreVoucherAmount());
        orderReturnPO.setXzCardAmount(afsProductVO.getXzCardAmount());
        orderReturnPO.setXzCardExpressFeeAmount(afsProductVO.getXzCardExpressFeeAmount());
        orderReturnPO.setActualReturnMoneyAmount(afsProductVO.getActualReturnMoneyAmount());
        orderReturnPO.setCustomerAssumeAmount(BigDecimal.ZERO);
        orderReturnPO.setOtherCompensationAmount(BigDecimal.ZERO);
        orderReturnPO.setPaymentMethod(afsProductVO.getPaymentMethod());
        orderReturnPO.setRefundType(afsProductVO.getRefundType());
        orderReturnPO.setRefundStartTime(new Date());
        int days = 0;
        RefundType refundType = getRefundType(orderPO.getOrderSn());
        // 代还设置利息承担方，预付订金。
        if (RefundType.ASSIST_PAYMENT.equals(refundType)) {
            try {
                days = DateUtil.days(DateUtil.format(orderPO.getLendingSuccessTime(), DateUtil.FORMAT_TIME),
                    DateUtil.format(new Date(), DateUtil.FORMAT_TIME), DateUtil.FORMAT_TIME);
                orderReturnPO.setInterestPayer(
                    days < 7 ? InterestPayerEnum.PLATFORM.getDesc() : InterestPayerEnum.STORE.getDesc());
            } catch (Exception e) {
                throw new MallException("还没有放款成功，请稍后重试！");
            }
        }

        orderReturnPO.setRemark(afsProductVO.getRemark());
        orderReturnPO.setServiceFee(afsProductVO.getServiceFee());
        orderReturnPO.setThirdpartnarFee(afsProductVO.getThirdpartnarFee());
        orderReturnPO.setOrderCommission(afsProductVO.getOrderCommission());
        orderReturnPO.setBusinessCommission(afsProductVO.getBusinessCommission());
        orderReturnPO.setApplyTime(new Date());
        //设置退款状态
        this.handleReturnStateAndType(orderReturnPO, afsProductVO.getReturnBy(), orderAfterDTO.getAfsType(), orderPO);
        //Andy.预售需求增加，退定金金额设置，2022-06-13
        if(OrderStatusEnum.isPresellDeposit(orderPO.getOrderState(),orderPO.getOrderType()) || OrderStatusEnum.isPresellDeposit(orderPO)){
            // 乡助卡实际退款金额
            orderReturnPO.setXzCardAmount(BigDecimal.ZERO);
            // 退还平台优惠金额
            orderReturnPO.setPlatformActivityAmount(BigDecimal.ZERO);
            orderReturnPO.setPlatformVoucherAmount(BigDecimal.ZERO);
            orderReturnPO.setPlatformVoucherAmountPlt(BigDecimal.ZERO);
            orderReturnPO.setPlatformVoucherAmountStore(BigDecimal.ZERO);
            orderReturnPO.setPlatformVoucherRetailAmountPlt(BigDecimal.ZERO);
            orderReturnPO.setPlatformVoucherRetailAmountStore(BigDecimal.ZERO);
            // 退还店铺优惠金额
            orderReturnPO.setStoreVoucherAmount(BigDecimal.ZERO);
            orderReturnPO.setStoreActivityAmount(BigDecimal.ZERO);
            // 退还平台服务费
            orderReturnPO.setServiceFee(BigDecimal.ZERO);
            // 退还代运营服务费
            orderReturnPO.setThirdpartnarFee(BigDecimal.ZERO);
            // 退还订单佣金
            orderReturnPO.setBusinessCommission(BigDecimal.ZERO);
            orderReturnPO.setOrderCommission(BigDecimal.ZERO);
            // 退还其他赔偿：代还退款 && 7日内退款 平台承担
            orderReturnPO.setOtherCompensationAmount(BigDecimal.ZERO);
            //客户承担金额
            orderReturnPO.setCustomerAssumeAmount(BigDecimal.ZERO);
         }
        int count = orderReturnMapper.insert(orderReturnPO);
        AssertUtil.isTrue(count == 0, "保存退货信息失败");

        BigDecimal channelServiceFee;
        // 实际退款金额
        BigDecimal totalRefund = orderReturnPO.getActualReturnMoneyAmount().add(orderReturnPO.getReturnExpressAmount());
        if (OrdersAfsConst.RETURN_TYPE_3 != orderReturnPO.getReturnType()
                && orderReturnService.isLastReturn(orderReturnPO.getAfsSn())) {
            // 查出【平台审批完成】和【退款完成】的退订单
            LambdaQueryWrapper<OrderReturnPO> orderReturnListQuery = new LambdaQueryWrapper<>();
            orderReturnListQuery.eq(OrderReturnPO::getOrderSn, orderPO.getOrderSn());
            orderReturnListQuery.eq(OrderReturnPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
            orderReturnListQuery.ne(OrderReturnPO::getAfsSn, orderReturnPO.getAfsSn());
            orderReturnListQuery.in(OrderReturnPO::getState, Arrays.asList(OrderReturnStatus.PLATFORM_AGREE.getValue(),
                    OrderReturnStatus.REFUND_SUCCESS.getValue()));
            List<OrderReturnPO> orderReturnPOS = orderReturnMapper.selectList(orderReturnListQuery);
            BigDecimal alRefundchannelServiceFee = orderReturnPOS.stream().map(OrderReturnPO::getChannelServiceFee).reduce(BigDecimal.ZERO, BigDecimal::add);
            channelServiceFee = orderPO.getChannelServiceFee().subtract(alRefundchannelServiceFee);
            // 非最后一单退则采用退单金额*手续费率
        } else {
            if (OrderTypeEnum.isPresell(orderPO.getOrderType())) {//Andy.预付
                channelServiceFee = BigDecimal.ZERO;
            } else {
                BillAccountTypeEnum accountEnum;
                if (null == orderPO.getNewOrder() || !orderPO.getNewOrder()) {
                    accountEnum = BillAccountTypeEnum.valueByPay(orderPO.getPaymentCode());
                } else {
                    accountEnum = BillAccountTypeEnum.payment2AccountType(orderPO.getPaymentCode());
                }
                String rate = channelFeeRateConfig.getMappedRate().get(accountEnum.getValue() + "-" + orderPO.getPaymentCode());
                BigDecimal thousandth = new BigDecimal(rate);
                channelServiceFee = totalRefund.multiply(thousandth).setScale(2, RoundingMode.HALF_UP);
            }
        }
        // 当实际退款金额是0的时候，渠道手续费也退0
        if (totalRefund.compareTo(BigDecimal.ZERO) == 0) {
            channelServiceFee = BigDecimal.ZERO;
        }
        LambdaUpdateWrapper<OrderReturnPO> updateWrapper = new LambdaUpdateWrapper<OrderReturnPO>();
        updateWrapper.set(OrderReturnPO::getChannelServiceFee,channelServiceFee);
        updateWrapper.eq(OrderReturnPO::getAfsSn,orderReturnPO.getAfsSn());
        if (!orderReturnService.update(updateWrapper)) {
            throw new MallException("修改渠道手续费失败,afsSn:"+orderReturnPO.getAfsSn());
        }
        this.insertAfsLog(afsSn, orderAfterDTO, orderPO, afsProductVO.getReturnBy());

        log.info("----->orderSn:{} 退款支付方式，orderReturnPO.PaymentMethod:{} PaymentMethod2:{}",orderPO.getOrderSn(),
        		orderReturnPO.getPaymentMethod(),afsProductVO.getPaymentMethod());
        return  orderReturnPO;
    }

    /**
     * 设置退款发起者和退款状态
     *
     * @param orderReturnPO
     *            退款单信息
     * @param returnBy
     *            退款发起者
     * @param afsType
     *            售后服务端类型
     * @param orderPO
     *             订单信息
     *
     */
    private void handleReturnStateAndType(OrderReturnPO orderReturnPO, Integer returnBy, Integer afsType, OrderPO orderPO) {
        orderReturnPO.setReturnBy(returnBy);
        // 自动取消、商家取消，直达平台审核
        if (returnBy != null && returnBy != OrderConst.RETURN_BY_1 && returnBy != OrderConst.RETURN_BY_4) {
            // 拼团自动退款，直达平台审批通过
            if (returnBy == OrderConst.RETURN_BY_0 && orderPO.getOrderType() == OrderTypeEnum.SPELL_GROUP.getValue()) {
                orderReturnPO.setState(OrdersAfsConst.RETURN_STATE_300);
                orderReturnPO.setReturnType(OrdersAfsConst.RETURN_TYPE_1);
            // 预售自动退定金，到商家审核通过，再自动调用平台审核通过
            } else if (returnBy == OrderConst.RETURN_BY_0 && orderPO.getOrderType() == OrderTypeEnum.PRE_SELL_DEPOSIT.getValue()) {
                orderReturnPO.setState(OrdersAfsConst.RETURN_STATE_200);
                orderReturnPO.setReturnType(OrdersAfsConst.RETURN_TYPE_1);
            } else if (returnBy == OrderConst.RETURN_BY_2) {
                //指定店铺&&erp出库商品不允许商家取消订单不允许自动审批
                if(isAllowAutoApprove(orderReturnPO.getAfsSn(),afsType)) {
                    orderReturnPO.setState(OrdersAfsConst.RETURN_STATE_200);
                    orderReturnPO.setReturnType(OrdersAfsConst.RETURN_TYPE_1);
                } else {
                    orderReturnPO.setState(OrdersAfsConst.RETURN_STATE_100);
                    orderReturnPO.setReturnType(OrdersAfsConst.RETURN_TYPE_1);
                }
                if (afsType == OrdersAfsConst.AFS_TYPE_REPLACEMENT) {
                    orderReturnPO.setState(OrdersAfsConst.RETURN_STATE_200);
                    orderReturnPO.setReturnType(OrdersAfsConst.RETURN_TYPE_3);
                }

            } else if (returnBy == OrderConst.RETURN_BY_3) {
                orderReturnPO.setState(OrdersAfsConst.RETURN_STATE_200);
                orderReturnPO.setReturnType(OrdersAfsConst.RETURN_TYPE_1);
            }else if (returnBy == OrderConst.RETURN_BY_6){
                // 返利中心发起
                if (afsType == OrdersAfsConst.AFS_TYPE_RETURN) {
                    orderReturnPO.setState(OrdersAfsConst.RETURN_STATE_101);
                    orderReturnPO.setReturnType(OrdersAfsConst.RETURN_TYPE_2);
                } else if (afsType == OrdersAfsConst.AFS_TYPE_REFUND) {
                    orderReturnPO.setState(OrdersAfsConst.RETURN_STATE_100);
                    orderReturnPO.setReturnType(OrdersAfsConst.RETURN_TYPE_1);
                }
            } else if (returnBy == OrderConst.RETURN_BY_5) {
                if (afsType == OrdersAfsConst.AFS_TYPE_RETURN) {
                    orderReturnPO.setState(OrdersAfsConst.RETURN_STATE_101);
                    orderReturnPO.setReturnType(OrdersAfsConst.RETURN_TYPE_2);
                } else if (afsType == OrdersAfsConst.AFS_TYPE_REFUND) {
                    orderReturnPO.setState(OrdersAfsConst.RETURN_STATE_100);
                    orderReturnPO.setReturnType(OrdersAfsConst.RETURN_TYPE_1);
                }
            }
        } else if (afsType == OrdersAfsConst.AFS_TYPE_REFUND) {
            orderReturnPO.setState(OrdersAfsConst.RETURN_STATE_100);
            orderReturnPO.setReturnType(OrdersAfsConst.RETURN_TYPE_1);
        } else if (afsType == OrdersAfsConst.AFS_TYPE_RETURN) {
            // 如果是生服店铺自提订单的退货退款，走独立流程
            if (OrderPatternEnum.SELF_LIFT.getValue().equals(orderPO.getOrderPattern())
                    && storeIsolateWhiteListFeignClient.isStoreOnList(WhiteListEnum.DING_DING_REFUND_WHITE_LIST, orderPO.getStoreId())) {
                orderReturnPO.setState(OrderReturnStatus.SELF_PICKUP_POINT_RETURN_APPLY.getValue());
            } else {
                orderReturnPO.setState(OrdersAfsConst.RETURN_STATE_101);
            }
            orderReturnPO.setReturnType(OrdersAfsConst.RETURN_TYPE_2);
        } else if (afsType == OrdersAfsConst.AFS_TYPE_REPLACEMENT) {
            orderReturnPO.setState(OrdersAfsConst.RETURN_STATE_100);
            orderReturnPO.setReturnType(OrdersAfsConst.RETURN_TYPE_3);
        }
    }

    private void insertAfsLog(String afsSn, OrderAfterDTO orderAfterDTO, OrderPO orderPO, Integer returnBy) {
        // 退款类型
        Integer afsType = orderAfterDTO.getAfsType();
        // 记录售后日志
        OrderAfterSaleLogPO afterSaleLog = new OrderAfterSaleLogPO();
        if (ReturnByEnum.isValet(returnBy)) {
            // 站长和客户经理代客售后，日志记录站长和客户经理信息
            afterSaleLog.setLogRole(OperationRoleEnum.getValueByReturnBy(returnBy));
            afterSaleLog.setLogUserId(orderAfterDTO.getOperatorUserId());
            String logUserName = StringUtils.isEmpty(orderAfterDTO.getContactName()) ? orderPO.getMemberName() : orderAfterDTO.getContactName();
            afterSaleLog.setLogUserName(logUserName);
        } else {
            afterSaleLog.setLogRole(OrderConst.LOG_ROLE_MEMBER);
            afterSaleLog.setLogUserId(Long.parseLong(String.valueOf(orderPO.getMemberId())));
            afterSaleLog.setLogUserName(orderPO.getMemberName());
        }
        afterSaleLog.setAfsSn(afsSn);
        afterSaleLog.setChannel(orderAfterDTO.getChannel());
        if (afsType == OrdersAfsConst.AFS_TYPE_REFUND) {
            afterSaleLog.setAfsType(OrdersAfsConst.AFS_TYPE_REFUND);
            afterSaleLog.setState(String.valueOf(OrdersAfsConst.RETURN_STATE_100));
            if (returnBy == OrderConst.RETURN_BY_2) {
                afterSaleLog.setContent("商家申请取消（仅退款）");
            } else if (returnBy == OrderConst.RETURN_BY_1) {
                afterSaleLog.setContent("买家申请仅退款");
            }else if (returnBy == OrderConst.RETURN_BY_4) {
                afterSaleLog.setContent("客户经理申请仅退款");
            } else if (returnBy == OrderConst.RETURN_BY_0) {
                afterSaleLog.setContent("系统自动取消订单");
            } else if (returnBy == OrderConst.RETURN_BY_3) {
                afterSaleLog.setContent("平台强制退款发起");
            }else if(returnBy == OrderConst.RETURN_BY_5) {
                afterSaleLog.setContent("乡信站长申请仅退款");
            }else if (returnBy == OrderConst.RETURN_BY_6){
                afterSaleLog.setContent("返利中心发起");
            }
        } else if (afsType == OrdersAfsConst.AFS_TYPE_RETURN) {
            afterSaleLog.setAfsType(OrdersAfsConst.AFS_TYPE_RETURN);
            afterSaleLog.setState(String.valueOf(OrdersAfsConst.RETURN_STATE_101));
            if (returnBy == OrderConst.RETURN_BY_2) {
                afterSaleLog.setContent("商家申请取消（退货退款）");
            } else if (returnBy == OrderConst.RETURN_BY_1) {
                afterSaleLog.setContent("买家申请退货退款");
            }else if (returnBy == OrderConst.RETURN_BY_4) {
                afterSaleLog.setContent("客户经理申请退货退款");
            } else if (returnBy == OrderConst.RETURN_BY_0) {
                afterSaleLog.setContent("系统自动取消订单");
            } else if (returnBy == OrderConst.RETURN_BY_3) {
                afterSaleLog.setContent("平台强制退款发起");
            }else if(returnBy == OrderConst.RETURN_BY_5) {
                afterSaleLog.setContent("乡信站长申请退货退款");
            }else if (returnBy == OrderConst.RETURN_BY_6){
                afterSaleLog.setContent("返利中心发起");
            }
        } else if (afsType == OrdersAfsConst.AFS_TYPE_REPLACEMENT) {
            afterSaleLog.setAfsType(OrdersAfsConst.AFS_TYPE_REPLACEMENT);
            afterSaleLog.setState(String.valueOf(OrdersAfsConst.RETURN_STATE_100));
            if (returnBy == OrderConst.RETURN_BY_1) {
                afterSaleLog.setContent("买家申请换货退款");
            }else if (returnBy == OrderConst.RETURN_BY_2) {
                afterSaleLog.setContent("商家申请换货退款");
            }
        }
        afterSaleLog.setCreateTime(new Date());
        //log.info("保存售后轨迹日志, afterSaleLog:{}, orderAfterDTO:{}", JSONObject.toJSONString(afterSaleLog), JSONObject.toJSONString(orderAfterDTO));
        int count = orderAfterSaleLogMapper.insert(afterSaleLog);
        AssertUtil.isTrue(count == 0, "记录退货信息失败");
    }

    /**
     * 更新订单信息，待发货订单申请售后后锁定订单
     *
     * @param orderPODb
     */
    private void updateOrder(OrderPO orderPODb, Integer lockNum) {
        if (orderPODb.getOrderState().equals(OrderConst.ORDER_STATE_20)
            || orderPODb.getOrderState().equals(OrderConst.ORDER_STATE_25)
            || orderPODb.getOrderState().equals(OrderConst.ORDER_STATE_30)) {
            // 待发货状态下申请售后，需要锁定订单，不允许在申请售后，商家也不能发货
            OrderPO updateOrderPO = new OrderPO();
            updateOrderPO.setOrderId(orderPODb.getOrderId());
            updateOrderPO.setLockState(orderPODb.getLockState() + lockNum);// 锁定状态+1
            int count = orderMapper.updateByPrimaryKeySelective(updateOrderPO);
            AssertUtil.isTrue(count == 0, "订单锁定失败");
        }
    }

    /**
     * 根据申请件数获取退款金额
     * @param orderProductId
     * @param applyNum
     * @return
     */
    public BigDecimal getReturnMoney(String orderProductId, Integer applyNum) {
        // 根据orderProductId获取订单货品信息
        OrderProductPO orderProductPODb = orderProductMapper.getByPrimaryKey(orderProductId);

        BizAssertUtil.isTrue(orderProductPODb.getReturnNumber().equals(orderProductPODb.getProductNum()), "该商品已全部退款");

        // 获取最大可退数量
        int orderTotalCanReturn = orderProductPODb.getProductNum() - orderProductPODb.getReturnNumber();
        // 获取本货品总可退金额
        BigDecimal orderTotalReturnAmount = orderProductPODb.getMoneyAmount()
            .subtract(orderProductPODb.getMoneyAmount().multiply(new BigDecimal(orderProductPODb.getReturnNumber()))
                .divide(new BigDecimal(orderProductPODb.getProductNum()), 2, RoundingMode.DOWN));
        // 计算本次可退金额 :
        BigDecimal moneyCanReturn = orderTotalReturnAmount.multiply(new BigDecimal(applyNum))
            .divide(new BigDecimal(orderTotalCanReturn), 2, RoundingMode.DOWN);
        return moneyCanReturn;
    }

    /**
     * 用户售后发货
     *
     * @param member            用户信息
     * @param afsSn             退款单号
     * @param expressId         物流id
     * @param logisticsNumber   物流编码
     * @return                  处理结果 true/false
     */
    @Transactional
    public boolean memberDeliverGoods(Member member, String afsSn, Integer expressId, String logisticsNumber) {
        log.info("SERVICE ARGS memberId:{} afsSn:{}  expressId:{}   logisticsNumber:{}", member.getMemberId(), afsSn, expressId,
            logisticsNumber);
        // 获取数据库中的售后信息
        OrderAfterPO orderAfterServicePO = this.getAfterServiceByAfsSn(afsSn);
        OrderReturnPO orderReturnPO = this.getOrderReturnByAfsSn(afsSn);
        OrderAfterSaleLogPO afterSaleLog = new OrderAfterSaleLogPO();
        OrderReturnPO updateReturn = new OrderReturnPO();

        log.info("OrderAfterService FROM DB:{}", JSON.toJSONString(orderAfterServicePO));
        log.info("orderReturnPO FROM DB:{}", JSON.toJSONString(orderReturnPO));

        // 自提订单退货退款，客户发货后，变为待自提点确认收货
        if (OrderReturnStatus.SELF_PICKUP_POINT_RETURN_APPLY.getValue().equals(orderReturnPO.getState())) {

            afterSaleLog.setState(String.valueOf(OrderReturnStatus.SELF_PICKUP_POINT_RETURN_APPLY.getValue()));

            updateReturn.setState(OrderReturnStatus.WAIT_SELF_PICKUP_POINT_RECEIVED.getValue());

            // 更新售后服务信息
            OrderAfterPO updateAfterService = new OrderAfterPO();
            updateAfterService.setAfsId(orderAfterServicePO.getAfsId());
            updateAfterService.setBuyerDeliverTime(new Date());
            int update = orderAfterMapper.updateByPrimaryKeySelective(updateAfterService);
            AssertUtil.isTrue(update == 0, "更新用户发货信息失败");

        } else {
            AssertUtil.isTrue(orderAfterServicePO.getMemberId().intValue() != member.getMemberId().intValue(), "您无权操作");

            // 获取快递公司信息
            BizAssertUtil.notNull(expressId, "快递公司id不能为空");
            Express express = expressFeignClient.getExpressByExpressId(expressId);
            BizAssertUtil.notNull(express, "获取物流信息为空");

            // 更新售后服务信息
            OrderAfterPO updateAfterService = new OrderAfterPO();
            updateAfterService.setAfsId(orderAfterServicePO.getAfsId());
            updateAfterService.setBuyerDeliverTime(new Date());
            updateAfterService.setBuyerExpressName(express.getExpressName());
            updateAfterService.setBuyerExpressCode(express.getExpressCode());
            updateAfterService.setBuyerExpressNumber(logisticsNumber);
            int update = orderAfterMapper.updateByPrimaryKeySelective(updateAfterService);
            AssertUtil.isTrue(update == 0, "更新用户发货信息失败");
            BizAssertUtil.isTrue(OrdersAfsConst.AFS_TYPE_RETURN != orderAfterServicePO.getAfsType(),
                    String.format("售后单%s售后类型不支持退货退款, 请确认", orderAfterServicePO.getAfsSn()));

            // 退货发货，更新退货表
            AssertUtil.isTrue(!(orderReturnPO.getState().equals(OrdersAfsConst.RETURN_STATE_200)
                    || orderReturnPO.getState().equals(OrdersAfsConst.RETURN_STATE_201)), "未到发货状态");

            if (OrderPerformanceModeEnum.PERFORMANCE_MODE_SUPPLIER.getValue().equals(orderAfterServicePO.getPerformanceMode())) {
                //供应商履约售后单，调dts用户寄回接口
                dealSupplierGoodsSendBack(orderAfterServicePO.getAfsSn(), logisticsNumber, express.getExpressCode(), express.getExpressName());
            }

            // 物流仓退货退款，用户发货需要调用erp进行发货
            OrderProductPO orderProductPO = orderProductService.selectOneByOrderProductId(orderAfterServicePO.getOrderProductId());
            if (orderAfterService.isJdInterceptOrderProduct(orderProductPO)) {
                if (!erpIntegration.orderAfterReturnGoods(afsSn)) {
                    throw new BusinessException("退货申请失败，请确认物流信息");
                }
            }

            updateReturn.setState(OrdersAfsConst.RETURN_STATE_102);
            afterSaleLog.setState(String.valueOf(OrdersAfsConst.RETURN_STATE_102));
        }

        int update = 0;

        updateReturn.setReturnId(orderReturnPO.getReturnId());
        update = orderReturnMapper.updateByPrimaryKeySelective(updateReturn);
        AssertUtil.isTrue(update == 0, "更新退货信息失败");

        // 记录售后日志
        afterSaleLog.setLogRole(OrderConst.LOG_ROLE_MEMBER);
        afterSaleLog.setLogUserId(Long.parseLong(String.valueOf(member.getMemberId())));
        afterSaleLog.setLogUserName(orderAfterServicePO.getMemberName());
        afterSaleLog.setAfsSn(afsSn);
        afterSaleLog.setAfsType(OrdersAfsConst.AFS_TYPE_RETURN);
        afterSaleLog.setContent("买家退货给商家");
        afterSaleLog.setCreateTime(new Date());
        update = orderAfterSaleLogMapper.insert(afterSaleLog);
        AssertUtil.isTrue(update == 0, "记录售后日志失败");

        // 记录退款轨迹
        OrderReturnTrackPO orderReturnTrackPO = OrderReturnTrackPO.builder().afsSn(afsSn)
                .operateType(OrderReturnOperateTypeEnum.CUSTOMER_DELIVER_GOODS.getValue())
                .operator(member.getMemberName() + "-" + member.getMemberMobile()).operateTime(new Date())
                .operateResult(AuditResultEnum.AUDIT_PASS.getValue()).operateRemark("用户售后发货")
                .channel(OrderCreateChannel.APP.getValue())     // 用户收货默认先设置渠道为APP
                .operatorRole(OperationRoleEnum.CUSTOMER.getValue()).build();
        orderReturnTrackMapper.insert(orderReturnTrackPO);

        // 发送售后发货MQ通知
        OrderPO orderPO = orderService.getByOrderSn(orderReturnPO.getOrderSn());
        orderCreateHelper.addOrderReturnEvent(orderPO, orderPO.getXzCardAmount(), orderReturnPO, OrderEventEnum.REFUND_DELIVER_GOODS,
                new Date(), null);

        return true;
    }

    /**
     * 主任确认收货
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean selfPointReceiveGoods(Member member, String afsSn) {
        OrderReturnPO orderReturnPO = this.getOrderReturnByAfsSn(afsSn);
        OrderAfterPO orderAfterServicePO = this.getAfterServiceByAfsSn(afsSn);

        // 状态校验
        AssertUtil.isTrue(!(orderReturnPO.getState().equals(OrderReturnStatus.WAIT_SELF_PICKUP_POINT_RECEIVED.getValue())),
                "未到待主任收货状态");

        // 更新售后单状态
        boolean update = orderReturnService.lambdaUpdate().eq(OrderReturnPO::getAfsSn, afsSn)
                .eq(OrderReturnPO::getState, OrderReturnStatus.WAIT_SELF_PICKUP_POINT_RECEIVED.getValue())
                .set(OrderReturnPO::getState, OrderReturnStatus.WAIT_RETURN.getValue())
                .set(OrderReturnPO::getUpdateTime, new Date())
                .update();
        AssertUtil.isTrue(!update, String.format("更新售后单状态失败, 售后单号: %s", afsSn));

        // 记录轨迹和日志
        OrderReturnTrackPO orderReturnTrackPO = OrderReturnTrackPO.builder().afsSn(afsSn)
                .operateType(OrderReturnOperateTypeEnum.SELF_POINT_RECEIVE.getValue())
                .operator(member.getMemberName() + "-" + member.getMemberMobile()).operateTime(new Date())
                .operateResult(AuditResultEnum.AUDIT_PASS.getValue()).operateRemark("自提点确认收货")
                .channel(OrderCreateChannel.APP.getValue())     // 用户收货默认先设置渠道为APP
                .operatorRole(OperationRoleEnum.DIRECTOR.getValue()).build();
        orderReturnTrackMapper.insert(orderReturnTrackPO);

        OrderAfterSaleLogPO record = new OrderAfterSaleLogPO();
        record.setAfsSn(afsSn);
        record.setContent("自提点确认收货");
        record.setState(OrderReturnStatus.REFUND_APPLY.getValue().toString());
        record.setLogRole(OrderConst.LOG_ROLE_ADMIN);
        record.setLogUserName(member.getMemberName());
        record.setLogUserId(member.getMemberId().longValue());
        record.setAfsType(orderAfterServicePO.getAfsType());
        orderAfterSaleLogMapper.insert(record);

        return true;
    }

    /**
     * 用户寄回商品物流信息发送给dts
     * @param afsSn
     * @param expressNumber
     * @param expressCode
     * @param expressName
     * */
    private void dealSupplierGoodsSendBack(String afsSn,String expressNumber, String expressCode, String expressName){
        SendBackGoodsRequest sendBackGoodsRequest = new SendBackGoodsRequest();
        sendBackGoodsRequest.setExpressCode(expressCode);
        sendBackGoodsRequest.setExpressName(expressName);
        sendBackGoodsRequest.setExpressNumber(expressNumber);
        sendBackGoodsRequest.setTparReturnOrderCode(afsSn);
        skyCraneFeignClient.sendBackGoods(sendBackGoodsRequest);
    }

    /**
     * 系统自动处理售后单 1.商户未审核的退货退款申请 2.商户未审核的换货 3.商户未审核的仅退款申请 4.用户退货发货但是到时间限制商户还未收货
     *
     * @return
     */
    @GlobalTransactional
    public Map<String, List<?>> jobSystemDealAfterService() {
        Map<String, List<?>> resultMap = new HashMap<>();

        // 退货收货集合
        List<OrderReturnPO> returnsReceiveList = new ArrayList<>();
        // 退货退款申请集合
        List<OrderReturnPO> returnsApplyList = new ArrayList<>();
        // 仅退款申请集合
        List<OrderReturnPO> refundApplyList = new ArrayList<>();
        // 换货申请集合
        // List<OrderReplacement> replacementsApplyList = new ArrayList<>();

        /** -----------------------------------处理退货未收货自动处理----------------------------------- */
        // 获取配置的相关自动天数期限设置
        int receiveDay = Integer.parseInt(stringRedisTemplate.opsForValue().get("time_limit_of_afs_seller_receive"));

        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, -receiveDay);

        String deliverTime = TimeUtil.getDateTimeString(cal.getTime());

        // 获取状态为待收货的退货单
        OrderReturnExample example = new OrderReturnExample();
        example.setState(OrdersAfsConst.RETURN_STATE_102);
        example.setDeliverTimeEnd(deliverTime);
        List<OrderReturnPO> orderReturnPOList = orderReturnMapper.listByExample(example);
        if (!CollectionUtils.isEmpty(orderReturnPOList)) {
            // 单条数据处理异常不影响其他数据执行
            for (OrderReturnPO orderReturnPO : orderReturnPOList) {
                // 获取会员发货时间时间
                OrderAfterServiceExample orderAfterServiceExample = new OrderAfterServiceExample();
                orderAfterServiceExample.setAfsSn(orderReturnPO.getAfsSn());
                List<OrderAfterPO> afterServiceList = orderAfterMapper.listByExample(orderAfterServiceExample);
                if (CollectionUtils.isEmpty(afterServiceList)) {
                    continue;
                }
                OrderAfterPO orderAfterServicePODb = afterServiceList.get(0);

                // 说明需要自动处理确认收货
                // 更新退货状态
                OrderReturnPO orderReturnPOUpdate = new OrderReturnPO();
                orderReturnPOUpdate.setReturnId(orderReturnPO.getReturnId());
                orderReturnPOUpdate.setState(OrdersAfsConst.RETURN_STATE_203);
                int update = orderReturnMapper.updateByPrimaryKeySelective(orderReturnPOUpdate);
                AssertUtil.notNull(update == 0, "修改系统自动确认收货状态信息失败");

                // 更新售后服务单时间
                OrderAfterPO orderAfterServicePOUpdate = new OrderAfterPO();
                orderAfterServicePOUpdate.setAfsId(orderAfterServicePODb.getAfsId());
                orderAfterServicePOUpdate.setStoreReceiveTime(new Date());
                update = orderAfterMapper.updateByPrimaryKeySelective(orderAfterServicePOUpdate);
                AssertUtil.notNull(update == 0, "修改系统自动确认收货时间失败");

                // 记录售后日志
                OrderAfterSaleLogPO afterSaleLog = new OrderAfterSaleLogPO();
                afterSaleLog.setLogRole(OrderConst.LOG_ROLE_ADMIN);
                afterSaleLog.setLogUserId(0L);
                afterSaleLog.setLogUserName("系统自动处理");
                afterSaleLog.setAfsSn(orderAfterServicePODb.getAfsSn());
                afterSaleLog.setAfsType(orderAfterServicePODb.getAfsType());
                afterSaleLog.setState(OrdersAfsConst.RETURN_STATE_203 + "");
                afterSaleLog.setContent("系统自动确认收货");
                afterSaleLog.setCreateTime(new Date());
                update = orderAfterSaleLogMapper.insert(afterSaleLog);
                AssertUtil.notNull(update == 0, "记录售后服务操作日志失败");

                returnsReceiveList.add(orderReturnPO);
            }
        }
        // 商家处理申请时间
        int auditDay = Integer.parseInt(stringRedisTemplate.opsForValue().get("time_limit_of_afs_seller_audit"));

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -auditDay);

        /** -----------------------------------自动处理未处理的退货退款申请----------------------------------- */
        // 获取所有未处理的退货退款申请
        OrderReturnExample orderReturnExample = new OrderReturnExample();
        orderReturnExample.setState(OrdersAfsConst.RETURN_STATE_101);
        orderReturnExample.setApplyTimeBefore(calendar.getTime());
        List<OrderReturnPO> returnList = orderReturnMapper.listByExample(orderReturnExample);
        if (!CollectionUtils.isEmpty(returnList)) {
            // 单条数据处理异常不影响其他数据执行
            for (OrderReturnPO orderReturnPO : returnList) {
                // 获取对应的售后单号
                OrderAfterServiceExample orderAfterServiceExample = new OrderAfterServiceExample();
                orderAfterServiceExample.setAfsSn(orderReturnPO.getAfsSn());
                List<OrderAfterPO> afterServiceList = orderAfterMapper.listByExample(orderAfterServiceExample);
                if (CollectionUtils.isEmpty(afterServiceList)) {
                    continue;
                }
                OrderAfterPO orderAfterServicePODb = afterServiceList.get(0);

                //供应商订单（云中鹤）的退货退款申请不需要自动审批
                if (OrderPerformanceModeEnum.PERFORMANCE_MODE_SUPPLIER.getValue().equals(orderAfterServicePODb.getPerformanceMode())) {
                    log.info("yunzhonghe orderSn = {}", orderAfterServicePODb.getOrderSn());
                    continue;
                }

                // 更新用户申请的状态
                OrderReturnPO orderReturnPOUpdate = new OrderReturnPO();
                orderReturnPOUpdate.setReturnId(orderReturnPO.getReturnId());
                orderReturnPOUpdate.setState(OrdersAfsConst.RETURN_STATE_201);
                int update = orderReturnMapper.updateByPrimaryKeySelective(orderReturnPOUpdate);
                AssertUtil.notNull(update == 0, "修改系统自动确认同意退货退款申请状态失败");

                // 更新售后服务单时间
                OrderAfterPO orderAfterServicePOUpdate = new OrderAfterPO();
                orderAfterServicePOUpdate.setAfsId(orderAfterServicePODb.getAfsId());
                orderAfterServicePOUpdate.setStoreAuditTime(new Date());
                update = orderAfterMapper.updateByPrimaryKeySelective(orderAfterServicePOUpdate);
                AssertUtil.notNull(update == 0, "修改系统自动确认同意退货退款审核时间失败");

                // 记录售后日志
                OrderAfterSaleLogPO afterSaleLog = new OrderAfterSaleLogPO();
                afterSaleLog.setLogRole(OrderConst.LOG_ROLE_ADMIN);
                afterSaleLog.setLogUserId(0L);
                afterSaleLog.setLogUserName("系统自动处理");
                afterSaleLog.setAfsSn(orderAfterServicePODb.getAfsSn());
                afterSaleLog.setAfsType(orderAfterServicePODb.getAfsType());
                afterSaleLog.setState(OrdersAfsConst.RETURN_STATE_201 + "");
                afterSaleLog.setContent("系统自动确认同意退货退款申请");
                afterSaleLog.setCreateTime(new Date());
                update = orderAfterSaleLogMapper.insert(afterSaleLog);
                AssertUtil.notNull(update == 0, "记录售后服务操作日志失败");

                // 记录退款轨迹
                this.refundStoreAuditAutoPassRecord(orderAfterServicePODb.getAfsSn());

                returnsApplyList.add(orderReturnPO);
            }
        }

        /** -----------------------------------自动处理未处理的仅退款申请----------------------------------- */
        // 获取所有未处理的仅退款申请
        OrderReturnExample returnExample = new OrderReturnExample();
        returnExample.setState(OrdersAfsConst.RETURN_STATE_100);
        returnExample.setApplyTimeBefore(calendar.getTime());
        List<OrderReturnPO> orderReturnPOS = orderReturnMapper.listByExample(returnExample);
        if (!CollectionUtils.isEmpty(orderReturnPOS)) {
            // 单条数据处理异常不影响其他数据执行
            for (OrderReturnPO orderReturnPO : orderReturnPOS) {
                // 获取对应的售后单号
                OrderAfterServiceExample orderAfterServiceExample = new OrderAfterServiceExample();
                orderAfterServiceExample.setAfsSn(orderReturnPO.getAfsSn());
                List<OrderAfterPO> afterServiceList = orderAfterMapper.listByExample(orderAfterServiceExample);
                if (CollectionUtils.isEmpty(afterServiceList)) {
                    continue;
                }
                OrderAfterPO orderAfterServicePODb = afterServiceList.get(0);

                // 更新用户申请的状态
                OrderReturnPO orderReturnPOUpdate = new OrderReturnPO();
                orderReturnPOUpdate.setReturnId(orderReturnPO.getReturnId());
                orderReturnPOUpdate.setState(OrdersAfsConst.RETURN_STATE_200);
                int update = orderReturnMapper.updateByPrimaryKeySelective(orderReturnPOUpdate);
                AssertUtil.notNull(update == 0, "修改系统自动确认同意仅退款申请状态失败");

                // 更新售后服务单时间
                OrderAfterPO orderAfterServicePOUpdate = new OrderAfterPO();
                orderAfterServicePOUpdate.setAfsId(orderAfterServicePODb.getAfsId());
                orderAfterServicePOUpdate.setStoreAuditTime(new Date());
                update = orderAfterMapper.updateByPrimaryKeySelective(orderAfterServicePOUpdate);
                AssertUtil.notNull(update == 0, "修改系统自动确认仅退款审核时间失败");

                // 记录售后日志
                OrderAfterSaleLogPO afterSaleLog = new OrderAfterSaleLogPO();
                afterSaleLog.setLogRole(OrderConst.LOG_ROLE_ADMIN);
                afterSaleLog.setLogUserId(0L);
                afterSaleLog.setLogUserName("系统自动处理");
                afterSaleLog.setAfsSn(orderAfterServicePODb.getAfsSn());
                afterSaleLog.setAfsType(orderAfterServicePODb.getAfsType());
                afterSaleLog.setState(OrdersAfsConst.RETURN_STATE_200 + "");
                afterSaleLog.setContent("系统自动确认同意仅退款申请");
                afterSaleLog.setCreateTime(new Date());
                update = orderAfterSaleLogMapper.insert(afterSaleLog);
                AssertUtil.notNull(update == 0, "记录售后服务操作日志失败");

                // 记录退款轨迹
                this.refundStoreAuditAutoPassRecord(orderAfterServicePODb.getAfsSn());

                refundApplyList.add(orderReturnPO);
            }
        }

        /** -----------------------------------自动处理未处理的换货申请----------------------------------- */
        // 获取所有未处理的申请
        // OrderReplacementExample replacementExample = new OrderReplacementExample();
        // replacementExample.setState(OrdersAfsConst.REPLACEMENT_STATE_MEMBER_APPLY);
        // replacementExample.setApplyTimeBefore(calendar.getTime());
        // List<OrderReplacement> replacementList = orderReplacementMapper.listByExample(replacementExample);
        // if (!CollectionUtils.isEmpty(replacementList)) {
        // //单条数据处理异常不影响其他数据执行
        // for (OrderReplacement replacement : replacementList) {
        // //获取对应的售后单号
        // OrderAfterServiceExample orderAfterServiceExample = new OrderAfterServiceExample();
        // orderAfterServiceExample.setAfsSn(replacement.getAfsSn());
        // List<OrderAfterService> afterServiceList = orderAfterMapper.listByExample(orderAfterServiceExample);
        // if (CollectionUtils.isEmpty(afterServiceList)) {
        // continue;
        // }
        // OrderAfterService orderAfterServiceDb = afterServiceList.get(0);
        //
        // //更新用户申请的状态
        // OrderReplacement orderReplacementUpdate = new OrderReplacement();
        // orderReplacementUpdate.setReplacementId(replacement.getReplacementId());
        // orderReplacementUpdate.setState(OrdersAfsConst.REPLACEMENT_STATE_STORE_AUDIT_PASS);
        // orderReplacementMapper.updateByPrimaryKeySelective(orderReplacementUpdate);
        //
        // replacementsApplyList.add(replacement);
        // }
        // }
        resultMap.put("returnsReceiveList", returnsReceiveList);
        resultMap.put("returnsApplyList", returnsApplyList);
        resultMap.put("refundApplyList", refundApplyList);
        // resultMap.put("replacementsApplyList", replacementsApplyList);
        return resultMap;
    }

    /**
     * 商家审核自动通过，记录轨迹
     *
     * @param afsSn 退款单号
     */
    private void refundStoreAuditAutoPassRecord(String afsSn) {
        // 记录退款轨迹
        OrderReturnTrackPO orderReturnTrackPO = OrderReturnTrackPO.builder().afsSn(afsSn)
            .operateType(OrderReturnOperateTypeEnum.STORE_AUDIT.getValue()).operator(CommonConst.ADMIN_NAME_EN)
            .operateTime(new Date()).operateResult(AuditResultEnum.AUDIT_PASS.getValue())
            .operateRemark(CommonConst.SYSTEM_PASS).channel(OrderCreateChannel.WEB.getValue()).build();
        orderReturnTrackMapper.insert(orderReturnTrackPO);
    }

    // region 订单退款信息查询 Mybatis Plus 形式

    /**
     * 根据退订单号查询售后信息
     *
     * @param afsSn
     *            退订单号
     * @return 售后信息
     */
    public OrderAfterPO getOrderAfterByAfsSn(String afsSn) {
        LambdaQueryWrapper<OrderAfterPO> queryWrapper = Wrappers.lambdaQuery(OrderAfterPO.class);
        queryWrapper.eq(OrderAfterPO::getAfsSn, afsSn).eq(OrderAfterPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
        return orderAfterMapper.selectOne(queryWrapper);
    }

    /**
     * 根据订单号查询所有售后信息
     *
     * @param orderSn
     *            订单号
     * @return 售后信息集
     */
    public List<OrderAfterPO> getOrderAftersByOrderSn(String orderSn) {
        LambdaQueryWrapper<OrderAfterPO> queryWrapper = Wrappers.lambdaQuery(OrderAfterPO.class);
        queryWrapper.eq(OrderAfterPO::getOrderSn, orderSn).eq(OrderAfterPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
        return orderAfterMapper.selectList(queryWrapper);
    }

    /**
     * 保存退换货信息和售后日志
     *
     * @param afsSn
     * @param orderAfterDTO
     * @param afsProductVO
     * @param orderPO
     */
    public void insertReturnOrderNew(String afsSn, OrderAfterDTO orderAfterDTO, AfsProductVO afsProductVO, OrderPO orderPO) {
        // 仅退款或退货退款
        OrderReturnPO orderReturnPO = new OrderReturnPO();
        orderReturnPO.setAfsSn(afsSn);
        orderReturnPO.setOrderSn(orderPO.getOrderSn());
        orderReturnPO.setReturnNum(afsProductVO.getAfsNum());
        orderReturnPO.setStoreId(orderPO.getStoreId());
        orderReturnPO.setStoreIsSelf(orderPO.getStoreIsSelf());
        orderReturnPO.setStoreName(orderPO.getStoreName());
        orderReturnPO.setMemberId(orderPO.getMemberId());
        orderReturnPO.setMemberName(orderPO.getMemberName());
        // 退款方式取系统设置
        orderReturnPO
            .setReturnMoneyType(Integer.valueOf(stringRedisTemplate.opsForValue().get("refund_setting_switch")));
        orderReturnPO.setReturnIntegralAmount(afsProductVO.getReturnIntegralAmount());
        orderReturnPO.setDeductIntegralAmount(afsProductVO.getDeductIntegralAmount());
        orderReturnPO.setReturnMoneyAmount(afsProductVO.getReturnMoneyAmount());
        orderReturnPO.setReturnExpressAmount(afsProductVO.getReturnExpressAmount());
        orderReturnPO.setReturnVoucherCode(afsProductVO.getReturnVoucherCode());
        orderReturnPO.setCommissionRate(afsProductVO.getCommissionRate());
        orderReturnPO.setCommissionAmount(afsProductVO.getCommissionAmount());
        orderReturnPO.setPlatformActivityAmount(afsProductVO.getPlatformActivityAmount());
        orderReturnPO.setXzCardAmount(afsProductVO.getXzCardAmount());
        orderReturnPO.setXzCardExpressFeeAmount(afsProductVO.getXzCardExpressFeeAmount());
        orderReturnPO.setPlatformVoucherAmount(afsProductVO.getPlatformVoucherAmount());
        orderReturnPO.setPlatformVoucherAmountPlt(afsProductVO.getPlatformVoucherAmountPlt());
        orderReturnPO.setPlatformVoucherAmountStore(afsProductVO.getPlatformVoucherAmountStore());
        orderReturnPO.setPlatformVoucherRetailAmountPlt(afsProductVO.getPlatformVoucherRetailAmountPlt());
        orderReturnPO.setPlatformVoucherRetailAmountStore(afsProductVO.getPlatformVoucherRetailAmountStore());
        orderReturnPO.setActualReturnMoneyAmount(afsProductVO.getActualReturnMoneyAmount());
        orderReturnPO.setCustomerAssumeAmount(BigDecimal.ZERO);
        orderReturnPO.setOtherCompensationAmount(BigDecimal.ZERO);
        orderReturnPO.setPaymentMethod(afsProductVO.getPaymentMethod());
        orderReturnPO.setRefundType(afsProductVO.getRefundType());
        orderReturnPO.setRefundStartTime(new Date());
        int days = 0;
        RefundType refundType = getRefundType(orderPO.getOrderSn());
        // 代还设置利息承担方
        if (RefundType.ASSIST_PAYMENT.getValue().equals(refundType.getValue())) {
            try {
                days = DateUtil.days(DateUtil.format(orderPO.getLendingSuccessTime(), DateUtil.FORMAT_TIME),
                    DateUtil.format(new Date(), DateUtil.FORMAT_TIME), DateUtil.FORMAT_TIME);
                orderReturnPO.setInterestPayer(
                    days < 7 ? InterestPayerEnum.PLATFORM.getDesc() : InterestPayerEnum.STORE.getDesc());
            } catch (Exception e) {
                throw new MallException("还没有放款成功，请稍后重试！");
            }
        }

        orderReturnPO.setRemark(afsProductVO.getRemark());
        orderReturnPO.setServiceFee(afsProductVO.getServiceFee());
        orderReturnPO.setThirdpartnarFee(afsProductVO.getThirdpartnarFee());
        orderReturnPO.setOrderCommission(afsProductVO.getOrderCommission());
        orderReturnPO.setBusinessCommission(afsProductVO.getBusinessCommission());
        orderReturnPO.setApplyTime(new Date());

        this.handleReturnStateAndTypeNew(orderReturnPO, afsProductVO.getReturnBy(), orderAfterDTO.getAfsType());
        int count = orderReturnMapper.insert(orderReturnPO);
        AssertUtil.isTrue(count == 0, "保存退货信息失败");

        this.insertAfsLog(afsSn, orderAfterDTO, orderPO, afsProductVO.getReturnBy());
    }

    /**
     * 设置退款发起者和退款状态
     *
     * @param orderReturnPO
     *            退款单信息
     * @param returnBy
     *            退款发起者
     * @param afsType
     *            售后服务端类型
     */
    private void handleReturnStateAndTypeNew(OrderReturnPO orderReturnPO, Integer returnBy, Integer afsType) {
        orderReturnPO.setReturnBy(returnBy);
        // 自动取消、商家取消，直达平台审核
        if (returnBy != null && returnBy != OrderConst.RETURN_BY_1) {
            if (returnBy == OrderConst.RETURN_BY_0
                || returnBy == OrderConst.RETURN_BY_3) {
                orderReturnPO.setState(OrdersAfsConst.RETURN_STATE_200);
                orderReturnPO.setReturnType(OrdersAfsConst.RETURN_TYPE_1);
            } else if (returnBy == OrderConst.RETURN_BY_2) {
                //指定店铺&&erp出库商品不允许商家取消订单不允许自动审批
                if(isAllowAutoApprove(orderReturnPO.getAfsSn(),afsType)) {
                    orderReturnPO.setState(OrdersAfsConst.RETURN_STATE_200);
                    orderReturnPO.setReturnType(OrdersAfsConst.RETURN_TYPE_1);
                }
            }
        } else if (afsType == OrdersAfsConst.AFS_TYPE_REFUND) {
            orderReturnPO.setState(OrdersAfsConst.RETURN_STATE_100);
            orderReturnPO.setReturnType(OrdersAfsConst.RETURN_TYPE_1);
        } else if (afsType == OrdersAfsConst.AFS_TYPE_RETURN) {
            orderReturnPO.setState(OrdersAfsConst.RETURN_STATE_101);
            orderReturnPO.setReturnType(OrdersAfsConst.RETURN_TYPE_2);
        }
    }

    /**
     *
     * @param afsSn
     * @param afsType
     * @return false -
     */
    private boolean isAllowAutoApprove (String afsSn,Integer afsType) {
        if(afsType != OrdersAfsConst.AFS_TYPE_REFUND){
            return true;
        }
        OrderAfterPO orderAfterPO = orderAfterServiceModel.getAfterServiceByAfsSn(afsSn);
        OrderProductPO orderProduct = orderProductService.selectOneByOrderProductId(orderAfterPO.getOrderProductId());
        return !orderAfterService.isJdInterceptOrderProduct(orderProduct);
    }

    public BigDecimal queryTotalDiscountAmount(OrderPO orderPO){
        if (org.apache.commons.lang3.StringUtils.isBlank(orderPO.getUserNo())) {
            throw new MallException(String.format("订单:%s,未维护客户信息userNo",orderPO.getOrderSn()));
        }
        LambdaQueryWrapper<OrderExtendFinancePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderExtendFinancePO::getOrderSn,orderPO.getOrderSn());
        List<OrderExtendFinancePO> extendFinancePOList = orderExtendFinanceService.list(queryWrapper);
        Set<String> couponBatchSet = new HashSet<>(extendFinancePOList.size());
        for (OrderExtendFinancePO itemPO : extendFinancePOList) {
            if (org.apache.commons.lang3.StringUtils.isNotBlank(itemPO.getCouponBatch())) {
                couponBatchSet.add(itemPO.getCouponBatch());
            }
        }
        if (couponBatchSet.isEmpty()) {
            log.info("未查询到任何预贴息批次券号,金额返回0");
            return BigDecimal.ZERO;
        }
        if (couponBatchSet.size() > 1) {
            throw new MallException(String.format("订单:%s,存在多张预贴息券",orderPO.getOrderSn()));
        }
        String couponBatch = extendFinancePOList.get(0).getCouponBatch();
        Result<BigDecimal> result = discountCouponFacade.queryTotalDiscountAmount(orderPO.getOrderSn(), couponBatch);
        if (Objects.isNull(result)) {
            String format = String.format("调用ms-promotion返回结果为空,orderSn:%s,couponBatch:%s", orderPO.getOrderSn(), couponBatch);
            throw new MallException(format);
        }
        if (!result.isSuccess()) {
            String format = String.format("调用ms-promotion返回结果为空,orderSn:%s,couponBatch:%s,原因:%s",
                    orderPO.getOrderSn(), couponBatch,result.getMessage());
            throw new MallException(format);
        }
        return result.getData();
    }

    /**
     * 构造售后货品信息
     * **/
    public AfsProductVO createAfsProductVO(Long productId,BigDecimal returnMoney,BigDecimal serviceFee,BigDecimal orderCommission,
                                         BigDecimal thirdpartnarFee,BigDecimal xzCardAmount,BigDecimal businessCommission,BigDecimal platformVoucherAmount,
                                         BigDecimal platformActivityAmount,
                                         int afsNum, int returnBy,int afsType){
        OrderProductPO orderProductPO = orderProductMapper.getByPrimaryKey(productId);
        OrderPO orderPO = orderModel.getOrderByOrderSn(orderProductPO.getOrderSn());
        AfsProductVO afsProductVO = new AfsProductVO(orderProductPO);
        afsProductVO.setReturnBy(returnBy);
        afsProductVO.setAfsNum(afsNum);
        afsProductVO.setOrderCommission(orderCommission);
        afsProductVO.setBusinessCommission(businessCommission);
        //设置支付方式、退款方式
        initPayMethodAndRefundType(afsProductVO, orderPO, orderProductPO);

        if(OrdersAfsConst.AFS_TYPE_REPLACEMENT == afsType){
            //换货单不计算订单佣金和业务佣金
            afsProductVO.setOrderCommission(BigDecimal.ZERO);
            afsProductVO.setBusinessCommission(BigDecimal.ZERO);
            if(returnMoney.compareTo(BigDecimal.ZERO) > 0 && PayMethodEnum.isLoanPay(orderPO.getPaymentCode())){
                //换货退款类型，如果是贷款类型的支付 且 还款金额大于0时，设置退款方式为 线下退款
                afsProductVO.setRefundType(RefundType.OFFLINE_REFUND.getValue());
            }else if (returnMoney.compareTo(BigDecimal.ZERO) == 0){
                afsProductVO.setRefundType(RefundType.ZERO_YUAN_REFUND.getValue());
            }
        }

        OrderExtendExample extendExample = new OrderExtendExample();
        extendExample.setOrderSn(orderPO.getOrderSn());
        OrderExtendPO orderExtendPODb = orderExtendMapper.listByExample(extendExample).get(0);
        afsProductVO.setContactPhone(orderExtendPODb.getReceiverMobile());
        afsProductVO.setReturnMoneyAmount(returnMoney);
        afsProductVO.setActualReturnMoneyAmount(returnMoney);
        afsProductVO.setServiceFee(serviceFee);
        afsProductVO.setThirdpartnarFee(thirdpartnarFee);
        afsProductVO.setXzCardAmount(xzCardAmount);
        afsProductVO.setCommissionAmount(orderCommission.add(businessCommission));
        afsProductVO.setPlatformVoucherAmount(platformVoucherAmount);
        if (orderProductPO.getPlatformVoucherAmount().compareTo(BigDecimal.ZERO) > 0) {
            afsProductVO.setPlatformVoucherAmountPlt(refundCalculator.calculateRefundPlatformVoucherFunderAmount(productId, CouponFunder.PLATFORM)
                    .multiply(platformVoucherAmount).divide(orderProductPO.getPlatformVoucherAmount(), 2, RoundingMode.HALF_UP));
            afsProductVO.setPlatformVoucherAmountStore(refundCalculator.calculateRefundPlatformVoucherFunderAmount(productId, CouponFunder.STORE)
                    .multiply(platformVoucherAmount).divide(orderProductPO.getPlatformVoucherAmount(), 2, RoundingMode.HALF_UP));
            afsProductVO.setPlatformVoucherRetailAmountPlt(refundCalculator.calculateOrderProductPlatformVoucherRetailAmount(productId, CouponFunder.PLATFORM)
                    .multiply(platformVoucherAmount).divide(orderProductPO.getPlatformVoucherAmount(), 2, RoundingMode.HALF_UP));
            afsProductVO.setPlatformVoucherRetailAmountStore(refundCalculator.calculateOrderProductPlatformVoucherRetailAmount(productId, CouponFunder.STORE)
                    .multiply(platformVoucherAmount).divide(orderProductPO.getPlatformVoucherAmount(), 2, RoundingMode.HALF_UP));
        }
        afsProductVO.setPlatformActivityAmount(platformActivityAmount);
        return afsProductVO;
    }

    public String createAfterSaleOrder(OrderPO orderPO, OrderAfterDTO orderAfterDTO, AfsProductVO afsProductVO, UserDTO userDTO) {

        // 1. 生成退订单号
        String afsSn = shardingId.next(SeqEnum.RNO, orderPO.getMemberId().toString()) + "";

        // 2. 插入售后申请表
        this.insertAfterService(afsSn, afsProductVO, orderAfterDTO, orderPO);

        // 3. 插入退订申请表
        OrderReturnPO orderReturnPODb = this.insertReturnOrder(afsSn, orderAfterDTO, afsProductVO, orderPO);

        // 4. 更新订单货品中的退换货数量
        // 这里维护了退货数量（售后数量），没有维护换货数量！！！数据未更新没有做处理
        orderProductService.addReturnNumber(afsProductVO.getOrderProductId(), afsProductVO.getAfsNum());

        // 处理订单商品行退款状态
        orderProductService.dealOrderProductReturnStatus(CommonConst.PRODUCT_RETURN_APPLY,
                afsProductVO.getOrderProductId());

        //批量保存退款记录 Andy.2022.05.27 预售
        orderRefundRecordService.saveBatch(orderReturnPODb, orderPO);

        // 计算扣罚金额，实际退款金额需要减去扣罚金额 并更新
        if (orderReturnValidation.refundPunishAmountSupport(orderReturnPODb)) {
            this.recalculateRefundAmount(orderPO, orderReturnPODb);
        }

        // 5. 记录退订日志（步骤3已经插入了日志）
        // this.insertAfsLog(afsSn, orderAfterDTO.getAfsType(), orderPODb, OrderConst.RETURN_BY_1);

        Date now = new Date();
        String operator = userDTO.getUserName();
        if(!StringUtils.isEmpty(userDTO.getMobile())){
            operator = operator + "-" + userDTO.getMobile();
        }
        // 6、记录退款轨迹
        OrderReturnTrackPO orderReturnTrackPO =
                OrderReturnTrackPO.builder().afsSn(afsSn).operateType(OrderReturnOperateTypeEnum.CREATE.getValue())
                        .operator(operator).operateTime(now)
                        .operateResult(AuditResultEnum.AUDIT_PASS.getValue()).operateRemark(orderAfterDTO.getApplyReasonContent())
                        .channel(orderAfterDTO.getChannel()).build();
        orderReturnTrackMapper.insert(orderReturnTrackPO);

        // 6. 发送消息，如果是用户发起的，则需要给商家发送消息

        //7. 售后申请MQ
        orderCreateHelper.addOrderReturnEvent(orderPO, orderPO.getXzCardAmount(), orderReturnPODb,
                OrderEventEnum.REFUND_APPLY, now,null);

        //8. 更新订单信息（锁定订单）
        this.updateOrder(orderPO, 1);

        return afsSn;
    }

    /**
     * 获取商品行仅退款数量
     */
    public Map<Long, Integer> getProductRefundCountMap(List<Long> orderProductIdList) {
        Map<Long,Integer> resultMap = new HashMap<>();
        if(CollectionUtils.isEmpty(orderProductIdList)) {
            return resultMap;
        }
        List<OrderRefundCountDTO> orderRefundCountList = orderAfterMapper.getProductRefundCount(orderProductIdList);
        if(CollectionUtils.isEmpty(orderRefundCountList)) {
            return resultMap;
        }
        return orderRefundCountList.stream().collect(Collectors.toMap(OrderRefundCountDTO::getOrderProductId,OrderRefundCountDTO::getRefundNum));
    }


    /** 已结清状态，商家是否白名单
     *  否，拦截并提示
     */
    public boolean isOfflineWhiteListAndSettled (Result<CDMallRefundTryResultVO> tryCalculateResult, Long storeId) {
        if(!isSettled(tryCalculateResult)) {
            return Boolean.FALSE;
        }
        BizAssertUtil.isTrue(!isSquareOfflineRefundWhiteList(storeId), "因该用户已主动还清贷款，系统无法进行在线退款，只能由商家进行线下退款处理。");
        return Boolean.TRUE;
    }

    public boolean isSettled (Result<CDMallRefundTryResultVO> tryCalculateResult) {
        return !tryCalculateResult.isSuccess() && OrdersAfsConst.LOAN_SETTLED_STATED.equals(tryCalculateResult.getErrorCode());
    }

    public boolean isSquareOfflineRefundWhiteList (Long storeId) {
        return storeIsolateWhiteListFeignClient.isStoreOnList(WhiteListEnum.OFFLINE_REFUND_WHITE_LIST, storeId);
    }

    /**
     * 返利订单申请售后
     *
     * @param orderSn 订单编号
     * @return 是否成功
     */
    public Map<Long,String> applyAfterServiceForRebate(String orderSn,Integer afsType) {
        // 根据订单号查询出对应的数据，封装成OrderAfterDTO
        OrderPO orderPo = orderModel.getOrderByOrderSn(orderSn);
        BizAssertUtil.isTrue(Objects.isNull(orderPo),"订单未找到");
        List<OrderProductPO> orderProductPOS = orderProductService.listByOrderSn(orderSn);
        BizAssertUtil.isTrue(CollectionUtils.isEmpty(orderProductPOS),"订单商品信息未找到");
        Member member = memberFeignClient.getMemberByMemberId(orderPo.getMemberId());
        BizAssertUtil.isTrue(Objects.isNull(member),"用户信息为空");
        OrderAfterDTO afterDTO = new OrderAfterDTO();
        afterDTO.setOrderSn(orderSn);
        Integer orderState = orderPo.getOrderState();
        afterDTO.setAfsType(afsType);
        afterDTO.setGoodsState(OrdersAfsConst.GOODS_STATE_NO);
        if (OrdersAfsConst.AFS_TYPE_RETURN == afsType){
            afterDTO.setGoodsState(OrdersAfsConst.GOODS_STATE_YES);
        }
        afterDTO.setApplyReasonContent("返利订单自动申请售后");
        // 返利订单下单金额为0
        afterDTO.setFinalReturnAmount(BigDecimal.ZERO);
        afterDTO.setReturnBy(ReturnByEnum.REBATE_CENTER.getValue());
        List<OrderAfterDTO.AfterProduct> afterProductList = orderProductPOS.stream().map(op -> {
            OrderAfterDTO.AfterProduct ap = new OrderAfterDTO.AfterProduct();
            ap.setOrderProductId(op.getOrderProductId());
            // 返利订单全退
            ap.setAfsNum(op.getProductNum());
            return ap;
        }).collect(Collectors.toList());
        afterDTO.setProductList(afterProductList);
        return submitAfterSaleApply(afterDTO,member);
    }

    /**
     * 将订单会员名称由手机号转换成用户名 (订单下单保存的用户名实际都是手机号码)
     * @param logList
     */
    public void dealLogUserName(List<OrderAfterSaleLogPO> logList) {
        if (CollectionUtils.isEmpty(logList)) {
            log.info("logList is empty");
            return;
        }
        List<String> mobileList = logList.stream().filter(afterLog -> !StringUtils.isEmpty(afterLog.getLogUserName())).map(afterLog ->{
                    if (afterLog.getLogUserName().contains("-")) {
                        // logUserName格式为：用户名(手机号)-手机号
                        return afterLog.getLogUserName().split("-")[1];
                    } else {
                        return afterLog.getLogUserName();
                    }
                }).collect(Collectors.toList());
        log.info("轨迹用户手机号:{}", JSONObject.toJSONString(mobileList));
        if (CollectionUtils.isEmpty(mobileList)) {
            log.info("mobileList is empty");
            return;
        }
        try {
            QueryPushInfoReq req = new QueryPushInfoReq();
            req.setMobiles(mobileList);
            Result<List<UserInfoVo>> result = customerServiceFeign.queryPushInfo(req);
            if (Objects.isNull(result) || !result.isSuccess()) {
                String msg = Objects.isNull(result) ? "系统开小差了" : StringUtils.isEmpty(result.getErrorMsg()) ? result.getMessage() : result.getErrorMsg();
                log.warn("根据手机号获取客户中心客户信息异常, 查询参数为:{}, 异常原因为:{}" , JSON.toJSONString(req), msg);
                return;
            }
            if (CollectionUtils.isEmpty(result.getData())) {
                log.warn("根据手机号获取客户中心客户信息为空, 查询参数为:{}" , JSON.toJSONString(req));
                return;
            }
            Map<String, UserInfoVo> userNameMap = result.getData().stream().collect(Collectors.toMap(UserInfoVo::getMobile, Function.identity(), (o, n) -> n));
            logList.forEach(afterLog -> {
                String key;
                if (StringUtils.isEmpty(afterLog.getLogUserName())) {
                    return;
                } else if (afterLog.getLogUserName().contains("-")) {
                    // logUserName格式为：用户名(手机号)-手机号
                    key = afterLog.getLogUserName().split("-")[1];
                } else {
                    key = afterLog.getLogUserName();
                }
                UserInfoVo userInfoVo = userNameMap.getOrDefault(key, null);
                if (Objects.nonNull(userInfoVo)) {
                     if (Objects.nonNull(userInfoVo.getCustInfoVo())) {
                         afterLog.setLogUserName(userInfoVo.getCustInfoVo().getCustName());
                     } else if (!StringUtils.isEmpty(userInfoVo.getUserName())) {
                         afterLog.setLogUserName(userInfoVo.getUserName());
                     }
                }
            });
        } catch (Exception e) {
            log.warn("获取客户信息失败, 异常原因:{}", e.getMessage(), e);
        }
    }

    /**
     * 天杰白名单订单校验
     *
     * @param orderSn
     * @return
     */
    private Boolean tianjieOrderSnCheck(String orderSn) {
        List<DictionaryItemVO> dictionary = bmsIntegration.listDictionaryItemsByTypeCode(CommonConst.KEY_TIANJIE_ORDER_SN_WHITE_LIST, CommonConst.MALL_SYSTEM_MANAGE_ID);
        if (CollectionUtils.isEmpty(dictionary)) {
            return true;
        }
        for (DictionaryItemVO dictionaryItemVO : dictionary) {
            String itemDesc = dictionaryItemVO.getItemDesc();
            if (itemDesc.contains(orderSn)) {
                log.info("checkTianjieOrderSn 命中,orderSn:{}", orderSn);
                return false;
            }
        }
        return true;
    }
}