package com.cfpamf.ms.mallorder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.ms.mallorder.po.ProofRecordPO;

import java.util.List;

public interface IProofRecordService extends IService<ProofRecordPO> {
    List<ProofRecordPO> getProofRecords(String proofDefinitionCode, String proofValue);

    ProofRecordPO getByKeyValue(String proofKey, String proofValue, String proofDefinitionCode);
}
