package com.cfpamf.ms.mallorder.integration.erp.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * author:llk2021
 * date:2022/3/3
 **/
@Data
public class ProductVO {

    private Long id;

    @ApiModelProperty("所属品牌ID")
    private Long brandId;

    @ApiModelProperty("编码")
    private String spuId;

    @ApiModelProperty("财务系统物料编码")
    private String materialCode;

    @ApiModelProperty("货品状态 0-下架 1-上架 2-淘汰")
    private Integer productStatus;

    @ApiModelProperty("货号")
    private String productNo;

    @ApiModelProperty("货品名称")
    private String productName;

    @ApiModelProperty("副标题")
    private String productSubName;

    @ApiModelProperty("货品明细描述")
    private String productDescription;

    @ApiModelProperty("发货方式： 0-拼车 1-一件代发")
    private Integer insteadSend;

    @ApiModelProperty("货品类型 外采、自产")
    private Integer productType;

    @ApiModelProperty("货品主图URL")
    private String mainImage;

    @ApiModelProperty("一级分类")
    private String category1Code;

    @ApiModelProperty("二级分类")
    private String category2Code;

    @ApiModelProperty("三级分类")
    private String category3Code;

    @ApiModelProperty("物料全路径分类名称，前后不带斜杠，例如:肥料81/肥料81-2/肥料81-3/肥料81-4/肥料81-5/肥料81-6")
    private String categoryPath;

    @ApiModelProperty("规格数")
    private Integer skuNum;

    @ApiModelProperty("进项税率")
    private java.math.BigDecimal inTax;

    @ApiModelProperty("销项税率")
    private java.math.BigDecimal outTax;

    @ApiModelProperty("创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    @ApiModelProperty("创建人")
    private String createUserName;

    @ApiModelProperty("是否存在上架经历 1-是 0-否")
    private Integer isOpen;

    @ApiModelProperty("组织机构中电商组织名称")
    private String companyName;

    @ApiModelProperty("物料全路径分类编码， 前后不带斜杠, 例如: WMS125/WMS12502/WMS125021/WMS1250211/WMS12502111")
    private String categoryCodePath;
}
