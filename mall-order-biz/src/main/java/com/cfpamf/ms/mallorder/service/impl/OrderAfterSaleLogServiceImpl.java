package com.cfpamf.ms.mallorder.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cfpamf.ms.mallorder.mapper.OrderAfterSaleLogMapper;
import com.cfpamf.ms.mallorder.po.OrderAfterSaleLogPO;
import com.cfpamf.ms.mallorder.service.IOrderAfterSaleLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 退款日志实现类
 */
@Slf4j
@Service
public class OrderAfterSaleLogServiceImpl extends ServiceImpl<OrderAfterSaleLogMapper, OrderAfterSaleLogPO> implements IOrderAfterSaleLogService {

    @Override
    public boolean insertOrderAfterSaleLog(Integer logRole, Long logUserId, String logUserName, String afsSn,
                                           Integer afsType, String state, String content, String channel) {
        OrderAfterSaleLogPO orderAfterSaleLogPO = new OrderAfterSaleLogPO();
        orderAfterSaleLogPO.setLogRole(logRole);
        orderAfterSaleLogPO.setLogUserId(logUserId);
        orderAfterSaleLogPO.setLogUserName(logUserName);
        orderAfterSaleLogPO.setAfsSn(afsSn);
        orderAfterSaleLogPO.setAfsType(afsType);
        orderAfterSaleLogPO.setState(state);
        orderAfterSaleLogPO.setContent(content);
        orderAfterSaleLogPO.setChannel(channel);
        return super.save(orderAfterSaleLogPO);
    }

}
