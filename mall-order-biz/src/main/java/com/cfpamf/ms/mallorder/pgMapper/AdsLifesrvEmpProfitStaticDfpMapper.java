package com.cfpamf.ms.mallorder.pgMapper;

import com.cfpamf.framework.autoconfigure.mybatis.MyBaseMapper;
import com.cfpamf.ms.mallorder.po.pgrpt.AdsLifesrvEmpProfitSaticDfpPO;
import com.cfpamf.ms.mallorder.req.LifeSrvIncomeReportRequest;
import com.cfpamf.ms.mallorder.vo.pgrpt.LifesrvEmpProfitBasicVO;
import com.cfpamf.ms.mallorder.vo.pgrpt.LifesrvEmpProfitSummaryVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-30
 */
public interface AdsLifesrvEmpProfitStaticDfpMapper extends MyBaseMapper<AdsLifesrvEmpProfitSaticDfpPO> {

    /**
     * 查询生服收益员工基础指标
     * @param request 查询参数
     * @return list
     */
    List<AdsLifesrvEmpProfitSaticDfpPO> getBasicMetrics(@Param("request") LifeSrvIncomeReportRequest request);

    /**
     * 查询生服收益员工汇总数据
     * @param request 查询参数
     * @return int
     */
     LifesrvEmpProfitSummaryVO getSummary(@Param("request") LifeSrvIncomeReportRequest request);
}
