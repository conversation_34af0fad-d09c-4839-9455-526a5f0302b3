package com.cfpamf.ms.mallorder.scheduler;

import com.cfpamf.ms.mallorder.api.feign.OrderFeignClient;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.slodon.bbc.core.util.AssertUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 系统自动取消没有付款的阶梯团尾款订单
 * cron:0 0/5 * * * ?
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/4 15:22
 */
@Component
@Slf4j
public class OrderCancelLadderGroupBalanceJob {


    @Autowired
    private OrderModel orderModel;

    @XxlJob(value = "OrderCancelLadderGroupBalanceJob")
    public ReturnT<String> execute(String s) {

        long startTime = System.currentTimeMillis();
        XxlJobHelper.log("start job at local time: {0}", LocalDateTime.now());

        log.info("jobSystemCancelOrder() start");
        try {
            boolean jobResult = orderModel.jobSystemCancelLadderGroupBalanceOrder();
            AssertUtil.isTrue(!jobResult, "[jobSystemCancelLadderGroupBalanceOrder] 定时任务系统自动取消没有付尾款阶梯团尾款订单时失败");
        } catch (Exception e) {
            log.error("jobSystemCancelOrder()", e);
        }

        XxlJobHelper.log("finish job at local time: {0}  cost:{1}",
                LocalDateTime.now(),
                (System.currentTimeMillis() - startTime) / 1000);

        return new ReturnT("SUCCESS");
    }
}
