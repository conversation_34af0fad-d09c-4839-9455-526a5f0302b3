package com.cfpamf.ms.mallorder.integration.facade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "ErpProductAndSkuDTO", description = "ERP货品信息响应类")
public class ErpProductAndSkuVo implements Serializable {

    private static final long serialVersionUID = 3251394728L;

    @ApiModelProperty(value = "skuId规格编码")
    private String skuId;

    @ApiModelProperty(value = "规格名称")
    private String skuName;

    @ApiModelProperty(value = "sku物料编码")
    private String skuMaterialCode;

    @ApiModelProperty(value = "单位编码")
    private String skuUnitCode;

    @ApiModelProperty(value = "一级分类编码")
    private String category1Code;

    @ApiModelProperty(value = "分类路径")
    private String categoryPath;

    @ApiModelProperty(value = "采购价")
    private BigDecimal costPrice;

    @ApiModelProperty(value = "ERP编号")
    private String erpNo;

    @ApiModelProperty(value = "财务单位")
    private String financeUnit;

    @ApiModelProperty(value = "进项税率")
    private BigDecimal inTax;

    @ApiModelProperty(value = "销项税率")
    private BigDecimal outTax;

    @ApiModelProperty(value = "货品ID")
    private Long productId;

    @ApiModelProperty(value = "货品名称")
    private String productName;

    @ApiModelProperty(value = "erp货品类型")
    private Integer productType;

    private String spuId;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "toB供货价")
    private BigDecimal toBPrice;

    @ApiModelProperty(value = "toC供货价")
    private BigDecimal toCPrice;
}
