package com.cfpamf.ms.mallorder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.ms.mallorder.common.enums.OrderReturnOperateTypeEnum;
import com.cfpamf.ms.mallorder.dto.OperatorDTO;
import com.cfpamf.ms.mallorder.po.OrderReturnTrackPO;
import com.cfpamf.ms.mallorder.vo.OrderReturnTrackVO;

import java.util.List;

/**
 * 退款轨迹 service
 */
public interface IOrderReturnTrackService extends IService<OrderReturnTrackPO> {

    /**
     * 根据退款单号查询轨迹
     *
     * @param AfsSn     退款单号
     * @return          轨迹信息
     */
    List<OrderReturnTrackVO> getOrderReturnTrackVOByAfsSn(String AfsSn);

    /**
     * 根据售后单号和售后类型查询退款轨迹
     */
    OrderReturnTrackVO getOrderReturnTrackVOByAfsSnAndType(String afsSn, OrderReturnOperateTypeEnum type);

    /**
     * 新增退款轨迹信息
     *
     * @param afsSn             退款单号
     * @param operateType       操作类型
     * @param operator          操作人
     * @param operateResult     操作结果
     * @param operateRemark     操作备注
     * @param channel           操作渠道
     */
    boolean insertOrderReturnTrack(String afsSn, OrderReturnOperateTypeEnum operateType, OperatorDTO operator,
                                Integer operateResult, String operateRemark, String channel);

}
