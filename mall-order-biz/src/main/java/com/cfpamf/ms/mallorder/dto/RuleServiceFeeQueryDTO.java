package com.cfpamf.ms.mallorder.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description: 平台服务费规则配置 分页列表
 * @Author: wyw
 * @Date: 2022/12/5
 */
@Data
public class RuleServiceFeeQueryDTO {

    @ApiModelProperty(value = "引荐商")
    private String introduceMerchant;

    @ApiModelProperty(value = "支付方式")
    private String payWay;

    @ApiModelProperty(value = "一级类目")
//    @NotBlank(message = "一级类目不能为空")
    private List<Integer> firstCategory;

    @ApiModelProperty(value = "二级类目")
    private List<Integer> secondCategory;

}
