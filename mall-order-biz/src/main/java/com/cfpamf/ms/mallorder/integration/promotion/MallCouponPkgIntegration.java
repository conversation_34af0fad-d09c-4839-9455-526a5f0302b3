package com.cfpamf.ms.mallorder.integration.promotion;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallpromotion.api.CouponPkgFeignClient;
import com.cfpamf.ms.mallpromotion.vo.CouponLockSummary;
import com.cfpamf.ms.mallpromotion.vo.CouponPkgRefundResultVo;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.exception.MallException;
import com.slodon.bbc.core.response.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 电商优惠券服务交互
 * <AUTHOR>
 */
@Slf4j
@Component
public class MallCouponPkgIntegration {

    @Autowired
    private CouponPkgFeignClient couponPkgFeignClient;

    public void lockCoupons(CouponLockSummary couponLockSummary) {
        try {
            JsonResult<Boolean> result = couponPkgFeignClient.lockCoupons(couponLockSummary);
            if (result == null || !Objects.equals(result.getState(), OrderConst.RESULT_STATE_SUCCESS)) {
                log.warn("锁定电商优惠券异常, error: {}", result == null ? "" : result.getMsg());
                throw new MallException(result == null ? "锁定电商优惠券异常" : result.getMsg());
            }
            if (!result.getData()) {
                log.info("锁定电商优惠券校验不通过");
                throw new BusinessException("您不符合该商品下单要求～");
            }
        } catch (Exception ex) {
            log.warn("锁定电商优惠券异常，error: {}", ex.getMessage());
            throw ex;
        }
    }

    /**
     * 锁定优惠券v2，去除seata的版本
     *
     * @param couponLockSummary 优惠券锁定参数实体
     */
    public void lockCouponsV2(CouponLockSummary couponLockSummary) {
        try {
            JsonResult<Boolean> result = couponPkgFeignClient.lockCouponsV2(couponLockSummary);
            if (result == null || !Objects.equals(result.getState(), OrderConst.RESULT_STATE_SUCCESS)) {
                log.warn("锁定电商优惠券异常, error: {}", result == null ? "" : result.getMsg());
                throw new MallException(result == null ? "锁定电商优惠券异常" : result.getMsg());
            }
            if (!result.getData()) {
                log.info("锁定电商优惠券校验不通过");
                throw new BusinessException("您不符合该商品下单要求～");
            }
        } catch (Exception ex) {
            log.warn("锁定电商优惠券异常，error: {}", ex.getMessage());
            throw ex;
        }
    }

    /**
     * 卡券订单是否支持退款
     */
    public CouponPkgRefundResultVo couponCentreOrderRefundable(String orderSn) {
        JsonResult<CouponPkgRefundResultVo> result;
        try {
            result = couponPkgFeignClient.refundable(orderSn);
        } catch (Exception e) {
            log.error("校验卡券订单是否支持退款异常", e);
            throw e;
        }
        if (Objects.isNull(result) || !Objects.equals(result.getState(), OrderConst.RESULT_STATE_SUCCESS) || Objects.isNull(result.getData())) {
            log.error("校验卡券订单是否支持退款失败,返回结果:{}", JSONObject.toJSONString(result));
            throw new BusinessException(String.format("校验卡券订单是否支持退款失败,返回结果:%s", JSONObject.toJSONString(result)));
        }
        return result.getData();
    }

    /**
     * 卡券订单退款
     */
    public CouponPkgRefundResultVo couponCentreOrderRefund(String orderSn) {
        JsonResult<CouponPkgRefundResultVo> result;
        try {
            result = couponPkgFeignClient.refund(orderSn);
        } catch (Exception e) {
            log.error("卡券订单退款异常", e);
            throw e;
        }
        if (Objects.isNull(result) || !Objects.equals(result.getState(), OrderConst.RESULT_STATE_SUCCESS) || Objects.isNull(result.getData())) {
            log.error("卡券订单退款失败,返回结果:{}", JSONObject.toJSONString(result));
            throw new BusinessException(String.format("卡券订单退款失败,返回结果:%s", JSONObject.toJSONString(result)));
        }
        return result.getData();
    }

}
