package com.cfpamf.ms.mallorder.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.math.BigDecimal;

/**
 * 订单交易真实性资料配置
 *
 * <AUTHOR>
 * @since 2024/10/22
 */
@RefreshScope
@EnableConfigurationProperties
@Configuration
@ConfigurationProperties(prefix = "material")
@Data
public class OrderMaterialConfig {

    /**
     * 收货确认书资料编码
     */
    private String receiveMaterialNo = "100101005";
    /**
     * 电子签收货确认书资料编码
     *
     */
    private String eleReceiveMaterialNo = "200101021";

    /**
     * 免责声明原始文档
     *
     */
    private String originContext = "收货确认函致中和农服（北京）农业科技有限公司" +
            "本人收到货物清单如下：序号" +
            "产品名称" +
            "规格" +
            "数量" +
            "单价（元）" +
            "合计（元）" +
            "收货地址" +
            "下单时间" +
            "收货人" +
            "联系方式" +
            "送货人" +
            "订单编号" +
            "签署此确认单即表明您对于上述产品验收无误，此后产生的货物毁损灭失的风险由您自行承担；以" +
            "备注" +
            "上内容填写需确实、完整，字迹要工整。" +
            "其他本人确认知悉：" +
            "1、本人是使用中和农信旗下小微金融机构提供的信货服务购买的上述货物，本人己确认收到并验货合格。" +
            "2、中和农信旗下小微金融机构是在本人的指示下将相关借款资金支付至本人指定的银行账户中。" +
            "3、本人知悉，我与上达货物买卖的相对方之间发生的货物买卖关系是独立的法律关系，与中和农信旗下小" +
            "微金融机构无关。我保证，绝不因与货物销售方之问的任何买卖纠纷而拒绝履行本人与中和农信旗下小微" +
            "金融机构之问签署的借贷合同项下约定的还款义务。" +
            "4、本人将及时关注借款信息及还款计划；并严格按照本人与中和农信旗下小微金融机构签署的借货合同之" +
            "还款计划约定，按期履行还款义务。" +
            "5、本人确认知悉，本人在中和农信旗下小徽金融机构的借款和还款信息将上传到中国人民银行征信系统，" +
            "本人将严格按照相关合同约定及时还款，保持良好的信用记录。" +
            "本人己充分知晓并理解上述全部内容。";
    /**
     * 匹配度
     */
    private BigDecimal matchRatio = new BigDecimal("0.7");

    /**
     * 示例图地址
     */
    private String example = "https://oms-2018.oss-cn-hangzhou.aliyuncs.com/template/agric-order-confirmation-pic-demo.png";

    /**
     * 纸质签示例文件
     */
    private String paperExample = "https://mall-sld-prod.oss-cn-beijing.aliyuncs.com/tempImages/100101021.jpg";

    /**
     * 农服经销商合同资料编码
     */
    private String agricDealerMaterialNo = "300000001";

    /**
     * 农服经销商电签合同编码
     */
    private String agricEleInfo = "agric_deal_contract";

}
