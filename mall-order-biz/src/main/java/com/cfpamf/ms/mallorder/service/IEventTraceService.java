package com.cfpamf.ms.mallorder.service;

import com.cfpamf.ms.mallorder.dto.EventTraceDTO;
import com.cfpamf.ms.mallorder.po.EventTracePO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.ms.mallorder.vo.EventTraceVO;

import java.util.List;

/**
 * <p>
 * 操作日志记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-11
 */
public interface IEventTraceService extends IService<EventTracePO> {
    default EventTraceDTO remakeEventTraceDTO(EventTraceDTO template, String targetObject, String contentTitle, String beforeValue, String afterValue){
        return remakeEventTrace(template,targetObject,contentTitle,beforeValue,afterValue);
    }

    static EventTraceDTO remakeEventTrace(EventTraceDTO template,String targetObject,String contentTitle, String beforeValue,String afterValue){
        EventTraceDTO eventTraceDTO = new EventTraceDTO(template);
        eventTraceDTO.setTargetObject(targetObject);
        eventTraceDTO.setContentTitle(contentTitle);
        eventTraceDTO.setBeforeValue(beforeValue);
        eventTraceDTO.setAfterValue(afterValue);
        return eventTraceDTO;
    }

    <T> void track(T executor, EventTraceDTO dto, String operateResult);
    <T> void batchTrack(T executor, List<EventTraceDTO> dtoList, String operateResult);

    List<EventTraceVO> listHistoryDiffContent(EventTraceDTO dto);

    List<EventTraceVO> listHistoryDiffContent(String targetDomain, List<String> targetObjectList);
}
