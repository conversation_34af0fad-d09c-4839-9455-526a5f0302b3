package com.cfpamf.ms.mallorder.integration.omsbase;

import com.alibaba.fastjson.JSON;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.mallorder.dto.RuleServiceFeeQueryDTO;
import com.cfpamf.ms.mallorder.integration.facade.OmsBaseFacade;
import com.slodon.bbc.core.exception.MallException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Create 2021-10-20 09:58
 * @Description :乡助平台外部调用
 */
@Slf4j
@Component
public class OmsBaseIntegration {

    @Autowired
    private OmsBaseFacade omsBaseFacade;

    /**
     * @return java.util.List<com.cdfinance.ms.card.facade.model.response.cardUse.CardUserQueryResponse>
     * @description :查询是否需要平台服务费
     */
    public Boolean query(RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO, String orderSn) {
        log.info("查询平台服务费，请求参数:{},订单号:{}", JSON.toJSON(ruleServiceFeeQueryDTO), orderSn);
        Result<Boolean> query;
        try {
            query = omsBaseFacade.query(ruleServiceFeeQueryDTO);
        } catch (Exception e) {
            throw new MallException("查询是否需要平台服务费异常" + orderSn, 999, e);
        }
        if (query == null || query.getData() == null) {
            throw new MallException("查询是否需要平台服务费未返回任何信息，请排查" + orderSn);
        }
        if (!query.isSuccess()) {
            throw new MallException("查询是否需要平台服务费返回失败，请排查" + orderSn);
        }
        return query.getData();
    }
}
