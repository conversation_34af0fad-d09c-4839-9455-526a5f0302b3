package com.cfpamf.ms.mallorder.service.payment;

/**
 * 类RetundHandleType.java的实现描述：
 *
 * <AUTHOR> 00:39
 */
public enum RetundHandleType {
    COMMON("common", "公共"), LOAN("loan", "信贷");

    private String code;
    private String desc;

    private RetundHandleType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}
