package com.cfpamf.ms.mallorder.controller.front;

import com.cfpamf.ms.mallorder.dto.SellerPrivilegeDTO;
import com.cfpamf.ms.mallorder.scheduler.OrderCancelSeckillJob;
import com.cfpamf.ms.mallorder.scheduler.OrderLoanLendingJob;
import com.cfpamf.ms.mallorder.service.IOrderService;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Classname FrontJobController
 * @Description job任務臨時調度controller
 * <AUTHOR>
 * @Date 2021/6/28 15:58
 * @Version 1.0
 **/
@RestController
@RequestMapping("front/job")
@Api(tags = "front-job調度controller")
@Slf4j
public class FrontJobController {
    @Autowired
    private OrderLoanLendingJob orderLoanLendingJob;

    @Autowired
    private OrderCancelSeckillJob seckillJob;

    @GetMapping("/OrderCancelSeckillJob")
    @ApiOperation("秒杀自动取消 job")
    public JsonResult<SellerPrivilegeDTO> jobSystemCancelOrder(Integer storeId) {
        seckillJob.execute("");
        return SldResponse.success();
    }

    @GetMapping("orderLoanLendingJob")
    public JsonResult<String> getLoanProductInfo() throws Exception {
        orderLoanLendingJob.execute("template");
        return SldResponse.success("job調度成功");
    }

    @Autowired
    private IOrderService orderService;

    /**
     * 清洗优惠数据
     * @return
     * @throws Exception
     */
    @GetMapping("cleanPromotionPrice")
    public JsonResult<String> cleanPromotion() {
        orderService.cleanPromotion();
        return SldResponse.success("清洗优惠数据完成");
    }

}
