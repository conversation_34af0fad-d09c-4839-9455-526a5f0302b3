package com.cfpamf.ms.mallorder.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.ms.mallorder.dto.*;
import com.cfpamf.ms.mallorder.integration.facade.dto.SiteVo;
import com.cfpamf.ms.mallorder.po.OrderOfflinePO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.request.OrderOfflineRequest;
import com.cfpamf.ms.mallorder.vo.CustInfoVo;
import com.cfpamf.ms.mallorder.vo.OrderOfflineInfoVO;
import com.cfpamf.ms.mallorder.vo.kingdee.KingdeeCustomerVo;
import com.cfpamf.ms.mallshop.resp.Vendor;
import com.cfpamf.ms.mallsystem.vo.Admin;

import java.util.List;

public interface OrderOfflineService extends IService<OrderOfflinePO> {

	/**
	 * 查询线下补录订单回执单信息
	 * 
	 * @param paySn
	 * @return
	 */
	List<OrderOfflinePO> queryOrderOfflineList(String paySn);

	CustInfoVo queryCustInfo(String jobNumber);
	
	/**
	 * 线下补录订单手动结算
	 * @param vendor
	 * @param orderOfflineManualSettlement
	 */
	void manualSettlement(Vendor vendor,OrderOfflineManualSettlementDTO orderOfflineManualSettlement);

	/**
	 * 线下补录订单手动结算
	 * @param vendor
	 * @param orderOfflineManualSettlement
	 */
	void manualSettlementV2(Vendor vendor,OrderOfflineManualSettlementDTO orderOfflineManualSettlement);


	/**
	 * 批量保存线下补录订单补充信息
	 * 
	 * @param list
	 */
	void saveBatch(List<OrderOfflinePO> list);

    void payOrder(OrderPO orderPO);

    void deliveryOrder(String orderSn, Vendor vendor,String deliveryWarehouse,String deliveryWarehouseName);

	OrderOfflineManualDTO getManualInfo(String orderSn);

	void manualPay(OrderOfflineManualDTO orderOfflineManualDTO, Vendor vendor);

	void manualPayAdmin(OrderOfflineManualDTO orderOfflineManualDTO, Admin admin);

	void manualDelivery(OrderOfflineManualDTO orderOfflineManualDTO, Vendor vendor);

	void manualReceive(OrderOfflineManualDTO orderOfflineManualDTO, Vendor vendor);

	void infoSupplement(OrderOfflineManualDTO orderOfflineManualDTO, Vendor vendor);

	Page<SiteVo> getSiteByPage(WmsSiteDTO wmsSiteDTO);

    void batchSaveInvoiceLabel(OrderOfflineInvoiceLabelDTO orderOfflineInvoiceLabelDTO, Vendor vendor);

	void batchSaveAccountPeriodDays(OrderOfflineAccountPeriodDaysDTO orderOfflineInvoiceLabelDTO, Vendor vendor);

	void batchSaveSupplierCode(OrderOfflineSupplierCodeDTO orderOfflineInvoiceLabelDTO, Vendor vendor);

    String getEmployeeBranchCode(String employeeCode);

	/**
	 * 钉钉通知财务
	 * @param orderSn
	 * @return
	 */
	Boolean notifyByDingTalk(String orderSn);

	void infoSupplementAdmin(OrderOfflineManualDTO orderOfflineManualDTO, Admin admin);

	List<OrderOfflineInfoVO> getReceiptInfoList(OrderOfflineRequest request);

	/**
	 * 查询获取金蝶客户分页列表
	 * @param query
	 * @param storeId
	 * @return
	 */
	Page<KingdeeCustomerVo> getKingdeeCustomerPage(KingdeeCustomerQuery query, Long storeId);
}
