package com.cfpamf.ms.mallorder.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class OfflineOrderListQueryRequest extends OrderListQueryRequest {

    @ApiModelProperty("线下补录订单收款截止日期")
    private Date overdueTimeAfter;

    @ApiModelProperty("线下补录订单收款截止日期")
    private Date overdueTimeBefore;

    @ApiModelProperty(value = "线下补录订单是否逾期 默认 0 - 未逾期， 1 - 已逾期")
    private Integer overdueFlag;

    @ApiModelProperty("线下补录订单买方供应商编码")
    private String buyerSupplierCode;


}
