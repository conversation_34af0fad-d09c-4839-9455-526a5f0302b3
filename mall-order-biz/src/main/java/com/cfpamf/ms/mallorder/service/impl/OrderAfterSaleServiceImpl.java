package com.cfpamf.ms.mallorder.service.impl;


import com.cfpamf.ms.mallorder.common.enums.OperationRoleEnum;
import com.cfpamf.ms.mallorder.common.enums.OrderCreateChannel;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.ExtAfterSalesApplyDTO;
import com.cfpamf.ms.mallorder.dto.OrderForceRefundDTO;
import com.cfpamf.ms.mallorder.enums.FacadeOrderReturnStatus;
import com.cfpamf.ms.mallorder.model.OrderReturnModel;
import com.cfpamf.ms.mallorder.po.OrderAfterPO;
import com.cfpamf.ms.mallorder.req.admin.AdminForceRefundRequest;
import com.cfpamf.ms.mallorder.service.IOrderAdminReturnService;
import com.cfpamf.ms.mallorder.service.IOrderAfterSaleService;
import com.cfpamf.ms.mallorder.v2.common.lock.Lock;
import com.cfpamf.ms.mallshop.resp.Store;
import com.cfpamf.ms.mallshop.resp.Vendor;
import com.cfpamf.ms.mallsystem.vo.Admin;
import com.slodon.bbc.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * @Author: zml
 * @CreateTime: 2022/7/25 11:07
 */
@Service
@Slf4j
public class OrderAfterSaleServiceImpl implements IOrderAfterSaleService {

    @Resource
    private IOrderAdminReturnService orderAdminReturnService;

    @Autowired
    private Lock lock;

    @Resource
    private OrderReturnModel orderReturnModel;

    @Override
    public void forceRefund(OrderForceRefundDTO orderForceRefundDTO) {
        Admin admin = new Admin();
        AdminForceRefundRequest adminForceRefundRequest = new AdminForceRefundRequest();
        adminForceRefundRequest.setOrderSns(orderForceRefundDTO.getOrderSn());
        adminForceRefundRequest.setRemark(Optional.ofNullable(orderForceRefundDTO.getRemark()).orElse("下单失败!"));
        orderAdminReturnService.adminForceRefund(admin, adminForceRefundRequest);
    }

    /**
     * 商家审批接口
     * 云中鹤仅处理拒绝售后申请的情况
     * @param extAfterSalesApplyDTO
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void extSellerAudit(ExtAfterSalesApplyDTO extAfterSalesApplyDTO) {
        OrderCreateChannel orderCreateChannel = OrderCreateChannel.getEnumByValue(extAfterSalesApplyDTO.getChannel());
        BizAssertUtil.notNull(orderCreateChannel,"未知渠道，请填写正确的渠道");

        if(!FacadeOrderReturnStatus.STORE_AGREE_REFUND.getValue().equals(extAfterSalesApplyDTO.getState())
                && !FacadeOrderReturnStatus.STORE_REFUSED.getValue().equals(extAfterSalesApplyDTO.getState())){
            throw new BusinessException("仅支持商家拒绝售后申请和商家同意仅退款申请两种审批状态，afsSn = " + extAfterSalesApplyDTO.getAfsSn() + ",state = " + extAfterSalesApplyDTO.getState());
        }

        // 查询售后服务信息
        OrderAfterPO orderAfterServicePODb = orderReturnModel.getOrderAfterServiceByAfsSn(extAfterSalesApplyDTO.getAfsSn());

        if(orderAfterServicePODb == null){
            throw new BusinessException("为查询到该售后单，afsSn = " + extAfterSalesApplyDTO.getAfsSn());
        }

        Vendor vendor = new Vendor();
        vendor.setStoreId(orderAfterServicePODb.getStoreId());
        Store store = new Store();
        store.setStoreId(orderAfterServicePODb.getStoreId());
        store.setStoreName(orderCreateChannel.getDesc());
        vendor.setStore(store);
        vendor.setVendorId(OrderConst.LOG_USER_ID);
        vendor.setVendorName(OrderConst.LOG_USER_NAME);
        boolean isPass = FacadeOrderReturnStatus.STORE_AGREE_REFUND.getValue().equals(extAfterSalesApplyDTO.getState());
        String lockKey = String.format("OrderAfterSaleServiceImpl:extAfsApplyCallBack:%s", extAfterSalesApplyDTO.getAfsSn());
        lock.tryLockExecuteFunction(lockKey, 0, 10, TimeUnit.SECONDS,
                () -> orderReturnModel.afsStoreAudit(vendor, extAfterSalesApplyDTO.getAfsSn(), isPass,
                        extAfterSalesApplyDTO.getRefuseReason(), null, extAfterSalesApplyDTO.getChannel(), null,
                        BigDecimal.ZERO, OperationRoleEnum.STORE,null));
    }


}
