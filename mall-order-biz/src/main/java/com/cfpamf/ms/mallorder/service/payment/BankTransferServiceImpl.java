package com.cfpamf.ms.mallorder.service.payment;

import com.alibaba.fastjson.JSON;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.mallpayment.facade.api.MallPaymentFacade;
import com.cfpamf.mallpayment.facade.request.PaymentRefundRequest;
import com.cfpamf.mallpayment.facade.vo.PaymentRefundVO;
import com.cfpamf.ms.mall.liquidate.api.BizActionFacade;
import com.cfpamf.ms.mall.liquidate.enums.ActionEnum;
import com.cfpamf.ms.mall.liquidate.request.RevokeReq;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.cfpamf.ms.mallorder.mapper.OrderPayMapper;
import com.cfpamf.ms.mallorder.model.OrderAfterServiceModel;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderPayPO;
import com.cfpamf.ms.mallorder.po.OrderReturnPO;
import com.cfpamf.ms.mallorder.request.OrderPayExample;
import com.cfpamf.ms.mallorder.service.IOrderPlacingService;
import com.cfpamf.ms.mallorder.validation.OrderReturnValidation;
import com.cfpamf.ms.mallshop.api.StoreFeignClient;
import com.cfpamf.ms.mallsystem.vo.Admin;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.exception.MallException;
import com.slodon.bbc.core.response.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.net.SocketTimeoutException;
import java.util.Map;

/**
 * <AUTHOR>
 * @Create 2022-05-09 13:39
 * @Description :微信支付相关处理方法
 */
@Slf4j
@Service("BANK_TRANSFER_ServiceImpl")
public class BankTransferServiceImpl extends AbstractPayHandleServiceImpl {

    public static final String SYSTEM_CODE = "mallOrder";
    @Value("${spring.application.name}")
    private String appName;
    @Autowired
    private BizActionFacade bizActionFacade;
    @Resource
    private OrderPayMapper orderPayMapper;
    @Value("${mall-payment.refundNotify}")
    private String refundNotifyUrl;
    @Autowired
    private StoreFeignClient storeFeignClient;
    @Autowired
    private MallPaymentFacade mallPaymentFacade;
    @Autowired
    private IOrderPlacingService orderPlacingService;

    @Resource
    private OrderAfterServiceModel orderAfterServiceModel;

    @Autowired
    private OrderReturnValidation orderReturnValidation;

    /**
     * @param orderPO
     * @param orderReturnPO
     * @param extendParam
     * @param actionType
     * @param admin
     * @return java.lang.Boolean
     * @description : 退款
     */
    @Override
    public Boolean paymentRefund(OrderPO orderPO, OrderReturnPO orderReturnPO, Map<String, Object> extendParam,
                                 String actionType, Admin admin) {
        // 实际退款金额 = 实际退款金额 + 退还运费
        BigDecimal refundAmount = orderReturnPO.getActualReturnMoneyAmount().add(orderReturnPO.getReturnExpressAmount());

        OrderPayExample example = new OrderPayExample();
        example.setPaySn(orderPO.getPaySn());
        OrderPayPO orderPayPO = orderPayMapper.listByExample(example).get(0);

        PaymentRefundRequest refundRequest = new PaymentRefundRequest();
        refundRequest.setOrderOn(orderPO.getPaySn());
        refundRequest.setRefundOn(orderReturnPO.getAfsSn());
        refundRequest.setSystemCode(SYSTEM_CODE);
        refundRequest.setRefundAmt(refundAmount);
        refundRequest.setRefundCreateTime(System.currentTimeMillis());
        refundRequest.setRefundReason("整单取消");
        refundRequest.setNotifyUrl(refundNotifyUrl);

        refundRequest.setPlatformAmount(
                orderReturnPO.getCommissionAmount()
                        .add(orderReturnPO.getServiceFee())
                        .add(orderReturnPO.getThirdpartnarFee()));
        refundRequest.setPlatformFlag(1);
        refundRequest.setOtherAmount(orderReturnPO.getReturnExpressAmount());
        refundRequest.setSubsidyAmount(orderReturnPO.getPlatformVoucherAmount());

        Result<PaymentRefundVO> refundVO = null;
        try {
            refundVO = mallPaymentFacade.paymentRefund(refundRequest);
        } catch (Exception ex) {
            orderAfterServiceModel.updateRefundFailReasonByAfsSn(orderReturnPO.getAfsSn(), "payment-service退款异常");
            throw new MallException("payment-service：退款异常", ErrorCodeEnum.C.CALL_EXCEPTION.getCode(), ex);
        }

        if (refundVO == null || refundVO.getData() == null) {
            orderAfterServiceModel.updateRefundFailReasonByAfsSn(orderReturnPO.getAfsSn(), "payment-service退款异常");
            throw new MallException("payment-service：退款失败：null", ErrorCodeEnum.C.CALL_EXCEPTION.getCode());
        }

        if (!refundVO.isSuccess()) {
            orderAfterServiceModel.updateRefundFailReasonByAfsSn(orderReturnPO.getAfsSn(), refundVO.getMessage());
            throw new MallException("payment-service：退款失败:" + JSON.toJSONString(refundVO), ErrorCodeEnum.C.CALL_EXCEPTION.getCode());
        }
        return Boolean.TRUE;
    }

    /**
     * 调用结算下账
     * @param orderPO
     * @param orderReturnPO
     * @param actionType
     * @return
     */
    @Override
    public Boolean syncExecute(OrderPO orderPO, OrderReturnPO orderReturnPO, String actionType) {
        RevokeReq req = new RevokeReq();
        req.setBizNo(orderReturnPO.getAfsSn());
        req.setActionType(ActionEnum.CHANNELSERVFEE);
        if (orderReturnValidation.isNeedCommissionIncentiveWhenBookkeeping(orderPO,orderReturnPO)) {
            req.setNextActionType(new ActionEnum[]{ActionEnum.COMMISSIONINCENTIVE, ActionEnum.SHAREPROFIT, ActionEnum.MARKETING,
                    ActionEnum.BANKTRANSFER});
        } else {
            req.setNextActionType(new ActionEnum[]{ActionEnum.SHAREPROFIT, ActionEnum.MARKETING, ActionEnum.BANKTRANSFER});
        }
        req.setCreateBy(appName);
        req.setExecuteBizNo(orderReturnPO.getOrderSn());
        JsonResult<Void> result;
        try {
            result = bizActionFacade.asyncRevoke(req);
            if (null == result) {
                log.warn("liquidate 调用贷款类赔偿退款接口失败,bizSn: {}", orderReturnPO.getAfsSn());
                throw new MallException("调用贷款类赔偿退款接口未返回任何信息,bizSn:" + req.getBizNo());
            }
            if (200 != result.getState()) {
                throw new MallException(result.getMsg());
            }
        } catch (Exception e) {
            if (e.getCause() instanceof SocketTimeoutException) {
                log.error("调用贷款类赔偿退款接口超时，请排查:{}", orderReturnPO.getAfsSn());
            } else {
                throw e;
            }
        }
        return Boolean.TRUE;
    }

    @Override
    public void verifyExecute(OrderPO orderPO, OrderReturnPO returnPO, String actionType) {
        OrderPO orderByOrderPOSn = orderPlacingService.getByOrderSn(returnPO.getOrderSn());
        if (!orderByOrderPOSn.getNewOrder()) {
            return;
        }
        RevokeReq executeReq = new RevokeReq();
        executeReq.setBizNo(returnPO.getAfsSn());
        executeReq.setActionType(ActionEnum.BANKTRANSFER);
        executeReq.setCreateBy(appName);
        executeReq.setExecuteBizNo(returnPO.getOrderSn());
        JsonResult<Void> jsonResult = null;
        try {
            jsonResult = bizActionFacade.balanceSurplus(executeReq);
        } catch (Exception e) {
            log.error("liquidate 调用结算校验用户资金余额异常：{}：", e.getMessage());
            throw e;
        }
        if (jsonResult == null ) {
            log.warn("调用liquidate 调用结算校验用户资金余额为空，退款单号：{}", returnPO.getAfsSn());
            throw new MallException("调用liquidate 调用结算校验用户资金余额未返回任何信息:{}" + returnPO.getAfsSn());
        }
        if (200 != jsonResult.getState()) {
            throw new BusinessException(jsonResult.getMsg());
        }
    }



}
