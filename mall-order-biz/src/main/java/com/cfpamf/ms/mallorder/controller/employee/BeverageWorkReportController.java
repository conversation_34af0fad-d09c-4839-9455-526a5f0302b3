package com.cfpamf.ms.mallorder.controller.employee;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.common.config.BeverageWorkReportConfig;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.util.FlowUtil;
import com.cfpamf.ms.mallorder.controller.fegin.facade.workflow.util.MyPage;
import com.cfpamf.ms.mallorder.controller.fegin.facade.workflow.vo.WorkflowTaskDetailVO;
import com.cfpamf.ms.mallorder.dto.AuditTaskSearchVO;
import com.cfpamf.ms.mallorder.dto.UserTokenDTO;
import com.cfpamf.ms.mallorder.model.OrderAfterServiceModel;
import com.cfpamf.ms.mallorder.req.BeverageWorkReportRequest;
import com.cfpamf.ms.mallorder.service.AdminAuditTaskService;
import com.cfpamf.ms.mallorder.service.EmployeeService;
import com.cfpamf.ms.mallorder.service.IBeverageWorkReportService;
import com.cfpamf.ms.mallorder.vo.AuditTaskVO;
import com.cfpamf.ms.mallorder.vo.BeverageWorkReportVO;
import com.cfpamf.ms.mallorder.vo.refund.RefundFlowMessageVO;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.PageVO;
import com.slodon.bbc.core.response.PagerInfo;
import com.slodon.bbc.core.response.SldResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Api(tags = "employee-员工操作")
@RestController
@RequestMapping("/employee/beverage/work")
@Slf4j
public class BeverageWorkReportController {

    @Autowired
    private BeverageWorkReportConfig beverageWorkReportConfig;

    @Autowired
    private EmployeeService employeeService;

    @Autowired
    private AdminAuditTaskService adminAuditTaskService;

    @Autowired
    private FlowUtil flowUtil;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private IBeverageWorkReportService beverageWorkReportService;

    @Autowired
    private OrderAfterServiceModel orderAfterServiceModel;


    @ApiOperation(value = "查询酒水工作台报表数据")
    @PostMapping("query/report")
    public JsonResult<BeverageWorkReportVO> queryBeverageWorkReport(@RequestBody @Valid BeverageWorkReportRequest request) {
        if (CollectionUtils.isEmpty(request.getStoreIdList())) {
            request.setStoreIdList(beverageWorkReportConfig.getStoreIdList());
        }
        BeverageWorkReportVO beverageWorkReportVO = employeeService.queryBeverageWorkReport(request);
        return SldResponse.success(beverageWorkReportVO);
    }

    @ApiOperation(value = "查询流程消息")
    @PostMapping("query/flowMessageList")
    public JsonResult<PageVO<AuditTaskVO>> flowMessageList(@RequestBody AuditTaskSearchVO searchVO) {
        if (StringUtils.isEmpty(searchVO.getJobNumber())) {
            return SldResponse.fail("工号不能为空");
        }
        MyPage<WorkflowTaskDetailVO> list = null;
        PagerInfo pagerInfo = new PagerInfo(searchVO.getPageSize(), searchVO.getCurrent());
        searchVO.setProcessDefKeyList(new ArrayList<>(beverageWorkReportConfig.getProcDefKeyList()));
        switch (searchVO.getTaskType()) {
            case CommonConst.FLOW_TASK_TYPE_1:
                list = flowUtil.getTodoList(searchVO, searchVO.getJobNumber());
                break;
            case CommonConst.FLOW_TASK_TYPE_2:
                list = flowUtil.getSubmittedList(searchVO, searchVO.getJobNumber());
                break;
            case CommonConst.FLOW_TASK_TYPE_3:
                list = flowUtil.getDoneList(searchVO, searchVO.getJobNumber());
                break;
            default:
                log.warn("请传入正确任务类型!");
                break;
        }

        this.urlParamReplace(list);
        searchVO.setSfnFlag(true);
        return SldResponse.success(adminAuditTaskService.getAuditTaskVoByTaskDetailVo(pagerInfo, list, searchVO));
    }

    /**
     * url 参数解析替换
     */
    private void urlParamReplace(MyPage<WorkflowTaskDetailVO> list) {

        if (Objects.isNull(list) || CollectionUtils.isEmpty(list.getData())) {
            return;
        }

        list.getData().forEach(item -> {
            // 获取webUrl和urlParam
            String webUrl = item.getWebUrl();
            String urlParamStr = item.getUrlParam();

            if (StringUtils.isEmpty(webUrl) || StringUtils.isEmpty(urlParamStr)) {
                return;
            }

            // 解析urlParam
            JSONObject urlParam = JSON.parseObject(urlParamStr);

            // 替换webUrl中的参数
            for (String key : urlParam.keySet()) {
                String placeholder = "{" + key + "}";
                String value = urlParam.getString(key);
                webUrl = webUrl.replace(placeholder, value);
            }
            item.setWebUrl(webUrl);
        });
    }


    @ApiOperation(value = "查询流程消息详情")
    @GetMapping("query/flowMessageDetail")
    public JsonResult<Map<String, Object>> queryFlowMessageDetail(HttpServletRequest request, @RequestParam("procDefKey") String procDefKey,
                                                                  @RequestParam("nodeKey") String nodeKey, @RequestParam("bizId") String bizId) {
        return SldResponse.success(beverageWorkReportService.queryFlowMessageDetail(procDefKey, nodeKey, bizId));
    }

    @ApiOperation(value = "流程员工自提收货")
    public JsonResult<Boolean> flowMessageAuditPass(HttpServletRequest request, @RequestParam("procDefKey") String procDefKey,
                                                         @RequestParam("processId") String processId, @RequestParam("flowJobNumber") String flowJobNumber) {
        flowUtil.auditPass(processId,"商家审批同意", flowJobNumber);
        return SldResponse.success();
    }


    @ApiOperation("用户token缓存")
    @PostMapping("redis/save")
    public JsonResult<Boolean> save(@RequestBody @Valid UserTokenDTO userTokenDTO) {
        if (StringUtils.isEmpty(userTokenDTO.getTokenJson())) {
            return SldResponse.fail("tokenJson不能为空");
        }
        stringRedisTemplate.opsForValue().set(userTokenDTO.getKey(), userTokenDTO.getTokenJson(), 2, TimeUnit.HOURS);
        return SldResponse.success();
    }

    @ApiOperation("用户token缓存")
    @PostMapping("redis/get")
    public JsonResult<UserTokenDTO> get(@RequestBody @Valid UserTokenDTO userTokenDTO) {
        userTokenDTO.setTokenJson(stringRedisTemplate.opsForValue().get(userTokenDTO.getKey()));
        return SldResponse.success(userTokenDTO);
    }


}
