package com.cfpamf.ms.mallorder.v2.service.impl;

import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.mallgoods.facade.api.GoodsExtendFeignClient;
import com.cfpamf.ms.mallgoods.facade.api.GoodsFeignClient;
import com.cfpamf.ms.mallgoods.facade.api.ProductFeignClient;
import com.cfpamf.ms.mallmember.api.MemberInvoiceFeignClient;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.common.constant.CartConst;
import com.cfpamf.ms.mallorder.common.enums.LoanStatusEnum;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.common.util.OrderLocalUtils;
import com.cfpamf.ms.mallorder.common.util.ValidUtils;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.OrderSubmitDTO;
import com.cfpamf.ms.mallorder.dto.OrderSubmitMqConsumerDTO;
import com.cfpamf.ms.mallorder.dto.OrderSubmitParamDTO;
import com.cfpamf.ms.mallorder.enums.OrderEventEnum;
import com.cfpamf.ms.mallorder.mapper.CartMapper;
import com.cfpamf.ms.mallorder.model.*;
import com.cfpamf.ms.mallorder.po.OrderExtendPO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderPayPO;
import com.cfpamf.ms.mallorder.po.OrderProductPO;
import com.cfpamf.ms.mallorder.request.CartExample;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.service.impl.OrderCreateHelper;
import com.cfpamf.ms.mallorder.v2.builder.OrderBuilder;
import com.cfpamf.ms.mallorder.v2.builder.OrderLogBuilder;
import com.cfpamf.ms.mallorder.v2.domain.dto.OrderDTO;
import com.cfpamf.ms.mallorder.v2.service.*;
import com.cfpamf.ms.mallorder.v2.strategy.OrderTypePlaceOrderStrategy;
import com.cfpamf.ms.mallorder.v2.strategy.context.OrderTypePlaceOrderStrategyContext;
import com.cfpamf.ms.mallshop.api.StoreFeignClient;
import com.cfpamf.ms.mallshop.resp.StoreContractReceiptInfoVO;
import com.cfpamf.smartid.client.utils.sharding.SeqEnum;
import com.cfpamf.smartid.client.utils.sharding.ShardingId;
import io.seata.core.context.RootContext;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Slf4j
@Service
public class PlaceOrderServiceImpl implements PlaceOrderService {

    @Resource
    private GoodsFeignClient goodsFeignClient;

    @Resource
    private GoodsExtendFeignClient goodsExtendFeignClient;

    @Resource
    private ProductFeignClient productFeignClient;

    @Resource
    private CartMapper cartMapper;

    @Autowired
    private ValidUtils validUtils;

    @Resource
    private OrderExtendModel orderExtendModel;

    @Autowired
    private ShardingId shardingId;

    @Autowired
    private StoreFeignClient storeFeignClient;

    @Autowired
    private OrderLocalUtils orderLocalUtils;

    @Autowired
    private OrderProductModel orderProductModel;

    @Resource
    private OrderPayModel orderPayModel;

    @Autowired
    private OrderCreateHelper orderCreateHelper;

    @Resource
    private BankTransferModel bankTransferModel;

    @Autowired
    private IOrderPayService iOrderPayService;

    @Autowired
    private IBzBankTransferService iBzBankTransferService;

    @Autowired
    private IOrderExtendService iOrderExtendService;

    @Autowired
    private OrderModel orderModel;

    @Autowired
    private OrderDetailService orderDetailService;

    @Autowired
    private IOrderLogService orderLogService;

    @Autowired
    private MemberInvoiceFeignClient memberInvoiceFeignClient;

    @Autowired
    private IOrderProductService orderProductService;

    @Autowired
    private IOrderProductExtendService orderProductExtendService;

    @Autowired
    private IOrderExtendService orderExtendService;

    @Autowired
    private IOrderPromotionDetailService orderPromotionDetailService;

    @Autowired
    private OrderPromotionSendCouponService orderPromotionSendCouponService;

    @Autowired
    private OrderPromotionSendProductService orderPromotionSendProductService;

    @Autowired
    private OrderDeductionService orderDeductionService;

    @Autowired
    private OrderPayProcessService orderPayProcessService;

    @Autowired
    private OrderPresellService orderPresellService;

    @Autowired
    private OrderPayRecordService orderPayRecordService;

    @Autowired
    private OrderTypePlaceOrderStrategyContext orderTypePlaceOrderStrategyContext;

    @Override
    @GlobalTransactional(rollbackFor = Throwable.class)
    public Set<String> submitOrder(OrderSubmitDTO orderSubmitDTO, Member member, OrderSubmitMqConsumerDTO consumerDTO) {
        // 构建OrderDTO信息
        OrderDTO order = OrderBuilder.buildOrderDTO();
        try {
            String verifyCodeTmp = orderSubmitDTO.getVerifyCode();
            log.info("【订单下单】==============提交订单==>>xid：【{}】 {} 验证码verifyCodeTmp：{}", RootContext.getXID(),
                orderSubmitDTO, verifyCodeTmp);
            String verifyCode = consumerDTO.getParamDTO().getVerifyCode();
            log.info("【订单下单】==============验证码verifyCode:{}", verifyCode);
            // 父订单号，一批订单共有一个
            String parentSn = String.valueOf(shardingId.next(SeqEnum.ONO, member.getMemberId().toString()));
            order.setParentSn(parentSn);
            // 支付单号，一批订单共有一个
            String paySn = consumerDTO.getPaySn();
            OrderSubmitParamDTO orderSubmitParamDTO = consumerDTO.getParamDTO();
            // 总支付金额，含运费
            BigDecimal payAmount = new BigDecimal("0.00");
            // 构建详细订单数据
            this.buildOrderInfo(order, orderSubmitDTO, member, consumerDTO, parentSn, paySn, orderSubmitParamDTO,
                payAmount);
            // 银行转账汇款支付的订单，购物车删除逻辑移到submitBankTransfer方法中
            this.deleteNoBankTransferMemberCart(orderSubmitParamDTO, member);
            // 批量写入订单数据
            this.orderSaveBatch(order);
            // 扣减订单资源
            orderDeductionService.deductionOrderResource(orderSubmitDTO, member, order);
            // 0金额订单支付处理
            this.zeroAmountOrderPayProcess(orderSubmitDTO, member, order, verifyCode);
            // 记录订单变更通知事件,并在事务提交后执行
            this.placeOrderEventNotification(order);
            return new HashSet<>();// 兼容之前版本返回
        } catch (Exception e) {
            log.warn("【订单下单】错误========xid：【{}】 parentSn:{} orderSubmitDTO：{} 异常:{}", RootContext.getXID(),
                order.getParentSn(), JSON.toJSONString(orderSubmitDTO), e);
            this.placeOrderErrorEventNotification(order);
            throw e;
        }
    }

    /**
     * 批量保存订单信息
     * 
     * @param order
     */
    private void orderSaveBatch(OrderDTO order) {
        log.info("【订单下单】【订单下单批量保存】orderSaveBatch===parentSn:{}==paySn:{}===order:{}", order.getParentSn(),
            order.getOrderPay().getPaySn(), JSON.toJSONString(order));
        boolean orderSaveStatus = orderDetailService.saveBatch(order.getOrders());// 批量保存订单信息
        log.info("【订单下单】【订单下单批量保存】orderSaveBatch=====paySn:{}===orderSaveStatus:{}", order.getOrderPay().getPaySn(),
            orderSaveStatus);
        BizAssertUtil.isTrue(!orderSaveStatus, "抱歉，下单失败，请稍后再试！");

        boolean orderPaySaveStatus = iOrderPayService.save(order.getOrderPay());
        log.info("【订单下单】【订单下单批量保存】orderSaveBatch=====paySn:{}===orderPaySaveStatus:{}", order.getOrderPay().getPaySn(),
            orderPaySaveStatus);
        BizAssertUtil.isTrue(!orderPaySaveStatus, "抱歉，下单失败，请稍后再试！");

        boolean orderExtendSaveStatus = orderExtendService.saveBatch(order.getOrderExtends());
        log.info("【订单下单】【订单下单批量保存】orderSaveBatch=====paySn:{}===orderExtendSaveStatus:{}",
            order.getOrderPay().getPaySn(), orderExtendSaveStatus);
        BizAssertUtil.isTrue(!orderExtendSaveStatus, "抱歉，下单失败，请稍后再试！");

        boolean orderProductSaveStatus = orderProductService.saveBatch(order.getOrderProducts());
        log.info("【订单下单】【订单下单批量保存】orderSaveBatch=====paySn:{}===orderProductSaveStatus:{}",
            order.getOrderPay().getPaySn(), orderProductSaveStatus);
        BizAssertUtil.isTrue(!orderProductSaveStatus, "抱歉，下单失败，请稍后再试！");

        if (!CollectionUtils.isEmpty(order.getOrderProductExtends())) {
            boolean orderProductExtendSaveStatus = orderProductExtendService.saveBatch(order.getOrderProductExtends());
            log.info("【订单下单】【订单下单批量保存】orderSaveBatch=====paySn:{}===orderProductExtendSaveStatus:{}",
                order.getOrderPay().getPaySn(), orderProductExtendSaveStatus);
            BizAssertUtil.isTrue(!orderProductExtendSaveStatus, "抱歉，下单失败，请稍后再试！");
        }

        if (!CollectionUtils.isEmpty(order.getOrderPromotionDetails())) {
            boolean orderPromotionDetailSaveStatus =
                orderPromotionDetailService.saveBatch(order.getOrderPromotionDetails());
            log.info("【订单下单】【订单下单批量保存】orderSaveBatch=====paySn:{}===orderPromotionDetailSaveStatus:{}",
                order.getOrderPay().getPaySn(), orderPromotionDetailSaveStatus);
            BizAssertUtil.isTrue(!orderPromotionDetailSaveStatus, "抱歉，下单失败，请稍后再试！");
        }
        if (!CollectionUtils.isEmpty(order.getOrderPromotionSendCoupons())) {
            boolean orderPromotionSendCouponSaveStatus =
                orderPromotionSendCouponService.saveBatch(order.getOrderPromotionSendCoupons());
            log.info("【订单下单】【订单下单批量保存】orderSaveBatch=====paySn:{}===orderPromotionSendCouponSaveStatus:{}",
                order.getOrderPay().getPaySn(), orderPromotionSendCouponSaveStatus);
            BizAssertUtil.isTrue(!orderPromotionSendCouponSaveStatus, "抱歉，下单失败，请稍后再试！");
        }
        if (!CollectionUtils.isEmpty(order.getOrderPromotionSendProducts())) {
            boolean orderPromotionSendProductSaveStatus =
                orderPromotionSendProductService.saveBatch(order.getOrderPromotionSendProducts());
            log.info("【订单下单批量保存】orderSaveBatch=====paySn:{}===orderPromotionSendProductSaveStatus:{}",
                order.getOrderPay().getPaySn(), orderPromotionSendProductSaveStatus);
            BizAssertUtil.isTrue(!orderPromotionSendProductSaveStatus, "抱歉，下单失败，请稍后再试！");
        }
        boolean orderLogSaveStatus = orderLogService.saveBatch(order.getOrderLogs());
        log.info("【订单下单】【订单下单批量保存】orderSaveBatch=====parentSn:{}==paySn:{}===orderLogSaveStatus:{}",
            order.getParentSn(), order.getOrderPay().getPaySn(), orderLogSaveStatus);
        BizAssertUtil.isTrue(!orderLogSaveStatus, "抱歉，下单失败，请稍后再试！");

        OrderTypePlaceOrderStrategy orderStrategyContextStrategy =
            orderTypePlaceOrderStrategyContext.getStrategy(order.getPromotionTypeMark());
        orderStrategyContextStrategy.save4OrderType(order);

    }

    /**
     * 下单失败事件通知
     * 
     * @param order
     */
    private void placeOrderErrorEventNotification(OrderDTO order) {
        for (OrderPO orderPO : order.getOrders()) {
            /**
             * 记录订单变更通知事件,并在事务提交后执行
             */
            orderCreateHelper.addOrderChangeEvent(orderPO, OrderEventEnum.FAIL, orderPO.getCreateTime());
        }
    }

    /**
     * 下单事件通知
     * 
     * @param order
     */
    private void placeOrderEventNotification(OrderDTO order) {
        for (OrderPO orderPO : order.getOrders()) {
            /**
             * 记录订单变更通知事件,并在事务提交后执行
             */
            orderCreateHelper.addOrderChangeEvent(orderPO, OrderEventEnum.CREATE, orderPO.getCreateTime());
        }
    }

    /**
     * 删除会员购物车，不包含银行转账汇款支付的订单
     * 
     * @param orderSubmitParamDTO
     * @param member
     */
    private void deleteNoBankTransferMemberCart(OrderSubmitParamDTO orderSubmitParamDTO, Member member) {
        // 银行转账汇款支付的订单，购物车删除逻辑移到submitBankTransfer方法中
        if (orderSubmitParamDTO.getIsCart() && !orderSubmitParamDTO.isBankTransferable()) {
            // bz_cart 删购物车
            CartExample cartExample = new CartExample();
            cartExample.setMemberId(member.getMemberId());
            cartExample.setIsChecked(CartConst.IS_CHECKED_YES);
            cartMapper.deleteByExample(cartExample);
        }
    }

    /**
     * 0金额订单支付处理
     * 
     * @param orderSubmitDTO
     * @param member
     * @param orderDTO
     * @param verifyCode
     */
    private void zeroAmountOrderPayProcess(OrderSubmitDTO orderSubmitDTO, Member member, OrderDTO orderDTO,
        String verifyCode) {
        BigDecimal totalAmount =
            orderDTO.getOrders().stream().map(OrderPO::getOrderAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 总单金额为0时才直接通过
        if (totalAmount.compareTo(BigDecimal.ZERO) == 0) {
            validUtils.checkVerifyCode(verifyCode, member, 1);
            // 订单金额为0时，直接支付成功
            orderDTO.getOrders().forEach(order -> {
                // 订单已支付
                orderPayProcessService.orderPaySuccess(order, null, order.getPaymentCode(), order.getPaymentName(),
                    new Date(), null);
            });
        }
    }

    /**
     * 构建订单详情信息
     * 
     * @param order
     * @param orderSubmitDTO
     * @param member
     * @param consumerDTO
     * @param parentSn
     * @param paySn
     * @param orderSubmitParamDTO
     * @param payAmount
     */
    public void buildOrderInfo(OrderDTO order, OrderSubmitDTO orderSubmitDTO, Member member,
        OrderSubmitMqConsumerDTO consumerDTO, String parentSn, String paySn, OrderSubmitParamDTO orderSubmitParamDTO,
        BigDecimal payAmount) {
        // 每次循环为一个店铺的订单
        Integer userIdentity = orderCreateHelper.getUserIdentity(member);
        for (OrderSubmitDTO.OrderInfo orderInfo : orderSubmitDTO.getOrderInfoList()) {
            // 订单号
            String orderSn = String.valueOf(shardingId.next(SeqEnum.ONO, member.getMemberId().toString()));
            orderInfo.setOrderSn(orderSn);
            log.info("【订单下单】============buildOrderInfo.orderSn:{} orderSubmitDTO:orderInfo:{}", orderSn,
                JSON.toJSONString(orderInfo));
            // 同一订单，金融规则必须相同，名字可能不同，名字使用逗号分隔拼接
            String ruleTag = OrderBuilder.buildRuleTags(orderInfo);
            StoreContractReceiptInfoVO storeContract =
                storeFeignClient.getStoreContractReciptInfo(orderInfo.getStoreId());

            // -bz_order 存订单
            OrderPO orderPO =
                orderModel.buildOrderPO(parentSn, orderSn, paySn, orderInfo, member, ruleTag, storeContract,null,null,null,userIdentity);
            // ============================== 构建OrderDTO信息 ==============================
            OrderBuilder.buildAddOrder(order, OrderBuilder.buildOrderPO(orderPO, orderSubmitParamDTO));

            payAmount = payAmount.add(orderPO.getOrderAmount());

            // -bz_order_extend 订单扩展信息
            OrderExtendPO orderExtendPO = OrderBuilder.buildOrderExtendPO(orderExtendModel, memberInvoiceFeignClient,
                orderSn, member.getUserNo(), orderInfo, orderSubmitParamDTO.getOrderAddress(), orderSubmitParamDTO,
                member.getMemberMobile(), storeContract, consumerDTO);
            OrderBuilder.buildAddOrderExtend(order, orderExtendPO);

            // bz_order_promotion_detail 保存订单活动优惠明细表
            OrderBuilder.buildAddOrderPromotionDetails(order,
                OrderBuilder.buildOrderPromotionDetails(orderInfo, orderSn));

            // bz_order_promotion_send_coupon 保存订单活动赠送优惠券表
            OrderBuilder.buildAddOrderPromotionSendCoupons(order,
                OrderBuilder.buildOrderPromotionSendCoupons(orderInfo, orderSn));

            // bz_order_promotion_send_product 保存订单活动赠送货品表
            OrderBuilder.buildAddOrderPromotionSendProducts(order, OrderBuilder
                .buildOrderPromotionSendProducts(goodsFeignClient, productFeignClient, orderInfo, parentSn));

            // bz_order_log 记录订单日志
            OrderBuilder.buildAddOrderLog(order,
                    OrderLogBuilder.buildOrderLogPO(OrderConst.LOG_ROLE_MEMBER, Long.valueOf(member.getMemberId()),
                            member.getMemberName(), orderSn, -1, OrderConst.ORDER_STATE_10,
                            LoanStatusEnum.DEAL_APPLY.getValue(), "用户提交订单", null));

            // bz_order_product 保存完订单货品
            List<OrderProductPO> orderProducts =
                OrderBuilder.buildOrderProductPO(orderProductModel, orderLocalUtils, goodsFeignClient,
                    goodsExtendFeignClient, orderPO, orderSubmitParamDTO, orderInfo.getOrderProductInfoList(),storeContract);
            // bz_order_product 保存完订单货品之后，保存活动赠品
            OrderBuilder.buildOrderAddOrderProducts(order, OrderBuilder.buildSendOrderProduct(goodsFeignClient,
                productFeignClient, orderSn, orderInfo, member.getMemberId(), orderProducts));
         // -bz_order_product_extend 保存订单货品扩展

            orderPO.setSettlementPrice(orderPO.getGoodsAmount().add(orderPO.getExpressFee())
                .subtract(orderPO.getStoreVoucherAmount()).subtract(orderPO.getStoreActivityAmount())
                .subtract(orderPO.getServiceFee()).subtract(orderPO.getThirdpartnarFee()));

            OrderTypePlaceOrderStrategy orderTypeStrategy =
                orderTypePlaceOrderStrategyContext.getStrategy(orderInfo.getOrderType());
            orderTypeStrategy.buildOrderInfo(order, member, orderInfo, paySn, orderSn);

        }
        // -bz_order_pay 支付信息
        OrderPayPO orderPayPO =
            orderPayModel.buildOrderPayPO(consumerDTO, orderSubmitDTO, parentSn, paySn, payAmount, member.getMemberId());
        // 订单支付信息构建
        order.setOrderPay(orderPayPO);
        log.info("【订单下单】===========buildOrderInfo=parentSn:{} paySn:{} orderDTO:{}", order.getParentSn(),
            order.getOrderPay().getPaySn(), JSON.toJSONString(order));
    }

}
