package com.cfpamf.ms.mallorder.common.mq.msg;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class PromotionDiscountMsg {

	@ApiModelProperty("分支编码")
	private String branchCode;

	@ApiModelProperty("信贷客户编码")
	private String customerCode;

	@ApiModelProperty("客户姓名")
	private String customerName;

	@ApiModelProperty("客户身份证")
	private String customerIdNo;

	@ApiModelProperty("预期减免金额（从核算试算的来）")
	private BigDecimal planDiscountAmount;

	@ApiModelProperty("订单交易号")
	private Long tradeNo;

	@ApiModelProperty("借据号")
	private String loanNo;

	@ApiModelProperty("放款时间")
	private Date loanOpenDate;
}
