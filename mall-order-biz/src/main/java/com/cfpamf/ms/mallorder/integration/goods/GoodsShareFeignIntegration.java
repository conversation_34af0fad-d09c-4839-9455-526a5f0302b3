package com.cfpamf.ms.mallorder.integration.goods;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.framework.autoconfigure.common.exception.MSException;
import com.cfpamf.ms.mallgoods.facade.api.GoodsSharecodeBindUserFeignClient;
import com.cfpamf.ms.mallgoods.facade.vo.GoodsSharecodeBindUser;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Slf4j
@Component
public class GoodsShareFeignIntegration {
    @Autowired
    private GoodsSharecodeBindUserFeignClient goodsSharecodeBindUserFeignClient;

    public GoodsSharecodeBindUser getGoodsShareBoundUser(String shareCode) {
        if (StringUtils.isBlank(shareCode)) {
            return null;
        }
        GoodsSharecodeBindUser sharedBoundUser;
        try {
            log.info("根据商品分享码查询绑定的推荐购买用户,shareCode:{}",shareCode);
            sharedBoundUser = goodsSharecodeBindUserFeignClient.getGoodsSharecodeBindUserByShareCode(shareCode);
        } catch (Exception ex) {
            log.error("根据商品分享码查询绑定用户出现未知异常,shareCode:{}",shareCode,ex);
            throw new MSException(String.valueOf(ErrorCodeEnum.C.CALL_EXCEPTION.getCode()), "根据商品分享码查询绑定用户调用服务异常");
        }
        if (Objects.isNull(sharedBoundUser)) {
            return null;
        }
        log.info("根据商品分享码查询绑定的推荐购买用户,返回结果:{}", JSONObject.toJSONString(sharedBoundUser));
        return sharedBoundUser;
    }
}
