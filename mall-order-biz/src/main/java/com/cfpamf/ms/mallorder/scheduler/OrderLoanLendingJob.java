package com.cfpamf.ms.mallorder.scheduler;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.mallorder.common.enums.LoanStatusEnum;
import com.cfpamf.ms.mallorder.common.enums.TaskQueueBizTypeEnum;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.mapper.OrderMapper;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderPresellPO;
import com.cfpamf.ms.mallorder.po.TaskQueuePO;
import com.cfpamf.ms.mallorder.request.OrderExample;
import com.cfpamf.ms.mallorder.service.ITaskQueueService;
import com.cfpamf.ms.mallorder.v2.service.OrderPresellService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 * @Classname OrderLoanLendingJob
 * @Description 放款失败job
 * <AUTHOR>
 * @Date 2021/6/23 19:37
 * @Version 1.0
 **/
@Component
@Slf4j
public class OrderLoanLendingJob  {
    @Autowired
    private OrderModel orderModel;

    @Autowired
    private ITaskQueueService taskQueueService;

    @Autowired
    private OrderMapper orderMapper;
    
    @Autowired
    private OrderPresellService orderPresellService;

    @XxlJob("OrderLoanLendingJob")
    public ReturnT<String> execute(String s) throws Exception {

        String dateFormat = DateUtil.format(DateUtil.addDay(new Date(),1), DateUtil.CN_LONG_FORMAT);
        log.info("START OrderLoanLendingJob systemTime:{} eventTime:{}", new Date(), dateFormat);
        // 查询定时任务，进行发起补偿
        List<TaskQueuePO> taskQueuePOS = taskQueueService.listTodoTask(TaskQueueBizTypeEnum.AUTO_LOAN, dateFormat);
        int count = 0;

        for (TaskQueuePO taskQueue : taskQueuePOS) {
            try {
                // Andy.预售
                OrderPresellPO orderPresellPO =
                    orderPresellService.queryBalanceInfoByPayNo(String.valueOf(taskQueue.getBizId()));
                String orderSn= ObjectUtils.isNotEmpty(orderPresellPO)?orderPresellPO.getOrderSn():String.valueOf(taskQueue.getBizId());
                log.info("OrderLoanLendingJob->orderSn：{}",orderSn);
                
                OrderExample orderExample = new OrderExample();
                orderExample.setOrderSn(orderSn);
                List<OrderPO> orderPOS = orderMapper.listByExample(orderExample);
                if(CollectionUtils.isEmpty(orderPOS)) {
                    continue;
                }
                OrderPO orderPODb = orderPOS.get(0);

                if (orderPODb.getLoanPayState() == LoanStatusEnum.LENDING_SUCCESS.getValue().intValue()){
                    log.warn("OrderLoanLendingJob 订单放款状态为:" + orderPODb.getLoanPayState() + " 跳过补偿申请起息任务【LoanStatusEnum.LENDING_SUCCESS】");
                    taskQueueService.taskSuccess(taskQueue);
                    continue;
                }
                String payNo = ObjectUtils.isNotEmpty(orderPresellPO)? orderPresellPO.getPayNo():null;
                log.info("OrderLoanLendingJob orderSn:{} payNo:{}",orderPODb.getOrderSn(),payNo);
                // Andy.预售
                Result<Void> voidResult = orderModel.doLoanOperate(orderPODb.getOrderSn(), OrderConst.OPT_USER_ID, payNo);
                log.info("OrderLoanLendingJob orderSn:{} payNo:{} voidResult：{}",orderPODb.getOrderSn() , payNo , voidResult);
                if (voidResult.isSuccess()) {
                    orderModel.loanPaySuccess(orderPODb, OrderConst.ADMIN_ROLE, OrderConst.USER_ID_SYSTEM, OrderConst.USER_NAME_SYSTEM);
                } else {
                    // 非系统异常导致放款失败，记录日志，人工处理
                    orderModel.loanPayFail(orderPODb, voidResult.getErrorMsg());
                }
                taskQueueService.taskSuccess(taskQueue);
                count ++;
            } catch (Exception ex) {
                log.error("放款失败job执行异常，BizId【订单号】{}", taskQueue.getBizId(), ex);
                taskQueueService.taskFail(taskQueue, "用呗放款", ex.getMessage());
            }
        }

        log.info("=============== FINISHED OrderLoanLendingJob, 应处理:{}, 实处理:{} ",taskQueuePOS.size(), count);

        return new ReturnT("SUCCESS");
    }

}
