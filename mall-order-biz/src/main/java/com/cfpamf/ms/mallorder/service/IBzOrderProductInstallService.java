package com.cfpamf.ms.mallorder.service;

import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.po.BzOrderProductInstallPO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.ms.mallorder.req.OrderProductInstallSaveRequest;
import com.cfpamf.ms.mallorder.vo.OrderProductInstallLogVO;

import java.util.List;

/**
 * <p>
 * 订单商品安装资料表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-14
 */
public interface IBzOrderProductInstallService extends IService<BzOrderProductInstallPO> {
    /**
     * 安装资料保存
     * @param request
     */
    void save(OrderProductInstallSaveRequest request);

    /**
     * 查询轨迹
     * @param orderProductId
     * @return
     */
    List<OrderProductInstallLogVO> queryTrackList(Long orderProductId);

    /**
     * 查询安装商列表
     * @param orderProductIds
     * @return
     */
    List<BzOrderProductInstallPO> queryByOrderProductIds(List<Long> orderProductIds);

}
