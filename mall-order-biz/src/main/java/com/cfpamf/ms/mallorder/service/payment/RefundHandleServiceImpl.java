package com.cfpamf.ms.mallorder.service.payment;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import com.google.common.collect.Maps;

@Primary
@Service
public class RefundHandleServiceImpl implements IRefundHandleService {

    /**
     * <pre>
     * key->xxxxRefundHandleServiceImpl
     * value->实例
     * </pre>
     */
    @Autowired
    private final Map<String, IRefundHandleService> strategyMap = Maps.newConcurrentMap();

    private IRefundHandleService instance(RetundHandleType retundHandleType) {
        String beanInstanceKey = retundHandleType.getCode().concat("RefundHandleServiceImpl");
        return strategyMap.get(beanInstanceKey);
    }

    @Override
    public <T> void verifyElectronicAccountBalance(RetundHandleType retundHandleType, T t) {
        this.instance(retundHandleType).verifyElectronicAccountBalance(retundHandleType, t);
    }

    @Override
    public <T> Boolean issuedBill(RetundHandleType retundHandleType, T t) {
        return this.instance(retundHandleType).issuedBill(retundHandleType, t);
    }

}
