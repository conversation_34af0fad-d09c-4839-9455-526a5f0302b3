package com.cfpamf.ms.mallorder.scheduler;

import com.cfpamf.ms.mallorder.service.IOrderExtendService;
import com.slodon.bbc.core.util.AssertUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 订单拓展实仓同步job
 *
 * <AUTHOR>
 * @since 2024/9/18
 */
@Component
@Slf4j
public class OrderExtendActualWarehouseSyncJob {

    @Autowired
    private IOrderExtendService orderExtendService;

    @XxlJob(value = "OrderExtendActualWarehouseSyncJob")
    public ReturnT<String> execute(String s) {

        long startTime = System.currentTimeMillis();
        XxlJobHelper.log("OrderExtendActualWarehouseSyncJob start job at local time: {0}", LocalDateTime.now());
        String ignoreOrderSn = XxlJobHelper.getJobParam();
        log.info("OrderExtendActualWarehouseSyncJob() start");
        try {
            orderExtendService.dealHistoryActualWareHouse(ignoreOrderSn);
        } catch (Exception e) {
            log.error("OrderExtendActualWarehouseSyncJob()", e);
        }

        XxlJobHelper.log("OrderExtendActualWarehouseSyncJob finish job at local time: {0}  cost:{1}",
                LocalDateTime.now(),
                (System.currentTimeMillis() - startTime) / 1000);

        return ReturnT.SUCCESS;
    }
}
