package com.cfpamf.ms.mallorder.controller.fegin.facade.workflow.util;

import com.github.pagehelper.Page;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@Builder
@AllArgsConstructor
public class MyPage<T> {
    private int page;
    private int pageSize;
    private long total;
    private int pages;
    private List<T> data;

    public MyPage(Page<T> page) {
        this.page = page.getPageNum();
        this.pageSize = page.getPageSize();
        this.total = page.getTotal();
        this.pages = page.getPages();
        this.data = page.getResult();
    }

    public static <T> MyPage<T> of(Page<T> page) {
        return new MyPage<>(page);
    }

    public static <T> MyPage<T> of(List<T> list, int totalCount, int pageSize, int currPage) {
        int pages = (int) Math.ceil((double) totalCount / pageSize);
        return MyPage.<T>builder()
                .page(currPage)
                .pageSize(pageSize)
                .total(totalCount)
                .pages(pages)
                .data(list)
                .build();
    }
}
