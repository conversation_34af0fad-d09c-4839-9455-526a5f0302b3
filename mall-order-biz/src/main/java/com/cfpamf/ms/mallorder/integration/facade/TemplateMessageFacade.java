package com.cfpamf.ms.mallorder.integration.facade;


import com.cfpamf.common.ms.result.Result;
import com.cfpamf.msgpush.facade.request.templateMessage.BatchBizMessageTemplateReq;
import com.cfpamf.msgpush.facade.request.templateMessage.NormalBizMessageTemplateReq;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "ms-messagepush-service", url = "${ms-messagepush-service.url}")
public interface TemplateMessageFacade {

    @PostMapping(value = "/v1/template/bizTemplateSend")
    Result<Void> bizTemplateSend(@RequestBody NormalBizMessageTemplateReq req);

    @PostMapping(value = "/v1/template/bizTemplateBatchSend")
    Result<Void> bizTemplateBatchSend(@RequestBody BatchBizMessageTemplateReq req);

}
