package com.cfpamf.ms.mallorder.integration.facade;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cfpamf.ms.mallorder.dto.KingdeeCustomerQuery;
import com.cfpamf.ms.mallorder.vo.kingdee.KingdeeCustomerVo;
import com.slodon.bbc.core.response.JsonResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(
        url = "${mall-external-adapter.url}",
        name = "mall-external-adapter"
)
public interface MallExternalAdapterFacade {


    @PostMapping("/feign/kingdee/customer/page")
    JsonResult<Page<KingdeeCustomerVo>> getCustomerPage(@RequestBody KingdeeCustomerQuery query);

}
