package com.cfpamf.ms.mallorder.service.impl;

import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.loanrisk.facade.vo.risk.RuleHitVo;
import com.cfpamf.ms.mallorder.common.config.RuleEngineConfig;
import com.cfpamf.ms.mallorder.common.enums.CommonEnum;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.ValetOrderFlowParamDTO;
import com.cfpamf.ms.mallorder.dto.ValetOrderRuleEngineParamDTO;
import com.cfpamf.ms.mallorder.integration.facade.SpyhunterEngineFacade;
import com.cfpamf.ms.mallorder.integration.facade.dto.SpyhunterEngineDTO;
import com.cfpamf.ms.mallorder.integration.facade.dto.SpyhunterEngineQuery;
import com.cfpamf.ms.mallorder.service.IAgricValetService;
import com.cfpamf.ms.mallorder.vo.ValetOrderFlowVO;
import com.slodon.bbc.core.exception.MallException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AgricValetServiceImpl implements IAgricValetService {
    @Autowired
    private RuleEngineConfig ruleEngineConfig;

    @Autowired
    private SpyhunterEngineFacade spyhunterEngineFacade;

    /**
     * 请求规则引擎获取代客下单选项
     *
     * @param dto
     * @return
     */
    @Override
    public ValetOrderFlowVO getFlowFromRuleEngine(ValetOrderFlowParamDTO dto) {
        // 初始化默认值flowVO
        ValetOrderFlowVO valetFlowVo = new ValetOrderFlowVO();
        // 根据参数生成对应reqId 规则引擎会根据reqId缓存事件结果
        String reqId = DigestUtil.md5Hex(dto.getOrderChannel() + dto.getBranchCode());

        //构造请求参数
        ValetOrderRuleEngineParamDTO paramDTO = new ValetOrderRuleEngineParamDTO();
        paramDTO.setEventId(reqId.substring(0, 16));
        paramDTO.setEventTime(System.currentTimeMillis());
        paramDTO.setUserNo(dto.getUserNo());
        paramDTO.setBranchcode(dto.getBranchCode());
        paramDTO.setOrderChannel(dto.getOrderChannel());

        SpyhunterEngineQuery engineQuery = new SpyhunterEngineQuery();
        engineQuery.setReqId(reqId);
        engineQuery.setModelGuid(ruleEngineConfig.getValetModelGuid());
        engineQuery.setJsonInfo(JSON.toJSONString(paramDTO));
        SpyhunterEngineDTO engineResultDTO = null;
        try {
            engineResultDTO = spyhunterEngineFacade.queryRuleHitResult(engineQuery);
        } catch (Exception e) {
            log.info("【getFlowFromRuleEngine】请求规则引擎异常,异常信息为{}", e.getMessage());
            throw new MallException("获取代客下单配置失败,请联系管理员查看~");
        }
        BizAssertUtil.isTrue((Objects.isNull(engineResultDTO) || !engineResultDTO.isSuccess()), "获取代客下单配置失败,请联系管理员查看~");
        log.info("【getFlowFromRuleEngine】请求决策引擎返回结果:{},参数{}", JSON.toJSONString(engineResultDTO), JSON.toJSONString(engineQuery));
        if (!engineResultDTO.isHitResult() || CollectionUtils.isEmpty(engineResultDTO.getRuleHitVos())) {
            // 没有命中规则,返回默认配置值
            return valetFlowVo;
        }
        List<String> codeList = engineResultDTO.getRuleHitVos().stream().map(RuleHitVo::getCode).collect(Collectors.toList());
        dealWithValetEngineResult(valetFlowVo, codeList);
        return valetFlowVo;
    }

    /**
     * 根据规则引擎返回结果处理
     *
     * @param codeList
     */
    private void dealWithValetEngineResult(ValetOrderFlowVO valetFlowVo, List<String> codeList) {
        if (CollectionUtils.isEmpty(codeList)) {
            return;
        }
        // 来源为bapp只需判断是否可以无需刷脸确认订单
        if (codeList.contains(ruleEngineConfig.getValetChannelBAPP())) {
            if (codeList.contains(ruleEngineConfig.getValetConfirmNoFace())) {
                valetFlowVo.setAllowConfirmNoFace(CommonEnum.NO.getCode());
            }
        } else if (codeList.contains(ruleEngineConfig.getValetChannelXX())) {
            // 来源为乡信小程序、乡信app判断该分支机构是否支持站长贷款代付/是否支持当前设备刷脸/是否无需阅读合同后刷脸
            if (codeList.contains(ruleEngineConfig.getValetAllowLoan())) {
                valetFlowVo.setAllowPlaceUserLoan(CommonEnum.YES.getCode());
            }
            if (codeList.contains(ruleEngineConfig.getValetConfirmMethod())) {
                valetFlowVo.setConfirmMethodCode(OrderConst.LOAN_CONFIRM_METHOD_FACE_DETECTION);
            }
            if (codeList.contains(ruleEngineConfig.getValetConfirmNoFace())) {
                // 命中无需刷脸白名单的分支 无需刷脸认证
                valetFlowVo.setAllowConfirmNoFace(CommonEnum.NO.getCode());
            }
        }
    }
}
