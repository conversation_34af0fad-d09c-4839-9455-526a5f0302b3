package com.cfpamf.ms.mallorder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.ms.mallgoods.facade.vo.ProductActivityGoodsBindVO;
import com.cfpamf.ms.mallorder.dto.CouponSendNotifyDto;
import com.cfpamf.ms.mallorder.po.OrderProductCouponPO;
import com.cfpamf.ms.mallorder.po.OrderProductPO;

import java.util.List;

/**
 * <p>
 * 订单货品优惠券信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-07
 */
public interface IOrderProductCouponService extends IService<OrderProductCouponPO> {

	/**
	 * 保存订单商品优惠券信息
	 *
	 * @param orderProductPO
	 * @param couponList
	 */
	Boolean insertOrderProductCoupon(OrderProductPO orderProductPO, List<ProductActivityGoodsBindVO> couponList);

	/**
	 * 发放优惠券结果回调处理
	 * @param notifyDto
	 * @return
	 */
	Boolean couponSendNotify(CouponSendNotifyDto notifyDto);

	/**
	 * 优惠券是否全部发放成功
	 * @param orderSn
	 * @param couponChannel
	 * @return
	 */
	Boolean isAllCouponSend(String orderSn, Integer couponChannel);

	/**
	 * 封装订单优惠券实体
	 *
	 * @param orderProductPO orderProductPo
	 * @param activityGoodsBindVOList 活动绑定vo列表
	 * @return 订单优惠券实体
	 */
	List<OrderProductCouponPO> buildOrderProductCoupon(OrderProductPO orderProductPO, List<ProductActivityGoodsBindVO> activityGoodsBindVOList);
}
