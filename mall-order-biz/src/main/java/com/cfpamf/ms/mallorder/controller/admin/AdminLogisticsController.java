package com.cfpamf.ms.mallorder.controller.admin;

import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.service.IOrderExtendService;
import com.cfpamf.ms.mallorder.service.IOrderService;
import com.slodon.bbc.core.express.TracesResult;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import com.slodon.bbc.core.util.AssertUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "admin-物流信息")
@RestController
@RequestMapping("admin/logistics")
@Slf4j
public class AdminLogisticsController {

    @Resource
    private IOrderExtendService orderExtendService;

    @Resource
    private OrderModel orderModel;
    @Resource
    private IOrderService orderService;

    @ApiOperation("订单查看物流")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderSn", value = "订单号", required = true, paramType = "query"),
            @ApiImplicitParam(name = "orderProductId", value = "订单商品ID",  required = true, paramType = "query")
    })
    @GetMapping("order/getTrace")
    public JsonResult<TracesResult> getOrderTrace( String orderSn, Long orderProductId) {
        //查询订单信息
        OrderPO orderPO = orderModel.getOrderByOrderSn(orderSn);
        log.info("查询订单物流，订单信息：{}", orderPO);
        AssertUtil.notNull(orderPO,"此订单物流信息为空");

        List<TracesResult> tracesResultList = orderService.getOrderTrace(orderSn, orderProductId);
        return SldResponse.success(tracesResultList.get(0));
    }

}
