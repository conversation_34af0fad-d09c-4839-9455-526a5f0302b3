package com.cfpamf.ms.mallorder.service;

import com.cfpamf.ms.mallorder.req.HouseholdCleanSalesReportRequest;
import com.cfpamf.ms.mallorder.req.LifeSrvIncomeReportRequest;
import com.cfpamf.ms.mallorder.vo.*;
import com.cfpamf.ms.mallorder.vo.bean.OuterPageInfo;
import com.cfpamf.ms.mallorder.vo.pgrpt.LifesrvDepartmentProfitBasicVO;
import com.cfpamf.ms.mallorder.vo.pgrpt.LifesrvEmpProfitBasicVO;
import org.springframework.stereotype.Service;

import javax.validation.Valid;
import java.util.List;

@Service
public interface LifeSrvInComeService {

	OuterPageInfo<LifesrvEmpProfitBasicVO, LifesrvDepartmentProfitBasicVO> basicMetrics(@Valid LifeSrvIncomeReportRequest request);
}
