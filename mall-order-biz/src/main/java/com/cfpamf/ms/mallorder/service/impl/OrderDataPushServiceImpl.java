package com.cfpamf.ms.mallorder.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.util.DateUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cdfinance.hrms.facade.vo.BranchRelationVO;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.bms.facade.vo.DictionaryItemVO;
import com.cfpamf.ms.bms.facade.vo.OrganizationBaseVO;
import com.cfpamf.ms.mallgoods.facade.api.ProductFeignClient;
import com.cfpamf.ms.mallgoods.facade.vo.Product;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.enums.OrderDataPushBusinessEnum;
import com.cfpamf.ms.mallorder.common.mq.RabbitMQUtils;
import com.cfpamf.ms.mallorder.common.mq.RabbitMqConfig;
import com.cfpamf.ms.mallorder.common.mq.msg.GoodNewsMessage;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.controller.fegin.facade.DbcServiceFeign;
import com.cfpamf.ms.mallorder.controller.fegin.facade.loan.ScrmFacade;
import com.cfpamf.ms.mallorder.controller.fegin.facade.loan.WecatFacade;
import com.cfpamf.ms.mallorder.dto.datapush.OrderArgicMessageDTO;
import com.cfpamf.ms.mallorder.dto.loan.WechatResult;
import com.cfpamf.ms.mallorder.dto.loan.WechatUserInfoPO;
import com.cfpamf.ms.mallorder.dto.loan.WechatUserInfoRequest;
import com.cfpamf.ms.mallorder.integration.bms.BmsOrgFeignIntegration;
import com.cfpamf.ms.mallorder.integration.erp.ERPIntegration;
import com.cfpamf.ms.mallorder.integration.facade.BmsIntegration;
import com.cfpamf.ms.mallorder.integration.facade.dto.UnitConvertVo;
import com.cfpamf.ms.mallorder.integration.hrms.HrmsIntegration;
import com.cfpamf.ms.mallorder.integration.system.TradeDocumentIntegration;
import com.cfpamf.ms.mallorder.mapper.OrderProductMapper;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.vo.DbcDistributionSaleOrderVO;
import com.cfpamf.ms.mallorder.vo.ManagerCommissionItemVO;
import com.cfpamf.ms.mallsystem.request.OrderTradeMatchDTO;
import com.cfpamf.ms.mallsystem.vo.OrderDataPushHitObject;
import com.google.api.client.util.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class OrderDataPushServiceImpl implements IOrderDatePushService {

    @Autowired
    private IOrderService orderService;

    @Autowired
    private IOrderExtendService orderExtendService;

    @Autowired
    private IOrderProductService orderProductService;

    @Autowired
    private OrderProductMapper orderProductMapper;

    @Autowired
    private ProductFeignClient productFeignClient;

    @Autowired
    private OrderProductErpExtendService orderProductErpExtendService;

    @Autowired
    private TradeDocumentIntegration tradeDocumentIntegration;

    @Autowired
    private ERPIntegration erpIntegration;

    @Autowired
    private HrmsIntegration hrmsIntegration;

    @Autowired
    private IOrderAmountStateRecordService orderAmountStateRecordService;

    @Autowired
    private ICommonMqEventService commonMqEventService;

    @Autowired
    private DbcServiceFeign dbcServiceFeign;

    @Autowired
    private IOrderPerformanceBelongsService orderPerformanceBelongsService;

    @Autowired
    private RabbitMQUtils rabbitMQUtils;

    @Autowired
    private WecatFacade wecatFacade;

    @Autowired
    private ScrmFacade scrmFacade;

    @Autowired
    private IBzOrderProductCombinationService orderProductCombinationService;

    @Autowired
    private BmsOrgFeignIntegration bmsOrgFeignIntegration;

    @Autowired
    private BmsIntegration bmsIntegration;

    @Value("${goodNews.qjb.productIds:200001000922,200000996704,200001010134,200001010321}")
    private String[] goodNewsProductIds;

    @Value("${goodNews.qjb.qjmProductIds:200001014961}")
    private String[] qjmProductIds;

    @Override
    public void paySuccessDataPush(String orderSn) {
        log.info("PaySuccessDataPush, Start DataPush, orderSn:{}",
                orderSn);
        /**
         * 查询订单信息
         */
        OrderPO orderPO = getOrderPO(orderSn);
        log.info("PaySuccessDataPush, Get OrderPo Item: {}, orderSn:{}",
                JSON.toJSONString(orderPO), orderSn);

        //获取订单是否客户支付成功首单
        Boolean firstOrderFlag = getCustomerFirstOrderFlag(orderPO);

        // 查询分支
        // 不取管护分支， 改为从业绩归属里面获取数据
//		LambdaQueryWrapper<OrderExtendPO> queryExtend = Wrappers.lambdaQuery();
//		queryExtend.eq(OrderExtendPO::getOrderSn, orderSn)
//				.select(OrderExtendPO::getExtendId, OrderExtendPO::getBranch, OrderExtendPO::getBranchName, OrderExtendPO::getZoneCode,
//						OrderExtendPO::getZoneName, OrderExtendPO::getAreaName, OrderExtendPO::getAreaCode, OrderExtendPO::getManager,
//						OrderExtendPO::getManagerName, OrderExtendPO::getCustomerName);
//		OrderExtendPO extendPO = orderExtendService.getOne(queryExtend);
        LambdaQueryWrapper<OrderPerformanceBelongsPO> queryBelongs = Wrappers.lambdaQuery();
        queryBelongs.eq(OrderPerformanceBelongsPO::getOrderSn, orderSn)
                .in(OrderPerformanceBelongsPO::getBindStateCode, Arrays.asList(OrderConst.BIND_STATE_CODE_TSX, OrderConst.BIND_STATE_CODE_YTZ))
                .select(OrderPerformanceBelongsPO::getEmployeeBranchCode, OrderPerformanceBelongsPO::getEmployeeBranchName,
                        OrderPerformanceBelongsPO::getBelongerEmployeeNo, OrderPerformanceBelongsPO::getBelongerName);
        OrderPerformanceBelongsPO belongsPO = orderPerformanceBelongsService.getOne(queryBelongs);
        log.info("PaySuccessDataPush, Get OrderBelongs Item: {}, orderSn:{}",
                JSON.toJSONString(belongsPO), orderSn);
        if (belongsPO == null ||
                StringUtils.isBlank(belongsPO.getBelongerEmployeeNo()) ||
                StringUtils.isBlank(belongsPO.getEmployeeBranchCode()) ||
                "E999999999".equals(belongsPO.getBelongerEmployeeNo())) {
            return;
        }
        //根据belongsPO转换为 OrderExtendPO extendPO, 缺少的片区和区域信息从bms查
        OrderExtendPO extendPO = buildExtendPoByBelongsPO(belongsPO, orderPO);

        List<OrderProductPO> orderProductPOS = new ArrayList<>();
        // 查询订单商品信息
        LambdaQueryWrapper<OrderProductPO> orderProductQuery = Wrappers.lambdaQuery();
        orderProductQuery.eq(OrderProductPO::getOrderSn, orderSn);

        orderProductPOS = orderProductService.list(orderProductQuery);

        // 商品ID
        List<Long> productIdList = orderProductPOS.stream().map(OrderProductPO::getProductId).collect(Collectors.toList());
        // 查询商品分类
        List<Product> productList = productFeignClient.getProductListByProductIds(productIdList);
        BizAssertUtil.notEmpty(productList, "商品为空，请检查");
        Map<Long, Product> productMap = productList.stream()
                .collect(Collectors.toMap(Product::getProductId, Function.identity()));
        //查询商品erp分类
        Map<Long, OrderProductErpExtendPO> erpExtendPOMap = orderProductErpExtendService.getOrderProductErpExtendMapByOrderProductIds(orderProductPOS.stream().map(OrderProductPO::getOrderProductId).collect(Collectors.toList()));
        //根据用户手机号，获取是否农服新媒体客户
        Integer isAgricMcnUser = getAgricMcnUserFlag(orderPO.getUserMobile());

        //根据客户经理工号，获取客户经理头像
        String managerAvatar = getManagerAvatar(extendPO.getManager());
        /* 匹配规则
         */
        // 获取商品总吨数
        BigDecimal ton = orderProductPOS.stream().map(orderProductPO ->
                        orderProductPO.getWeight().multiply(BigDecimal.valueOf(0.001D))
                                .multiply(BigDecimal.valueOf(orderProductPO.getProductNum())))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        List<OrderDataPushHitObject> hitObjects = new ArrayList<>();
        for (OrderProductPO orderProductPO : orderProductPOS) {
            Product product = productMap.get(orderProductPO.getProductId());
            OrderTradeMatchDTO queryDTO = new OrderTradeMatchDTO();
            queryDTO.setOrderAmount(orderPO.getOrderAmount());
            queryDTO.setOrderPattern(orderPO.getOrderPattern());
            queryDTO.setPaymentCode(orderPO.getPaymentCode());
            queryDTO.setStoreId(orderPO.getStoreId().toString());
            queryDTO.setPerformanceModes(orderPO.getPerformanceModes());
            queryDTO.setBranchCode(extendPO.getBranch());
            queryDTO.setZoneCode(extendPO.getZoneCode());
            queryDTO.setAreaCode(extendPO.getAreaCode());
            queryDTO.setTonWeight(ton);
            queryDTO.setGoodsCategoryId1(product.getCategoryId1().toString());
            queryDTO.setGoodsCategoryId2(product.getCategoryId2().toString());
            queryDTO.setGoodsCategoryId3(product.getCategoryId3().toString());
            queryDTO.setIsAgricMcnUser(isAgricMcnUser);
            queryDTO.setUserTag(firstOrderFlag ? "1" : "0");
            queryDTO.setGoodsId(product.getGoodsId());
            queryDTO.setProductId(product.getProductId());
            queryDTO.setChannel(orderPO.getChannel());
            queryDTO.setGoodsName(product.getGoodsName());

            log.info("PaySuccessDataPush, DataPushMatch Query: {}, orderSn:{}",
                    JSON.toJSONString(queryDTO), orderSn);
            List<OrderDataPushHitObject> hits = tradeDocumentIntegration.orderDataPushMatch(queryDTO);
            if (!CollectionUtils.isEmpty(hits)) {
                hitObjects.addAll(hits);
            }
        }
        log.info("PaySuccessDataPush, DataPushMatch Result: {}, orderSn:{}",
                JSON.toJSONString(hitObjects), orderSn);

        // 没有命中规则，不推喜报
        if (CollectionUtils.isEmpty(hitObjects)) {
            return;
        }

        // 一个规则对应一个推送模版，进行去重
        hitObjects = hitObjects.stream().collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(OrderDataPushHitObject::getCode)))
                , ArrayList::new));

        // 获取需要按得分最高推送的喜报模板字典
        List<DictionaryItemVO> dictionaryItemVOS = bmsIntegration.listDictionaryItemsByTypeCode(CommonConst.PUSH_THE_HIGHEST_SCORING_GOOD_NEWS,
                CommonConst.MALL_SYSTEM_MANAGE_ID);
        log.info("PaySuccessDataPush, listDictionaryItemsByTypeCode Result:{}, typeCode:{}",
                dictionaryItemVOS, CommonConst.PUSH_THE_HIGHEST_SCORING_GOOD_NEWS);
        // 未配置字典，推送全部喜报
        if (CollectionUtil.isNotEmpty(dictionaryItemVOS)) {
            List<String> itemCodeList = dictionaryItemVOS.stream()
                    .map(DictionaryItemVO::getItemCode)
                    .distinct()
                    .collect(Collectors.toList());

            // 收集需要按得分最高推送的规则
            List<OrderDataPushHitObject> pushbyscoreHitObjectList = hitObjects.stream()
                    .filter(hitObject -> itemCodeList.contains(hitObject.getCode()))
                    .collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(pushbyscoreHitObjectList)) {
                // 过滤字典配置的规则
                hitObjects = hitObjects.stream()
                        .filter(hitObject -> !itemCodeList.contains(hitObject.getCode()))
                        .collect(Collectors.toList());
                // 获取最高得分
                Optional<Double> maxScore = pushbyscoreHitObjectList.stream()
                        .map(OrderDataPushHitObject::getValue)
                        .max(Comparator.naturalOrder());
                // 获取最高得分规则
                List<OrderDataPushHitObject> maxScoreHitObjects = pushbyscoreHitObjectList.stream()
                        .filter(d -> ObjectUtil.equal(d.getValue(), maxScore.get()))
                        .collect(Collectors.toList());

                hitObjects.addAll(maxScoreHitObjects);
            }
        }

        /**
         * 组装推送参数
         */
        Map<String, List<OrderArgicMessageDTO>> pushParams = new HashMap<>(hitObjects.size());
        for (OrderDataPushHitObject hitObject : hitObjects) {
//			OrderDataPushBusinessEnum pushBusinessEnum = OrderDataPushBusinessEnum.getByCode(hitObject.getCode());
//			BizAssertUtil.notNull(pushBusinessEnum, "推送业务编码[" + hitObject.getCode() + "]无法识别，请检查规则配置");

            List<OrderArgicMessageDTO> pushParam = getParams(orderPO, extendPO,erpExtendPOMap, orderProductPOS, managerAvatar, hitObject.getCode());
            pushParams.put(hitObject.getCode(), pushParam);
        }
        log.info("PaySuccessDataPush, pushParams: {}, orderSn:{}",
                JSON.toJSONString(pushParams), orderSn);

        /**
         * 推MQ
         */
        for (String businessType : pushParams.keySet()) {
            List<OrderArgicMessageDTO> pushParam = pushParams.get(businessType);
            if (CollectionUtils.isEmpty(pushParam)) {
                continue;
            }

            // 一个模版可能有多个分类，推送多次
            for (OrderArgicMessageDTO param : pushParam) {
                final String MALL_PAY_ORDER = "2023MALL_PAY_ORDER";
                GoodNewsMessage goodNewsMessage = new GoodNewsMessage();
                goodNewsMessage.setReportType(businessType);
                goodNewsMessage.setBranch(extendPO.getBranch());
                goodNewsMessage.setMarketingCode(MALL_PAY_ORDER);
                goodNewsMessage.setReportLevel(1);
                goodNewsMessage.setPushParam(JSON.toJSONString(param));
                goodNewsMessage.setStatisticalObject(extendPO.getManager());
                goodNewsMessage.setStatisticalTime(getStatisticalTime(param, orderPO));
                goodNewsMessage.setParamAddStatisticalCount(true);
                goodNewsMessage.setMessageId(UUID.fastUUID().toString());
                goodNewsMessage.setText(getText(businessType, param));

                Long eventId = commonMqEventService.saveEvent(goodNewsMessage, RabbitMqConfig.PERFORMANCE_GOOD_NEWS_EXCHANGE_NAME);
                log.info("PaySuccessDataPush, GoodNewsMessage Result: {}, orderSn:{}",
                        JSON.toJSONString(goodNewsMessage), orderSn);

                rabbitMQUtils.sendByEventId(eventId);
            }
        }
    }

    private static String getStatisticalTime(OrderArgicMessageDTO param, OrderPO orderPO) {
        if (StringUtils.isEmpty(param.getGoodsCategory2())) {
            return orderPO.getOrderSn();
        }
        return orderPO.getOrderSn() + "_" + param.getGoodsCategory2();
    }

    /**
     * 查询订单信息
     */
    private OrderPO getOrderPO(String orderSn) {
        LambdaQueryWrapper<OrderPO> queryOrder = Wrappers.lambdaQuery();
        queryOrder.eq(OrderPO::getOrderSn, orderSn);
        OrderPO orderPO = orderService.getOne(queryOrder);
        return orderPO;
    }


    private OrderExtendPO buildExtendPoByBelongsPO(OrderPerformanceBelongsPO belongsPO, OrderPO orderPO) {
        //根据belongsPO转换为 OrderExtendPO extendPO, 缺少的片区和区域信息从bms查
        OrderExtendPO extendPO = new OrderExtendPO();
        extendPO.setBranch(belongsPO.getEmployeeBranchCode());
        extendPO.setBranchName(belongsPO.getEmployeeBranchName());
        extendPO.setManager(belongsPO.getBelongerEmployeeNo());
        extendPO.setManagerName(belongsPO.getBelongerName());
        extendPO.setCustomerName(orderPO.getMemberName());
        //根据分支编码获取片区和区域信息
        if (!StringUtils.isEmpty(extendPO.getBranch())) {
            BranchRelationVO branchRelationVO = hrmsIntegration.getBranchRelation(extendPO.getBranch());
            if (branchRelationVO != null) {
                extendPO.setAreaCode(branchRelationVO.getRegionCode());
                extendPO.setAreaName(branchRelationVO.getRegionName());
                extendPO.setZoneCode(branchRelationVO.getZoneCode());
                extendPO.setZoneName(branchRelationVO.getZoneName());
            }
        }
        return extendPO;
    }

    private String getManagerAvatar(String managerJobNumber) {
        if (StringUtils.isBlank(managerJobNumber)) {
            return "";
        }
        //构造请求参数
        WechatUserInfoRequest request = new WechatUserInfoRequest();
        request.setWechatUserIdList(Lists.newArrayList());
        request.getWechatUserIdList().add(managerJobNumber);
        WechatResult<List<WechatUserInfoPO>> result = wecatFacade.getUserInfoByUserIdList(request);
        if (CollectionUtils.isEmpty(result.getData())) {
            return "";
        }
        return result.getData().get(0).getAvatar();
    }

    private Integer getAgricMcnUserFlag(String userMobile) {
        if (StringUtils.isBlank(userMobile)) {
            return 0;
        }
        try {
            WechatResult<Boolean> jobNumberFile = scrmFacade.fromSocialMedia(userMobile);
            log.info("PaySuccessDataPush, getAgricMcnUserFlag Query: {}, Result:{}",
                    userMobile, JSON.toJSONString(jobNumberFile));
            if (jobNumberFile.getData() == null || !jobNumberFile.getData()) {
                return 0;
            }
        } catch (Exception ex) {
            log.warn("PaySuccessDataPush, getAgricMcnUserFlag Error!!", ex);
            return 0;
        }
        return 1;
    }

    private List<OrderArgicMessageDTO> getParams(OrderPO orderPO, OrderExtendPO extendPO, Map<Long, OrderProductErpExtendPO> productMap, List<OrderProductPO> orderProductPOS, String managerAvatar, String code) {
        /**
         * 以二级分类分组，统计吨数及销售单位
         */
        Map<String, OrderArgicMessageDTO> messageMap = new HashMap<>();
        List<String> filterGoodsCategory = new ArrayList<>();
        //如果是农服喜报
        if(code.equals(OrderDataPushBusinessEnum.MALL_PAY_ORDER.getCode())){
            //检查当前商品是否为特殊指定【例如种子、套餐】
            List<DictionaryItemVO> specialGoodsCategory = bmsIntegration.listDictionaryItemsByTypeCode(CommonConst.GOODNEWS_SPECIAL_GOOD_CATEGORY,
                    CommonConst.MALL_SYSTEM_MANAGE_ID);
            log.info("PaySuccessDataPush, specialGoodsCategory Result:{}, typeCode:{}",
                    specialGoodsCategory, CommonConst.GOODNEWS_SPECIAL_GOOD_CATEGORY);
            if (CollectionUtil.isNotEmpty(specialGoodsCategory)){
                filterGoodsCategory = specialGoodsCategory.stream().map(DictionaryItemVO::getItemCode).collect(Collectors.toList());
            }
        }
        for (OrderProductPO orderProductPO : orderProductPOS) {
            OrderProductErpExtendPO orderProductErpExtendPO = productMap.get(orderProductPO.getOrderProductId());
            List<String> erpCategoryPath = Lists.newArrayList();
            if (Objects.nonNull(orderProductErpExtendPO)){
                erpCategoryPath = Arrays.asList(orderProductErpExtendPO.getProductCategoryPath().split("/"));
            }
            String skuMaterialName = orderProductPO.getSkuMaterialName();
            // 分类信息
            String[] cateGoryPath = orderProductPO.getGoodsCategoryPath().split("->");
            log.info("PaySuccessDataPush, cateGoryPath result: {}", JSON.toJSONString(cateGoryPath));
            // 分类没有三级，属于有问题数据
            if (cateGoryPath.length < 3) {
                continue;
            }

            OrderArgicMessageDTO messageDTO = messageMap.get(cateGoryPath[1]);
            BigDecimal tonWeight = orderProductPO.getWeight().multiply(BigDecimal.valueOf(0.001D))
                    .multiply(BigDecimal.valueOf(orderProductPO.getProductNum()));
            if (messageDTO == null) {
                List<Long> productIdList = new ArrayList<>();
                messageDTO = new OrderArgicMessageDTO();
                messageDTO.setGoodsCategory1(cateGoryPath[0]);
                messageDTO.setGoodsCategory2(cateGoryPath[1]);
                messageDTO.setGoodsCategory3(cateGoryPath[2]);
                messageDTO.setGoodsCategoryNum(1);
                messageDTO.setTon(tonWeight);
                messageDTO.setWeight(0);
                messageDTO.setUnitNum(0);
                messageDTO.setNewMediaGoodsName(orderProductPO.getGoodsName());
                // 新媒体喜报农服订单统一使用单位 袋
                messageDTO.setNewMediaUnitName("袋");

                productIdList.add(orderProductPO.getProductId());
                messageDTO.setProductIdList(productIdList);

                messageMap.put(cateGoryPath[1], messageDTO);
            } else {
                messageDTO.getProductIdList().add(orderProductPO.getProductId());
                messageDTO.setProductIdList(messageDTO.getProductIdList());
                messageDTO.setTon(messageDTO.getTon().add(tonWeight));
            }
            //如果需要过滤，并且存在该分类，则设置吨数为0
            if(CollectionUtils.isNotEmpty(filterGoodsCategory)
                    && filterGoodsCategory.stream().anyMatch(erpCategoryPath::contains)) {
                messageDTO.setTon(BigDecimal.ZERO);
            }
            messageDTO.setImageUrl(managerAvatar);
            messageDTO.setSkuMaterialName(skuMaterialName);

            messageDTO.setOrderNum(getOrderNum(goodNewsProductIds, orderPO.getOrderSn(), orderProductPO.getProductId(), extendPO));
            messageDTO.setQjmOrderNum(getOrderNum(qjmProductIds, orderPO.getOrderSn(), orderProductPO.getProductId(), extendPO));

            // 没有单位，不转换
            if (StringUtils.isBlank(orderProductPO.getChannelSkuId())) {
                continue;
            }

            // 单位转换
            Map<String, Object> thisProductQty = new HashMap<>(3);
            thisProductQty.put("skuId", orderProductPO.getChannelSkuId());
            thisProductQty.put("skuUnit", orderProductPO.getChannelSkuUnit());
            thisProductQty.put("deliveryQty", orderProductPO.getProductNum());
            List<UnitConvertVo> unitConvertVos = erpIntegration.unitConvert(Collections.singletonList(thisProductQty));
            Integer sumPackageNum = unitConvertVos.stream().map(UnitConvertVo::getPackageNum).mapToInt(BigDecimal::intValue).sum();
            messageDTO.setWeight(messageDTO.getWeight() + sumPackageNum);
            messageDTO.setUnitNum(messageDTO.getUnitNum() + sumPackageNum);
            messageDTO.setUnitName(unitConvertVos.get(0).getUnitName());

        }
        BizAssertUtil.notEmpty(messageMap.values(), "订单【" + orderPO.getOrderSn() + "】喜报推送参数组装失败，请检查商品分类");


        /**
         * 报文信息组装
         */
        List<OrderArgicMessageDTO> messageDTOList = new ArrayList<>(messageMap.size());
        for (String category : messageMap.keySet()) {
            OrderArgicMessageDTO messageDTO = messageMap.get(category);
            messageDTO.setOrderSn(orderPO.getOrderSn());
            messageDTO.setStoreId(orderPO.getStoreId());

            BigDecimal productCategoryAmount = orderProductPOS.stream()
                    .filter(x -> messageDTO.getProductIdList().contains(x.getProductId()))
                    .map(x -> x.getMoneyAmount() == null ? BigDecimal.ZERO : x.getMoneyAmount())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            messageDTO.setOrderAmount(productCategoryAmount);
            messageDTO.setGoodsAmount(orderPO.getGoodsAmount());
            messageDTO.setOrderCommission(getManagerCommissionForProduct(orderPO, messageDTO.getProductIdList(), extendPO));
            messageDTO.setOrderTotalCommission(getTotalCommissionForProduct(orderPO, messageDTO.getProductIdList()));
            messageDTO.setPushTime(DateUtils.format(orderPO.getPayTime(), DateUtils.DATE_FORMAT_19));
            messageDTO.setBranchName(extendPO.getBranchName());
            messageDTO.setAreaName(extendPO.getAreaName());
            messageDTO.setManagerName(extendPO.getManagerName());
            messageDTO.setCustomerName(extendPO.getCustomerName());

            // 查询分支及区域总销量
            Integer branchSalesCount = orderProductMapper.airEnergySalesCount(extendPO.getBranch(), null);
            Integer areaSalesCount = orderProductMapper.airEnergySalesCount(null, extendPO.getAreaCode());
            messageDTO.setBranchTotalNum(branchSalesCount);
            messageDTO.setAreaTotalNum(areaSalesCount);

            // 商品总数量
            int sum = orderProductPOS.stream().mapToInt(OrderProductPO::getProductNum).sum();
            messageDTO.setProductCount(sum);

            messageDTOList.add(messageDTO);
        }

        return messageDTOList;
    }

    private Integer getOrderNum(String[] productIds, String orderSn, Long productId, OrderExtendPO extendPO) {
        if (productIds == null || productIds.length == 0 || productId == null || extendPO == null || StringUtils.isEmpty(extendPO.getAreaCode())) {
            return 1;
        }
        try {
            List<OrganizationBaseVO> branches = bmsOrgFeignIntegration.listBranchesByHrOrgCodesV2(extendPO.getAreaCode());
            if (CollectionUtils.isEmpty(branches)) {
                return 1;
            }
            List<String> branchCodeList = branches.stream()
                    .map(OrganizationBaseVO::getOrgCode)
                    .collect(Collectors.toList());
            List<String> productIdList = Arrays.asList(productIds);
            String productIdStr = String.valueOf(productId);
            if (!productIdList.contains(productIdStr)) {
                productIdList = Arrays.asList(productIdStr);
            }
            Integer orderNum = orderProductMapper.countPayOrderNum(orderSn, productIdList, branchCodeList);
            return orderNum == null || orderNum == 0 ? 1 : orderNum;
        } catch (Exception e) {
            log.warn("查询商品订单数失败", e);
            return 1;
        }
    }


    private BigDecimal getManagerCommission(OrderPO orderPO) {

        Result<BigDecimal> managerCommission = dbcServiceFeign.managerCommission(
                orderPO.getOrderSn(), OrderConst.STAND_MALL_BUSINESS_ChANNEL, orderPO.getStoreId().toString());
        if (managerCommission == null || managerCommission.getData() == null) {
            log.error("推送喜报查询客户经理佣金为空，订单号: {}", orderPO.getOrderSn());
            return BigDecimal.ZERO;
        }

        return managerCommission.getData();
    }

    private BigDecimal getManagerCommissionForProduct(OrderPO orderPO, List<Long> productIdList, OrderExtendPO extendPO) {

        //缓存订单商品佣金，10分支后释放
        //分类取佣金
        Result<List<ManagerCommissionItemVO>> orderProductCommissionList = dbcServiceFeign.managerCommissionByCommodities(
                orderPO.getOrderSn(), OrderConst.STAND_MALL_BUSINESS_ChANNEL, orderPO.getStoreId().toString());
        if (orderProductCommissionList == null || orderProductCommissionList.getData() == null) {
            log.error("推送喜报查询客户经理佣金为空，订单号: {}", orderPO.getOrderSn());
            return BigDecimal.ZERO;
        }


        Map<String, List<ManagerCommissionItemVO>> skuManagerCommissionListMap = orderProductCommissionList.getData().stream().collect(Collectors.groupingBy(ManagerCommissionItemVO::getCommodityCode));

        BigDecimal result = BigDecimal.ZERO;
        for (Long productId : productIdList) {
            List<ManagerCommissionItemVO> managerCommissionItemVOList = skuManagerCommissionListMap.get(productId.toString());
            if (!CollectionUtils.isEmpty(managerCommissionItemVOList)) {
                BigDecimal productIdsComminssion = managerCommissionItemVOList.stream().map(ManagerCommissionItemVO::getManagerCommission).reduce(BigDecimal.ZERO, BigDecimal::add);
                result = result.add(productIdsComminssion);
            }
        }

        // 广东区域，还需要加上物流费 2025-04-24不需要增加物流费
//        if (ObjectUtil.equal(CommonConst.GDQY, extendPO.getAreaCode())) {
//            // 分销：查询物流费
//            Result<List<FreightCostItemVO>> freightCostByCommodities = dbcServiceFeign.freightCostByCommodities(orderPO.getOrderSn(),
//                    OrderConst.STAND_MALL_BUSINESS_ChANNEL,
//                    orderPO.getStoreId().toString());
//
//            log.info("freightCostByCommodities orderSn:{}，storeId:{}，result:{}",orderPO.getOrderSn(), orderPO.getStoreId(), JSON.toJSONString(freightCostByCommodities));
//
//            if (freightCostByCommodities != null && freightCostByCommodities.getData() != null) {
//                Map<String, List<FreightCostItemVO>> skuFreightCostItemVOMap = freightCostByCommodities.getData()
//                        .stream()
//                        .collect(Collectors.groupingBy(FreightCostItemVO::getCommodityCode));
//
//                for (Long productId : productIdList) {
//                    List<FreightCostItemVO> freightCostItemVOList = skuFreightCostItemVOMap.get(productId.toString());
//                    if (!CollectionUtils.isEmpty(freightCostItemVOList)) {
//                        BigDecimal productIdsComminssion = freightCostItemVOList.stream().map(FreightCostItemVO::getFreightCost).reduce(BigDecimal.ZERO, BigDecimal::add);
//                        result = result.add(productIdsComminssion);
//                    }
//                }
//
//            }
//        }
        log.info("getManagerCommissionForProduct productIdList commission = {} ", result);
        return result;
    }


    private BigDecimal getTotalCommissionForProduct(OrderPO orderPO, List<Long> productIdList) {
        //缓存订单商品佣金，10分支后释放
        //分类取佣金
        Result<List<DbcDistributionSaleOrderVO>> orderProductCommissionList = dbcServiceFeign.getListByOrderId(orderPO.getOrderSn());
        if (orderProductCommissionList == null || orderProductCommissionList.getData() == null) {
            log.error("推送喜报查询佣金为空，订单号: {}", orderPO.getOrderSn());
            return BigDecimal.ZERO;
        }

        Map<String, List<DbcDistributionSaleOrderVO>> skuManagerCommissionListMap = orderProductCommissionList.getData()
                .stream()
                .filter(x -> Arrays.asList(10001, 10002).contains(x.getCommissionOrderType()))
                .collect(Collectors.groupingBy(DbcDistributionSaleOrderVO::getCommodityCode));

        BigDecimal result = BigDecimal.ZERO;
        for (Long productId : productIdList) {
            List<DbcDistributionSaleOrderVO> managerCommissionItemVOList = skuManagerCommissionListMap.get(productId.toString());
            if (!CollectionUtils.isEmpty(managerCommissionItemVOList)) {
                BigDecimal productIdsComminssion = managerCommissionItemVOList.stream()
                        .map(DbcDistributionSaleOrderVO::getOrderCommission)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                result = result.add(productIdsComminssion);
            }
        }
        log.info("getTotalCommissionForProduct productIdList commission = {} ", result);
        return result;
    }

    /**
     * 获取村代佣金
     *
     * @param orderPO
     * @param productIdList
     * @return
     */
    private BigDecimal getLevel2CommissionForProduct(OrderPO orderPO, List<Long> productIdList) {
        //缓存订单商品佣金，10分支后释放
        //分类取佣金
        Result<List<DbcDistributionSaleOrderVO>> orderProductCommissionList = dbcServiceFeign.getListByOrderId(orderPO.getOrderSn());
        if (orderProductCommissionList == null || orderProductCommissionList.getData() == null) {
            log.error("推送喜报查询佣金为空，订单号: {}", orderPO.getOrderSn());
            return BigDecimal.ZERO;
        }

        Map<String, List<DbcDistributionSaleOrderVO>> skuManagerCommissionListMap = orderProductCommissionList.getData()
                .stream()
//                .filter(x -> Arrays.asList(10001, 10002).contains(x.getCommissionOrderType()))
                .filter(x -> x.getDistributionOrderType().equals(9))
                .collect(Collectors.groupingBy(DbcDistributionSaleOrderVO::getCommodityCode));

        BigDecimal result = BigDecimal.ZERO;
        for (Long productId : productIdList) {
            List<DbcDistributionSaleOrderVO> managerCommissionItemVOList = skuManagerCommissionListMap.get(productId.toString());
            if (!CollectionUtils.isEmpty(managerCommissionItemVOList)) {
                BigDecimal productIdsComminssion = managerCommissionItemVOList.stream()
                        .map(DbcDistributionSaleOrderVO::getOrderCommission)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                result = result.add(productIdsComminssion);
            }
        }
        log.info("getTotalCommissionForProduct productIdList commission = {} ", result);
        return result;
    }

    /**
     * 判断订单是否用户支付成功首单
     *
     * @param thisOrderPO
     * @return
     */
    private Boolean getCustomerFirstOrderFlag(OrderPO thisOrderPO) {
        LambdaQueryWrapper<OrderPO> queryOrder = Wrappers.lambdaQuery();
        queryOrder.select(OrderPO::getOrderSn)
                .eq(OrderPO::getMemberId, thisOrderPO.getMemberId())
                .isNotNull(OrderPO::getPayTime)
                .eq(OrderPO::getEnabledFlag, 1)
                .orderByAsc(OrderPO::getPayTime)
                .last(" limit 1");
        OrderPO firstPaySuccessOrderPO = orderService.getOne(queryOrder);
        if (firstPaySuccessOrderPO == null) {
            log.warn("未查到首次支付成功的订单");
            return false;
        }
        return ObjectUtils.nullSafeEquals(firstPaySuccessOrderPO.getOrderSn(), thisOrderPO.getOrderSn());
    }

    public String getText(String businessType, OrderArgicMessageDTO param) {
        try {
            GoodNewsEnum type = GoodNewsEnum.getByCode(businessType);
            if (type == null) {
                return null;
            }
            Map<String, Object> paramMap = JSON.parseObject(JSON.toJSONString(param));
            return replacePlaceholders(type.getTextTemplate(), paramMap);
        } catch (Exception e) {
            log.warn("替换占位符异常", e);
            return null;
        }
    }

    public static String replacePlaceholders(String template, Map<String, Object> replacements) {
        for (Map.Entry<String, Object> entry : replacements.entrySet()) {
            template = template.replace("{" + entry.getKey() + "}", entry.getValue().toString());
        }
        return template;
    }

    @AllArgsConstructor
    @Getter
    enum GoodNewsEnum {
        CDMALL_CLEAN_SALES("cdmall_clean_sales", "清洁宝下单支付成功喜报", "\uD83C\uDF89热烈祝贺！\uD83C\uDF8A<br>" +
                "<font color=\"#dd0000\">**【{areaName}·{branchName}】**</font>清洁宝自营第<font color=\"#dd0000\">**-{orderNum}-**</font>单[烟花]<br>" +
                "电商自营业务达成{orderAmount}元[鼓掌][赞]<br>" +
                "<font color=\"#dd0000\">**{areaName}{branchName}**</font>成功达成洗护类目核心产品——“清洁宝”自营项目，共计<font color=\"#dd0000\">**销售{productCount}箱\uD83C\uDF89\uD83C\uDF89，总计{qjbBottleCount}瓶**</font>。\uD83D\uDC4F\uD83D\uDC4F<br>" +
                "此项成绩不仅体现了“清洁宝”产品的优质性能和市场认可[你强][你强]，更是彰显了<font color=\"#dd0000\">**{areaName}{branchName}**</font>团队卓越的经营策略与高效的执行力。[虎虎生威][虎虎生威]乡村电商线下业务中心在此对<font color=\"#dd0000\">**{areaName}**</font>参与此项目的同仁表示由衷的感谢与诚挚的祝贺！[烟花][烟花]<br>" +
                "再接再厉，共创辉煌！[爆竹][爆竹][元气满满][元气满满]"),
        QING_JIE_MIAO_SALES("cdmall_clean_sales_qjm", "清洁喵下单支付成功喜报", "\uD83C\uDF89热烈祝贺！\uD83C\uDF8A<br>" +
                "<font color=\"#dd0000\">**【{areaName}·{branchName}】**</font>清洁喵自营第<font color=\"#dd0000\">**-{qjmOrderNum}-**</font>单[烟花]<br>" +
                "电商自营业务达成{orderAmount}元[鼓掌][赞]<br>" +
                "<font color=\"#dd0000\">**{areaName}{branchName}**</font>成功达成洗护类目核心产品——“清洁喵”自营项目，共计<font color=\"#dd0000\">**销售{productCount}箱\uD83C\uDF89\uD83C\uDF89，总计{qjmBottleCount}瓶**</font>。\uD83D\uDC4F\uD83D\uDC4F<br>" +
                "此项成绩不仅体现了“清洁喵”产品的优质性能和市场认可[你强][你强]，更是彰显了<font color=\"#dd0000\">**{areaName}{branchName}**</font>团队卓越的经营策略与高效的执行力。[虎虎生威][虎虎生威]乡村电商线下业务中心在此对<font color=\"#dd0000\">**{areaName}**</font>参与此项目的同仁表示由衷的感谢与诚挚的祝贺！[烟花][烟花]<br>" +
                "再接再厉，共创辉煌！[爆竹][爆竹][元气满满][元气满满]");

        private String code;
        private String name;
        private String textTemplate;

        public static GoodNewsEnum getByCode(String code) {
            for (GoodNewsEnum goodNewsEnum : GoodNewsEnum.values()) {
                if (goodNewsEnum.getCode().equals(code)) {
                    return goodNewsEnum;
                }
            }
            return null;
        }
    }

}
