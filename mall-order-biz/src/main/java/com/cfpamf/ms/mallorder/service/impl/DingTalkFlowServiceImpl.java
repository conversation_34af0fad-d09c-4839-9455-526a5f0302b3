package com.cfpamf.ms.mallorder.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.framework.autoconfigure.ZhnxServiceException;
import com.cfpamf.ms.bms.facade.constants.BmsUserStatusEnum;
import com.cfpamf.ms.bms.facade.vo.DictionaryItemVO;
import com.cfpamf.ms.bms.facade.vo.UserListVO;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.builder.DingTalkMessageTemplateBuilder;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.constant.OrdersAfsConst;
import com.cfpamf.ms.mallorder.common.constant.SymbolConstant;
import com.cfpamf.ms.mallorder.common.constant.WorkflowConst;
import com.cfpamf.ms.mallorder.common.enums.*;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.common.util.FlowUtil;
import com.cfpamf.ms.mallorder.common.util.UserUtil;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.controller.fegin.facade.workflow.constant.WorkflowConstant;
import com.cfpamf.ms.mallorder.dto.OrderRefundEventNotifyDTO;
import com.cfpamf.ms.mallorder.integration.facade.BmsIntegration;
import com.cfpamf.ms.mallorder.integration.facade.BmsUserFacade;
import com.cfpamf.ms.mallorder.integration.facade.TemplateMessageFacade;
import com.cfpamf.ms.mallorder.integration.facade.dto.BmsUserInfoVO;
import com.cfpamf.ms.mallorder.integration.hrms.HrmsIntegration;
import com.cfpamf.ms.mallorder.integration.shop.ShopIntegration;
import com.cfpamf.ms.mallorder.mapper.OrderAfterSaleLogMapper;
import com.cfpamf.ms.mallorder.mapper.OrderReturnTrackMapper;
import com.cfpamf.ms.mallorder.model.OrderAfterServiceModel;
import com.cfpamf.ms.mallorder.model.OrderReturnModel;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.v2.common.lock.Lock;
import com.cfpamf.ms.mallorder.validation.OrderReturnAuditValidation;
import com.cfpamf.ms.mallorder.vo.DictionaryCodeVO;
import com.cfpamf.ms.mallorder.vo.OrderReturnApplyCallBackVO;
import com.cfpamf.ms.mallorder.vo.OrderReturnTrackVO;
import com.cfpamf.ms.mallorder.vo.hrmsVO.OutBranchLifeAndInteriorInfoVO;
import com.cfpamf.ms.mallshop.api.StoreAddressFeignClient;
import com.cfpamf.ms.mallshop.api.StoreFeignClient;
import com.cfpamf.ms.mallshop.api.StoreIsolateWhiteListFeignClient;
import com.cfpamf.ms.mallshop.api.VendorFeignClient;
import com.cfpamf.ms.mallshop.enums.WhiteListEnum;
import com.cfpamf.ms.mallshop.request.StoreAddressExample;
import com.cfpamf.ms.mallshop.resp.Store;
import com.cfpamf.ms.mallshop.resp.StoreAddress;
import com.cfpamf.ms.mallshop.resp.Vendor;
import com.cfpamf.ms.mallshop.vo.SelfLiftingPointVo;
import com.cfpamf.ms.mallsystem.vo.Admin;
import com.cfpamf.msgpush.facade.request.templateMessage.BatchBizMessageTemplateReq;
import com.google.api.client.util.Lists;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.util.AssertUtil;
import com.slodon.bbc.core.util.WebUtil;
import com.slodon.bbc.starter.mq.entity.AdminLogSendVO;
import com.slodon.bbc.starter.mq.entity.VendorLogSendVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.slodon.bbc.core.constant.StarterConfigConst.*;

/**
 * 钉钉消息提醒
 */
@Slf4j
@Service
public class DingTalkFlowServiceImpl implements IDingTalkFlowService {
    @Autowired
    private IOrderService orderService;
    @Autowired
    private StoreIsolateWhiteListFeignClient storeIsolateWhiteListFeignClient;
    @Autowired
    private IOrderAfterService orderAfterService;
    @Autowired
    private IOrderReturnService orderReturnService;
    @Autowired
    private IOrderExtendService orderExtendService;
    @Autowired
    private IOrderProductService orderProductService;
    @Autowired
    private BmsIntegration bmsIntegration;
    @Autowired
    private IOrderReturnTrackService returnTrackService;
    @Autowired
    private BmsUserFacade bmsUserFacade;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private FlowUtil flowUtil;
    @Resource
    private TemplateMessageFacade templateMessageFacade;
    @Resource
    private OrderReturnModel orderReturnModel;
    @Resource
    private RabbitTemplate rabbitTemplate;
    @Resource
    private VendorFeignClient vendorFeignClient;
    @Autowired
    private HttpServletRequest request;
    @Autowired
    private Lock lock;
    @Autowired
    private StoreFeignClient storeFeignClient;
    @Autowired
    private StoreAddressFeignClient storeAddressFeignClient;
    @Autowired
    private DingTalkMessageTemplateBuilder dingTalkMessageTemplateBuilder;

    @Autowired
    private OrderReturnAuditValidation orderReturnAuditValidation;

    @Resource
    private OrderAfterSaleLogMapper orderAfterSaleLogMapper;

    @Resource
    private OrderReturnTrackMapper orderReturnTrackMapper;

    @Resource
    private ShopIntegration shopIntegration;
    @Resource
    private IOrderPerformanceBelongsService orderPerformanceBelongsService;
    @Resource
    private OrderAfterServiceModel orderAfterServiceModel;
    @Resource
    private HrmsIntegration hrmsIntegration;




    @Override
    public void orderReturnDingTalkFlowNotify(OrderRefundEventNotifyDTO notifyDTO) {

        OrderPO orderPO = orderService.getByOrderSn(notifyDTO.getOrderSn());
        BizAssertUtil.isTrue(orderPO == null, "OrderReturnApplyConsumer,查询不到" + notifyDTO.getOrderSn() + "的订单信息");
        OrderAfterPO orderAfterPO = orderAfterService.getByAfsSn(notifyDTO.getAfsSn());
        OrderReturnPO orderReturnPO = orderReturnService.getByAfsSn(orderAfterPO.getAfsSn());
        OrderExtendPO orderExtendPO = orderExtendService.getOrderExtendByOrderSn(orderAfterPO.getOrderSn());
        OrderProductPO orderProductPO = orderProductService.getById(orderAfterPO.getOrderProductId());

        if (!orderReturnAuditValidation.isSupportDingTalkAudit(orderPO, orderReturnPO)) {
            log.warn(String.format("售后单%s不支持钉钉审批流", orderReturnPO.getAfsSn()));
            return;
        }

        // key:操作节点、value:操作人
        Map<Integer, String> operatorMap = new HashMap<>();
        // key:操作节点、value:操作人工号
        Map<Integer, String> jobNumberMap = new HashMap<>();
        // key:操作节点、value:操作人手机号码
        Map<Integer, String> mobileMap = new HashMap<>();
        // 通过售后轨迹解析操作人信息
        this.parseReturnTrackUserInfo(orderAfterPO, operatorMap, jobNumberMap, mobileMap);

        log.info("orderReturnDingTalkFlowNotify parseReturnTrackUserInfo 售后单号:{},操作人信息:{},操作人工号:{},操作人手机号:{}",
                orderAfterPO.getAfsSn(), operatorMap, jobNumberMap, mobileMap);

        String key = "order_return_apply_biz_id_" + orderAfterPO.getAfsSn();
        String processId = stringRedisTemplate.opsForValue().get(key);
        String eventType = notifyDTO.getEventType();

        // 流程推动人工号
        String jobNumber = ReturnByEnum.ACCOUNT_MANAGER.getValue().equals(orderReturnPO.getReturnBy())
                ? jobNumberMap.get(OrderReturnOperateTypeEnum.CREATE.getValue()) : orderExtendPO.getManager();

        switch (eventType) {
            // 用户申请
            case "REFUND_APPLY":
                log.info("REFUND_APPLY 退款申请, 启动钉钉审批流程 afsSn:{}", orderReturnPO.getAfsSn());
                // 启动退款申请钉钉审批流程
                this.refundApplyStartDingTalkFlow(orderPO, orderAfterPO, orderProductPO, orderReturnPO, orderExtendPO, key,
                        jobNumberMap.get(OrderReturnOperateTypeEnum.CREATE.getValue()),
                        operatorMap.get(OrderReturnOperateTypeEnum.CREATE.getValue()),
                        mobileMap.get(OrderReturnOperateTypeEnum.CREATE.getValue()));
                break;
            // 商家同意
            case "STORE_AGREE":
                log.info("STORE_AGREE 商家同意通知人 operatorJobNumber:{} afsSn:{}", jobNumber, orderAfterPO.getAfsSn());
                // 推动流程进行
                if (!RefundHandleSourceEnum.DING_TALK.getCode().equals(notifyDTO.getApplySource())) {
                    // 流程推进人获取：如果实际审批人在bms中无法获取，则取默认值
                    String flowJobNumber = jobNumberMap.getOrDefault(OrderReturnOperateTypeEnum.STORE_AUDIT.getValue(),
                            parseRefundStoreAuditor(orderReturnPO, orderExtendPO).split(SymbolConstant.COMMA_EN)[0]);
                    flowUtil.auditPass(processId,"商家审批同意", flowJobNumber);
                }
                if (ReturnTypeEnum.RETURN_AND_REFUND.getValue().equals(orderReturnPO.getReturnType())) {
                    this.refundCustomerReturnGoodsNotify(orderExtendPO, orderProductPO, orderReturnPO, orderAfterPO,
                            jobNumber, mobileMap.get(OrderReturnOperateTypeEnum.CREATE.getValue()));
                }
                break;
            // 商家拒绝
            case "STORE_REFUSED":
                log.info("STORE_REFUSED 商家拒绝通知人 afsSn:{} operatorJobNumber:{}", notifyDTO.getAfsSn(), jobNumber);
                if (!RefundHandleSourceEnum.DING_TALK.getCode().equals(notifyDTO.getApplySource())) {
                    // 流程推进人获取：如果实际审批人在bms中无法获取，则取默认值
                    String flowJobNumber = jobNumberMap.getOrDefault(OrderReturnOperateTypeEnum.STORE_AUDIT.getValue(),
                            parseRefundStoreAuditor(orderReturnPO, orderExtendPO).split(SymbolConstant.COMMA_EN)[0]);
                    flowUtil.auditReject(flowJobNumber, processId,"商家拒绝审批");
                }
                break;
            // 售后用户发货
            case "REFUND_DELIVER_GOODS":
                // 推动流程进行
                log.info("REFUND_DELIVER_GOODS 售后用户发货 afsSn:{} operatorJobNumber:{}", orderReturnPO.getAfsSn(), jobNumber);
                if (!RefundHandleSourceEnum.DING_TALK.getCode().equals(notifyDTO.getApplySource())) {
                    String[] storeAuditArr = parseRefundStoreAuditor(orderReturnPO, orderExtendPO).split(SymbolConstant.COMMA_EN);
                    flowUtil.auditPass(processId, "售后用户发货自动审批通过",
                            jobNumberMap.getOrDefault(OrderReturnOperateTypeEnum.CUSTOMER_DELIVER_GOODS.getValue(),storeAuditArr[0]));
                }
                break;
            case "REFUND_STORE_RECEIVE_GOODS":
                // 推动流程进行
                log.info("REFUND_STORE_RECEIVE_GOODS 售后商家收货 afsSn:{}", orderReturnPO.getAfsSn());
                if (!RefundHandleSourceEnum.DING_TALK.getCode().equals(notifyDTO.getApplySource())) {
                    String[] storeAuditArr = parseRefundStoreAuditor(orderReturnPO, orderExtendPO).split(SymbolConstant.COMMA_EN);
                    log.info("REFUND_STORE_RECEIVE_GOODS 售后商家收货  operatorJobNumber:{}", jobNumber);
                    flowUtil.auditPass(processId, "售后商家收货", storeAuditArr[0]);
                }
                // 如果当前工作流是待商家收货，则推动流程
                /*if (!RefundHandleSourceEnum.DING_TALK.getCode().equals(notifyDTO.getApplySource())) {
                    TaskVo currentTask = flowUtil.getCurrentTaskByInstanceId(FlowProcessType.MALL_ORDER_REFUND_RETURN_APPLY.getCode(), processId);
                    if (RefundDingTalkAuditRoleEnum.STORE_RECEIVE_GOODS.getRoleCode().equals(currentTask.getAssignee()))
                }*/
                break;
            case "REFUND_STORE_REJECT_RECEIVE_GOODS":
                if (!RefundHandleSourceEnum.DING_TALK.getCode().equals(notifyDTO.getApplySource())) {
                    // 流程推进人获取：如果实际审批人在bms中无法获取，则取默认值
                    String flowJobNumber = jobNumberMap.getOrDefault(OrderReturnOperateTypeEnum.STORE_RECEIVE.getValue(),
                            parseRefundStoreAuditor(orderReturnPO, orderExtendPO).split(SymbolConstant.COMMA_EN)[0]);
                    log.info("REFUND_STORE_REJECT_RECEIVE_GOODS 商家拒绝收货 通知人 afsSn:{} jobNumber:{}", notifyDTO.getAfsSn(), flowJobNumber);
                    // 流程拒绝
                    flowUtil.suspendProc(flowJobNumber, processId, "商家拒绝收货");
                }
                break;
            // 退款成功
            case "REFUND":
                if (!RefundHandleSourceEnum.DING_TALK.getCode().equals(notifyDTO.getApplySource())) {
                    // 流程推进人获取：如果实际审批人在bms中无法获取，则取默认值
                    String flowJobNumber = jobNumberMap.getOrDefault(OrderReturnOperateTypeEnum.PLATFORM_AUDIT.getValue(),
                            parseRefundPlatformAuditor(orderReturnPO, orderExtendPO).split(SymbolConstant.COMMA_EN)[0]);
                    log.info("REFUND 平台同意通知申请人信息 afsSn:{} flowJobNumber:{}", orderReturnPO.getAfsSn(), flowJobNumber);
                    // 推动流程进行
                    flowUtil.auditPass(processId, "平台同意", flowJobNumber);
                }
                // 发送预警消息
                if (ReturnByEnum.ACCOUNT_MANAGER.getValue().equals(orderReturnPO.getReturnBy())) {
                    this.agreeApplyNotify(orderAfterPO, orderProductPO,"售后单退款成功", orderReturnPO, jobNumber,
                            mobileMap.get(OrderReturnOperateTypeEnum.CREATE.getValue()));
                } else if (StringUtils.isNotBlank(orderExtendPO.getManager())) {
                    this.agreeApplyNotify(orderAfterPO, orderProductPO,"售后单退款成功", orderReturnPO, orderExtendPO.getManager(),
                            mobileMap.get(OrderReturnOperateTypeEnum.CREATE.getValue()));
                }
                break;
            //平台拒绝
            case "PLATFORM_REFUSED":
                if (!RefundHandleSourceEnum.DING_TALK.getCode().equals(notifyDTO.getApplySource())) {
                    // 流程推进人获取：如果实际审批人在bms中无法获取，则取默认值
                    String flowJobNumber = jobNumberMap.getOrDefault(OrderReturnOperateTypeEnum.PLATFORM_AUDIT.getValue(),
                            parseRefundPlatformAuditor(orderReturnPO, orderExtendPO).split(SymbolConstant.COMMA_EN)[0]);
                    log.info("PLATFORM_REFUSED 平台拒绝通知人 afsSn:{} jobNumber:{}", notifyDTO.getAfsSn(), flowJobNumber);
                    // 流程拒绝
                    flowUtil.suspendProc(flowJobNumber, processId, "平台拒绝");
                }
                break;
            // 售后单撤销
            case "REVOKE_REFUND":
                if (!RefundHandleSourceEnum.DING_TALK.getCode().equals(notifyDTO.getApplySource())) {
                    // 流程推进人获取：如果实际审批人在bms中无法获取，则取默认值
                    String flowJobNumber = jobNumberMap.getOrDefault(OrderReturnOperateTypeEnum.REVOKE_REFUND.getValue(),
                            parseRefundStoreAuditor(orderReturnPO, orderExtendPO).split(SymbolConstant.COMMA_EN)[0]);
                    flowUtil.revokeProc(flowJobNumber, processId, "售后撤销");
                }
                break;
            default:
                log.info("无此审批类型{}",eventType);
                break;
        }

    }

    /**
     * 通过售后轨迹解析操作人信息
     */
    private void parseReturnTrackUserInfo(OrderAfterPO orderAfterPO, Map<Integer, String> operatorMap, Map<Integer, String> jobNumberMap,
                                          Map<Integer, String> mobileMap) {
        List<OrderReturnTrackVO> orderReturnTrackVOByAfsSn = returnTrackService.getOrderReturnTrackVOByAfsSn(orderAfterPO.getAfsSn());
        if (CollectionUtils.isNotEmpty(orderReturnTrackVOByAfsSn)) {
            for (OrderReturnTrackVO trackVO : orderReturnTrackVOByAfsSn) {
                operatorMap.put(trackVO.getOperateTypeValue(), trackVO.getOperator());
                String[] operatorInfo = trackVO.getOperator().split(SymbolConstant.HYPHEN);
                if (operatorInfo.length > 1) {
                    mobileMap.put(trackVO.getOperateTypeValue(), operatorInfo[1]);
                    UserListVO employeesByAccount = bmsIntegration.getEmployeesByAccount(operatorInfo[1]);
                    if (Objects.nonNull(employeesByAccount)) {
                        jobNumberMap.put(trackVO.getOperateTypeValue(), employeesByAccount.getJobNumber());
                    }
                }
            }
        }
    }

    /**
     * 退款申请启动钉钉审批流程
     */
    private void refundApplyStartDingTalkFlow(OrderPO orderPO, OrderAfterPO orderAfterPO, OrderProductPO orderProductPO, OrderReturnPO orderReturnPO,
                                              OrderExtendPO orderExtendPO, String key, String jobNumber, String applyer, String applyPhone) {
        // 查询对应商家审核人
        String storeAudit = this.parseRefundStoreAuditor(orderReturnPO, orderExtendPO);

        // 查询配置的平台审批人
        String platformAudit = this.parseRefundPlatformAuditor(orderReturnPO, orderExtendPO);

        // 解析申请人信息
        UserListVO employee = bmsIntegration.getEmployeesByAccount(applyPhone);
        if (Objects.nonNull(employee)) {
            applyer = String.format("%s %s", employee.getEmployeeName(), employee.getMobile());
        }

        // 内部退款，钉钉发起人取退款申请人
        jobNumber = ReturnByEnum.ACCOUNT_MANAGER.getValue().equals(orderReturnPO.getReturnBy()) ? jobNumber : orderExtendPO.getManager();
        // 申请人无管护客户经理，取商家审批的一人处理
        jobNumber = StringUtils.isBlank(jobNumber) ? storeAudit.split(SymbolConstant.COMMA_EN)[0] : jobNumber;

        // 钉钉卡片参数
        Map<String, Object> vars = new HashMap<>();
        // 卡片消息参数
        vars.put("FORM_DATA", dingTalkMessageTemplateBuilder.buildRefundApplyDingTalkMessageTemplates(orderPO, orderAfterPO,
                orderProductPO, orderReturnPO, orderExtendPO, applyer));

        // 获取主任审批节点的审批人
        String directorAudit;
        List<String> directorJobNumberList = this.parseRefundDirectorAudit(orderPO, orderExtendPO);
        if (CollectionUtils.isNotEmpty(directorJobNumberList)) {
            directorAudit = String.join(SymbolConstant.COMMA_EN, directorJobNumberList);
        } else {
            log.info("获取分支主任工号为空,默认取店铺审批人");
            directorAudit = storeAudit.split(SymbolConstant.COMMA_EN)[0];
        }

        // 是否是生服自提订单
        boolean isSelfLift = OrderPatternEnum.SELF_LIFT.getValue().equals(orderPO.getOrderPattern())
                && storeIsolateWhiteListFeignClient.isStoreOnList(WhiteListEnum.DING_DING_REFUND_WHITE_LIST, orderPO.getStoreId());

        // 启动的流程ID
        String processId = SymbolConstant.HYPHEN;
        if (ReturnTypeEnum.REFUND.getValue().equals(orderReturnPO.getReturnType())) {
            // 仅退款，启动仅退款流程
            //指定审批人
            vars.put(RefundDingTalkAuditRoleEnum.DIRECTOR_AUDIT.getCandidateAudit(), directorAudit);
            vars.put(RefundDingTalkAuditRoleEnum.STORE_AUDIT.getCandidateAudit(), storeAudit);
            vars.put(RefundDingTalkAuditRoleEnum.PLATFORM_AUDIT.getCandidateAudit(), platformAudit);
            // 发起审批流
            processId = flowUtil.startProcess(FlowProcessType.MALL_ORDER_REFUND_APPLY, orderAfterPO.getAfsSn(), jobNumber, vars);
        } else if (ReturnTypeEnum.RETURN_AND_REFUND.getValue().equals(orderReturnPO.getReturnType())) {
            // 退货退款，启动退货退款流程
            //指定审批人
            vars.put("input", isSelfLift ? 1 : 2);

            if (isSelfLift)  {
                vars.put(RefundDingTalkAuditRoleEnum.CUSTOMER_DELIVER_GOODS_2.getCandidateAudit(), jobNumber);
                vars.put(RefundDingTalkAuditRoleEnum.SELF_POINT_RECEIVE_GOODS.getCandidateAudit(), directorAudit);
                vars.put(RefundDingTalkAuditRoleEnum.STORE_AUDIT_2.getCandidateAudit(), storeAudit);
            } else {
                vars.put(RefundDingTalkAuditRoleEnum.DIRECTOR_AUDIT.getCandidateAudit(), directorAudit);
                vars.put(RefundDingTalkAuditRoleEnum.STORE_AUDIT.getCandidateAudit(), storeAudit);
                vars.put(RefundDingTalkAuditRoleEnum.CUSTOMER_DELIVER_GOODS.getCandidateAudit(), jobNumber);
                vars.put(RefundDingTalkAuditRoleEnum.STORE_RECEIVE_GOODS.getCandidateAudit(), storeAudit);
            }
            vars.put(RefundDingTalkAuditRoleEnum.PLATFORM_AUDIT.getCandidateAudit(), platformAudit);

            // 发起审批流
            processId = flowUtil.startProcess(FlowProcessType.MALL_ORDER_REFUND_RETURN_APPLY, orderAfterPO.getAfsSn(), jobNumber, vars);
        }

//        // 内部申请退款，直接客户经理审批节点审批通过
//        if (ReturnByEnum.ACCOUNT_MANAGER.getValue().equals(orderReturnPO.getReturnBy())) {
//            flowUtil.auditPass(processId,"内部退款客户经理审批通过", jobNumber);
//        } else if (StringUtils.isBlank(orderExtendPO.getBranch()) || CollectionUtils.isEmpty(directorJobNumberList)) {
//            flowUtil.auditPass(processId,"无管护分支或未找到分支主任审批通过", jobNumber);
//        }
        // 订单无管护分支或未找到分支主任 直接审批通过
        if ((StringUtils.isBlank(orderExtendPO.getBranch()) || CollectionUtils.isEmpty(directorJobNumberList)) && !isSelfLift) {
            flowUtil.auditPass(processId,"无管护分支或未找到分支主任审批通过", jobNumber);
        }

        //将流程id缓存
        stringRedisTemplate.opsForValue().set(key, processId);
        log.info("流程开启成功processId={}", processId);
    }

    /**
     * 根据分支编码获取分支主任工号集合
     * @param branch
     * @return
     */
    private List<String> getBranchDirector(String branch) {
        if (StringUtils.isEmpty(branch)) {
            return Collections.emptyList();
        }
        List<UserListVO> directorList = bmsIntegration.getDirectorByBranchCode(branch);
        // 只保留正常状态和锁定状态的主任
        return directorList.stream().filter(item -> Objects.equals(BmsUserStatusEnum.UserNormal.getValue(), item.getUserStatus())
                        || Objects.equals(BmsUserStatusEnum.UserLock.getValue(), item.getUserStatus())).map(UserListVO::getJobNumber).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
    }

    /**
     * 获取主任审批节点分支
     */
    public String parseRefundDirectorAuditBranchCode(OrderPO orderPO, OrderExtendPO orderExtendPO) {
        if (bmsIntegration.getLifeServiceStoreId().contains(orderPO.getStoreId())) {
            String branchCode = null;
            // 自提订单，取自提点负责人
            if (Objects.equals(OrderPatternEnum.SELF_LIFT.getValue(), orderPO.getOrderPattern())) {
                SelfLiftingPointVo selfLiftingPoint = shopIntegration.getSelfLiftingPointV2(orderExtendPO.getPointId());
                // 查询自提点的分支主任作为审批人
                if (Objects.nonNull(selfLiftingPoint)) {
                    branchCode = selfLiftingPoint.getBranchCode();
                }
            } else {
                // 非自提订单，取业绩归属人分支处理
                OrderPerformanceBelongsPO orderPerformanceBelongsPO = orderPerformanceBelongsService.getEntityByOrderSn(orderPO.getOrderSn());
                if (Objects.nonNull(orderPerformanceBelongsPO)) {
                    branchCode = orderPerformanceBelongsPO.getEmployeeBranchCode();
                }
            }
            return StringUtils.isNotBlank(branchCode) ? branchCode : "";
        } else {
            return orderExtendPO.getBranch();
        }
    }

    /**
     * 查询主任审批节点审批人
     * （如果是生服店铺且是自提订单，则机构主任取自提点负责人，非自提订单取业绩归属分支负责人）
     */
    public List<String> parseRefundDirectorAudit(OrderPO orderPO, OrderExtendPO orderExtendPO) {
        String branchCode = this.parseRefundDirectorAuditBranchCode(orderPO, orderExtendPO);
        return StringUtils.isBlank(branchCode) ? Collections.emptyList() : getBranchDirector(branchCode);
    }


    /**
     * 查询退款钉钉审核店铺审批人
     */
    private String parseRefundStoreAuditor(OrderReturnPO orderReturnPO, OrderExtendPO orderExtendPO) {
        // 查询配置的该店铺指定的审批人
        List<DictionaryItemVO> sellerDictionaryItems = bmsIntegration.listDictionaryItemsByTypeCode(CommonConst.ORDER_RETURN_APPLYER_SELLER,
                CommonConst.MALL_SYSTEM_MANAGE_ID);
        DictionaryItemVO sellerDictionaryItem = sellerDictionaryItems.stream()
                .filter(x -> x.getItemName().contains(String.valueOf(orderReturnPO.getStoreId())))
                .findFirst().orElse(null);
        BizAssertUtil.isTrue(Objects.isNull(sellerDictionaryItem) || StringUtils.isEmpty(sellerDictionaryItem.getItemCode()),
                String.format("售后单%s发起售后申请,未找到bms配置的店铺审核人信息", orderReturnPO.getAfsSn()));

        // 店铺配置
        List<DictionaryCodeVO> approverList = JSONArray.parseArray(sellerDictionaryItem.getItemCode(), DictionaryCodeVO.class);

        // 根据区域编码，查询对应商家审核人,无对应区域取default
        Optional<DictionaryCodeVO> storeAudit;
        if (StringUtils.isBlank(orderExtendPO.getAreaCode())) {
            storeAudit = approverList.stream().filter(o -> o.getAreaCode().contains("default")).findFirst();
        } else {
            storeAudit = approverList.stream().filter(o -> o.getAreaCode().contains(orderExtendPO.getAreaCode()))
                    .findFirst();
            if (!storeAudit.isPresent()) {
                storeAudit = approverList.stream().filter(o -> o.getAreaCode().contains("default")).findFirst();
            }
        }
        if (!storeAudit.isPresent()) {
            throw new BusinessException(String.format("售后单%s未找到对应的商家审核人, 请确认BMS配置信息", orderReturnPO.getAfsSn()));
        }

        log.info("parseRefundStoreAuditor 钉钉退款提醒退款单号:{},店铺审批人:{}", orderReturnPO.getAfsSn(), storeAudit.get().getJobNumber());
        return storeAudit.get().getJobNumber();
    }

    /**
     * 查询退款钉钉审核平台审批人
     */
    private String parseRefundPlatformAuditor(OrderReturnPO orderReturnPO, OrderExtendPO orderExtendPO) {
        List<DictionaryItemVO> platformDictionaryItems = bmsIntegration.listDictionaryItemsByTypeCode(CommonConst.ORDER_RETURN_APPLYER_PLATFORM,
                CommonConst.MALL_SYSTEM_MANAGE_ID);
        DictionaryItemVO platformDictionaryItem = platformDictionaryItems.stream()
                .filter(x -> x.getItemName().contains(String.valueOf(orderReturnPO.getStoreId())))
                .findFirst().orElse(null);

        BizAssertUtil.isTrue(Objects.isNull(platformDictionaryItem) || StringUtils.isEmpty(platformDictionaryItem.getItemCode()),
                String.format("售后单%s发起售后申请,未找到bms配置的平台审核人信息", orderReturnPO.getAfsSn()));

        // 平台配置
        List<DictionaryCodeVO> auditList = JSONArray.parseArray(platformDictionaryItem.getItemCode(), DictionaryCodeVO.class);

        // 根据区域编码，查询对应商家审核人,无对应区域取default
        Optional<DictionaryCodeVO> platformAudit;
        if (StringUtils.isBlank(orderExtendPO.getAreaCode())) {
            platformAudit = auditList.stream().filter(o -> o.getAreaCode().contains("default")).findFirst();
        } else {
            platformAudit = auditList.stream().filter(o -> o.getAreaCode().contains(orderExtendPO.getAreaCode()))
                    .findFirst();
            if (!platformAudit.isPresent()) {
                platformAudit = auditList.stream().filter(o -> o.getAreaCode().contains("default")).findFirst();
            }
        }
        if (!platformAudit.isPresent()) {
            throw new BusinessException(String.format("售后单%s未找到对应的平台审核人, 请确认BMS配置信息", orderReturnPO.getAfsSn()));
        }

        log.info("parseRefundPlatformAuditor 钉钉退款提醒退款单号:{},平台审批人:{}", orderReturnPO.getAfsSn(), platformAudit.get().getJobNumber());
        return platformAudit.get().getJobNumber();
    }


    /**
     * 售后工作流结束钉钉结果回调
     */
    @Override
    public String flowEndCallBack(OrderReturnApplyCallBackVO flowCallBackVO) {
        return this.flowCallBack(flowCallBackVO);
    }

    /**
     * 售后工作流审批钉钉结果回调
     */
    @Override
    public String flowCallBack(OrderReturnApplyCallBackVO flowCallBackVO) {
        if (!flowCallBackVO.getExecuteResult()){
            return "【售后审批】钉钉审核失败";
        }
        OrderAfterPO orderAfterPO = orderAfterService.getByAfsSn(flowCallBackVO.getBizId());
        OrderPO orderPO = orderService.getByOrderSn(orderAfterPO.getOrderSn());
        OrderExtendPO orderExtendPO = orderExtendService.getOrderExtendByOrderSn(orderAfterPO.getOrderSn());
        OrderReturnPO orderReturnPO = orderReturnService.getByAfsSn(flowCallBackVO.getBizId());
        OrderProductPO orderProductPO = orderProductService.selectOneByOrderProductId(orderAfterPO.getOrderProductId());

        // 根据用户ID从bms查询用户信息
        BmsUserInfoVO userInfo = bmsIntegration.getUserInfo(flowCallBackVO.getOperatorUserId());
        flowCallBackVO.setOperatorMobile(userInfo.getMobile());
        flowCallBackVO.setJobNumber(userInfo.getJobNumber());

        switch (flowCallBackVO.getOptType()) {
            case WorkflowConstant.PROCESS_ACCEPT:
                // 审批同意 推动业务下行-商家审批通过
                if (RefundDingTalkAuditRoleEnum.STORE_AUDIT.getRoleCode().equals(flowCallBackVO.getNodeKey()) ) {
                    log.info("钉钉退款商家同意{}", JSONObject.toJSONString(flowCallBackVO));
                    this.dingTalkStoreAuditRefund(flowCallBackVO, orderAfterPO, orderPO, orderReturnPO, true);
                } else if (RefundDingTalkAuditRoleEnum.STORE_RECEIVE_GOODS.getRoleCode().equals(flowCallBackVO.getNodeKey()) ||
                        RefundDingTalkAuditRoleEnum.STORE_AUDIT_2.getRoleCode().equals(flowCallBackVO.getNodeKey())) {
                    log.info("钉钉退款商家确认收货{}", JSONObject.toJSONString(flowCallBackVO));
                    this.dingTalkRefundStoreReceiveGoods(flowCallBackVO, orderAfterPO, orderPO, orderReturnPO, true);
                }
                // 如果是主任审批的回调，售后单的实际状态已经商家审批了，审批通过
                else if (RefundDingTalkAuditRoleEnum.DIRECTOR_AUDIT.getRoleCode().equals(flowCallBackVO.getNodeKey())
                    || RefundDingTalkAuditRoleEnum.SELF_POINT_RECEIVE_GOODS.getRoleCode().equals(flowCallBackVO.getNodeKey())) {
                    if (OrderReturnStatus.WAIT_SELF_PICKUP_POINT_RECEIVED.getValue().equals(orderReturnPO.getState())) {
                        Member member = new Member();
                        member.setMemberId(userInfo.getEmployeeId().intValue());
                        member.setMemberName(userInfo.getEmployeeName());
                        member.setMemberMobile(userInfo.getMobile());
                        orderAfterServiceModel.selfPointReceiveGoods(member, flowCallBackVO.getBizId());
                    } else if (OrderReturnStatus.STORE_AGREE_REFUND.getValue().equals(orderReturnPO.getState()) ||
                            OrderReturnStatus.STORE_AGREE_RETURN.getValue().equals(orderReturnPO.getState())) {
                        log.info("钉钉退款分支主任审批通过 RefundDingTalkAuditRole DIRECTOR_AUDIT {}", JSONObject.toJSONString(flowCallBackVO));
                        String[] storeAuditArr = parseRefundStoreAuditor(orderReturnPO, orderExtendPO).split(SymbolConstant.COMMA_EN);
                        flowUtil.auditPass(flowCallBackVO.getProcInstId(), "商家审批同意", storeAuditArr[0]);
                    } else {
                        // 记录分支主任审批轨迹
                        directorApprovalRecord(flowCallBackVO, true, userInfo.getEmployeeId());
                    }
                }
                break;
            case WorkflowConstant.PROCESS_SUSPEND:
                // 如果当前订单状态为撤销，说明售后单是在非流程平台撤销时触发的流程终止（流程平台没有撤销按钮），直接发送撤销通知
                if (OrderReturnStatus.REVOKE_REFUND.getValue().equals(orderReturnPO.getState())) {
                    this.refundRevokeNotify(orderAfterPO, orderProductPO,"撤销退款", flowCallBackVO);
                } else if (RefundDingTalkAuditRoleEnum.DIRECTOR_AUDIT.getRoleCode().equals(flowCallBackVO.getNodeKey())
                        || RefundDingTalkAuditRoleEnum.SELF_POINT_RECEIVE_GOODS.getRoleCode().equals(flowCallBackVO.getNodeKey())) {
                    // 订单为商家拒绝状态 说明已经在平台端已审核，默认主任审批的回调不需要记录轨迹
                    if (!OrderReturnStatus.STORE_REFUSED.getValue().equals(orderReturnPO.getState())) {
                        // 如果是自提订单，设置为商家拒绝，但轨迹记录为主任拒绝
                        if (storeIsolateWhiteListFeignClient.isStoreOnList(WhiteListEnum.DING_DING_REFUND_WHITE_LIST, orderPO.getStoreId())) {
                            this.dingTalkStoreAuditRefund(flowCallBackVO, orderAfterPO, orderPO, orderReturnPO, false);
                        } else {
                            directorApprovalRecord(flowCallBackVO, false, userInfo.getEmployeeId());
                        }
                    }
                    // 主任拒绝，给店铺运营人员发送拒绝的提醒
                    this.manageDingTalkRefuseNotify("分支主任拒绝", orderAfterPO, orderReturnPO, flowCallBackVO);
                } else if (RefundDingTalkAuditRoleEnum.STORE_AUDIT.getRoleCode().equals(flowCallBackVO.getNodeKey()) ) {
                    // 商家拒绝
                    log.info("钉钉退款商家拒绝{}", JSONObject.toJSONString(flowCallBackVO));
                    dingTalkStoreAuditRefund(flowCallBackVO, orderAfterPO, orderPO, orderReturnPO, false);
                    // 发送钉钉拒绝消息
                    this.refuseApplyNotify(orderExtendPO, orderAfterPO, orderProductPO,"商家拒绝退款", flowCallBackVO, orderAfterPO.getStoreRemark());
                } else if (RefundDingTalkAuditRoleEnum.STORE_AUDIT_2.getRoleCode().equals(flowCallBackVO.getNodeKey())) {
                    this.dingTalkRefundStoreReceiveGoods(flowCallBackVO, orderAfterPO, orderPO, orderReturnPO, false);
                } else if (RefundDingTalkAuditRoleEnum.STORE_RECEIVE_GOODS.getRoleCode().equals(flowCallBackVO.getNodeKey())) {
                    log.info("钉钉退款商家确认收货拒绝{}", JSONObject.toJSONString(flowCallBackVO));
                    // 商家收货无拒绝
                } else if (RefundDingTalkAuditRoleEnum.PLATFORM_AUDIT.getRoleCode().equals(flowCallBackVO.getNodeKey())) {
                    // 平台审批拒绝中止流程
                    log.info("钉钉退款平台拒绝{}", JSONObject.toJSONString(flowCallBackVO));
                    this.dingTalkPlatformAuditRefund(flowCallBackVO, orderAfterPO, orderPO, orderReturnPO, false);
                    this.refuseApplyNotify(orderExtendPO, orderAfterPO, orderProductPO,"平台拒绝退款", flowCallBackVO, orderAfterPO.getPlatformRemark());
                } else {
                    throw new BusinessException(String.format("售后单%s未知的钉钉审核拒绝节点", orderAfterPO.getAfsSn()));
                }
                break;
            case WorkflowConstant.PROCESS_END:
                // 审批同意流程中止
                log.info("钉钉退款平台同意{}", JSONObject.toJSONString(flowCallBackVO));
                this.dingTalkPlatformAuditRefund(flowCallBackVO, orderAfterPO, orderPO, orderReturnPO, true);
                break;
            default:
                log.info("无此审批类型{}", flowCallBackVO.getOptType());
                break;
        }

        // 生服店铺，每个节点都需要通知店铺审核人
        if (bmsIntegration.getLifeServiceStoreId().contains(orderPO.getStoreId())) {
            this.refundNotifyLifeEmployee(orderAfterPO, orderReturnPO, orderPO, orderProductPO, orderExtendPO, flowCallBackVO);
        }

        return WorkflowConst.CALL_BACK_RESULT_SUCCESS;
    }

    private void directorApprovalRecord(OrderReturnApplyCallBackVO flowCallBackVO, boolean approvalFlag, Long employeeId) {
        // 记录轨迹
        OrderAfterPO orderafterPo = orderAfterService.getByAfsSn(flowCallBackVO.getBizId());
        AssertUtil.notNull(orderafterPo, "售后单不存在,售后单号:" + flowCallBackVO.getBizId());
        OrderAfterSaleLogPO record = new OrderAfterSaleLogPO();
        record.setAfsSn(flowCallBackVO.getBizId());
        String content = "分支主任钉钉审批".concat(approvalFlag ? "通过" : "拒绝");
        record.setContent(content);
        record.setState(approvalFlag ? OrdersAfsConst.RETURN_STATE_150 + "" : OrdersAfsConst.RETURN_STATE_151 + "");
        record.setLogRole(OrderConst.LOG_ROLE_ADMIN);
        record.setLogUserName(flowCallBackVO.getOperatorName());
        record.setLogUserId(employeeId);
        record.setAfsType(orderafterPo.getAfsType());

        //
        OrderReturnTrackPO trackPo = OrderReturnTrackPO.builder().afsSn(flowCallBackVO.getBizId()).operatorRole(OperationRoleEnum.NULL.getValue())
                        .operateResult(approvalFlag ? AuditResultEnum.AUDIT_PASS.getValue() : AuditResultEnum.AUDIT_REFUSE.getValue()).operateTime(new Date())
                        .operateRemark(content).operateType(OrderReturnOperateTypeEnum.DIRECTOR_AUDIT.getValue())
                .operator(flowCallBackVO.getOperatorUserId() + "-" + flowCallBackVO.getOperatorName()).build();


        orderAfterSaleLogMapper.insert(record);
        orderReturnTrackMapper.insert(trackPo);

    }

    /**
     * 售后撤销钉钉通知
     */
     public void refundRevokeNotify(OrderAfterPO orderAfterPO, OrderProductPO orderProductPO, String title,
                                    OrderReturnApplyCallBackVO flowCallBackVO) {
         log.info("退款撤销通知处理人工号{} afsSn {}", flowCallBackVO.getJobNumber(), orderAfterPO.getAfsSn());
         OrderReturnTrackVO createTrackVO = returnTrackService.getOrderReturnTrackVOByAfsSnAndType(orderAfterPO.getAfsSn(), OrderReturnOperateTypeEnum.CREATE);
         if (Objects.isNull(createTrackVO)) {
             log.info("未找到售后单 {} 的创建轨迹, 不发送钉钉消息", orderAfterPO.getAfsSn());
             return;
         }
         String[] operatorInfo = createTrackVO.getOperator().split(SymbolConstant.HYPHEN);
         if (operatorInfo.length < 2) {
             return;
         }
         // 获取申请人的工号
         UserListVO employeesByAccount = bmsIntegration.getEmployeesByAccount(operatorInfo[1]);
         if (Objects.isNull(employeesByAccount)) {
             return;
         }
         // 审批人取撤销人
         OrderReturnTrackVO revokeTrackVO = returnTrackService.getOrderReturnTrackVOByAfsSnAndType(orderAfterPO.getAfsSn(),
                 OrderReturnOperateTypeEnum.REVOKE_REFUND);
         if (Objects.isNull(revokeTrackVO)) {
             log.info("未找到售后单 {} 的撤销轨迹, 不发送钉钉消息", orderAfterPO.getAfsSn());
             return;
         }

         BatchBizMessageTemplateReq req = dingTalkMessageTemplateBuilder.buildRefundRefuseNoticeDingTalkMessage(title, orderAfterPO,
                 orderProductPO, Collections.singletonList(employeesByAccount.getJobNumber()),"撤销退款", revokeTrackVO.getOperator(),
                  revokeTrackVO.getOperateRemark());
         log.info("退款撤销发送钉钉消息入参{}", JSONObject.toJSONString(req));
         templateMessageFacade.bizTemplateBatchSend(req);
     }

    /**
     * 分支主任拒绝，发送钉钉通知
     */
     public void manageDingTalkRefuseNotify(String title, OrderAfterPO orderAfterPO, OrderReturnPO orderReturnPO,
                                            OrderReturnApplyCallBackVO flowCallBackVO) {
         // 查询售后单商家审批人
         OrderExtendPO orderExtendPO = orderExtendService.getOrderExtendByOrderSn(orderAfterPO.getOrderSn());
         String storeAuditors = parseRefundStoreAuditor(orderReturnPO, orderExtendPO);
         if (StringUtils.isBlank(storeAuditors)) {
             log.warn("售后单{}分支主任钉钉审批拒绝,未找到对应商家审批人", orderAfterPO.getAfsSn());
             return;
         }

         // 构建消息内容
         OrderProductPO orderProductPO = orderProductService.selectOneByOrderProductId(orderAfterPO.getOrderProductId());
         BatchBizMessageTemplateReq messageTemplateReq = dingTalkMessageTemplateBuilder.buildManageRefundRefuseNoticeDingTalkMessage(
                 title, Arrays.asList(storeAuditors.split(SymbolConstant.COMMA_EN)), orderAfterPO, orderProductPO, flowCallBackVO);

         // 发送钉钉通知
         templateMessageFacade.bizTemplateBatchSend(messageTemplateReq);
     }

    /**
     * 售后消息通知生服对接人
     */
     public void refundNotifyLifeEmployee(OrderAfterPO orderAfterPO, OrderReturnPO orderReturnPO, OrderPO orderPO, OrderProductPO orderProductPO,
                                          OrderExtendPO orderExtendPO, OrderReturnApplyCallBackVO flowCallBackVO) {

         String branchCode = parseRefundDirectorAuditBranchCode(orderPO, orderExtendPO);

         if (StringUtils.isBlank(branchCode)) {
             log.info("refundNotifyLifeEmployee branchCode 为空");
             return;
         }

         // 查询生服对接人
         Map<String, OutBranchLifeAndInteriorInfoVO> branch2Info = hrmsIntegration.queryBranchLifeAndInteriorList(Collections.singletonList(branchCode));
         if (MapUtils.isEmpty(branch2Info) || !branch2Info.containsKey(branchCode)) {
             log.info("未找到分支{}的生服对接人", branchCode);
             return;
         }
         List<String> lifeEmployeeCodeList = branch2Info.get(branchCode).getLifeEmployeeCodeList();
         if (CollectionUtils.isEmpty(lifeEmployeeCodeList)) {
             log.info("分支 {} 的生服对接人不存在", orderExtendPO.getBranch());
             return;
         }

         // 查询自提点信息
         SelfLiftingPointVo selfLiftingPoint = null;
         if (Objects.nonNull(orderExtendPO.getPointId())) {
             selfLiftingPoint = shopIntegration.getSelfLiftingPointV2(orderExtendPO.getPointId());
         }

         BatchBizMessageTemplateReq messageTemplateReq = dingTalkMessageTemplateBuilder.buildLifeEmployeeRefundDingTalkMessage(
                 orderPO, orderReturnPO, orderAfterPO, orderProductPO, flowCallBackVO, lifeEmployeeCodeList, selfLiftingPoint);

         log.info("退款通知生服对接人入参{}", JSONObject.toJSONString(messageTemplateReq));

         // 发送钉钉通知
         templateMessageFacade.bizTemplateBatchSend(messageTemplateReq);
     }

    /**
     * 售后申请被平台/商家拒绝，发送通知给客户经理
     */
    private void refuseApplyNotify(OrderExtendPO orderExtendPO, OrderAfterPO orderAfterPO, OrderProductPO orderProductPO,
                                   String title, OrderReturnApplyCallBackVO flowCallBackVO, String remark) {
        log.info("退款审核通知申请人工号{} afsSn {}", flowCallBackVO.getJobNumber(), orderAfterPO.getAfsSn());
        if (StringUtils.isNotEmpty(flowCallBackVO.getJobNumber())) {
            BatchBizMessageTemplateReq req = dingTalkMessageTemplateBuilder.buildRefundRefuseNoticeDingTalkMessage(title,
                    orderAfterPO, orderProductPO, Collections.singletonList(orderExtendPO.getManager()), "售后审批不通过",
                    String.format("%s %s", flowCallBackVO.getOperatorName(), flowCallBackVO.getOperatorMobile()), remark);
            log.info("退款审核发送钉钉消息入参{}", JSONObject.toJSONString(req));
            templateMessageFacade.bizTemplateBatchSend(req);
        }
    }

    /**
     * 退款成功，发送钉钉消息给申请人
     */
    private void agreeApplyNotify(OrderAfterPO orderAfterPO, OrderProductPO orderProductPO, String title, OrderReturnPO orderReturnPO,
                                  String jobNumber, String noticePhone) {
        log.info("退款审核通过通知 afsSn {}, jobNumber {}", noticePhone, jobNumber);
        if (StringUtils.isNotEmpty(jobNumber)) {
            // 从bms查询用户信息
            UserListVO employee = bmsIntegration.getEmployeesByAccount(noticePhone);
            BatchBizMessageTemplateReq messageTemplateReq = dingTalkMessageTemplateBuilder
                    .buildRefundAgreeNoticeDingTalkMessage(title, jobNumber, orderAfterPO, orderProductPO, orderReturnPO, employee);
            log.info("退款审核发送钉钉消息入参{}", JSONObject.toJSONString(messageTemplateReq));
            templateMessageFacade.bizTemplateBatchSend(messageTemplateReq);
        }
    }

    /**
     * 售后用户发货通知
     */
    private void refundCustomerReturnGoodsNotify(OrderExtendPO orderExtendPO, OrderProductPO orderProductPO, OrderReturnPO orderReturnPO,
                                                 OrderAfterPO orderAfterPO, String jobNumber, String noticePhone) {
        log.info("用户售后退款通知 afsSn {}, jobNumber {}", orderAfterPO.getAfsSn(), jobNumber);
        UserListVO employee = bmsIntegration.getEmployeesByAccount(noticePhone);
        BatchBizMessageTemplateReq messageTemplateReq = dingTalkMessageTemplateBuilder.buildRefundCustomerReturnNoticeDingTalkMessage(
                "买家退货提醒", orderExtendPO, orderProductPO, orderReturnPO, orderAfterPO, jobNumber, employee);
        log.info("退款审核发送钉钉消息入参{}", JSONObject.toJSONString(messageTemplateReq));
        templateMessageFacade.bizTemplateBatchSend(messageTemplateReq);
    }

    /**
     * 钉钉退款流程，平台审批
     */
    private String dingTalkPlatformAuditRefund(OrderReturnApplyCallBackVO callBackVO, OrderAfterPO orderAfterPO, OrderPO orderPO,
                                               OrderReturnPO orderReturnPO, boolean isPass) {
        log.info("钉钉退款流程，dingTalkPlatformAuditRefund, 售后单信息 afsSn{}, state{}", orderReturnPO.getAfsSn(), orderReturnPO.getState());
        if (OrderReturnStatus.isFinish(orderReturnPO.getState())) {
            return WorkflowConst.CALL_BACK_RESULT_SUCCESS;
        }

        if (OrderReturnStatus.isEnd(orderReturnPO.getState())) {
            return WorkflowConst.CALL_BACK_RESULT_SUCCESS;
        }

        log.info("dingTalkPlatformAuditRefund 钉钉平台审批售后单{}", orderReturnPO.getAfsSn());

        // 构造审批人信息
        Admin admin = UserUtil.buildBaseAdmin(Integer.parseInt(callBackVO.getOperatorUserId()), callBackVO.getOperatorName(),
                callBackVO.getOperatorMobile());

        String lockKey = "AdminAfterSaleController:confirmRefund:" + orderAfterPO.getOrderSn();
        String result;
        try {
            log.info("钉钉平台审批退款单afsSn：{}", orderAfterPO.getAfsSn());
            result = lock.tryLockExecuteFunction(lockKey, 0, 30, TimeUnit.SECONDS,
                    () -> orderReturnModel.adminRefundOperation(admin, orderAfterPO.getAfsSn(), "平台钉钉审批",
                            "平台钉钉审批", isPass, orderPO.getChannel(), orderReturnPO.getRefundPunishAmount()));
        } catch (ZhnxServiceException ex) {
            throw new BusinessException("点击过快，稍等一下~");
        }

        if (!OrderConst.RESULT_CODE_SUCCESS.equals(result)) {
            return WorkflowConst.CALL_BACK_RESULT_SUCCESS;
        }

        //操作日志
        AdminLogSendVO adminLogSendVO = new AdminLogSendVO(admin.getAdminId(), admin.getAdminName(), request.getRequestURI(),
                String.format("确认退款[确认退款成功][售后单号字符串：%s]", orderAfterPO.getAfsSn()), WebUtil.getRealIp(request));
        try {
            rabbitTemplate.convertAndSend(MQ_EXCHANGE_NAME, MQ_QUEUE_NAME_ADMINLOG_MSG, adminLogSendVO);
        } catch (Exception e) {
            log.info("发消息异常捕获,{}", JSON.toJSONString(adminLogSendVO), e);
        }
        return WorkflowConst.CALL_BACK_RESULT_SUCCESS;
    }


    /**
     * 钉钉退款流程，商家审批
     */
    private String dingTalkStoreAuditRefund(OrderReturnApplyCallBackVO flowCallBackVO, OrderAfterPO orderAfterPO, OrderPO orderPO,
                                            OrderReturnPO orderReturnPO, boolean isPass) {
        if (orderReturnPO.getState() >= OrderReturnStatus.STORE_AGREE_REFUND.getValue()){
            return WorkflowConst.CALL_BACK_RESULT_SUCCESS;
        }
        log.info("dingTalkStoreAuditRefund 钉钉商家审批售后单{}", orderReturnPO.getAfsSn());
        //根据处理人id获取商户信息
        Vendor vendor = vendorFeignClient.getVendorByStoreId(orderPO.getStoreId());
        Store store = storeFeignClient.getStoreByStoreId(orderPO.getStoreId());
        store.setStoreName(flowCallBackVO.getOperatorName());
        vendor.setVendorMobile(flowCallBackVO.getOperatorMobile());
        vendor.setStore(store);

        Integer storeReceiveId;
        if (ReturnTypeEnum.RETURN_AND_REFUND.getValue().equals(orderReturnPO.getReturnType())) {
            StoreAddressExample storeAddressExample = new StoreAddressExample();
            storeAddressExample.setStoreId(orderPO.getStoreId());
            storeAddressExample.setIsDefault(1);
            List<StoreAddress> storeAddressList = storeAddressFeignClient.getStoreAddressList(storeAddressExample);
            if (CollectionUtils.isEmpty(storeAddressList)) {
                log.warn("售后单{},获取商家默认之地址为空", orderAfterPO.getAfsSn());
                return WorkflowConst.CALL_BACK_RESULT_SUCCESS;
            }
            storeReceiveId = storeAddressList.get(0).getAddressId();
        } else {
            storeReceiveId = 0;
        }

        log.info("dingTalkStoreAuditRefund 钉钉审批售后单{} vendor信息:", JSON.toJSONString(vendor));
        String lockKey = String.format("SellerAfterSaleController:audit:%s", flowCallBackVO.getBizId());

        OperationRoleEnum operationRole = (RefundDingTalkAuditRoleEnum.DIRECTOR_AUDIT.getRoleCode().equals(flowCallBackVO.getNodeKey())
                || RefundDingTalkAuditRoleEnum.SELF_POINT_RECEIVE_GOODS.getRoleCode().equals(flowCallBackVO.getNodeKey()))
                ? OperationRoleEnum.DIRECTOR : OperationRoleEnum.STORE;

        String remark;
        if (operationRole == OperationRoleEnum.DIRECTOR) {
            remark = isPass ? "主任审批通过" : "主任审批拒绝";
        } else {
            remark = isPass ? "商家钉钉审核通过" : "商家钉钉审核拒绝";
        }

        String result;
        if (RefundDingTalkAuditRoleEnum.DIRECTOR_AUDIT.getRoleCode().equals(flowCallBackVO.getNodeKey())
                && OrderReturnStatus.WAIT_RETURN.getValue().equals(orderReturnPO.getState())) {
            result = lock.tryLockExecuteFunction(lockKey, 0, 10, TimeUnit.SECONDS,
                    () -> orderReturnModel.afsStoreReceive(vendor, orderAfterPO.getAfsSn(), isPass, remark,
                            orderPO.getChannel(), 0, orderReturnPO.getRefundPunishAmount(), operationRole, RefundHandleSourceEnum.DING_TALK.getCode()));
        } else {
            result = lock.tryLockExecuteFunction(lockKey, 0, 10, TimeUnit.SECONDS,
                    () -> orderReturnModel.afsStoreAudit(vendor, orderAfterPO.getAfsSn(), isPass, remark, storeReceiveId,
                            orderPO.getChannel(), 0, orderReturnPO.getRefundPunishAmount(), operationRole,
                            RefundHandleSourceEnum.DING_TALK.getCode()));
        }

        if (!OrderConst.RESULT_CODE_SUCCESS.equals(result)) {
            log.warn("售后审批钉钉商家审核售后单{}失败{},result:{}", orderAfterPO.getAfsSn(), JSONObject.toJSONString(flowCallBackVO),result);
            //流程必须返回success，否则一直回调
            return WorkflowConst.CALL_BACK_RESULT_SUCCESS;
        }
        //操作行为
        String opt = "审核退款申请[审核退款成功][售后单号：" + flowCallBackVO.getBizId() + "]";
        VendorLogSendVO vendorLogSendVO = new VendorLogSendVO(vendor.getVendorId(), vendor.getVendorName(),
                request.getRequestURI(), opt, WebUtil.getRealIp(request));
        try {
            rabbitTemplate.convertAndSend(MQ_EXCHANGE_NAME, MQ_QUEUE_NAME_SELLERLOG_MSG, vendorLogSendVO);
        } catch (Exception e) {
            log.info("发消息异常捕获,{}", JSON.toJSONString(vendorLogSendVO), e);
        }
        return WorkflowConst.CALL_BACK_RESULT_SUCCESS;
    }

    /**
     * 钉钉退款流程，商家收货
     */
    private String dingTalkRefundStoreReceiveGoods(OrderReturnApplyCallBackVO orderReturnApplyCallBackVO, OrderAfterPO orderAfterPO, OrderPO orderPO,
                                                   OrderReturnPO orderReturnPO, boolean isPass) {
        if (orderReturnPO.getState() >= OrderReturnStatus.STORE_AGREE_REFUND.getValue()){
            return WorkflowConst.CALL_BACK_RESULT_SUCCESS;
        }
        log.info("dingTalkRefundStoreReceiveGoods 钉钉商家审批售后单{}", orderReturnPO.getAfsSn());
        //根据处理人id获取商户信息
        Vendor vendor = vendorFeignClient.getVendorByStoreId(orderPO.getStoreId());
        Store store = storeFeignClient.getStoreByStoreId(orderPO.getStoreId());
        store.setStoreName(orderReturnApplyCallBackVO.getOperatorName());
        vendor.setVendorMobile(orderReturnApplyCallBackVO.getOperatorMobile());
        vendor.setStore(store);

        log.info("dingTalkRefundStoreReceiveGoods 钉钉审批售后单{} vendor信息:", JSON.toJSONString(vendor));
        String lockKey = String.format("dingTalkRefundStoreReceiveGoods:audit:%s", orderReturnApplyCallBackVO.getBizId());
        String result = lock.tryLockExecuteFunction(lockKey, 0, 10, TimeUnit.SECONDS,
                () -> orderReturnModel.afsStoreReceive(vendor, orderAfterPO.getAfsSn(), isPass,
                        "商家钉钉审核", orderPO.getChannel(),
                        0, orderReturnPO.getRefundPunishAmount(), OperationRoleEnum.STORE, RefundHandleSourceEnum.DING_TALK.getCode()));
        if (!OrderConst.RESULT_CODE_SUCCESS.equals(result)) {
            log.warn("售后审批钉钉商家审核售后单{}失败{},result:{}", orderAfterPO.getAfsSn(), JSONObject.toJSONString(orderReturnApplyCallBackVO),result);
            //流程必须返回success，否则一直回调
            return WorkflowConst.CALL_BACK_RESULT_SUCCESS;
        }
        //操作行为
        String opt = "审核退款申请[审核退款成功][售后单号：" + orderReturnApplyCallBackVO.getBizId() + "]";
        VendorLogSendVO vendorLogSendVO = new VendorLogSendVO(vendor.getVendorId(), vendor.getVendorName(),
                request.getRequestURI(), opt, WebUtil.getRealIp(request));
        try {
            rabbitTemplate.convertAndSend(MQ_EXCHANGE_NAME, MQ_QUEUE_NAME_SELLERLOG_MSG, vendorLogSendVO);
        } catch (Exception e) {
            log.info("发消息异常捕获,{}", JSON.toJSONString(vendorLogSendVO), e);
        }
        return WorkflowConst.CALL_BACK_RESULT_SUCCESS;
    }

    @Override
    public boolean directorAuditRefund(String afsSn, String jobNumber, boolean isPass) {
        OrderReturnPO orderReturnPO = orderReturnService.getByAfsSn(afsSn);
        if (Objects.isNull(orderReturnPO)) {
            log.warn("DingTalkFlowServiceImpl directorAuditRefund 售后单不存在: {}", afsSn);
            return false;
        }

        if (!OrderReturnStatus.WAIT_SELF_PICKUP_POINT_RECEIVED.getValue().equals(orderReturnPO.getState())) {
            log.warn("DingTalkFlowServiceImpl directorAuditRefund 售后单状态不正确, state: {}, afsSn {}", orderReturnPO.getState(), afsSn);
            return false;
        }

        // 从 redis 中获取流程id
        String key = "order_return_apply_biz_id_" + orderReturnPO.getAfsSn();
        String processId = stringRedisTemplate.opsForValue().get(key);

        log.info("DingTalkFlowServiceImpl directorAuditRefund, afsSn {}, jobNumber {}, isPass {}, processId {}",
                afsSn, jobNumber, isPass, processId);

        if (StringUtils.isBlank(processId)) {
            log.warn("DingTalkFlowServiceImpl directorAuditRefund 流程id为空, afsSn: {} 流程id:{}", afsSn, processId);
            return false;
        }

        if (isPass) {
            flowUtil.auditPass(processId, "审核通过", jobNumber);
        } else {
            flowUtil.auditReject(jobNumber, processId, "审核不通过");
        }

        return true;
    }




}
