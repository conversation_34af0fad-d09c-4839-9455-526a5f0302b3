package com.cfpamf.ms.mallorder.scheduler;

import com.cfpamf.ms.mallorder.model.ComplainModel;
import com.slodon.bbc.core.util.AssertUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 定时器定时处理超过3天的交易投诉交由商家申诉
 * <p>
 * cron: 0 0 5 * * ?
 */
@Component
@Slf4j
public class StoreComplaintJob {

    @Autowired
    private ComplainModel complainModel;

    @XxlJob(value = "StoreComplaintJob")
    public ReturnT<String> execute(String s) {

        long startTime = System.currentTimeMillis();
        XxlJobHelper.log("start job at local time: {0}", LocalDateTime.now());

        log.info("jobSystemCancelOrder() start");
        try {
            boolean jobResult = complainModel.jobSubmitStoreComplaint();
            AssertUtil.isTrue(!jobResult, "[jobSubmitStoreComplaint] 定时任务默认3天后交由商家申诉时失败");
        } catch (Exception e) {
            log.error("jobSubmitStoreComplaint()", e);
        }

        XxlJobHelper.log("finish job at local time: {0}  cost:{1}",
                LocalDateTime.now(),
                (System.currentTimeMillis() - startTime) / 1000);

        return new ReturnT("SUCCESS");
    }
}
