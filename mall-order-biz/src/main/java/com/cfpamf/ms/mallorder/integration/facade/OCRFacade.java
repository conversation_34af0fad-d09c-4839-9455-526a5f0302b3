package com.cfpamf.ms.mallorder.integration.facade;

import com.cfpamf.ms.mallorder.integration.facade.dto.OCRWordResult;
import com.cfpamf.ms.mallorder.integration.facade.dto.OcrResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * ocr外部接口
 *
 * <AUTHOR>
 * @since 2024/10/22
 */
@FeignClient(name = "third-gateway", url = "${third-gateway.url:third-gateway.tsg.cfpamf.com}")
public interface OCRFacade {

    @PostMapping("/api/ocr/general-basic")
    OcrResult<OCRWordResult> normalWordCheck(@RequestParam("fileUrl")String fileUrl, @RequestParam("mock")String mock, @RequestParam("requestId")String requestId, @RequestParam("consumer")String consumer);

}
