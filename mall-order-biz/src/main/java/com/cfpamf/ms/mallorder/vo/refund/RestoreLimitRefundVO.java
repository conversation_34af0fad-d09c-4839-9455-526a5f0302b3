package com.cfpamf.ms.mallorder.vo.refund;

import com.slodon.bbc.core.util.FileUrlUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 恢复额度售后单信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RestoreLimitRefundVO {

    /**
     * 售后单号
     */
    private String afsSn;

    /**
     * 售后单状态
     */
    private Integer state;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品图片
     */
    private String productImage;

    /**
     * 售后数量
     */
    private Integer returnNum;

    public String getProductImage() {
        return FileUrlUtil.getFileUrl(this.productImage, null);
    }

}
