package com.cfpamf.ms.mallorder.controller.fegin.facade.loan;


import com.cfpamf.ms.mallorder.dto.loan.WechatResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;


@FeignClient(
        name = "scrm-service",
        url = "${scrm-service.url}"
)
public interface ScrmFacade {
    /**
     * 分支照片查询
     * @param branchCode 分支编码
     * @param jobNumber 员工公号
     * @return
     */
    @GetMapping({"/scrm/feign/user/fromAgricultureSocialMedia"})
    WechatResult<Boolean> getJobNumberFile(@RequestParam("mobile") String mobile);


    @ApiOperation(value = "是否客户来源为新媒体")
    @GetMapping("/scrm/feign/user/fromSocialMedia")
    WechatResult<Boolean> fromSocialMedia(@RequestParam(value = "mobile") String mobile);
}
