package com.cfpamf.ms.mallorder.controller.fegin.facade.workflow.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class TaskTrackQueryDTO {
    @ApiModelProperty("系统编号ID")
    private Integer systemId = 38;
    @ApiModelProperty("流程定义的key")
    @NotNull(message = "流程定义的key不能为空")
    private String procDefKey;
    @ApiModelProperty("业务ID")
    @NotNull(message = "业务ID不能为空")
    private String bizId;
    @ApiModelProperty("当前页，默认1")
    private Integer pageNumber;
    @ApiModelProperty("分页size，默认10")
    private Integer pageSize;

    public TaskTrackQueryDTO(Integer systemId, String procDefKey, String bizId, Integer pageNumber, Integer pageSize) {
        this.systemId = systemId;
        this.procDefKey = procDefKey;
        this.bizId = bizId;
        this.pageNumber = pageNumber == null ? 1 : pageNumber;
        this.pageSize = pageSize == null ? 10 : pageSize;
    }
}
