package com.cfpamf.ms.mallorder.controller.fegin.facade;


import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.mallorder.common.aspect.ThreadLocalRemoveTag;
import com.cfpamf.ms.mallorder.common.enums.OrderCreateChannel;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.cfpamf.ms.mallorder.common.help.ResultUtils;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.*;
import com.cfpamf.ms.mallorder.enums.OrderPatternEnum;
import com.cfpamf.ms.mallorder.enums.OrderPerformanceModeEnum;
import com.cfpamf.ms.mallorder.integration.facade.MallExternalAdapterFacade;
import com.cfpamf.ms.mallorder.model.OrderExtendModel;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.model.OrderProductModel;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.request.OrderExample;
import com.cfpamf.ms.mallorder.request.OrderProductExample;
import com.cfpamf.ms.mallorder.request.req.ErpOrderDeliveryRequest;
import com.cfpamf.ms.mallorder.request.req.StoreExistOrderReq;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.vo.*;
import com.cfpamf.ms.mallorder.vo.hrmsVO.OutBranchLifeAndInteriorInfoVO;
import com.cfpamf.ms.mallorder.vo.kingdee.KingdeeCustomerVo;
import com.cfpamf.ms.mallpromotion.constant.PromotionConst;
import com.cfpamf.ms.mallpromotion.enums.CouponFunder;
import com.google.api.client.util.Lists;
import com.google.common.collect.Maps;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.PageVO;
import com.slodon.bbc.core.response.PagerInfo;
import com.slodon.bbc.core.response.SldResponse;
import com.slodon.bbc.core.util.AssertUtil;
import com.slodon.bbc.core.util.FileUrlUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@RestController
@Slf4j
@Api(tags = "OrderFeign")
public class OrderFeign {

    @Resource
    private OrderModel orderModel;
    @Resource
    private OrderProductModel orderProductModel;
    @Resource
    private IOrderService orderService;
    @Resource
    private IOrderInfoService orderInfoService;
    @Resource
    private IOrderReturnService orderReturnService;
    @Resource
    private IOrderPayService orderPayService;
    @Resource
    private OrderExtendModel orderExtendModel;
    @Resource
    private MallExternalAdapterFacade mallExternalAdapterFacade;
    @GetMapping("/v1/feign/order/test")
    public String test(@RequestParam("time") Long time) {
        KingdeeCustomerQuery query = new KingdeeCustomerQuery();
        query.setOrgNumber("01.19");
        JsonResult<Page<KingdeeCustomerVo>> customerPage = mallExternalAdapterFacade.getCustomerPage(query);
        log.info("kingdeeCustomerVoPage:{}", customerPage);
        return "success";
    }

    @Resource
    private IOrderSellerService orderSellerService;

    @Resource
    private IPerformanceService performanceService;

    @Autowired
    private ITransactionLogService transactionLogService;

    @Autowired
    private IOrderProductExtendService orderProductExtendService;

    @Autowired
    private IOrderPromotionDetailService orderPromotionDetailService;

    @Autowired
    private IOrderPlacingService orderPlacingService;

    /**
     * 根据orderSn获取订单详情
     *
     * @param orderSn orderSn
     * @return
     */
    @GetMapping("/v1/feign/business/order/get")
    public OrderPO getOrderByOrderSn(@RequestParam("orderSn") String orderSn) {
        OrderExample example = new OrderExample();
        example.setOrderSn(orderSn);
        List<OrderPO> orderPOList = orderModel.getOrderList(example, null);
        AssertUtil.notEmpty(orderPOList, new BusinessException("订单不存在"));
        return orderPOList.get(0);
    }

    /**
     * 根据orderSn获取订单详情
     *
     * @param orderSn orderSn
     * @return JsonResult
     */
    @GetMapping("/v1/feign/business/order/get/js")
    public JsonResult<OrderPO> getOrderByOrderSnJs(@RequestParam("orderSn") String orderSn) {
        return SldResponse.success(getOrderByOrderSn(orderSn));
    }

    /**
     * 根据店铺id集合获取 近期存在订单的店铺id集合
     *
     * @param storeExistOrderReq
     * @return
     */
    @PostMapping("/v1/feign/business/order/store/existOrder")
    public List<Long> existOrderStoreIdByStoreId(@RequestBody@Valid StoreExistOrderReq storeExistOrderReq) {
        if(CollectionUtils.isEmpty(storeExistOrderReq.getStoreIdList())){
            return Lists.newArrayList();
        }
        return orderModel.existOrderStoreIdByStoreId(storeExistOrderReq);
    }

    @GetMapping("/v1/feign/business/order/get/V2")
    public Order getOrderByOrderSnV2(@RequestParam("orderSn") String orderSn) {
        OrderExample example = new OrderExample();
        example.setOrderSn(orderSn);
        List<OrderPO> orderPOList = orderModel.getOrderList(example, null);
        AssertUtil.notEmpty(orderPOList, new BusinessException("订单不存在"));
        OrderPO orderPO = orderPOList.get(0);
        Order order = new Order();
        BeanUtils.copyProperties(orderPO, order);

        order.setOrderBizPattern(orderModel.isToBOrder(orderPO) ? OrderPatternEnum.PURCHASE_CENTRE.getValue()
                : OrderPatternEnum.SHOP_STREET.getValue());
        //获取商品信息
        OrderProductExample orderProductExample = new OrderProductExample();
        orderProductExample.setOrderSn(example.getOrderSn());
        List<OrderProductPO> orderProductPOList = orderProductModel.getOrderProductList(orderProductExample, null);

        List<OrderProduct> productList = new ArrayList<>();
        orderProductPOList.forEach(x -> {
            OrderProduct orderProduct = new OrderProduct();
            BeanUtils.copyProperties(x, orderProduct);
            orderProduct.setDeliveryState(x.getDeliveryState().getValue());
            productList.add(orderProduct);
        });

        OrderExtendPO orderExtendPO = orderExtendModel.getOrderExtendByOrderSn(order.getOrderSn());
        OrderExtend orderExtend = new OrderExtend();
        BeanUtils.copyProperties(orderExtendPO, orderExtend);
        order.setOrderExtend(orderExtend);

        order.setOrderProductList(productList);

        // 订单货品拓展表查询
        List<OrderProductExtendPO> orderProductExtendPOList = orderProductExtendService.getOrderProductExtendPOList(orderSn);
        List<OrderProductExtend> opeList = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(orderProductExtendPOList)){
            opeList = orderProductExtendPOList.stream().map(po -> {
                OrderProductExtend ope = new OrderProductExtend();
                BeanUtils.copyProperties(po, ope);
                return ope;
            }).collect(Collectors.toList());
        }
        order.setOrderProductExtendList(opeList);
        // 订单优惠详情查询
        List<OrderPromotionDetailPO> promotionDetailList = orderPromotionDetailService.getOrderPromotionDetailByOrderSn(orderSn);
        Map<String,BigDecimal> reatilMap = Maps.newHashMap();
        Map<String,BigDecimal> discountMap = Maps.newHashMap();
        List<OrderPromotionDetail> detailList = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(promotionDetailList)){
            detailList = promotionDetailList.stream().map(po -> {
                OrderPromotionDetail detail = new OrderPromotionDetail();
                BeanUtils.copyProperties(po, detail);
                return detail;
            }).collect(Collectors.toList());
            // 根据出资方的不同，计算总计的出售价
            for (OrderPromotionDetailPO detailPo : promotionDetailList) {
                if (detailPo.getPromotionType().equals(PromotionConst.PROMOTION_TYPE_402)){
                    String funder = detailPo.getFunder();
                    CouponFunder couponFunder = CouponFunder.getByCode(funder);
                    if (Objects.nonNull(couponFunder)){
                        BigDecimal retailPrice = detailPo.getRetailPrice();
                        if (Objects.nonNull(retailPrice)){
                            reatilMap.put(funder,reatilMap.getOrDefault(funder,BigDecimal.ZERO).add(retailPrice));
                        }
                        BigDecimal promotionAmount = detailPo.getPromotionAmount();
                        if (Objects.nonNull(promotionAmount)){
                            discountMap.put(funder,discountMap.getOrDefault(funder,BigDecimal.ZERO).add(promotionAmount));
                        }
                    }else if (StringUtils.isNotBlank(funder)){
                        // 出资方有值，且未匹配到对应的枚举
                        log.warn("getOrderByOrderSnV2 出资方未在枚举中定义!,orderSn:{}",orderSn);
                    }
                }
            }
        }
        order.setOrderPromotionDetailList(detailList);
        order.setRetailMap(reatilMap);
        order.setDiscountMap(discountMap);
        return order;
    }

    /**
     * 根据外部业务单号查询订单号
     *
     * @param outBizSource
     * @param outBizId
     * @return
     */
    @GetMapping("/v1/feign/business/order/listOrderSnByOutBizId")
    public JsonResult<List<String>> listOrderSnByOutBizId(@NotBlank(message = "outBizSource 不能为空") @RequestParam("outBizSource") String outBizSource,
                                                          @NotBlank(message = "outBizId 不能为空") @RequestParam("outBizId") String outBizId) {

        LambdaQueryWrapper<OrderPayPO> orderPayQueryWrapper = new LambdaQueryWrapper<>();
        orderPayQueryWrapper.eq(OrderPayPO::getOutBizSource, outBizSource);
        orderPayQueryWrapper.eq(OrderPayPO::getOutBizId, outBizId);
        orderPayQueryWrapper.last("limit 1");
        OrderPayPO orderPayPO = orderPayService.getOne(orderPayQueryWrapper);
        if (Objects.isNull(orderPayPO)){
            return SldResponse.success(null);
        }

        LambdaQueryWrapper<OrderPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.select(OrderPO::getOrderSn);
        queryWrapper.eq(OrderPO::getPaySn, orderPayPO.getPaySn());
        List<OrderPO> orderPOList = orderService.list(queryWrapper);
        if (CollectionUtil.isEmpty(orderPOList)){
            return SldResponse.success(null);
        }

        List<String> orderSns = orderPOList.stream().map(OrderPO::getOrderSn).collect(Collectors.toList());
        return SldResponse.success(orderSns);
    }

    /**
     * 根据订单号查询外部业务单号
     *
     * @param orderSn
     * @return
     */
    @GetMapping("/v1/feign/business/order/queryOutBizIdByOrderSn")
    public JsonResult<OrderPayOuterBizIdVO> queryOutBizIdByOrderSn(@NotBlank(message = "orderSn 不能为空") @RequestParam("orderSn") String orderSn) {

        LambdaQueryWrapper<OrderPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.select(OrderPO::getPaySn);
        queryWrapper.eq(OrderPO::getOrderSn, orderSn);
        queryWrapper.last("limit 1");
        OrderPO orderPO = orderService.getOne(queryWrapper);
        if (Objects.isNull(orderPO)){
            return SldResponse.success(null);
        }

        LambdaQueryWrapper<OrderPayPO> orderPayQueryWrapper = new LambdaQueryWrapper<>();
        orderPayQueryWrapper.select(OrderPayPO::getOutBizId);
        orderPayQueryWrapper.select(OrderPayPO::getOutBizSource);

        orderPayQueryWrapper.eq(OrderPayPO::getPaySn, orderPO.getPaySn());
        orderPayQueryWrapper.last("limit 1");
        OrderPayPO orderPayPO = orderPayService.getOne(orderPayQueryWrapper);
        if (Objects.isNull(orderPayPO)){
            return SldResponse.success(null);
        }
        OrderPayOuterBizIdVO vo = new OrderPayOuterBizIdVO();
        BeanUtils.copyProperties(orderPayPO,vo);
        return SldResponse.success(vo);
    }

    /**
     * 获取条件获取订单列表
     *
     * @param example 查询条件信息
     * @return
     */
    @PostMapping("/v1/feign/business/order/getList")
    public List<OrderPO> getOrderList(@RequestBody OrderExample example) {
        return orderModel.getOrderList(example, example.getPager());
    }

    @PostMapping("/v1/feign/business/order/getList/V2")
    public List<Order> getOrderListV2(@RequestBody OrderExample example) {
        List<OrderPO> orderPOList = orderModel.getOrderList(example, example.getPager());
        List<Order> orderList = new ArrayList<>();
        List<String> orderSnList = orderPOList.stream().map(OrderPO::getOrderSn).collect(Collectors.toList());
        if(CollectionUtil.isEmpty(orderSnList)){
            return new ArrayList<>();
        }
        //获取商品信息
        OrderProductExample orderProductExample = new OrderProductExample();
        orderProductExample.setOrderSnIn(orderSnList);
        List<OrderProductPO> orderProductPOList = orderProductModel.getOrderProductList(orderProductExample, null);
        Map<String, List<OrderProductPO>> orderProductMap = orderProductPOList.stream().collect(Collectors.groupingBy(OrderProductPO::getOrderSn));
        orderPOList.forEach(x -> {
            Order order = new Order();
            List<OrderProduct> productList = new ArrayList<>();
            List<OrderProductPO> productPOList = orderProductMap.get(x.getOrderSn());
            BeanUtils.copyProperties(x, order);
            productPOList.forEach(y -> {
                OrderProduct orderProduct = new OrderProduct();
                BeanUtils.copyProperties(y, orderProduct);
                productList.add(orderProduct);
            });

            //到家服务的订单需要查询订单扩展表中的信息
            if (StringUtils.isNotEmpty(x.getPerformanceModes()) && x.getPerformanceModes().contains(OrderPerformanceModeEnum.PERFORMANCE_MODE_HOME_SERVICE.getValue().toString())) {
                OrderExtendPO orderExtendPO = orderExtendModel.getOrderExtendByOrderSn(x.getOrderSn());
                OrderExtend orderExtend = new OrderExtend();
                BeanUtils.copyProperties(orderExtendPO, orderExtend);
                order.setOrderExtend(orderExtend);
            }
            order.setOrderProductList(productList);
            orderList.add(order);
        });


        return orderList;
    }

    @PostMapping("/v1/feign/business/order/getList/V4")
    public PageVO<Order> getOrderListV4(@RequestBody OrderExample example){
        //获取分页信息，这个接口是分页接口，分页肯定不能为空
        PagerInfo pager = example.getPager();
        //做分页兜底
        if(pager == null){
            pager = new PagerInfo();
        }
        //清除分页信息
        example.setPager(null);
        //拿到该客户名下所有的订单
        List<Order> orderList = getOrderListV2(example);
        //如果查询商品的字段不为空,则进行筛选
        List<Order> filterOrder = new ArrayList<>();
        if(StringUtils.isNotEmpty(example.getGoodsNameLike())){
            for(Order order : orderList){
                Boolean isContain = false;
                if(order.getStoreName().contains(example.getGoodsNameLike())){
                    isContain = true;
                }
                if(CollectionUtil.isNotEmpty(order.getOrderProductList()) && !isContain){
                    for(OrderProduct orderProduct : order.getOrderProductList()){
                        if(StringUtils.isNotEmpty(orderProduct.getGoodsName()) && orderProduct.getGoodsName().contains(example.getGoodsNameLike())){
                            isContain = true;
                            break;
                        }
                    }
                }
                if(isContain){
                    filterOrder.add(order);
                }
            }
        }else{
            filterOrder = orderList;
        }

        //进行手动分页
        List<Order> pageOrder = pageOrder(filterOrder,pager);

        //添加 url
        addOrderUrl(pageOrder);

        //这个接口 page 肯定不能为空,不需要判断
        if(!CollectionUtil.isEmpty(filterOrder)){
            pager.setRowsCount(filterOrder.size());
        }else{
            pager.setRowsCount(0);
        }
        return new PageVO<>(pageOrder,pager);
    }

    private List<Order> pageOrder(List<Order> filterOrder,PagerInfo pager){
        if(CollectionUtil.isEmpty(filterOrder)){
            return null;
        }
        if(pager == null){
            return filterOrder;
        }
        int startIndex =  pager.getPageSize() * (pager.getPageIndex() - 1);
        //如果起始位置大于了总长度，返回 null
        if(startIndex + 1 > filterOrder.size()){
            return null;
        }
        //结束位置取总长度和页码数据结束位置的比较小的值
        int endIndex =  Math.min(startIndex + pager.getPageSize(),filterOrder.size());

        return filterOrder.subList(startIndex,endIndex);
    }

    private void addOrderUrl(List<Order> orderList){
        if(CollectionUtil.isEmpty(orderList)){
            return;
        }
        orderList.stream().forEach(order->{
            if(!CollectionUtil.isEmpty(order.getOrderProductList())){
                order.getOrderProductList().stream().forEach(temp->{
                    if(!StringUtils.isBlank(temp.getProductImage())){
                        temp.setProductImage(FileUrlUtil.getFileUrl(temp.getProductImage(), null));
                    }
                });
            }
        });
    }

    /**
     * 根据订单状态统计数量
     *
     * @return
     */
    @GetMapping("/v1/feign/business/order/countByStatus")
    @ApiOperation("根据订单状态统计数量")
    public OrderCountVo countByStatus(@RequestParam("memberId") Integer memberId) {
        return orderModel.countByStatus(memberId);
    }

    /**
     * 根据订单状态统计数量
     *
     * @return
     */
    @GetMapping("/v1/feign/business/order/countStatusByStationMaster")
    @ApiOperation("根据订单状态统计数量")
    public OrderCountVo countStatusByStationMaster(@RequestParam("masterStation") String stationMater) {
        return orderModel.countStatusByStationMaster(stationMater);
    }

    /**
     * 获取条件获取订单数量
     *
     * @param example 查询条件信息
     * @return
     */
    @PostMapping("/v1/feign/business/order/getCount")
    public Integer getOrderCount(@RequestBody OrderExample example) {
        return orderModel.getOrderCount(example);
    }

    /**
     * 新增订单
     *
     * @param orderPO
     * @return
     */
    @PostMapping("/v1/feign/business/order/addOrder")
    public Integer saveOrder(@RequestBody OrderPO orderPO) {
        return orderModel.saveOrder(orderPO);
    }

    /**
     * 根据orderSn更新订单
     *
     * @param orderPO
     * @return
     */
    @PostMapping("/v1/feign/business/order/updateOrder")
    public Integer updateOrder(@RequestBody OrderPO orderPO) {
        return orderModel.updateOrder(orderPO);
    }

    /**
     * 根据orderSn更新订单分销金额
     *
     * @param orderSn
     * @return
     */
    @PostMapping("/v1/feign/business/order/updateCommissionFee")
    public JsonResult<Boolean> updateCommissionFee4Order(@RequestParam("orderSn") String orderSn) {
        if (!orderService.updateCommissionFee4Order(orderSn)) {
            return SldResponse.fail(String.format("更新订单的分销佣金失败,orderSn:%s", orderSn));
        }
        return SldResponse.success(true);
    }

    /**
     * 根据orderSn更新订单分销金额
     *
     * @param orderSn
     * @return
     */
    @PostMapping("/v1/feign/business/order/updateCommissionFeeV3")
    public JsonResult<Boolean> updateCommissionFeeV3(@RequestParam("orderSn") String orderSn) {
        if (!orderService.updateCommissionFeeV3(orderSn)) {
            return SldResponse.fail(String.format("更新订单的分销佣金失败,orderSn:%s", orderSn));
        }
        return SldResponse.success(true);
    }

    /**
     * 根据orderId删除订单
     *
     * @param orderId orderId
     * @return
     */
    @PostMapping("/v1/feign/business/order/deleteOrder")
    public Integer deleteOrder(@RequestParam("orderId") Integer orderId) {
        return orderModel.deleteOrder(orderId);
    }

    /**
     * 系统自动完成订单
     *
     * @return
     */
    @GetMapping("/v1/feign/business/order/jobSystemFinishOrder")
    public boolean jobSystemFinishOrder() {
        return orderModel.jobSystemFinishOrder();
    }

    /**
     * 系统自动取消24小时没有付款订单
     *
     * @return
     */
    @GetMapping("/v1/feign/business/order/jobSystemCancelOrder")
    public boolean jobSystemCancelOrder() {
        return orderModel.jobSystemCancelOrder();
    }

    /**
     * 系统自动取消没有付款的秒杀订单
     *
     * @return
     */
    @GetMapping("/v1/feign/business/order/jobSystemCancelSeckillOrder")
    public boolean jobSystemCancelSeckillOrder() {
        return orderModel.jobSystemCancelSeckillOrder();
    }

    /**
     * 系统自动取消没有付款的阶梯团订单
     *
     * @return
     */
    @GetMapping("/v1/feign/business/order/jobSystemCancelLadderGroupOrder")
    public boolean jobSystemCancelLadderGroupOrder() {
        return orderModel.jobSystemCancelLadderGroupOrder();
    }

    /**
     * 系统自动取消没有付款的阶梯团尾款订单
     *
     * @return
     */
    @GetMapping("/v1/feign/business/order/jobSystemCancelLadderGroupBalanceOrder")
    public boolean jobSystemCancelLadderGroupBalanceOrder() {
        return orderModel.jobSystemCancelLadderGroupBalanceOrder();
    }

    /**
     * 订单量统计
     *
     * @param example
     * @return
     */
    @PostMapping("/v1/feign/business/order/getOrderDayDto")
    public List<OrderDayDTO> getOrderDayDto(@RequestBody OrderExample example) {
        return orderModel.getOrderDayDto(example);
    }

    /**
     * 系统自动取消没有付款的预售订单
     *
     * @return
     */
    @GetMapping("/v1/feign/business/order/jobSystemCancelPreSellOrder")
    public boolean jobSystemCancelPreSellOrder() {
        return orderModel.jobSystemCancelPreSellOrder();
    }

    /**
     * 系统自动取消没有付款的预售尾款订单
     *
     * @return
     */
    @GetMapping("/v1/feign/business/order/jobSystemCancelPreSellBalanceOrder")
    public boolean jobSystemCancelPreSellBalanceOrder() {
        return orderModel.jobSystemCancelPreSellBalanceOrder();
    }

    /**
     * 系统自动取消没有付款的拼团订单
     *
     * @return
     */
    @GetMapping("/v1/feign/business/order/jobSystemCancelSpellOrder")
    public boolean jobSystemCancelSpellOrder() {
        return orderModel.jobSystemCancelSpellOrder();
    }

    /**
     * 准实时，由营销模块发起，活动结束时触发：处理现有拼团订单，包括成功的和失败的
     *
     * @return
     */
    @GetMapping("/v1/feign/business/order/jobSystemCancelSpellFailOrder")
    public boolean jobSystemCancelSpellFailOrder() {
        try {
            orderModel.handleFailSpellTeam();
        } catch (Exception e) {
            log.error("系统自动取消拼团失败异常", e);
            return false;
        }
        return true;
    }

    /**
     * 准实时，由营销模块发起，现在未调用：处理现有拼团订单，包括成功的和失败的
     *
     * @return
     */
    @GetMapping("/v1/feign/business/order/launchSpellRefund")
    public boolean jobSystemLaunchSpellRefund() {
        return orderModel.handleFailSpellTeam();
    }

    @PostMapping("/v1/feign/business/order/deliverForErp")
    public Result<Boolean> deliver4Erp(@RequestBody @NotNull @Valid ErpOrderDeliveryRequest deliveryReq) {

        return orderService.deliveryForErp(deliveryReq);
    }

    @GetMapping("/listProductsByTno")
    public Result<List<OrderProductsVO>> listProductsByTno(List<Long> tnos) {
        return ResultUtils.buildSuccessResult(orderProductModel.listProductsByTno(tnos));
    }

    @GetMapping("/baseDetail")
    public Result<MerchantBaseVo> getMerchantBaseInfo(@RequestParam("merchantId") @NotNull Long merchantId) {
        return ResultUtils.buildSuccessResult(orderProductModel.getMerchantBaseInfo(merchantId));
    }

    @GetMapping("/orderInfo")
    public Result<OrderProductsVO> orderInfo(@RequestParam("tno") @NotNull Long tno) {
        return ResultUtils.buildSuccessResult(orderProductModel.orderInfo(tno));
    }

    /**
     * 营销发起，手动模拟成团时调用：处理拼团成功订单，设为可发货
     *
     * @param orderSns
     * @return
     */
    @GetMapping("/v1/feign/business/order/setOrdersDeliverable")
    Result<Boolean> setOrdersDeliverable(@RequestParam("orderSns") List<String> orderSns) {
        return ResultUtils.buildSuccessResult(orderService.setOrdersDeliverable(orderSns));
    }

    /**
     * 分销查询订单信息
     *
     * @param orderSn 订单号
     * @return 订单信息
     */
    @GetMapping("/v1/feign/business/order/orderDetailInfoForDbc")
    public Result<OrderInfoForDbcVO> orderDetailInfoFordbc(@RequestParam("orderSn") String orderSn) {
        OrderInfoForDbcVO orderInfoForDbcVO;
        try {
            orderInfoForDbcVO = orderInfoService.orderDetailInfoFordbc(orderSn);
        } catch (Exception e) {
            log.error("分销查询订单异常，订单号：{}", orderSn, e);
            return ResultUtils.buildErrorResult(String.valueOf(ErrorCodeEnum.S.SYSTEM_EXCEPTION.getCode()), e.getMessage());
        }
        return ResultUtils.buildSuccessResult(orderInfoForDbcVO);
    }

    /**
     * 分销查询退单信息
     *
     * @param orderSn 订单号
     * @param afsSns  退款单集
     * @return 退款信息
     */
    @GetMapping("/v1/feign/business/order/orderAfterSaleDetailInfoForDbc")
    public Result<OrderReturnInfoForDbcVO> orderAfterSaleDetailInfoFordbc(@RequestParam("orderSn") String orderSn,
                                                                          @RequestParam("afsSns") List<String> afsSns) {
        OrderReturnInfoForDbcVO orderReturnVO;
        try {
            orderReturnVO = orderReturnService.orderAfterSaleDetailInfoFordbc(orderSn, afsSns);
        } catch (Exception e) {
            return ResultUtils.buildErrorResult(String.valueOf(ErrorCodeEnum.S.SYSTEM_EXCEPTION.getCode()), e.getMessage());
        }
        return ResultUtils.buildSuccessResult(orderReturnVO);
    }

    /**
     * 分销查询退单信息
     *
     * @param orderSn 订单号
     * @return 退款信息
     */
    @GetMapping("/v1/feign/business/order/orderAfterSaleDetailInfoForDbcWithOrderSn")
    public Result<OrderReturnInfoForDbcVO> orderAfterSaleDetailInfoForDbcWithOrderSn(@RequestParam("orderSn") String orderSn) {
        OrderReturnInfoForDbcVO orderReturnVO;
        try {
            orderReturnVO = orderReturnService.orderAfterSaleDetailInfoForDbcWithOrderSn(orderSn);
        } catch (Exception e) {
            return ResultUtils.buildErrorResult(String.valueOf(ErrorCodeEnum.S.SYSTEM_EXCEPTION.getCode()), e.getMessage());
        }
        return ResultUtils.buildSuccessResult(orderReturnVO);
    }

    /**
     * 查询订单信息
     *
     * @param orderSn 订单号
     * @return 订单信息
     */
    @GetMapping("/v1/feign/business/order/orderBriefInfo")
    public Result<OrderBriefInfoVO> orderBriefInfo(@RequestParam("orderSn") String orderSn) {
        return ResultUtils.buildSuccessResult(orderInfoService.orderBriefInfo(orderSn));
    }

    /**
     * 获取商家订单的有效总额
     *
     * @param storeId 商家ID
     * @return 有效订单总金额
     */
    @GetMapping("/v1/feign/business/order/getStoreValidOrderAmountSum")
    public Result<BigDecimal> getStoreValidOrderAmountSum(@RequestParam("storeId") String storeId) {
        return ResultUtils.buildSuccessResult(orderService.getStoreValidOrderAmountSum(storeId));
    }

    /**
     * 查询订单信息
     *
     * @param orderSn 订单号
     * @return 订单信息
     */
    @GetMapping("/v1/feign/business/order/getOrderDetailInfo")
    public Result<OrderInfoVO> getOrderDetailInfo(@RequestParam("orderSn") String orderSn) {
        OrderInfoVO orderDetailInfo;
        try {
            orderDetailInfo = orderInfoService.getOrderDetailInfo(orderSn);
        } catch (Exception e) {
            return ResultUtils.buildErrorResult(String.valueOf(ErrorCodeEnum.S.SYSTEM_EXCEPTION.getCode()), e.getMessage());
        }
        return ResultUtils.buildSuccessResult(orderDetailInfo);
    }

    /**
     * 发送订单签收码
     *
     * @param orderSn 订单号
     * @return 订单信息
     */
    @GetMapping("/v1/feign/business/order/getOrderReceiveCode")
    public Result<String> getOrderReceiveCode(@RequestParam("orderSn") String orderSn,@RequestParam("optUserName") String optUserName, @RequestParam("channel") OrderCreateChannel channel) {
        UserDTO userDTO = new UserDTO();
        userDTO.setUserId(OrderConst.OPT_USER_ID);
        userDTO.setUserName(optUserName);
        userDTO.setUserRole(OrderConst.LOG_ROLE);
        // 获取签收码
        String receiveCode = performanceService.sendSmsReceiveCodeAndInsertLog(orderSn, userDTO, channel);
        return ResultUtils.buildSuccessResult(receiveCode);
    }

    /**
     * 根据分支编码查询生服合伙人、分支内务工号
     *
     * @param branchCodeList 分支编码
     * @return 生服合伙人、分支内部工号信息
     */
    @PostMapping("/v1/feign/business/order/queryBranchLifeAndInteriorList")
    public Result<Map<String, OutBranchLifeAndInteriorInfoVO>> queryBranchLifeAndInteriorList(@RequestBody List<String> branchCodeList){
        Map<String, OutBranchLifeAndInteriorInfoVO> result = performanceService.queryBranchLifeAndInteriorList(branchCodeList);
        return ResultUtils.buildSuccessResult(result);
    }

    @ApiOperation("获取订单签收码商家白名单")
    @GetMapping("/v1/feign/business/order/getReceiveCodeStoreIdList")
    public Result<List<String>> getReceiveCodeStoreIdList() {
        return ResultUtils.buildSuccessResult(orderInfoService.getReceiveCodeStoreIdList());
    }


    /**
     * 查询退单信息
     *
     * @param afsSn 退款单号
     * @return 退款信息
     */
    @GetMapping("/v1/feign/business/order/getOrderReturnDetailInfo")
    public Result<OrderReturnInfoVO> getOrderReturnDetailInfo(@RequestParam("afsSn") String afsSn) {
        OrderReturnInfoVO orderReturnDetailInfo;
        try {
            orderReturnDetailInfo = orderReturnService.getOrderReturnDetailInfo(afsSn);
        } catch (Exception e) {
            return ResultUtils.buildErrorResult(String.valueOf(ErrorCodeEnum.S.SYSTEM_EXCEPTION.getCode()), e.getMessage());
        }
        return ResultUtils.buildSuccessResult(orderReturnDetailInfo);
    }

    /**
     * 提供theme-activity服务，全程喜宴订单查询接口
     * @param orderForThemeDTO orderForThemeDTO
     * @return OrderInfoForThemeVO
     */
    @PostMapping("/v1/feign/business/order/getOrderFilterBriefInfoForTheme")
    public JsonResult<PageVO<OrderInfoForThemeVO>> getOrderFilterBriefInfoForThemeActivity(@RequestBody @Valid OrderForThemeDTO orderForThemeDTO) {
        PageVO<OrderInfoForThemeVO> orderInfoForTheme;
        try {
            orderInfoForTheme = orderService.getOrderFilterBriefInfoForThemeActivity(orderForThemeDTO);
        } catch (Exception e) {
            return SldResponse.fail(String.valueOf(ErrorCodeEnum.S.SYSTEM_EXCEPTION.getCode()), e.getMessage());
        }
        return SldResponse.success(orderInfoForTheme);
    }

    /**
     * 提供theme-activity服务，全程喜宴订单查询接口
     * @param orderForThemeDTO orderForThemeDTO
     * @return OrderInfoForThemeVO
     */
    @PostMapping("/v1/feign/business/order/listOrderFilterBriefInfoForTheme")
    public JsonResult<List<OrderInfoForThemeVO>> listOrderFilterBriefInfoForTheme(@RequestBody @Valid OrderForThemeDTO orderForThemeDTO) {
        List<OrderInfoForThemeVO> orderInfoForTheme;
        try {
            orderInfoForTheme = orderService.listOrderFilterBriefInfoForThemeActivity(orderForThemeDTO);
        } catch (Exception e) {
            return SldResponse.fail(String.valueOf(ErrorCodeEnum.S.SYSTEM_EXCEPTION.getCode()), e.getMessage());
        }
        return SldResponse.success(orderInfoForTheme);
    }

    /**
     * 查询订单实体的映射信息
     *
     * @param orderSn 订单号
     * @return 订单信息
     */
    @GetMapping("/v1/feign/business/order/getOrderSnapshot")
    Result<OrderInfoVOV2> getOrderSnapshot(@RequestParam("orderSn") String orderSn) {
        OrderInfoVOV2 orderVOV2 = orderInfoService.getOrderSnapshot(orderSn);
        return ResultUtils.buildSuccessResult(orderVOV2);
    }


    @ApiOperation("判断店铺是否可以注销")
    @PostMapping("/seller/layoutStore")
    JsonResult<StoreLayoutVO> layoutStore(@Valid @RequestBody StoreLayoutDTO storeLayoutDTO) throws Exception {
       return SldResponse.success(orderSellerService.layoutStore(storeLayoutDTO));
    }

    @ApiOperation("更新事务记录状态")
    @GetMapping("changeTransactionStatus")
    JsonResult<Boolean> changeTransactionStatus(@RequestParam("transactionId")String transactionId,@RequestParam("oldStatus") Integer oldStatus,@RequestParam("newStatus") Integer newStatus){
        transactionLogService.updateTransactionStatusById(transactionId,oldStatus,newStatus);
        return SldResponse.success("success");
    }


    @ApiOperation("店铺风控预警测试")
    @GetMapping("/testRiskWarning")
    public JsonResult<String> testRiskWarning(@RequestParam("orderSn")String orderSn){
        OrderPO orderPO = orderModel.getOrderByOrderSn(orderSn);
        orderService.tradingRiskWareNing(orderPO);
        return SldResponse.success("success");
    }

    @ApiOperation("创建返利赠品订单")
    @PostMapping("/v1/feign/business/order/createRebateGiftOrder")
    @ThreadLocalRemoveTag
    JsonResult<OrderSubmitVO> createRebateGiftOrder(@RequestBody @Valid RebateOrderSubmitDTO rebateOrderSubmitDTO){
        log.info("start createRebateGiftOrder,param:{}",rebateOrderSubmitDTO);
        OrderSubmitVO res = orderPlacingService.createRebateGiftOrder(rebateOrderSubmitDTO);
        return SldResponse.success(res);
    }

}