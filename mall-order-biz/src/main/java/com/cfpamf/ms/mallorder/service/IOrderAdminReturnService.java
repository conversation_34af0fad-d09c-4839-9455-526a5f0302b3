package com.cfpamf.ms.mallorder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.ms.mallorder.po.OrderReturnPO;
import com.cfpamf.ms.mallorder.req.RefundTypeChangeRequest;
import com.cfpamf.ms.mallorder.req.admin.AdminForceRefundListRequest;
import com.cfpamf.ms.mallorder.req.admin.AdminForceRefundRequest;
import com.cfpamf.ms.mallorder.vo.OrderReturnVOV2;
import com.cfpamf.ms.mallsystem.vo.Admin;
import com.slodon.bbc.core.response.PageVO;
import com.slodon.bbc.core.response.PagerInfo;

/**
 * 平台端退款service
 */
public interface IOrderAdminReturnService extends IService<OrderReturnPO> {

    /**
     * 平台端强制退款
     *
     * @param admin             发起人
     * @param forceRefundReq    强制退款入参
     * @return                  发起结果
     */
    String adminForceRefund(Admin admin, AdminForceRefundRequest forceRefundReq);

    /**
     * 平台强制退款单查询
     *
     * @param request       查询入参
     * @param pager         分页信息
     * @return              分页查询结果
     */
    PageVO<OrderReturnVOV2> adminForceRefundList(PagerInfo pager, AdminForceRefundListRequest request);

    String changeRefundType(Admin admin, RefundTypeChangeRequest refundTypeChangeRequest);

}
