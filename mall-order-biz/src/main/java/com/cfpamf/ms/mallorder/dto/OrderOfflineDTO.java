package com.cfpamf.ms.mallorder.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 *
 */
@Data
public class OrderOfflineDTO implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 683011422865812319L;

	@ApiModelProperty(value = "主键id")
	private Long id;

	@NotEmpty(message = "收款帐号不能为空")
	@ApiModelProperty(value = "收款帐号")
	private String receiptAccount;

	@ApiModelProperty(value = "收款金额，保留2位小数")
	@NotNull(message = "收款金额不能为空")
	private BigDecimal receiptAmount;

	@ApiModelProperty(value = "收款时间")
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@NotNull(message = "收款时间不能为空")
	private Date receiptTime;

	@ApiModelProperty(value = "回执单图片")
	@NotEmpty(message = "图片不能为空")
	private String receiptListImageUrl;

	@ApiModelProperty("收款类型，NORMAL_RECEIPT 正常收款，DEBT_OFFSE 债权抵消；见OrderOfflineReceiptTypeEnum枚举")
	@NotEmpty(message = "收款类型不能为空")
	private String receiptType;

	@ApiModelProperty("债权供应商编码")
	private String supplierCode;

	@ApiModelProperty("债权供应商")
	private String supplierName;

	@ApiModelProperty(value = "是否可用，1-未删除，0-删除")
	private Integer enabledFlag = 1;

}
