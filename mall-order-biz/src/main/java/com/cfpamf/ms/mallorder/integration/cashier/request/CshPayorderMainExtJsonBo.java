package com.cfpamf.ms.mallorder.integration.cashier.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Create 2022-08-26 14:51
 * @Description :主单扩展json实体
 */
@Data
public class CshPayorderMainExtJsonBo {

    @ApiModelProperty(value = "分支编号")
    private String  branchCode;

    @ApiModelProperty(value = "分支名称")
    private String  branchName;

    @ApiModelProperty(value = "区域编号")
    private String  areaCode;

    @ApiModelProperty(value = "区域名称")
    private String  areaName;

    @ApiModelProperty("订单模式编码")
    private Integer orderPatternCode;

    @ApiModelProperty("订单模式名称")
    private String orderPatternDesc;
}
