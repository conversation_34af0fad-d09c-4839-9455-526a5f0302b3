package com.cfpamf.ms.mallorder.integration.hrms;

import com.cdfinance.hrms.facade.vo.BranchRelationVO;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.mallorder.common.util.ExternalApiUtil;
import com.cfpamf.ms.mallorder.controller.fegin.facade.SalaryRecordFacade;
import com.cfpamf.ms.mallorder.vo.EmployeePaySubjectRelationVO;
import com.cfpamf.ms.mallorder.vo.hrmsVO.OutBranchLifeAndInteriorInfoVO;
import com.cfpamf.ms.mallorder.vo.hrmsVO.QueryBranchLifeAndInteriorRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Create 2021-09-17 15:28
 * @Description :hr调用
 */
@Component
@Slf4j
public class HrmsIntegration {
    @Autowired
    private SalaryRecordFacade salaryRecordFacade;

    public BranchRelationVO getBranchRelation(String branchCode) {
        Result<BranchRelationVO> result = null;
        try {
            result = salaryRecordFacade.queryBranchRelationByCode(branchCode);
        } catch (Exception e) {
            log.error("HR获取区域信息异常:{}", branchCode, e);
        }

        if (result == null) {
            return null;
        }

        return result.getData();
    }

    /**
     * @param employeeCodeList
     * @return java.util.List<com.cdfinance.hrms.facade.vo.EmployeePaySubjectRelationVO>
     * @description : 批量查询员工与发薪主体
     */
    public List<EmployeePaySubjectRelationVO> queryEmployeePaySubjectRelation(List<String> employeeCodeList) {

        Result<List<EmployeePaySubjectRelationVO>> result = null;
        try {
            result = salaryRecordFacade.queryEmployeePaySubjectRelation(employeeCodeList);
        } catch (Exception e) {
            log.warn("调用hr系统获取员工发薪主体异常/hrms/salary/system/record/queryEmployeePaySubjectRelation  ：", e);
        }
        if (result == null || result.getData() == null) {
            log.warn("调用hr系统获取员工发薪主体为空/hrms/salary/system/record/queryEmployeePaySubjectRelation  ：{}", result);
            return new ArrayList();
        }
        return result.getData();
    }

    /**
     * 批量根据分支获取生服对接人和内务工号
     *
     * @param branchCodeList 分支编码列表
     * @return 生服对接人、内部工号实体
     */
    public Map<String, OutBranchLifeAndInteriorInfoVO> queryBranchLifeAndInteriorList(List<String> branchCodeList) {
        List<OutBranchLifeAndInteriorInfoVO> result = null;
        QueryBranchLifeAndInteriorRequest request = new QueryBranchLifeAndInteriorRequest();
        request.setBranchCodeList(branchCodeList);
        try {
            result = ExternalApiUtil.callResultApi(() -> salaryRecordFacade.queryBranchLifeAndInteriorList(request),request,"/hrms/employee/queryBranchLifeAndInteriorList","根据分支编码集合，查询分支最新的生服对接人和分支内务人员信息集合");
        } catch (Exception e) {
            log.error("HR获取区域信息异常:{}", branchCodeList, e);
        }

        if (result == null) {
            return null;
        }

        return result.stream().collect(Collectors.toMap(OutBranchLifeAndInteriorInfoVO::getBranchCode, Function.identity(),(o1,o2) -> o1));
    }

}
