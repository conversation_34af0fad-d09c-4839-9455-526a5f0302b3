package com.cfpamf.ms.mallorder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderProductErpExtendPO;
import com.cfpamf.ms.mallorder.po.OrderProductPO;

import java.util.List;
import java.util.Map;

public interface OrderProductErpExtendService extends IService<OrderProductErpExtendPO> {

    /**
     * 根据商品行id 和订单sn 获取对应的商品erp拓展信息 (如果有的话)
     * @param orderSn
     * @param orderProductId
     * @return
     */
    public OrderProductErpExtendPO selectOrderProductErpExtendByOrderSnAndOrderProductId(String orderSn, Long orderProductId);

    /**
     * 生成订单商品erp拓展信息,过滤掉没有物料编码的商品行
     * @param orderPO
     * @param orderProductPoList
     * @return
     */
    List<OrderProductErpExtendPO> buildOrderProductErpExtendList(OrderPO orderPO, List<OrderProductPO> orderProductPoList);

    void fillOrderErpSkuMaterialCode(List<String> orderSnList);

    Map<Long, OrderProductErpExtendPO> getOrderProductErpExtendMapByOrderProductIds(List<Long> orderProductIds);
}
