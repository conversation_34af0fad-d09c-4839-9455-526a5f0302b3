package com.cfpamf.ms.mallorder.scheduler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cfpamf.ms.mallorder.common.enums.OrderStatusEnum;
import com.cfpamf.ms.mallorder.common.enums.PaymentTagEnum;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.common.util.DateUtil;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.po.OrderOfflineExtendPO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.service.IOrderOfflineExtendService;
import com.cfpamf.ms.mallorder.service.IOrderService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;


/**
 * 每天计算线下补录订单是否逾期及逾期时间
 * <p>
 * 捞取:预占订单、线下订单(不含待付款、已关闭、已取消订单)
 * <p>
 * cron: 0 0 1 * * ?
 */
@Component
@Slf4j
public class OrderOfflineOverdueJob {

    @Resource
    private IOrderService orderService;

    @Resource
    private IOrderOfflineExtendService orderOfflineExtendService;

    @XxlJob("OrderOfflineOverdueJob")
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobHelper.log("=============== START OrderPartDeliver2DeliverCheckJob , DATE :{}", LocalDateTime.now());
        LambdaQueryWrapper<OrderPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        List<Integer> orderStateList = Arrays.asList(OrderStatusEnum.WAIT_DELIVER.getValue(), OrderStatusEnum.PART_DELIVERED.getValue(), OrderStatusEnum.WAIT_RECEIPT.getValue(), OrderStatusEnum.TRADE_SUCCESS.getValue());
        lambdaQueryWrapper.in(OrderPO::getOrderType, Arrays.asList(OrderTypeEnum.ORDER_TYPE_5.getValue(), OrderTypeEnum.ORDER_TYPE_6.getValue()))
                .in(OrderPO::getOrderState, orderStateList)
                .eq(OrderPO::getEnabledFlag, 1);
        List<OrderPO> orderPOList = orderService.list(lambdaQueryWrapper);
        log.info("OrderAutoDeliveryJob orderPOList size = {}", orderPOList.size());
        if (CollectionUtils.isEmpty(orderPOList)) {
            log.info("OrderAutoDeliveryJob orderPOList is Empty");
            return ReturnT.SUCCESS;
        }

        for (OrderPO orderPO : orderPOList) {
            LambdaUpdateWrapper<OrderOfflineExtendPO> updateWrapper = new LambdaUpdateWrapper<>();
            OrderOfflineExtendPO orderOfflineExtendPO = orderOfflineExtendService.getOrderOfflineExtendByOrderSn(orderPO.getOrderSn());
            if (Objects.isNull(orderOfflineExtendPO)) {
                log.warn("orderSn = {},该订单没有对应的线下补录扩展信息", orderPO.getOrderSn());
                continue;
            }
            if (PaymentTagEnum.RECEIVED.getValue().equals(orderPO.getPaymentTag())) {
                if (OrderConst.OVERDUE_FLAG_1 == orderOfflineExtendPO.getOverdueFlag()) {
                    updateWrapper.eq(OrderOfflineExtendPO::getOrderSn, orderPO.getOrderSn())
                            .set(OrderOfflineExtendPO::getOverdueFlag, OrderConst.OVERDUE_FLAG_0);
                    orderOfflineExtendService.update(updateWrapper);
                }
            } else {
                if (Objects.isNull(orderOfflineExtendPO.getOverdueTime())) {
                    continue;
                }
                if (DateUtil.isEndDateLessBeginDateByDay(new Date(), orderOfflineExtendPO.getOverdueTime())) {
                    int days = DateUtil.days(DateUtil.format(orderOfflineExtendPO.getOverdueTime(), DateUtil.FORMAT_DATE),
                            DateUtil.format(new Date(), DateUtil.FORMAT_DATE), DateUtil.FORMAT_DATE);
                    updateWrapper.eq(OrderOfflineExtendPO::getOrderSn, orderPO.getOrderSn())
                            .set(OrderOfflineExtendPO::getOverdueFlag, OrderConst.OVERDUE_FLAG_1)
                            .set(OrderOfflineExtendPO::getOverdueDays, days);
                    //逾期金额通过计算获取
                    orderOfflineExtendService.update(updateWrapper);
                }
            }

        }

        log.info("OrderAutoDeliveryJob end");


        XxlJobHelper.log("=============== END OrderPartDeliver2DeliverCheckJob , DATE :{}", LocalDateTime.now());
        return ReturnT.SUCCESS;
    }

}
