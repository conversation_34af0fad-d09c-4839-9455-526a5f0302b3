package com.cfpamf.ms.mallorder.service.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cfpamf.ms.mallorder.mapper.OrderProductExtendMapper;
import com.cfpamf.ms.mallorder.po.OrderProductExtendPO;
import com.cfpamf.ms.mallorder.service.IOrderProductExtendService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> 2021/8/17.
 */
@Slf4j
@Service
public class OrderProductExtendServiceImpl extends ServiceImpl<OrderProductExtendMapper, OrderProductExtendPO>
    implements IOrderProductExtendService {

    @Autowired
    private OrderProductExtendMapper orderProductExtendMapper;

    @Override
    public List<OrderProductExtendPO> getOrderProductExtendPOList(List<Long> orderProductIds) {
        if (CollectionUtils.isEmpty(orderProductIds)) {
            return null;
        }
        LambdaQueryWrapper<OrderProductExtendPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(OrderProductExtendPO::getOrderProductId, orderProductIds);
        return orderProductExtendMapper.selectList(queryWrapper);
    }

    @Override
    public List<OrderProductExtendPO> getOrderProductExtendPOList(String orderSn) {
        if (StringUtils.isEmpty(orderSn)) {
            return null;
        }
        LambdaQueryWrapper<OrderProductExtendPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(OrderProductExtendPO::getOrderSn, orderSn);
        return orderProductExtendMapper.selectList(queryWrapper);
    }

}
