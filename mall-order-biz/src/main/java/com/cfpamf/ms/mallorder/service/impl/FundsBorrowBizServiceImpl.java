package com.cfpamf.ms.mallorder.service.impl;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.mall.filecenter.constant.IScenesMaterialProofConstant;
import com.cfpamf.ms.mall.filecenter.domain.vo.FileScenesWithMaterialVO;
import com.cfpamf.ms.mall.filecenter.domain.vo.MaterialVO;
import com.cfpamf.ms.mallorder.common.config.FundsBorrowDayConfig;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.cfpamf.ms.mallorder.common.mq.RabbitMQUtils;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.enums.SettleModeEnum;
import com.cfpamf.ms.mallorder.integration.crawler.CrawlerIntegration;
import com.cfpamf.ms.mallorder.integration.filecenter.FileCenterIntegration;
import com.cfpamf.ms.mallorder.integration.filecenter.SceneTypeEnum;
import com.cfpamf.ms.mallorder.integration.shop.StoreIntegration;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.service.ICommonMqEventService;
import com.cfpamf.ms.mallorder.service.IFundsBorrowBizService;
import com.cfpamf.ms.mallorder.vo.OrderProductDeliveryVO;
import com.slodon.bbc.core.constant.StarterConfigConst;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.util.TimeUtil;
import com.slodon.bbc.starter.mq.entity.MessageSendProperty;
import com.slodon.bbc.starter.mq.entity.MessageSendVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/*
现款现货业务实现
 */
@Slf4j
@Service
public class FundsBorrowBizServiceImpl implements IFundsBorrowBizService {

    @Autowired
    private FundsBorrowDayConfig fundsBorrowDayConfig;
    @Autowired
    private ICommonMqEventService commonMqEventService;
    @Autowired
    private RabbitMQUtils rabbitMQUtils;
    @Autowired
    private FileCenterIntegration fileCenterIntegration;
    @Autowired
    private StoreIntegration storeIntegration;
    @Autowired
    private CrawlerIntegration crawlerIntegration;

    @Override
    public Result<Void> verifyOrderReceive(OrderPO orderPO,SceneTypeEnum sceneTypeEnum) {
        Result<Void> result = new Result<>();
        if (StringUtils.isAnyBlank(orderPO.getSettleMode(), orderPO.getOrderSn())) {
            result.setSuccess(false);
            result.addError(String.valueOf(ErrorCodeEnum.U.EMPTY_PARAM.getCode()), "校验订单收货缺失必要的订单信息", "");
            return result;
        }
        if (!SettleModeEnum.BORROW.getCode().equals(orderPO.getSettleMode())) {
            result.setSuccess(true);
            result.setData(null);
            return result;
        }
        FileScenesWithMaterialVO receiveProofVO = fileCenterIntegration.queryScenesProofMaterial(
                orderPO.getOrderSn(),
                IScenesMaterialProofConstant.ProofTypeEnum.MALL_ORDER_ORDER_SN,
                sceneTypeEnum);
        result.setSuccess(true);
        result.setData(null);
        return result;
    }

    @Override
    public Result<Void> verifyOrderDelivery(OrderPO orderPO,SceneTypeEnum sceneTypeEnum) {
        Result<Void> result = new Result<>();
        if (StringUtils.isAnyBlank(orderPO.getSettleMode(), orderPO.getOrderSn())) {
            result.setSuccess(false);
            result.addError(String.valueOf(ErrorCodeEnum.U.EMPTY_PARAM.getCode()), "校验订单发货缺失必要的订单信息", "");
            return result;
        }
        if (!SettleModeEnum.BORROW.getCode().equals(orderPO.getSettleMode())) {
            result.setSuccess(true);
            result.setData(null);
            return result;
        }
        FileScenesWithMaterialVO deliveryProofVO = fileCenterIntegration.queryScenesProofMaterial(
                orderPO.getOrderSn(),
                IScenesMaterialProofConstant.ProofTypeEnum.MALL_ORDER_ORDER_SN, sceneTypeEnum);
        List<MaterialVO> deliveryProofMaterialList = deliveryProofVO.getMaterialVOList();
        boolean haveFactoryNo = false;
        for (MaterialVO itemVO : deliveryProofMaterialList) {
            if (!itemVO.getMaterialNo().equals(fundsBorrowDayConfig.getFactoryNo())) {
                continue;
            }
            haveFactoryNo = true;
            Result<Boolean> existResult = crawlerIntegration.existFactoryNo(itemVO.getProofRemark().trim());
            if (!existResult.isSuccess() || Objects.isNull(existResult.getData())) {
                throw new BusinessException(String.format("校验现款现货发货资料失败,orderSn:%s,资料名称:%s",
                        orderPO.getOrderSn(),itemVO.getMaterialName()));
            }
            if (existResult.getData()) {
                result.setSuccess(false);
                result.addError(String.valueOf(ErrorCodeEnum.U.EMPTY_PARAM.getCode()),
                        String.format("存在已被使用的现款现货发货资料,orderSn:%s,资料名称:%s", orderPO.getOrderSn(),itemVO.getMaterialName()), "");
                return result;
            }
        }
        if (!haveFactoryNo) {
            result.setSuccess(false);
            result.addError(String.valueOf(ErrorCodeEnum.U.EMPTY_PARAM.getCode()),
                    String.format("现款现货发货资料缺失出厂编号,orderSn:%s", orderPO.getOrderSn()), "");
            return result;
        }
        result.setSuccess(true);
        result.setData(null);
        return result;
    }
    @Override
    public Result<Integer> fundsBorrowExpireAlert(List<OrderProductDeliveryVO> orderProductPOList, String alertType) {
        Result<Integer> result = new Result<>();
        if (CollectionUtils.isEmpty(orderProductPOList)) {
            log.info("现款现货逾期发货提醒的入参为空,alertType:{}", alertType);
            result.setSuccess(true);
            result.setData(0);
            return result;
        }
        if (!StringUtils.equalsAny(alertType, OrderConst.FUNDS_BORROW_STORE_DELIVERY_EXPIRE_ALERT,
                OrderConst.FUNDS_BORROW_STORE_CLOSE_ALERT)) {
            log.info("暂不支持其他形式的现款现货逾期发货提醒,alertType:{}", alertType);
            result.setSuccess(false);
            result.addError(HttpStatus.BAD_REQUEST.toString(), "暂不支持其他形式的现款现货逾期发货提醒", null);
            return result;
        }
        Map<Long, String> storeMap = new HashMap<>();
        Map<Long, List<OrderProductDeliveryVO>> productGroupMap = new HashMap<>();
        List<OrderProductDeliveryVO> productList;
        for (OrderProductDeliveryVO itemVO : orderProductPOList) {
            productList = productGroupMap.get(itemVO.getStoreId());
            if (Objects.isNull(productList)) {
                productList = new ArrayList<>();
                productGroupMap.put(itemVO.getStoreId(), productList);
                storeMap.put(itemVO.getStoreId(), itemVO.getStoreName());
            }
            productList.add(itemVO);
        }
        int count = 0;
        for (Map.Entry<Long, List<OrderProductDeliveryVO>> entry : productGroupMap.entrySet()) {
            this.sendStoreDeliveryExpireMessage(entry.getKey(), storeMap.get(entry.getKey()),
                    entry.getValue().size(), alertType);
            for (OrderProductDeliveryVO itemPO : entry.getValue()) {
                this.sendProductDeliveryExpireMessage(itemPO, alertType);
                count++;
            }
        }
        result.setSuccess(true);
        result.setData(count);
        return result;
    }

    private void sendStoreDeliveryExpireMessage(Long storeId, String storeName, Integer total, String alertType) {
        //消息通知
        List<MessageSendProperty> messagePropertyList = new ArrayList<>();
        messagePropertyList.add(new MessageSendProperty("storeId", String.valueOf(storeId)));
        messagePropertyList.add(new MessageSendProperty("storeName", storeName));
        messagePropertyList.add(new MessageSendProperty("total", String.valueOf(total)));

        MessageSendVO messageSendVO = new MessageSendVO(messagePropertyList,
                null, storeId, alertType, "{}");

        Long eventId = commonMqEventService.saveEvent(messageSendVO, OrderConst.NEWMALL_QUEUE_SELLER_ADMIN_MSG, StarterConfigConst.MQ_EXCHANGE_NAME);

        rabbitMQUtils.sendByEventId(eventId);
    }

    private void sendProductDeliveryExpireMessage(OrderProductDeliveryVO orderProductPO, String alertType) {
        //消息通知
        List<MessageSendProperty> messagePropertyList = new ArrayList<>();
        String tplType;
        if (OrderConst.FUNDS_BORROW_STORE_DELIVERY_EXPIRE_ALERT.equals(alertType)) {
            messagePropertyList.add(new MessageSendProperty("first", "【发货提醒通知】"));
            messagePropertyList.add(new MessageSendProperty("keyword1", orderProductPO.getReceiverName()));
            messagePropertyList.add(new MessageSendProperty("keyword2", "发货提醒"));
            messagePropertyList.add(new MessageSendProperty("keyword3", String.format("订单号【%s】，商品名称【%s】，请及时进行发货。", orderProductPO.getOrderSn(), orderProductPO.getGoodsName())));
            tplType = OrderConst.FUNDS_BORROW_STORE_DELIVERY_EXPIRE_WECHAT;
        } else {
            messagePropertyList.add(new MessageSendProperty("first", "【下单权限关闭通知】"));
            messagePropertyList.add(new MessageSendProperty("keyword1", orderProductPO.getStoreName()));
            messagePropertyList.add(new MessageSendProperty("keyword2", "下单权限关闭"));
            messagePropertyList.add(new MessageSendProperty("keyword3", String.format("因订单号【%s】，商品名称【%s】，未及时进行发货，您的店铺下单权限将被关闭。", orderProductPO.getOrderSn(), orderProductPO.getGoodsName())));
            tplType = OrderConst.FUNDS_BORROW_STORE_CLOSE_WECHAT;
        }
        messagePropertyList.add(new MessageSendProperty("keyword4", TimeUtil.getDateTimeString(new Date())));
        messagePropertyList.add(new MessageSendProperty("remark", "服务农村最后一百米"));

        MessageSendVO messageSendVO = new MessageSendVO(null,
                messagePropertyList, null, orderProductPO.getStoreId().intValue(), tplType, "{}");

        Long eventId = commonMqEventService.saveEvent(messageSendVO, OrderConst.NEWMALL_QUEUE_SELLER_ADMIN_MSG, StarterConfigConst.MQ_EXCHANGE_NAME);

        rabbitMQUtils.sendByEventId(eventId);
    }

    @Override
    public Result<Integer> fundsBorrowCloseStoreTrade(List<OrderProductDeliveryVO> orderProductPOList) {
        Result<Integer> result = new Result<>();
        if (CollectionUtils.isEmpty(orderProductPOList)) {
            log.info("现款现货逾期发货关闭店铺下单权限的入参为空");
            result.setSuccess(true);
            result.setData(0);
            return result;
        }
        int successCount = 0;
        Set<Long> storeIdSet = orderProductPOList.stream().map(OrderProductDeliveryVO::getStoreId).collect(Collectors.toSet());
        for (Long itemStoreId : storeIdSet) {
            Result<Void> closeResult = storeIntegration.orderAuthorityClose(itemStoreId);
            if (!closeResult.isSuccess()) {
                log.error("遍历执行现款现货关闭店铺下单权限失败,storeId:{}", itemStoreId);
            }else {
                successCount++;
            }
        }
        result.setSuccess(true);
        result.setData(successCount);
        return result;
    }
}
