package com.cfpamf.ms.mallorder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.mallorder.po.JsPkDynamicPerformancePO;
import com.cfpamf.ms.mallorder.po.LoanResultPO;
import com.cfpamf.ms.mallorder.req.admin.LoanListResultRequest;
import com.cfpamf.ms.mallorder.vo.JsPkDynamicPerformanceVO;
import com.cfpamf.ms.mallorder.vo.LoanResultVo;
import com.cfpamf.ms.mallsystem.vo.Admin;
import com.slodon.bbc.core.response.PageVO;
import com.slodon.bbc.core.response.PagerInfo;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-23
 */
public interface IJsPkDynamicPerformanceService {

    /**
     *
     *
     * @param rptDateStart     统计时间-开始
     * @param rptDateEnd     统计时间-结束
     * @return
     */
    void queryList(Date rptDateStart, Date rptDateEnd);




}
