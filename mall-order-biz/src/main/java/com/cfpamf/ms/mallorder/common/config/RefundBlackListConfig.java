package com.cfpamf.ms.mallorder.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 订单退款(平台审批)黑名单
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/29 10:02
 */
@RefreshScope
@EnableConfigurationProperties
@Configuration
@ConfigurationProperties(prefix = "refund-black-list")
public class RefundBlackListConfig {

    public List<String> orderList;

    public List<String> getOrderList() {
        return orderList;
    }

    public void setOrderList(List<String> orderList) {
        this.orderList = orderList;
    }
}
