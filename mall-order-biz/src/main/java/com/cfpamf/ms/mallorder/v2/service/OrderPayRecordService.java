package com.cfpamf.ms.mallorder.v2.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.ms.mallorder.po.OrderPayRecordPO;
import com.cfpamf.ms.mallorder.v2.domain.bo.OrderPayAddBalanceBO;
import com.cfpamf.ms.mallorder.v2.domain.bo.OrderPayRecordBO;

/**
 * 类OrderPayRecordService.java的实现描述：
 *
 * <AUTHOR> 14:56
 */
public interface OrderPayRecordService extends IService<OrderPayRecordPO> {

    /**
     * 根据paySn查询支付待支付记录
     * 
     * @param paySn
     * @return
     */
    OrderPayRecordPO queryUnpaidOrderPayOneByPaySn(String paySn);

    /**
     * 根据支付单号查询支付中的支付记录
     *
     * @param paySn 支付单号
     * @return 支付记录
     */
    OrderPayRecordPO queryPayingOrderPayOneByPaySn(String paySn);

    /**
     * 查询支付信息
     * 
     * @param payNo
     * @return
     */
    OrderPayRecordPO queryOrderPayByPayNo(String payNo);

    /**
     * 根据paySn查询贷款支付订单
     * 
     * @param paySn
     * @return
     */
    List<OrderPayRecordPO> queryLoanOrderPayByPaySn(String paySn);
    
    /**
     * 根据payNos查询支付订单
     * @param payNos
     * @return
     */
    List<OrderPayRecordPO> queryOrderPayByPayNos(List<String> payNos);
    
    /**
     * 根据paySn查询贷款已支付待退款订单
     * @param paySn
     * @return
     */
    List<OrderPayRecordPO> queryLoanNoRefundPayOrderByPaySn(String paySn);

    /**
     * 根据paySn查询支付订单
     * 
     * @param paySn
     * @return
     */
    List<OrderPayRecordPO> queryOrderPayByPaySn(String paySn);

    /**
     * 根据paySn查询贷款支付订单数量
     * 
     * @param paySn
     * @return
     */
    Integer countLoanOrderPayByPaySn(String paySn);

    /**
     * 支付结果更新
     * 
     * @param entity
     * @param payNo
     * @param updateLimitConditionWaitPay 更新限制条件待支付
     * @return
     */
    boolean updateByPayNo(OrderPayRecordPO entity, String payNo, boolean updateLimitConditionWaitPay);
    
    /**
     * 更新支付结果等信息
     * 
     * @param entity
     * @param payNo
     * @return
     */
    boolean updateByPayNo(OrderPayRecordPO entity, String payNo);

    /**
     * 校验主支付单号下的子单是否全部支付完成
     * 
     * @param paySn
     * @return
     */
    boolean verifyPayOrderAllFinishByPaySn(String paySn);

    /**
     * 校验支付订单是否支付完成
     * 
     * @param payNo
     * @return
     */
    boolean verifyPayOrderFinishByPayNo(String payNo);

    /**
     * 根据paySn查询未退款订单集合
     * 
     * @param paySn
     * @return
     */
    List<OrderPayRecordPO> queryNoRefundPayOrderByPaySn(String paySn);

    /**
     * 根据paySn统计未退款订单集合
     * 
     * @param paySn
     * @return
     */
    Integer countNotRefundOrderPayByPaySn(String paySn);

    /**
     * 更新退款余额
     * 
     * @param entity
     * @return
     */
    boolean updateBalanceBatchByIds(List<OrderPayRecordBO> entity);

    /**
     * Add退款余额
     * 
     * @param entity
     * @return
     */
    boolean addBalanceBatchByPayNos(List<OrderPayAddBalanceBO> entity);

}
