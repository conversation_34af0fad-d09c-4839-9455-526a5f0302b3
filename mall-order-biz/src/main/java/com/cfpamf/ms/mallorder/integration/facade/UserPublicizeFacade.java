package com.cfpamf.ms.mallorder.integration.facade;

import com.cfpamf.framework.autoconfigure.common.result.Result;
import com.cfpamf.ms.mallorder.integration.facade.vo.DbcDistributionSaleUserVO;
import com.cfpamf.ms.mallorder.vo.CustRelateManagerVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(
        name = "user-publicize-service",
        url = "${user-publicize-service.url}"
)
public interface UserPublicizeFacade {

    @ApiOperation("根据身份证查询推广用户数据")
    @GetMapping("/v1/feign/ups/distributionSaleUser/userInfo")
    Result<DbcDistributionSaleUserVO> getSaleUserInfo(@RequestParam("identityNum") String identityNum);

    @ApiOperation(value = "根据自身推荐码查询推广用户数据")
    @GetMapping("/v1/feign/ups/distributionSaleUser/queryUserByMyReferenceCode")
    Result<DbcDistributionSaleUserVO> queryUserByMyReferenceCode(@RequestParam("myReferenceCode") String myReferenceCode);

    @ApiOperation(value = "管护关系查询")
    @GetMapping("/v1/feign/ups/custManager/queryByUserNo")
    Result<CustRelateManagerVO> queryBizManager(@RequestParam(value = "userNo") String userNo, @RequestParam(value = "businessModel") Integer businessModel);
}
