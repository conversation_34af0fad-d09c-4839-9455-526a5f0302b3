package com.cfpamf.ms.mallorder.req.pgrpt;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.List;


/**
 * 匹配对手请求实体
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PkMatchOpponentsRequest {

    @NotBlank(message = "类型不能为空")
    @ApiModelProperty(value = "1.区域 2.分支 3.客户经理 4.督导 必填")
    private String type;

    @NotBlank(message = "匹配人员不能为空")
    @ApiModelProperty(value = "匹配人员工编号， 必填")
    private String userCode;

    @ApiModelProperty(value = "匹配人分支编号， 必填")
    private String branchCode;

    @ApiModelProperty(value = "匹配人区域编码， 必填")
    private String regionCode;

    /**
     * @see com.cfpamf.ms.mallorder.common.enums.PkTypeEnum
     */
    @NotBlank(message = "PK指标不能为空")
    @ApiModelProperty(value = "PK指标类型， 必填")
    private String indicatorsType;

    @ApiModelProperty(value = "范围查询-指标最大值， 非必填")
    private BigDecimal indicatorsMaxValue;

    @ApiModelProperty(value = "范围查询-指标最小值， 非必填")
    private BigDecimal indicatorsMinValue;

    @ApiModelProperty(value = "匹配姓名，前缀模糊匹配,客户经理姓名，督导姓名，主任姓名，区域员工姓名 ， 非必填")
    private String matchName;

    @ApiModelProperty(value = "黑名单人员列表")
    private List<String> blackUserCodeList;

    @ApiModelProperty(value = "开始日期： 2023-08-01")
    private String startDate;

    @ApiModelProperty(value = "结束日期 ： 2023-08-03")
    private String endDate;

    @ApiModelProperty(value = "页码， 必填")
    int pageNum = 1;

    @ApiModelProperty(value = "每页大小， 必填")
    int pageSize = 10;

}
