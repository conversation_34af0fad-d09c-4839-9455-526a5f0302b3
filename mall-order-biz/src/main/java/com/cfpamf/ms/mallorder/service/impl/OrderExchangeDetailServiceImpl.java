package com.cfpamf.ms.mallorder.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.aliyuncs.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cfpamf.ms.mallorder.constant.ExchangeOrderConst;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.*;
import com.cfpamf.ms.mallorder.mapper.OrderExchangeDetailMapper;
import com.cfpamf.ms.mallorder.mapper.OrderExchangeMapper;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.model.OrderProductModel;
import com.cfpamf.ms.mallorder.po.OrderExchangeDetailPO;
import com.cfpamf.ms.mallorder.po.OrderExchangePO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderProductPO;
import com.cfpamf.ms.mallorder.req.exchange.OrderExchangeListRequest;
import com.cfpamf.ms.mallorder.request.OrderExchangeDetailExample;
import com.cfpamf.ms.mallorder.request.OrderExchangeListExample;
import com.cfpamf.ms.mallorder.service.IOrderExchangeDetailService;
import com.cfpamf.ms.mallorder.service.IOrderTradeProofService;
import com.cfpamf.ms.mallorder.vo.MallFileScenesProofVO;
import com.cfpamf.ms.mallorder.vo.OrderExchangeDetailVO;
import com.cfpamf.ms.mallorder.vo.OrderExchangeListVO;
import com.slodon.bbc.core.response.PagerInfo;
import com.slodon.bbc.core.util.AssertUtil;
import com.slodon.bbc.core.util.FileUrlUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class OrderExchangeDetailServiceImpl extends ServiceImpl<OrderExchangeDetailMapper, OrderExchangeDetailPO> implements IOrderExchangeDetailService {

    @Resource
    private OrderExchangeDetailMapper orderExchangeDetailMapper;

    @Resource
    private OrderExchangeMapper orderExchangeMapper;

    @Resource
    private OrderProductModel orderProductModel;

    @Resource
    private OrderModel orderModel;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    @Lazy
    private IOrderTradeProofService orderTradeProofService;

    @Override
    public List<OrderExchangeListVO> orderExchangeList(OrderExchangeListRequest orderExchangeListRequest, PagerInfo pager) {
        List<OrderExchangeListVO> orderExchangeList = new ArrayList<>();
        OrderExchangeListExample example = new OrderExchangeListExample();
        BeanUtil.copyProperties(orderExchangeListRequest, example);
        if (pager != null) {
            orderExchangeList = orderExchangeDetailMapper.getOrderExchangeListVOByPage(example, pager.getStart(), pager.getPageSize());
            pager.setRowsCount(orderExchangeDetailMapper.getCountOrderExchangeList(example));
        }
        orderExchangeList.stream().forEach(x -> {
            x.setProductImage(FileUrlUtil.getFileUrl(x.getProductImage(), null));
        });
        return orderExchangeList;
    }

    @Override
    public List<OrderExchangeDetailVO> productOrderExchangeDetail(Long orderProductId, UserDTO userDTO) {
        List<OrderExchangeDetailVO> orderExchangeDetailVOS = new ArrayList<>();

        OrderExchangeDetailExample example = new OrderExchangeDetailExample();
        example.setOrderProductId(orderProductId);
        List<OrderExchangeDetailPO> orderExchangeList = orderExchangeDetailMapper.getOrderExchangeDetailList(example);
        orderExchangeList.stream().forEach(x -> {
            OrderExchangeDetailVO orderExchangeDetailVO = this.orderExchangeApplyDetail(x.getExchangeSn(), userDTO);
            orderExchangeDetailVOS.add(orderExchangeDetailVO);
        });
        return orderExchangeDetailVOS;
    }

    @Override
    public List<OrderExchangeDetailVO> getOrderExchangeDetail(String OrderSn, UserDTO userDTO) {
        List<OrderExchangeDetailVO> list = new ArrayList<>();
        OrderExchangeDetailExample example = new OrderExchangeDetailExample();
        example.setOrderSn(OrderSn);
        List<OrderExchangeDetailPO> orderExchangeList = orderExchangeDetailMapper.getOrderExchangeDetailList(example);
        if (CollectionUtils.isEmpty(orderExchangeList)) {
            return list;
        }
        orderExchangeList.stream().forEach(x -> {
            list.add(orderExchangeApplyDetail(x.getExchangeSn(), userDTO));
        });
        return list;
    }

    @Override
    public OrderExchangeDetailVO orderExchangeApplyDetail(String exchangeSn, UserDTO userDTO) {
        OrderExchangeDetailVO orderExchangeDetailVO = new OrderExchangeDetailVO();

        OrderExchangeDetailPO orderExchangeDetailPO = orderExchangeDetailMapper.getExchangeDetailByExchangeSn(exchangeSn);
        OrderExchangePO orderExchangePO = orderExchangeMapper.getOrderExchangeByExchangeSn(exchangeSn);

        AssertUtil.isTrue(orderExchangeDetailPO == null || orderExchangePO == null, "该换货单不存在");
        if (OrderConst.LOG_ROLE_VENDOR == userDTO.getUserRole()) {
            AssertUtil.isTrue(!orderExchangePO.getStoreId().equals(userDTO.getStoreId()), "您无权查看该换货单详情");
        } else if (OrderConst.LOG_ROLE_MEMBER == userDTO.getUserRole()) {
            AssertUtil.isTrue(!orderExchangePO.getMemberId().equals(userDTO.getUserId().intValue()), "您无权查看该换货单详情");
        }
        OrderPO oldOrderPO = orderModel.getOrderByOrderSn(orderExchangeDetailPO.getOrderSn());
        OrderProductPO orderProductPO = orderProductModel.getOrderProductByOrderProductId(orderExchangeDetailPO.getOrderProductId());
        OrderProductPO exchangeOrderProductPO = orderProductModel.getOrderProductByOrderProductId(orderExchangeDetailPO.getExchangeOrderProductId());

        ExchangeOrderDTO productInfo = new ExchangeOrderDTO(orderProductPO, orderExchangeDetailPO.getProductNum());
        ExchangeOrderDTO orderInfo = new ExchangeOrderDTO(orderProductPO);
        ExchangeOrderDTO newOrderInfo = new ExchangeOrderDTO(exchangeOrderProductPO);

        orderExchangeDetailVO.setExchangeSn(exchangeSn);
        orderExchangeDetailVO.setBuyerConfirmFlag(orderExchangePO.getBuyerConfirmFlag());
        orderExchangeDetailVO.setRefundAmount(orderExchangeDetailPO.getRefundAmount());
        orderExchangeDetailVO.setActualRefundAmount(orderExchangeDetailPO.getActualRefundAmount());
        orderExchangeDetailVO.setXzCardAmount(orderExchangeDetailPO.getXzCardAmount());
        orderExchangeDetailVO.setPlatformVoucherAmount(orderExchangeDetailPO.getPlatformVoucherAmount());
        orderExchangeDetailVO.setPlatformActivityAmount(orderExchangeDetailPO.getPlatformActivityAmount());
        orderExchangeDetailVO.setStoreVoucherAmount(orderExchangeDetailPO.getStoreVoucherAmount());
        orderExchangeDetailVO.setStoreActivityAmount(orderExchangeDetailPO.getStoreActivityAmount());
        orderExchangeDetailVO.setStoreActivityAmount(orderExchangeDetailPO.getStoreActivityAmount());
        orderExchangeDetailVO.setExchangeOrderState(orderExchangePO.getExchangeOrderState());
        orderExchangeDetailVO.setExchangeReason(orderExchangePO.getExchangeReason());
        orderExchangeDetailVO.setAfsSn(orderExchangeDetailPO.getAfsSn());
        orderExchangeDetailVO.setStoreId(orderExchangePO.getStoreId());
        orderExchangeDetailVO.setCreateTime(orderExchangePO.getCreateTime());
        orderExchangeDetailVO.setUserNo(oldOrderPO.getUserNo());
        orderExchangeDetailVO.setUserMobile(oldOrderPO.getUserMobile());

        if (ExchangeOrderConst.EXCHANGE_ORDER_STATE_WAIT_AUDIT == orderExchangePO.getExchangeOrderState()
                && ExchangeOrderConst.BUYER_CONFIRM_FLAG_1 == orderExchangePO.getBuyerConfirmFlag()) {
            //换货单审批时间倒计时
            orderExchangeDetailVO.setRemainTime(dealRemainTime("exchange_order_auto_audit_time", orderExchangePO.getCreateTime(), 300));
        }

        OrderPO newOrderPO = orderModel.getOrderByOrderSn(newOrderInfo.getOrderSn());
        if (ExchangeOrderConst.EXCHANGE_ORDER_STATE_AGREE == orderExchangePO.getExchangeOrderState()) {
            //查询换货后的订单状态
            orderExchangeDetailVO.setOrderState(newOrderPO.getOrderState());
        }
        orderExchangeDetailVO.setProductInfo(productInfo);
        orderExchangeDetailVO.setNewOrderInfo(newOrderInfo);
        orderExchangeDetailVO.setOrderInfo(orderInfo);
        // 获取订单下单、发货、签收凭证资料
        List<MallFileScenesProofVO> fileScenesProofVOS = orderTradeProofService.queryScenesMaterialV2(newOrderPO);
        orderExchangeDetailVO.setFileScenesProofVOS(fileScenesProofVOS);

        return orderExchangeDetailVO;
    }

    public long dealRemainTime(String key, Date createTime, Integer defaultValue) {

        String value = stringRedisTemplate.opsForValue().get(key);
        int limitMinute = value == null ? defaultValue : Integer.parseInt(value);
        //Integer value = 7 * 24 * 60;
        //int limitMinute = value == null ? defaultValue : value;
        // 获取当前时间limitHour小时之前的时间
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, -limitMinute);
        Date cancelTime = calendar.getTime();

        long time1 = createTime.getTime();
        long time2 = cancelTime.getTime();
        long remainTime = (time1 - time2) / 1000;
        return remainTime < 0 ? 0 : remainTime;
    }

    @Override
    public ExchangeOrderReturnDetailDTO dealExchangeOrderReturnDetail(String afsSn) {
        if (StringUtils.isEmpty(afsSn)) {
            return null;
        }
        LambdaQueryWrapper<OrderExchangeDetailPO> exchangeDetailPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        exchangeDetailPOLambdaQueryWrapper.eq(OrderExchangeDetailPO::getAfsSn, afsSn);

        OrderExchangeDetailPO orderExchangeDetailPO = this.getOne(exchangeDetailPOLambdaQueryWrapper);

        OrderProductPO orderProductPO = orderProductModel.getOrderProductByOrderProductId(orderExchangeDetailPO.getExchangeOrderProductId());
        OrderPO orderPO = orderModel.getOrderByOrderSn(orderExchangeDetailPO.getExchangeOrderSn());
        AssertUtil.isTrue(orderPO == null || orderProductPO == null, "查询不到换货信息");
        ExchangeOrderReturnDetailDTO exchangeOrderReturnDetailDTO = new ExchangeOrderReturnDetailDTO(orderProductPO);
        exchangeOrderReturnDetailDTO.setOrderState(orderPO.getOrderState());
        exchangeOrderReturnDetailDTO.setExchangeSn(orderExchangeDetailPO.getExchangeSn());

        return exchangeOrderReturnDetailDTO;
    }

    @Override
    public OrderExchangeDetailPO getExchangeOrderDetailByExample(OrderExchangeDetailExample example) {
        return orderExchangeDetailMapper.getExchangeOrderDetailByExample(example);
    }

    @Override
    public ExchangeOrderDetailDTO getOrderExchangeDetailByExchangeSn(String exchangeSn) {
        ExchangeOrderDetailDTO exchangeOrderDetailDTO = new ExchangeOrderDetailDTO();
        exchangeOrderDetailDTO.setAfsSn(exchangeSn);
        return orderExchangeDetailMapper.getOrderExchangeDetail(exchangeOrderDetailDTO);
    }

    @Override
    public ExchangeOrderDetailDTO getOrderExchangeDetailByAfsSn(String afsSn) {
        ExchangeOrderDetailDTO exchangeOrderDetailDTO = new ExchangeOrderDetailDTO();
        exchangeOrderDetailDTO.setAfsSn(afsSn);
        return orderExchangeDetailMapper.getOrderExchangeDetail(exchangeOrderDetailDTO);
    }


    /**
     * 获取商品行换货退款数量
     */
    public Map<Long, Integer> getProductExchangeCountMap(List<Long> orderProductIdList) {
        Map<Long,Integer> resultMap = new HashMap<>();
        if(CollectionUtils.isEmpty(orderProductIdList)) {
            return resultMap;
        }
        List<OrderExchangeCountDTO> orderExchangeCountList = orderExchangeDetailMapper.getProductExchangeCount(orderProductIdList);
        if(org.springframework.util.CollectionUtils.isEmpty(orderExchangeCountList)) {
            return resultMap;
        }
        return orderExchangeCountList.stream().collect(Collectors.toMap(OrderExchangeCountDTO::getOrderProductId,OrderExchangeCountDTO::getExchangedNum));
    }
}
