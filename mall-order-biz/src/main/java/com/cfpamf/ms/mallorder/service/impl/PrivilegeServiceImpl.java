package com.cfpamf.ms.mallorder.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cfpamf.ms.mallorder.common.config.*;
import com.cfpamf.ms.mallorder.common.enums.PayMethodEnum;
import com.cfpamf.ms.mallorder.dto.SellerPrivilegeDTO;
import com.cfpamf.ms.mallorder.enums.OrderPlaceUserRole;
import com.cfpamf.ms.mallorder.integration.filecenter.ProofSceneEnum;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.service.IOrderService;
import com.cfpamf.ms.mallorder.service.IOrderTradeProofService;
import com.cfpamf.ms.mallorder.service.PrivilegeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

@Slf4j
@Service
public class PrivilegeServiceImpl implements PrivilegeService {

    @Autowired
    private OrderOfflinePrivilegeConfig orderOfflinePrivilegeConfig;

    @Autowired
    private StoreForDeliverConfig storeForDeliverConfig;

    @Autowired
    private StoreForSelfOrderDeliverConfig storeForSelfOrderDeliverConfig;

    @Autowired
    private FaceScanForPayConfig faceScanForPayConfig;

    @Resource
    private IOrderService orderService;

    @Autowired
    private StoreForReceiveConfig storeForReceiveConfig;

    @Autowired
    private IOrderTradeProofService orderTradeProofService;

    @Override
    public SellerPrivilegeDTO getSellerPrivilegeInfo(Integer storeId) {
        SellerPrivilegeDTO sellerPrivilegeDTO = new SellerPrivilegeDTO();
        log.info("【privilege】storeId：{} orderOfflinePrivilege:{}", storeId, orderOfflinePrivilegeConfig.getPrivilege());
        sellerPrivilegeDTO.setOrderOfflinePrivilege(
                ObjectUtils.isNotEmpty(storeId)
                        && ObjectUtils.isNotEmpty(orderOfflinePrivilegeConfig.getPrivilege())
                        && orderOfflinePrivilegeConfig.getPrivilege().contains(String.valueOf(storeId)));
        return sellerPrivilegeDTO;
    }

    @Override
    public boolean storeDeliveryPrivilege(String orderSn) {
        if (StringUtils.isEmpty(orderSn)) {
            return false;
        }

        // 订单规则匹配，查bapp发货权限
        return orderTradeProofService.matchBappPrivilege(orderSn, ProofSceneEnum.DELIVERY);

//        LambdaQueryWrapper<OrderPO> orderQuery = Wrappers.lambdaQuery();
//        orderQuery.eq(OrderPO::getOrderSn, orderSn)
//                .select(OrderPO::getStoreId, OrderPO::getOrderPattern);
//        OrderPO orderPO = orderService.getOne(orderQuery);
//        if (Objects.isNull(orderPO)) {
//            return false;
//        }
//
//        boolean storeForDeliver = true;
//        if (ObjectUtils.isNotEmpty(storeForDeliverConfig.getStoreId())) {
//            storeForDeliver = storeForDeliverConfig.getStoreId().contains(String.valueOf(orderPO.getStoreId()));
//        }
//        boolean storeForSelfOrderDeliver = true;
//        if (ObjectUtils.isNotEmpty(storeForSelfOrderDeliverConfig.getStoreId())) {
//            storeForSelfOrderDeliver = storeForSelfOrderDeliverConfig.getStoreId().contains(String.valueOf(orderPO.getStoreId()))
//                    && orderPO.getOrderPattern().equals(OrderPatternEnum.SELF_LIFT.getValue());
//        }
//        return storeForDeliver || storeForSelfOrderDeliver;
    }

    @Override
    public boolean storeReceivePrivilege(String orderSn) {
        if (StringUtils.isEmpty(orderSn)){
            return false;
        }

        // 订单规则匹配，查bapp签收权限
        return orderTradeProofService.matchBappPrivilege(orderSn, ProofSceneEnum.RECEIVE);

//        LambdaQueryWrapper<OrderPO> orderQuery = Wrappers.lambdaQuery();
//        orderQuery.eq(OrderPO::getOrderSn, orderSn)
//                .select(OrderPO::getOrderId, OrderPO::getStoreId, OrderPO::getSettleMode, OrderPO::getPerformanceModes);
//        OrderPO orderPO = orderService.getOne(orderQuery);
//        if (Objects.isNull(orderPO)){
//            return false;
//        }
//
//        // 订单属于农机厂商/现款现货订单，允许签收
//        if (orderPO.getPerformanceModes().contains(OrderPerformanceModeEnum.PERFORMANCE_MODE_MFR_DEALER.getValue().toString())
//                || SettleModeEnum.BORROW.getCode().equals(orderPO.getSettleMode())) {
//            return true;
//        }
//
//        //否则判断店铺是否在签收白名单
//        boolean storeForReceive = false;
//        log.info("front/orderOperate/receive storeForReceiveConfig:{}",storeForReceiveConfig.getStoreId());
//        if (ObjectUtils.isNotEmpty(storeForReceiveConfig.getStoreId())){
//            storeForReceive = storeForReceiveConfig.getStoreId().contains(String.valueOf(orderPO.getStoreId()));
//        }
//        return storeForReceive ;
    }

    @Override
    public boolean payPrivilege(String orderSn) {
        LambdaQueryWrapper<OrderPO> orderQuery = Wrappers.lambdaQuery();
        orderQuery.eq(OrderPO::getOrderSn, orderSn)
                .select(OrderPO::getOrderSn, OrderPO::getPaymentCode, OrderPO::getOrderPlaceUserRoleCode);
        OrderPO orderpo = orderService.getOne(orderQuery);
        if (Objects.isNull(orderpo)) {
            return false;
        }

        // 代客下单、贷款支付，查询支付配置
        if (OrderPlaceUserRole.isValetOrder(orderpo.getOrderPlaceUserRoleCode())
                && PayMethodEnum.isLoanPay(orderpo.getPaymentCode())) {
            return faceScanForPayConfig.getEnabled() == null || faceScanForPayConfig.getEnabled();
        }
        return true;
    }

}
