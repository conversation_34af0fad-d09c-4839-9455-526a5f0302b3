package com.cfpamf.ms.mallorder.integration.cust;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.cmis.common.base.CommonResult;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.framework.autoconfigure.common.exception.MSBizNormalException;
import com.cfpamf.framework.autoconfigure.common.exception.MSException;
import com.cfpamf.ms.bizconfig.facade.vo.BranchAreaVo;
import com.cfpamf.ms.bizconfig.facade.vo.LoanOrgVo;
import com.cfpamf.ms.bizconfig.facade.vo.UserVo;
import com.cfpamf.ms.bms.facade.constants.BmsOrgLevelEnum;
import com.cfpamf.ms.bms.facade.vo.OrganizationVO;
import com.cfpamf.ms.customer.facade.request.UserRecommendQueryReq;
import com.cfpamf.ms.customer.facade.request.credit.QueryCreditLimitReq;
import com.cfpamf.ms.customer.facade.request.cust.CustBaseQueryByIdNoRequest;
import com.cfpamf.ms.customer.facade.request.user.QueryPushInfoReq;
import com.cfpamf.ms.customer.facade.request.user.QueryUserBaseInfoReq;
import com.cfpamf.ms.customer.facade.request.user.QueryUserInfoByRecommendReq;
import com.cfpamf.ms.customer.facade.vo.*;
import com.cfpamf.ms.customer.facade.vo.user.UserBaseInfoVo;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.cfpamf.ms.mallorder.common.util.ExternalApiUtil;
import com.cfpamf.ms.mallorder.controller.fegin.facade.CustomerFacade;
import com.cfpamf.ms.mallorder.controller.fegin.facade.CustomerServiceFeign;
import com.cfpamf.ms.mallorder.integration.facade.*;
import com.cfpamf.ms.mallorder.vo.custVO.SimpleUserRecommendVo;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.exception.MallException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 客户中心交互
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/25 10:52
 */
@Slf4j
@Component
public class CustomerIntegration {

    static Map<String, LoanOrgVo> LOAN_COMPANY_CACHE = new HashMap();

    @Resource
    private CustomerServiceFeign custCreditLimitFacade;

    @Resource
    private BmsOrganizationFacade bmsOrganizationFacade;

    @Resource
    private UserFacade userFacade;

    @Resource
    private BizUserFacade bizUserFacade;

    @Resource
    private BranchFacade branchFacade;

    @Autowired
    private LoanOrgFacade loanOrgFacade;

    @Autowired
    private CustomerFacade customerFacade;

    /**
     * 根据用户编号获取用户及客户信息
     *
     * @param userNo
     * @return
     */
    public UserBaseInfoVo userBaseInfo(String userNo) {
        QueryUserBaseInfoReq var1 = new QueryUserBaseInfoReq();
        var1.setUserNo(userNo);
        UserBaseInfoVo result = null;
        try {
            result = ExternalApiUtil.callResultApi(() -> userFacade.baseInfoByUserNo(var1),var1,"/user/baseInfoByUserNo","获取用户基本信息");
        } catch (Exception e) {
            throw new MallException(
                    "客户中心-服务异常：" + userNo, ErrorCodeEnum.C.CALL_EXCEPTION.getCode(), e);
        }

        if (result == null) {
            throw new BusinessException(ErrorCodeEnum.C.RESULT_INVALID.getCode(),
                    "客户中心-用户不存在：" + userNo);
        }

        return result;
    }

   public CustBaseInfoVo baseInfoByIdNo(String idNo) {
        if (StringUtils.isBlank(idNo)) {
            return null;
        }
        CustBaseQueryByIdNoRequest request = new CustBaseQueryByIdNoRequest();
        request.setIdNo(idNo);
       return ExternalApiUtil.callResultApi(() -> customerFacade.baseInfoByIdNo(request), request,
               "/cust/baseInfoByIdNo", "根据身份证查询客户信息");
    }

    public Boolean isBranchLevel(String orgCode) {

        if (StringUtils.isBlank(orgCode)) {
            return Boolean.FALSE;
        }

        BmsOrgLevelEnum levelEnum = BmsOrgLevelEnum.Unknown;

        try {
            Result<OrganizationVO> orgResult = bmsOrganizationFacade.getOrganizationByOrgCode(null, orgCode);

            if (orgResult == null
                    || !orgResult.isSuccess()
                    || orgResult.getData() == null
                    || orgResult.getData().getOrgId() == null) {
                return Boolean.FALSE;
            }

            Integer orgId = orgResult.getData().getOrgId();
            levelEnum = getOrgLevelByOrgId(orgId);
        } catch (Exception e) {
            log.warn("BMS校验是否为分支异常 orgCode:{}", orgCode, e);
            return Boolean.FALSE;
        }

        return levelEnum == BmsOrgLevelEnum.Branch;
    }

    public BmsOrgLevelEnum getOrgLevelByOrgId(Integer orgId) {
        Result<BmsOrgLevelEnum> result = bmsOrganizationFacade.getOrgLevelByOrgId(null, orgId);
        if (result == null) {
            throw new BusinessException(
                    ErrorCodeEnum.C.CALL_EXCEPTION.getCode(), "调用bms获取机构级别异常");
        } else if (!result.isSuccess()) {
            throw new BusinessException(
                    ErrorCodeEnum.C.RESULT_INVALID.getCode(), "BMS:" + result.getErrorMsg());
        }
        return result.getData();
    }

    /**
     * 根据分支查询小贷公司<br />
     * 异常或无数据时返回空对象
     *
     * @param branchCode
     * @return
     */
    public LoanOrgVo getLoanCompany(String branchCode) {
        if (LOAN_COMPANY_CACHE.containsKey(branchCode)) {
            return LOAN_COMPANY_CACHE.get(branchCode);
        }
        CommonResult<LoanOrgVo> result = null;
        try {
            result = loanOrgFacade.getLoanOrgInfo(branchCode, "02");
        } catch (Exception e) {
            log.warn("调用bizconfig获取小贷公司异常", e);
            return new LoanOrgVo();
        }
        if (result == null) {
            return new LoanOrgVo();
        }
        if (result.getData() == null) {
            return new LoanOrgVo();
        }
        LOAN_COMPANY_CACHE.put(branchCode, result.getData());

        return LOAN_COMPANY_CACHE.get(branchCode);
    }

    public List<CustCreditLimitVo> queryLimitList(String loanCustId) {

        QueryCreditLimitReq var1 = new QueryCreditLimitReq();
        var1.setLoanCustId(loanCustId);
        Result<CustCreditLimitListVo> result = null;
        try {
            result = custCreditLimitFacade.queryLimitList(var1);
        } catch (Exception e) {
            log.warn("调用客户中获取客户支付方式异常/cust/credit/queryLimitList ：", e);
        }

        if (result == null || !result.isSuccess() || result.getData() == null || result.getData().getList() == null) {
            return  Collections.emptyList();
        }

        return result.getData().getList();
    }

    /**
     * 根据 userNo 查询用户基本信息
     *
     * @param userNo 用户编号
     * @return 用户基本信息
     */
    public UserBaseInfoVo baseInfoByUserNo(String userNo) {
        Result<UserBaseInfoVo> result = null;
        try {
            QueryUserBaseInfoReq userReq = new QueryUserBaseInfoReq();
            userReq.setUserNo(userNo);
            log.info("根据userNo查询客户详情,入参userNo:{}",userNo);
            result = custCreditLimitFacade.baseInfoByUserNo(userReq);
        } catch (Exception ex) {
            log.error("根据userNo查询客户详情出现未知异常,userNo:{}",userNo,ex);
            throw new MSException(String.valueOf(ErrorCodeEnum.C.CALL_EXCEPTION),"根据userNo查询客户详情调用服务异常");
        }
        if (Objects.isNull(result)) {
            throw new MSBizNormalException(String.valueOf(ErrorCodeEnum.C.RESULT_INVALID.getCode()), "根据userNo查询客户详情返回结果为空");
        }
        if(!result.isSuccess() && "001001003".equals(result.getCode())){
            log.info("未查到userNo对应的客户详情:{}",userNo);
            return null;
        }
        if(!result.isSuccess()){
            throw new MSBizNormalException(String.valueOf(ErrorCodeEnum.C.RESULT_INVALID.getCode()), "根据userNo查询客户详情返回结果为空");
        }
        log.info("根据userNo查询客户详情,返回结果:{}",JSONObject.toJSONString(result.getData()));
        return result.getData();
    }

    public CustInfoVo queryByCustNo(String custNo) {
        Result<CustInfoVo> result = null;
        try {
            result = custCreditLimitFacade.queryByCustNo(custNo);
        } catch (Exception ex) {
            log.error("根据custNo查询实名客户详情出现未知异常,custNo:{}",custNo,ex);
            throw new MSException(String.valueOf(ErrorCodeEnum.C.CALL_EXCEPTION),"根据custNo查询实名客户详情调用服务异常");
        }
        if (Objects.isNull(result) || !result.isSuccess() || Objects.isNull(result.getData())) {
            throw new MSBizNormalException(String.valueOf(ErrorCodeEnum.C.RESULT_INVALID.getCode()), "根据custNo查询实名客户详情返回结果为空");
        }
        return result.getData();
    }

    public UserVo getUserInfoByUserCode(String userCode, String orgCode) {
        Result<UserVo> result = null;
        try {
            result = bizUserFacade.getUserInfoContainLeaveByUserCdeV2(userCode, orgCode);
        } catch (Exception e) {
            log.error("bizconfig/user/getUserInfoByUserCode查询客户经理信息异常", e);
            return new UserVo();
        }
        if (result == null || !result.isSuccess() || result.getData() == null) {

            if (userCode == null || org.apache.commons.lang3.StringUtils.isNotBlank(orgCode)) {
                UserVo userVo = new UserVo();
                // 无管护有分支查分支名称
                if (userCode == null && org.apache.commons.lang3.StringUtils.isNotBlank(orgCode)) {
                    BranchAreaVo branchInfo = getBaseBranchInfo(orgCode);
                    userVo.setBranchName(branchInfo.getBranchName());
                }

                return userVo;
            }

            log.warn("bizconfig/user/getUserInfoByUserCode查询客户经理信息为空:" + JSON.toJSONString(result));

            return new UserVo();
        }
        return result.getData();
    }

    public BranchAreaVo getBaseBranchInfo(String branchCode) {
        CommonResult<BranchAreaVo> result = null;
        try {
            result = branchFacade.getBaseBranchInfo(branchCode);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (result == null || !result.isSuccess()) {
            return new BranchAreaVo();
        }
        return result.getData();
    }

    public UserInfoVo queryPlatformShareCodeRecommendUser(String shareCode) {
        if (StringUtils.isBlank(shareCode)) {
            return null;
        }
        Result<UserInfoVo> result = null;
        try {
            QueryUserInfoByRecommendReq userReq = new QueryUserInfoByRecommendReq();
            userReq.setRecommendCode(shareCode);
            log.info("根据商品的平台分享码查询推荐客户购买的用户信息,shareCode:{}",shareCode);
            result = userFacade.queryUserInfoByRecommend(userReq);
        } catch (Exception ex) {
            log.error("根据商品的平台分享码查询客户详情出现未知异常,shareCode:{}",shareCode,ex);
            throw new MSException(String.valueOf(ErrorCodeEnum.C.CALL_EXCEPTION),"根据商品的平台分享码查询客户详情调用服务异常");
        }
        if (Objects.isNull(result)) {
            throw new MSBizNormalException(String.valueOf(ErrorCodeEnum.C.RESULT_INVALID.getCode()), "根据商品的平台分享码查询客户详情返回结果为空");
        }
        if(!result.isSuccess() && "001001050".equals(result.getCode())){
            log.info("未查到该商品的平台分享码对应的推荐客户购买的用户信息:{}",shareCode);
            return null;
        }
        if(!result.isSuccess()){
            throw new MSBizNormalException(String.valueOf(ErrorCodeEnum.C.RESULT_INVALID.getCode()), "根据商品的平台分享码查询客户详情返回结果为空");
        }
        log.info("根据平台分享码查询推荐客户购买的用户信息,返回结果:{}",JSONObject.toJSONString(result.getData()));
        return result.getData();
    }

    public SimpleUserRecommendVo queryRegisterRecommender(String userNo) {
        if (StringUtils.isBlank(userNo)) {
            return null;
        }
        Result<List<SimpleUserRecommendVo>> result = null;
        try {
            UserRecommendQueryReq userReq = new UserRecommendQueryReq();
            userReq.setUserNo(userNo);
            log.info("根据userNo查询客户的推荐注册人详情,userNo:{}",userNo);
            result = userFacade.queryRegisterRecommender(userReq);
        } catch (Exception ex) {
            log.error("根据userNo查询客户的推荐注册人详情出现未知异常,userNo:{}",userNo,ex);
            throw new MSException(String.valueOf(ErrorCodeEnum.C.CALL_EXCEPTION),"根据userNo查询客户的推荐注册人详情调用服务异常");
        }
        if (Objects.isNull(result) || !result.isSuccess()) {
            throw new MSBizNormalException(String.valueOf(ErrorCodeEnum.C.RESULT_INVALID.getCode()), "根据userNo查询客户的推荐注册人详情返回结果为空");
        }
        if (CollectionUtils.isEmpty(result.getData())) {
            return null;
        }
        log.info("根据userNo查询客户的推荐注册人详情,返回结果:{}",JSONObject.toJSONString(result.getData().get(0)));
        return result.getData().get(0);
    }

    /**
     * 根据用户编码获取用户信息
     * @param userNoList 用户编码
     * @param throwException 是否抛异常
     * @return key 为用户编码, value 为用户信息
     */
    public Map<String, UserInfoVo> queryUserInfoByUserNoList(List<String> userNoList, boolean throwException) {
        if (CollectionUtils.isEmpty(userNoList)) {
            log.info("根据用户编码批量获取用户信息, 用户编码集合为空, return 空map");
            return new HashMap<>();
        }
        QueryPushInfoReq req = new QueryPushInfoReq();
        req.setUserNos(userNoList.stream().distinct().collect(Collectors.toList()));
        try {
            Result<List<UserInfoVo>> result = custCreditLimitFacade.queryPushInfo(req);
            if (Objects.isNull(result) || !result.isSuccess()) {
                String msg = Objects.isNull(result) ? "系统开小差了" : StringUtils.isEmpty(result.getErrorMsg()) ? result.getMessage() : result.getErrorMsg();
                if (throwException) {
                    log.warn("根据用户编码批量获取用户信息异常, 查询参数为:{}, 异常原因为:{}", JSONObject.toJSONString(req), msg);
                    throw new MSBizNormalException(String.valueOf(ErrorCodeEnum.C.CALL_EXCEPTION), "根据用户编码批量获取用户信息调用服务异常, 异常原因:" + msg);
                } else {
                    log.warn("根据用户编码批量获取用户信息异常, 查询参数为:{}, 异常原因为:{}", JSONObject.toJSONString(req), msg);
                    // 获取用户信息失败，返回空map
                    return new HashMap<>();
                }
            }
            log.info("根据用户编码批量获取用户信息, 入参:{},返回结果:{}", JSONObject.toJSONString(req), JSONObject.toJSONString(result.getData()));
            return result.getData().stream().collect(Collectors.toMap(UserInfoVo::getUserNo, Function.identity(), (o, n) -> n));
        } catch (Exception e) {
            log.warn("根据用户编码批量获取用户信息异常, 查询参数为:{}, 异常原因为:{}", JSONObject.toJSONString(req), e.getMessage(), e);
            if (throwException) {
                throw new MSBizNormalException(String.valueOf(ErrorCodeEnum.C.CALL_EXCEPTION), "根据用户编码批量获取用户信息调用服务异常, 异常原因:" + e.getMessage());
            } else {
                // 获取用户信息失败，返回空map
                return new HashMap<>();
            }
        }
    }
}
