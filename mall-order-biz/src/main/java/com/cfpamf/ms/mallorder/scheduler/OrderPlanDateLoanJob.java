package com.cfpamf.ms.mallorder.scheduler;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.mallorder.common.config.OrderPlanDateLoanConfig;
import com.cfpamf.ms.mallorder.common.enums.OrderStatusEnum;
import com.cfpamf.ms.mallorder.common.enums.TaskQueueBizTypeEnum;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.model.OrderReturnModel;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderPresellPO;
import com.cfpamf.ms.mallorder.po.TaskQueuePO;
import com.cfpamf.ms.mallorder.service.IOrderService;
import com.cfpamf.ms.mallorder.service.ITaskQueueService;
import com.cfpamf.ms.mallorder.v2.service.OrderPresellService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Classname OrderPlanDateLoanJob
 * @Description 计划放款日执行放款job-包含下单后N天起息
 * <AUTHOR>
 * @Date 2022/2/15 19:37
 * @Version 1.0
 **/
@Component
@Slf4j
public class OrderPlanDateLoanJob {
    @Autowired
    private OrderModel orderModel;

    @Autowired
    private ITaskQueueService taskQueueService;
    @Autowired
    private OrderReturnModel orderReturnModel;
    @Autowired
    private IOrderService iOrderService;

    @Autowired
    private OrderPresellService orderPresellService;

    @Autowired
    private OrderPlanDateLoanConfig orderPlanDateLoanConfig;

    @XxlJob("OrderPlanDateLoanJob")
    public ReturnT<String> execute(String s) throws Exception {

        String dateFormat = DateUtil.format(new Date(), DateUtil.CN_LONG_FORMAT);
        log.info("START OrderPlanDateLoanJob systemTime:{} eventTime:{}", new Date(), dateFormat);
        // 查询定时任务，进行发起补偿
        //执行条数限制，五分钟执行一次，每次50条
        String limit = XxlJobHelper.getJobParam();

//        String limit = StringUtils.isEmpty(orderPlanDateLoanConfig.perSize) ? "50" : orderPlanDateLoanConfig.perSize;
        List<TaskQueuePO> taskQueuePOS = taskQueueService.listTodoTaskLimit(
                Arrays.asList(TaskQueueBizTypeEnum.PLAN_DATE_LOAN.getValue(), TaskQueueBizTypeEnum.N_DAYS_AFTER_PAY.getValue())
                , dateFormat, limit);
        int count = 0;

        for (TaskQueuePO taskQueue : taskQueuePOS) {
            try {
                // Andy.预售
                OrderPresellPO orderPresellPO =
                    orderPresellService.queryBalanceInfoByOrderSn(String.valueOf(taskQueue.getBizId()));
                String orderSn = ObjectUtils.isNotEmpty(orderPresellPO) ? orderPresellPO.getOrderSn()
                    : String.valueOf(taskQueue.getBizId());
                log.info("OrderPlanDateLoanJob->orderSn：{}", orderSn);
                OrderPO orderPODb = iOrderService.getByOrderSn(taskQueue.getBizId().toString());
                if (Objects.isNull(orderPODb)) {
                    log.error("执行OrderPlanDateLoanJob时订单信息为空:{}", taskQueue.getBizId());
                    continue;
                }

                // 交易关闭、订单取消则删除放款任务后跳过
                if (orderPODb.getOrderState().equals(OrderStatusEnum.TRADE_CLOSE.getValue())
                    || orderPODb.getOrderState().equals(OrderStatusEnum.CANCELED.getValue())) {
                    log.warn("OrderLoanLendingJob 订单状态为:" + orderPODb.getOrderState() + "删除任务");
                    taskQueueService.taskSuccess(taskQueue);
                    continue;
                }
                // 订单处于待付款、付款中，则本此跳过
                if (orderPODb.getOrderState().equals(OrderStatusEnum.DEAL_PAY.getValue())
                    || orderPODb.getOrderState().equals(OrderStatusEnum.WAIT_PAY.getValue())) {
                    log.error("执行OrderPlanDateLoanJob时订单状态异常:{}", taskQueue.getBizId());
                    continue;
                }

                // 查询订单是否存在退款中退款单，存在则本此执行跳过
                // 2025-05 修改为排除换货退款 --wangdong
                boolean haveReturn = orderReturnModel.whetherHasReturningProductExceptExchange(orderPODb.getOrderSn());
                if (haveReturn) {
                    log.warn("执行OrderPlanDateLoanJob时订单存在在途非换货退款单:{}", taskQueue.getBizId());
                    continue;
                }
                Result<Void> voidResult = orderModel.doLoanOperate(String.valueOf(taskQueue.getBizId()),
                    OrderConst.OPT_USER_ID, ObjectUtils.isNotEmpty(orderPresellPO) ? orderPresellPO.getPayNo() : null);

                if (voidResult.isSuccess()) {
                    orderModel.loanPaySuccess(orderPODb, OrderConst.ADMIN_ROLE, OrderConst.USER_ID_SYSTEM,
                        OrderConst.USER_NAME_SYSTEM);
                } else {
                    // 非系统异常导致放款失败，记录日志，人工处理
                    orderModel.loanPayFail(orderPODb, voidResult.getErrorMsg());
                }
                taskQueueService.taskSuccess(taskQueue);
                count++;
            } catch (Exception ex) {
                log.error("OrderLoanLendingJob()", ex);
                taskQueueService.taskFail(taskQueue, "用呗放款", ex.getMessage());
            }
        }

        log.info("=============== FINISHED OrderLoanLendingJob, 应处理:{}, 实处理:{} ", taskQueuePOS.size(), count);

        return new ReturnT("SUCCESS");
    }

}
