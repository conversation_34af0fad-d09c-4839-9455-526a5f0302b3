package com.cfpamf.ms.mallorder.service.payment;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.cfpamf.ms.mall.liquidate.request.ExecuteReq;

@Service
public class LoanRefundHandleServiceImpl implements IRefundHandleService {

    @Autowired
    private IRefundSettlementService refundSettlementService;

    @Override
    public <T> void verifyElectronicAccountBalance(RetundHandleType retundHandleType, T t) {
        refundSettlementService.verifyLoanElectronicAccountBalance((ExecuteReq)t);
    }

    @Override
    public <T> Boolean issuedBill(RetundHandleType retundHandleType, T t) {
        return refundSettlementService.loanIssuedBill((ExecuteReq)t);
    }

}
