package com.cfpamf.ms.mallorder.integration.cashier.request;


import com.alibaba.fastjson.annotation.JSONType;
import com.baomidou.mybatisplus.core.enums.IEnum;

/**
 * 活动起息方式
 *
 * @Author: maoliang
 * @Date: 2019/12/24
 */
@JSONType(serializeEnumAsJavaBean = true)
public enum InterestType implements IEnum<Integer> {
    TRADE_SUCCESS(1, "交易成功后起息"),
    CUSTOMER_CONFIRM(2, "下单确认后起息"),
    PLAN_DATE_LOAN(3, "计划放款日起息"),
    N_DAYS_AFTER_PAY(4, "下单后N天起息");
    private Integer value;
    private String desc;

    InterestType(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static InterestType valueOf(int value){
        for(InterestType ps : InterestType.values()){
            if (value == ps.value){
                return ps;
            }
        }
        return null;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }

    public String getDesc() {
        return this.desc;
    }
}
