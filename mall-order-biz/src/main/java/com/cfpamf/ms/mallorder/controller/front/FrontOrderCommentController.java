package com.cfpamf.ms.mallorder.controller.front;

import com.cfpamf.framework.autoconfigure.ZhnxServiceException;
import com.cfpamf.framework.autoconfigure.redis.lock.DistributeLock;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.dto.OrderCommentAddDTO;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.slodon.bbc.core.controller.BaseController;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import com.slodon.bbc.core.util.UserUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.concurrent.TimeUnit;

/**
 * front-订单列表
 */
@Api(tags = "front-待评价")
@Slf4j
@RestController
@RequestMapping("front/orderComment")
public class FrontOrderCommentController extends BaseController {

    @Autowired
    private DistributeLock distributeLock;

    @Resource
    private OrderModel orderModel;

    @ApiOperation("发布评价")
    @PostMapping("addOrderComment")
    public JsonResult addOrderComment(HttpServletRequest request, @RequestBody @Valid OrderCommentAddDTO orderCommentAddDTO) throws Exception {
        Member member = UserUtil.getUser(request, Member.class);
        String lockKey = "orderComment:orderModel:" + orderCommentAddDTO.getOrderSn();
        try {
            distributeLock.lockAndProcess(lockKey, 0, 10, TimeUnit.SECONDS,
                    () -> {
                        try {
                            orderModel.addOrderComment(orderCommentAddDTO, member);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        return null;
                    });
        } catch (ZhnxServiceException ex) {
            throw new BusinessException("点击过快，稍等一下~");
        }
        return SldResponse.success("评价成功");
    }


}
