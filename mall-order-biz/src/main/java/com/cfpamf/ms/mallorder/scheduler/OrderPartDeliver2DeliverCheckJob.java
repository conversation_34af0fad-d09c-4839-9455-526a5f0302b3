package com.cfpamf.ms.mallorder.scheduler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cfpamf.ms.mallorder.common.enums.OrderProductDeliveryEnum;
import com.cfpamf.ms.mallorder.common.enums.OrderReturnStatus;
import com.cfpamf.ms.mallorder.common.enums.OrderStatusEnum;
import com.cfpamf.ms.mallorder.common.enums.ReturnTypeEnum;
import com.cfpamf.ms.mallorder.constant.ExchangeOrderConst;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.ExchangeOrderDetailDTO;
import com.cfpamf.ms.mallorder.mapper.OrderExchangeMapper;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.service.IOrderAfterService;
import com.cfpamf.ms.mallorder.service.IOrderProductService;
import com.cfpamf.ms.mallorder.service.IOrderReturnService;
import com.cfpamf.ms.mallorder.service.IOrderService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 *  Nmall-5449
 * 【部分发货】定时任务：销售订单交易完成前，发生部分发货（状态25），
 * 需要增加定时任务，针对25状态的销售订单，查询是否关联了【部分订单发生了退货退款订单/退款订单】，
 * 且【部分退货退款订单/退款订单】已完成退款时，应该检查【整单金额-退款金额（考虑可能存在利息）】≤部分发货金额，
 * 则需要更新【销售订单】状态为【待签收】
 *
 * 每天 5 点：0 0 5 * * ?
 */
@Component
@Slf4j
public class OrderPartDeliver2DeliverCheckJob {

    @Resource
    private IOrderService orderService;
    @Resource
    private IOrderProductService orderProductService;
    @Resource
    private IOrderAfterService orderAfterService;
    @Resource
    private IOrderReturnService orderReturnService;

    @Resource
    private OrderExchangeMapper orderExchangeMapper;

    @XxlJob("OrderPartDeliver2DeliverCheckJob")
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobHelper.log("=============== START OrderPartDeliver2DeliverCheckJob , DATE :{}", LocalDateTime.now());
        // 1.查出所有的部分发货订单
        LambdaQueryWrapper<OrderPO> orderQueryWrapper = Wrappers.lambdaQuery(OrderPO.class);
        orderQueryWrapper.eq(OrderPO::getOrderState, OrderStatusEnum.PART_DELIVERED.getValue())
                .eq(OrderPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
        List<OrderPO> orderList = orderService.list(orderQueryWrapper);
        XxlJobHelper.log("部分发货的订单数量为：{}", orderList.size());
        if (!CollectionUtils.isEmpty(orderList)) {
            for (OrderPO orderPO : orderList) {
                // 2.查询订单的未发货商品行是否全部退完
                LambdaQueryWrapper<OrderProductPO> productQueryWrapper = Wrappers.lambdaQuery(OrderProductPO.class);
                productQueryWrapper.eq(OrderProductPO::getOrderSn, orderPO.getOrderSn())
                        .in(OrderProductPO::getDeliveryState, OrderProductDeliveryEnum.getWaitDeliveryAndOutboundState())
                        .eq(OrderProductPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
                List<OrderProductPO> orderProductList = orderProductService.list(productQueryWrapper);
                // 3.订单未发货商品行已全退，则将订单这种为已发货
                if (this.productAllReturnCheck(orderProductList)) {
                    orderPO.setDeliverTime(new Date());
                    orderService.orderAllDeliveryItems(orderPO);
                    XxlJobHelper.log("定时任务【OrderPartDeliver2DeliverCheckJob】将订单【{}】由部分发货处理为已发货",
                            orderPO.getOrderSn());
                }
            }
        }

        XxlJobHelper.log("=============== END OrderPartDeliver2DeliverCheckJob , DATE :{}", LocalDateTime.now());
        return ReturnT.SUCCESS;
    }

    /**
     * 判断商品是否全部退完
     * 1、没有售后，--》未全退
     * 2、有售后，--》全退 (购买数量 - 已发货数量 - 换货数量 - - 仅退款数量 == 0 )
     *
     *
     * @param orderProductList  商品列表
     * @return                  true/false
     */
    private boolean productAllReturnCheck(List<OrderProductPO> orderProductList) {
        for (OrderProductPO orderProductPO : orderProductList) {
            // 查询商品的退款单
            LambdaQueryWrapper<OrderAfterPO> orderAfterQueryWrapper = Wrappers.lambdaQuery(OrderAfterPO.class);
            orderAfterQueryWrapper.eq(OrderAfterPO::getOrderProductId, orderProductPO.getOrderProductId())
                    .eq(OrderAfterPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                    .select(OrderAfterPO::getAfsSn);
            List<OrderAfterPO> orderAfterPOs = orderAfterService.list(orderAfterQueryWrapper);
            // 商品没有退款
            if (CollectionUtils.isEmpty(orderAfterPOs)) {
                return false;
            }

            //查询换货的数量
            ExchangeOrderDetailDTO exchangeOrderDetailDTO = new ExchangeOrderDetailDTO();
            exchangeOrderDetailDTO.setOrderProductId(orderProductPO.getOrderProductId());
            List<ExchangeOrderDetailDTO> exchangeOrderDetailDTOList = orderExchangeMapper.getExchangeOrderList(exchangeOrderDetailDTO);
            //List<ExchangeOrderDetailDTO> exchangeList = exchangeOrderDetailDTOList.stream().filter( x->x.getExchangeOrderState() != 0).collect(Collectors.toList());
            List<ExchangeOrderDetailDTO> exchangeList = exchangeOrderDetailDTOList.stream().filter(
                    x -> x.getExchangeOrderState() == ExchangeOrderConst.EXCHANGE_ORDER_STATE_AGREE
                            || x.getExchangeOrderState() == ExchangeOrderConst.EXCHANGE_ORDER_STATE_FINISH
                            || x.getExchangeOrderState() == ExchangeOrderConst.EXCHANGE_ORDER_STATE_WAIT_AUDIT
            ).collect(Collectors.toList());
            int exchangeNum = exchangeList.stream().mapToInt(ExchangeOrderDetailDTO::getProductNum).sum();
            //商品数量 != 换货数量
            //商品可发货数量
            int deliveredNum = orderProductPO.getProductNum() - orderProductPO.getDeliveryNum() - exchangeNum;

            if(deliveredNum > 0) {
                List<String> afsSns = orderAfterPOs.stream().map(OrderAfterPO::getAfsSn).collect(Collectors.toList());
                LambdaQueryWrapper<OrderReturnPO> orderReturnQueryWrapper = Wrappers.lambdaQuery(OrderReturnPO.class);
                orderReturnQueryWrapper.in(OrderReturnPO::getAfsSn, afsSns)
                        .eq(OrderReturnPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                        .eq(OrderReturnPO::getState, OrderReturnStatus.REFUND_SUCCESS.getValue())
                        .in(OrderReturnPO::getReturnType, Arrays.asList(ReturnTypeEnum.REFUND.getValue(), ReturnTypeEnum.RETURN_AND_REFUND.getValue()));
                List<OrderReturnPO> orderReturnPOList = orderReturnService.list(orderReturnQueryWrapper);

                //售后数量
                if(CollectionUtils.isEmpty(orderReturnPOList)) {
                    return false;
                }
                int returnNum = orderReturnPOList.stream().mapToInt(OrderReturnPO::getReturnNum).sum();

                if(deliveredNum - returnNum > 0) {
                    return false;
                }
            }

        }
        return true;
    }

}
