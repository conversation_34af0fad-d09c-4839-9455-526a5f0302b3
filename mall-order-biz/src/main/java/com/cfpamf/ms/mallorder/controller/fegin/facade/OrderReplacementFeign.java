package com.cfpamf.ms.mallorder.controller.fegin.facade;

import java.util.List;
import javax.annotation.Resource;

import com.cfpamf.ms.mallorder.model.OrderReplacementModel;
import com.cfpamf.ms.mallorder.po.OrderReplacementPO;
import com.cfpamf.ms.mallorder.request.OrderReplacementExample;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@RestController
@Slf4j
public class OrderReplacementFeign {

    @Resource
    private OrderReplacementModel orderReplacementModel;

    /**
     * 根据replacementId获取订单换货表详情
     *
     * @param replacementId replacementId
     * @return
     */
    @GetMapping("/v1/feign/business/orderReplacement/get")
    public OrderReplacementPO getOrderReplacementByReplacementId(@RequestParam("replacementId") Integer replacementId) {
        return orderReplacementModel.getOrderReplacementByReplacementId(replacementId);
    }

    /**
     * 获取条件获取订单换货表列表
     *
     * @param example 查询条件信息
     * @return
     */
    @PostMapping("/v1/feign/business/orderReplacement/getList")
    public List<OrderReplacementPO> getOrderReplacementList(@RequestBody OrderReplacementExample example) {
        return orderReplacementModel.getOrderReplacementList(example, example.getPager());
    }

    /**
     * 新增订单换货表
     *
     * @param orderReplacementPO
     * @return
     */
    @PostMapping("/v1/feign/business/orderReplacement/addOrderReplacement")
    public Integer saveOrderReplacement(@RequestBody OrderReplacementPO orderReplacementPO) {
        return orderReplacementModel.saveOrderReplacement(orderReplacementPO);
    }

    /**
     * 根据replacementId更新订单换货表
     *
     * @param orderReplacementPO
     * @return
     */
    @PostMapping("/v1/feign/business/orderReplacement/updateOrderReplacement")
    public Integer updateOrderReplacement(@RequestBody OrderReplacementPO orderReplacementPO) {
        return orderReplacementModel.updateOrderReplacement(orderReplacementPO);
    }

    /**
     * 根据replacementId删除订单换货表
     *
     * @param replacementId replacementId
     * @return
     */
    @PostMapping("/v1/feign/business/orderReplacement/deleteOrderReplacement")
    public Integer deleteOrderReplacement(@RequestParam("replacementId") Integer replacementId) {
        return orderReplacementModel.deleteOrderReplacement(replacementId);
    }

    /**
     * 系统自动完成换货单
     *
     * @return
     */
    @GetMapping("/v1/feign/business/orderReplacement/jobSystemFinishReplacementOrder")
    public boolean jobSystemFinishReplacementOrder() {
        return orderReplacementModel.jobSystemFinishReplacementOrder();
    }
}