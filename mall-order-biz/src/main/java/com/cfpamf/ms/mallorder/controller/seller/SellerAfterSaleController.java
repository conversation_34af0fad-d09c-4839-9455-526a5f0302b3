package com.cfpamf.ms.mallorder.controller.seller;

import com.alibaba.fastjson.JSON;
import com.cfpamf.cmis.common.utils.DateUtils;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.framework.autoconfigure.redis.lock.DistributeLock;
import com.cfpamf.mallpayment.facade.vo.CDMallRefundTryResultVO;
import com.cfpamf.ms.mall.filecenter.domain.dto.FileDTO;
import com.cfpamf.ms.mallorder.common.aspect.ThreadLocalRemoveTag;
import com.cfpamf.ms.mallorder.common.enums.CommonEnum;
import com.cfpamf.ms.mallorder.common.enums.OperationRoleEnum;
import com.cfpamf.ms.mallorder.common.enums.RefundType;
import com.cfpamf.ms.mallorder.common.util.PoiUtils;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.OrderExchangeAuditDTO;
import com.cfpamf.ms.mallorder.dto.UserDTO;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.model.*;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.req.exchange.OrderExchangeListRequest;
import com.cfpamf.ms.mallorder.req.exchange.OrderExchangeRequest;
import com.cfpamf.ms.mallorder.req.exportreq.OrderRefundExportRequest;
import com.cfpamf.ms.mallorder.req.seller.SellerAfterSaleAuditRequest;
import com.cfpamf.ms.mallorder.req.seller.SellerAfterSaleListRequest;
import com.cfpamf.ms.mallorder.request.ComplainExample;
import com.cfpamf.ms.mallorder.request.ComplainTalkExample;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.v2.common.lock.Lock;
import com.cfpamf.ms.mallorder.vo.*;
import com.cfpamf.ms.mallshop.api.StoreIsolateWhiteListFeignClient;
import com.cfpamf.ms.mallshop.enums.WhiteListEnum;
import com.cfpamf.ms.mallshop.resp.Vendor;
import com.slodon.bbc.core.constant.CommonConst;
import com.slodon.bbc.core.constant.ComplainConst;
import com.slodon.bbc.core.controller.BaseController;
import com.slodon.bbc.core.exception.MallException;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.PageVO;
import com.slodon.bbc.core.response.PagerInfo;
import com.slodon.bbc.core.response.SldResponse;
import com.slodon.bbc.core.util.AssertUtil;
import com.slodon.bbc.core.util.StringUtil;
import com.slodon.bbc.core.util.UserUtil;
import com.slodon.bbc.core.util.WebUtil;
import com.slodon.bbc.starter.mq.entity.VendorLogSendVO;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.cfpamf.ms.mallorder.common.constant.CommonConst.DEFAULT_PAGE_INDEX;
import static com.slodon.bbc.core.constant.StarterConfigConst.MQ_EXCHANGE_NAME;
import static com.slodon.bbc.core.constant.StarterConfigConst.MQ_QUEUE_NAME_SELLERLOG_MSG;

@Api(tags = "seller-售后管理")
@RestController
@RequestMapping("seller/after/sale")
@Slf4j
public class SellerAfterSaleController extends BaseController {

    @Resource
    private IOrderAfterService orderAfterService;
    @Resource
    private IOrderReturnService orderReturnService;
    @Resource
    private IOrderReturnTrackService orderReturnTrackService;

    @Resource
    private OrderReturnModel orderReturnModel;
    @Resource
    private OrderAfterServiceModel orderAfterServiceModel;
    @Resource
    private OrderProductModel orderProductModel;
    @Resource
    private ComplainModel complainModel;
    @Resource
    private ComplainTalkModel complainTalkModel;

    @Resource
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private DistributeLock distributeLock;
    
    @Autowired
    private Lock lock;

    @Resource
    private IOrderExchangeService orderExchangeService;

    @Resource
    private StoreIsolateWhiteListFeignClient storeIsolateWhiteListFeignClient;

    @Resource
    private IOrderExchangeDetailService orderExchangeDetailService;

    @Resource
    private IOrderAfterExcelDataExportService orderAfterExcelDataExportService;


    @ApiOperation("售后列表")
    @PostMapping("list")
    public JsonResult<PageVO<OrderReturnVOV2>> list(HttpServletRequest request, @RequestBody SellerAfterSaleListRequest sellerAfterSaleListRequest) {
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        int pageSize = Objects.isNull(sellerAfterSaleListRequest.getPageSize()) || StringUtil.isNullOrZero(Integer.parseInt(sellerAfterSaleListRequest.getPageSize())) ?
                CommonConst.DEFAULT_PAGE_SIZE : Integer.parseInt(sellerAfterSaleListRequest.getPageSize());
        int pageIndex = Objects.isNull(sellerAfterSaleListRequest.getCurrent()) || StringUtil.isNullOrZero(Integer.parseInt(sellerAfterSaleListRequest.getCurrent())) ?
                DEFAULT_PAGE_INDEX : Integer.parseInt(sellerAfterSaleListRequest.getCurrent());
        PagerInfo pager = new PagerInfo(pageSize, pageIndex);
        return SldResponse.success(orderAfterService.sellerAfterSaleList(vendor, pager, sellerAfterSaleListRequest));
    }

    @ApiOperation("售后详情")
    @GetMapping("detail")
    public JsonResult<OrderReturnDetailVO> detail(HttpServletRequest request, @ApiParam(value = "退订单号", required = true) @RequestParam("afsSn") String afsSn,
                                                  @ApiParam(value = "是否配销操作") @RequestParam(value = "distribution",required = false) Integer distribution) {

        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        return SldResponse.success(orderAfterService.sellerAfterSaleDetail(afsSn, vendor,distribution));
    }

    @ApiOperation(value = "审核退款申请", tags = "CORE")
    @PostMapping("audit")
    public JsonResult audit(HttpServletRequest request, @RequestBody SellerAfterSaleAuditRequest auditRequest) {
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        log.info("商家审核：vendor:{}", JSON.toJSONString(vendor));
        String lockKey = String.format("SellerAfterSaleController:audit:%s", auditRequest.getAfsSn());
        String result = lock.tryLockExecuteFunction(lockKey, 0, 10, TimeUnit.SECONDS,
                () -> orderReturnModel.afsStoreAudit(vendor, auditRequest.getAfsSn(), auditRequest.getIsPass(),
                        auditRequest.getRemark(), auditRequest.getStoreAddressId(), auditRequest.getChannel(),
                        auditRequest.getDistribution(), auditRequest.getRefundPunishAmount(), OperationRoleEnum.STORE,null));
        if (!OrderConst.RESULT_CODE_SUCCESS.equals(result)) {
            return new JsonResult(255, result);
        }
        //操作行为
        String opt = "审核退款申请[审核退款成功][售后单号：" + auditRequest.getAfsSn() + "]";
        VendorLogSendVO vendorLogSendVO = new VendorLogSendVO(vendor.getVendorId(), vendor.getVendorName(),
                request.getRequestURI(), opt, WebUtil.getRealIp(request));
        try {
            rabbitTemplate.convertAndSend(MQ_EXCHANGE_NAME, MQ_QUEUE_NAME_SELLERLOG_MSG, vendorLogSendVO);
        } catch (Exception e) {
            log.info("发消息异常捕获,{}", JSON.toJSONString(vendorLogSendVO), e);
        }

        return SldResponse.success("审核成功");
    }

    @ApiOperation(value = "售后风险提示")
    @GetMapping("riskMessage")
    public JsonResult<OrderReturnRiskMessageVO> riskMessage(HttpServletRequest request, @RequestParam String afsSn,
                                                            @RequestParam Boolean isPass) {
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        log.info("riskMessage with vendor:{}", JSON.toJSONString(vendor));

        // 如果是售后拒绝，不需要进行风险处理
        if (Objects.nonNull(isPass) && !isPass) {
            return SldResponse.success(new OrderReturnRiskMessageVO());
        }

        return SldResponse.success(orderAfterService.riskMessage(afsSn));
    }

    @ApiOperation(value = "ERP-发货单拦截接口")
    @GetMapping("erpDeliveryIntercept")
    public JsonResult<String> erpDeliveryIntercept (HttpServletRequest request, @RequestParam String afsSn) {
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        UserDTO userDTO = new UserDTO(vendor);
        log.info("erpDeliveryIntercept ERP-发货单拦截接口, 操作用户信息: {}", JSON.toJSONString(userDTO));
        return SldResponse.fail("暂不支持物流拦截");
        //return SldResponse.success(orderAfterService.erpDeliveryIntercept(afsSn, userDTO));
    }

    @ApiOperation(value = "确认收货", tags = "CORE")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "afsSn", value = "售后单号", required = true, paramType = "query"),
            @ApiImplicitParam(name = "isReceive", value = "是否收货：true-收货，false-拒收", required = true, paramType = "query"),
            @ApiImplicitParam(name = "remark", value = "备注(拒绝必填)", paramType = "query"),
            @ApiImplicitParam(name = "channel", value = "渠道", paramType = "query"),
            @ApiImplicitParam(name = "distribution", value = "是否配销订单 0-否 1-是", paramType = "query"),
            @ApiImplicitParam(name = "refundPunish", value = "退款扣罚金额", paramType = "query"),
    })
    @PostMapping("confirmReceive")
    public JsonResult confirmReceive(HttpServletRequest request, String afsSn, boolean isReceive,
                                     String remark, String channel,Integer distribution, BigDecimal refundPunish) {
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        String result = orderReturnModel.afsStoreReceive(vendor, afsSn, isReceive, remark, channel,distribution,
                refundPunish, OperationRoleEnum.STORE, null);
        if (!OrderConst.RESULT_CODE_SUCCESS.equals(result)){
            return new JsonResult(255, result);
        }
        //操作行为
        String opt = "确认收货[审核成功][售后单号：" + afsSn + "]";
        VendorLogSendVO vendorLogSendVO = new VendorLogSendVO(vendor.getVendorId(), vendor.getVendorName(), request.getRequestURI(), opt, WebUtil.getRealIp(request));
        try {
            rabbitTemplate.convertAndSend(MQ_EXCHANGE_NAME, MQ_QUEUE_NAME_SELLERLOG_MSG, vendorLogSendVO);
        } catch (Exception e) {
            log.info("发消息异常捕获,{}", JSON.toJSONString(vendorLogSendVO), e);
        }
        return SldResponse.success("审核成功");
    }

    @ApiOperation("投诉列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderSn", value = "订单号", paramType = "query"),
            @ApiImplicitParam(name = "afsSn", value = "退款编号", paramType = "query"),
            @ApiImplicitParam(name = "memberName", value = "投诉人", paramType = "query"),
            @ApiImplicitParam(name = "goodsName", value = "投诉商品名称", paramType = "query"),
            @ApiImplicitParam(name = "state", value = "投诉状态，1-待平台处理/2-待商家申诉/3-待双方对话/4-待平台仲裁/5-会员撤销投诉/6-会员胜诉/7-商家胜诉", paramType = "query"),
            @ApiImplicitParam(name = "startTime", value = "投诉开始时间", paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "投诉结束时间", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "分页大小", defaultValue = "20", paramType = "query"),
            @ApiImplicitParam(name = "current", value = "当前页面位置", defaultValue = "1", paramType = "query")
    })
    @GetMapping("complainList")
    public JsonResult<PageVO<ComplainVO>> complainList(HttpServletRequest request, String orderSn, String afsSn, String memberName,
                                                       String goodsName, Integer state, Date startTime, Date endTime) {
        PagerInfo pager = WebUtil.handlerPagerInfo(request);
        //查询投诉列表
        ComplainExample example = new ComplainExample();
        example.setOrderSnLike(orderSn);
        example.setAfsSnLike(afsSn);
        example.setComplainMemberNameLike(memberName);
        example.setGoodsNameLike(goodsName);
        example.setComplainState(state);
        example.setComplainTimeAfter(startTime);
        example.setComplainTimeBefore(endTime);
        List<ComplainPO> list = complainModel.getComplainList(example, pager);
        List<ComplainVO> vos = new ArrayList<>();

        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(complain -> {
                //查询售后信息
                OrderAfterPO orderAfterServicePO = orderAfterServiceModel.getAfterServiceByAfsSn(complain.getAfsSn());
                //查询订单货品
                OrderProductPO orderProductPO = orderProductModel.getOrderProductByOrderProductId(orderAfterServicePO.getOrderProductId());
                AssertUtil.notNull(orderProductPO, "获取订单货品信息为空，请重试");
                vos.add(new ComplainVO(complain, orderAfterServicePO, orderProductPO));
            });
        }
        return SldResponse.success(new PageVO<>(vos, pager));
    }

    @ApiOperation("投诉详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "complainId", value = "投诉id", required = true, paramType = "query")
    })
    @GetMapping("complainDetail")
    public JsonResult<ComplainDetailVO> complainDetail(HttpServletRequest request, Integer complainId) {
        AssertUtil.notNullOrZero(complainId, "投诉id不能为空");
        //查询投诉信息
        ComplainPO complainPO = complainModel.getComplainByComplainId(complainId);
        AssertUtil.notNull(complainPO, "查询的投诉信息为空");
        //查询售后信息
        OrderAfterPO orderAfterServicePO = orderAfterServiceModel.getAfterServiceByAfsSn(complainPO.getAfsSn());
        //查询订单货品
        OrderProductPO orderProductPO = orderProductModel.getOrderProductByOrderProductId(orderAfterServicePO.getOrderProductId());
        AssertUtil.notNull(orderProductPO, "获取订单货品信息为空，请重试");
        //查询对话信息列表
        ComplainTalkExample example = new ComplainTalkExample();
        example.setComplainId(complainId);
        example.setOrderBy("complain_talk_id ASC");
        List<ComplainTalk> list = complainTalkModel.getComplainTalkList(example, null);

        ComplainDetailVO vo = new ComplainDetailVO(complainPO, orderAfterServicePO, orderProductPO);

        if (!CollectionUtils.isEmpty(list)) {
            List<ComplainDetailVO.ComplainTalkInfo> complainTalkInfoList = new ArrayList<>();
            list.forEach(complainTalk -> {
                ComplainDetailVO.ComplainTalkInfo complainTalkInfo = new ComplainDetailVO.ComplainTalkInfo(complainTalk);
                complainTalkInfoList.add(complainTalkInfo);
            });
            vo.setComplainTalkInfoList(complainTalkInfoList);
        }

        return SldResponse.success(vo);
    }

    @ApiOperation("提交申诉")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "complainId", value = "投诉id", required = true, paramType = "query"),
            @ApiImplicitParam(name = "appealContent", value = "申诉内容", required = true, paramType = "query"),
            @ApiImplicitParam(name = "appealImage", value = "上传凭证,图片之间用逗号隔开", paramType = "query")
    })
    @PostMapping("appeal")
    public JsonResult appeal(HttpServletRequest request, Integer complainId, String appealContent, String appealImage) {
        //校验
        AssertUtil.notNullOrZero(complainId, "投诉id不能为空");
        AssertUtil.notEmpty(appealContent, "申诉内容不能为空");
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        //申诉
        ComplainPO updateOne = new ComplainPO();
        updateOne.setAppealContent(appealContent);
        updateOne.setAppealImage(appealImage);
        updateOne.setAppealTime(new Date());
        updateOne.setAppealVendorId(vendor.getVendorId());
        updateOne.setComplainState(ComplainConst.COMPLAIN_STATE_4);

        ComplainExample example = new ComplainExample();
        example.setStoreId(vendor.getStoreId());
        example.setComplainId(complainId);
        complainModel.updateByExampleSelective(updateOne, example);

        //添加操作日志
        StringBuilder opt = new StringBuilder("提交申诉");
        opt.append("[");
        opt.append("提交申诉成功");
        opt.append("]");
        opt.append("[");
        opt.append("投诉id：");
        opt.append(complainId);
        opt.append("]");
        VendorLogSendVO vendorLogSendVO = new VendorLogSendVO(vendor.getVendorId(), vendor.getVendorName(), request.getRequestURI(), opt.toString(), WebUtil.getRealIp(request));
        try {
            rabbitTemplate.convertAndSend(MQ_EXCHANGE_NAME, MQ_QUEUE_NAME_SELLERLOG_MSG, vendorLogSendVO);
        } catch (Exception e) {
            log.info("发消息异常捕获,{}", JSON.toJSONString(vendorLogSendVO), e);
        }
        return SldResponse.success("提交申诉成功");
    }

    @ApiOperation("放弃申诉")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "complainId", value = "投诉id", required = true, paramType = "query")
    })
    @PostMapping("cancelAppeal")
    public JsonResult cancelAppeal(HttpServletRequest request, Integer complainId) {
        AssertUtil.notNullOrZero(complainId, "投诉id不能为空");
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        //放弃申诉
        ComplainPO updateOne = new ComplainPO();
        updateOne.setComplainState(ComplainConst.COMPLAIN_STATE_6);

        ComplainExample example = new ComplainExample();
        example.setComplainId(complainId);
        example.setStoreId(vendor.getStoreId());
        Integer count = complainModel.updateByExampleSelective(updateOne, example);

        //添加操作日志
        StringBuilder opt = new StringBuilder("放弃申诉");
        opt.append("[");
        opt.append("放弃申诉成功");
        opt.append("]");
        opt.append("[");
        opt.append("投诉id：");
        opt.append(complainId);
        opt.append("]");
        VendorLogSendVO vendorLogSendVO = new VendorLogSendVO(vendor.getVendorId(), vendor.getVendorName(), request.getRequestURI(), opt.toString(), WebUtil.getRealIp(request));
        try {
            rabbitTemplate.convertAndSend(MQ_EXCHANGE_NAME, MQ_QUEUE_NAME_SELLERLOG_MSG, vendorLogSendVO);
        } catch (Exception e) {
            log.info("发消息异常捕获,{}", JSON.toJSONString(vendorLogSendVO), e);
        }
        return SldResponse.success("放弃申诉成功");
    }

    @ApiOperation("发送对话")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "complainId", value = "投诉id", required = true, paramType = "query"),
            @ApiImplicitParam(name = "talkContent", value = "投诉对话内容", required = true, paramType = "query")
    })
    @PostMapping("addTalk")
    public JsonResult addTalk(HttpServletRequest request, Integer complainId, String talkContent) {
        AssertUtil.notNullOrZero(complainId, "投诉id不能为空");
        AssertUtil.notEmpty(talkContent, "投诉对话内容不能为空");
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        int talkUserType = ComplainConst.TALK_USER_TYPE_2;
        complainTalkModel.saveComplainTalk(complainId, talkContent, vendor.getStoreId(), vendor.getVendorName(), talkUserType);

        //添加操作日志
        StringBuilder opt = new StringBuilder("商户发送对话");
        opt.append("[");
        opt.append("新增成功");
        opt.append("]");
        opt.append("[");
        opt.append("投诉id：");
        opt.append(complainId);
        opt.append("]");
        VendorLogSendVO vendorLogSendVO = new VendorLogSendVO(vendor.getVendorId(), vendor.getVendorName(), request.getRequestURI(), opt.toString(), WebUtil.getRealIp(request));
        try {
            rabbitTemplate.convertAndSend(MQ_EXCHANGE_NAME, MQ_QUEUE_NAME_SELLERLOG_MSG, vendorLogSendVO);
        } catch (Exception e) {
            log.info("发消息异常捕获,{}", JSON.toJSONString(vendorLogSendVO), e);
        }
        return SldResponse.success("发送对话成功");
    }

    @ApiOperation("提交仲裁")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "complainId", value = "投诉id", required = true, paramType = "query")
    })
    @PostMapping("handle")
    public JsonResult handle(HttpServletRequest request, Integer complainId) {
        //校验
        AssertUtil.notNullOrZero(complainId, "投诉id不能为空");
        //仲裁
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        ComplainPO updateOne = new ComplainPO();
        updateOne.setComplainState(ComplainConst.COMPLAIN_STATE_4);

        ComplainExample example = new ComplainExample();
        example.setStoreId(vendor.getStoreId());
        example.setComplainId(complainId);
        complainModel.updateByExampleSelective(updateOne, example);

        //添加操作日志
        StringBuilder opt = new StringBuilder("提交仲裁");
        opt.append("[");
        opt.append("提交仲裁成功");
        opt.append("]");
        opt.append("[");
        opt.append("投诉id：");
        opt.append(complainId);
        opt.append("]");
        VendorLogSendVO vendorLogSendVO = new VendorLogSendVO(vendor.getVendorId(), vendor.getVendorName(), request.getRequestURI(), opt.toString(), WebUtil.getRealIp(request));
        try {
            rabbitTemplate.convertAndSend(MQ_EXCHANGE_NAME, MQ_QUEUE_NAME_SELLERLOG_MSG, vendorLogSendVO);
        } catch (Exception e) {
            log.info("发消息异常捕获,{}", JSON.toJSONString(vendorLogSendVO), e);
        }
        return SldResponse.success("提交仲裁成功");
    }

    @ApiOperation("获取试算结果")
    @GetMapping("getTryCaculateResult")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "returnMoneyAmount", value = "试算金额", required = true, paramType = "query"),
            @ApiImplicitParam(name = "afsSn", value = "退款单号", required = true, paramType = "query")
    })
    public JsonResult<LoanTryResultVO> getTryCaculateResult(HttpServletRequest request, @RequestParam("returnMoneyAmount") @NotNull BigDecimal returnMoneyAmount, @RequestParam("afsSn") @NotNull String afsSn) {
        CDMallRefundTryResultVO setlTryResultVo = null;
        try {
            Result<CDMallRefundTryResultVO> tryCaculateResult = orderAfterServiceModel.getTryCaculateResult(returnMoneyAmount, afsSn);
            setlTryResultVo = tryCaculateResult.getData();
        } catch (Exception e) {
            log.info("试算参数：{}，{}", JSON.toJSON(returnMoneyAmount), afsSn);
            log.info("试算异常：{}", JSON.toJSON(e));
        }
        return SldResponse.success(new LoanTryResultVO(setlTryResultVo));
    }

    @ApiOperation("恢复额度退款查询该批次所有退款单号")
    @GetMapping("getAllRestoreLimitOrders")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderSn", value = "订单号", required = true, paramType = "query")
    })
    public JsonResult<RestoreLimitOrders> getAllRestoreLimitOrders(@NotNull String orderSn) {
        List<String> afsSn = orderReturnService.getAllRestoreLimitOrders(orderSn);
        RestoreLimitOrders restoreLimitOrders = new RestoreLimitOrders();
        restoreLimitOrders.setRestoreLimitOrders(String.join(",", afsSn));
        return SldResponse.success(restoreLimitOrders);
    }

    @ApiOperation("代还退款查询金额是否不足1元")
    @GetMapping("getAmountByAssistPayment")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "afsSn", value = "退款单号", required = true, paramType = "query")
    })
    public JsonResult<AssistPaymentAmount> getAmountByAssistPayment(HttpServletRequest request, @RequestParam("afsSn") @NotNull String afsSn) {
        if (StringUtils.isEmpty(afsSn)) {
            throw new MallException(afsSn + "不能为空！");
        }
        // 根据退款单号查询orderReturn记录
        OrderReturnPO orderReturnPOByAfsSn = orderReturnModel.getOrderReturnByAfsSn(afsSn);
        if (!RefundType.ASSIST_PAYMENT.getValue().equals(orderReturnPOByAfsSn.getRefundType())) {
            throw new MallException("当前退款类型不为代还退款！");
        }
        AssistPaymentAmount assistPaymentAmount = new AssistPaymentAmount();
        try {
            Result<CDMallRefundTryResultVO> tryCaculateResult = orderAfterServiceModel.getTryCaculateResult(orderReturnPOByAfsSn.getActualReturnMoneyAmount().add(orderReturnPOByAfsSn.getReturnExpressAmount()), afsSn);
            CDMallRefundTryResultVO data = tryCaculateResult.getData();
            assistPaymentAmount.setNewRefundAmount(data.getSumAmount());
            assistPaymentAmount.setOldRefundAmount(data.getRepayPrincipal());
            assistPaymentAmount.setResult("退款后金额正常");
        } catch (Exception e) {
            if (e.getMessage().equals("余额必须大于1元")) {
                Result<CDMallRefundTryResultVO> tryCaculateResult = orderAfterServiceModel.getTryCaculateResult(BigDecimal.ZERO, afsSn);
                CDMallRefundTryResultVO data = tryCaculateResult.getData();
                assistPaymentAmount.setNewRefundAmount(data.getSumAmount());
                assistPaymentAmount.setOldRefundAmount(data.getRepayPrincipal());
                assistPaymentAmount.setResult("贷款金额/余额必须大于1元");
            }
        }
        return SldResponse.success(assistPaymentAmount);
    }

    @ApiOperation("退款订单表导出")
    @PostMapping("orderRefundExport")
    public void orderRefundExport(HttpServletRequest request, HttpServletResponse response,
                                  @RequestBody OrderRefundExportRequest refundExportRequest) {
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        if (refundExportRequest.getDistribution() == null || refundExportRequest.getDistribution() == 0) {
            refundExportRequest.setStoreId(vendor.getStoreId());
        } else {
            refundExportRequest.setRecommendStoreId(vendor.getStoreId());
            refundExportRequest.setOrderType(OrderTypeEnum.ORDER_TYPE_7.getValue());
        }
        Map<String, List<? extends Object>> sheepMap = new HashMap<>(16);
        sheepMap.put("退款订单表", orderReturnService.sellerOrderRefundExport(refundExportRequest));
        PoiUtils.exportByHttp(request, response, "退款订单表" + DateUtils.format(new Date(), "yyyyMMdd"),
                sheepMap, PoiUtils.DEFAULT_DATE_FORMAT, null, false);
    }


    @ApiOperation("退款订单表导出")
    @PostMapping("orderRefundExport/V2")
    public JsonResult<FileDTO> orderRefundExportV2(HttpServletRequest request, @RequestBody OrderRefundExportRequest refundExportRequest)
            throws Exception {
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        if (refundExportRequest.getDistribution() == null || refundExportRequest.getDistribution() == 0) {
            refundExportRequest.setStoreId(vendor.getStoreId());
        } else {
            refundExportRequest.setRecommendStoreId(vendor.getStoreId());
            refundExportRequest.setOrderType(OrderTypeEnum.ORDER_TYPE_7.getValue());
        }
        JsonResult<FileDTO> jsonResult = new JsonResult<>(200,"成功");
        UserDTO userDTO = new UserDTO(vendor);
        refundExportRequest.setUserDTO(userDTO);
        jsonResult.setData(orderAfterExcelDataExportService.executeAsyncExportExcel(refundExportRequest));
        return jsonResult;
    }


    @ApiOperation("获取商家审核超时退款单数")
    @GetMapping("getAfsCountInStoreAuditOvertime")
    public JsonResult<Integer> getAfsCountInStoreAuditOvertime(HttpServletRequest request,
                                                               @ApiParam(value = "是否配销操作") @RequestParam(value = "distribution",required = false) Integer distribution) {
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        return SldResponse.success(orderReturnService.getAfsCountInStoreAuditOvertime(vendor.getStoreId(),distribution));
    }

    @ApiOperation("商家端查询退款单轨迹信息")
    @GetMapping("getOrderReturnTrack")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "afsSn", value = "退款单号", required = true, paramType = "query")
    })
    public JsonResult<List<OrderReturnTrackVO>> getOrderReturnTrack(@RequestParam("afsSn") @NotNull String afsSn) {
        return SldResponse.success(orderReturnTrackService.getOrderReturnTrackVOByAfsSn(afsSn));
    }

    @ApiOperation("卖家申请换货接口")
    @PostMapping("applyOrderExchange")
    @ThreadLocalRemoveTag
    public JsonResult<OrderExchangeDetailVO> applyOrderExchange(HttpServletRequest request,
                                                                @RequestBody @NotNull @Valid OrderExchangeRequest orderExchangeRequest) throws Exception {
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        // 只有白名单店铺才支持设置卖家换货
        Boolean whiteList = storeIsolateWhiteListFeignClient.isStoreOnList(WhiteListEnum.SELLER_EXCHANGE_WHITE_LIST_TYPE, vendor.getStoreId());
        if (!whiteList) {
            return SldResponse.fail("该商家不允许换货");
        }
        UserDTO userDTO = new UserDTO(vendor);
        orderExchangeRequest.setApplicantInfo(userDTO);
        return SldResponse.success(orderExchangeService.applyExchange(orderExchangeRequest));
    }


    @ApiOperation("换货列表")
    @PostMapping("orderExchangeList")
    public JsonResult<PageVO<OrderExchangeListVO>> orderExchangeList(HttpServletRequest request,@RequestBody OrderExchangeListRequest orderExchangeListRequest) {
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        orderExchangeListRequest.setStoreId(vendor.getStoreId());
        PagerInfo pager = new PagerInfo(Integer.parseInt(Optional.ofNullable(orderExchangeListRequest.getPageSize()).orElse("10")),
                Integer.parseInt(Optional.ofNullable(orderExchangeListRequest.getCurrent()).orElse("1")));
        return SldResponse.success(new PageVO<>(orderExchangeDetailService.orderExchangeList(orderExchangeListRequest, pager), pager));
    }

    @ApiOperation("商品换货详情")
    @GetMapping("orderExchangeDetail")
    public JsonResult<List<OrderExchangeDetailVO>> productOrderExchangeDetail(HttpServletRequest request,@RequestParam("orderProductId") @NotNull Long orderProductId) {
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        UserDTO userDTO = new UserDTO(vendor);
        return SldResponse.success(orderExchangeDetailService.productOrderExchangeDetail(orderProductId, userDTO));
    }

    @ApiOperation("换货申请详情")
    @GetMapping("orderExchangeApplyDetail")
    public JsonResult<OrderExchangeDetailVO> orderExchangeApplyDetail(HttpServletRequest request,@RequestParam("exchangeSn") @NotNull String exchangeSn) {
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        UserDTO userDTO = new UserDTO(vendor);
        return SldResponse.success(orderExchangeDetailService.orderExchangeApplyDetail(exchangeSn, userDTO));
    }

    @ApiOperation("卖家申请换货试算")
    @PostMapping("orderExchangeTrial")
    public JsonResult<OrderExchangeDetailVO> orderExchangeTrial(@RequestBody @NotNull @Valid OrderExchangeRequest orderExchangeRequest) {
        return SldResponse.success(orderExchangeService.orderExchangeTrial(orderExchangeRequest));
    }


    @ApiOperation(value="换货审批")
    @PostMapping("/exchangeOrder/audit")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "exchangeSn", value = "换货单号", paramType = "query",required = true)
    })
    public JsonResult<Void> exchangeOrderAudit(HttpServletRequest request,@RequestBody OrderExchangeAuditDTO orderExchangeAuditDTO) throws Exception {
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        UserDTO userDTO = new UserDTO(vendor);
        orderExchangeService.exchangeOrderAudit(orderExchangeAuditDTO, userDTO);
        return SldResponse.success();
    }

}
