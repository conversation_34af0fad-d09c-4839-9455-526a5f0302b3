package com.cfpamf.ms.mallorder.service.payment;

import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderReturnPO;
import com.cfpamf.ms.mallsystem.vo.Admin;
import com.slodon.bbc.core.exception.BusinessException;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @Create 2022-05-09 09:59
 * @Description :支付通用处理器抽象类
 */
@Service("AbstractPayHandleServiceImpl")
public abstract class AbstractPayHandleServiceImpl implements IPayHandleService {

    @Override
    public Boolean paymentRefund(OrderPO orderPO, OrderReturnPO orderReturnPO,
                                 Map<String, Object> extendParam, String actionType, Admin admin) {
        return true;
    }

    @Override
    public Boolean syncExecute(OrderPO orderPO, OrderReturnPO orderReturnPO,String actionType) {
        return true;
    }

    @Override
    public void verifyExecute(OrderPO orderPO, OrderReturnPO returnPO, String actionType) {
    }

}
