package com.cfpamf.ms.mallorder.vo.kingdee;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@ApiModel(value = "金蝶客户对象VO类", description = "金蝶客户对象")
@Data
public class KingdeeCustomerVo implements Serializable {

    private static final long serialVersionUID = 399948352L;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "客户编码")
    private String customerCode;

    @ApiModelProperty(value = "客户简称")
    private String customerShortName;

    @ApiModelProperty(value = "单据状态: 0- 未审核,1-已审核")
    private Integer documentStatus;

    @ApiModelProperty(value = "禁用状态:0- 启用,1- 禁用")
    private Integer forbidStatus;

    @ApiModelProperty(value = "单据状态: 0- 未审核,1-已审核")
    private String documentStatusStr;

    @ApiModelProperty(value = "禁用状态:0- 启用,1- 禁用")
    private String forbidStatusStr;

    @ApiModelProperty(value = "组织")
    private String organization;

    @ApiModelProperty(value = "客户分组")
    private String customerGroup;

    @ApiModelProperty(value = "审核时间")
    private String auditTime;
}
