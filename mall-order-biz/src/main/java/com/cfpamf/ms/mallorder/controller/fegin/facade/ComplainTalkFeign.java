package com.cfpamf.ms.mallorder.controller.fegin.facade;


import java.util.List;
import javax.annotation.Resource;

import com.cfpamf.ms.mallorder.api.feign.ComplainTalkFeignClient;
import com.cfpamf.ms.mallorder.model.ComplainTalkModel;
import com.cfpamf.ms.mallorder.po.ComplainTalk;
import com.cfpamf.ms.mallorder.request.ComplainTalkExample;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@RestController
@Slf4j
public class ComplainTalkFeign     {


    @Resource
    private ComplainTalkModel complainTalkModel;

    /**
     * 根据complainTalkId获取投诉对话表详情
     *
     * @param complainTalkId complainTalkId
     * @return
     */
    @GetMapping("/v1/feign/business/complainTalk/get")
    public ComplainTalk getComplainTalkByComplainTalkId(@RequestParam("complainTalkId") Integer complainTalkId) {
        return complainTalkModel.getComplainTalkByComplainTalkId(complainTalkId);
    }

    /**
     * 获取条件获取投诉对话表列表
     *
     * @param example 查询条件信息
     * @return
     */
    @PostMapping("/v1/feign/business/complainTalk/getList")
    public List<ComplainTalk> getComplainTalkList(@RequestBody ComplainTalkExample example) {
        return complainTalkModel.getComplainTalkList(example, example.getPager());
    }

    /**
     * 新增投诉对话表
     *
     * @param complainTalk
     * @return
     */
    @PostMapping("/v1/feign/business/complainTalk/addComplainTalk")
    public Integer saveComplainTalk(@RequestBody ComplainTalk complainTalk) {
        return complainTalkModel.saveComplainTalk(complainTalk);
    }

    /**
     * 根据complainTalkId更新投诉对话表
     *
     * @param complainTalk
     * @return
     */
    @PostMapping("/v1/feign/business/complainTalk/updateComplainTalk")
    public Integer updateComplainTalk(@RequestBody ComplainTalk complainTalk) {
        return complainTalkModel.updateComplainTalk(complainTalk);
    }

    /**
     * 根据complainTalkId删除投诉对话表
     *
     * @param complainTalkId complainTalkId
     * @return
     */
    @PostMapping("/v1/feign/business/complainTalk/deleteComplainTalk")
    public Integer deleteComplainTalk(@RequestParam("complainTalkId") Integer complainTalkId) {
        return complainTalkModel.deleteComplainTalk(complainTalkId);
    }
}