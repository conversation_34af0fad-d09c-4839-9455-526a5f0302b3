package com.cfpamf.ms.mallorder.controller.fegin.facade;

import com.cfpamf.ms.mallorder.dto.*;
import com.cfpamf.ms.mallorder.model.OrderProductModel;
import com.cfpamf.ms.mallorder.po.OrderProductPO;
import com.cfpamf.ms.mallorder.request.OrderProductExample;
import com.cfpamf.ms.mallorder.service.IOrderProductCouponService;
import com.cfpamf.ms.mallorder.service.IOrderProductService;
import com.cfpamf.ms.mallorder.vo.OrderProductInfoVO;
import com.cfpamf.ms.mallorder.vo.OrderProductRebateVO;
import com.cfpamf.ms.mallorder.vo.OrderProductVO;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController
@Slf4j
public class OrderProductFeign {

    @Resource
    private OrderProductModel orderProductModel;
    @Resource
    private IOrderProductService orderProductService;
    @Autowired
    private IOrderProductCouponService orderProductCouponService;

    /**
     * 根据orderProductId获取订单货品明细表详情
     *
     * @param orderProductId orderProductId
     * @return
     */
    @GetMapping("/v1/feign/business/orderProduct/get")
    public OrderProductPO getOrderProductByOrderProductId(@RequestParam("orderProductId") Long orderProductId) {
        return orderProductModel.getOrderProductByOrderProductId(orderProductId);
    }

    /**
     * 获取条件获取订单货品明细表列表
     *
     * @param example 查询条件信息
     * @return
     */
    @PostMapping("/v1/feign/business/orderProduct/getList")
    public List<OrderProductPO> getOrderProductList(@RequestBody OrderProductExample example) {
        return orderProductModel.getOrderProductList(example, example.getPager());
    }

    /**
     * 获取条件获取订单货品明细表列表 VO 对象
     *
     * @param example 查询条件信息
     * @return        商品 VO 信息
     */
    @PostMapping("/v1/feign/business/orderProduct/getInfoVOList")
    public List<OrderProductInfoVO> getOrderProductInfoVOList(@RequestBody OrderProductExample example) {
        return orderProductService.getOrderProductInfoVOList(example);
    }

    /**
     * 新增订单货品明细表
     *
     * @param orderProductPO
     * @return
     */
    @PostMapping("/v1/feign/business/orderProduct/addOrderProduct")
    public Integer saveOrderProduct(@RequestBody OrderProductPO orderProductPO) {
        return orderProductModel.saveOrderProduct(orderProductPO);
    }

    /**
     * 根据orderProductId更新订单货品明细表
     *
     * @param orderProductPO
     * @return
     */
    @PostMapping("/v1/feign/business/orderProduct/updateOrderProduct")
    public Integer updateOrderProduct(@RequestBody OrderProductPO orderProductPO) {
        return orderProductModel.updateOrderProduct(orderProductPO);
    }

    /**
     * 根据orderProductId删除订单货品明细表
     *
     * @param orderProductId orderProductId
     * @return
     */
    @PostMapping("/v1/feign/business/orderProduct/deleteOrderProduct")
    public Integer deleteOrderProduct(@RequestParam("orderProductId") Long orderProductId) {
        return orderProductModel.deleteOrderProduct(orderProductId);
    }

    /**
     * 结算查询业务单商品信息
     * @param bizSnList
     * @return
     */
    @PostMapping("/v1/feign/business/orderProduct/get4Bill")
    public Map<String, OrderProductVO> bizOrderProducts(@RequestBody List<String> bizSnList) {
        return orderProductService.bizOrderProducts(bizSnList);
    }

    @PostMapping("/v1/feign/business/orderProduct/couponSendNotify")
    JsonResult<Boolean> couponSendNotify(@RequestBody @Valid CouponSendNotifyDto notifyDto){
        orderProductCouponService.couponSendNotify(notifyDto);
        orderProductCouponService.isAllCouponSend(notifyDto.getOrderSn(), null);

        return SldResponse.success(Boolean.TRUE);
    }

    /**
     * 返利活动查询商品信息
     * @param dto
     * @return
     */
    @PostMapping("/v1/feign/business/orderProduct/queryByRebateProduct")
    public List<OrderProductRebateVO> queryByRebateProduct(@RequestBody @Valid OrderProductRebateDTO dto){
        return orderProductService.queryByRebateProduct(dto);
    }

    /**
     * 农服购买物料返利活动查询商品信息
     * @param dto
     * @return
     */
    @PostMapping("/v1/feign/business/orderProduct/queryBuyCommodityRebateProduct")
    public List<OrderProductRebateVO> queryBuyCommodityRebateProduct(@RequestBody @Valid BuyCommodityRebateProductDTO dto){
        return orderProductService.queryBuyCommodityRebateProduct(dto);
    }

    /**
     * 酒水满十赠一返利活动查询商品信息
     * @param dto
     * @return
     */
    @PostMapping("/v1/feign/business/orderProduct/buyWineGift")
    public List<OrderProductRebateVO> queryBuyWineGift(@RequestBody @Valid BuyWineRebateProductDTO dto){
        return orderProductService.queryBuyWineGift(dto);
    }


    /**
     * 同步税率
     *
     */
    @PostMapping("/v1/feign/business/orderProduct/syncTaxRate")
    public void syncTaxRate(){
        orderProductService.syncTaxRate();
    }


    /**
     * 更新商品行发货状态
     * @param dto
     * @return
     */
    @PostMapping("/v1/feign/business/orderProduct/updateProductDeliveryState")
    public JsonResult<Boolean> updateProductDeliveryState(@Valid @RequestBody ProductDeliveryDTO dto) {
        return SldResponse.success(orderProductService.updateProductDeliveryState(dto));
    }

    @GetMapping("/v1/feign/business/orderProduct/syncChannelNewSkuId")
    public void syncChannelNewSkuId(@RequestParam("orderProductId")String param){
        orderProductService.dealHistorySkuId(param);
        log.info("syncChannelNewSkuId success");
    }

    /**
     * 天杰满十赠一返利活动查询商品信息
     * @param dto
     * @return
     */
    @PostMapping("/v1/feign/business/orderProduct/tjBuyNzRebateGift")
    public List<OrderProductRebateVO> tjBuyNzRebateGift(@RequestBody @Valid TjBuyNzRebateProductDTO dto){
        return orderProductService.tjBuyNzRebateGift(dto);
    }

    /**
     * 根据userNo和sku查询用户最新订单的收货地址
     * @param userNo
     * @param skuId
     * @return
     */
    @GetMapping("/v1/feign/business/orderProduct/userAddress")
    public OrderAddressDTO queryUserAddress(@RequestParam("userNo") String userNo, @RequestParam(value = "skuId", required = false) Long skuId){
        return orderProductService.queryUserAddress(userNo, skuId);
    }

    @PostMapping("/v1/feign/business/orderProduct/queryOrderSnByProductIdAndFinanceRule")
    public JsonResult<List<HistoryDealOrderEventNotifyDTO>> queryOrderSnByProductIdAndFinanceRule(@RequestBody List<AgricQueryOrderDTO> list){
        List<HistoryDealOrderEventNotifyDTO> orderProductPOS = orderProductService.queryOrderSnByProductIdAndFinanceRule(list);
        return SldResponse.success(orderProductPOS);
    }

    /**
     * 根据订单号集合查询订单信息
     * @param orderQueryDTO orderQueryDTO
     * @return JsonResult<List<HistoryDealOrderEventNotifyDTO>>
     */
    @PostMapping("/v1/feign/business/orderProduct/queryOrderInfoByOrderIdList")
    public JsonResult<List<HistoryDealOrderEventNotifyDTO>> queryOrderInfoByOrderIdList(@RequestBody DbcOrderQueryDTO orderQueryDTO){
        List<HistoryDealOrderEventNotifyDTO> orderProductPOS = orderProductService.queryOrderInfoByOrderIdList(orderQueryDTO);
        return SldResponse.success(orderProductPOS);
    }
}