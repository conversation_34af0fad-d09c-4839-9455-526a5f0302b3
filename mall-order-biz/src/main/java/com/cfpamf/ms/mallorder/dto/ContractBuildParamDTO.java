package com.cfpamf.ms.mallorder.dto;

import lombok.Data;

import java.util.List;

/**
 * 合同模板参数dto
 *
 * <AUTHOR>
 * @since 2024/11/21
 */
@Data
public class ContractBuildParamDTO {
    /**
     * 商品信息list
     */
    List<OrderProductBuildParamDTO> productInfoList;

    /**
     * 营业执照名
     */
    String businessLicenseName;

    /**
     * 收货地址
     */
    String receiveAddress;

    /**
     * 下单时间
     */
    String orderDate;

    /**
     * 收货人
     */
    String receiver;

    /**
     * 收货人联系方式
     */
    String contactMobile;
    /**
     * 业绩归属人
     */
    String performBelong;
    /**
     * 订单编号
     */
    String orderSn;

    /**
     * 主借人
     *
     */
    String custName;
    /**
     * 主借人身份证
     */
    String custIdNo;
    /**
     * 总计金额
     */
    String totalAmount;

    /**
     * 唯一码
     */
    String uniqueCode;

    /**
     * 用户手机号
     */
    String custPhone;

    /**
     * 年
     */
    String year;
    /**
     * 月
     */
    String month;
    /**
     * 日
     */
    String day;
}
