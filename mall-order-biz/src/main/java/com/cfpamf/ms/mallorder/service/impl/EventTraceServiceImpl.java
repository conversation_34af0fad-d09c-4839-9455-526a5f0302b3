package com.cfpamf.ms.mallorder.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cfpamf.framework.autoconfigure.common.exception.MSException;
import com.cfpamf.framework.autoconfigure.web.bms.JwtUserInfo;
import com.cfpamf.ms.mallorder.common.enums.OperateTypeEnum;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.EventTraceDTO;
import com.cfpamf.ms.mallorder.po.EventTracePO;
import com.cfpamf.ms.mallorder.mapper.EventTraceMapper;
import com.cfpamf.ms.mallorder.service.IEventTraceService;
import com.cfpamf.framework.autoconfigure.mybatis.BaseRepoServiceImpl;
import com.cfpamf.ms.mallorder.vo.EventTraceVO;
import com.cfpamf.ms.mallshop.resp.Vendor;
import com.slodon.bbc.core.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <p>
 * 操作日志记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-11
 */
@Slf4j
@Service
public class EventTraceServiceImpl extends BaseRepoServiceImpl<EventTraceMapper, EventTracePO> implements IEventTraceService {

    public static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    @Value("${spring.application.name}")
    private String applicationName;

    @Override
    public <T> void track(T executor, EventTraceDTO dto, String operateResult) {
        if (null == executor || null == dto || null == dto.getOperateType() || null == dto.getOperateTime()
                || StringUtils.isAnyBlank(dto.getTargetDomain(), dto.getTargetObject())) {
            throw new MSException(String.valueOf(ErrorCodeEnum.U.EMPTY_PARAM.getCode()),"记录操作轨迹缺失必要的入参");
        }
        EventTracePO entity = this.assemble(executor);
        this.fillTracePOWithDTO(entity,dto);
        entity.setOperateResult(operateResult);
        boolean saveResult = this.save(entity);
        if (!saveResult) {
            log.error("操作日志保存失败,eventChannel:{},targetObject:{}", entity.getEventChannel(), entity.getTargetObject());
        }
    }

    @Override
    public <T> void batchTrack(T executor, List<EventTraceDTO> dtoList, String operateResult) {
        if (null == executor) {
            throw new MSException(String.valueOf(ErrorCodeEnum.U.EMPTY_PARAM.getCode()),"批量记录操作轨迹缺失必要的入参");
        }
        if (CollectionUtils.isEmpty(dtoList)) {
            return;
        }
        EventTraceDTO traceDTO = dtoList.get(0);
        List<EventTracePO> entityList = new ArrayList<>();
        EventTracePO entity;
        for (EventTraceDTO dto : dtoList) {
            if (null == dto || null == dto.getOperateType() || null == dto.getOperateTime()
                    || StringUtils.isAnyBlank(dto.getTargetDomain(), dto.getTargetObject())) {
                throw new MSException(String.valueOf(ErrorCodeEnum.U.ILLEGAL_PARAM.getCode()),"批量记录操作轨迹存在非法的参数");
            }
            entity = this.assemble(executor);
            this.fillTracePOWithDTO(entity,dto);
            entity.setOperateResult(operateResult);
            entityList.add(entity);
        }
        if (!this.saveBatch(entityList)) {
            log.error("批量操作日志保存失败,eventChannel:{},targetObject:{}", traceDTO.getEventChannel(), traceDTO.getTargetObject());
        }
    }

    @Override
    public List<EventTraceVO> listHistoryDiffContent(EventTraceDTO dto) {
        if (null == dto || StringUtils.isAnyBlank(dto.getTargetDomain(), dto.getTargetObject())) {
            throw new MSException(String.valueOf(ErrorCodeEnum.U.EMPTY_PARAM.getCode()),"记录操作轨迹缺失必要的入参");
        }
        LambdaQueryWrapper<EventTracePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EventTracePO::getTargetDomain, dto.getTargetDomain());
        queryWrapper.eq(EventTracePO::getTargetObject, dto.getTargetObject());
        queryWrapper.orderByDesc(EventTracePO::getOperateTime);
        List<EventTracePO> entityList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(entityList)) {
            return Collections.emptyList();
        }
        Map<String, List<EventTracePO>> operateTimeMap = new LinkedHashMap<>(entityList.size());
        String operateTime;
        List<EventTracePO> valueList;
        for (EventTracePO itemPO : entityList) {
            operateTime = itemPO.getOperateTime().format(FORMATTER);
            valueList = operateTimeMap.get(operateTime);
            if (Objects.isNull(valueList)) {
                valueList = new ArrayList<>();
                operateTimeMap.put(operateTime,valueList);
            }
            valueList.add(itemPO);
        }
        if (operateTimeMap.isEmpty()) {
            return Collections.emptyList();
        }
        List<EventTraceVO> voList = new ArrayList<>(operateTimeMap.size());
        EventTraceVO vo;
        List<EventTraceVO.DiffContent> diffContentList;
        for (Map.Entry<String, List<EventTracePO>> itemEntry : operateTimeMap.entrySet()) {
            diffContentList = this.groupEntity2DiffContent(itemEntry.getValue());
            vo = this.entity2VO(itemEntry.getValue().get(0), diffContentList);
            voList.add(vo);
        }
        return voList;
    }

    @Override
    public List<EventTraceVO> listHistoryDiffContent(String targetDomain, List<String> targetObjectList) {
        if (StringUtils.isBlank(targetDomain) || CollectionUtils.isEmpty(targetObjectList)) {
            throw new MSException(String.valueOf(ErrorCodeEnum.U.EMPTY_PARAM.getCode()),"记录操作轨迹缺失必要的入参");
        }
        LambdaQueryWrapper<EventTracePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EventTracePO::getTargetDomain, targetDomain);
        queryWrapper.in(EventTracePO::getTargetObject, targetObjectList);
        queryWrapper.orderByDesc(EventTracePO::getOperateTime);
        List<EventTracePO> entityList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(entityList)) {
            return Collections.emptyList();
        }
        Map<String, List<EventTracePO>> operateTimeMap = new HashMap<>(entityList.size());
        String operateTime;
        List<EventTracePO> valueList;
        for (EventTracePO itemPO : entityList) {
            operateTime = itemPO.getOperateTime().format(FORMATTER);
            valueList = operateTimeMap.get(operateTime);
            if (Objects.isNull(valueList)) {
                valueList = new ArrayList<>();
                operateTimeMap.put(operateTime,valueList);
            }
            valueList.add(itemPO);
        }
        if (operateTimeMap.isEmpty()) {
            return Collections.emptyList();
        }
        List<EventTraceVO> voList = new ArrayList<>(operateTimeMap.size());
        EventTraceVO vo;
        List<EventTraceVO.DiffContent> diffContentList;
        for (Map.Entry<String, List<EventTracePO>> itemEntry : operateTimeMap.entrySet()) {
            diffContentList = this.groupEntity2DiffContent(itemEntry.getValue());
            vo = this.entity2VO(itemEntry.getValue().get(0), diffContentList);
            voList.add(vo);
        }
        return voList;
    }


    private <T> EventTracePO assemble(T executor) {
        EventTracePO entity = new EventTracePO();
        entity.setAppName("dbc-service");
        if (executor instanceof JwtUserInfo) {
            JwtUserInfo userInfo = (JwtUserInfo) executor;
            AssertUtil.notEmpty(userInfo.getUserName(), "平台登陆用户名为空");
            AssertUtil.notEmpty(userInfo.getAccount(), "平台登陆用户手机号为空");
            entity.setExecutorNo(userInfo.getJobNumber());
            entity.setExecutorName(userInfo.getUserName());
            entity.setExecutorPhone(userInfo.getAccount());
        } else if (executor instanceof Vendor) {
            Vendor userInfo = (Vendor) executor;
            AssertUtil.notNull(userInfo.getVendorId(), "商户登陆用户id为空");
            AssertUtil.notEmpty(userInfo.getVendorName(), "商户登陆用户名为空");
            entity.setExecutorNo(String.valueOf(userInfo.getVendorId()));
            entity.setExecutorName(userInfo.getVendorName());
            entity.setExecutorPhone(userInfo.getVendorMobile());
        } else {
            entity.setExecutorName(applicationName);
            entity.setExecutorPhone("-");
            entity.setEventChannel("XXL-JOB");
        }
        return entity;
    }

    private void fillTracePOWithDTO(EventTracePO entity,EventTraceDTO dto){
        entity.setNodeName(dto.getNodeName());
        entity.setEventChannel(dto.getEventChannel());
        entity.setTargetObject(dto.getTargetObject());
        entity.setTargetDomain(dto.getTargetDomain());
        entity.setOperateTime(dto.getOperateTime());
        entity.setOperateType(dto.getOperateType().getValue());
        entity.setContentTitle(dto.getContentTitle());
        entity.setBeforeValue(dto.getBeforeValue());
        entity.setAfterValue(dto.getAfterValue());
//        entity.setOperateResult(dto.getOperateResult());
        entity.setOperateRemark(dto.getOperateRemark());
    }

    private EventTraceVO entity2VO(EventTracePO entity, List<EventTraceVO.DiffContent> diffContentList) {
        EventTraceVO vo = new EventTraceVO();
        vo.setAppName(entity.getAppName());
        vo.setExecutorNo(entity.getExecutorNo());
        vo.setExecutorName(entity.getExecutorName());
        vo.setExecutorPhone(entity.getExecutorPhone());
        vo.setEventChannel(entity.getEventChannel());
        OperateTypeEnum typeEnum = OperateTypeEnum.parseEnum(entity.getOperateType());
        vo.setOperateTypeDesc(typeEnum.getDesc());
        vo.setNodeName(entity.getNodeName());
        vo.setTargetObject(entity.getTargetObject());

        vo.setOperateTime(entity.getOperateTime().format(FORMATTER));
        vo.setDiffContentList(diffContentList);
        vo.setOperateResult(OrderConst.RESULT_CODE_SUCCESS.equals(entity.getOperateResult()) ? "通过" : "拒绝");
        vo.setOperateRemark(entity.getOperateRemark());
        return vo;
    }

    private List<EventTraceVO.DiffContent> groupEntity2DiffContent(List<EventTracePO> groupEntityList){
        List<EventTraceVO.DiffContent> diffContentList = new ArrayList<>(groupEntityList.size());
        EventTraceVO.DiffContent diffContent;
        for (EventTracePO itemPO : groupEntityList) {
            diffContent = new EventTraceVO.DiffContent();
            diffContent.setContentTitle(itemPO.getContentTitle());
            diffContent.setBeforeValue(itemPO.getBeforeValue());
            diffContent.setAfterValue(itemPO.getAfterValue());
            diffContentList.add(diffContent);
        }
        return diffContentList;
    }
}
