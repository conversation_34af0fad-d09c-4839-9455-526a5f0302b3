package com.cfpamf.ms.mallorder.integration.facade;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.mallorder.vo.ContractDetailVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2025/5/27
 */
@FeignClient(
        name = "agric-crm",
        url = "${agric.crm.url}"
)
public interface AgricCrmFacade {
    /**
     * 根据合同模板名称、类型获取合同模板信息
     *
     * @param templateName 合同模板名称
     * @param type 合同模板类型
     * @return 合同模板
     */
    @GetMapping("/feign/contract/getByTemplateNameAndType")
    Result<ContractDetailVO> getByTemplateNameAndType(@RequestParam("templateName") String templateName,
                                                     @RequestParam("type") Integer type);
}
