package com.cfpamf.ms.mallorder.scheduler;

import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.mallorder.common.enums.TaskQueueBizTypeEnum;
import com.cfpamf.ms.mallorder.po.TaskQueuePO;
import com.cfpamf.ms.mallorder.service.IOrderPayService;
import com.cfpamf.ms.mallorder.service.ITaskQueueService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Create 2022-06-06 13:57
 * @Description :渠道交易流水号补偿  0 0/5 * * * ?
 */
@Component
@Slf4j
public class OrderBankTrxNoMakeUp {

    @Autowired
    private IOrderPayService iOrderPayService;

    @Autowired
    private ITaskQueueService taskQueueService;

    @XxlJob(value = "OrderBankTrxNoMakeUp")
    public ReturnT<String> execute(String s) {
        String dateFormat = DateUtil.format(new Date(), DateUtil.CN_LONG_FORMAT);
        log.info("START OrderBankTrxNoMakeUp systemTime:{} eventTime:{}", new Date(), dateFormat);
        // 查询定时任务，进行发起补偿
        List<TaskQueuePO> taskQueuePOS = taskQueueService.listTodoTask(TaskQueueBizTypeEnum.MAKE_UP_BANK_TRX_NO, dateFormat);
        int count = 0;
        for (TaskQueuePO taskQueuePO : taskQueuePOS) {
            try {
                Boolean makeUpReslut = iOrderPayService.orderBankTrxNoMakeUp(taskQueuePO.getBizId());
                if (makeUpReslut) {
                    taskQueueService.taskSuccess(taskQueuePO);
                    count++;
                } else {
                    taskQueueService.taskFail(taskQueuePO, taskQueuePO.getBizId().toString(), "补偿失败，暂未查询到渠道交易流水");
                }
            } catch (Exception e) {
                taskQueueService.taskFail(taskQueuePO, taskQueuePO.getBizId().toString(), e.getMessage());
            }
        }
        log.info("=============== FINISHED OrderBankTrxNoMakeUp, 应处理:{}, 实处理:{} ",taskQueuePOS.size(), count);

        return new ReturnT("SUCCESS");
    }
}
