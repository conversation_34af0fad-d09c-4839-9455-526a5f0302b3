package com.cfpamf.ms.mallorder.integration.facade;

import com.cfpamf.cmis.common.base.CommonResult;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.bizconfig.facade.request.BranchLoanOrgRequest;
import com.cfpamf.ms.bizconfig.facade.request.LoanOrgRequest;
import com.cfpamf.ms.bizconfig.facade.vo.BranchLoanOrgBankInfoVo;
import com.cfpamf.ms.bizconfig.facade.vo.CompanyVo;
import com.cfpamf.ms.bizconfig.facade.vo.LoanOrgBankInfoVo;
import com.cfpamf.ms.bizconfig.facade.vo.LoanOrgVo;
import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(
        name = "ms-service-bizconfig",
        url = "${ms-bizconfig-service.url}"
)
@RequestMapping({"/bizconfig"})
public interface LoanOrgFacade {
    @GetMapping({"/loanorg/getLoanOrgInfo"})
    CommonResult<LoanOrgVo> getLoanOrgInfo(@RequestParam("branchCode") String var1, @RequestParam("loanType") String var2);

    @GetMapping({"/loanorg/getLoanOrgList"})
    CommonResult<List<LoanOrgVo>> getLoanOrgList(@RequestParam("branchCode") String var1);

    @GetMapping({"/loanorg/getLoanOrgByCode"})
    CommonResult<CompanyVo> getLoanOrgByCode(@RequestParam("companyCode") String var1);

    @GetMapping({"/loanorg/getLoanCompanyList"})
    CommonResult<List<CompanyVo>> getLoanCompanyList();

    @PostMapping({"/loanorg/getLoanCompanyBankList"})
    CommonResult<List<LoanOrgBankInfoVo>> getLoanOrgBankInfo(@RequestBody LoanOrgRequest var1);

    @PostMapping({"/loanorg/getBranchLoanOrgBankInfo"})
    Result<List<BranchLoanOrgBankInfoVo>> getBranchLoanOrgBankInfo(@RequestBody BranchLoanOrgRequest var1);
}

