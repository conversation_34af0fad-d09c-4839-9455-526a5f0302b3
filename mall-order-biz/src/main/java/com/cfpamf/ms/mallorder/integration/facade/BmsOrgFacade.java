package com.cfpamf.ms.mallorder.integration.facade;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.bms.facade.vo.OrganizationBaseVO;
import com.cfpamf.ms.bms.facade.vo.OrganizationVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


@FeignClient(
        name = "bms-service-biz",
        url = "${bms.api.url}"
)
public interface BmsOrgFacade {

    @ApiOperation("根据hrOrgId获取组织机构详情")
    @GetMapping({"/org/getOrganizationByHrOrgId/{hrOrgId}"})
    Result<OrganizationVO> getOrganizationByHrOrgId(@RequestHeader("authorization") String authorization, @PathVariable("hrOrgId") Long hrOrgId);

    @ApiOperation("根据orgCode获取组织机构详情")
    @GetMapping({"/org/getOrganizationByOrgCode/{orgCode}"})
    Result<OrganizationVO> getOrganizationByOrgCode(@RequestHeader("authorization") String authorization, @PathVariable("orgCode") String orgCode);


    @ApiOperation("根据hrOrgCode所对应的级别获取对应下的所有子孙的分支信息")
    @GetMapping({"/org/listBranchesByHrOrgCodesV2"})
    Result<List<OrganizationBaseVO>> listBranchesByHrOrgCodesV2(@RequestParam("hrOrgCodes") String hrOrgCodes);

}
