package com.cfpamf.ms.mallorder.integration.facade;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.mallorder.dto.RuleServiceFeeQueryDTO;
import com.cfpamf.ms.mallorder.integration.cashier.request.PushOrderRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @Create 2022-09-08 10:40
 * @Description :乡助平台外部接口
 */
@FeignClient(value = "oms-base-service", url = "${oms-base-service.url}", name = "oms-base-service")
public interface OmsBaseFacade {

    /**
     * @param ruleServiceFeeQueryDTO
     * @return Result<Boolean>
     * @description : 查询是否需要平台服务费
     */
    @PostMapping("/rule-service-fee/query")
    Result<Boolean> query(@RequestBody RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO);
}
