package com.cfpamf.ms.mallorder.service.impl;

import com.cfpamf.framework.autoconfigure.mybatis.BaseRepoServiceImpl;
import com.cfpamf.ms.mallorder.mapper.BzBankTransferMapper;
import com.cfpamf.ms.mallorder.po.BzBankTransferPO;
import com.cfpamf.ms.mallorder.service.IBzBankTransferService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 银行转账汇款支付 服务实现类，来自需求M109-银行卡汇款支付
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-16
 */
@Service
@Slf4j
public class BzBankTransferServiceImpl extends BaseRepoServiceImpl<BzBankTransferMapper, BzBankTransferPO> implements IBzBankTransferService {


}
