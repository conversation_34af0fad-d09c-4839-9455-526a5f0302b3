package com.cfpamf.ms.mallorder.integration.trade;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.controller.fegin.facade.PayFacade;
import com.cfpamf.ms.mallorder.req.PayV2Request;
import com.cfpamf.ms.trade.facade.vo.PayRequestVo;
import com.slodon.bbc.core.exception.MallException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @Create 2021-09-17 09:58
 * @Description :核算交易
 */
@Component
@Slf4j
public class CoreTradeIntegration {

    @Autowired
    private PayFacade payFacade;

    public Result<String> payV2(PayV2Request request) {
        Result<String> result = null;
        try {
            result = payFacade.payV2(request);
        } catch (Exception e) {
            log.error("调用交易支付接口进行对公转账异常/pay/v2:{}", e);
            if (!CommonConst.TIME_OUT.equals(e.getMessage())) {
                throw e;
            }
        }
        if (result == null) {
            log.warn("调用交易支付接口进行对公转账未返回信息,请求参数:{}", JSONObject.toJSONString(request));
            throw new MallException("调用交易支付接口进行对公转账未返回信息");
        } else if (!result.isSuccess()) {
            log.warn("调用交易支付接口进行对公转账失败,请求参数:{}，响应参数:{}", JSONObject.toJSONString(request), JSONObject.toJSON(result));
            throw new MallException(result.getMessage());
        }
        return result;
    }

    public PayRequestVo queryPayResult(String tradeNo) {
        Result<PayRequestVo> result = null;
        try {
            result = payFacade.queryPayResult(tradeNo);
        } catch (Exception e) {
            throw new MallException("查询对公转账异常:" + e.getMessage());
        }
        if (result == null) {
            throw new MallException("查询对公转账未返回信息");
        } else if (!result.isSuccess()) {
            throw new MallException(result.getMessage());
        }
        return result.getData();
    }
}
