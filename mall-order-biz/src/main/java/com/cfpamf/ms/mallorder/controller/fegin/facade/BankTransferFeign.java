package com.cfpamf.ms.mallorder.controller.fegin.facade;

import com.cfpamf.ms.mallorder.api.feign.IBankTransferFeignClient;
import com.cfpamf.ms.mallorder.dto.BankTransferDTO;
import com.cfpamf.ms.mallorder.model.BankTransferModel;
import com.cfpamf.ms.mallorder.po.BzBankTransferPO;
import com.cfpamf.ms.mallorder.vo.BankTransferVO;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.RestController;

import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

@RefreshScope
@RestController
public class BankTransferFeign implements IBankTransferFeignClient {

    @Autowired
    private BankTransferModel bankTransferModel;

    @Override
    public List<BankTransferVO> listByRecommendStoreId(BankTransferDTO bankTransferDTO) {
        List<BzBankTransferPO> entityList = bankTransferModel.listByRecommendStoreId(bankTransferDTO);
        List<BankTransferVO> voList = new ArrayList<>(entityList.size());
        BankTransferVO vo;
        for (BzBankTransferPO entity : entityList) {
            vo = new BankTransferVO();
            vo.setPaySn(entity.getPaySn());
            vo.setPayAmount(entity.getPayAmount());
            vo.setCurrencyCode(entity.getCurrencyCode());
            vo.setOrderDatetime(entity.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
            vo.setTransferState(entity.getTransferState());
            vo.setMemberId(entity.getMemberId());
            vo.setMemberName(entity.getMemberName());
            vo.setUserNo(entity.getUserNo());
            vo.setUserMobile(entity.getUserMobile());
            vo.setPaymentAccount(entity.getPaymentAccount());
            vo.setPaymentName(entity.getPaymentName());
            vo.setPaymentBankName(entity.getPaymentBankName());
            vo.setPaymentBankCode(entity.getPaymentBankCode());
            vo.setVerifyCode(entity.getVerifyCode());
            vo.setRecommendStoreId(entity.getRecommendStoreId());
            vo.setRecommendStoreName(entity.getRecommendStoreName());
            vo.setReceiptAccount(entity.getReceiptAccount());
            vo.setReceiptName(entity.getReceiptName());
            vo.setReceiptBankName(entity.getReceiptBankName());
            vo.setReceiptBankCode(entity.getReceiptBankCode());
            vo.setTransactionTime(entity.getTransactionTime());
            vo.setTransactionNo(entity.getTransactionNo());
            voList.add(vo);
        }
        return voList;
    }
}
