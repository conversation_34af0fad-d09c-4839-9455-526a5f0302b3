package com.cfpamf.ms.mallorder.controller.fegin.facade;

import com.cfpamf.cmis.common.base.CommonResult;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.customer.facade.request.CustBaseBatchReq;
import com.cfpamf.ms.customer.facade.request.MobileLoginAndRegisterReq;
import com.cfpamf.ms.customer.facade.request.cust.CustBaseQueryByIdNoRequest;
import com.cfpamf.ms.customer.facade.request.cust.ModifyMobileStsRequest;
import com.cfpamf.ms.customer.facade.request.cust.RegisterAndCertifyRequest;
import com.cfpamf.ms.customer.facade.request.user.QueryUserBaseInfoReq;
import com.cfpamf.ms.customer.facade.vo.CustBaseInfoVo;
import com.cfpamf.ms.customer.facade.vo.CustInfoVo;
import com.cfpamf.ms.customer.facade.vo.CustomerEtyVo;
import com.cfpamf.ms.customer.facade.vo.UserInfoVo;
import com.cfpamf.ms.customer.facade.vo.cust.RegisrterAndCertifyReturnVo;
import com.cfpamf.ms.customer.facade.vo.user.UserBaseInfoVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;


@FeignClient(name = "ms-service-customer", url = "${ms-service-customer.url}")
public interface CustomerFacade {

    /**
     * 获取公众号客户信息
     *
     * @param mobile
     * @return
     */
    @PostMapping(value = "/cust/customerEty")
    CommonResult<CustomerEtyVo> customerEty(@RequestParam("mobile") String mobile);

    /**
     * 客户基础信息查询接口
     * @param request
     * @return
     */
    @PostMapping(value = "/cust/baseInfoByIdNo")
    Result<CustBaseInfoVo> baseInfoByIdNo(@RequestBody CustBaseQueryByIdNoRequest request);

    /**
     * 根据custNo查询客户信息
     * @param custNo
     * @return
     */
    @PostMapping(value = "/cust/queryByCustNo")
    Result<CustInfoVo> queryByCustNo(@RequestParam("custNo") String custNo) ;

    /**
     * 客户基础信息查询接口(批量)
     * @param req
     * @return
     */
    @PostMapping(value = "/cust/baseInfoBatch")
    Result<List<CustBaseInfoVo>> custBaseBatch(@RequestBody CustBaseBatchReq req);

    /**
     * 注册实名自动化接口
     * @param req
     * @return
     */
    @PostMapping(value = "/cust/registerAndCertify")
    Result<RegisrterAndCertifyReturnVo> registerAndCertify(@RequestBody RegisterAndCertifyRequest req);

    @PostMapping(value = "/cust/modifyMobileStsBatch")
    Result<Void> modifyMobileStsBatch(@RequestBody List<ModifyMobileStsRequest> req);

    @PostMapping(value = "/auth/mobileLoginAndRegister")
    Result<UserInfoVo> mobileLoginAndRegister(@RequestBody @Valid MobileLoginAndRegisterReq req);

}
