package com.cfpamf.ms.mallorder.service;

import com.cfpamf.ares.trade.domain.dto.InvoiceGoodsDTO;
import com.cfpamf.ares.trade.domain.vo.InvoiceBuyerVO;
import com.cfpamf.ares.trade.domain.vo.InvoiceTitleVO;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderProductPO;
import com.cfpamf.ms.mallorder.vo.CreateInvoiceParamVO;
import com.cfpamf.ms.mallorder.vo.MallInvoiceVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * 发票实现接口
 *
 * <AUTHOR>
 * @since 2024/12/3
 */
public interface IOrderInvoiceService {
    /**
     * 根据订单编号查询发票信息
     *
     * @param member 用户信息
     * @param orderSn 订单编号
     * @return 发票信息
     */
    MallInvoiceVO queryInvoice(Member member, String orderSn);

    /**
     * 申请开票
     * @param member  用户信息
     * @param createInvoiceParamVO 申请开票的参数
     * @return 开票结果
     */
    Boolean createInvoice(Member member,CreateInvoiceParamVO createInvoiceParamVO);

    /**
     * 发票冲冲
     *
     * @param member 用户信息
     * @param createInvoiceParamVO 申请冲红的参数
     * @return 冲红结果
     */
    Boolean undoInvoice(Member member, CreateInvoiceParamVO createInvoiceParamVO);

    /**
     * 根据订单编号判断是否存在发票
     *
     * @param orderSn 订单编号
     * @return 是否存在
     */
    Boolean checkExist(String orderSn);

    /**
     * 售后冲红
     *
     * @param orderSn 订单编号
     */
    void undoInvoiceFromAfter(String orderSn);
    /**
     * 获取商品dto列表
     *
     * @param orderProductPOS 商品信息列表
     * @param isAfterInvoke 是否由售后发起
     * @return 商品dto列表
     */

    List<InvoiceGoodsDTO> getGoodsDTOList(List<OrderProductPO> orderProductPOS, Boolean isAfterInvoke);

    /**
     * 发票抬头查询企业抬头
     *
     * @param name
     * @param searchType
     * @return
     */
    List<InvoiceBuyerVO> buyerSearch(String name, Integer searchType);

    /**
     * 查询发票抬头
     *
     * @param member 用户信息
     * @return 发票抬头列表
     */
    List<InvoiceTitleVO> queryTitleList(Member member);

    /**
     * 查询发票抬头详情
     *
     * @param id 发票抬头主键
     * @return 发票抬头详情
     */
    InvoiceTitleVO queryTitleDetail(Long id);

    /**
     * 更新发票抬头
     *
     * @param vo 发票抬头vo
     */
    void updateTitle(InvoiceTitleVO vo);

    /**
     * 保存发票抬头
     *
     * @param vo 发票抬头vo
     */
    void saveTitle(InvoiceTitleVO vo);

    /**
     * 删除发票抬头
     *
     * @param id 发票抬头id
     */
    void delTitle(Long id);

    /**
     * 获取订单可开票金额
     *
     * @param orderSn 订单编号
     * @return 可开票金额
     */
    BigDecimal getInvoiceAmount(String orderSn);

    /**
     * 校验是否可以进行售后平台审核
     *
     * @param orderPODb
     */
    boolean checkPlatformAudit(OrderPO orderPODb);
}
