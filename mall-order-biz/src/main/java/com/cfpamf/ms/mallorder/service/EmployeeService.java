package com.cfpamf.ms.mallorder.service;

import com.cdfinance.hrms.facade.vo.OnBoardEmployeeVO;
import com.cfpamf.ms.mallorder.req.BeverageWorkFlowMessageRequest;
import com.cfpamf.ms.mallorder.req.BeverageWorkReportRequest;
import com.cfpamf.ms.mallorder.vo.AuthBranchAndUserVO;
import com.cfpamf.ms.mallorder.vo.BeverageWorkReportVO;

public interface EmployeeService {

	/**
	 * 查询客户经理所在分支信息
	 * 
	 * @param employeeCode
	 * @return
	 */
	OnBoardEmployeeVO queryByEmployeeCode(String employeeCode);

    BeverageWorkReportVO queryBeverageWorkReport(BeverageWorkReportRequest request);

	AuthBranchAndUserVO getBranchAndUserList(String jobNumber, String orgCode);

}
