package com.cfpamf.ms.mallorder.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.mallorder.common.enums.OrderCreateChannel;
import com.cfpamf.ms.mallorder.common.enums.OrderStatusEnum;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.constant.OrderPerformanceConstant;
import com.cfpamf.ms.mallorder.dto.ErpDeliveryDTO;
import com.cfpamf.ms.mallorder.dto.OrderPerformanceDeliveryDTO;
import com.cfpamf.ms.mallorder.dto.OrderPerformanceOutboundDTO;
import com.cfpamf.ms.mallorder.dto.OrderPerformanceReceiptDTO;
import com.cfpamf.ms.mallorder.enums.ChannelEnum;
import com.cfpamf.ms.mallorder.enums.FacadeOrderReturnStatus;
import com.cfpamf.ms.mallorder.integration.facade.ERPFacade;
import com.cfpamf.ms.mallorder.integration.facade.dto.ErpDepotStockQuery;
import com.cfpamf.ms.mallorder.integration.facade.dto.ErpDepotVO;
import com.cfpamf.ms.mallorder.mapper.OrderAfterMapper;
import com.cfpamf.ms.mallorder.model.OrderLogModel;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.model.OrderProductModel;
import com.cfpamf.ms.mallorder.po.OrderAfterPO;
import com.cfpamf.ms.mallorder.po.OrderLogisticPO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderProductPO;
import com.cfpamf.ms.mallorder.req.OrderDeliveryReq;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallshop.resp.Vendor;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: zml
 * @CreateTime: 2022/7/8 11:27
 */
@Service
@Slf4j
public class OrderExternalPerformanceServiceImpl implements IOrderExternalPerformanceService {


    @Resource
    private IOrderService orderService;

    @Resource
    private OrderModel orderModel;

    @Resource
    private OrderProductModel orderProductModel;

    @Resource
    private OrderLogModel orderLogModel;

    @Resource
    private ERPFacade erpFacade;

    @Resource
    private IOrderLogisticService orderLogisticService;

    @Resource
    private IOrderAfterService orderAfterService;

    @Resource
    private OrderAfterMapper orderAfterMapper;

    @Resource
    private IPerformanceService performanceService;

    /**
     * 供应商发货接口对接
     * 1、根据快递单号查询快递公司
     * 2、更新订单状态
     * 3、插入发货记录
     * 4、产品发货信息记录
     * 5、下发MQ
     * 6、记录日志轨迹
     *
     * @param orderPerformanceDeliveryDTO
     */
    @Override
    public void extDeliver(OrderPerformanceDeliveryDTO orderPerformanceDeliveryDTO) {
        channelValid(orderPerformanceDeliveryDTO.getChannel());


        boolean allowNoLogisticsFlag = orderPerformanceDeliveryDTO.getDeliverType() == OrderConst.DELIVER_TYPE_1 ;
        //构建发货参数
        OrderDeliveryReq deliveryReq = OrderDeliveryReq.builder()
                .orderSn(orderPerformanceDeliveryDTO.getOrderSn())
                .productIds(orderPerformanceDeliveryDTO.getProductIds())
                .deliverType(orderPerformanceDeliveryDTO.getDeliverType())
                .expressCompanyCode(orderPerformanceDeliveryDTO.getExpressCompanyCode())
                .expressName(orderPerformanceDeliveryDTO.getExpressCompanyName())
                .expressNumber(orderPerformanceDeliveryDTO.getExpressNumber())
                .deliverName(orderPerformanceDeliveryDTO.getDeliverName())
                .deliverMobile(orderPerformanceDeliveryDTO.getDeliverMobile())
                .deliverType(orderPerformanceDeliveryDTO.getDeliverType())
                .allowNoLogistics(allowNoLogisticsFlag)
                .channel(OrderCreateChannel.getEnumByValue(orderPerformanceDeliveryDTO.getChannel()))
                .deliveryWarehouse(orderPerformanceDeliveryDTO.getDeliveryWarehouse())
                .deliveryWarehouseName(orderPerformanceDeliveryDTO.getDeliveryWarehouseName())
                .externalOrderNo(orderPerformanceDeliveryDTO.getExternalOrderNo())
                .outboundOrderNo(orderPerformanceDeliveryDTO.getOutboundOrderNo())
                .actualDepotId(orderPerformanceDeliveryDTO.getActualDepotId())
                .build();

        if(ChannelEnum.CHANNEL_HSQ.getValue().equalsIgnoreCase(orderPerformanceDeliveryDTO.getChannel())) {
            deliveryReq.setOutboundProductFlag(Boolean.TRUE);
        }


        String operator = StringUtils.isBlank(orderPerformanceDeliveryDTO.getDeliverName())
                ? ChannelEnum.getDescByValue(orderPerformanceDeliveryDTO.getChannel())
                : orderPerformanceDeliveryDTO.getDeliverName();

        Vendor vendor = new Vendor();
        vendor.setVendorId(OrderConst.LOG_USER_ID);
        vendor.setVendorName(operator);

        orderService.deliveryV2(deliveryReq, vendor);
    }

    private void channelValid(String channel) {
        ChannelEnum channelEnum = ChannelEnum.getValue(channel);
        BizAssertUtil.notNull(channelEnum,"未知渠道，请填写正确的渠道");
    }

    /**
     * 订单签收，全部商品已发货才能签收
     * @param orderPerformanceReceiptDTO
     * @return void
     * */
    @Override
    public boolean extReceipt(OrderPerformanceReceiptDTO orderPerformanceReceiptDTO) {
        channelValid(orderPerformanceReceiptDTO.getChannel());
        // 订单信息
        OrderPO orderPO = orderModel.getOrderByOrderSn(orderPerformanceReceiptDTO.getOrderSn());
        BizAssertUtil.isTrue(orderPO.getLockState() > 0, "售后中的订单无法操作");

        //已签收的订单返回false
        if(orderPO.getOrderState() > OrderStatusEnum.WAIT_RECEIPT.getValue()){
            return false;
        }

        /**
         * 操作人
         */
        String optUserName = StringUtils.isBlank(orderPerformanceReceiptDTO.getReceiptName())
                ? ChannelEnum.getDescByValue(orderPerformanceReceiptDTO.getChannel())
                : orderPerformanceReceiptDTO.getReceiptName();

        /**
         * 签收
         */
        orderModel.receiveOrder(
                orderPO,
                OrderConst.LOG_ROLE,
                OrderConst.LOG_USER_ID,
                optUserName,
                "会员确认收货",
                OrderCreateChannel.getEnumByValue(orderPerformanceReceiptDTO.getChannel())
        );

       return true;
    }

    /***
     * 获取实仓
     */
    @Override
    public List<ErpDepotVO> getDepotListWithResult(Long storeId, List<String> skuIdList) {
        ErpDepotStockQuery query = new ErpDepotStockQuery();
        query.setStoreId(storeId);
        query.setBizSource(OrderPerformanceConstant.ERP_NORM_MALL_BIZ_SOURCE);
        query.setSkuIdList(skuIdList);
        Result<List<ErpDepotVO>>  resultList = erpFacade.getDepotStockList(query);
        if(resultList == null) {
            throw new BusinessException(resultList.getMessage() + "; 获取ERP仓库信息失败，请联系技术人员查看");
        }
        if(CollectionUtils.isEmpty(resultList.getData())) {
            throw new BusinessException("发货商品对应的ERP仓库没有库存，请先补充ERP的库存");
        }
        return resultList.getData();
    }

    /**
     * 更新快递单号 并 记录轨迹
     * @param erpDeliveryDTO
     * @return
     */
    @Override
    public boolean updateDeliveryInfo(ErpDeliveryDTO erpDeliveryDTO) {
        OrderPO orderPO = orderModel.getOrderByOrderSn(erpDeliveryDTO.getOrderSn());

        LambdaUpdateWrapper<OrderLogisticPO> logisticPOLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        logisticPOLambdaUpdateWrapper.eq(OrderLogisticPO::getOrderSn, erpDeliveryDTO.getOrderSn())
                .eq(OrderLogisticPO::getExpressNumber, erpDeliveryDTO.getOrgExpressNubmer())
                .set(OrderLogisticPO::getExpressNumber, erpDeliveryDTO.getExpressNumber())
                .set(OrderLogisticPO::getExpressCompanyCode, erpDeliveryDTO.getExpressCompanyCode())
                .set(OrderLogisticPO::getExpressName, erpDeliveryDTO.getExpressName());
        orderLogisticService.update(logisticPOLambdaUpdateWrapper);


        //记录操作轨迹
        orderLogModel.insertOrderLog(OrderConst.LOG_ROLE, Long.valueOf(erpDeliveryDTO.getOptId()), erpDeliveryDTO.getOptName(), orderPO.getOrderSn(), orderPO.getOrderState(),
                orderPO.getOrderState(), orderPO.getLoanPayState(), "修改快递单号",
                OrderCreateChannel.getEnumByValue(erpDeliveryDTO.getChannel()));
        return Boolean.TRUE;
    }

    /**
     * 检查订单是否处于 已支付状态，&& 是好食期渠道商品 && 无售后
     * 有售后则返回特定的编码  1000001表示有售后
     * @param orderPerformanceDeliveryDTO
     * @return
     */
    @Override
    public JsonResult<Boolean> extOutbound(OrderPerformanceOutboundDTO orderPerformanceDeliveryDTO) {
        ChannelEnum channelEnum = ChannelEnum.getValue(orderPerformanceDeliveryDTO.getChannel());
        BizAssertUtil.isTrue(channelEnum == null, orderPerformanceDeliveryDTO.getChannel() + "该渠道未知，请检查");
        OrderPO orderPO = orderModel.getOrderByOrderSn(orderPerformanceDeliveryDTO.getOrderSn());

        BizAssertUtil.isTrue(orderPO == null, String.format("该订单不存在,orderSn = %s", orderPerformanceDeliveryDTO.getOrderSn()));

        if(orderPO.getOrderState().equals(OrderStatusEnum.CANCELED.getValue())
                || orderPO.getOrderState().equals(OrderStatusEnum.TRADE_CLOSE.getValue()) ) {
            return SldResponse.fail(OrderPerformanceConstant.CHANNEL_ORDER_REFUND_CODE,"该订单已交易关闭");
        }


        BizAssertUtil.isTrue(!Objects.equals(OrderStatusEnum.WAIT_DELIVER.getValue(), orderPO.getOrderState())
                , String.format("该订单不在待发货状态，不能置为发货中状态，请检查,,orderSn = %s", orderPerformanceDeliveryDTO.getOrderSn()));


        List<OrderProductPO> orderProductPOList = orderProductModel.getOrderProductListByOrderSnAndProductIds(orderPerformanceDeliveryDTO.getOrderSn(), orderPerformanceDeliveryDTO.getProductIdList());
        Set<Long> productIdList = new HashSet<>();
        Set<Long> orderProductIdList = new HashSet<>();
        for(OrderProductPO orderProductPO:orderProductPOList) {
            productIdList.add(orderProductPO.getProductId());
            orderProductIdList.add(orderProductPO.getOrderProductId());
        }

        BizAssertUtil.isTrue(!productIdList.containsAll(orderPerformanceDeliveryDTO.getProductIdList()), "请检查参数，该订单下的productId和入参不匹配");


        int count =  orderAfterMapper.getRefundStatusCountByIdAndState(orderProductIdList,FacadeOrderReturnStatus.refuseStateList());

        if(count > 0) {
            return SldResponse.fail(OrderPerformanceConstant.CHANNEL_ORDER_REFUND_CODE,"该订单存在售后");
        }

        //更新商品行为出库中
        int updateCount = performanceService.updateProductForOutbound(orderProductIdList);
        if(updateCount != orderPerformanceDeliveryDTO.getProductIdList().size()) {
            log.warn("更新出库中的商品条数和商品入参长度不一致, orderSn={}", orderPO.getOrderSn());
            log.info("extOutbound updateCount = {}", updateCount);
        }

        return SldResponse.success(Boolean.TRUE);
    }

    @Override
    public JsonResult<Boolean> outboundToWaitDelivery(OrderPerformanceOutboundDTO orderPerformanceDeliveryDTO) {
        ChannelEnum channelEnum = ChannelEnum.getValue(orderPerformanceDeliveryDTO.getChannel());
        BizAssertUtil.isTrue(channelEnum == null, orderPerformanceDeliveryDTO.getChannel() + "该渠道未知，请检查");
        OrderPO orderPO = orderModel.getOrderByOrderSn(orderPerformanceDeliveryDTO.getOrderSn());

        BizAssertUtil.isTrue(orderPO == null, String.format("该订单不存在,orderSn = %s", orderPerformanceDeliveryDTO.getOrderSn()));

        BizAssertUtil.isTrue(!Objects.equals(OrderStatusEnum.WAIT_DELIVER.getValue(), orderPO.getOrderState())
                , String.format("该订单不在待发货状态，不能置为待发货，请检查,orderSn = %s", orderPerformanceDeliveryDTO.getOrderSn()));

        //更新商品行为出库中
        int updateCount = performanceService.outboundToWaitDelivery(orderPerformanceDeliveryDTO.getOrderSn(), orderPerformanceDeliveryDTO.getProductIdList());
        if(updateCount != orderPerformanceDeliveryDTO.getProductIdList().size()) {
            log.warn("更新为待出库的商品条数和商品入参长度不一致, orderSn={}", orderPO.getOrderSn());
            log.info("extOutbound updateCount = {}", updateCount);
        }


        return SldResponse.success(Boolean.TRUE);
    }




}
