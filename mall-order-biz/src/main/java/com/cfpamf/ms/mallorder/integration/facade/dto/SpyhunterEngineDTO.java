package com.cfpamf.ms.mallorder.integration.facade.dto;

import com.cfpamf.ms.loanrisk.facade.vo.risk.RuleHitVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 规则引擎返回结果类
 */
@Data
public class SpyhunterEngineDTO implements Serializable {

    private static final long serialVersionUID = -2234155638332L;
    @ApiModelProperty("响应状态码")
    private String code;

    @ApiModelProperty("是否命中规则")
    private boolean hitResult;

    @ApiModelProperty("响应信息,成功时为空")
    private String msg;

    private String resultWay;

    @ApiModelProperty("响应状态")
    private boolean success;

    @ApiModelProperty("命中的规则业务编码,在规则引擎控制台设置")
    private List<RuleHitVo> ruleHitVos;
}
