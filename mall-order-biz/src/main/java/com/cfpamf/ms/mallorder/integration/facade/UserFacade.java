package com.cfpamf.ms.mallorder.integration.facade;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.customer.facade.request.GetUserNoByMobileReq;
import com.cfpamf.ms.customer.facade.request.QueryCustMobileListReq;
import com.cfpamf.ms.customer.facade.request.ResetPwdRequest;
import com.cfpamf.ms.customer.facade.request.UserRecommendQueryReq;
import com.cfpamf.ms.customer.facade.request.user.QueryUserBaseInfoReq;
import com.cfpamf.ms.customer.facade.request.user.QueryUserInfoByRecommendReq;
import com.cfpamf.ms.customer.facade.request.user.QueryUserLackInfoReq;
import com.cfpamf.ms.customer.facade.vo.*;
import com.cfpamf.ms.customer.facade.vo.user.QuerySensitiveInfoVo;
import com.cfpamf.ms.customer.facade.vo.user.QueryUserLackInfoVo;
import com.cfpamf.ms.customer.facade.vo.user.UserBaseInfoVo;
import java.util.List;

import com.cfpamf.ms.mallorder.vo.custVO.SimpleUserRecommendVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(
        name = "ms-service-customer",
        url = "${ms-service-customer.url}"
)
public interface UserFacade {
    @PostMapping({"/user/getUserNoByMobile"})
    Result<String> getUserNoByMobile(@RequestBody GetUserNoByMobileReq var1);

    @PostMapping({"/user/resetPwd"})
    Result<Void> resetPwd(@RequestBody ResetPwdRequest var1);

    @PostMapping({"/user/queryCustMobileList"})
    Result<List<QueryCustMobileListVo>> queryCustMobileList(@RequestBody QueryCustMobileListReq var1);

    @PostMapping({"/user/lack"})
    Result<QueryUserLackInfoVo> lack(@RequestBody QueryUserLackInfoReq var1);

    @PostMapping({"/user/baseInfoByUserNo"})
    Result<UserBaseInfoVo> baseInfoByUserNo(@RequestBody QueryUserBaseInfoReq var1);

    @RequestMapping(
            value = {"/user/latestDeviceInfoByCustId"},
            method = {RequestMethod.GET}
    )
    Result<CustLoginLogVo> latestDeviceInfoByCustId(@RequestParam("custId") String var1);

    @RequestMapping(
            value = {"/user/baseInfoByRegisterMobile"},
            method = {RequestMethod.GET}
    )
    Result<CustInfoVo> baseInfoByRegisterMobile(@RequestParam("mobile") String var1);

    @RequestMapping(
            value = {"/user/querySensitiveInfo"},
            method = {RequestMethod.GET}
    )
    Result<QuerySensitiveInfoVo> querySensitiveInfo(@RequestParam("userNo") String var1);

    @PostMapping({"/user/queryUserInfoByRecommend"})
    Result<UserInfoVo> queryUserInfoByRecommend(@RequestBody QueryUserInfoByRecommendReq var1);

    @PostMapping(value = "/recommend/query")
    Result<List<SimpleUserRecommendVo>> queryRegisterRecommender(UserRecommendQueryReq req);
}

