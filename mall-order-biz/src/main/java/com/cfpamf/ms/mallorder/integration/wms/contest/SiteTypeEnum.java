package com.cfpamf.ms.mallorder.integration.wms.contest;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

@AllArgsConstructor
@Getter
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum SiteTypeEnum implements IEnum<Integer> {

    DELIVERY_STATION(1, "发货站"),
    SERVICE_STATION(2, "服务站"),
    RECEIVE_STATION(3, "收货站"),
    TRAN_SITE(4,"中转站");

    Integer value;
    String desc;

    public static SiteTypeEnum valueOf(int value){
        for(SiteTypeEnum ps : SiteTypeEnum.values()){
            if (value == ps.value){
                return ps;
            }
        }
        return null;
    }

    @JsonCreator
    public static SiteTypeEnum fromObject(@JsonProperty("value") Integer value) {
        if(value != null) {
            return valueOf(value);
        }
        return null;
    }

    public static List<Integer> getReceiveSiteCodeList(){
        return Lists.newArrayList(SiteTypeEnum.RECEIVE_STATION.value,SiteTypeEnum.TRAN_SITE.value);
    }
}

