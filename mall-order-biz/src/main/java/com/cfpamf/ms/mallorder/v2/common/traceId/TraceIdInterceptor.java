package com.cfpamf.ms.mallorder.v2.common.traceId;

import java.util.UUID;

import javax.annotation.Nullable;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

/**
 * 类TraceIdInterceptor.java的实现描述：
 *
 * <AUTHOR> 15:10
 */
@Component
public class TraceIdInterceptor extends HandlerInterceptorAdapter {

    private static final String TRACE_ID = "X-B3-TraceId";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        ///////////////////////// traceId///////////////////////////////
        if (StringUtils.isEmpty(MDC.get(TRACE_ID))) {
            MDC.put(TRACE_ID, UUID.randomUUID().toString().replaceAll("-", ""));
        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
        @Nullable Exception ex) {
        MDC.clear();
    }
}
