package com.cfpamf.ms.mallorder.integration.facade;


import com.cfpamf.ares.trade.domain.dto.OrderAmountDTO;
import com.cfpamf.ares.trade.domain.dto.OrderStatusSyncDTO;
import com.cfpamf.ares.trade.domain.dto.PayResultNotifyDTO;
import com.cfpamf.ares.trade.domain.dto.TradeDTO;
import com.cfpamf.ares.trade.domain.vo.TradeVO;
import com.cfpamf.athena.common.domain.dto.ResultEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;


@FeignClient(
        name = "ares-trade",
        url = "${ares-trade.url}"
)
public interface AresTradeFacade {
    @PostMapping({"/trade/create"})
    ResultEntity<TradeVO> create(@Valid @RequestBody TradeDTO trade);

    @PostMapping({"/trade/order/status/sync"})
    ResultEntity<Void> orderStatusSync(@Valid @RequestBody OrderStatusSyncDTO orderStatusSync);

    @PostMapping("/order/amount/batch/add")
    ResultEntity<Void> batchAdd(@Valid @RequestBody List<OrderAmountDTO> orderAmount);

    @PostMapping("/pay/result/notify/callback")
    ResultEntity<Void> callback(@Valid @RequestBody PayResultNotifyDTO payResultNotifyDTO);

    @GetMapping("/trade/order/trade/order/receiveConfirmStatusSync")
    ResultEntity<Void> receiveConfirmStatusSync(@RequestParam("channel")String channel,@RequestParam("orderSn")String orderSn,@RequestParam("receiveConfirmStatus")Integer receiveConfirmStatus);
}

