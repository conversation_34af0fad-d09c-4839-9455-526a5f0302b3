package com.cfpamf.ms.mallorder.scheduler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cfpamf.athena.common.domain.dto.ResultEntity;
import com.cfpamf.cmis.common.exception.BizException;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.enums.OrderEventEnum;
import com.cfpamf.ms.mallorder.integration.facade.AresAfterSaleFacade;
import com.cfpamf.ms.mallorder.integration.facade.AresTradeFacade;
import com.cfpamf.ms.mallorder.model.OrderAfterServiceModel;
import com.cfpamf.ms.mallorder.model.OrderExtendModel;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.service.IOrderEventMsgService;
import com.cfpamf.ms.mallorder.service.IOrderReturnService;
import com.slodon.bbc.core.exception.MallException;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * @Author: zhoucs
 * 订单同步交易中心补偿任务
 */
@Slf4j
@Component
public class OrderStatusEventMQMsgMakeupJob {
    private static Integer MAX_RETRY_TIMES = 5;
    @Resource
    private IOrderEventMsgService orderEventMsgService;

    @Resource
    private OrderExtendModel orderExtendModel;

    @Resource
    private OrderModel orderModel;

    @Resource
    private IOrderReturnService orderReturnService;

    @Resource
    private OrderAfterServiceModel orderAfterModel;

    @Resource
    private AresTradeFacade aresTradeFacade;

    @Resource
    private AresAfterSaleFacade aresAfterSaleFacade;

    @XxlJob("orderStatusEventMQMsgMakeupJob")
    public Boolean execute() {
        log.info("订单状态变更MQ消息补偿任务开始 {}", new Date());
        String jobParam = XxlJobHelper.getJobParam();
        //获取待补偿的消息记录
        List<OrderEventMsgPO> orderEventFailedList = getTodoEventList(jobParam);
        for (OrderEventMsgPO orderEvent : orderEventFailedList) {
            // 正向消息补偿
            if (OrderConst.MQ_MESSAGE_TYPE_0.equals(orderEvent.getMsgType())) {
                makeupOrderEventMsg(orderEvent);
            }
            //逆向消息补偿
            if (OrderConst.MQ_MESSAGE_TYPE_1.equals(orderEvent.getMsgType())) {
                makeupRefundEventMsg(orderEvent);
            }
            orderEvent.setRetryTimes(orderEvent.getRetryTimes() + 1);
            LocalDateTime now = LocalDateTime.now();
            orderEvent.setLastNotifyTime(now);
            setNextSyncTime(orderEvent);
        }
        if (!CollectionUtils.isEmpty(orderEventFailedList)) {
            orderEventMsgService.updateBatchById(orderEventFailedList);
        }
        log.info("订单状态变更MQ消息补偿任务结束 {}", new Date());
        return XxlJobHelper.handleSuccess();
    }

    private List<OrderEventMsgPO> getTodoEventList(String jobParam) {
        List<OrderEventMsgPO> orderEventFailedList = new ArrayList<>();
        if (StringUtils.isBlank(jobParam)) {
            LambdaQueryWrapper<OrderEventMsgPO> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(OrderEventMsgPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
            queryWrapper.lt(OrderEventMsgPO::getRetryTimes, MAX_RETRY_TIMES);
            queryWrapper.lt(OrderEventMsgPO::getNextNotifyTime, LocalDateTime.now());
            // 非处理成功事件
            queryWrapper.ne(OrderEventMsgPO::getStatus, OrderConst.SYNC_SUCCESS);
            queryWrapper.orderBy(true, true, OrderEventMsgPO::getId);
            orderEventFailedList = orderEventMsgService.list(queryWrapper);
        } else {
            String[] split = jobParam.split(",");
            orderEventFailedList = orderEventMsgService.getByMsgIdList(Arrays.asList(split));
        }
        return orderEventFailedList;
    }

    private void makeupOrderEventMsg(OrderEventMsgPO orderEvent) {
        OrderPO orderPO = orderModel.getOrderByOrderSn(orderEvent.getOrderSn());
        OrderEventEnum orderEventEnum = OrderEventEnum.getByCode(orderEvent.getEventType());
        try {
            // 存在后续环节同步成功，忽略
            if (orderEventMsgService.laterStepExist(orderEvent.getOrderSn(), orderEvent.getEventType(), orderEvent.getMsgType(), orderEvent.getExecuteOrder())) {
                //支付事件单独补偿
                if (OrderEventEnum.PAY.equals(orderEventEnum)) {
                    makeupPayEvent(orderPO);
                }
                orderEvent.setStatus(OrderConst.SYNC_PASS);
                return;
            }
            orderEventMsgService.doExecuteSync(orderEventEnum, orderPO);
            orderEvent.setStatus(1);
        } catch (Exception e) {
            log.info("订单消息补偿失败 {}", orderEvent.getOrderSn(), e);
        }
    }

    private void makeupRefundEventMsg(OrderEventMsgPO orderEvent) {
        OrderPO orderPO = orderModel.getOrderByOrderSn(orderEvent.getOrderSn());
        OrderEventEnum orderEventEnum = OrderEventEnum.getByCode(orderEvent.getEventType());
        OrderReturnPO orderReturnPO = orderReturnService.getByAfsSn(orderEvent.getAfsSn());
        OrderAfterPO orderAfterPO = orderAfterModel.getOrderAfterByAfsSn(orderEvent.getAfsSn());
        try {
            // 存在后续环节同步成功，忽略
            if (orderEventMsgService.laterStepExist(orderEvent.getOrderSn(), orderEvent.getEventType(), orderEvent.getMsgType(), orderEvent.getExecuteOrder())) {
                //退款成功回调事件补偿
                if (OrderEventEnum.REFUND.equals(orderEventEnum)) {
                    makeupRefundEvent(orderPO, orderReturnPO);
                }
                orderEvent.setStatus(OrderConst.SYNC_PASS);
                return;
            }
            orderEventMsgService.doExecuteRefundSync(orderEventEnum, orderPO, orderReturnPO, orderAfterPO);
            orderEvent.setStatus(1);
        } catch (Exception e) {
            log.info("售后单消息补偿失败 {}", orderEvent.getAfsSn(), e);
        }
    }

    private void setNextSyncTime(OrderEventMsgPO orderEvent) {
        Integer notifyTimes = orderEvent.getRetryTimes();
        LocalDateTime lastNotifyTime = orderEvent.getLastNotifyTime();
        if (notifyTimes == 1) {
            orderEvent.setNextNotifyTime(lastNotifyTime.plusMinutes(5));
        }
        if (notifyTimes == 2) {
            orderEvent.setNextNotifyTime(lastNotifyTime.plusMinutes(10));
        }
        if (notifyTimes == 3) {
            orderEvent.setNextNotifyTime(lastNotifyTime.plusMinutes(20));
        }
        if (notifyTimes == 4) {
            orderEvent.setNextNotifyTime(lastNotifyTime.plusMinutes(40));
        }
        if (notifyTimes == 5) {
            orderEvent.setNextNotifyTime(lastNotifyTime.plusMinutes(60));
        }
    }

    /**
     * 单独补偿支付成功回调
     * @param orderPO
     */
    private void makeupPayEvent(OrderPO orderPO) {
        ResultEntity<Void> callbackResult = aresTradeFacade.callback(orderEventMsgService.buildPayResultNotifyDTO(orderPO));
        if (!callbackResult.isSuccess()) {
            log.info("支付成功回调同步失败 {} {}", orderPO.getOrderSn(), callbackResult.getMsg());
            throw new BizException("支付成功回调同步失败：" + callbackResult.getMsg());
        }
    }

    /**
     * 单独补偿退款成功回调
     * @param orderPO
     * @param orderReturnPO
     */
    private void makeupRefundEvent(OrderPO orderPO, OrderReturnPO orderReturnPO) {
        ResultEntity<Boolean> notifyResult = aresAfterSaleFacade.refundNotify(orderEventMsgService.buildAfterSaleRefundNotifyDTO(orderPO, orderReturnPO));
        if (!notifyResult.isSuccess()) {
            log.info("售后结果通知失败 {} {}", orderReturnPO.getAfsSn(), notifyResult.getMsg());
            throw new MallException("售后结果通知失败：" + notifyResult.getMsg());
        }
    }

}
