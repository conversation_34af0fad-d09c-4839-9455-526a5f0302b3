package com.cfpamf.ms.mallorder.service.impl;

import com.cfpamf.ms.mallorder.dto.CategoryLoanDTO;
import com.cfpamf.ms.mallorder.dto.CategoryLoanDetailDTO;
import com.cfpamf.ms.mallorder.po.BzOrderPayCategoryPO;
import com.cfpamf.ms.mallorder.mapper.BzOrderPayCategoryMapper;
import com.cfpamf.ms.mallorder.service.IBzOrderPayCategoryService;
import com.cfpamf.framework.autoconfigure.mybatis.BaseRepoServiceImpl;
import com.cfpamf.ms.mallorder.vo.BzOrderPayCategoryVO;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 支付匹配分类表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-26
 */
@Service
public class BzOrderPayCategoryServiceImpl extends BaseRepoServiceImpl<BzOrderPayCategoryMapper, BzOrderPayCategoryPO> implements IBzOrderPayCategoryService {

    @Override
    public Boolean isAllowLoan(CategoryLoanDTO categoryLoanDTO) {
        List<CategoryLoanDetailDTO> loanDetailDTOS = categoryLoanDTO.getCategoryLoanDetailDTOS();
        if (CollectionUtils.isEmpty(loanDetailDTOS)) {
            return Boolean.FALSE;
        }
        Set<Integer> categoryId1s = new HashSet<>();
        Set<Integer> categoryId2s = new HashSet<>();
        Set<Integer> categoryId3s = new HashSet<>();
        //提取店铺类目，去重
        for (CategoryLoanDetailDTO loanDetailDTO : loanDetailDTOS) {
            categoryId1s.add(loanDetailDTO.getCategoryId1());
            categoryId2s.add(loanDetailDTO.getCategoryId2());
            categoryId3s.add(loanDetailDTO.getCategoryId3());
        }
        //提取支付适用类目
        List<BzOrderPayCategoryPO> categoryDb = this.lambdaQuery().list();
        List<Integer> categoryIdDb = categoryDb.stream().map(BzOrderPayCategoryPO::getCategoryId).collect(Collectors.toList());

        //判断店铺一级类目是否有存在适用类目表
        boolean exist = this.fullIntersection(categoryId1s, categoryIdDb);
        if (exist) {
            return Boolean.TRUE;
        }
        //一级类目不存在，判断店铺二级类目是否有存在
        boolean exist2 = this.fullIntersection(categoryId2s, categoryIdDb);
        if (exist2) {
            return Boolean.TRUE;
        }
        //二级类目不存在，判断店铺三级类目是否有存在
        boolean exist3 = this.fullIntersection(categoryId3s, categoryIdDb);
        if (exist3) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    private boolean fullIntersection(Set<Integer> list1, List<Integer> list2) {
        for (Integer s : list1) {
            if (list2.contains(s)) {
                return true;
            }
        }
        return false;
    }
}
