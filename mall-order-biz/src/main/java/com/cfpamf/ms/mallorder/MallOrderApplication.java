package com.cfpamf.ms.mallorder;

import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.support.config.FastJsonConfig;
import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;
import com.slodon.bbc.starter.mq.SlodonMqAutoConfig;
import feign.Logger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.netflix.hystrix.EnableHystrix;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.List;

@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class,
        DataSourceTransactionManagerAutoConfiguration.class, SlodonMqAutoConfig.class,
        org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration.class})
@EnableSwagger2
@EnableRetry
@ComponentScan(basePackages = {
        "com.slodon.bbc.core",
        "com.cfpamf.smartid",
        "com.cfpamf.ms.mallorder.**",
        "com.cfpamf.ms.mall.filecenter"})
@EnableFeignClients(basePackages = {
        "com.cfpamf.mallpayment.facade.api",
        "com.cfpamf.ms.**.api",
        "com.cfpamf.dts.biz.api",
        "com.cdfinance.ms.**.api",
        "com.cfpamf.ms.mallorder.**.facade",
        "com.cfpamf.ms.**.facade",
        "com.cfpamf.ms.mall.filecenter.facade"
})
@EnableDiscoveryClient
@EnableTransactionManagement
@EnableHystrix
//@MapperScan(basePackages = "com.cfpamf.ms.mallorder.mapper")
@ComponentScan({"com.cfpamf.ms.bms.facade.util"})
@Slf4j
public class MallOrderApplication extends WebMvcConfigurerAdapter {

    public static void main(String[] args) {
        SpringApplication.run(MallOrderApplication.class, args);
        log.info("===================================================================");
        log.info("mall-order start success");
        log.info("===================================================================");
    }

    @Bean
    public Logger.Level feignLoggerLevel() {
        return Logger.Level.FULL;
    }

    @Override
    public void configureMessageConverters (List<HttpMessageConverter<?>> converters) {
        super.configureMessageConverters (converters);
        //1.需要先定义一个convert转换消息的对象；
        FastJsonHttpMessageConverter fastConverter = new FastJsonHttpMessageConverter ();
        //2.添加fastJson的配置信息，比如：是否要格式化返回的json数据；
        FastJsonConfig fastJsonConfig = new FastJsonConfig ();
        fastJsonConfig.setSerializerFeatures (SerializerFeature.PrettyFormat);
        //fastJsonConfig.setSerializerFeatures(SerializerFeature.WriteEnumUsingToString);
        fastJsonConfig.setSerializerFeatures (SerializerFeature.DisableCircularReferenceDetect);     //关闭循环引用
        //全局时间字段格式(会覆盖实体内注解)
        //fastJsonConfig.setDateFormat(MallConstants.FORMAT_DATE);
        //3.在convert中添加配置信息
        fastConverter.setFastJsonConfig (fastJsonConfig);
        //4.将convert添加到converters中
        converters.add (fastConverter);
    }
}
