package com.cfpamf.ms.mallorder.scheduler;

import com.cfpamf.ms.mallorder.model.ComplainModel;
import com.slodon.bbc.core.util.AssertUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 定时器定时处理商家申诉超过7天的交易投诉自动投诉成功
 * <p>
 * cron: 0 30 5 * * ?
 */
@Component
@Slf4j
public class StoreComplaintSuccessJob {

    @Autowired
    private ComplainModel complainModel;

    @XxlJob(value = "StoreComplaintSuccessJob")
    public ReturnT<String> execute(String s) {

        long startTime = System.currentTimeMillis();
        XxlJobHelper.log("start job at local time: {0}", LocalDateTime.now());

        log.info("jobSystemCancelOrder() start");

        try {
            boolean jobResult = complainModel.jobAutoComplaintSuccess();
            AssertUtil.isTrue(!jobResult, "[jobAutoComplaintSuccess] 定时任务处理商家申诉超过7天的交易投诉自动投诉成功时失败");
        } catch (Exception e) {
            log.error("jobAutoComplaintSuccess()", e);
        }

        XxlJobHelper.log("finish job at local time: {0}  cost:{1}",
                LocalDateTime.now(),
                (System.currentTimeMillis() - startTime) / 1000);

        return new ReturnT("SUCCESS");
    }
}
