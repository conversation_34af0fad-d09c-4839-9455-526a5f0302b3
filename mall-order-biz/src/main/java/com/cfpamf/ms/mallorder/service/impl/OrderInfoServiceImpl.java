package com.cfpamf.ms.mallorder.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cdfinance.hrms.facade.vo.BranchRelationVO;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.bizconfig.facade.vo.UserVo;
import com.cfpamf.ms.bms.facade.vo.DictionaryItemVO;
import com.cfpamf.ms.customer.facade.vo.user.UserBaseInfoVo;
import com.cfpamf.ms.loan.facade.vo.CmisContractFileVo;
import com.cfpamf.ms.loan.facade.vo.external.mall.CdmallOrderVo;
import com.cfpamf.ms.mall.filecenter.constant.IScenesMaterialProofConstant;
import com.cfpamf.ms.mall.filecenter.domain.vo.FileScenesProofVO;
import com.cfpamf.ms.mall.filecenter.domain.vo.FileScenesWithMaterialVO;
import com.cfpamf.ms.mallgoods.facade.api.GoodsExtendFeignClient;
import com.cfpamf.ms.mallgoods.facade.api.GoodsSharecodeBindUserFeignClient;
import com.cfpamf.ms.mallgoods.facade.api.ProductExtendFeignClient;
import com.cfpamf.ms.mallgoods.facade.api.ProductFeignClient;
import com.cfpamf.ms.mallgoods.facade.request.GoodsExtendExample;
import com.cfpamf.ms.mallgoods.facade.vo.GoodsExtend;
import com.cfpamf.ms.mallgoods.facade.vo.GoodsSharecodeBindUser;
import com.cfpamf.ms.mallgoods.facade.vo.Product;
import com.cfpamf.ms.mallgoods.facade.vo.ProductExtend;
import com.cfpamf.ms.mallmember.api.MemberFeignClient;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.builder.OrderQueryBuilder;
import com.cfpamf.ms.mallorder.common.config.InvoiceConfig;
import com.cfpamf.ms.mallorder.common.config.OrderMaterialConfig;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.constant.OrderProductConst;
import com.cfpamf.ms.mallorder.common.constant.OrdersAfsConst;
import com.cfpamf.ms.mallorder.common.constant.SymbolConstant;
import com.cfpamf.ms.mallorder.common.enums.*;
import com.cfpamf.ms.mallorder.common.help.SystemSettingObtainHelper;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.common.util.OrderLocalUtils;
import com.cfpamf.ms.mallorder.constant.ExchangeOrderConst;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.*;
import com.cfpamf.ms.mallorder.enums.OrderEventEnum;
import com.cfpamf.ms.mallorder.enums.OrderPerformanceModeEnum;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.enums.SettleModeEnum;
import com.cfpamf.ms.mallorder.integration.cust.BizConfigIntegration;
import com.cfpamf.ms.mallorder.integration.cust.CustomerIntegration;
import com.cfpamf.ms.mallorder.integration.erp.ERPIntegration;
import com.cfpamf.ms.mallorder.integration.erp.vo.MallProductQuery;
import com.cfpamf.ms.mallorder.integration.erp.vo.ProductVO;
import com.cfpamf.ms.mallorder.integration.facade.BmsIntegration;
import com.cfpamf.ms.mallorder.integration.facade.ContractFacade;
import com.cfpamf.ms.mallorder.integration.filecenter.FileCenterIntegration;
import com.cfpamf.ms.mallorder.integration.filecenter.SceneTypeEnum;
import com.cfpamf.ms.mallorder.integration.hrms.HrmsIntegration;
import com.cfpamf.ms.mallorder.integration.loan.LoanPayIntegration;
import com.cfpamf.ms.mallorder.integration.shop.ShopIntegration;
import com.cfpamf.ms.mallorder.mapper.OrderExchangeDetailMapper;
import com.cfpamf.ms.mallorder.mapper.OrderExtendMapper;
import com.cfpamf.ms.mallorder.mapper.OrderLogisticMapper;
import com.cfpamf.ms.mallorder.mapper.OrderMapper;
import com.cfpamf.ms.mallorder.model.*;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.req.OfflineOrderListQueryRequest;
import com.cfpamf.ms.mallorder.req.OrderListQueryRequest;
import com.cfpamf.ms.mallorder.req.seller.SellerOrderInfoListRequest;
import com.cfpamf.ms.mallorder.request.*;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.v2.builder.OrderPresellBuilder;
import com.cfpamf.ms.mallorder.v2.domain.dto.OrderPresellDTO;
import com.cfpamf.ms.mallorder.v2.domain.dto.PreSellPromotionPayDetail;
import com.cfpamf.ms.mallorder.v2.service.IPreSellPromotionService;
import com.cfpamf.ms.mallorder.v2.service.OrderPayRecordService;
import com.cfpamf.ms.mallorder.v2.service.OrderPresellService;
import com.cfpamf.ms.mallorder.v2.strategy.OrderTypeQueryStrategy;
import com.cfpamf.ms.mallorder.v2.strategy.context.OrderTypeQueryStrategyContext;
import com.cfpamf.ms.mallorder.vo.*;
import com.cfpamf.ms.mallorder.vo.filescene.FileScenesProductWithResultVO;
import com.cfpamf.ms.mallpromotion.api.*;
import com.cfpamf.ms.mallpromotion.constant.PromotionConst;
import com.cfpamf.ms.mallpromotion.enums.CouponFunder;
import com.cfpamf.ms.mallpromotion.request.LadderGroupOrderExtend;
import com.cfpamf.ms.mallpromotion.request.PresellOrderExtendExample;
import com.cfpamf.ms.mallpromotion.request.SpellTeamMemberExample;
import com.cfpamf.ms.mallpromotion.vo.*;
import com.cfpamf.ms.mallshop.api.HomeServiceFeignClient;
import com.cfpamf.ms.mallshop.api.StoreFeignClient;
import com.cfpamf.ms.mallshop.api.StoreIsolateWhiteListFeignClient;
import com.cfpamf.ms.mallshop.enums.StoreStateEnum;
import com.cfpamf.ms.mallshop.enums.WhiteListEnum;
import com.cfpamf.ms.mallshop.request.StoreExample;
import com.cfpamf.ms.mallshop.resp.Store;
import com.cfpamf.ms.mallshop.resp.Vendor;
import com.cfpamf.ms.mallshop.vo.SelfLiftingPointVo;
import com.cfpamf.ms.mallshop.vo.WorkerVo;
import com.cfpamf.ms.mallsystem.vo.Admin;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.slodon.bbc.core.constant.LadderGroupConst;
import com.slodon.bbc.core.constant.SpellConst;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.exception.MallException;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.PageVO;
import com.slodon.bbc.core.response.PagerInfo;
import com.slodon.bbc.core.response.SldResponse;
import com.slodon.bbc.core.util.AssertUtil;
import com.slodon.bbc.core.util.FileUrlUtil;
import com.slodon.bbc.core.util.StringUtil;
import com.slodon.bbc.core.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 订单信息service
 */
@Slf4j
@Service
public class OrderInfoServiceImpl extends ServiceImpl<OrderMapper, OrderPO> implements IOrderInfoService {

    public static final LocalDateTime INIT_DATETIME = LocalDateTime.of(1970, 1, 1, 8, 0, 0);

    public static final DateTimeFormatter FULL_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Value("${platform-collection.account}")
    private String platformCollectionAccount;

    @Value("${promotion.deposit.remind.days:3}")
    private int promotionDepositRemindDays;
    @Resource
    private IPreSellPromotionService preSellPromotionService;
    @Resource
    private IOrderExtendService orderExtendService;
    @Resource
    private IOrderLogService orderLogService;
    @Resource
    private IOrderExtendFinanceService orderExtendFinanceService;
    @Resource
    private IOrderPromotionDetailService orderPromotionDetailService;
    @Resource
    private IOrderProductExtendService orderProductExtendService;
    @Resource
    private IOrderPayService orderPayService;
    @Resource
    private IOrderService orderService;
    @Resource
    private OrderModel orderModel;
    @Resource
    private PresellOrderExtendFeignClient presellOrderExtendFeignClient;
    @Resource
    private LadderGroupOrderExtendFeignClient ladderGroupOrderExtendFeignClient;
    @Resource
    private LadderGroupFeignClient ladderGroupFeignClient;
    @Resource
    private OrderProductModel orderProductModel;
    @Resource
    private OrderProductExtendModel orderProductExtendModel;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private IOrderAfterService orderAfterService;
    @Resource
    private IOrderReturnService orderReturnService;
    @Resource
    private IOrderPerformanceBelongsService orderPerformanceBelongsService;
    @Resource
    private OrderExtendModel orderExtendModel;
    @Resource
    private OrderLogModel orderLogModel;
    @Resource
    private OrderPayModel orderPayModel;
    @Resource
    private OrderAfterServiceModel orderAfterServiceModel;
    @Resource
    private OrderReturnModel orderReturnModel;
    @Resource
    private StoreFeignClient storeFeignClient;
    @Resource
    private SpellTeamFeignClient spellTeamFeignClient;
    @Resource
    private SpellTeamMemberFeignClient spellTeamMemberFeignClient;
    @Resource
    private SpellFeignClient spellFeignClient;
    @Resource
    private MemberFeignClient memberFeignClient;
    @Resource
    private GoodsSharecodeBindUserFeignClient goodsSharecodeBindUserFeignClient;
    @Resource
    private IOrderPriceRecordService orderPriceRecordService;
    @Resource
    private OrderMapper orderMapper;
    @Resource
    private OrderLogisticMapper orderLogisticMapper;

    @Autowired
    private IOrderProductService orderProductService;
    @Autowired
    private IBzOrderSearchHistoryService orderSearchHistoryService;
    @Autowired
    private CustomerIntegration customerIntegration;
    @Autowired
    private LoanPayIntegration loanPayIntegration;
    @Resource
    private SystemSettingObtainHelper systemSettingObtainHelper;
    @Autowired
    private ProductExtendFeignClient productExtendFeignClient;
    @Autowired
    private BankTransferModel bankTransferModel;
    @Autowired
    private ProductFeignClient productFeignClient;
    @Autowired
    private GoodsExtendFeignClient goodsExtendFeignClient;
    @Resource
    private HrmsIntegration hrmsIntegration;
    @Resource
    private OrderExtendMapper orderExtendMapper;

    @Resource
    private OrderTypeQueryStrategyContext orderTypeQueryStrategyContext;

    @Resource
    private OrderPresellService orderPresellService;

    @Resource
    private UserInfoComponent userInfoComponent;

    @Resource
    private HomeServiceFeignClient homeServiceFeignClient;

    @Resource
    private ContractFacade contractFacade;

    @Resource
    private IOrderExchangeService orderExchangeService;

    @Resource
    private OrderExchangeDetailMapper orderExchangeDetailMapper;

    @Resource
    private ERPIntegration erpIntegration;

    @Resource
    private StoreIsolateWhiteListFeignClient storeIsolateWhiteListFeignClient;

    @Autowired
    private FileCenterIntegration fileCenterIntegration;

    @Resource
    private IOrderLogisticService orderLogisticService;

    @Resource
    private IOrderExchangeDetailService orderExchangeDetailService;

    @Autowired
    private OrderLocalUtils orderLocalUtils;

    @Autowired
    private IOrderTradeProofService orderTradeProofService;

    @Resource
    private IOrderOfflineExtendService orderOfflineExtendService;

    @Resource
    private OrderOfflineService orderOfflineService;

    @Resource
    private IPerformanceService performanceService;

    @Resource
    private IBzOrderProductCombinationService orderProductCombinationService;

    @Resource
    private EmployeeService employeeService;

    @Resource
    private OrderCreateHelper orderCreateHelper;
    @Resource
    private BmsIntegration bmsIntegration;

    @Resource
    private BizConfigIntegration bizConfigIntegration;

    @Autowired
    private OrderProductAgricExtendModel orderProductAgricExtendModel;

    @Autowired
    private OrderProductErpExtendService orderProductErpExtendService;

    @Autowired
    private OrderPayRecordService payRecordService;

    @Autowired
    private IOrderProductCouponService productCouponService;

    @Autowired
    private IOrderTradeProofService tradeProofService;

    @Autowired
    private OrderMaterialConfig orderMaterialConfig;

    @Autowired
    private IOrderInvoiceService orderInvoiceService;

    @Autowired
    private InvoiceConfig invoiceConfig;

    @Autowired
    private ShopIntegration shopIntegration;

    /**
     * 处理平台端订单列表是否展示退款按钮
     *
     * @param vos 列表信息
     */
    private void dealAdminOrderListShowRefundBtn(List<OrderListVOV2> vos) {
        if (CollectionUtils.isEmpty(vos)) {
            return;
        }
        // 设置平台端订单列表是否展示退款按钮
        try {
            List<Long> storeIds = new ArrayList<>();
            vos.stream().mapToLong(o -> Long.parseLong(o.getStoreId())).forEach(storeIds::add);
            Map<Long, Boolean> storeId2Boolean = storeIsolateWhiteListFeignClient.queryStoreWhiteListBatch(WhiteListEnum.INNER_REFUND, storeIds);

            StoreExample storeExample = new StoreExample();
            storeExample.setStoreIdIn(storeIds.stream().map(String::valueOf).collect(Collectors.joining(SymbolConstant.COMMA_EN)));
            Map<Long, Store> storeId2Store = storeFeignClient.getStoreList(storeExample).stream().collect(Collectors.toMap(Store::getStoreId, Function.identity()));

            for (OrderListVOV2 vo : vos) {
                // 订单状态 和 平台退款白名单校验
                if (OrderStatusEnum.paidStatus().contains(vo.getOrderState()) && storeId2Boolean.get(Long.parseLong(vo.getStoreId()))) {
                    vo.setIsAdminShowRefundBtn(Boolean.TRUE);
                }
                if (OrderStatusEnum.isPresellDeposit(vo.getOrderState(), vo.getOrderType()) && storeId2Boolean.get(Long.parseLong(vo.getStoreId()))) {
                    vo.setIsAdminShowRefundBtn(Boolean.TRUE);
                }
                // 查不到店铺不展示
                if (Objects.isNull(storeId2Store.get(Long.valueOf(vo.getStoreId())))) {
                    vo.setIsAdminShowRefundBtn(Boolean.FALSE);
                    continue;
                }
                // 注销中的店铺不展示
                if (StoreStateEnum.LOGOUT.getCode().equals(storeId2Store.get(Long.valueOf(vo.getStoreId())).getState())
                        || StoreStateEnum.LOGOUTING.getCode().equals(storeId2Store.get(Long.valueOf(vo.getStoreId())).getState())) {
                    vo.setIsAdminShowRefundBtn(Boolean.FALSE);
                    continue;
                }
                // 换货后的订单也不展示
                if (ExchangeOrderConst.ORDER_EXCHANGE_FLAG_2 == vo.getExchangeFlag()) {
                    vo.setIsAdminShowRefundBtn(Boolean.FALSE);
                    continue;
                }

                // 获取售后限制时间校验
                if (!OrderStatusEnum.TRADE_SUCCESS.isTrue(vo.getOrderState())) {
                    continue;
                }
                if (vo.getAfterSalesDeadline() != null) {
                    if (vo.getAfterSalesDeadline().before(new Date())) {
                        vo.setIsAdminShowRefundBtn(Boolean.FALSE);
                    }
                } else {
                    if (vo.getFinishTime().before(TimeUtil.getDateApartDay(-systemSettingObtainHelper.getTimeLimitOfAfterSale()))) {
                        vo.setIsAdminShowRefundBtn(Boolean.FALSE);
                    }
                }

            }

        } catch (Exception e) {
            log.warn("dealAdminOrderListShowRefundBtn error, show the default value FALSE", e);
        }
    }


    /**
     * 处理平台端订单列表是否展示退款按钮
     *
     * @param vos 列表信息
     */
    private void dealAdminOfflineOrderListShowRefundBtn(List<OfflineOrderListVOV2> vos) {
        if (CollectionUtils.isEmpty(vos)) {
            return;
        }
        // 设置平台端订单列表是否展示退款按钮
        try {
            List<Long> storeIds = new ArrayList<>();
            vos.stream().mapToLong(o -> Long.parseLong(o.getStoreId())).forEach(storeIds::add);
            Map<Long, Boolean> storeId2Boolean = storeIsolateWhiteListFeignClient.queryStoreWhiteListBatch(WhiteListEnum.INNER_REFUND, storeIds);

            for (OrderListVOV2 vo : vos) {
                // 订单状态 和 平台退款白名单校验
                if (OrderStatusEnum.paidStatus().contains(vo.getOrderState()) && storeId2Boolean.get(Long.parseLong(vo.getStoreId()))) {
                    vo.setIsAdminShowRefundBtn(Boolean.TRUE);
                }

                // 换货后的订单也不展示
                if (ExchangeOrderConst.ORDER_EXCHANGE_FLAG_2 == vo.getExchangeFlag()) {
                    vo.setIsAdminShowRefundBtn(Boolean.FALSE);
                    continue;
                }

                // 获取售后限制时间校验
                if (!OrderStatusEnum.TRADE_SUCCESS.isTrue(vo.getOrderState())) {
                    continue;
                }
                if (vo.getAfterSalesDeadline() != null) {
                    if (vo.getAfterSalesDeadline().before(new Date())) {
                        vo.setIsAdminShowRefundBtn(Boolean.FALSE);
                    }
                } else {
                    if (vo.getFinishTime().before(TimeUtil.getDateApartDay(-systemSettingObtainHelper.getTimeLimitOfAfterSale()))) {
                        vo.setIsAdminShowRefundBtn(Boolean.FALSE);
                    }
                }

            }

        } catch (Exception e) {
            log.warn("dealAdminOfflineOrderListShowRefundBtn error, show the default value FALSE", e);
        }
    }


    @Override
    public JsonResult<PageVO<MemberOrderListVO>> listPageOrder(String orderSn, Integer orderState, Integer evaluateState,
                                                               PagerInfo pager, Member member, String paySn, Integer orderType,
                                                               Integer isSpelling, String channel) {
        List<MemberOrderListVO> vos = new ArrayList<>();
        //根据父订单分组
        String fields = "parent_sn";
        OrderExample orderExample = new OrderExample();
        orderExample.setOrderSnLike(orderSn);
        orderExample.setEvaluateState(evaluateState);
        orderExample.setMemberId(member.getMemberId());
        orderExample.setPaySn(paySn);
        orderExample.setOrderType(orderType);
        orderExample.setChannel(channel);
        orderExample.setGroupBy("parent_sn");
        orderExample.setOrderBy("parent_sn desc");
        orderExample.setDeleteState(OrderConst.DELETE_STATE_0);
        // 待收货订单添加查询【部分发货】
        if (OrderStatusEnum.WAIT_RECEIPT.getValue().equals(orderState)) {
            orderExample.setOrderStateIn("25,30");
        } else {
            orderExample.setOrderState(orderState);
        }
        List<OrderPO> orderPOListParent = orderModel.getOrderFieldList(fields, orderExample, pager);

        //遍历父订单列表
        for (OrderPO orderPOParent : orderPOListParent) {
            OrderExample orderExample1 = new OrderExample();
            orderExample1.setParentSn(orderPOParent.getParentSn());
            //获取子订单列表
            List<OrderPO> orderPOListChild = orderModel.getOrderList(orderExample1, null);

            List<OrderPO> showList = new ArrayList<>();
            //合计金额
            BigDecimal totalMoney = BigDecimal.ZERO;
            //共计商品数
            Integer goodsNum = 0;
            //订单子状态
            Integer orderSubState = 0;
            //定金剩余时间
            Date depositRemainTime = null;
            //尾款开始时间
            Date remainTime = null;
            //预售发货时间
            Date deliverTime = null;
            //是否有定金
            boolean isHasDeposit = false;
            //是否退还定金
            Boolean isRefundDeposit = false;
            //遍历子订单
            for (OrderPO orderPOChild : orderPOListChild) {
                if (!orderPOChild.getOrderState().equals(OrderConst.ORDER_STATE_10)) {
                    //查询预售订单扩展信息
                    if (orderPOChild.getOrderType() == PromotionConst.PROMOTION_TYPE_103) {
                        PresellOrderExtendExample presellOrderExtendExample = new PresellOrderExtendExample();
                        presellOrderExtendExample.setOrderSn(orderPOChild.getOrderSn());
                        List<PresellOrderExtendVO> presellOrderExtendList = presellOrderExtendFeignClient.getPresellOrderExtendList(presellOrderExtendExample);
                        AssertUtil.notEmpty(presellOrderExtendList, "获取预售订单扩展信息为空");
                        PresellOrderExtendVO presellOrderExtend = presellOrderExtendList.get(0);
                        orderSubState = presellOrderExtend.getOrderSubState();
                        if (presellOrderExtend.getIsAllPay() == OrderConst.IS_ALL_PAY_0) {
                            depositRemainTime = presellOrderExtend.getDepositEndTime();
                            remainTime = presellOrderExtend.getRemainStartTime();
                            isHasDeposit = true;
                        }
                        deliverTime = presellOrderExtend.getDeliverTime();
                    }
                    //查询阶梯团订单扩展信息
                    if (orderPOChild.getOrderType() == PromotionConst.PROMOTION_TYPE_105) {
                        LadderGroupOrderExtend groupOrderExtendExample = new LadderGroupOrderExtend();
                        groupOrderExtendExample.setOrderSn(orderPOChild.getOrderSn());
                        List<LadderGroupOrderExtendVO> groupOrderExtendList = ladderGroupOrderExtendFeignClient.getLadderGroupOrderExtendList(groupOrderExtendExample);
                        AssertUtil.notEmpty(groupOrderExtendList, "获取阶梯团订单扩展信息为空");
                        LadderGroupOrderExtendVO groupOrderExtend = groupOrderExtendList.get(0);
                        orderSubState = groupOrderExtend.getOrderSubState();
                        remainTime = groupOrderExtend.getRemainStartTime();
                        isHasDeposit = true;
                        //查询是否退还定金
                        LadderGroupVO ladderGroup = ladderGroupFeignClient.getLadderGroupByGroupId(groupOrderExtend.getGroupId());
                        AssertUtil.notNull(ladderGroup, "获取阶梯团活动信息为空，请重试！");
                        if (ladderGroup.getIsRefundDeposit() == LadderGroupConst.IS_REFUND_DEPOSIT_1) {
                            isRefundDeposit = true;
                        }
                    }
                    OrderExtendPO orderExtendPO = orderExtendService.getOrderExtendByOrderSn(orderPOChild.getOrderSn());
                    MemberOrderListVO vo = new MemberOrderListVO(orderPOChild, orderExtendPO, orderSubState,
                            remainTime, isHasDeposit);
                    vo.setShowExtendAutoReceiveTimeBtn(this.dealExtendAutoReceiveTimeBtn(orderPOChild));
                    vo.setIsRefundDeposit(isRefundDeposit);
                    if (orderPOChild.getOrderType() == PromotionConst.PROMOTION_TYPE_103) {
                        if (depositRemainTime != null) {
                            long time1 = depositRemainTime.getTime();
                            long time2 = System.currentTimeMillis();
                            long differTime = (time1 - time2) / 1000;
                            vo.setDepositRemainTime(differTime < 0 ? 0 : differTime);
                            long time3 = remainTime.getTime();
                            long remainEndTime = (time3 - time2) / 1000;
                            vo.setRemainEndTime(remainEndTime < 0 ? 0 : remainEndTime);
                        }
                    }
                    vo.setDeliverTime(TimeUtil.getZDDay(deliverTime));
                    vo.setOrderSubState(orderSubState);
                    //获取商家名称
                    vo.setStoreId(orderPOChild.getStoreId());
                    vo.setStoreName(orderPOChild.getStoreName());
                    //合计金额
                    totalMoney = totalMoney.add(orderPOChild.getOrderAmount());
                    vo.setTotalMoney(totalMoney);
                    //获取订单货品列表信息
                    OrderProductExample orderProductExample = new OrderProductExample();
                    orderProductExample.setOrderSn(orderPOChild.getOrderSn());
                    List<OrderProductPO> orderProductPOList = orderProductModel.getOrderProductList(orderProductExample, null);
                    List<OrderProductListVO> productListVOS = new ArrayList<>();
                    if (!CollectionUtils.isEmpty(orderProductPOList)) {
                        List<Long> products = new ArrayList<>();
                        for (OrderProductPO orderProductPO : orderProductPOList) {
                            //有退货,查询退货是否已完成
                            dealReturnInfo(orderProductPO, null);
                            OrderProductListVO orderProductListVO = new OrderProductListVO(orderProductPO);
                            productListVOS.add(orderProductListVO);
                            //合计商品数
                            goodsNum += orderProductListVO.getProductNum();

                            products.add(orderProductPO.getProductId());
                        }
                        //存在商品开启地区价格，则不能修改地址
                        List<ProductExtend> productId = productExtendFeignClient.getProductExtendListByProductId(products);
                        log.info("获取订单详情查询地区价格信息:{}", productId);
                        if (!CollectionUtils.isEmpty(productId)) {
                            List<ProductExtend> extendList = productId.stream().filter(x -> x.getIsAreaPrice() == 1).collect(Collectors.toList());
                            if (extendList.size() > 0) {
                                vo.setIsUpdate(CommonEnum.NO.getCode());
                            }
                        }
                    }
                    vo.setGoodsNum(goodsNum);
                    List<OrderProductListVO> orderProductListVOS = orderLocalUtils.sortFullGiftProductVO(orderPOChild.getOrderType(), productListVOS);
                    vo.setOrderProductListVOList(orderProductListVOS);
                    vo.setFinanceRuleCode(orderPOChild.getFinanceRuleCode());
                    vo.setRuleTag(orderPOChild.getRuleTag());
                    vos.add(vo);
                } else {
                    //获取订单货品列表信息
                    OrderProductExample orderProductExample = new OrderProductExample();
                    orderProductExample.setOrderSn(orderPOChild.getOrderSn());
                    List<OrderProductPO> orderProductPOList = orderProductModel.getOrderProductList(orderProductExample, null);
                    List<OrderProductPO> orderProductPOS = orderLocalUtils.sortFullGiftProduct(orderPOChild.getOrderType(), orderProductPOList);
                    orderPOChild.setOrderProductPOList(orderProductPOS);
                    showList.add(orderPOChild);
                }
            }
            if (!CollectionUtils.isEmpty(showList)) {
                //查询预售订单扩展信息
                if (showList.get(0).getOrderType() == PromotionConst.PROMOTION_TYPE_103) {
                    PresellOrderExtendExample presellOrderExtendExample = new PresellOrderExtendExample();
                    presellOrderExtendExample.setOrderSn(showList.get(0).getOrderSn());
                    List<PresellOrderExtendVO> presellOrderExtendList = presellOrderExtendFeignClient.getPresellOrderExtendList(presellOrderExtendExample);
                    AssertUtil.notEmpty(presellOrderExtendList, "获取预售订单扩展信息为空");
                    PresellOrderExtendVO presellOrderExtend = presellOrderExtendList.get(0);
                    orderSubState = presellOrderExtend.getOrderSubState();
                    if (presellOrderExtend.getIsAllPay() == OrderConst.IS_ALL_PAY_0) {
                        depositRemainTime = presellOrderExtend.getDepositEndTime();
                        remainTime = presellOrderExtend.getRemainStartTime();
                        isHasDeposit = true;
                    }
                    deliverTime = presellOrderExtend.getDeliverTime();
                }
                //查询阶梯团订单扩展信息
                if (showList.get(0).getOrderType() == PromotionConst.PROMOTION_TYPE_105) {
                    LadderGroupOrderExtend groupOrderExtendExample = new LadderGroupOrderExtend();
                    groupOrderExtendExample.setOrderSn(showList.get(0).getOrderSn());
                    List<LadderGroupOrderExtendVO> groupOrderExtendList = ladderGroupOrderExtendFeignClient.getLadderGroupOrderExtendList(groupOrderExtendExample);
                    AssertUtil.notEmpty(groupOrderExtendList, "获取阶梯团订单扩展信息为空");
                    LadderGroupOrderExtendVO groupOrderExtend = groupOrderExtendList.get(0);
                    orderSubState = groupOrderExtend.getOrderSubState();
                    remainTime = groupOrderExtend.getRemainStartTime();
                    isHasDeposit = true;
                    //查询是否退还定金
                    LadderGroupVO ladderGroup = ladderGroupFeignClient.getLadderGroupByGroupId(groupOrderExtend.getGroupId());
                    AssertUtil.notNull(ladderGroup, "获取阶梯团活动信息为空，请重试！");
                    if (ladderGroup.getIsRefundDeposit() == LadderGroupConst.IS_REFUND_DEPOSIT_1) {
                        isRefundDeposit = true;
                    }
                }
                OrderExtendPO orderExtendPO = orderExtendService.getOrderExtendByOrderSn(showList.get(0).getOrderSn());
                MemberOrderListVO vo = new MemberOrderListVO(showList.get(0), orderExtendPO, orderSubState, remainTime, isHasDeposit);
                vo.setShowExtendAutoReceiveTimeBtn(this.dealExtendAutoReceiveTimeBtn(showList.get(0)));
                vo.setIsRefundDeposit(isRefundDeposit);
                if (showList.get(0).getOrderType() == PromotionConst.PROMOTION_TYPE_103) {
                    if (depositRemainTime != null) {
                        long time1 = depositRemainTime.getTime();
                        long time2 = System.currentTimeMillis();
                        long differTime = (time1 - time2) / 1000;
                        vo.setDepositRemainTime(differTime < 0 ? 0 : differTime);
                        long time3 = remainTime.getTime();
                        long remainEndTime = (time3 - time2) / 1000;
                        vo.setRemainEndTime(remainEndTime < 0 ? 0 : remainEndTime);
                    }
                }
                vo.setDeliverTime(TimeUtil.getZDDay(deliverTime));
                vo.setOrderSubState(orderSubState);
                List<OrderProductListVO> productListVOS = new ArrayList<>();

                if (showList.size() > 1) {
                    //获取商家名称
                    vo.setStoreId(0L);
                    vo.setStoreName(stringRedisTemplate.opsForValue().get("basic_site_name"));

                    ArrayList<OrderProductPO> orderProductPOS = new ArrayList<>();
                    for (OrderPO orderPO : showList) {
                        orderProductPOS.addAll(orderPO.getOrderProductPOList());
                        //获取合计金额
                        totalMoney = totalMoney.add(orderPO.getOrderAmount());
                    }
                    //合计金额
                    vo.setTotalMoney(totalMoney);
                    vo.setOrderAmount(vo.getTotalMoney());
                    //获取订单货品列表信息
                    for (OrderProductPO orderProductPO : orderProductPOS) {
                        OrderProductListVO orderProductListVO = new OrderProductListVO(orderProductPO);
                        productListVOS.add(orderProductListVO);
                        //获取共计商品数
                        goodsNum += orderProductPO.getProductNum();
                    }
                    //共计商品数
                    vo.setGoodsNum(goodsNum);
                    //订单货品列表信息
                    vo.setOrderProductListVOList(productListVOS);
                    vos.add(vo);
                } else {
                    vo.setStoreId(showList.get(0).getStoreId());
                    vo.setStoreName(showList.get(0).getStoreName());
                    //合计金额
                    vo.setTotalMoney(showList.get(0).getOrderAmount());
                    //获取订单货品列表信息
                    List<OrderProductPO> orderProductPOList = showList.get(0).getOrderProductPOList();
                    for (OrderProductPO orderProductPO : orderProductPOList) {
                        OrderProductListVO orderProductListVO = new OrderProductListVO(orderProductPO);
                        productListVOS.add(orderProductListVO);
                        //共计商品数
                        goodsNum += orderProductPO.getProductNum();
                    }
                    vo.setGoodsNum(goodsNum);
                    vo.setOrderProductListVOList(productListVOS);
                    vos.add(vo);

                }
            }
        }

        // 包装团购信息
        try {
            spellOrderMessageWrapper(vos, isSpelling);
        } catch (Exception e) {
            log.error("订单注入团购信息异常", e);
        }

        return SldResponse.success(new PageVO<>(vos, pager));
    }

    /**
     * 包装订单团购信息
     *
     * @param vos        被包装对象
     * @param isSpelling 是否需要正在拼团中
     */
    private void spellOrderMessageWrapper(List<MemberOrderListVO> vos, Integer isSpelling) {
        Iterator<MemberOrderListVO> iterator = vos.iterator();
        while (iterator.hasNext()) {
            MemberOrderListVO memberOrderListVO = iterator.next();
            if (memberOrderListVO.getOrderType() == PromotionConst.PROMOTION_TYPE_102) {
                String orderSn = memberOrderListVO.getOrderSn();
                List<OrderProductPO> products = orderProductModel.getOrderProductListByOrderSn(orderSn);
                Integer spellTeamId = products.get(0).getSpellTeamId();
                if (Objects.isNull(spellTeamId)) {
                    log.error("团购订单【" + orderSn + "】拼团ID为null");
                    return;
                }
                JsonResult<Map<Integer, SpellTeamListInfo>> spellInfos = spellFeignClient
                        .getSpellListInfoBySpellIds(Collections.singletonList(spellTeamId));
                SpellTeamListInfo teamInfo = spellInfos.getData().get(spellTeamId);
                if (Objects.isNull(teamInfo)) {
                    log.error("团购订单【" + orderSn + "】查询拼团信息为null");
                    return;
                }
                // 需要拼团中的，去掉拼团已超时的和拼团成功的
                Integer spellTeamState = teamInfo.getState();
                if (OrderConst.IS_SPELLING_1.equals(isSpelling)
                        && (spellTeamState == null || spellTeamState != 1)) {
                    iterator.remove();
                    continue;
                }
                memberOrderListVO.setSpellLackNum(teamInfo.getLackNum());
                memberOrderListVO.setSpellTeamId(spellTeamId);
                memberOrderListVO.setSpellOrderFinishTime(teamInfo.getEndTime());
                memberOrderListVO.setSpellTeamState(spellTeamState);
            }
        }
    }

    @Override
    public JsonResult<MemberOrderDetailVO> getOrderDetail(@RequestParam("orderSn") String orderSn, Member member) {
        OrderPO orderPO = orderModel.getOrdersWithOpByOrderSn(orderSn);
        AssertUtil.notNull(orderPO, "无此订单");
        BizAssertUtil.isTrue(!member.getMemberId().equals(orderPO.getMemberId()), "您无权操作此订单");


        //获取订单扩展表信息
        OrderExtendExample orderExtendExample = new OrderExtendExample();
        orderExtendExample.setOrderSn(orderPO.getOrderSn());
        OrderExtendPO orderExtendPO = orderExtendModel.getOrderExtendList(orderExtendExample, null).get(0);

        //获取订单支付信息
        OrderPayExample orderPayExample = new OrderPayExample();
        orderPayExample.setPaySn(orderPO.getPaySn());
        OrderPayPO orderPayPO = orderPayModel.getOrderPayList(orderPayExample, null).get(0);

        SceneTypeEnum sceneTypeEnum = orderService.getOrderFileSceneNo(orderPO, OrderProductDeliveryEnum.DELIVERED);

        //获取组合商品信息
        BzOrderProductCombinationPO productCombinationPO = null;
        if (OrderConst.ORDER_TYPE_13 == orderPO.getOrderType()) {
            LambdaQueryWrapper<BzOrderProductCombinationPO> queryOrder = Wrappers.lambdaQuery();
            queryOrder.eq(BzOrderProductCombinationPO::getOrderSn, orderSn)
                    .select(BzOrderProductCombinationPO::getProductId, BzOrderProductCombinationPO::getGoodsId, BzOrderProductCombinationPO::getGoodsName,
                            BzOrderProductCombinationPO::getMainProductId, BzOrderProductCombinationPO::getMainImage, BzOrderProductCombinationPO::getBuyNum);
            productCombinationPO = orderProductCombinationService.getOne(queryOrder);
        }


        MemberOrderDetailVO orderVO = new MemberOrderDetailVO(orderPO, orderExtendPO, orderPayPO, productCombinationPO);

        //仓库编码
        if (orderPO.getOrderState() > 20) {
            List<OrderLogisticPO> orderLogisticPOList = orderLogisticService.getOrderLogisticByOrderSn(orderPO.getOrderSn(), null);
            if (!CollectionUtils.isEmpty(orderLogisticPOList)) {
                orderVO.setDeliveryWarehouse(orderLogisticPOList.get(0).getDeliverWarehouse());
                orderVO.setDeliveryWarehouseName(orderLogisticPOList.get(0).getDeliverWarehouseName());
            }
        }

        // 支付表分期信息
        JSONObject payWayExtraInfo = orderPayPO.getPayWayExtraInfo();
        orderVO.setPayWayExtraInfo(payWayExtraInfo);

        // 获取订单下单、发货、签收凭证资料
        List<MallFileScenesProofVO> fileScenesProofVOS = orderTradeProofService.queryScenesMaterialV2(orderPO);
        orderVO.setFileScenesProofVOS(fileScenesProofVOS);

        //处理到家服务订单安装人员信息
        if (orderPO.getOrderState() >= OrderStatusEnum.WAIT_DELIVER.getValue()) {
            if (!StringUtil.isEmpty(orderPO.getPerformanceModes())
                    && orderPO.getPerformanceModes().contains(OrderPerformanceModeEnum.PERFORMANCE_MODE_HOME_SERVICE.getValue().toString())) {
                orderVO.setInstallerList(dealInstallInfo(orderSn));
            }
        }
        orderVO.setOrderTypeValue(MemberOrderListVO.dealOrderTypeValue(orderPO.getOrderType(), false));
        //获取会员邮箱信息
        orderVO.setMemberEmail(member.getMemberEmail());
        //自动收货按钮
        orderVO.setShowExtendAutoReceiveTimeBtn(this.dealExtendAutoReceiveTimeBtn(orderPO));

        //获取订单日志信息
        OrderLogExample orderLogExample = new OrderLogExample();
        orderLogExample.setOrderSn(orderPO.getOrderSn());
        orderLogExample.setOrderBy("log_time asc");
        List<OrderLogPO> orderLogPOList = orderLogModel.getOrderLogList(orderLogExample, null);
        orderVO.setOrderLogs(orderLogPOList);

        //总运费
        BigDecimal totalExpress = BigDecimal.ZERO;
        //实付款(含运费)
        BigDecimal actualPayment = BigDecimal.ZERO;
        //商品总金额
        BigDecimal goodsAmount = BigDecimal.ZERO;

        ArrayList<ChildOrdersVO> childOrdersVOS = new ArrayList<>();
        //判断该订单的状态
        if (!(orderPO.getOrderState().equals(OrderConst.ORDER_STATE_10) || (orderPO.getOrderState().equals(OrderConst.ORDER_STATE_5)))) {
            //售后按钮展示
            dealAfsButton(orderPO);
            //查询店铺logo
            Store store = storeFeignClient.getStoreByStoreId(orderPO.getStoreId());
            AssertUtil.notNull(store, "店铺不存在");
            // 店铺注销中、已注销不展示售后按钮
            if (StoreStateEnum.LOGOUTING.getCode().equals(store.getState()) || StoreStateEnum.LOGOUT.getCode().equals(store.getState())) {
                orderPO.getOrderProductPOList().forEach(p -> p.setAfsButton(null));
            }
            //默认店铺logo
            if (StringUtil.isEmpty(store.getStoreLogo())) {
                store.setStoreLogo(stringRedisTemplate.opsForValue().get("default_image_store_logo"));
            }
            ChildOrdersVO childOrdersVO = new ChildOrdersVO(orderPO, orderExtendPO, store.getStoreLogo());
            childOrdersVO.setServicePhone(store.getServicePhone());
            //获取货品信息
            List<OrderProductListVO> productListVOS = new ArrayList<>();
            if (!CollectionUtils.isEmpty(orderPO.getOrderProductPOList())) {
                List<Long> products = new ArrayList<>();
                for (OrderProductPO orderProductPO : orderPO.getOrderProductPOList()) {
                    OrderProductListVO orderProductVO = new OrderProductListVO(orderProductPO);
                    //商品换货换货标识
                    if (ExchangeOrderConst.ORDER_EXCHANGE_FLAG_1 == orderPO.getExchangeFlag()) {
                        //查询该商品是否有换货
                        OrderExchangeDetailExample example = new OrderExchangeDetailExample();
                        example.setOrderProductId(orderProductPO.getOrderProductId());
                        List<OrderExchangeDetailPO> orderExchangeList = orderExchangeDetailMapper.getOrderExchangeDetailList(example);
                        List<String> exchangeSnList = orderExchangeList.stream().map(x -> x.getExchangeSn()).collect(Collectors.toList());
                        orderProductVO.setExchangeSnList(exchangeSnList);
                        if (!CollectionUtils.isEmpty(orderExchangeList)) {
                            orderProductVO.setExchangeFlag(Boolean.TRUE);
                        }
                    }
                    if (ObjectUtil.isNotNull(productCombinationPO) && orderProductPO.getProductId().equals(productCombinationPO.getMainProductId())) {
                        orderProductVO.setIsMain(1);
                    }
                    productListVOS.add(orderProductVO);
                    products.add(orderProductPO.getProductId());
                }
                List<String> categoryId = new ArrayList<>();

                List<Product> productListByProductIds = productFeignClient.getProductListByProductIds(products);
                for (Product product : productListByProductIds) {
                    categoryId.add(product.getCategoryId1().toString());
                    categoryId.add(product.getCategoryId2().toString());
                    categoryId.add(product.getCategoryId3().toString());
                }
                boolean showInsuranceUrl = orderLocalUtils.showInsuranceUrl(categoryId);
                childOrdersVO.setShowInsuranceUrl(showInsuranceUrl);

                //存在商品开启地区价格，则不能修改地址
                List<ProductExtend> productId = productExtendFeignClient.getProductExtendListByProductId(products);
                log.info("获取订单详情查询地区价格信息:{}", productId);
                if (!CollectionUtils.isEmpty(productId)) {
                    List<ProductExtend> extendList = productId.stream().filter(x -> x.getIsAreaPrice() == 1).collect(Collectors.toList());
                    if (extendList.size() > 0) {
                        orderVO.setIsUpdate(CommonEnum.NO.getCode());
                    }
                }
            }
            List<OrderProductListVO> orderProductListVOS = orderLocalUtils.sortFullGiftProductVO(orderPO.getOrderType(), productListVOS);
            childOrdersVO.setOrderProductListVOList(orderProductListVOS);

            //实付款(含运费)
            actualPayment = actualPayment.add(orderPO.getOrderAmount());
            orderVO.setActualPayment(actualPayment);
            goodsAmount = goodsAmount.add(orderPO.getGoodsAmount());
            orderVO.setGoodsAmount(goodsAmount);
            //总运费
            totalExpress = totalExpress.add(orderPO.getExpressFee());
            orderVO.setTotalExpress(totalExpress);
            //商品总额
            orderVO.setTotalMoney(actualPayment.subtract(totalExpress));
            //查询贷款产品
            getLoanInfo(orderPO, childOrdersVO, payWayExtraInfo);
            childOrdersVO.setFinanceRuleCode(orderPO.getFinanceRuleCode());
            childOrdersVO.setRuleTag(orderPO.getRuleTag());
            childOrdersVOS.add(childOrdersVO);
            orderVO.setChildOrdersVOS(childOrdersVOS);
        } else {
            //获得该订单号的父订单号下的所有子订单
            OrderExample orderExample = new OrderExample();
            orderExample.setParentSn(orderPO.getParentSn());
            List<OrderPO> orderPOList = orderModel.getOrderList(orderExample, null);
            //订单列表大于1说明是多个店铺
            if (orderPOList.size() > 1) {
                orderVO.setIsManyStore(true);
            }

            //遍历所有子订单
            for (OrderPO orderPOChild : orderPOList) {
                //查询店铺logo
                Store store = storeFeignClient.getStoreByStoreId(orderPOChild.getStoreId());
                AssertUtil.notNull(store, "店铺不存在");
                //默认店铺logo
                if (StringUtil.isEmpty(store.getStoreLogo())) {
                    store.setStoreLogo(stringRedisTemplate.opsForValue().get("default_image_store_logo"));
                }
                ChildOrdersVO childOrdersVO = new ChildOrdersVO(orderPOChild, orderExtendPO, store.getStoreLogo());
                childOrdersVO.setServicePhone(store.getServicePhone());

                //获取货品信息
                OrderProductExample orderProductExample = new OrderProductExample();
                orderProductExample.setOrderSn(orderPOChild.getOrderSn());
                List<OrderProductPO> orderProductPOList = orderProductModel.getOrderProductList(orderProductExample, null);
                List<OrderProductListVO> productListVOS = new ArrayList<>();
                if (!CollectionUtils.isEmpty(orderProductPOList)) {
                    List<Long> products = new ArrayList<>();
                    orderPOChild.setOrderProductPOList(orderProductPOList);

                    //售后按钮展示
                    dealAfsButton(orderPOChild);

                    for (OrderProductPO orderProductPO : orderProductPOList) {
                        OrderProductListVO orderProductListVO = new OrderProductListVO(orderProductPO);
                        if (ObjectUtil.isNotNull(productCombinationPO) && orderProductPO.getProductId().equals(productCombinationPO.getMainProductId())) {
                            orderProductListVO.setIsMain(1);
                        }
                        productListVOS.add(orderProductListVO);
                        products.add(orderProductPO.getProductId());
                    }
                    List<String> categoryId = new ArrayList<>();

                    List<Product> productListByProductIds = productFeignClient.getProductListByProductIds(products);
                    for (Product product : productListByProductIds) {
                        categoryId.add(product.getCategoryId1().toString());
                        categoryId.add(product.getCategoryId2().toString());
                        categoryId.add(product.getCategoryId3().toString());
                    }

                    boolean showInsuranceUrl = orderLocalUtils.showInsuranceUrl(categoryId);
                    childOrdersVO.setShowInsuranceUrl(showInsuranceUrl);
                    //存在商品开启地区价格，则不能修改地址
                    List<ProductExtend> productId = productExtendFeignClient.getProductExtendListByProductId(products);
                    log.info("获取订单详情查询地区价格信息:{}", productId);
                    if (!CollectionUtils.isEmpty(productId)) {
                        List<ProductExtend> extendList = productId.stream().filter(x -> x.getIsAreaPrice() == 1).collect(Collectors.toList());
                        if (extendList.size() > 0) {
                            orderVO.setIsUpdate(CommonEnum.NO.getCode());
                        }
                    }
                }
                List<OrderProductListVO> orderProductListVOS = orderLocalUtils.sortFullGiftProductVO(orderPO.getOrderType(), productListVOS);
                childOrdersVO.setOrderProductListVOList(orderProductListVOS);

                //实付款(含运费)
                actualPayment = actualPayment.add(orderPOChild.getOrderAmount());
                orderVO.setActualPayment(actualPayment);

                //总运费
                totalExpress = totalExpress.add(orderPOChild.getExpressFee());
                orderVO.setTotalExpress(totalExpress);

                //商品总额
                orderVO.setTotalMoney(actualPayment.subtract(totalExpress));
                //查询贷款产品
                getLoanInfo(orderPO, childOrdersVO, payWayExtraInfo);
                childOrdersVO.setFinanceRuleCode(orderPO.getFinanceRuleCode());
                childOrdersVO.setRuleTag(orderPO.getRuleTag());
                childOrdersVOS.add(childOrdersVO);
            }
            orderVO.setChildOrdersVOS(childOrdersVOS);
        }
        if (orderPO.getOrderState() == OrderConst.ORDER_STATE_30) {
            //计算时间
            Date autoReceiveTime = TimeUtil.getDayAgoDate(orderPO.getDeliverTime(), orderPO.getAutoReceiveDay());
            orderVO.setAutoReceiveTime(autoReceiveTime);
            //是否展示获取签收码按钮
            orderVO.setReceiveCodeButtonFlag(performanceService.isReceiveCodeStore(orderPO.getStoreId()));
        }

        OrderTypeQueryStrategy orderTypeQueryStrategy = orderTypeQueryStrategyContext.getStrategy(orderPO.getOrderType());
        if (ObjectUtil.isNotEmpty(orderTypeQueryStrategy)) {
            orderTypeQueryStrategy.queryPromotionOrderInfo(orderVO);
        }

        //拼团订单倒计时（秒）
        if (orderPO.getOrderType() == PromotionConst.PROMOTION_TYPE_102 && orderPO.getOrderState() == OrderConst.ORDER_STATE_10) {
            orderVO.setRemainTime(dealRemainTime("spell_order_auto_cancel_time", orderPO.getCreateTime(), 30));
        }
        //秒杀订单倒计时（秒）
        if (orderPO.getOrderType() == PromotionConst.PROMOTION_TYPE_104 && orderPO.getOrderState() == OrderConst.ORDER_STATE_10) {
            orderVO.setRemainTime(dealRemainTime("seckill_order_cancle", orderPO.getCreateTime(), 5));
        }

        //预售订单
        if (orderPO.getOrderType() == PromotionConst.PROMOTION_TYPE_103) {
            PresellOrderExtendExample presellOrderExtendExample = new PresellOrderExtendExample();
            presellOrderExtendExample.setOrderSn(orderSn);
            List<PresellOrderExtendVO> presellOrderExtendList = presellOrderExtendFeignClient.getPresellOrderExtendList(presellOrderExtendExample);
            AssertUtil.notEmpty(presellOrderExtendList, "获取预售订单扩展信息为空");
            PresellOrderExtendVO presellOrderExtend = presellOrderExtendList.get(0);
            orderVO.setPresellInfo(new MemberOrderDetailVO.PresellDetailInfo(presellOrderExtend));
            if (orderPO.getOrderState() == OrderConst.ORDER_STATE_10) {
                orderVO.setOrderSubState(presellOrderExtend.getOrderSubState());
                if (presellOrderExtend.getIsAllPay() == OrderConst.IS_ALL_PAY_1) {
                    orderVO.setOrderStateValue(MemberOrderListVO.getRealOrderStateValue(orderPO.getOrderState(),
                            0, 0L, orderPO.getOrderType()));
                } else {
                    long time1 = presellOrderExtend.getRemainStartTime().getTime();
                    long time2 = System.currentTimeMillis();
                    long depositRemainTime = (time1 - time2) / 1000;
                    depositRemainTime = depositRemainTime < 0 ? 0 : depositRemainTime;

                    orderVO.setOrderStateValue(MemberOrderListVO.getRealOrderStateValue(orderPO.getOrderState(),
                            presellOrderExtend.getOrderSubState(), depositRemainTime, orderPO.getOrderType()));
                    //预售订单倒计时（秒）
                    if (presellOrderExtend.getOrderSubState() == OrderConst.ORDER_SUB_STATE_101) {
                        //付定金倒计时
                        orderVO.setRemainTime(dealRemainTime("deposit_order_auto_cancel_time", orderPO.getCreateTime(), 30));
                    } else if (presellOrderExtend.getOrderSubState() == OrderConst.ORDER_SUB_STATE_102) {
                        //付尾款倒计时
                        long time3 = presellOrderExtend.getRemainEndTime().getTime();
                        long endRemainTime = (time3 - time2) / 1000;
                        orderVO.setRemainTime(endRemainTime < 0 ? 0 : endRemainTime);
                    }
                    orderVO.setOrderTypeValue(MemberOrderListVO.dealOrderTypeValue(orderPO.getOrderType(), true));
                }
            }
        }
        //阶梯团订单
        if (orderPO.getOrderType() == PromotionConst.PROMOTION_TYPE_105) {
            LadderGroupOrderExtend groupOrderExtendExample = new LadderGroupOrderExtend();
            groupOrderExtendExample.setOrderSn(orderSn);
            List<LadderGroupOrderExtendVO> groupOrderExtendList = ladderGroupOrderExtendFeignClient.getLadderGroupOrderExtendList(groupOrderExtendExample);
            AssertUtil.notEmpty(groupOrderExtendList, "获取阶梯团订单扩展信息为空");
            LadderGroupOrderExtendVO groupOrderExtend = groupOrderExtendList.get(0);
            MemberOrderDetailVO.LadderGroupDetailInfo groupDetailInfo = new MemberOrderDetailVO.LadderGroupDetailInfo(groupOrderExtend, orderPO.getActivityDiscountAmount());
            //查询是否退还定金
            LadderGroupVO ladderGroup = ladderGroupFeignClient.getLadderGroupByGroupId(groupOrderExtend.getGroupId());
            AssertUtil.notNull(ladderGroup, "获取阶梯团活动信息为空，请重试！");
            if (ladderGroup.getIsRefundDeposit() == LadderGroupConst.IS_REFUND_DEPOSIT_1) {
                groupDetailInfo.setIsRefundDeposit(true);
            }
            orderVO.setLadderGroupDetailInfo(groupDetailInfo);
            if (orderPO.getOrderState() == OrderConst.ORDER_STATE_10) {
                long time1 = groupOrderExtend.getRemainStartTime().getTime();
                long time2 = System.currentTimeMillis();
                long depositRemainTime = (time1 - time2) / 1000;
                depositRemainTime = depositRemainTime < 0 ? 0 : depositRemainTime;

                orderVO.setOrderSubState(groupOrderExtend.getOrderSubState());
                orderVO.setOrderStateValue(MemberOrderListVO.getRealOrderStateValue(orderPO.getOrderState(),
                        groupOrderExtend.getOrderSubState(), depositRemainTime, orderPO.getOrderType()));
                //阶梯团订单倒计时（秒）
                if (groupOrderExtend.getOrderSubState() == LadderGroupConst.ORDER_SUB_STATE_1) {
                    //付定金倒计时
                    orderVO.setRemainTime(dealRemainTime("ladder_group_deposit_order_auto_cancel_time", orderPO.getCreateTime(), 30));
                } else if (groupOrderExtend.getOrderSubState() == LadderGroupConst.ORDER_SUB_STATE_2) {
                    if (depositRemainTime > 0) {
                        //生成尾款倒计时
                        orderVO.setRemainTime(depositRemainTime);
                    } else {
                        //付尾款倒计时
                        long time3 = groupOrderExtend.getRemainEndTime().getTime();
                        long endRemainTime = (time3 - time2) / 1000;
                        orderVO.setRemainTime(endRemainTime < 0 ? 0 : endRemainTime);
                    }
                }
            }
        }

        if (PayMethodEnum.BANK_TRANSFER.getValue().equals(orderPO.getPaymentCode())) {
            BzBankTransferPO entity = bankTransferModel.queryByPaySn(orderPayPO.getPaySn());
            // 支付时才记汇款信息，需判断空
            if (Objects.nonNull(entity)) {
                orderVO.setShowBankTransfer(StringUtils.isNotBlank(entity.getReceiptAccount()));
                orderVO.setExpireDatetime(entity.getExpireTime().format(FULL_FORMATTER));
                orderVO.setPayerAccount(entity.getPaymentAccount());
                orderVO.setPayerName(entity.getPaymentName());
                orderVO.setReceiptAccount(entity.getReceiptAccount());
                orderVO.setReceiptName(entity.getReceiptName());
                orderVO.setReceiptBankName(entity.getReceiptBankName());
            } else {
                PaymentLargeRecvVO paymentLargeRecvVO = bankTransferModel.queryPaymentBankInfoByPaySn(orderPayPO.getPaySn());
                if (Objects.nonNull(paymentLargeRecvVO)) {
                    orderVO.setShowBankTransfer(StringUtils.isNotBlank(paymentLargeRecvVO.getLargePaymentNo()));
                    orderVO.setExpireDatetime(DateUtil.format(paymentLargeRecvVO.getExpiredAt(), DateUtil.CN_YEAR_MONTH_DAY_FORMAT));
                    orderVO.setPayerAccount(paymentLargeRecvVO.getPayBankAcctNo());
                    orderVO.setPayerName(paymentLargeRecvVO.getPayBankAcctName());
                    orderVO.setReceiptAccount(paymentLargeRecvVO.getLargePaymentNo());
                    orderVO.setReceiptName(paymentLargeRecvVO.getRecvBankAcctName());
                    orderVO.setReceiptBankName(paymentLargeRecvVO.getRecvBankName());
                }
            }
        }

        if (SettleModeEnum.BORROW.getCode().equals(orderPO.getSettleMode())) {
            orderVO.setReceiveAbleToCApp(true);
            try {
                fileCenterIntegration.queryScenesProofMaterial(orderPO.getOrderSn(),
                        IScenesMaterialProofConstant.ProofTypeEnum.MALL_ORDER_ORDER_SN,
                        sceneTypeEnum);
            } catch (BusinessException ex) {
                orderVO.setReceiveAbleToCApp(false);
            }
        }

        Integer minutes = orderModel.getAutoCancelMinutes(orderPO.getOrderPattern(), orderPO.getOrderType());
        log.info("getAutoCancelMinutes minutes:{}",minutes);

        // 支付倒计时
//        String value = stringRedisTemplate.opsForValue().get("time_limit_of_auto_cancle_order");
//        int autoCancelDay = value == null ? 24 : Integer.parseInt(value);
//        Date autoCancelTime = TimeUtil.getHourAgoDate(orderPayPO.getCreateTime(), autoCancelDay);
        // 计算时间差毫秒数
        Date now = new Date();
        long createTimeMills = orderPO.getCreateTime().getTime();
        long time = createTimeMills + TimeUnit.MINUTES.toMillis(minutes) - now.getTime();
        if (time > 0){
            // 转换为秒
            orderVO.setPayTimeLimit(TimeUnit.MILLISECONDS.toSeconds(time));
        }
//        if (now.before(autoCancelTime)) {
//            orderVO.setPayTimeLimit((autoCancelTime.getTime() - now.getTime()) / 1000);
//        }

        // 是否有发票
        Boolean checkExist = orderInvoiceService.checkExist(orderSn);
        orderVO.setExistInvoice(checkExist);
        orderVO.setInvoiceAllow(checkOrderPoInvoiceAllow(orderPO,orderPO.getOrderProductPOList()));

        // 只有自提订单，且在白名单内才展示轨迹
        if (OrderPatternEnum.SELF_LIFT.getValue().equals(orderPO.getOrderPattern())
                && storeIsolateWhiteListFeignClient.isStoreOnList(WhiteListEnum.DING_DING_REFUND_WHITE_LIST, orderPO.getStoreId())) {
            orderVO.setSelfLiftReturnTrack(true);
        }

        return SldResponse.success(orderVO);
    }

    /**
     * 校验订单是否可以开票
     *
     * @param orderPO
     * @return
     */
    private Boolean checkOrderPoInvoiceAllow(OrderPO orderPO,List<OrderProductPO> orderProductPOList) {
        // 是否可以开发票
        Boolean whiteList = storeIsolateWhiteListFeignClient.isStoreOnList(WhiteListEnum.INVOICE_WHITE_LIST, orderPO.getStoreId());
        if (!whiteList) {
            log.info("checkOrderPoInvoiceAllow fail,不在白名单");
            // 必须在指定白名单内
            return Boolean.FALSE;
        }
        Integer storeIsSelf = orderPO.getStoreIsSelf();
        if (!OrderConst.STORE_TYPE_SELF_1.equals(storeIsSelf)) {
            log.info("checkOrderPoInvoiceAllow fail,不是自营店铺");
            // 非自营店铺不允许开票
            return Boolean.FALSE;
        }
        boolean hasDuringRefund = orderReturnService.hasDuringRefund(orderPO.getOrderSn());
        if (hasDuringRefund){
            log.info("checkOrderPoInvoiceAllow fail,有售后单");
            return Boolean.FALSE;
        }
        Integer orderState = orderPO.getOrderState();
        boolean tradeSuccess = OrderStatusEnum.isTradeSuccess(orderState);
        if (!tradeSuccess) {
            log.info("checkOrderPoInvoiceAllow fail,订单状态不是交易成功");
            return Boolean.FALSE;
        }
        BigDecimal orderAmount = orderPO.getOrderAmount();
        if (BigDecimal.ZERO.compareTo(orderAmount) == 0){
            log.info("checkOrderPoInvoiceAllow fail,实付为0");
            return Boolean.FALSE;
        }
        String invoiceStart = invoiceConfig.getStart();
        if (StringUtils.isNotBlank(invoiceStart)){
            // 校验订单创建时间是否在指定时间之后
            Date createTime = orderPO.getCreateTime();
            DateTime date = cn.hutool.core.date.DateUtil.parse(invoiceStart, "yyyyMMdd");
            if (createTime.before(date)){
                return Boolean.FALSE;
            }
        }
        return Boolean.TRUE;
    }

    private List<InstallerVO> dealInstallInfo(String orderSn) {
        List<InstallerVO> installerVOList = new ArrayList<>();
        try {
            JsonResult<List<WorkerVo>> listJsonResult = homeServiceFeignClient.getHomeServiceWorkers(orderSn);
            if (listJsonResult != null && listJsonResult.getData() != null) {

                listJsonResult.getData().stream().forEach(x -> {
                    InstallerVO installerVO = new InstallerVO();
                    installerVO.setInstallerName(x.getUserName());
                    installerVO.setInstallerPhone(x.getUserPhone());
                    installerVOList.add(installerVO);
                });
            }
        } catch (Exception e) {
            log.error("获取安装工人信息出错,orderSn = {}", orderSn, e.getMessage());
        }
        return installerVOList;
    }

    @Override
    public JsonResult<BankTransferOrderDetailVO> getBankTransferDetail(String paySn, String orderSn, Member member) {
        BankTransferOrderDetailVO bktDetailVO = new BankTransferOrderDetailVO();
        List<MemberOrderDetailVO> orderDetailVOList = new ArrayList<>();
        if (StringUtils.isAllBlank(paySn, orderSn)) {
            throw new MallException("paySn和orderSn至少一个不能为空");
        }
        if (StringUtils.isNotBlank(orderSn)) {
            bktDetailVO.setOrderSn(orderSn);
            OrderPO orderPO = orderModel.getOrderByOrderSn(orderSn);
            paySn = orderPO.getPaySn();
        }
        bktDetailVO.setPaySn(paySn);
        OrderExample orderExampleQuery = new OrderExample();
        orderExampleQuery.setPaySn(paySn);
        List<OrderPO> orderPOList = orderModel.getOrderList(orderExampleQuery, null);
        OrderPO orderPO1 = orderPOList.get(0);
        Integer timeLimitOfAutoCancelOrder = systemSettingObtainHelper.getTimeLimitOfAutoCancelOrder();
        Date autoCancelTime = TimeUtil.getHourAgoDate(orderPO1.getCreateTime(), timeLimitOfAutoCancelOrder);

        for (OrderPO orderPO : orderPOList) {
            JsonResult<MemberOrderDetailVO> orderDetailJsonResult = this.getOrderDetail(orderPO.getOrderSn(), member);
            if (null != orderDetailJsonResult.getData()) {
                orderDetailVOList.add(orderDetailJsonResult.getData());
            }
        }
        boolean stateFlag = false;
        Integer closeCount = 0;
        Integer orderState = null;
        String orderStateDsc = "";
        for (MemberOrderDetailVO itemVO : orderDetailVOList) {
            if (null == bktDetailVO.getInvoice()) {
                bktDetailVO.setInvoice(itemVO.getInvoice());
            }
            if (null == bktDetailVO.getCreateTime()) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                bktDetailVO.setCreateTime(sdf.format(itemVO.getCreateTime()));
            }
            if (!stateFlag && (OrderStatusEnum.CANCELED.getValue().equals(itemVO.getOrderState())
                    || OrderStatusEnum.DEAL_PAY.getValue().equals(itemVO.getOrderState())
                    || OrderStatusEnum.WAIT_PAY_DEPOSIT.getValue().equals(itemVO.getOrderState()))) {
                orderState = itemVO.getOrderState();
                orderStateDsc = OrderStatusEnum.parseDesc(itemVO.getOrderState());
                stateFlag = true;
            }
            if (!stateFlag && OrderStatusEnum.TRADE_CLOSE.getValue().equals(itemVO.getOrderState())) {
                closeCount++;
            }
            bktDetailVO.setTotalMoney(bktDetailVO.getTotalMoney().add(itemVO.getTotalMoney()));
            bktDetailVO.setActualPayment(bktDetailVO.getActualPayment().add(itemVO.getActualPayment()));
            bktDetailVO.setTotalExpress(bktDetailVO.getTotalExpress().add(itemVO.getTotalExpress()));
            bktDetailVO.setGoodsAmount(bktDetailVO.getGoodsAmount().add(itemVO.getGoodsAmount()));
            bktDetailVO.setActivityDiscountAmount(bktDetailVO.getActivityDiscountAmount().add(itemVO.getActivityDiscountAmount()));
            bktDetailVO.setStoreVoucherAmount(bktDetailVO.getStoreVoucherAmount().add(itemVO.getStoreVoucherAmount()));
            bktDetailVO.setPlatformVoucherAmount(bktDetailVO.getPlatformVoucherAmount().add(itemVO.getPlatformVoucherAmount()));
            bktDetailVO.setFullDiscountAmount(bktDetailVO.getFullDiscountAmount().add(itemVO.getFullDiscountAmount()));
            bktDetailVO.setXzCardAmount(bktDetailVO.getXzCardAmount().add(itemVO.getXzCardAmount()));
            bktDetailVO.setXzCardExpressFeeAmount(bktDetailVO.getXzCardExpressFeeAmount().add(itemVO.getXzCardExpressFeeAmount()));
        }
        if (!stateFlag && closeCount == 0) {
            throw new BusinessException("订单状态已变更，请返回订单列表查看最新状态！");
        }
        if (!stateFlag && closeCount == orderDetailVOList.size()) {
            orderState = OrderStatusEnum.TRADE_CLOSE.getValue();
            orderStateDsc = OrderStatusEnum.TRADE_CLOSE.getDesc();
            stateFlag = true;
        }
        if (!stateFlag && closeCount < orderDetailVOList.size()) {
            orderState = OrderStatusEnum.TRADE_CLOSE.getValue();
            orderStateDsc = "部分关闭";
            stateFlag = true;
        }
        bktDetailVO.setOrderState(orderState);
        bktDetailVO.setOrderStateValue(orderStateDsc);
        bktDetailVO.setOrderDetailVOList(orderDetailVOList);
        bktDetailVO.setOrderType(orderPO1.getOrderType());

        if (OrderStatusEnum.TRADE_CLOSE.getValue().equals(orderState)
                || OrderStatusEnum.CANCELED.getValue().equals(orderState)) {
            return SldResponse.success(bktDetailVO);
        }

        BzBankTransferPO bankTransferPO = null;

        bankTransferPO = bankTransferModel.queryByPaySn(bktDetailVO.getPaySn());
        if (Objects.isNull(bankTransferPO) && !orderPO1.getOrderType().equals(OrderTypeEnum.PRE_SELL_DEPOSIT.getValue())) {
            bankTransferPO = new BzBankTransferPO();
            PaymentLargeRecvVO paymentLargeRecvVO = bankTransferModel.getOrCreatePaymentLargeRecvInfo(bktDetailVO.getPaySn());
            AssertUtil.notNull(paymentLargeRecvVO, "无此订单");

            Date expiredAt = autoCancelTime;
            bankTransferPO.setPaymentBankName(paymentLargeRecvVO.getPayBankAcctNo());
            bankTransferPO.setPaymentAccount(paymentLargeRecvVO.getPayBankAcctNo());
            bankTransferPO.setPaymentName(paymentLargeRecvVO.getPayBankAcctName());

            bankTransferPO.setReceiptBankName(paymentLargeRecvVO.getRecvBankName());
            bankTransferPO.setReceiptAccount(paymentLargeRecvVO.getLargePaymentNo());
            bankTransferPO.setReceiptName(paymentLargeRecvVO.getRecvBankAcctName());
            bankTransferPO.setExpireTime(cn.hutool.core.date.DateUtil.toLocalDateTime(expiredAt));
        } else if (orderPO1.getOrderType().equals(OrderTypeEnum.PRE_SELL_DEPOSIT.getValue())) {
            bankTransferPO = new BzBankTransferPO();

            String paySn1 = orderPO1.getPaySn();
            OrderPresellPO orderPresellPO = orderPresellService.lambdaQuery()
                    .eq(OrderPresellPO::getPaySn, paySn1)
                    .eq(OrderPresellPO::getPayStatus, CommonConst.PAY_STATUS_2)
                    .last("limit 1")
                    .one();
            PaymentLargeRecvVO paymentLargeRecvVO = bankTransferModel.getOrCreatePaymentLargeRecvInfo(orderPresellPO.getPayNo());
            AssertUtil.notNull(paymentLargeRecvVO, "预付银行卡汇款信息为空");
            Date expiredAt = autoCancelTime;
            bankTransferPO.setPaymentBankName(paymentLargeRecvVO.getPayBankAcctNo());
            bankTransferPO.setPaymentAccount(paymentLargeRecvVO.getPayBankAcctNo());
            bankTransferPO.setPaymentName(paymentLargeRecvVO.getPayBankAcctName());

            bankTransferPO.setReceiptBankName(paymentLargeRecvVO.getRecvBankName());
            bankTransferPO.setReceiptAccount(paymentLargeRecvVO.getLargePaymentNo());
            bankTransferPO.setReceiptName(paymentLargeRecvVO.getRecvBankAcctName());
            bankTransferPO.setExpireTime(cn.hutool.core.date.DateUtil.toLocalDateTime(expiredAt));

            PreSellPromotionPayDetail preSellPromotionPayDetail = preSellPromotionService.getPreSellDetailByOrderSn(orderPO1.getOrderSn());
            bktDetailVO.setPromotionPayDetail(preSellPromotionPayDetail);
        }

        if (!INIT_DATETIME.isEqual(bankTransferPO.getExpireTime())) {
            bktDetailVO.setExpireDatetime(FULL_FORMATTER.format(bankTransferPO.getExpireTime()));
        }
        bktDetailVO.setExpired(TransferStatusEnum.EXPIRED.getValue().equals(bankTransferPO.getTransferState()));
        BankTransferOrderDetailVO.BankTransferAccountInfo accountInfo
                = new BankTransferOrderDetailVO.BankTransferAccountInfo(bankTransferPO);
        bktDetailVO.setBankTransferAccountInfo(accountInfo);
        return SldResponse.success(bktDetailVO);
    }

    /**
     * 分页查询聊天界面我的订单
     */
    @Override
    public JsonResult<PageVO<ChatOrdersVO>> pageMyOrderList(OrderExample example, PagerInfo pager) {
        List<OrderPO> list = orderModel.getOrderList(example, pager);
        ArrayList<ChatOrdersVO> vos = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(order -> {
                //查询货品列表
                OrderProductExample productExample = new OrderProductExample();
                productExample.setOrderSn(order.getOrderSn());
                List<OrderProductPO> productList = orderProductModel.getOrderProductList(productExample, null);
                vos.add(new ChatOrdersVO(order, productList));
            });
        }
        return SldResponse.success(new PageVO<>(vos, pager));
    }

    /**** @param storeId
     * @return com.slodon.bbc.core.response.JsonResult<com.cfpamf.ms.mallorder.vo.OrderStatusCountVO>
     * @description : 获取订单管理状态汇总
     */
    @Override
    public JsonResult<OrderStatusCountVO> getOrderCountInfo(String storeId) {
        if (StringUtil.isBlank(storeId)) {
            return SldResponse.success("");
        }
        OrderStatusCountVO orderStatusCountVO = orderMapper.getOrderCountInfo(storeId);
        return SldResponse.success(orderStatusCountVO);
    }

    /**** @param queryDTO
     * @description : 分页查询店铺列表(小程序使用)
     */
    @Override
    public JsonResult<Page<OrderListVO>> pageStoreOrderList(StoreOrderReq queryDTO) {
        String searchValue = "";
        if (!StringUtils.isEmpty(queryDTO.getGoodsName())) {
            searchValue = queryDTO.getGoodsName();
        }
        if (!StringUtils.isEmpty(queryDTO.getOrderSn())) {
            searchValue = queryDTO.getOrderSn();
        }
        if (!StringUtils.isEmpty(queryDTO.getUserMobile())) {
            searchValue = queryDTO.getUserMobile();
        }
        // 待发货订单查询【部分发货】
        if (OrderStatusEnum.WAIT_DELIVER.getValue().equals(queryDTO.getOrderState())) {
            queryDTO.setOrderStateIn("20,25");
            queryDTO.setOrderState(null);
        }
        Page<OrderListVO> page = new Page<>(queryDTO.getCurrent(), queryDTO.getPageSize());
        List<OrderPO> orderPOList = orderMapper.pageStoreOrderList(page, queryDTO);
        List<OrderListVO> vos = dealOrderInfo(queryDTO.getGoodsName(), orderPOList);
        page.setRecords(vos);

        if (!StringUtils.isEmpty(searchValue)) {
            BzOrderSearchHistoryPO historyPO = new BzOrderSearchHistoryPO();
            historyPO.setSearchValue(searchValue);
            historyPO.setStoreId(Long.valueOf(queryDTO.getStoreId()));
            //异步储存搜索历史
            CompletableFuture.runAsync(() -> orderSearchHistoryService.saveOrderSearchHistory(historyPO));
        }

        return SldResponse.success(page);
    }

    /**
     * 分页查询店铺订单列表(web端)
     */
    @Override
    public JsonResult<PageVO<OrderListVOV2>> listStoreWeb(String orderSn, String memberName, String goodsName, Date startTime,
                                                          Date endTime, Date finishTimeAfter, Date finishTimeBefore,
                                                          String paymentCode, Integer orderState, Integer orderReturnState,
                                                          PagerInfo pager, Vendor vendor, String channel, List<Integer> orderType,
                                                          String customerId, String customerName, String storeName, String recommendStoreId,
                                                          String recommendStoreName, String branchName, Integer distribution, Integer orderPattern,
                                                          String areaName, String receiverName, String receiverMobile, String userMobile) {
        OrderExample orderExample = new OrderExample();
        orderExample.setOrderSnLike(orderSn);
        orderExample.setMemberNameLike(memberName);
        orderExample.setGoodsNameLike(goodsName);
        orderExample.setCreateTimeAfter(startTime);
        orderExample.setCreateTimeBefore(endTime);
        //配销订单，引荐商需要看到子店订单
        if (distribution != null && distribution.equals(CommonEnum.YES.getCode())) {
            orderExample.setRecommendStoreId(vendor.getStoreId().toString());
            orderExample.setOrderType(OrderTypeEnum.ORDER_TYPE_7.getValue());
        } else {
            orderExample.setStoreId(vendor.getStoreId());
            orderExample.setRecommendStoreId(recommendStoreId);
            if (!CollectionUtils.isEmpty(orderType)) {
                StringBuffer orderTypeIn = new StringBuffer();
                boolean firstFlag = true;
                for (Integer item : orderType) {
                    if (!firstFlag) {
                        orderTypeIn.append(",");
                    }
                    orderTypeIn.append(item);
                    firstFlag = false;
                }
                orderExample.setOrderTypeIn(orderTypeIn.toString());
            }
        }

        orderExample.setFinishTimeAfter(finishTimeAfter);
        orderExample.setFinishTimeBefore(finishTimeBefore);
        orderExample.setPaymentCode(paymentCode);
        orderExample.setOrderReturnState(orderReturnState);
        orderExample.setChannel(channel);
        orderExample.setCustomerId(customerId);
        orderExample.setCustomerName(customerName);
        orderExample.setStoreNameLike(storeName);
        orderExample.setRecommendStoreName(recommendStoreName);
        orderExample.setBranchNameLike(branchName);
        orderExample.setAreaNameLike(areaName);
        orderExample.setReceiverNameLike(receiverName);
        orderExample.setReceiverMobile(receiverMobile);
        orderExample.setUserMobile(userMobile);
        orderExample.setOrderPattern(orderPattern);
        // 待发货订单查询【部分发货】
        if (OrderStatusEnum.WAIT_DELIVER.getValue().equals(orderState)) {
            orderExample.setOrderStateIn("20,25");
        } else if (orderType == null && OrderStatusEnum.WAIT_PAY.getValue().equals(orderState)) {
            orderExample.setOrderStateIn("5,10");
        } else {
            orderExample.setOrderState(orderState);
        }

        List<OrderListVOV2> vos = orderModel.getOrderListWithJoin(orderExample, pager);
        vos.forEach(vo -> {
            //是否有定金
            boolean isHasDeposit = false;
            //拼团订单
            if (vo.getOrderType() == PromotionConst.PROMOTION_TYPE_102) {
                SpellTeamVO spellTeam = getSpellTeamVO(vo.getOrderSn());
                //拼团没有成功就不显示发货按钮
                if (spellTeam.getState() != SpellConst.SPELL_GROUP_STATE_2) {
                    vo.setIsShowDeliverButton(false);
                }
            }
            //查询预售订单扩展信息
            if (vo.getOrderType() == PromotionConst.PROMOTION_TYPE_103) {
                isHasDeposit = wrapperPresellOrderMsg(vo);
            }
            //阶梯团订单
            if (vo.getOrderType() == PromotionConst.PROMOTION_TYPE_105) {
                wrapperLadderGroupOrderMsg(vo);
            }
            if (vo.getOrderState() == OrderConst.ORDER_STATE_10) {
                Date autoReceiveTime = TimeUtil.getHourAgoDate(vo.getCreateTime(),
                        systemSettingObtainHelper.getTimeLimitOfAutoCancelOrder());
                vo.setAutoCancelTime(autoReceiveTime);
            }
            //预付订金订单，处理显示组合方式
            /*if(OrderTypeEnum.PRE_SELL_DEPOSIT.getValue() == vo.getOrderType()){
                OrderPresellDTO orderPresellDTO = orderPresellService.getDepositOrderDetail(vo.getOrderSn());
                vo.setComposePayName(dealPreSellComposePayName(orderPresellDTO));
            }*/
            vo.setOrderTypeValue(MemberOrderListVO.dealOrderTypeValue(vo.getOrderType(), isHasDeposit));
        });
        return SldResponse.success(new PageVO<>(vos, pager));
    }


    @Override
    public int getDepositRemindCount(Long storeId) {
        return orderMapper.getDepositRemindCount(storeId, promotionDepositRemindDays);
    }

    /**
     * 商家端端分页查询条件转换
     *
     * @param searchRequest 请求入参
     * @param vendor        商家信息
     * @return OrderExample
     */
    private OrderExample convertSellerOrderInfoListRequest2OrderExample(SellerOrderInfoListRequest searchRequest, Vendor vendor) {
        OrderExample orderExample = new OrderExample();
        orderExample.setOrderSnLike(searchRequest.getOrderSn());
        orderExample.setMemberNameLike(searchRequest.getMemberName());
        orderExample.setGoodsNameLike(searchRequest.getGoodsName());
        orderExample.setCreateTimeAfter(searchRequest.getStartTime());
        orderExample.setCreateTimeBefore(searchRequest.getEndTime());
        orderExample.setStoreId(vendor.getStoreId());
        orderExample.setFinishTimeAfter(searchRequest.getFinishTimeAfter());
        orderExample.setFinishTimeBefore(searchRequest.getFinishTimeBefore());
        orderExample.setPaymentCode(searchRequest.getPaymentCode());
        orderExample.setOrderReturnState(searchRequest.getOrderReturnState());
        orderExample.setChannel(searchRequest.getChannel());
        orderExample.setOrderType(searchRequest.getOrderType());
        orderExample.setCustomerId(searchRequest.getCustomerId());
        orderExample.setCustomerName(searchRequest.getCustomerName());
        orderExample.setStoreName(searchRequest.getStoreName());
        orderExample.setRecommendStoreId(searchRequest.getRecommendStoreId());
        orderExample.setRecommendStoreName(searchRequest.getRecommendStoreName());
        orderExample.setOrderPattern(searchRequest.getOrderPattern());
        // 待发货订单查询【部分发货】
        if (OrderStatusEnum.WAIT_DELIVER.getValue().equals(searchRequest.getOrderState())) {
            orderExample.setOrderStateIn("20,25");
        } else {
            orderExample.setOrderState(searchRequest.getOrderState());
        }
        return orderExample;
    }


    /**
     * 包装订单预售信息
     *
     * @param vo 订单信息
     * @return 是否全款订单：1-全款订单，0-定金预售订单
     */
    private boolean wrapperPresellOrderMsg(OrderListVOV2 vo) {
        PresellOrderExtendExample presellOrderExtendExample = new PresellOrderExtendExample();
        presellOrderExtendExample.setOrderSn(vo.getOrderSn());
        List<PresellOrderExtendVO> presellOrderExtendList = presellOrderExtendFeignClient.getPresellOrderExtendList(presellOrderExtendExample);
        AssertUtil.notEmpty(presellOrderExtendList, "获取预售订单扩展信息为空");
        PresellOrderExtendVO presellOrderExtend = presellOrderExtendList.get(0);
        vo.setOrderSubState(presellOrderExtend.getOrderSubState());
        //未付定金处理
        if (presellOrderExtend.getOrderSubState() == OrderConst.ORDER_SUB_STATE_101) {
            //商品实际总额
            BigDecimal goodsAmount = (presellOrderExtend.getDepositAmount().add(presellOrderExtend.getRemainAmount())).multiply(new BigDecimal(presellOrderExtend.getProductNum()));
            vo.setOrderAmount(goodsAmount.add(vo.getExpressFee()));
            //订单货品价格处理
            for (OrderProductListVO orderProductListVO : vo.getOrderProductListVOList()) {
                orderProductListVO.setProductShowPrice(presellOrderExtend.getPresellPrice());
            }
        }
        return presellOrderExtend.getIsAllPay() == OrderConst.IS_ALL_PAY_0;
    }

    /**
     * 包装订单阶梯团信息
     *
     * @param vo 订单信息
     */
    private void wrapperLadderGroupOrderMsg(OrderListVOV2 vo) {
        LadderGroupOrderExtend groupOrderExtendExample = new LadderGroupOrderExtend();
        groupOrderExtendExample.setOrderSn(vo.getOrderSn());
        List<LadderGroupOrderExtendVO> groupOrderExtendList = ladderGroupOrderExtendFeignClient.getLadderGroupOrderExtendList(groupOrderExtendExample);
        AssertUtil.notEmpty(groupOrderExtendList, "获取阶梯团订单扩展信息为空");
        LadderGroupOrderExtendVO groupOrderExtend = groupOrderExtendList.get(0);
        vo.setOrderSubState(groupOrderExtend.getOrderSubState());
        //未付定金处理
        if (groupOrderExtend.getOrderSubState() != OrderConst.ORDER_SUB_STATE_103) {
            //商品实际总额
            BigDecimal goodsAmount;
            if (!StringUtil.isNullOrZero(groupOrderExtend.getRemainAmount())) {
                goodsAmount = (groupOrderExtend.getAdvanceDeposit().add(groupOrderExtend.getRemainAmount())).multiply(new BigDecimal(groupOrderExtend.getProductNum()));
            } else {
                goodsAmount = groupOrderExtend.getAdvanceDeposit().multiply(new BigDecimal(groupOrderExtend.getProductNum()));
            }
            vo.setOrderAmount(goodsAmount.add(vo.getExpressFee()));
            //订单货品价格处理
            for (OrderProductListVO orderProductListVO : vo.getOrderProductListVOList()) {
                orderProductListVO.setProductShowPrice(groupOrderExtend.getProductPrice());
            }
        }
    }


    /***
     * @param goodsName
     * @param orderPOList
     * @return java.util.List<com.cfpamf.ms.mallorder.vo.OrderListVO>
     * @description : 处理订单信息
     */
    private List<OrderListVO> dealOrderInfo(String goodsName, List<OrderPO> orderPOList) {
        List<OrderListVO> vos = new ArrayList<>();
        if (CollectionUtils.isEmpty(orderPOList)) {
            return Lists.newArrayListWithCapacity(0);
        }
        for (OrderPO order : orderPOList) {
            //是否有定金
            boolean isHasDeposit = false;
            OrderExtendPO orderExtendPO = orderExtendService.getOrderExtendByOrderSn(order.getOrderSn());
            OrderListVO vo = new OrderListVO(order, orderExtendPO);
            if (orderReturnModel.whetherHasReturningProduct(order.getOrderSn())) {
                vo.setOrderReturnState(1);
            }
            vo.setIsDelivery(order.getIsDelivery());
            vo.setActivityDiscountAmount(order.getActivityDiscountAmount());
            //获取订单货品列表信息
            LambdaQueryWrapper<OrderProductPO> queryWrapper = Wrappers.lambdaQuery(OrderProductPO.class);
            queryWrapper.eq(OrderProductPO::getOrderSn, order.getOrderSn());
            if (!StringUtil.isEmpty(goodsName)) {
                queryWrapper.like(OrderProductPO::getGoodsName, goodsName);
            }
            List<OrderProductPO> orderProductPOList = orderProductService.list(queryWrapper);

            List<OrderProductListVO> productListVOS = new ArrayList<>();
            if (!CollectionUtils.isEmpty(orderProductPOList)) {
                for (OrderProductPO orderProductPO : orderProductPOList) {
                    OrderProductListVO orderProductListVO = new OrderProductListVO(orderProductPO);
                    productListVOS.add(orderProductListVO);
                }
            }
            List<OrderProductListVO> orderProductListVOS = orderLocalUtils.sortFullGiftProductVO(order.getOrderType(), productListVOS);
            //获取组合商品信息
            BzOrderProductCombinationPO productCombinationPO = null;
            if (OrderConst.ORDER_TYPE_13 == order.getOrderType()) {
                LambdaQueryWrapper<BzOrderProductCombinationPO> queryOrder = Wrappers.lambdaQuery();
                queryOrder.eq(BzOrderProductCombinationPO::getOrderSn, order.getOrderSn())
                        .select(BzOrderProductCombinationPO::getMainProductId);
                productCombinationPO = orderProductCombinationService.getOne(queryOrder);
            }
            for (OrderProductListVO orderProductListVO : orderProductListVOS) {
                if (ObjectUtil.isNotNull(productCombinationPO) && orderProductListVO.getProductId().equals(productCombinationPO.getMainProductId())) {
                    orderProductListVO.setIsMain(1);
                }
            }
            vo.setOrderProductListVOList(orderProductListVOS);
            //拼团订单
            if (order.getOrderType() == PromotionConst.PROMOTION_TYPE_102) {
                SpellTeamVO spellTeam = getSpellTeamVO(order.getOrderSn());
                //拼团没有成功就不显示发货按钮
                if (spellTeam.getState() != SpellConst.SPELL_GROUP_STATE_2) {
                    vo.setIsShowDeliverButton(false);
                }
            }
            //查询预售订单扩展信息
            if (order.getOrderType() == PromotionConst.PROMOTION_TYPE_103) {
                PresellOrderExtendExample presellOrderExtendExample = new PresellOrderExtendExample();
                presellOrderExtendExample.setOrderSn(order.getOrderSn());
                List<PresellOrderExtendVO> presellOrderExtendList = presellOrderExtendFeignClient.getPresellOrderExtendList(presellOrderExtendExample);
                AssertUtil.notEmpty(presellOrderExtendList, "获取预售订单扩展信息为空");
                PresellOrderExtendVO presellOrderExtend = presellOrderExtendList.get(0);
                if (presellOrderExtend.getIsAllPay() == OrderConst.IS_ALL_PAY_0) {
                    isHasDeposit = true;
                }
                vo.setOrderSubState(presellOrderExtend.getOrderSubState());
                //未付定金处理
                if (presellOrderExtend.getOrderSubState() == OrderConst.ORDER_SUB_STATE_101) {
                    //商品实际总额
                    BigDecimal goodsAmount = (presellOrderExtend.getDepositAmount().add(presellOrderExtend.getRemainAmount())).multiply(new BigDecimal(presellOrderExtend.getProductNum()));
                    vo.setOrderAmount(goodsAmount.add(order.getExpressFee()));
                    //订单货品价格处理
                    for (OrderProductListVO orderProductListVO : vo.getOrderProductListVOList()) {
                        orderProductListVO.setProductShowPrice(presellOrderExtend.getPresellPrice());
                    }
                }
            }
            //阶梯团订单
            if (order.getOrderType() == PromotionConst.PROMOTION_TYPE_105) {
                LadderGroupOrderExtend groupOrderExtendExample = new LadderGroupOrderExtend();
                groupOrderExtendExample.setOrderSn(order.getOrderSn());
                List<LadderGroupOrderExtendVO> groupOrderExtendList = ladderGroupOrderExtendFeignClient.getLadderGroupOrderExtendList(groupOrderExtendExample);
                AssertUtil.notEmpty(groupOrderExtendList, "获取阶梯团订单扩展信息为空");
                LadderGroupOrderExtendVO groupOrderExtend = groupOrderExtendList.get(0);
                vo.setOrderSubState(groupOrderExtend.getOrderSubState());
                //未付定金处理
                if (groupOrderExtend.getOrderSubState() != OrderConst.ORDER_SUB_STATE_103) {
                    //商品实际总额
                    BigDecimal goodsAmount;
                    if (!StringUtil.isNullOrZero(groupOrderExtend.getRemainAmount())) {
                        goodsAmount = (groupOrderExtend.getAdvanceDeposit().add(groupOrderExtend.getRemainAmount())).multiply(new BigDecimal(groupOrderExtend.getProductNum()));
                    } else {
                        goodsAmount = groupOrderExtend.getAdvanceDeposit().multiply(new BigDecimal(groupOrderExtend.getProductNum()));
                    }
                    vo.setOrderAmount(goodsAmount.add(order.getExpressFee()));
                    //订单货品价格处理
                    for (OrderProductListVO orderProductListVO : vo.getOrderProductListVOList()) {
                        orderProductListVO.setProductShowPrice(groupOrderExtend.getProductPrice());
                    }
                }
            }
            if (order.getOrderState() == OrderConst.ORDER_STATE_10) {
                String value = stringRedisTemplate.opsForValue().get("time_limit_of_auto_cancle_order");
                int autoCancelDay = value == null ? 24 : Integer.parseInt(value);
                Date autoReceiveTime = TimeUtil.getHourAgoDate(order.getCreateTime(), autoCancelDay);
                vo.setAutoCancelTime(autoReceiveTime);
            }
            vo.setOrderTypeValue(MemberOrderListVO.dealOrderTypeValue(order.getOrderType(), isHasDeposit));
            vos.add(vo);
        }
        return vos;
    }

    @Override
    public OrderVO adminOrderDetailInfo(String orderSn, Admin admin) {
        OrderPO orderPO = orderModel.getOrdersWithOpByOrderSn(orderSn);
        BizAssertUtil.notNull(orderPO, "无此订单");

        //获取订单扩展表信息
        OrderExtendExample orderExtendExample = new OrderExtendExample();
        orderExtendExample.setOrderSn(orderSn);
        OrderExtendPO orderExtendPO = orderExtendModel.getOrderExtendList(orderExtendExample, null).get(0);

        OrderVO orderVO = new OrderVO(orderPO, orderExtendPO);


        if (PayMethodEnum.BANK_TRANSFER.getValue().equals(orderPO.getPaymentCode())) {
            BzBankTransferPO entity = bankTransferModel.queryByPaySn(orderPO.getPaySn());
            if (Objects.nonNull(entity)) {
                orderVO.setShowBankTransfer(StringUtils.isNotBlank(entity.getReceiptAccount()));
                orderVO.setExpireDatetime(entity.getExpireTime().format(FULL_FORMATTER));
                orderVO.setPayerAccount(entity.getPaymentAccount());
                orderVO.setPayerName(entity.getPaymentName());
                orderVO.setReceiptAccount(entity.getReceiptAccount());
                orderVO.setReceiptName(entity.getReceiptName());
                orderVO.setReceiptBankName(entity.getReceiptBankName());
            } else {
                PaymentLargeRecvVO paymentLargeRecvVO = bankTransferModel.queryPaymentBankInfoByPaySn(orderPO.getPaySn());
                if (Objects.nonNull(paymentLargeRecvVO)) {
                    orderVO.setShowBankTransfer(StringUtils.isNotBlank(paymentLargeRecvVO.getLargePaymentNo()));
                    orderVO.setExpireDatetime(DateUtil.format(paymentLargeRecvVO.getExpiredAt(), DateUtil.CN_YEAR_MONTH_DAY_FORMAT));
                    orderVO.setPayerAccount(paymentLargeRecvVO.getPayBankAcctNo());
                    orderVO.setPayerName(paymentLargeRecvVO.getPayBankAcctName());
                    orderVO.setReceiptAccount(paymentLargeRecvVO.getLargePaymentNo());
                    orderVO.setReceiptName(paymentLargeRecvVO.getRecvBankAcctName());
                    orderVO.setReceiptBankName(paymentLargeRecvVO.getRecvBankName());
                }
            }

        }

        // 组合支付银行卡汇款信息查询
        if (PayMethodEnum.COMBINATION_PAY.getValue().equals(orderPO.getPaymentCode())) {
            List<OrderPayRecordPO> orderPayRecordPOS = payRecordService.queryOrderPayByPaySn(orderPO.getPaySn());
            if (!CollectionUtils.isEmpty(orderPayRecordPOS)) {
                for (OrderPayRecordPO record : orderPayRecordPOS) {
                    String paymentCode = record.getPaymentCode();
                    if (PayMethodEnum.BANK_TRANSFER.getValue().equals(paymentCode)) {
                        // 组合支付，且为银行卡汇款,查询payment获取支付详情
                        PaymentLargeRecvVO paymentLargeRecvVO = bankTransferModel.queryPaymentBankInfoByPaySn(record.getPayNo());
                        if (Objects.nonNull(paymentLargeRecvVO)) {
                            // 判断是定金还是尾款
                            Integer payOrder = record.getPayOrder();
                            if (PresellCapitalTypeEnum.DEPOSIT.getValue().equals(payOrder)) {
                                // 定金
                                orderVO.setDepositShowBankTransfer(StringUtils.isNotBlank(paymentLargeRecvVO.getLargePaymentNo()));
                                orderVO.setDepositExpireDatetime(DateUtil.format(paymentLargeRecvVO.getExpiredAt(), DateUtil.CN_YEAR_MONTH_DAY_FORMAT));
                                orderVO.setDepositPayerAccount(paymentLargeRecvVO.getPayBankAcctNo());
                                orderVO.setDepositPayerName(paymentLargeRecvVO.getPayBankAcctName());
                                orderVO.setDepositReceiptAccount(paymentLargeRecvVO.getLargePaymentNo());
                                orderVO.setDepositReceiptName(paymentLargeRecvVO.getRecvBankAcctName());
                                orderVO.setDepositReceiptBankName(paymentLargeRecvVO.getRecvBankName());
                            } else if (PresellCapitalTypeEnum.BALANCE.getValue().equals(payOrder)) {
                                // 尾款
                                orderVO.setBalanceShowBankTransfer(StringUtils.isNotBlank(paymentLargeRecvVO.getLargePaymentNo()));
                                orderVO.setBalanceExpireDatetime(DateUtil.format(paymentLargeRecvVO.getExpiredAt(), DateUtil.CN_YEAR_MONTH_DAY_FORMAT));
                                orderVO.setBalancePayerAccount(paymentLargeRecvVO.getPayBankAcctNo());
                                orderVO.setBalancePayerName(paymentLargeRecvVO.getPayBankAcctName());
                                orderVO.setBalanceReceiptAccount(paymentLargeRecvVO.getLargePaymentNo());
                                orderVO.setBalanceReceiptName(paymentLargeRecvVO.getRecvBankAcctName());
                                orderVO.setBalanceReceiptBankName(paymentLargeRecvVO.getRecvBankName());
                            }
                        }
                    }
                }
            }
        }

        //获取会员信息
        Member member = memberFeignClient.getMemberByMemberId(orderPO.getMemberId());
        orderVO.setMemberEmail(member.getMemberEmail());

        //该货品是否有售后信息
        for (OrderProductPO orderProductPO : orderPO.getOrderProductPOList()) {
            //图片处理
            orderProductPO.setProductImage(FileUrlUtil.getFileUrl(orderProductPO.getProductImage(), null));
            OrderAfterServiceExample orderAfterServiceExample = new OrderAfterServiceExample();
            orderAfterServiceExample.setOrderProductId(orderProductPO.getOrderProductId());
            orderAfterServiceExample.setOrderBy("afs_id desc");
            List<OrderAfterPO> orderAfterServicePOList = orderAfterServiceModel.getOrderAfterServiceList(orderAfterServiceExample, null);
            if (CollectionUtils.isEmpty(orderAfterServicePOList)) {
                orderProductPO.setIsHasAfs(OrdersAfsConst.NOT_HAS_AFS);
            } else {
                orderProductPO.setIsHasAfs(OrdersAfsConst.IS_HAS_AFS);
                orderProductPO.setAfsSn(orderAfterServicePOList.get(0).getAfsSn());
            }
            OrderProductAdminVO orderProductAdminVO = new OrderProductAdminVO();
            BeanUtils.copyProperties(orderProductPO, orderProductAdminVO);
            orderVO.getOrderProductPOList().add(orderProductAdminVO);
            orderVO.setSupplierName(orderProductPO.getSupplierName());
        }

        //获取订单日志信息
        OrderLogExample orderLogExample = new OrderLogExample();
        orderLogExample.setOrderSn(orderSn);
        orderLogExample.setOrderBy("log_time asc");
        List<OrderLogPO> orderLogPOList = orderLogModel.getOrderLogList(orderLogExample, null);
        orderVO.setOrderLogs(orderLogPOList);
        OrderPerformanceBelongsVO performanceBelongsVo = orderPerformanceBelongsService.getVoByOrderSn(orderPO.getOrderSn());
        if (Objects.nonNull(performanceBelongsVo)) {
            orderVO.setPerformanceBelongsVO(performanceBelongsVo);
        }

        String appIyId = orderSn;//贷款申请单号
        boolean isLoanPay = PayMethodEnum.isLoanPay(orderPO.getPaymentCode());
        //预付订单
        if (orderPO.getOrderType() == PromotionConst.PROMOTION_TYPE_107) {
            try {
                OrderPresellDTO orderPresellDTO = orderPresellService.getDepositOrderDetail(orderVO.getOrderSn());
                orderVO.setOrderPresellDTO(orderPresellDTO);
                orderVO.setComposePayName(OrderPresellBuilder.dealPreSellComposePayName(orderPresellDTO));
                PayMethodEnum remainMethodEnum = PayMethodEnum.getValue(orderPresellDTO.getRemainPaymentCode());
                log.info("orderPresellDTO.getRemainPaymentCode() = {},orderPresellDTO.getRemainPayNo() = {}", orderPresellDTO.getRemainPaymentCode(), orderPresellDTO.getRemainPayNo());

                if (remainMethodEnum != null && PayMethodEnum.isLoanPay(remainMethodEnum)) {
                    CdmallOrderVo loanInfoByOrderSn = loanPayIntegration.getLoanInfoByOrderSn(orderPresellDTO.getRemainPayNo());
                    log.info("OrderInfoServiceImpl loanInfoByOrderSn = {}", JSONObject.toJSONString(loanInfoByOrderSn));
                    if (ObjectUtil.isNotNull(loanInfoByOrderSn)) {
                        LoanInfoVo loanInfoVo = new LoanInfoVo();
                        BeanUtils.copyProperties(loanInfoByOrderSn, loanInfoVo);
                        loanInfoVo.setFinanceRuleCode(orderPO.getFinanceRuleCode());
                        loanInfoVo.setRuleTag(orderPO.getRuleTag());
                        orderVO.setLoanInfoVo(loanInfoVo);
                    }
                }
                appIyId = orderPresellDTO.getRemainPayNo();//贷款申请单号，预付目前只能尾款使用贷款支付，优先检查尾款支付方式是否为贷款支付
                isLoanPay = PayMethodEnum.isLoanPay(orderPresellDTO.getRemainPaymentCode());
                if (Boolean.FALSE.equals(isLoanPay)) {
                    appIyId = orderPresellDTO.getDepositPayNo();//贷款申请单号，预付目前只能尾款使用贷款支付，优先检查尾款支付方式是否为贷款支付
                    isLoanPay = PayMethodEnum.isLoanPay(orderPresellDTO.getDepositPaymentCode());
                }
            } catch (Exception e) {
                log.error("【平台端订单详情】获取预付订金详情失败", e);
            }
        } else {
            PayMethodEnum methodEnum = PayMethodEnum.getValue(orderPO.getPaymentCode());
            //查询贷款信息
            if (methodEnum != null && PayMethodEnum.isLoanPay(methodEnum)) {
                CdmallOrderVo loanInfoByOrderSn = loanPayIntegration.getLoanInfoByOrderSn(orderPO.getOrderSn());
                if (ObjectUtil.isNotNull(loanInfoByOrderSn)) {
                    LoanInfoVo loanInfoVo = new LoanInfoVo();
                    BeanUtils.copyProperties(loanInfoByOrderSn, loanInfoVo);
                    loanInfoVo.setFinanceRuleCode(orderPO.getFinanceRuleCode());
                    loanInfoVo.setRuleTag(orderPO.getRuleTag());
                    orderVO.setLoanInfoVo(loanInfoVo);
                }
            }
        }

        if (Boolean.TRUE.equals(isLoanPay)) {
            //信贷贷款支付方式，查询贷款合同
            orderVO.setContractFiles(this.getContracts4Backend(appIyId));
        }

        // 查询需要上传的交易凭证并根据场景分组，与下面查询file-center有所不同，file-center只保存已上传的
        List<OrderTradeProofPO> proofList = tradeProofService.getListByOrderSn(orderSn);
        Map<String, List<OrderTradeProofPO>> proofMap = proofList.stream().collect(Collectors.groupingBy(OrderTradeProofPO::getSceneNo));
        List<MaterialSceneDTO> materialSceneDTOS = Lists.newArrayList();
        proofMap.forEach((sceneNo,list) -> {
            OrderTradeProofPO proofPO = list.get(0);
            MaterialSceneDTO materialSceneDTO = new MaterialSceneDTO();
            materialSceneDTO.setSceneNo(proofPO.getSceneNo());
            materialSceneDTO.setSceneName(proofPO.getSceneName());
            List<OrderTradeProofVO> voList = list.stream().map(OrderTradeProofVO::new).collect(Collectors.toList());
            // 判定是否需要判断风险，目前只需要判断收货确认书的风险
            for (OrderTradeProofVO orderTradeProofVO : voList) {
                if (orderMaterialConfig.getReceiveMaterialNo().equals(orderTradeProofVO.getMaterialNo())){
                    orderTradeProofVO.setNeedCheckRisk(Boolean.TRUE);
                }else{
                    orderTradeProofVO.setNeedCheckRisk(Boolean.FALSE);
                }
            }
            materialSceneDTO.setProofVOList(voList);
            materialSceneDTOS.add(materialSceneDTO);
        });
        orderVO.setProofList(materialSceneDTOS);
        // 获取订单下单、发货、签收凭证资料
        List<FileScenesProductWithResultVO> fileScenesProductWithResultVOList = orderTradeProofService.queryScenesMaterialWithResult(orderPO.getOrderSn(), null, null, true);

        orderVO.setFileScenesProofVOS(fileScenesProductWithResultVOList);

        //获取线下补录信息
        if (OrderTypeEnum.isOfflineAll(orderPO.getOrderType())) {
            orderVO.setOfflineOrderInfo(getOfflineOrderInfo(orderPO, orderExtendPO));
        }

        //换货订单，查询换货详情
        if (ExchangeOrderConst.ORDER_EXCHANGE_FLAG_1 == orderPO.getExchangeFlag()) {
            orderVO.setExchangeDetailVOList(orderExchangeDetailService.getOrderExchangeDetail(orderSn, new UserDTO(admin)));
        }

        return orderVO;
    }


    /**
     * 获取用呗合同列表(后台只展示"指示付款通知书")
     *
     * @param applyId
     * @return
     */
    private List<CmisContractFileVo> getContracts4Backend(String applyId) {
        // 需要展示的合同：用呗、随心去 指示付款通知书
        List<String> showContract = Arrays.asList("XTYongBeiNoticePayment", "YongBeiNoticePayment");
        List<CmisContractFileVo> result = new ArrayList<>();
        try {
            //信贷贷款支付方式，查询贷款合同
            Result<List<CmisContractFileVo>> contractFilesResult = contractFacade.getAllContractByApplyId(applyId);
            if (Objects.nonNull(contractFilesResult) && contractFilesResult.isSuccess()) {
                for (CmisContractFileVo contractFileVo : contractFilesResult.getData()) {
                    if (showContract.contains(contractFileVo.getTemplateNo())) {
                        result.add(contractFileVo);
                    }
                }
            }
        } catch (Exception e) {
            log.error("【后台订单详情】获取信贷支付合同链接失败, applyId: {}", applyId, e);
        }
        return result;
    }

    /***
     * @param orderSn
     * @param vendor
     * @param distribution
     * @return com.slodon.bbc.core.response.JsonResult<com.cfpamf.ms.mallorder.vo.SellerOrderVO>
     * @description :获取订单详情(web端)
     */
    @Override
    public JsonResult<SellerOrderVO> getOrderDetailWeb(String orderSn, Vendor vendor, Integer distribution, Boolean showAgreement) {
        BizAssertUtil.notNull(vendor, "商户信息为空，请确认接口经过网关");

        OrderPO orderPO = orderModel.getOrdersWithOpByOrderSn(orderSn);
        BizAssertUtil.notNull(orderPO, "无此订单");
        BizAssertUtil.notNull(vendor, "商户信息为空，请确认接口经过网关");
        //配销订单，引荐商需要看到子店订单 厂商订单，允许经销商查看
        if (!orderPO.getPerformanceModes().contains(OrderPerformanceModeEnum.PERFORMANCE_MODE_MFR_DEALER.getValue().toString())) {
            OrderLocalUtils.checkOrderPermissions(vendor.getStoreId(), orderPO.getStoreId(), orderPO.getRecommendStoreId(), distribution, orderPO.getOrderType());
        }

        //获取订单扩展表信息
        OrderExtendExample orderExtendExample = new OrderExtendExample();
        if (distribution == null || CommonEnum.NO.getCode().equals(distribution)) {
            orderExtendExample.setStoreId(vendor.getStoreId());
        }
        orderExtendExample.setOrderSn(orderSn);
        OrderExtendPO orderExtendPO = orderExtendModel.getOrderExtendList(orderExtendExample, null).get(0);

        SellerOrderVO sellerOrderVO = new SellerOrderVO(orderPO, orderExtendPO);

        //获取会员信息
        Member member = memberFeignClient.getMemberByMemberId(orderPO.getMemberId());
        sellerOrderVO.setMemberEmail(member.getMemberEmail());

        //获取订单日志信息
        OrderLogExample orderLogExample = new OrderLogExample();
        orderLogExample.setOrderSn(orderSn);
        orderLogExample.setOrderBy("log_time asc");
        List<OrderLogPO> orderLogPOList = orderLogModel.getOrderLogList(orderLogExample, null);
        sellerOrderVO.setOrderLogs(orderLogPOList);

        // 查询业绩归属信息
        OrderPerformanceBelongsVO performanceBelongsVo = orderPerformanceBelongsService.getVoByOrderSn(orderPO.getOrderSn());
        if (Objects.nonNull(performanceBelongsVo)) {
            sellerOrderVO.setPerformanceBelongsVO(performanceBelongsVo);
        }

        //售后按钮展示
        dealAfsButton(orderPO);

        //仓库编码
        if (orderPO.getOrderState() > 20) {
            List<OrderLogisticPO> orderLogisticPOList = orderLogisticService.getOrderLogisticByOrderSn(orderPO.getOrderSn(), null);
            if (!CollectionUtils.isEmpty(orderLogisticPOList)) {
                sellerOrderVO.setDeliveryWarehouse(orderLogisticPOList.get(0).getDeliverWarehouse());
                sellerOrderVO.setDeliveryWarehouseName(orderLogisticPOList.get(0).getDeliverWarehouseName());
            }
        }

        // 查询商品是否开启业务分销
        List<Long> productIds = orderPO.getOrderProductPOList().stream()
                .map(OrderProductPO::getProductId).collect(Collectors.toList());
        /*List<ProductExtend> productExtendList = ExternalApiUtil.callMq(
                () -> productExtendFeignClient.getProductExtendListByProductId(productIds), productIds,
                "goods/productExtend/productExtendListByProductId", "查询ProductExtend信息");*/
        List<ProductExtend> productExtendList = productExtendFeignClient.getProductExtendListByProductId(productIds);
        Map<Long, ProductExtend> productExtendMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(productExtendList)) {
            productExtendMap = productExtendList.stream()
                    .collect(Collectors.toMap(ProductExtend::getProductId, p -> p));
        }

        List<Long> orderProductIdList = orderPO.getOrderProductPOList().stream()
                .map(OrderProductPO::getOrderProductId).collect(Collectors.toList());
        Map<Long, Integer> productRefundNumMap = orderAfterServiceModel.getProductRefundCountMap(orderProductIdList);
        Map<Long, Integer> productExchangeNumMap = orderExchangeDetailService.getProductExchangeCountMap(orderProductIdList);

        //获取货品列表信息
        List<Long> products = new ArrayList<>();
        for (OrderProductPO orderProductPO : orderPO.getOrderProductPOList()) {
            OrderProductListVO orderProductListVO = new OrderProductListVO(orderProductPO);
            ProductExtend productExtend = productExtendMap.get(orderProductPO.getProductId());
            orderProductListVO.setDistributionNew(productExtend == null ? 0 : productExtend.getDistributionNew());
            int refundNum = productRefundNumMap.get(orderProductPO.getOrderProductId()) == null ? 0 : productRefundNumMap.get(orderProductPO.getOrderProductId());
            int exchangedNum = productExchangeNumMap.get(orderProductPO.getOrderProductId()) == null ? 0 : productExchangeNumMap.get(orderProductPO.getOrderProductId());
            //默认可发货数量 = 购买数量 - 已发货数量 - 仅退款数量 - 换货退款数量
            orderProductListVO.setWaitDeliveryNum(orderProductPO.getProductNum() - orderProductPO.getDeliveryNum() - refundNum - exchangedNum);

            sellerOrderVO.getOrderProductList().add(orderProductListVO);
            sellerOrderVO.setSupplierName(orderProductPO.getSupplierName());
            products.add(orderProductPO.getProductId());
        }
        sellerOrderVO.setOrderProductList(orderLocalUtils.sortFullGiftProductVO(orderPO.getOrderType(), sellerOrderVO.getOrderProductList()));
        List<String> categoryId = new ArrayList<>();

        List<Product> productListByProductIds = productFeignClient.getProductListByProductIds(products);
        for (Product product : productListByProductIds) {
            categoryId.add(product.getCategoryId1().toString());
            categoryId.add(product.getCategoryId2().toString());
            categoryId.add(product.getCategoryId3().toString());
        }

        boolean showInsuranceUrl = orderLocalUtils.showInsuranceUrl(categoryId);
        sellerOrderVO.setShowInsuranceUrl(showInsuranceUrl);

        //查询是否有商品开启地区价格
        List<ProductExtend> productId = productExtendFeignClient.getProductExtendListByProductId(products);
        log.info("获取订单详情查询地区价格信息:{}", productId);
        if (!CollectionUtils.isEmpty(productId)) {
            List<ProductExtend> extendList = productId.stream().filter(x -> x.getIsAreaPrice() == 1).collect(Collectors.toList());
            if (extendList.size() > 0) {
                sellerOrderVO.setIsUpdate(CommonEnum.NO.getCode());
            }
        }

        //处理商品换货标识
        if (ExchangeOrderConst.ORDER_EXCHANGE_FLAG_1 == sellerOrderVO.getExchangeFlag()) {
            orderExchangeService.dealProductExchangeFlag(sellerOrderVO.getOrderProductList());
        }
        //拼团订单
        if (orderPO.getOrderType() == PromotionConst.PROMOTION_TYPE_102) {
            SpellTeamVO spellTeam = getSpellTeamVO(orderPO.getOrderSn());
            //拼团没有成功就不显示发货按钮
            if (spellTeam.getState() != SpellConst.SPELL_GROUP_STATE_2) {
                sellerOrderVO.setIsShowDeliverButton(false);
            }
        }

        //预售订单
        if (orderPO.getOrderType() == PromotionConst.PROMOTION_TYPE_103) {
            PresellOrderExtendExample presellOrderExtendExample = new PresellOrderExtendExample();
            presellOrderExtendExample.setOrderSn(orderSn);
            List<PresellOrderExtendVO> presellOrderExtendList = presellOrderExtendFeignClient.getPresellOrderExtendList(presellOrderExtendExample);
            AssertUtil.notEmpty(presellOrderExtendList, "获取预售订单扩展信息为空");
            PresellOrderExtendVO presellOrderExtend = presellOrderExtendList.get(0);
            sellerOrderVO.setPresellInfo(new MemberOrderDetailVO.PresellDetailInfo(presellOrderExtend));
            if (presellOrderExtend.getIsAllPay() == OrderConst.IS_ALL_PAY_0) {
                sellerOrderVO.setOrderTypeValue(MemberOrderListVO.dealOrderTypeValue(orderPO.getOrderType(), true));
                //未付定金处理
                if (presellOrderExtend.getOrderSubState() == OrderConst.ORDER_SUB_STATE_101) {
                    //商品实际总额
                    BigDecimal goodsAmount = (presellOrderExtend.getDepositAmount().add(presellOrderExtend.getRemainAmount())).multiply(new BigDecimal(presellOrderExtend.getProductNum()));
                    sellerOrderVO.setGoodsAmount(goodsAmount);
                    sellerOrderVO.setOrderAmount(goodsAmount.add(orderPO.getExpressFee()));
                    //订单货品价格处理
                    for (OrderProductListVO orderProductListVO : sellerOrderVO.getOrderProductList()) {
                        orderProductListVO.setProductShowPrice(presellOrderExtend.getPresellPrice());
                    }
                }
            }
        }
        //阶梯团订单
        if (orderPO.getOrderType() == PromotionConst.PROMOTION_TYPE_105) {
            LadderGroupOrderExtend groupOrderExtendExample = new LadderGroupOrderExtend();
            groupOrderExtendExample.setOrderSn(orderSn);
            List<LadderGroupOrderExtendVO> groupOrderExtendList = ladderGroupOrderExtendFeignClient.getLadderGroupOrderExtendList(groupOrderExtendExample);
            AssertUtil.notEmpty(groupOrderExtendList, "获取阶梯团订单扩展信息为空");
            LadderGroupOrderExtendVO groupOrderExtend = groupOrderExtendList.get(0);
            sellerOrderVO.setLadderGroupDetailInfo(new MemberOrderDetailVO.LadderGroupDetailInfo(groupOrderExtend, orderPO.getActivityDiscountAmount()));
            //未付定金处理
            if (groupOrderExtend.getOrderSubState() != OrderConst.ORDER_SUB_STATE_103) {
                //商品实际总额
                BigDecimal goodsAmount;
                if (!StringUtil.isNullOrZero(groupOrderExtend.getRemainAmount())) {
                    goodsAmount = (groupOrderExtend.getAdvanceDeposit().add(groupOrderExtend.getRemainAmount())).multiply(new BigDecimal(groupOrderExtend.getProductNum()));
                } else {
                    goodsAmount = groupOrderExtend.getAdvanceDeposit().multiply(new BigDecimal(groupOrderExtend.getProductNum()));
                }
                sellerOrderVO.setGoodsAmount(goodsAmount);
                sellerOrderVO.setOrderAmount(goodsAmount.add(orderPO.getExpressFee()));
                //订单货品价格处理
                for (OrderProductListVO orderProductListVO : sellerOrderVO.getOrderProductList()) {
                    orderProductListVO.setProductShowPrice(groupOrderExtend.getProductPrice());
                }
            }
        }
        if (orderPO.getOrderState() == OrderConst.ORDER_STATE_30) {
            Date autoReceiveTime = TimeUtil.getDayAgoDate(orderPO.getDeliverTime(), orderPO.getAutoReceiveDay());
            sellerOrderVO.setAutoReceiveTime(autoReceiveTime);
        }
        if (orderPO.getOrderState() == OrderConst.ORDER_STATE_10) {
            String value = stringRedisTemplate.opsForValue().get("time_limit_of_auto_cancle_order");
            int autoCancelDay = value == null ? 24 : Integer.parseInt(value);
            Date autoCancelTime = TimeUtil.getHourAgoDate(orderPO.getCreateTime(), autoCancelDay);
            sellerOrderVO.setAutoCancelTime(autoCancelTime);

            Date now = new Date();
            if (now.before(autoCancelTime)) {
                sellerOrderVO.setPayTimeLimit((autoCancelTime.getTime() - now.getTime()) / 1000);
            }
        }

        String appIyId = orderSn;//贷款申请单号
        boolean isLoanPay = PayMethodEnum.isLoanPay(orderPO.getPaymentCode());
        //预付订金
        if (orderPO.getOrderType() == PromotionConst.PROMOTION_TYPE_107) {
            try {
                OrderPresellDTO orderPresellDTO = orderPresellService.getDepositOrderDetail(sellerOrderVO.getOrderSn());
                sellerOrderVO.setOrderPresellDTO(orderPresellDTO);
                sellerOrderVO.setComposePayName(OrderPresellBuilder.dealPreSellComposePayName(orderPresellDTO));
                PayMethodEnum remainMethodEnum = PayMethodEnum.getValue(orderPresellDTO.getRemainPaymentCode());
                //查询尾款贷款类信息
                if (remainMethodEnum != null && PayMethodEnum.isLoanPay(remainMethodEnum)) {
                    CdmallOrderVo loanInfoByOrderSn = loanPayIntegration.getLoanInfoByOrderSn(orderPresellDTO.getRemainPayNo());
                    if (ObjectUtil.isNotNull(loanInfoByOrderSn)) {
                        LoanInfoVo loanInfoVo = new LoanInfoVo();
                        BeanUtils.copyProperties(loanInfoByOrderSn, loanInfoVo);
                        loanInfoVo.setFinanceRuleCode(orderPO.getFinanceRuleCode());
                        loanInfoVo.setRuleTag(orderPO.getRuleTag());
                        sellerOrderVO.setLoanInfoVo(loanInfoVo);
                    }
                }
                appIyId = orderPresellDTO.getRemainPayNo();//贷款申请单号，预付目前只能尾款使用贷款支付，优先检查尾款支付方式是否为贷款支付
                isLoanPay = PayMethodEnum.isLoanPay(orderPresellDTO.getRemainPaymentCode());
                if (Boolean.FALSE.equals(isLoanPay)) {
                    appIyId = orderPresellDTO.getDepositPayNo();//贷款申请单号，预付目前只能尾款使用贷款支付，优先检查尾款支付方式是否为贷款支付
                    isLoanPay = PayMethodEnum.isLoanPay(orderPresellDTO.getDepositPaymentCode());
                }
            } catch (Exception e) {
                log.error("【seller端订单详情】获取预付订金详情失败", e);
            }
        } else {
            PayMethodEnum methodEnum = PayMethodEnum.getValue(orderPO.getPaymentCode());
            //查询贷款信息
            if (methodEnum != null && PayMethodEnum.isLoanPay(methodEnum)) {
                CdmallOrderVo loanInfoByOrderSn = loanPayIntegration.getLoanInfoByOrderSn(orderPO.getOrderSn());
                if (ObjectUtil.isNotNull(loanInfoByOrderSn)) {
                    LoanInfoVo loanInfoVo = new LoanInfoVo();
                    BeanUtils.copyProperties(loanInfoByOrderSn, loanInfoVo);
                    loanInfoVo.setFinanceRuleCode(orderPO.getFinanceRuleCode());
                    loanInfoVo.setRuleTag(orderPO.getRuleTag());
                    sellerOrderVO.setLoanInfoVo(loanInfoVo);
                }
            }
        }

        if (Boolean.TRUE.equals(isLoanPay)) {
            sellerOrderVO.setContractFiles(this.getContracts4Backend(appIyId));
        }

        // 查询需要上传的交易凭证并根据场景分组，与下面查询file-center有所不同，file-center只保存已上传的
        List<OrderTradeProofPO> proofList = tradeProofService.getListByOrderSn(orderSn);
        Map<String, List<OrderTradeProofPO>> proofMap = proofList.stream().collect(Collectors.groupingBy(OrderTradeProofPO::getSceneNo));
        List<MaterialSceneDTO> materialSceneDTOS = Lists.newArrayList();
        proofMap.forEach((sceneNo,list) -> {
            OrderTradeProofPO proofPO = list.get(0);
            MaterialSceneDTO materialSceneDTO = new MaterialSceneDTO();
            materialSceneDTO.setSceneNo(proofPO.getSceneNo());
            materialSceneDTO.setSceneName(proofPO.getSceneName());
            List<OrderTradeProofVO> voList = list.stream().map(OrderTradeProofVO::new).collect(Collectors.toList());
            // 判定是否需要判断风险，目前只需要判断收货确认书的风险
            for (OrderTradeProofVO orderTradeProofVO : voList) {
                if (orderMaterialConfig.getReceiveMaterialNo().equals(orderTradeProofVO.getMaterialNo())){
                    orderTradeProofVO.setNeedCheckRisk(Boolean.TRUE);
                }else{
                    orderTradeProofVO.setNeedCheckRisk(Boolean.FALSE);
                }
            }
            materialSceneDTO.setProofVOList(voList);
            materialSceneDTOS.add(materialSceneDTO);
        });
        sellerOrderVO.setProofList(materialSceneDTOS);
//        Map<String,List<OrderTradeProofVO>> proofVOMap = Maps.newHashMap();
//        for (OrderTradeProofPO proofPO : proofList) {
//            String sceneName = proofPO.getSceneName();
//            OrderTradeProofVO proofVO = new OrderTradeProofVO(proofPO);
//            proofVOMap.getOrDefault(sceneName,Lists.newArrayList()).add(proofVO);
//        }
//        sellerOrderVO.setProofVOMap(proofVOMap);

        // 获取订单下单、发货、签收凭证资料
        List<FileScenesProofVO> fileScenesProofVOS = orderTradeProofService.queryScenesMaterial(orderPO.getOrderSn(), null, null, null, showAgreement);
        sellerOrderVO.setFileScenesProofVOS(fileScenesProofVOS);

        //获取线下补录信息
        if (OrderTypeEnum.isOfflineAll(orderPO.getOrderType())) {
            sellerOrderVO.setOfflineOrderInfo(getOfflineOrderInfo(orderPO, orderExtendPO));
        }

        //换货订单，查询换货详情
        if (ExchangeOrderConst.ORDER_EXCHANGE_FLAG_1 == orderPO.getExchangeFlag()) {
            sellerOrderVO.setExchangeDetailVOList(orderExchangeDetailService.getOrderExchangeDetail(orderSn, new UserDTO(vendor)));
        }
        return SldResponse.success(sellerOrderVO);
    }

    private OfflineOrderInfoDTO getOfflineOrderInfo(OrderPO orderPO, OrderExtendPO extendPO) {
        OfflineOrderInfoDTO offlineOrderInfoDTO = new OfflineOrderInfoDTO();
        offlineOrderInfoDTO.setPaymentTag(orderPO.getPaymentTag());
        offlineOrderInfoDTO.setPaymentTagValue(PaymentTagEnum.valueOf(orderPO.getPaymentTag()).getDesc());
        offlineOrderInfoDTO.setCustomerContract(extendPO.getCustomerContract());
        offlineOrderInfoDTO.setSignInImageUrl(extendPO.getSignInImageUrl());

        offlineOrderInfoDTO.setInvoiceStatus(extendPO.getInvoiceStatus());
        offlineOrderInfoDTO.setInvoiceStatusValue(InvoiceStatusEnum.valueOf(extendPO.getInvoiceStatus()).getDesc());
        offlineOrderInfoDTO.setInvoiceAmount(extendPO.getInvoiceAmount());
        offlineOrderInfoDTO.setInvoiceTime(extendPO.getInvoiceTime());

        OrderOfflineExtendPO orderOfflineExtendPO = orderOfflineExtendService.getOrderOfflineExtendByOrderSn(orderPO.getOrderSn());
        offlineOrderInfoDTO.setAccountPeriodDays(orderOfflineExtendPO.getAccountPeriodDays());
        offlineOrderInfoDTO.setOverdueDays(orderOfflineExtendPO.getOverdueDays());
        offlineOrderInfoDTO.setOverdueFlag(orderOfflineExtendPO.getOverdueFlag());
        offlineOrderInfoDTO.setOverdueFlagValue(orderOfflineExtendPO.getOverdueFlag() == 1 ? "已逾期" : "未逾期");
        offlineOrderInfoDTO.setOverdueTime(orderOfflineExtendPO.getOverdueTime());
        offlineOrderInfoDTO.setBuyerSupplierCode(orderOfflineExtendPO.getBuyerSupplierCode());
        offlineOrderInfoDTO.setBuyerSupplierName(orderOfflineExtendPO.getBuyerSupplierName());

        //已回款金额
        BigDecimal paymentAmounts = BigDecimal.ZERO;
        List<OrderOfflinePO> data = orderOfflineService.queryOrderOfflineList(orderPO.getPaySn());
        if (!CollectionUtils.isEmpty(data)) {
            paymentAmounts = data.stream().map(OrderOfflinePO::getReceiptAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        //待回款金额=订单实付金额-∑已回款金额
        BigDecimal waitPaymentAmount = orderPO.getOrderAmount().subtract(paymentAmounts).compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : orderPO.getOrderAmount().subtract(paymentAmounts);
        offlineOrderInfoDTO.setWaitPaymentAmount(waitPaymentAmount);
        offlineOrderInfoDTO.setReceiptTotalAmount(paymentAmounts);

        offlineOrderInfoDTO.setOrderOfflinePOList(data);

        return offlineOrderInfoDTO;
    }

    /**
     * 获取订单场景凭证资料
     *
     * @param orderPO
     * @return
     */
    private Map<SceneTypeEnum, List<OrderMaterialDTO>> obtainOrderMaterial(OrderPO orderPO) {
        Map<SceneTypeEnum, List<OrderMaterialDTO>> listMap = new HashMap<>(3);

        // 查询订单资料
        List<FileScenesWithMaterialVO> fileScenesWithMaterialVOS = fileCenterIntegration.listScenesMaterialProof(orderPO.getOrderSn());
        if (CollectionUtils.isEmpty(fileScenesWithMaterialVOS)) {
            return listMap;
        }

        for (FileScenesWithMaterialVO materialVO : fileScenesWithMaterialVOS) {
            SceneTypeEnum sceneTypeEnum = SceneTypeEnum.parseEnum(materialVO.getSceneNo());
            List<OrderMaterialDTO> orderMaterialDTOS = materialVO.getMaterialVOList().stream()
                    .map(x -> new OrderMaterialDTO(sceneTypeEnum, x.getMaterialNo(), x.getMaterialName(), x.getMaterialContentList(), x.getProofRemark()))
                    .collect(Collectors.toList());
            listMap.put(sceneTypeEnum, orderMaterialDTOS);
        }

        return listMap;
    }

    /*** @param orderPO
     * @return com.cfpamf.ms.mallpromotion.vo.SpellTeamVO
     * @description : 查询拼团信息
     */
    private SpellTeamVO getSpellTeamVO(String orderSn) {
        SpellTeamMemberExample teamMemberExample = new SpellTeamMemberExample();
        teamMemberExample.setOrderSn(orderSn);
        List<SpellTeamMemberVO> teamMemberList = spellTeamMemberFeignClient.getSpellTeamMemberList(teamMemberExample);
        AssertUtil.notEmpty(teamMemberList, "获取拼团成员信息为空");
        //查询拼团团队状态
        SpellTeamVO spellTeam = spellTeamFeignClient.getSpellTeamBySpellTeamId(teamMemberList.get(0).getSpellTeamId());
        AssertUtil.notNull(spellTeam, "获取拼团团队信息为空，请重试");
        return spellTeam;
    }

    /***
     * @param orderSn
     * @param vendor
     * @return com.slodon.bbc.core.response.JsonResult<com.cfpamf.ms.mallorder.vo.SellerOrderVO>
     * @description : 获取订单详情（小程序端）
     */
    @Override
    public JsonResult<SellerOrderVO> getMinOrderDetail(String orderSn, Vendor vendor) {
        return getOrderDetailWeb(orderSn, vendor, CommonEnum.NO.getCode(), false);
    }

    /**** @param storeId
     * @return com.slodon.bbc.core.response.JsonResult<java.util.List < com.cfpamf.ms.mallorder.vo.BzOrderSearchHistoryVO>>
     * @description : 查询搜索历史
     */
    @Override
    public JsonResult<List<BzOrderSearchHistoryVO>> getSearchHistory(String storeId) {
        List<BzOrderSearchHistoryPO> historyPOS = orderSearchHistoryService.lambdaQuery()
                .eq(BzOrderSearchHistoryPO::getStoreId, Long.valueOf(storeId))
                .orderByDesc(BzOrderSearchHistoryPO::getCreateTime)
                .list();
        List<BzOrderSearchHistoryVO> historyVOS = historyPOS.stream().map(x -> {
            BzOrderSearchHistoryVO historyVO = new BzOrderSearchHistoryVO();
            BeanUtils.copyProperties(x, historyVO);
            return historyVO;
        }).collect(Collectors.toList());
        return SldResponse.success(historyVOS);
    }

    @Override
    public JsonResult deleteSearchHistory(String storeId) {
        LambdaQueryWrapper<BzOrderSearchHistoryPO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(BzOrderSearchHistoryPO::getStoreId, storeId);
        orderSearchHistoryService.remove(wrapper);
        return SldResponse.success("删除成功");
    }


    /**
     * 处理订单货品售后展示按钮
     *
     * @param orderPO
     */
    private void dealAfsButton(OrderPO orderPO) {
        Integer orderState = orderPO.getOrderState();

        if (orderState.equals(OrderConst.ORDER_STATE_20)
                || orderState.equals(OrderConst.ORDER_STATE_25)
                || orderState.equals(OrderConst.ORDER_STATE_30)
                || orderState.equals(OrderConst.ORDER_STATE_40)) {
            //可以售后的状态，遍历订单货品，设置售后按钮
            for (OrderProductPO orderProductPO : orderPO.getOrderProductPOList()) {
                if (OrderTypeEnum.isOfflineAll(orderPO.getOrderType())) {
                    orderProductPO.setAfsButton(null);
                    continue;
                }
                if (orderProductPO.getIsGift() == OrderConst.IS_GIFT_YES) {
                    continue;
                }
                switch (orderState) {
                    case OrderConst.ORDER_STATE_20://待发货，只能申请仅退款
                        dealReturnInfo(orderProductPO, OrderProductConst.AFS_BUTTON_100);
                        break;
                    case OrderConst.ORDER_STATE_25://部分发货，显示退款
                    case OrderConst.ORDER_STATE_30://待买家收货，显示退款
                        dealReturnInfo(orderProductPO, OrderProductConst.AFS_BUTTON_200);
                        break;
                    case OrderConst.ORDER_STATE_40:
                        dealReturnInfo(orderProductPO, OrderProductConst.AFS_BUTTON_300);
                        break;
                    default:
                        break;
                }
            }
        }

        //预付订金特殊处理
        if (OrderTypeEnum.PRE_SELL_DEPOSIT.getValue() == orderPO.getOrderType() && orderState.equals(OrderConst.ORDER_STATE_10)) {
            //可以售后的状态，遍历订单货品，设置售后按钮
            for (OrderProductPO orderProductPO : orderPO.getOrderProductPOList()) {
                if (orderProductPO.getIsGift() == OrderConst.IS_GIFT_YES) {
                    continue;
                }
                dealReturnInfo(orderProductPO, OrderProductConst.AFS_BUTTON_100);
            }
        }
    }

    /**
     * 处理退货信息
     *
     * @param orderProductPO
     * @param button         没有申请过退换货时展示的状态
     */
    private void dealReturnInfo(OrderProductPO orderProductPO, Integer button) {
        // 查询所有退款单，无退款单时直接返回
        LambdaQueryWrapper<OrderAfterPO> orderAfterQuery = new LambdaQueryWrapper<>();
        orderAfterQuery.eq(OrderAfterPO::getOrderProductId, orderProductPO.getOrderProductId())
                .eq(OrderAfterPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                .select(OrderAfterPO::getAfsSn, OrderAfterPO::getAfsId)
                .orderByDesc(OrderAfterPO::getAfsId);
        List<OrderAfterPO> orderAfterPOS = orderAfterService.list(orderAfterQuery);

        if (CollectionUtils.isEmpty(orderAfterPOS)) {
            orderProductPO.setAfsButton(button);
            return;
        }

        // 允许多次退款，根据最新一次的退款判断售后按钮
        LambdaQueryWrapper<OrderReturnPO> orderReturnQuery = new LambdaQueryWrapper();
        orderReturnQuery.eq(OrderReturnPO::getAfsSn, orderAfterPOS.get(0).getAfsSn())
                .eq(OrderReturnPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                .select(OrderReturnPO::getAfsSn, OrderReturnPO::getState);
        OrderReturnPO orderReturnPO = orderReturnService.getOne(orderReturnQuery);

        orderProductPO.setAfsSn(orderReturnPO.getAfsSn());
        orderProductPO.setAfsState(orderReturnPO.getState());
        if (OrderReturnStatus.duringRefund(orderReturnPO.getState())) {
            // 退款单正在退款中
            // 显示 退款中
            orderProductPO.setAfsButton(OrderProductConst.AFS_BUTTON_401);
        } else if (Objects.equals(orderProductPO.getProductNum(), orderProductPO.getReturnNumber())
                && OrderReturnStatus.isFinish(orderReturnPO.getState())) {
            // 最后一单退款完成 且 商品全部退完
            // 显示 退款完成
            orderProductPO.setAfsButton(OrderProductConst.AFS_BUTTON_402);
        } else if (!Objects.equals(orderProductPO.getProductNum(), orderProductPO.getReturnNumber())
                && OrderReturnStatus.isFinish(orderReturnPO.getState())) {
            // 最后一单退款完成 且 商品未全部退完 允许继续退款
            orderProductPO.setAfsButton(button);
        } else {
            // 退款单已关闭 允许继续退款
            orderProductPO.setAfsButton(button);
        }
//        else if (orderReturnPO.getState().equals(OrdersAfsConst.RETURN_STATE_202)
//                || orderReturnPO.getState().equals(OrdersAfsConst.RETURN_STATE_301)
//                || orderReturnPO.getState().equals(OrdersAfsConst.RETURN_STATE_401)) {
//            //退款失败
//            orderProductPO.setAfsButton(OrderProductConst.AFS_BUTTON_301);
//        } else if (orderReturnPO.getState().equals(OrdersAfsConst.RETURN_STATE_302)) {
//            //退款撤销
//            orderProductPO.setAfsButton(OrderProductConst.AFS_BUTTON_302);
//        } else {
//            //没有退款完成的，展示退款中
//            orderProductPO.setAfsButton(OrderProductConst.AFS_BUTTON_401);
//        }
    }

    /***
     * @param orderPO
     * @param childOrdersVO
     * @return void
     * @description : 获取贷款信息
     */
    private void getLoanInfo(OrderPO orderPO, ChildOrdersVO childOrdersVO, JSONObject payWayExtraInfo) {
        PayMethodEnum methodEnum = PayMethodEnum.getValue(orderPO.getPaymentCode());
        //查询贷款信息
        if (methodEnum == null || !PayMethodEnum.isLoanPay(methodEnum)) {
            return;
        }
        log.info("获取贷款类信息:{}", orderPO.getOrderSn());
        CdmallOrderVo loanInfoByOrderSn = loanPayIntegration.getLoanInfoByOrderSn(orderPO.getOrderSn());
        if (ObjectUtil.isNotNull(loanInfoByOrderSn)) {
            LoanInfoVo loanInfoVo = new LoanInfoVo();
            BeanUtils.copyProperties(loanInfoByOrderSn, loanInfoVo);
            childOrdersVO.setLoanInfoVo(loanInfoVo);
        } else if (ObjectUtil.isNotNull(payWayExtraInfo) && payWayExtraInfo.size() > 0) {
            LoanInfoVo loanInfoVo = new LoanInfoVo();
            loanInfoVo.setLoanPeriod(Integer.valueOf(payWayExtraInfo.get("loanPeriod").toString()));
            loanInfoVo.setDueDay(payWayExtraInfo.get("repaymentDay").toString());
            loanInfoVo.setRepaymentMode(payWayExtraInfo.get("repaymentMode").toString());
            loanInfoVo.setRepaymentModeDesc(payWayExtraInfo.get("repaymentModeDesc").toString());
            childOrdersVO.setLoanInfoVo(loanInfoVo);
        }
    }

    /**
     * 处理订单支付剩余时间
     *
     * @param key          redis存储的key
     * @param createTime   订单创建时间
     * @param defaultValue 默认值
     * @return
     */
    @Override
    public long dealRemainTime(String key, Date createTime, Integer defaultValue) {
        //买家几分钟未支付订单，订单取消
        String value = stringRedisTemplate.opsForValue().get(key);
        int limitMinute = value == null ? defaultValue : Integer.parseInt(value);
        // 获取当前时间limitHour小时之前的时间
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, -limitMinute);
        Date cancelTime = calendar.getTime();

        long time1 = createTime.getTime();
        long time2 = cancelTime.getTime();
        long remainTime = (time1 - time2) / 1000;
        return remainTime < 0 ? 0 : remainTime;
    }

    @Override
    public OrderInfoForDbcVO orderDetailInfoFordbc(String orderSn) {

        OrderInfoForDbcVO orderInfoVO = new OrderInfoForDbcVO();

        // 订单信息
        OrderPO orderPO = orderModel.getOrderByOrderSnLambda(orderSn);

        // 订单扩展信息
        OrderExtendPO orderExtendPO = orderExtendModel.getOrderExtendByOrderSnLambda(orderSn);

        //获取组合商品信息
        if (OrderConst.ORDER_TYPE_13 == orderPO.getOrderType()) {
            LambdaQueryWrapper<BzOrderProductCombinationPO> queryOrder = Wrappers.lambdaQuery();
            queryOrder.eq(BzOrderProductCombinationPO::getOrderSn, orderSn)
                    .select(BzOrderProductCombinationPO::getProductId, BzOrderProductCombinationPO::getGoodsId, BzOrderProductCombinationPO::getGoodsName);
            BzOrderProductCombinationPO productCombinationPO = orderProductCombinationService.getOne(queryOrder);
            orderInfoVO.setCombinationProductId(ObjectUtil.isNull(productCombinationPO) ? null : productCombinationPO.getProductId());
        }

        orderInfoVO.setOrderSn(orderPO.getOrderSn());
        orderInfoVO.setPaymentCode(orderPO.getPaymentCode());
        orderInfoVO.setOrderState(orderPO.getOrderState());
        orderInfoVO.setOrderType(orderPO.getOrderType());
        orderInfoVO.setSubmitTime(orderPO.getCreateTime());
        orderInfoVO.setPayTime(orderPO.getPayTime());
        orderInfoVO.setFinishTime(orderPO.getFinishTime());
        orderInfoVO.setChannel(orderPO.getChannel());
        orderInfoVO.setUserNo(orderPO.getUserNo());
        orderInfoVO.setCustomerId(orderExtendPO.getCustomerId());
        orderInfoVO.setMemberId(orderPO.getMemberId());
        orderInfoVO.setStoreId(orderPO.getStoreId());
        orderInfoVO.setStoreName(orderPO.getStoreName());
        orderInfoVO.setStoreBranchName(orderPO.getStoreBranchName());
        orderInfoVO.setStoreBranchCode(orderPO.getStoreBranch());
        orderInfoVO.setFinanceRuleCode(orderPO.getFinanceRuleCode());
        orderInfoVO.setStoreActivityAmount(orderPO.getStoreActivityAmount());
        orderInfoVO.setStoreVoucherAmount(orderPO.getStoreVoucherAmount());
        orderInfoVO.setAreaCode(orderPO.getAreaCode());
        orderInfoVO.setManager(orderExtendPO.getManager());
        orderInfoVO.setManageType(orderExtendPO.getManageType());
        orderInfoVO.setManagerBranch(orderExtendPO.getBranch());
        orderInfoVO.setManagerBranchName(orderExtendPO.getBranchName());
        orderInfoVO.setOrderPattern(orderPO.getOrderPattern());
        orderInfoVO.setExchangeFlag(orderPO.getExchangeFlag());
        orderInfoVO.setUserIdentity(orderPO.getUserIdentity());
        orderInfoVO.setShareCode(orderExtendPO.getShareCode());

        // 查询邀请人信息
        if (Objects.nonNull(orderExtendPO.getShareCode())) {
            GoodsSharecodeBindUser shareMsg = goodsSharecodeBindUserFeignClient
                    .getGoodsSharecodeBindUserByShareCode(orderExtendPO.getShareCode());
            if (Objects.nonNull(shareMsg) && Objects.nonNull(shareMsg.getUserNo())) {
                UserBaseInfoVo userBaseInfoVo = customerIntegration.baseInfoByUserNo(shareMsg.getUserNo());
                if (Objects.nonNull(userBaseInfoVo)) {
                    Optional.ofNullable(userBaseInfoVo.getCustInfoVo()).ifPresent(custInfoVo -> {
                        orderInfoVO.setInviterName(custInfoVo.getCustName());
                        orderInfoVO.setInviterIdCard(custInfoVo.getIdNo());
                        Optional.ofNullable(custInfoVo.getCustDetail()).ifPresent(custDetail -> {
                            orderInfoVO.setBranchCode(custDetail.getLoanBranch());
                            orderInfoVO.setBranchName(custDetail.getLoanBranchName());
                        });
                    });
                }
            }
        }

        // 订单商品信息
        List<OrderInfoForDbcVO.OrderProductForDbcVO> orderProductVOs = new ArrayList<>();
        List<OrderProductPO> orderProducts = orderProductModel.getOrderProductListByOrderSn(orderSn);

        List<OrderPriceRecordPO> orderPriceRecordPOS = orderPriceRecordService.listPriceRecord(orderSn);
        Set<Long> priceRecordPOMap = orderPriceRecordPOS.stream().map(OrderPriceRecordPO::getOrderProductId).collect(Collectors.toSet());

        //查询商品行乡信数据信息
        List<OrderProductAgricExtendPO> orderProductAgricExtendPOList = orderProductAgricExtendModel.getByOrderSn(orderSn);
        if (!CollectionUtils.isEmpty(orderProducts)) {
            orderProducts.forEach(orderProductPO -> {
                if (orderProductPO.getIsGift().equals(OrderConst.IS_GIFT_NO)) {
                    OrderInfoForDbcVO.OrderProductForDbcVO orderProductVO = new OrderInfoForDbcVO.OrderProductForDbcVO();
                    orderProductVO.setPriceChanged(priceRecordPOMap.contains(orderProductPO.getOrderProductId()));
                    orderProductVO.setOrderProductId(orderProductPO.getOrderProductId());
                    orderProductVO.setProductId(orderProductPO.getProductId());
                    orderProductVO.setGoodsId(orderProductPO.getGoodsId());
                    orderProductVO.setGoodsName(orderProductPO.getGoodsName());
                    orderProductVO.setProductNum(orderProductPO.getProductNum());
                    orderProductVO.setDeliveryNum(orderProductPO.getDeliveryNum());
                    orderProductVO.setCommissionRate(orderProductPO.getCommissionRate());
                    if (Objects.nonNull(orderProductPO.getDistributeParent())) {
                        orderProductVO.setDistributionType(OrderConst.DISTRIBUTION_TYPE_4);
                    } else {
                        orderProductVO.setDistributionType(orderProductPO.getIsDistribution().equals(OrderConst.DISTRIBUTION_TYPE_1) ? OrderConst.DISTRIBUTION_TYPE_2 : OrderConst.DISTRIBUTION_TYPE_1);
                    }
                    orderProductVO.setValidQty(orderProductPO.getProductNum() - orderProductPO.getReturnNumber());

                    orderProductVO.setTaxPrice(orderProductPO.getMoneyAmount().
                            divide(new BigDecimal(orderProductPO.getProductNum()), 2, RoundingMode.DOWN));
                    orderProductVO.setProductTaxPrice(orderProductPO.getTaxPrice());
                    orderProductVO.setLandingPrice(orderProductPO.getLandingPrice());
                    orderProductVO.setMoneyAmount(orderProductPO.getMoneyAmount());
                    orderProductVO.setProductShowPrice(orderProductPO.getProductShowPrice());
                    orderProductVO.setStoreVoucherAmount(orderProductPO.getStoreVoucherAmount());
                    orderProductVO.setPlatformVoucherAmount(orderProductPO.getPlatformVoucherAmount());
                    orderProductVO.setPlatformVoucherNetValue(orderProductPO.getPlatformVoucherNetValue());
                    orderProductVO.setStoreActivityAmount(orderProductPO.getStoreActivityAmount());
                    orderProductVO.setPlatformActivityAmount(orderProductPO.getPlatformActivityAmount());
                    orderProductVO.setChannelSkuId(orderProductPO.getChannelSkuId());
                    orderProductVO.setChannelSkuUnit(orderProductPO.getChannelSkuUnit());
                    orderProductVO.setDistributeParent(orderProductPO.getDistributeParent());
                    orderProductVO.setGoodsIsDistribute(orderProductPO.getGoodsIsDistribute());
                    orderProductVO.setAgricDistribute(orderProductPO.getAgricDistribute());
                    orderProductVO.setBatchNo(orderProductPO.getBatchNo());
                    orderProductVO.setPurchaseSubCode(orderProductPO.getPurchaseSubCode());

                    if (CollectionUtil.isNotEmpty(orderProductAgricExtendPOList)) {
                        Optional<OrderProductAgricExtendPO> first = orderProductAgricExtendPOList.stream().filter(temp -> Objects.equals(orderProductPO.getOrderProductId(), temp.getOrderProductId())).findFirst();
                        first.ifPresent(temp -> orderProductVO.setSalesSeasonCode(temp.getSalesSeasonCode()));

                    }
                    OrderProductErpExtendPO erpExtendPO = orderProductErpExtendService.selectOrderProductErpExtendByOrderSnAndOrderProductId(orderSn, orderProductPO.getOrderProductId());
                    if (Objects.nonNull(erpExtendPO)) {
                        orderProductVO.setCategoryPath(erpExtendPO.getProductCategoryPath());
                        orderProductVO.setCategoryPathName(erpExtendPO.getProductCategoryPathName());
                    }
                    orderProductVOs.add(orderProductVO);
                }
            });
        }

        orderInfoVO.setOrderProducts(orderProductVOs);

        return orderInfoVO;
    }

    @Override
    public OrderBriefInfoVO orderBriefInfo(String orderSn) {
        // 订单信息
        OrderPO orderPO = orderModel.getOrderByOrderSnLambda(orderSn);
        BizAssertUtil.notNull(orderPO, "查询订单信息为空");
        // 订单商品
        List<OrderProductPO> orderProducts = orderProductModel.getOrderProductListByOrderSn(orderSn);
        BizAssertUtil.notEmpty(orderProducts, "查询订单商品信息为空");

        OrderBriefInfoVO orderBriefInfoVO = new OrderBriefInfoVO();
        orderBriefInfoVO.setOrderSn(orderPO.getOrderSn());
        orderBriefInfoVO.setPaySn(orderPO.getPaySn());
        orderBriefInfoVO.setOrderState(orderPO.getOrderState());
        orderBriefInfoVO.setOrderType(orderPO.getOrderType());
        orderBriefInfoVO.setMemberId(orderPO.getMemberId());
        List<OrderProductBriefInfoVO> orderProductBriefInfoVOs = new ArrayList<>();
        for (OrderProductPO orderProductPO : orderProducts) {
            OrderProductBriefInfoVO orderProductBriefInfoVO = new OrderProductBriefInfoVO();
            orderProductBriefInfoVO.setGoodsId(orderProductPO.getGoodsId());
            orderProductBriefInfoVO.setProductId(orderProductPO.getProductId());
            orderProductBriefInfoVO.setProductNum(orderProductPO.getProductNum());
            orderProductBriefInfoVOs.add(orderProductBriefInfoVO);
        }
        orderBriefInfoVO.setOrderProductInfos(orderProductBriefInfoVOs);

        return orderBriefInfoVO;
    }

    @Override
    public OrderInfoVO getOrderDetailInfo(String orderSn) {
        OrderInfoVO orderInfoVO = new OrderInfoVO();

        // 订单信息
        OrderPO orderPO = orderModel.getOrderByOrderSnLambda(orderSn);
        // 订单扩展信息
        OrderExtendPO orderExtendPO = orderExtendModel.getOrderExtendByOrderSnLambda(orderSn);

        orderInfoVO.setOrderSn(orderPO.getOrderSn());
        orderInfoVO.setCreateTime(orderPO.getCreateTime());
        orderInfoVO.setAreaCode(orderPO.getAreaCode());
        orderInfoVO.setPaymentCode(orderPO.getPaymentCode());
        orderInfoVO.setStoreBankAccount(orderPO.getSellerId());
        orderInfoVO.setZhnxReceiveAccount(platformCollectionAccount);
        orderInfoVO.setPayTime(orderPO.getPayTime());
        orderInfoVO.setExpressFee(orderPO.getExpressFee());
        orderInfoVO.setThirdpartnarFeeRate(orderPO.getThirdpartnarFeeRate());
        orderInfoVO.setServiceFeeRate(orderPO.getServiceFeeRate());
        orderInfoVO.setOrderCommission(orderPO.getOrderCommission());
        orderInfoVO.setBusinessCommission(orderPO.getBusinessCommission());
        orderInfoVO.setOrderAmount(orderPO.getOrderAmount());
        orderInfoVO.setOrderType(orderPO.getOrderType());
        orderInfoVO.setPaySn(orderPO.getPaySn());
        Integer orderPattern = orderPO.getOrderPattern();
        orderInfoVO.setOrderPattern(orderPattern);
        orderInfoVO.setInvoiceStatus(orderExtendPO.getInvoiceStatus());
        orderInfoVO.setInvoiceInfo(orderExtendPO.getInvoiceInfo());
        orderInfoVO.setOrderState(orderPO.getOrderState());
        orderInfoVO.setReceiverName(orderExtendPO.getReceiverName());
        orderInfoVO.setFinishTime(orderPO.getFinishTime());
        orderInfoVO.setChannel(orderPO.getChannel());
        orderInfoVO.setUserNo(orderPO.getUserNo());
        orderInfoVO.setMemberId(orderPO.getMemberId());
        orderInfoVO.setStoreId(orderPO.getStoreId());
        orderInfoVO.setStoreCompanyName(orderPO.getStoreCompanyName());
        orderInfoVO.setBranchName(orderExtendPO.getBranchName());
        orderInfoVO.setBranch(orderExtendPO.getBranch());
        orderInfoVO.setReceiveBranchCode(orderExtendPO.getReceiveBranchCode());
        orderInfoVO.setReceiveBranchName(orderExtendPO.getReceiveBranchName());
        orderInfoVO.setIsSelf(orderPO.getStoreIsSelf());
        orderInfoVO.setDiscountAmount(orderPO.getActivityDiscountAmount());
        orderInfoVO.setOrderActivityDiscountDetail(orderPO.getActivityDiscountDetail());
        orderInfoVO.setReceivedByTrack(this.receivedByTrackForJindie(orderPO));
        orderInfoVO.setCustomerCode(orderExtendPO.getCustomerId());
        orderInfoVO.setCustomerBusinessName(orderExtendPO.getCustomerName());
        orderInfoVO.setCustomerBusinessCode(orderExtendPO.getCustomerId());
        orderInfoVO.setCustomerManagerCode(orderExtendPO.getManager());
        orderInfoVO.setCustomerManagerName(orderExtendPO.getManagerName());
        orderInfoVO.setGoodsAmount(orderPO.getGoodsAmount());
        orderInfoVO.setExchangeFlag(orderPO.getExchangeFlag());
        orderInfoVO.setDepotCode(orderExtendPO.getWarehouseCode());
        orderInfoVO.setUserMobile(orderPO.getUserMobile());

        // 补录订单如果为企业客户信息，使用企业客户信息返回
        if (OrderTypeEnum.isOfflineAll(orderPO.getOrderType())) {
            OrderOfflineExtendPO orderOfflineExtendPo = orderOfflineExtendService.getOrderOfflineExtendByOrderSn(orderSn);
            if (Objects.nonNull(orderOfflineExtendPo) && Objects.equals(CustomerTypeEnum.ENTERPRISE_CUSTOMER.getCode(), orderOfflineExtendPo.getCustomerType())) {
                orderInfoVO.setCustomerType(orderOfflineExtendPo.getCustomerType().toString());
                //企业客户传客户编码
                orderInfoVO.setCompanyName(orderOfflineExtendPo.getCustomerCode());
            }
        }

        if (orderPO.getOrderState() > 20) {
            List<OrderLogisticPO> orderLogisticPOList = orderLogisticService.getOrderLogisticByOrderSn(orderPO.getOrderSn(), null);
            if (!CollectionUtils.isEmpty(orderLogisticPOList)) {
                orderInfoVO.setDeliveryWarehouse(orderLogisticPOList.get(0).getDeliverWarehouse());
                orderInfoVO.setDeliveryWarehouseName(orderLogisticPOList.get(0).getDeliverWarehouseName());
            }
        }

        // 自提订单推送金蝶仓库寻仓逻辑调整 https://cfpamf.yuque.com/xqzx/boskh8/bvmtcf0z5cypiqwl
        if (OrderPatternEnum.SELF_LIFT.getValue().equals(orderPO.getOrderPattern())) {
            // 自提地址
            SelfLiftingPointVo selfLiftingPoint = shopIntegration.getSelfLiftingPoint(orderExtendPO.getPointId());
            if (Objects.nonNull(selfLiftingPoint)) {
                if (Objects.equals(SelfLiftingPointTypeEnum.BRANCH_POINT.getType(), selfLiftingPoint.getPointType())) {
                    /**
                     * 分支自提点，使用分支机构编码 作为仓库编码推送金蝶，mall-external-adapter服务会优先判断该字段是否有值，
                     * 有值则使用该字段值作为仓库推送金蝶，无值则取actualWarehouseCode字段
                     */
                    orderInfoVO.setSelfLiftingBranchCode(selfLiftingPoint.getBranchCode());
                }
                orderInfoVO.setActualWarehouseCode(selfLiftingPoint.getActualWarehouseCode());
            }
            if (StringUtils.isNotBlank(orderExtendPO.getActualWarehouseCode())) {
                orderInfoVO.setActualWarehouseCode(orderExtendPO.getActualWarehouseCode());
            }
        }

        //如果是换货后的订单，将原来的订单进行返回
        if (ExchangeOrderConst.ORDER_EXCHANGE_FLAG_2 == orderPO.getExchangeFlag()) {
            OrderExchangeDetailExample orderExchangeDetailExample = new OrderExchangeDetailExample();
            orderExchangeDetailExample.setExchangeOrderSn(orderSn);
            OrderExchangeDetailPO orderExchangeDetailPO = orderExchangeDetailMapper.getExchangeOrderDetailByExample(orderExchangeDetailExample);
            if (orderExchangeDetailPO == null) {
                log.error("换货后的订单号：{}，查询不到该换货订单的详情", orderSn);
            }
            orderInfoVO.setOriginalOrderSn(orderExchangeDetailPO.getOrderSn());
        }

        // 订单收货信息
        this.buildReceiveInfo(orderInfoVO, orderExtendPO);

        // 补充订单扩展信息
        this.buildOrderExtendInfo(orderInfoVO, orderExtendPO);

        // 发货信息
        orderInfoVO.setDeliverTime(orderPO.getDeliverTime());
        LambdaQueryWrapper<OrderLogisticPO> lambdaQueryWrapper = Wrappers.lambdaQuery(OrderLogisticPO.class);
        lambdaQueryWrapper.eq(OrderLogisticPO::getOrderSn, orderSn)
                .eq(OrderLogisticPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                .orderByDesc(OrderLogisticPO::getCreateTime);
        List<OrderLogisticPO> orderLogisticPOs = orderLogisticMapper.selectList(lambdaQueryWrapper);
        if (CollectionUtil.isNotEmpty(orderLogisticPOs)) {
            orderInfoVO.setExpressNumber(orderLogisticPOs.get(0).getExpressNumber());
            orderInfoVO.setDeliverName(orderLogisticPOs.get(0).getDeliverName());
        }

        List<OrderProductDetailVO> productDetailVOs = new ArrayList<>();

        // 查询订单商品信息
        List<OrderProductPO> orderProducts = orderProductModel.getOrderProductListByOrderSn(orderSn);
        for (OrderProductPO orderProduct : orderProducts) {
            OrderProductDetailVO orderProductDetailVO = new OrderProductDetailVO();
            orderProductDetailVO.setOrderProductId(orderProduct.getOrderProductId());
            orderProductDetailVO.setProductId(orderProduct.getProductId());
            orderProductDetailVO.setGoodsId(orderProduct.getGoodsId());
            orderProductDetailVO.setGoodsName(orderProduct.getGoodsName());
            orderProductDetailVO.setProductName(orderProduct.getGoodsName() + orderProduct.getSpecValues());
            orderProductDetailVO.setProductNum(orderProduct.getProductNum());
            orderProductDetailVO.setValidProductNum(orderProduct.getProductNum() - orderProduct.getReturnNumber());
            orderProductDetailVO.setTaxPrice(orderProduct.getTaxPrice());
            orderProductDetailVO.setTaxRate(orderProduct.getTaxRate());
            orderProductDetailVO.setLandingPrice(orderProduct.getLandingPrice());
            orderProductDetailVO.setCost(orderProduct.getCost());
            orderProductDetailVO.setMoneyAmount(orderProduct.getMoneyAmount());
            orderProductDetailVO.setUnit(orderProduct.getProductUnit());
            orderProductDetailVO.setDiscountAmount(orderProduct.getActivityDiscountAmount());
            orderProductDetailVO.setStoreActivityAmount(orderProduct.getStoreActivityAmount());
            orderProductDetailVO.setPlatformActivityAmount(orderProduct.getPlatformActivityAmount());
            orderProductDetailVO.setXzCardAmount(orderProduct.getXzCardAmount());
            orderProductDetailVO.setStoreVoucherAmount(orderProduct.getStoreVoucherAmount());
            orderProductDetailVO.setPlatformVoucherAmount(orderProduct.getPlatformVoucherAmount());
            orderProductDetailVO.setIntegralCashAmount(orderProduct.getIntegralCashAmount());
            orderProductDetailVO.setProductShowPrice(orderProduct.getProductShowPrice());
            orderProductDetailVO.setOrderProductActivityDiscountDetail(orderProduct.getActivityDiscountDetail());
            orderProductDetailVO.setDistributeParentProductId(orderProduct.getDistributeParent());
            orderProductDetailVO.setChannelSkuId(orderProduct.getChannelSkuId());
            orderProductDetailVO.setChannelSkuUnit(orderProduct.getChannelSkuUnit());
            orderProductDetailVO.setChannelNewSkuId(orderProduct.getChannelNewSkuId());
            orderProductDetailVO.setChannelSource(orderProduct.getChannelSource());
            orderProductDetailVO.setSkuMaterialCode(orderProduct.getSkuMaterialCode());
            orderProductDetailVO.setSupplierCode(orderProduct.getSupplierCode());
            orderProductDetailVO.setSupplierName(orderProduct.getSupplierName());
            orderProductDetailVO.setBatchNo(orderProduct.getBatchNo());
            orderProductDetailVO.setDeliveryState(orderProduct.getDeliveryState().getValue());
            orderProductDetailVO.setPerformanceMode(orderProduct.getPerformanceMode());
            BigDecimal activityDiscountAmount = orderProduct.getActivityDiscountAmount() == null ? BigDecimal.ZERO : orderProduct.getActivityDiscountAmount();
            BigDecimal xzCardAmount = orderProduct.getXzCardAmount() == null ? BigDecimal.ZERO : orderProduct.getXzCardAmount();
            orderProductDetailVO.setGoodsDiscountTotalAmount(activityDiscountAmount.add(xzCardAmount));

            // 查询原商品扩展信息
            LambdaQueryWrapper<OrderProductExtendPO> queryWrapper = Wrappers.lambdaQuery(OrderProductExtendPO.class);
            queryWrapper.eq(OrderProductExtendPO::getOrderProductId, orderProduct.getOrderProductId())
                    .eq(OrderProductExtendPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
            List<OrderProductExtendPO> orderProductExtendPOs = orderProductExtendModel.getOneOrderProductExtendPOByQueryWrapper(queryWrapper);
            if (CollectionUtil.isNotEmpty(orderProductExtendPOs)) {
                StringJoiner promotionIdJoin = new StringJoiner(",");
                orderProductExtendPOs.forEach(extend -> {
                    promotionIdJoin.add(extend.getPromotionId());
                });
                orderProductDetailVO.setPromotionId(promotionIdJoin.toString());
            }

            orderProductDetailVO.setSpecValues(orderProduct.getSpecValues());
            //获取商品物流信息,取第一条物流信息
            orderProductDetailVO.setOrderLogisticVO(dealProductLogisticInfo(orderProduct.getOrderProductId()));

            productDetailVOs.add(orderProductDetailVO);
        }

        orderInfoVO.setOrderProducts(productDetailVOs);

        // 订单货品拓展表查询
        List<OrderProductExtendPO> orderProductExtendPOList = orderProductExtendService.getOrderProductExtendPOList(orderSn);
        List<OrderProductExtend> opeList = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(orderProductExtendPOList)) {
            opeList = orderProductExtendPOList.stream().map(po -> {
                OrderProductExtend ope = new OrderProductExtend();
                BeanUtils.copyProperties(po, ope);
                return ope;
            }).collect(Collectors.toList());
        }
        orderInfoVO.setOrderProductExtendList(opeList);
        // 订单优惠详情查询
        List<OrderPromotionDetailPO> promotionDetailList = orderPromotionDetailService.getOrderPromotionDetailByOrderSn(orderSn);
        Map<String, BigDecimal> reatilMap = Maps.newHashMap();
        Map<String, BigDecimal> discountMap = Maps.newHashMap();
        List<OrderPromotionDetail> detailList = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(promotionDetailList)) {
            detailList = promotionDetailList.stream().map(po -> {
                OrderPromotionDetail detail = new OrderPromotionDetail();
                BeanUtils.copyProperties(po, detail);
                return detail;
            }).collect(Collectors.toList());
            // 根据出资方的不同，计算总计的出售价
            for (OrderPromotionDetailPO detailPo : promotionDetailList) {
                if (detailPo.getPromotionType().equals(PromotionConst.PROMOTION_TYPE_402)) {
                    String funder = detailPo.getFunder();
                    CouponFunder couponFunder = CouponFunder.getByCode(funder);
                    if (Objects.nonNull(couponFunder)) {
                        BigDecimal retailPrice = detailPo.getRetailPrice();
                        if (Objects.nonNull(retailPrice)) {
                            reatilMap.put(funder, reatilMap.getOrDefault(funder, BigDecimal.ZERO).add(retailPrice));
                        }
                        BigDecimal promotionAmount = detailPo.getPromotionAmount();
                        if (Objects.nonNull(promotionAmount)) {
                            discountMap.put(funder, discountMap.getOrDefault(funder, BigDecimal.ZERO).add(promotionAmount));
                        }
                    } else if (StringUtils.isNotBlank(funder)) {
                        // 出资方有值，且未匹配到对应的枚举
                        log.warn("getOrderByOrderSnV2 出资方未在枚举中定义!,orderSn:{}", orderSn);
                    }
                }
            }
        }
        orderInfoVO.setOrderPromotionDetailList(detailList);
        orderInfoVO.setRetailMap(reatilMap);
        orderInfoVO.setDiscountMap(discountMap);

        // 卡券包信息查询
        if (OrderPatternEnum.COUPON_CENTRE.getValue().equals(orderPattern)) {
            LambdaQueryWrapper<OrderProductCouponPO> couponQuery = Wrappers.lambdaQuery(OrderProductCouponPO.class);
            couponQuery.eq(OrderProductCouponPO::getOrderSn, orderSn);
            List<OrderProductCouponPO> list = productCouponService.list(couponQuery);
            List<OrderProductCoupon> opcList = list.stream().map(po -> {
                OrderProductCoupon opc = new OrderProductCoupon();
                BeanUtils.copyProperties(po, opc);
                return opc;
            }).collect(Collectors.toList());
            orderInfoVO.setOrderProductCouponList(opcList);
        }

        //组合商品信息
        if (orderInfoVO.getOrderType() == OrderTypeEnum.COMBINATION.getValue()) {
            LambdaQueryWrapper<BzOrderProductCombinationPO> queryOrder = Wrappers.lambdaQuery();
            queryOrder.eq(BzOrderProductCombinationPO::getOrderSn, orderInfoVO.getOrderSn());
            BzOrderProductCombinationPO combinationPO = orderProductCombinationService.getOne(queryOrder);
            if (combinationPO != null) {
                orderInfoVO.setCombinationGoodsId(combinationPO.getGoodsId());
                orderInfoVO.setCombinationGoodsName(combinationPO.getGoodsName());
            }
        }

        return orderInfoVO;
    }

    private OrderLogisticVO dealProductLogisticInfo(Long orderProductId) {
        return orderLogisticService.getOrderLogisticByOrderProductId(orderProductId);
    }

    /**
     * 补充订单扩展信息
     *
     * @param orderInfoVO   订单信息VO
     * @param orderExtendPO 订单扩展信息
     */
    private void buildOrderExtendInfo(OrderInfoVO orderInfoVO, OrderExtendPO orderExtendPO) {
        OrderExtendInfoVO extendInfoVO = new OrderExtendInfoVO();
        extendInfoVO.setOrderSn(orderExtendPO.getOrderSn());
        extendInfoVO.setStoreKingdeeOrgName(orderExtendPO.getStoreKingdeeOrgName());
        Store store = storeFeignClient.getStoreByStoreId(orderInfoVO.getStoreId());
        // 20250421 金蝶库存管理模式修改为动态获取，并增加金蝶库存编码返回
        extendInfoVO.setKingdeeStockPushMode(store.getJindieStockFlag());
        extendInfoVO.setJindieWarehouseCode(store.getJindieWarehouseCode());

        extendInfoVO.setReceiveCode(orderExtendPO.getReceiveCode());
        orderInfoVO.setOrderExtendInfoVO(extendInfoVO);
    }

    /**
     * 填入收货信息
     *
     * @param orderInfoVO   订单信息（target）
     * @param orderExtendPO 订单扩展表信息（source）
     */
    private void buildReceiveInfo(OrderInfoVO orderInfoVO, OrderExtendPO orderExtendPO) {
        OrderReceiveInfoVO orderReceiveInfoVO = new OrderReceiveInfoVO();
        orderReceiveInfoVO.setReceiverName(orderExtendPO.getReceiverName());
        orderReceiveInfoVO.setReceiverMobile(orderExtendPO.getReceiverMobile());
        orderReceiveInfoVO.setProvinceName(orderExtendPO.getReceiverProvinceCode());
        orderReceiveInfoVO.setCityName(orderExtendPO.getReceiverCityCode());
        orderReceiveInfoVO.setDistrictName(orderExtendPO.getReceiverDistrictCode());
        orderReceiveInfoVO.setTownName(orderExtendPO.getReceiverTownCode());
        orderReceiveInfoVO.setDetailAddress(orderExtendPO.getReceiverAddress());
        orderInfoVO.setOrderReceiveInfo(orderReceiveInfoVO);
    }

    @Override
    @Transactional
    public int updateData(String tableName, String columnName, String columnValue, String idColumn, String idValue) {
        List<HashMap<String, String>> datas = orderMapper.selectUpdateData(tableName, columnName, columnValue, idColumn, idValue);
        int updateCount;
        if (datas.size() > 1) {
            throw new MallException("处理数据大于一条，禁止处理");
        } else {
            log.warn("处理的数据内容为{}", JSON.toJSONString(datas));
            updateCount = orderMapper.updateData(tableName, columnName, columnValue, idColumn, idValue);
            if (updateCount > 1) {
                throw new MallException("处理数据【{}】大于一条，禁止处理，回滚");
            }
        }
        return updateCount;
    }

    @Override
    public Boolean updateProductInfo(List<Long> productIds) {
        Map<Long, GoodsExtend> goodsExtendMap = new HashMap<>();
        for (Long productId : productIds) {
            // 查询商品信息
            Product product = productFeignClient.getProductByProductId(productId);
            if (Objects.isNull(product)) {
                continue;
            }

            GoodsExtend goodsExtend = goodsExtendMap.get(product.getGoodsId());
            if (Objects.isNull(goodsExtend)) {
                // 商品扩展信息
                GoodsExtendExample goodsExtendExample = new GoodsExtendExample();
                goodsExtendExample.setGoodsId(product.getGoodsId());
                List<GoodsExtend> goodsExtendList = goodsExtendFeignClient.getGoodsExtendList(goodsExtendExample);
                goodsExtend = goodsExtendList.get(0);
                goodsExtendMap.put(product.getGoodsId(), goodsExtend);
            }

            // 更新订单商品
            LambdaUpdateWrapper<OrderProductPO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(OrderProductPO::getProductId, productId)
                    .eq(OrderProductPO::getEnabledFlag, 1)
                    .set(OrderProductPO::getGoodsParameter, goodsExtend.getGoodsParameter())
                    .set(OrderProductPO::getWeight, product.getWeight());
            orderProductService.update(updateWrapper);
        }

        return Boolean.TRUE;
    }

    @Override
    public Boolean updateOrderBranch() {
        List<String> userNos = orderExtendMapper.listUserNo2Fix();
        if (CollectionUtils.isEmpty(userNos)) {
            return Boolean.TRUE;
        }

        for (String userNo : userNos) {
            try {
                UserBaseInfo userBaseInfo = userInfoComponent.userBaseInfoByUserNo(userNo);
                if (Objects.isNull(userBaseInfo)) {
                    continue;
                }

                LambdaUpdateWrapper<OrderExtendPO> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(OrderExtendPO::getCustomerId, userBaseInfo.getCustomerId())
                        .isNull(OrderExtendPO::getBranch)
                        .set(OrderExtendPO::getCustomerName, userBaseInfo.getCustomerName())
                        .set(OrderExtendPO::getManager, userBaseInfo.getManager())
                        .set(OrderExtendPO::getManagerName, userBaseInfo.getManagerName())
                        .set(OrderExtendPO::getSupervisor, userBaseInfo.getSupervisor())
                        .set(OrderExtendPO::getSupervisorName, userBaseInfo.getSupervisorName())
                        .set(OrderExtendPO::getBranch, userBaseInfo.getBranchCode())
                        .set(OrderExtendPO::getBranchName, userBaseInfo.getBranchName())
                        .set(OrderExtendPO::getAreaCode, userBaseInfo.getAreaCode())
                        .set(OrderExtendPO::getAreaName, userBaseInfo.getAreaName())
                        .set(OrderExtendPO::getUpdateBy, "0802batch");
                orderExtendService.update(updateWrapper);

            } catch (Exception e) {
                log.warn("分支信息处理失败, userNo:{}", userNo);
                continue;
            }
        }

        return Boolean.TRUE;
    }

    @Override
    public Boolean updateOrderZone() {
        List<String> branches = orderExtendMapper.listAllBranch();
        for (String branch : branches) {
            try {
                BranchRelationVO branchRelationVO = hrmsIntegration.getBranchRelation(branch);
                LambdaUpdateWrapper<OrderExtendPO> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(OrderExtendPO::getBranch, branch)
                        .isNull(OrderExtendPO::getZoneCode)
                        .set(OrderExtendPO::getZoneCode, branchRelationVO.getZoneCode())
                        .set(OrderExtendPO::getZoneName, branchRelationVO.getZoneName());
                orderExtendService.update(updateWrapper);
            } catch (Exception e) {
                log.warn("更新片区信息失败，branch：{}", branch);
            }
        }

        return Boolean.TRUE;
    }

    @Override
    public Integer replenishSkuMaterialName() {
        List<String> materialCodeList = orderExtendMapper.listSkuMaterialCode();
        MallProductQuery query = new MallProductQuery();
        query.setSkuMaterialCodes(materialCodeList);
        List<com.cfpamf.ms.mallorder.integration.erp.vo.ProductVO> productInfoList = erpIntegration.getProductInfoList(query);

        for (ProductVO productVO : productInfoList) {

            // 更新订单商品
            LambdaUpdateWrapper<OrderProductPO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(OrderProductPO::getSkuMaterialCode, productVO.getProductNo())
                    .eq(OrderProductPO::getEnabledFlag, 1)
                    .set(OrderProductPO::getSkuMaterialName, productVO.getProductName());
            orderProductService.update(updateWrapper);
        }

        return productInfoList.size();
    }

    @Transactional
    @Override
    public Integer replenishOrderUserCode() {
        // 查询出userCode为空的订单，一次处理1000条
        LambdaQueryWrapper<OrderPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(OrderPO::getUserNo, "")
                .eq(OrderPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                .last("limit 1000");
        List<OrderPO> orderPos = this.list(queryWrapper);
        int count = 0;
        for (OrderPO order : orderPos) {
            if (StringUtils.isNotBlank(order.getUserNo()) || StringUtils.isNotBlank(order.getUserMobile())) {
                continue;
            }
            // 补充userCode信息
            Member member = memberFeignClient.getMemberByMemberId(order.getMemberId());
            if (Objects.nonNull(member) && Objects.nonNull(member.getUserNo())) {
                LambdaUpdateWrapper<OrderPO> updateWrapper = Wrappers.lambdaUpdate();
                updateWrapper.eq(OrderPO::getOrderSn, order.getOrderSn())
                        .eq(OrderPO::getUserNo, "")
                        .set(OrderPO::getUserNo, member.getUserNo())
                        .set(OrderPO::getUserMobile, member.getMemberMobile());
                int update = orderMapper.update(null, updateWrapper);
                if (update > 1) {
                    throw new BusinessException("处理数据多余一条，回滚！！！，订单号: " + order.getOrderSn());
                }
                count++;
            }
        }
        return count;
    }

    @Override
    public OrderInfoVOV2 getOrderSnapshot(String orderSn) {
        OrderInfoVOV2 orderInfoVo = new OrderInfoVOV2();

        // 订单信息
        OrderPO orderPo = super.getOne(Wrappers.lambdaQuery(OrderPO.class)
                .eq(OrderPO::getOrderSn, orderSn)
                .eq(OrderPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                .last("limit 1"));
        AssertUtil.notNull(orderPo, "查询订单信息为空，订单号: " + orderSn);
        Map<String, Object> orderVo = JSON.parseObject(JSON.toJSONString(orderPo), new TypeReference<Map<String, Object>>() {
        });
        orderInfoVo.setOrder(orderVo);

        // 订单支付信息
        OrderPayPO orderPayPo = orderPayService.getOne(Wrappers.lambdaQuery(OrderPayPO.class)
                .eq(OrderPayPO::getPaySn, orderPo.getPaySn())
                .eq(OrderPayPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                .last("limit 1"));
        if (Objects.nonNull(orderPayPo)) {
            Map<String, Object> orderPayVo = JSON.parseObject(JSON.toJSONString(orderPayPo), new TypeReference<Map<String, Object>>() {
            });
            orderInfoVo.setOrderPay(orderPayVo);
        }

        // 订单扩展信息
        OrderExtendPO orderExtendPo = orderExtendService.getOne(Wrappers.lambdaQuery(OrderExtendPO.class)
                .eq(OrderExtendPO::getOrderSn, orderSn)
                .eq(OrderExtendPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                .last("limit 1"));
        if (Objects.nonNull(orderExtendPo)) {
            Map<String, Object> orderExtendVo = JSON.parseObject(JSON.toJSONString(orderExtendPo), new TypeReference<Map<String, Object>>() {
            });
            orderInfoVo.setOrderExtend(orderExtendVo);
        }

        // 订单扩展金融信息
        OrderExtendFinancePO orderExtendFinancePo = orderExtendFinanceService.getOne(Wrappers.lambdaQuery(OrderExtendFinancePO.class)
                .eq(OrderExtendFinancePO::getOrderSn, orderSn)
                .eq(OrderExtendFinancePO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                .last("limit 1"));
        if (Objects.nonNull(orderExtendFinancePo)) {
            Map<String, Object> orderExtendFinanceVo = JSON.parseObject(JSON.toJSONString(orderExtendFinancePo), new TypeReference<Map<String, Object>>() {
            });
            orderInfoVo.setOrderExtendFinance(orderExtendFinanceVo);
        }

        // 订单活动信息
        List<OrderPromotionDetailPO> orderPromotionDetailPos = orderPromotionDetailService.list(Wrappers.lambdaQuery(OrderPromotionDetailPO.class)
                .eq(OrderPromotionDetailPO::getOrderSn, orderSn)
                .eq(OrderPromotionDetailPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y));
        if (!CollectionUtils.isEmpty(orderPromotionDetailPos)) {
            List<Map<String, Object>> promotionDetailVos = new ArrayList<>(orderPromotionDetailPos.size());
            for (OrderPromotionDetailPO OrderPromotionDetailPo : orderPromotionDetailPos) {
                Map<String, Object> orderPromotionDetailVo = JSON.parseObject(JSON.toJSONString(OrderPromotionDetailPo), new TypeReference<Map<String, Object>>() {
                });
                promotionDetailVos.add(orderPromotionDetailVo);
            }
            orderInfoVo.setOrderPromotionDetails(promotionDetailVos);
        }

        // 订单商品信息
        List<OrderProductPO> orderProductPos = orderProductService.list(Wrappers.lambdaQuery(OrderProductPO.class)
                .eq(OrderProductPO::getOrderSn, orderSn)
                .eq(OrderProductPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y));
        if (!CollectionUtils.isEmpty(orderProductPos)) {
            List<Map<String, Object>> orderProductVos = new ArrayList<>(orderProductPos.size());
            for (OrderProductPO orderProductPo : orderProductPos) {
                Map<String, Object> orderProductVo = JSON.parseObject(JSON.toJSONString(orderProductPo), new TypeReference<Map<String, Object>>() {
                });
                OrderProductExtendPO orderProductExtendPo = orderProductExtendService.getOne(Wrappers.lambdaQuery(OrderProductExtendPO.class)
                        .eq(OrderProductExtendPO::getOrderProductId, orderProductPo.getOrderProductId())
                        .eq(OrderProductExtendPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                        .last("limit 1"));
                if (Objects.nonNull(orderProductExtendPo)) {
                    Map<String, Object> orderProductExtendVo = JSON.parseObject(JSON.toJSONString(orderProductExtendPo), new TypeReference<Map<String, Object>>() {
                    });
                    orderProductVo.putAll(orderProductExtendVo);
                }
                orderProductVos.add(orderProductVo);
            }
            orderInfoVo.setOrderProducts(orderProductVos);
        }

        return orderInfoVo;
    }

    @Override
    public boolean dealOrderLock(String orderSn, Integer value) {
        OrderPO orderPo = orderModel.getOrderByOrderSnLambda(orderSn);
        LambdaUpdateWrapper<OrderPO> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(OrderPO::getOrderSn, orderPo.getOrderSn())
                .set(OrderPO::getLockState, orderPo.getLockState() + value);
        return super.update(updateWrapper);
    }

    @Override
    public boolean updateOrderLockStates(String orderSn, Integer lockTimes) {
        LambdaUpdateWrapper<OrderPO> orderUpdateWrapper = Wrappers.lambdaUpdate();
        orderUpdateWrapper.eq(OrderPO::getOrderSn, orderSn)
                .eq(OrderPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                .setSql("lock_state =  lock_state + " + lockTimes);
        return this.update(orderUpdateWrapper);
    }

    // region 辅助私有方法

    /**
     * 基于订单状态流转轨迹技术订单是否签收
     */
    private boolean receivedByTrackForJindie(OrderPO orderPO) {
        if (orderPO.getOrderState().equals(OrderStatusEnum.TRADE_SUCCESS.getValue())) {
            return true;
        }
        if (orderPO.getOrderState().equals(OrderStatusEnum.TRADE_CLOSE.getValue())) {
            OrderLogPO orderLogPO = orderLogService.lambdaQuery()
                    .eq(OrderLogPO::getOrderSn, orderPO.getOrderSn())
                    .eq(OrderLogPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                    .select(OrderLogPO::getOrderPreState)
                    .orderByDesc(OrderLogPO::getCreateTime)
                    .last("limit 1")
                    .one();
            if (Objects.isNull(orderLogPO) || Objects.isNull(orderLogPO.getOrderPreState())) {
                return false;
            }
            return OrderStatusEnum.TRADE_SUCCESS.getValue().equals(orderLogPO.getOrderPreState());
        }
        return false;
    }

    private boolean dealExtendAutoReceiveTimeBtn(OrderPO orderPO) {
        if (orderPO.getDelayTimes() >= CommonConst.MAX_EXTEND_RECEIVE_TIMES
                || orderPO.getOrderState() != OrderConst.ORDER_STATE_30) {
            return false;
        }
        Date autoReceiveDay = DateUtil.addDay(orderPO.getDeliverTime(), orderPO.getAutoReceiveDay());
        return DateUtil.getDayBetweenDates(new Date(), autoReceiveDay) <= CommonConst.SHOW_EXTEND_RECEIVE_LIMIT;
    }

    // region end

    @Override
    public PageVO<OrderListVOV2> listStoreWebV2(OrderListQueryRequest orderRequest, PagerInfo pager, Vendor vendor) {

        OrderExample orderExample = OrderQueryBuilder.buildOrderExportDataQueryCondition(orderRequest, Objects.isNull(vendor) ? null : vendor.getStoreId());

        List<OrderListVOV2> vos = orderModel.getOrderListWithJoin(orderExample, pager);

        Map<String, OrderPresellDTO> orderPresellDTOMap = null;
        if (!CollectionUtils.isEmpty(orderRequest.getOrderType())
                && orderRequest.getOrderType().contains(OrderTypeEnum.PRE_SELL_DEPOSIT.getValue())
                && !CollectionUtils.isEmpty(vos)) {
            List<String> bizSnList = vos.stream().map(OrderListVOV2::getOrderSn)
                    .collect(Collectors.toList());

            // 预付订单需要 展示组合支付
            orderPresellDTOMap = orderModel.getPreSellOrderDetailByOrderSnList(bizSnList);
        }

        //是否展示分支维护入口
        List<DictionaryItemVO> mp_version = bmsIntegration.listDictionaryItemsByTypeCode(CommonConst.BRANCH_MANAGE_CONFIG, CommonConst.MALL_SYSTEM_MANAGE_ID);
        DictionaryItemVO dictionaryItemVO = null;
        if (!CollectionUtils.isEmpty(mp_version)) {
            dictionaryItemVO = mp_version.stream().filter(x -> CommonConst.GUANHU_STOREID_MANAGE.equals(x.getItemName())).findFirst().orElse(null);
        }

         List<String> bizSnList = vos.stream().map(OrderListVOV2::getOrderSn)
                .collect(Collectors.toList());
        // 处理B端补录订单企业客户信息字段填充
        Map<String, OrderOfflineExtendPO> orderOfflineExtendPoMap = orderOfflineExtendService.getOrderOfflineExtendMapByOrderSnList(bizSnList);


        for (OrderListVOV2 vo : vos) {

            if (ObjectUtil.isNotNull(dictionaryItemVO) && !StringUtil.isEmpty(dictionaryItemVO.getItemCode()) && dictionaryItemVO.getItemCode().contains(vo.getStoreId()) && StringUtil.isEmpty(vo.getBranchName())
                    && (vo.getOrderState().equals(OrderStatusEnum.WAIT_DELIVER.getValue()) || vo.getOrderState().equals(OrderStatusEnum.PART_DELIVERED.getValue()) || vo.getOrderState().equals(OrderStatusEnum.WAIT_RECEIPT.getValue()))) {
                vo.setIsShow(true);
            }

            //是否有定金
            boolean isHasDeposit = false;
            //拼团订单
            if (vo.getOrderType() == PromotionConst.PROMOTION_TYPE_102) {
                SpellTeamVO spellTeam = getSpellTeamVO(vo.getOrderSn());
                //拼团没有成功就不显示发货按钮
                if (spellTeam.getState() != SpellConst.SPELL_GROUP_STATE_2) {
                    vo.setIsShowDeliverButton(false);
                }
            }

            if (vo.getOrderState() == OrderConst.ORDER_STATE_10) {
                Date autoReceiveTime = TimeUtil.getHourAgoDate(vo.getCreateTime(),
                        systemSettingObtainHelper.getTimeLimitOfAutoCancelOrder());
                vo.setAutoCancelTime(autoReceiveTime);
            }
            vo.setOrderTypeValue(MemberOrderListVO.dealOrderTypeValue(vo.getOrderType(), isHasDeposit));
            //组合支付
            if (orderPresellDTOMap != null) {
                OrderPresellDTO orderPresellDTO = Optional
                        .ofNullable(orderPresellDTOMap.get(vo.getOrderSn()))
                        .orElse(new OrderPresellDTO());
                //组合支付方式
                vo.setComposePayName(OrderPresellBuilder.dealPreSellComposePayName(orderPresellDTO));
            }
            List<OrderProductListVO> orderProductListVOList = vo.getOrderProductListVOList();
            List<OrderProductListVO> orderProductListVOS = orderLocalUtils.sortFullGiftProductVO(vo.getOrderType(), orderProductListVOList);
            if (vo.getOrderType() == OrderTypeEnum.COMBINATION.getValue()) {
                //如果是组合订单，标识出主/子商品行
                LambdaQueryWrapper<BzOrderProductCombinationPO> queryOrder = Wrappers.lambdaQuery();
                queryOrder.eq(BzOrderProductCombinationPO::getOrderSn, vo.getOrderSn())
                        .select(BzOrderProductCombinationPO::getMainProductId, BzOrderProductCombinationPO::getGoodsId);
                BzOrderProductCombinationPO combinationPO = orderProductCombinationService.getOne(queryOrder);
                if (ObjectUtil.isNotEmpty(combinationPO)) {
                    vo.setCombinationGoodsId(combinationPO.getGoodsId());
                    for (OrderProductListVO orderProductListVO : orderProductListVOS) {
                        if (orderProductListVO.getProductId().equals(combinationPO.getMainProductId())) {
                            orderProductListVO.setIsMain(1);
                        }
                    }
                }
            }
            vo.setOrderProductListVOList(orderProductListVOS);

            // B端客户信息填充
            OrderOfflineExtendPO orderOfflineExtendPo = orderOfflineExtendPoMap.getOrDefault(vo.getOrderSn(), null);
            if (Objects.nonNull(orderOfflineExtendPo) && Objects.equals(CustomerTypeEnum.ENTERPRISE_CUSTOMER.getCode(), orderOfflineExtendPo.getCustomerType())) {
                vo.setCustomerId(orderOfflineExtendPo.getCustomerCode());
                vo.setCustomerName(orderOfflineExtendPo.getCustomerName());
            }
        }

        // 处理平台端订单列表是否展示退款按钮
        this.dealAdminOrderListShowRefundBtn(vos);

        return new PageVO<>(vos, pager);
    }

    @Override
    public PageVO<OfflineOrderListVOV2> getOfflineOrderList(OfflineOrderListQueryRequest orderRequest, PagerInfo pager, Vendor vendor) {
        OrderExample orderExample = OrderQueryBuilder.buildOfflineOrderExportDataQueryCondition(orderRequest, Objects.isNull(vendor) ? null : vendor.getStoreId());

        List<OfflineOrderListVOV2> vos = orderModel.getOfflineOrderListWithJoin(orderExample, pager);

        Map<String, OrderPresellDTO> orderPresellDTOMap = null;
        if (!CollectionUtils.isEmpty(orderRequest.getOrderType())
                && orderRequest.getOrderType().contains(OrderTypeEnum.PRE_SELL_DEPOSIT.getValue())
                && !CollectionUtils.isEmpty(vos)) {
            List<String> bizSnList = vos.stream().map(OrderListVOV2::getOrderSn)
                    .collect(Collectors.toList());

            // 预付订单需要 展示组合支付
            orderPresellDTOMap = orderModel.getPreSellOrderDetailByOrderSnList(bizSnList);
        }
        List<String> bizSnList = vos.stream().map(OrderListVOV2::getOrderSn)
                .collect(Collectors.toList());
        // 处理B端补录订单企业客户信息字段填充
        Map<String, OrderOfflineExtendPO> orderOfflineExtendPoMap = orderOfflineExtendService.getOrderOfflineExtendMapByOrderSnList(bizSnList);

        for (OrderListVOV2 vo : vos) {
            //是否有定金
            boolean isHasDeposit = false;
            //拼团订单
            if (vo.getOrderType() == PromotionConst.PROMOTION_TYPE_102) {
                SpellTeamVO spellTeam = getSpellTeamVO(vo.getOrderSn());
                //拼团没有成功就不显示发货按钮
                if (spellTeam.getState() != SpellConst.SPELL_GROUP_STATE_2) {
                    vo.setIsShowDeliverButton(false);
                }
            }

            if (vo.getOrderState() == OrderConst.ORDER_STATE_10) {
                Date autoReceiveTime = TimeUtil.getHourAgoDate(vo.getCreateTime(),
                        systemSettingObtainHelper.getTimeLimitOfAutoCancelOrder());
                vo.setAutoCancelTime(autoReceiveTime);
            }
            vo.setOrderTypeValue(MemberOrderListVO.dealOrderTypeValue(vo.getOrderType(), isHasDeposit));
            //组合支付
            if (orderPresellDTOMap != null) {
                OrderPresellDTO orderPresellDTO = Optional
                        .ofNullable(orderPresellDTOMap.get(vo.getOrderSn()))
                        .orElse(new OrderPresellDTO());
                //组合支付方式
                vo.setComposePayName(OrderPresellBuilder.dealPreSellComposePayName(orderPresellDTO));
            }
            // B端客户信息填充
            OrderOfflineExtendPO orderOfflineExtendPo = orderOfflineExtendPoMap.getOrDefault(vo.getOrderSn(), null);
            if (Objects.nonNull(orderOfflineExtendPo) && Objects.equals(CustomerTypeEnum.ENTERPRISE_CUSTOMER.getCode(), orderOfflineExtendPo.getCustomerType())) {
                vo.setCustomerId(orderOfflineExtendPo.getCustomerCode());
                vo.setCustomerName(orderOfflineExtendPo.getCustomerName());
            }
        }
        // 处理平台端订单列表是否展示退款按钮
        this.dealAdminOfflineOrderListShowRefundBtn(vos);

        return new PageVO<>(vos, pager);
    }


    @Override
    public List<OrderLogPO> getOrderLogs(String orderSn) {
        //获取订单日志信息
        OrderLogExample orderLogExample = new OrderLogExample();
        orderLogExample.setOrderSn(orderSn);
        orderLogExample.setOrderBy("log_time asc");
        return orderLogModel.getOrderLogList(orderLogExample, null);
    }

    @Override
    public List<String> getReceiveCodeStoreIdList() {
        return performanceService.getReceiveCodeStoreList();
    }

    @Override
    @Transactional
    public Void manageBranch(OrderManageBranchPramDTO paramDTO, Vendor vendor) {
        OrderPO orderPO = orderService.lambdaQuery().eq(OrderPO::getOrderSn, paramDTO.getOrderSn()).one();
        BizAssertUtil.notNull(orderPO, "订单不存在");

        // 订单扩展信息
        OrderExtendPO orderExtendPO = orderExtendService.getOne(Wrappers.lambdaQuery(OrderExtendPO.class)
                .eq(OrderExtendPO::getOrderSn, paramDTO.getOrderSn())
                .eq(OrderExtendPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                .last("limit 1"));
        BizAssertUtil.notNull(orderExtendPO, "订单扩展信息不存在");

        //根据工号查询客户经理信息
        UserVo byUserCode = bizConfigIntegration.getDefaultUserByUserCode(paramDTO.getManager());
        BizAssertUtil.notNull(byUserCode, "客户经理信息不存在");
        BizAssertUtil.isTrue(!paramDTO.getManagerName().equals(byUserCode.getUserName()), "客户经理名称错误");

        //更新订单管护维护
        String userCode = byUserCode.getUserCode();
        String userName = byUserCode.getUserName();
        LambdaUpdateWrapper<OrderExtendPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OrderExtendPO::getOrderSn, paramDTO.getOrderSn());
        updateWrapper.set(OrderExtendPO::getManager, userCode);
        updateWrapper.set(OrderExtendPO::getManagerName, userName);
        updateWrapper.set(OrderExtendPO::getBranch, byUserCode.getBranchCode());
        updateWrapper.set(OrderExtendPO::getBranchName, byUserCode.getBranchName());
        updateWrapper.set(OrderExtendPO::getManageType, ManageTypeEnum.FIX.getValue());
        // 区域、片区信息
        if (!org.springframework.util.StringUtils.isEmpty(byUserCode.getBranchCode())) {
            BranchRelationVO branchRelationVO = hrmsIntegration.getBranchRelation(byUserCode.getBranchCode());
            if (branchRelationVO != null) {
                updateWrapper.set(OrderExtendPO::getZoneCode, branchRelationVO.getZoneCode());
                updateWrapper.set(OrderExtendPO::getZoneName, branchRelationVO.getZoneName());
            }
        }
        orderExtendService.update(updateWrapper);

        //重新推送拼车系统
        orderCreateHelper.addOrderChangeEvent(orderPO, OrderEventEnum.MANAGE_BRANCH, new Date());

        //落库订单日志
        orderLogModel.insertOrderLog(OrderConst.LOG_ROLE_VENDOR, vendor.getVendorId(), vendor.getVendorName(),
                paramDTO.getOrderSn(), orderPO.getOrderState(), orderPO.getOrderState(),
                LoanStatusEnum.DEFAULT.getValue(), "手动维护管护信息", OrderCreateChannel.WEB, paramDTO.getManagerName() + "(" + paramDTO.getManager() + ")");

        return null;
    }
}
