package com.cfpamf.ms.mallorder.controller.admin;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.framework.autoconfigure.ZhnxServiceException;
import com.cfpamf.framework.autoconfigure.redis.lock.DistributeLock;
import com.cfpamf.ms.mall.filecenter.domain.dto.FileDTO;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.builder.OrderQueryBuilder;
import com.cfpamf.ms.mallorder.common.enums.OrderStatusEnum;
import com.cfpamf.ms.mallorder.common.util.FileUtil;
import com.cfpamf.ms.mallorder.common.util.OrderExportUtil;
import com.cfpamf.ms.mallorder.common.util.OrderLocalUtils;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.controller.fegin.facade.DbcServiceFeign;
import com.cfpamf.ms.mallorder.dto.DatePermissionDTO;
import com.cfpamf.ms.mallorder.dto.ExtendedAfterSalesDTO;
import com.cfpamf.ms.mallorder.dto.OrderEventNotifyDTO;
import com.cfpamf.ms.mallorder.dto.UserDTO;
import com.cfpamf.ms.mallorder.enums.OrderEventEnum;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.po.OrderLogPO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.req.ExchangeApplyListPcReq;
import com.cfpamf.ms.mallorder.req.OrderListQueryRequest;
import com.cfpamf.ms.mallorder.req.OrderRenewalPriceReq;
import com.cfpamf.ms.mallorder.request.OrderExample;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.service.impl.BzBankPayServiceImpl;
import com.cfpamf.ms.mallorder.vo.*;
import com.cfpamf.ms.mallsystem.vo.Admin;
import com.slodon.bbc.core.constant.WebConst;
import com.slodon.bbc.core.controller.BaseController;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.PageVO;
import com.slodon.bbc.core.response.PagerInfo;
import com.slodon.bbc.core.response.SldResponse;
import com.slodon.bbc.core.util.ExcelExportUtil;
import com.slodon.bbc.core.util.UserUtil;
import com.slodon.bbc.core.util.WebUtil;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Api(tags = "admin-订单管理")
@Slf4j
@RestController
@RequestMapping("admin/orderInfo")
public class AdminOrderInfoController extends BaseController {

    @Resource
    private IOrderInfoService orderInfoService;
    @Resource
    private IOrderService orderService;
    @Resource
    private ITaskQueueService taskQueueService;
    @Resource
    private OrderModel orderModel;
    @Autowired
    private BzBankPayServiceImpl bzBankPayService;
    @Autowired
    private DistributeLock distributeLock;
    @Resource
    private OrderExcelDataExportService orderExcelDataExportService;
    @Autowired
    private IOrderSnapshotService orderSnapshotService;
    @Resource
    private IOrderExchangeService orderExchangeService;
    @Autowired
    private IOrderPerformanceBelongsService orderPerformanceBelongsService;
    @Resource
    private IOrderExchangeExcelDataExportService orderExchangeExcelDataExportService;
    @Autowired
    private OrderLocalUtils orderLocalUtils;
    @Autowired
    private DbcServiceFeign dbcServiceFeign;



    @ApiOperation(value = "订单列表接口")
    @PostMapping("list/V2")
    public JsonResult<PageVO<OrderListVOV2>> getList(HttpServletRequest request,@RequestBody OrderListQueryRequest orderRequest) {

        // 获取商户信息
        Admin admin = UserUtil.getUser(request, Admin.class);
        // 修改为基于业绩归属人分支和业绩归属人的权限控制
        DatePermissionDTO datePermissionDTO = orderLocalUtils.getBranchAndManageListByAdmin(
                orderRequest.getPerformanceBranchCodeList(), 
                orderRequest.getPerformanceBelongerName(), 
                admin);
        
        // 设置业绩归属人分支权限
        orderRequest.setPerformanceBranchCodeList(datePermissionDTO.getBranchCodeList());
        // 设置业绩归属人权限
        orderRequest.setPerformanceBelongerNameList(datePermissionDTO.getManageNameList());
        orderRequest.setPerformanceBelongerName(null);
        
        // 保持原有的分支和客户经理字段为空，确保查询基于业绩归属人
        orderRequest.setBranchCodeIn(null);
        orderRequest.setManagerNameList(null);
        orderRequest.setManagerNameLike(null);
        
        //PagerInfo pager = WebUtil.handlerPagerInfo(request);
        PagerInfo pager = new PagerInfo(Integer.parseInt(Optional.ofNullable(orderRequest.getPageSize()).orElse("10")),
                Integer.parseInt(Optional.ofNullable(orderRequest.getCurrent()).orElse("1")));
        return SldResponse.success(orderInfoService.listStoreWebV2(orderRequest,pager,null));
    }

    @ApiOperation("获取订单签收码商家白名单")
    @GetMapping("getReceiveCodeStoreIdList")
    public JsonResult<List<String>> getReceiveCodeStoreIdList() {
        return SldResponse.success(orderInfoService.getReceiveCodeStoreIdList());
    }

    @ApiOperation(value = "获取订单详情接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderSn", value = "订单号", required = true)
    })
    @GetMapping("detail")
    public JsonResult<OrderVO> getOrderDetail(HttpServletRequest request, @RequestParam("orderSn") String orderSn) {
        Admin admin = UserUtil.getUser(request, Admin.class);
        return SldResponse.success(orderInfoService.adminOrderDetailInfo(orderSn, admin));
    }

    @ApiOperation("获取订单轨迹")
    @GetMapping("getOrderLogs")
    public JsonResult<List<OrderLogPO>> getOrderLogs(HttpServletRequest request, @RequestParam("orderSn") String orderSn) {
        return SldResponse.success(orderInfoService.getOrderLogs(orderSn));
    }

    @ApiOperation(value = "订单改价")
    @PostMapping("renewalPrice")
    public JsonResult renewalPrice(HttpServletRequest request, @RequestBody @NotNull @Valid OrderRenewalPriceReq renewalPriceReq) {

        // 获取商户信息
        Admin admin = UserUtil.getUser(request, Admin.class);
        orderService.renewalPrice(renewalPriceReq, Long.valueOf(admin.getAdminId()), admin.getAdminName());

        return SldResponse.success("订单改价成功");
    }

    @ApiOperation("获取预付订金尾款支付提醒接口")
    @GetMapping("getDepositRemindCount")
    public JsonResult<Object> getDepositRemindInfo(){
        return SldResponse.success(orderInfoService.getDepositRemindCount(null));
    }

    @ApiOperation("订单商品发货详情")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = WebConst.USER_HEADER,
                    value = "商户信息 由网关处理",
                    paramType = "header",
                    dataType = "String"),
            @ApiImplicitParam(name = "orderSn", value = "订单号", required = true)
    })
    @GetMapping("deliveryDetail")
    public JsonResult<List<OrderProductDeliveryVO>> deliveryDetail(HttpServletRequest request, @RequestParam("orderSn") String orderSn) {

        return SldResponse.success(orderService.deliveryDetail(orderSn));
    }

    @Deprecated
    @ApiOperation("订单导出")
    @PostMapping("export/V2")
    public JsonResult<Void> export(HttpServletRequest request, HttpServletResponse response,
                                   @RequestBody OrderListQueryRequest orderListQueryRequest) {
        String lockKey = "admin:orderInfo:export";
        try {
            distributeLock.lockAndProcess(lockKey, 0, 5, TimeUnit.MINUTES,
                    () -> {
                        OrderExample orderExample = OrderQueryBuilder.buildOrderExportDataQueryCondition(orderListQueryRequest, orderListQueryRequest.getStoreId());
                        OrderExportUtil util = new OrderExportUtil(orderModel, orderExample);

                        try {
                            Map<String, SXSSFWorkbook> excelMap = util.doExport();

                            String fileNameNoSuffix = "订单导出-" + ExcelExportUtil.getSystemDate();
                            FileUtil.exportFile(request, response, excelMap, fileNameNoSuffix);

                        } catch (BusinessException e) {
                            throw e;
                        } catch (Exception e) {
                            log.error("订单导出异常", e);
                        }
                        return null;
                    });
        } catch (ZhnxServiceException e) {
            throw new BusinessException("点击过快，稍等一下~");
        }
        return null;
    }


    @ApiOperation(value = "订单导出")
    @PostMapping("export/V3")
    public JsonResult<FileDTO> exportV3(HttpServletRequest request, @RequestBody OrderListQueryRequest orderListQueryRequest)
            throws Exception {
        JsonResult<FileDTO> jsonResult = new JsonResult<>(200, "成功");
        Admin admin = UserUtil.getUser(request, Admin.class);
        DatePermissionDTO datePermissionDTO = orderLocalUtils.getBranchAndManageListByAdmin(orderListQueryRequest.getBranchCodeIn(), orderListQueryRequest.getManagerNameLike(), admin);
        orderListQueryRequest.setBranchCodeIn(datePermissionDTO.getBranchCodeList());
        orderListQueryRequest.setManagerNameList(datePermissionDTO.getManageNameList());
        orderListQueryRequest.setManagerNameLike(null);
        UserDTO userDTO = new UserDTO(admin);
        orderListQueryRequest.setUserDTO(userDTO);
        jsonResult.setData(orderExcelDataExportService.executeAsyncExportExcel(orderListQueryRequest));
        return jsonResult;
    }

    @ApiOperation("更新订单商品数据")
    @GetMapping("updateProductInfo")
    public JsonResult<Boolean> updateProductInfo(@RequestParam @NotNull List<Long> productIds) {
        return SldResponse.success(orderInfoService.updateProductInfo(productIds));
    }

    @ApiOperation("更新订单区域信息")
    @GetMapping("updateOrderBranch")
    public JsonResult<Boolean> updateOrderBranch() {
        return SldResponse.success(orderInfoService.updateOrderBranch());
    }

    @ApiOperation("更新订单片区信息")
    @GetMapping("updateOrderZone")
    public JsonResult<Boolean> updateOrderZone() {
        return SldResponse.success(orderInfoService.updateOrderZone());
    }

    @ApiOperation("补充订单userCode信息")
    @GetMapping("replenishOrderUserCode")
    public JsonResult<Integer> replenishOrderUserCode() {
        return SldResponse.success(orderInfoService.replenishOrderUserCode());
    }

    @ApiOperation("补充订单skuMaterialName信息")
    @GetMapping("replenishSkuMaterialName")
    public JsonResult<Integer> replenishSkuMaterialName() {
        return SldResponse.success(orderInfoService.replenishSkuMaterialName());
    }

    @ApiOperation("刷新订单快照")
    @GetMapping("refreshOrderSnapshot")
    public JsonResult<Boolean> refreshOrderSnapshot() {
        return SldResponse.success(orderSnapshotService.refreshOrderSnapshot());
    }


    @ApiOperation("物资订单失败重试")
    @GetMapping("omsPayMakeUp")
    public JsonResult<Boolean> omsPayMakeUp(@ApiParam(value = "订单号", required = true)
                                                @RequestParam(value = "orderSn") String orderSn) throws ParseException {
        return SldResponse.success(bzBankPayService.omsPayMakeUp(orderSn));
    }

    @ApiOperation("修改taskQueue状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "bizId", value = "业务单号", paramType = "query", required = true),
            @ApiImplicitParam(name = "bizType", value = "业务单号类型", paramType = "query", required = true),
            @ApiImplicitParam(name = "excludeId", value = "过滤的主键id", paramType = "query", required = false),
    })
    @GetMapping("modifyTaskQueue")
    public JsonResult<Integer> modifyTaskQueue(Long bizId, Integer bizType, Long excludeId) {
        return SldResponse.success(taskQueueService.modifyTaskQueue(bizId, bizType, excludeId));
    }

    /**
     * 超期售后
     *
     */
    @ApiOperation("超期售后")
    @PostMapping("extendedAfterSales")
    public JsonResult<Boolean> extendedAfterSales(HttpServletRequest request,@RequestBody ExtendedAfterSalesDTO extendedAfterSalesDTO){
        Admin admin = UserUtil.getUser(request, Admin.class);
        return SldResponse.success(orderService.extendedAfterSales(extendedAfterSalesDTO, OrderConst.LOG_ROLE_ADMIN, admin.getAdminId().longValue(), admin.getAdminName()));
    }

    @ApiOperation("换货申请列表")
    @PostMapping("exchangeOrderList")
    public JsonResult<PageVO<OrderExchangeReturnListVO>> getOrderExchangeReturnList(HttpServletRequest request, @RequestBody ExchangeApplyListPcReq exchangeApplyListReq) {
        Admin admin = UserUtil.getUser(request, Admin.class);

        PagerInfo pager = new PagerInfo(Integer.parseInt(Optional.ofNullable(exchangeApplyListReq.getPageSize()).orElse("10")),
                Integer.parseInt(Optional.ofNullable(exchangeApplyListReq.getCurrent()).orElse("1")));
        UserDTO userDTO = new UserDTO(admin);
        DatePermissionDTO datePermissionDTO = orderLocalUtils.getBranchAndManageListByAdmin(exchangeApplyListReq.getBranchCodeList(), exchangeApplyListReq.getManagerName(), admin);
        exchangeApplyListReq.setBranchCodeList(datePermissionDTO.getBranchCodeList());
        exchangeApplyListReq.setManagerNameList(datePermissionDTO.getManageNameList());
        exchangeApplyListReq.setManagerName(null);
        exchangeApplyListReq.setUserDTO(userDTO);
        List<OrderExchangeReturnListVO> list = orderExchangeService.getExchangeApplyList(exchangeApplyListReq, pager);
        return SldResponse.success(new PageVO<>(list, pager));
    }

    @ApiOperation("换货申请列表导出")
    @PostMapping("exchangeOrderList/export")
    public JsonResult<FileDTO> orderExchangeReturnListExport(HttpServletRequest request,
                                                             @RequestBody ExchangeApplyListPcReq exchangeApplyListReq) throws Exception {
        Admin admin = UserUtil.getUser(request, Admin.class);
        UserDTO userDTO = new UserDTO(admin);
        DatePermissionDTO datePermissionDTO = orderLocalUtils.getBranchAndManageListByAdmin(exchangeApplyListReq.getBranchCodeList(), exchangeApplyListReq.getManagerName(), admin);
        exchangeApplyListReq.setBranchCodeList(datePermissionDTO.getBranchCodeList());
        exchangeApplyListReq.setManagerNameList(datePermissionDTO.getManageNameList());
        exchangeApplyListReq.setManagerName(null);
        exchangeApplyListReq.setUserDTO(userDTO);
        JsonResult<FileDTO> jsonResult = new JsonResult<>(200,"成功");
        exchangeApplyListReq.setUserDTO(userDTO);
        jsonResult.setData(orderExchangeExcelDataExportService.executeAsyncExportExcel(exchangeApplyListReq));
        return jsonResult;
    }

    @ApiOperation("佣金激励费历史数据处理")
    @PostMapping("commissionIncentiveFeeHistoryData")
    public JsonResult<List<String>> commissionIncentiveFeeHistoryData(@RequestParam("orderSnList") List<String> orderSnList,
                                                                      @RequestParam("storeId") String storeId,
                                                                      @RequestParam("orderPattern") Integer orderPattern) {
        LambdaQueryWrapper<OrderPO> orderQueryWrapper = new LambdaQueryWrapper<>();
        orderQueryWrapper.in(OrderPO::getOrderSn,orderSnList);
        orderQueryWrapper.eq(OrderPO::getStoreId,storeId);
        orderQueryWrapper.eq(OrderPO::getOrderPattern,orderPattern);
        List<OrderPO> entityList = orderInfoService.list(orderQueryWrapper);
        if (CollectionUtils.isEmpty(entityList)) {
            log.info("根据查询条件未获取到任何订单记录,orderIdList:{},storeId:{},orderPattern:{}",
                    JSONObject.toJSONString(orderSnList),storeId,orderPattern);
            return SldResponse.success(Collections.emptyList());
        }
        List<OrderEventNotifyDTO> orderEventNotifyDTOList = new ArrayList<>(entityList.size());
        OrderEventNotifyDTO deliveryNotifyDTO;
        for (OrderPO itemPO : entityList) {
            deliveryNotifyDTO = new OrderEventNotifyDTO();
            deliveryNotifyDTO.setPaySn(itemPO.getPaySn());
            deliveryNotifyDTO.setOrderSn(itemPO.getOrderSn());
            deliveryNotifyDTO.setOrderPattern(itemPO.getOrderPattern());
            deliveryNotifyDTO.setOrderType(itemPO.getOrderType());
            deliveryNotifyDTO.setUserNo(itemPO.getUserNo());
            deliveryNotifyDTO.setStoreId(itemPO.getStoreId());
            deliveryNotifyDTO.setIsSelf(itemPO.getStoreIsSelf());
            deliveryNotifyDTO.setEventType(OrderEventEnum.DELIVERY.getCode());
            deliveryNotifyDTO.setEventTypeDesc(OrderEventEnum.DELIVERY.getDesc());
            deliveryNotifyDTO.setEventTime(itemPO.getDeliverTime());
            deliveryNotifyDTO.setChannel(itemPO.getChannel());
            orderEventNotifyDTOList.add(deliveryNotifyDTO);
        }
        Result<List<String>> failResult = dbcServiceFeign.commissionIncentiveFeeHistoryData(orderEventNotifyDTOList);
        if (!failResult.isSuccess()) {
            log.info("调用分销处理历史佣金激励费数据接口失败,storeId:{},原因:{}",storeId,failResult.getErrorMsg());
            return SldResponse.failSpecial(orderSnList);
        }
        log.info("调用分销处理历史佣金激励费数据接口成功,storeId:{},返回处理失败订单列表:{}",storeId,failResult.getData());
        return SldResponse.success(failResult.getData());
    }

    @ApiOperation(value = "业绩归属信息轨迹查询")
    @PostMapping("/listOperatedTrack")
    public JsonResult<List<EventTraceVO>> listCommissionTransferTrack(HttpServletRequest request, @RequestParam("orderSn") String orderSn) {
        Member member = UserUtil.getUser(request, Member.class);
        List<EventTraceVO> resultList = orderPerformanceBelongsService.listAdjustPerformanceHistory(orderSn);
        if (CollectionUtils.isEmpty(resultList)) {
            return SldResponse.success(Collections.emptyList());
        }
        return SldResponse.success(resultList);
    }
}
