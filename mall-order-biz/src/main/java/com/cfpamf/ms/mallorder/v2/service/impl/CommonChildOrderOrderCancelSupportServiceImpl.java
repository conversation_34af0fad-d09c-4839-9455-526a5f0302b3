package com.cfpamf.ms.mallorder.v2.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.v2.common.enums.OrderSnType;
import com.cfpamf.ms.mallorder.v2.domain.bo.OrderCancelDataBO;
import com.cfpamf.ms.mallorder.v2.domain.vo.OperationUserVO;
import com.cfpamf.ms.mallorder.v2.domain.vo.OrderCancelVO;
import com.cfpamf.ms.mallorder.v2.service.OrderCancelSupportService;
import com.cfpamf.ms.mallorder.v2.service.OrderDetailService;

@Service("commonChildOrderOrderCancelSupportService")
public class CommonChildOrderOrderCancelSupportServiceImpl implements OrderCancelSupportService {

    @Autowired
    private OrderDetailService orderDetailService;

    @Override
    public boolean orderCancelFlowOrderState(OrderCancelVO vo, OperationUserVO oprationUser) {
        BizAssertUtil.isTrue(!OrderSnType.isChildOrder(vo.getOrderSnType()), "抱歉，取消订单退款申请，传入订单号类型必须是子订单，请重新操作！");
        OrderPO orderPO = new OrderPO();
        orderPO.setOrderState(OrderConst.ORDER_STATE_0);
        orderPO.setRefuseReason(vo.getReasonDesc());
        orderPO.setRefuseRemark(oprationUser.getOperationRemark());
        UpdateWrapper<OrderPO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("order_sn", vo.getOrderSn());
        updateWrapper.eq("order_state", OrderConst.ORDER_STATE_10);
        // 批量更新订单状态记录
        return orderDetailService.update(orderPO, updateWrapper);
    }

    @Override
    public OrderCancelDataBO getOrderCancelData(OrderCancelVO vo, OperationUserVO oprationUser) {
        List<OrderPO> orderPOList =
            orderDetailService.getOrderListByOrderSn(vo.getOrderSn(), OrderConst.ORDER_STATE_0, null);
        return new OrderCancelDataBO(orderPOList, OrderConst.ORDER_STATE_10, OrderConst.ORDER_STATE_0);
    }

}
