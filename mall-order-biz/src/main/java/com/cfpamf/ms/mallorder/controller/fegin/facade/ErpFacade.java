package com.cfpamf.ms.mallorder.controller.fegin.facade;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cfpamf.framework.autoconfigure.common.result.Result;
import com.cfpamf.ms.mallorder.dto.ProductSkuDetailDTO;
import com.cfpamf.ms.mallorder.dto.ProductSkuQuery;
import com.cfpamf.ms.mallorder.dto.ProductSkuVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * erp facade
 *
 * <AUTHOR>
 * @since 2024-12-04
 */
@FeignClient(name = "erp-service", url = "${erp-service.url:}")
public interface ErpFacade {
    @PostMapping("/feign/product/getProductSkuListV2")
    @ApiOperation(value = "获取货品规格列表(物料编码+规格编码+库存单位 维度)")
    Page<ProductSkuVO> getDepotProductSkuListV2(@RequestBody ProductSkuQuery productSkuQuery);

    @ApiOperation(value = "获取货品规格信息通过skuId")
    @PostMapping("/feign/product/getProductSku/info")
    Result<List<ProductSkuDetailDTO>> getProductSkuInfo(@RequestBody List<String> skuIdList);
}
