package com.cfpamf.ms.mallorder.controller.fegin.facade;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.mallorder.api.feign.OrderAmountRecordFeignClient;
import com.cfpamf.ms.mallorder.enums.OrderAmountTypeEnum;
import com.cfpamf.ms.mallorder.service.IOrderAmountStateRecordService;
import com.cfpamf.ms.mallorder.vo.OrderAmountStateRecordVO;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
public class OrderAmountRecordFeign implements OrderAmountRecordFeignClient {

    @Autowired
    private IOrderAmountStateRecordService orderAmountRecordService;

    @Override
    @PostMapping("/v1/feign/business/orderAmountRecord/listByOrderSn")
    public JsonResult<List<OrderAmountStateRecordVO>> listByOrderSn(@RequestParam("orderSn") String orderSn,
                                                                    @RequestParam(value = "amountType",required = false) OrderAmountTypeEnum amountType) {
        Result<List<OrderAmountStateRecordVO>> result = orderAmountRecordService.listByOrderSn(orderSn,amountType);
        if (!result.isSuccess()) {
            return SldResponse.fail(result.getMessage());
        }
        return SldResponse.success(result.getData());
    }
}
