package com.cfpamf.ms.mallorder.service;

import com.cfpamf.ms.mallorder.dto.OrderSubmitDTO;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * 活动类型，等级1的活动类型为 1xx；等级2的活动类型为 2xx；等级3的活动类型为 3xx;
 * <p>
 * 店铺成本：活动类型 101-单品立减, 102-拼团活动, 103-预售活动, 104-秒杀活动, 105-阶梯团活动
 * 店铺成本：活动类型 201-阶梯满金额减, 202-循环满减, 203-阶梯满折扣, 204-阶梯满件折扣
 * 店铺成本：活动类型 301-跨店满减
 * 积分、优惠券活动类型
 * 401-积分兑换，402-优惠券
 * 平台成本：积分、平台优惠券
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/15 17:30
 */
public class OrderInfoHelper {

    /**
     * 获取店铺优惠金额
     *
     * @param promotionInfos
     * @param promotionType
     * <AUTHOR>
     * @date 2021/7/15 20:01
     * @return java.math.BigDecimal
     */
    public static BigDecimal getStoreDiscount(
            List<OrderSubmitDTO.PromotionInfo> promotionInfos,
            int promotionType) {
        return getDiscountAmount(promotionInfos, promotionType, true);
    }

    /**
     * 获取平台优惠金额
     *
     * @param promotionInfos
     * @param promotionType
     * <AUTHOR>
     * @date 2021/7/15 20:01
     * @return java.math.BigDecimal
     */
    public static BigDecimal getPlatformDiscount(
            List<OrderSubmitDTO.PromotionInfo> promotionInfos,
            int promotionType) {
        return getDiscountAmount(promotionInfos, promotionType, false);
    }

    /**
     * 获取指定类型优惠金额
     *
     * @param promotionInfos
     * @param promotionType
     * @param isStore
     * <AUTHOR>
     * @date 2021/7/15 20:01
     * @return java.math.BigDecimal
     */
    public static BigDecimal getDiscountAmount(
            List<OrderSubmitDTO.PromotionInfo> promotionInfos,
            int promotionType, boolean isStore) {
        BigDecimal discountAmount = BigDecimal.ZERO;
        if (CollectionUtils.isEmpty(promotionInfos)) {
            return discountAmount;
        }
        for (OrderSubmitDTO.PromotionInfo promotionInfo : promotionInfos) {
            if (promotionInfo.getIsStore()
                    && promotionInfo.getPromotionType().equals(promotionType)) {
                discountAmount = discountAmount.add(promotionInfo.getDiscount());
            }
        }
        return discountAmount;
    }

}
