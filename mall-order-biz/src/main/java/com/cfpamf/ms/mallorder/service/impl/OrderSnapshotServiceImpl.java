package com.cfpamf.ms.mallorder.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.cfpamf.framework.autoconfigure.mybatis.BaseRepoServiceImpl;
import com.cfpamf.ms.mallgoods.facade.vo.StockCutVO;
import com.cfpamf.ms.mallorder.common.enums.BizTypeEnum;
import com.cfpamf.ms.mallorder.mapper.OrderSnapshotMapper;
import com.cfpamf.ms.mallorder.po.OrderSnapshotPO;
import com.cfpamf.ms.mallorder.request.req.OrderSnapshotExample;
import com.cfpamf.ms.mallorder.service.IOrderSnapshotService;
import com.google.api.client.util.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 订单快照记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-28
 */
@Service
@Slf4j
public class OrderSnapshotServiceImpl extends BaseRepoServiceImpl<OrderSnapshotMapper, OrderSnapshotPO> implements IOrderSnapshotService {

	@Resource
	private OrderSnapshotMapper orderSnapshotMapper;

	@Override
	public Boolean saveOrderSnapshot(String bizSn, String subBizSn, BizTypeEnum bizType, String remark, Object snapshot, String operator) {
		OrderSnapshotPO orderSnapshotPO = new OrderSnapshotPO();
		orderSnapshotPO.setBizSn(bizSn);
		orderSnapshotPO.setSubBizSn(subBizSn);
		orderSnapshotPO.setBizType(bizType.getValue());
		orderSnapshotPO.setBizTypeDesc(bizType.getDesc());
		orderSnapshotPO.setRemark(remark);
		orderSnapshotPO.setSnapshot(JSON.toJSONString(snapshot));
		orderSnapshotPO.setOrderSnapshot(JSON.parseObject(JSON.toJSONString(snapshot)));
		orderSnapshotPO.setCreateBy(operator);
		orderSnapshotPO.setUpdateBy(operator);
		return this.save(orderSnapshotPO);
	}

	@Override
	public Boolean saveOrderProductStockBatch(String bizSn, String subBizSn, BizTypeEnum bizType, List<StockCutVO> snapshot, String operator) {
		if (CollectionUtils.isEmpty(snapshot)) {
			return Boolean.FALSE;
		}
		List<OrderSnapshotPO> snapshotList = Lists.newArrayList();
		for (StockCutVO stockCutVO : snapshot) {
			stockCutVO.setSellCutCount(stockCutVO.getSellCutCount() == null ?
					BigDecimal.ZERO : stockCutVO.getSellCutCount().stripTrailingZeros());
			stockCutVO.setFinanceCutCount(stockCutVO.getFinanceCutCount() == null ?
					BigDecimal.ZERO :stockCutVO.getFinanceCutCount().stripTrailingZeros());
//			saveOrderSnapshot(bizSn, subBizSn, bizType, "", stockCutVO, operator);
//			log.info("save order product stock snapshot,bizSn:{},stockCutVo:{}", bizSn, JSON.toJSONString(stockCutVO));
			OrderSnapshotPO snapshotPO = buildOrderSnapshot(bizSn, subBizSn, bizType, "", stockCutVO, operator);
			snapshotList.add(snapshotPO);
		}
        saveBatch(snapshotList);
        return Boolean.TRUE;
	}

	@Override
	public Boolean refreshOrderSnapshot() {
		// 处理历史快照信息
		List<OrderSnapshotPO> orderSnapshotPOS = orderSnapshotMapper.list(new OrderSnapshotExample());
		if (CollectionUtils.isEmpty(orderSnapshotPOS)) {
			return Boolean.TRUE;
		}

		List<OrderSnapshotPO> saveOrderSnapshot = new ArrayList<>();
		List<Long> removeIds = new ArrayList<>(orderSnapshotPOS.size());
		for (OrderSnapshotPO orderSnapshotPO : orderSnapshotPOS) {
			String bizType;
			if ("INCREASE_STOCK_BATCH_LIST".equals(orderSnapshotPO.getBizType())) {
				bizType = BizTypeEnum.INCREASE_STOCK.getValue();
			} else if ("REDUCE_STOCK_BATCH_LIST".equals(orderSnapshotPO.getBizType())) {
				bizType = BizTypeEnum.REDUCE_STOCK.getValue();
			} else {
				continue;
			}

			List<StockCutVO> stockCutVOS = JSONArray.parseArray(orderSnapshotPO.getSnapshot(), StockCutVO.class);
			for (StockCutVO stockCutVO : stockCutVOS) {
				stockCutVO.setSellCutCount(stockCutVO.getSellCutCount() == null ?
						BigDecimal.ZERO : stockCutVO.getSellCutCount().stripTrailingZeros());
				stockCutVO.setFinanceCutCount(stockCutVO.getFinanceCutCount() == null ?
						BigDecimal.ZERO : stockCutVO.getFinanceCutCount().stripTrailingZeros());

				OrderSnapshotPO savePo = new OrderSnapshotPO();
				savePo.setBizSn(orderSnapshotPO.getBizSn());
				savePo.setSubBizSn(stockCutVOS.get(0).getProductId().toString());
				savePo.setBizType(bizType);
				savePo.setBizTypeDesc(Objects.requireNonNull(BizTypeEnum.getForValue(bizType)).getDesc());
				savePo.setRemark("");
				savePo.setSnapshot(JSON.toJSONString(stockCutVO));
				savePo.setOrderSnapshot(JSON.parseObject(JSON.toJSONString(stockCutVO)));
				savePo.setCreateBy(orderSnapshotPO.getCreateBy());
				savePo.setUpdateBy(orderSnapshotPO.getCreateBy());
				savePo.setCreateTime(orderSnapshotPO.getCreateTime());
				savePo.setUpdateTime(orderSnapshotPO.getUpdateTime());

				saveOrderSnapshot.add(savePo);
			}

			removeIds.add(orderSnapshotPO.getSnapshotId());
		}

		if (!CollectionUtils.isEmpty(saveOrderSnapshot)) {
			this.saveBatch(saveOrderSnapshot);
		}
		this.removeByIds(removeIds);

		return Boolean.TRUE;
	}

	/**
	 * 组装实体
	 * @return 快照实体
	 */
	@Override
	public List<OrderSnapshotPO> buildOrderProductStockVo(String bizSn, String subBizSn, BizTypeEnum bizType, List<StockCutVO> snapshot, String operator) {
		if (CollectionUtils.isEmpty(snapshot)) {
			return Lists.newArrayList();
		}
		List<OrderSnapshotPO> snapshotList = Lists.newArrayList();
		for (StockCutVO stockCutVO : snapshot) {
			stockCutVO.setSellCutCount(stockCutVO.getSellCutCount() == null ?
					BigDecimal.ZERO : stockCutVO.getSellCutCount().stripTrailingZeros());
			stockCutVO.setFinanceCutCount(stockCutVO.getFinanceCutCount() == null ?
					BigDecimal.ZERO :stockCutVO.getFinanceCutCount().stripTrailingZeros());
//			saveOrderSnapshot(bizSn, subBizSn, bizType, "", stockCutVO, operator);
//			log.info("save order product stock snapshot,bizSn:{},stockCutVo:{}", bizSn, JSON.toJSONString(stockCutVO));
			OrderSnapshotPO snapshotPO = buildOrderSnapshot(bizSn, subBizSn, bizType, "", stockCutVO, operator);
			snapshotList.add(snapshotPO);
		}
		return snapshotList;
	}

	/**
	 * 异步批量保存快照
	 *
	 * @param snapshotPOS 快照
	 */
	@Override
	@Async(value = "threadPoolTaskExecutor")
	public void saveBatchAsync(List<OrderSnapshotPO> snapshotPOS) {
		this.saveBatch(snapshotPOS);
	}

	/**
	 * 组装快照实体
	 *
	 * @param bizSn 业务编号
	 * @param subBizSn 子业务编号
	 * @param bizType 类型
	 * @param remark 备注
	 * @param snapshot 快照
	 * @param operator 操作人
	 * @return 快照实体
	 */
	private OrderSnapshotPO buildOrderSnapshot(String bizSn, String subBizSn, BizTypeEnum bizType, String remark, Object snapshot, String operator) {
		OrderSnapshotPO orderSnapshotPO = new OrderSnapshotPO();
		orderSnapshotPO.setBizSn(bizSn);
		orderSnapshotPO.setSubBizSn(subBizSn);
		orderSnapshotPO.setBizType(bizType.getValue());
		orderSnapshotPO.setBizTypeDesc(bizType.getDesc());
		orderSnapshotPO.setRemark(remark);
		orderSnapshotPO.setSnapshot(JSON.toJSONString(snapshot));
		orderSnapshotPO.setOrderSnapshot(JSON.parseObject(JSON.toJSONString(snapshot)));
		orderSnapshotPO.setCreateBy(operator);
		orderSnapshotPO.setUpdateBy(operator);
		return orderSnapshotPO;
	}
}
