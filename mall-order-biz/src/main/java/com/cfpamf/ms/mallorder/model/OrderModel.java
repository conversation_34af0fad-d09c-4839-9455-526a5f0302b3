package com.cfpamf.ms.mallorder.model;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.framework.autoconfigure.redis.lock.DistributeLock;
import com.cfpamf.framework.autoconfigure.redis.lock.SlodonLock;
import com.cfpamf.mallpayment.facade.api.MallPaymentFacade;
import com.cfpamf.mallpayment.facade.request.loan.LendingCdMallApplyRequest;
import com.cfpamf.ms.bms.facade.vo.DictionaryItemVO;
import com.cfpamf.ms.customer.facade.request.user.QueryUserBaseInfoReq;
import com.cfpamf.ms.customer.facade.vo.CustDetailVo;
import com.cfpamf.ms.customer.facade.vo.user.UserBaseInfoVo;
import com.cfpamf.ms.mall.account.constant.AccountConstans;
import com.cfpamf.ms.mall.account.enums.AccountCardTypeEnum;
import com.cfpamf.ms.mall.account.vo.AccountCard;
import com.cfpamf.ms.mall.settlement.api.SettlementBillFeignClient;
import com.cfpamf.ms.mallgoods.facade.api.*;
import com.cfpamf.ms.mallgoods.facade.enums.EventStockTypeEnum;
import com.cfpamf.ms.mallgoods.facade.enums.GoodsTypeEnum;
import com.cfpamf.ms.mallgoods.facade.enums.InterestWayEnum;
import com.cfpamf.ms.mallgoods.facade.request.ProductAreaDTO;
import com.cfpamf.ms.mallgoods.facade.request.StockCutRequest;
import com.cfpamf.ms.mallgoods.facade.vo.*;
import com.cfpamf.ms.mallmember.api.MemberAddressFeignClient;
import com.cfpamf.ms.mallmember.api.MemberBalanceLogFeignClient;
import com.cfpamf.ms.mallmember.api.MemberFeignClient;
import com.cfpamf.ms.mallmember.api.MemberIntegralLogFeignClient;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallmember.po.MemberAddress;
import com.cfpamf.ms.mallmember.po.MemberIntegralLog;
import com.cfpamf.ms.mallmember.request.MemberExample;
import com.cfpamf.ms.mallmember.vo.MemberBalanceLogVO;
import com.cfpamf.ms.mallorder.builder.OrderOfflineBuilder;
import com.cfpamf.ms.mallorder.common.constant.CartConst;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.constant.OrderPaymentConst;
import com.cfpamf.ms.mallorder.common.constant.OrderProductConst;
import com.cfpamf.ms.mallorder.common.constant.*;
import com.cfpamf.ms.mallorder.common.enums.OrderCreateChannel;
import com.cfpamf.ms.mallorder.common.enums.OrderPatternEnum;
import com.cfpamf.ms.mallorder.common.enums.PayMethodEnum;
import com.cfpamf.ms.mallorder.common.enums.*;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.cfpamf.ms.mallorder.common.util.*;
import com.cfpamf.ms.mallorder.constant.ExchangeOrderConst;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.controller.fegin.facade.CustomerServiceFeign;
import com.cfpamf.ms.mallorder.dto.*;
import com.cfpamf.ms.mallorder.enums.*;
import com.cfpamf.ms.mallorder.integration.cust.CustomerIntegration;
import com.cfpamf.ms.mallorder.integration.erp.ERPIntegration;
import com.cfpamf.ms.mallorder.integration.erp.vo.MallProductQuery;
import com.cfpamf.ms.mallorder.integration.facade.BmsIntegration;
import com.cfpamf.ms.mallorder.integration.facade.BmsUserFacade;
import com.cfpamf.ms.mallorder.integration.facade.OrderQueryFacade;
import com.cfpamf.ms.mallorder.integration.filecenter.ProofSceneEnum;
import com.cfpamf.ms.mallorder.integration.pay.PayIntegration;
import com.cfpamf.ms.mallorder.integration.promotion.MallCouponPkgIntegration;
import com.cfpamf.ms.mallorder.integration.promotion.MsCouponPkgIntegration;
import com.cfpamf.ms.mallorder.integration.settlement.BillOperatinIntegration;
import com.cfpamf.ms.mallorder.integration.shop.ShopIntegration;
import com.cfpamf.ms.mallorder.mapper.*;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.request.*;
import com.cfpamf.ms.mallorder.request.req.StoreExistOrderReq;
import com.cfpamf.ms.mallorder.scheduler.OrderCancelSpellRefundJob;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.service.impl.OrderCreateHelper;
import com.cfpamf.ms.mallorder.service.impl.OrderInfoServiceImpl;
import com.cfpamf.ms.mallorder.v2.builder.OrderPayRecordBuilder;
import com.cfpamf.ms.mallorder.v2.builder.OrderPresellBuilder;
import com.cfpamf.ms.mallorder.v2.config.CommonConfig;
import com.cfpamf.ms.mallorder.v2.domain.dto.LoanInfoDTO;
import com.cfpamf.ms.mallorder.v2.domain.dto.OrderPresellDTO;
import com.cfpamf.ms.mallorder.v2.manager.CouponOrderManager;
import com.cfpamf.ms.mallorder.v2.manager.GoodsStockService;
import com.cfpamf.ms.mallorder.v2.manager.PromotionManager;
import com.cfpamf.ms.mallorder.v2.service.*;
import com.cfpamf.ms.mallorder.validation.OrderCancelValidation;
import com.cfpamf.ms.mallorder.validation.OrderOfflineValidation;
import com.cfpamf.ms.mallorder.validation.OrderReturnValidation;
import com.cfpamf.ms.mallorder.vo.*;
import com.cfpamf.ms.mallpromotion.api.*;
import com.cfpamf.ms.mallpromotion.dto.SubmitPromotionOrderDTO;
import com.cfpamf.ms.mallpromotion.dto.SubmitPromotionOrderResult;
import com.cfpamf.ms.mallpromotion.dto.UseCouponBatchDTO;
import com.cfpamf.ms.mallpromotion.request.*;
import com.cfpamf.ms.mallpromotion.vo.*;
import com.cfpamf.ms.mallshop.api.*;
import com.cfpamf.ms.mallshop.enums.WhiteListEnum;
import com.cfpamf.ms.mallshop.request.StoreCommentExample;
import com.cfpamf.ms.mallshop.resp.StoreComment;
import com.cfpamf.ms.mallshop.resp.StoreContractReceiptInfoVO;
import com.cfpamf.ms.mallshop.resp.Vendor;
import com.cfpamf.ms.mallshop.vo.VendorInfoVo;
import com.cfpamf.ms.mallsystem.api.ExpressFeignClient;
import com.cfpamf.ms.mallsystem.request.ExpressExample;
import com.cfpamf.ms.mallsystem.vo.Express;
import com.cfpamf.smartid.client.utils.sharding.SeqEnum;
import com.cfpamf.smartid.client.utils.sharding.ShardingId;
import com.google.api.client.util.Lists;
import com.google.common.collect.Maps;
import com.slodon.bbc.core.constant.OrdersAfsConst;
import com.slodon.bbc.core.constant.*;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.exception.MallException;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.PagerInfo;
import com.slodon.bbc.core.util.AssertUtil;
import com.slodon.bbc.core.util.StringUtil;
import com.slodon.bbc.core.util.TimeUtil;
import com.slodon.bbc.starter.mq.entity.MessageSendProperty;
import com.slodon.bbc.starter.mq.entity.MessageSendVO;
import io.seata.core.context.RootContext;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.ibatis.annotations.Param;
import org.apache.logging.log4j.util.Strings;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.alibaba.fastjson.serializer.SerializerFeature.MapSortField;
import static com.alibaba.fastjson.serializer.SerializerFeature.SortField;
import static com.slodon.bbc.core.constant.StarterConfigConst.MQ_EXCHANGE_NAME;
import static com.slodon.bbc.core.constant.StarterConfigConst.MQ_QUEUE_NAME_MEMBER_MSG;

@Component
@Slf4j
public class OrderModel {

    @Resource
    private OrderMapper orderMapper;
    @Resource
    private OrderProductMapper orderProductMapper;
    @Resource
    private CartMapper cartMapper;
    @Autowired
    private OrderPresellService orderPresellService;
    @Autowired
    private OrderPayRecordService orderPayRecordService;
    @Resource
    private OrderExtendMapper orderExtendMapper;
    @Resource
    private OrderReturnTrackMapper orderReturnTrackMapper;
    @Resource
    private GoodsFeignClient goodsFeignClient;
    @Resource
    private GoodsExtendFeignClient goodsExtendFeignClient;
    @Resource
    private ProductFeignClient productFeignClient;
    @Resource
    private ProductFinanceGoodsLabelFeignClient productFinanceGoodsLabelFeignClient;
    @Resource
    private MemberFeignClient memberFeignClient;
    @Resource
    private SpellFeignClient spellFeignClient;
    @Autowired
    private ExclusiveFeignClient exclusiveFeignClient;
    @Resource
    private MemberIntegralLogFeignClient memberIntegralLogFeignClient;
    @Resource
    private MemberBalanceLogFeignClient memberBalanceLogFeignClient;
    @Resource
    private MemberAddressFeignClient memberAddressFeignClient;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private CouponMemberFeignClient couponMemberFeignClient;
    @Resource
    private CouponUseLogFeignClient couponUseLogFeignClient;
    @Resource
    private CouponFeignClient couponFeignClient;
    @Resource
    private StoreCommentFeignClient storeCommentFeignClient;
    @Resource
    private GoodsCommentFeignClient goodsCommentFeignClient;
    @Resource
    private PromotionCommonFeignClient promotionCommonFeignClient;
    @Resource
    private SeckillOrderExtendFeignClient seckillOrderExtendFeignClient;
    @Resource
    private SeckillStageProductFeignClient seckillStageProductFeignClient;
    @Resource
    private LadderGroupFeignClient ladderGroupFeignClient;
    @Resource
    private LadderGroupOrderExtendFeignClient ladderGroupOrderExtendFeignClient;
    @Resource
    private PresellOrderExtendFeignClient presellOrderExtendFeignClient;
    @Resource
    private PresellDepositCompensationFeignClient presellDepositCompensationFeignClient;
    @Resource
    private PresellGoodsFeignClient presellGoodsFeignClient;
    @Resource
    private SpellTeamFeignClient spellTeamFeignClient;
    @Resource
    private ExpressFeignClient expressFeignClient;
    @Autowired
    private ValidUtils validUtils;
    @Resource
    private OrderExtendModel orderExtendModel;
    @Resource
    private OrderLogModel orderLogModel;
    @Resource
    private OrderReturnModel orderReturnModel;
    @Resource
    private OrderProductModel orderProductModel;
    @Resource
    private OrderProductExtendModel orderProductExtendModel;
    @Resource
    private OrderPayModel orderPayModel;
    @Resource
    private OrderPromotionDetailModel orderPromotionDetailModel;
    @Resource
    private OrderPromotionSendCouponModel orderPromotionSendCouponModel;
    @Resource
    private OrderPromotionSendProductModel orderPromotionSendProductModel;
    @Autowired
    private PayIntegration payIntegration;
    @Resource
    private RabbitTemplate rabbitTemplate;
    @Resource
    private SlodonLock slodonLock;
    @Autowired
    private ShardingId shardingId;
    @Autowired
    private MallPaymentFacade mallPaymentFacade;
    @Autowired
    private StoreFeignClient storeFeignClient;
    @Autowired
    private CustomerServiceFeign customerServiceFeign;
    @Resource
    private OrderPayMapper orderPayMapper;
    @Autowired
    private ITaskQueueService taskQueueService;
    @Autowired
    private ILoanResultService loanResultService;
    @Autowired
    private ProductStockFeignClient productStockFeignClient;
    @Autowired
    private OrderLocalUtils orderLocalUtils;
    @Autowired
    private OrderSubmitAttributesUtils orderSubmitAttributesUtils;
    @Autowired
    private IOrderPlacingService orderPlacingService;
    @Autowired
    private IOrderService orderService;
    @Autowired
    private OrderInfoServiceImpl orderInfoService;
    @Autowired
    private IOrderProductService orderProductService;
    @Autowired
    private IBzOrderProductCombinationService orderProductCombinationService;
    @Resource
    private OrderAfterServiceModel orderAfterServiceModel;
    @Resource
    private IOrderReturnService orderReturnService;
    @Resource
    private DistributeLock distributeLock;
    @Autowired
    private OrderCreateHelper orderCreateHelper;
    @Autowired
    private IOrderExtendFinanceService financeService;
    @Autowired
    private IOrderPayService iOrderPayService;
    @Autowired
    private IOrderExtendService iOrderExtendService;
    @Resource
    private SettlementBillFeignClient settlementBillFeignClient;
    @Autowired
    private BillOperatinIntegration billOperatinIntegration;
    @Autowired
    private OrderQueryFacade orderQueryFacade;

    @Autowired
    private CouponOrderManager couponOrderManager;

    @Autowired
    private GoodsStockService goodsStockService;

    @Autowired
    private PromotionManager promotionManager;

    @Autowired
    private MallCouponPkgIntegration mallCouponPkgIntegration;
    @Autowired
    private MsCouponPkgIntegration msCouponPkgIntegration;
    @Autowired
    private IOrderProductCouponService orderProductCouponService;

    @Autowired
    private OrderModel orderModel;

    @Autowired
    private PayService payService;
    @Autowired
    private OrderRefundRecordService orderRefundRecordService;

    @Resource
    private OrderPresellMapper orderPresellMapper;

    @Resource
    private VendorFeignClient vendorFeignClient;

    @Autowired
    private ShopIntegration shopIntegration;
    @Resource
    private OrderReturnValidation orderReturnValidation;
    @Autowired
    private IOrderExtendFinanceService iOrderExtendFinanceService;

    @Autowired
    private OrderCancelValidation orderCancelValidation;

    @Autowired
    private IOrderTradeProofService orderTradeProofService;

    @Resource
    private OrderExchangeDetailMapper orderExchangeDetailMapper;

    @Autowired
    private IOrderExtendService orderExtendService;

    @Autowired
    private BmsUserFacade bmsUserFacade;

    @Autowired
    private IOrderGroupBuyingRecordService orderGroupBuyingRecordService;
    @Autowired
    private IOrderGroupBuyingBindService orderGroupBuyingBindService;

    @Resource
    private OrderOfflineService orderOfflineService;

    @Autowired
    private CustomerIntegration customerIntegration;

    @Autowired
    private PurchaserFeign purchaserFeign;

    @Autowired
    private ITransactionLogService transactionLogService;

    @Autowired
    private IOrderSnapshotService orderSnapshotService;

    @Autowired
    private StoreIsolateWhiteListFeignClient storeIsolateWhiteListFeignClient;

    @Autowired
    private OrderProductAgricExtendModel orderProductAgricExtendModel;

    @Autowired
    private OrderProductErpExtendService orderProductErpExtendService;

    @Autowired
    private IBzOrderProductInstallService orderProductInstallService;

    @Autowired
    private IOrderOfflineExtendService orderOfflineExtendService;

    @Autowired
    private BmsIntegration bmsIntegration;

    @Value("#{'${self-lift-order.auto-delivery-store-list}'.split(',')}")
    private List<Long> autoDeliveryStoreIdList;

    /**
     * 新增订单
     *
     * @param orderPO
     * @return
     */
    public Integer saveOrder(OrderPO orderPO) {
        int count = orderMapper.insert(orderPO);
        if (count == 0) {
            throw new MallException("添加订单失败，请重试");
        }
        return count;
    }

    /**
     * 根据orderId删除订单
     *
     * @param orderId orderId
     * @return
     */
    public Integer deleteOrder(Integer orderId) {
        if (ValidUtils.isEmpty(orderId)) {
            throw new MallException("请选择要删除的数据");
        }
        int count = orderMapper.deleteByPrimaryKey(orderId);
        if (count == 0) {
            log.error("根据orderId：" + orderId + "删除订单失败");
            throw new MallException("删除订单失败,请重试");
        }
        return count;
    }

    /**
     * 根据orderSn更新订单
     *
     * @param orderPO
     * @return
     */
    public Integer updateOrder(OrderPO orderPO) {
        if (StringUtils.isEmpty(orderPO.getOrderSn())) {
            throw new MallException("请选择要修改的数据");
        }
        OrderExample example = new OrderExample();
        example.setOrderSn(orderPO.getOrderSn());
        // 用呗支付时会被置为空
        // order.setOrderSn(null);
        int count = orderMapper.updateByExampleSelective(orderPO, example);
        if (count == 0) {
            log.error("根据orderId：" + orderPO.getOrderId() + "更新订单失败");
            throw new MallException("更新订单失败,请重试");
        }
        return count;
    }

    /**
     * 根据条件更新订单
     *
     * @param orderPO
     * @return
     */
    public Integer updateOrderByExample(OrderPO orderPO, OrderExample example) {
        return orderMapper.updateByExampleSelective(orderPO, example);
    }

    /**
     * 根据订单号获取订单详情
     *
     * @param orderSn 订单号
     * @return
     */
    public OrderPO getOrderByOrderSn(String orderSn) {
        if (StringUtils.isEmpty(orderSn)) {
            return null;
        }
        OrderExample orderExample = new OrderExample();
        orderExample.setOrderSn(orderSn);
        List<OrderPO> orderPOList = orderMapper.listByExample(orderExample);
        BizAssertUtil.notEmpty(orderPOList, "订单不存在");
        return orderPOList.get(0);
    }

    public List<OrderPO> getOrderByOrderSnList(List<String> orderSnList) {
        if (CollectionUtils.isEmpty(orderSnList)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<OrderPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(OrderPO::getOrderSn, orderSnList);
        queryWrapper.eq(OrderPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
        List<OrderPO> orderPOList = orderMapper.selectList(queryWrapper);
        BizAssertUtil.notEmpty(orderPOList, "订单不存在");
        return orderPOList;
    }

    /**
     * 根据orderSn获取详情
     *
     * @param orderSn
     * @return
     */
    public OrderExtendPO getOrderExtendByOrderSn(String orderSn) {
        OrderExtendExample extendExample = new OrderExtendExample();
        extendExample.setOrderSn(orderSn);
        return orderExtendMapper.listByExample(extendExample).get(0);
    }

    /**
     * 根据条件获取订单列表
     *
     * @param example 查询条件信息
     * @param pager   分页信息
     * @return
     */
    public List<OrderPO> getOrderList(OrderExample example, PagerInfo pager) {
        if (!StringUtils.isEmpty(example.getOrderSnIn())) {
            example.setOrderSnList(Arrays.asList(example.getOrderSnIn().split(",")));
        }
        List<OrderPO> orderPOList;
        if (pager != null) {
            pager.setRowsCount(orderMapper.countByExample(example));
            orderPOList = orderMapper.listPageByExample(example, pager.getStart(), pager.getPageSize());

        } else {
            orderPOList = orderMapper.listByExample(example);
        }
        return orderPOList;
    }

    /**
     * 根据订单状态统计客户订单数量(站长)
     *
     * @return
     */
    public OrderCountVo countStatusByStationMaster(String stationMaster) {
        OrderCountVo orderCountVo = new OrderCountVo();

        List<Map<String, Object>> status = orderMapper.countStatusByStationMaster(stationMaster);
        if (CollectionUtils.isEmpty(status)) {
            return orderCountVo;
        }

        int toPaidOrderStatus5 = 0;     // 状态为5的订单数量
        int toPaidOrder = 0;
        int toDeliverOrder = 0;
        int toReceivedOrder = 0;
        int toFinishedOrder = 0;
        int toClosedOrder = 0;
        for (Map<String, Object> countByStatus : status) {
            Integer orderState = new Integer(countByStatus.get("orderState").toString());
            Integer num = new Integer(countByStatus.get("num").toString());
            switch (orderState) {
                case OrderConst.ORDER_STATE_5:
                    toPaidOrderStatus5 = num;
                    break;
                case OrderConst.ORDER_STATE_10:
                    toPaidOrder = num;
                    break;
                case OrderConst.ORDER_STATE_20:
                    toDeliverOrder = num;
                    break;
                case OrderConst.ORDER_STATE_25:
                case OrderConst.ORDER_STATE_30:
                    toReceivedOrder += num;
                    break;
                case OrderConst.ORDER_STATE_40:
                    toFinishedOrder = num;
                    break;
                case OrderConst.ORDER_STATE_50:
                    toClosedOrder = num;
                    break;
                default:
                    break;
            }
        }

        orderCountVo.setToPaidOrder(toPaidOrder + toPaidOrderStatus5);
        orderCountVo.setToDeliverOrder(toDeliverOrder);
        orderCountVo.setToReceivedOrder(toReceivedOrder);

        orderCountVo.setFinishedNum(toFinishedOrder);
        orderCountVo.setClosedNum(toClosedOrder);

        return orderCountVo;
    }

    /**
     * 根据订单状态统计客户订单数量
     *
     * @return
     */
    public OrderCountVo countByStatus(Integer memberId) {
        OrderCountVo orderCountVo = new OrderCountVo();

        List<Map<String, Object>> status = orderMapper.countByStatus(memberId);
        if (CollectionUtils.isEmpty(status)) {
            return orderCountVo;
        }

        int toPaidOrderStatus5 = 0;     // 状态为5的订单数量
        int toPaidOrder = 0;
        int toDeliverOrder = 0;
        int toReceivedOrder = 0;
        for (Map<String, Object> countByStatus : status) {
            Integer orderState = new Integer(countByStatus.get("orderState").toString());
            Integer num = new Integer(countByStatus.get("num").toString());
            switch (orderState) {
                case OrderConst.ORDER_STATE_5:
                    toPaidOrderStatus5 = num;
                    break;
                case OrderConst.ORDER_STATE_10:
                    toPaidOrder = num;
                    break;
                case OrderConst.ORDER_STATE_20:
                    toDeliverOrder = num;
                    break;
                case OrderConst.ORDER_STATE_25:
                case OrderConst.ORDER_STATE_30:
                    toReceivedOrder += num;
                    break;
                default:
                    break;
            }
        }

        // 待评价数
        LambdaQueryWrapper<OrderPO> orderQuery = new LambdaQueryWrapper<>();
        orderQuery.eq(OrderPO::getMemberId, memberId)
                .eq(OrderPO::getOrderState, OrderConst.ORDER_STATE_40)
                .ne(OrderPO::getEvaluateState, 3)
                .select(OrderPO::getOrderSn, OrderPO::getUserNo);
        List<OrderPO> orderPOS = orderService.list(orderQuery);
        if (!CollectionUtils.isEmpty(orderPOS)) {
            orderCountVo.setToEvaluateOrder(orderPOS.size());
        }

        // 查询自研电商
        OrderCountVo countVo = orderQueryFacade.countByStatus(status.get(0).get("userNo").toString());
        orderCountVo.setToPaidOrder(toPaidOrder + toPaidOrderStatus5 + countVo.getToPaidOrder());
        orderCountVo.setToDeliverOrder(toDeliverOrder + countVo.getToDeliverOrder());
        orderCountVo.setToReceivedOrder(toReceivedOrder + countVo.getToReceivedOrder());

        // 售后数
        /*LambdaQueryWrapper<OrderReturnPO> returnQuery = new LambdaQueryWrapper<>();
        returnQuery.eq(OrderReturnPO::getMemberId, memberId)
                .in(OrderReturnPO::getState, OrderReturnStatus.duringRefundStatus())
                .select(OrderReturnPO::getReturnId);
        List<OrderReturnPO> orderReturnPOS = orderReturnService.list(returnQuery);
        orderCountVo.setAfterSaleNum(orderReturnPOS.size());*/
        AfsOrderCountVO afsOrderCountVO = orderReturnModel.countDuringRefundStatus(memberId);
        orderCountVo.setAfterSaleNum(afsOrderCountVO.getTotalNum());

        return orderCountVo;
    }

    /**
     * 根据条件获取订单列表
     *
     * @param example 查询条件信息
     * @param pager   分页信息
     * @return
     */
    public List<OrderListVOV2> getOrderListWithJoin(OrderExample example, PagerInfo pager) {
        List<OrderListVOV2> orderPOList;
        if (pager != null) {
            pager.setRowsCount(orderMapper.countByExampleWithJoin(example));
            orderPOList = orderMapper.listPageByExampleWithJoin(example, pager.getStart(), pager.getPageSize());
        } else {
            orderPOList = orderMapper.listByExampleWithJoin(example);
        }
        return orderPOList;
    }


    /**
     * 根据条件获取线下补录订单列表
     *
     * @param example 查询条件信息
     * @param pager   分页信息
     * @return
     */
    public List<OfflineOrderListVOV2> getOfflineOrderListWithJoin(OrderExample example, PagerInfo pager) {
        if (Objects.isNull(pager)) {
            throw new BusinessException("线下补录订单订单列表请分页查询！");
        }
        pager.setRowsCount(orderMapper.countOfflineOrderByExampleWithJoin(example));
        return orderMapper.listOfflineOrderPageByExampleWithJoin(example, pager.getStart(), pager.getPageSize());
    }

    /**
     * 根据条件获取订单字段列表
     *
     * @param fields  查询字段，逗号分隔
     * @param example 查询条件信息
     * @param pager   分页信息
     * @return
     */
    public List<OrderPO> getOrderFieldList(String fields, OrderExample example, PagerInfo pager) {
        List<OrderPO> orderPOList;
        if (pager != null) {
            pager.setRowsCount(orderMapper.countGroupFieldsByExample(fields, example));
            orderPOList = orderMapper.listFieldsPageByExample(fields, example, pager.getStart(), pager.getPageSize());
        } else {
            orderPOList = orderMapper.listFieldsByExample(fields, example);
        }
        return orderPOList;
    }

    public Map<String, String> orderBillList(List<String> bizSnList) {
        try {
            if (CollectionUtils.isEmpty(bizSnList)) {
                return null;
            }
            return settlementBillFeignClient.orderBillList(bizSnList);
        } catch (Exception e) {
            log.warn("settlement 查询订单结算单异常，error:{}，bizSnList:{}", e.getMessage(), bizSnList);
            return null;
        }

    }

    /**
     * 获取条件获取订单数量
     *
     * @param example 查询条件信息
     * @return
     */
    public Integer getOrderCount(OrderExample example) {
        return orderMapper.countByExample(example);
    }

    /**
     * 根据订单号获取订单，WithOp查询订单关联的货品列表
     *
     * @param orderSn
     * @return
     */
    public OrderPO getOrdersWithOpByOrderSn(String orderSn) {
        OrderPO orderPO = getOrderByOrderSn(orderSn);
        OrderProductExample productExample = new OrderProductExample();
        productExample.setOrderSn(orderSn);
        List<OrderProductPO> orderProductPOList = orderProductMapper.listByExample(productExample);
        AssertUtil.notEmpty(orderProductPOList, "订单有误");
        orderPO.setOrderProductPOList(orderProductPOList);
        return orderPO;
    }

    /**
     * 下单，使用全局事务
     *
     * @param orderSubmitDTO 计算优惠后的提交订单信息
     * @param member         会员信息
     * @param consumerDTO    前端提交订单参数
     * @return
     */
    public List<String> submit(OrderSubmitDTO orderSubmitDTO, Member member, OrderSubmitMqConsumerDTO consumerDTO) {
        if (CommonConfig.enableTransactionLog()) {
            // 走本地事务
            return orderService.submitOrderWithLocalTransaction(orderSubmitDTO, member, consumerDTO);
        } else {
            // 走seata全局事务
            return orderService.submitOrderWithGlobleTransaction(orderSubmitDTO, member, consumerDTO);
        }
    }


    /**
     * 下单，使用全局事务
     *
     * @param orderSubmitDTO 计算优惠后的提交订单信息
     * @param member         会员信息
     * @param consumerDTO    前端提交订单参数
     * @return
     */


    /**
     * 提交订单,操作数据表：
     * -bz_order 存订单
     * -bz_order_extend 保存订单扩展信息
     * -bz_order_promotion_detail 保存订单活动优惠明细表
     * -bz_order_promotion_send_coupon 保存订单活动赠送优惠券表
     * -bz_order_promotion_send_product 保存订单活动赠送货品表
     * -bz_order_log 记录订单日志
     * -member 减积分
     * member_integral_log 记录积分使用日志
     * -promotion_coupon_member 扣优惠券；
     * -promotion_coupon_use_log 记录优惠券使用日志；-
     * promotion_coupon 统计优惠券使用量
     * <p>
     * -bz_order_product 保存订单货品
     * -bz_order_product_extend 保存订单货品扩展
     * -product、goods 减库存
     * <p>
     * -bz_order_product 保存完订单货品之后，保存活动赠品
     * <p>
     * -bz_order_pay 支付信息 -bz_cart 删购物车
     *
     * @param orderSubmitDTO 计算优惠后的提交订单信息
     * @param member         会员信息
     * @param consumerDTO    前端提交订单参数
     * @param orderSnList    订单编号列表，用于发送取消事务mq，不能直接返回给前端，因为包含了父单号
     */
    public List<String> submitOrder(OrderSubmitDTO orderSubmitDTO, Member member, OrderSubmitMqConsumerDTO consumerDTO, List<String> orderSnList) {
        String verifyCodeTmp = orderSubmitDTO.getVerifyCode();
        log.info("orderSubmitDTO验证码:{}", verifyCodeTmp);
        String verifyCode = consumerDTO.getParamDTO().getVerifyCode();
        log.info("consumerDTO:{}", verifyCode);
        List<OrderPO> orderPOList = new ArrayList<>();// 插入的订单列表
        // 记录此次提交获取的所有锁，在事务提交后统一释放
        Set<String> lockSet = new HashSet<>();
        List<String> result = Lists.newArrayList();
        try {
            //获取乡信订单提交参数
            OrderAgricInfoDto orderAgricInfoDto = null;
            if (Objects.nonNull(consumerDTO.getOrderParamDTO()) && Objects.nonNull(consumerDTO.getOrderParamDTO().getOrderAgricInfoDto())) {
                orderAgricInfoDto = consumerDTO.getOrderParamDTO().getOrderAgricInfoDto();
            }
            // 父订单号，一批订单共有一个
            String pOrderSn = String.valueOf(shardingId.next(SeqEnum.ONO, member.getMemberId().toString()));
            orderSnList.add(pOrderSn);
            // 支付单号，一批订单共有一个
            String paySn = consumerDTO.getPaySn();
            OrderSubmitParamDTO orderSubmitParamDTO = consumerDTO.getParamDTO();
            // 总支付金额，含运费
            BigDecimal payAmount = new BigDecimal("0.00");
            // 店铺信息获取
//            List<Long> storeIdList = orderSubmitDTO.getOrderInfoList().stream().map(OrderSubmitDTO.OrderInfo::getStoreId).collect(Collectors.toList());
            // FIX 商户批量接口存在问题，第一期下单优化暂时不调用批量接口
//            List<StoreContractReceiptInfoVO> storeReceiptInfoBatch = orderSubmitAttributesUtils.getStoreReceiptInfoBatch(storeIdList);
//            Map<Long, StoreContractReceiptInfoVO> receiptInfoVOMap = storeReceiptInfoBatch.stream().collect(Collectors.toMap(StoreContractReceiptInfoVO::getStoreId, v -> v, (v1, v2) -> v1));
            // 用户身份获取
            Integer userIdentity = orderCreateHelper.getUserIdentity(member);
            // orderExtendFinanceList
            List<OrderExtendFinancePO> extendFinanceList = Lists.newArrayList();
            List<OrderPromotionDetailPO> orderPromotionDetailsTotalList = Lists.newArrayList();
            // orderPromotionPoList
            List<OrderPromotionSendCouponPO> couponPromotionPoTotalList = Lists.newArrayList();
            VendorInfoVo vendorInfoVo = null;
            List<VendorInfoVo> vendorInfoVoList = vendorFeignClient.getVendorInfoByPhone(Collections.singletonList(member.getMemberMobile()));
            if (!CollectionUtils.isEmpty(vendorInfoVoList)) {
                log.info("===========purchase order result ======={}", JSONObject.toJSONString(vendorInfoVoList));
                vendorInfoVo = vendorInfoVoList.get(0);
            }
            List<OrderExtendPO> extendPOList = Lists.newArrayList();
            // 每次循环为一个店铺的订单
            Integer autoReceiveDays = null;
            List<DictionaryItemVO> dictionary = bmsIntegration.listDictionaryItemsByTypeCode(CommonConst.AUTO_RECEIVE_DAYS, CommonConst.MALL_SYSTEM_MANAGE_ID);
            if (!org.springframework.util.CollectionUtils.isEmpty(dictionary)) {
                DictionaryItemVO dictionaryItemVO = dictionary.get(0);
                autoReceiveDays = Integer.valueOf(dictionaryItemVO.getItemCode());
            }
            for (OrderSubmitDTO.OrderInfo orderInfo : orderSubmitDTO.getOrderInfoList()) {
                //是否组合商品
                Integer isCombination = Objects.equals(orderInfo.getOrderType(), OrderTypeEnum.COMBINATION.getValue()) ? 1 : 0;

                Set<String> performanceModeList = new HashSet<>();
                log.info("============= orderSubmitDTO:orderInfo:{}", JSON.toJSONString(orderInfo));
                // 同一订单，金融规则必须相同，名字可能不同，名字使用逗号分隔拼接
                List<String> ruleTags = orderInfo.getOrderProductInfoList().stream()
                        .map(OrderSubmitDTO.OrderInfo.OrderProductInfo::getRuleTag).filter(x -> !StringUtils.isEmpty(x))
                        .distinct().collect(Collectors.toList());
                String ruleTag = "";
                if (ruleTags.size() > 1) {
                    ruleTag = StringUtils.join(ruleTags, ",");
                } else if (ruleTags.size() == 1) {
                    ruleTag = ruleTags.get(0);
                }
                // 不同商品地区编码可能不同，获取最后一个商品的地区编码
                String areaCode = orderInfo.getOrderProductInfoList()
                        .get(orderInfo.getOrderProductInfoList().size() - 1).getAreaCode();
                // 同一订单，金融规则编号必须相同，获取第一个金融规则编号
                String ruleCode = orderInfo.getOrderProductInfoList().get(0).getFinanceRuleCode();

                JsonResult<StoreContractReceiptInfoVO> storeContractResult = ExternalApiUtil.callJsonResultApi(
                        () -> storeFeignClient.getStoreContractReciptInfoV2(orderInfo.getStoreId()),
                        orderInfo.getStoreId(),
                        "/v1/feign/seller/store/getStoreContractReciptInfoV2",
                        "查询店铺合同相关信息");
                // 店铺信息
                StoreContractReceiptInfoVO storeContract = storeContractResult.getData();

                if (!storeContract.getTradable()) {
                    log.info("当前店铺无下单权限,storeId:{}", orderInfo.getStoreId());
                    throw new BusinessException("【当前店铺无下单权限，请联系客服处理】");
                }

                // 订单号
                long sn = shardingId.next(SeqEnum.ONO, member.getMemberId().toString());
                String orderSn = String.valueOf(sn);
                orderSnList.add(orderSn);
                result.add(orderSn);
                log.info("============= NEW ORDER :{}", orderSn);

                // -bz_order 存订单
                OrderPO orderPO =
                        this.buildOrderPO(pOrderSn, orderSn, paySn, orderInfo, member, ruleTag, storeContract,
                                consumerDTO.getOrderPlaceUserRole(), consumerDTO.getOrderPlaceUserDTO(), consumerDTO, userIdentity);
                log.info("============= submitOrder orderPO:{}", JSON.toJSONString(orderPO));

                payAmount = payAmount.add(orderPO.getOrderAmount());

                // -bz_order_extend 订单扩展信息
//                OrderExtendPO orderExtendPO = orderExtendModel.insertOrderExtend(orderPO, orderInfo, orderSubmitParamDTO,
//                        member, storeContract, consumerDTO, vendorInfoVo);
                OrderExtendPO orderExtendPO = orderExtendModel.buildOrderExtend(orderPO, orderInfo, orderSubmitParamDTO,
                        member, storeContract, consumerDTO, vendorInfoVo);
                extendPOList.add(orderExtendPO);

                // 订单下单校验
                orderCreateHelper.employeeTradeCheck(orderInfo, orderPO, orderExtendPO);

                // 从商品上获取是否为自营店铺
                OrderSubmitDTO.OrderInfo.OrderProductInfo orderProductInfo = orderInfo.getOrderProductInfoList().get(0);

                // product信息
                ProductPriceVO productPriceByProductId = orderSubmitAttributesUtils.getProductPriceByProductId(
                        orderProductInfo.getProductId(), orderProductInfo.getAreaCode(), orderProductInfo.getFinanceRuleCode());
                Integer isSelf = productPriceByProductId.getGoods().getIsSelf();
                orderPO.setStoreIsSelf(isSelf);
                //保存金融规则信息
//                iOrderExtendFinanceService.insertOrderExtendFinance(orderInfo.getOrderProductInfoList().get(0).getProductId(),
//                        orderPO.getAreaCode(), orderPO.getFinanceRuleCode(), orderPO.getOrderSn());
                OrderExtendFinancePO extendFinancePO = iOrderExtendFinanceService.getOrderExtendFinance(orderInfo.getOrderProductInfoList().get(0).getProductId(),
                        orderPO.getAreaCode(), orderPO.getFinanceRuleCode(), orderPO, autoReceiveDays);
                if (Objects.nonNull(extendFinancePO)) {
                    extendFinanceList.add(extendFinancePO);
                }

                // -bz_order_promotion_detail 保存订单活动优惠明细表
//                orderPromotionDetailModel.insertOrderPromotionDetails(orderInfo, orderSn);
                List<OrderPromotionDetailPO> orderPromotionDetailsList = orderPromotionDetailModel.getOrderPromotionDetails(orderInfo, orderSn);
                if (!CollectionUtils.isEmpty(orderPromotionDetailsList)) {
                    orderPromotionDetailsTotalList.addAll(orderPromotionDetailsList);
                }

                // -bz_order_promotion_send_coupon 保存订单活动赠送优惠券表
//                orderPromotionSendCouponModel.insertOrderPromotionSendCoupons(orderInfo, orderSn);
                List<OrderPromotionSendCouponPO> couponPoList = orderPromotionSendCouponModel.buildOrderPromotionSendCoupons(orderInfo, orderSn);
                if (!CollectionUtils.isEmpty(couponPoList)) {
                    couponPromotionPoTotalList.addAll(couponPoList);
                }

                // -bz_order_promotion_send_product 保存订单活动赠送货品表
                orderPromotionSendProductModel.insertOrderPromotionSendProducts(orderInfo, orderSn);

                // -member 减积分;member_integral_log 记录积分使用日志
                this.reduceMemberIntegral(member.getMemberId(), orderInfo.getIntegral(), orderSn);

                int productSize = orderInfo.getOrderProductInfoList().size();
                boolean isSpecial = !StringUtil.isNullOrZero(orderInfo.getPromotionType())
                        && !orderInfo.getOrderType().equals(OrderTypeEnum.REBATE_GIFT.getValue())     // 返利订单不视为特殊订单，避免调用促销
                        && promotionCommonFeignClient.specialOrder(orderInfo.getPromotionType());

                // BigDecimal fixedServiceFee = BigDecimal.ZERO; // 商品行服务费累计值
                BigDecimal fixedThirdpartnarFee = BigDecimal.ZERO; // 商品行代运营费累计值


                if (Objects.nonNull(orderInfo.getPromotionType()) && orderInfo
                        .getPromotionType() == com.cfpamf.ms.mallpromotion.constant.PromotionConst.PROMOTION_TYPE_107) {
                    // 校验优惠和尾款
                    BizAssertUtil.isTrue(orderInfo.getTotalDiscount().compareTo(orderInfo.getBalance()) >= 0,
                            SentenceConst.PRESELL_ORDER_DISCOUNT_LIMIT);
                    // 保存当订单尾款信息
                    this.buildPresellPoAndPayRecordPo(member, orderInfo, paySn, orderSn);
                }

                //保存普通商品行
                if (CommonConfig.enableTransactionLog()) {
                    saveOrderProductV2(member, lockSet, consumerDTO, orderInfo, isCombination, performanceModeList, orderSn, orderPO, orderExtendPO, productSize, isSpecial, fixedThirdpartnarFee, storeContract, orderAgricInfoDto);
                } else {
                    saveOrderProduct(member, lockSet, consumerDTO, orderInfo, isCombination, performanceModeList, orderSn, orderPO, orderExtendPO, productSize, isSpecial, fixedThirdpartnarFee, storeContract, orderAgricInfoDto);
                }

                // 活动特殊处理
                // if (!StringUtil.isNullOrZero(orderInfo.getPromotionType())
                // && promotionCommonFeignClient.specialOrder(orderInfo.getPromotionType())) {
                // promotionCommonFeignClient.submitPromotionOrder(orderInfo.getPromotionType(), orderSn);
                // }

                // -bz_order_product 保存完订单货品之后，保存活动赠品
                // 此方法未使用
                orderProductModel.insertSendOrderProduct(orderSn, orderInfo, member.getMemberId(), areaCode,
                        ruleCode, orderExtendPO.getBranch(), orderExtendPO.getReceiveBranchCode(), orderExtendPO.getWarehouseCode(), orderPO, orderExtendPO);

                //保存满赠订单商品行
                this.saveFullGiftSendOrderProduct(orderInfo, member.getMemberId(), areaCode,
                        ruleCode, orderExtendPO, orderPO);


                orderPO.setStoreCompanyName(storeContract.getCompanyName());
                orderPO.setSettlementPrice(orderPO.getGoodsAmount().add(orderPO.getExpressFee())
                        .subtract(orderPO.getStoreVoucherAmount()).subtract(orderPO.getStoreActivityAmount())
                        // .subtract(orderPO.getOrderCommission())
                        // .subtract(orderPO.getBusinessCommission())
                        .subtract(orderPO.getServiceFee()).subtract(orderPO.getThirdpartnarFee()));

                log.info("============= orderSubmitDTO:orderPO2:{}", JSON.toJSONString(orderPO));

                // orderPlacingService.save(orderPO);

                if (OrderPlaceUserRole.isValetOrder(orderPO.getOrderPlaceUserRoleCode())) {
                    orderLogModel.insertOrderLog(OrderPlaceUserRole.valueOf(orderPO.getOrderPlaceUserRoleCode()).getOperationRoleCode(), consumerDTO.getOrderPlaceUserDTO().getOrderPlaceUserId(),
                            consumerDTO.getOrderPlaceUserDTO().getOrderPlaceUserName(), orderSn, -1, orderPO.getOrderState(),
                            LoanStatusEnum.DEAL_APPLY.getValue(), "草稿创建", OrderCreateChannel.getEnumByValue(orderSubmitParamDTO.getChannel()));
                } else {
                    // -bz_order_log 记录订单日志
                    orderLogModel.insertOrderLog(OrderConst.LOG_ROLE_MEMBER, Long.valueOf(member.getMemberId()),
                            member.getMemberName(), orderSn, -1, orderPO.getOrderState(),
                            LoanStatusEnum.DEAL_APPLY.getValue(), "用户提交订单", OrderCreateChannel.getEnumByValue(orderSubmitParamDTO.getChannel()));
                }

                // 处理订单的平台服务费信息
                this.dealOrderServiceFeeRate(orderPO, storeContract);

                // 现款现货下单校验，区分B端和C端
                if (SettleModeEnum.BORROW.getCode().equals(orderInfo.getSettleMode()) && !storeContract.getFundsBorrowable()) {
                    if (this.isToBOrder(orderPO) && !storeContract.getFundsBorrowableTob()) {
                        throw new BusinessException("【当前店铺无法售卖现款现货商品，请联系客服处理】");
                    }
                    if (!this.isToBOrder(orderPO) && !storeContract.getFundsBorrowable()) {
                        throw new BusinessException("【当前店铺无法售卖现款现货商品，请联系客服处理】");
                    }
                }

                // 赠品订单下单
                if (OrderTypeEnum.GROUP_BUYING_GIFT == OrderTypeEnum.getValue(orderInfo.getOrderType())) {
                    this.groupOrderProcess(consumerDTO.getGroupOrderProductSubmitDTO(), orderExtendPO, orderPO);
                }
                // FIX  改为批量保存
//                orderService.save(orderPO);

                orderPOList.add(orderPO);

                // -promotion_coupon_member 扣优惠券；promotion_coupon_use_log
                // 记录优惠券使用日志；promotion_coupon 统计优惠券使用量(店铺优惠券)
                if (CollectionUtil.isNotEmpty(orderInfo.getVoucherCodeList())) {
                    this.deductMemberCoupon(member, orderInfo.getVoucherCodeList(), orderSn, orderInfo.getStoreId());
                }

                // 供应商校验
                if (!StringUtils.isEmpty(orderPO.getDealerCode())) {
                    BizAssertUtil.isTrue(!shopIntegration.isFactoryBind(orderPO.getDealerCode(), orderPO.getStoreId().toString()), "该经销商已失效或不属于该厂商，不可使用～");
                }

                /**
                 * 记录订单变更通知事件,并在事务提交后执行
                 */
                orderCreateHelper.addOrderChangeEventWithOrderPoAndExtend(orderPO, orderExtendPO, OrderEventEnum.CREATE, orderPO.getCreateTime());
            }

            if (!CollectionUtils.isEmpty(orderPOList)) {
                orderService.saveBatch(orderPOList);
            }

            if (!CollectionUtils.isEmpty(extendPOList)) {
                orderExtendService.saveBatch(extendPOList);
            }

            if (!CollectionUtils.isEmpty(extendFinanceList)) {
                iOrderExtendFinanceService.saveBatch(extendFinanceList);
            }
            if (!CollectionUtils.isEmpty(orderPromotionDetailsTotalList)) {
                orderPromotionDetailModel.saveBatch(orderPromotionDetailsTotalList);
            }
            if (!CollectionUtils.isEmpty(couponPromotionPoTotalList)) {
                orderPromotionSendCouponModel.saveBatch(couponPromotionPoTotalList);
            }

            boolean judgeNewOrder = this.unifiedNewOrder(orderPOList);
            if (!judgeNewOrder) {
                //旧订单则需要更新，将本批支付单，更新为旧订单
                orderService.lambdaUpdate().eq(OrderPO::getPaySn, paySn).set(OrderPO::getNewOrder, false).update();
            }
            // -promotion_coupon_member 扣优惠券；promotion_coupon_use_log
            // 记录优惠券使用日志；promotion_coupon 统计优惠券使用量(平台优惠券)
            if (CollectionUtil.isNotEmpty(orderSubmitDTO.getCouponCodeList())) {
                this.deductMemberCoupon(member, orderSubmitDTO.getCouponCodeList(), pOrderSn, 0L);
            }

            // -bz_order_pay 支付信息
            OrderPayPO orderPayPO = orderPayModel.buildOrderPayPO(consumerDTO, orderSubmitDTO, pOrderSn, paySn,
                    payAmount, member.getMemberId());

            iOrderPayService.save(orderPayPO);

            // 银行转账汇款支付的订单，购物车删除逻辑移到submitBankTransfer方法中
            if (orderSubmitParamDTO.getIsCart()) {
                // -bz_cart 删购物车
                CartExample cartExample = new CartExample();
                cartExample.setMemberId(member.getMemberId());
                cartExample.setIsChecked(CartConst.IS_CHECKED_YES);
                cartMapper.deleteByExample(cartExample);
            }
            BigDecimal totalAmount =
                    orderPOList.stream().map(OrderPO::getOrderAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            // 排除不需要处理的0元订单
            long count = orderPOList.stream().filter(po -> OrderTypeEnum.GROUP_BUYING_GIFT.getValue().equals(po.getOrderType())).count();
            // 是否为返利满赠
            boolean isRebate = orderPOList.stream().allMatch(po -> OrderTypeEnum.REBATE_GIFT.getValue().equals(po.getOrderType()));
            // 总单金额为0 && 非赠品订单
            if (totalAmount.compareTo(BigDecimal.ZERO) == 0 && count == 0) {
                if (!isRebate) {
                    // 0元订单验证码验证
                    validUtils.checkVerifyCode(verifyCode, member, 1);
                }

                // 订单金额为0时，直接支付成功
                orderPOList.forEach(order -> {
                    //0元代客下单 更新 客户状态为已确认
                    if (OrderSourceEnum.CLIENT.getValue().equals(order.getOrderSource())) {
                        orderService.setCustomerConfirmStatusForZeroOrder(order.getOrderSn());
                    }
                    // 订单已支付
                    orderPayModel.orderPaySuccess(order, null, order.getPaymentCode(), order.getPaymentName(), null,
                            null);
                });
            }
            // 执行完成，更新事务记录
        } catch (Exception e) {
            log.warn("下单接口异常，orderSubmitDTO：{}", JSON.toJSONString(orderSubmitDTO), e);
            for (OrderPO orderPO : orderPOList) {
                /**
                 * 记录订单变更通知事件,并在事务提交后执行
                 */
                orderCreateHelper.addOrderChangeEventWithNewTransaction(orderPO, OrderEventEnum.FAIL,
                        orderPO.getCreateTime());
            }
            throw e;
        } finally {
            //释放此次获取的所有锁
            lockSet.forEach(lockName -> {
                slodonLock.unlock(lockName);
            });
        }
        return result;
    }

    /**
     * 拼单满赠下单处理
     *
     * @param submitDTO
     * @param orderExtendPO
     * @param orderPO
     */
    public void groupOrderProcess(GroupOrderProductSubmitDTO submitDTO, OrderExtendPO orderExtendPO, OrderPO orderPO) {
        // bz_order_group_buying_record 拼单满赠订单
        OrderGroupBuyingRecordPO groupBuyingRecordPO = orderGroupBuyingRecordService
                .buildOrderGroupBuyingRecord(submitDTO, orderExtendPO, orderPO);
        orderGroupBuyingRecordService.save(groupBuyingRecordPO);

        // 更新订单商品拼单标识
        orderProductService.dealOrderProductGroupBuyingTag(submitDTO.getGroupOrderProductIdList());

        // bz_order_group_buying_bind 拼单满赠关联表
        orderGroupBuyingBindService.saveOrderGroupBuyingBind(
                submitDTO, groupBuyingRecordPO.getGroupBuyingCode(), groupBuyingRecordPO.getGiftOrderSn());
    }

    /**
     * 判断订单是否是ToB订单
     *
     * @param orderPO 订单信息
     * @return true/false
     */
    public boolean isToBOrder(OrderPO orderPO) {
        List<OrderProductPO> orderProductPOs = orderProductService.listByOrderSn(orderPO.getOrderSn());
        Set<Integer> goodsTypeSet = orderProductPOs.stream().map(OrderProductPO::getGoodsType)
                .collect(Collectors.toSet());
        if (goodsTypeSet.size() != NumberUtils.INTEGER_ONE) {
            throw new BusinessException(String.format("订单：%s C端订单和B端订单不能同时存在，数据异常请确认~", orderPO.getOrderSn()));
        }
        Integer goodsType = null;
        for (Integer tmp : goodsTypeSet) {
            goodsType = tmp;
        }
        return GoodsTypeEnum.PURCHASE.getCode().equals(goodsType);
    }

    /**
     * 设置订单的平台服务信息，按B端和C端分别设置
     *
     * @param orderPO   订单信息
     * @param storeInfo 店铺信息
     */
    private void dealOrderServiceFeeRate(OrderPO orderPO, StoreContractReceiptInfoVO storeInfo) {
        if (this.isToBOrder(orderPO)) {
            orderPO.setServiceFeeRate(storeInfo.getPlatformServiceRateDecimalTob());
        } else {
            orderPO.setServiceFeeRate(storeInfo.getPlatformServiceRateDecimal());
        }
        orderPO.setServiceFee(BigDecimal.ZERO);
    }

    /**
     * 下单库存扣减，确认履约模式
     */
    private void dealStockCutPerformance(List<StockCutVO> stockCutVOList, OrderProductPO orderProductPO) {
        if (CollectionUtils.isEmpty(stockCutVOList)) {
            return;
        }
        if (stockCutVOList.get(0).getStockDeductionsModel() == null) {
            return;
        }
        if (goodsStockService.isErpStockDeductionsModel(stockCutVOList.get(0).getStockDeductionsModel())) {
            orderProductPO.setPerformanceChannel(OrderPerformanceChannelEnum.PERFORMANCE_CHANNEL_ERP.getValue());
            if (StringUtils.isEmpty(orderProductPO.getPerformanceService())) {
                orderProductPO.setPerformanceService(OrderPerformanceModeEnum.PERFORMANCE_MODE_COMMON.getValue().toString());
            } else {
                String[] performanceServiceArr = orderProductPO.getPerformanceService().split(",");
                if (!Arrays.asList(performanceServiceArr).contains(OrderPerformanceModeEnum.PERFORMANCE_MODE_COMMON.getValue().toString())) {
                    orderProductPO.setPerformanceService(orderProductPO.getPerformanceService() + "," + OrderPerformanceModeEnum.PERFORMANCE_MODE_COMMON.getValue());
                }
            }
        }
    }

    private boolean judgeNewOrder(List<OrderPO> orderPOList) {
        //判断微信总开关，未打开则设置成false直接返回
        String openNewOrder = stringRedisTemplate.opsForValue().get("openNewOrder");
        if (StringUtils.isEmpty(openNewOrder)) {
            return false;
        }
        //判断是否开启指定店铺使用，本次子单不存在指定店铺中，则设置成false返回
        String wxWhite = stringRedisTemplate.opsForValue().get("wx_white");
        if (StringUtils.isNotBlank(wxWhite)) {
            List<String> whiteList = Arrays.asList(wxWhite.split(","));
            for (OrderPO po : orderPOList) {
                if (!whiteList.contains(po.getStoreId().toString())) {
                    return false;
                }
            }
        }
        //微信同一个支付单下，存在部分旧子订单则全置为旧订单
        long count = orderPOList.stream().map(OrderPO::getNewOrder).distinct().count();
        return count <= 1;
    }

    /**
     * 统一支付单中一批订单的标识
     *
     * @param orderPOList
     * @return
     */
    private boolean unifiedNewOrder(List<OrderPO> orderPOList) {
        //微信同一个支付单下，存在部分旧子订单则全置为旧订单
        long count = orderPOList.stream().map(OrderPO::getNewOrder).distinct().count();
        return count <= 1;
    }

    private void lockCoupons(Member member, String orderSn, Integer buyNum, List<ProductActivityGoodsBindVO> couponList) {

        Map<Integer, List<ProductActivityGoodsBindVO>> couponListMap = couponList.stream().collect(Collectors.groupingBy(ProductActivityGoodsBindVO::getActivityChannel));
        // 优惠券渠道:1->电商优惠券,2->运管优惠券
        List<ProductActivityGoodsBindVO> mallCoupon = couponListMap.get(1);
        List<ProductActivityGoodsBindVO> msCoupon = couponListMap.get(2);

        // 锁定电商优惠券
        if (!CollectionUtils.isEmpty(mallCoupon)) {
            CouponLockSummary mallCouponLock = new CouponLockSummary();
            mallCouponLock.setOrderSn(orderSn);
            mallCouponLock.setUserNo(member.getUserNo());
            mallCouponLock.setCouponPkgVos(new ArrayList<>());
            for (ProductActivityGoodsBindVO coupon : mallCoupon) {
                CouponPkgVo pkgVo = new CouponPkgVo();
                pkgVo.setCouponId(coupon.getActivityId());
                pkgVo.setReceiveNum(buyNum);
                mallCouponLock.getCouponPkgVos().add(pkgVo);
            }
            if (CommonConfig.enableTransactionLog()) {
                mallCouponPkgIntegration.lockCouponsV2(mallCouponLock);
            } else {
                mallCouponPkgIntegration.lockCoupons(mallCouponLock);
            }
        }

        // 锁定乡助优惠券
        if (!CollectionUtils.isEmpty(msCoupon)) {
            com.cfpamf.ms.promotion.facade.vo.discount.CouponLockSummary msCouponLock = new com.cfpamf.ms.promotion.facade.vo.discount.CouponLockSummary();
            msCouponLock.setOrderSn(orderSn);
            msCouponLock.setUserNo(member.getUserNo());
            msCouponLock.setCouponPkgVos(new ArrayList<>());
            for (ProductActivityGoodsBindVO coupon : msCoupon) {
                com.cfpamf.ms.promotion.facade.vo.discount.CouponPkgVo pkgVo = new com.cfpamf.ms.promotion.facade.vo.discount.CouponPkgVo();
                pkgVo.setCouponId(coupon.getActivityId());
                pkgVo.setReceiveNum(buyNum);
                msCouponLock.getCouponPkgVos().add(pkgVo);
            }
            msCouponPkgIntegration.lockCoupons(msCouponLock);
        }

    }

    /**
     * 构建预售信息和对应支付流水信息
     *
     * @param member    用户信息
     * @param orderInfo 订单信息
     * @param paySn     支付单号
     * @param orderSn   订单号
     */
    private void buildPresellPoAndPayRecordPo(Member member, OrderSubmitDTO.OrderInfo orderInfo, String paySn,
                                              String orderSn) {
        String depositPayNo = String.valueOf(shardingId.next(SeqEnum.PONO, member.getMemberId().toString()));
        // 定金信息
        OrderPresellPO depositPresellPo = OrderPresellBuilder.buildOrderPresellPO(orderSn, paySn, depositPayNo,
                PresellCapitalTypeEnum.DEPOSIT.getValue(), orderInfo.getDeposit(), BigDecimal.ZERO,
                orderInfo.getDeposit(), orderInfo.getDepositDeadTime(), member.getMemberName());

        // 定金支付流水信息
        OrderPayRecordPO orderPayDepositRecordPo = OrderPayRecordBuilder.buildOrderPayRecordPO(depositPayNo, paySn,
                orderInfo.getDeposit(), 1, member.getMemberName());

        // 尾款信息
        String balancePayNo = String.valueOf(shardingId.next(SeqEnum.PONO, member.getMemberId().toString()));
        OrderPresellPO balancePresellPo = OrderPresellBuilder.buildOrderPresellPO(orderSn, paySn, balancePayNo,
                PresellCapitalTypeEnum.BALANCE.getValue(), orderInfo.getBalance(), orderInfo.getTotalDiscount(),
                orderInfo.getBalance().subtract(orderInfo.getTotalDiscount()).add(orderInfo.getExpressFee()),
                orderInfo.getBalanceDeadTime(), member.getMemberName());

        // 尾款支付流水信息
        OrderPayRecordPO orderPayBalanceRecordPo = OrderPayRecordBuilder.buildOrderPayRecordPO(balancePayNo, paySn,
                orderInfo.getBalance().subtract(orderInfo.getTotalDiscount()).add(orderInfo.getExpressFee()), 2,
                member.getMemberName());

        // 批量保存预售信息
        orderPresellService.saveBatch(Arrays.asList(depositPresellPo, balancePresellPo));
        // 批量保存流水记录信息
        orderPayRecordService.saveBatch(Arrays.asList(orderPayDepositRecordPo, orderPayBalanceRecordPo));

    }

    /**
     * @param finance
     * @param orderPO
     * @return void
     * @description :保存贷款类型
     */
    public void saveOrderExtendFinance(ProductPriceVO finance, OrderPO orderPO, Integer autoReceiveDays) {
        String orderSn = orderPO.getOrderSn();
        OrderExtendFinancePO financePODb = financeService.lambdaQuery()
                .eq(OrderExtendFinancePO::getOrderSn, orderSn)
                .last("limit 1")
                .one();
        if (Objects.nonNull(financePODb)) {
            return;
        }

        ProductFinanceRuleLabel productFinanceRuleLabel = finance.getProductFinanceRuleLabel();
        if (Objects.isNull(productFinanceRuleLabel)) {
            return;
        }
        OrderExtendFinancePO financePO = new OrderExtendFinancePO();
        if (productFinanceRuleLabel.getInterestWay().equals(InterestWayEnum.PLAN_LOAN_DATE.getCode())
                && productFinanceRuleLabel.getPlanLoanDate() == null) {
            throw new MallException("下单异常，请联系管理员处理！", "保存贷款类型时计划放款日为空：" + orderSn,
                    ErrorCodeEnum.C.RESULT_INVALID.getCode());
        }
        financePO.setPlanLoanDate(productFinanceRuleLabel.getPlanLoanDate());
        financePO.setOrderSn(orderSn);
        financePO.setDeliverMethod(productFinanceRuleLabel.getDeliverMethod());
        financePO.setAutoDeliverTime(productFinanceRuleLabel.getAutoDeliverTime());
        financePO.setAutoReceiveDays(productFinanceRuleLabel.getAutoReceiveDays());
        if (PayMethodEnum.isLoanPay(orderPO.getPaymentCode())) {
            log.info("贷款类支付，设置自动收货天数,orderSn:{}", orderPO.getOrderSn());
            if (!Objects.isNull(autoReceiveDays)) {
                log.info("贷款类支付，设置自动收货天数,days:{}", autoReceiveDays);
                financePO.setAutoReceiveDays(autoReceiveDays);
            } else {
                log.error("贷款类支付，设置自动收货天数失败，bms未配置字典");
            }
        }
        financePO.setInterestWay(productFinanceRuleLabel.getInterestWay());
        financePO.setCouponBatch(productFinanceRuleLabel.getCouponBatch());
        financePO.setInterestStartType(productFinanceRuleLabel.getInterestStartType());
        financePO.setInterestStartDays(productFinanceRuleLabel.getInterestStartDays());
        financePO.setEmployeeInterestStartDate(productFinanceRuleLabel.getEmployeeInterestStartDate());
        financePO.setFinanceRuleCode(productFinanceRuleLabel.getFinanceRuleCode());
        financePO.setPlanDiscountType(productFinanceRuleLabel.getPlanDiscountType());
        financePO.setPlanDiscountStoreRate(productFinanceRuleLabel.getPlanDiscountStoreRate());
        financePO.setPlanInterestStartDays(productFinanceRuleLabel.getPlanInterestStartDays());
        financePO.setPlanDiscountUpperLimit(productFinanceRuleLabel.getPlanDiscountUpperLimit());

        financeService.save(financePO);
    }

    /**
     * 提交尾款订单
     *
     * @param orderSubmitDTO
     * @param orderSubmitParamDTO
     * @param member
     * @param orderPODb
     * @return
     */
    @GlobalTransactional
    public boolean submitBalanceOrder(OrderSubmitDTO orderSubmitDTO, OrderSubmitParamDTO orderSubmitParamDTO,
                                      Member member, OrderPO orderPODb, String userAreaCode) {
        // 收货地址
        MemberAddress memberAddress =
                memberAddressFeignClient.getMemberAddressByAddressId(orderSubmitParamDTO.getAddressId());
        // 总支付金额，含运费
        BigDecimal payAmount = new BigDecimal("0.00");

        for (OrderSubmitDTO.OrderInfo orderInfo : orderSubmitDTO.getOrderInfoList()) {// 每次循环为一个店铺的订单
            // -bz_order 存订单
            OrderPO orderPO = this.updateBalanceOrder(orderPODb, orderInfo, memberAddress);
            String orderSn = orderPODb.getOrderSn();
            payAmount = payAmount.add(orderPO.getOrderAmount());

            // -bz_order_extend 订单扩展信息
            orderExtendModel.updateOrderExtend(orderSn, orderInfo, memberAddress, orderSubmitParamDTO);

            // -bz_order_promotion_detail 保存订单活动优惠明细表
            orderPromotionDetailModel.insertOrderPromotionDetails(orderInfo, orderSn);

            // -bz_order_promotion_send_coupon 保存订单活动赠送优惠券表
            orderPromotionSendCouponModel.insertOrderPromotionSendCoupons(orderInfo, orderSn);

            // -bz_order_promotion_send_product 保存订单活动赠送货品表
            orderPromotionSendProductModel.insertOrderPromotionSendProducts(orderInfo, orderSn);

            // -member 减积分;member_integral_log 记录积分使用日志
            this.reduceMemberIntegral(member.getMemberId(), orderInfo.getIntegral(), orderSn);

            // -promotion_coupon_member 扣优惠券；promotion_coupon_use_log
            // 记录优惠券使用日志；promotion_coupon 统计优惠券使用量(店铺优惠券)
            this.deductMemberCoupon(member, orderInfo.getVoucherCodeList(), orderSn, orderInfo.getStoreId());

            orderInfo.getOrderProductInfoList().forEach(orderProductInfo -> {// 每次循环为一个订单货品

                // 修改订单货品信息
                Long orderProductId = orderProductModel.updateBalanceOrderProduct(orderSn, orderProductInfo);

                if (!CollectionUtils.isEmpty(orderProductInfo.getPromotionInfoList())) { // 有活动优惠
                    orderProductInfo.getPromotionInfoList().forEach(promotionInfo -> {
                        // -bz_order_product_extend 保存订单货品扩展
                        orderProductExtendModel.insertOrderProductExtend(orderSn, orderProductId, promotionInfo);
                    });
                }
            });

            OrderExtendPO orderExtendPO = orderExtendModel.getOrderExtendByOrderSn(orderSn);
            // -bz_order_product 保存完订单货品之后，保存活动赠品
            orderProductModel.insertSendOrderProduct(orderSn, orderInfo, member.getMemberId(), userAreaCode,
                    orderPODb.getFinanceRuleCode(), orderExtendPO.getBranch(), orderExtendPO.getReceiveBranchCode(), orderExtendPO.getWarehouseCode(), orderPODb, orderExtendPO);
        }

        // -promotion_coupon_member 扣优惠券；promotion_coupon_use_log
        // 记录优惠券使用日志；promotion_coupon 统计优惠券使用量(平台优惠券)
        this.deductMemberCoupon(member, orderSubmitDTO.getCouponCodeList(), orderPODb.getOrderSn(), 0L);

        return true;
    }

    /**
     * 提交订单-保存订单
     *
     * @param pOrderSn      父订单号
     * @param paySn         支付单号
     * @param orderInfo     订单信息
     * @param member        会员信息
     * @param storeContract
     * @return 订单号
     */
    public OrderPO buildOrderPO(String pOrderSn, String orderSn, String paySn, OrderSubmitDTO.OrderInfo orderInfo,
                                Member member, String ruleTag, StoreContractReceiptInfoVO storeContract, OrderPlaceUserRole orderPlaceUserRole,
                                OrderPlaceUserDTO orderPlaceUserDTO, OrderSubmitMqConsumerDTO consumerDTO, Integer userIdentity) {
        String areaCode =
                orderInfo.getOrderProductInfoList().get(orderInfo.getOrderProductInfoList().size() - 1).getAreaCode();
        String ruleCode = orderInfo.getOrderProductInfoList().get(0).getFinanceRuleCode();

        log.info("============= orderSubmitDTO:orderInfo2:{}", JSON.toJSONString(orderInfo));

        OrderPO orderPO = new OrderPO();
        orderPO.setOrderSn(orderSn + "");
        orderPO.setPaySn(paySn);
        orderPO.setParentSn(pOrderSn);
        orderPO.setStoreId(orderInfo.getStoreId());
        orderPO.setStoreName(orderInfo.getStoreName());
        orderPO.setUserNo(member.getUserNo());
        orderPO.setUserMobile(member.getMemberMobile());
        orderPO.setUserIdentity(userIdentity);
        orderPO.setMemberId(member.getMemberId());
        orderPO.setMemberName(member.getMemberName());
        orderPO.setCreateTime(new Date());
        if (orderInfo.getOrderType() == com.cfpamf.ms.mallpromotion.constant.PromotionConst.PROMOTION_TYPE_107) {
            orderPO.setOrderState(OrderConst.ORDER_STATE_5);
        } else {
            orderPO.setOrderState(OrderConst.ORDER_STATE_10);
        }
        orderPO.setOrderAmountTotal(orderInfo.getOrderAmountTotal());
        orderPO.setExpressFeeTotal(orderInfo.getExpressFeeTotal());
        orderPO.setGoodsAmount(orderInfo.getGoodsAmount());
        orderPO.setExpressFee(orderInfo.getExpressFee());
        orderPO.setOrderAmount(orderInfo.getTotalAmount().add(orderInfo.getExpressFee()));
        orderPO.setXzCardExpressFeeAmount(orderInfo.getXzCardExpressFeeAmount());
        orderPO.setStoreVoucherAmount(orderInfo.getStoreVoucherAmount());
        orderPO.setStoreActivityAmount(orderInfo.getStoreActivityAmount());
        orderPO.setPlatformVoucherAmount(orderInfo.getPlatformVoucherAmount());
        orderPO.setPlatformActivityAmount(orderInfo.getPlatformActivityAmount());
        orderPO.setActivityDiscountAmount(orderInfo.getTotalDiscount());
        orderPO.setXzCardAmount(orderInfo.getXzCardAmount());
        orderPO.setOrderPattern(orderInfo.getOrderPattern());
        orderPO.setPromotionId(orderInfo.getPromotionId() == null ? "" : orderInfo.getPromotionId().toString());
        orderPO.setChannel(consumerDTO.getParamDTO().getChannel());
        orderPO.setChannelOrderSn(consumerDTO.getChannelOrderSn());
        orderPO.setAreaCode(areaCode);

        log.info("============= orderSubmitDTO:StoreVoucher:{}", orderInfo.getStoreVoucherAmount());
        log.info("============= orderSubmitDTO:StoreActivity:{}", orderInfo.getStoreActivityAmount());

        log.info("============= orderSubmitDTO:PlatformVoucher:{}", orderInfo.getPlatformVoucherAmount());
        log.info("============= orderSubmitDTO:PlatformActivity:{}", orderInfo.getPlatformActivityAmount());

        orderPO.setFinanceRuleCode(ruleCode);
        orderPO.setRuleTag(ruleTag);
        // 活动优惠明细
        List<OrderSubmitDTO.PromotionInfo> promotionInfoList = orderInfo.getPromotionInfoList();
        if (!CollectionUtils.isEmpty(promotionInfoList)) {
            orderPO.setActivityDiscountDetail(
                    JSON.toJSONString(orderInfo.getPromotionInfoList(), SortField, MapSortField));
        }
        orderPO.setIntegral(orderInfo.getIntegral());
        orderPO.setIntegralCashAmount(orderInfo.getIntegralCashAmount());

        // 引荐商户
        orderPO.setRecommendStoreId(storeContract.getRecommentBusiness());
        orderPO.setRecommendStoreName(storeContract.getRecommentBusinessName());

        //经销商编码
        orderPO.setDealerCode(consumerDTO.getParamDTO().getDealerCode());

        // 换货标识
        if (OrderPatternEnum.EXCHANGE_ORDER.getValue().equals(orderPO.getOrderPattern())) {
            orderPO.setExchangeFlag(ExchangeOrderConst.ORDER_EXCHANGE_FLAG_2);
        }

        // 商户分支信息
        orderPO.setStoreBranch(storeContract.getBranchCode());
        orderPO.setStoreBranchName(storeContract.getBranchName());

        // 设置平台服务费 和 代运营服务费
        orderPO.setThirdpartnarFeeRate(storeContract.getSettleRateDecimal()); // 代运营服务费率

        BigDecimal priceInFee =
                orderInfo.getGoodsAmount().add(orderInfo.getExpressFee()).add(orderInfo.getXzCardExpressFeeAmount())
                        .subtract(orderInfo.getStoreVoucherAmount()).subtract(orderInfo.getStoreActivityAmount());

        orderPO.setServiceFee(BigDecimal.ZERO); // 平台服务费
        orderPO.setOrderType(orderInfo.getOrderType());
        orderPO.setThirdpartnarFee(priceInFee.multiply(orderPO.getThirdpartnarFeeRate())); // 代运营服务费
        // 线下补录和预占订单不需要待运营服务费
        if (OrderTypeEnum.isOfflineAll(orderPO.getOrderType())) {
            orderPO.setThirdpartnarFee(BigDecimal.ZERO); // 代运营服务费
        }
        AccountCard bankAccount = billOperatinIntegration.detailByBankAccount(orderInfo.getStoreId().toString(),
                AccountCardTypeEnum.UNI_JS_STORE_HEAD);
        if (Objects.nonNull(bankAccount) && orderInfo.getOrderType() != OrderTypeEnum.ORDER_TYPE_7.getValue()) {
            orderPO.setNewOrder(true);
        } else {
            orderPO.setNewOrder(false);
        }

        // 根据白名单兜底设置支付渠道
        orderPO.setPayChannel(orderLocalUtils.isYztCanUse(orderPO.getStoreId().toString(),
                orderPO.getNewOrder()).getValue());

        // 设置支付方式
        orderPO.setPaymentName(OrderPaymentConst.PAYMENT_NAME_ONLINE);
        orderPO.setPaymentCode(OrderPaymentConst.PAYMENT_CODE_ONLINE);
        // 子订单支付金额为0且乡助卡支付金额为0，记为卡券
        if (orderPO.getOrderAmount().compareTo(BigDecimal.ZERO) == 0) {
            if (orderPO.getXzCardAmount().compareTo(BigDecimal.ZERO) == 0) {
                orderPO.setPaymentName(OrderPaymentConst.PAYMENT_NAME_CARD_VOUCHER);
                orderPO.setPaymentCode(OrderPaymentConst.PAYMENT_CODE_CARD_VOUCHER);
                // 子订单支付金额为0且乡助卡支付金额为0，记为乡助卡
            } else if (orderPO.getXzCardAmount().compareTo(BigDecimal.ZERO) > 0) {
                orderPO.setPaymentName(OrderPaymentConst.PAYMENT_NAME_CARD);
                orderPO.setPaymentCode(OrderPaymentConst.PAYMENT_CODE_CARD);
            }
        }
        if (OrderCreateChannel.OMS.getValue().equals(consumerDTO.getParamDTO().getChannel())) {
            orderPO.setPaymentCode(PayMethodEnum.BANK_PAY.getValue());
            orderPO.setPaymentName(PayMethodEnum.BANK_PAY.getDesc());
        }
        if (orderInfo.getOrderType() == com.cfpamf.ms.mallpromotion.constant.PromotionConst.PROMOTION_TYPE_107) {
            orderPO.setPaymentCode(PayMethodEnum.COMBINATION_PAY.getValue());
            orderPO.setPaymentName(PayMethodEnum.COMBINATION_PAY.getDesc());
        }

        orderPO.setSettleMode(orderInfo.getSettleMode());

        if (ObjectUtils.isNotEmpty(consumerDTO.getOrderParamDTO())) {//批量非购物车下单，附件存储
            orderPO.setAttachmentUrls(consumerDTO.getOrderParamDTO().getAttachmentUrls());
            orderPO.setLoanPayer(consumerDTO.getOrderParamDTO().getLoanPayer());
            orderPO.setLoanConfirmMethod(consumerDTO.getOrderParamDTO().getLoanConfirmMethod());
        } else {
            if (consumerDTO.getParamDTO().isPlacingCombinationFlag()) {
                orderPO.setAttachmentUrls(consumerDTO.getParamDTO().getAttachmentUrls());
                orderPO.setLoanPayer(consumerDTO.getParamDTO().getLoanPayer());
                orderPO.setLoanConfirmMethod(consumerDTO.getParamDTO().getLoanConfirmMethod());
            }
        }

        if (Objects.isNull(orderPlaceUserRole) || OrderPlaceUserRole.isSelfOrderPlace(orderPlaceUserRole)) {
            //设置订单下单角色
            orderPO.setOrderPlaceUserRoleCode(OrderPlaceUserRole.SELF.getValue());
            orderPO.setOrderPlaceUserRoleDesc(OrderPlaceUserRole.SELF.getDesc());
            //设置订单客户确认状态
            orderPO.setCustomerConfirmStatus(CustomerConfirmStatusEnum.NO_NEED_CONFIRM.getValue());
            orderPO.setCustomerConfirmStatusDesc(CustomerConfirmStatusEnum.NO_NEED_CONFIRM.getDesc());

            if (Objects.isNull(orderPlaceUserDTO)) {
                orderPlaceUserDTO = new OrderPlaceUserDTO(Long.valueOf(member.getMemberId()), member.getMemberName(), member.getUserNo());
            }
            log.info("客户自主下单，设置订单下单角色|设置订单客户确认状态，orderSn:{}", orderPO.getOrderSn());
        } else {
            //设置订单下单角色
            orderPO.setOrderPlaceUserRoleCode(orderPlaceUserRole.getValue());
            orderPO.setOrderPlaceUserRoleDesc(orderPlaceUserRole.getDesc());
            //设置订单客户确认状态
            orderPO.setCustomerConfirmStatus(CustomerConfirmStatusEnum.DRAFT.getValue());
            orderPO.setCustomerConfirmStatusDesc(CustomerConfirmStatusEnum.DRAFT.getDesc());
            log.info("代客下单，设置订单下单角色|设置订单客户确认状态，orderSn:{}", orderPO.getOrderSn());
        }
        orderPO.setCreateBy(orderPlaceUserDTO.getOrderPlaceUserId() + "".concat("-").concat(orderPlaceUserDTO.getOrderPlaceUserName()));
        if (Objects.nonNull(consumerDTO.getOrderOfflineParamDTO())) {
            orderPO.setPaymentTag(consumerDTO.getOrderOfflineParamDTO().getPaymentTag());
            orderPO.setCreateTime(consumerDTO.getOrderOfflineParamDTO().getCreateTime());
        }
        Integer orderSource = OrderSourceEnum.SELF.getValue();
        if (!OrderPlaceUserRole.isSelfOrderPlace(orderPO.getOrderPlaceUserRoleCode())) {
            // 代客下单
            orderSource = OrderSourceEnum.CLIENT.getValue();
        }
        if (!StringUtils.isEmpty(consumerDTO.getParamDTO().getShareCode())) {
            // 分享下单
            orderSource = OrderSourceEnum.SHARE.getValue();
        }
        if (consumerDTO.getParamDTO().isShareResource()) {
            // 邀请下单
            orderSource = OrderSourceEnum.INVITE.getValue();
        }
        orderPO.setOrderSource(orderSource);

        return orderPO;
    }

    /**
     * 修改尾款订单
     *
     * @param orderPODb
     * @param orderInfo
     * @param memberAddress
     * @return
     */
    private OrderPO updateBalanceOrder(OrderPO orderPODb, OrderSubmitDTO.OrderInfo orderInfo,
                                       MemberAddress memberAddress) {
        OrderPO orderPO = new OrderPO();
        orderPO.setOrderSn(orderPODb.getOrderSn());
        orderPO.setActivityDiscountAmount(orderInfo.getTotalDiscount());
        // 活动优惠明细
        List<OrderSubmitDTO.PromotionInfo> promotionInfoList = orderInfo.getPromotionInfoList();
        if (!CollectionUtils.isEmpty(promotionInfoList)) {
            orderPO.setActivityDiscountDetail(
                    JSON.toJSONString(orderInfo.getPromotionInfoList(), SortField, MapSortField));
            // 没有活动优惠就不做改动
            orderPO.setGoodsAmount(orderInfo.getGoodsAmount());
            orderPO.setOrderAmount(orderInfo.getTotalAmount().add(orderPODb.getExpressFee()));
        } else {
            // 默认金额
            orderPO.setGoodsAmount(orderPODb.getGoodsAmount());
            orderPO.setOrderAmount(orderPODb.getOrderAmount());
        }
        orderPO.setIntegral(orderInfo.getIntegral());
        orderPO.setIntegralCashAmount(orderInfo.getIntegralCashAmount());

        this.updateOrder(orderPO);
        return orderPO;
    }

    /**
     * 提交订单-扣除用户使用积分，记录积分日志
     *
     * @param memberId 会员id
     * @param integral 使用积分数
     * @param orderSn  订单号
     */
    private void reduceMemberIntegral(Integer memberId, Integer integral, String orderSn) {
        if (!StringUtil.isNullOrZero(integral)) {
            // 查询会员信息,此处不能用登录缓存的会员信息，需要查询最新的积分数量
            Member memberDb = orderSubmitAttributesUtils.getMemberByMemberId(memberId);
//            Member memberDb = memberFeignClient.getMemberByMemberId(memberId);
            // 更新会员积分
            Member updateMember = new Member();
            updateMember.setMemberId(memberDb.getMemberId());
            updateMember.setMemberIntegral(memberDb.getMemberIntegral() - integral);
            updateMember.setUpdateTime(new Date());
            memberFeignClient.updateMember(updateMember);

            // 记录积分日志
            MemberIntegralLog memberIntegralLog = new MemberIntegralLog();
            memberIntegralLog.setMemberId(memberDb.getMemberId());
            memberIntegralLog.setMemberName(memberDb.getMemberName());
            memberIntegralLog.setValue(integral);
            memberIntegralLog.setCreateTime(new Date());
            memberIntegralLog.setType(MemberIntegralLogConst.TYPE_7);
            memberIntegralLog.setDescription("商品下单使用积分，订单号：" + orderSn + "，使用积分数量：" + integral);
            memberIntegralLog.setRefCode(orderSn);
            memberIntegralLog.setOptId(memberDb.getMemberId());
            memberIntegralLog.setOptName(memberDb.getMemberName());
            memberIntegralLogFeignClient.saveMemberIntegralLog(memberIntegralLog);

            // 发送积分变动消息通知
            // this.sendMsgIntegralChange(updateMember, "下单支付");
        }
    }

    /**
     * 发送余额变动消息通知
     *
     * @param memberBalanceLog 余额变动信息
     */
    public void sendMsgAccountChange(MemberBalanceLogVO memberBalanceLog) {
        // 消息通知
        List<MessageSendProperty> messageSendPropertyList = new ArrayList<>();
        messageSendPropertyList.add(new MessageSendProperty("description", "取消订单返还余额"));
        messageSendPropertyList
                .add(new MessageSendProperty("availableBalance", memberBalanceLog.getAfterChangeAmount().toString()));
        messageSendPropertyList
                .add(new MessageSendProperty("frozenBalance", memberBalanceLog.getFreezeAmount().toString()));
        // 微信消息通知
        List<MessageSendProperty> messageSendPropertyList4Wx = new ArrayList<>();
        messageSendPropertyList4Wx.add(new MessageSendProperty("first", "您的账户发生了资金变动。"));
        messageSendPropertyList4Wx.add(new MessageSendProperty("keyword1", "取消订单返还余额"));
        messageSendPropertyList4Wx
                .add(new MessageSendProperty("keyword2", memberBalanceLog.getChangeValue().toString()));
        messageSendPropertyList4Wx
                .add(new MessageSendProperty("keyword3", TimeUtil.getDateTimeString(memberBalanceLog.getCreateTime())));
        messageSendPropertyList4Wx
                .add(new MessageSendProperty("keyword4", memberBalanceLog.getAfterChangeAmount().toString()));
        String msgLinkInfo = "{\"type\":\"balance_change\"}";
        MessageSendVO messageSendVO = new MessageSendVO(messageSendPropertyList, messageSendPropertyList4Wx,
                "changeTime", memberBalanceLog.getMemberId(), MemberTplConst.BALANCE_CHANGE_REMINDER, msgLinkInfo);
        // 发送到mq
        try {
            rabbitTemplate.convertAndSend(MQ_EXCHANGE_NAME, MQ_QUEUE_NAME_MEMBER_MSG, messageSendVO);
        } catch (Exception e) {
            log.info("发消息异常捕获,{}", JSON.toJSONString(messageSendVO), e);
        }
    }

    /**
     * 提交订单-扣除用户优惠券，记录优惠券使用日志，统计优惠券使用数量
     *
     * @param memberDb       会员信息
     * @param couponCodeList 优惠券编码
     * @param orderSn        订单号
     * @param storeId        店铺id
     */
    private void deductMemberCoupon(Member memberDb, List<String> couponCodeList, String orderSn, Long storeId) {
        if (!CommonConfig.enableTransactionLog()) {
            // 走老逻辑
            if (!CollectionUtil.isEmpty(couponCodeList)) {
                //            for (String couponCode : couponCodeList) {
                //                orderPromotionDetailModel.isCouponUsed(0L != storeId, orderSn, couponCode);
                //            }
                JsonResult<Map<String, CouponMemberVO>> mapJsonResult = couponMemberFeignClient.getCouponInfoByCodes(couponCodeList);
                AssertUtil.notNull(mapJsonResult.getData(), "用户优惠券不存在");
                List<CouponMemberVO> couponMemberList = new ArrayList<CouponMemberVO>(mapJsonResult.getData().values());

                List<Integer> couponMemberIdList = couponMemberList.stream().map(CouponMemberVO::getCouponMemberId).collect(Collectors.toList());
                List<CouponUseLog> couponUseLogList = new ArrayList<>(couponMemberList.size());
                Map<Integer, List<CouponMemberVO>> map = couponMemberList.stream().collect(Collectors.groupingBy(CouponMemberVO::getCouponId));

                // 记录优惠券使用日志
                for (CouponMemberVO couponMemberDb : couponMemberList) {
                    CouponUseLog couponUseLog = new CouponUseLog();
                    couponUseLog.setCouponCode(couponMemberDb.getCouponCode());
                    couponUseLog.setMemberId(couponMemberDb.getMemberId());
                    couponUseLog.setMemberName(couponMemberDb.getMemberName());
                    couponUseLog.setStoreId(couponMemberDb.getStoreId());
                    couponUseLog.setOrderSn(orderSn);
                    couponUseLog.setLogType(CouponConst.LOG_TYPE_2);
                    couponUseLog.setLogContent("商品下单使用优惠券，订单号：" + orderSn);
                    couponUseLog.setLogTime(new Date());
                    couponUseLogList.add(couponUseLog);
                }
                for (Integer couponId : map.keySet()) {
                    // 修改优惠券使用数量
                    Coupon updateCoupon = new Coupon();
                    updateCoupon.setCouponId(couponId);
                    updateCoupon.setUsedNum(map.get(couponId).size());
                    couponFeignClient.updateOrderCoupon(updateCoupon);
                }
                //修改为使用
                couponMemberFeignClient.updateCouponMemberUse(couponMemberIdList, orderSn);

                // 记录优惠券使用日志
                couponUseLogFeignClient.saveCouponUseLogBatch(couponUseLogList);
            }
        } else {
            // 走本地事务
            UseCouponBatchDTO useCouponBatchDTO = new UseCouponBatchDTO();
            useCouponBatchDTO.setCouponCodeList(couponCodeList);
            useCouponBatchDTO.setOrderSn(orderSn);
            JsonResult<Integer> result = ExternalApiUtil.callJsonResultApi(
                    () -> couponMemberFeignClient.updateCouponMemberUseV2(useCouponBatchDTO), useCouponBatchDTO,
                    "/v1/feign/promotion/couponMember/updateCouponMemberUseV2", "优惠券批量使用");
            log.info("商品库存批量操作API结果， stockCutRequest:{} result：{}", useCouponBatchDTO, result);
        }
    }

    /**
     * 取消订单（整单取消） 1.修改订单状态 2.记录订单日志 3.返还订单使用的积分，记录积分日志 4.返还使用的店铺优惠券，记录优惠券日志 5.返还使用的平台优惠券，记录优惠券日志（最后一个订单才退）
     * 6.增加货品库存，增加商品库存，秒杀订单增加redis中的秒杀库存 7.处理用户余额，记录余额日志
     *
     * @param orderPOList  取消的订单列表
     * @param cancelReason 取消原因
     * @param optRole      操作角色{@link OrderConst#LOG_ROLE_ADMIN}
     * @param optUserId    操作人id
     * @param optUserName  操作人名称
     * @param optRemark    操作备注
     * @param returnBy     发起者
     */
    @GlobalTransactional
    public void cancelOrder(List<OrderPO> orderPOList, String cancelReason, String cancelRemark, Integer optRole,
                            Long optUserId, String optUserName, String optRemark, Integer returnBy) {
        // 标识是否为拼团订单
        AtomicReference<Boolean> isSpellPaid = new AtomicReference<>(false);
        orderPOList.forEach(orderDb -> {

            LambdaQueryWrapper<OrderReturnPO> orderReturnQuery = new LambdaQueryWrapper<>();
            orderReturnQuery.eq(OrderReturnPO::getOrderSn, orderDb.getOrderSn());
            orderReturnQuery.eq(OrderReturnPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
            orderReturnQuery.notIn(OrderReturnPO::getState, OrderReturnStatus.endStatus());
            List<OrderReturnPO> ors = orderReturnService.list(orderReturnQuery);
            BizAssertUtil.isTrue(!CollectionUtils.isEmpty(ors), "订单已经存在退款记录, 不允许取消：" + orderDb.getOrderSn());

            BizAssertUtil.isTrue(!orderCancelValidation.dealOrderBeforeCancel(orderDb), "订单暂不支持取消，请稍后~");

            // 2.记录订单日志
            orderLogModel.insertOrderLog(optRole, optUserId, optUserName, orderDb.getOrderSn(), orderDb.getOrderState(),
                    OrderConst.ORDER_STATE_0, LoanStatusEnum.DEFAULT.getValue(), optRemark, OrderCreateChannel.WEB);

            // 不是已支付的拼团订单，才做以下处理，常规订单进行下面处理
            if (!(orderDb.getOrderState().equals(OrderConst.ORDER_STATE_20)
                    && orderDb.getOrderType().equals(PromotionConst.PROMOTION_TYPE_102))) {
                // 1.修改订单状态
                OrderPO updateOrderPO = new OrderPO();
                updateOrderPO.setOrderId(orderDb.getOrderId());
                if (orderDb.getOrderState() == OrderConst.ORDER_STATE_20) {
                    updateOrderPO.setOrderState(OrderConst.ORDER_STATE_50);
                } else {
                    updateOrderPO.setOrderState(OrderConst.ORDER_STATE_0);
                }
                updateOrderPO.setRefuseReason(cancelReason);
                updateOrderPO.setRefuseRemark(cancelRemark);
                boolean result = orderPlacingService.updateById(updateOrderPO);
                AssertUtil.isTrue(!result, "取消订单失败");

                // 3.返还订单使用的积分，记录积分日志
                this.orderCancelReturnIntegral(orderDb, optUserId, optUserName);

                // 4.返还使用的店铺优惠券，记录优惠券日志
                this.orderCancelReturnStoreCoupon(orderDb);
            } else {
                isSpellPaid.set(true);
            }

            // 7.处理用户余额，记录余额日志
            /*if (orderDb.getOrderType() == PromotionConst.PROMOTION_TYPE_103) {
                //预售定金赔偿倍数
                String value = stringRedisTemplate.opsForValue().get("presale_compensate");
                int compensateValue = StringUtil.isEmpty(value) ? 0 : Integer.parseInt(value);
            if (orderDb.getOrderType() == PromotionConst.PROMOTION_TYPE_103) {
                //预售处理用户余额
                this.preSellOrderCancelDeal(orderDb, optRole, optUserId, optUserName, compensateValue);
            } else if (orderDb.getOrderType() == PromotionConst.PROMOTION_TYPE_105) {
                //阶梯团处理用户余额
                this.ladderGroupOrderCancelDealMemberBalance(orderDb, optRole, optUserId, optUserName);
            } else {
                this.orderCancelDealMemberBalance(orderDb, optRole, optUserId, optUserName);
            }*/

            // 8.订单状态为已付款，需记录售后服务信息，
            if (orderDb.getOrderState().equals(OrderConst.ORDER_STATE_20)) {
                orderCancelInsertAfterServiceAndReturn(orderDb, cancelReason, cancelReason, returnBy, optRole, optUserId,
                        optUserName, null);
            } else {
                // 6.增加货品库存，增加商品库存
                this.orderCancelAddGoodsStock(orderDb.getOrderSn(), orderDb.getOrderType(), orderDb.getAreaCode(),
                        orderDb.getFinanceRuleCode(), orderDb);
                // 订单取消，更新商品的已退数量
                orderProductService.updateProductReturnNumAfterCancel(orderDb.getOrderSn());
                // 取消不涉及退款，发送取消通知
                orderCreateHelper.addOrderChangeEvent(orderDb, OrderEventEnum.CANCEL, new Date());
            }

            if (CustomerConfirmStatusEnum.isDraftOrUnconfirmed(orderDb.getCustomerConfirmStatus())) {
                log.info("非客户取消订单，且为未确认的订单，客户确认状态为关闭。orderSn:{}", orderDb.getOrderSn());
                orderService.setCustomerConfirmClosedStatus(orderDb.getOrderSn(), optRole, optUserId, optUserName);
            }
        });

        // 5.返还使用的平台优惠券，记录优惠券日志
        if (!isSpellPaid.get()) {
            this.orderCancelReturnPlatformCoupon(orderPOList);
        }
    }

    /**
     * 生成售后和商品退款记录
     *
     * @param orderDb      订单信息
     * @param cancelReason 取消理由
     * @param returnBy     退款发起者
     */
    @Transactional(rollbackFor = Exception.class)
    public void orderCancelInsertAfterServiceAndReturn(OrderPO orderDb, String cancelReason, String cancelRemark, Integer returnBy,
                                                       Integer optRole, Long optUserId, String optUserName, String productIds) {
        // 构造退款单信息
        OrderAfterDTO orderAfterDTO = new OrderAfterDTO();
        orderAfterDTO.setOrderSn(orderDb.getOrderSn());
        orderAfterDTO.setAfsType(OrdersAfsConst.AFS_TYPE_REFUND);
        orderAfterDTO.setGoodsState(OrdersAfsConst.GOODS_STATE_NO);
        orderAfterDTO.setApplyReasonContent(cancelReason);
        orderAfterDTO.setAfsDescription(cancelRemark);
        orderAfterDTO.setFinalReturnAmount(orderDb.getPayAmount());
        orderAfterDTO.setReturnBy(returnBy);
        orderAfterDTO.setContactName(optUserName);

        // 查询订单货品
        OrderProductExample orderProductExample = new OrderProductExample();
        orderProductExample.setOrderSn(orderDb.getOrderSn());
        if (!StringUtils.isEmpty(productIds)) {
            orderProductExample.setProductIdIn(productIds);
        }
        List<OrderProductPO> orderProductList = orderProductModel.getOrderProductList(orderProductExample, null);
        List<OrderAfterDTO.AfterProduct> afterProductList = orderProductList.stream().filter(x -> x.getIsGift().equals(OrderConst.IS_GIFT_NO))
                .map((orderProduct -> {
                    //赠品不生成退款单
                    OrderAfterDTO.AfterProduct afterProduct = new OrderAfterDTO.AfterProduct();
                    afterProduct.setOrderProductId(orderProduct.getOrderProductId());
                    afterProduct.setAfsNum(orderProduct.getProductNum() - orderProduct.getReturnNumber());
                    //afterProduct.setReturnAmount(orderProduct.getMoneyAmount());
                    return afterProduct;
                })).collect(Collectors.toCollection(LinkedList::new));

        orderAfterDTO.setProductList(afterProductList);

        List<AfsProductVO> afsProductVOS = orderAfterServiceModel.dealAfsProduct(orderDb, orderAfterDTO);

        afsProductVOS.forEach(afsProductVO -> {

            BizAssertUtil.isTrue(!orderReturnValidation.refundProductCheck(afsProductVO.getOrderProductId()),
                    "商品存在退款中的退款单，不允许再次发起退款~");

            //商品的退货退款数量，不能超过发货数量
            if (OrdersAfsConst.AFS_TYPE_RETURN == orderAfterDTO.getAfsType()) {
                BizAssertUtil.isTrue(orderReturnValidation.validReturnNumGtDeliverNum(afsProductVO.getOrderProductId(), afsProductVO.getAfsNum()),
                        "商品的退货退款数量，不能超过发货数量~");
            }


            String afsSn = shardingId.next(SeqEnum.RNO, orderDb.getMemberId().toString()) + "";
            // 1.插入售后申请表， 退款明细
            orderAfterServiceModel.insertAfterService(afsSn, afsProductVO, orderAfterDTO, orderDb);
            // 2.根据售后类型插入退货表或者换货表，保存售后日志 退款主档
            OrderReturnPO orderReturnPO =
                    orderAfterServiceModel.insertReturnOrder(afsSn, orderAfterDTO, afsProductVO, orderDb);
            // 3.更新订单货品中的退换货数量
            orderProductService.addReturnNumber(afsProductVO.getOrderProductId(), afsProductVO.getAfsNum());
            // 处理订单商品行退款状态
            orderProductService.dealOrderProductReturnStatus(CommonConst.PRODUCT_RETURN_APPLY,
                    afsProductVO.getOrderProductId());
            // 锁订单
            orderInfoService.dealOrderLock(orderDb.getOrderSn(), 1);
            // 批量保存退款记录
            orderRefundRecordService.saveBatch(orderReturnPO, orderDb);

            // 计算扣罚金额，实际退款金额需要减去扣罚金额 并更新
            if (orderReturnValidation.refundPunishAmountSupport(orderReturnPO)) {
                orderAfterServiceModel.recalculateRefundAmount(orderDb, orderReturnPO);
            }

            Integer operateType = 0;
            if (OrderConst.RETURN_BY_3 == returnBy) {
                operateType = OrderReturnOperateTypeEnum.PLATFORM_REFUND.getValue();
            } else if (orderDb.getOrderType() == OrderTypeEnum.PRE_SELL_DEPOSIT.getValue()) {
                operateType = OrderReturnOperateTypeEnum.PRESELL_AUTO_REFUND_DEPOSIT.getValue();
            } else if (orderDb.getOrderType() == OrderTypeEnum.SPELL_GROUP.getValue()) {
                operateType = OrderReturnOperateTypeEnum.SPELL_AUTO_REFUND.getValue();
            }
            // 4.记录轨迹
            Date now = new Date();
            OrderReturnTrackPO orderReturnTrackPO = OrderReturnTrackPO.builder().afsSn(afsSn).operateType(operateType)
                    .operator(optUserName).operateTime(now).operateResult(AuditResultEnum.AUDIT_PASS.getValue())
                    .operateRemark(cancelReason).build();
            orderReturnTrackMapper.insert(orderReturnTrackPO);

            // 5.拼团订单,换货后的订单，直接退款，不经过审批
            if (orderDb.getOrderType() == PromotionConst.PROMOTION_TYPE_102 && OrderConst.RETURN_BY_0 == returnBy) {
                // 直接发起退款
                this.autoReturnPaidSpellOrder(orderDb, afsSn, optRole, optUserId, optUserName, afsProductVO.getOrderProductId());
            }

            //售后申请MQ
            orderCreateHelper.addOrderReturnEvent(orderDb, orderDb.getXzCardAmount(), orderReturnPO,
                    OrderEventEnum.REFUND_APPLY, now, null);
        });
    }

    /**
     * 已付款的拼团订单自动取消
     *
     * @param orderDb     订单信息
     * @param afsSn       退款单
     * @param optRole     角色信息
     * @param optUserId   角色id
     * @param optUserName 角色名
     */
    private void autoReturnPaidSpellOrder(OrderPO orderDb, String afsSn, Integer optRole, Long optUserId,
                                          String optUserName, Long orderProductId) {
        try {
            this.orderCancelDealMemberBalance(orderDb, afsSn, optRole, optUserId, optUserName);
        } catch (Exception e) {
            // 把退款单信息修正为待平台审核阶段
            LambdaUpdateWrapper<OrderReturnPO> returnUpdateWrapper = Wrappers.lambdaUpdate(OrderReturnPO.class);
            returnUpdateWrapper.eq(OrderReturnPO::getAfsSn, afsSn)
                    .eq(OrderReturnPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                    .set(OrderReturnPO::getState, OrderReturnStatus.STORE_AGREE_REFUND.getValue());
            orderReturnService.update(returnUpdateWrapper);
            return;
        }

        // 把订单置为关闭
        LambdaUpdateWrapper<OrderPO> orderUpdateWrapper = Wrappers.lambdaUpdate(OrderPO.class);
        orderUpdateWrapper.eq(OrderPO::getOrderSn, orderDb.getOrderSn())
                .eq(OrderPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                .set(OrderPO::getOrderState, OrderConst.ORDER_STATE_50);
        orderService.update(orderUpdateWrapper);

//        // 触发结算的信息
//        OrderPO orderPO = orderMapper.getOrderAmount(orderDb.getOrderSn());
//        if (orderPO.getFinishTime() == null) {
//            orderPO.setFinishTime(new Date());
//        }
//        try {
//            orderService.updateOperatin(orderPO);
//        } catch (Exception ex) {
//            log.warn("同步更新支付订单的完结时间出错", ex);
//            taskQueueService.saveTaskQueue(Long.valueOf(orderPO.getOrderSn()),
//                TaskQueueBizTypeEnum.UPDATE_BILL_OPERATIN, new Date(), TaskConstant.DEFAULT_JOB_NUMBER);
//        }
        OrderPO order = orderService.getByOrderSn(orderDb.getOrderSn());
        // 推送订单关闭消息
        orderCreateHelper.addOrderChangeEvent(order, OrderEventEnum.CLOSE, new Date());

        // 零元支付的情况下，不会回调支付成功，直接调用支付成功
        if (orderDb.getPaymentCode().equals(PayMethodEnum.CARD_VOUCHER.getValue())
                || orderDb.getPaymentCode().equals(PayMethodEnum.BANK_PAY.getValue())
                || orderDb.getPaymentCode().equals(PayMethodEnum.CARD.getValue())) {
            orderReturnModel.refundSuccess(null, afsSn);
            // 修改订单退款金额和锁定状态
            OrderPO updateOrderPOPO = new OrderPO();
            updateOrderPOPO.setOrderId(orderDb.getOrderId());
            updateOrderPOPO.setRefundAmount(BigDecimal.ZERO);
            updateOrderPOPO.setLockState(orderDb.getLockState() - 1);
            orderPlacingService.updateById(updateOrderPOPO);
        }

        // 6.增加货品库存，增加商品库存
        this.orderCancelAddGoodsStock(orderDb.getOrderSn(), orderDb.getOrderType(), orderDb.getAreaCode(),
                orderDb.getFinanceRuleCode(), orderDb);

        // 3.返还订单使用的积分，记录积分日志
        this.orderCancelReturnIntegral(orderDb, optUserId, optUserName);

        // 4.返还使用的店铺优惠券，记录优惠券日志
        this.orderCancelReturnStoreCoupon(orderDb);

        // 5.平台券
        this.orderCancelReturnPlatformCoupon(Collections.singletonList(orderDb));
    }

    /**
     * 取消订单返还订单使用的积分，记录积分日志
     *
     * @param orderPODb   订单
     * @param optUserId   操作人id
     * @param optUserName 操作人名称
     */
    public void orderCancelReturnIntegral(OrderPO orderPODb, Long optUserId, String optUserName) {
        if (StringUtil.isNullOrZero(orderPODb.getIntegral())) {
            // 未使用积分
            return;
        }
        // 查询会员信息
        Member memberDb = memberFeignClient.getMemberByMemberId(orderPODb.getMemberId());

        // 更新积分数量
        Member updateMember = new Member();
        updateMember.setMemberId(memberDb.getMemberId());
        updateMember.setMemberIntegral(memberDb.getMemberIntegral() + orderPODb.getIntegral());
        memberFeignClient.updateMember(updateMember);

        // 记录积分日志
        MemberIntegralLog memberIntegralLog = new MemberIntegralLog();
        memberIntegralLog.setMemberId(memberDb.getMemberId());
        memberIntegralLog.setMemberName(memberDb.getMemberName());
        memberIntegralLog.setValue(orderPODb.getIntegral());
        memberIntegralLog.setCreateTime(new Date());
        memberIntegralLog.setType(MemberIntegralLogConst.TYPE_11);
        memberIntegralLog.setDescription("订单取消退回积分，订单号：" + orderPODb.getOrderSn());
        memberIntegralLog.setRefCode(orderPODb.getOrderSn());
        memberIntegralLog.setOptId(Math.toIntExact(optUserId));
        memberIntegralLog.setOptName(optUserName);
        memberIntegralLogFeignClient.saveMemberIntegralLog(memberIntegralLog);
    }

    /**
     * 订单取消返还使用的店铺优惠券，记录优惠券日志
     *
     * @param orderPODb
     */
    public void orderCancelReturnStoreCoupon(OrderPO orderPODb) {
        if (CommonConfig.enableTcc()) {
            couponOrderManager.storeCouponReturnBatch(orderPODb);
            return;
        }
        // 查询订单使用的优惠券
        OrderPromotionDetailExample example = new OrderPromotionDetailExample();
        example.setOrderSn(orderPODb.getOrderSn());
        example.setPromotionType(PromotionConst.PROMOTION_TYPE_402);
        example.setIsStore(OrderConst.IS_STORE_PROMOTION_YES);
        List<OrderPromotionDetailPO> orderPromotionDetailPOList =
                orderPromotionDetailModel.getOrderPromotionDetailList(example, null);
        if (CollectionUtils.isEmpty(orderPromotionDetailPOList)) {
            // 未使用优惠券
            return;
        }
        orderPromotionDetailPOList.forEach(orderPromotionDetailPO -> {
            this.doReturnCoupon(orderPromotionDetailPO.getPromotionId(), orderPromotionDetailPO.getOrderSn());
        });
    }

    /**
     * 订单取消返还使用的平台优惠券，记录优惠券日志（最后一个订单才退）
     *
     * @param orderPOList 订单列表
     */
    public void orderCancelReturnPlatformCoupon(List<OrderPO> orderPOList) {
        if (CommonConfig.enableTcc()) {
            couponOrderManager.platformCouponReturnBatch(orderPOList);
            return;
        }
        // 同一张优惠券记录多次，只返还一次
        Map<String/*优惠券编码*/, OrderPromotionDetailPO/*订单活动优惠*/> mapByCouponCode = new HashMap<>();
        orderPOList.forEach(orderDb -> {
            OrderPromotionDetailExample example = new OrderPromotionDetailExample();
            example.setOrderSn(orderDb.getOrderSn());
            example.setPromotionType(PromotionConst.PROMOTION_TYPE_402);
            example.setIsStore(OrderConst.IS_STORE_PROMOTION_NO);
            List<OrderPromotionDetailPO> orderPromotionDetailPOList =
                    orderPromotionDetailModel.getOrderPromotionDetailList(example, null);
            if (!CollectionUtils.isEmpty(orderPromotionDetailPOList)) {
                orderPromotionDetailPOList.forEach(orderPromotionDetailPO -> {
                    if (!mapByCouponCode.containsKey(orderPromotionDetailPO.getPromotionId())) {
                        // 不同的优惠券编码，放入map
                        mapByCouponCode.put(orderPromotionDetailPO.getPromotionId(), orderPromotionDetailPO);
                    }
                    // 重复的优惠券编码，不做任何处理
                });
            }
        });
        if (CollectionUtils.isEmpty(mapByCouponCode)) {
            // 没有使用平台优惠券
            return;
        }
        mapByCouponCode.forEach((couponCode, orderPromotionDetailPO) -> {
            this.doReturnCoupon(couponCode, orderPromotionDetailPO.getOrderSn());
        });
    }

    /**
     * 返还优惠券，记录优惠券日志
     *
     * @param couponCode 优惠券编码
     * @param orderSn    订单号
     */
    public void doReturnCoupon(String couponCode, String orderSn) {
        // 获取优惠券code，修改使用状态
        CouponMember couponMemberExample = new CouponMember();
        couponMemberExample.setCouponCode(couponCode);
        List<String> couponCodeList = Arrays.asList(couponCode.split(","));
        JsonResult<Map<String, CouponMemberVO>> mapJsonResult = couponMemberFeignClient.getCouponInfoByCodes(couponCodeList);
        AssertUtil.notNull(mapJsonResult.getData(), "用户优惠券不存在");
        List<CouponMemberVO> couponMemberList = new ArrayList<CouponMemberVO>(mapJsonResult.getData().values());

        List<Integer> couponMemberIdList = couponMemberList.stream().map(CouponMemberVO::getCouponMemberId).collect(Collectors.toList());
        List<CouponUseLog> couponUseLogList = new ArrayList<>(couponMemberList.size());
        Map<Integer, List<CouponMemberVO>> map = couponMemberList.stream().collect(Collectors.groupingBy(CouponMemberVO::getCouponId));

        // 优惠券使用状态不等于已使用，可能是其他情况已经退还了优惠券，不再记录
        List<CouponMemberVO> collect = couponMemberList.stream().filter(e -> e.getUseState().equals(CouponConst.USE_STATE_2)).collect(Collectors.toList());
        for (CouponMemberVO couponMemberDb : collect) {
            CouponUseLog couponUseLog = new CouponUseLog();
            couponUseLog.setCouponCode(couponMemberDb.getCouponCode());
            couponUseLog.setMemberId(couponMemberDb.getMemberId());
            couponUseLog.setMemberName(couponMemberDb.getMemberName());
            couponUseLog.setStoreId(couponMemberDb.getStoreId());
            couponUseLog.setOrderSn(orderSn);
            couponUseLog.setLogType(CouponConst.LOG_TYPE_3);// 订单取消返回
            couponUseLog.setLogContent("订单取消返还优惠券，订单号：" + orderSn);
            couponUseLog.setLogTime(new Date());
            couponUseLogList.add(couponUseLog);
        }
        for (Integer couponId : map.keySet()) {
            // 修改优惠券使用数量
            Coupon updateCoupon = new Coupon();
            updateCoupon.setCouponId(couponId);
            updateCoupon.setUsedNum(-map.get(couponId).size());
            couponFeignClient.updateOrderCoupon(updateCoupon);
        }


        //修改未未使用
        couponMemberFeignClient.updateCouponMemberV3(couponMemberIdList);

        // 记录优惠券使用日志
        couponUseLogFeignClient.saveCouponUseLogBatch(couponUseLogList);

    }

    /**
     * 订单取消-增加货品库存，增加商品库存
     *
     * @param orderSn
     * @param orderType       订单类型，1-普通订单，其他为活动类型
     * @param areaCode
     * @param financeRuleCode
     * @param orderDb
     */
    public void orderCancelAddGoodsStock(String orderSn, Integer orderType, String areaCode, String financeRuleCode, OrderPO orderDb) {
        log.info("【orderCancelAddGoodsStock】orderSn:{} , xid:{}", orderSn, RootContext.getXID());
        // 查订单扩展信息
        OrderExtendPO orderExtendPO = orderExtendService.lambdaQuery()
                .eq(OrderExtendPO::getOrderSn, orderSn)
                .eq(OrderExtendPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                .select(OrderExtendPO::getOrderSn, OrderExtendPO::getBranch, OrderExtendPO::getReceiveBranchCode,
                        OrderExtendPO::getWarehouseCode).one();

        // 查询订单货品
        OrderProductExample orderProductExample = new OrderProductExample();
        orderProductExample.setOrderSn(orderSn);
        List<OrderProductPO> orderProductPOList = orderProductModel.getOrderProductList(orderProductExample, null);
        Integer isCombination = orderDb.getOrderType() == OrderTypeEnum.COMBINATION.getValue() ? 1 : 0;

        for (OrderProductPO orderProduct : orderProductPOList) {
            // 查询现有库存
            Product productDb = productFeignClient.getProductByProductId(orderProduct.getProductId());
            String orderSnTmp = "";
            if (orderProduct.getIsGift().equals(OrderConst.IS_GIFT_YES)) {
                orderSnTmp = orderSn + "+" + orderProduct.getOrderProductId();
            } else {
                orderSnTmp = orderSn;
            }
            // 库存扣减
            goodsStockService.goodsStock(orderSnTmp, null, productDb.getProductId(), areaCode,
                    -orderProduct.getProductNum(), financeRuleCode, orderProduct.getBatchNo(), orderProduct.getPurchaseSubCode(),
                    orderProduct.getChannelSkuId(), orderExtendPO.getBranch(), orderExtendPO.getReceiveBranchCode(),
                    orderExtendPO.getWarehouseCode(), EventStockTypeEnum.CANCEL_ORDER_IN_STOCK, BizTypeEnum.INCREASE_STOCK,
                    OrderConst.LOG_USER_NAME, orderDb, orderProduct.getChannelSkuUnit(), orderProduct.getIsGift(), isCombination);

            if (CommonConfig.enableTcc() && (orderType.equals(PromotionConst.PROMOTION_TYPE_102)
                    || orderType.equals(PromotionConst.PROMOTION_TYPE_103)
                    || orderType.equals(PromotionConst.PROMOTION_TYPE_104)
                    || orderType.equals(PromotionConst.PROMOTION_TYPE_105)
                    || orderType == OrderTypeEnum.SPELL_GROUP.getValue()
                    || orderType == PromotionConst.PROMOTION_TYPE_106)) {
                promotionManager.cancelActivityResource(orderSn, orderType, orderProduct);
            } else {
                if (orderType.equals(PromotionConst.PROMOTION_TYPE_102)) {
                    orderReturnModel.returnSpellGoodsStock(productDb, orderSn, orderProduct);
                }
                if (orderType.equals(PromotionConst.PROMOTION_TYPE_103)) {
                    // 预售订单，修改redis中的已购买数量
                    String stockKey = RedisConst.PRE_SELL_PURCHASED_NUM_PREFIX + productDb.getGoodsId() + "_"
                            + orderProduct.getMemberId();
                    if (stringRedisTemplate.opsForValue().get(stockKey) != null) {
                        stringRedisTemplate.opsForValue().decrement(stockKey, orderProduct.getProductNum());
                    }
                    // 查询预售订单扩展信息
                    PresellOrderExtendExample orderExtendExample = new PresellOrderExtendExample();
                    orderExtendExample.setOrderSn(orderSn);
                    List<PresellOrderExtendVO> orderExtendList =
                            presellOrderExtendFeignClient.getPresellOrderExtendList(orderExtendExample);
                    AssertUtil.notEmpty(orderExtendList, "获取预售订单扩展信息为空");
                    PresellOrderExtendVO orderExtend = orderExtendList.get(0);
                    // 查询预售商品信息
                    PresellGoodsExample goodsExample = new PresellGoodsExample();
                    goodsExample.setPresellId(orderExtend.getPresellId());
                    goodsExample.setProductId(orderExtend.getProductId());
                    List<PresellGoodsVO> goodsList = presellGoodsFeignClient.getPresellGoodsList(goodsExample);
                    AssertUtil.notEmpty(goodsList, "预售商品不存在");
                    PresellGoodsVO presellGoods = goodsList.get(0);
                    // 修改预售商品库存
                    PromotionPresellGoods presellGoodsNew = new PromotionPresellGoods();
                    presellGoodsNew.setPresellGoodsId(presellGoods.getPresellGoodsId());
                    presellGoodsNew.setPresellStock(presellGoods.getPresellStock() + orderProduct.getProductNum());
                    presellGoodsFeignClient.updatePresellGoods(presellGoodsNew);
                }
                if (orderType.equals(PromotionConst.PROMOTION_TYPE_104)) {
                    // 查询秒杀订单扩展信息
                    SeckillOrderExtendExample extendExample = new SeckillOrderExtendExample();
                    extendExample.setOrderSn(orderSn);
                    List<SeckillOrderExtendVO> seckillOrderExtendList =
                            seckillOrderExtendFeignClient.getSeckillOrderExtendList(extendExample);
                    AssertUtil.notEmpty(seckillOrderExtendList, "秒杀订单扩展信息不存在");
                    SeckillOrderExtendVO seckillOrderExtend = seckillOrderExtendList.get(0);
                    // 查询秒杀商品信息
                    SeckillStageProductVO stageProduct = seckillStageProductFeignClient
                            .getSeckillStageProductByStageProductId(seckillOrderExtend.getStageProductstageProductId());
                    AssertUtil.notNull(stageProduct, "秒杀商品不存在");
                    Date date = new Date();
                    // 未结束修改redis中的秒杀库存
                    if (!date.after(stageProduct.getEndTime())) {
                        String stockKey = RedisConst.REDIS_SECKILL_PRODUCT_STOCK_PREFIX + productDb.getProductId();
                        String memberLimitKey = RedisConst.REDIS_SECKILL_MEMBER_BUY_NUM_PREFIX + productDb.getProductId() + "_"
                                + orderDb.getMemberId();
                        if (stringRedisTemplate.opsForValue().get(stockKey) != null) {
                            // 加库存
                            stringRedisTemplate.opsForValue().increment(stockKey, orderProduct.getProductNum());
                        }
                        if (stringRedisTemplate.opsForValue().get(memberLimitKey) != null) {
                            stringRedisTemplate.opsForValue().decrement(memberLimitKey, orderProduct.getProductNum());
                        }
                    }
                    // 修改秒杀商品表的已购买人数和购买数量
                    PromotionSeckillStageProduct seckillStageProduct = new PromotionSeckillStageProduct();
                    seckillStageProduct.setStageProductId(stageProduct.getStageProductId());
                    seckillStageProduct.setBuyerCount(-1);
                    seckillStageProduct.setBuyQuantity(-orderProduct.getProductNum());
                    seckillStageProduct.setSeckillStock(orderProduct.getProductNum());
                    seckillStageProductFeignClient.updateSeckillStageProductByOrder(seckillStageProduct);
                }
                if (orderType.equals(PromotionConst.PROMOTION_TYPE_105)) {
                    // 阶梯团订单，修改redis中的已购买数量
                    String stockKey = RedisConst.LADDER_GROUP_PURCHASED_NUM_PREFIX + productDb.getGoodsId() + "_"
                            + orderProduct.getMemberId();
                    if (stringRedisTemplate.opsForValue().get(stockKey) != null) {
                        stringRedisTemplate.opsForValue().decrement(stockKey, orderProduct.getProductNum());
                    }
                }
                // 6.拼团释放占位
                if (orderType == OrderTypeEnum.SPELL_GROUP.getValue()) {
                    spellFeignClient.cancelOrder(orderSn);
                } else if (orderType == PromotionConst.PROMOTION_TYPE_106) {
                    exclusiveFeignClient.cancelExclusiveOrder(orderSn);
                }
            }
        }
    }

    /**
     * 取消订单-处理用户余额，记录余额日志
     */
    private void orderCancelDealMemberBalance(OrderPO orderPODb, String afsSn, Integer optRole, Long optUserId,
                                              String optUserName) {
        if (orderPODb.getOrderState().equals(OrderConst.ORDER_STATE_10)) {
            if (!StringUtil.isNullOrZero(orderPODb.getBalanceAmount())) {
                // 待付款，余额支付金额大于0,说明使用了余额部分支付，取消时需要解冻
                Member memberDb = memberFeignClient.getMemberByMemberId(orderPODb.getMemberId());

                Member updateMember = new Member();
                updateMember.setMemberId(memberDb.getMemberId());
                updateMember.setBalanceAvailable(memberDb.getBalanceAvailable().add(orderPODb.getBalanceAmount()));// 增加可用余额
                updateMember.setBalanceFrozen(memberDb.getBalanceFrozen().subtract(orderPODb.getBalanceAmount()));// 减少冻结金额
                memberFeignClient.updateMember(updateMember);

                // 记录余额日志
                MemberBalanceLogVO memberBalanceLogVO = new MemberBalanceLogVO();
                memberBalanceLogVO.setMemberId(memberDb.getMemberId());
                memberBalanceLogVO.setMemberName(memberDb.getMemberName());
                memberBalanceLogVO
                        .setAfterChangeAmount(updateMember.getBalanceAvailable().add(updateMember.getBalanceFrozen()));
                memberBalanceLogVO.setChangeValue(new BigDecimal("0.00"));
                memberBalanceLogVO.setFreezeAmount(updateMember.getBalanceFrozen());
                memberBalanceLogVO.setFreezeValue(orderPODb.getBalanceAmount());
                memberBalanceLogVO.setCreateTime(new Date());
                memberBalanceLogVO.setType(MemberConst.TYPE_8);
                if (optRole.equals(OrderConst.LOG_ROLE_ADMIN)) {
                    // 系统取消订单
                    memberBalanceLogVO.setDescription("支付超时，系统自动取消订单解冻余额，订单号：" + orderPODb.getOrderSn());
                    memberBalanceLogVO.setAdminId(Math.toIntExact(optUserId));
                    memberBalanceLogVO.setAdminName(optUserName);
                } else if (optRole.equals(OrderConst.LOG_ROLE_VENDOR)) {
                    // 商家取消，默人为系统操作
                    memberBalanceLogVO.setDescription("商家取消订单解冻余额，订单号：" + orderPODb.getOrderSn());
                    memberBalanceLogVO.setAdminId(0);
                    memberBalanceLogVO.setAdminName("system");
                } else {
                    memberBalanceLogVO.setDescription("会员取消订单解冻余额，订单号：" + orderPODb.getOrderSn());
                    memberBalanceLogVO.setAdminId(0);
                    memberBalanceLogVO.setAdminName(optUserName);
                }
                memberBalanceLogFeignClient.saveMemberBalanceLog(memberBalanceLogVO);

                // 发送余额变动消息通知
                this.sendMsgAccountChange(memberBalanceLogVO);
            }
            return;
        }
        /*if (optRole != OrderConst.LOG_ROLE_VENDOR) {
            //其他订单状态，只有商家可以取消订单
            throw new MallException("非法操作");
        }*/
        // 已支付的订单，商家取消，执行退款操作
        BigDecimal returnBalance;// 要退的余额
        BigDecimal returnPay = new BigDecimal("0.00");// 要退的三方金额

        // 退款方式，1为退回到余额，0为原路退回
        String value = stringRedisTemplate.opsForValue().get("refund_setting_switch");
        if ("1".equals(value)) {
            // 退回到余额
            returnBalance = orderPODb.getBalanceAmount().add(orderPODb.getPayAmount());
        } else {
            // 原路退回
            returnPay = orderPODb.getPayAmount();
            returnBalance = orderPODb.getBalanceAmount();
        }

        if (returnBalance.compareTo(BigDecimal.ZERO) > 0) {
            // 返还用户余额
            Member memberDb = memberFeignClient.getMemberByMemberId(orderPODb.getMemberId());

            Member updateMember = new Member();
            updateMember.setMemberId(memberDb.getMemberId());
            updateMember.setBalanceAvailable(memberDb.getBalanceAvailable().add(returnBalance));// 增加可用余额
            memberFeignClient.updateMember(updateMember);

            // 记录余额日志
            MemberBalanceLogVO memberBalanceLogVO = new MemberBalanceLogVO();
            memberBalanceLogVO.setMemberName(memberDb.getMemberName());
            memberBalanceLogVO.setMemberId(memberDb.getMemberId());
            memberBalanceLogVO
                    .setAfterChangeAmount(updateMember.getBalanceAvailable().add(memberDb.getBalanceFrozen()));
            memberBalanceLogVO.setChangeValue(returnBalance);
            memberBalanceLogVO.setFreezeAmount(memberDb.getBalanceFrozen());
            memberBalanceLogVO.setFreezeValue(new BigDecimal("0.00"));
            memberBalanceLogVO.setCreateTime(new Date());
            memberBalanceLogVO.setType(MemberConst.TYPE_2);
            // 商家取消，默人为系统操作
            memberBalanceLogVO.setDescription("商家取消订单返还余额，订单号：" + orderPODb.getOrderSn());
            memberBalanceLogVO.setAdminId(0);
            memberBalanceLogVO.setAdminName("system");

            memberBalanceLogFeignClient.saveMemberBalanceLog(memberBalanceLogVO);

            // 发送余额变动消息通知
            updateMember.setBalanceFrozen(memberDb.getBalanceFrozen());

            this.sendMsgAccountChange(memberBalanceLogVO);
        }

        if (returnPay.compareTo(BigDecimal.ZERO) > 0) {
            // 执行原路退回
            if (!orderPODb.getPaymentCode().contains(PayMethodEnum.ALIPAY.getValue())
                    && !orderPODb.getPaymentCode().contains(PayMethodEnum.WXPAY.getValue())) {
                throw new MallException("支付方式未开启");
            }
            try {
                Boolean refundResult =
                        payIntegration.refund(orderPODb, afsSn, returnPay, optUserId + "-" + optUserName);

                if (!refundResult) {
                }
            } catch (Exception e) {
                log.error("原路退回失败：", e);
                throw new MallException("原路退回失败");
            }
        }
    }

    /**
     * 商家取消预售订单赔偿定金
     *
     * @param orderPODb       订单
     * @param compensateValue 赔偿倍数
     */
    private void preSellOrderCancelDeal(OrderPO orderPODb, Integer optRole, Long optUserId, String optUserName,
                                        int compensateValue) {
        // 预售订单只有商家可以取消订单，如果不是，直接返回不处理
        if (optRole != OrderConst.LOG_ROLE_VENDOR) {
            return;
        }
        // 已支付的订单，商家取消，执行定金赔偿操作
        // 查询预售订单扩展表
        PresellOrderExtendExample example = new PresellOrderExtendExample();
        example.setOrderSn(orderPODb.getOrderSn());
        example.setOrderSubStateNotEquals(OrderConst.ORDER_SUB_STATE_101); // 只查已付款的
        List<PresellOrderExtendVO> orderExtendList = presellOrderExtendFeignClient.getPresellOrderExtendList(example);
        // 预售订单扩展表没有满足条件的数据，不处理直接返回
        if (CollectionUtils.isEmpty(orderExtendList)) {
            return;
        }
        PresellOrderExtendVO orderExtend = orderExtendList.get(0);
        // 支付定金
        BigDecimal compensateBalance =
                orderExtend.getDepositAmount().multiply(new BigDecimal(orderExtend.getProductNum()));
        // 大于0表示设置了定金赔偿,必须是定金预售才走这一步
        if (compensateValue > 0 && orderExtend.getIsAllPay() == OrderConst.IS_ALL_PAY_0) {
            // 返还用户余额
            Member memberDb = memberFeignClient.getMemberByMemberId(orderPODb.getMemberId());

            Member updateMember = new Member();
            updateMember.setMemberId(memberDb.getMemberId());
            // 赔偿金额=数倍定金
            BigDecimal compensateAmount = compensateBalance.multiply(new BigDecimal(compensateValue));
            updateMember.setBalanceAvailable(memberDb.getBalanceAvailable().add(compensateAmount));// 增加可用余额
            memberFeignClient.updateMember(updateMember);

            // 记录余额日志
            MemberBalanceLogVO memberBalanceLog = new MemberBalanceLogVO();
            memberBalanceLog.setMemberName(memberDb.getMemberName());
            memberBalanceLog.setMemberId(memberDb.getMemberId());
            memberBalanceLog.setAfterChangeAmount(updateMember.getBalanceAvailable().add(memberDb.getBalanceFrozen()));
            memberBalanceLog.setChangeValue(compensateAmount);
            memberBalanceLog.setFreezeAmount(memberDb.getBalanceFrozen());
            memberBalanceLog.setFreezeValue(new BigDecimal("0.00"));
            memberBalanceLog.setCreateTime(new Date());
            memberBalanceLog.setType(MemberConst.TYPE_2);
            memberBalanceLog.setDescription("商家取消预售订单赔偿" + compensateValue + "倍定金，订单号：" + orderPODb.getOrderSn());
            // 商家取消，默人为系统操作
            memberBalanceLog.setAdminId(optUserId.intValue());
            memberBalanceLog.setAdminName(optUserName);

            memberBalanceLogFeignClient.saveMemberBalanceLog(memberBalanceLog);

            // 发送余额变动消息通知
            updateMember.setBalanceFrozen(memberDb.getBalanceFrozen());
            this.sendMsgAccountChange(memberBalanceLog);

            // 赔偿记录
            PromotionPresellDepositCompensation depositCompensation = new PromotionPresellDepositCompensation();
            depositCompensation.setCompensationAmount(compensateBalance.multiply(new BigDecimal(compensateValue)));
            depositCompensation.setDepositAmount(orderExtend.getDepositAmount());
            depositCompensation.setOrderSn(orderPODb.getOrderSn());
            depositCompensation.setStoreId(orderPODb.getStoreId());
            depositCompensation.setStoreName(orderPODb.getStoreName());
            depositCompensation.setMemberId(orderPODb.getMemberId());
            depositCompensation.setMemberName(orderPODb.getMemberName());
            depositCompensation.setCreateTime(new Date());
            presellDepositCompensationFeignClient.savePresellDepositCompensation(depositCompensation);
        }
        // 不等于103说明只支付了定金，只赔偿定金就行
        if (orderExtend.getOrderSubState() != OrderConst.ORDER_SUB_STATE_103) {
            return;
        }

        // 已支付的订单，商家取消，执行退款操作（这里只退尾款）
        BigDecimal returnBalance;// 要退的余额
        BigDecimal returnPay = new BigDecimal("0.00");// 要退的三方金额

        // 退款方式，1为退回到余额，0为原路退回
        String value = stringRedisTemplate.opsForValue().get("refund_setting_switch");
        if ("1".equals(value)) {
            if (orderExtend.getIsAllPay() == OrderConst.IS_ALL_PAY_0) {
                // 定金预售退回到余额(减去支付的定金)
                returnBalance = orderPODb.getBalanceAmount().add(orderPODb.getPayAmount()).subtract(compensateBalance);
            } else {
                // 全款预售，正常全部退回余额
                returnBalance = orderPODb.getBalanceAmount().add(orderPODb.getPayAmount());
            }
        } else {
            if (orderExtend.getIsAllPay() == OrderConst.IS_ALL_PAY_0) {
                // 剩余可退的钱
                BigDecimal remainMoney =
                        orderPODb.getBalanceAmount().add(orderPODb.getPayAmount()).subtract(compensateBalance);
                // 查询尾款支付方式
                OrderPayPO orderPayPO = orderPayModel.getOrderPayByPaySn(orderPODb.getPaySn());
                // 脏数据不处理
                if (orderPayPO.getPaymentCode().equals(OrderPaymentConst.PAYMENT_CODE_ONLINE)) {
                    return;
                }
                // 定金预售原路退回（要么余额，要么现金，只退一次）
                if (orderPayPO.getPaymentCode().equals(OrderPaymentConst.PAYMENT_CODE_BALANCE)) {
                    returnBalance = remainMoney;
                    returnPay = BigDecimal.ZERO;
                } else {
                    returnPay = remainMoney;
                    returnBalance = BigDecimal.ZERO;
                }
            } else {
                // 全款预售原路退回
                returnPay = orderPODb.getPayAmount();
                returnBalance = orderPODb.getBalanceAmount();
            }
        }

        if (returnBalance.compareTo(BigDecimal.ZERO) > 0) {
            // 返还用户余额
            Member memberDb = memberFeignClient.getMemberByMemberId(orderPODb.getMemberId());

            Member updateMember = new Member();
            updateMember.setMemberId(memberDb.getMemberId());
            updateMember.setBalanceAvailable(memberDb.getBalanceAvailable().add(returnBalance));// 增加可用余额
            memberFeignClient.updateMember(updateMember);

            // 记录余额日志
            MemberBalanceLogVO memberBalanceLog = new MemberBalanceLogVO();
            memberBalanceLog.setMemberName(memberDb.getMemberName());
            memberBalanceLog.setMemberId(memberDb.getMemberId());
            memberBalanceLog.setAfterChangeAmount(updateMember.getBalanceAvailable().add(memberDb.getBalanceFrozen()));
            memberBalanceLog.setChangeValue(returnBalance);
            memberBalanceLog.setFreezeAmount(memberDb.getBalanceFrozen());
            memberBalanceLog.setFreezeValue(new BigDecimal("0.00"));
            memberBalanceLog.setCreateTime(new Date());
            memberBalanceLog.setType(MemberConst.TYPE_2);
            memberBalanceLog.setDescription("商家取消预售订单返还尾款，订单号：" + orderPODb.getOrderSn());
            memberBalanceLog.setAdminId(optUserId.intValue());
            memberBalanceLog.setAdminName(optUserName);

            memberBalanceLogFeignClient.saveMemberBalanceLog(memberBalanceLog);

            // 发送余额变动消息通知
            updateMember.setBalanceFrozen(memberDb.getBalanceFrozen());
            this.sendMsgAccountChange(memberBalanceLog);
        }

        // if (returnPay.compareTo(BigDecimal.ZERO) > 0) {
        // SlodonRefundRequest refundRequest = new SlodonRefundRequest();
        // //执行原路退回
        // if (orderDb.getPaymentCode().toLowerCase().contains(PayConst.METHOD_ALIPAY.toLowerCase())) {
        // //支付宝退款
        // refundRequest.setAlipayProperties(payPropertiesUtil.getAliPayProperties());
        // } else if (orderDb.getPaymentCode().toLowerCase().contains(PayConst.METHOD_WX.toLowerCase())) {
        // //微信退款
        // refundRequest.setWxPayProperties(payPropertiesUtil.getWxPayProperties(orderDb.getPaymentCode().replace(PayConst.METHOD_WX.toUpperCase(),
        // ""), 1));
        //
        // //微信需要设置原支付金额
        // if ("1".equals(stringRedisTemplate.opsForValue().get("wxpay_test_enable_h5"))) {
        // refundRequest.setTotalAmount(new BigDecimal("0.01"));
        // } else {
        // OrderExample orderExample = new OrderExample();
        // orderExample.setPaySn(orderDb.getPaySn());
        // List<Order> orderList = orderMapper.listByExample(orderExample);
        // BigDecimal totalPayAmount = new BigDecimal("0.00");//支付单号下总支付金额
        // for (Order order : orderList) {
        // totalPayAmount = totalPayAmount.add(order.getPayAmount());
        // }
        // refundRequest.setTotalAmount(totalPayAmount);
        // }
        // } else {
        // throw new MallException("支付方式未开启");
        // }
        // refundRequest.setPaySn(orderDb.getPaySn());
        // refundRequest.setRefundSn(orderDb.getOrderSn());
        // if ("1".equals(stringRedisTemplate.opsForValue().get("wxpay_test_enable_h5"))) {
        // refundRequest.setRefundAmount(new BigDecimal("0.01"));
        // } else {
        // refundRequest.setRefundAmount(returnPay);
        // }
        // try {
        // SlodonRefundResponse refundResponse = slodonPay.refund(refundRequest, orderDb.getPaymentCode());
        // if (!refundResponse.getSuccess()) {
        // log.error("原路退回失败：" + refundResponse.getMsg());
        // throw new MallException("原路退回失败");
        // }
        // } catch (Exception e) {
        // log.error("原路退回失败：", e);
        // throw new MallException("原路退回失败");
        // }
        // }
    }

    /**
     * 阶梯团取消订单-处理用户余额，记录余额日志
     *
     * @param orderPODb   订单
     * @param optRole     操作角色
     * @param optUserId   操作人id
     * @param optUserName 操作人名称
     */
    private void ladderGroupOrderCancelDealMemberBalance(OrderPO orderPODb, Integer optRole, Long optUserId,
                                                         String optUserName) {
        // 阶梯团订单只有商家可以取消订单，如果不是，直接返回不处理
        // 现在允许用户取消，如果用户不能取消，修改为LOG_ROLE_VENDOR
        if (optRole == OrderConst.LOG_ROLE_ADMIN) {
            return;
        }
        // 查询阶梯团订单扩展表
        LadderGroupOrderExtend orderExtendExample = new LadderGroupOrderExtend();
        orderExtendExample.setOrderSn(orderPODb.getOrderSn());
        List<LadderGroupOrderExtendVO> groupOrderExtendList =
                ladderGroupOrderExtendFeignClient.getLadderGroupOrderExtendList(orderExtendExample);
        AssertUtil.notEmpty(groupOrderExtendList, "获取阶梯团订单扩展信息为空");
        LadderGroupOrderExtendVO groupOrderExtend = groupOrderExtendList.get(0);
        if (groupOrderExtend.getOrderSubState() == LadderGroupConst.ORDER_SUB_STATE_1) {
            // 未付定金直接返回
            return;
        }
        // 查询阶梯团活动
        LadderGroupVO ladderGroup = ladderGroupFeignClient.getLadderGroupByGroupId(groupOrderExtend.getGroupId());
        AssertUtil.notNull(ladderGroup, "获取阶梯团活动信息为空");

        // 已支付的订单，商家取消，执行退款操作
        BigDecimal returnBalance;// 要退的余额
        BigDecimal returnPay = new BigDecimal("0.00");// 要退的三方金额

        // 退款方式，1为退回到余额，0为原路退回
        String value = stringRedisTemplate.opsForValue().get("refund_setting_switch");
        if ("1".equals(value)) {
            // 退回到余额
            if (ladderGroup.getIsRefundDeposit() == LadderGroupConst.IS_REFUND_DEPOSIT_0) {
                // 不退定金需要减去定金
                returnBalance = orderPODb.getBalanceAmount().add(orderPODb.getPayAmount()).subtract(
                        groupOrderExtend.getAdvanceDeposit().multiply(new BigDecimal(groupOrderExtend.getProductNum())));
            } else {
                returnBalance = orderPODb.getBalanceAmount().add(orderPODb.getPayAmount());
            }
        } else {
            // 原路退回（阶梯团不支持组合支付）
            returnPay = orderPODb.getPayAmount();
            returnBalance = orderPODb.getBalanceAmount();
        }

        if (returnBalance.compareTo(BigDecimal.ZERO) > 0) {
            // 返还用户余额
            Member memberDb = memberFeignClient.getMemberByMemberId(orderPODb.getMemberId());

            Member updateMember = new Member();
            updateMember.setMemberId(memberDb.getMemberId());
            updateMember.setBalanceAvailable(memberDb.getBalanceAvailable().add(returnBalance));// 增加可用余额
            memberFeignClient.updateMember(updateMember);

            // 记录余额日志
            MemberBalanceLogVO memberBalanceLog = new MemberBalanceLogVO();
            memberBalanceLog.setMemberName(memberDb.getMemberName());
            memberBalanceLog.setMemberId(memberDb.getMemberId());
            memberBalanceLog.setAfterChangeAmount(updateMember.getBalanceAvailable().add(memberDb.getBalanceFrozen()));
            memberBalanceLog.setChangeValue(returnBalance);
            memberBalanceLog.setFreezeAmount(memberDb.getBalanceFrozen());
            memberBalanceLog.setFreezeValue(new BigDecimal("0.00"));
            memberBalanceLog.setCreateTime(new Date());
            memberBalanceLog.setType(MemberConst.TYPE_2);
            memberBalanceLog.setDescription("商家取消阶梯团订单返还余额，订单号：" + orderPODb.getOrderSn());
            memberBalanceLog.setAdminId(optUserId.intValue());
            memberBalanceLog.setAdminName(optUserName);

            memberBalanceLogFeignClient.saveMemberBalanceLog(memberBalanceLog);

            // 发送余额变动消息通知
            updateMember.setBalanceFrozen(memberDb.getBalanceFrozen());
            this.sendMsgAccountChange(memberBalanceLog);
        }

        // if (returnPay.compareTo(BigDecimal.ZERO) > 0) {
        // OrderPay orderPay = null;
        // if (groupOrderExtend.getOrderSubState() == LadderGroupConst.ORDER_SUB_STATE_2) {
        // if (ladderGroup.getIsRefundDeposit() == LadderGroupConst.IS_REFUND_DEPOSIT_0) {
        // //不退定金
        // return;
        // }
        // //说明已付定金
        // orderPay = orderPayModel.getOrderPayByPaySn(groupOrderExtend.getDepositPaySn());
        // //定金金额以阶梯团订单扩展表为准
        // orderPay.setPayAmount(groupOrderExtend.getAdvanceDeposit().multiply(new
        // BigDecimal(groupOrderExtend.getProductNum())));
        // cancelOrderRefund(orderPay);
        // } else {
        // //进入这里说明已经付完尾款，需要退两次（定金和尾款）
        // //剩余可退的钱（减去定金）
        // BigDecimal remainMoney = orderDb.getBalanceAmount().add(orderDb.getPayAmount())
        // .subtract(groupOrderExtend.getAdvanceDeposit().multiply(new BigDecimal(groupOrderExtend.getProductNum())));
        // if (ladderGroup.getIsRefundDeposit() == LadderGroupConst.IS_REFUND_DEPOSIT_0) {
        // //不退定金，只退尾款
        // orderPay = orderPayModel.getOrderPayByPaySn(groupOrderExtend.getRemainPaySn());
        // orderPay.setPayAmount(remainMoney);
        // cancelOrderRefund(orderPay);
        // } else {
        // //定金
        // orderPay = orderPayModel.getOrderPayByPaySn(groupOrderExtend.getDepositPaySn());
        // //定金金额以阶梯团订单扩展表为准
        // orderPay.setPayAmount(groupOrderExtend.getAdvanceDeposit().multiply(new
        // BigDecimal(groupOrderExtend.getProductNum())));
        // cancelOrderRefund(orderPay);
        // //尾款
        // orderPay = orderPayModel.getOrderPayByPaySn(groupOrderExtend.getRemainPaySn());
        // orderPay.setPayAmount(remainMoney);
        // cancelOrderRefund(orderPay);
        // }
        // }
        // }
    }

    /**
     * 取消订单退还现金
     *
     * @param orderPayPO
     */
    private void cancelOrderRefund(OrderPayPO orderPayPO) {
        // SlodonRefundRequest refundRequest = new SlodonRefundRequest();
        // //执行原路退回
        // if (orderPay.getPaymentCode().toLowerCase().contains(PayConst.METHOD_ALIPAY.toLowerCase())) {
        // //支付宝退款
        // refundRequest.setAlipayProperties(payPropertiesUtil.getAliPayProperties());
        // } else if (orderPay.getPaymentCode().toLowerCase().contains(PayConst.METHOD_WX.toLowerCase())) {
        // //微信退款
        // refundRequest.setWxPayProperties(payPropertiesUtil.getWxPayProperties(orderPay.getPaymentCode().replace(PayConst.METHOD_WX.toUpperCase(),
        // ""), 1));
        //
        // //微信需要设置原支付金额
        // if ("1".equals(stringRedisTemplate.opsForValue().get("wxpay_test_enable_h5"))) {
        // refundRequest.setTotalAmount(new BigDecimal("0.01"));
        // } else {
        // refundRequest.setTotalAmount(orderPay.getPayAmount());
        // }
        // } else {
        // //如果都没有，说明是余额支付，上面已处理
        // return;
        // }
        // refundRequest.setPaySn(orderPay.getPaySn());
        // refundRequest.setRefundSn(orderPay.getOrderSn());
        // if ("1".equals(stringRedisTemplate.opsForValue().get("wxpay_test_enable_h5"))) {
        // refundRequest.setRefundAmount(new BigDecimal("0.01"));
        // } else {
        // refundRequest.setRefundAmount(orderPay.getPayAmount());
        // }
        // try {
        // SlodonRefundResponse refundResponse = slodonPay.refund(refundRequest, orderPay.getPaymentCode());
        // if (!refundResponse.getSuccess()) {
        // log.error("原路退回失败：" + refundResponse.getMsg());
        // throw new MallException("原路退回失败");
        // }
        // } catch (Exception e) {
        // log.error("原路退回失败：", e);
        // throw new MallException("原路退回失败");
        // }
    }

    /**
     * 系统自动取消12小时没有付款订单
     *
     * @return
     */
    public boolean jobSystemCancelOrder() {
        //获取自动取消时间限制（小时）
//        String value = stringRedisTemplate.opsForValue().get("time_limit_of_auto_cancle_order");
//        int limitHour = value == null ? 12 : Integer.parseInt(value);
//
//        // 获取当前时间limitHour小时之前的时间
//        Calendar calendar = Calendar.getInstance();
//        calendar.add(Calendar.HOUR, -limitHour);

        Integer minutes = getAutoCancelMinutes(OrderPatternEnum.SHOP_STREET.getValue(), OrderTypeEnum.NORMAL.getValue());

        log.info("jobSystemCancelOrder minutes:{}", minutes);

        // 获取当前时间limitHour小时之前的时间
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, -minutes);

        Date cancelTime = calendar.getTime();

        // 订单信息
        LambdaQueryWrapper<OrderPO> queryWrapper = Wrappers.lambdaQuery(OrderPO.class);
        queryWrapper.le(OrderPO::getCreateTime, cancelTime);
        queryWrapper.ne(OrderPO::getExchangeFlag, ExchangeOrderConst.ORDER_EXCHANGE_FLAG_2);
        queryWrapper.in(OrderPO::getOrderType, Arrays.asList(OrderConst.ORDER_TYPE_1, OrderConst.ORDER_TYPE_7, OrderConst.ORDER_TYPE_11, OrderConst.ORDER_TYPE_13));
        // 自提订单不走这些逻辑
        queryWrapper.ne(OrderPO::getOrderPattern, OrderPatternEnum.SELF_LIFT.getValue());
        queryWrapper.and(
                wrapper -> wrapper.eq(OrderPO::getOrderState, OrderStatusEnum.WAIT_PAY.getValue())
                        .or(
                                wrapper2 -> wrapper2.eq(OrderPO::getOrderState, OrderStatusEnum.DEAL_PAY.getValue())
                                        .eq(OrderPO::getPaymentCode, PayMethodEnum.BANK_TRANSFER.getValue())));
        queryWrapper.orderByDesc(OrderPO::getParentSn);
        List<OrderPO> parentOrderPOList = orderService.list(queryWrapper);

        Map<String, List<OrderPO>> map = parentOrderPOList.stream().collect(Collectors.groupingBy(OrderPO::getParentSn));
        if (!CollectionUtils.isEmpty(parentOrderPOList)) {
            // 已经执行取消的父订单号
            List<String> alreadyCanceledParentSn = new ArrayList<>();

            for (OrderPO orderPO : parentOrderPOList) {
                if (alreadyCanceledParentSn.contains(orderPO.getParentSn())) {
                    continue;
                }
                try {
                    //根据父订单号查询待付款的子订单
                    List<OrderPO> orderPOList = map.get(orderPO.getParentSn());
                    //取消订单
                    orderModel.cancelOrder(orderPOList, OrderConst.WAIT_PAY_ORDER_EXPIRE_REASON, null,
                            OrderConst.LOG_ROLE_ADMIN, OrderConst.LOG_USER_ID, OrderConst.LOG_USER_NAME,
                            "系统自动取消订单", OrderConst.RETURN_BY_0);
                    alreadyCanceledParentSn.add(orderPO.getParentSn());
                } catch (Exception e) {
                    log.error("系统自动取消{}分钟没有付款订单，异常继续处理后续订单，parentOrderSn:{}", minutes,
                            orderPO.getParentSn(), e);
                }
            }
        }
        return true;
    }

    /**
     * 系统自动取消没有付款的秒杀订单
     *
     * @return
     */
    public boolean jobSystemCancelSeckillOrder() {
        // 买家几分钟未支付订单，订单取消
        String value = stringRedisTemplate.opsForValue().get("seckill_order_cancle");
        int limitMinute = value == null ? 5 : Integer.parseInt(value);

        // 获取当前时间limitHour小时之前的时间
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, -limitMinute);

        Date cancelTime = calendar.getTime();

        // 获取超时未付款的订单，此处查询的都是待付款状态的父订单
        OrderExample example = new OrderExample();
        example.setCreateTimeBefore(cancelTime);
        example.setOrderState(OrderConst.ORDER_STATE_10);
        example.setOrderType(PromotionConst.PROMOTION_TYPE_104);// 秒杀订单
        example.setOrderBy("parent_sn");// 按照父订单号分组
        List<OrderPO> parentOrderPOList = orderMapper.listByExample(example);

        if (!CollectionUtils.isEmpty(parentOrderPOList)) {
            parentOrderPOList.forEach(parentOrder -> {
                // 根据父订单号查询待付款的子订单
                OrderExample example1 = new OrderExample();
                example1.setParentSn(parentOrder.getParentSn());
                example1.setOrderState(OrderConst.ORDER_STATE_10);
                List<OrderPO> orderPOList = orderMapper.listByExample(example1);

                // 取消订单
                orderModel.cancelOrder(orderPOList, "支付超时系统自动取消秒杀订单", null, OrderConst.LOG_ROLE_ADMIN, 0L, "system",
                        "系统自动取消秒杀订单", OrderConst.RETURN_BY_0);
            });
        }
        return true;
    }

    /**
     * 系统自动取消没有付款的阶梯团订单
     *
     * @return
     */
    public boolean jobSystemCancelLadderGroupOrder() {
        // 买家几分钟未支付订单，订单取消
        String value = stringRedisTemplate.opsForValue().get("ladder_group_deposit_order_auto_cancel_time");
        int limitMinute = value == null ? 30 : Integer.parseInt(value);

        // 获取当前时间limitMinute分钟之前的时间
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, -limitMinute);

        Date cancelTime = calendar.getTime();

        LadderGroupOrderExtend extendExample = new LadderGroupOrderExtend();
        extendExample.setOrderSubState(LadderGroupConst.ORDER_SUB_STATE_1);
        extendExample.setParticipateTimeBefore(cancelTime);
        List<LadderGroupOrderExtendVO> orderExtendList =
                ladderGroupOrderExtendFeignClient.getLadderGroupOrderExtendList(extendExample);
        log.info("支付超时系统自动取消阶梯团订单:" + orderExtendList.size());
        if (CollectionUtils.isEmpty(orderExtendList)) {
            return true;
        }
        // 拼接订单号
        StringBuilder orderSns = new StringBuilder();
        for (LadderGroupOrderExtendVO orderExtend : orderExtendList) {
            orderSns.append(",").append(orderExtend.getOrderSn());
        }
        String orderSnIn = orderSns.substring(1);
        // 查询订单列表
        OrderExample orderExample = new OrderExample();
        orderExample.setOrderSnIn("'" + orderSnIn.replace(",", "','") + "'");
        orderExample.setOrderState(OrderConst.ORDER_STATE_10);
        List<OrderPO> orderPOList = orderMapper.listByExample(orderExample);
        // 取消订单
        orderModel.cancelOrder(orderPOList, "支付超时系统自动取消阶梯团订单", null, OrderConst.LOG_ROLE_ADMIN, 0L, "system", "系统自动取消阶梯团订单",
                OrderConst.RETURN_BY_0);
        // 修改扩展表状态
        LadderGroupOrderExtend groupOrderExtend = new LadderGroupOrderExtend();
        groupOrderExtend.setOrderSubState(LadderGroupConst.ORDER_SUB_STATE_0);
        ladderGroupOrderExtendFeignClient.updateLadderGroupOrderExtendByOrderSn(groupOrderExtend, orderSnIn);
        return true;
    }

    /**
     * 系统自动取消没有付款的阶梯团尾款订单
     *
     * @return
     */
    public boolean jobSystemCancelLadderGroupBalanceOrder() {
        LadderGroupOrderExtend extendExample = new LadderGroupOrderExtend();
        extendExample.setOrderSubState(LadderGroupConst.ORDER_SUB_STATE_2);
        extendExample.setRemainEndTimeBefore(new Date());
        List<LadderGroupOrderExtendVO> orderExtendList =
                ladderGroupOrderExtendFeignClient.getLadderGroupOrderExtendList(extendExample);
        log.info("支付超时系统自动取消阶梯团尾款订单:" + orderExtendList.size());
        if (CollectionUtils.isEmpty(orderExtendList)) {
            return true;
        }
        // 拼接订单号
        StringBuilder orderSns = new StringBuilder();
        for (LadderGroupOrderExtendVO orderExtend : orderExtendList) {
            orderSns.append(",").append(orderExtend.getOrderSn());
        }
        String orderSnIn = orderSns.substring(1);
        // 查询订单列表
        OrderExample orderExample = new OrderExample();
        orderExample.setOrderSnIn("'" + orderSnIn.replace(",", "','") + "'");
        orderExample.setOrderState(OrderConst.ORDER_STATE_10);
        List<OrderPO> orderPOList = orderMapper.listByExample(orderExample);
        // 取消订单
        orderModel.cancelOrder(orderPOList, "支付超时系统自动取消阶梯团尾款订单", null, OrderConst.LOG_ROLE_ADMIN, 0L, "system",
                "系统自动取消阶梯团尾款订单", OrderConst.RETURN_BY_0);
        // 修改扩展表状态
        LadderGroupOrderExtend groupOrderExtend = new LadderGroupOrderExtend();
        groupOrderExtend.setOrderSubState(LadderGroupConst.ORDER_SUB_STATE_0);
        ladderGroupOrderExtendFeignClient.updateLadderGroupOrderExtendByOrderSn(groupOrderExtend, orderSnIn);
        return true;
    }

    /**
     * 系统自动取消没有付款的预售订单
     *
     * @return
     */
    public boolean jobSystemCancelPreSellOrder() {
        // 买家几分钟未支付订单，订单取消
        String value = stringRedisTemplate.opsForValue().get("deposit_order_auto_cancel_time");
        int limitMinute = value == null ? 30 : Integer.parseInt(value);

        // 获取当前时间limitMinute分钟之前的时间
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, -limitMinute);

        Date cancelTime = calendar.getTime();

        PresellOrderExtendExample extendExample = new PresellOrderExtendExample();
        extendExample.setOrderSubState(OrderConst.ORDER_SUB_STATE_101);
        extendExample.setCreateTimeBefore(cancelTime);
        List<PresellOrderExtendVO> orderExtendList =
                presellOrderExtendFeignClient.getPresellOrderExtendList(extendExample);
        log.info("支付超时系统自动取消预售订单:" + orderExtendList.size());
        if (CollectionUtils.isEmpty(orderExtendList)) {
            return true;
        }
        // 拼接订单号
        StringBuilder orderSns = new StringBuilder();
        for (PresellOrderExtendVO orderExtend : orderExtendList) {
            orderSns.append(",").append(orderExtend.getOrderSn());
        }
        String orderSnIn = orderSns.substring(1);
        // 查询订单列表
        OrderExample orderExample = new OrderExample();
        orderExample.setOrderSnIn("'" + orderSnIn.replace(",", "','") + "'");
        orderExample.setOrderState(OrderConst.ORDER_STATE_10);
        List<OrderPO> orderPOList = orderMapper.listByExample(orderExample);
        // 取消订单
        orderModel.cancelOrder(orderPOList, "支付超时系统自动取消预售订单", null, OrderConst.LOG_ROLE_ADMIN, 0L, "system", "系统自动取消预售订单",
                OrderConst.RETURN_BY_0);
        // 修改扩展表状态
        PromotionPresellOrderExtend orderExtend = new PromotionPresellOrderExtend();
        orderExtend.setOrderSubState(OrderConst.ORDER_STATE_0);
        presellOrderExtendFeignClient.updatePreSellOrderExtendByOrderSn(orderExtend, orderSnIn);
        return true;
    }

    /**
     * 系统自动取消没有付款的预售尾款订单
     *
     * @return
     */
    public boolean jobSystemCancelPreSellBalanceOrder() {
        log.error("无效方法，禁止调用");
        /*PresellOrderExtendExample extendExample = new PresellOrderExtendExample();
        extendExample.setOrderSubState(OrderConst.ORDER_SUB_STATE_102);
        extendExample.setRemainEndTimeBefore(new Date());
        List<PresellOrderExtendVO> orderExtendList =
                presellOrderExtendFeignClient.getPresellOrderExtendList(extendExample);
        log.info("支付超时系统自动取消预售尾款订单:" + orderExtendList.size());
        if (CollectionUtils.isEmpty(orderExtendList)) {
            return true;
        }
        // 拼接订单号
        StringBuilder orderSns = new StringBuilder();
        for (PresellOrderExtendVO orderExtend : orderExtendList) {
            orderSns.append(",").append(orderExtend.getOrderSn());
        }
        String orderSnIn = orderSns.substring(1);
        // 查询订单列表
        OrderExample orderExample = new OrderExample();
        orderExample.setOrderSnIn("'" + orderSnIn.replace(",", "','") + "'");
        orderExample.setOrderState(OrderConst.ORDER_STATE_10);
        List<OrderPO> orderPOList = orderMapper.listByExample(orderExample);
        // 取消订单
        orderModel.cancelOrder(orderPOList, "支付超时系统自动取消预售尾款订单", null, OrderConst.LOG_ROLE_ADMIN, 0L, "system", "系统自动取消预售尾款订单",
                OrderConst.RETURN_BY_0);
        // 修改扩展表状态
        PromotionPresellOrderExtend orderExtend = new PromotionPresellOrderExtend();
        orderExtend.setOrderSubState(OrderConst.ORDER_STATE_0);
        presellOrderExtendFeignClient.updatePreSellOrderExtendByOrderSn(orderExtend, orderSnIn);*/
        return true;
    }

    /**
     * 系统自动取消没有付款的拼团订单
     *
     * @return
     */
    public boolean jobSystemCancelSpellOrder() {
        // 买家多少分钟内未支付订单，订单取消
        String value = stringRedisTemplate.opsForValue().get("spell_order_auto_cancel_time");
        int limitMinute = value == null ? 30 : Integer.parseInt(value);

        // 获取当前时间limitMinute分钟之前的时间
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, -limitMinute);
        Date cancelTime = calendar.getTime();

        // 获取超时未付款的订单
        OrderExample example = new OrderExample();
        example.setCreateTimeBefore(cancelTime);
        example.setOrderState(OrderConst.ORDER_STATE_10);
        example.setOrderType(PromotionConst.PROMOTION_TYPE_102);
        List<OrderPO> orderPOList = orderMapper.listByExample(example);

        // 取消订单
        orderModel.cancelOrder(orderPOList,
                SentenceConst.SYSTEM_AUTO_CANCEL_SPELL_FAIL_ORDER, null, OrderConst.LOG_ROLE_ADMIN,
                OrderConst.LOG_USER_ID, OrderConst.LOG_USER_NAME, SentenceConst.SYSTEM_AUTO_CANCEL_SPELL_FAIL_ORDER,
                OrderConst.RETURN_BY_0);

        return true;
    }

    /**
     * 系统自动取消没有付款的新人订单
     *
     * @return
     */
    public boolean jobSystemCancelNewPeopleOrder() {
        // 买家多少分钟内未支付订单，订单取消
        String value = stringRedisTemplate.opsForValue().get("exclusive_order_cancle");
        int limitMinute = value == null ? 30 : Integer.parseInt(value);

        // 获取当前时间limitMinute分钟之前的时间
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, -limitMinute);

        Date cancelTime = calendar.getTime();

        // 获取超时未付款的订单
        OrderExample example = new OrderExample();
        example.setCreateTimeBefore(cancelTime);
        example.setOrderState(OrderConst.ORDER_STATE_10);
        example.setOrderType(PromotionConst.PROMOTION_TYPE_106);
        List<OrderPO> orderPOList = orderMapper.listByExample(example);
        // 取消订单
        orderModel.cancelOrder(orderPOList, "支付超时系统自动取消新人订单", null, OrderConst.LOG_ROLE_ADMIN, 0L, "system", "系统自动取消订单新人订单",
                OrderConst.RETURN_BY_0);
        // 营销处理取消的订单
        orderPOList.forEach(order -> {
            exclusiveFeignClient.cancelExclusiveOrder(order.getOrderSn());
        });
        // 营销处理取消的订单
        return true;
    }

    /**
     * 处理拼团失效
     *
     * @return
     */
    public boolean handleFailSpellTeam() {
        distributeLock.lockAndProcess(OrderCancelSpellRefundJob.SPELL_CANCEL_LOCK_KEY, 0, 60, TimeUnit.SECONDS, () -> {
            // 获取拼团超过截团时间的订单，包括需要成功的和失败的
            JsonResult<Map<String, List<String>>> orders = spellTeamFeignClient.getSpellTeamNeededCancel();

            if (Objects.isNull(orders.getData())) {
                return null;
            }

            log.info("拼团超时获取的订单信息为：【{}】", orders.getData().toString());

            // 需要取消的订单
            List<String> cancelOrderSns = orders.getData().get(OrderCancelSpellRefundJob.CANCEL_ORDER_SN);
            // 需要完成的订单
            List<String> simulateOrderSn = orders.getData().get(OrderCancelSpellRefundJob.SIMULATE_ORDER_SN);

            // 取消订单
            if (!CollectionUtils.isEmpty(cancelOrderSns)) {
                orderReturnService.cancelSpellOrders(cancelOrderSns);
            }

            // 完成订单
            if (!CollectionUtils.isEmpty(simulateOrderSn)) {
                orderService.setOrdersDeliverable(simulateOrderSn);
            }

            return null;
        });

        return Boolean.TRUE;
    }

    /**
     * 确认收货 1.修改订单状态 2.记录订单日志 3.交易成功：更新对账明细
     *
     * @param orderPODb   订单
     * @param optRole     操作人角色
     * @param optUserId   操作人id
     * @param optUserName 操作人名称
     * @param optRemark   操作备注
     * @param channel     渠道
     */
    @GlobalTransactional
    public void receiveOrder(OrderPO orderPODb, Integer optRole, Long optUserId, String optUserName, String optRemark, OrderCreateChannel channel) {
        if (OrderStatusEnum.TRADE_SUCCESS.isTrue(orderPODb.getOrderState())) {
            return;
        }

        BizAssertUtil.isTrue(!OrderStatusEnum.WAIT_RECEIPT.isTrue(orderPODb.getOrderState()), "未到收货状态");
        log.info("【确认收货】orderSn：{}", orderPODb.getOrderSn());

        // 检查订单签收文件资料是否上传
        Boolean isUpload = orderTradeProofService.checkOrderProofUpload(orderPODb.getOrderSn(), null, ProofSceneEnum.RECEIVE);
        if (!isUpload) {
            throw new BusinessException("请先上传订单对应的签收资料！");
        }

        //交易完成
        orderModel.orderFinishSuccess(orderPODb, optRole, optUserId, optUserName, optRemark, channel, Boolean.TRUE);
    }

    /**
     * 自动交易成功，无需收货
     */
    @Transactional(rollbackFor = Exception.class, transactionManager = "masterdbTx")
    public void finishOrder(String orderSn) {
        // 1.修改订单状态
        LambdaUpdateWrapper<OrderPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OrderPO::getOrderSn, orderSn)
                .eq(OrderPO::getOrderState, OrderStatusEnum.WAIT_DELIVER.getValue())
                .set(OrderPO::getOrderState, OrderStatusEnum.WAIT_RECEIPT.getValue());
        boolean update = orderService.update(updateWrapper);
        if (!update) {
            return;
        }

        // 修改订单商品发货状态
        orderProductService.orderProductDelivery(orderSn);

        OrderPO orderByOrderSn = getOrderByOrderSn(orderSn);
        receiveOrder(orderByOrderSn, OrderConst.LOG_ROLE_ADMIN, OrderConst.USER_ID_SYSTEM, OrderConst.LOG_USER_NAME, "卡券包订单无需收货，自动交易成功", OrderCreateChannel.WEB);

    }


    public void loanPayFail(OrderPO orderPODb, String failureReason) {

        // 修改当前订单状态，loanPayState状态为起息申请失败
        OrderPO orderPOUpdate = new OrderPO();
        orderPOUpdate.setLoanPayState(LoanStatusEnum.WAIT_LENDING.getValue());
        OrderExample orderExampleUpdate = new OrderExample();
        orderExampleUpdate.setOrderSn(orderPODb.getOrderSn());
        updateOrderByExample(orderPOUpdate, orderExampleUpdate);
        //1.记录订单放款失败日志
        orderLogModel.insertOrderLog(
                OrderConst.LOG_ROLE,
                OrderConst.LOG_USER_ID,
                OrderConst.LOG_USER_NAME,
                orderPODb.getOrderSn(),
                orderPODb.getOrderState(),
                OrderConst.ORDER_STATE_70,
                LoanStatusEnum.WAIT_LENDING.getValue(),
                "放款申请失败",
                OrderCreateChannel.WEB
        );

    }

    public void loanPaySuccess(OrderPO orderPO, Integer optRole, Long optUserId, String optUserName) {
        //1.记录订单放款申请成功日志
        orderLogModel.insertOrderLog(
                optRole,
                optUserId,
                optUserName,
                orderPO.getOrderSn(),
                orderPO.getOrderState(),
                OrderConst.ORDER_STATE_60,
                LoanStatusEnum.DEAL_LENDING.getValue(),
                "放款申请成功",
                OrderCreateChannel.WEB
        );
        //2.修改当前订单状态
        OrderPO orderPOUpdate = new OrderPO();
        orderPOUpdate.setLoanPayState(LoanStatusEnum.DEAL_LENDING.getValue());
        OrderExample orderExampleUpdate = new OrderExample();
        orderExampleUpdate.setOrderSn(orderPO.getOrderSn());
        updateOrderByExample(orderPOUpdate, orderExampleUpdate);
        // 根据paySn查询所有的订单，判断是否所有的订单状态为放款成功，全为放款成功，则更新orderPay，否则不更新
        /* OrderExample orderExample = new OrderExample();
        orderExample.setPaySn(order.getPaySn());
        List<Order> orderList = getOrderList(orderExample, null);*/
        // 1.修改OrderPay状态
        OrderPayPO updateOrderPayPO = new OrderPayPO();
        // 0-未放款 1-放款中 2-放款成功
        updateOrderPayPO.setLoanSuccess(1);
        OrderPayExample orderPayExample = new OrderPayExample();
        orderPayExample.setPaySn(orderPO.getPaySn());
        orderPayMapper.updateByExampleSelective(updateOrderPayPO, orderPayExample);
    }

    // public Result<Void> doLoanOperate(String orderSn, Long optUserId) {
    // return doLoanOperate(orderSn, optUserId,null);
    // }

    public Result<Void> doLoanOperate(String orderSn, Long optUserId, String payNo) {
        log.info("信贷放款【doLoanOperate】orderSn->{} payNo:{}", orderSn, payNo);
        AssertUtil.isTrue(StringUtil.isBlank(orderSn), "orderSn不能为空！");
        Result<Void> voidResult = null;
        // 查询order信息
        OrderExample orderExample = new OrderExample();
        orderExample.setOrderSn(orderSn);
        List<OrderPO> orderPOS = orderMapper.listByExample(orderExample);
        OrderPO orderPODb = orderPOS.get(0);
        LendingCdMallApplyRequest lendingCdMallApplyRequest = new LendingCdMallApplyRequest();
        LoanInfoDTO loanInfo = payService.buildLoanInfo(orderPODb, payNo);
        log.info("放款信息loanInfo：{}", loanInfo);
        lendingCdMallApplyRequest.setLendingAmount(loanInfo.getAmount());// 信贷放款金额，Andy。2022.05.20.修改
        lendingCdMallApplyRequest.setChannel(loanInfo.getLoanOrderSn());
        lendingCdMallApplyRequest.setOrderBatchId(loanInfo.getLoanOrderSn());
        // 从客户中心获取CustId
        try {
            Member member = memberFeignClient.getMemberByMemberId(orderPODb.getMemberId());
            QueryUserBaseInfoReq queryUserBaseInfoReq = new QueryUserBaseInfoReq();
            queryUserBaseInfoReq.setUserNo(member.getUserNo());
            Result<UserBaseInfoVo> userBaseInfoVoResult = customerServiceFeign.baseInfoByUserNo(queryUserBaseInfoReq);
            if (!userBaseInfoVoResult.isSuccess() || Objects.isNull(userBaseInfoVoResult.getData())) {
                throw new MallException("执行放款时，调用客户中心获取客户信息异常");
            }
            UserBaseInfoVo data = userBaseInfoVoResult.getData();
            if (Objects.isNull(data.getCustInfoVo()) ||
                    Objects.isNull(data.getCustInfoVo().getCustDetail()) ||
                    Objects.isNull(data.getCustInfoVo().getCustDetail().getLoanCustId())) {
                throw new MallException("执行放款时、调用客户中心获取客户信息为空");
            }
            CustDetailVo custDetail = data.getCustInfoVo().getCustDetail();
            lendingCdMallApplyRequest.setLoanCustId(custDetail.getLoanCustId());
        } catch (Exception e) {
            log.error("执行放款时，调用客户中心获取客户信息异常", e);
            throw new MallException("调用客户中心服务异常,请重试!");
        }
        lendingCdMallApplyRequest.setOrderDetailId(loanInfo.getLoanOrderSn());// Andy.修改
        OrderProductExample orderProductExample = new OrderProductExample();
        orderProductExample.setOrderSn(orderPODb.getOrderSn());
        List<OrderProductPO> orderProductPOList = orderProductModel.getOrderProductList(orderProductExample, null);
        OrderProductPO orderProductPO = orderProductPOList.get(0);
        // 商家收款卡号需要从mall-shop获取
        StoreContractReceiptInfoVO contractReceipt =
                storeFeignClient.getStoreContractReciptInfo(orderProductPO.getStoreId());
        // 获取引荐商户收款卡信息
        if (orderPODb.getNewOrder()) {
            AccountCard accountCard = billOperatinIntegration.detailByBankAccount(AccountConstans.UNI_PLF_STORE_ID,
                    AccountCardTypeEnum.UNI_JS_PLF_SUP);
            lendingCdMallApplyRequest.setMerchantPaymentCard(accountCard.getLoanCardId());
        } else {
            StoreContractReceiptInfoVO storeContractRecommend =
                    storeFeignClient.getStoreContractReciptInfo(contractReceipt.getRecommentBusiness());
            lendingCdMallApplyRequest.setMerchantPaymentCard(storeContractRecommend.getStore().getAcctId());
        }
        lendingCdMallApplyRequest.setMerchantId(String.valueOf(orderProductPO.getStoreId()));
        lendingCdMallApplyRequest.setChannel("CMIS");
        lendingCdMallApplyRequest.setOperator(String.valueOf(optUserId));
        lendingCdMallApplyRequest.setPlanLoanDate(DateUtil.format(new Date(), DateUtil.FORMAT_DATE));
        // 从客户中心获取订单使用的优惠券code 从goods获取用呗免息券批次号
        Set<String> couponBatchs = new HashSet<>();
        for (OrderProductPO orderProductPODb : orderProductPOList) {
            Integer isGift = orderProductPODb.getIsGift();
            if (Objects.nonNull(isGift) && OrderConst.IS_GIFT_YES == isGift) {
                // 赠品不判断金融规则
                continue;
            }
            ProductFinanceVO byCode = productFinanceGoodsLabelFeignClient
                    .getProductFinanceRuleLabelByCode(orderProductPODb.getFinanceRuleCode());
            couponBatchs.add(Objects.isNull(byCode) ? null : byCode.getCouponBatch());
        }
        if (couponBatchs.size() > 1) {
            // 同一家店铺的商品优惠券批次号只能一个
            log.error("优惠券批次号,{}", JSON.toJSONString(couponBatchs));
            throw new MallException("同一家店铺订单优惠券批次号不唯一,请检查重试!");
        }
        List<String> promotionBatchNoList = new ArrayList<>(couponBatchs);
        lendingCdMallApplyRequest.setPromotionBatchNoList(promotionBatchNoList);
        // 调用pay服务可能会调用异常，直接抛出，上层业务捕获处理
        log.info("調用payment服務放款，{}", JSON.toJSONString(lendingCdMallApplyRequest));
        voidResult = mallPaymentFacade.paymentLoanLending(lendingCdMallApplyRequest);
        if (!voidResult.isSuccess() && voidResult.getCode().equals("002400002")) {
            voidResult.setSuccess(true);
        }

        // 记录放款结果日志
        LoanResultPO resultPo = new LoanResultPO();
        resultPo.setPayNo(orderPODb.getOrderSn());
        if (voidResult.isSuccess()) {
            resultPo.setLoanResult(LoanResultPO.LOAN_RESULT_P);
            resultPo.setFailureReason("");
        } else {
            resultPo.setLoanResult(LoanResultPO.LOAN_RESULT_N);
            resultPo.setFailureReason(voidResult.getErrorMsg());
            resultPo.setFailureType(LoanFailureTypeEnum.APPLY_FAILURE.getCode());
        }
        resultPo.setCreateBy(TaskConstant.DEFAULT_JOB_NUMBER);
        resultPo.setUpdateBy(TaskConstant.DEFAULT_JOB_NUMBER);
        resultPo.setEnabledFlag(1);
        LambdaUpdateWrapper<LoanResultPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(LoanResultPO::getPayNo, orderPODb.getOrderSn()).setSql("retry_times = retry_times + 1");
        loanResultService.saveOrUpdate(resultPo, updateWrapper);

        return voidResult;
    }

    /**
     * 系统自动完成订单
     */
    public boolean jobSystemFinishOrder() {
        // 非贷款类支付 且 无售后中的订单 且 非换货订单
        List<OrderPO> orderList = orderMapper.getAutoDeliveryOrders();
        if (CollectionUtils.isEmpty(orderList)) {
            return true;
        }

        for (OrderPO orderPo : orderList) {
            // 过滤用呗支付且带金融规则的订单
            if (PayMethodEnum.isLoanPay(PayMethodEnum.valueOf(orderPo.getPaymentCode()))
                    && Strings.isNotBlank(orderPo.getFinanceRuleCode())) {
                continue;
            }
            // 过滤退款中的订单
            if (orderReturnModel.whetherHasReturningProduct(orderPo.getOrderSn())) {
                continue;
            }
            // 过滤换货后的销售订单
            if (ExchangeOrderConst.ORDER_EXCHANGE_FLAG_2 == orderPo.getExchangeFlag()) {
                continue;
            }
            try {
                this.receiveOrder(orderPo, OrderConst.LOG_ROLE_ADMIN, OrderConst.LOG_USER_ID, OrderConst.LOG_USER_NAME,
                        SentenceConst.SYSTEM_AUTO_FINISH_ORDER, OrderCreateChannel.WEB);
            } catch (Exception e) {
                log.warn("订单【{}】自动收货失败，请检测", orderPo.getOrderSn(), e);
            }
            log.info("jobSystemFinishOrder orderSn = {}", orderPo.getOrderSn());
        }

        return true;
    }
    // endregion

    /**
     * 订单量统计
     *
     * @param example
     * @return
     */
    public List<OrderDayDTO> getOrderDayDto(OrderExample example) {
        return orderMapper.getOrderDayDto(example);
    }

    /**
     * @param orderCommentAddDTO
     * @return
     */
    @GlobalTransactional
    public Boolean addOrderComment(OrderCommentAddDTO orderCommentAddDTO, Member member) throws Exception {

        OrderPO orderPO = getOrdersWithOpByOrderSn(orderCommentAddDTO.getOrderSn());
        // 1.查询是否评价店铺,如果为空则插入数据
        StoreCommentExample example = new StoreCommentExample();
        example.setMemberId(member.getMemberId());
        example.setOrderSn(orderCommentAddDTO.getOrderSn());
        List<StoreComment> storeCommentList = storeCommentFeignClient.getStoreCommentList(example);
        if (CollectionUtils.isEmpty(storeCommentList)) {
            StoreComment storeComment = new StoreComment();
            PropertyUtils.copyProperties(storeComment, orderCommentAddDTO);
            storeComment.setMemberId(member.getMemberId());
            storeComment.setMemberName(member.getMemberName());
            storeComment.setStoreId(orderPO.getStoreId());
            storeComment.setStoreName(orderPO.getStoreName());
            storeComment.setCreateTime(new Date());
            storeComment.setChannel(orderPO.getChannel());
            storeCommentFeignClient.saveStoreComment(storeComment);
        }

        // 评价赠送积分
        Integer evaluateIntegral =
                Integer.parseInt(stringRedisTemplate.opsForValue().get("integral_present_order_evaluate"));

        // 2.新增商品评论
        // 2.1获取商品评论集合
        List<OrderCommentAddDTO.GoodsCommentInfo> goodsCommentInfoList = orderCommentAddDTO.getGoodsCommentInfoList();
        // 2.2遍历,然后新增商品评论
        for (OrderCommentAddDTO.GoodsCommentInfo goodsCommentInfo : goodsCommentInfoList) {
            // 通过订单货品明细id获取订单货品明细信息
            OrderProductPO orderProductPO = orderProductMapper.getByPrimaryKey(goodsCommentInfo.getOrderProductId());
            // 新增商品评论
            GoodsComment goodsComment = new GoodsComment();
            goodsComment.setMemberId(member.getMemberId());
            goodsComment.setMemberName(member.getMemberName());
            goodsComment.setScore(goodsCommentInfo.getScore());
            goodsComment.setContent(goodsCommentInfo.getContent());
            goodsComment.setGoodsId(orderProductPO.getGoodsId());
            goodsComment.setGoodsName(orderProductPO.getGoodsName());
            goodsComment.setProductId(orderProductPO.getProductId());
            goodsComment.setSpecValues(orderProductPO.getSpecValues());
            goodsComment.setCreateTime(new Date());
            goodsComment.setStoreId(orderProductPO.getStoreId());
            goodsComment.setStoreName(orderProductPO.getStoreName());
            goodsComment.setOrderSn(orderCommentAddDTO.getOrderSn());
            goodsComment.setOrderProductId(goodsCommentInfo.getOrderProductId());
            goodsComment.setImage(goodsCommentInfo.getImage());
            goodsComment.setState(GoodsConst.COMMENT_AUDIT);
            goodsComment.setChannel(orderPO.getChannel());
            ExternalApiUtil.callJsonResultApi(
                    () -> goodsCommentFeignClient.saveGoodsComment(goodsComment),
                    goodsComment,
                    "/v1/feign/goods/goodsComment/addGoodsComment",
                    "保存商品评论");

            // 获取该订单货品明细所对应订单下所有未评价的订单货品明细
            OrderProductExample productExample = new OrderProductExample();
            productExample.setOrderSn(orderCommentAddDTO.getOrderSn());
            productExample.setIsComment(OrderProductConst.IS_EVALUATE_0);
            productExample.setOrderProductIdNotEquals(goodsCommentInfo.getOrderProductId());// 排除此次评价的订单
            List<OrderProductPO> ordersGoodsList = orderProductMapper.listByExample(productExample);
            if (CollectionUtils.isEmpty(ordersGoodsList)) {
                // 该订单下的所有货品都已评价，修改订单评价时间
                OrderExtendExample extendExample = new OrderExtendExample();
                extendExample.setOrderSn(orderCommentAddDTO.getOrderSn());
                List<OrderExtendPO> orderExtendPOList = orderExtendMapper.listByExample(extendExample);
                AssertUtil.notEmpty(orderExtendPOList, "查询订单扩展信息失败");
                OrderExtendPO dbExtend = orderExtendPOList.get(0);

                OrderExtendPO updateExtend = new OrderExtendPO();
                updateExtend.setExtendId(dbExtend.getExtendId());
                updateExtend.setEvaluationTime(new Date());
                int update = orderExtendMapper.updateByPrimaryKeySelective(updateExtend);
                AssertUtil.isTrue(update == 0, "更新订单扩展信息失败");

                // 更新订单评价状态为全部评价
                OrderPO updateOrders = new OrderPO();
                updateOrders.setEvaluateState(OrderConst.EVALUATE_STATE_3);
                OrderExample orderExample = new OrderExample();
                orderExample.setOrderSn(orderCommentAddDTO.getOrderSn());
                update = orderMapper.updateByExampleSelective(updateOrders, orderExample);
                AssertUtil.isTrue(update == 0, "更新订单评价状态失败");

            } else {
                // 还有未评价的订单货品，更新订单评价状态为部分评价
                OrderPO updateOrders = new OrderPO();
                updateOrders.setEvaluateState(OrderConst.EVALUATE_STATE_2);
                OrderExample orderExample = new OrderExample();
                orderExample.setOrderSn(orderCommentAddDTO.getOrderSn());
                int update = orderMapper.updateByExampleSelective(updateOrders, orderExample);
                AssertUtil.isTrue(update == 0, "更新订单评价状态失败");
            }

            // 修改订单货品明细评论状态
            OrderProductPO updateProduct = new OrderProductPO();
            updateProduct.setOrderProductId(goodsCommentInfo.getOrderProductId());
            updateProduct.setIsComment(OrderProductConst.IS_EVALUATE_1);
            updateProduct.setCommentTime(new Date());
            int upOrderProduct = orderProductMapper.updateByPrimaryKeySelective(updateProduct);
            AssertUtil.isTrue(upOrderProduct == 0, "修改订单货品明细评价失败，请重试");

            // 查询商品信息
            Goods goodsDb = goodsFeignClient.getGoodsByGoodsId(orderProductPO.getGoodsId());
            // 更新商品评论数量
            Goods goods = new Goods();
            goods.setGoodsId(goodsDb.getGoodsId());
            goods.setCommentNumber(goodsDb.getCommentNumber() + 1);
            goodsFeignClient.updateGoods(goods);
        }

        // 发送评论通知
        orderCreateHelper.addOrderChangeEvent(orderPO, OrderEventEnum.EVALUATE, new Date());

        return true;
    }

    /**
     * 获取导出订单列表
     *
     * @param example
     * @return
     */
    public List<OrderExportDTO> getExportOrderList(OrderExample example) {
        return orderMapper.getOrderExportList(example);
    }
    // endregion 订单导出

    public List<OrderPO> selectByOrderSnOrpaySn(@Param("example") OrderExample example) {
        return orderMapper.selectByOrderSnOrpaySn(example);
    }

    /**
     * 查询地址信息
     */
    public Map<Integer, Express> getExpressList(ExpressExample expressExample) {
        Map<Integer, Express> id2Express = new HashMap<>();
        List<Express> expressList = expressFeignClient.getExpressList(expressExample);
        expressList.forEach(express -> id2Express.put(express.getExpressId(), express));
        return id2Express;
    }

    /**
     * 根据订单号查询订单信息（以 MyBatisPlus 形式查询）
     *
     * @param orderSn 订单号
     * @return 订单信息
     */
    public OrderPO getOrderByOrderSnLambda(String orderSn) {
        LambdaQueryWrapper<OrderPO> queryWrapper = Wrappers.lambdaQuery(OrderPO.class);
        queryWrapper.eq(OrderPO::getOrderSn, orderSn).eq(OrderPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
        return orderMapper.selectOne(queryWrapper);
    }

    /**
     * 查询所有订单（包括已删除的）
     *
     * @param orderSn 订单号
     * @return 订单
     */
    public OrderPO getAllOrderByOrderSnLambda(String orderSn) {
        LambdaQueryWrapper<OrderPO> queryWrapper = Wrappers.lambdaQuery(OrderPO.class);
        queryWrapper.eq(OrderPO::getOrderSn, orderSn);
        return orderMapper.selectOne(queryWrapper);
    }

    public Map<String, OrderPresellDTO> getPreSellOrderDetailByOrderSnList(List<String> orderList) {
        Map<String, OrderPresellDTO> orderPresellDTOMap = new HashMap<>();
        if (CollectionUtils.isEmpty(orderList)) {
            return orderPresellDTOMap;
        }
        List<OrderPresellPO> list = orderPresellMapper.getPreSellOrderDetailByOrderSnList(orderList);
        log.info("getPreSellOrderDetailByOrderSnList list = {}", JSONObject.toJSONString(list));
        Map<String, List<OrderPresellPO>> preSellOrderMap = list.stream().collect(Collectors.groupingBy(OrderPresellPO::getOrderSn));

        for (Map.Entry<String, List<OrderPresellPO>> entry : preSellOrderMap.entrySet()) {
            OrderPresellDTO orderPresellDTO = orderPresellService.convertOrderPresellPOToDTO(entry.getValue());
            orderPresellDTOMap.put(entry.getKey(), orderPresellDTO);
        }

        return orderPresellDTOMap;
    }


    /**
     * 分页获取导出订单列表
     *
     * @param example
     * @return
     */
    public List<OrderExportDTO> getExportOrderListByPage(OrderExample example) {
        //return orderMapper.getOrderExportListByPage(example);
        return orderMapper.getOrderExportListByPageNew(example);
    }

    /**
     * 分页获取导出订单列表总记录数
     *
     * @param example
     * @return
     */
    public Integer countOrderExportList(OrderExample example) {
        return orderMapper.countOrderExportList(example);
    }


    /**
     * 交易成功后放款操作
     * 订单为40的时候，如果是贷款类支付&&且是交易成功后放款&&且贷款状态不在（50‘60’70‘80）的订单需要调放款接口
     */
    @Transactional
    public void doLoan(String orderSn, Integer optRole, Long optUserId, String optName) {
        log.info("OrderModel doLoan method,orderSn = {}", orderSn);
        OrderPO orderPODb = orderModel.getOrderByOrderSn(orderSn);
        if (OrderConst.ORDER_STATE_40 != orderPODb.getOrderState()) {
            log.warn("订单状态为{}，不能操作交易成功后的放款", orderPODb.getOrderState());
            return;
        }
        if (!LoanStatusEnum.APPLY_SUCCESS.isTrue(orderPODb.getLoanPayState())
                && !LoanStatusEnum.LENDING_FAIL.isTrue(orderPODb.getLoanPayState())) {
            log.warn("订单状态为{}，不能操作交易成功后的放款", orderPODb.getOrderState());
            return;
        }
        OrderPresellPO orderPresellPO = null;
        if (OrderTypeEnum.isPresell(orderPODb.getOrderType())) {
            log.info("orderSn：{} 预售订单", orderPODb.getOrderSn());
            orderPresellPO =
                    orderPresellService.queryBalanceInfoByOrderSn(orderPODb.getOrderSn());
            BizAssertUtil.isTrue(OrderTypeEnum.isPresell(orderPODb.getOrderType()) && (ObjectUtils.isEmpty(orderPresellPO) ||
                    StringUtils.isEmpty(orderPresellPO.getPayNo())), "抱歉，预售订单payNo为空，放款异常，请联系管理员！");
            orderPODb.setPaymentCode(orderPresellPO.getPaymentCode());
            log.info("orderSn：{} 预售尾款订单替换支付方式：payNo:{} paymentCode:{}", orderPODb.getOrderSn(), orderPresellPO.getPayNo(),
                    orderPresellPO.getPaymentCode());
        }
        // 金融规则订单、交易成功放款：进行放款
        if (PayMethodEnum.isLoanPay(PayMethodEnum.valueOf(orderPODb.getPaymentCode()))
                && ExchangeOrderConst.ORDER_EXCHANGE_FLAG_2 != orderPODb.getExchangeFlag()) {
            log.info("orderSn：{} paymentCode:{} 订单、交易成功放款开始", orderPODb.getOrderSn(), orderPODb.getPaymentCode());
            OrderExtendFinancePO financePO = financeService.getByOrderSn(orderPODb.getOrderSn());
            if ((financePO == null && !SettleModeEnum.BORROW.getCode().equals(orderPODb.getSettleMode()))
                    || (Objects.nonNull(financePO) && financePO.getInterestWay() == 1)) {
                // Andy.预售修改
                Result<Void> voidResult = null;
                try {
                    log.info("交易成功开始放款orderSn:{} payNo:{}", orderPODb.getOrderSn(), ObjectUtils.isNotEmpty(orderPresellPO) ? orderPresellPO.getPayNo() : null);
                    voidResult = this.doLoanOperate(orderPODb.getOrderSn(), optUserId, ObjectUtils.isNotEmpty(orderPresellPO) ? orderPresellPO.getPayNo() : null);
                } catch (Exception exception) {
                    // 系统异常导致放款失败，记录放款任务，通过定时任务补偿发起
                    log.warn("doLoanOperate()方法中调用pay服务失败，服务调用异常orderPresellPO:{} exception：{}", orderPresellPO, exception);
                    // Andy.预售
                    taskQueueService.saveTaskQueue(OrderTypeEnum.isPresell(orderPODb.getOrderType()) ? Long.valueOf(orderPresellPO.getPayNo())
                                    : Long.valueOf(orderPODb.getOrderSn()), TaskQueueBizTypeEnum.AUTO_LOAN,
                            new Date(), TaskConstant.DEFAULT_JOB_NUMBER);
                }
                if (Objects.nonNull(voidResult)) {
                    if (!voidResult.isSuccess()) {
                        // 非系统异常导致放款失败，记录日志，人工处理
                        loanPayFail(orderPODb, voidResult.getErrorMsg());
                    } else {
                        loanPaySuccess(orderPODb, optRole, optUserId, optName);
                    }
                }
            }
        }

    }

    /***
     * 交易完成 处理
     * @param orderPODb 订单
     * @param optRole 操作人角色
     * @param optUserId 操作人Id
     * @param optUserName 操作人姓名
     * @param optRemark 备注
     * @param channel 渠道
     * @param validPreOrderState 实付需要校验订单的前置状态
     * */
    @Transactional
    public void orderFinishSuccess(OrderPO orderPODb, Integer optRole, Long optUserId,
                                   String optUserName, String optRemark, OrderCreateChannel channel,
                                   Boolean validPreOrderState) {

        log.info("orderFinishSuccess orderSn = {}", orderPODb.getOrderSn());
        // 1.修改订单状态
        OrderPO updateOrderPO = new OrderPO();
        updateOrderPO.setOrderSn(orderPODb.getOrderSn());

        updateOrderPO.setOrderState(OrderConst.ORDER_STATE_40);
        updateOrderPO.setFinishTime(new Date());
        OrderExample orderExample = new OrderExample();
        orderExample.setOrderSn(orderPODb.getOrderSn());
        if (validPreOrderState != null && validPreOrderState) {
            orderExample.setOrderState(OrderConst.ORDER_STATE_30);
        }

        int limitDay = Integer.parseInt(stringRedisTemplate.opsForValue().get("time_limit_of_after_sale"));
        updateOrderPO.setAfterSalesDeadline(DateUtil.addDays(updateOrderPO.getFinishTime(), limitDay));

        int count = orderMapper.updateByExampleSelective(updateOrderPO, orderExample);
        BizAssertUtil.isTrue(count == 0, "交易完成，更新订单状态失败，请确认");

        // 2.记录订单日志
        orderLogModel.insertOrderLog(optRole, optUserId, optUserName, orderPODb.getOrderSn(), orderPODb.getOrderState(),
                OrderConst.ORDER_STATE_40, LoanStatusEnum.valueOf(orderPODb.getLoanPayState()).getValue(), optRemark, channel);

        //换货日志记录
        insertExchangeOrderLog(orderPODb, optRole, optUserId, optUserName, optRemark, channel);


        // 金融规则订单、交易成功放款：进行放款
        orderModel.doLoan(orderPODb.getOrderSn(), optRole, optUserId, optUserName);

        // 订单完成通知发送
        orderCreateHelper.addOrderChangeEvent(orderPODb, OrderEventEnum.FINISH, updateOrderPO.getFinishTime());
    }

    public void insertExchangeOrderLog(OrderPO orderPO, int optRole, Long optUserId, String optUserName, String optRemark, OrderCreateChannel channel) {
        if (ExchangeOrderConst.ORDER_EXCHANGE_FLAG_2 == orderPO.getExchangeFlag()) {
            OrderExchangeDetailExample orderExchangeDetailExample = new OrderExchangeDetailExample();
            orderExchangeDetailExample.setExchangeOrderSn(orderPO.getOrderSn());
            OrderExchangeDetailPO orderExchangeDetailPO = orderExchangeDetailMapper.getExchangeOrderDetailByExample(orderExchangeDetailExample);
            orderLogModel.insertOrderLog(optRole, optUserId, optUserName, orderExchangeDetailPO.getExchangeSn(), orderPO.getOrderState(),
                    OrderConst.ORDER_STATE_40, LoanStatusEnum.valueOf(orderPO.getLoanPayState()).getValue(), optRemark, channel);
        }
    }

    @Autowired
    private ERPIntegration erpIntegration;
    @Autowired
    private OrderPromotionSendProductService orderPromotionSendProductService;

    @GlobalTransactional(rollbackFor = Throwable.class)
    public Boolean saveFullGiftSendOrderProduct(OrderSubmitDTO.OrderInfo orderInfo, Integer memberId,
                                                String areaCode, String ruleCode, OrderExtendPO orderExtendPO, OrderPO orderPO) {
        String orderSn = orderPO.getOrderSn();
        List<OrderSubmitDTO.PromotionInfo> promotionInfoList = orderInfo.getFullGiftPromotionInfoList();
        if (CollectionUtils.isEmpty(promotionInfoList)) {
            return true;
        }
        int giftGroup = 1;
        Integer isCombination = orderInfo.getOrderType() == OrderTypeEnum.COMBINATION.getValue() ? 1 : 0;
        //保存赠品行
        List<StockCutRequest> stockCutRequestList = Lists.newArrayList();
        List<OrderPromotionSendProductPO> sendProductList = Lists.newArrayList();
        List<OrderProductPO> orderProductPOList = Lists.newArrayList();
        for (OrderSubmitDTO.PromotionInfo promotionInfo : promotionInfoList) {
            //满赠活动的赠品
            List<OrderSubmitDTO.PromotionInfo.ConsumptionFreebie> consumptionFreebieList = promotionInfo.getConsumptionFreebieList();
            if (CollectionUtils.isEmpty(consumptionFreebieList)) {
                continue;
            }
            //遍历赠品集合
            for (OrderSubmitDTO.PromotionInfo.ConsumptionFreebie consumptionFreebie : consumptionFreebieList) {
                List<Long> productIds = consumptionFreebie.getConsumptionGoods()
                        .stream().map(OrderSubmitDTO.PromotionInfo.ConsumptionGoods::getProductId)
                        .collect(Collectors.toList());
                //把普通商品行打上赠品分组标识
                orderProductService.lambdaUpdate().in(OrderProductPO::getProductId, productIds)
                        .eq(OrderProductPO::getOrderSn, orderSn)
                        .eq(OrderProductPO::getIsGift, CommonEnum.NO.getCode())
                        .set(OrderProductPO::getGiftGroup, giftGroup)
                        .update();

                for (OrderSubmitDTO.PromotionInfo.ConsumptionGift consumptionGift : consumptionFreebie.getConsumptionGift()) {
                    Long giftProductId = Long.valueOf(consumptionGift.getGiftId());
                    // product信息
                    ProductPriceVO productPriceVO = orderSubmitAttributesUtils.getProductPriceByProductId(Long.valueOf(consumptionGift.getGiftId()), areaCode, null);
//                    ProductPriceVO productPriceVO =
//                            orderLocalUtils.getProductPrice(Long.valueOf(consumptionGift.getGiftId()), areaCode, null);
                    Product productDb = productPriceVO.getProduct();
                    Goods goodsDb = productPriceVO.getGoods();
                    //商品有效才能赠送
                    if (!goodsDb.getState().equals(GoodsConst.GOODS_STATE_UPPER)) {
                        continue;
                    }
                    /*
                     * 保存赠品至商品行
                     * */
                    OrderProductPO orderProductPO = new OrderProductPO();
                    orderProductPO.setOrderSn(orderSn);
                    orderProductPO.setCost(productDb.getCost());
                    orderProductPO.setTaxRate(productDb.getTaxRate());
                    orderProductPO.setStoreId(orderInfo.getStoreId());
                    orderProductPO.setStoreName(orderInfo.getStoreName());
                    orderProductPO.setGoodsCategoryId(productDb.getCategoryId3());
                    orderProductPO.setGoodsCategoryPath(goodsDb.getCategoryPath());
                    orderProductPO.setMemberId(memberId);
                    orderProductPO.setGoodsId(productDb.getGoodsId());
                    orderProductPO.setGoodsName(productDb.getGoodsName());
                    orderProductPO.setProductImage(productDb.getMainImage());
                    orderProductPO.setSpecValues(productDb.getSpecValues());
                    orderProductPO.setProductId(productDb.getProductId());
                    orderProductPO.setCommissionRate(BigDecimal.ZERO);
                    orderProductPO.setGoodsType(goodsDb.getGoodsType());
                    orderProductPO.setSupplierCode(goodsDb.getSupplierCode());
                    orderProductPO.setSupplierName(goodsDb.getSupplierName());
                    orderProductPO.setSpuOutId(goodsDb.getSpuOutId());
                    orderProductPO.setSkuMaterialCode(productDb.getSkuMaterialCode());
                    orderProductPO.setPurchaseSubCode(productDb.getPurchaseSubCode());
                    //orderProductPO.setBatchNo(productDb.getBatchNo());
                    orderProductPO.setChannelSkuId(productDb.getAgricSkuId());
                    orderProductPO.setChannelNewSkuId(productDb.getAgricSkuIdNew());
                    orderProductPO.setChannelSkuUnit(productDb.getSkuUnit());
                    orderProductPO.setChannelSource(goodsDb.getChannelSource());
                    orderProductPO.setSpecValues(productDb.getSpecValues());
                    //赠品价格为0
                    orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
                    //赠品数量不能大于货品库存
                    orderProductPO.setProductNum(Integer.min(consumptionGift.getGiftNum(), productDb.getProductStock()));
                    orderProductPO.setIsGift(OrderConst.IS_GIFT_YES);//是赠品
                    orderProductPO.setGiftGroup(giftGroup);
                    orderProductPO.setGroupBuyingTag(orderInfo.getOrderProductInfoList().get(0).getGroupBuyingTag());
                    // sku物料名称
                    if (!StringUtils.isEmpty(productDb.getSkuMaterialCode())) {
                        MallProductQuery query = new MallProductQuery();
                        query.setSkuMaterialCodes(Collections.singletonList(productDb.getSkuMaterialCode()));
                        List<com.cfpamf.ms.mallorder.integration.erp.vo.ProductVO> productInfoList = erpIntegration.getProductInfoList(query);
                        if (!CollectionUtils.isEmpty(productInfoList)) {
                            // 保存货品erp信息
                            orderProductPO.setSkuMaterialName(productInfoList.get(0).getProductName());
                            orderProductPO.setProductCategoryPath(productInfoList.get(0).getCategoryCodePath());
                            orderProductPO.setProductCategoryPathName(productInfoList.get(0).getCategoryPath());
                        }
                    }
                    // 商品店铺级分类
                    if (!CollectionUtils.isEmpty(productPriceVO.getStoreLabelGoodsVOList())) {
                        StringBuilder storeLabel = new StringBuilder();
                        productPriceVO.getStoreLabelGoodsVOList().forEach(storeLabelGoodsVO -> {
                            storeLabel.append(",").append(storeLabelGoodsVO.getInnerLabelId());
                        });
                        orderProductPO.setStoreCategoryId(storeLabel.length() == 0 ? "0" : storeLabel.substring(1));
                    }
                    orderProductPO.setPerformanceService(OrderPerformanceModeEnum.PERFORMANCE_MODE_COMMON.getValue()
                            + "," + OrderPerformanceModeEnum.PERFORMANCE_MODE_FULL_GIFT.getValue());
                    orderProductService.save(orderProductPO);

                    String orderSnTmp = orderSn + "+" + orderProductPO.getOrderProductId();
                    orderProductPOList.add(orderProductPO);

                    if (CommonConfig.enableTransactionLog()) {
                        StockCutRequest cutRequest = goodsStockService.buildCutRequest(orderSnTmp, null, giftProductId, areaCode, consumptionGift.getGiftNum(), ruleCode,
                                productDb.getBatchNo(), productDb.getPurchaseSubCode(), productDb.getAgricSkuId(), orderExtendPO.getBranch(), orderExtendPO.getReceiveBranchCode(), orderExtendPO.getWarehouseCode(),
                                EventStockTypeEnum.SALES_OUT_STOCK_CUT, BizTypeEnum.REDUCE_STOCK, OrderConst.LOG_USER_NAME, orderPO,
                                productDb.getSkuUnit(), OrderConst.IS_GIFT_YES, isCombination, orderExtendPO.getManager(), orderExtendPO.getManagerName());
                        stockCutRequestList.add(cutRequest);
                    } else {
                        // 库存扣减
                        goodsStockService.goodsStock(orderSnTmp, null, giftProductId, areaCode, consumptionGift.getGiftNum(), ruleCode,
                                productDb.getBatchNo(), productDb.getPurchaseSubCode(), productDb.getAgricSkuId(), orderExtendPO.getBranch(), orderExtendPO.getReceiveBranchCode(), orderExtendPO.getWarehouseCode(),
                                EventStockTypeEnum.SALES_OUT_STOCK_CUT, BizTypeEnum.REDUCE_STOCK, OrderConst.LOG_USER_NAME, orderPO,
                                productDb.getSkuUnit(), OrderConst.IS_GIFT_YES, isCombination);
                    }

                    /*
                     * 保存商品行上的赠品扩展信息
                     * */
                    OrderPromotionSendProductPO orderPromotionSendProductPO = new OrderPromotionSendProductPO();
                    orderPromotionSendProductPO.setOrderSn(orderSn);
                    orderPromotionSendProductPO.setOrderProductId(orderProductPO.getOrderProductId());
                    orderPromotionSendProductPO.setPromotionGrade(promotionInfo.getPromotionType() / 100);
                    orderPromotionSendProductPO.setPromotionType(promotionInfo.getPromotionType());
                    orderPromotionSendProductPO.setPromotionId(promotionInfo.getPromotionId());
                    orderPromotionSendProductPO.setIsStore(promotionInfo.getIsStore() ? OrderConst.IS_STORE_PROMOTION_YES : OrderConst.IS_STORE_PROMOTION_NO);
                    orderPromotionSendProductPO.setProductId(giftProductId);
                    orderPromotionSendProductPO.setNumber(consumptionGift.getGiftNum());
                    orderPromotionSendProductPO.setProductIds(StringUtils.join(productIds, ","));
                    sendProductList.add(orderPromotionSendProductPO);
//                    orderPromotionSendProductService.save(orderPromotionSendProductPO);
                }
                giftGroup++;
            }
        }
        if (!CollectionUtils.isEmpty(stockCutRequestList)) {
            List<StockCutVO> stockCutVOS = goodsStockService.stockCommonOperationV2(stockCutRequestList);
            if (!CollectionUtils.isEmpty(stockCutVOS)) {
                Map<Long, List<StockCutVO>> cutResultVoMap = stockCutVOS.stream().collect(Collectors.groupingBy(StockCutVO::getProductId));
                for (OrderProductPO orderProductPO : orderProductPOList) {
                    Long productId = orderProductPO.getProductId();
                    if (cutResultVoMap.containsKey(productId)) {
                        dealStockCutPerformance(cutResultVoMap.get(productId), orderProductPO);
                    }
                }
                orderProductService.updateBatchById(orderProductPOList);
            }
        }
        if (!CollectionUtils.isEmpty(sendProductList)) {
            orderPromotionSendProductService.saveBatch(sendProductList);
        }
        return true;
    }

    public List<OrderOfflinePO> queryOrderOfflineList(String paySn) {
        return orderOfflineService.queryOrderOfflineList(paySn);
    }

    /**
     * 库存预检
     *
     * @param orderInfo
     * @param orderPO
     * @param orderExtendPO
     * @param
     * @return
     */
    private void stockCutCheck(OrderSubmitDTO.OrderInfo orderInfo, OrderPO orderPO, OrderExtendPO orderExtendPO, Integer isCombination) {
        for (int i = 0, s = orderInfo.getOrderProductInfoList().size(); i < s; i++) {
            OrderSubmitDTO.OrderInfo.OrderProductInfo orderProductInfo =
                    orderInfo.getOrderProductInfoList().get(i);
            // product信息
            ProductPriceVO productPriceByProductId = orderSubmitAttributesUtils.getProductPriceByProductId(
                    orderProductInfo.getProductId(), orderProductInfo.getAreaCode(), orderProductInfo.getFinanceRuleCode());
            Product productDb = productPriceByProductId.getProduct();
            productPriceByProductId.getGoods().getIsSelf();
            List<CombinationChildGoodsVO> combinationChildGoodsVOList = productPriceByProductId.getCombinationChildGoodsVOList();
            if (CollectionUtil.isNotEmpty(combinationChildGoodsVOList)) {
                //组合商品逻辑
                for (CombinationChildGoodsVO combinationChildGoodsVO : combinationChildGoodsVOList) {
                    // 库存扣减预校验
                    //子商品的总数量 = 组合商品数量 * 子商品数量
                    int buyNum = orderProductInfo.getBuyNum() * combinationChildGoodsVO.getCount();
                    goodsStockService.preStockCutV2(combinationChildGoodsVO.getChildProductId(), orderProductInfo.getAreaCode(),
                            buyNum, orderProductInfo.getFinanceRuleCode(), combinationChildGoodsVO.getBatchNo(),
                            combinationChildGoodsVO.getAgricSkuId(), orderExtendPO.getBranch(), orderExtendPO.getReceiveBranchCode(),
                            orderExtendPO.getWarehouseCode(), orderPO, isCombination);
                }
            } else {
                //普通商品逻辑
                goodsStockService.preStockCutV2(orderProductInfo.getProductId(), orderProductInfo.getAreaCode(),
                        orderProductInfo.getBuyNum(), orderProductInfo.getFinanceRuleCode(), productDb.getBatchNo(),
                        productDb.getAgricSkuId(), orderExtendPO.getBranch(), orderExtendPO.getReceiveBranchCode(),
                        orderExtendPO.getWarehouseCode(), orderPO, isCombination);
            }

        }
    }

    /**
     * 赠品库存预校验
     *
     * @param orderInfo
     * @param isCombination
     * @param areaCode
     * @param ruleCode
     * @param orderPO
     * @param orderExtendPO
     */
    private void giftStockCutCheck(OrderSubmitDTO.OrderInfo orderInfo, Integer isCombination, String areaCode, String ruleCode, OrderPO orderPO, OrderExtendPO orderExtendPO) {
        List<OrderSubmitDTO.PromotionInfo> promotionInfoList = orderInfo.getPromotionInfoList();
        if (!CollectionUtils.isEmpty(promotionInfoList)) {
            //保存赠品行
            for (OrderSubmitDTO.PromotionInfo promotionInfo : promotionInfoList) {
                //满赠活动的赠品
                List<OrderSubmitDTO.PromotionInfo.ConsumptionFreebie> consumptionFreebieList = promotionInfo.getConsumptionFreebieList();
                if (CollectionUtils.isEmpty(consumptionFreebieList)) {
                    continue;
                }
                //遍历赠品集合
                for (OrderSubmitDTO.PromotionInfo.ConsumptionFreebie consumptionFreebie : consumptionFreebieList) {
                    for (OrderSubmitDTO.PromotionInfo.ConsumptionGift consumptionGift : consumptionFreebie.getConsumptionGift()) {
                        Long giftProductId = Long.valueOf(consumptionGift.getGiftId());
                        Product giftProductDb = productFeignClient.getProductByProductId(Long.valueOf(consumptionGift.getGiftId()));

                        // 库存扣减预校验
                        goodsStockService.preStockCutV2(giftProductId, areaCode, consumptionGift.getGiftNum(), ruleCode,
                                giftProductDb.getBatchNo(), giftProductDb.getAgricSkuId(), orderExtendPO.getBranch(), orderExtendPO.getReceiveBranchCode(),
                                orderExtendPO.getWarehouseCode(), orderPO, isCombination);
                    }
                }
            }
        }
    }

    /**
     * 保存商品行
     *
     * @param member
     * @param lockSet
     * @param consumerDTO
     * @param orderInfo
     * @param isCombination
     * @param performanceModeList
     * @param orderSn
     * @param orderPO
     * @param orderExtendPO
     * @param productSize
     * @param isSpecial
     * @param fixedThirdpartnarFee
     */
    private void saveOrderProduct(Member member, Set<String> lockSet, OrderSubmitMqConsumerDTO consumerDTO, OrderSubmitDTO.OrderInfo orderInfo, Integer isCombination, Set<String> performanceModeList, String orderSn, OrderPO orderPO,
                                  OrderExtendPO orderExtendPO, int productSize, boolean isSpecial, BigDecimal fixedThirdpartnarFee, StoreContractReceiptInfoVO storeContract, OrderAgricInfoDto orderAgricInfoDto) {
        //判断是否在农服店铺白名单中，必须配置销售季
        Boolean isAgricShopFlag = storeIsolateWhiteListFeignClient.isStoreOnList(WhiteListEnum.FUSION_STORE_WHITE_LIST, storeContract.getStoreId());

        OrderSubmitParamDTO orderSubmitParamDTO = consumerDTO.getParamDTO();

        // 每次循环为一个订单货品
        for (int i = 0, s = orderInfo.getOrderProductInfoList().size(); i < s; i++) {
            OrderSubmitDTO.OrderInfo.OrderProductInfo orderProductInfo =
                    orderInfo.getOrderProductInfoList().get(i);
            // 锁名称
            if (OrderTypeEnum.SECONDS_KILL != OrderTypeEnum.getValue(orderPO.getOrderType())
                    && !Objects.equals(OrderPatternEnum.COUPON_CENTRE.getValue(), orderPO.getOrderPattern())) {
                String lockKey = "lock-" + orderProductInfo.getProductId();
                lockSet.add(lockKey);
                slodonLock.lock(lockKey);// 获取分布式锁
            }

            List<Long> productIdList = new ArrayList<>();


            // product信息
            ProductPriceVO productPriceByProductId = orderSubmitAttributesUtils.getProductPriceByProductId(
                    orderProductInfo.getProductId(), orderProductInfo.getAreaCode(), orderProductInfo.getFinanceRuleCode());
            Product productDb = productPriceByProductId.getProduct();
            ProductPriceBranchRange productPriceBranchRange = productPriceByProductId.getProductPriceBranchRange();

            List<CombinationChildGoodsVO> combinationChildGoodsVOList = productPriceByProductId.getCombinationChildGoodsVOList();
            HashMap<Long, CombinationChildGoodsVO> map = new HashMap<>();
            Map<String, BigDecimal> calculateMap = null;
            if (CollectionUtil.isNotEmpty(combinationChildGoodsVOList)) {
                map = combinationChildGoodsVOList.stream().collect(HashMap::new,
                        (hashMap, combinationChildGoodsVO) -> hashMap.put(combinationChildGoodsVO.getChildProductId(), combinationChildGoodsVO), HashMap::putAll);
                productIdList = combinationChildGoodsVOList.stream().map(CombinationChildGoodsVO::getChildProductId).collect(Collectors.toList());
                log.info("组合商品Id{}", productIdList);
                //组合商品及关系入库
                saveCombination(orderSn, orderProductInfo, productPriceByProductId, combinationChildGoodsVOList);

                calculateMap = calculateAmount(orderProductInfo, productIdList, combinationChildGoodsVOList);
            } else {
                productIdList.add(orderProductInfo.getProductId());
                log.info("普通商品Id{}", productIdList);
            }


            for (int j = 0; j < productIdList.size(); j++) {
                Long productId = productIdList.get(j);
                //销售季编码
                String salesSeasonCode = null;

                // 商品扩展信息
                GoodsExtend goodsExtend = productPriceByProductId.getGoodsExtend();

                OrderProductPO orderProductPO = null;
                if (CollectionUtil.isEmpty(combinationChildGoodsVOList)) {
                    // -bz_order_product 保存订单货品
                    orderProductPO = orderProductModel.buildOrderProduct(orderPO, orderProductInfo,
                            productPriceByProductId, goodsExtend, orderProductInfo.getSpellTeamId(),
                            storeContract.getCompanyName(), orderSubmitParamDTO.getUsrNo());

                    if (OrderTypeEnum.isOfflineAll(orderPO.getOrderType())) {
                        for (OrderSkuInfoDTO orderSkuInfoDTO : consumerDTO.getOrderOfflineParamDTO().getSkuInfoList()) {
                            if (productId.equals(orderSkuInfoDTO.getProductId())) {
                                orderProductPO.setAgriServiceFees(orderSkuInfoDTO.getAgriServiceFees());
                            }
                        }
                    }

                    //农服店铺 且 销售季编码为空，不允许下单
                    if (isAgricShopFlag) {
                        if (Objects.isNull(productPriceByProductId.getGoodsBusinessExtend()) || StringUtils.isEmpty(productPriceByProductId.getGoodsBusinessExtend().getSalesSeasonCode())) {
                            throw new BusinessException("商品" + orderProductPO.getGoodsName() + "未绑定销售季，请联系客服处理！");
                        }
                        salesSeasonCode = productPriceByProductId.getGoodsBusinessExtend().getSalesSeasonCode();
                    }

                } else {

                    //组合商品
                    orderProductPO = orderProductModel.buildCombinationOrderProduct(orderPO, orderProductInfo,
                            productPriceByProductId, goodsExtend, orderProductInfo.getSpellTeamId(),
                            productPriceBranchRange == null ? null : productPriceBranchRange.getCommissionRate(),
                            productPriceBranchRange == null ? null : productPriceBranchRange.getLandingPrice(),
                            productPriceBranchRange == null ? null : productPriceBranchRange.getTaxPrice(),
                            orderSubmitParamDTO.getUsrNo(), map.get(productId), combinationChildGoodsVOList, calculateMap);

                    //农服店铺 且 销售季编码为空，不允许下单
                    if (isAgricShopFlag) {
                        if (Objects.isNull(map.get(productId)) || StringUtils.isEmpty(map.get(productId).getSalesSeasonCode())) {
                            throw new BusinessException("商品" + orderProductPO.getGoodsName() + "未绑定销售季，请联系客服处理！");
                        }
                        salesSeasonCode = map.get(productId).getSalesSeasonCode();
                    }
                }

                log.info("订单货品信息:{}", orderProductPO);
                //计算运营服务费
                if (CollectionUtil.isEmpty(combinationChildGoodsVOList)) {
                    fixedThirdpartnarFee = getThirdpartnarFee(orderPO, fixedThirdpartnarFee, s, i, orderProductPO);
                } else {
                    fixedThirdpartnarFee = getThirdpartnarFee(orderPO, fixedThirdpartnarFee, productIdList.size(), j, orderProductPO);
                }

                // 库存扣减
                Integer buyNum = orderProductInfo.getBuyNum();
                if (!productId.equals(orderProductInfo.getProductId())) {
                    //如果是组合子商品要传子商品的AgricSkuId、BatchNo、SkuUnit
                    CombinationChildGoodsVO vo = map.get(productId);
                    productDb.setBatchNo(vo.getBatchNo());
                    productDb.setAgricSkuId(vo.getAgricSkuId());
                    productDb.setSkuUnit(vo.getSkuUnit());
                    //购买数量：组合商品子商品的总数量 = 组合商品数量 * 子商品数量
                    buyNum = orderProductInfo.getBuyNum() * vo.getCount();
                }

                List<StockCutVO> stockCutVOList = goodsStockService.goodsStock(orderSn, null, productId, orderProductInfo.getAreaCode(),
                        buyNum, orderProductInfo.getFinanceRuleCode(), productDb.getBatchNo(), productDb.getPurchaseSubCode(),
                        productDb.getAgricSkuId(), orderExtendPO.getBranch(), orderExtendPO.getReceiveBranchCode(),
                        orderExtendPO.getWarehouseCode(), EventStockTypeEnum.SALES_OUT_STOCK_CUT, BizTypeEnum.REDUCE_STOCK,
                        OrderConst.LOG_USER_NAME, orderPO, productDb.getSkuUnit(), null, isCombination);

                //ERP 扣减模式
                dealStockCutPerformance(stockCutVOList, orderProductPO);

                // 订单履约集合
                List<String> performances = Arrays.asList(orderProductPO.getPerformanceService().split(","));
                performanceModeList.addAll(performances);
                orderPO.setPerformanceModes(performanceModeList.toString());
                BizAssertUtil.isTrue(performances.contains(OrderPerformanceModeEnum.PERFORMANCE_MODE_MFR_DEALER.getValue().toString())
                        && StringUtils.isEmpty(orderPO.getDealerCode()), "农机厂商模式订单，经销商不能为空");
                if (!StringUtils.isEmpty(orderPO.getDealerCode())) {
                    BizAssertUtil.isTrue(!shopIntegration.isFactoryBind(orderPO.getDealerCode(), orderPO.getStoreId().toString()), "该经销商已失效或不属于该厂商，不可使用～");
                }

                // 订单商品入库(普通商品)
                orderProductService.save(orderProductPO);

                // 如果物料编码不为空,保存订单货品erp信息
                if (!StringUtils.isEmpty(orderProductPO.getSkuMaterialCode())) {
                    OrderProductErpExtendPO erpExtendPO = OrderProductErpExtendPO.of(orderPO, orderProductPO);
                    log.info("订单货品erp拓展信息:{}", erpExtendPO);


                    // 保存订单货品erp拓展信息
                    orderProductErpExtendService.save(erpExtendPO);
                }
                Long orderProductId = orderProductPO.getOrderProductId();
                if (!CollectionUtils.isEmpty(orderProductInfo.getPromotionInfoList())) { // 有活动优惠
                    orderProductInfo.getPromotionInfoList().forEach(promotionInfo -> {
                        // -bz_order_product_extend 保存订单货品扩展
                        orderProductExtendModel.insertOrderProductExtend(orderSn,
                                orderProductId, promotionInfo);
                    });
                }

                // 拼团、秒杀 提交活动订单+扣除活动库存
                if (productSize == 1 && isSpecial) {
                    OrderProduct product = new OrderProduct();
                    product.setOrderSn(orderSn);
                    product.setMemberId(orderProductInfo.getMemberId());
                    product.setGoodsId(orderProductInfo.getGoodsId());
                    product.setGoodsName(orderProductInfo.getGoodsName());
                    product.setProductImage(orderProductInfo.getProductImage());
                    product.setProductId(orderProductInfo.getProductId());
                    product.setProductNum(orderProductInfo.getBuyNum());
                    product.setOrderProductId(orderProductPO.getOrderProductId());
                    product.setSpellTeamId(orderProductInfo.getSpellTeamId());

                    if (CommonConfig.enableTcc() && !orderInfo.getPromotionType().equals(PromotionConst.PROMOTION_TYPE_102)) {
                        promotionManager.submitPromotionActivitiesOrder(orderSn, orderInfo.getPromotionType(), product);
                    } else {
                        JsonResult<Integer> promotionResult =
                                promotionCommonFeignClient.submitPromotionOrderV2(orderInfo.getPromotionType(), product);
                        BizAssertUtil.isTrue(promotionResult.getState() != 200, promotionResult.getMsg());
                    }
                }


                // 卡券订单：锁定优惠券、保存优惠券信息
                if (OrderPatternEnum.COUPON_CENTRE.getValue().equals(orderPO.getOrderPattern())) {
                    // 商品优惠券信息
                    List<ProductActivityGoodsBindVO> activityGoodsBindVOList = productPriceByProductId.getActivityGoodsBindVOList();
                    BizAssertUtil.notEmpty(activityGoodsBindVOList, "卡券商品查询优惠券信息为空，商品编号：" + orderProductPO.getProductId());

                    // 锁定优惠券
                    lockCoupons(member, orderSn, orderProductInfo.getBuyNum(), activityGoodsBindVOList);

                    //-bz_order_product_coupon 保存订单商品优惠券信息
                    orderProductCouponService.insertOrderProductCoupon(orderProductPO, activityGoodsBindVOList);
                }

                if (isAgricShopFlag) {
                    OrderProductAgricExtendPO orderProductAgricExtendPO = orderProductAgricExtendModel.buildOrderProductAgricExtend(orderProductPO, orderAgricInfoDto, member, salesSeasonCode);
                    orderProductAgricExtendModel.saveOrderProductAgricExtend(orderProductAgricExtendPO);
                }
            }
        }
    }

    /**
     * 保存商品行
     *
     * @param member
     * @param lockSet
     * @param consumerDTO
     * @param orderInfo
     * @param isCombination
     * @param performanceModeList
     * @param orderSn
     * @param orderPO
     * @param orderExtendPO
     * @param productSize
     * @param isSpecial
     * @param fixedThirdpartnarFee
     */
    private void saveOrderProductV2(Member member, Set<String> lockSet, OrderSubmitMqConsumerDTO consumerDTO, OrderSubmitDTO.OrderInfo orderInfo, Integer isCombination, Set<String> performanceModeList, String orderSn, OrderPO orderPO,
                                    OrderExtendPO orderExtendPO, int productSize, boolean isSpecial, BigDecimal fixedThirdpartnarFee, StoreContractReceiptInfoVO storeContract, OrderAgricInfoDto orderAgricInfoDto) {
        //判断是否在农服店铺白名单中，必须配置销售季
        Boolean isAgricShopFlag = storeIsolateWhiteListFeignClient.isStoreOnList(WhiteListEnum.FUSION_STORE_WHITE_LIST, storeContract.getStoreId());
        //productId所对应的销售季map
        Map<Long, String> salesSeasonCodeMap = Maps.newHashMap();

        OrderSubmitParamDTO orderSubmitParamDTO = consumerDTO.getParamDTO();

        // 每次循环为一个订单货品
        List<StockCutRequest> stockCutRequestList = Lists.newArrayList();
        List<OrderProductPO> orderProductPoList = Lists.newArrayList();
        Map<Long, List<OrderSubmitDTO.PromotionInfo>> productIdPromotionInfoMap = Maps.newHashMap();
        Map<Long, SubmitPromotionOrderDTO> promotionOrderMap = Maps.newHashMap();
        // 是否包含组合商品
        boolean containsCombination = false;
        Map<String, BigDecimal> calculateTotal = Maps.newHashMap();
        for (int i = 0, s = orderInfo.getOrderProductInfoList().size(); i < s; i++) {
            OrderSubmitDTO.OrderInfo.OrderProductInfo orderProductInfo =
                    orderInfo.getOrderProductInfoList().get(i);
            // 锁名称
            if (OrderTypeEnum.SECONDS_KILL != OrderTypeEnum.getValue(orderPO.getOrderType())
                    && !Objects.equals(OrderPatternEnum.COUPON_CENTRE.getValue(), orderPO.getOrderPattern())) {
                String lockKey = "lock-" + orderProductInfo.getProductId();
                lockSet.add(lockKey);
                slodonLock.lock(lockKey);// 获取分布式锁
            }

            List<Long> productIdList = new ArrayList<>();


            // product信息
            ProductPriceVO productPriceByProductId = orderSubmitAttributesUtils.getProductPriceByProductId(
                    orderProductInfo.getProductId(), orderProductInfo.getAreaCode(), orderProductInfo.getFinanceRuleCode());
            Product productDb = productPriceByProductId.getProduct();

            ProductPriceBranchRange productPriceBranchRange = productPriceByProductId.getProductPriceBranchRange();

            List<CombinationChildGoodsVO> combinationChildGoodsVOList = productPriceByProductId.getCombinationChildGoodsVOList();
            HashMap<Long, CombinationChildGoodsVO> map = new HashMap<>();
            Map<String, BigDecimal> calculateMap = null;
            if (CollectionUtil.isNotEmpty(combinationChildGoodsVOList)) {
                containsCombination = true;
                map = combinationChildGoodsVOList.stream().collect(HashMap::new,
                        (hashMap, combinationChildGoodsVO) -> hashMap.put(combinationChildGoodsVO.getChildProductId(), combinationChildGoodsVO), HashMap::putAll);
                productIdList = combinationChildGoodsVOList.stream().map(CombinationChildGoodsVO::getChildProductId).collect(Collectors.toList());
                log.info("组合商品Id{}", productIdList);
                //组合商品及关系入库
                saveCombination(orderSn, orderProductInfo, productPriceByProductId, combinationChildGoodsVOList);

                calculateMap = calculateAmount(orderProductInfo, productIdList, combinationChildGoodsVOList);
                if (null != calculateMap) {
                    calculateTotal.putAll(calculateMap);
                }
            } else {
                productIdList.add(orderProductInfo.getProductId());
                log.info("普通商品Id{}", productIdList);
            }


            for (int j = 0; j < productIdList.size(); j++) {
                Long productId = productIdList.get(j);

                // 商品扩展信息
                GoodsExtend goodsExtend = productPriceByProductId.getGoodsExtend();

                OrderProductPO orderProductPO = null;
                if (CollectionUtil.isEmpty(combinationChildGoodsVOList)) {
                    // -bz_order_product 保存订单货品
                    orderProductPO = orderProductModel.buildOrderProduct(orderPO, orderProductInfo,
                            productPriceByProductId, goodsExtend, orderProductInfo.getSpellTeamId(),
                            storeContract.getCompanyName(), orderSubmitParamDTO.getUsrNo());
                    if (OrderTypeEnum.isOfflineAll(orderPO.getOrderType())) {
                        for (OrderSkuInfoDTO orderSkuInfoDTO : consumerDTO.getOrderOfflineParamDTO().getSkuInfoList()) {
                            if (productId.equals(orderSkuInfoDTO.getProductId())) {
                                orderProductPO.setAgriServiceFees(orderSkuInfoDTO.getAgriServiceFees());
                            }
                        }
                    }
                    //农服店铺 且 销售季编码为空，不允许下单
                    if (isAgricShopFlag) {
                        if (Objects.isNull(productPriceByProductId.getGoodsBusinessExtend()) || StringUtils.isEmpty(productPriceByProductId.getGoodsBusinessExtend().getSalesSeasonCode())) {
                            throw new BusinessException("商品" + orderProductPO.getGoodsName() + "未绑定销售季，请联系客服处理！");
                        }
                        salesSeasonCodeMap.put(productId, productPriceByProductId.getGoodsBusinessExtend().getSalesSeasonCode());
                    }

                } else {

                    //组合商品
                    orderProductPO = orderProductModel.buildCombinationOrderProduct(orderPO, orderProductInfo,
                            productPriceByProductId, goodsExtend, orderProductInfo.getSpellTeamId(),
                            productPriceBranchRange == null ? null : productPriceBranchRange.getCommissionRate(),
                            productPriceBranchRange == null ? null : productPriceBranchRange.getLandingPrice(),
                            productPriceBranchRange == null ? null : productPriceBranchRange.getTaxPrice(),
                            orderSubmitParamDTO.getUsrNo(), map.get(productId), combinationChildGoodsVOList, calculateMap);
                    //农服店铺 且 销售季编码为空，不允许下单
                    if (isAgricShopFlag) {
                        if (Objects.isNull(map.get(productId)) || StringUtils.isEmpty(map.get(productId).getSalesSeasonCode())) {
                            throw new BusinessException("商品" + orderProductPO.getGoodsName() + "未绑定销售季，请联系客服处理！");
                        }
                        salesSeasonCodeMap.put(productId, map.get(productId).getSalesSeasonCode());
                    }
                }
                //计算运营服务费
                if (CollectionUtil.isEmpty(combinationChildGoodsVOList)) {
                    fixedThirdpartnarFee = getThirdpartnarFee(orderPO, fixedThirdpartnarFee, s, i, orderProductPO);
                } else {
                    fixedThirdpartnarFee = getThirdpartnarFee(orderPO, fixedThirdpartnarFee, productIdList.size(), j, orderProductPO);
                }

                // 库存扣减
                Integer buyNum = orderProductInfo.getBuyNum();
                if (!productId.equals(orderProductInfo.getProductId())) {
                    //如果是组合子商品要传子商品的AgricSkuId、BatchNo、SkuUnit
                    CombinationChildGoodsVO vo = map.get(productId);
                    productDb.setBatchNo(vo.getBatchNo());
                    productDb.setAgricSkuId(vo.getAgricSkuId());
                    productDb.setSkuUnit(vo.getSkuUnit());
                    //购买数量：组合商品子商品的总数量 = 组合商品数量 * 子商品数量
                    buyNum = orderProductInfo.getBuyNum() * vo.getCount();
                }
                // 批量调用，先生成request
                StockCutRequest cutRequest = goodsStockService.buildCutRequest(orderSn, null, productId, orderProductInfo.getAreaCode(),
                        buyNum, orderProductInfo.getFinanceRuleCode(), productDb.getBatchNo(), productDb.getPurchaseSubCode(),
                        productDb.getAgricSkuId(), orderExtendPO.getBranch(), orderExtendPO.getReceiveBranchCode(),
                        orderExtendPO.getWarehouseCode(), EventStockTypeEnum.SALES_OUT_STOCK_CUT, BizTypeEnum.REDUCE_STOCK,
                        OrderConst.LOG_USER_NAME, orderPO, productDb.getSkuUnit(), null, isCombination, orderExtendPO.getManager(), orderExtendPO.getManagerName());
                stockCutRequestList.add(cutRequest);

                // 订单商品入库(普通商品)
//                orderProductService.save(orderProductPO);
                productIdPromotionInfoMap.put(productId, orderProductInfo.getPromotionInfoList());
                // 拼团、秒杀 提交活动订单+扣除活动库存
                if (productSize == 1 && isSpecial) {
                    OrderProduct product = new OrderProduct();
                    product.setOrderSn(orderSn);
                    product.setMemberId(orderProductInfo.getMemberId());
                    product.setGoodsId(orderProductInfo.getGoodsId());
                    product.setGoodsName(orderProductInfo.getGoodsName());
                    product.setProductImage(orderProductInfo.getProductImage());
                    product.setProductId(orderProductInfo.getProductId());
                    product.setProductNum(orderProductInfo.getBuyNum());
                    product.setOrderProductId(orderProductPO.getOrderProductId());
                    product.setSpellTeamId(orderProductInfo.getSpellTeamId());

                    if (CommonConfig.enableTcc() && !orderInfo.getPromotionType().equals(PromotionConst.PROMOTION_TYPE_102)) {
                        promotionManager.submitPromotionActivitiesOrder(orderSn, orderInfo.getPromotionType(), product);
                    } else if (CommonConfig.enableTransactionLog() && !orderInfo.getPromotionType().equals(PromotionConst.PROMOTION_TYPE_102)) {
                        SubmitPromotionOrderDTO promotionOrderDTO = new SubmitPromotionOrderDTO();
                        promotionOrderDTO.setPromotionType(orderInfo.getPromotionType());
                        promotionOrderDTO.setOrderProduct(product);
                        promotionOrderMap.put(orderProductInfo.getProductId(), promotionOrderDTO);
                    } else {
                        JsonResult<Integer> promotionResult =
                                promotionCommonFeignClient.submitPromotionOrderV2(orderInfo.getPromotionType(), product);
                        BizAssertUtil.isTrue(promotionResult.getState() != 200, promotionResult.getMsg());
                    }
                }

                // 改为批量保存
                orderProductPO.setAreaCode(orderProductInfo.getAreaCode());
                orderProductPoList.add(orderProductPO);
            }
        }
        // 调用批量扣库存接口
        Map<Long, List<StockCutVO>> cutResultVoMap = Maps.newHashMap();
        if (CommonConfig.enableTransactionLog()) {
            List<StockCutVO> originVo = goodsStockService.stockCommonOperationV2(stockCutRequestList);
            if (!CollectionUtils.isEmpty(originVo)) {
                cutResultVoMap = originVo.stream().collect(Collectors.groupingBy(StockCutVO::getProductId));
            }
        }
        List<OrderSnapshotPO> snapshotPOS = Lists.newArrayList();
        for (OrderProductPO orderProductPO : orderProductPoList) {
            List<StockCutVO> stockCutVOList = cutResultVoMap.get(orderProductPO.getProductId());
            if (!CollectionUtils.isEmpty(stockCutVOList)) {
//                orderSnapshotService.saveOrderProductStockBatch(orderSn, orderProductPO.getProductId().toString(),
//                        BizTypeEnum.REDUCE_STOCK, stockCutVOList, OrderConst.LOG_USER_NAME);
                List<OrderSnapshotPO> poList = orderSnapshotService.buildOrderProductStockVo(orderSn, orderProductPO.getProductId().toString(),
                        BizTypeEnum.REDUCE_STOCK, stockCutVOList, OrderConst.LOG_USER_NAME);
                if (!CollectionUtils.isEmpty(poList)) {
                    snapshotPOS.addAll(poList);
                }
                dealStockCutPerformance(stockCutVOList, orderProductPO);
            }
            // 订单履约集合
            List<String> performances = Arrays.asList(orderProductPO.getPerformanceService().split(","));
            performanceModeList.addAll(performances);
            orderPO.setPerformanceModes(performanceModeList.toString());
            BizAssertUtil.isTrue(performances.contains(OrderPerformanceModeEnum.PERFORMANCE_MODE_MFR_DEALER.getValue().toString())
                    && StringUtils.isEmpty(orderPO.getDealerCode()), "农机厂商模式订单，经销商不能为空");
            // 订单级别校验即可，不在商品行级别校验
//            if (!StringUtils.isEmpty(orderPO.getDealerCode())) {
//                BizAssertUtil.isTrue(!shopIntegration.isFactoryBind(orderPO.getDealerCode(), orderPO.getStoreId().toString()), "该经销商已失效或不属于该厂商，不可使用～");
//            }
        }
        if (!CollectionUtils.isEmpty(snapshotPOS)) {
            // 异步保存快照
            orderSnapshotService.saveBatchAsync(snapshotPOS);
        }
        orderProductService.saveBatch(orderProductPoList);
        System.out.println(JSONObject.toJSONString(orderProductPoList));
        // 生成需要保存的订单货品erp信息
        List<OrderProductErpExtendPO> erpExtendPOList = orderProductErpExtendService.buildOrderProductErpExtendList(orderPO, orderProductPoList);

        log.info("订单货品erp拓展信息:{}", erpExtendPOList);
        // 保存订单货品erp拓展信息
        if (!CollectionUtils.isEmpty(erpExtendPOList)) {
            orderProductErpExtendService.saveBatch(erpExtendPOList);
        }
        List<OrderProductExtendPO> orderProductExtendPOList = Lists.newArrayList();
        List<OrderProductAgricExtendPO> addOrderProductAgricExtendPOList = Lists.newArrayList();
        if (containsCombination) {
            // 包含组合商品，需要重新查一次
            List<ProductAreaDTO> productAreaDTOS = Lists.newArrayList();
            for (OrderProductPO orderProductPO : orderProductPoList) {
                ProductAreaDTO areaDTO = new ProductAreaDTO();
                areaDTO.setProductId(orderProductPO.getProductId());
                areaDTO.setAreaCode(orderProductPO.getAreaCode());
                areaDTO.setFinanceRuleCode(orderProductPO.getFinanceRuleCode());
                productAreaDTOS.add(areaDTO);
            }
            orderSubmitAttributesUtils.getProductBatchV2(productAreaDTOS);
        }
        List<OrderProductCouponPO> orderCouponPoTotalList = Lists.newArrayList();
        List<OrderProductPO> updateList = Lists.newArrayList();
        for (OrderProductPO orderProductPO : orderProductPoList) {
            Long orderProductId = orderProductPO.getOrderProductId();
            Long productId = orderProductPO.getProductId();
            List<OrderSubmitDTO.PromotionInfo> promotionInfos = productIdPromotionInfoMap.get(productId);
            if (!CollectionUtils.isEmpty(promotionInfos)) { // 有活动优惠
                boolean finalContainsCombination = containsCombination;
                promotionInfos.forEach(promotionInfo -> {
                    if (!finalContainsCombination) {
                        // -bz_order_product_extend 保存订单货品扩展
                        orderProductExtendPOList.add(orderProductExtendModel.buildOrderProductExtend(orderSn,
                                orderProductId, promotionInfo));
                    } else {
                        // -bz_order_product_extend 保存订单货品扩展
                        orderProductExtendPOList.add(orderProductExtendModel.buildOrderProductExtendCombination(orderSn,
                                orderProductId, promotionInfo, calculateTotal, productId));
                    }
                });
            }
            // 卡券订单：锁定优惠券、保存优惠券信息
            if (OrderPatternEnum.COUPON_CENTRE.getValue().equals(orderPO.getOrderPattern())) {
                ProductPriceVO productPriceByProductId = orderSubmitAttributesUtils.getProductPriceByProductId(
                        productId, orderProductPO.getAreaCode(), orderProductPO.getFinanceRuleCode());
                // 商品优惠券信息
                List<ProductActivityGoodsBindVO> activityGoodsBindVOList = productPriceByProductId.getActivityGoodsBindVOList();
                BizAssertUtil.notEmpty(activityGoodsBindVOList, "卡券商品查询优惠券信息为空，商品编号：" + productId);

                // 锁定优惠券
                lockCoupons(member, orderSn, orderProductPO.getProductNum(), activityGoodsBindVOList);

                //-bz_order_product_coupon 保存订单商品优惠券信息
                List<OrderProductCouponPO> orderCouponPoList = orderProductCouponService.buildOrderProductCoupon(orderProductPO, activityGoodsBindVOList);
                if (!CollectionUtils.isEmpty(orderCouponPoList)) {
                    orderCouponPoTotalList.addAll(orderCouponPoList);
                }
            }
            if (promotionOrderMap.containsKey(productId)) {
                SubmitPromotionOrderDTO submitPromotionOrderDTO = promotionOrderMap.get(productId);
                if (null != submitPromotionOrderDTO) {
                    OrderProduct op = submitPromotionOrderDTO.getOrderProduct();
                    // 重新设置orderProductPoId
                    op.setOrderProductId(orderProductPO.getOrderProductId());
                    submitPromotionOrderDTO.setOrderProduct(op);
                    JsonResult<SubmitPromotionOrderResult> result = ExternalApiUtil.callJsonResultApi(() -> promotionCommonFeignClient.submitPromotionOrderV3(submitPromotionOrderDTO), submitPromotionOrderDTO,
                            "/v1/feign/promotion/common/submitPromotionOrder/v3", "促销提交订单v3");
                    SubmitPromotionOrderResult orderResult = result.getData();
                    orderProductPO.setSpellTeamId(orderResult.getSpellTeamId());
                    // 获取到了拼团团队id，需要重新更新一次orderProduct
                    updateList.add(orderProductPO);
                }
            }
            //保存乡信商品扩展信息
            if (isAgricShopFlag) {
                OrderProductAgricExtendPO orderProductAgricExtendPO = orderProductAgricExtendModel.buildOrderProductAgricExtend(orderProductPO, orderAgricInfoDto, member, salesSeasonCodeMap.get(productId));
                addOrderProductAgricExtendPOList.add(orderProductAgricExtendPO);
            }
        }
        if (!CollectionUtils.isEmpty(orderCouponPoTotalList)) {
            orderProductCouponService.saveBatch(orderCouponPoTotalList);
        }
        if (!CollectionUtils.isEmpty(updateList)) {
            // 批量更新
            orderProductService.updateBatchById(updateList);
        }
        if (!CollectionUtils.isEmpty(orderProductExtendPOList)) {
            orderProductExtendModel.saveOrderProductExtendBatch(orderProductExtendPOList);
//            for (OrderProductExtendPO orderProductExtendPO : orderProductExtendPOList) {
//                // 暂时使用原来的单个保存的方法，后续优化为批量
//                orderProductExtendModel.saveOrderProductExtend(orderProductExtendPO);
//            }
        }
        //保存商品行乡信数据
        if (!CollectionUtils.isEmpty(addOrderProductAgricExtendPOList)) {
            orderProductAgricExtendModel.saveOrderProductAgricExtendBatch(addOrderProductAgricExtendPOList);
        }

    }

    /**
     * 计算组合商品运营服务费
     *
     * @param orderPO
     * @param fixedThirdpartnarFee
     * @param size
     * @param j
     * @param orderProductPO
     * @return
     */
    private BigDecimal getThirdpartnarFee(OrderPO orderPO, BigDecimal fixedThirdpartnarFee, int size, int j, OrderProductPO orderProductPO) {
        // 只有一行商品
        if (size == 1) {
            // orderProductPO.setServiceFee(orderPO.getServiceFee());
            orderProductPO.setThirdpartnarFee(orderPO.getThirdpartnarFee());
        } else if (j == size - 1) { // 最后一行商品 钆差
            // orderProductPO.setServiceFee(orderPO.getServiceFee().subtract(fixedServiceFee));
            orderProductPO.setThirdpartnarFee(orderPO.getThirdpartnarFee().subtract(fixedThirdpartnarFee));
        } else {
            BigDecimal productPrice = orderProductPO.getProductShowPrice()
                    .multiply(BigDecimal.valueOf(orderProductPO.getProductNum()))
                    .subtract(orderProductPO.getStoreActivityAmount())
                    .subtract(orderProductPO.getStoreVoucherAmount());

            BigDecimal orderPrice = orderPO.getGoodsAmount().subtract(orderPO.getStoreActivityAmount())
                    .subtract(orderPO.getStoreVoucherAmount());

            // BigDecimal productServiceFee = BigDecimal.ZERO;
            BigDecimal productThirdpartnarFee = BigDecimal.ZERO;
            if (orderPrice.compareTo(BigDecimal.ZERO) > 0) {
                // productServiceFee = productPrice.multiply(orderPO.getServiceFee()).divide(orderPrice, 2,
                //         RoundingMode.HALF_UP);
                // // .setScale(2, BigDecimal.ROUND_HALF_UP);

                productThirdpartnarFee = productPrice.multiply(orderPO.getThirdpartnarFeeRate()).setScale(2, RoundingMode.HALF_UP);
                // .setScale(2, BigDecimal.ROUND_HALF_UP);
            }

            // orderProductPO.setServiceFee(productServiceFee);
            orderProductPO.setThirdpartnarFee(productThirdpartnarFee);

            // fixedServiceFee = fixedServiceFee.add(productServiceFee);
            fixedThirdpartnarFee = fixedThirdpartnarFee.add(productThirdpartnarFee);
        }
        return fixedThirdpartnarFee;
    }

    /**
     * 组合商品计算分摊金额
     *
     * @param orderProductInfo
     * @param productIdList
     * @param combinationChildGoodsVOList
     * @return
     */
    private Map<String, BigDecimal> calculateAmount(OrderSubmitDTO.OrderInfo.OrderProductInfo orderProductInfo, List<Long> productIdList, List<CombinationChildGoodsVO> combinationChildGoodsVOList) {
        //计算分摊金额

        List<AmountApportionmentProductVo> productVoList = new ArrayList<>(productIdList.size());
        for (CombinationChildGoodsVO combinationChildGoodsVO : combinationChildGoodsVOList) {
            AmountApportionmentProductVo amountApportionmentProductVo = new AmountApportionmentProductVo();
            amountApportionmentProductVo.setProductId(combinationChildGoodsVO.getChildProductId());
            amountApportionmentProductVo.setBuyNum(combinationChildGoodsVO.getCount() * orderProductInfo.getBuyNum());
            amountApportionmentProductVo.setSinglePrice(combinationChildGoodsVO.getPrice());
            productVoList.add(amountApportionmentProductVo);
        }


        List<AmountApportionmentVo> apportionmentVoList = new ArrayList<>();

        AmountApportionmentVo xzCardAmountVO = new AmountApportionmentVo();
        xzCardAmountVO.setPromotionTotalAmount(orderProductInfo.getXzCardAmount());
        xzCardAmountVO.setType("xzCardAmount");
        xzCardAmountVO.setProductVoList(productVoList);
        apportionmentVoList.add(xzCardAmountVO);

        AmountApportionmentVo activityDiscountAmountVo = new AmountApportionmentVo();
        activityDiscountAmountVo.setPromotionTotalAmount(orderProductInfo.getTotalDiscount());
        activityDiscountAmountVo.setType("activityDiscountAmount");
        activityDiscountAmountVo.setProductVoList(productVoList);
        apportionmentVoList.add(activityDiscountAmountVo);

        AmountApportionmentVo storeActivityAmountVO = new AmountApportionmentVo();
        storeActivityAmountVO.setPromotionTotalAmount(orderProductInfo.getStoreActivityAmount());
        storeActivityAmountVO.setType("storeActivityAmount");
        storeActivityAmountVO.setProductVoList(productVoList);
        apportionmentVoList.add(storeActivityAmountVO);

        AmountApportionmentVo platformActivityAmountVO = new AmountApportionmentVo();
        platformActivityAmountVO.setPromotionTotalAmount(orderProductInfo.getPlatformActivityAmount());
        platformActivityAmountVO.setType("platformActivityAmount");
        platformActivityAmountVO.setProductVoList(productVoList);
        apportionmentVoList.add(platformActivityAmountVO);

        AmountApportionmentVo storeVoucherAmountVO = new AmountApportionmentVo();
        storeVoucherAmountVO.setPromotionTotalAmount(orderProductInfo.getStoreVoucherAmount());
        storeVoucherAmountVO.setType("storeVoucherAmount");
        storeVoucherAmountVO.setProductVoList(productVoList);
        apportionmentVoList.add(storeVoucherAmountVO);

        AmountApportionmentVo platformVoucherAmountVO = new AmountApportionmentVo();
        platformVoucherAmountVO.setPromotionTotalAmount(orderProductInfo.getPlatformVoucherAmount());
        platformVoucherAmountVO.setType("platformVoucherAmount");
        platformVoucherAmountVO.setProductVoList(productVoList);
        apportionmentVoList.add(platformVoucherAmountVO);

        List<OrderSubmitDTO.PromotionInfo> promotionInfoList = orderProductInfo.getPromotionInfoList();

        JsonResult<List<AmountApportionmentVo>> listJsonResult = promotionCommonFeignClient.promotionAmountApportionment(apportionmentVoList);
        if (listJsonResult.getState() != 200) {
            throw new MallException("组合商品id" + orderProductInfo.getProductId() + "：计算优惠金额失败");
        }
        List<AmountApportionmentVo> resultData = listJsonResult.getData();

        if (CollectionUtil.isEmpty(resultData)) {
            return null;
        }
        Map<String, BigDecimal> map = new HashMap<>(resultData.size());
        for (AmountApportionmentVo resultDatum : resultData) {
            for (AmountApportionmentProductVo productVo : resultDatum.getProductVoList()) {
                map.put(resultDatum.getType() + "_" + productVo.getProductId(), productVo.getApportionment());
            }
        }
        // 分两次计算，因为促销会总累计金额并汇总返回，但是优惠出售价不应该累加到活动优惠金额中去
        apportionmentVoList.clear();
        if (!CollectionUtils.isEmpty(promotionInfoList)) {
            for (OrderSubmitDTO.PromotionInfo promotionInfo : promotionInfoList) {
                if (promotionInfo.getPromotionType().equals(PromotionConst.PROMOTION_TYPE_402)) {
                    AmountApportionmentVo temp = new AmountApportionmentVo();
                    temp.setPromotionTotalAmount(promotionInfo.getDiscount());
                    temp.setType(promotionInfo.getPromotionType() + CommonConst.SPLIT_CALCULATE_DEFAULT + promotionInfo.getCouponId() + CommonConst.SPLIT_CALCULATE_DEFAULT + CommonConst.DISCOUNT);
                    temp.setProductVoList(productVoList);
                    apportionmentVoList.add(temp);
                    AmountApportionmentVo tempRetail = new AmountApportionmentVo();
                    tempRetail.setPromotionTotalAmount(promotionInfo.getRetailPrice());
                    tempRetail.setType(promotionInfo.getPromotionType() + CommonConst.SPLIT_CALCULATE_DEFAULT + promotionInfo.getCouponId() + CommonConst.SPLIT_CALCULATE_DEFAULT + CommonConst.RETAIL);
                    tempRetail.setProductVoList(productVoList);
                    apportionmentVoList.add(tempRetail);
                } else {
                    AmountApportionmentVo temp = new AmountApportionmentVo();
                    temp.setPromotionTotalAmount(promotionInfo.getDiscount());
                    temp.setType(promotionInfo.getPromotionType() + CommonConst.SPLIT_CALCULATE_DEFAULT + promotionInfo.getPromotionId() + CommonConst.SPLIT_CALCULATE_DEFAULT + CommonConst.DISCOUNT);
                    temp.setProductVoList(productVoList);
                    apportionmentVoList.add(temp);
                }
            }
        }
        JsonResult<List<AmountApportionmentVo>> listJsonResultTemp = promotionCommonFeignClient.promotionAmountApportionment(apportionmentVoList);
        if (listJsonResultTemp.getState() != 200) {
            throw new MallException("组合商品id" + orderProductInfo.getProductId() + "：计算优惠金额失败");
        }
        List<AmountApportionmentVo> calculationData = listJsonResultTemp.getData();

        if (CollectionUtil.isEmpty(calculationData)) {
            return null;
        }
        for (AmountApportionmentVo resultDatum : calculationData) {
            // 单独计算优惠券出售价分摊结果，不对总优惠金额做累计
            if (resultDatum.getType().equals("activityDiscountAmount")) {
                continue;
            }
            for (AmountApportionmentProductVo productVo : resultDatum.getProductVoList()) {
                map.put(resultDatum.getType() + "_" + productVo.getProductId(), productVo.getApportionment());
            }
        }
        return map;
    }

    /**
     * 组合商品关系入库
     *
     * @param orderSn
     * @param orderProductInfo
     * @param productPriceByProductId
     * @param combinationChildGoodsVOList
     */
    private void saveCombination(String orderSn, OrderSubmitDTO.OrderInfo.OrderProductInfo orderProductInfo, ProductPriceVO productPriceByProductId, List<CombinationChildGoodsVO> combinationChildGoodsVOList) {
        Long mainProductId = null;
        if (CollectionUtil.isNotEmpty(combinationChildGoodsVOList)) {
            List<CombinationChildGoodsVO> mains = combinationChildGoodsVOList.stream().filter(e -> e.getIsMain() == 1).collect(Collectors.toList());
            mainProductId = mains.get(0).getChildProductId();
        }
        orderProductCombinationService.save(BzOrderProductCombinationPO.builder()
                .orderSn(orderSn)
                .goodsId(productPriceByProductId.getProduct().getGoodsId())
                .goodsName(productPriceByProductId.getProduct().getGoodsName())
                .mainProductId(mainProductId)
                .productId(orderProductInfo.getProductId())
                .mainImage(productPriceByProductId.getProduct().getMainImage())
                .buyNum(orderProductInfo.getBuyNum())
                .build());
    }

    public List<Long> existOrderStoreIdByStoreId(StoreExistOrderReq storeExistOrderReq) {
        return orderMapper.existOrderStoreIdByStoreId(storeExistOrderReq.getStoreIdList(), storeExistOrderReq.getStartTime());
    }

    public Map<String, List<BzOrderProductInstallPO>> buildOrderProductInstallSupplierInfo(List<Long> orderProductIds) {
        if (!CollectionUtils.isEmpty(orderProductIds)) {
            return orderProductInstallService.queryByOrderProductIds(orderProductIds)
                    .stream()
                    .collect(Collectors.groupingBy(i -> i.getOrderProductId() + "_" + i.getStoreId()));
        }
        return Collections.EMPTY_MAP;
    }


    public Boolean isSelfLift(String orderSn) {
        OrderPO orderPO = orderModel.getOrderByOrderSn(orderSn);
        BizAssertUtil.notNull(orderPO, "订单不存在");
        if (OrderPatternEnum.SELF_LIFT == OrderPatternEnum.valueOf(orderPO.getOrderPattern())) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 补录订单导入，创建补录订单方法, 暂不支持企业用户补录订单
     * @param vendor
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW, transactionManager = "masterdbTx")
    public OrderSubmitVO createOfflineOrderLocalTransaction(Vendor vendor, OrderOfflineParamDTO dto) {
        log.info("【createOfflineOrder】线下补录订单 {}", dto);
        //客户手机号为内部员工不允许下单
        Boolean isInter = ExternalApiUtil.callResultApi(() -> bmsUserFacade.isInter(dto.getUserMobile()), dto.getUserMobile(),
                "/user/isInter", "根据手机号判断是否是内部员工");
        if (!Objects.isNull(isInter) && isInter) {
            Boolean whiteList = storeIsolateWhiteListFeignClient.isStoreOnList(WhiteListEnum.ORDER_OFFLINE_INNER_STAFF, vendor.getStoreId());
            BizAssertUtil.isTrue(!whiteList, "手机号为【内部员工】手机号，请重新输入");
        }

        /////////////////////////////出单条件验证/////////////////////////////
        OrderOfflineValidation.isValidOfflineOrderBaseInfo(dto);
        //查询员工信息 20230710修改：获取员工管护分支信息，不从hr获取
        CustInfoVo custInfoVo = orderOfflineService.queryCustInfo(dto.getEmployeeCode());
        OrderOfflineValidation.isValidOfflineOrderEmployeeInfo(custInfoVo, dto.getEmployeeCode());
        //查询用户信息
        MemberExample example = new MemberExample();
        example.setMemberMobile(dto.getUserMobile());
        List<Member> members = memberFeignClient.getMemberList(example);
        OrderOfflineValidation.isValidOfflineOrderMemberInfo(dto.getUserMobile(), members);
        // 自提订单，查询自提点信息
        if (com.cfpamf.ms.mallorder.common.enums.OrderPatternEnum.SELF_LIFT.getValue().equals(dto.getOrderPattern())) {
            OrderAddressDTO orderAddressDTO = orderCreateHelper.buildOrderAddressByPointId(dto.getPointId());
            dto.setAddress(orderAddressDTO);
        }

        //线下补录订单扩展信息校验
        OrderOfflineValidation.validOfflineInfoOrder(dto.getOfflineInfoDTO());

        /////////////////////////////出单参数构建/////////////////////////////
        Member member = members.get(0);
        orderSubmitAttributesUtils.setMember(member);
        long paySn = shardingId.next(SeqEnum.PNO, member.getMemberId().toString());

        OrderSubmitParamDTO orderSubmitParam = orderCreateHelper.buildOrderSubmit(dto, member.getUserNo(), custInfoVo);

        // 构造入mq对象
        OrderSubmitMqConsumerDTO consumerDTO = orderCreateHelper.buildOrderSubmit(orderSubmitParam, dto, member, paySn);
        // 将表标识放入redis，key=标识前缀+paySn，mq处理成功或失败后删除标识
        stringRedisTemplate.opsForValue().set(OrderConst.ORDER_SUBMIT_MQ_REDIS_PREFIX + paySn, "");
        log.info("【createOfflineOrder】线下补录订单，下单前处理：{}", consumerDTO);

        /////////////////////////////创建订单/////////////////////////////
        List<String> orderSnList = orderPlacingService.createOrder(consumerDTO);

        log.info("【createOfflineOrder】线下补录订单--------------paySn:{}", paySn);
        LambdaQueryWrapper<OrderPO> orderQuery = new LambdaQueryWrapper<>();
        //orderQuery.select(OrderPO::getOrderSn);
        orderQuery.eq(OrderPO::getPaySn, consumerDTO.getPaySn());
        List<OrderPO> orderPOList = orderService.list(orderQuery);
        OrderSubmitVO vo = new OrderSubmitVO();
        vo.setPaySn(String.valueOf(paySn));
        vo.setOrderSnList(orderPOList.stream().map(OrderPO::getOrderSn).collect(Collectors.toList()));

        /////////////////////////////创建线下订单收款信息/////////////////////////////
        if (!CollectionUtils.isEmpty(dto.getOrderOfflineList())) {
            List<OrderOfflinePO> orderOfflines = OrderOfflineBuilder.buildOrderOfflinePOList(
                    vo.getPaySn(), member.getMemberName(), dto.getOrderOfflineList());
            orderOfflineService.saveBatch(orderOfflines);
        }

        /////////////////////////////线下补录扩展信息/////////////////////////////
        if (ObjectUtils.isNotEmpty(dto.getOfflineInfoDTO())) {
            orderOfflineExtendService.saveOfflineOrder(orderPOList, dto, vendor.getVendorName());
        }
        log.info("【createOfflineOrder】线下补录订单--------------paySn:{} vo:{}", paySn, vo);
        return vo;
    }

    /**
     * 自动取消自提订单
     *
     * @return 取消结果
     */
    public boolean jobSystemCancelSelfLiftOrder() {
        //获取自动取消时间限制（小时）
//        String value = stringRedisTemplate.opsForValue().get("time_limit_of_auto_cancle_order");
//        int limitHour = value == null ? 12 : Integer.parseInt(value);

        Integer minutes = getAutoCancelMinutes(OrderPatternEnum.SELF_LIFT.getValue(), OrderTypeEnum.NORMAL.getValue());
        log.info("jobSystemCancelSelfLiftOrder minutes:{}", minutes);

        // 获取当前时间limitHour小时之前的时间
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, -minutes);

        Date cancelTime = calendar.getTime();

        // 订单信息
        LambdaQueryWrapper<OrderPO> queryWrapper = Wrappers.lambdaQuery(OrderPO.class);
        queryWrapper.le(OrderPO::getCreateTime, cancelTime);
        queryWrapper.ne(OrderPO::getExchangeFlag, ExchangeOrderConst.ORDER_EXCHANGE_FLAG_2);
        queryWrapper.in(OrderPO::getOrderType, Arrays.asList(OrderConst.ORDER_TYPE_1, OrderConst.ORDER_TYPE_7, OrderConst.ORDER_TYPE_11, OrderConst.ORDER_TYPE_13));
        // 必须是自提
        queryWrapper.eq(OrderPO::getOrderPattern, OrderPatternEnum.SELF_LIFT.getValue());
        queryWrapper.and(
                wrapper -> wrapper.eq(OrderPO::getOrderState, OrderStatusEnum.WAIT_PAY.getValue())
                        .or(
                                wrapper2 -> wrapper2.eq(OrderPO::getOrderState, OrderStatusEnum.DEAL_PAY.getValue())
                                        .eq(OrderPO::getPaymentCode, PayMethodEnum.BANK_TRANSFER.getValue())));
        queryWrapper.orderByDesc(OrderPO::getParentSn);
        List<OrderPO> parentOrderPOList = orderService.list(queryWrapper);

        Map<String, List<OrderPO>> map = parentOrderPOList.stream().collect(Collectors.groupingBy(OrderPO::getParentSn));
        if (!CollectionUtils.isEmpty(parentOrderPOList)) {
            // 已经执行取消的父订单号
            List<String> alreadyCanceledParentSn = new ArrayList<>();

            for (OrderPO orderPO : parentOrderPOList) {
                if (alreadyCanceledParentSn.contains(orderPO.getParentSn())) {
                    continue;
                }
                try {
                    //根据父订单号查询待付款的子订单
                    List<OrderPO> orderPOList = map.get(orderPO.getParentSn());
                    //取消订单
                    orderModel.cancelOrder(orderPOList, OrderConst.WAIT_PAY_ORDER_EXPIRE_REASON, null,
                            OrderConst.LOG_ROLE_ADMIN, OrderConst.LOG_USER_ID, OrderConst.LOG_USER_NAME,
                            "系统自动取消订单", OrderConst.RETURN_BY_0);
                    alreadyCanceledParentSn.add(orderPO.getParentSn());
                } catch (Exception e) {
                    log.error("系统自动取消{}分钟没有付款订单，异常继续处理后续订单，parentOrderSn:{}", minutes,
                            orderPO.getParentSn(), e);
                }
            }
        }
        return true;
    }

    /**
     * 订单自动取消时间：分钟
     * @param orderPattern
     * @param orderType
     * @return
     */
    public Integer getAutoCancelMinutes(Integer orderPattern, Integer orderType) {

        /* 自提订单取消时间 */
        if (OrderPatternEnum.SELF_LIFT.getValue().equals(orderPattern)) {
            List<DictionaryItemVO> dictionary = bmsIntegration.listDictionaryItemsByTypeCode(CommonConst.SELF_LIFT_AUTO_CANCEL_MINUTES, CommonConst.MALL_SYSTEM_MANAGE_ID);
            if (CollectionUtils.isEmpty(dictionary)) {
                return CommonConst.DEFAULT_SELF_LIFT_AUTO_CANCEL_MINUTES;
            }
            DictionaryItemVO dictionaryItemVO = dictionary.get(0);
            return Integer.valueOf(dictionaryItemVO.getItemDesc());
        }

        /* 秒杀订单取消时间 */
        if (OrderTypeEnum.SECONDS_KILL.getValue().equals(orderType)) {
            String valueKill = stringRedisTemplate.opsForValue().get("seckill_order_cancle");
            return valueKill == null ? 5 : Integer.parseInt(valueKill);
        }

        /* 新人专享订单取消时间 */
        if (OrderTypeEnum.NEW_PEOPLE.getValue().equals(orderType)) {
            String valueNewPeople = stringRedisTemplate.opsForValue().get("exclusive_order_cancle");
            return valueNewPeople == null ? 30 : Integer.parseInt(valueNewPeople);
        }

        /* 拼团订单取消时间 */
        if (OrderTypeEnum.SPELL_GROUP.getValue().equals(orderType)) {
            String value = stringRedisTemplate.opsForValue().get("spell_order_auto_cancel_time");
            return value == null ? 30 : Integer.parseInt(value);
        }

        /* 其他订单取消时间 */
        List<DictionaryItemVO> dictionary = bmsIntegration.listDictionaryItemsByTypeCode(CommonConst.NOT_SELF_LIST_AUTO_CANCEL_MINUTES, CommonConst.MALL_SYSTEM_MANAGE_ID);
        if (CollectionUtils.isEmpty(dictionary)) {
            return CommonConst.DEFAULT_NOT_SELF_LIST_AUTO_CANCEL_MINUTES;
        }
        DictionaryItemVO dictionaryItemVO = dictionary.get(0);
        return Integer.parseInt(dictionaryItemVO.getItemDesc());
    }
}