package com.cfpamf.ms.mallorder.controller.fegin.facade.workflow.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
public class NotifyDTO implements Serializable {
    @ApiModelProperty("流程实例ID")
    private String procInstId;
    @ApiModelProperty("流程定义的key")
    private String procDefKey;
    @ApiModelProperty("节点定义的key")
    private String nodeKey;
    @ApiModelProperty("业务ID")
    private String bizId;
    @ApiModelProperty("流程处理结果：true成功，false失败")
    private Boolean executeResult;
    @ApiModelProperty("操作类型")
    private String optType;
}
