package com.cfpamf.ms.mallorder.integration.facade.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "还款计划表预览请求参数")
public class RepaymentScheduleRequest {

    // 必传
    @ApiModelProperty(value = "电商订单号（8开头），农服支付号（3开头）唯一", required = true)
    private String orderBatchId;

    // 必传
    @ApiModelProperty(value = "客户号", required = true)
    private String loanCustId;

    @ApiModelProperty(value = "分支编号", required = true)
    private String branchId;

    @ApiModelProperty(value = "操作人工号", required = true)
    private String operator;

    //默认 agric
    @ApiModelProperty(value = "订单来源CDMALL/agric")
    private String orderSource;
}
