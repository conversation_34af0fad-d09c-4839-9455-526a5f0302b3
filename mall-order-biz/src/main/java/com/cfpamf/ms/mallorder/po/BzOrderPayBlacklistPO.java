package com.cfpamf.ms.mallorder.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cfpamf.ms.mallorder.common.annotation.PoiExportProperty;
import com.cfpamf.ms.mallorder.common.base.BasePO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 支付黑名单表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bz_order_pay_blacklist")
@ApiModel(value="BzOrderPayBlacklistPO对象", description="支付黑名单表")
public class BzOrderPayBlacklistPO extends BasePO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "支付方式id")
    private Long payId;

    @ApiModelProperty(value = "商品id")
    @PoiExportProperty(name = "商品SKU", order = 4)
    private Long productId;

    @ApiModelProperty(value = "商品名称为3到50个字符(商品副标题)")
    @PoiExportProperty(name = "商品名称", order = 3)
    private String goodsName;

    @ApiModelProperty(value = "规格值的ID，用逗号分隔")
    private String specValueIds;

    @ApiModelProperty(value = "规格值，用逗号分隔")
    @PoiExportProperty(name = "商品规格", order = 5)
    private String specValues;

    @ApiModelProperty(value = "店铺id")
    @PoiExportProperty(name = "店铺id", order = 2)
    private Long storeId;

    @ApiModelProperty(value = "店铺名称")
    @PoiExportProperty(name = "店铺名称", order = 1)
    private String storeName;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "一级类目id")
    private Integer categoryId1;

    @ApiModelProperty(value = "二级类目id")
    private Integer categoryId2;

    @ApiModelProperty(value = "三级类目id")
    private Integer categoryId3;

    @ApiModelProperty(value = "一级类目名")
    private String categoryName1;

    @ApiModelProperty(value = "二级类目名")
    private String categoryName2;

    @ApiModelProperty(value = "三级类目名")
    private String categoryName3;

    @ApiModelProperty(value = "总商品id")
    private Long goodsId;

    @ApiModelProperty(value = "商品所属分类路径，(例如：分类1/分类2/分类3，前后都无斜杠)")
    @PoiExportProperty(name = "所属类目", order = 6)
    private String categoryPath;
}
