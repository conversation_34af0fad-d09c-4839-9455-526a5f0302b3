package com.cfpamf.ms.mallorder.integration.system;

import com.cfpamf.ms.mallorder.common.util.ExternalApiUtil;
import com.cfpamf.ms.mallorder.dto.OrderTradeProofQueryDTO;
import com.cfpamf.ms.mallsystem.api.TradeDocumentFeignClient;
import com.cfpamf.ms.mallsystem.request.OrderSubmitRuleMatchDTO;
import com.cfpamf.ms.mallsystem.request.OrderTradeMatchDTO;
import com.cfpamf.ms.mallsystem.request.TradeDocumentDto;
import com.cfpamf.ms.mallsystem.vo.OrderDataPushHitObject;
import com.cfpamf.ms.mallsystem.vo.TradeDocumentVo;
import com.slodon.bbc.core.response.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class TradeDocumentIntegration {

    @Autowired
    private TradeDocumentFeignClient tradeDocumentFeignClient;

    public TradeDocumentVo matchOrderSceneMaterials(OrderTradeProofQueryDTO queryDTO) {

        TradeDocumentDto documentDto = new TradeDocumentDto();
        documentDto.setStoreId(queryDTO.getStoreId());
        documentDto.setTradePayMethod(queryDTO.getPaymentCode());
        documentDto.setTradePerformanceMode(queryDTO.getPerformanceModes());
        documentDto.setGoodsCategory1(queryDTO.getGoodsCategoryId1());
        documentDto.setGoodsCategory2(queryDTO.getGoodsCategoryId2());
        documentDto.setGoodsCategory3(queryDTO.getGoodsCategoryId3());
        if (queryDTO.getOrderPattern() != null) {
            documentDto.setGoodsType(queryDTO.getOrderPattern().toString());
        }
        documentDto.setTradeOrderAmount(queryDTO.getOrderAmount());
        documentDto.setTradeOrderNode(queryDTO.getSceneNo());
        documentDto.setZone(queryDTO.getZone());
        documentDto.setBranch(queryDTO.getBranch());

        JsonResult<TradeDocumentVo> tradeDocumentVo = ExternalApiUtil.callJsonResultApi(() ->
                        tradeDocumentFeignClient.uploadInfo(documentDto), documentDto,
                "system/tradeDocument/uploadInfo", "匹配订单规则");

        return tradeDocumentVo.getData();
    }

    public List<OrderDataPushHitObject> orderDataPushMatch(OrderTradeMatchDTO orderTradeMatchDTO) {

        JsonResult<List<OrderDataPushHitObject>> result = ExternalApiUtil.callJsonResultApi(() ->
                        tradeDocumentFeignClient.orderDataPushMatch(orderTradeMatchDTO), orderTradeMatchDTO,
                "system/tradeDocument/dataPushMatch", "订单喜报推送匹配");

        return result.getData();
    }

    public List<OrderDataPushHitObject> orderSubmitRuleMatch(OrderSubmitRuleMatchDTO matchDTO) {

        JsonResult<List<OrderDataPushHitObject>> result = ExternalApiUtil.callJsonResultApi(() ->
                        tradeDocumentFeignClient.orderSubmitMatch(matchDTO), matchDTO,
                "system/tradeDocument/orderSubmitMatch", "订单下单限制规则匹配");

        return result.getData();
    }

}
