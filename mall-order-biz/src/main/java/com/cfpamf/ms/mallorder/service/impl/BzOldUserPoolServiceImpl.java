package com.cfpamf.ms.mallorder.service.impl;

import com.cfpamf.ms.mallorder.po.BzOldUserPoolPO;
import com.cfpamf.ms.mallorder.mapper.BzOldUserPoolMapper;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.service.IBzOldUserPoolService;
import com.cfpamf.framework.autoconfigure.mybatis.BaseRepoServiceImpl;
import com.cfpamf.ms.mallorder.service.IOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * <p>
 * 订单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-16
 */
@Service
public class BzOldUserPoolServiceImpl extends BaseRepoServiceImpl<BzOldUserPoolMapper, BzOldUserPoolPO> implements IBzOldUserPoolService {
    @Autowired
    private IOrderService iOrderService;

    /**
     * 根据用户编号判断是否贷款首单,true为首单，false为老用户
     *
     * @param userNo
     * @return
     */
    @Override
    public Boolean isFirstLoanOrderByUserNo(String userNo) {
        BzOldUserPoolPO userPoolPo = this.lambdaQuery()
                .eq(BzOldUserPoolPO::getUserNo, userNo)
                .last("limit 1")
                .one();
        return Objects.isNull(userPoolPo);
    }

    /**
     * 根据订单号恢复首单
     *
     * @param orderSn
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean restoreFirstLoanOrder(String orderSn) {
        OrderPO orderPO = iOrderService.lambdaQuery()
                .eq(OrderPO::getOrderSn,orderSn)
                .last("limit 1")
                .one();
        if (Objects.isNull(orderPO)) {
            return Boolean.TRUE;
        }
        BzOldUserPoolPO oldUserPool = this.lambdaQuery()
                .eq(BzOldUserPoolPO::getUserNo, orderPO.getUserNo())
                .eq(BzOldUserPoolPO::getOrderSn, orderPO.getOrderSn())
                .last("limit 1")
                .one();
        if (Objects.isNull(oldUserPool)) {
            return Boolean.TRUE;
        }
        return this.removeById(oldUserPool);
    }

    /**
     * 支付成功（保存申请单成功）置为老用户池
     *
     * @param userNo
     * @param orderSn
     * @return
     */
    @Override
    public Boolean insertFirstLoanOrder(String userNo, String orderSn) {
        Boolean isFirstLoanOrder = this.isFirstLoanOrderByUserNo(userNo);
        if (Boolean.FALSE.equals(isFirstLoanOrder)) {
            return Boolean.TRUE;
        }
        BzOldUserPoolPO userPoolPo = new BzOldUserPoolPO();
        userPoolPo.setUserNo(userNo);
        userPoolPo.setOrderSn(orderSn);
        return this.save(userPoolPo);
    }
}
