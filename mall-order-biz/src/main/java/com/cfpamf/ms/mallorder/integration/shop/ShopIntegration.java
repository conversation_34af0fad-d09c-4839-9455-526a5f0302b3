package com.cfpamf.ms.mallorder.integration.shop;

import com.cfpamf.ms.mallorder.common.util.ExternalApiUtil;
import com.cfpamf.ms.mallshop.api.HomeServiceFeignClient;
import com.cfpamf.ms.mallshop.api.SelfLiftingPointFeignClient;
import com.cfpamf.ms.mallshop.api.StoreIsolateWhiteListFeignClient;
import com.cfpamf.ms.mallshop.api.StoreLabelBindGoodsFeignClient;
import com.cfpamf.ms.mallshop.enums.WhiteListEnum;
import com.cfpamf.ms.mallshop.vo.SelfLiftingPointVo;
import com.cfpamf.ms.mallshop.vo.SimpleSelfLiftingPointVo;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.response.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class ShopIntegration {
	@Autowired
	private StoreIsolateWhiteListFeignClient storeIsolateWhiteListFeignClient;

	@Autowired
	private HomeServiceFeignClient homeServiceFeignClient;

	@Autowired
	private SelfLiftingPointFeignClient selfLiftingPointFeignClient;

	@Autowired
	private StoreLabelBindGoodsFeignClient storeLabelBindGoodsFeignClient;

	/**
	 * 查询店铺是否在白名单内
	 *
	 * @param whiteListEnum
	 * @param storeId
	 * @return
	 */
	public Boolean isStoreOnList(WhiteListEnum whiteListEnum, Long storeId) {
		return ExternalApiUtil.callMq(() -> storeIsolateWhiteListFeignClient.isStoreOnList(whiteListEnum, storeId), storeId,
				"/storeIsolateWhiteList/isStoreOnList", "查询店铺白名单");
	}

	public Boolean isFactoryBind(String dealerCode, String storeId) {
		JsonResult<Boolean> result = ExternalApiUtil.callJsonResultApi(() -> homeServiceFeignClient.isFactoryBind(dealerCode, storeId), dealerCode,
				"/homeService/factory/bind", "查询经销商是否有效");
		if (result.getData() == null) {
			throw new BusinessException("调用商户查询经销商是否有效失败，数据返回null");
		}
		return result.getData();
	}

	public SelfLiftingPointVo getSelfLiftingPoint(Long pointId) {
		JsonResult<SelfLiftingPointVo> result = ExternalApiUtil.callJsonResultApi(() -> selfLiftingPointFeignClient.getSelfLiftingPoint(pointId), pointId,
				"/seller/point/detail", "查询自提点信息");
		if (result.getData() == null) {
			throw new BusinessException("调用商户查询自提点不存在，数据返回null");
		}
		return result.getData();
	}

	/**
	 * 根据自提点ID查询自提点信息，异常则返回null
	 */
	public SelfLiftingPointVo getSelfLiftingPointV2(Long pointId) {
		try {
			JsonResult<SelfLiftingPointVo> result = ExternalApiUtil.callJsonResultApi(() -> selfLiftingPointFeignClient.getSelfLiftingPoint(pointId), pointId,
					"/seller/point/detail", "查询自提点信息");
			return result.getData();
		} catch (BusinessException e) {
			log.warn("调用商户查询自提点不存在, 数据返回null, pointId: {}", pointId);
		}
		return null;
	}

	/**
	 * 批量根据自提点id获取自提点信息
	 *
	 * @param pointIdList 自提点id列表
	 * @return 自提点列表
	 */
	public List<SimpleSelfLiftingPointVo> getSelfLiftingPointBatchByPointId(List<Long> pointIdList) {
		JsonResult<List<SimpleSelfLiftingPointVo>> result = ExternalApiUtil.callJsonResultApi(() -> selfLiftingPointFeignClient.getSelfLiftingPointListByIds(pointIdList), pointIdList,
				"/v1/feign/seller/point/getSelfLiftingPointListByIds", "批量查询自提点信息");
		if (result.getData() == null) {
			throw new BusinessException("调用商户查询自提点不存在，数据返回null");
		}
		return result.getData();
	}

	/**
	 * 根据店铺和分支编码查询自提点
	 *
	 * @param storeId 店铺id
	 * @param branch 分支
	 * @return 自提点
	 */
	public SelfLiftingPointVo getSelfLiftingPointByStoreIdAndBranch(Long storeId, String branch) {
		JsonResult<SelfLiftingPointVo> result = ExternalApiUtil.callJsonResultApi(() -> selfLiftingPointFeignClient.getSelfLiftingPointByStoreIdAndBranchCode(storeId,branch), storeId,
				"/v1/feign/seller/point/getSelfLiftingPointByStoreIdAndBranchCode", "根据店铺id、分支编码查询自提点");
		if (result.getData() == null) {
			throw new BusinessException("调用商户查询自提点不存在，数据返回null");
		}
		return result.getData();
	}

}
