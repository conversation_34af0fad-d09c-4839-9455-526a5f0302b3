package com.cfpamf.ms.mallorder.controller.fegin.facade;


import com.cfpamf.ms.mallorder.model.OrderExtendModel;
import com.cfpamf.ms.mallorder.po.OrderExtendPO;
import com.cfpamf.ms.mallorder.request.OrderExtendExample;
import com.cfpamf.ms.mallorder.service.IOrderExtendService;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Slf4j
public class OrderExtendFeign {

    @Resource
    private OrderExtendModel orderExtendModel;

    @Autowired
    private IOrderExtendService orderExtendService;

    /**
     * 根据extendId获取订单信息扩展表详情
     *
     * @param extendId extendId
     * @return
     */
    @GetMapping("/v1/feign/business/orderExtend/get")
    public OrderExtendPO getOrderExtendByExtendId(@RequestParam("extendId") Integer extendId) {
        return orderExtendModel.getOrderExtendByExtendId(extendId);
    }

    /**
     * 获取条件获取订单信息扩展表列表
     *
     * @param example 查询条件信息
     * @return
     */
    @PostMapping("/v1/feign/business/orderExtend/getList")
    public List<OrderExtendPO> getOrderExtendList(@RequestBody OrderExtendExample example) {
        return orderExtendModel.getOrderExtendList(example, example.getPager());
    }

    @GetMapping("/v1/feign/business/orderExtend/orderSn")
    public OrderExtendPO getOrderExtendByOrderSn(@RequestParam("orderSn") String orderSn){
        return orderExtendModel.getOrderExtendByOrderSnLambda(orderSn);
    }

    /**
     * 新增订单信息扩展表
     *
     * @param orderExtendPO
     * @return
     */
    @PostMapping("/v1/feign/business/orderExtend/addOrderExtend")
    public Integer saveOrderExtend(@RequestBody OrderExtendPO orderExtendPO) {
        return orderExtendModel.saveOrderExtend(orderExtendPO);
    }

    /**
     * 根据extendId更新订单信息扩展表
     *
     * @param orderExtendPO
     * @return
     */
    @PostMapping("/v1/feign/business/orderExtend/updateOrderExtend")
    public Integer updateOrderExtend(@RequestBody OrderExtendPO orderExtendPO) {
        return orderExtendModel.updateOrderExtend(orderExtendPO);
    }

    /**
     * 根据extendId删除订单信息扩展表
     *
     * @param extendId extendId
     * @return
     */
    @PostMapping("/v1/feign/business/orderExtend/deleteOrderExtend")
    public Integer deleteOrderExtend(@RequestParam("extendId") Integer extendId) {
        return orderExtendModel.deleteOrderExtend(extendId);
    }

    @GetMapping("/v1/feign/business/orderExtend/obtainSelfLiftingPoint")
    public JsonResult<Long> obtainSelfLiftingPoint(@RequestParam("memberId") Integer memberId,
                                                   @RequestParam("storeId") String storeId) {
        return SldResponse.success(orderExtendModel.obtainSelfLiftingPoint(memberId, storeId));
    }

    @PostMapping("/v1/feign/business/orderExtend/dealHistoryWareHouse")
    public JsonResult<String> dealHistoryWareHouse(@RequestBody String ignoreOrderSn){
        orderExtendService.dealHistoryActualWareHouse(ignoreOrderSn);
        return SldResponse.success("成功");
    }

    @PostMapping("/v1/feign/business/orderExtend/orderPointFixJob")
    public JsonResult<String> orderPointFixJob(@RequestBody List<String> orderSnList){
        orderExtendService.orderPointFix(orderSnList);
        return SldResponse.success("成功");
    }

}