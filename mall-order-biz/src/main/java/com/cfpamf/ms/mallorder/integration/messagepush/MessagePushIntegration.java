package com.cfpamf.ms.mallorder.integration.messagepush;


import com.cfpamf.ms.mallorder.integration.facade.TemplateMessageFacade;
import com.cfpamf.msgpush.facade.enums.ReceiverTypeEnum;
import com.cfpamf.msgpush.facade.request.templateMessage.NormalBizMessageTemplateReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;

/**
 * 消息推送对接
 */
@Slf4j
@Component
public class MessagePushIntegration {

    @Resource
    private TemplateMessageFacade templateMessageFacade;

    /**
     * 智能营销平台短信发送 "mallOrderReceiveCode"
     */
    public void sendSms(String bizType, ReceiverTypeEnum receiverTypeEnum, String receiver, HashMap<String, String> paramMap) {
        try {
            NormalBizMessageTemplateReq normalBizMessageTemplateReq = new NormalBizMessageTemplateReq();
            normalBizMessageTemplateReq.setReceiverTypeEnum(receiverTypeEnum);
            normalBizMessageTemplateReq.setReceiver(receiver);
            normalBizMessageTemplateReq.setBizParams(paramMap);
            normalBizMessageTemplateReq.setBizType(bizType);
            templateMessageFacade.bizTemplateSend(normalBizMessageTemplateReq);
        } catch (Exception e) {
            log.error("MessagePushIntegration短信发送失败,", e.getMessage());
        }

    }
}
