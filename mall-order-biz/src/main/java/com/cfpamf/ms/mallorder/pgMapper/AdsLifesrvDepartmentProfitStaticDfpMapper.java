package com.cfpamf.ms.mallorder.pgMapper;

import com.cfpamf.framework.autoconfigure.mybatis.MyBaseMapper;
import com.cfpamf.ms.mallorder.po.pgrpt.AdsLifesrvDepartmentProfitStaticDfpPO;
import com.cfpamf.ms.mallorder.req.LifeSrvIncomeReportRequest;
import com.cfpamf.ms.mallorder.vo.pgrpt.LifesrvDepartmentProfitBasicVO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-30
 */
public interface AdsLifesrvDepartmentProfitStaticDfpMapper extends MyBaseMapper<AdsLifesrvDepartmentProfitStaticDfpPO> {
    /**
     * 查询利润统计指标
     */
    AdsLifesrvDepartmentProfitStaticDfpPO getBasicMetrics(@Param("request") LifeSrvIncomeReportRequest request);
}
