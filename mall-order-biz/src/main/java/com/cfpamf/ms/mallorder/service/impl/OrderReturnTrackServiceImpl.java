package com.cfpamf.ms.mallorder.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cfpamf.ms.mallorder.common.enums.AuditResultEnum;
import com.cfpamf.ms.mallorder.common.enums.OrderReturnOperateTypeEnum;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.OperatorDTO;
import com.cfpamf.ms.mallorder.mapper.OrderReturnTrackMapper;
import com.cfpamf.ms.mallorder.po.OrderReturnTrackPO;
import com.cfpamf.ms.mallorder.service.IOrderReturnTrackService;
import com.cfpamf.ms.mallorder.vo.OrderReturnTrackVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 退款轨迹实现类
 */
@Slf4j
@Service
public class OrderReturnTrackServiceImpl extends ServiceImpl<OrderReturnTrackMapper, OrderReturnTrackPO> implements IOrderReturnTrackService {

    @Override
    public List<OrderReturnTrackVO> getOrderReturnTrackVOByAfsSn(String afsSn) {
        BizAssertUtil.notEmpty(afsSn, "退款单号不能为空");
        LambdaQueryWrapper<OrderReturnTrackPO> queryWrapper = Wrappers.lambdaQuery(OrderReturnTrackPO.class);
        queryWrapper.eq(OrderReturnTrackPO::getAfsSn, afsSn)
                .eq(OrderReturnTrackPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                .orderByDesc(OrderReturnTrackPO::getOperateTime)
                .orderByDesc(OrderReturnTrackPO::getId);
        List<OrderReturnTrackPO> orderReturnTrackPOs = this.list(queryWrapper);

        List<OrderReturnTrackVO> orderReturnTrackVOs = new ArrayList<>(orderReturnTrackPOs.size());
        for (OrderReturnTrackPO po : orderReturnTrackPOs) {
            orderReturnTrackVOs.add(new OrderReturnTrackVO(po));
        }
        return orderReturnTrackVOs;
    }

    /**
     * 根据售后单号和售后类型查询退款轨迹
     */
    public OrderReturnTrackVO getOrderReturnTrackVOByAfsSnAndType(String afsSn, OrderReturnOperateTypeEnum type) {
        BizAssertUtil.notEmpty(afsSn, "退款单号不能为空");
        BizAssertUtil.notNull(type, "售后类型不能为空");
        OrderReturnTrackPO orderReturnTrackPO = lambdaQuery().eq(OrderReturnTrackPO::getAfsSn, afsSn)
                .eq(OrderReturnTrackPO::getOperateType, type.getValue())
                .eq(OrderReturnTrackPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                .one();
        if (Objects.isNull(orderReturnTrackPO)) {
            return null;
        }
        return new OrderReturnTrackVO(orderReturnTrackPO);
    }

    public boolean insertOrderReturnTrack(String afsSn, OrderReturnOperateTypeEnum operateType, OperatorDTO operator,
                                       Integer operateResult, String operateRemark, String channel) {
        OrderReturnTrackPO orderReturnTrackPO = OrderReturnTrackPO
                .builder()
                .afsSn(afsSn)
                .operateType(operateType.getValue())
                .operator(operator.getUserName() + "-" + operator.getMobile())
                .operateTime(new Date())
                .operatorRole(operator.getRoleId())
                .operateResult(operateResult)
                .operateRemark(operateRemark)
                .channel(channel)
                .build();
        return super.save(orderReturnTrackPO);
    }

}
