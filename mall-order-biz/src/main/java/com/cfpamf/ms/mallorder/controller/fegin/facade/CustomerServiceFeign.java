package com.cfpamf.ms.mallorder.controller.fegin.facade;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.customer.facade.request.bankcard.QueryBankcardInfoReq;
import com.cfpamf.ms.customer.facade.request.credit.QueryCreditLimitReq;
import com.cfpamf.ms.customer.facade.request.receiveInfo.CreateReceiveInfoReq;
import com.cfpamf.ms.customer.facade.request.receiveInfo.DeleteReceiveInfoReq;
import com.cfpamf.ms.customer.facade.request.receiveInfo.ModifyReceiveInfoReq;
import com.cfpamf.ms.customer.facade.request.receiveInfo.QueryReceiveInfoReq;
import com.cfpamf.ms.customer.facade.request.user.QueryPushInfoReq;
import com.cfpamf.ms.customer.facade.request.user.QueryUserBaseInfoReq;
import com.cfpamf.ms.customer.facade.vo.CustCreditLimitListVo;
import com.cfpamf.ms.customer.facade.vo.CustInfoVo;
import com.cfpamf.ms.customer.facade.vo.UserInfoVo;
import com.cfpamf.ms.customer.facade.vo.bankcard.BankcardVo;
import com.cfpamf.ms.customer.facade.vo.receiveInfo.ReceiveInfoVo;
import com.cfpamf.ms.customer.facade.vo.user.UserBaseInfoVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/*
 * @Classname CustserviceFegin 调用客户中心的接口
 * @Description
 * <AUTHOR>
 * @Date 2021/5/30 13:25
 * @Version 1.0
 *
*/

@FeignClient(
        name = "ms-service-customer",
        url = "${ms-service-customer.url}"
)
public interface CustomerServiceFeign {
    @PostMapping({"/user/baseInfoByUserNo"})
    Result<UserBaseInfoVo> baseInfoByUserNo(@RequestBody QueryUserBaseInfoReq var1);

    @PostMapping({"/receiveInfo/create"})
    Result<Void> create(@RequestBody CreateReceiveInfoReq var1);

    @PostMapping({"/receiveInfo/query"})
    Result<List<ReceiveInfoVo>> query(@RequestBody QueryReceiveInfoReq var1);

    @GetMapping(value = {"/receiveInfo/info"})
    Result<ReceiveInfoVo> info(@RequestParam("id") String var1);

    @PostMapping({"/receiveInfo/modify"})
    Result<Void> modify(@RequestBody ModifyReceiveInfoReq var1);

    @PostMapping({"/receiveInfo/delete"})
    Result<Void> delete(@RequestBody DeleteReceiveInfoReq var1);

    @PostMapping({"/cust/queryByCustNo"})
    Result<CustInfoVo> queryByCustNo(@RequestParam("custNo") String var1);

    @PostMapping(value = "/cust/credit/queryLimitList")
    Result<CustCreditLimitListVo> queryLimitList(@RequestBody QueryCreditLimitReq req);

    @PostMapping({"/bankcard/info"})
    Result<BankcardVo> info(@RequestBody QueryBankcardInfoReq var1);

    @PostMapping(value = "/user/queryPushInfo")
    Result<List<UserInfoVo>> queryPushInfo(@RequestBody QueryPushInfoReq req);
}
