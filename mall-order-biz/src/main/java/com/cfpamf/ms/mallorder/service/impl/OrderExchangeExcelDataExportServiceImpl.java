package com.cfpamf.ms.mallorder.service.impl;

import com.cfpamf.ms.mall.filecenter.component.FileComponent;
import com.cfpamf.ms.mall.filecenter.domain.dto.FileDTO;
import com.cfpamf.ms.mall.filecenter.domain.vo.ExcelAsyncExportVO;
import com.cfpamf.ms.mall.filecenter.service.impl.AbstractExcelDataExportServiceImpl;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.integration.erp.ERPIntegration;
import com.cfpamf.ms.mallorder.integration.facade.ErpProductAndSkuVo;
import com.cfpamf.ms.mallorder.mapper.OrderExchangeMapper;
import com.cfpamf.ms.mallorder.model.OrderProductModel;
import com.cfpamf.ms.mallorder.po.OrderProductErpExtendPO;
import com.cfpamf.ms.mallorder.po.OrderProductPO;
import com.cfpamf.ms.mallorder.req.ExchangeApplyListPcReq;
import com.cfpamf.ms.mallorder.service.IOrderExchangeExcelDataExportService;
import com.cfpamf.ms.mallorder.service.IOrderExchangeService;
import com.cfpamf.ms.mallorder.service.OrderProductErpExtendService;
import com.cfpamf.ms.mallorder.vo.OrderExchangeReturnListVO;
import com.google.api.client.util.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;


@Slf4j
@Service
public class OrderExchangeExcelDataExportServiceImpl extends AbstractExcelDataExportServiceImpl<OrderExchangeReturnListVO, ExchangeApplyListPcReq>
        implements IOrderExchangeExcelDataExportService {

    @Resource
    private FileComponent fileComponent;


    @Value("${spring.application.name}")
    private String appName;

    @Resource
    private OrderExchangeMapper orderExchangeMapper;

    @Resource
    private OrderProductModel orderProductModel;

    @Resource
    private ERPIntegration erpIntegration;

    @Resource
    private OrderProductErpExtendService orderProductErpExtendService;

    @Override
    public FileDTO executeAsyncExportExcel(ExchangeApplyListPcReq exchangeApplyListReq) throws Exception {
        ExcelAsyncExportVO excelAsyncExportVO = new ExcelAsyncExportVO();
        String bizModule = "换货申请";
        excelAsyncExportVO.setBizModule(bizModule);
        excelAsyncExportVO.setUserId(Long.valueOf(exchangeApplyListReq.getUserDTO().getUserId()));
        excelAsyncExportVO.setUserName(exchangeApplyListReq.getUserDTO().getUserName());
        excelAsyncExportVO.setUserType(getUserType(exchangeApplyListReq.getUserDTO().getUserRole()));
        excelAsyncExportVO.setApplicationCondition(exchangeApplyListReq.getApplyCondition());
        return fileComponent.executeAsyncExportExcelWithAnnotation(excelAsyncExportVO, appName, CommonConst.ORDER_EXCEL_SHEET_NAME,
                this, exchangeApplyListReq, 1000, new OrderExchangeReturnListVO());
    }

    @Override
    public Integer getDataCounts(ExchangeApplyListPcReq exchangeApplyListPcReq) {
        return orderExchangeMapper.getExchangeApplyListCountNew(exchangeApplyListPcReq);
    }

    @Override
    public List<OrderExchangeReturnListVO> getDataByPage(ExchangeApplyListPcReq exchangeApplyListPcReq, Integer pageNum, Integer pageSize) {
        log.info("OrderExchangeExcelDataExportServiceImpl getDataByPage pageNum = {}, pageSize= {}", pageNum, pageSize);
        int startRow = (pageNum - 1) * pageSize;
        List<OrderExchangeReturnListVO> exchangeApplyListPageNew = orderExchangeMapper.getExchangeApplyListPageNew(exchangeApplyListPcReq, startRow, pageSize);
        // 获取对应订单商品行id集合
        List<Long> orderProductIdList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(exchangeApplyListPageNew)) {
            exchangeApplyListPageNew.forEach(item -> {
                if (Objects.nonNull(item.getOrderProductId())) {
                    orderProductIdList.add(item.getOrderProductId());
                }
                if (Objects.nonNull(item.getExchangeOrderProductId())) {
                    orderProductIdList.add(item.getExchangeOrderProductId());
                }
            });

            if (CollectionUtils.isNotEmpty(orderProductIdList)) {
                // 查询新老商品行信息
                List<Long> orderProductIds = orderProductIdList.stream().distinct().collect(Collectors.toList());
                List<OrderProductPO> orderProductPoList = orderProductModel.getOrderProductListByOrderProductIds(orderProductIds);
                Map<Long, OrderProductPO> orderProductPoMap = orderProductPoList.stream().collect(Collectors.toMap(OrderProductPO::getOrderProductId, Function.identity(), (o, n) -> n));
                // 根据订单商品行id查询erp物料拓展信息
                Map<Long, OrderProductErpExtendPO> orderProductErpExtendPoMap = orderProductErpExtendService.getOrderProductErpExtendMapByOrderProductIds(orderProductIds);
                List<String> skuIdList =  orderProductPoList.stream().map(OrderProductPO::getChannelNewSkuId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());

                // 获取商品erp规格值
                Map<String, ErpProductAndSkuVo> erpProductMap = Maps.newHashMap();
                if (CollectionUtils.isNotEmpty(skuIdList)) {
                    List<ErpProductAndSkuVo> erpProductList = erpIntegration.getProductAndSkuListBySkuIds(skuIdList);
                    erpProductMap = erpProductList.stream().collect(Collectors.toMap(ErpProductAndSkuVo::getSkuId, Function.identity(), (o, n) -> n));
                }

                // 填充物料相关信息
                Map<String, ErpProductAndSkuVo> finalErpProductMap = erpProductMap;
                exchangeApplyListPageNew.forEach(item -> {
                    ///////////////////////////////////// 原订单商品号信息 ///////////////////////////////////
                    OrderProductErpExtendPO productErpExtendPo = orderProductErpExtendPoMap.getOrDefault(item.getOrderProductId(), null);
                    if (Objects.nonNull(productErpExtendPo)) {
                        item.setOriginProductCategoryPathName(productErpExtendPo.getProductCategoryPathName());
                    }


                    if (Objects.nonNull(item.getOrderProductId()) && orderProductPoMap.containsKey(item.getOrderProductId())) {
                        OrderProductPO orderProductPo = orderProductPoMap.get(item.getOrderProductId());
                        item.setOriginSkuMaterialCode(orderProductPo.getSkuMaterialCode());
                        item.setOriginWmsSkuId(orderProductPo.getChannelNewSkuId());
                        if (StringUtils.isNotBlank(orderProductPo.getChannelNewSkuId()) && finalErpProductMap.containsKey(orderProductPo.getChannelNewSkuId())) {
                            ErpProductAndSkuVo erpProductAndSkuVo = finalErpProductMap.get(orderProductPo.getChannelNewSkuId());
                            item.setOriginWmsSku(erpProductAndSkuVo.getSkuName());
                            // 如果物料分类信息为空，则在此处实时查询填充
                            if (StringUtils.isBlank(item.getOriginProductCategoryPathName())) {
                                item.setOriginProductCategoryPathName(erpProductAndSkuVo.getCategoryPath());
                            }
                        }

                        // 计算原订单商品行有效重量(吨)
                        //////////////// 属性解析、重量计算////////////////
                        BigDecimal validWeight = caculateValidWeight(orderProductPo);
                        item.setOriginProductEffectiveWeight(validWeight);
                    }


                    ///////////////////////////////////// 新订单商品号信息 ///////////////////////////////////
                    productErpExtendPo = orderProductErpExtendPoMap.getOrDefault(item.getExchangeOrderProductId(), null);
                    if (Objects.nonNull(productErpExtendPo)) {
                        item.setNewProductCategoryPathName(productErpExtendPo.getProductCategoryPathName());
                    }

                    if (Objects.nonNull(item.getExchangeOrderProductId()) && orderProductPoMap.containsKey(item.getExchangeOrderProductId())) {
                        OrderProductPO orderProductPo = orderProductPoMap.get(item.getExchangeOrderProductId());
                        item.setNewSkuMaterialCode(orderProductPo.getSkuMaterialCode());
                        item.setNewWmsSkuId(orderProductPo.getChannelNewSkuId());
                        if (StringUtils.isNotBlank(orderProductPo.getChannelNewSkuId()) && finalErpProductMap.containsKey(orderProductPo.getChannelNewSkuId())) {
                            ErpProductAndSkuVo erpProductAndSkuVo = finalErpProductMap.get(orderProductPo.getChannelNewSkuId());
                            item.setNewWmsSku(erpProductAndSkuVo.getSkuName());
                            // 如果物料分类信息为空，则在此处实时查询填充
                            if (StringUtils.isBlank(item.getNewProductCategoryPathName())) {
                                item.setNewProductCategoryPathName(erpProductAndSkuVo.getCategoryPath());
                            }
                        }

                        // 计算新订单商品行有效重量(吨)
                        //////////////// 属性解析、重量计算////////////////
                        BigDecimal validWeight = caculateValidWeight(orderProductPo);
                        item.setNewProductEffectiveWeight(validWeight);
                    }


                });
            }
        }

        // 填充物料信息
        return exchangeApplyListPageNew;
    }

    /**
     * 计算有效重量
     * @param orderProductPo
     * @return
     */
    private BigDecimal caculateValidWeight(OrderProductPO orderProductPo) {
        BigDecimal tonUnit = BigDecimal.valueOf(0.001D);
        BigDecimal weight = orderProductPo.getWeight() == null ? BigDecimal.ZERO : orderProductPo.getWeight();
        int productNum = Objects.isNull(orderProductPo.getProductNum()) ? 0 : orderProductPo.getProductNum();
//        int returnNumber = Objects.isNull(orderProductPo.getReturnNumber()) ? 0 : orderProductPo.getReturnNumber();
//        int validNum = Math.max(productNum - returnNumber, 0);
        return weight.multiply(tonUnit).multiply(new BigDecimal(productNum));
    }

    private Integer getUserType(Integer userRole) {
        int result = 2;
        if (userRole == null) {
            return result;
        }
        if (userRole == 1) {
            return 1;
        } else if (userRole == 2) {
            return 0;
        }
        return result;
    }
}
