package com.cfpamf.ms.mallorder.v2.strategy;

import com.cfpamf.ms.mallorder.vo.MemberOrderDetailVO;

import java.util.List;
import java.util.Set;

/**
 * 类OrderTypeQueryStrategy.java的实现描述：
 *
 * <AUTHOR> 10:43
 */
public interface OrderTypeQueryStrategy {

    /**
     * 根据订单类型查询订单明细
     * 
     * @param memberOrderDetailVO
     * @return
     */
    void queryPromotionOrderInfo(MemberOrderDetailVO memberOrderDetailVO);

    /**
     * 根据订单类型查询订单售后明细
     *
     * @param orderSn 订单号
     * @return
     */
    <T> T queryPromotionReturnOrderInfo(String orderSn);

    /**
     * 根据订单类型查询订单集合
     * 
     * @param orderSns
     * @return
     */
    <T> List<T> queryForList(Set<String> orderSns);

}
