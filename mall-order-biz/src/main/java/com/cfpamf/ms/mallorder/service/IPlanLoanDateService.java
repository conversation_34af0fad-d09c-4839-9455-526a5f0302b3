package com.cfpamf.ms.mallorder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.ms.loan.facade.request.external.mall.CdmallOrderUpdatePlanLoan;
import com.cfpamf.ms.mallorder.dto.PlanLoanDateDTO;
import com.cfpamf.ms.mallorder.dto.PlanLoanDateImportDTO;
import com.cfpamf.ms.mallorder.po.PlanLoanDatePO;
import com.cfpamf.ms.mallorder.req.admin.AdminPlanLoanDateRequest;
import com.cfpamf.ms.mallorder.vo.ExportingFailedDataToExcelVO;
import com.cfpamf.ms.mallorder.vo.PlanLoanDateVO;
import com.cfpamf.ms.mallsystem.vo.Admin;
import com.slodon.bbc.core.response.PagerInfo;

import java.util.Date;
import java.util.List;
import java.util.Set;

public interface IPlanLoanDateService extends IService<PlanLoanDatePO> {
    List<PlanLoanDateVO> getPlanLoanDateList(PagerInfo pager, AdminPlanLoanDateRequest req);

    ExportingFailedDataToExcelVO adjustPlanLoanDate(PlanLoanDateDTO planLoanDateDTO, Admin admin) throws Exception;

    void updatePlanLoanDateForOrderSnList(List<String> orderSnList, Date planDate, String remark, Admin admin);

    String validPlanLoanDate(String orderSn, Date planDate);

    ExportingFailedDataToExcelVO excelParseAndSave(List<PlanLoanDateImportDTO> list, Admin admin) throws Exception;

    void insertPlanLoanDateList(List<String> orderSnList, Date planDate, String remark, String adminName);
}
