package com.cfpamf.ms.mallorder.integration.facade;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.bms.facade.constants.BmsConstantCore;
import com.cfpamf.ms.bms.facade.vo.*;
import com.cfpamf.ms.mallorder.integration.facade.dto.BmsUserInfoVO;
import com.cfpamf.ms.mallorder.vo.OrganizationWithDirectorVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @description bms用户管理
 **/
@FeignClient(name = "bms-service-biz", url = "${bms.api.url}")
public interface BmsUserFacade {

	@ApiOperation(value = "根据手机号判断是否是内部员工")
	@GetMapping("/user/isInter")
	Result<Boolean> isInter(@RequestParam("account") String account);

	@ApiOperation(value = "按字典类型编码获取字典明细",notes = "/dictionary/items/{typeCode}?systemId=1,2,3")
	@GetMapping(value = "/dictionary/items/{typeCode}")
	Result<List<DictionaryItemVO>> getDictionaryItemsByTypeCode(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization, @RequestParam("systemId") Integer systemId, @PathVariable("typeCode") String typeCode);

	@ApiOperation(value = "根据hrOrgCode所对应的级别获取对应下的主任", notes = "/org/listDirectorByHrOrgCodes")
	@PostMapping(value = "/org/listDirectorByHrOrgCodes")
	Result<List<OrganizationWithDirectorVO>> listDirectorByHrOrgCodes(@RequestParam(value = "hrOrgCodes",required = false) List<String> hrOrgCodes);

	@ApiOperation(value = "根据hrOrgCode所对应的级别获取对应下的主任", notes = "/org/listDirectorByHrOrgCodesV2")
	@PostMapping(value = "/org/listDirectorByHrOrgCodesV2")
	Result<List<OrganizationWithDirectorVO>> listDirectorByHrOrgCodesV2(@RequestParam(value = "hrOrgCodes",required = false) List<String> hrOrgCodes);

	/**
	 * 根据工号精确匹配获取所有用户信息列表(所有包括正职和兼职)
	 */
	@ApiOperation(value = "根据员工号查询用户基本详情（不包含敏感数据）", notes = "/user/list/detail?jobNumbers=")
	@PostMapping(value = "/user/list/baseDetail")
	Result<List<UserListBaseVO>> getEmployeesByJobNumbersBase(@RequestBody BaseUserDetailQuery query);

	/**
	 * 根据手机号码精确匹配获取所有用户信息列表(所有包括正职和兼职)
	 */
	@ApiOperation(value = "根据手机号码精确匹配获取所有用户信息列表(所有包括正职和兼职)", notes = "/user/list/detailByAccounts?userAccounts=")
	@PostMapping(value = "/user/list/detailByAccounts")
	Result<List<UserListVO>> getEmployeesByAccounts(@RequestParam("userAccounts") String [] userAccounts);

	/**
	 * 根据身份证号查询用户基本信息、任职信息、岗位和角色信息
	 */
	@ApiOperation(value = "根据身份证查询用户基本信息、任职信息、岗位和角色信息", notes = "/user/idcard/{idcard}/detail")
	@GetMapping(value = "/user/idcard/{idcard}/detail")
	Result<UserDetailVO> getUserDetailVOByIdcard(@PathVariable("idcard") String idcard);

	/**
	 * GET /user/list/condition/userIdList根据用户Id列表获取用户列表信息
	 */
	@ApiOperation(value = "根据用户Id列表获取用户列表信息", notes = "/user/list/condition/userIdList")
	@GetMapping(value = "/user/list/condition/userIdList")
	Result<List<BmsUserInfoVO>> getUserInfo(@RequestParam("userIdList") String  userIdList);

}