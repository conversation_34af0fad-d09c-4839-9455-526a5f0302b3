package com.cfpamf.ms.mallorder.integration.cashier.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 子单信息
 * <AUTHOR>
 */
@Data
public class OrderSub {

    @ApiModelProperty("业务子订单号")
    private String subOrderNo;

    @ApiModelProperty("订单金额（元）")
    private BigDecimal orderAmt;

    @ApiModelProperty("订单描述")
    private String description;

    @ApiModelProperty("支付金额（元）")
    private BigDecimal payAmt;

    @ApiModelProperty("是否分账")
    private Integer profitSharing;

    @ApiModelProperty("收货地址数据")
    private CshPayOrderSubRegisterAddressJsonBo receiveAddress;

    @ApiModelProperty("支付通道类型，微信服务商 WX_MCH, 微信收付通 WX_SVC，目前只支持微信")
    private String payChannelType;

    @ApiModelProperty("商家店铺")
    private OrderMerchant orderMerchant;

    @ApiModelProperty("产品信息")
    private List<OrderProduct> orderProductList;

    @ApiModelProperty("子单拓展信息")
    private List<SubOrderExt> subExtList;

    @ApiModelProperty("支付渠道 1-微信云直通，2-微信收付通")
    private Integer payChannel;
}
