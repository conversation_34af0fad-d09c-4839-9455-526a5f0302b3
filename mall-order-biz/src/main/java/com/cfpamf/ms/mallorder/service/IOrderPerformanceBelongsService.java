package com.cfpamf.ms.mallorder.service;

import com.cfpamf.ms.mallorder.dto.BasicUserInfoDTO;
import com.cfpamf.ms.mallorder.dto.CommissionAffiliationUpdateDTO;
import com.cfpamf.ms.mallorder.dto.OrderPerformanceBelongsDTO;
import com.cfpamf.ms.mallorder.enums.OrderPerformanceBindStateEnum;
import com.cfpamf.ms.mallorder.po.OrderPerformanceBelongsPO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.vo.EventTraceVO;
import com.cfpamf.ms.mallorder.vo.OrderPerformanceBelongsVO;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <p>
 * 订单业绩归属信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-18
 */
public interface IOrderPerformanceBelongsService extends IService<OrderPerformanceBelongsPO> {

    default OrderPerformanceBelongsVO entityConvertVO(OrderPerformanceBelongsPO entity) {
        return entity2VO(entity);
    }

    static OrderPerformanceBelongsVO entity2VO(OrderPerformanceBelongsPO entity) {
        OrderPerformanceBelongsVO vo = new OrderPerformanceBelongsVO();
        vo.setOrderSn(entity.getOrderSn());
        vo.setPerformanceType(entity.getPerformanceType());
        vo.setPerformanceDesc(entity.getPerformanceDesc());
        vo.setBelongerEmployeeNo(entity.getBelongerEmployeeNo());
        vo.setBelongerName(entity.getBelongerName());
        vo.setEmployeeBranchName(entity.getEmployeeBranchName());
        vo.setEmployeeBranchCode(entity.getEmployeeBranchCode());
        vo.setEmployeeBranchOrgId(entity.getEmployeeBranchOrgId());
        vo.setBindStateCode(entity.getBindStateCode());
        vo.setBindStateDesc(entity.getBindStateDesc());
        vo.setEffectTime(entity.getEffectTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        return vo;
    }

    Boolean bindPerformanceToOrder(CommissionAffiliationUpdateDTO notifyDTO);

    boolean verifyBoundBelongerByOrderSn(String orderSn);

    OrderPerformanceBelongsPO getEntityByOrderSn(String orderSn);

    boolean generatePerformanceBelongs(BasicUserInfoDTO basicUserDTO, OrderPO orderPO);

    OrderPerformanceBelongsPO modifyPerformanceBelongs(BasicUserInfoDTO basicUserDTO, OrderPO orderPO, OrderPerformanceBindStateEnum bindStateEnum);

    List<EventTraceVO> listAdjustPerformanceHistory(String orderSn);

    OrderPerformanceBelongsVO getVoByOrderSn(String orderSn);

    OrderPerformanceBelongsPO modifyPerformanceBelongsInfo(OrderPerformanceBelongsDTO orderPerformanceBelongsDTO);
}
