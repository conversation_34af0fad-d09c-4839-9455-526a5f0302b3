package com.cfpamf.ms.mallorder.service.payment;

import com.cfpamf.mallpayment.facade.request.OrgCustomerRepayRequestV2;
import com.cfpamf.mallpayment.facade.request.PaymentRefundRequest;
import com.cfpamf.mallpayment.facade.request.loan.OrgCustomerRepayRequest;
import com.cfpamf.mallpayment.facade.vo.PaymentLoanInfoVO;
import com.cfpamf.ms.mall.liquidate.api.BizActionFacade;
import com.cfpamf.ms.mall.liquidate.enums.ActionEnum;
import com.cfpamf.ms.mall.liquidate.request.ExecuteReq;
import com.cfpamf.ms.mall.liquidate.request.RevokeReq;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.constant.OrdersAfsConst;
import com.cfpamf.ms.mallorder.integration.pay.PayIntegration;
import com.cfpamf.ms.mallorder.model.OrderAfterServiceModel;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderReturnLoanExtendPO;
import com.cfpamf.ms.mallorder.po.OrderReturnPO;
import com.cfpamf.ms.mallorder.service.IOrderPlacingService;
import com.cfpamf.ms.mallorder.service.IOrderReturnLoanExtendService;
import com.cfpamf.ms.mallorder.validation.OrderReturnValidation;
import com.cfpamf.ms.mallsystem.vo.Admin;
import com.netflix.hystrix.exception.HystrixRuntimeException;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.exception.MallException;
import com.slodon.bbc.core.response.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.net.SocketTimeoutException;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Create 2022-05-09 13:39
 * @Description :贷款支付相关处理方法
 */
@Slf4j
@Service("Loan_ServiceImpl")
public class LoanPayServiceImpl extends AbstractPayHandleServiceImpl {

    @Value("${spring.application.name}")
    private String appName;
    @Autowired
    private BizActionFacade bizActionFacade;
    @Value("${mall-payment.refundNotify}")
    private String refundNotifyUrl;
    @Autowired
    private OrderAfterServiceModel afterServiceModel;
    @Autowired
    private PayIntegration payIntegration;
    @Autowired
    private IOrderPlacingService orderPlacingService;
    @Autowired
    private IOrderReturnLoanExtendService orderReturnLoanExtendService;
    @Autowired
    private OrderReturnValidation orderReturnValidation;

    /**
     * @param orderPO
     * @param orderReturnPO
     * @param extendParam
     * @param actionType
     * @param ad
     * @return java.lang.Boolean
     * @description : 用呗此退款接口使用的机构间代还
     * 机构间代还接口：在核算只做记录处理，具体退款在前置下账接口进行处理
     */
    @Override
    public Boolean paymentRefund(OrderPO orderPO, OrderReturnPO orderReturnPO, Map<String, Object> extendParam,
                                 String actionType, Admin ad) {

        BigDecimal actualReturnMoneyAmount = orderReturnPO.getActualReturnMoneyAmount()
                .add(orderReturnPO.getReturnExpressAmount());

        PaymentRefundRequest refundRequest = new PaymentRefundRequest();
        // 构造信贷渠道退款参数
        OrgCustomerRepayRequestV2 orgCustomerRepayRequest = buildOrgCustomerRepayRequestV2(orderPO, orderReturnPO, extendParam.get("paymentId").toString());
        //实际退款金额设值
        orgCustomerRepayRequest.setAmount(actualReturnMoneyAmount);
        //构建代还接口参数
        refundRequest.setRefundOn(orderReturnPO.getAfsSn());
        refundRequest.setRefundAmt(actualReturnMoneyAmount);
        refundRequest.setRefundReason("代还退款");
        refundRequest.setNotifyUrl(refundNotifyUrl);
        refundRequest.setOrgCustomerRepayRequest(orgCustomerRepayRequest);
        refundRequest.setOrderOn(orderPO.getOrderSn());
        refundRequest.setSystemCode(PayIntegration.SYSTEM_CODE);
        refundRequest.setRefundCreateTime(System.currentTimeMillis());
        // 补充代还退款备注信息
        afterServiceModel.recordAssistPaymentRefundMsg(orderPO, orderReturnPO);
        //调用支付服务进行退款
        try {
            payIntegration.loanRefundByPayment(orderPO, refundRequest, orderReturnPO.getAfsSn());
        } catch (Exception e) {
            log.error("调用支付服务进行机构间代还失败:{}", e.getMessage());
        }
        return true;
    }

    /**
     * 调用结算进行云直通下账
     * @param orderPO
     * @param orderReturnPO
     * @param actionType
     * @return
     */
    @Override
    public Boolean syncExecute(OrderPO orderPO, OrderReturnPO orderReturnPO, String actionType) {
        RevokeReq req = new RevokeReq();
        req.setBizNo(orderReturnPO.getAfsSn());
        req.setExecuteBizNo(orderReturnPO.getOrderSn());
        req.setActionType(ActionEnum.CHANNELSERVFEE);

        if (orderReturnValidation.isNeedCommissionIncentiveWhenBookkeeping(orderPO,orderReturnPO)) {
            req.setNextActionType(new ActionEnum[]{ActionEnum.COMMISSIONINCENTIVE, ActionEnum.SHAREPROFIT,ActionEnum.MARKETING,ActionEnum.SUBSIDYREFUND});
        } else {
            req.setNextActionType(new ActionEnum[]{ActionEnum.SHAREPROFIT,ActionEnum.MARKETING,ActionEnum.SUBSIDYREFUND});
        }

        req.setCreateBy(appName);
        JsonResult<Void> result = null;
        try {
            result = bizActionFacade.asyncRevoke(req);
            if (null == result) {
                log.warn("liquidate 调用贷款类赔偿退款接口失败,bizSn: {}", orderReturnPO.getAfsSn());
                throw new MallException("调用贷款类赔偿退款接口未返回任何信息,bizSn:" + req.getBizNo());
            }
            if (200 != result.getState()) {
                throw new MallException(result.getMsg());
            }
        } catch (Exception e) {
            if (e.getCause() instanceof SocketTimeoutException || e.getCause() instanceof HystrixRuntimeException) {
                log.error("调用贷款类赔偿退款接口超时，请排查:{}", orderReturnPO.getAfsSn());
            } else {
                throw e;
            }
        }
        return Boolean.TRUE;
    }

    /**
     * @param returnPO
     * @param actionType
     * @return void
     * @description :校验电子账户余额
     */
    @Override
    public void verifyExecute(OrderPO orderPO, OrderReturnPO returnPO, String actionType) {
        OrderPO orderByOrderPOSn = orderPlacingService.getByOrderSn(returnPO.getOrderSn());
        if (!orderByOrderPOSn.getNewOrder()) {
            return;
        }
        ExecuteReq executeReq = new ExecuteReq();
        executeReq.setBizNo(returnPO.getAfsSn());
        executeReq.setExtra(Collections.singletonMap("planDiscountAmount", returnPO.getPlanDiscountAmount()));
        executeReq.setActionType(ActionEnum.SUBSIDYREFUND);
        if (OrdersAfsConst.RETURN_TYPE_3 == returnPO.getReturnType()) {
            executeReq.setActionType(ActionEnum.EXCHANGEREFUND);
        }
        executeReq.setCreateBy(appName);
        JsonResult<Void> jsonResult = null;
        try {
            jsonResult = bizActionFacade.balanceSurplus(executeReq);
        } catch (Exception e) {
            log.error("liquidate 调用结算校验用户资金余额异常：{}：", e.getMessage());
            throw e;
        }
        if (jsonResult == null) {
            log.warn("调用liquidate 调用结算校验用户资金余额为空，退款单号：{}", returnPO.getAfsSn());
            throw new MallException("调用liquidate 调用结算校验用户资金余额未返回任何信息:{}" + returnPO.getAfsSn());
        }
        if (200 != jsonResult.getState()) {

            PaymentLoanInfoVO loanInfoVO = payIntegration.queryLoanInfo(orderPO.getOrderSn());
            if (loanInfoVO != null && !loanInfoVO.getRecordedFlag()) {
                String showMsg = "当前订单的贷款处于在途状态，不能操作退款，请在1小时后重试。";
                throw new BusinessException(showMsg);
            }

            throw new BusinessException(jsonResult.getMsg());
        }
    }


    /**
     * @param orderPO
     * @param OrderReturnPO
     * @param paymentId
     * @return com.cfpamf.mallpayment.facade.request.loan.OrgCustomerRepayRequest
     * @description :构建机构间代还退款请求参数
     */
    public OrgCustomerRepayRequest buildOrgCustomerRepayRequest(OrderPO orderPO, OrderReturnPO OrderReturnPO, String paymentId) {
        OrgCustomerRepayRequest orgCustomerRepayRequest = new OrgCustomerRepayRequest();
        orgCustomerRepayRequest.setRequestId(OrderReturnPO.getAfsSn());
        orgCustomerRepayRequest.setOrderNo(orderPO.getOrderSn());
        orgCustomerRepayRequest.setConsumeSeq(paymentId);
        orgCustomerRepayRequest.setOperateUsrCde(CommonConst.ADMIN_PHONE);
        orgCustomerRepayRequest.setOperateUsrName(CommonConst.ADMIN_NAME_EN);
        orgCustomerRepayRequest.setOperateUsrBch(CommonConst.ADMIN_BRANCH);
        orgCustomerRepayRequest.setRepayResource("organization_repay");
        orgCustomerRepayRequest.setDataSource("MALL");
        orgCustomerRepayRequest.setRemark("机构间代还退款:" + OrderReturnPO.getAfsSn());
        return orgCustomerRepayRequest;
    }

    /**
     * 代还退款请求参数构建
     */
    public OrgCustomerRepayRequestV2 buildOrgCustomerRepayRequestV2(OrderPO orderPO, OrderReturnPO orderReturnPO, String paymentId) {
        OrgCustomerRepayRequestV2 orgCustomerRepayRequest = new OrgCustomerRepayRequestV2();
        orgCustomerRepayRequest.setRequestId(orderReturnPO.getAfsSn());
        orgCustomerRepayRequest.setOrderNo(orderPO.getOrderSn());
        orgCustomerRepayRequest.setConsumeSeq(paymentId);
        orgCustomerRepayRequest.setOperateUsrCde(CommonConst.ADMIN_PHONE);
        orgCustomerRepayRequest.setOperateUsrName(CommonConst.ADMIN_NAME_EN);
        orgCustomerRepayRequest.setOperateUsrBch(CommonConst.ADMIN_BRANCH);
        orgCustomerRepayRequest.setRepayResource(CommonConst.LOAN_REPAY_RESOURCE);
        orgCustomerRepayRequest.setDataSource(CommonConst.LOAN_REPAY_DATA_SOURCE);
        orgCustomerRepayRequest.setRemark("机构间代还退款:" + orderReturnPO.getAfsSn());

        // 查询相关的贷款类信息
        OrderReturnLoanExtendPO loanExtendPO = orderReturnLoanExtendService.getOrderReturnLoanExtendPOBySn(orderPO.getOrderSn(),
                orderReturnPO.getAfsSn(), orderReturnPO.getOrderSn());
        if (Objects.isNull(loanExtendPO)) {
            throw new BusinessException(String.format("未查询到相关贷款类信息,orderSn:%s,afsSn:%s,bizSn:%s", orderPO.getOrderSn(),
                    orderReturnPO.getAfsSn(), orderReturnPO.getOrderSn()));
        }

        orgCustomerRepayRequest.setRepayPrincipal(loanExtendPO.getRepayPrincipal());
        orgCustomerRepayRequest.setRepayInterest(loanExtendPO.getRepayInterest());
        orgCustomerRepayRequest.setRepayOverdue(loanExtendPO.getRepayOverdue());
        orgCustomerRepayRequest.setRepayPrincipalOverdue(loanExtendPO.getRepayPrincipalOverdue());
        orgCustomerRepayRequest.setRepayInterestOverdue(loanExtendPO.getRepayInterestOverdue());
        orgCustomerRepayRequest.setRepayOverdueInterestOverdue(loanExtendPO.getRepayOverdueInterestOverdue());

        return orgCustomerRepayRequest;

    }

}



