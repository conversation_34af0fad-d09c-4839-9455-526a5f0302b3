package com.cfpamf.ms.mallorder.controller.fegin;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.mallorder.common.constant.WorkflowConst;
import com.cfpamf.ms.mallorder.service.IDingTalkFlowService;
import com.cfpamf.ms.mallorder.service.IOrderService;
import com.cfpamf.ms.mallorder.v2.common.lock.Lock;
import com.cfpamf.ms.mallorder.vo.OrderReturnApplyCallBackVO;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * Description
 *
 * <AUTHOR>
 * @since 2024/3/14
 */
@Api(tags = "feign-退款审核feign")
@RestController
@RequestMapping("/v1/feign/order/returnApply")
@Slf4j
public class OrderReturnApplyFeign {
    private static final String FLOW_END_CALLBACK = "flowEndCallBack";
    private static final String FLOW_CALLBACK = "flowCallBack";


    @Autowired
    private IDingTalkFlowService dingTalkFlowService;

    @ApiOperation(value = "流程结束回调")
    @PostMapping(FLOW_END_CALLBACK)
    public String flowEndCallBack(@RequestBody OrderReturnApplyCallBackVO orderReturnApplyCallBackVO) {
        log.info("start flow end callback ...orderReturnApplyCallBackVO: {}", JSONObject.toJSONString(orderReturnApplyCallBackVO));
        return dingTalkFlowService.flowEndCallBack(orderReturnApplyCallBackVO);
    }

    @ApiOperation(value = "流程节点回调")
    @PostMapping(FLOW_CALLBACK)
    public String flowCallBack(@RequestBody OrderReturnApplyCallBackVO orderReturnApplyCallBackVO) {
        log.info("start flow callback ...orderReturnApplyCallBackVO: {}", JSONObject.toJSONString(orderReturnApplyCallBackVO));
        return dingTalkFlowService.flowCallBack(orderReturnApplyCallBackVO);
    }

    @ApiOperation(value = "售后主任审批流程")
    @GetMapping("/directorAuditRefund")
    public JsonResult<Boolean> directorAuditRefund(@RequestParam(value = "afsSn") String afsSn, @RequestParam(value = "jobNumber") String jobNumber,
                                                   @RequestParam(value = "isPass") boolean isPass) {
        log.info("OrderReturnApplyFeign 售后主任审批流程 ...afsSn: {}, jobNumber: {}, isPass: {}", afsSn, jobNumber, isPass);
        return SldResponse.success(dingTalkFlowService.directorAuditRefund(afsSn, jobNumber, isPass));
    }

}
