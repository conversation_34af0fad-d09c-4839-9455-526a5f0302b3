package com.cfpamf.ms.mallorder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.ms.mallorder.dto.GroupOrderProductSubmitDTO;
import com.cfpamf.ms.mallorder.dto.OrderAddressDTO;
import com.cfpamf.ms.mallorder.po.OrderExtendPO;
import com.cfpamf.ms.mallorder.po.OrderGroupBuyingRecordPO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.req.query.GroupOrderQuery;
import com.cfpamf.ms.mallorder.req.query.GroupOrderRecordQuery;
import com.cfpamf.ms.mallorder.vo.GroupOrderRecordDetailVO;
import com.cfpamf.ms.mallorder.vo.GroupOrderRecordVO;
import com.cfpamf.ms.mallorder.vo.GroupOrderStatisticVO;
import com.cfpamf.ms.mallorder.vo.GroupOrderVO;
import com.slodon.bbc.core.response.PageVO;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * 拼单满赠记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-16
 */
public interface IOrderGroupBuyingRecordService extends IService<OrderGroupBuyingRecordPO> {

	/**
	 *  拼单满赠信息列表
	 * @param query
	 * @return
	 */
	PageVO<GroupOrderRecordVO> ListOrderGroupBuyingRecord(GroupOrderRecordQuery query);

	/**
	 * 拼单满赠详情
	 *
	 * @param groupBuyingCode
	 * @return
	 */
	GroupOrderRecordDetailVO orderGroupBuyingDetail(String groupBuyingCode);

	/**
	 * 导出
	 *
	 * @param request
	 * @param response
	 * @param query
	 */
	void export(HttpServletRequest request, HttpServletResponse response,GroupOrderRecordQuery query);

	/**
	 * 根据拼单满赠编码获取拼单满赠信息
	 *
	 * @param groupBuyingCode
	 * @return
	 */
	OrderGroupBuyingRecordPO getRecordByGroupBuyingCode(String groupBuyingCode);

	/**
	 * 可拼单订单列表
	 * 指定店铺、分类、分支
	 * 订单商品拼单标识：1-未拼单
	 * 订单商品发货状态：0-待发货
	 *
	 * @param query
	 * @return
	 */
	PageVO<GroupOrderVO> groupOrderList(GroupOrderQuery query);

	/**
	 * 可拼单订单数量统计
	 *
	 * @param query
	 * @return
	 */
	List<GroupOrderStatisticVO> groupOrderStatistic(GroupOrderQuery query);

	/**
	 * 订单收货信息
	 *
	 * @param orderSn
	 * @return
	 */
	OrderAddressDTO orderReceiveInfo(String orderSn);

	/**
	 * 校验拼单订单
	 *
	 * @param dto
	 * @return
	 */
	Boolean checkGroupOrder(GroupOrderProductSubmitDTO dto);

	/**
	 * 构建拼单满赠记录
	 *
	 * @param dto
	 * @return
	 */
	OrderGroupBuyingRecordPO buildOrderGroupBuyingRecord(GroupOrderProductSubmitDTO dto, OrderExtendPO orderExtendPO, OrderPO giftOrder);
}
