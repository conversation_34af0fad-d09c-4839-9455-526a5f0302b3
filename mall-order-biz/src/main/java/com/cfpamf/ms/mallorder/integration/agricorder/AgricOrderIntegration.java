package com.cfpamf.ms.mallorder.integration.agricorder;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cfpamf.framework.autoconfigure.common.result.Result;
import com.cfpamf.ms.mallorder.common.base.AgricResult;
import com.cfpamf.ms.mallorder.controller.fegin.facade.AgricOrderFacade;
import com.cfpamf.ms.mallorder.dto.agricorder.LoanListResultQueryDTO;
import com.cfpamf.ms.mallorder.vo.agricorder.AgricLoanResultVo;
import com.slodon.bbc.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * @Description:
 * @Author: suzhao
 * @Date: 2025/5/19 14:27
 **/
@Component
@Slf4j
public class AgricOrderIntegration {

    @Autowired
    private AgricOrderFacade agricOrderFacade;

    public Page<AgricLoanResultVo> agricLoanResultPage(LoanListResultQueryDTO loanRequest){
        AgricResult<Page<AgricLoanResultVo>> result;
        try{
            result = agricOrderFacade.loanResultPage(loanRequest);
        }catch (Exception e){
            log.warn("查询乡信放款结果列表异常！",e);
            throw new BusinessException("查询乡信放款结果列表异常！");
        }
        if(Objects.isNull(result)){
            throw new BusinessException("查询乡信放款结果列表返回为空！");
        }
        if(!result.isSuccess()){
            throw new BusinessException("查询乡信放款结果列表失败！原因为:"+result.getMsg());
        }

        return result.getData();
    }

    /**
     * 乡信订单-重新代付
     * @param paySn
     * @param jobNumber
     */
    public void loanRepay(String paySn, String jobNumber){
        AgricResult<Void> result;
        try{
            result = agricOrderFacade.loanRepay(paySn, jobNumber);
        }catch (Exception e){
            log.warn("乡信重新代付异常！",e);
            throw new BusinessException("乡信重新代付异常！");
        }
        if(Objects.isNull(result)){
            throw new BusinessException("乡信重新代付返回为空！");
        }
        if(!result.isSuccess()){
            throw new BusinessException("乡信重新代付失败！原因为:"+result.getMsg());
        }
    }

}
