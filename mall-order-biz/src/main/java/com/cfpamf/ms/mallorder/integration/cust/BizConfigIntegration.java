package com.cfpamf.ms.mallorder.integration.cust;

import com.cfpamf.cmis.common.base.CommonResult;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.bizconfig.facade.vo.UserVo;
import com.cfpamf.ms.mallorder.controller.fegin.facade.UserFacade;
import com.slodon.bbc.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Create 2021-09-22 15:16
 * @Description :获取用户信息
 */
@Slf4j
@Component
public class BizConfigIntegration {
    @Autowired
    private UserFacade userFacade;

    public UserVo getUserMasterByMobile(String mobile) {

        Result<UserVo> result = null;
        try {
            result = userFacade.getUserMasterByMobile(mobile);
        } catch (Exception e) {
            log.warn("调用bizConfig获取工号异常 /bizconfig/user/getUserMasterByMobile ：", e);
        }

        if (result == null || result.getData() == null) {
            log.warn("调用bizConfig获取工号为空 /bizconfig/user/getUserMasterByMobile ");
            return null;
        }

        if (!result.isSuccess()) {
            log.warn("调用bizConfig获取工号为空 /bizconfig/user/getUserMasterByMobile ");
            return null;
        }
        return result.getData();
    }

    public UserVo getDefaultUserByUserCode(String userCode) {
        CommonResult<UserVo> result = null;
        try {
            result = userFacade.getUserListByUserCode(userCode);
        } catch (Exception e) {
            throw new BusinessException("根据工号查用户信息异常：" + e.getMessage());
        }

        if (result == null || result.getData() == null) {
            throw new BusinessException("根据工号查用户信息为空");
        }

        if (!result.isSuccess()) {
            throw new BusinessException("根据工号查用户信息失败:" + result.getMessage());
        }
        return result.getData();
    }

}
