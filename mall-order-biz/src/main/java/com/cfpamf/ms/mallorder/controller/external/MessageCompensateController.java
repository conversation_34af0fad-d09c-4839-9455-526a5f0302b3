package com.cfpamf.ms.mallorder.controller.external;

import com.cfpamf.ms.mallorder.service.external.IMessageCompensateService;
import com.slodon.bbc.core.controller.BaseController;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> 2021/10/28.
 *
 * MQ 消息补偿
 */

@Api(tags = "external-消息补偿")
@RestController
@RequestMapping("external/rabbitmq/messageSend")
@Slf4j
public class MessageCompensateController extends BaseController {

    @Resource
    private IMessageCompensateService messageCompensateService;


    @ApiOperation("订单正向补偿消息（谨慎使用：exchange发送消息，都会接收；实际订单事件类型和发送的类型可能不一致）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderSn", value = "订单号", required = true, paramType = "query"),
            @ApiImplicitParam(name = "orderEvent",  value = "事件类型: 创建-CREATE，支付成功-PAY，" +
                    "发货-DELIVERY，完成-FINISH，取消-CANCEL，关闭-CLOSE", required = true, paramType = "query")
    })
    @GetMapping("rabbitmq/orderMessage")
    public JsonResult orderMessageSend(@NotNull String orderSn, @NotNull String orderEvent) {
        messageCompensateService.rabbitmqOrderEventCompensate(orderSn, orderEvent);
        return SldResponse.success("发送成功");
    }

    /*@ApiOperation("订单逆向补偿消息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "afsSn", value = "退款单号", required = true, paramType = "query"),
            @ApiImplicitParam(name = "orderEvent", value = "事件类型: 退款成功-REFUND", required = true, paramType = "query")
    })
    @GetMapping("rabbitmq/orderReturnMessage")
    public JsonResult returnMessageSend(@NotNull String afsSn, @NotNull String orderEvent) {
        messageCompensateService.rabbitmqOrderReturnEventCompensate(afsSn, orderEvent);
        return SldResponse.success("发送成功");
    }*/

}
