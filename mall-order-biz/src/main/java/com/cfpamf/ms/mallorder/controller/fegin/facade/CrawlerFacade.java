//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//

package com.cfpamf.ms.mallorder.controller.fegin.facade;

import com.cfpamf.common.ms.result.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(
    value = "crawler-biz",
    url = "${crawler-service.url}"
)
public interface CrawlerFacade {

    @GetMapping(
        path = {"/agricMachine/existFactoryNo"},
        produces = {"application/json"}
    )
    Result<Boolean> existFactoryNo(@RequestParam("factoryNo") String factoryNo);
}
