package com.cfpamf.ms.mallorder.common.mq;

import java.util.Objects;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import com.cfpamf.ms.mallorder.common.enums.OrderOperationEventEnum;
import com.cfpamf.ms.mallorder.dto.OrderOperationEventNotifyDTO;
import com.cfpamf.ms.mallorder.service.ValetOrderMessagePushService;
import com.rabbitmq.client.Channel;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class ValetOrderEventConsumer {

	@Resource
	private ValetOrderMessagePushService valetOrderMessagePushService;

	@RabbitListener(queues = RabbitMqConfig.QUEUE_VALET_ORDER_CUSTOMER_CONFIRM_MESSAGE_PUSH)
	public void customerUnConfirmOnMessage(Message message, Channel channel) throws Exception {
		try {
			String body = new String(message.getBody());
			log.info("【客户确认MESSAGE_PUSH】Payload: {} messageId：{}", body, message.getMessageProperties().getMessageId());
			if (Objects.isNull(body)) {
				log.info("【客户确认MESSAGE_PUSH】 body为空拒绝处理， messageId：{}", message.getMessageProperties().getMessageId());
				channel.basicAck(message.getMessageProperties().getDeliveryTag(), true);// ACK
				return;
			}
			OrderOperationEventNotifyDTO messageBody = JSON.parseObject(body, OrderOperationEventNotifyDTO.class);
			if (Objects.isNull(messageBody.getOrderSn())) {
				log.info("【客户确认MESSAGE_PUSH】body中orderSn为空拒绝处理， messageId：{}", message.getMessageProperties().getMessageId());
				channel.basicAck(message.getMessageProperties().getDeliveryTag(), true);// ACK
				return;
			}
			if(OrderOperationEventEnum.isCustomerUnConfirmed(messageBody.getOperationEventCode())) {
				valetOrderMessagePushService.customerUnConfirmMessagePush(messageBody.getOrderSn());
				log.info("【客户确认MESSAGE_PUSH】[客户待确认]Payload: {} messageId：{} 处理成功 手工ACK MQ中事件消息数据", body,
						message.getMessageProperties().getMessageId());
				channel.basicAck(message.getMessageProperties().getDeliveryTag(), true);
				return;
			}
			if(OrderOperationEventEnum.isCustomerConfirm(messageBody.getOperationEventCode())) {
				valetOrderMessagePushService.customerConfirmMessagePush(messageBody.getOrderSn());
				log.info("【客户确认MESSAGE_PUSH】[客户确认]Payload: {} messageId：{} 处理成功 手工ACK MQ中事件消息数据", body,
						message.getMessageProperties().getMessageId());
				channel.basicAck(message.getMessageProperties().getDeliveryTag(), true);
				return;
			}
			if(OrderOperationEventEnum.isCustomerConfirmRefuse(messageBody.getOperationEventCode())) {
				valetOrderMessagePushService.customerConfirmRefuseMessagePush(messageBody.getOrderSn());
				log.info("【客户确认MESSAGE_PUSH】[客户确认拒绝]Payload: {} messageId：{} 处理成功 手工ACK MQ中事件消息数据", body,
						message.getMessageProperties().getMessageId());
				channel.basicAck(message.getMessageProperties().getDeliveryTag(), true);
				return;
			}
			log.info("【客户确认MESSAGE_PUSH】Payload: {} messageId：{} 消息未匹配要处理的业务， 手工ACK MQ中事件消息数据", body,
					message.getMessageProperties().getMessageId());
			channel.basicAck(message.getMessageProperties().getDeliveryTag(), true);
		} catch (Exception e) {
			log.warn("【客户确认MESSAGE_PUSH】Payload: {} messageId：{} 消费异常：", new String(message.getBody()),
					message.getMessageProperties().getMessageId(), e);
//            channel.basicReject(message.getMessageProperties().getDeliveryTag(), false);
			channel.basicAck(message.getMessageProperties().getDeliveryTag(), true);// ACK
		}
	}

}
