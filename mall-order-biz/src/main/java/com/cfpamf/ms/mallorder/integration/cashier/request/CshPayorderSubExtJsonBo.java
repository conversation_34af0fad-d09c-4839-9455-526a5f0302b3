package com.cfpamf.ms.mallorder.integration.cashier.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Create 2022-08-26 14:51
 * @Description :子单扩展json实体
 */
@Data
public class CshPayorderSubExtJsonBo {

    @ApiModelProperty(value = "放款卡ID", required = true)
    private String drawCardId;

    @ApiModelProperty(value = "放款卡号", required = true)
    private String drawCardNo;

    @ApiModelProperty(value = "业务收款账户")
    private String busiReceiveAccount;

    @ApiModelProperty(value = "放款银行编号", required = true)
    private String drawBankCode;

    @ApiModelProperty(value = "放款银行名称", required = true)
    private String drawBankName;

    @ApiModelProperty(value = "放款户名", required = true)
    private String drawBankAccount;

    @ApiModelProperty(value = "开户支行", required = true)
    private String openingBank;

    @ApiModelProperty(value = "组织机构编号", required = true)
    private String  orgCode;

    @ApiModelProperty(value = "支付宝商户号")
    private String sellerId;

    // 起息方式 1-交易成功后起息 2-下单确认后起息  3-计划放款日起息
    private Integer interestType;

    // 员工计息差类型：1-固定日期 2-下单后N天
    private Integer interestStartType;

    @ApiModelProperty(value = "员工计息差:下单确认后N天")
    private Integer interestStartDays;

    @ApiModelProperty(value = "员工计息差开始日")
    @JsonFormat(timezone = "GMT+8",pattern="yyyy-MM-dd HH:mm:ss")
    private Date interestStartDate;

    @ApiModelProperty(value = "计划放款日")
    @JsonFormat(timezone = "GMT+8",pattern="yyyy-MM-dd HH:mm:ss")
    private Date planLoanDate;

    @ApiModelProperty(value = "优惠卷批次号")
    private String couponBatch;

    @ApiModelProperty(value = "金融规则编码")
    private String financeRuleCode;

    @ApiModelProperty(value = "金融规则名")
    private String financeRuleName;

    @ApiModelProperty(value = "平台服务费")
    private BigDecimal platformAmount;

    @ApiModelProperty(value = "其他费用")
    private BigDecimal otherAmount;

    @ApiModelProperty(value = "微信二级商户号")
    private String wxSellerId;

    @ApiModelProperty("补差金额-profit_sharing为true时，该金额才生效")
    private BigDecimal subsidyAmount;

    @ApiModelProperty("店铺所属区域编码")
    private String storeAreaCode;

    @ApiModelProperty("店铺所属区域名")
    private String storeAreaName;

    @ApiModelProperty("技术服务费标识")
    private Boolean platformServiceRateFlag;

    @ApiModelProperty("是否贷款首单标识")
    private Boolean isFirstLoanOrder;

    @ApiModelProperty("interest_way=4时，N天对应的值")
    private Integer planInterestStartDays;

}
