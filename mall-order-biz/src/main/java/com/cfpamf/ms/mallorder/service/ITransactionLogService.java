package com.cfpamf.ms.mallorder.service;

import com.cfpamf.ms.mallorder.po.TransactionLogPO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 事务记录日志表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-06
 */
public interface ITransactionLogService extends IService<TransactionLogPO> {
    /**
     * 生成全局事务id
     *
     * @return 全局事务id
     */
    String generateTransactionId();

    /**
     * 生成新事务
     *
     * @param logType 事务类型
     * @param role 事务角色
     * @return 事务记录po
     */
    TransactionLogPO generateNewTransactionLogPO(Integer logType,Integer role);

    /**
     * 根据事务id更新事务状态
     *
     * @param transactionId 事务id
     * @param oldStatus 旧状态，可为空
     * @param newStatus 新状态，不可为空
     */
    void updateTransactionStatusById(String transactionId,Integer oldStatus,Integer newStatus);

    /**
     * 根据事务id获取事务记录列表
     *
     * @param transactionId 事务id
     * @return 事务记录列表
     */
    List<TransactionLogPO> listTransactionLogByTransactionId(String transactionId);

    /**
     * 更新请求参数
     *
     * @param id 主键
     * @param jsonString 参数
     */
    void updateRequestParam(Long id, String jsonString);
}
