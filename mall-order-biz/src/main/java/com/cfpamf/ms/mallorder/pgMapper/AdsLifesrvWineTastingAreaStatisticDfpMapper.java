package com.cfpamf.ms.mallorder.pgMapper;

import com.cfpamf.framework.autoconfigure.mybatis.MyBaseMapper;
import com.cfpamf.ms.mallorder.po.pgrpt.AdsLifesrvDepartmentProfitStaticDfpPO;
import com.cfpamf.ms.mallorder.po.pgrpt.AdsLifesrvWineTastingAreaStatisticDfpPO;
import com.cfpamf.ms.mallorder.req.LifeSrvIncomeReportRequest;
import com.cfpamf.ms.mallorder.req.LifeSrvWineTastingReportRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-30
 */
public interface AdsLifesrvWineTastingAreaStatisticDfpMapper extends MyBaseMapper<AdsLifesrvWineTastingAreaStatisticDfpPO> {
    /**
     * 查询品酒会活动统计指标
     */
    AdsLifesrvWineTastingAreaStatisticDfpPO getBasicMetrics(@Param("request") LifeSrvWineTastingReportRequest request);

    /**
     * 获取分支排行榜前十
     * @param request 请求参数
     * @return list
     */
    List<AdsLifesrvWineTastingAreaStatisticDfpPO> getTOP10RankList(@Param("request") LifeSrvWineTastingReportRequest request, @Param("type") String type);
}
