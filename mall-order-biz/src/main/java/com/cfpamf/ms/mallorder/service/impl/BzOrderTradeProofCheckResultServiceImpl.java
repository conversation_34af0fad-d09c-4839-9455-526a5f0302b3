package com.cfpamf.ms.mallorder.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cfpamf.ms.mallorder.dto.BzOrderTradeProofCheckResultDTO;
import com.cfpamf.ms.mallorder.dto.TransactionVerifyDTO;
import com.cfpamf.ms.mallorder.po.BzOrderTradeProofCheckResultPO;
import com.cfpamf.ms.mallorder.mapper.BzOrderTradeProofCheckResultMapper;
import com.cfpamf.ms.mallorder.service.IBzOrderTradeProofCheckResultService;
import com.cfpamf.framework.autoconfigure.mybatis.BaseRepoServiceImpl;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 订单交易凭证校验结果表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-21
 */
@Service
public class BzOrderTradeProofCheckResultServiceImpl extends BaseRepoServiceImpl<BzOrderTradeProofCheckResultMapper, BzOrderTradeProofCheckResultPO> implements IBzOrderTradeProofCheckResultService {

    @Override
    public List<TransactionVerifyDTO> orderTradeProofQueryResult(String orderNo) {
        List<TransactionVerifyDTO> transactionVerifyDTOList = new ArrayList<>();
        LambdaQueryWrapper<BzOrderTradeProofCheckResultPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BzOrderTradeProofCheckResultPO::getOrderSn, orderNo);
        queryWrapper.orderByDesc(BzOrderTradeProofCheckResultPO::getId);
        List<BzOrderTradeProofCheckResultPO> list = list(queryWrapper);
        Map<String, List<BzOrderTradeProofCheckResultPO>> sceneNoMap = list.stream().collect(Collectors.groupingBy(BzOrderTradeProofCheckResultPO::getSceneNo));
        for (Map.Entry<String, List<BzOrderTradeProofCheckResultPO>> entry : sceneNoMap.entrySet()) {
            List<BzOrderTradeProofCheckResultPO> v = entry.getValue();
            TransactionVerifyDTO transactionVerifyDTO = new TransactionVerifyDTO();
            Set<String> repeatOrderNoSet = new HashSet<>();
            Set<String> commodityStatusSet = new HashSet<>();
            List<String> allResult = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(v)){
                BzOrderTradeProofCheckResultPO item = v.get(0);
                repeatOrderNoSet.addAll(JSON.parseArray(item.getRepeatOrderNo(), String.class));
                commodityStatusSet.addAll(JSON.parseArray(item.getResultCode(), String.class));
                allResult.addAll(JSON.parseArray(item.getAllResult(), String.class));
            }

            transactionVerifyDTO.setRepeatOrderId(String.join(",",repeatOrderNoSet));
            transactionVerifyDTO.setCommodityStatus(String.join(",",commodityStatusSet));
            transactionVerifyDTO.setTransactionVerifyTips(allResult);
            transactionVerifyDTOList.add(transactionVerifyDTO);
        }
        return transactionVerifyDTOList;
    }

    @Override
    public List<BzOrderTradeProofCheckResultDTO> orderTradeProofBatchQueryResult(List<String> orderNoList) {
        List<BzOrderTradeProofCheckResultDTO> checkResultDTOList = new ArrayList<>();
        LambdaQueryWrapper<BzOrderTradeProofCheckResultPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(BzOrderTradeProofCheckResultPO::getOrderSn, orderNoList);
        queryWrapper.orderByDesc(BzOrderTradeProofCheckResultPO::getId);
        List<BzOrderTradeProofCheckResultPO> list = list(queryWrapper);
        Map<String, List<BzOrderTradeProofCheckResultPO>> orderNoMap = list.stream().collect(Collectors.groupingBy(BzOrderTradeProofCheckResultPO::getOrderSn));
        for (Map.Entry<String, List<BzOrderTradeProofCheckResultPO>> entry : orderNoMap.entrySet()) {
            List<BzOrderTradeProofCheckResultPO> v = entry.getValue();
            BzOrderTradeProofCheckResultDTO tradeProofCheckResultDTO = new BzOrderTradeProofCheckResultDTO();
            if(CollectionUtils.isNotEmpty(v)){
                List<String> allResult = new ArrayList<>(JSON.parseArray(v.get(0).getAllResult(), String.class));
                tradeProofCheckResultDTO.setOrderNo(entry.getKey());
                tradeProofCheckResultDTO.setTransactionVerifyTips(allResult);
                checkResultDTOList.add(tradeProofCheckResultDTO);
            }
        }
        return checkResultDTOList;
    }
}
