package com.cfpamf.ms.mallorder.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cfpamf.framework.autoconfigure.redis.lock.SlodonLock;
import com.cfpamf.ms.customer.facade.vo.UserInfoVo;
import com.cfpamf.ms.mallgoods.facade.enums.EventStockTypeEnum;
import com.cfpamf.ms.mallgoods.facade.vo.ProductPriceVO;
import com.cfpamf.ms.mallmember.api.MemberFeignClient;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.common.constant.OrdersAfsConst;
import com.cfpamf.ms.mallorder.common.constant.SentenceConst;
import com.cfpamf.ms.mallorder.common.enums.*;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.cfpamf.ms.mallorder.common.template.ordersubmitdto.OrderSubmitDTOContext;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.common.util.DateUtil;
import com.cfpamf.ms.mallorder.common.util.OrderLocalUtils;
import com.cfpamf.ms.mallorder.constant.ExchangeOrderConst;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.*;
import com.cfpamf.ms.mallorder.enums.OrderPerformanceModeEnum;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.integration.cust.CustomerIntegration;
import com.cfpamf.ms.mallorder.mapper.OrderAfterMapper;
import com.cfpamf.ms.mallorder.mapper.OrderExchangeDetailMapper;
import com.cfpamf.ms.mallorder.mapper.OrderExchangeMapper;
import com.cfpamf.ms.mallorder.model.*;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.req.BappOrderReturnRequest;
import com.cfpamf.ms.mallorder.req.ExchangeApplyListPcReq;
import com.cfpamf.ms.mallorder.req.ExchangeApplyListReq;
import com.cfpamf.ms.mallorder.req.admin.AdminForceRefundRequest;
import com.cfpamf.ms.mallorder.req.exchange.OrderExchangeRequest;
import com.cfpamf.ms.mallorder.req.front.FrontRevokeRefundBaseRequest;
import com.cfpamf.ms.mallorder.request.OrderExample;
import com.cfpamf.ms.mallorder.request.OrderExchangeDetailExample;
import com.cfpamf.ms.mallorder.request.OrderExchangeExample;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.v2.manager.GoodsStockService;
import com.cfpamf.ms.mallorder.vo.*;
import com.cfpamf.ms.mallsystem.vo.Admin;
import com.cfpamf.smartid.client.utils.sharding.SeqEnum;
import com.cfpamf.smartid.client.utils.sharding.ShardingId;
import com.google.common.collect.Lists;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.exception.MallException;
import com.slodon.bbc.core.response.PageVO;
import com.slodon.bbc.core.response.PagerInfo;
import com.slodon.bbc.core.util.AssertUtil;
import com.slodon.bbc.core.util.TimeUtil;
import com.slodon.bbc.starter.mq.entity.MessageSendProperty;
import com.slodon.bbc.starter.mq.entity.MessageSendVO;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.slodon.bbc.core.constant.StarterConfigConst.MQ_EXCHANGE_NAME;
import static com.slodon.bbc.core.constant.StarterConfigConst.MQ_QUEUE_NAME_MEMBER_MSG;

@Slf4j
@Service
public class OrderExchangeServiceImpl extends ServiceImpl<OrderExchangeMapper, OrderExchangePO> implements IOrderExchangeService {


    @Resource
    private OrderExchangeDetailMapper orderExchangeDetailMapper;

    @Resource
    private OrderExchangeMapper orderExchangeMapper;

    @Resource
    private OrderProductModel orderProductModel;

    @Resource
    private OrderLocalUtils orderLocalUtils;

    @Resource
    private OrderExtendModel orderExtendModel;

    @Resource
    private OrderModel orderModel;

    @Resource
    private IOrderService orderService;

    @Resource
    private IOrderProductService orderProductService;

    @Resource
    private IOrderExtendService orderExtendService;

    @Resource
    private IOrderExchangeService orderExchangeService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private MemberFeignClient memberFeignClient;

    @Resource
    private SlodonLock slodonLock;

    @Resource
    private ShardingId shardingId;

    @Resource
    private OrderLogModel orderLogModel;

    @Resource
    private RabbitTemplate rabbitTemplate;

    @Resource
    private OrderPayModel orderPayModel;

    @Resource
    private OrderAfterServiceModel orderAfterServiceModel;

    @Resource
    private IOrderExchangeDetailService orderExchangeDetailService;

    @Resource
    private GoodsStockService goodsStockService;

    @Resource
    private IOrderPayService orderPayService;

    @Resource
    private OrderReturnModel orderReturnModel;

    @Resource
    private IOrderReturnService orderReturnService;

    @Value("${new.mall.domain.url:https://h5-test.cdfinance.com.cn}")
    private String newMallDomainUrl;

    @Resource
    private IOrderAdminReturnService orderAdminReturnService;

    @Resource
    private OrderAfterMapper orderAfterMapper;

    @Resource
    private EmployeeService employeeService;

    @Resource
    private CustomerIntegration customerIntegration;


    private boolean exchangePermissionValidate(OrderPO orderPO, OrderProductPO orderProductPO) {
        /*if (OrderProductDeliveryEnum.DELIVERED == orderProductPO.getDeliveryState()) {
            throw new BusinessException("该订单商品已发货，不允许换货");
        }*/
        //
        BizAssertUtil.isTrue(Objects.equals(OrderProductDeliveryEnum.OUTBOUND.getValue(), orderProductPO.getDeliveryState().getValue())
                ,"发货中的商品不允许换货");
        //有效数量判断
        log.info("exchangePermissionValidate orderSn = {}, orderProductId = {}", orderPO.getOrderSn(), orderProductPO.getOrderProductId());
        log.info("exchangePermissionValidate productNum = {}, returnNumber = {}", orderProductPO.getProductNum(), orderProductPO.getReturnNumber());
        int validNum = orderProductPO.getProductNum() - orderProductPO.getReturnNumber();
        if (validNum == 0) {
            throw new BusinessException("该订单商品有效数量为0，不允许换货");
        } else if (validNum > 0) {
            //已申请换货，且未审批的个数
            ExchangeOrderDetailDTO exchangeOrderDetailDTO = new ExchangeOrderDetailDTO();
            exchangeOrderDetailDTO.setOrderProductId(orderProductPO.getOrderProductId());
            exchangeOrderDetailDTO.setOrderSn(orderProductPO.getOrderSn());
            exchangeOrderDetailDTO.setExchangeOrderState(ExchangeOrderConst.EXCHANGE_ORDER_STATE_WAIT_AUDIT);
            List<ExchangeOrderDetailDTO> exchangeOrderDetailDTOList = orderExchangeMapper.getExchangeOrderList(exchangeOrderDetailDTO);
            int num = exchangeOrderDetailDTOList.stream().mapToInt(x -> x.getProductNum()).sum();
            //可换数量
            int exchangeNum = validNum - num;
            if ((0 >= exchangeNum)) {
                throw new BusinessException("该订单商品有效数量为" + validNum + "件,换货中" + num + "件，本次最多可换" + 0 + "件");
            }
        }


        if (!Objects.equals(orderPO.getOrderState(), OrderStatusEnum.WAIT_DELIVER.getValue())
                && !Objects.equals(orderPO.getOrderState(), OrderStatusEnum.PART_DELIVERED.getValue())
                && !orderPO.getOrderState().equals(OrderStatusEnum.TRADE_SUCCESS.getValue())) {
            throw new BusinessException("该订单状态下不允许换货");
        }

        if (ExchangeOrderConst.ORDER_EXCHANGE_FLAG_2 == orderPO.getExchangeFlag()) {
            throw new BusinessException("换货后的订单不允许换货");
        }

        if (orderPO.getPerformanceModes().contains(OrderPerformanceModeEnum.PERFORMANCE_MODE_SUPPLIER.getValue().toString())) {
            //云中鹤订单不允许换货
            throw new BusinessException("供应商履约模式的订单不允许换货");
        }

        if (OrderTypeEnum.isPresell(orderPO.getOrderType())) {
            //预付订单不允许换货
            throw new BusinessException("预付订单不允许换货");
        }
        if (OrderTypeEnum.isOfflineAll(orderPO.getOrderType())) {
            //线下补录订单不允许换货
            throw new BusinessException("线下补录订单不允许换货");
        }
        boolean duringRefund = orderReturnService.hasDuringRefund(orderPO.getOrderSn());
        if (duringRefund){
            // 存在在途售后单，不能换货
            throw new BusinessException("订单正在售后处理，请售后完成后再发起换货申请");
        }
        return Boolean.TRUE;
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public OrderExchangeDetailVO applyExchange(OrderExchangeRequest orderExchangeRequest) throws Exception {
        OrderPO orderPO = orderModel.getOrderByOrderSn(orderExchangeRequest.getOrderSn());
        if (orderPO.getOrderType().equals(OrderTypeEnum.FULL_GIFT.getValue())) {
            throw new BusinessException("满赠订单不支持换货");
        }
//        if (orderPO.getOrderType().equals(OrderTypeEnum.COMBINATION.getValue()) &&
//                orderPO.getOrderState().equals(OrderStatusEnum.WAIT_RECEIPT.getValue())) {
//            throw new BusinessException("组合订单待已发货状态不支持换货");
//        }
        OrderProductPO orderProductPO = orderProductModel.getOrderProductByOrderProductId(orderExchangeRequest.getOrderProductId());

        exchangePermissionValidate(orderPO, orderProductPO);

        //商品试算
        OrderExchangeDetailVO orderExchangeDetailVO = orderExchangeTrial(orderExchangeRequest);
        if (orderExchangeDetailVO.getNewOrderInfo().getProductNum() == 0) {
            throw new BusinessException("换货后的商品价格为" + orderExchangeDetailVO.getNewOrderInfo().getProductShowPrice() + "可换商品数量为0,请重新选择换货商品");
        }

        //生成换货单号
        long exchangeSnLong = shardingId.next(OrderSeqEnum.ENO.getName(), OrderSeqEnum.ENO.prefix(), orderPO.getMemberId().toString(), new Date());
        String exchangeSn = String.valueOf(exchangeSnLong);
        orderExchangeDetailVO.setExchangeSn(exchangeSn);

        //保存申请记录
        orderExchangeService.saveExchangeApplyOrder(exchangeSn, orderPO.getStoreId(), orderPO.getMemberId(),
                orderExchangeRequest.getApplicantInfo(), orderExchangeRequest.getBuyerConfirmFlag(), orderExchangeRequest.getExchangeReason());


        //记录换货操作轨迹
        orderLogModel.insertOrderLog(OrderConst.LOG_ROLE_VENDOR, orderExchangeRequest.getApplicantInfo().getUserId(),
                orderExchangeRequest.getApplicantInfo().getUserName(), exchangeSn, orderPO.getOrderState(), orderPO.getOrderState(),
                LoanStatusEnum.APPLY_SUCCESS.getValue(), "创建换货申请", OrderCreateChannel.getEnumByValue(orderExchangeRequest.getChannel()));


        //原记录操作轨迹
        orderLogModel.insertOrderLog(OrderConst.LOG_ROLE_VENDOR, orderExchangeRequest.getApplicantInfo().getUserId(),
                orderExchangeRequest.getApplicantInfo().getUserName(), orderPO.getOrderSn(), orderPO.getOrderState(), orderPO.getOrderState(),
                LoanStatusEnum.APPLY_SUCCESS.getValue(), "换货申请", OrderCreateChannel.getEnumByValue(orderExchangeRequest.getChannel()));

        //创建待支付订单
        OrderPO exchangeOrder = orderExchangeService.createExchangeOrder(orderPO, orderExchangeDetailVO, orderExchangeRequest);
        orderExchangeDetailVO.getNewOrderInfo().setOrderSn(exchangeOrder.getOrderSn());

        List<OrderProductPO> exchangeProductList = orderProductModel.getOrderProductListByOrderSn(exchangeOrder.getOrderSn());
        orderExchangeDetailVO.getNewOrderInfo().setOrderProductId(exchangeProductList.get(0).getOrderProductId());

        //生成换货单详细
        orderExchangeService.saveExchangeOrderDetail(orderExchangeDetailVO, orderExchangeRequest.getApplicantInfo().getUserName());

        //更改换货后订单的相关金额、换货后的订单、标记原来的订单为换货前的订单
        //更改原订单为：被换货的订单
        orderExchangeService.updateOrderToExchange(orderPO.getOrderSn(), ExchangeOrderConst.ORDER_EXCHANGE_FLAG_1);

        //更新换货后订单明细
        orderExchangeService.updateOrderAmount(exchangeOrder.getOrderSn(), orderExchangeDetailVO);

        //更新换货后商品表优惠金额
        orderExchangeService.updateOrderProductAmount(orderExchangeDetailVO);

        //获取最新的数据
        exchangeOrder = orderModel.getOrderByOrderSn(exchangeOrder.getOrderSn());

        // 更新支付表支付金额
        orderExchangeService.updatePayAmount(exchangeOrder.getPaySn(), exchangeOrder.getOrderAmount());

        //更新换货后扩展表优惠金额
        orderExchangeService.updateExtendAmount(exchangeOrder.getOrderSn(), orderExchangeDetailVO);

        //退还库存，换货创建入库
        //退还原商品的库存
        OrderExtendPO orderExtendPO = orderExtendModel.getOrderExtendByOrderSn(orderPO.getOrderSn());

        Integer isCombination = orderPO.getOrderType() == OrderTypeEnum.COMBINATION.getValue()?1:0;
        goodsStockService.goodsStock(orderPO.getOrderSn(), orderExchangeDetailVO.getExchangeSn(), orderProductPO.getProductId(),
                orderPO.getAreaCode(), -orderExchangeRequest.getProductNum(), orderProductPO.getFinanceRuleCode(),
                orderProductPO.getBatchNo(), orderProductPO.getPurchaseSubCode(), orderProductPO.getChannelSkuId(), orderExtendPO.getBranch(), orderExtendPO.getReceiveBranchCode(), orderExtendPO.getWarehouseCode(),
                EventStockTypeEnum.ORDER_EXCHANGE_CREATE_IN_STOCK, BizTypeEnum.EXCHANGE_ORDER_INCREASE_STOCK,
                orderExchangeRequest.getApplicantInfo().getUserName(), orderPO, orderProductPO.getChannelSkuUnit(),null,isCombination);


        CompletableFuture.runAsync(() -> {
            //无需用户审批的自动审批
            if (orderExchangeRequest.getBuyerConfirmFlag() != null && ExchangeOrderConst.BUYER_CONFIRM_FLAG_0 == orderExchangeRequest.getBuyerConfirmFlag()) {
                OrderExchangeAuditDTO orderExchangeAuditDTO = new OrderExchangeAuditDTO();
                orderExchangeAuditDTO.setExchangeSn(orderExchangeDetailVO.getExchangeSn());
                orderExchangeAuditDTO.setExchangeAuditState(ExchangeOrderStatusEnum.AGREE_EXCHANGE.getValue());
                orderExchangeAuditDTO.setChannel(orderExchangeRequest.getChannel());
                try {
                    this.exchangeOrderAudit(orderExchangeAuditDTO, orderExchangeRequest.getApplicantInfo());
                } catch (Exception e) {
                    log.error("【换货申请】异步审批失败,exchangeSn={}", orderExchangeDetailVO.getExchangeSn(), e.getMessage());
                }
            }
            //发送会员MQ消息、推送微信消息
            this.exchangeMessage(orderPO.getMemberId(), orderPO.getMemberName(), exchangeSn, orderPO.getOrderSn());
        });

        return orderExchangeDetailVO;
    }

    @Override
    public void updateOrderAmount(String orderSn, OrderExchangeDetailVO orderExchangeDetailVO) {
        LambdaUpdateWrapper<OrderPO> exchangeOrderUpdateWrapper = Wrappers.lambdaUpdate(OrderPO.class);
        exchangeOrderUpdateWrapper.eq(OrderPO::getOrderSn, orderSn)
                .set(OrderPO::getStoreActivityAmount, orderExchangeDetailVO.getNewOrderInfo().getStoreActivityAmount())
                .set(OrderPO::getPlatformActivityAmount, orderExchangeDetailVO.getNewOrderInfo().getPlatformActivityAmount())
                .set(OrderPO::getStoreVoucherAmount, orderExchangeDetailVO.getNewOrderInfo().getStoreVoucherAmount())
                .set(OrderPO::getPlatformVoucherAmount, orderExchangeDetailVO.getNewOrderInfo().getPlatformVoucherAmount())
                .set(OrderPO::getPayAmount, BigDecimal.ZERO)
                .set(OrderPO::getXzCardAmount, orderExchangeDetailVO.getNewOrderInfo().getXzCardAmount())
                .set(OrderPO::getOrderAmount, orderExchangeDetailVO.getNewOrderInfo().getMoneyAmount())
                .set(OrderPO::getActivityDiscountAmount,
                        orderExchangeDetailVO.getNewOrderInfo().getGoodsAmountTotal()
                                .subtract(orderExchangeDetailVO.getNewOrderInfo().getMoneyAmount())
                                .subtract(orderExchangeDetailVO.getNewOrderInfo().getXzCardAmount()));
        orderService.update(exchangeOrderUpdateWrapper);
    }


    @Override
    public void updateOrderProductAmount(OrderExchangeDetailVO orderExchangeDetailVO) {
        LambdaUpdateWrapper<OrderProductPO> exchangeOrderProductUpdateWrapper = Wrappers.lambdaUpdate(OrderProductPO.class);
        exchangeOrderProductUpdateWrapper.eq(OrderProductPO::getOrderProductId, orderExchangeDetailVO.getNewOrderInfo().getOrderProductId())
                .set(OrderProductPO::getStoreActivityAmount, orderExchangeDetailVO.getNewOrderInfo().getStoreActivityAmount())
                .set(OrderProductPO::getPlatformActivityAmount, orderExchangeDetailVO.getNewOrderInfo().getPlatformActivityAmount())
                .set(OrderProductPO::getStoreVoucherAmount, orderExchangeDetailVO.getNewOrderInfo().getStoreVoucherAmount())
                .set(OrderProductPO::getPlatformVoucherAmount, orderExchangeDetailVO.getNewOrderInfo().getPlatformVoucherAmount())
                .set(OrderProductPO::getMoneyAmount, orderExchangeDetailVO.getNewOrderInfo().getMoneyAmount())
                .set(OrderProductPO::getXzCardAmount, orderExchangeDetailVO.getNewOrderInfo().getXzCardAmount())
                .set(OrderProductPO::getActivityDiscountAmount,
                        orderExchangeDetailVO.getNewOrderInfo().getGoodsAmountTotal()
                        .subtract(orderExchangeDetailVO.getNewOrderInfo().getMoneyAmount())
                        .subtract(orderExchangeDetailVO.getNewOrderInfo().getXzCardAmount()));
        orderProductService.update(exchangeOrderProductUpdateWrapper);
    }

    @Override
    public void updatePayAmount(String paySn, BigDecimal orderAmount) {
        OrderPayPO orderPayPO = orderPayService.getByPaySn(paySn);
        LambdaUpdateWrapper<OrderPayPO> updatePay = Wrappers.lambdaUpdate();
        updatePay.eq(OrderPayPO::getPaySn, orderPayPO.getPaySn())
                .set(OrderPayPO::getPayAmount, orderAmount);
        orderPayService.update(updatePay);
    }

    @Override
    public void updateExtendAmount(String orderSn, OrderExchangeDetailVO orderExchangeDetailVO) {
        LambdaUpdateWrapper<OrderExtendPO> exchangeOrderExtendUpdateWrapper = Wrappers.lambdaUpdate(OrderExtendPO.class);
        exchangeOrderExtendUpdateWrapper.eq(OrderExtendPO::getOrderSn, orderSn)
                .set(OrderExtendPO::getStoreVoucherAmount, orderExchangeDetailVO.getNewOrderInfo().getStoreVoucherAmount())
                .set(OrderExtendPO::getPlatformVoucherAmount, orderExchangeDetailVO.getNewOrderInfo().getPlatformVoucherAmount());
        orderExtendService.update(exchangeOrderExtendUpdateWrapper);
    }

    @Override
    public void updateOrderToExchange(String orderSn, int exchangeFlag) {
        LambdaUpdateWrapper<OrderPO> orderUpdateWrapper = Wrappers.lambdaUpdate(OrderPO.class);
        orderUpdateWrapper.eq(OrderPO::getOrderSn, orderSn)
                .set(OrderPO::getExchangeFlag, exchangeFlag);
        orderService.update(orderUpdateWrapper);
    }


    @Override
    public OrderExchangeDetailPO saveExchangeOrderDetail(OrderExchangeDetailVO orderExchangeDetailVO, String operatorName) {
        ExchangeOrderDTO productInfo = orderExchangeDetailVO.getProductInfo();
        ExchangeOrderDTO newOrderDTO = orderExchangeDetailVO.getNewOrderInfo();

        OrderExchangeDetailPO orderExchangeDetailPO = new OrderExchangeDetailPO();
        orderExchangeDetailPO.setExchangeSn(orderExchangeDetailVO.getExchangeSn());
        orderExchangeDetailPO.setOrderSn(productInfo.getOrderSn());
        orderExchangeDetailPO.setOrderProductId(productInfo.getOrderProductId());
        orderExchangeDetailPO.setProductName(productInfo.getGoodsName());
        orderExchangeDetailPO.setProductNum(productInfo.getProductNum());

        orderExchangeDetailPO.setExchangeOrderSn(newOrderDTO.getOrderSn());
        orderExchangeDetailPO.setExchangeOrderProductId(newOrderDTO.getOrderProductId());
        orderExchangeDetailPO.setExchangeProductName(newOrderDTO.getGoodsName());
        orderExchangeDetailPO.setExchangeProductNum(newOrderDTO.getProductNum());

        orderExchangeDetailPO.setRefundAmount(orderExchangeDetailVO.getRefundAmount());
        orderExchangeDetailPO.setActualRefundAmount(orderExchangeDetailVO.getRefundAmount());

        orderExchangeDetailPO.setXzCardAmount(orderExchangeDetailVO.getXzCardAmount());
        orderExchangeDetailPO.setPlatformVoucherAmount(orderExchangeDetailVO.getPlatformVoucherAmount());
        orderExchangeDetailPO.setPlatformActivityAmount(orderExchangeDetailVO.getPlatformActivityAmount());

        orderExchangeDetailPO.setStoreActivityAmount(orderExchangeDetailVO.getStoreActivityAmount());
        orderExchangeDetailPO.setStoreVoucherAmount(orderExchangeDetailVO.getStoreVoucherAmount());

        orderExchangeDetailPO.setCreateBy(operatorName);

        orderExchangeDetailMapper.insert(orderExchangeDetailPO);
        return orderExchangeDetailPO;

    }

    private void exchangeMessage(Integer memberId, String memberName, String exchangeSn, String orderSn) {
        //消息通知
        List<MessageSendProperty> messageSendPropertyList = new ArrayList<>();
        // 微信消息通知
        List<MessageSendProperty> messageSendPropertyList4Wx = new ArrayList<>();
        messageSendPropertyList4Wx.add(new MessageSendProperty("first", "【订单换货通知】"));
        messageSendPropertyList4Wx.add(new MessageSendProperty("keyword1", memberName));
        messageSendPropertyList4Wx.add(new MessageSendProperty("keyword2", "换货提醒通知"));
        messageSendPropertyList4Wx.add(new MessageSendProperty("keyword3", String.format("订单号%s中的商品已申请换货", orderSn)));
        messageSendPropertyList4Wx.add(new MessageSendProperty("keyword4", TimeUtil.getDateTimeString(new Date())));
        messageSendPropertyList4Wx.add(new MessageSendProperty("remark", "请前往个人中心查看详情"));
        log.info("exchangeMessage===url====");
        log.info(newMallDomainUrl + "/cdmallchat/pages/order/exchangeDetail?exchangeSn=" + exchangeSn);
        //messageSendPropertyList4Wx.add(new MessageSendProperty("url", newMallDomainUrl + "cdmallchat/pages/order/exchangeDetail?exchangeSn=" + exchangeSn));
        String msgLinkInfo = "{}";
        MessageSendVO messageSendVO = new MessageSendVO(messageSendPropertyList, messageSendPropertyList4Wx,
                null, memberId, ExchangeOrderConst.ORDER_EXCHANGE_REMINDER_TEMPLATE, msgLinkInfo);
        // 发送到mq
        try {
            rabbitTemplate.convertAndSend(MQ_EXCHANGE_NAME, MQ_QUEUE_NAME_MEMBER_MSG, messageSendVO);
        } catch (Exception e) {
            log.info("发消息异常捕获,{}", JSON.toJSONString(messageSendVO), e);
        }
    }

    /**
     * 创建换货订单（换货后商品订单）
     */
    @Override
    public OrderPO createExchangeOrder(OrderPO orderPO, OrderExchangeDetailVO orderExchangeDetailVO, OrderExchangeRequest orderExchangeRequest) {
        long pno = shardingId.next(SeqEnum.PNO, orderPO.getMemberId().toString());
        String paySn = pno + "";
        OrderExtendPO extendPO = orderExtendModel.getOrderExtendByOrderSn(orderPO.getOrderSn());
        //构成提交订单参数
        OrderSubmitParamDTO submitParam = new OrderSubmitParamDTO();
        submitParam.setAreaCode(orderPO.getAreaCode());
        submitParam.setStoreId(orderPO.getStoreId().toString());
        submitParam.setChannel(OrderCreateChannel.WEB.getValue());
        submitParam.setIsCart(false);
        submitParam.setOrderFrom(1);
        submitParam.setSource(3);
        submitParam.setProductId(orderExchangeDetailVO.getNewOrderInfo().getProductId());
        List<OrderSubmitParamDTO.StoreInfo> storeInfoList = new ArrayList<>();
        OrderSubmitParamDTO.StoreInfo storeInfo = new OrderSubmitParamDTO.StoreInfo();
        storeInfo.setStoreId(orderPO.getStoreId());
        storeInfoList.add(storeInfo);
        submitParam.setStoreInfoList(storeInfoList);
        submitParam.setNumber(orderExchangeDetailVO.getNewOrderInfo().getProductNum());
        submitParam.setOrderPattern(OrderPatternEnum.EXCHANGE_ORDER.getValue());
        submitParam.setFinanceRuleCode(orderExchangeRequest.getFinanceRuleCode());
        submitParam.setDealerCode(orderExchangeRequest.getDealerCode());


        //构造地址
        OrderAddressDTO addressDTO = new OrderAddressDTO();
        addressDTO.setReceiverName(extendPO.getReceiverName());
        addressDTO.setReceiverMobile(extendPO.getReceiverMobile());
        addressDTO.setProvince(extendPO.getReceiverProvinceCode());
        addressDTO.setCity(extendPO.getReceiverCityCode());
        addressDTO.setCityCode(extendPO.getReceiverCityCode());
        addressDTO.setDistrict(extendPO.getReceiverDistrictCode());
        addressDTO.setTown(extendPO.getReceiverTownCode());
        addressDTO.setDetailAddress(extendPO.getReceiverAddress());
        submitParam.setOrderAddress(addressDTO);

        //构造订单传输对象，用于下单处理
        OrderSubmitMqConsumerDTO consumerDTO = new OrderSubmitMqConsumerDTO();
        consumerDTO.setParamDTO(submitParam);
        consumerDTO.setMemberId(orderPO.getMemberId());
        consumerDTO.setUserNo(orderPO.getUserNo());
        consumerDTO.setPaySn(paySn);
        consumerDTO.setAreaCode(orderPO.getAreaCode());

        //将表标识放入redis，key=标识前缀+paySn，mq处理成功或失败后删除标识
        stringRedisTemplate.opsForValue().set(OrderConst.ORDER_SUBMIT_MQ_REDIS_PREFIX + orderPO.getPaySn(), "");

        //查询用户信息
        Member member = memberFeignClient.getMemberByMemberId(orderPO.getMemberId());

        OrderSubmitDTO orderSubmitDTO = OrderSubmitDTOContext.ORDER_SUBMIT_DTO_CREATOR_MAP
                .get(OrderPatternEnum.valueOf(submitParam.getOrderPattern()))
                .getOrderSubmitDto(submitParam, member, submitParam.getAreaCode(), addressDTO, true);


        //处理运费
        List<BigDecimal> expressFeeList = new ArrayList<>();
        for (OrderSubmitDTO.OrderInfo orderInfo : orderSubmitDTO.getOrderInfoList()) {
            BigDecimal expressFee = BigDecimal.ZERO;
            expressFeeList.add(expressFee);
        }
        orderService.dealExpress(orderSubmitDTO, expressFeeList);

        //解析入参
        List<String> result = Lists.newArrayList();
        try {
            //通用提交订单
            result = orderModel.submit(orderSubmitDTO, member, consumerDTO);
        } catch (Exception e) {
            log.warn("异常为：{}", e.getClass().getName());
            if (e instanceof BusinessException || e instanceof MallException) {
                throw e;
            }
            if (e instanceof DuplicateKeyException) {
                throw new MallException("创建换货订单失败,请联系管理员！",
                        "创建换货订单失败," + ErrorCodeEnum.S.DATA_UNIQUE_CONFLICT.getMsg() + ",支付单号:" + orderPO.getPaySn(),
                        ErrorCodeEnum.S.DATA_UNIQUE_CONFLICT.getCode(), e);
            } else {
                throw new MallException("创建订单失败,请联系管理员！", "创建订单失败, 支付单号:" + orderPO.getPaySn(),
                        ErrorCodeEnum.S.SYSTEM_EXCEPTION.getCode(), e);
            }
        }

        OrderExample example = new OrderExample();
        example.setPaySn(paySn);
        List<OrderPO> orderPOList = orderModel.getOrderList(example, null);
        BizAssertUtil.notEmpty(orderPOList, new BusinessException("订单不存在"));
        return orderPOList.get(0);
    }

    @Override
    public OrderExchangePO saveExchangeApplyOrder(String exchangeSn, Long storeId, Integer memberId
            , UserDTO userDTO, Integer buyerConfirmFlag, String exchangeReason) throws Exception {
        OrderExchangePO orderExchangePO = new OrderExchangePO();
        orderExchangePO.setExchangeSn(exchangeSn);
        orderExchangePO.setStoreId(storeId);
        orderExchangePO.setMemberId(memberId);
        orderExchangePO.setExchangeReason(exchangeReason);
        orderExchangePO.setBuyerConfirmFlag(buyerConfirmFlag);
        orderExchangePO.setApplicantId(userDTO.getUserId());
        orderExchangePO.setApplicantName(userDTO.getUserName());
        orderExchangePO.setApplicantRole(userDTO.getUserRole());
        orderExchangePO.setCreateBy(userDTO.getUserName());
        orderExchangeMapper.insert(orderExchangePO);
        return orderExchangePO;
    }

    /**
     * --四舍五入取两位小数
     * 可换数量 = 原商品价格 * 数量 / 新商品价格
     * 新商品实付金额 = 新商品价格 * 可换数量 * （原商品实付金额 / 应付金额 ）
     * 乡助卡金额 = 新商品价格 * 可换数量 * （乡助卡金额 / 应付金额 ）
     * 店铺活动优惠金额 = 新商品价格 * 可换数量 * （店铺活动优惠金额 / 应付金额 ）
     * 平台活动优惠金额 = 新商品价格 * 可换数量 * （平台活动优惠金额 / 应付金额 ）
     * 店铺优惠券优惠金额 = 新商品价格 * 可换数量 * （店铺优惠券优惠金额 / 应付金额 ）
     * 平台优惠券优惠金额 = 新商品价格 * 可换数量 * （平台优惠券优惠金额 / 应付金额 ）
     * 最后一个金额用轧差的方式计算
     */
    @Override
    public OrderExchangeDetailVO orderExchangeTrial(OrderExchangeRequest orderExchangeRequest) {
        OrderExchangeDetailVO orderExchangeDetailVO = new OrderExchangeDetailVO();


        OrderProductPO orderProductPO = orderProductModel.getOrderProductByOrderProductId(orderExchangeRequest.getOrderProductId());
        BizAssertUtil.isTrue(orderProductPO.getProductNum() - orderProductPO.getReturnNumber() < orderExchangeRequest.getProductNum(),
                "商品有效数量小于输入的换货数量，不能进行换货");


        OrderPO orderPO = orderModel.getOrderByOrderSn(orderExchangeRequest.getOrderSn());

        ProductPriceVO productPriceVO = orderLocalUtils.getProductPrice(orderExchangeRequest.getProductId(), orderPO.getAreaCode(), orderExchangeRequest.getFinanceRuleCode());

        //被换的商品信息
        ExchangeOrderDTO exchangeOrderDTO = new ExchangeOrderDTO(orderProductPO, orderExchangeRequest.getProductNum());
        //换后的商品信息
        ExchangeOrderDTO newExchangeOrderDTO = getExchangeOrder(exchangeOrderDTO, productPriceVO);

        //试算的时候才校验，如果是预占订单落库则不需要
        if (orderExchangeRequest.getIsTrial() == null || orderExchangeRequest.getIsTrial()) {
            BizAssertUtil.isTrue(productPriceVO.getProduct().getProductStock() < newExchangeOrderDTO.getProductNum(),
                    "库存不足");
        }

        BizAssertUtil.isTrue(orderProductPO.getGoodsAmountTotal().compareTo(newExchangeOrderDTO.getProductShowPrice()
                        .multiply(new BigDecimal(newExchangeOrderDTO.getProductNum()))) < 0,
                "换货商品总价值大于被换商品的总价值，不能进行换货");

        orderExchangeDetailVO.setProductInfo(exchangeOrderDTO);
        orderExchangeDetailVO.setNewOrderInfo(newExchangeOrderDTO);

        orderExchangeDetailVO.setXzCardAmount(exchangeOrderDTO.getXzCardAmount().subtract(newExchangeOrderDTO.getXzCardAmount()));
        orderExchangeDetailVO.setPlatformVoucherAmount(exchangeOrderDTO.getPlatformVoucherAmount().subtract(newExchangeOrderDTO.getPlatformVoucherAmount()));
        orderExchangeDetailVO.setPlatformActivityAmount(exchangeOrderDTO.getPlatformActivityAmount().subtract(newExchangeOrderDTO.getPlatformActivityAmount()));
        orderExchangeDetailVO.setStoreActivityAmount(exchangeOrderDTO.getStoreActivityAmount().subtract(newExchangeOrderDTO.getStoreActivityAmount()));
        orderExchangeDetailVO.setStoreVoucherAmount(exchangeOrderDTO.getStoreVoucherAmount().subtract(newExchangeOrderDTO.getStoreVoucherAmount()));

        return orderExchangeDetailVO;
    }

    /**
     * 根据原订单相关金额及数量 计算得出 换货后商品的相关金额
     */
    private ExchangeOrderDTO getExchangeOrder(ExchangeOrderDTO exchangeOrderDTO, ProductPriceVO productPriceVO) {
        //获取原商品
        OrderProductPO orderProductPO = orderProductModel.getOrderProductByOrderProductId(exchangeOrderDTO.getOrderProductId());
        ExchangeOrderDTO newExchangeOrderDTO = new ExchangeOrderDTO();
        //新商品价格
        BigDecimal productShowPrice;
        if (productPriceVO.getProductPriceBranchRange() == null) {
            if (productPriceVO.getProduct() == null || BigDecimal.ZERO.compareTo(productPriceVO.getProduct().getProductPrice()) == 0) {
                throw new BusinessException("该商品不可售");
            }
            productShowPrice = productPriceVO.getProduct().getProductPrice();
        } else {
            productShowPrice = productPriceVO.getProductPriceBranchRange().getTaxPrice();
        }

        //原商品用于换货的数量
        BigDecimal productNum = new BigDecimal(exchangeOrderDTO.getProductNum());
        //可换数量=原商品价格*数量/新商品价格
        int exchangeProductNum = exchangeOrderDTO.getProductShowPrice().multiply(productNum).divide(productShowPrice, 0, RoundingMode.DOWN).intValue();
        //新商品应付金额
        BigDecimal goodsAmountTotal = productShowPrice.multiply(new BigDecimal(exchangeProductNum));
        //实付金额,四舍五入取两位小数:  实付金额 = 新商品应付金额 *(原商品用于换货的实付金额/原商品用于换货的总价);原商品用于换货的实付金额 = 商品实付 *（ 换货数量 / 总数量）；
        // 注意：：：除法的部分都要写到最后
        //BigDecimal moneyAmount = goodsAmountTotal.multiply(exchangeOrderDTO.getMoneyAmount()).divide(exchangeOrderDTO.getGoodsAmountTotal(), 2, RoundingMode.HALF_UP);
        BigDecimal moneyAmount = goodsAmountTotal.multiply(orderProductPO.getMoneyAmount()).multiply(productNum)
                .divide(exchangeOrderDTO.getGoodsAmountTotal().multiply(new BigDecimal(orderProductPO.getProductNum())), 2, RoundingMode.HALF_UP);


        //乡助卡金额
        BigDecimal xzCardAmount = goodsAmountTotal.multiply(exchangeOrderDTO.getXzCardAmount()).divide(exchangeOrderDTO.getGoodsAmountTotal(), 2, RoundingMode.HALF_UP);

        //店铺活动优惠金额 = 应付金额 * (原活动金额 / 原应付金额)
        BigDecimal storeActivityAmount = goodsAmountTotal.multiply(exchangeOrderDTO.getStoreActivityAmount()).divide(exchangeOrderDTO.getGoodsAmountTotal(), 2, RoundingMode.HALF_UP);

        //平台活动优惠金额
        BigDecimal platformActivityAmount = goodsAmountTotal.multiply(exchangeOrderDTO.getPlatformActivityAmount()).divide(exchangeOrderDTO.getGoodsAmountTotal(), 2, RoundingMode.HALF_UP);

        //店铺优惠券优惠金额
        BigDecimal storeVoucherAmount = goodsAmountTotal.multiply(exchangeOrderDTO.getStoreVoucherAmount()).divide(exchangeOrderDTO.getGoodsAmountTotal(), 2, RoundingMode.HALF_UP);

        //平台优惠券优惠金额  1
        BigDecimal platformVoucherAmount = goodsAmountTotal.multiply(exchangeOrderDTO.getPlatformVoucherAmount()).divide(exchangeOrderDTO.getGoodsAmountTotal(), 2, RoundingMode.HALF_UP);

        //优惠总金额
        BigDecimal totalDiscountAmount = storeActivityAmount.add(platformActivityAmount).add(storeVoucherAmount).add(platformVoucherAmount);
        //实际应优惠总金额（应付金额-实付金额-乡助卡金额）
        BigDecimal actualTotalDiscountAmount = goodsAmountTotal.subtract(moneyAmount).subtract(xzCardAmount);

        //轧差
        //没有使用优惠券的情况，1、只有支付金额，2、乡助卡，3、支付金额 + 乡助卡
        if (totalDiscountAmount.compareTo(BigDecimal.ZERO) == 0) {
            //轧差：新商品乡助卡金额 = 新商品应付金额 - 新商品实付金额
            xzCardAmount = goodsAmountTotal.subtract(moneyAmount);
        } else {
            //轧差：只要有优惠，选取其中一种优惠进行轧差：实际应优惠总金额（应付金额-实付金额-乡助卡金额） - 其余三种优惠金额
            if (storeActivityAmount.compareTo(BigDecimal.ZERO) != 0) {
                storeActivityAmount = actualTotalDiscountAmount.subtract(platformActivityAmount)
                        .subtract(storeVoucherAmount)
                        .subtract(platformVoucherAmount);
            } else if (platformActivityAmount.compareTo(BigDecimal.ZERO) != 0) {
                platformActivityAmount = actualTotalDiscountAmount.subtract(storeActivityAmount)
                        .subtract(storeVoucherAmount)
                        .subtract(platformVoucherAmount);
            } else if (storeVoucherAmount.compareTo(BigDecimal.ZERO) != 0) {
                storeVoucherAmount = actualTotalDiscountAmount.subtract(storeActivityAmount)
                        .subtract(platformVoucherAmount)
                        .subtract(platformActivityAmount);
            } else if (platformVoucherAmount.compareTo(BigDecimal.ZERO) != 0) {
                platformVoucherAmount = actualTotalDiscountAmount.subtract(storeActivityAmount)
                        .subtract(platformActivityAmount)
                        .subtract(storeVoucherAmount);
            }
        }

        newExchangeOrderDTO.setProductShowPrice(productShowPrice);
        newExchangeOrderDTO.setProductNum(exchangeProductNum);
        newExchangeOrderDTO.setGoodsAmountTotal(goodsAmountTotal);
        newExchangeOrderDTO.setXzCardAmount(xzCardAmount);
        newExchangeOrderDTO.setMoneyAmount(moneyAmount);
        newExchangeOrderDTO.setStoreVoucherAmount(storeVoucherAmount);
        newExchangeOrderDTO.setPlatformVoucherAmount(platformVoucherAmount);
        newExchangeOrderDTO.setStoreActivityAmount(storeActivityAmount);
        newExchangeOrderDTO.setPlatformActivityAmount(platformActivityAmount);

        newExchangeOrderDTO.setProductId(productPriceVO.getProduct().getProductId());
        newExchangeOrderDTO.setProductImage(productPriceVO.getProduct().getMainImage());
        newExchangeOrderDTO.setGoodsId(productPriceVO.getGoods().getGoodsId());
        newExchangeOrderDTO.setGoodsName(productPriceVO.getGoods().getGoodsName());
        newExchangeOrderDTO.setSpecValues(productPriceVO.getProduct().getSpecValues());
        return newExchangeOrderDTO;
    }

    @Override
    public void dealProductExchangeFlag(List<OrderProductListVO> orderProductList) {
        if (CollectionUtils.isEmpty(orderProductList)) {
            return;
        }
        List<Long> orderProductIdList = orderProductList.stream().map(x -> x.getOrderProductId()).collect(Collectors.toList());
        OrderExchangeDetailExample orderExchangeExample = new OrderExchangeDetailExample();
        orderExchangeExample.setOrderProductIdIn(orderProductIdList);
        List<OrderExchangeDetailPO> orderExchangePOList = orderExchangeDetailMapper.getOrderExchangeDetailList(orderExchangeExample);
        if (CollectionUtils.isEmpty(orderExchangePOList)) {
            return;
        }

        for (OrderProductListVO orderProductVO : orderProductList) {
            for (OrderExchangeDetailPO orderExchangeDetailVO : orderExchangePOList) {
                if (orderProductVO.getOrderProductId().equals(orderExchangeDetailVO.getOrderProductId())) {
                    orderProductVO.setExchangeFlag(true);
                    break;
                }
            }
        }

    }


    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void exchangeOrderAudit(OrderExchangeAuditDTO orderExchangeAuditDTO, UserDTO userDTO) throws Exception {

        BizAssertUtil.isTrue(ExchangeOrderStatusEnum.valueOf(orderExchangeAuditDTO.getExchangeAuditState()) == null, "审批异常，不存在该审批状态");

        String exchangeSn = orderExchangeAuditDTO.getExchangeSn();

        OrderExchangeExample example = new OrderExchangeExample();
        example.setExchangeSn(exchangeSn);

        OrderExchangePO exchangePO = orderExchangeMapper.getOrderExchange(example);
        BizAssertUtil.isTrue(exchangePO == null, "换货单不存在");
        BizAssertUtil.isTrue(ExchangeOrderConst.EXCHANGE_ORDER_STATE_FINISH == exchangePO.getExchangeOrderState(),
                "该换货单已经完成不能执该操作");

        orderExchangeService.exchangeOrderAuditDeal(orderExchangeAuditDTO, userDTO);

    }

    @Override
    @Transactional
    public void exchangeOrderAuditDeal(OrderExchangeAuditDTO orderExchangeAuditDTO, UserDTO userDTO) throws Exception {
        int exchangeOrderState = orderExchangeAuditDTO.getExchangeAuditState();
        String exchangeSn = orderExchangeAuditDTO.getExchangeSn();

        OrderExchangeExample example = new OrderExchangeExample();
        example.setExchangeSn(exchangeSn);

        OrderExchangePO exchangePO = orderExchangeMapper.getOrderExchange(example);
        BizAssertUtil.isTrue(exchangePO == null, "换货单不存在");
        BizAssertUtil.isTrue(ExchangeOrderConst.EXCHANGE_ORDER_STATE_FINISH == exchangePO.getExchangeOrderState(),
                "该换货单已经完成不能执该操作");
        int returnBy = 0;
        String content = "";

        if (OrderConst.LOG_ROLE_MEMBER == userDTO.getUserRole()) {
            BizAssertUtil.isTrue(!exchangePO.getMemberId().equals(userDTO.getUserId().intValue()), "不能审批非本人的换货单");
            returnBy = OrderConst.RETURN_BY_1;
        } else if (OrderConst.LOG_ROLE_VENDOR == userDTO.getUserRole()) {
            BizAssertUtil.isTrue(!exchangePO.getStoreId().equals(userDTO.getStoreId()), "不能审批非本店铺的换货单");
            returnBy = OrderConst.RETURN_BY_2;
        }

        OrderExchangeDetailPO exchangeDetailPO = orderExchangeDetailMapper.getExchangeDetailByExchangeSn(exchangeSn);
        switch (exchangeOrderState) {
            case ExchangeOrderConst.EXCHANGE_ORDER_STATE_AUTO_CLOSE:
                BizAssertUtil.isTrue(ExchangeOrderConst.EXCHANGE_ORDER_STATE_WAIT_AUDIT != exchangePO.getExchangeOrderState()
                        , String.format("该换货申请单状态为%s,不能自动取消换货", ExchangeOrderStatusEnum.valueOf(exchangePO.getExchangeOrderState()).getDesc()));
                content = "自动取消换货";
                orderExchangeService.dealCloseExchangeOrder(exchangeDetailPO, userDTO, returnBy, content);
                break;
            case ExchangeOrderConst.EXCHANGE_ORDER_STATE_CANCEL:
                BizAssertUtil.isTrue(ExchangeOrderConst.EXCHANGE_ORDER_STATE_WAIT_AUDIT != exchangePO.getExchangeOrderState()
                                && ExchangeOrderConst.EXCHANGE_ORDER_STATE_AGREE != exchangePO.getExchangeOrderState()
                        , String.format("该换货申请单状态为%s,不能撤销换货", ExchangeOrderStatusEnum.valueOf(exchangePO.getExchangeOrderState()).getDesc()));
                //撤销换货：取消换货订单，退还换货后订单的库存
                content = "撤销换货";
                orderExchangeService.dealCloseExchangeOrder(exchangeDetailPO, userDTO, returnBy, content);
                break;
            case ExchangeOrderConst.EXCHANGE_ORDER_STATE_DISAGREE:
                //拒绝换货：取消换货订单，退还换货后订单的库存
                BizAssertUtil.isTrue(ExchangeOrderConst.EXCHANGE_ORDER_STATE_WAIT_AUDIT != exchangePO.getExchangeOrderState()
                        , String.format("该换货申请单状态为%s,不能拒绝换货", ExchangeOrderStatusEnum.valueOf(exchangePO.getExchangeOrderState()).getDesc()));
                content = "拒绝换货";
                orderExchangeService.dealCloseExchangeOrder(exchangeDetailPO, userDTO, returnBy, content);
                break;
            case ExchangeOrderConst.EXCHANGE_ORDER_STATE_AGREE:
                //同意换货：支付换货后的订单，生成原商品的换货退款单
                BizAssertUtil.isTrue(ExchangeOrderConst.EXCHANGE_ORDER_STATE_WAIT_AUDIT != exchangePO.getExchangeOrderState()
                        , String.format("该换货申请单状态为%s,不能同意换货", ExchangeOrderStatusEnum.valueOf(exchangePO.getExchangeOrderState()).getDesc()));
                OrderProductPO orderProductPO = orderProductModel.getOrderProductByOrderProductId(exchangeDetailPO.getOrderProductId());
                BizAssertUtil.isTrue(orderProductPO.getProductNum() - orderProductPO.getReturnNumber() < exchangeDetailPO.getProductNum(),
                        "商品有效数量小于可换货数量,不能进行换货,请取消这次同意换货");
                content = "同意换货";
                //判断有效数量是否大于等于换货数量
                orderExchangeService.dealAgreeExchangeOrder(exchangeDetailPO, userDTO);
                break;
            default:
                break;
        }
        //记录换货单操作轨迹
        orderLogModel.insertOrderLog(userDTO.getUserRole(), Long.valueOf(userDTO.getUserId()),
                userDTO.getUserName(), exchangeSn, exchangePO.getExchangeOrderState(), exchangeOrderState,
                LoanStatusEnum.APPLY_SUCCESS.getValue(), content, OrderCreateChannel.valueOf(orderExchangeAuditDTO.getChannel()));

        //记录原订单操作轨迹
        orderLogModel.insertOrderLog(userDTO.getUserRole(), Long.valueOf(userDTO.getUserId()),
                userDTO.getUserName(), exchangeDetailPO.getOrderSn(), exchangePO.getExchangeOrderState(), exchangeOrderState,
                LoanStatusEnum.APPLY_SUCCESS.getValue(), content, OrderCreateChannel.valueOf(orderExchangeAuditDTO.getChannel()));

        //更新换货申请审批记录
        LambdaUpdateWrapper<OrderExchangePO> orderExchangePOLambdaUpdateWrapper = new LambdaUpdateWrapper();
        orderExchangePOLambdaUpdateWrapper.eq(OrderExchangePO::getExchangeSn, exchangeSn)
                .set(OrderExchangePO::getExchangeOrderState, exchangeOrderState)
                .set(OrderExchangePO::getApproverId, userDTO.getUserId())
                .set(OrderExchangePO::getApproverName, userDTO.getUserName())
                .set(OrderExchangePO::getApproverRole, userDTO.getUserRole());
        boolean result = orderExchangeService.update(orderExchangePOLambdaUpdateWrapper);
        if(!result) {
            log.error("更新换货申请审批记录失败,exchangeSn = {}", exchangeSn);
        }
    }

    /**
     * 处理同意换货的流程
     * 1、退还原商品的库存
     * 2、支付换货后的订单
     * 3、原订单生成换货退款的售后单
     */
    @Override
    public void dealAgreeExchangeOrder(OrderExchangeDetailPO exchangeDetailPO, UserDTO userDTO) {
        OrderPO orderPO = orderModel.getOrderByOrderSn(exchangeDetailPO.getOrderSn());
        OrderPO exchangeOrderPO = orderModel.getOrderByOrderSn(exchangeDetailPO.getExchangeOrderSn());
        // 支付待付款订单
        orderPayModel.orderPaySuccess(exchangeOrderPO, null, orderPO.getPaymentCode(), orderPO.getPaymentName(), null,
                null);
        //生成 换货退款 售后单,并更新的换货详情里面的afsSn
        dealExchangeAfterSale(orderPO, exchangeDetailPO, userDTO);

    }

    /**
     * 1、原订单生成换货退款的售后单
     * 2、更新换货单详情的关联的售后单
     */
    private void dealExchangeAfterSale(OrderPO orderPO, OrderExchangeDetailPO exchangeDetailPO, UserDTO userDTO) {
        OrderExchangePO exchangePO = orderExchangeMapper.getOrderExchangeByExchangeSn(exchangeDetailPO.getExchangeSn());
        int returnBy = 0;
        if (exchangePO.getApplicantRole() == 2) {
            returnBy = OrderConst.RETURN_BY_2;
        } else if (exchangePO.getApplicantRole() == 3) {
            returnBy = OrderConst.RETURN_BY_1;
        }


        OrderAfterDTO orderAfterDTO = buildExchangeAfterDTO(exchangeDetailPO, returnBy, "同意换货");

        AfsProductVO afsProductVO = orderAfterServiceModel.createAfsProductVO(exchangeDetailPO.getOrderProductId(), exchangeDetailPO.getRefundAmount(),
                BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO,
                exchangeDetailPO.getXzCardAmount(), BigDecimal.ZERO,
                exchangeDetailPO.getPlatformVoucherAmount(), exchangeDetailPO.getPlatformActivityAmount(),
                exchangeDetailPO.getProductNum(), OrderConst.RETURN_BY_2, orderAfterDTO.getAfsType());
        String afsSn = orderAfterServiceModel.createAfterSaleOrder(orderPO, orderAfterDTO, afsProductVO, userDTO);
        LambdaUpdateWrapper<OrderExchangeDetailPO> orderExchangeDetailUpdateWrapper = new LambdaUpdateWrapper<>();
        orderExchangeDetailUpdateWrapper.eq(OrderExchangeDetailPO::getExchangeSn, exchangeDetailPO.getExchangeSn());
        orderExchangeDetailUpdateWrapper.set(OrderExchangeDetailPO::getAfsSn, afsSn);
        orderExchangeDetailService.update(orderExchangeDetailUpdateWrapper);
    }

    private OrderAfterDTO buildExchangeAfterDTO(OrderExchangeDetailPO exchangeDetailPO, int returnBy, String applyReasonContent) {
        OrderAfterDTO orderAfterDTO = new OrderAfterDTO();
        orderAfterDTO.setOrderSn(exchangeDetailPO.getOrderSn());
        orderAfterDTO.setAfsType(OrdersAfsConst.AFS_TYPE_REPLACEMENT);
        orderAfterDTO.setChannel(OrderCreateChannel.WEB.getValue());
        orderAfterDTO.setGoodsState(OrdersAfsConst.GOODS_STATE_NO);
        orderAfterDTO.setReturnBy(returnBy);
        orderAfterDTO.setApplyReasonContent(applyReasonContent);
        orderAfterDTO.setAfsDescription(applyReasonContent);
        List<OrderAfterDTO.AfterProduct> productList = new ArrayList<>();
        OrderAfterDTO.AfterProduct afterProduct = new OrderAfterDTO.AfterProduct();
        afterProduct.setOrderProductId(exchangeDetailPO.getOrderProductId());
        afterProduct.setAfsNum(exchangeDetailPO.getProductNum());
        productList.add(afterProduct);

        orderAfterDTO.setProductList(productList);
        return orderAfterDTO;
    }

    /**
     * 处理关闭（拒绝换货/撤销换货/取消换货）换货的流程
     * 1、扣减原商品库存
     * 2、恢复订单换货标识
     */
    @Override
    public void dealCloseExchangeOrder(OrderExchangeDetailPO exchangeDetailPO, UserDTO userDTO, int returnBy, String content) throws Exception {
        OrderPO exchangeOrderPO = orderModel.getOrderByOrderSn(exchangeDetailPO.getExchangeOrderSn());
        //1、扣减原商品库存
        this.reduceProductStock(exchangeDetailPO, userDTO);
        switch (exchangeOrderPO.getOrderState()) {
            case OrderConst.ORDER_STATE_10:
                List<OrderPO> orderPOList = new ArrayList<>();
                orderPOList.add(exchangeOrderPO);
                //换货订单未支付---取消订单
                orderModel.cancelOrder(orderPOList, content, null, userDTO.getUserRole(), userDTO.getUserId(),
                        userDTO.getUserName(), content, returnBy);
                break;
            case OrderConst.ORDER_STATE_20:
            case OrderConst.ORDER_STATE_30:
                /**
                 * 换货订单已支付
                 * 1、扣减原商品库存---库存不足的话，重新选择批次号
                 * 2、撤销原商品的售后单
                 * 3、生成换后商品的售后单
                 * */
                orderExchangeService.cancelPaidOrder(exchangeDetailPO, userDTO);
                break;
            default:
                break;
        }
        //恢复订单换货标识
        ExchangeOrderDetailDTO exchangeOrderDetailDTO = new ExchangeOrderDetailDTO();
        exchangeOrderDetailDTO.setOrderSn(exchangeDetailPO.getOrderSn());
        //换货订单可用状态
        List<Integer> exchangeOrderStateList = Arrays.asList(ExchangeOrderConst.EXCHANGE_ORDER_STATE_WAIT_AUDIT, ExchangeOrderConst.EXCHANGE_ORDER_STATE_AGREE, ExchangeOrderConst.EXCHANGE_ORDER_STATE_FINISH);
        exchangeOrderDetailDTO.setExchangeOrderStateList(exchangeOrderStateList);
        List<ExchangeOrderDetailDTO> exchangeOrderDetailDTOList = orderExchangeMapper.getExchangeOrderList(exchangeOrderDetailDTO);
        if(CollectionUtils.isEmpty(exchangeOrderDetailDTOList)){
            //若该订单的全部换货单都是 拒绝换货/撤销换货/取消换货 的状态，则变更换货单未非换货单
            orderExchangeService.updateOrderToExchange(exchangeDetailPO.getOrderSn(), ExchangeOrderConst.ORDER_EXCHANGE_FLAG_0);
        }

    }

    /**
     * 撤销换货（换货订单已支付）
     * 1、扣减原商品库存
     * 2、撤销原商品的售后单
     * 3、生成换后商品的售后单
     */
    @Override
    public void cancelPaidOrder(OrderExchangeDetailPO exchangeDetailPO, UserDTO userDTO) {
        //2、撤销原商品的售后单
        this.revokeRefund(exchangeDetailPO.getAfsSn(), userDTO);
        //3、生成换后商品的售后单,不需要平台审批
        this.createChangeAfterSales(exchangeDetailPO.getExchangeOrderSn(), exchangeDetailPO.getExchangeOrderProductId(), userDTO);
    }

    /**
     * bapp 客户经理查询管护关系的换货订单列表
     * @param request
     * @return
     */
    @Override
    public PageVO<OrderExchangeReturnListVO> getBappOrderExchangeList(BappOrderReturnRequest request) {

        AuthBranchAndUserVO vo = employeeService.getBranchAndUserList(request.getJobNumber(), request.getOrgCode());
        log.info("客户经理查看换货单列表入参:{}, AuthBranchAndUserVO:{}", JSONObject.toJSONString(request), JSONObject.toJSONString(vo));
        request.setBranchList(vo.getBranchList());
        request.setUserList(vo.getUserList());
        request.setStartRow(request.getPageSize() * (request.getNumber() - 1));
        // 如果分支权限与用户权限都没有，直接返回空集合
        if (CollectionUtils.isEmpty(request.getBranchList()) && CollectionUtils.isEmpty(request.getUserList())) {
            return new PageVO<>();
        }
        int count = orderExchangeMapper.getBappOrderExchangeCount(request);
        if (count == 0) {
            log.info("工号为【{}】的客户经理管护关系下的换货单数量为0", request.getJobNumber());
            return new PageVO<>();
        }

        List<OrderExchangeReturnListVO> returnListVOList = new ArrayList<>();
        List<ExchangeOrderDetailDTO> orderDetailDTOList = new ArrayList<>();
        orderDetailDTOList = orderExchangeMapper.getBappOrderExchangeList(request);

        if (CollectionUtils.isEmpty(orderDetailDTOList)) {
            return new PageVO<>();
        }
        List<Long> orderProductIdList = orderDetailDTOList.stream().
                map(ExchangeOrderDetailDTO::getOrderProductId).filter(Objects::nonNull).collect(Collectors.toList());

        Map<Long, OrderProductPO> map = orderProductModel.getOrderProductListByOrderProductIds(orderProductIdList)
                .stream().collect(Collectors.toMap(OrderProductPO::getOrderProductId, Function.identity(), (o, n) -> o));

        orderDetailDTOList.stream().forEach(x -> {
            OrderExchangeReturnListVO orderExchangeReturnListVO = new OrderExchangeReturnListVO(x);
            OrderProductPO oldOrderProductPO = Optional.ofNullable(map.getOrDefault(x.getOrderProductId(), null))
                    .orElseThrow(() -> new BusinessException("根据订单货品id【" + x.getOrderProductId() + "】查询订单货品为空"));
            BigDecimal orderAmount = oldOrderProductPO.getProductShowPrice().multiply(new BigDecimal(x.getProductNum()));
            orderExchangeReturnListVO.setOrderAmount(orderAmount);
            //换货后的售后单号如果为空，则展示原订单信息；审批通过的换货单一定会产生售后单
            if (StringUtils.isEmpty(x.getAfsSn())) {
                OrderPO oldOrderPO = orderModel.getOrderByOrderSn(oldOrderProductPO.getOrderSn());
                //orderExchangeReturnListVO.setProductImage(FileUrlUtil.getFileUrl(oldOrderProductPO.getProductImage(), null));
                orderExchangeReturnListVO.setProductImage(oldOrderProductPO.getProductImage());
                orderExchangeReturnListVO.setSpecValues(oldOrderProductPO.getSpecValues());
                orderExchangeReturnListVO.setGoodsName(oldOrderProductPO.getGoodsName());
                orderExchangeReturnListVO.setOrderState(oldOrderPO.getOrderState());
                orderExchangeReturnListVO.setUserNo(oldOrderPO.getUserNo());

            } else {
                log.info("x.getExchangeOrderSn() == {}", x.getExchangeOrderSn());
                OrderPO newOrderPO = orderModel.getOrderByOrderSn(x.getExchangeOrderSn());
                OrderProductPO newOrderProductPO = orderProductModel.getOrderProductById(x.getExchangeOrderProductId());
                //orderExchangeReturnListVO.setProductImage(FileUrlUtil.getFileUrl(newOrderProductPO.getProductImage(), null));
                orderExchangeReturnListVO.setProductImage(newOrderProductPO.getProductImage());
                orderExchangeReturnListVO.setSpecValues(newOrderProductPO.getSpecValues());
                orderExchangeReturnListVO.setOrderState(newOrderPO.getOrderState());
                orderExchangeReturnListVO.setGoodsName(newOrderProductPO.getGoodsName());
                orderExchangeReturnListVO.setOrderAmount(newOrderProductPO.getProductShowPrice().multiply(new BigDecimal(newOrderProductPO.getProductNum())));
                orderExchangeReturnListVO.setProductNum(newOrderProductPO.getProductNum());
                orderExchangeReturnListVO.setUserNo(newOrderPO.getUserNo());
            }
            returnListVOList.add(orderExchangeReturnListVO);
        });

        // 根据用户编码批量从客户中心获取用户信息
        List<String> userNoList = returnListVOList.stream().map(OrderExchangeReturnListVO::getUserNo).collect(Collectors.toList());

        Map<String, UserInfoVo> userInfoVoMap = customerIntegration.queryUserInfoByUserNoList(userNoList, false);
        returnListVOList.forEach(x -> {
            UserInfoVo userInfoVo = userInfoVoMap.getOrDefault(x.getUserNo(), null);
            if (Objects.nonNull(userInfoVo)) {
                if (Objects.nonNull(userInfoVo.getCustInfoVo())) {
                    x.setMemberName(userInfoVo.getCustInfoVo().getCustName());
                } else if (!org.springframework.util.StringUtils.isEmpty(userInfoVo.getUserName())) {
                    x.setMemberName(userInfoVo.getUserName());
                }
            }
        });
        PagerInfo pagerInfo = new PagerInfo(request.getPageSize(), request.getNumber());
        pagerInfo.setRowsCount(count);
        return new PageVO<>(returnListVOList, pagerInfo);
    }

    /**
     * 创建换后商品的售后
     * 1、用强制退款接口生成待平台审核的售后单
     * 2、调用确认退款接口进行平台售后审批
     */
    private void createChangeAfterSales(String orderSn, Long exchangeOrderProductId, UserDTO userDTO) {
        AdminForceRefundRequest adminForceRefundRequest = new AdminForceRefundRequest();
        adminForceRefundRequest.setOrderSns(orderSn);
        adminForceRefundRequest.setReason("换货撤销");
        Admin admin = new Admin();
        admin.setAdminId(userDTO.getUserId().intValue());
        admin.setAdminName(userDTO.getUserName());
        admin.setRoleId(userDTO.getUserRole());
        admin.setPhone(userDTO.getMobile());

        //生成待平台审核的售后单
        String result = orderAdminReturnService.adminForceRefund(admin, adminForceRefundRequest);
        AssertUtil.isTrue(!SentenceConst.DEAL_SUCCESS.equals(result), "换货后的商品售后出错，请联系管理员");

        LambdaQueryWrapper<OrderAfterPO> queryWrapper = Wrappers.lambdaQuery(OrderAfterPO.class);
        queryWrapper.eq(OrderAfterPO::getOrderProductId, exchangeOrderProductId).eq(OrderAfterPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
        OrderAfterPO orderAfterPO = orderAfterMapper.selectOne(queryWrapper);
        //换后商品确认退款
        orderReturnModel.adminRefundOperation(admin, orderAfterPO.getAfsSn(), "换货后的商品售后",
                null, true, "WEB", BigDecimal.ZERO);

    }

    private void revokeRefund(String afsSn, UserDTO userDTO) {
        AssertUtil.isTrue(StringUtils.isEmpty(afsSn), "换货撤销失败（换货售后单为空）,请联系管理员");
        OperatorDTO operator = new OperatorDTO(userDTO.getUserId().intValue(), userDTO.getUserName(), userDTO.getUserRole(), userDTO.getMobile());
        FrontRevokeRefundBaseRequest revokeRequest = new FrontRevokeRefundBaseRequest();
        revokeRequest.setRemark("换货撤销");
        revokeRequest.setChannel("WEB");
        revokeRequest.setAfsSn(afsSn);
        orderReturnService.revokeRefund(revokeRequest, OrderRefundRevokingPartyEnum.PLATFORM_REJECT, operator);
    }

    private void reduceProductStock(OrderExchangeDetailPO exchangeDetailPO, UserDTO userDTO) {
        OrderPO orderPO = orderModel.getOrderByOrderSn(exchangeDetailPO.getOrderSn());
        OrderExtendPO orderExtendPO = orderExtendModel.getOrderExtendByOrderSn(exchangeDetailPO.getOrderSn());
        OrderProductPO orderProductPO = orderProductModel.getOrderProductByOrderProductId(exchangeDetailPO.getOrderProductId());
        Integer isCombination = orderPO.getOrderType() == OrderTypeEnum.COMBINATION.getValue()?1:0;
        goodsStockService.goodsStock(exchangeDetailPO.getOrderSn(), exchangeDetailPO.getExchangeSn(), orderProductPO.getProductId(),
                orderPO.getAreaCode(), exchangeDetailPO.getProductNum(), orderProductPO.getFinanceRuleCode(),
                orderProductPO.getBatchNo(), orderProductPO.getPurchaseSubCode(),
                orderProductPO.getChannelSkuId(), orderExtendPO.getBranch(), orderExtendPO.getReceiveBranchCode(), orderExtendPO.getWarehouseCode(),
                EventStockTypeEnum.ORDER_EXCHANGE_REVOKE_OUT_STOCK, BizTypeEnum.EXCHANGE_ORDER_REDUCE_STOCK, userDTO.getUserName(),
                orderPO, orderProductPO.getChannelSkuUnit(),null,isCombination);
    }


    @Override
    public void exchangeOrderAutoAuditJob() throws Exception {
        log.info("exchangeOrderAutoAuditJob==============start");
        List<OrderExchangePO> orderExchangePOList = orderExchangeMapper.getExchangeOrderAutoAuditData();
        if (CollectionUtils.isEmpty(orderExchangePOList)) {
            log.info("=======没有无需用户审批的换货订单==========");
            return;
        }
        UserDTO userDTO = new UserDTO();
        userDTO.setUserRole(OrderConst.LOG_ROLE);
        userDTO.setUserId(OrderConst.LOG_USER_ID);
        userDTO.setUserName(OrderConst.LOG_USER_NAME);
        for (OrderExchangePO x : orderExchangePOList) {
            OrderExchangeAuditDTO orderExchangeAuditDTO = new OrderExchangeAuditDTO();
            orderExchangeAuditDTO.setExchangeSn(x.getExchangeSn());
            orderExchangeAuditDTO.setExchangeAuditState(ExchangeOrderStatusEnum.AGREE_EXCHANGE.getValue());
            orderExchangeAuditDTO.setChannel(OrderCreateChannel.WEB.getValue());
            orderExchangeService.exchangeOrderAudit(orderExchangeAuditDTO, userDTO);
        }
        log.info("exchangeOrderAutoAuditJob==============end");
    }


    /**
     * 处理换货后 订单的完成逻辑
     * 1、如果原订单有效数量为0，将原订单状态置位 交易成功（原订单的售后单审批通过时，判断有效数量为0时，不将原订单状态置位 交易关闭 ）
     */
    @Override
    @Transactional
    public void dealExchangeOrderFinish(String orderSn) {
        OrderPO orderPO = orderModel.getOrderByOrderSn(orderSn);
        BizAssertUtil.isTrue(orderPO == null, "dealExchangeOrderFinish，查询不到" + orderSn + "的订单信息");
        if (ExchangeOrderConst.ORDER_EXCHANGE_FLAG_2 != orderPO.getExchangeFlag()) {
            return;
        }

        OrderExchangeDetailExample orderExchangeDetailExample = new OrderExchangeDetailExample();
        orderExchangeDetailExample.setExchangeOrderSn(orderPO.getOrderSn());
        //查询换货前的订单（换后的订单只能是一个订单所以直接用换货的订单号查询的）
        OrderExchangeDetailPO orderExchangeDetailPO = orderExchangeDetailService.getExchangeOrderDetailByExample(orderExchangeDetailExample);

        //更新换货单状态
        LambdaUpdateWrapper<OrderExchangePO> exchangePOLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        exchangePOLambdaUpdateWrapper.eq(OrderExchangePO::getExchangeSn, orderExchangeDetailPO.getExchangeSn())
                .set(OrderExchangePO::getExchangeOrderState, ExchangeOrderStatusEnum.EXCHANGE_FINISH.getValue())
                .set(OrderExchangePO::getUpdateTime, new Date())
                .set(OrderExchangePO::getUpdateBy, OrderConst.USER_NAME_SYSTEM);
        this.update(exchangePOLambdaUpdateWrapper);


        //记录换货操作轨迹
        orderLogModel.insertOrderLog(OrderConst.LOG_ROLE, OrderConst.LOG_USER_ID, OrderConst.LOG_USER_NAME,
                orderExchangeDetailPO.getExchangeSn(), orderPO.getOrderState(), orderPO.getOrderState(),
                LoanStatusEnum.APPLY_SUCCESS.getValue(), "换货完成", OrderCreateChannel.WEB);

        //记录换货操作轨迹
        orderLogModel.insertOrderLog(OrderConst.LOG_ROLE, OrderConst.LOG_USER_ID, OrderConst.LOG_USER_NAME,
                orderExchangeDetailPO.getOrderSn(), orderPO.getOrderState(), orderPO.getOrderState(),
                LoanStatusEnum.APPLY_SUCCESS.getValue(), "换货完成", OrderCreateChannel.WEB);
    }


    /**
     * 更新换货申请单的状态为已完成
     */
    @Override
    public void updateExchangeApplyStatus(String afsSn) {
        OrderReturnPO orderReturnPO = orderReturnModel.getOrderReturnByAfsSn(afsSn);
        BizAssertUtil.isTrue(orderReturnPO == null, afsSn + "售后单不存在");
        if (OrdersAfsConst.RETURN_TYPE_3 != orderReturnPO.getReturnType()) {
            return;
        }

        OrderExchangeDetailExample orderExchangeDetailExample = new OrderExchangeDetailExample();
        orderExchangeDetailExample.setAfsSn(afsSn);
        OrderExchangeDetailPO orderExchangeDetailPO = orderExchangeDetailService.getExchangeOrderDetailByExample(orderExchangeDetailExample);
        if (orderExchangeDetailPO == null) {
            log.error("updateExchangeApplyStatus，查询不到" + afsSn + "售后单的换货申请单");
            return;
        }

        LambdaUpdateWrapper<OrderExchangePO> orderExchangePOLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        orderExchangePOLambdaUpdateWrapper.eq(OrderExchangePO::getExchangeSn, orderExchangeDetailPO.getExchangeSn())
                .set(OrderExchangePO::getExchangeOrderState, ExchangeOrderStatusEnum.EXCHANGE_FINISH.getValue());
        this.update(orderExchangePOLambdaUpdateWrapper);
    }

    /**
     * 获取换货列表
     * 未审批的放前面，再按创建时间降序
     * 未审批的换货单展示原订单信息(审批通过的，一定会产生售后单号)
     * 已审批的换货单展示换货后的订单信息
     */
    @Override
    public List<OrderExchangeReturnListVO> getOrderExchangeReturnList(ExchangeApplyListReq exchangeApplyListReq, PagerInfo pager) {
        // 处理参数
        ExchangeOrderDetailDTO exchangeOrderDetailDTO = new ExchangeOrderDetailDTO();
        BeanUtils.copyProperties(exchangeApplyListReq, exchangeOrderDetailDTO);
        if (exchangeApplyListReq.isAgric()) {
            exchangeOrderDetailDTO.setMemberIdList(exchangeApplyListReq.getMemberIdList());
        } else {
            UserDTO userDTO = exchangeApplyListReq.getUserDTO();
            BizAssertUtil.isTrue(userDTO == null, "请先登录");
            if (OrderConst.LOG_ROLE_MEMBER == userDTO.getUserRole()) {
                exchangeOrderDetailDTO.setMemberId(userDTO.getUserId().intValue());
            } else if (OrderConst.LOG_ROLE_VENDOR == userDTO.getUserRole()) {
                exchangeOrderDetailDTO.setStoreId(userDTO.getStoreId());
            }
        }
        List<OrderExchangeReturnListVO> returnListVOList = new ArrayList<>();
        List<ExchangeOrderDetailDTO> orderDetailDTOList = new ArrayList<>();
        if (pager != null) {
            orderDetailDTOList = orderExchangeMapper.getExchangeOrderListByPage(exchangeOrderDetailDTO, pager.getStart(), pager.getPageSize());
            pager.setRowsCount(orderExchangeMapper.getExchangeOrderListCount(exchangeOrderDetailDTO));
        }
        if (CollectionUtils.isEmpty(orderDetailDTOList)) {
            return returnListVOList;
        }
        orderDetailDTOList.stream().forEach(x -> {
            OrderExchangeReturnListVO orderExchangeReturnListVO = new OrderExchangeReturnListVO(x);
            OrderProductPO oldOrderProductPO = orderProductModel.getOrderProductById(x.getOrderProductId());
            BigDecimal orderAmount = oldOrderProductPO.getProductShowPrice().multiply(new BigDecimal(x.getProductNum()));
            orderExchangeReturnListVO.setOrderAmount(orderAmount);
            //换货后的售后单号如果为空，则展示原订单信息；审批通过的换货单一定会产生售后单
            if (StringUtils.isEmpty(x.getAfsSn())) {
                OrderPO oldOrderPO = orderModel.getOrderByOrderSn(oldOrderProductPO.getOrderSn());
                //orderExchangeReturnListVO.setProductImage(FileUrlUtil.getFileUrl(oldOrderProductPO.getProductImage(), null));
                orderExchangeReturnListVO.setProductImage(oldOrderProductPO.getProductImage());
                orderExchangeReturnListVO.setSpecValues(oldOrderProductPO.getSpecValues());
                orderExchangeReturnListVO.setGoodsName(oldOrderProductPO.getGoodsName());
                orderExchangeReturnListVO.setOrderState(oldOrderPO.getOrderState());
                orderExchangeReturnListVO.setUserNo(oldOrderPO.getUserNo());
                orderExchangeReturnListVO.setUserMobile(oldOrderPO.getUserMobile());
                orderExchangeReturnListVO.setMemberName(oldOrderPO.getMemberName());
            } else {
                log.info("x.getExchangeOrderSn() == {}", x.getExchangeOrderSn());
                OrderPO newOrderPO = orderModel.getOrderByOrderSn(x.getExchangeOrderSn());
                OrderProductPO newOrderProductPO = orderProductModel.getOrderProductById(x.getExchangeOrderProductId());
                //orderExchangeReturnListVO.setProductImage(FileUrlUtil.getFileUrl(newOrderProductPO.getProductImage(), null));
                orderExchangeReturnListVO.setProductImage(newOrderProductPO.getProductImage());
                orderExchangeReturnListVO.setSpecValues(newOrderProductPO.getSpecValues());
                orderExchangeReturnListVO.setOrderState(newOrderPO.getOrderState());
                orderExchangeReturnListVO.setGoodsName(newOrderProductPO.getGoodsName());
                orderExchangeReturnListVO.setOrderAmount(newOrderProductPO.getProductShowPrice().multiply(new BigDecimal(newOrderProductPO.getProductNum())));
                orderExchangeReturnListVO.setProductNum(newOrderProductPO.getProductNum());
                orderExchangeReturnListVO.setUserNo(newOrderPO.getUserNo());
                orderExchangeReturnListVO.setUserMobile(newOrderPO.getUserMobile());
                orderExchangeReturnListVO.setMemberName(newOrderPO.getMemberName());
            }
            returnListVOList.add(orderExchangeReturnListVO);
        });
        // 根据用户编码批量获取用户信息
        List<String> userNoList = returnListVOList.stream().map(OrderExchangeReturnListVO::getUserNo).collect(Collectors.toList());

        Map<String, UserInfoVo> userInfoVoMap = customerIntegration.queryUserInfoByUserNoList(userNoList, false);
        returnListVOList.forEach(x -> {
            UserInfoVo userInfoVo = userInfoVoMap.getOrDefault(x.getUserNo(), null);
            if (Objects.nonNull(userInfoVo)) {
                if (Objects.nonNull(userInfoVo.getCustInfoVo())) {
                    x.setMemberName(userInfoVo.getCustInfoVo().getCustName());
                } else if (!org.springframework.util.StringUtils.isEmpty(userInfoVo.getUserName())) {
                    x.setMemberName(userInfoVo.getUserName());
                }
            }
        });
        return returnListVOList;
    }

    /**
     * 获取换货申请列表（PC端）
     * 未审批的放前面，再按创建时间降序
     * 未审批的换货单展示原订单信息(审批通过的，一定会产生售后单号)
     * 已审批的换货单展示换货后的订单信息
     */
    @Override
    public List<OrderExchangeReturnListVO> getExchangeApplyList(ExchangeApplyListPcReq exchangeApplyListReq, PagerInfo pager) {
        UserDTO userDTO = exchangeApplyListReq.getUserDTO();
        BizAssertUtil.isTrue(userDTO == null, "请先登录");

        if (exchangeApplyListReq.getExchangeOrderState() != null) {
            if (ExchangeOrderConst.EXCHANGE_ORDER_STATE_AGREE_WAIT_DELIVERY == exchangeApplyListReq.getExchangeOrderState()) {
                exchangeApplyListReq.setOrderState(OrderConst.ORDER_STATE_20);
            } else if (ExchangeOrderConst.EXCHANGE_ORDER_STATE_AGREE_WAIT_RECEIVE == exchangeApplyListReq.getExchangeOrderState()) {
                exchangeApplyListReq.setOrderState(OrderConst.ORDER_STATE_30);
            }
        }
        if (OrderConst.LOG_ROLE_MEMBER == userDTO.getUserRole()) {
            exchangeApplyListReq.setCustomerId(userDTO.getUserId().toString());
        } else if (OrderConst.LOG_ROLE_VENDOR == userDTO.getUserRole()) {
            exchangeApplyListReq.setStoreId(userDTO.getStoreId());
        }
        List<OrderExchangeReturnListVO> returnListVOList = new ArrayList<>();
        if (pager != null) {
            returnListVOList = orderExchangeMapper.getExchangeApplyListPageNew(exchangeApplyListReq, pager.getStart(), pager.getPageSize());
            pager.setRowsCount(orderExchangeMapper.getExchangeApplyListCountNew(exchangeApplyListReq));
        }

        /*returnListVOList.stream().forEach(x -> {
            x.setProductImage(FileUrlUtil.getFileUrl(x.getProductImage(), null));
        });*/
        return returnListVOList;
    }

    /**
     * 换货订单，用户超时未审批的，直接取消订单
     */
    @Override
    public void exchangeOrderAutoCancelJob() throws Exception {
        log.info("exchangeOrderAutoCancelJob==============start");
        //用户审批时间,当前时间减用户审批时间
        String value = stringRedisTemplate.opsForValue().get("exchange_order_auto_audit_time");
        int limitMinute = value == null ? 7 * 24 * 60 : Integer.parseInt(value);
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, -limitMinute);
        Date cancelTime = calendar.getTime();

        String timeout = DateUtil.format(cancelTime, DateUtil.FORMAT_TIME);
        List<OrderExchangePO> orderExchangePOList = orderExchangeMapper.exchangeOrderAutoCancelData(timeout);
        if (CollectionUtils.isEmpty(orderExchangePOList)) {
            log.info("=======没有无需用户审批的换货订单==========");
            return;
        }
        UserDTO userDTO = new UserDTO();
        userDTO.setUserRole(OrderConst.LOG_ROLE);
        userDTO.setUserId(OrderConst.LOG_USER_ID);
        userDTO.setUserName(OrderConst.LOG_USER_NAME);
        for (OrderExchangePO x : orderExchangePOList) {
            OrderExchangeAuditDTO orderExchangeAuditDTO = new OrderExchangeAuditDTO();
            orderExchangeAuditDTO.setExchangeSn(x.getExchangeSn());
            orderExchangeAuditDTO.setExchangeAuditState(ExchangeOrderStatusEnum.EXCHANGE_CANCEL.getValue());
            orderExchangeAuditDTO.setChannel(OrderCreateChannel.WEB.getValue());
            orderExchangeService.exchangeOrderAudit(orderExchangeAuditDTO, userDTO);
        }
        log.info("exchangeOrderAutoCancelJob==============end");
    }
}
