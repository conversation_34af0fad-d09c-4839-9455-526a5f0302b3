package com.cfpamf.ms.mallorder.validation;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.mallorder.common.constant.OrdersAfsConst;
import com.cfpamf.ms.mallorder.common.enums.*;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.common.util.OrderLocalUtils;
import com.cfpamf.ms.mallorder.constant.ExchangeOrderConst;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.enums.OrderAmountStateEnum;
import com.cfpamf.ms.mallorder.enums.OrderAmountTypeEnum;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.integration.promotion.MallCouponPkgIntegration;
import com.cfpamf.ms.mallorder.mapper.OrderReturnMapper;
import com.cfpamf.ms.mallorder.model.OrderReturnModel;
import com.cfpamf.ms.mallorder.po.OrderAfterPO;
import com.cfpamf.ms.mallorder.po.OrderProductPO;
import com.cfpamf.ms.mallorder.po.OrderRefundRecordPO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderReturnPO;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.v2.service.OrderRefundRecordService;
import com.cfpamf.ms.mallpromotion.vo.CouponPkgRefundResultVo;
import com.cfpamf.ms.mallshop.api.StoreFeignClient;
import com.cfpamf.ms.mallshop.api.StoreIsolateWhiteListFeignClient;
import com.cfpamf.ms.mallshop.enums.StoreStateEnum;
import com.cfpamf.ms.mallshop.enums.WhiteListEnum;
import com.cfpamf.ms.mallorder.vo.OrderAmountStateRecordVO;
import com.cfpamf.ms.mallshop.resp.Store;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 退款类的校验
 */
@Slf4j
@Component
public class OrderReturnValidation {


    @Resource
    private OrderReturnMapper orderReturnMapper;
    @Autowired
    private IOrderAmountStateRecordService orderAmountRecordService;
    @Resource
    private IOrderProductService orderProductService;
    @Resource
    private IOrderAfterService orderAfterService;
    @Resource
    private IOrderReturnService orderReturnService;
    @Resource
    private OrderRefundRecordService orderRefundRecordService;
    @Resource
    private IPerformanceService performanceService;

    @Resource
    private OrderReturnModel orderReturnModel;

    @Resource
    private StoreIsolateWhiteListFeignClient storeIsolateWhiteListFeignClient;
    @Resource
    private StoreFeignClient storeFeignClient;
    @Resource
    private MallCouponPkgIntegration mallCouponPkgIntegration;


    public static String validRevoke(ReturnByEnum returnByEnum, Integer returnType, Integer state){
        if (null == returnByEnum || !returnByEnum.isAllowRevoke()){
            return "该订单暂不支持撤销售后～";
        }
        if (!validSupportRevoke(returnType,state)){
            return "退款单当前状态不可撤销";
        }
        return null;
    }

    /**
     * 校验是否支持退款撤销
     *
     * @param returnType    退款单类型
     * @param state         退款单状态
     * @return              true/false
     */
    public static boolean validSupportRevoke(Integer returnType, Integer state) {
        if (returnType.equals(ReturnTypeEnum.REFUND.getValue())) {
            // 仅退款，待平台审核前
            return OrderReturnStatus.REFUND_APPLY.getValue().equals(state)
                    || OrderReturnStatus.STORE_AGREE_REFUND.getValue().equals(state);
        } else if (returnType.equals(ReturnTypeEnum.RETURN_AND_REFUND.getValue())) {
            // 退货退款，待用户发货前
            return OrderReturnStatus.RETURN_APPLY.getValue().equals(state)
                    || OrderReturnStatus.STORE_AGREE_RETURN.getValue().equals(state)
                    || OrderReturnStatus.SELF_PICKUP_POINT_RETURN_APPLY.getValue().equals(state);
        } /*else if (returnType.equals(ReturnTypeEnum.EXCHANGE_AND_REFUND.getValue())) {
            return Boolean.FALSE;
        }*/
        return true;
    }

    /**
     * 售后单是否支持重新申请
     *
     * @param state             售后单状态
     * @param productReturnNum  对应商品已退数量
     * @return                  true/false
     */
    public static Boolean validSupportReApply(Integer state, Integer productReturnNum) {
        if (Objects.isNull(state) || Objects.isNull(productReturnNum)) {
            return Boolean.FALSE;
        }
        return OrderReturnStatus.supportReapplyStatus(state) && productReturnNum == 0;
    }

    /**
     * 试算前校验，避免贷款类重复还款
     *
     * @param orderReturnPo     退款对象
     */
    public void refundTryCalculatePreCheck(OrderReturnPO orderReturnPo) {
        // 退款审批通过时，代还退款类退款需要避免重复发起
        log.info("refundTryCalculatePreCheck, afsSn:{}, refundType:{}",
                orderReturnPo.getAfsSn(), orderReturnPo.getRefundType());
        if (RefundType.ASSIST_PAYMENT.getValue().equals(orderReturnPo.getRefundType())) {
            LambdaQueryWrapper<OrderReturnPO> refundTypeCheckQueryWrapper = Wrappers.lambdaQuery();
            refundTypeCheckQueryWrapper.select(OrderReturnPO::getReturnId)
                    .eq(OrderReturnPO::getOrderSn, orderReturnPo.getOrderSn())
                    .eq(OrderReturnPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
            if (OrderReturnStatus.STORE_AGREE_REFUND.getValue().equals(orderReturnPo.getState())
                    || OrderReturnStatus.STORE_RECEIVED.getValue().equals(orderReturnPo.getState())) {
                refundTypeCheckQueryWrapper.in(OrderReturnPO::getState, Collections.singletonList(OrderReturnStatus.PLATFORM_AGREE.getValue()));
                BizAssertUtil.isTrue(orderReturnMapper.selectCount(refundTypeCheckQueryWrapper) > 0,
                        String.format("该退款单【%s】所属订单【%s】存在其它处理中的退款单，请待其退款完成再审批",
                                orderReturnPo.getAfsSn(), orderReturnPo.getOrderSn()));
            }/* else if (OrderReturnStatus.REFUND_APPLY.getValue().equals(orderReturnPo.getState()) ||
                    OrderReturnStatus.RETURN_APPLY.getValue().equals(orderReturnPo.getState())) {
                refundTypeCheckQueryWrapper.in(OrderReturnPO::getState, Arrays.asList(OrderReturnStatus.PLATFORM_AGREE.getValue(),
                        OrderReturnStatus.STORE_AGREE_REFUND.getValue(), OrderReturnStatus.STORE_RECEIVED.getValue()));
                BizAssertUtil.isTrue(orderReturnMapper.selectCount(refundTypeCheckQueryWrapper) > 0,
                        String.format("该退款单【%s】所属订单【%s】存在其它处理中的退款单，请待其退款完成再审批",
                                orderReturnPo.getAfsSn(), orderReturnPo.getOrderSn()));
            }*/

        }
    }

    /**
     * 退款扣罚金额校验
     * 代还退款、组合退款（贷款类退款），才需要自动计算，允许手动修改，可有值，其它必须为 0
     *
     * @param orderReturnPo 退款单
     */
    public boolean refundPunishAmountSupport(OrderReturnPO orderReturnPo) {
        log.info("OrderReturnValidation refundPunishAmountCheck OrderReturnPO : {}", orderReturnPo.toString());

        // 只有白名单店铺才支持设置退款扣罚金额
        Boolean whiteList = storeIsolateWhiteListFeignClient.isStoreOnList(WhiteListEnum.REFUND_PUNISH_WHITE_LIST_TYPE, orderReturnPo.getStoreId());
        if (!whiteList) {
            return false;
        }

        // 只有代还退款可以有退款扣罚；组合退款存在贷款类退款才允许退款扣罚
        if (!RefundType.ASSIST_PAYMENT.getValue().equals(orderReturnPo.getRefundType())
                && !RefundType.COMBINATION_REFUND.getValue().equals(orderReturnPo.getRefundType())) {
            return false;
        }

        // 判断组合退款下是否存在贷款类退款
        if (RefundType.COMBINATION_REFUND.getValue().equals(orderReturnPo.getRefundType())) {
            Integer count = orderRefundRecordService.lambdaQuery()
                    .eq(OrderRefundRecordPO::getAfsSn, orderReturnPo.getAfsSn())
                    .eq(OrderRefundRecordPO::getRefundType, RefundType.ASSIST_PAYMENT.getValue())
                    .eq(OrderRefundRecordPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                    .count();
            return count > 0;
        }
        return true;
    }

    /**
     * 退款扣罚的金额校验：扣罚金额不能大于退款申请金额（商品退款金额+运费）,组合退款不能大于贷款类的退款金额
     *
     * @param orderReturnPo 退款单
     * @return              true/false
     */
    public boolean refundPunishAmountCheck(OrderReturnPO orderReturnPo, BigDecimal refundPunishAmount) {
        log.info("OrderReturnValidation refundPunishAmountCheck OrderReturnPO : {}", orderReturnPo.toString());

        // 非代还退款和组合退款，不允许设置，只能为0
        if (!RefundType.ASSIST_PAYMENT.getValue().equals(orderReturnPo.getRefundType())
                && !RefundType.COMBINATION_REFUND.getValue().equals(orderReturnPo.getRefundType())) {
            return BigDecimal.ZERO.compareTo(refundPunishAmount) == 0;
        }

        // 代还退款，不能大于退款申请金额（商品退款金额+运费）
        if (RefundType.ASSIST_PAYMENT.getValue().equals(orderReturnPo.getRefundType())) {
            return BigDecimal.ZERO.compareTo(refundPunishAmount) <= 0 &&
                    orderReturnPo.getRefundApplySumAmount().compareTo(refundPunishAmount) >= 0;
        }

        // 组合退款，不能大于贷款类的退款金额
        if (RefundType.COMBINATION_REFUND.getValue().equals(orderReturnPo.getRefundType())) {
            List<OrderRefundRecordPO> refundRecords = orderRefundRecordService.lambdaQuery()
                    .eq(OrderRefundRecordPO::getAfsSn, orderReturnPo.getAfsSn())
                    .eq(OrderRefundRecordPO::getRefundType, RefundType.ASSIST_PAYMENT.getValue())
                    .eq(OrderRefundRecordPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                    .list();
            if (CollectionUtils.isEmpty(refundRecords)) {
                return BigDecimal.ZERO.compareTo(refundPunishAmount) == 0;
            }
            BigDecimal assistPaymentSum = refundRecords.stream().map(OrderRefundRecordPO::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            return BigDecimal.ZERO.compareTo(refundPunishAmount) <= 0 && assistPaymentSum.compareTo(refundPunishAmount) >= 0;
        }

        return Boolean.FALSE;
    }

    /**
     * 是否需要计算退单佣金
     *
     * @param orderPO   订单信息
     * @return          true/false
     */
    public boolean needCalculateReturnOrderCommission(OrderPO orderPO) {
        // 正向佣金大于0，必须计算
        if (orderPO.getOrderCommission().compareTo(BigDecimal.ZERO) > 0) {
            return Boolean.TRUE;
        }
        // 预付订单只支付了定金（订单状态10和15），不会产生分销佣金，检查通过
        if (OrderTypeEnum.PRE_SELL_DEPOSIT.getValue() == orderPO.getOrderType()
                && (OrderStatusEnum.WAIT_PAY.getValue().equals(orderPO.getOrderState()) ||
                OrderStatusEnum.DEAL_PAY.getValue().equals(orderPO.getOrderState()))) {
            return Boolean.FALSE;
        }
        //换货后的商品产生佣金，检查通过
        if(ExchangeOrderConst.ORDER_EXCHANGE_FLAG_2 == orderPO.getExchangeFlag()){
            return Boolean.FALSE;
        }
        //OMS的订单,不产生佣金,检查通过
        if (StringUtils.equals(orderPO.getChannel(), OrderCreateChannel.OMS.getValue())) {
            return Boolean.FALSE;
        }
        //线下补录的订单,不产生佣金,检查通过
        if (OrderTypeEnum.isOfflineAll(orderPO.getOrderType())) {
            return Boolean.FALSE;
        }
        //赠品订单,不产生佣金,检查通过
        if (OrderTypeEnum.isGiftAll(orderPO.getOrderType())) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    public boolean checkOrderCommissionAmount(OrderPO orderPO){
        // 查询订单分销佣金
        Result<OrderAmountStateRecordVO> commissionResult = orderAmountRecordService.getOneByOrderSn(orderPO.getOrderSn(),
                OrderAmountTypeEnum.ORDER_COMMISSION);
        //未找到有效状态的分销佣金，检查不通过
        if (!commissionResult.isSuccess() || Objects.isNull(commissionResult.getData())) {
            return Boolean.FALSE;
        }
        //检查分销佣金状态的有效状态
        return OrderAmountStateEnum.EFFECT == commissionResult.getData().getRecordState();
    }

    /**
     * 判断该商品行是否支持再次退款
     *
     * @param orderProductId    商品行id
     * @return                  true支持 / false不支持
     */
    public Boolean refundProductCheck(Long orderProductId) {
        List<OrderAfterPO> orderAfterPOs = orderAfterService.lambdaQuery()
                .eq(OrderAfterPO::getOrderProductId, orderProductId)
                .eq(OrderAfterPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                .list();

        if (!CollectionUtils.isEmpty(orderAfterPOs)) {
            LambdaQueryWrapper<OrderReturnPO> orderReturnQuery = Wrappers.lambdaQuery();
            orderReturnQuery.in(OrderReturnPO::getState, OrderReturnStatus.duringRefundStatus())
                    .in(OrderReturnPO::getAfsSn, orderAfterPOs.stream().map(OrderAfterPO::getAfsSn).collect(Collectors.toSet()))
                    .eq(OrderReturnPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
            return orderReturnMapper.selectCount(orderReturnQuery) == 0;
        }

        return Boolean.TRUE;
    }

    public boolean validReturnNumGtDeliverNum(Long orderProductId,int returnNum) {
        List<OrderAfterPO> orderAfterPOs = orderAfterService.lambdaQuery()
                .eq(OrderAfterPO::getOrderProductId, orderProductId)
                .eq(OrderAfterPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                .list();

        if (!CollectionUtils.isEmpty(orderAfterPOs)) {
            OrderProductPO orderProductPO = orderProductService.selectOneByOrderProductId(orderProductId);
            int deliveredNum = orderProductPO.getDeliveryNum();

            LambdaQueryWrapper<OrderReturnPO> orderReturnQuery = Wrappers.lambdaQuery();
            orderReturnQuery.eq(OrderReturnPO::getReturnType, OrdersAfsConst.AFS_TYPE_RETURN)
                    .notIn(OrderReturnPO::getState, OrderReturnStatus.endStatus())
                    .in(OrderReturnPO::getAfsSn, orderAfterPOs.stream().map(OrderAfterPO::getAfsSn).collect(Collectors.toSet()))
                    .eq(OrderReturnPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
            List<OrderReturnPO> orderReturnPOList = orderReturnMapper.selectList(orderReturnQuery);
            if(CollectionUtils.isEmpty(orderReturnPOList)) {
                return returnNum > deliveredNum;
            }
            int returnedNum = orderReturnPOList.stream().map(OrderReturnPO::getReturnNum).reduce(Integer::sum).get();

            return  returnedNum + returnedNum > deliveredNum;
        }
        return Boolean.FALSE;
    }

    /**
     * 判断售后申请是否是全退
     *
     * @param orderSn                   订单号
     * @param afsOrderProductInfos      售后申请信息
     * @return                          true/false
     */
    public boolean isAllProductsRefundApply(String orderSn, List<String> afsOrderProductInfos) {
        List<String[]> afsInfo = afsOrderProductInfos.stream().map(i -> i.split("-")).collect(Collectors.toList());
        // 商品id和售后申请数量的关系
        Map<String, String> product2AfsNum = afsInfo.stream().collect(Collectors.toMap(i -> i[0], i -> i[1]));

        List<OrderProductPO> orderProducts = orderProductService.listByOrderSn(orderSn);
        for (OrderProductPO orderProduct : orderProducts) {
            String productNum = product2AfsNum.get(String.valueOf(orderProduct.getOrderProductId()));
            if (Objects.isNull(productNum) || !orderProduct.getProductNum().equals(Integer.parseInt(productNum))) {
                return false;
            }
        }

        return true;
    }

    /**
     * 店铺是否是内部退款商家
     *
     * @param storeId   店铺id
     * @return          true/false
     */
    public boolean isInnerRefundStore(Long storeId) {
        try {
            return storeIsolateWhiteListFeignClient.isStoreOnList(WhiteListEnum.INNER_REFUND, storeId);
        } catch (Exception e) {
            log.warn("OrderReturnValidation isInnerRefundStore feign error, return false, please check it");
            return Boolean.FALSE;
        }
    }

    /**
     * 售后单店铺审核状态审核时信息校验
     *
     * @param role          处理人角色
     * @param auditId       审核人员的id
     * @param billStoreId   售后单所属店铺的id
     * @param distribution  是否配销单子：0-否 1-是
     */
    public void storeAuditStateAuditValidate(OperationRoleEnum role, Long auditId, Long billStoreId, Integer distribution) {
        if (OperationRoleEnum.ACCOUNT_MANAGER != role) {
            OrderLocalUtils.checkOrderPermissions(auditId, billStoreId, distribution);
        } else {
            BizAssertUtil.isTrue(!this.isInnerRefundStore(billStoreId), "不可审批非内部退款白名单商家");
        }
    }

    /**
     * 退款单是否支持下账
     *
     * @param orderPO           订单信息
     * @param orderReturnPO     退款单信息
     * @return                  true/false
     */
    public Boolean isSupportBookkeeping(OrderPO orderPO, OrderReturnPO orderReturnPO) {
        if (orderPO.getNewOrder() && (RefundType.ASSIST_PAYMENT.getValue().equals(orderReturnPO.getRefundType())
                || RefundType.OFFLINE_REFUND.getValue().equals(orderReturnPO.getRefundType())
                || RefundType.SQUARE_OFFLINE_REFUND.getValue().equals(orderReturnPO.getRefundType())
                || RefundType.TRANSFER_REFUND.getValue().equals(orderReturnPO.getRefundType()))
                || RefundType.APLIPAY_REFUND.getValue().equals(orderReturnPO.getRefundType())) {
            return Boolean.TRUE;
        }

        // 微信云直通和江苏银行渠道都需要下账处理
        if (RefundType.WXPAY_REFUND.getValue().equals(orderReturnPO.getRefundType())
                && (PayChannelEnum.YZT_WX.getValue().equals(orderPO.getPayChannel())
                || PayChannelEnum.JS_WX.getValue().equals(orderPO.getPayChannel()))) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;

    }

    /**
     * 判断该笔退款单是否是该商品行的最后一笔退单，即该退款单完成后，该商品行商品全退完成
     *
     * @return  true/false
     */
    public boolean isProductLastRefund(OrderAfterPO orderAfterPO) {
        return Objects.equals(NumberUtils.INTEGER_ONE, orderAfterPO.getProductLastRefund());
    }

    /**
     * 判断该笔退款单是否是该订单的最后一笔退单，即该退款单完成后，该订单商品全退完成
     *
     * @return  true/false
     */
    public boolean isOrderLastRefund(OrderAfterPO orderAfterPO) {
        return Objects.equals(NumberUtils.INTEGER_ONE, orderAfterPO.getOrderLastRefund());
    }


    /**
     * 判断下账的时候是否要执行退佣金激励费的action
     * 存在【订单佣金激励费】则需要进行处理。 且只有 EFFECT 状态才能执行，其他状态需要等候
     *
     * @param orderReturnPO     售后单
     * @return                  true/false
     */
    public boolean isNeedCommissionIncentiveWhenBookkeeping(OrderPO orderPO, OrderReturnPO orderReturnPO) {
        if (OrderTypeEnum.ORDER_TYPE_6.getValue().equals(orderPO.getOrderType())) {
            return false;
        }
        Result<OrderAmountStateRecordVO> orderAmountResult = orderAmountRecordService.getOneByOrderSn(orderReturnPO.getOrderSn(),
                OrderAmountTypeEnum.ORDER_COMMISSION_INCENTIVE_FEE);
        if (Objects.isNull(orderAmountResult.getData())) {
            return Boolean.FALSE;
        }
        if (!OrderAmountStateEnum.isEffect(orderAmountResult.getData().getRecordState().getName())) {
            throw new BusinessException("售后处理中，请稍后");
        }
        return Boolean.TRUE;
    }


    /**
     * 平台审批时信息校验
     */
    public void adminConfirmRefundValidate(OrderReturnPO orderReturnPO, boolean isPassed) {
        // 售后单状态校验
        if (!OrderReturnStatus.STORE_AGREE_REFUND.getValue().equals(orderReturnPO.getState())
                && !OrderReturnStatus.STORE_RECEIVED.getValue().equals(orderReturnPO.getState())
                    && !OrderReturnStatus.STORE_AGREE_RETURN.getValue().equals(orderReturnPO.getState())) {
            throw new BusinessException(String.format("售后单%s状态异常,不可平台审批,请确认", orderReturnPO.getAfsSn()));
        }

        //出库中商品不允许售后
        BizAssertUtil.isTrue(performanceService.outbounding(orderReturnPO.getOrderSn()), "商品出库中，禁止退款，请稍后在试");

        // 1、平台审核通过时校验的内容
        if (isPassed) {
            // 1.1、退款类型为恢复额度时校验的内容
            if (RefundType.RESTORE_LIMIT.getValue().equals(orderReturnPO.getRefundType())) {
                List<OrderReturnPO> orderReturnPOs = orderReturnService.lambdaQuery()
                        .eq(OrderReturnPO::getOrderSn, orderReturnPO.getOrderSn())
                        .in(OrderReturnPO::getState, OrderReturnStatus.duringRefundStatus())
                        .eq(OrderReturnPO::getEnabledFlag, CommonEnum.YES.getCode())
                        .list();
                // 1.1.1、恢复额度且是退货退款，审批通过时，对应的售后单状态都必须为商家确认收货
                if (ReturnTypeEnum.RETURN_AND_REFUND.getValue().equals(orderReturnPO.getReturnType())) {
                    for (OrderReturnPO orderReturnPOTmp : orderReturnPOs) {
                        if (!OrderReturnStatus.STORE_RECEIVED.getValue().equals(orderReturnPOTmp.getState())) {
                            throw new BusinessException(String.format("订单下售后单%s未处于商家收货状态,不可平台审批", orderReturnPOTmp.getAfsSn()));
                        }
                    }
                }
            }
        } else {
            // 2、平台审核拒绝时校验的内容
            // 2.1、退款失败过的订单不支持拒绝
            BizAssertUtil.isTrue(orderReturnPO.getRefundFailTimes() > NumberUtils.INTEGER_ZERO, "退款失败过的订单不能拒绝处理");
        }
    }

    /**
     * 售后失败回调校验
     */
    public void refundFailValidate(OrderReturnPO orderReturnPO) {
        AssertUtil.isTrue(orderReturnPO.getState().equals(OrderReturnStatus.REFUND_SUCCESS.getValue()), "已退款成功");
        AssertUtil.isTrue(orderReturnPO.getState().equals(OrderReturnStatus.REFUND_FAILED.getValue()), "已退款失败");
        AssertUtil.isTrue(!orderReturnPO.getState().equals(OrderReturnStatus.PLATFORM_AGREE.getValue()), "售后单状态非平台审批通过");
    }


    /**
     * 判断是否支持强制退款
     */
    public String orderForceRefundValidate(OrderPO orderPO) {
        if (Objects.isNull(orderPO)) {
            return "查询订单为空";
        }
        if (OrderStatusEnum.WAIT_PAY.isTrue(orderPO.getOrderState())
                || OrderStatusEnum.TRADE_CLOSE.isTrue(orderPO.getOrderState())
                || OrderStatusEnum.CANCELED.isTrue(orderPO.getOrderState())) {
            return "待付款、交易关闭、已关闭订单不允许强制退款";
        }
        if (orderReturnModel.whetherHasReturningProduct(orderPO.getOrderSn())) {
            return "未结束的售后单的订单不允许强制退款";
        }
        // 卡券订单，所属店铺在【卡券订单强制退款白名单】内，且【通过营销的是否可退款校验】的才允许强制退款
        if (OrderPatternEnum.COUPON_CENTRE.getValue().equals(orderPO.getOrderPattern())) {
            // 校验是否在【卡券订单强制退款白名单】内
            if (!storeIsolateWhiteListFeignClient.isStoreOnList(WhiteListEnum.CARD_ITEM_REFUND_WHITE_LIST, orderPO.getStoreId())) {
                return "抱歉，卡券订单不允许进行退款";
            }
            // 营销侧校验卡券订单
            CouponPkgRefundResultVo checkResult = mallCouponPkgIntegration.couponCentreOrderRefundable(orderPO.getOrderSn());
            if (!checkResult.getResult()) {
                return checkResult.getMessage();
            }
        }
        // 退店的店铺不支持强制退款
        Store store = storeFeignClient.getStoreByStoreId(orderPO.getStoreId());
        if (StoreStateEnum.LOGOUT.getCode().equals(store.getState()) || StoreStateEnum.LOGOUTING.getCode().equals(store.getState())) {
            return "店铺已注销，不支持强制退款";
        }
        return OrderConst.CHECK_SUCCESS;
    }

}
