package com.cfpamf.ms.mallorder.dto;


import com.cfpamf.ms.mallorder.po.OrderProductPO;
import com.slodon.bbc.core.util.FileUrlUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExchangeOrderDTO {

    @ApiModelProperty("订单号")
    private String orderSn;

    @ApiModelProperty("货品ID")
    private Long orderProductId;

    @ApiModelProperty("商品Id")
    private Long productId;

    @ApiModelProperty("商品名称")
    private String goodsName;

    @ApiModelProperty("商品图片")
    private String productImage;

    @ApiModelProperty("商品编号")
    private Long goodsId;

    @ApiModelProperty("商品规格")
    private String specValues;

    @ApiModelProperty("商品单价")
    private BigDecimal productShowPrice;

    @ApiModelProperty("商品数量（如果是被换商品，即被换商品的数量；如果是换后的商品，即换后商品的数量;如果是原订单，及原订单该商品的总数）")
    private Integer productNum;

    @ApiModelProperty("应付金额")
    private BigDecimal goodsAmountTotal;

    @ApiModelProperty("实付金额=货品单价*数量 -活动优惠总金额 -乡助卡抵扣金额")
    private BigDecimal moneyAmount;

    @ApiModelProperty("乡助卡金额")
    private BigDecimal xzCardAmount;

    @ApiModelProperty("店铺活动优惠金额")
    private BigDecimal storeActivityAmount;

    @ApiModelProperty("平台活动优惠金额")
    private BigDecimal platformActivityAmount;

    @ApiModelProperty("店铺优惠券优惠金额")
    private BigDecimal storeVoucherAmount;

    @ApiModelProperty("平台优惠券优惠金额")
    private BigDecimal platformVoucherAmount;

    @ApiModelProperty("供应商名称")
    private String supplierName;

    @ApiModelProperty("原商品对应的售后单号")
    private String afsSn;



    //生成被换的或者原订单商品的信息
    public ExchangeOrderDTO(OrderProductPO orderProductPO) {
        orderSn = orderProductPO.getOrderSn();
        orderProductId = orderProductPO.getOrderProductId();
        goodsAmountTotal = orderProductPO.getGoodsAmountTotal();
        productShowPrice = orderProductPO.getProductShowPrice();
        moneyAmount = orderProductPO.getMoneyAmount();
        xzCardAmount = orderProductPO.getXzCardAmount();
        storeActivityAmount = orderProductPO.getStoreActivityAmount();
        platformActivityAmount = orderProductPO.getPlatformActivityAmount();
        storeVoucherAmount = orderProductPO.getStoreVoucherAmount();
        platformVoucherAmount = orderProductPO.getPlatformVoucherAmount();
        productId = orderProductPO.getProductId();
        goodsName = orderProductPO.getGoodsName();
        goodsId = orderProductPO.getGoodsId();
        specValues = orderProductPO.getSpecValues();
        productImage = FileUrlUtil.getFileUrl(orderProductPO.getProductImage(), null);
        productNum = orderProductPO.getProductNum();
        supplierName = orderProductPO.getSupplierName();
    }

    //生成被换的商品信息
    public ExchangeOrderDTO(OrderProductPO orderProductPO,int productNum) {
        orderSn = orderProductPO.getOrderSn();
        orderProductId = orderProductPO.getOrderProductId();
        goodsAmountTotal = orderProductPO.getProductShowPrice().multiply(new BigDecimal(productNum));
        productShowPrice = orderProductPO.getProductShowPrice();
        //（换货数量/原商品购买数量）* 实付金额，这里查询涉及金额的都未轧差；注：先乘再除
        BigDecimal newProductNum = new BigDecimal(productNum);
        BigDecimal originalProductNum = new BigDecimal(orderProductPO.getProductNum());

        moneyAmount = orderProductPO.getMoneyAmount().multiply(newProductNum).divide(originalProductNum,2, RoundingMode.HALF_UP);
        xzCardAmount = orderProductPO.getXzCardAmount().multiply(newProductNum).divide(originalProductNum,2, RoundingMode.HALF_UP);
        storeActivityAmount = orderProductPO.getStoreActivityAmount().multiply(newProductNum).divide(originalProductNum,2, RoundingMode.HALF_UP);
        platformActivityAmount = orderProductPO.getPlatformActivityAmount().multiply(newProductNum).divide(originalProductNum,2, RoundingMode.HALF_UP);
        storeVoucherAmount = orderProductPO.getStoreVoucherAmount().multiply(newProductNum).divide(originalProductNum,2, RoundingMode.HALF_UP);
        platformVoucherAmount = orderProductPO.getPlatformVoucherAmount().multiply(newProductNum).divide(originalProductNum,2, RoundingMode.HALF_UP);

        productId = orderProductPO.getProductId();
        goodsName = orderProductPO.getGoodsName();
        goodsId = orderProductPO.getGoodsId();
        specValues = orderProductPO.getSpecValues();
        productImage = FileUrlUtil.getFileUrl(orderProductPO.getProductImage(), null);
        supplierName = orderProductPO.getSupplierName();
        this.productNum = productNum;
    }
}
