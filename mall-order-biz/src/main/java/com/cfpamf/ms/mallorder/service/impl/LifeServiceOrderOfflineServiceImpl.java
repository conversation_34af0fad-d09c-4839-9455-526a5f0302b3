package com.cfpamf.ms.mallorder.service.impl;

import com.cfpamf.ms.mallorder.mapper.LifeServiceOrderOfflineMapper;
import com.cfpamf.ms.mallorder.po.LifeServiceOrderOfflinePO;
import com.cfpamf.ms.mallorder.service.ILifeServiceOrderOfflineService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class LifeServiceOrderOfflineServiceImpl implements ILifeServiceOrderOfflineService {
    @Autowired
    private LifeServiceOrderOfflineMapper lifeServiceOrderOfflineMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSave(List<LifeServiceOrderOfflinePO> list) {
        if (list != null && !list.isEmpty()) {
            for (LifeServiceOrderOfflinePO po : list) {
                lifeServiceOrderOfflineMapper.insert(po);
            }
        }
    }
} 