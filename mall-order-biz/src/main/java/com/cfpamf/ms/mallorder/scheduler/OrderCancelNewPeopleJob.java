package com.cfpamf.ms.mallorder.scheduler;

import java.time.LocalDateTime;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.mapper.OrderMapper;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.request.OrderExample;
import com.cfpamf.ms.mallorder.v2.common.enums.OrderSnType;
import com.cfpamf.ms.mallorder.v2.config.CommonConfig;
import com.cfpamf.ms.mallorder.v2.domain.vo.OperationUserVO;
import com.cfpamf.ms.mallorder.v2.domain.vo.OrderCancelVO;
import com.cfpamf.ms.mallorder.v2.service.OrderCancelService;
import com.cfpamf.ms.mallorder.v2.service.OrderCancelSupportService;
import com.slodon.bbc.core.constant.PromotionConst;
import com.slodon.bbc.core.util.AssertUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;

import lombok.extern.slf4j.Slf4j;

/**
 * 系统自动取消没有付款的新人订单
 * cron:0 0/5 * * * ?
 *
 */
@Component
@Slf4j
public class OrderCancelNewPeopleJob {

    @Autowired
    private OrderModel orderModel;
    
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    
    @Resource
    private OrderMapper orderMapper;
    
    @Autowired
    private OrderCancelService orderCancelService;

    @Autowired
    private OrderCancelSupportService newPeopleOrderCancelSupportService;
    
    @Autowired
    private CommonConfig commonConfig;

    @XxlJob(value = "OrderCancelNewPeopleJob")
    public ReturnT<String> execute(String s) {

        long startTime = System.currentTimeMillis();
        XxlJobHelper.log("start job at local time: {0}", LocalDateTime.now());

        log.info("jobSystemCancelOrder() start");
        try {
            if (commonConfig.isOpenV2()) {
                log.info("开启V2流程系统自动取消没有付款新人专享订单，jobSystemCancelNewPeopleOrder()");
                boolean jobResult = this.jobSystemCancelNewPeopleOrder();
                AssertUtil.isTrue(!jobResult, "[jobSystemCancelNewPeopleOrder] 定时任务系统自动取消没有付款新人专享订单时失败");
                XxlJobHelper.log("finish job at local time: {0}  cost:{1}", LocalDateTime.now(),
                    (System.currentTimeMillis() - startTime) / 1000);
                return ReturnT.SUCCESS;
            }
            boolean jobResult = orderModel.jobSystemCancelNewPeopleOrder();
            AssertUtil.isTrue(!jobResult, "[OrderCancelNewPeopleJob] 定时任务系统自动取消没有付款新人专享订单时失败");
        } catch (Exception e) {
            log.error("OrderCancelNewPeopleJob()", e);
        }

        XxlJobHelper.log("finish job at local time: {0}  cost:{1}",
                LocalDateTime.now(),
                (System.currentTimeMillis() - startTime) / 1000);
        return ReturnT.SUCCESS;
    }
    
    /**
     * 系统自动取消没有付款的新人订单
     *
     * @return
     */
    public boolean jobSystemCancelNewPeopleOrder() {
        // 买家多少分钟内未支付订单，订单取消
        String value = stringRedisTemplate.opsForValue().get("exclusive_order_cancle");
        int limitMinute = value == null ? 30 : Integer.parseInt(value);

        // 获取当前时间limitMinute分钟之前的时间
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, -limitMinute);
        Date cancelTime = calendar.getTime();
        // 获取超时未付款的订单
        OrderExample example = new OrderExample();
        example.setCreateTimeBefore(cancelTime);
        example.setOrderState(OrderConst.ORDER_STATE_10);
        example.setOrderType(PromotionConst.PROMOTION_TYPE_106);
        List<OrderPO> orderPOList = orderMapper.listByExample(example);
        if (CollectionUtils.isEmpty(orderPOList)) {
            log.warn("V2流程系统自动取消没有付款的新人订单，orderPOList为空，不存在相关记录无需取消操作");
            return true;
        }
        // 父订单号去重
        Set<String> orderParentSns = orderPOList.stream().map(OrderPO::getParentSn).collect(Collectors.toSet());
        orderParentSns.forEach(parentSn -> {
            try {
                log.info("开启V2流程取消订单，job自动取消没有付款的新人订单。parentSn:{}", parentSn);
                orderCancelService.cancelOrder(newPeopleOrderCancelSupportService,
                    new OrderCancelVO(OrderSnType.PARENT_ORDER_SN, parentSn, null, "system"),
                    new OperationUserVO(OrderConst.LOG_ROLE_ADMIN,OrderConst.RETURN_BY_0, 0, "system", "系统自动取消订单新人订单"));
            } catch (Exception e) {
                log.error("开启V2流程取消订单，job自动取消没有付款的新人订单。parentSn:{}", parentSn, e);
            }
        });
        return true;
    }
}
