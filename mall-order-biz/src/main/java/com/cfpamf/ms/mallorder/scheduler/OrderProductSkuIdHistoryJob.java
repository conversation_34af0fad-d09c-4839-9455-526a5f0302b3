package com.cfpamf.ms.mallorder.scheduler;

import com.cfpamf.ms.mallorder.service.IOrderProductService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 订单商品sku id 历史数据处理job
 *
 * <AUTHOR>
 * @since 2024/8/28
 */
@Component
@Slf4j
public class OrderProductSkuIdHistoryJob {

    @Autowired
    private IOrderProductService orderProductService;

    @XxlJob("OrderProductSkuIdHistoryJob")
    public ReturnT<String> execute(String param) throws Exception {

        XxlJobHelper.log("OrderProductSkuIdHistoryJob===============start, DATE :{}", LocalDateTime.now());
        orderProductService.dealHistorySkuId(param);
        XxlJobHelper.log("OrderProductSkuIdHistoryJob===============end, DATE :{}", LocalDateTime.now());
        return ReturnT.SUCCESS;
    }
}
