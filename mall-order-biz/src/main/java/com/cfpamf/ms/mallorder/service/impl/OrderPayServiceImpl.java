package com.cfpamf.ms.mallorder.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cfpamf.mallpayment.facade.vo.PaymentLoanInfoVO;
import com.cfpamf.ms.loan.facade.request.external.mall.*;
import com.cfpamf.ms.loan.facade.vo.external.mall.MallContractCodesVo;
import com.cfpamf.ms.loan.facade.vo.external.mall.MallContractContentVO;
import com.cfpamf.ms.mall.account.constant.AccountConstans;
import com.cfpamf.ms.mall.account.enums.AccountCardTypeEnum;
import com.cfpamf.ms.mall.account.vo.AccountCard;
import com.cfpamf.ms.mallgoods.facade.vo.Product;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.enums.PayMethodEnum;
import com.cfpamf.ms.mallorder.common.enums.PresellCapitalTypeEnum;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.common.util.DateUtil;
import com.cfpamf.ms.mallorder.common.util.OrderLocalUtils;
import com.cfpamf.ms.mallorder.common.util.ValidUtils;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.OrderAutoPayDTO;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.enums.PayWayEnum;
import com.cfpamf.ms.mallorder.integration.loan.LoanPayIntegration;
import com.cfpamf.ms.mallorder.integration.pay.PayIntegration;
import com.cfpamf.ms.mallorder.integration.settlement.BillOperatinIntegration;
import com.cfpamf.ms.mallorder.mapper.OrderMapper;
import com.cfpamf.ms.mallorder.mapper.OrderPayMapper;
import com.cfpamf.ms.mallorder.mapper.OrderPresellMapper;
import com.cfpamf.ms.mallorder.model.OrderProductModel;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.req.OrderPayRequest;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.v2.service.OrderPayRecordService;
import com.cfpamf.ms.mallorder.v2.service.OrderPresellService;
import com.cfpamf.ms.mallorder.vo.*;
import com.cfpamf.ms.mallshop.api.StoreFeignClient;
import com.cfpamf.ms.mallshop.resp.Store;
import com.cfpamf.smartid.client.utils.sharding.SeqEnum;
import com.cfpamf.smartid.client.utils.sharding.ShardingId;
import com.slodon.bbc.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @Create 2021-09-17 13:53
 * @Description :订单支付表
 */
@Slf4j
@Service
public class OrderPayServiceImpl extends ServiceImpl<OrderPayMapper, OrderPayPO> implements IOrderPayService {

    @Autowired
    OrderPayMapper orderPayMapper;
    @Autowired
    private StoreFeignClient storeFeignClient;
    @Resource
    private OrderProductModel orderProductModel;
    @Resource
    private OrderMapper orderMapper;
    @Autowired
    private IOrderProductService iOrderProductService;
    @Autowired
    private IPayMethodService iPayMethodService;
    @Resource
    private ShardingId shardingId;
    @Autowired
    private BillOperatinIntegration billOperatinIntegration;
    @Autowired
    private IOrderProductService orderProductService;
    @Autowired
    private PayIntegration payIntegration;
    @Autowired
    private IOrderService orderService;
    @Autowired
    private OrderPayRecordService orderPayRecordService;
    @Autowired
    private OrderPresellService orderPresellService;

    @Resource
    private OrderPresellMapper orderPresellMapper;
    @Autowired
    private OrderLocalUtils orderLocalUtils;
    @Autowired
    private IOrderExtendService iOrderExtendService;
    /**
     * @param
     * @return java.util.List<com.cfpamf.ms.mallorder.dto.OrderAutoPayDTO>
     * @description : 获取待自动支付订单
     */
    @Override
    public List<OrderAutoPayDTO> listOrderAutoPay() {
        return orderPayMapper.listOrderAutoPay();
    }

    /**
     * @param isOwnStore
     * @param enterType
     * @return void
     * @description :转换商家类型
     * OWN_BUSINESS("OWN_BUSINESS","自营商家", 1),
     * DOW_CENTER("DOW_CENTER","企业入驻商家", 2),
     * INSURE("INSURE","保险", 3),
     * RECHARGE("RECHARGE","话费充值", 4),
     * PERSON_DOW_CENTER("PERSON_DOW_CENTER","个人入驻商家", 5);
     */
    private String convertMerchantType(Integer isOwnStore, Integer enterType) {
        String tmp = "";
        //自营店铺1-自营店铺，2-入驻店铺
        if (isOwnStore == 1) {
            tmp = "OWN_BUSINESS";
        } else if (isOwnStore == 2) {
            //入驻类型0-个人入驻，1-企业入驻
            if (enterType == 0) {
                tmp = "PERSON_DOW_CENTER";
            } else if (enterType == 1) {
                tmp = "DOW_CENTER";
            }
        }
        return tmp;
    }

    @Override
    public String getOrderPno(String memberId) {
        return String.valueOf(shardingId.next(SeqEnum.PNO, memberId));
    }

    @Override
    public OrderPayBriefInfoVO getOrderPayBriefInfoByPno(String pno) {
        OrderPayBriefInfoVO orderPayBriefInfoVO = new OrderPayBriefInfoVO();
        orderPayBriefInfoVO.setPaySn(pno);
        List<OrderBriefInfoVO> orderBriefInfoVOs = new ArrayList<>();

        LambdaQueryWrapper<OrderPO> orderPOQueryWrapper = Wrappers.lambdaQuery(OrderPO.class);
        orderPOQueryWrapper.eq(OrderPO::getPaySn, pno)
                .eq(OrderPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
        List<OrderPO> orderPOs = orderMapper.selectList(orderPOQueryWrapper);

        orderPOs.forEach(orderPO -> {
            BizAssertUtil.notNull(orderPO, "查询订单信息为空");
            // 订单商品
            List<OrderProductPO> orderProducts = orderProductModel.getOrderProductListByOrderSn(orderPO.getOrderSn());
            BizAssertUtil.notEmpty(orderProducts, "查询订单商品信息为空");

            OrderBriefInfoVO orderBriefInfoVO = new OrderBriefInfoVO();
            orderBriefInfoVO.setOrderSn(orderPO.getOrderSn());
            orderBriefInfoVO.setOrderState(orderPO.getOrderState());
            orderBriefInfoVO.setOrderType(orderPO.getOrderType());
            orderBriefInfoVO.setMemberId(orderPO.getMemberId());
            List<OrderProductBriefInfoVO> orderProductBriefInfoVOs = new ArrayList<>();
            for (OrderProductPO orderProductPO : orderProducts) {
                OrderProductBriefInfoVO orderProductBriefInfoVO = new OrderProductBriefInfoVO();
                orderProductBriefInfoVO.setGoodsId(orderProductPO.getGoodsId());
                orderProductBriefInfoVO.setProductId(orderProductPO.getProductId());
                orderProductBriefInfoVO.setProductNum(orderProductPO.getProductNum());
                orderProductBriefInfoVOs.add(orderProductBriefInfoVO);
            }
            orderBriefInfoVO.setOrderProductInfos(orderProductBriefInfoVOs);
            orderBriefInfoVOs.add(orderBriefInfoVO);
        });
        orderPayBriefInfoVO.setOrderBriefInfos(orderBriefInfoVOs);

        return orderPayBriefInfoVO;
    }

    @Override
    public OrderPayPO getByPaySn(String paySn) {
    	if(StringUtils.isEmpty(paySn)) {
    		return null;
    	}
        LambdaQueryWrapper<OrderPayPO> orderPayQuery = new LambdaQueryWrapper<>();
        orderPayQuery.eq(OrderPayPO::getPaySn, paySn);
        orderPayQuery.eq(OrderPayPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
        return this.getOne(orderPayQuery);
    }

    @Autowired
    private IOrderService iOrderService;
    @Autowired
    private IOrderExtendFinanceService iOrderExtendFinanceService;
    @Autowired
    private LoanPayIntegration loanPayIntegration;

    @Override
    public MallContractContentVO contractPreview(@NotNull @Valid OrderPayRequest payRequest) {

        if (StringUtils.isBlank(payRequest.getTempleId())) {
            throw new BusinessException("合同ID不能为空");
        }

        OrderPO orderPO = iOrderService.getOne(Wrappers.lambdaQuery(OrderPO.class)
                .eq(OrderPO::getPaySn, payRequest.getOno().get(0))
                .last("limit 1"));

        if (orderPO == null) {
            throw new BusinessException("订单不存在");
        }
        OrderExtendFinancePO financeServiceOne = iOrderExtendFinanceService.getOne(Wrappers.lambdaQuery(OrderExtendFinancePO.class)
                .eq(OrderExtendFinancePO::getOrderSn, orderPO.getOrderSn())
                .last("limit 1"));
        OrderExtendPO orderExtendPO = iOrderExtendService.lambdaQuery().eq(OrderExtendPO::getOrderSn, orderPO.getOrderSn()).one();

       
        ContractPreviewRequest request = new ContractPreviewRequest();
        request.setTempleId(payRequest.getTempleId());
        request.setLoanCustId(orderExtendPO.getCustomerId());
        request.setOrderBatchId(orderPO.getOrderSn());
        request.setAmount(orderPO.getOrderAmount());
        
        if(PayMethodEnum.isCombinationPay(orderPO.getPaymentCode())) {
        	OrderPresellPO orderPresellPO =
        		       orderPresellService.queryBalanceInfoByOrderSn(orderPO.getOrderSn());
        	if(ObjectUtils.isNotEmpty(orderPresellPO)) {
        		request.setOrderBatchId(orderPresellPO.getPayNo());
                request.setAmount(orderPresellPO.getPayAmount());
                log.info("【contractPreview】:orderSn:{} payNo:{} 预付订单，替换贷款合同预览金额和单号",orderPO.getOrderSn(),orderPresellPO.getPayNo());
        	}
        }
        
        request.setApplyPeriod(payRequest.getLoanPeriod());
        request.setDueDay(payRequest.getRepaymentDay());
        request.setRepaymentMode(payRequest.getRepaymentMode());
        request.setLoanPurposeDesc(payRequest.getLoanPurpose());
        if (StringUtils.isNotBlank(payRequest.getPayMode())) {
            request.setPayMode(payRequest.getPayMode());
        } else {
            request.setPayMode(PayWayEnum.convertLoanPayModel(payRequest.getPayWay()));
        }
        request.setMerchantId(String.valueOf(orderPO.getStoreId()));
        request.setOrderSource("mallOrder");
        request.setRecommendStoreId(orderPO.getRecommendStoreId());
        if (ObjectUtil.isNotNull(financeServiceOne)) {
            request.setPromotionBatchNo(financeServiceOne.getCouponBatch());
        }

        MerchantBaseVo merchantBaseInfo = orderProductModel.getMerchantBaseInfo(orderPO.getStoreId());
        MerchantBaseVo recommendStoreInfo = orderProductModel.getMerchantBaseInfo(orderPO.getRecommendStoreId());

        MerchantInfoVO merchantInfo = new MerchantInfoVO();
        merchantInfo.setId(merchantBaseInfo.getId());
        merchantInfo.setCompanySealCode(merchantBaseInfo.getCompanySealCode());
        merchantInfo.setMerchantName(merchantBaseInfo.getMerchantName());
        merchantInfo.setMerchantMobile(merchantBaseInfo.getMerchantMobile());
        merchantInfo.setCorporateName(merchantBaseInfo.getCorporateName());
        merchantInfo.setProvince(merchantBaseInfo.getProvince());
        merchantInfo.setCity(merchantBaseInfo.getCity());
        merchantInfo.setCounty(merchantBaseInfo.getCounty());
        merchantInfo.setTown(merchantBaseInfo.getTown());
        merchantInfo.setTownFlag(ValidUtils.isEmpty(merchantBaseInfo.getTown()) ? 0 : 1);
        merchantInfo.setAddress(merchantBaseInfo.getAddress());
        merchantInfo.setJoinDate(merchantBaseInfo.getJoinDate());
        merchantInfo.setEndDate(merchantBaseInfo.getEndDate());
        merchantInfo.setMerchantDesc(merchantBaseInfo.getMerchantDesc());
        merchantInfo.setMerchantType(merchantBaseInfo.getMerchantType());
        merchantInfo.setMerchantCode(merchantBaseInfo.getMerchantCode());
        merchantInfo.setMerchantNo(merchantBaseInfo.getMerchantNo());
        merchantInfo.setCustId(merchantBaseInfo.getCustId());
        merchantInfo.setVirtualAcctNo(merchantBaseInfo.getVirtualAcctNo());
        merchantInfo.setSellerId(merchantBaseInfo.getSellerId());
        if (orderPO.getNewOrder()) {
            AccountCard accountCard = billOperatinIntegration.detailByBankAccount(AccountConstans.UNI_PLF_STORE_ID, AccountCardTypeEnum.UNI_JS_PLF_SUP);
            if (Objects.nonNull(accountCard)) {
                merchantInfo.setDrawCardNo(accountCard.getBankAccountNumber());
                merchantInfo.setDrawBankCode(accountCard.getBankCode());
                merchantInfo.setDrawBankName(accountCard.getBankBranch());
                merchantInfo.setDrawCardId(accountCard.getLoanCardId());
                merchantInfo.setDrawBankAccount(accountCard.getBankAccountName());
            }
        } else {
            merchantInfo.setDrawCardNo(recommendStoreInfo.getDrawCardNo());
            merchantInfo.setDrawBankCode(recommendStoreInfo.getDrawBankCode());
            merchantInfo.setDrawBankName(recommendStoreInfo.getDrawBankName());
            merchantInfo.setDrawCardId(recommendStoreInfo.getDrawCardId());
            merchantInfo.setDrawBankAccount(recommendStoreInfo.getDrawBankAccount());
        }
        merchantInfo.setOpeningBank(merchantBaseInfo.getDrawBankName());
        request.setMerchantInfo(merchantInfo);
        List<MallCommodityBriefInfo> commodityList = initCommodityBriefInfo(orderPO);
        request.setCommodityBriefInfos(commodityList);

        return loanPayIntegration.contractPreviewV3(request);
    }

    /**
     * @param orderPO
     * @return java.util.List<com.cfpamf.ms.loan.facade.request.external.mall.MallCommodityBriefInfo>
     * @description : 初始化商品信息
     */
    private List<MallCommodityBriefInfo> initCommodityBriefInfo(OrderPO orderPO) {
        //查询订单商品信息
        List<OrderProductPO> orderProductPOS = orderProductService.lambdaQuery()
                .eq(OrderProductPO::getOrderSn, orderPO.getOrderSn())
                .list();

        Map<Long, Product> longProductMap = orderLocalUtils.getLongProductMap(orderProductPOS);

        List<MallCommodityBriefInfo> commodityList = new ArrayList<>();

        for (OrderProductPO orderProductPO : orderProductPOS) {
            //商家信息
            MallCommodityBriefInfo mallCommodityBriefInfo = new MallCommodityBriefInfo();
            mallCommodityBriefInfo.setMerchantId(orderPO.getStoreId().toString());
            mallCommodityBriefInfo.setMerchantName(orderPO.getStoreName());
            mallCommodityBriefInfo.setMerchantType(orderPO.getStoreIsSelf().toString());
            mallCommodityBriefInfo.setRecommendStoreId(orderPO.getRecommendStoreId());
            //商品信息
            List<String> strings = Arrays.asList(orderProductPO.getGoodsCategoryPath().split("->"));
            mallCommodityBriefInfo.setCommodityId(orderProductPO.getGoodsId().toString());
            mallCommodityBriefInfo.setCommodityName(orderProductPO.getGoodsName());
            mallCommodityBriefInfo.setFirstItemCategoryName(strings.size() > 0 ? strings.get(0) : "");
            mallCommodityBriefInfo.setSecondItemCategoryName(strings.size() > 1 ? strings.get(1) : "");
            mallCommodityBriefInfo.setThreeItemCategoryName(strings.size() > 2 ? strings.get(2) : "");

            //处理类目id
            if (longProductMap.containsKey(orderProductPO.getProductId())) {
                Product product = longProductMap.get(orderProductPO.getProductId());
                mallCommodityBriefInfo.setFirstItemCategoryId(product.getCategoryId1().toString());
                mallCommodityBriefInfo.setSecondItemCategoryId(product.getCategoryId2().toString());
                mallCommodityBriefInfo.setThreeItemCategoryId(product.getCategoryId3().toString());
            }
            commodityList.add(mallCommodityBriefInfo);
        }
        return commodityList;
    }

    @Override
    public MallContractCodesVo listLoanContractCode(OrderPayRequest payRequest) {
        OrderPO orderPO = iOrderService.getOne(Wrappers.lambdaQuery(OrderPO.class)
                .eq(OrderPO::getPaySn, payRequest.getOno().get(0))
                .last("limit 1"));

        if (orderPO == null) {
            throw new BusinessException("订单不存在");
        }
        OrderExtendPO orderExtendPO = iOrderExtendService.lambdaQuery().eq(OrderExtendPO::getOrderSn, orderPO.getOrderSn()).one();
        GetMallContractCodesRequest request = new GetMallContractCodesRequest();

        request.setOrderBatchId(String.valueOf(orderPO.getOrderSn()));
        request.setLoanCustId(orderExtendPO.getCustomerId());

        MallApplyInfo mallApplyInfo = new MallApplyInfo();
        if (StringUtils.isNotBlank(payRequest.getPayMode())) {
            mallApplyInfo.setPayMode(payRequest.getPayMode());
        } else {
            mallApplyInfo.setPayMode(PayWayEnum.convertLoanPayModel(payRequest.getPayWay()));
        }
        mallApplyInfo.setAmount(orderPO.getOrderAmount());


        PayMethodPO methodServiceOne = iPayMethodService.getOne(Wrappers.lambdaQuery(PayMethodPO.class)
                .eq(PayMethodPO::getPayMethodCode, payRequest.getPayWay().getValue())
                .last("limit 1"));
        if (Objects.nonNull(methodServiceOne)) {
            mallApplyInfo.setLoanProductId(methodServiceOne.getLoanProduct());
        } else {
            log.error("获取合同列表时支付方式匹配异常，单号:{}", payRequest.getOno());
            throw new BusinessException("支付方式为空，请联系管理员!");
        }

        mallApplyInfo.setApplyPeriod(payRequest.getLoanPeriod());
        mallApplyInfo.setDueDay(payRequest.getRepaymentDay());
        mallApplyInfo.setRepaymentMode(payRequest.getRepaymentMode());
        mallApplyInfo.setLoanPurposeDesc(payRequest.getLoanPurpose());

        MerchantBaseVo recommendStoreInfo = orderProductModel.getMerchantBaseInfo(orderPO.getRecommendStoreId());

        Store storeByStoreId = storeFeignClient.getStoreByStoreId(orderPO.getRecommendStoreId());
        if (Objects.nonNull(storeByStoreId) && Objects.nonNull(storeByStoreId.getStoreCertificate())) {
            mallApplyInfo.setOrgCode(storeByStoreId.getStoreCertificate().getCertificateRegistrationNum());
        } else {
            log.error("获取合同列表时店铺资质异常，单号:{}", payRequest.getOno());
        }


        mallApplyInfo.setDrawCardId(recommendStoreInfo.getDrawCardId());
        mallApplyInfo.setDrawCardNo(recommendStoreInfo.getDrawCardNo());
        mallApplyInfo.setDrawBankCode(recommendStoreInfo.getDrawBankCode());
        mallApplyInfo.setDrawBankName(recommendStoreInfo.getDrawBankName());
        mallApplyInfo.setDrawBankAccount(recommendStoreInfo.getDrawBankAccount());
        mallApplyInfo.setOpeningBank(recommendStoreInfo.getDrawBankName());
        request.setApplyInfo(mallApplyInfo);

        MallOrderInfo mallOrderInfo = new MallOrderInfo();
        // 重复参数
        mallOrderInfo.setOrderBatchId(String.valueOf(orderPO.getOrderSn()));
        mallOrderInfo.setTradeNo(String.valueOf(orderPO.getOrderSn()));
        mallOrderInfo.setOrderSource("mallOrder");
        mallOrderInfo.setMallChannel(orderPO.getChannel());
        mallOrderInfo.setMerchantId(String.valueOf(orderPO.getStoreId()));
        mallOrderInfo.setMerchantName(orderPO.getStoreName());
        mallOrderInfo.setMerchantType(orderPO.getStoreIsSelf() == 1 ? "1" : "2");
        mallOrderInfo.setRecommendStoreId(orderPO.getRecommendStoreId());


        List<OrderProductPO> orderProductPOS = iOrderProductService.lambdaQuery().eq(OrderProductPO::getOrderSn, orderPO.getOrderSn()).list();

        Map<Long, Product> longProductMap = orderLocalUtils.getLongProductMap(orderProductPOS);

        mallOrderInfo.setCommodityInfos(new ArrayList<>(orderProductPOS.size()));

        for (OrderProductPO orderItemPo : orderProductPOS) {
            MallCommodityInfo mallCommodityInfo = new MallCommodityInfo();
            mallCommodityInfo.setCommodityId(String.valueOf(orderItemPo.getGoodsId()));
            mallCommodityInfo.setCommoditySku(String.valueOf(orderItemPo.getProductId()));
            mallCommodityInfo.setCommodityName(orderItemPo.getGoodsName());
            mallCommodityInfo.setCommodityNumber(orderItemPo.getProductNum());
            mallCommodityInfo.setUnitPrice(orderItemPo.getProductShowPrice());
            mallCommodityInfo.setItemCategory(String.valueOf(orderItemPo.getGoodsCategoryId()));
            mallCommodityInfo.setItemCategoryTree(
                    orderItemPo.getGoodsCategoryPath()
            );

            List<String> strings = Arrays.asList(orderItemPo.getGoodsCategoryPath().split("->"));
            mallCommodityInfo.setFirstItemCategoryName(strings.size() > 0 ? strings.get(0) : "");
            mallCommodityInfo.setSecondItemCategoryName(strings.size() > 1 ? strings.get(1) : "");
            mallCommodityInfo.setThreeItemCategoryName(strings.size() > 2 ? strings.get(2) : "");

            //处理类目id
            if (longProductMap.containsKey(orderItemPo.getProductId())) {
                Product product = longProductMap.get(orderItemPo.getProductId());
                mallCommodityInfo.setFirstItemCategoryId(product.getCategoryId1().toString());
                mallCommodityInfo.setSecondItemCategoryId(product.getCategoryId2().toString());
                mallCommodityInfo.setThreeItemCategoryId(product.getCategoryId3().toString());
            }

            mallOrderInfo.getCommodityInfos().add(mallCommodityInfo);
        }
        request.setOrderInfo(mallOrderInfo);

        return loanPayIntegration.listLoanContractCode(request);
    }

    @Override
    public void dealPreSellResult(String orderSn, Integer payType, OrderPayInfoVO orderPayInfoVO) {
        List<OrderPresellPO> orderPreSellPOList = orderPresellMapper.getPreSellOrderDetailByOrderSn(orderSn);
        if (CollectionUtils.isEmpty(orderPreSellPOList)) {
            return;
        }

        orderPayInfoVO.setOrderType(OrderTypeEnum.PRE_SELL_DEPOSIT.getValue());
        orderPreSellPOList.sort(Comparator.comparing(OrderPresellPO::getType));

        BigDecimal needPay = BigDecimal.ZERO;
        String payNo = null;
        LocalDateTime remainTime = null;
        boolean preSellPayFlag = false;

        /**
         * 是否展示支付金额，订金支付完成之后，页面不立马展示尾款支付
         * */
        boolean isShowPriceFlag = false;

        for (OrderPresellPO orderPresellPO : orderPreSellPOList) {
            payNo = orderPresellPO.getPayNo();
            needPay = orderPresellPO.getPayAmount();
            if (orderPresellPO.getType() == 1 && orderPresellPO.getPayStatus() == 1) {
                remainTime = orderPresellPO.getDeadTime();
                preSellPayFlag = true;
                break;
            } else if (orderPresellPO.getType() == 2 && orderPresellPO.getPayStatus() == 1) {
                remainTime = orderPresellPO.getDeadTime();
                preSellPayFlag = true;
                if (payType != null && payType.equals(CommonConst.CHECKOUT_COUNTER_PAY_TYPE)) {
                    isShowPriceFlag = true;
                }
                break;
            }
        }
        if (!preSellPayFlag) {
            log.error("payInfo dealPreSellResult method，预售订金订单未取预售单的支付信息，orderSn = {},paySn = {}", orderSn, orderPayInfoVO.getPaySn());
        }
        if (isShowPriceFlag) {
            orderPayInfoVO.setNeedPay(null);
            orderPayInfoVO.setPayTimeLimit(0L);
        } else {
            if (remainTime != null) {
                //截止时间
                orderPayInfoVO.setPayTimeLimit(DateUtil.dealRemainTime(remainTime, LocalDateTime.now()));
            }
            orderPayInfoVO.setNeedPay(needPay);
            orderPayInfoVO.setPayNo(payNo);
        }
    }


    /**
     * @param bizId 预付订单为payNo、普通订单为orderSn
     * @return java.lang.Boolean
     * @description :放款流水补偿
     */
    @Override
    public Boolean orderBankTrxNoMakeUp(Long bizId) {
        PaymentLoanInfoVO loanInfoVO = payIntegration.queryLoanInfo(bizId.toString());

        OrderPO orderDb = iOrderService.getOne(Wrappers.lambdaQuery(OrderPO.class)
                .eq(OrderPO::getOrderSn, bizId.toString())
                .last("limit 1"));

        //旧订单不需要同步贷款类流水，直接跳过
        if (Objects.isNull(loanInfoVO) && Objects.nonNull(orderDb) && !orderDb.getNewOrder()) {
            return true;
        }

        if (Objects.isNull(loanInfoVO) || StringUtils.isEmpty(loanInfoVO.getTransactionNo())) {
            return false;
        }
        //非预售订单，更新order表
        if (Objects.nonNull(orderDb) && !orderDb.getOrderType().equals(OrderTypeEnum.PRE_SELL_DEPOSIT.getValue())) {
            return orderService.lambdaUpdate()
                    .eq(OrderPO::getOrderSn, bizId.toString())
                    .set(OrderPO::getBankPayTrxNo, loanInfoVO.getTransactionNo())
                    .update();

        }
        //预售订单，更新预售表
        return orderPresellService.lambdaUpdate()
                .eq(OrderPresellPO::getPayNo, bizId.toString())
                .set(OrderPresellPO::getBankPayTrxNo, loanInfoVO.getTransactionNo())
                .update();
    }

    /**
     * 代扣还款校验
     *
     * @param orderNo 订单编号
     * @return 校验结果
     */
    @Override
    public MallWithholdCheckResponseVO checkWithHold(String orderNo) {
        OrderPO orderPO = orderService.getByOrderSn(orderNo);
        if (Objects.isNull(orderPO)){
            throw new BusinessException("订单不存在");
        }
        String orderBatchId = null;
        if (PayMethodEnum.isLoanPay(orderPO.getPaymentCode())){
            // 支付方式为贷款类，直接使用订单编号
            orderBatchId = orderPO.getOrderSn();
        } else if (PayMethodEnum.isCombinationPay(orderPO.getPaymentCode())){
            // 组合支付，判断尾款是否为贷款类支付
            String paySn = orderPO.getPaySn();
            if (StringUtils.isNotBlank(paySn)){
                List<OrderPayRecordPO> orderPayRecordPOS = orderPayRecordService.queryOrderPayByPaySn(paySn);
                if (!CollectionUtils.isEmpty(orderPayRecordPOS)){
                    OrderPayRecordPO balanceRecord = orderPayRecordPOS.stream().filter(p -> PresellCapitalTypeEnum.BALANCE.getValue().equals(p.getPayOrder()) && PayMethodEnum.isLoanPay(p.getPaymentCode())).findFirst().orElse(null);
                    if (Objects.nonNull(balanceRecord)){
                        // 组合支付使用支付编号
                        orderBatchId = balanceRecord.getPayNo();
                    }
                }
            }
        }
        if (StringUtils.isBlank(orderBatchId)){
            return null;
        }
        log.info("checkWithHold orderBatchId:{}",orderBatchId);
        WithholdCheckResponseVO responseVO = null;
        try {
            responseVO = loanPayIntegration.checkCdMallCanDoWithhold(orderBatchId);
        } catch (Exception e) {
            log.info("checkWithHold exp:{}",e.getMessage());
            return null;
        }
        return new MallWithholdCheckResponseVO(responseVO);
    }
}
