package com.cfpamf.ms.mallorder.integration.userPubService;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.framework.autoconfigure.common.exception.MSException;
import com.cfpamf.framework.autoconfigure.common.result.Result;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.cfpamf.ms.mallorder.enums.ManagerBusinessModelEnum;
import com.cfpamf.ms.mallorder.integration.facade.UserPublicizeFacade;
import com.cfpamf.ms.mallorder.integration.facade.vo.DbcDistributionSaleUserVO;
import com.cfpamf.ms.mallorder.vo.CustRelateManagerVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Slf4j
@Component
public class UpsServiceFeignIntegration {

    @Autowired
    private UserPublicizeFacade userPublicizeFacade;

    public DbcDistributionSaleUserVO getSaleUserInfo(String identityNum) {
        if (StringUtils.isBlank(identityNum)) {
            return null;
        }
        Result<DbcDistributionSaleUserVO> result = null;
        try {
            log.info("根据id查询分销用户详情,identityNum:{}",identityNum);
            result = userPublicizeFacade.getSaleUserInfo(identityNum);
        }catch (Exception ex){
            log.error("根据id查询个人详情调用用户推广中心出现未知异常,identityNum:{}",identityNum,ex);
            throw new MSException(String.valueOf(ErrorCodeEnum.C.CALL_EXCEPTION.getCode()),"根据id查询个人详情调用用户推广中心异常");
        }
        if (Objects.isNull(result) || !result.isSuccess()) {
            throw new MSException(String.valueOf(ErrorCodeEnum.C.RESULT_INVALID.getCode()),"根据id查询个人详情调用用户推广中心失败");
        }
        log.info("根据id查询分销用户详情,返回结果:{}", JSONObject.toJSONString(result.getData()));
        return result.getData();
    }

    public DbcDistributionSaleUserVO queryUserByMyReferenceCode(String myReferenceCode) {
        if (StringUtils.isBlank(myReferenceCode)) {
            return null;
        }
        Result<DbcDistributionSaleUserVO> result = null;
        try {
            log.info("根据自身推荐码查询分销用户详情,myReferenceCode:{}",myReferenceCode);
            result = userPublicizeFacade.queryUserByMyReferenceCode(myReferenceCode);
        }catch (Exception ex){
            log.error("根据自身推荐码查询个人详情调用用户推广中心出现未知异常,myReferenceCode:{}",myReferenceCode,ex);
            throw new MSException(String.valueOf(ErrorCodeEnum.C.CALL_EXCEPTION.getCode()),"根据自身推荐码查询个人详情调用用户推广中心异常");
        }
        if (Objects.isNull(result) || !result.isSuccess()) {
            throw new MSException(String.valueOf(ErrorCodeEnum.C.RESULT_INVALID.getCode()),"根据自身推荐码查询个人详情调用用户推广中心失败");
        }
        log.info("根据自身推荐码查询分销用户详情,返回结果:{}", JSONObject.toJSONString(result.getData()));
        return result.getData();
    }

    public CustRelateManagerVO queryAgricManager(String userNo) {
        if (StringUtils.isBlank(userNo)) {
            return null;
        }
        Result<CustRelateManagerVO> result = null;
        try {
            log.info("根据用户userNo查询农服管护客户经理详情,userNo:{}",userNo);
            result = userPublicizeFacade.queryBizManager(userNo, ManagerBusinessModelEnum.AGRIC_MANAGER.getCode());
        }catch (Exception ex){
            log.error("根据userNo查询农服管护客户经理调用用户推广中心出现未知异常,userNo:{}",userNo,ex);
            throw new MSException(String.valueOf(ErrorCodeEnum.C.CALL_EXCEPTION.getCode()),"根据userNo查询农服管护客户经理调用用户推广中心异常");
        }
        if (Objects.isNull(result) || !result.isSuccess()) {
            throw new MSException(String.valueOf(ErrorCodeEnum.C.RESULT_INVALID.getCode()),"根据userNo查询农服管护客户经理调用用户推广中心失败");
        }
        log.info("根据用户userNo查询农服管护客户经理详情,返回结果:{}", JSONObject.toJSONString(result.getData()));
        return result.getData();
    }

}
