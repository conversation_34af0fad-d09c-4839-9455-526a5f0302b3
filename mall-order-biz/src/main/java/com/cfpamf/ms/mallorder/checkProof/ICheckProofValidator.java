package com.cfpamf.ms.mallorder.checkProof;

import com.cfpamf.ms.mall.filecenter.domain.dto.FileScenesMaterialProofDTO;
import com.cfpamf.ms.mallorder.common.enums.OrderMaterialTypeEnum;
import com.cfpamf.ms.mallorder.vo.OrderTradeProofVO;

/**
 * <AUTHOR>
 */
public interface ICheckProofValidator {
    boolean needValidate(OrderTradeProofVO orderTradeProofVO, OrderMaterialTypeEnum materialTypeEnum);

    CheckProofValidateResult validate(FileScenesMaterialProofDTO paramDTO, OrderTradeProofVO orderTradeProofVO, OrderMaterialTypeEnum materialTypeEnum);
}
