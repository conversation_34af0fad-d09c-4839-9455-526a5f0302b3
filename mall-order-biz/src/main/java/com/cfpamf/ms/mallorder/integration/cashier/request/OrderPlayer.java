package com.cfpamf.ms.mallorder.integration.cashier.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 收银台-支付参与者
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-25 15:06:22
 */
@Data
public class OrderPlayer {

    @ApiModelProperty("参与者，为支付者时 可以是微信的openid,支付宝的手机号，银联支付电子账簿id，为收款者时，店铺id，收款者电子账簿id，用呗时为loanCustId:C开头")
    private String player;

    @ApiModelProperty("参与者名称")
    private String name;

    @ApiModelProperty("4663447短数字客户中心id")
    private String custId;

    @ApiModelProperty("管护客户经理")
    private String loanManager;

    @ApiModelProperty("参与者账号，用呗时为userNo：U开头")
    private String account;

    @ApiModelProperty("支付方式")
    private PayWayCasierEnum payWay;

    @ApiModelProperty("类型 个人、个体工商户、企业等")
    private String type;

    @ApiModelProperty(value = "手机号")
    private String mobile;

}
