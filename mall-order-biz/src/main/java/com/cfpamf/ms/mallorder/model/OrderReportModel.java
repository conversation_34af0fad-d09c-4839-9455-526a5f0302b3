package com.cfpamf.ms.mallorder.model;

import com.cfpamf.ms.mallgoods.facade.api.GoodsCategoryFeignClient;
import com.cfpamf.ms.mallgoods.facade.api.GoodsFeignClient;
import com.cfpamf.ms.mallgoods.facade.request.GoodsCategoryExample;
import com.cfpamf.ms.mallgoods.facade.request.GoodsExample;
import com.cfpamf.ms.mallgoods.facade.vo.Goods;
import com.cfpamf.ms.mallgoods.facade.vo.GoodsCategory;
import com.cfpamf.ms.mallmember.api.MemberFeignClient;
import com.cfpamf.ms.mallmember.dto.MemberDayDTO;
import com.cfpamf.ms.mallmember.request.MemberExample;
import com.cfpamf.ms.mallorder.common.enums.OrderStatusEnum;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.OrderSaleInfoDTO;
import com.cfpamf.ms.mallorder.dto.SaleTotalDayDTO;
import com.cfpamf.ms.mallorder.mapper.OrderMapper;
import com.cfpamf.ms.mallorder.mapper.OrderProductMapper;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderProductPO;
import com.cfpamf.ms.mallorder.request.OrderExample;
import com.cfpamf.ms.mallorder.request.OrderProductExample;
import com.cfpamf.ms.mallorder.vo.AdminIndexVO;
import com.cfpamf.ms.mallorder.vo.SalesVolumeVO;
import com.cfpamf.ms.mallshop.api.StoreFeignClient;
import com.cfpamf.ms.mallshop.request.StoreExample;
import com.cfpamf.ms.mallshop.resp.Store;
import com.slodon.bbc.core.util.TimeUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

@Component
@Slf4j
public class OrderReportModel {

    @Autowired
    ThreadPoolTaskExecutor threadPoolTaskExecutor;
    @Resource
    private OrderMapper orderMapper;
    @Resource
    private OrderProductMapper orderProductMapper;
    @Resource
    private GoodsCategoryFeignClient goodsCategoryFeignClient;
    @Resource
    private GoodsFeignClient goodsFeignClient;
    @Resource
    private StoreFeignClient storeFeignClient;
    @Resource
    private MemberFeignClient memberFeignClient;

    /**
     * 销售数据统计
     *
     * @param example
     * @return
     */
    public OrderSaleInfoDTO orderSaleInfo(OrderExample example) {
        return orderMapper.orderSaleInfo(example);
    }

    /**
     * 销售总额统计
     *
     * @param example
     * @return
     */
    public List<SaleTotalDayDTO> getSaleTotalDayDto(OrderExample example) {
        return orderMapper.getSaleTotalDayDto(example);
    }

    /**
     * 今日累计营业额
     *
     * @param payState
     * @return
     */
    @SneakyThrows
    public BigDecimal getDailySale(String payState) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        OrderExample example = new OrderExample();
        String today = TimeUtil.getToday();
        example.setCreateTimeAfter(sdf.parse(today + " 00:00:00"));
        example.setCreateTimeBefore(sdf.parse(today + " 23:59:59"));
        if (payState.equals(OrderConst.API_PAY_STATE_0)) {
            //待付款
            example.setOrderState(OrderConst.ORDER_STATE_10);
        } else if (payState.equals(OrderConst.API_PAY_STATE_1)) {
            example.setOrderStateIn(OrderConst.ORDER_STATE_20 + "," + OrderConst.ORDER_STATE_30 + "," + OrderConst.ORDER_STATE_40);
        }
        List<OrderPO> orderPOList = orderMapper.listByExample(example);
        BigDecimal totalAmount = BigDecimal.ZERO;
        if (!CollectionUtils.isEmpty(orderPOList)) {
            for (OrderPO orderPO : orderPOList) {
                totalAmount = totalAmount.add(orderPO.getOrderAmount());
            }
        }
        return totalAmount;
    }

    /**
     * 商品销售额类别占比
     *
     * @param model
     * @return
     */
    public List<SalesVolumeVO> getSaleCateStatistics(String model) {
        //订单支付完成即计算销售额
        Date date = new Date();
        Date payTimeStart;
        Date payTimeEnd = new Date();
        switch (model) {
            case "year":
                payTimeStart = TimeUtil.getYearAgoDate(date, -1);
                break;
            case "month":
                payTimeStart = TimeUtil.getMonthAgoDate(date, -1);
                break;
            case "week":
                payTimeStart = TimeUtil.getDayAgoDate(date, -7);
                break;
            default:
                throw new IllegalStateException("Unexpected value: " + model);
        }
        //查询已完成的订单
        OrderExample ordersExample = new OrderExample();
        ordersExample.setPayTimeAfter(payTimeStart);
        ordersExample.setPayTimeBefore(payTimeEnd);
        ordersExample.setOrderStateList(OrderStatusEnum.paidStatus());
        List<OrderProductPO> orderProductPOS = orderProductMapper.salesGroupByCategoryId(ordersExample);

        List<SalesVolumeVO> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(orderProductPOS)) {

            List<Integer> goodsCategoryId = new ArrayList<>(orderProductPOS.size());

            //原始数据，key为分类id，value为该分类的总销售额
            BigDecimal totalAmount = BigDecimal.ZERO;//总的销售额，用于计算各分类占比

            for (OrderProductPO orderProductPO : orderProductPOS) {
                totalAmount = totalAmount.add(orderProductPO.getMoneyAmount());
                goodsCategoryId.add(orderProductPO.getGoodsCategoryId());
            }

            String goodsCategoryIds = StringUtils.join(goodsCategoryId, ",");

            //对构造好的原始dataMap 按照销售总额倒序排列,放入newMap
            Map<Integer, BigDecimal> newMap = new LinkedHashMap<>();

            orderProductPOS.stream()
                    .sorted((p1, p2) -> p2.getMoneyAmount().compareTo(p1.getMoneyAmount()))//定义排序规则，倒序
                    .collect(Collectors.toList())
                    .forEach(ele -> newMap.put(ele.getGoodsCategoryId(), ele.getMoneyAmount()));

            GoodsCategoryExample goodsCategoryExample = new GoodsCategoryExample();
            goodsCategoryExample.setCategoryIdIn(goodsCategoryIds);
            List<GoodsCategory> categories = goodsCategoryFeignClient.getGoodsCategoryList(goodsCategoryExample);

            Map<String, String> categoryMap = new HashMap<>();
            for (GoodsCategory category : categories) {
                categoryMap.put(String.valueOf(category.getCategoryId()), category.getCategoryName());
            }

            //循环newMap,构造返回数据
            if (newMap.size() <= 5) {
                //分类少于等于5个，直接记录每个分类的销售额
                for (Map.Entry<Integer, BigDecimal> entry : newMap.entrySet()) {
                    Integer cateId = entry.getKey();
                    BigDecimal moneyAmount = entry.getValue();
                    SalesVolumeVO vo = new SalesVolumeVO();
                    //key为分类id，value为销售额
                    //按照key查询分类名称
                    vo.setName(categoryMap.get(String.valueOf(cateId)));
                    vo.setMoneyAmount(moneyAmount);
                    vo.setPer(totalAmount.multiply(moneyAmount).equals(BigDecimal.ZERO) ? "0%" : moneyAmount.multiply(BigDecimal.valueOf(100))
                            .divide(totalAmount, new MathContext(3, RoundingMode.HALF_UP)) + "%");
                    result.add(vo);
                }
            } else {
                //分类大于5，记录前5个，剩余记为其他
                Iterator<Map.Entry<Integer, BigDecimal>> iterator = newMap.entrySet().iterator();
                int i = 0;
                BigDecimal otherAmount = totalAmount;//其他分类金额
                BigDecimal otherPer = new BigDecimal(100);//其他分类占比
                while (iterator.hasNext() && i < 5) {
                    Map.Entry<Integer, BigDecimal> entry = iterator.next();
                    Integer cateId = entry.getKey();
                    BigDecimal moneyAmount = entry.getValue();

                    SalesVolumeVO vo = new SalesVolumeVO();
                    //key为分类id，value为销售额
                    //按照key查询分类名称
                    vo.setName(categoryMap.get(String.valueOf(cateId)));
                    vo.setMoneyAmount(moneyAmount);
                    BigDecimal per = totalAmount.multiply(moneyAmount).equals(BigDecimal.ZERO) ? BigDecimal.ZERO :
                            moneyAmount.multiply(BigDecimal.valueOf(100)).divide(totalAmount, new MathContext(3, RoundingMode.HALF_UP));
                    vo.setPer(per + "%");
                    result.add(vo);

                    //数据计算
                    otherAmount = otherAmount.subtract(moneyAmount);
                    otherPer = otherPer.subtract(per);
                    i++;
                }
                //其他
                SalesVolumeVO vo = new SalesVolumeVO();
                vo.setName("其他");
                vo.setMoneyAmount(otherAmount);
                vo.setPer(otherPer + "%");
                result.add(vo);
            }
        }
        return result;
    }

    public List<AdminIndexVO.GoodsSaleRankVO> goodsSaleRankV2(OrderExample example) {
        List<OrderSaleInfoDTO> rankListDTO = orderMapper.goodsSaleRank(example);
        List<AdminIndexVO.GoodsSaleRankVO> result = new ArrayList<>(rankListDTO.size());

        if (CollectionUtils.isEmpty(rankListDTO)) {
            return result;
        }

        for (OrderSaleInfoDTO en : rankListDTO) {
            AdminIndexVO.GoodsSaleRankVO vo = new AdminIndexVO.GoodsSaleRankVO();
            vo.setGoodsName(en.getGoodsName());
            vo.setNumber(en.getTotalNum());
            result.add(vo);
        }
        return result;
    }

    /**
     * 商品销量排行
     *
     * @param example
     * @return
     */
    public List<AdminIndexVO.GoodsSaleRankVO> goodsSaleRank(OrderExample example) {
        List<AdminIndexVO.GoodsSaleRankVO> result = new ArrayList<>();
        List<OrderPO> ordersList = orderMapper.listByExample(example);
        if (ordersList != null && ordersList.size() > 0) {
            //原始数据，key为goodsId，value为商品销量 = 销售数量-退货数量
            Map<Long, Integer> dataMap = new HashMap<>();
            ordersList.forEach(order -> {
                //根据订单号查询货品列表
                OrderProductExample productExample = new OrderProductExample();
                productExample.setOrderSn(order.getOrderSn());
                List<OrderProductPO> productList = orderProductMapper.listByExample(productExample);
                productList.forEach(op -> {
                    if (dataMap.containsKey(op.getGoodsId())) {
                        //已经有该商品的数据，累加
                        dataMap.replace(op.getGoodsId(), dataMap.get(op.getGoodsId()) + op.getProductNum() - op.getReturnNumber());
                    } else {
                        //没有该商品的数据，新增
                        dataMap.put(op.getGoodsId(), op.getProductNum() - op.getReturnNumber());
                    }
                });
            });

            //对构造好的原始dataMap 按照商品销量倒序排列,放入newMap
            Map<Long, Integer> newMap = new LinkedHashMap<>();
            if (dataMap.size() > 0) {
                dataMap.entrySet()
                        .stream()
                        .sorted((p1, p2) -> p2.getValue().compareTo(p1.getValue()))//定义排序规则，倒序
                        .collect(Collectors.toList())
                        .forEach(ele -> newMap.put(ele.getKey(), ele.getValue()));
            }

            //取newMap中前20个元素，封装返回数据
            Iterator<Map.Entry<Long, Integer>> iterator = newMap.entrySet().iterator();

            String ids = StringUtils.join(newMap.keySet(), ",");
            GoodsExample goodsExample = new GoodsExample();
            goodsExample.setGoodsIdIn(ids);
            List<Goods> goods = goodsFeignClient.getGoodsList(goodsExample);
            Map<String, String> goodsMap = new HashMap<>();
            for (Goods good : goods) {
                goodsMap.put(String.valueOf(good.getGoodsId()), good.getGoodsName());
            }

            int i = 0;
            while (iterator.hasNext() && i < 20) {
                Map.Entry<Long, Integer> entry = iterator.next();
                AdminIndexVO.GoodsSaleRankVO vo = new AdminIndexVO.GoodsSaleRankVO();
                vo.setGoodsName(goodsMap.get(String.valueOf(entry.getKey())));
                vo.setNumber(entry.getValue());
                result.add(vo);
                i++;
            }
        }
        return result;
    }

    public List<AdminIndexVO.StoreSaleRankVO> storeSaleRankV2(OrderExample example) {
        List<OrderSaleInfoDTO> rankListDTO = orderMapper.storeSaleRank(example);
        List<AdminIndexVO.StoreSaleRankVO> result = new ArrayList<>(rankListDTO.size());

        if (CollectionUtils.isEmpty(rankListDTO)) {
            return result;
        }

        for (OrderSaleInfoDTO en : rankListDTO) {
            if (en == null) {
                continue;
            }
            AdminIndexVO.StoreSaleRankVO vo = new AdminIndexVO.StoreSaleRankVO();
            vo.setStoreName(en.getStoreName());
            vo.setAmount(en.getTotalAmount());
            result.add(vo);
        }
        return result;
    }

    /**
     * 店铺销量排行
     *
     * @param example
     * @return
     */
    public List<AdminIndexVO.StoreSaleRankVO> storeSaleRank(OrderExample example) {
        List<AdminIndexVO.StoreSaleRankVO> result = new ArrayList<>();
        List<OrderPO> ordersList = orderMapper.listByExample(example);
        if (ordersList != null && ordersList.size() > 0) {
            //原始数据，key为storeId，value为销售额 = 订单总金额-退款金额
            Map<Long, BigDecimal> dataMap = new HashMap<>();
            ordersList.forEach(order -> {
                if (dataMap.containsKey(order.getStoreId())) {
                    //已经有该商品的数据，累加
                    dataMap.replace(order.getStoreId(), dataMap.get(order.getStoreId()).add(order.getOrderAmount().subtract(order.getRefundAmount())));
                } else {
                    //没有该商品的数据，新增
                    dataMap.put(order.getStoreId(), order.getOrderAmount().subtract(order.getRefundAmount()));
                }
            });

            //对构造好的原始dataMap 按照销售总额倒序排列,放入newMap
            Map<Long, BigDecimal> newMap = new LinkedHashMap<>();
            if (dataMap.size() > 0) {
                dataMap.entrySet()
                        .stream()
                        .sorted((p1, p2) -> p2.getValue().compareTo(p1.getValue()))//定义排序规则，倒序
                        .collect(Collectors.toList())
                        .forEach(ele -> newMap.put(ele.getKey(), ele.getValue()));
            }

            String ids = StringUtils.join(newMap.keySet(), ",");
            StoreExample storeExample = new StoreExample();
            storeExample.setStoreIdIn(ids);
            List<Store> stores = storeFeignClient.getStoreList(storeExample);
            Map<String, String> storeMap = new HashMap<>();
            for (Store store : stores) {
                storeMap.put(String.valueOf(store.getStoreId()), store.getStoreName());
            }

            //取newMap中前20个元素，封装返回数据
            Iterator<Map.Entry<Long, BigDecimal>> iterator = newMap.entrySet().iterator();
            int i = 0;
            while (iterator.hasNext() && i < 20) {
                Map.Entry<Long, BigDecimal> entry = iterator.next();
                AdminIndexVO.StoreSaleRankVO vo = new AdminIndexVO.StoreSaleRankVO();
                vo.setStoreName(storeMap.get(String.valueOf(entry.getKey())));
                vo.setAmount(entry.getValue());
                result.add(vo);
                i++;
            }
        }
        return result;
    }

    /**
     * 并行封装远程数据 member、shop、goods
     *
     * @param vo
     * @param startTime
     * @param endTime
     * @throws ExecutionException
     * @throws InterruptedException
     */
    public void wrapBoardRemoteData(AdminIndexVO vo, Date startTime, Date endTime) throws ExecutionException, InterruptedException {

        CountDownLatch countDownLatch = new CountDownLatch(7);

        Future<Integer> newMemberTodayFuture = threadPoolTaskExecutor.submit(
                new NewMemberTodayFuture(countDownLatch, startTime, endTime));

        Future<List<MemberDayDTO>> newMemberWeeklyFuture = threadPoolTaskExecutor.submit(
                new NewMemberWeeklyFuture(countDownLatch, startTime, endTime));

        Future<Integer> totalMemberFuture = threadPoolTaskExecutor.submit(
                new TotalMemberFuture(countDownLatch));

        Future<Integer> newStoreTodayFuture = threadPoolTaskExecutor.submit(
                new NewStoreTodayFuture(countDownLatch, startTime, endTime));

        Future<Integer> totalStoreFuture = threadPoolTaskExecutor.submit(
                new TotalStoreFuture(countDownLatch));

        Future<Integer> newGoodsTodayFuture = threadPoolTaskExecutor.submit(
                new NewGoodsTodayFuture(countDownLatch, startTime, endTime));

        Future<Integer> totalGoodsFuture = threadPoolTaskExecutor.submit(
                new TotalGoodsFuture(countDownLatch));

        countDownLatch.await();

        // 新增会员数
        vo.setNewMemberNum(newMemberTodayFuture.get());
        // 一周新增会员情况
        vo.setMemberWeeklyReport(getMemberWeeklyReport(newMemberWeeklyFuture.get()));
        // 会员总数
        vo.setMemberTotal(totalMemberFuture.get());
        // 新增店铺数
        vo.setNewStoreNum(newStoreTodayFuture.get());
        // 店铺总数
        vo.setStoreTotal(totalStoreFuture.get());
        // 新增商品数
        vo.setNewGoodsNum(newGoodsTodayFuture.get());
        // 商品总数
        vo.setGoodsTotal(totalGoodsFuture.get());
    }

    public List<AdminIndexVO.MemberReportVO> getMemberWeeklyReport(List<MemberDayDTO> result) {
        List<AdminIndexVO.MemberReportVO> list = new ArrayList<>(7);
        if (CollectionUtils.isEmpty(result)) {
            for (int i = 0; i < 7; i++) {
                AdminIndexVO.MemberReportVO vo = new AdminIndexVO.MemberReportVO();
                vo.setDay(TimeUtil.getYesterdayOfWeek(i - 6).toString());
                vo.setNumber(0);
                list.add(vo);
            }
        } else {
            for (int i = 0; i < 7; i++) {
                String yesterday = TimeUtil.getYesterday(i - 6);
                AdminIndexVO.MemberReportVO vo = new AdminIndexVO.MemberReportVO();
                vo.setDay(TimeUtil.getYesterdayOfWeek(i - 6).toString());
                vo.setNumber(0);
                for (MemberDayDTO o : result) {
                    if (o.getDay().equals(yesterday)) {
                        vo.setNumber(o.getNumber());
                        break;
                    }
                }
                list.add(vo);
            }
        }
        return list;
    }

    /**
     * 今日新增会员数 并行线程
     *
     * @Author: maoliang
     * @Date:2022-08-23
     */
    class NewMemberTodayFuture implements Callable<Integer> {

        CountDownLatch countDownLatch;
        Date startTime;
        Date endTime;

        NewMemberTodayFuture(CountDownLatch countDownLatch,
                             Date startTime,
                             Date endTime) {
            log.info("============ init thread NewMemberTodayFuture:" + Thread.currentThread().getName());

            this.countDownLatch = countDownLatch;
            this.startTime = startTime;
            this.endTime = endTime;
        }

        @Override
        public Integer call() throws Exception {
            log.info("============ run thread NewMemberTodayFuture:" + Thread.currentThread().getName());
            Thread.currentThread().setPriority(Thread.MAX_PRIORITY);
            Integer result = 0;
            try {
                //今日新增会员数
                MemberExample example = new MemberExample();
                example.setRegisterTimeAfter(startTime);
                example.setRegisterTimeBefore(endTime);
                result = memberFeignClient.getMemberCount(example);
            } finally {
                countDownLatch.countDown();
            }
            return result;
        }
    }

    /**
     * 一周会员数变化 并行线程
     *
     * @Author: maoliang
     * @Date:2022-08-23
     */
    class NewMemberWeeklyFuture implements Callable<List<MemberDayDTO>> {

        CountDownLatch countDownLatch;
        Date startTime;
        Date endTime;

        NewMemberWeeklyFuture(CountDownLatch countDownLatch,
                              Date startTime,
                              Date endTime) {
            this.countDownLatch = countDownLatch;
            this.startTime = startTime;
            this.endTime = endTime;
        }

        @Override
        public List<MemberDayDTO> call() throws Exception {
            Thread.currentThread().setPriority(Thread.MAX_PRIORITY);
            List<MemberDayDTO> result = new ArrayList<>();
            try {
                //今日新增会员数
                MemberExample memberExample = new MemberExample();
                memberExample.setRegisterTimeAfter(TimeUtil.getDayAgoDate(new Date(), -6));
                memberExample.setRegisterTimeBefore(TimeUtil.getDayAgoDate(new Date(), 0));
                result = memberFeignClient.getMemberDayDto(memberExample);
            } finally {
                countDownLatch.countDown();
            }
            return result;
        }
    }

    /**
     * 会员总数 并行线程
     *
     * @Author: maoliang
     * @Date:2022-08-23
     */
    class TotalMemberFuture implements Callable<Integer> {

        CountDownLatch countDownLatch;

        TotalMemberFuture(CountDownLatch countDownLatch) {
            this.countDownLatch = countDownLatch;
        }

        @Override
        public Integer call() throws Exception {
            Thread.currentThread().setPriority(Thread.MAX_PRIORITY);
            Integer result = 0;
            try {
                result = memberFeignClient.getMemberCount(new MemberExample());
            } finally {
                countDownLatch.countDown();
            }
            return result;
        }
    }

    /**
     * 新增店铺数 并行线程
     *
     * @Author: maoliang
     * @Date:2022-08-23
     */
    class NewStoreTodayFuture implements Callable<Integer> {

        CountDownLatch countDownLatch;
        Date startTime;
        Date endTime;

        NewStoreTodayFuture(CountDownLatch countDownLatch,
                            Date startTime,
                            Date endTime) {
            this.countDownLatch = countDownLatch;
            this.startTime = startTime;
            this.endTime = endTime;
        }

        @Override
        public Integer call() throws Exception {
            Thread.currentThread().setPriority(Thread.MAX_PRIORITY);
            Integer result = 0;
            try {
                StoreExample storeExample = new StoreExample();
                storeExample.setCreateTimeAfter(startTime);
                storeExample.setCreateTimeBefore(endTime);
                result = storeFeignClient.getStoreCount(storeExample);
            } finally {
                countDownLatch.countDown();
            }
            return result;
        }
    }

    /**
     * 店铺总数 并行线程
     *
     * @Author: maoliang
     * @Date:2022-08-23
     */
    class TotalStoreFuture implements Callable<Integer> {

        CountDownLatch countDownLatch;

        TotalStoreFuture(CountDownLatch countDownLatch) {
            this.countDownLatch = countDownLatch;
        }

        @Override
        public Integer call() throws Exception {
            Thread.currentThread().setPriority(Thread.MAX_PRIORITY);
            Integer result = 0;
            try {
                result = storeFeignClient.getStoreCount(new StoreExample());
            } finally {
                countDownLatch.countDown();
            }
            return result;
        }
    }

    /**
     * 新增商品数 并行线程
     *
     * @Author: maoliang
     * @Date:2022-08-23
     */
    class NewGoodsTodayFuture implements Callable<Integer> {

        CountDownLatch countDownLatch;
        Date startTime;
        Date endTime;

        NewGoodsTodayFuture(CountDownLatch countDownLatch,
                            Date startTime,
                            Date endTime) {
            this.countDownLatch = countDownLatch;
            this.startTime = startTime;
            this.endTime = endTime;
        }

        @Override
        public Integer call() throws Exception {
            Thread.currentThread().setPriority(Thread.MAX_PRIORITY);
            Integer result = 0;
            try {
                GoodsExample goodsExample = new GoodsExample();
                goodsExample.setCreateTimeAfter(startTime);
                goodsExample.setCreateTimeBefore(endTime);
                result = goodsFeignClient.getGoodsCount(goodsExample);
            } finally {
                countDownLatch.countDown();
            }
            return result;
        }
    }

    /**
     * 商品总数 并行线程
     *
     * @Author: maoliang
     * @Date:2022-08-23
     */
    class TotalGoodsFuture implements Callable<Integer> {

        CountDownLatch countDownLatch;

        TotalGoodsFuture(CountDownLatch countDownLatch) {
            this.countDownLatch = countDownLatch;
        }

        @Override
        public Integer call() throws Exception {
            Thread.currentThread().setPriority(Thread.MAX_PRIORITY);
            Integer result = 0;
            try {
                result = goodsFeignClient.getGoodsCount(new GoodsExample());
            } finally {
                countDownLatch.countDown();
            }
            return result;
        }
    }

}
