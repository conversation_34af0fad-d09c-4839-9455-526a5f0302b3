package com.cfpamf.ms.mallorder.integration.wms;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cfpamf.framework.autoconfigure.common.result.Result;
import com.cfpamf.ms.mallorder.integration.facade.WmsFacade;
import com.cfpamf.ms.mallorder.integration.facade.dto.MallDeliverQtyQuery;
import com.cfpamf.ms.mallorder.integration.facade.dto.SiteQuery;
import com.cfpamf.ms.mallorder.integration.facade.dto.SiteVo;
import com.cfpamf.ms.mallorder.integration.wms.contest.WmsErrorCodeEnum;
import com.cfpamf.ms.mallorder.integration.wms.dto.JingDongInterceptDTO;
import com.cfpamf.ms.mallorder.integration.wms.dto.JingDongInterceptItemDTO;
import com.slodon.bbc.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;

@Service
@Slf4j
public class WmsIntegration {
	@Autowired
	private WmsFacade wmsFacade;

	/**
	 * 传电商店铺ID：在WMS存在，即该店铺需要拼车。不存在，返回判断
	 * 传单位编码、分支：查询商品总发货数量
	 *
	 * @return
	 */
	public Long getDeliverTotalQty(String skuId, String branch, Long storeId, String deliveryTimeBeg, String deliveryTimeEnd) {
		MallDeliverQtyQuery query = new MallDeliverQtyQuery();
		query.setSkuId(skuId);
		query.setBranchCode(branch);
		query.setOutMerchantId(storeId);
		query.setDeliveryTimeBeg(deliveryTimeBeg);
		query.setDeliveryTimeEnd(deliveryTimeEnd);

		Result<Long> deliverTotalQty;
		try {
			deliverTotalQty = wmsFacade.getDeliverTotalQty(query);
			log.info("connect wms-service getDeliverTotalQty,query:{},result:{}", JSONObject.toJSONString(query), deliverTotalQty);
		} catch (Exception e) {
			throw new BusinessException("调用wms服务异常，请检查～");
		}

		if (deliverTotalQty == null) {
			throw new BusinessException("调用wms服务返回null，请检查～");
		}

		if (WmsErrorCodeEnum.MERCHANT_NOT_EXIST.getCode().equals(deliverTotalQty.getCode())) {
			// 商户不存在，返回null
			return null;
		}

		if (!deliverTotalQty.isSuccess() || deliverTotalQty.getData() == null) {
			throw new BusinessException("调用wms服务返回失败，请检查～");
		}

		return deliverTotalQty.getData();
	}

	public Page<SiteVo> getSiteByPage(SiteQuery siteQuery) {
		Result<Page<SiteVo>> pageResult = null;
		try {
			pageResult = wmsFacade.pageList(siteQuery);
			log.info("connect wms-service getSiteByPage,query:{},result:{}", JSONObject.toJSONString(siteQuery), pageResult);
		} catch (Exception e) {
			throw new BusinessException("调用wms服务异常，请检查～");
		}

		if (pageResult == null) {
			throw new BusinessException("调用wms服务返回null，请检查～");
		}
		return pageResult.getData();

	}


	public boolean jingDongInterceptCheck(String afsSn,String orderSn,Integer returnNum,String skuId,String skuUnitCode) {
		Result<Boolean> result;
		try {
			JingDongInterceptDTO query = new JingDongInterceptDTO();
			query.setSystemSource("norm_mall");
			query.setDeliverBusinessNo(orderSn);
			query.setInterceptBusinessNo(afsSn);
			JingDongInterceptItemDTO jingDongInterceptItemDTO = new JingDongInterceptItemDTO();
			jingDongInterceptItemDTO.setReturnNum(returnNum);
			jingDongInterceptItemDTO.setSkuId(skuId);
			jingDongInterceptItemDTO.setSkuUnitCode(skuUnitCode);
			query.setJingDongInterceptItemDTOList(Arrays.asList(jingDongInterceptItemDTO));
			result = wmsFacade.jingDongInterceptCheck(query);
			log.info("connect wms-service jingDongInterceptCheck,query:{},result:{}", JSONObject.toJSONString(query), result);
		} catch (Exception e) {
			throw new BusinessException("调用wms服务异常，请检查～");
		}
		if (result == null) {
			throw new BusinessException("调用wms服务返回null，请检查～");
		}
		return result.getData();
	}

	public Integer jingDongIntercept(String afsSn,String orderSn,Integer returnNum,String skuId,String skuUnitCode,String operator) {
		Result<Integer> result;
		try {
			JingDongInterceptDTO query = new JingDongInterceptDTO();
			query.setSystemSource("norm_mall");
			query.setDeliverBusinessNo(orderSn);
			query.setInterceptBusinessNo(afsSn);
			query.setOperate(operator);
			JingDongInterceptItemDTO jingDongInterceptItemDTO = new JingDongInterceptItemDTO();
			jingDongInterceptItemDTO.setReturnNum(returnNum);
			jingDongInterceptItemDTO.setSkuId(skuId);
			jingDongInterceptItemDTO.setSkuUnitCode(skuUnitCode);
			query.setJingDongInterceptItemDTOList(Arrays.asList(jingDongInterceptItemDTO));
			result = wmsFacade.jingDongIntercept(query);
			log.info("connect wms-service jingDongInterceptCheck,query:{},result:{}", JSONObject.toJSONString(query), result);
		} catch (Exception e) {
			throw new BusinessException("调用wms服务异常，请检查～");
		}
		if (result == null) {
			throw new BusinessException("调用wms服务返回null，请检查～");
		}
		if (!result.isSuccess()) {
			throw new BusinessException(result.getErrorCode());
		}
		return result.getData();
	}
}
