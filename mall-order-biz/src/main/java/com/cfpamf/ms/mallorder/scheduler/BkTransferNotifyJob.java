package com.cfpamf.ms.mallorder.scheduler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cfpamf.ms.mallorder.common.enums.TransferStatusEnum;
import com.cfpamf.ms.mallorder.model.BankTransferModel;
import com.cfpamf.ms.mallorder.po.BzBankTransferPO;
import com.cfpamf.ms.mallorder.service.IBzBankTransferService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Slf4j
@Component
public class BkTransferNotifyJob {

    public static final String DATE_FORMATTER_PATTERN = "yyyyMMdd";
    @Autowired
    private BankTransferModel bankTransferModel;
    @Autowired
    private IBzBankTransferService bzBankTransferService;

    @XxlJob("bktExpireCaution")
    public ReturnT<String> bktExpireCaution() {
        String param = XxlJobHelper.getJobParam();
        LocalDate thatDay = LocalDate.now();
        if (StringUtils.isNotBlank(param)) {
            try {
                thatDay = LocalDate.parse(param, DateTimeFormatter.ofPattern(DATE_FORMATTER_PATTERN));
            } catch (Exception ex) {
                log.info("入参格式非[yyyyMMdd],将默认为今天");
                thatDay = LocalDate.now();
            }
        }
        LocalDateTime endDatetime = thatDay.atTime(8, 0, 0);
        LocalDateTime startDatetime = thatDay.minusDays(NumberUtils.LONG_ONE).atTime(8, 0, 0);
        
        log.info("执行银行卡汇款快过期订单提醒的定时任务,业务截止日:{}", endDatetime);
        LambdaQueryWrapper<BzBankTransferPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.lt(BzBankTransferPO::getExpireTime,endDatetime);
        queryWrapper.ge(BzBankTransferPO::getExpireTime,startDatetime);
        queryWrapper.eq(BzBankTransferPO::getTransferState, TransferStatusEnum.DEAL_TRANSFER.getValue());
        List<BzBankTransferPO> entityList = bzBankTransferService.list(queryWrapper);
        if (CollectionUtils.isEmpty(entityList)) {
            return ReturnT.SUCCESS;
        }
        bankTransferModel.expireCaution(entityList);
        return ReturnT.SUCCESS;
    }
}
