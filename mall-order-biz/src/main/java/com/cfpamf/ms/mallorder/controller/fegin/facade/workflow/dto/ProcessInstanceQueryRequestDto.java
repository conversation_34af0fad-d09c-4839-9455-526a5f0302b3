package com.cfpamf.ms.mallorder.controller.fegin.facade.workflow.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

@Getter
@Setter
public class ProcessInstanceQueryRequestDto {
    @ApiModelProperty("页码")
    @Min(value = 1, message = "pageIndex 必须从1开始")
    private Integer pageIndex = 1;

    @ApiModelProperty("每页大小,单页默认最大限制2000")
    @Min(value = 1, message = "pageSize 必须大于1")
    @Max(value = 2000, message = "pageSize 必须小于2000")
    private Integer pageSize = 10;
}
