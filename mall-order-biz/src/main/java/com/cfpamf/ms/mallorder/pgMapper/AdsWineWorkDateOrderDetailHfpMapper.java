package com.cfpamf.ms.mallorder.pgMapper;

import com.cfpamf.framework.autoconfigure.mybatis.MyBaseMapper;
import com.cfpamf.ms.mallorder.po.pgrpt.AdsWineWorkDateOrderDetailHfpPO;
import com.cfpamf.ms.mallorder.req.BeverageWorkReportRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 转账记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-17
 */
public interface AdsWineWorkDateOrderDetailHfpMapper extends MyBaseMapper<AdsWineWorkDateOrderDetailHfpPO> {

    List<AdsWineWorkDateOrderDetailHfpPO> getList(@Param("request") BeverageWorkReportRequest request);
}
