package com.cfpamf.ms.mallorder.service;

import com.cfpamf.ms.mallorder.po.BzOldUserPoolPO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 订单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-16
 */
public interface IBzOldUserPoolService extends IService<BzOldUserPoolPO> {

    /**
     * 根据用户编号判断是否贷款首单,true为首单，false为老用户
     * @param userNo
     * @return
     */
    Boolean isFirstLoanOrderByUserNo(String userNo);


    /**
     * 根据订单号恢复首单
     * @param orderSn
     * @return
     */
    Boolean restoreFirstLoanOrder(String orderSn);


    /**
     * 支付成功（保存申请单成功）置为老用户池
     * @param userNo
     * @param orderSn
     * @return
     */
    Boolean insertFirstLoanOrder(String userNo, String orderSn);

}
