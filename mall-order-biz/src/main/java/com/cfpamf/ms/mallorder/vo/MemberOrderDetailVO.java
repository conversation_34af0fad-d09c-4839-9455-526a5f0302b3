package com.cfpamf.ms.mallorder.vo;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.mallmember.po.MemberInvoice;
import com.cfpamf.ms.mallorder.common.enums.PayMethodEnum;
import com.cfpamf.ms.mallorder.common.enums.ReceiveConfirmDocType;
import com.cfpamf.ms.mallorder.constant.DomainUrlConstant;
import com.cfpamf.ms.mallorder.dto.OrderMaterialDTO;
import com.cfpamf.ms.mallorder.integration.filecenter.SceneTypeEnum;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.vo.basevo.OrderDetailBaseVO;
import com.cfpamf.ms.mallpromotion.vo.LadderGroupOrderExtendVO;
import com.cfpamf.ms.mallpromotion.vo.PresellOrderExtendVO;
import com.slodon.bbc.core.constant.LadderGroupConst;
import com.slodon.bbc.core.constant.OrderConst;
import com.slodon.bbc.core.constant.PromotionConst;
import com.slodon.bbc.core.i18n.Language;
import com.slodon.bbc.core.util.StringUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 */
@Data
@Slf4j
public class MemberOrderDetailVO extends OrderDetailBaseVO {

    @ApiModelProperty("店铺ID")
    private Long storeId;

    @ApiModelProperty("店铺名称")
    private String storeName;

    @ApiModelProperty("订单id")
    private Integer orderId;

    @ApiModelProperty("订单号")
    private String orderSn;

    @ApiModelProperty("支付单号")
    private String paySn;

    @ApiModelProperty("父订单号，无需拆单时，父订单号=订单号")
    private String parentSn;

    @ApiModelProperty("支付方式名称，参考OrderPaymentConst类")
    private String paymentName;

    @ApiModelProperty("支付方式code, 参考OrderPaymentConst类")
    private String paymentCode;

    @ApiModelProperty("会员名称")
    private String memberName;

    @ApiModelProperty("用户userNo")
    private String userNo;

    @ApiModelProperty("收货人")
    private String receiverName;

    @ApiModelProperty("省市区组合")
    private String receiverAreaInfo;

    @ApiModelProperty("收货地址")
    private String receiverAddress;

    @ApiModelProperty("收货省")
    private String receiverProvince;

    @ApiModelProperty("收货市")
    private String receiverCity;

    @ApiModelProperty("收货区")
    private String receiverDistrict;

    @ApiModelProperty("收货县/镇")
    private String receiverTown;

    @ApiModelProperty("收货人手机号")
    private String receiverMobile;

    @ApiModelProperty("自提点名称")
    private String pointName;

    @ApiModelProperty("订单创建时间")
    private Date createTime;

    @ApiModelProperty("支付成功时间")
    private Date payTime;

    @ApiModelProperty(value = "支付倒计时间 (秒)")
    private Long payTimeLimit;

    @ApiModelProperty("订单完成时间")
    private Date finishTime;

    @ApiModelProperty("订单状态：0-已取消；5-待支付订金；10-未付款订单；20-已付款；30-已发货；40-已完成;50-已关闭")
    private Integer orderState;

    @ApiModelProperty("订单状态值：0-已取消；5-待支付订金；10-未付款订单；20-已付款；30-已发货；40-已完成;50-已关闭")
    private String orderStateValue;

    @ApiModelProperty("订单子状态：101-待付定金；102-待付尾款；103-已完成付款")
    private Integer orderSubState = 0;

    @ApiModelProperty("锁定状态:0是正常,大于0是锁定,默认是0")
    private Integer lockState;

    @ApiModelProperty("商品总金额")
    private BigDecimal totalMoney;

    @ApiModelProperty("实付款")
    private BigDecimal actualPayment;

    @ApiModelProperty("总运费")
    private BigDecimal totalExpress;

    @ApiModelProperty("商品总金额")
    private BigDecimal goodsAmount;

    @ApiModelProperty("活动优惠总金额 （= 店铺优惠券 + 平台优惠券 + 活动优惠【店铺活动 + 平台活动】 + 积分抵扣金额）")
    private BigDecimal activityDiscountAmount;

    @ApiModelProperty("取消原因")
    private String refuseReason;

    @ApiModelProperty("取消备注")
    private String refuseRemark;

    @ApiModelProperty("会员邮箱")
    private String memberEmail;

    @ApiModelProperty("订单备注")
    private String orderRemark;

    @ApiModelProperty("优惠券面额")
    private BigDecimal voucherPrice;

    @ApiModelProperty("发货类型：0-物流发货，1-无需物流")
    private Integer deliverType;

    @ApiModelProperty("发货人")
    private String deliverName;

    @ApiModelProperty("发货人电话")
    private String deliverMobile;

    @ApiModelProperty("自动收货时间，=商家发货时间+后台设置的自动收货时间")
    private Date autoReceiveTime;

    @ApiModelProperty("已经延长收货的次数")
    private Integer delayTimes;

    @ApiModelProperty("结算模式(standard:标准结算,borrow:预支结算)")
    private String settleMode;

    @ApiModelProperty("CAPP用户是否可签收(true:可签收,false:不可签收)")
    private Boolean receiveAbleToCApp;

    @ApiModelProperty(value = "是否显示延长收货时间按钮")
    private Boolean showExtendAutoReceiveTimeBtn;

    @ApiModelProperty("支付状态:：0默认未支付,1已支付")
    private String payState;

    @ApiModelProperty("支付状态值:：0默认未支付,1已支付")
    private String payStateValue;

    @ApiModelProperty(value = "支付扩展信息")
    private JSONObject payWayExtraInfo;

    @ApiModelProperty("是否评价:1.未评价,2.部分评价,3.全部评价")
    private Integer evaluateState;

    @ApiModelProperty("发票信息")
    private MemberInvoice invoice;

    @ApiModelProperty("订单类型：1-普通订单")
    private Integer orderType;

    @ApiModelProperty("订单类型：1-普通订单；其他直接存活动类型（具体类型查看ActivityConst）")
    private String orderTypeValue;

    @ApiModelProperty("商家优惠券优惠金额")
    private BigDecimal storeVoucherAmount;

    @ApiModelProperty("平台优惠券优惠金额")
    private BigDecimal platformVoucherAmount;

    @ApiModelProperty("满优惠金额")
    private BigDecimal fullDiscountAmount;

    @ApiModelProperty("是否多店铺")
    private Boolean isManyStore = false;

    @ApiModelProperty("剩余时间（秒）")
    private long remainTime;

    @ApiModelProperty(value = "下单操作渠道：H5-浏览器H5；APP-乡助APP；WE_CHAT-微信浏览器；MINI_PRO-小程序；OMS-运管物资")
    private String channel;

    @ApiModelProperty("分支编号")
    private String branch;

    @ApiModelProperty("订单日志列表")
    private List<OrderLogPO> orderLogs;

    @ApiModelProperty("子订单列表")
    private List<ChildOrdersVO> childOrdersVOS;

    @ApiModelProperty("预售信息")
    private PresellDetailInfo presellInfo;

    @ApiModelProperty("阶梯团信息")
    private LadderGroupDetailInfo ladderGroupDetailInfo;

    @ApiModelProperty("乡助卡优惠金额")
    private BigDecimal xzCardAmount;

    @ApiModelProperty("乡助卡运费优惠金额")
    private BigDecimal xzCardExpressFeeAmount;

    @ApiModelProperty("订单佣金")
    private BigDecimal commission;

    @ApiModelProperty("是否能修改地址 0-否 1-是")
    private Integer isUpdate = 1;

    @ApiModelProperty("每种活动promotionType对应的支付详情")
    private Object promotionPayDetail;

    @ApiModelProperty("订单模式：1-B端采购中心，2-C端店铺街")
    private Integer orderPattern;

    @ApiModelProperty("展示银行卡汇款信息")
    private Boolean showBankTransfer;

    @ApiModelProperty("大额订单的过期时间")
    private String expireDatetime;

    @ApiModelProperty(value = "付款账号")
    private String payerAccount;

    @ApiModelProperty(value = "付款人姓名")
    private String payerName;

    @ApiModelProperty(value = "收款账号")
    private String receiptAccount;

    @ApiModelProperty(value = "收款人姓名")
    private String receiptName;

    @ApiModelProperty(value = "收款银行名称")
    private String receiptBankName;

    @ApiModelProperty(value = "安装人员信息")
    private List<InstallerVO> installerList;

    @ApiModelProperty("履约模式，0-常规，1-供应商,2-服务到家")
    private List<Integer> performanceModeList;

    @ApiModelProperty("客户确认: 0无需确认、1草稿、2待确认、3已确认、4已拒绝")
    private Integer customerConfirmStatus;

    @ApiModelProperty("客户确认描述: 0无需确认、1草稿、2待确认、3已确认、4已拒绝")
    private String customerConfirmStatusDesc;

    @ApiModelProperty("下单用户角色:1、本人、2、客户经理")
    private Integer orderPlaceUserRoleCode;

    @ApiModelProperty("下单用户角色:1、本人、2、客户经理")
    private String orderPlaceUserRoleDesc;

    @ApiModelProperty("换货标识：0=普通订单；1=被换货的订单 2=换货后新生成的订单")
    private Integer exchangeFlag;

    @ApiModelProperty(value = "经销商编码")
    private String dealerCode;

    @ApiModelProperty("发货资料集合")
    private List<OrderMaterialDTO> deliverMaterialImageUrlList;

    @ApiModelProperty("收货资料集合")
    private List<OrderMaterialDTO> receiveMaterialImageUrlList;

    @ApiModelProperty("订单资料集合：发货资料、收货资料、贷款类资料")
    private Map<SceneTypeEnum, List<OrderMaterialDTO>> orderMaterialMap;

    @ApiModelProperty("订单各场景凭证资料集合")
    private List<MallFileScenesProofVO> fileScenesProofVOS;

    @ApiModelProperty("是否贷款类支付")
    private Boolean isLoanPay;

    @ApiModelProperty("订单相关资料的场景编号")
    private String sceneNo;

    @ApiModelProperty("客户姓名")
    private String customerName;

    @ApiModelProperty(value = "金蝶库存管理标识(1-按店铺,2-按分支,3-按仓库)")
    private Integer kingdeeStockPushMode;

    @ApiModelProperty(value = "发货仓库")
    private String deliveryWarehouse;

    @ApiModelProperty(value = "发货仓库名称")
    private String deliveryWarehouseName;

    @ApiModelProperty(value = "组合货品ID")
    private Long combinationProductId;

    @ApiModelProperty(value = "组合商品id")
    private Long combinationGoodsId;

    @ApiModelProperty(value = "组合商品名称")
    private String combinationGoodsName;

    @ApiModelProperty(value = "组合商品图片")
    private String combinationImage;

    @ApiModelProperty(value = "组合商品购买数量")
    private Integer combinationBuyNum;

    @ApiModelProperty(value = "是否展示获取签收码按钮")
    private Boolean receiveCodeButtonFlag;

    @ApiModelProperty("订单签收码")
    private String receiveCode;

    @ApiModelProperty(value = "是否为电签")
    private Boolean isEleSign;

    @ApiModelProperty("用户手机号")
    private String userMobile;

    @ApiModelProperty(value = "是否有发票")
    private Boolean existInvoice;

    @ApiModelProperty(value = "是否能开发票")
    private Boolean invoiceAllow;

    @ApiModelProperty("是否展示自提点退货退款轨迹")
    private Boolean selfLiftReturnTrack = false;


    public MemberOrderDetailVO(OrderPO orderPO, OrderExtendPO orderExtendPO, OrderPayPO orderPayPO, BzOrderProductCombinationPO productCombinationPO) {

        super(orderPO, orderExtendPO);
        if (ObjectUtil.isNotNull(productCombinationPO)){
            combinationProductId = productCombinationPO.getProductId();
            combinationGoodsId = productCombinationPO.getGoodsId();
            combinationGoodsName = productCombinationPO.getGoodsName();
            combinationImage = productCombinationPO.getMainImage();
            combinationBuyNum = productCombinationPO.getBuyNum();
        }
        orderPattern = orderPO.getOrderPattern();
        storeId = orderPO.getStoreId();
        storeName = orderPO.getStoreName();
        orderId = orderPO.getOrderId();
        orderSn = orderPO.getOrderSn();
        paySn = orderPO.getPaySn();
        userMobile = orderPO.getUserMobile();
        parentSn = orderPO.getParentSn();
        paymentName = orderPO.getPaymentName();
        paymentCode = orderPO.getPaymentCode();
        memberName = orderPO.getMemberName();
        this.userNo = orderPO.getUserNo();
        branch = orderExtendPO.getBranch();
        receiverName = orderExtendPO.getReceiverName();
        receiverAreaInfo = orderExtendPO.getReceiverAreaInfo();
        receiverAddress = orderExtendPO.getReceiverAddress();
        receiverProvince = orderExtendPO.getReceiverProvinceCode();
        receiverCity = orderExtendPO.getReceiverCityCode();
        receiverDistrict = orderExtendPO.getReceiverDistrictCode();
        receiverTown = orderExtendPO.getReceiverTownCode();
        receiverMobile = dealMemberMobile(orderExtendPO.getReceiverMobile());
        pointName = orderExtendPO.getPointName();
        customerName = orderExtendPO.getCustomerName();
        createTime = orderPO.getCreateTime();
        payTime = orderPO.getPayTime();
        finishTime = orderPO.getFinishTime();
        orderState = orderPO.getOrderState();
        orderStateValue = MemberOrderListVO.getRealOrderStateValue(orderState, 0, 0L, orderPO.getOrderType());
        lockState = orderPO.getLockState();
//        payAmount = order.getPayAmount();
//        balanceAmount = order.getBalanceAmount();
//        integralCashAmount = order.getIntegralCashAmount();
        refuseReason = orderPO.getRefuseReason();
        refuseRemark = orderPO.getRefuseRemark();
        channel = orderPO.getChannel();
        orderRemark = orderExtendPO.getOrderRemark();
        voucherPrice = orderExtendPO.getVoucherPrice();
        payState = orderPayPO.getApiPayState();
        payStateValue = getRealPayStateValue(payState);
        evaluateState = orderPO.getEvaluateState();
        invoice = StringUtils.isEmpty(orderExtendPO.getInvoiceInfo()) ? null : JSONObject.parseObject(orderExtendPO.getInvoiceInfo(), MemberInvoice.class);
        orderType = orderPO.getOrderType();
        storeVoucherAmount = orderExtendPO.getStoreVoucherAmount();
        platformVoucherAmount = orderExtendPO.getPlatformVoucherAmount();
        fullDiscountAmount = dealFullDiscountAmount(orderPO.getActivityDiscountDetail());
        xzCardAmount = orderPO.getXzCardAmount();
        xzCardExpressFeeAmount = orderPO.getXzCardExpressFeeAmount();
        goodsAmount = orderPO.getGoodsAmount();
        commission = orderPO.getOrderCommission().add(orderPO.getBusinessCommission());
        delayTimes = orderPO.getDelayTimes();
        settleMode = orderPO.getSettleMode();
        customerConfirmStatus = orderPO.getCustomerConfirmStatus();
        customerConfirmStatusDesc = orderPO.getCustomerConfirmStatusDesc();
        orderPlaceUserRoleCode = orderPO.getOrderPlaceUserRoleCode();
        orderPlaceUserRoleDesc = orderPO.getOrderPlaceUserRoleDesc();
        try {
            //履约模式
            this.performanceModeList = JSONObject.parseArray(orderPO.getPerformanceModes(), Integer.class);
        }catch (Exception e) {
            log.error("履约模式 orderInfo.getPerformanceModes() = {}" ,orderPO.getPerformanceModes() );
            log.error(JSONObject.toJSONString(e));
        }
        exchangeFlag = orderPO.getExchangeFlag();
        dealerCode = orderPO.getDealerCode();
        isLoanPay = PayMethodEnum.isLoanPay(orderPO.getPaymentCode());
        kingdeeStockPushMode = orderExtendPO.getKingdeeStockPushMode();
        receiveCode = orderExtendPO.getReceiveCode();
        Integer receiveConfirmDocType = orderExtendPO.getReceiveConfirmDocType();
        if (ReceiveConfirmDocType.ELE_SIGN.getCode().equals(receiveConfirmDocType)){
            isEleSign = true;
        }else{
            isEleSign = false;
        }
        // 默认不包含发票
        existInvoice = false;
    }

    public static String getRealPayStateValue(String payState) {
        String value = null;
        if (StringUtils.isEmpty(payState)) {
            return Language.translate("未知");
        }
        switch (payState) {
            case OrderConst.API_PAY_STATE_0:
                value = "未支付";
                break;
            case OrderConst.API_PAY_STATE_1:
                value = "已支付";
                break;
            default:
                break;
        }
        //翻译
        value = Language.translate(value);
        return value;
    }

    /**
     * 处理会员手机号，隐藏中间四位
     *
     * @return
     */
    private static String dealMemberMobile(String memberMobile) {
        if (StringUtils.isEmpty(memberMobile)) {
            return memberMobile;
        }
        List<String> hiddenHosts = Arrays.asList("https://jbbcadmin.slodon.cn", "http://jbbcs-admin.slodon.cn");
        if (hiddenHosts.contains(DomainUrlConstant.SLD_ADMIN_URL)) {
            return memberMobile.replaceFirst("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
        }
        return memberMobile;
    }

    public BigDecimal dealFullDiscountAmount(String activityDiscountDetail) {
        BigDecimal fullDiscountAmount = new BigDecimal("0.00");
        if (StringUtil.isEmpty(activityDiscountDetail)) {
            return fullDiscountAmount;
        }
        //只解析属于满优惠的信息（满优惠类型：201-204）
        JSONArray jsonArray = JSONObject.parseArray(activityDiscountDetail);
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            int promotionType = Integer.parseInt(jsonObject.getString("promotionType"));
            if (promotionType >= PromotionConst.PROMOTION_TYPE_201 && promotionType <= PromotionConst.PROMOTION_TYPE_204) {
                BigDecimal discount = new BigDecimal(jsonObject.getString("discount"));
                fullDiscountAmount = fullDiscountAmount.add(discount);
            }
        }
        return fullDiscountAmount;
    }

    public BigDecimal getActivityDiscountAmount() {
        BigDecimal discount = new BigDecimal("0.00");
        if (CollectionUtils.isEmpty(childOrdersVOS)) {
            return discount;
        }
        for (ChildOrdersVO childOrdersVO : childOrdersVOS) {
            discount = discount.add(childOrdersVO.getActivityDiscountAmount());
        }
        return discount;
    }
    @Data
    public static class PresellDetailInfo {
        @ApiModelProperty("订单状态：101-待付定金；102-待付尾款；103-已付全款")
        private Integer orderSubState;

        @ApiModelProperty("预售价格")
        private BigDecimal presellPrice;

        @ApiModelProperty("定金可以抵现的金额（全款预售不需要此项，定金预售需要）")
        private BigDecimal firstExpand;

        @ApiModelProperty("商品定金")
        private BigDecimal depositAmount;

        @ApiModelProperty("定金需付款")
        private BigDecimal needDepositAmount;

        @ApiModelProperty("商品尾款")
        private BigDecimal remainAmount;

        @ApiModelProperty("尾款需付款")
        private BigDecimal needRemainAmount;

        @ApiModelProperty("是否全款订单：1-全款订单，0-定金预售订单")
        private Integer isAllPay;

        @ApiModelProperty("尾款支付的开始时间")
        private Date remainStartTime;

        @ApiModelProperty("尾款优惠")
        private BigDecimal finalDiscount;

        @ApiModelProperty("支付定金剩余时间（秒）")
        private long depositRemainTime;

        @ApiModelProperty("是否开始支付尾款")
        private Boolean isStartRemainPay = false;

        @ApiModelProperty("尾款支付剩余时间（秒）")
        private long remainEndTime;

        @ApiModelProperty("发货时间")
        private Date deliverTime;

        public PresellDetailInfo(PresellOrderExtendVO presellOrderExtend) {
            this.orderSubState = presellOrderExtend.getOrderSubState();
            this.presellPrice = presellOrderExtend.getPresellPrice();
            this.firstExpand = StringUtil.isNullOrZero(presellOrderExtend.getFirstExpand()) ? BigDecimal.ZERO : presellOrderExtend.getFirstExpand();
            this.depositAmount = presellOrderExtend.getDepositAmount();
            this.needDepositAmount = presellOrderExtend.getDepositAmount().multiply(new BigDecimal(presellOrderExtend.getProductNum()));
            this.remainAmount = presellOrderExtend.getRemainAmount();
            this.needRemainAmount = presellOrderExtend.getRemainAmount().multiply(new BigDecimal(presellOrderExtend.getProductNum()));
            this.isAllPay = presellOrderExtend.getIsAllPay();
            this.finalDiscount = presellOrderExtend.getIsAllPay() == OrderConst.IS_ALL_PAY_1 ? BigDecimal.ZERO
                    : StringUtil.isNullOrZero(presellOrderExtend.getFirstExpand()) ? BigDecimal.ZERO
                    : (presellOrderExtend.getFirstExpand().subtract(presellOrderExtend.getDepositAmount())).multiply(new BigDecimal(presellOrderExtend.getProductNum()));
            if (presellOrderExtend.getIsAllPay() == OrderConst.IS_ALL_PAY_0) {
                this.remainStartTime = presellOrderExtend.getRemainStartTime();
                long time1 = presellOrderExtend.getDepositEndTime().getTime();
                long time4 = presellOrderExtend.getRemainStartTime().getTime();

                long time2 = System.currentTimeMillis();
                long startRemainPay = (time4 - time2) / 1000;
                if (startRemainPay < 0) {
                    isStartRemainPay = true;
                }
                long depositRemainTime = (time1 - time2) / 1000;
                this.depositRemainTime = depositRemainTime < 0 ? 0 : depositRemainTime;
                long time3 = presellOrderExtend.getRemainEndTime().getTime();
                long remainEndTime = (time3 - time2) / 1000;
                this.remainEndTime = remainEndTime < 0 ? 0 : remainEndTime;
            }
            this.deliverTime = presellOrderExtend.getDeliverTime();
        }
    }

    @Data
    public static class LadderGroupDetailInfo {
        @ApiModelProperty("订单状态：101-待付定金；102-待付尾款；103-已付全款")
        private Integer orderSubState;

        @ApiModelProperty("商品定金")
        private BigDecimal advanceDeposit;

        @ApiModelProperty("定金需付款")
        private BigDecimal needAdvanceDeposit;

        @ApiModelProperty("商品尾款")
        private BigDecimal remainAmount;

        @ApiModelProperty("尾款需付款")
        private BigDecimal needRemainAmount;

        @ApiModelProperty("实付尾款金额")
        private BigDecimal realRemainAmount;

        @ApiModelProperty("尾款支付的开始时间")
        private Date remainStartTime;

        @ApiModelProperty("支付定金剩余时间（秒）")
        private long depositRemainTime;

        @ApiModelProperty("尾款支付剩余时间（秒）")
        private long remainEndTime;

        @ApiModelProperty("是否退还定金")
        private Boolean isRefundDeposit = false;

        public LadderGroupDetailInfo(LadderGroupOrderExtendVO ladderGroupOrderExtend, BigDecimal activityDiscountAmount) {
            this.orderSubState = ladderGroupOrderExtend.getOrderSubState();
            this.advanceDeposit = ladderGroupOrderExtend.getAdvanceDeposit();
            this.needAdvanceDeposit = ladderGroupOrderExtend.getAdvanceDeposit().multiply(new BigDecimal(ladderGroupOrderExtend.getProductNum()));
            this.remainAmount = ladderGroupOrderExtend.getRemainAmount();
            if (!StringUtil.isNullOrZero(ladderGroupOrderExtend.getRemainAmount())) {
                this.needRemainAmount = ladderGroupOrderExtend.getRemainAmount().multiply(new BigDecimal(ladderGroupOrderExtend.getProductNum()));
            }
            if (ladderGroupOrderExtend.getOrderSubState() == LadderGroupConst.ORDER_SUB_STATE_3) {
                this.realRemainAmount = ladderGroupOrderExtend.getRemainAmount().multiply(new BigDecimal(ladderGroupOrderExtend.getProductNum())).subtract(activityDiscountAmount);
            } else {
                if (!StringUtil.isNullOrZero(ladderGroupOrderExtend.getRemainAmount())) {
                    this.realRemainAmount = ladderGroupOrderExtend.getRemainAmount().multiply(new BigDecimal(ladderGroupOrderExtend.getProductNum()));
                }
            }
            this.remainStartTime = ladderGroupOrderExtend.getRemainStartTime();
            long time1 = ladderGroupOrderExtend.getRemainStartTime().getTime();
            long time2 = System.currentTimeMillis();
            long depositRemainTime = (time1 - time2) / 1000;
            this.depositRemainTime = depositRemainTime < 0 ? 0 : depositRemainTime;
            long time3 = ladderGroupOrderExtend.getRemainEndTime().getTime();
            long remainEndTime = (time3 - time2) / 1000;
            this.remainEndTime = remainEndTime < 0 ? 0 : remainEndTime;
        }
    }
}
