package com.cfpamf.ms.mallorder.controller.front;

import com.cfpamf.ms.mallorder.dto.ValetOrderFlowParamDTO;
import com.cfpamf.ms.mallorder.service.IAgricValetService;
import com.cfpamf.ms.mallorder.vo.ValetOrderFlowVO;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@RequestMapping("front/agric")
@Api(tags = "front-农服代客下单前端控制器")
@Slf4j
public class FrontAgricValetController {

    @Autowired
    private IAgricValetService agricValetService;

    /**
     * 获取站长代客下单流程接口
     */
    @PostMapping("/valetOrder/flow")
    @ApiOperation(value = "代客下单获取规则引擎配置流程接口")
    public JsonResult<ValetOrderFlowVO> getValetOrderFlow(@Valid @RequestBody ValetOrderFlowParamDTO dto){
        ValetOrderFlowVO vo = agricValetService.getFlowFromRuleEngine(dto);

        return SldResponse.success(vo);
    }
}
