package com.cfpamf.ms.mallorder.service.impl;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cfpamf.ms.mallorder.common.enums.PkTypeEnum;
import com.cfpamf.ms.mallorder.common.enums.PkUserJobCodeEnum;
import com.cfpamf.ms.mallorder.common.enums.PkUserTypeEnum;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.common.util.DateUtil;
import com.cfpamf.ms.mallorder.pgMapper.WineScrmStaticMapper;
import com.cfpamf.ms.mallorder.po.pgrpt.WineScrmStaticPO;
import com.cfpamf.ms.mallorder.req.pgrpt.IndicatorsRequest;
import com.cfpamf.ms.mallorder.req.pgrpt.PkMatchOpponentsRequest;
import com.cfpamf.ms.mallorder.req.pgrpt.WineScrmStaticQuery;
import com.cfpamf.ms.mallorder.service.IWineScrmStaticService;
import com.cfpamf.ms.mallorder.vo.pgrpt.IndicatorsResponse;
import com.cfpamf.ms.mallorder.vo.pgrpt.IndicatorsVO;
import com.cfpamf.ms.mallorder.vo.pgrpt.PkMatchOpponentsResponse;
import com.slodon.bbc.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class WineScrmStaticServiceImpl implements IWineScrmStaticService {

	@Autowired
	private WineScrmStaticMapper wineScrmStaticMapper;

	@Override
	public Page<PkMatchOpponentsResponse> matchOpponents(PkMatchOpponentsRequest request) {
		/**
		 * 查询邀请员工信息
		 */
		// 根据类型带上岗位
		PkUserTypeEnum pkUserTypeEnum = PkUserTypeEnum.getByCode(request.getType());
		BizAssertUtil.notNull(pkUserTypeEnum, "未知类型，请确认");
		WineScrmStaticPO inviterStaticPO = wineScrmStaticMapper.selectByEmpId(
				request.getUserCode(), pkUserTypeEnum.getJobCodeEnum().getCode());
		BizAssertUtil.notNull(inviterStaticPO, "邀请人信息为空，请检查");
		log.info("matchOpponents-> inviter info:{}", JSONObject.toJSONString(inviterStaticPO));

		/**
		 * 处理筛选条件
		 */
		WineScrmStaticQuery query = new WineScrmStaticQuery();
		// 筛选最新记录
		query.setRptDate(inviterStaticPO.getRptDate());
		// 筛选邀请人同岗位员工
		query.setJobCode(inviterStaticPO.getJobCode());
		// 精确匹配，模糊查询条件
		if (StringUtils.isNotBlank(request.getMatchName())) {
			if (PkUserTypeEnum.isUserName(request.getType())) {
				query.setEmpName(request.getMatchName());
			} else if (PkUserTypeEnum.BRANCH.getCode().equals(request.getType())) {
				query.setBchName(request.getMatchName());
			} else {
				query.setAreaName(request.getMatchName());
			}
		}
		// 随机匹配，计算战力值范围
		if (request.getIndicatorsMaxValue() != null && request.getIndicatorsMinValue() != null) {
			if (PkTypeEnum.VALID_SALE_AMT.getCode().equals(request.getIndicatorsType())) {
				// pk有效销售额
				BigDecimal inviterLmWineValidSaleAmt = inviterStaticPO.getLmWineValidSaleAmt();
				if (inviterLmWineValidSaleAmt.compareTo(BigDecimal.ZERO) == 0) {
					// 邀请人业绩为0，默认为1
					inviterLmWineValidSaleAmt = BigDecimal.ONE;
				}
				BigDecimal inviteeMaxAmt = inviterLmWineValidSaleAmt.multiply(request.getIndicatorsMaxValue());
				BigDecimal inviteeMinAmt = inviterLmWineValidSaleAmt.multiply(request.getIndicatorsMinValue());
				if (inviteeMinAmt.compareTo(BigDecimal.ONE) < 0) {
					// 将新员工及业绩为0的员工筛选上
					inviteeMinAmt = BigDecimal.ZERO;
				}
				query.setLmWineValidSaleAmtMax(inviteeMaxAmt);
				query.setLmWineValidSaleAmtMin(inviteeMinAmt);
			} else if (PkTypeEnum.AVG_VALID_SALE_AMT.getCode().equals(request.getIndicatorsType())) {
				// pk人均有效销售额
				BigDecimal inviterLmWineAvgValidSaleAmt = inviterStaticPO.getLmWineAvgValidSaleAmt();
				if (inviterLmWineAvgValidSaleAmt.compareTo(BigDecimal.ZERO) == 0) {
					// 邀请人业绩为0，默认为1
					inviterLmWineAvgValidSaleAmt = BigDecimal.ONE;
				}
				BigDecimal inviteeMaxAvgAmt = inviterLmWineAvgValidSaleAmt.multiply(request.getIndicatorsMaxValue());
				BigDecimal inviteeMinAvgAmt = inviterLmWineAvgValidSaleAmt.multiply(request.getIndicatorsMinValue());
				if (inviteeMinAvgAmt.compareTo(BigDecimal.ONE) < 0) {
					// 将新员工及业绩为0的员工筛选上
					inviteeMinAvgAmt = BigDecimal.ZERO;
				}
				query.setLmWineAvgValidSaleAmtMax(inviteeMaxAvgAmt);
				query.setLmWineAvgValidSaleAmtMin(inviteeMinAvgAmt);
			} else if (PkTypeEnum.TARGET_FINISH_RADIO.getCode().equals(request.getIndicatorsType())) {
				// pk年度目标完成率
				BigDecimal inviterLmSyWineValidSalesAmtTargetFinishRadio = inviterStaticPO.getLmSyWineValidSalesAmtTargetFinishRadio();
				if (inviterLmSyWineValidSalesAmtTargetFinishRadio.compareTo(BigDecimal.ZERO) == 0) {
					// 邀请人业绩为0，默认为1
					inviterLmSyWineValidSalesAmtTargetFinishRadio = BigDecimal.ONE;
				}
				BigDecimal inviteeMaxRadio = inviterLmSyWineValidSalesAmtTargetFinishRadio.multiply(request.getIndicatorsMaxValue());
				BigDecimal inviteeMinRadio = inviterLmSyWineValidSalesAmtTargetFinishRadio.multiply(request.getIndicatorsMinValue());
				if (inviteeMinRadio.compareTo(BigDecimal.ONE) < 0) {
					// 将新员工及业绩为0的员工筛选上
					inviteeMinRadio = BigDecimal.ZERO;
				}
				query.setLmSyWineValidSalesAmtTargetFinishRadioMax(inviteeMaxRadio);
				query.setLmSyWineValidSalesAmtTargetFinishRadioMin(inviteeMinRadio);
			} else {
				throw new BusinessException("未知PK指标类型，请确认");
			}
		}

		/**
		 * 查询对手员工信息
		 */
		Page<WineScrmStaticPO> page = new Page<>(request.getPageNum(), request.getPageSize());
		IPage<WineScrmStaticPO> inviteeStaticPageList = wineScrmStaticMapper.pageList(page, query);
		log.info("matchOpponents-> invitee info with query:{},result:{}",
				JSONObject.toJSONString(query), JSONObject.toJSONString(inviteeStaticPageList));
		if (CollectionUtil.isEmpty(inviteeStaticPageList.getRecords())) {
			return null;
		}

		/**
		 * 封装返回
		 */
		Page<PkMatchOpponentsResponse> responsePage =
				new Page<>(inviteeStaticPageList.getCurrent(), inviteeStaticPageList.getSize(), inviteeStaticPageList.getTotal());
		responsePage.setRecords(new ArrayList<>(inviteeStaticPageList.getRecords().size()));
		// 邀请人pk数据
		BigDecimal inviterIndicatorData = BigDecimal.ZERO;
		// 被邀请人pk数据
		BigDecimal inviteeIndicatorData = BigDecimal.ZERO;
		// 邀请人指标数据
		IndicatorsVO inviterIndicator = new IndicatorsVO();
		inviterIndicator.setType(request.getIndicatorsType());
		inviterIndicator.setTypeName(Objects.requireNonNull(PkTypeEnum.getByCode(request.getIndicatorsType())).getDesc());
		inviterIndicator.setUnit(Objects.requireNonNull(PkTypeEnum.getByCode(request.getIndicatorsType())).getUnit());
		if (PkTypeEnum.VALID_SALE_AMT.getCode().equals(request.getIndicatorsType())) {
			// pk有效销售额
			inviterIndicatorData = inviterStaticPO.getLmWineValidSaleAmt();
		} else if (PkTypeEnum.AVG_VALID_SALE_AMT.getCode().equals(request.getIndicatorsType())) {
			// pk人均有效销售额
			inviterIndicatorData = inviterStaticPO.getLmWineAvgValidSaleAmt();
		} else if (PkTypeEnum.TARGET_FINISH_RADIO.getCode().equals(request.getIndicatorsType())) {
			// pk年度目标完成率
			inviterIndicatorData = inviterStaticPO.getLmSyWineValidSalesAmtTargetFinishRadio();
			inviterIndicatorData = inviterIndicatorData.multiply(BigDecimal.valueOf(100));
		}
		inviterIndicator.setValue(
				inviterIndicatorData.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ONE : inviterIndicatorData);
		inviterIndicator.setDataOrder(1);
		for (WineScrmStaticPO record : inviteeStaticPageList.getRecords()) {
			PkMatchOpponentsResponse inviteeRecord = new PkMatchOpponentsResponse();
			inviteeRecord.setType(request.getType());
			inviteeRecord.setUserCode(record.getEmpId());
			inviteeRecord.setUserName(record.getEmpName());
			inviteeRecord.setBranchCode(record.getBchCode());
			inviteeRecord.setBranchName(record.getBchName());
			inviteeRecord.setRegionCode(record.getAreaCode());
			inviteeRecord.setRegionName(record.getAreaName());
			inviteeRecord.setOriginatorIndicatorsVO(inviterIndicator);
			// 被邀请人指标数据
			IndicatorsVO inviteeIndicator = new IndicatorsVO();
			inviteeIndicator.setType(request.getIndicatorsType());
			inviteeIndicator.setTypeName(Objects.requireNonNull(PkTypeEnum.getByCode(request.getIndicatorsType())).getDesc());
			inviteeIndicator.setUnit(Objects.requireNonNull(PkTypeEnum.getByCode(request.getIndicatorsType())).getUnit());
			if (PkTypeEnum.VALID_SALE_AMT.getCode().equals(request.getIndicatorsType())) {
				// pk有效销售额
				inviteeIndicatorData = record.getLmWineValidSaleAmt();
			} else if (PkTypeEnum.AVG_VALID_SALE_AMT.getCode().equals(request.getIndicatorsType())) {
				// pk人均有效销售额
				inviteeIndicatorData = record.getLmWineAvgValidSaleAmt();
			} else if (PkTypeEnum.TARGET_FINISH_RADIO.getCode().equals(request.getIndicatorsType())) {
				// pk年度目标完成率
				inviteeIndicatorData = record.getLmSyWineValidSalesAmtTargetFinishRadio();
				inviteeIndicatorData = inviteeIndicatorData.multiply(BigDecimal.valueOf(100));
			}
			inviteeIndicator.setValue(
					inviteeIndicatorData.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ONE : inviteeIndicatorData);
			inviteeIndicator.setDataOrder(1);
			inviteeRecord.setRecipientIndicatorsVO(inviteeIndicator);

			responsePage.getRecords().add(inviteeRecord);
		}

		return responsePage;
	}

	@Override
	public List<IndicatorsResponse> queryPkHistoryIndicators(IndicatorsRequest request) {
		/**
		 * 筛选查询
		 */
		WineScrmStaticQuery query = new WineScrmStaticQuery();
		if (PkUserTypeEnum.MANAGER.getCode().equals(request.getType())) {
			// 查客户经理
			query.setEmpIdIn(request.getCodeList());
			query.setJobCode(PkUserJobCodeEnum.MANAGER.getCode());
		} else if (PkUserTypeEnum.SUPERVISE.getCode().equals(request.getType())) {
			// 查督导
			query.setEmpIdIn(request.getCodeList());
			query.setJobCode(PkUserJobCodeEnum.SUPERVISE.getCode());
		} else if (PkUserTypeEnum.BRANCH.getCode().equals(request.getType())) {
			// 查分支、主任
			query.setBchCodeIn(request.getCodeList());
			query.setJobCode(PkUserJobCodeEnum.DIRECTOR.getCode());
		} else if (PkUserTypeEnum.AREA.getCode().equals(request.getType())) {
			// 查区域、总经理
			query.setAreaCodeIn(request.getCodeList());
			query.setJobCode(PkUserJobCodeEnum.GENERAL_MANAGER.getCode());
		} else {
			throw new BusinessException("未知PK类型，请确认");
		}
		// 查最新数据
		String latestStatisticsTime = wineScrmStaticMapper.getLatestStatisticsTime();
		query.setRptDate(latestStatisticsTime);
		List<WineScrmStaticPO> list = wineScrmStaticMapper.list(query);
		log.info("queryPkHistoryIndicators-> info with query:{},result:{}",
				JSONObject.toJSONString(query), JSONObject.toJSONString(list));
		if (CollectionUtil.isEmpty(list)) {
			return null;
		}

		/**
		 * 数据组装返回
		 */
		List<IndicatorsResponse> responses = new ArrayList<>(list.size());
		for (WineScrmStaticPO wineScrmStaticPO : list) {
			IndicatorsResponse response = new IndicatorsResponse();
			response.setType(request.getType());
			if (PkUserTypeEnum.isUserName(request.getType())) {
				response.setCode(wineScrmStaticPO.getEmpId());
			} else if (PkUserTypeEnum.BRANCH.getCode().equals(request.getType())) {
				response.setCode(wineScrmStaticPO.getBchCode());
			} else {
				response.setCode(wineScrmStaticPO.getAreaCode());
			}
			// 组装指标数据
			response.setIndicatorsVOList(new ArrayList<>(request.getIndicatorTypeList().size()));
			for (String indicatorType : request.getIndicatorTypeList()) {
				IndicatorsVO indicatorsVO = new IndicatorsVO();
				indicatorsVO.setType(indicatorType);
				indicatorsVO.setTypeName(PkTypeEnum.getByCode(indicatorType).getDesc());
				indicatorsVO.setUnit(PkTypeEnum.getByCode(indicatorType).getUnit());
				if (PkTypeEnum.VALID_SALE_AMT.getCode().equals(indicatorType)) {
					// pk有效销售额 上月
					indicatorsVO.setValue(wineScrmStaticPO.getLmWineValidSaleAmt());
				} else if (PkTypeEnum.AVG_VALID_SALE_AMT.getCode().equals(indicatorType)) {
					// pk人均有效销售额 上月
					indicatorsVO.setValue(wineScrmStaticPO.getLmWineAvgValidSaleAmt());
				} else if (PkTypeEnum.TARGET_FINISH_RADIO.getCode().equals(indicatorType)) {
					// pk年度目标完成率 上月
					indicatorsVO.setValue(
							wineScrmStaticPO.getLmSyWineValidSalesAmtTargetFinishRadio().multiply(BigDecimal.valueOf(100)));
				} else {
					// 司龄
					indicatorsVO.setValue(BigDecimal.valueOf(wineScrmStaticPO.getJoinMonths()));
				}
				indicatorsVO.setDataOrder(0);

				response.getIndicatorsVOList().add(indicatorsVO);
			}

			responses.add(response);
		}

		return responses;
	}

	@Override
	public List<IndicatorsResponse> queryPkRealTimeIndicators(IndicatorsRequest request) {
		/**
		 * 筛选查询
		 */
		WineScrmStaticQuery query = new WineScrmStaticQuery();
		if (PkUserTypeEnum.MANAGER.getCode().equals(request.getType())) {
			// 查客户经理
			query.setEmpIdIn(request.getCodeList());
			query.setJobCode(PkUserJobCodeEnum.MANAGER.getCode());
		} else if (PkUserTypeEnum.SUPERVISE.getCode().equals(request.getType())) {
			// 查督导
			query.setEmpIdIn(request.getCodeList());
			query.setJobCode(PkUserJobCodeEnum.SUPERVISE.getCode());
		} else if (PkUserTypeEnum.BRANCH.getCode().equals(request.getType())) {
			// 查分支、主任
			query.setBchCodeIn(request.getCodeList());
			query.setJobCode(PkUserJobCodeEnum.DIRECTOR.getCode());
		} else if (PkUserTypeEnum.AREA.getCode().equals(request.getType())) {
			// 查区域、总经理
			query.setAreaCodeIn(request.getCodeList());
			query.setJobCode(PkUserJobCodeEnum.GENERAL_MANAGER.getCode());
		} else {
			throw new BusinessException("未知PK类型，请确认");
		}
		// 查最新数据
		String latestStatisticsTime = wineScrmStaticMapper.getLatestStatisticsTime();
		query.setRptDate(latestStatisticsTime);
		List<WineScrmStaticPO> list = wineScrmStaticMapper.list(query);
		log.info("queryPkRealTimeIndicators-> info with query:{},result:{}",
				JSONObject.toJSONString(query), JSONObject.toJSONString(list));
		if (CollectionUtil.isEmpty(list)) {
			return null;
		}

		/**
		 * 数据组装返回
		 */
		List<IndicatorsResponse> responses = new ArrayList<>(list.size());
		for (WineScrmStaticPO wineScrmStaticPO : list) {
			IndicatorsResponse response = new IndicatorsResponse();
			response.setType(request.getType());
			if (PkUserTypeEnum.isUserName(request.getType())) {
				response.setCode(wineScrmStaticPO.getEmpId());
			} else if (PkUserTypeEnum.BRANCH.getCode().equals(request.getType())) {
				response.setCode(wineScrmStaticPO.getBchCode());
			} else {
				response.setCode(wineScrmStaticPO.getAreaCode());
			}
			// 组装指标数据
			response.setIndicatorsVOList(new ArrayList<>(request.getIndicatorTypeList().size()));
			for (String indicatorType : request.getIndicatorTypeList()) {
				IndicatorsVO indicatorsVO = new IndicatorsVO();
				indicatorsVO.setType(indicatorType);
				indicatorsVO.setTypeName(PkTypeEnum.getByCode(indicatorType).getDesc());
				indicatorsVO.setUnit(PkTypeEnum.getByCode(indicatorType).getUnit());
				if (PkTypeEnum.VALID_SALE_AMT.getCode().equals(indicatorType)) {
					// pk有效销售额 当月
					indicatorsVO.setValue(wineScrmStaticPO.getSmWineValidSaleAmt());
				} else if (PkTypeEnum.AVG_VALID_SALE_AMT.getCode().equals(indicatorType)) {
					// pk人均有效销售额 当月
					indicatorsVO.setValue(wineScrmStaticPO.getSmWineAvgValidSaleAmt());
				} else if (PkTypeEnum.TARGET_FINISH_RADIO.getCode().equals(indicatorType)) {
					// pk年度目标完成率 当月
					indicatorsVO.setValue(
							wineScrmStaticPO.getSmSyWineValidSalesAmtTargetFinishRadio().multiply(BigDecimal.valueOf(100)));
				} else {
					// 司龄
					indicatorsVO.setValue(BigDecimal.valueOf(wineScrmStaticPO.getJoinMonths()));
				}
				indicatorsVO.setDataOrder(0);

				response.getIndicatorsVOList().add(indicatorsVO);
			}

			responses.add(response);
		}

		return responses;
	}

	@Override
	public List<IndicatorsResponse> pkSettlement(IndicatorsRequest request) {
		/**
		 * 筛选查询
		 */
		WineScrmStaticQuery query = new WineScrmStaticQuery();
		if (PkUserTypeEnum.MANAGER.getCode().equals(request.getType())) {
			// 查客户经理
			query.setEmpIdIn(request.getCodeList());
			query.setJobCode(PkUserJobCodeEnum.MANAGER.getCode());
		} else if (PkUserTypeEnum.SUPERVISE.getCode().equals(request.getType())) {
			// 查督导
			query.setEmpIdIn(request.getCodeList());
			query.setJobCode(PkUserJobCodeEnum.SUPERVISE.getCode());
		}  else if (PkUserTypeEnum.BRANCH.getCode().equals(request.getType())) {
			// 查分支、主任
			query.setBchCodeIn(request.getCodeList());
			query.setJobCode(PkUserJobCodeEnum.DIRECTOR.getCode());
		} else if (PkUserTypeEnum.AREA.getCode().equals(request.getType())) {
			// 查区域、总经理
			query.setAreaCodeIn(request.getCodeList());
			query.setJobCode(PkUserJobCodeEnum.GENERAL_MANAGER.getCode());
		} else {
			throw new BusinessException("未知PK类型，请确认");
		}
		// 查pk截止时间,数仓每日统计时间段为（5：00-22：00）
		String endDateTime = request.getEndDate() + " 22:00:00";
		Date endDate = DateUtil.parse(endDateTime, DateUtil.FORMAT_TIME);
		String latestStatisticsTime = wineScrmStaticMapper.getLatestStatisticsTime();
		Date latestDate = DateUtil.parse(latestStatisticsTime, DateUtil.FORMAT_TIME);
		if (latestDate.before(endDate)) {
			// 截止时间在统计时间之后，查最新统计时间
			query.setRptDate(latestStatisticsTime);
		} else {
			query.setRptDate(endDateTime);
		}
		List<WineScrmStaticPO> list = wineScrmStaticMapper.list(query);
		log.info("pkSettlement-> info with query:{},result:{}",
				JSONObject.toJSONString(query), JSONObject.toJSONString(list));
		if (CollectionUtil.isEmpty(list)) {
			return null;
		}

		/**
		 * 数据组装返回
		 */
		List<IndicatorsResponse> responses = new ArrayList<>(list.size());
		for (WineScrmStaticPO wineScrmStaticPO : list) {
			IndicatorsResponse response = new IndicatorsResponse();
			response.setType(request.getType());
			if (PkUserTypeEnum.isUserName(request.getType())) {
				response.setCode(wineScrmStaticPO.getEmpId());
			} else if (PkUserTypeEnum.BRANCH.getCode().equals(request.getType())) {
				response.setCode(wineScrmStaticPO.getBchCode());
			} else {
				response.setCode(wineScrmStaticPO.getAreaCode());
			}
			// 组装指标数据
			response.setIndicatorsVOList(new ArrayList<>(request.getIndicatorTypeList().size()));
			for (String indicatorType : request.getIndicatorTypeList()) {
				IndicatorsVO indicatorsVO = new IndicatorsVO();
				indicatorsVO.setType(indicatorType);
				indicatorsVO.setTypeName(PkTypeEnum.getByCode(indicatorType).getDesc());
				indicatorsVO.setUnit(PkTypeEnum.getByCode(indicatorType).getUnit());
				if (PkTypeEnum.VALID_SALE_AMT.getCode().equals(indicatorType)) {
					// pk有效销售额 pk时间段内
					indicatorsVO.setValue(wineScrmStaticPO.getSmWineValidSaleAmt());
				} else if (PkTypeEnum.AVG_VALID_SALE_AMT.getCode().equals(indicatorType)) {
					// pk人均有效销售额 pk时间段内
					indicatorsVO.setValue(wineScrmStaticPO.getSmWineAvgValidSaleAmt());
				} else if (PkTypeEnum.TARGET_FINISH_RADIO.getCode().equals(indicatorType)) {
					// pk年度目标完成率 pk时间段内
					indicatorsVO.setValue(
							wineScrmStaticPO.getSmSyWineValidSalesAmtTargetFinishRadio().multiply(BigDecimal.valueOf(100)));
				} else {
					// 司龄
					indicatorsVO.setValue(BigDecimal.valueOf(wineScrmStaticPO.getJoinMonths()));
				}
				indicatorsVO.setDataOrder(0);

				response.getIndicatorsVOList().add(indicatorsVO);
			}

			responses.add(response);
		}

		return responses;
	}
}
