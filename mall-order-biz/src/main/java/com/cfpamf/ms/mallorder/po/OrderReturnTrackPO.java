package com.cfpamf.ms.mallorder.po;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * 退款单轨迹表
 */
@Data
@Builder
@TableName("bz_order_return_track")
public class OrderReturnTrackPO implements Serializable {
    /**
     * 
     */
    private static final long serialVersionUID = 5932437667050636647L;

    @ApiModelProperty("轨迹id")
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("退款单号")
    private String afsSn;

    @ApiModelProperty("操作：1、创建；2、店铺审核；3、平台审核；4、退款成功；5、退款失败；6、售后关闭")
    private Integer operateType;

    @ApiModelProperty("操作处理人，账号-手机号，系统取admin")
    private String operator;

    @ApiModelProperty("操作人角色")
    private Integer operatorRole;

    @ApiModelProperty("操作时间")
    private Date operateTime;

    @ApiModelProperty("处理结果 1:通过 2:拒绝")
    private Integer operateResult;

    @ApiModelProperty("备注")
    private String operateRemark;

    @ApiModelProperty("渠道")
    private String channel;

    @ApiModelProperty("是否可用：1-是 0-否")
    private Short enabledFlag;
}
