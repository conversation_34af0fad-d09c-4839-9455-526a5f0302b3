package com.cfpamf.ms.mallorder.scheduler;

import com.cfpamf.ms.mallorder.service.IHistoryDataDealService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-07-20
 */
@Component
@Slf4j
public class OrderProductCostJob {

    @Resource
    private IHistoryDataDealService historyDataDealService;

    @XxlJob("OrderProductCostJob")
    public ReturnT<String> execute(String s) throws Exception {

        XxlJobHelper.log("OrderProductCostJob===============start, DATE :{}", LocalDateTime.now());
        historyDataDealService.orderProductCostFix(s);
        XxlJobHelper.log("OrderProductCostJob===============end, DATE :{}", LocalDateTime.now());
        return ReturnT.SUCCESS;
    }
}
