package com.cfpamf.ms.mallorder.scheduler;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.bms.facade.vo.DingTalkUserBmsVO;
import com.cfpamf.ms.mallorder.integration.facade.BmsDingTalkFacade;
import com.cfpamf.ms.mallorder.service.IOrderReturnService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 【待平台审核】，被挂起7天到财务那里的，钉钉消息提醒，
 * 人员：王静、刘晓义、毕晓丹、李楠、丰小娟、【2022-01-11增加】董晓杨 +86-13611032629
 * 提示消息：中和表单电商尚有N单退款订单未完成退款，请尽快处理！
 *
 * 每天1次，上午10点，cron:   0 0 10 * * *
 */
@Component
@Slf4j
public class PlatformAuditNoticeJob {

    private static final String REMIND_MESSAGE = "提示消息：中和表单电商尚有{0}单退款订单未完成退款，请尽快处理！";

    /**
     * 审核人员
     */
    @Value("${auditors.platform}")
    private String[] auditUserPhones;

    @Resource
    private IOrderReturnService orderReturnService;

    @Resource
    private BmsDingTalkFacade bmsDingTalkFacade;


    @XxlJob("PlatformAuditNoticeJob")
    public ReturnT<String> execute(String s) throws Exception {

        XxlJobHelper.log("=============== START PlatformAuditNoticeJob , DATE :{}", LocalDateTime.now());

        try {
            int overtimeCount = orderReturnService.getAfsCountInPlatformAuditOvertime(7);
            XxlJobHelper.log("至平台未审核数量：{}", overtimeCount);
            if (overtimeCount > 0) {
                Set<String> dingTalkUserIds = new TreeSet<>();
                XxlJobHelper.log("审批用户信息：{}", Arrays.asList(auditUserPhones));
                Result<List<DingTalkUserBmsVO>> listResult = bmsDingTalkFacade.listUserDetailVOByMobiles(Arrays.asList(auditUserPhones));
                listResult.getData().forEach(dingTalkUserBmsVO -> {
                    Optional.ofNullable(dingTalkUserBmsVO.getDeptList()).ifPresent(deptList -> {
                        deptList.forEach(dingTalkUserDeptBmsVO -> {
                            dingTalkUserIds.add(dingTalkUserDeptBmsVO.getDingTalkUserId());
                        });
                    });
                    Optional.ofNullable(dingTalkUserBmsVO.getRoleList()).ifPresent(roleList -> {
                        roleList.forEach(dingTalkUserRoleBmsVO -> {
                            dingTalkUserIds.add(dingTalkUserRoleBmsVO.getDingTalkUserId());
                        });
                    });
                });
                XxlJobHelper.log("发送钉钉消息开始：钉钉发送人信息列表：{}", dingTalkUserIds);
                bmsDingTalkFacade.sendPlatformTextMessage(new ArrayList<>(dingTalkUserIds), MessageFormat.format(REMIND_MESSAGE, overtimeCount));
                XxlJobHelper.log("发送钉钉消息结束：钉钉发送人信息列表：{}", dingTalkUserIds);
            }
        } catch (Exception e) {
            log.error("platformAuditNoticeJob()", e);
        }

        XxlJobHelper.log("=============== END PlatformAuditNoticeJob , DATE :{}", LocalDateTime.now());
        return ReturnT.SUCCESS;
    }

}
