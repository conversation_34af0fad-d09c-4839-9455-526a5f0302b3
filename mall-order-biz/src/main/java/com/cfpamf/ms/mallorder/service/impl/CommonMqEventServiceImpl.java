package com.cfpamf.ms.mallorder.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.cfpamf.ms.mallorder.mapper.CommonMqEventMapper;
import com.cfpamf.ms.mallorder.po.CommonMqEvent;
import com.cfpamf.ms.mallorder.service.ICommonMqEventService;
import com.slodon.bbc.core.exception.MallException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-16
 */
@Service
public class CommonMqEventServiceImpl extends ServiceImpl<CommonMqEventMapper, CommonMqEvent> implements ICommonMqEventService {

    @Resource
    private CommonMqEventMapper commonMqEventMapper;

    @Override
    public Long saveEvent(String body, String exchange) {

        CommonMqEvent mqEvent = new CommonMqEvent();
        mqEvent.setExchange(exchange);
        mqEvent.setRoutingKey("");
        mqEvent.setMessage(body);
        save(mqEvent);

        return mqEvent.getId();
    }

    @Override
    public Long saveEvent(Object body, String exchange) {
        if (body == null){
            return null;
        }
        String message = JSON.toJSONString(body);
        return saveEvent(message, exchange);
    }

    @Override
    public <T> Long saveEvent(T body, String routeKey, String exchange) {
        if (Objects.isNull(body)) {
            return null;
        }
        CommonMqEvent mqEvent = new CommonMqEvent();
        mqEvent.setExchange(exchange);
        mqEvent.setRoutingKey(routeKey);
        mqEvent.setMessage(JSON.toJSONString(body));
        if (!this.save(mqEvent)) {
            throw new MallException("保存待发送的MQ消息失败",ErrorCodeEnum.S.SYSTEM_EXCEPTION.getCode());
        }
        return mqEvent.getId();
    }

    @Override
    public List<CommonMqEvent> getMqMessage4Resend() {
        return commonMqEventMapper.getMqMessage4Resend();
    }


}
