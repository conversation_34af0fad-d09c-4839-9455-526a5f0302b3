package com.cfpamf.ms.mallorder.scheduler;


import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.mallorder.common.util.DateUtil;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.service.IJsPkDynamicPerformanceService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.jetty.util.StringUtil;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;

import static com.cfpamf.ms.mallorder.common.util.DateUtil.FORMAT_TIME;

/**
 * <AUTHOR>
 * @Create 2023-10-19 14:36
 * @Description : 酒水PK销售业绩动态推送
 */
@Component
@Slf4j
public class JsPkDynamicPerformanceJob {

    @Resource
    private IJsPkDynamicPerformanceService iJsPkDynamicPerformanceService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RabbitTemplate rabbitTemplate;

    @XxlJob(value = "JsPkDynamicPerformanceJob")
    public ReturnT<String> execute() {
        long startTime = System.currentTimeMillis();
        XxlJobHelper.log("start job at local time: {0}", LocalDateTime.now());
        log.info("JsPkDynamicPerformanceJob() start");
        //调度器入参
        Date paramStartTime = null;
        Date paramEndTime = null;
        try {
            String param = XxlJobHelper.getJobParam();
            //缓存上次调度时间
            String strTime = null;
            if (StringUtils.isNotBlank(param)) {
                JSONObject jsonObject = JSONObject.parseObject(param);
                paramStartTime = DateUtil.parse(jsonObject.get("startTime").toString(), FORMAT_TIME);
                paramEndTime = DateUtil.parse(jsonObject.get("endTime").toString(), FORMAT_TIME);
            } else {
                strTime = stringRedisTemplate.opsForValue().get(OrderConst.JS_PK_DYNAMIC_PERFORMANCE_TIME);
                if (StringUtil.isNotBlank(strTime)) {
                    //缓存存在则说明上次执行中断，从上次开始查询
                    paramStartTime = DateUtil.parse(strTime, FORMAT_TIME);
                } else {
                    //缓存不存在则取当前时间前1小时
                    paramStartTime = DateUtil.beforeHours(1);
                }
                //当前时间
                paramEndTime = new Date();
            }

            XxlJobHelper.log("开始查询时间段【{}】-【{}】内的销售数据", paramStartTime, paramEndTime);
            iJsPkDynamicPerformanceService.queryList(paramStartTime, paramEndTime);
            //推送成功删除key
            if (StringUtil.isNotBlank(strTime)) {
                stringRedisTemplate.delete(OrderConst.JS_PK_DYNAMIC_PERFORMANCE_TIME);
            }
        } catch (Exception e) {
            log.error("JsPkDynamicPerformanceJob()", e);
            //执行异常记录此次开始时间，下次从此开始查询
            stringRedisTemplate.opsForValue().set(OrderConst.JS_PK_DYNAMIC_PERFORMANCE_TIME,
                    DateUtil.format(paramStartTime, FORMAT_TIME));
        }
        XxlJobHelper.log("finish job at local time: {0}  cost:{1}",
                LocalDateTime.now(),
                (System.currentTimeMillis() - startTime) / 1000);
        return new ReturnT("SUCCESS");
    }

}

