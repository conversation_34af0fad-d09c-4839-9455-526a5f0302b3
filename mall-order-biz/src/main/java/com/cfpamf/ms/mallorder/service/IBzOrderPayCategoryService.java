package com.cfpamf.ms.mallorder.service;

import com.cfpamf.ms.mallorder.dto.CategoryLoanDTO;
import com.cfpamf.ms.mallorder.po.BzOrderPayCategoryPO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.ms.mallorder.vo.BzOrderPayCategoryVO;
import com.slodon.bbc.core.response.JsonResult;

import java.util.List;

/**
 * <p>
 * 支付匹配分类表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-26
 */
public interface IBzOrderPayCategoryService extends IService<BzOrderPayCategoryPO> {

    /**
     * 判断店铺分类是否允许开通金融支付
     *
     * @param categoryLoanDTO
     * @return
     */
    Boolean isAllowLoan(CategoryLoanDTO categoryLoanDTO);
}
