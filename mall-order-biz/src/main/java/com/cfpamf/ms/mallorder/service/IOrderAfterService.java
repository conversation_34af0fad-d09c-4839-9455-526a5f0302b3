package com.cfpamf.ms.mallorder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.dto.OrderAfterDTO;
import com.cfpamf.ms.mallorder.dto.UserDTO;
import com.cfpamf.ms.mallorder.po.OrderAfterPO;
import com.cfpamf.ms.mallorder.po.OrderProductPO;
import com.cfpamf.ms.mallorder.req.admin.AdminAfterSaleListRequest;
import com.cfpamf.ms.mallorder.req.seller.SellerAfterSaleListRequest;
import com.cfpamf.ms.mallorder.vo.*;
import com.cfpamf.ms.mallshop.resp.Vendor;
import com.slodon.bbc.core.response.PageVO;
import com.slodon.bbc.core.response.PagerInfo;

import java.util.Date;
import java.util.List;

/**
 * 订单正向操作 service 接口
 *
 * <AUTHOR>
 * @date 2021/5/30 17:00
 * @return
 */
public interface IOrderAfterService extends IService<OrderAfterPO> {

    List<OrderAfterPO> listByOrderSn(String orderSn);

    boolean isAllReturnApplied(List<OrderAfterDTO.AfterProduct> afsDTOs, String orderSn);

    /**
     * 对比订单是否全退
     *
     * @param afs
     * @param ops
     * @return
     */
    boolean isAllReturnApplied(List<OrderAfterPO> afs, List<OrderProductPO> ops);

    /**
     * admin 端查询售后信息
     *
     * @param pager                         分页信息
     * @param adminAfterSaleListRequest     入参信息
     *
     * @return                              退款信息结果
     */
    PageVO<OrderReturnVOV2> adminAfterSaleList(PagerInfo pager, AdminAfterSaleListRequest adminAfterSaleListRequest);

    /**
     * 商家端售后列表查询
     */
    PageVO<OrderReturnVOV2> sellerAfterSaleList(Vendor vendor, PagerInfo pager, SellerAfterSaleListRequest request);

    /**
     * 商家端查询售后信息详情
     *
     * @param afsSn 退款单号
     * @param vendor 商家
     *
     * @param distribution
     * @return 退款详情信息
     */
    OrderReturnDetailVO sellerAfterSaleDetail(String afsSn, Vendor vendor, Integer distribution);

    /**
     * admin 端查询售后信息详情
     *
     * @param afsSn 退款单号
     *
     * @return 退款详情信息
     */
    OrderReturnDetailVO adminAfterSaleDetail(String afsSn);

    /**
     * 根据退款单号查询售后信息
     *
     * @param afsSn     退款单号
     * @return          退款售后信息
     */
    OrderAfterPO getByAfsSn(String afsSn);


    DistributionOrderInfoVO balanceReminder(String afsSn);

    /**
     * 售后申请计算金额信息
     *
     * @param orderSn              订单号
     * @param afsOrderProductInfos 售后申请商品信息
     * @return 计算的售后金额信息
     */
    AfsCountVO refundCountMoneyInfo(String orderSn, String afsOrderProductInfos, Member member);

    /**
     * 获取订单所有可退款商品
     * 指定商品退款时，将该商品放到第一个
     *
     * @return
     */
    List<AfsOrderProductVO> getRefundableOrderProduct(String orderSn, Long orderProductId, Member member);

    /**
     * 售后风险提示
     * @param afsSn
     * @return
     */
    OrderReturnRiskMessageVO riskMessage(String afsSn);
    String erpDeliveryIntercept(String afsSn, UserDTO userDTO);

    OrderReturnRiskMessageVO groupBuyingRiskCheck(OrderAfterPO orderAfterPO,OrderProductPO orderProductPO);

    /**
     * 是否满足京东物流拦截条件
     * @param orderProductPO
     * @return
     */
    Boolean isJdInterceptOrderProduct(OrderProductPO orderProductPO);

    OrderReturnRiskMessageVO erpLogisticsRiskCheck(OrderAfterPO orderAfterPO,OrderProductPO orderProductPO);

    List<OrderAfterPO> getByAfsSnList(List<String> afsSnList);
}
