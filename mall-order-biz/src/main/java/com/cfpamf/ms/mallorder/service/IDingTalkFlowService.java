package com.cfpamf.ms.mallorder.service;

import com.cfpamf.ms.mallorder.dto.OrderRefundEventNotifyDTO;
import com.cfpamf.ms.mallorder.vo.OrderReturnApplyCallBackVO;

public interface IDingTalkFlowService {

	/**
	 * 退款流程引擎处理
	 */
	void orderReturnDingTalkFlowNotify(OrderRefundEventNotifyDTO notifyDTO);

	/**
	 * 流程实例回调
	 * @param orderReturnApplyCallBackVO 回调参数vo
	 * @return 是否成功
	 */
	String flowEndCallBack(OrderReturnApplyCallBackVO orderReturnApplyCallBackVO);

	/**
	 * 流程回调
	 *
	 * @param orderReturnApplyCallBackVO 回调参数vo
	 * @return 是否成功
	 */
	String flowCallBack(OrderReturnApplyCallBackVO orderReturnApplyCallBackVO);

	/**
	 * 售后主任审批流程
	 */
	boolean directorAuditRefund(String afsSn, String jobNumber, boolean isPass);

}
