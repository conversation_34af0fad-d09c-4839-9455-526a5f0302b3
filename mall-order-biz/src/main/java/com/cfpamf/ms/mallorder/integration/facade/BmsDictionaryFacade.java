package com.cfpamf.ms.mallorder.integration.facade;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.bms.facade.constants.BmsConstantCore;
import com.cfpamf.ms.bms.facade.vo.DictionaryItemVO;

import io.swagger.annotations.ApiOperation;

/**
 * @author:老K
 * @description:字典操作相关对外接口
 * @date:2018-12-20 14:32
 **/
@FeignClient(name = "bms-service-biz", url = "${bms.api.url}")
public interface BmsDictionaryFacade {

    @ApiOperation(value = "按字典类型编码获取字典明细",notes = "/dictionary/items/{typeCode}?systemId=1,2,3")
    @GetMapping(value = "/dictionary/items/{typeCode}")
    Result<List<DictionaryItemVO>> getDictionaryItemsByTypeCode(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization, @RequestParam("systemId") Integer systemId, @PathVariable("typeCode") String typeCode);
}