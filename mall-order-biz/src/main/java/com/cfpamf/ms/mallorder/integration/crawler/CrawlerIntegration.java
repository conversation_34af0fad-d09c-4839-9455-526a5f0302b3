package com.cfpamf.ms.mallorder.integration.crawler;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.cfpamf.ms.mallorder.controller.fegin.facade.CrawlerFacade;
import com.cfpamf.ms.mallshop.api.StoreExpenseFeignClient;
import com.slodon.bbc.core.response.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.Objects;

/*
店铺域对接
 */
@Slf4j
@Component
public class CrawlerIntegration {

    @Autowired
    private CrawlerFacade crawlerFacade;

    public Result<Boolean> existFactoryNo(String factoryNo){
        Result<Boolean> result = new Result<>();
        try {
            Result<Boolean> existResult = crawlerFacade.existFactoryNo(factoryNo);
            if (!existResult.isSuccess()) {
                result.setSuccess(false);
                result.addError(String.valueOf(ErrorCodeEnum.C.CALL_EXCEPTION.getCode()),
                        String.format("查询厂商编号信息接口返回失败,factoryNo:%s",factoryNo),"");
                return result;
            }
            result.setSuccess(true);
            result.setData(existResult.getData());
            return result;
        } catch (Exception ex) {
            result.setSuccess(false);
            result.addError(String.valueOf(ErrorCodeEnum.C.CALL_EXCEPTION.getCode()),
                    String.format("查询厂商编号信息接口出现未知异常,factoryNo:%s",factoryNo),"");
            return result;
        }
    }
}
