package com.cfpamf.ms.mallorder.scheduler;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.constant.SentenceConst;
import com.cfpamf.ms.mallorder.common.enums.OrderRefundRevokingPartyEnum;
import com.cfpamf.ms.mallorder.common.enums.OrderReturnStatus;
import com.cfpamf.ms.mallorder.common.enums.RefundType;
import com.cfpamf.ms.mallorder.common.enums.ReturnTypeEnum;
import com.cfpamf.ms.mallorder.common.help.SystemSettingObtainHelper;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.OperatorDTO;
import com.cfpamf.ms.mallorder.po.OrderAfterPO;
import com.cfpamf.ms.mallorder.po.OrderReturnPO;
import com.cfpamf.ms.mallorder.req.base.RevokeRefundBaseRequest;
import com.cfpamf.ms.mallorder.service.IOrderAfterService;
import com.cfpamf.ms.mallorder.service.IOrderReturnService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 退货退款客户发货超时
 *
 * 商家退货退款审批通过，买家指定时间（默认10天）未发货，自动撤销退款
 */
@Component
@Slf4j
public class OrderAfterCustomerDeliverOverTimeJob {

    @Autowired
    private IOrderReturnService orderReturnService;
    @Autowired
    private IOrderAfterService orderAfterService;

    @Autowired
    private SystemSettingObtainHelper systemSettingObtainHelper;

    @XxlJob(value = "OrderAfterCustomerDeliverOverTimeJob")
    public ReturnT<String> execute(String s) {
        XxlJobHelper.log("=============== START OrderAfterCustomerDeliverOverTimeJob , DATE :{}", LocalDateTime.now());
        // 查询为退货退款且处于商家同意退货退款申请的订单
        LambdaQueryWrapper<OrderReturnPO> orderReturnQuery = Wrappers.lambdaQuery();
        orderReturnQuery.eq(OrderReturnPO::getReturnType, ReturnTypeEnum.RETURN_AND_REFUND.getValue())
                .eq(OrderReturnPO::getState, OrderReturnStatus.STORE_AGREE_RETURN.getValue())
                .eq(OrderReturnPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                .select(OrderReturnPO::getAfsSn);
        List<OrderReturnPO> orderReturns = orderReturnService.list(orderReturnQuery);
        if (CollectionUtil.isEmpty(orderReturns)) {
            return ReturnT.SUCCESS;
        }

        // 查询发货超过指定时间的订单
        LambdaQueryWrapper<OrderAfterPO> orderAfterQuery = Wrappers.lambdaQuery();
        orderAfterQuery.in(OrderAfterPO::getAfsSn, orderReturns.stream().map(OrderReturnPO::getAfsSn).collect(Collectors.toList()))
                .eq(OrderAfterPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                .le(OrderAfterPO::getStoreAuditTime, LocalDateTime.now().plusDays(-systemSettingObtainHelper.getTimeLimitOfAfsMemberSend()))
                .select(OrderAfterPO::getAfsSn, OrderAfterPO::getOrderSn);
        List<OrderAfterPO> orderAfters = orderAfterService.list(orderAfterQuery);
        List<String> overTimeAfsSns = orderAfters.stream().map(OrderAfterPO::getAfsSn).collect(Collectors.toList());

        XxlJobHelper.log("自动撤销的退款单数量为{}", orderAfters.size());

        // 自动撤销退款
        RevokeRefundBaseRequest revokeRefundBaseRequest = new RevokeRefundBaseRequest();
        revokeRefundBaseRequest.setChannel("WEB");
        revokeRefundBaseRequest.setRemark(SentenceConst.AUTO_REVOKE_MEMBER_DELIVERY_OVERTIME_AFS);
        for (OrderAfterPO orderAfter : orderAfters) {
            XxlJobHelper.log("退款单{}发起自动撤销退款", orderAfter.getAfsSn());
            revokeRefundBaseRequest.setAfsSn(orderAfter.getAfsSn());
            try {
                OrderReturnPO orderReturnPO = orderReturnService.getByAfsSn(orderAfter.getAfsSn());
                // 恢复额度的自动撤销，需要对应的售后中的退款单都在商家审批通过，待用户发货状态，且都超期
                if (RefundType.RESTORE_LIMIT.getValue().equals(orderReturnPO.getRefundType())
                        && !this.restoreLimitRefundCheck(orderAfter, overTimeAfsSns)) {
                    continue;
                }
                orderReturnService.revokeRefund(revokeRefundBaseRequest, OrderRefundRevokingPartyEnum.AUTO_REJECT,
                        new OperatorDTO(CommonConst.SYSTEM_ID, CommonConst.ADMIN_NAME_EN, CommonConst.SYSTEM_ID, CommonConst.ADMIN_PHONE));
            } catch (Exception e) {
                log.warn("自动撤销退款单【{}】异常", orderAfter.getAfsSn(), e);
            }
        }

        XxlJobHelper.log("=============== END OrderAfterCustomerDeliverOverTimeJob, DATE :{}", LocalDateTime.now());

        return ReturnT.SUCCESS;
    }

    /**
     * 恢复额度的自动撤销，需要对应的售后中的退款单都在商家审批通过，待用户发货状态，且都超期
     */
    private boolean restoreLimitRefundCheck(OrderAfterPO orderAfterPO, List<String> overTimeAfsSns) {

        log.info("restoreLimitRefundCheck afsSn {}, overTimeAfsSns {}", orderAfterPO.getAfsSn(), overTimeAfsSns);

        // 查询对应所有的售后单是否都在商家收货状态
        Integer afsSnCount = orderReturnService.lambdaQuery().eq(OrderReturnPO::getOrderSn, orderAfterPO.getOrderSn())
                .in(OrderReturnPO::getState, OrderReturnStatus.duringRefundStatus())
                .ne(OrderReturnPO::getState, OrderReturnStatus.STORE_AGREE_RETURN.getValue()).count();
        log.info("restoreLimitRefundCheck count {}", afsSnCount);
        if (afsSnCount > 0) {
            return false;
        }

        // 是否都属于超期售后单
        List<OrderReturnPO> restoreLimitAfsSns = orderReturnService.lambdaQuery().eq(OrderReturnPO::getOrderSn, orderAfterPO.getOrderSn())
                .eq(OrderReturnPO::getState, OrderReturnStatus.STORE_AGREE_RETURN.getValue()).list();
        log.info("restoreLimitRefundCheck restoreLimitAfsSns {}", restoreLimitAfsSns);
        for (OrderReturnPO orderReturnPO : restoreLimitAfsSns) {
            if (!overTimeAfsSns.contains(orderReturnPO.getAfsSn())) {
                return false;
            }
        }
        return true;
    }


}
