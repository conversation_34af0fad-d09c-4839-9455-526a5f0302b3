package com.cfpamf.ms.mallorder.po;

import com.cfpamf.ms.mallorder.common.enums.OrderProductDeliveryEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单货品明细表
 */
@Data
public class OrderProduct implements Serializable {
    private static final long serialVersionUID = -6852129665331958072L;
    @ApiModelProperty("订单货品id")
    private Long orderProductId;

    @ApiModelProperty("成本")
    private BigDecimal cost;

    @ApiModelProperty("税率")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "金融规则编号")
    private String financeRuleCode;

    @ApiModelProperty("金融规则标签")
    private String ruleTag;

    @ApiModelProperty("订单号")
    private String orderSn;

    @ApiModelProperty("商家ID")
    private Long storeId;

    @ApiModelProperty("商家名称")
    private String storeName;

    @ApiModelProperty("会员ID")
    private Integer memberId;

    @ApiModelProperty("商品分类ID")
    private Integer goodsCategoryId;

/*    @ApiModelProperty("商品分类名称")
    private String goodsCategoryName;*/

    @ApiModelProperty("商品id")
    private Long goodsId;

    @ApiModelProperty("商品名称")
    private String goodsName;

    @ApiModelProperty("货品图片")
    private String productImage;

    @ApiModelProperty("规格详情")
    private String specValues;

    @ApiModelProperty("货品ID")
    private Long productId;

/*    @ApiModelProperty("货品名称")
    private String productName;*/

    @ApiModelProperty("货品单价，与订单表中goods_amount对应")
    private BigDecimal productShowPrice;

    @ApiModelProperty("商品数量")
    private Integer productNum;

    @ApiModelProperty("货品分摊的活动优惠总额，与订单表中activity_discount_amount对应")
    private BigDecimal activityDiscountAmount;

    @ApiModelProperty("乡助卡优惠金额")
    private String xzCardAmount;

    @ApiModelProperty("活动优惠明细，json存储(对应List<PromotionInfo>)")
    private String activityDiscountDetail;

    @ApiModelProperty("店铺活动优惠金额（包含优惠券）")
    private BigDecimal storeActivityAmount;

    @ApiModelProperty("平台活动优惠金额（包含积分、优惠券）")
    private BigDecimal platformActivityAmount;

    @ApiModelProperty("店铺优惠券优惠金额")
    private BigDecimal storeVoucherAmount;

    @ApiModelProperty("平台优惠券优惠金额")
    private BigDecimal platformVoucherAmount;

    @ApiModelProperty("使用积分数量")
    private Integer integral;

    @ApiModelProperty("积分抵扣金额")
    private BigDecimal integralCashAmount;

    @ApiModelProperty("订单货品总金额（用户支付金额，发生退款时最高可退金额）（=货品单价*数量-活动优惠总额）")
    private BigDecimal moneyAmount;

    @ApiModelProperty(value = "落地价")
    private BigDecimal landingPrice;

    @ApiModelProperty(value = "含税售价")
    private BigDecimal taxPrice;

    @ApiModelProperty(value = "分享用户")
    private String usrNo;

    @ApiModelProperty("平台佣金比率")
    private BigDecimal commissionRate;

    @ApiModelProperty("平台佣金")
    private BigDecimal commissionAmount;

    @ApiModelProperty("平台服务费")
    private BigDecimal serviceFee;

    @ApiModelProperty("代运营服务费")
    private BigDecimal thirdpartnarFee;

    @ApiModelProperty("订单佣金")
    private BigDecimal orderCommission;

    @ApiModelProperty("业务佣金")
    private BigDecimal businessCommission;

    @ApiModelProperty("拼团团队id（拼团活动使用）")
    private Integer spellTeamId;

    @ApiModelProperty("是否是赠品，0、不是；1、是；")
    private Integer isGift;

    @ApiModelProperty("赠品分组id，默认为0，有赠品关系时大于0")
    private Integer giftGroup;

    @ApiModelProperty("赠品ID 0:不是赠品；大于0：是赠品，存赠品的ID")
    private Integer giftId;

    @ApiModelProperty("退货数量，默认为0")
    private Integer returnNumber;

    @ApiModelProperty("换货数量，默认为0")
    private Integer replacementNumber;

    @ApiModelProperty("是否评价:0-未评价，1-已评价")
    private Integer isComment;

    @ApiModelProperty("评价时间")
    private Date commentTime;

    @ApiModelProperty(value = "售后按钮，100-退款（商家未发货），200-退款（商家发货,买家未收货），300-申请售后，401-退款中，402-退款完成,403-换货中，404-换货完成", hidden = true)
    private Integer afsButton;

    @ApiModelProperty(value = "售后单号,查看售后详情用", hidden = true)
    private String afsSn;

    @ApiModelProperty(value = "售后状态：100-买家申请仅退款；101-买家申请退货退款；102-买家退货给商家；200-商家处理仅退款申请；201-商家处理退货退款申请；300-退款完成；301-退款关闭（商家拒绝退款申请）", hidden = true)
    private Integer afsState;

    @ApiModelProperty("是否正在售后，0-无售后，1-正在售后")
    private Integer isHasAfs;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "是否可用:1-是 0-否")
    private Integer enabledFlag;
    
    @ApiModelProperty(value = "临时性增加字段，用于promotion识别调用来源：tcc，默认为空")
    private String triggerSource;

    @ApiModelProperty("履约渠道：1-标准电商，2-云中鹤，3-京东物流")
    private Integer performanceChannel;

    @ApiModelProperty("履约服务：0-常规，1-供应商履约，2-安装服务，3-自提，4-农机厂商，5-现款现货")
    private String performanceService;

    @ApiModelProperty("供应商编码")
    private String supplierCode;

    @ApiModelProperty("供应商名称")
    private String supplierName;

    @ApiModelProperty("SKU维度----货号")
    private String productCode;

    @ApiModelProperty("发货状态：0-待发货 1-已发货 2-出库中 3-部分发货")
    private Integer deliveryState;

}