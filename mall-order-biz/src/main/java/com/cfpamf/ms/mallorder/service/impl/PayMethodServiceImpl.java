package com.cfpamf.ms.mallorder.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cfpamf.cmis.common.base.CommonResult;
import com.cfpamf.cmis.common.base.PageBean;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.common.ms.vo.PageVO;
import com.cfpamf.mallpayment.facade.enums.PayWayEnum;
import com.cfpamf.ms.bizconfig.facade.request.ProductEssentialQueryRequet;
import com.cfpamf.ms.bizconfig.facade.request.ProductRequest;
import com.cfpamf.ms.bizconfig.facade.vo.product.ProductVo;
import com.cfpamf.ms.bizconfig.facade.vo.product.RepaymentRelationVo;
import com.cfpamf.ms.bms.facade.vo.DictionaryItemVO;
import com.cfpamf.ms.customer.facade.vo.CustCreditLimitVo;
import com.cfpamf.ms.customer.facade.vo.user.UserBaseInfoVo;
import com.cfpamf.ms.loan.facade.request.external.mall.CdmallPreconditionCheckRequest;
import com.cfpamf.ms.loan.facade.vo.external.mall.OrderInfoVo;
import com.cfpamf.ms.loan.facade.vo.external.mall.OverallPreWithdrawRequest;
import com.cfpamf.ms.loan.facade.vo.external.mall.PayModeListVO;
import com.cfpamf.ms.loan.facade.vo.external.mall.PayModeVO;
import com.cfpamf.ms.mallgoods.facade.api.ProductFeignClient;
import com.cfpamf.ms.mallgoods.facade.request.ProductAreaDTO;
import com.cfpamf.ms.mallgoods.facade.vo.Goods;
import com.cfpamf.ms.mallgoods.facade.vo.Product;
import com.cfpamf.ms.mallgoods.facade.vo.ProductPriceVO;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.builder.OrderBuilder;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.enums.*;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.common.util.DateUtil;
import com.cfpamf.ms.mallorder.common.util.OrderLocalUtils;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.*;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.enums.PayWayShowStatusEnum;
import com.cfpamf.ms.mallorder.integration.cust.CustomerIntegration;
import com.cfpamf.ms.mallorder.integration.facade.BmsIntegration;
import com.cfpamf.ms.mallorder.integration.facade.LoanProductFacade;
import com.cfpamf.ms.mallorder.integration.facade.MallPaymentFacade;
import com.cfpamf.ms.mallorder.integration.loan.LoanPayIntegration;
import com.cfpamf.ms.mallorder.integration.shop.StoreIntegration;
import com.cfpamf.ms.mallorder.mapper.OrderPresellMapper;
import com.cfpamf.ms.mallorder.mapper.PayMethodMapper;
import com.cfpamf.ms.mallorder.model.BankTransferModel;
import com.cfpamf.ms.mallorder.model.OrderPayModel;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.req.WayMerchantSyncRequest;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.vo.*;
import com.cfpamf.ms.mallpromotion.constant.PromotionConst;
import com.cfpamf.ms.mallshop.api.StoreFeignClient;
import com.cfpamf.ms.mallshop.dto.PayMethodStoreQueryDTO;
import com.cfpamf.ms.mallshop.request.StoreQueryReq;
import com.cfpamf.ms.mallshop.resp.PaymethodStoreVO;
import com.cfpamf.ms.mallshop.resp.StoreAccountBookInfo;
import com.cfpamf.ms.mallshop.vo.NewStoreVo;
import com.google.common.collect.Maps;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.exception.MallException;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import com.slodon.bbc.core.util.FileUrlUtil;
import com.slodon.bbc.core.util.WebUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 支付配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-24
 */
@Service
@Slf4j
public class PayMethodServiceImpl extends ServiceImpl<PayMethodMapper, PayMethodPO> implements IPayMethodService {

    // 客户中心支付编号与电商支付方式对应关系
    public static final Map<String, PayMethodEnum> CUST_PAY_CODE_MAP = new HashedMap() {
        {
            put("01", PayMethodEnum.CREDIT_PAY);
            put("02", PayMethodEnum.ENJOY_PAY);
            put("03", PayMethodEnum.FOLLOW_HEART);
            put("04", PayMethodEnum.FOLLOW_HEART);
        }
    };

    @Resource
    private PayMethodMapper payMethodMapper;

    @Autowired
    private IBzPayMethodTrackService payMethodTrackService;

    @Autowired
    private StoreFeignClient storeFeignClient;

    @Autowired
    private LoanProductFacade loanProductFacade;

    @Autowired
    private IBzOrderPayBlacklistService iBzOrderPayBlacklistService;

    @Autowired
    private IBzOrderPayWhitelistService iBzOrderPayWhitelistService;

    @Autowired
    private IBzOrderPayCategoryService orderPayCategoryService;

    @Autowired
    private IPayMethodService iPayMethodService;

    @Autowired
    private CustomerIntegration customerIntegration;
    @Autowired
    private StoreIntegration storeIntegration;
    @Resource
    private OrderPayModel orderPayModel;

    @Autowired
    private IOrderService iOrderService;

    @Autowired
    private IOrderProductService iOrderProductService;

    @Autowired
    private ProductFeignClient productFeignClient;

    @Autowired
    private IOrderExtendService orderExtendService;

    @Autowired
    private LoanPayIntegration loanPayIntegration;

    @Autowired
    private BankTransferModel bankTransferModel;

    @Autowired
    private IOrderProductService orderProductService;

    @Resource
    private OrderPresellMapper orderPresellMapper;

    @Value("${presell.paymethods.deposit}")
    private String depositPayMethods;

    @Value("${presell.paymethods.remain}")
    private String remainPayMethods;

    @Autowired
    private OrderLocalUtils orderLocalUtils;

    @Autowired
    private MallPaymentFacade mallPaymentFacade;

    @Autowired
    private BmsIntegration bmsIntegration;

    @Resource
    private HttpServletRequest request;

    @Resource
    private UserInfoComponent userInfoComponent;

    private static String convertLoanPayModel(PayWayEnum payWayEnum) {
        if (payWayEnum == PayWayEnum.ENJOY_PAY) {
            return "enjoypay";
        } else if (payWayEnum == PayWayEnum.CREDIT_PAY) {
            return "credit";
        } else if (payWayEnum == PayWayEnum.FOLLOW_HEART) {
            return "followheart";
        }
        return "";
    }

    public static PayMethodEnum convertPayModelToPayMethod(String payModel) {
        if ("enjoypay".equals(payModel)) {
            return PayMethodEnum.ENJOY_PAY;
        } else if ("credit".equals(payModel)) {
            return PayMethodEnum.CREDIT_PAY;
        } else if ("followheart".equals(payModel)) {
            return PayMethodEnum.FOLLOW_HEART;
        } else if ("followheartcycle".equals(payModel)) {
            return PayMethodEnum.FOLLOW_HEART;
        }
        return null;
    }

    @Override
    public JsonResult insertPayMethodMerchant(Long vendorId) {
        if (vendorId == null) {
            return SldResponse.fail("商家id不能为空");
        }
        PayMethodStoreQueryDTO dto = new PayMethodStoreQueryDTO();
        dto.setPageSize(100000);
        dto.setCurrent(1);
        dto.setMerchantIds(Collections.singletonList(vendorId.toString()));

        List<PaymethodStoreVO> paymethodStoreVOS = new ArrayList<>();

        try {
            JsonResult<Page<PaymethodStoreVO>> pagePayMethodStoreList = storeFeignClient.pagePayMethodStoreList(dto);
            if (200 == (pagePayMethodStoreList.getState()) && pagePayMethodStoreList.getData() != null) {
                paymethodStoreVOS = pagePayMethodStoreList.getData().getRecords();
                if (paymethodStoreVOS.size() < 1) {
                    throw new BusinessException("未查询到该店铺");
                }
            }
        } catch (Exception e) {
            log.error("插入默认微信支付：查询店铺列表失败:{},原因:{}", JSONObject.toJSONString(dto), e);
        }

        PaymethodStoreVO paymethodStoreVO = paymethodStoreVOS.get(0);

        PayMethodMerchantV2ListDTO listDTO = new PayMethodMerchantV2ListDTO();
        listDTO.setIsOwnStore(paymethodStoreVO.getIsOwnStore());
        listDTO.setStoreId(paymethodStoreVO.getStoreId());
        listDTO.setStoreName(paymethodStoreVO.getStoreName());

        PayMethodMerchantV2DTO merchantV2DTO = new PayMethodMerchantV2DTO();
        merchantV2DTO.setMerchants(Collections.singletonList(listDTO));
        merchantV2DTO.setOperateUserName("微信通过");
        merchantV2DTO.setPayMethodId(2L);

        this.insertMerchantV2(merchantV2DTO);

        PayMethodMerchantV2DTO merchantV2AlipayDTO = new PayMethodMerchantV2DTO();
        merchantV2AlipayDTO.setMerchants(Collections.singletonList(listDTO));
        merchantV2AlipayDTO.setOperateUserName("支付宝通过");
        merchantV2AlipayDTO.setPayMethodId(1L);

        this.insertMerchantV2(merchantV2AlipayDTO);

        return SldResponse.success("新增成功");
    }

    /**** @param queryDTO
     * @description :分页查询支付方式列表
     */
    @Override
    public JsonResult<Page<PayMethodPageVO>> pagePayMethodList(PayMethodReq queryDTO) {
        Page<PayMethodPageVO> page = new Page<>(queryDTO.getCurrent(), queryDTO.getPageSize());
        List<PayMethodPO> payMethodPOS = payMethodMapper.pagePayMethodList(page, queryDTO);
        List<PayMethodPageVO> payMethodVOS = payMethodPOS.stream().map(x -> {
            PayMethodPageVO payMethodVO = new PayMethodPageVO();
            BeanUtils.copyProperties(x, payMethodVO);
            payMethodVO.setPayIconUrl(FileUrlUtil.getFileUrl(x.getPayIcon(), null));
            return payMethodVO;
        }).collect(Collectors.toList());
        page.setRecords(payMethodVOS);
        return SldResponse.success(page);
    }

    //=================================================适用商家V1.0

    /**** @param dto
     * @return com.slodon.bbc.core.response.JsonResult
     * @description :保存支付方式
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public JsonResult savePayMethod(PayMethodDTO dto) {
        PayMethodPO payMethodPO = new PayMethodPO();
        payMethodPO.setPayIcon(dto.getPayIconPath());
        BeanUtils.copyProperties(dto, payMethodPO);

        if (dto.getId() == 0) {
            save(payMethodPO);
        } else {
            updateById(payMethodPO);
        }

        BzPayMethodTrackPO bzPayMethodTrackPO = new BzPayMethodTrackPO();
        bzPayMethodTrackPO.setPayMethodId(payMethodPO.getId().toString());
        bzPayMethodTrackPO.setFlowName("修改支付方式");
        bzPayMethodTrackPO.setOperateTime(DateUtil.getNow());
        bzPayMethodTrackPO.setOperateUserName(dto.getOperateUserName());
        bzPayMethodTrackPO.setOperateResult("通过");
        bzPayMethodTrackPO.setChannel("WEB");
        payMethodTrackService.save(bzPayMethodTrackPO);

        return SldResponse.success("保存成功");
    }

    /**** @param dto
     * @return com.slodon.bbc.core.response.JsonResult
     * @description :新增商家
     */
    @Deprecated
    @Override
    @Transactional(rollbackFor = Exception.class)
    public JsonResult insertMerchant(PayMethodMerchantDTO dto) {

        List<String> newMerchant = dto.getMerchantIds();

        if (CollectionUtils.isEmpty(newMerchant)) {
            return SldResponse.fail("请选择需要新增的商家");
        }
        PayMethodPO payMethodPO = getOne(Wrappers.lambdaQuery(PayMethodPO.class)
                .eq(PayMethodPO::getId, dto.getPayMethodId())
                .last("limit 1"));
        if (payMethodPO == null) {
            return SldResponse.fail("支付方式不存在，请刷新");
        }
        List<String> merchantDb = Arrays.asList(payMethodPO.getSupportMerchant().split(","));

        //筛选数据库中没有的商家
        List<String> needInsert = newMerchant.stream().filter(x -> !merchantDb.contains(x)).collect(Collectors.toList());
        if (needInsert.size() < 1) {
            return SldResponse.success("新增成功");
        }
        String insertMerchant = String.join(",", needInsert);

        if (StringUtils.isEmpty(payMethodPO.getSupportMerchant())) {
            payMethodPO.setSupportMerchant(insertMerchant);
        } else {
            payMethodPO.setSupportMerchant(payMethodPO.getSupportMerchant() + "," + insertMerchant);
        }
        updateById(payMethodPO);

        BzPayMethodTrackPO bzPayMethodTrackPO = new BzPayMethodTrackPO();
        bzPayMethodTrackPO.setPayMethodId(dto.getPayMethodId().toString());
        bzPayMethodTrackPO.setFlowName("适用商家|新增");
        bzPayMethodTrackPO.setOperateTime(DateUtil.getNow());
        bzPayMethodTrackPO.setOperateUserName(dto.getOperateUserName());
        bzPayMethodTrackPO.setOperateResult("通过");
        bzPayMethodTrackPO.setChannel("WEB");
        bzPayMethodTrackPO.setRemark("本次支付方式Id:" + dto.getPayMethodId() + "新增商家" + insertMerchant);
        payMethodTrackService.save(bzPayMethodTrackPO);

        return SldResponse.success("新增成功");
    }

    /**** @param queryDTO
     * @description :分页查询适用商家列表
     */
    @Deprecated
    @Override
    public JsonResult<Page<PaymethodMerchantVO>> pageMerchantList(PayMethodMerchantReq queryDTO) {

        Integer pageIndex = queryDTO.getCurrent();
        Integer pageSize = queryDTO.getPageSize();

        Page<PaymethodMerchantVO> page = new Page<>(pageIndex, pageSize);

        PayMethodPO po = getOne(Wrappers.lambdaQuery(PayMethodPO.class)
                .eq(PayMethodPO::getId, queryDTO.getPayMethodId())
                .last("limit 1"));

        List<String> merchantIds = Arrays.asList(po.getSupportMerchant().split(","));
        List<String> subMerchantIds = merchantIds.stream().skip((pageIndex - 1) * pageSize).limit(pageSize)
                .collect(Collectors.toList());

        PayMethodStoreQueryDTO dto = new PayMethodStoreQueryDTO();
        dto.setPageSize(100000);
        dto.setCurrent(1);
        dto.setMerchantIds(subMerchantIds);

        List<PaymethodStoreVO> paymethodStoreVOS = null;

        try {
            JsonResult<Page<PaymethodStoreVO>> pagePayMethodStoreList = storeFeignClient.pagePayMethodStoreList(dto);
            if (200 == (pagePayMethodStoreList.getState()) && pagePayMethodStoreList.getData() != null) {
                paymethodStoreVOS = pagePayMethodStoreList.getData().getRecords();
            }
        } catch (Exception e) {
            log.info("查询店铺列表失败:{}", JSONObject.toJSONString(dto));
        }

        List<PaymethodStoreVO> finalPaymethodStoreVOS = paymethodStoreVOS;

        List<PaymethodMerchantVO> merchantVOS = subMerchantIds.stream().map(x -> {
            PaymethodMerchantVO paymethodMerchantVO = new PaymethodMerchantVO();
            paymethodMerchantVO.setVendorId(x);
            if (!CollectionUtils.isEmpty(finalPaymethodStoreVOS)) {
                finalPaymethodStoreVOS.stream().filter(y -> y.getStoreId().equals(x)).findFirst().ifPresent(z -> {
                    paymethodMerchantVO.setStoreName(z.getStoreName());
                    paymethodMerchantVO.setIsOwnStore(z.getIsOwnStore());
                });
            }
            return paymethodMerchantVO;
        }).collect(Collectors.toList());

        page.setTotal(merchantIds.size());
        page.setRecords(merchantVOS);

        return SldResponse.success(page);
    }

    /**** @param dto
     * @return com.slodon.bbc.core.response.JsonResult
     * @description :删除商家
     */
    @Deprecated
    @Override
    @Transactional(rollbackFor = Exception.class)
    public JsonResult deleteMerchant(PayMethodMerchantDTO dto) {
        if (CollectionUtils.isEmpty(dto.getMerchantIds())) {
            return SldResponse.fail("请选择需要删除的商家");
        }
        List<String> needDelete = dto.getMerchantIds();

        PayMethodPO payMethodPO = getOne(Wrappers.lambdaQuery(PayMethodPO.class)
                .eq(PayMethodPO::getId, dto.getPayMethodId())
                .last("limit 1"));
        if (payMethodPO == null) {
            return SldResponse.fail("支付方式不存在，请刷新");
        }
        List<String> merchantDb = Arrays.asList(payMethodPO.getSupportMerchant().split(","));

        List<String> newMerchant = merchantDb.stream().filter(x -> !needDelete.contains(x)).collect(Collectors.toList());

        payMethodPO.setSupportMerchant(String.join(",", newMerchant));
        updateById(payMethodPO);

        BzPayMethodTrackPO bzPayMethodTrackPO = new BzPayMethodTrackPO();
        bzPayMethodTrackPO.setPayMethodId(dto.getPayMethodId().toString());
        bzPayMethodTrackPO.setFlowName("适用商家|删除");
        bzPayMethodTrackPO.setOperateTime(DateUtil.getNow());
        bzPayMethodTrackPO.setOperateUserName(dto.getOperateUserName());
        bzPayMethodTrackPO.setOperateResult("通过");
        bzPayMethodTrackPO.setChannel("WEB");
        bzPayMethodTrackPO.setRemark("本次支付方式Id:" + dto.getPayMethodId() + "删除商家" + needDelete);

        payMethodTrackService.save(bzPayMethodTrackPO);

        return SldResponse.success("删除成功");
    }

    /**
     * @param dto
     * @return com.slodon.bbc.core.response.JsonResult
     * @description : 新增商品
     */
    @Override
    public JsonResult insertGoods(PayMethodGoodsDTO dto) {
        PayMethodPO payMethodPO = getOne(Wrappers.lambdaQuery(PayMethodPO.class)
                .eq(PayMethodPO::getId, dto.getPayMethodId())
                .last("limit 1"));
        if (payMethodPO == null) {
            return SldResponse.fail("支付方式不存在，请刷新");
        }
        //批量查询商品，获取三级类目等信息
        List<Long> productList = dto.getGoodsListDTOS().stream().map(PayMethodGoodsListDTO::getProductId).collect(Collectors.toList());
        List<Product> productListByProductIds = productFeignClient.getProductListByProductIds(productList);

        Map<Long, Product> longProductMap = productListByProductIds.stream().collect(Collectors.toMap(Product::getProductId, item -> item, (v1, v2) -> (v2)));

        List<BzOrderPayBlacklistPO> blackDb = iBzOrderPayBlacklistService.lambdaQuery().eq(BzOrderPayBlacklistPO::getPayId, dto.getPayMethodId()).list();
        List<Long> productDb = blackDb.stream().map(BzOrderPayBlacklistPO::getProductId).collect(Collectors.toList());

        List<BzOrderPayBlacklistPO> needAdd = new ArrayList<>();
        List<String> list = new ArrayList<>();

        for (PayMethodGoodsListDTO payMethodGoodsListDTO : dto.getGoodsListDTOS()) {
            if (!productDb.contains(payMethodGoodsListDTO.getProductId())) {
                BzOrderPayBlacklistPO blacklistPO = new BzOrderPayBlacklistPO();
                blacklistPO.setPayId(dto.getPayMethodId());
                blacklistPO.setProductId(payMethodGoodsListDTO.getProductId());
                blacklistPO.setGoodsName(payMethodGoodsListDTO.getGoodsName());
                blacklistPO.setSpecValues(payMethodGoodsListDTO.getSpecValues());
                blacklistPO.setStoreId(payMethodGoodsListDTO.getStoreId());
                blacklistPO.setStoreName(payMethodGoodsListDTO.getStoreName());
                if (longProductMap.containsKey(payMethodGoodsListDTO.getProductId())) {
                    Product y = longProductMap.get(payMethodGoodsListDTO.getProductId());
                    blacklistPO.setSpecValueIds(y.getSpecValueIds());
                    blacklistPO.setGoodsId(y.getGoodsId());

                    List<String> strings = Arrays.asList(y.getCategoryPath().split("->"));

                    blacklistPO.setCategoryName1(strings.size() > 0 ? strings.get(0) : "");
                    blacklistPO.setCategoryName2(strings.size() > 1 ? strings.get(1) : "");
                    blacklistPO.setCategoryName3(strings.size() > 2 ? strings.get(2) : "");

                    blacklistPO.setCategoryId1(y.getCategoryId1());
                    blacklistPO.setCategoryId2(y.getCategoryId2());
                    blacklistPO.setCategoryId3(y.getCategoryId3());

                    blacklistPO.setCategoryPath(y.getCategoryPath());

                    needAdd.add(blacklistPO);
                    list.add(blacklistPO.getProductId().toString());
                }
            }
        }
        iBzOrderPayBlacklistService.saveBatch(needAdd);
        if (list.size() > 0) {
            BzPayMethodTrackPO bzPayMethodTrackPO = new BzPayMethodTrackPO();
            bzPayMethodTrackPO.setPayMethodId(dto.getPayMethodId().toString());
            bzPayMethodTrackPO.setFlowName("商品黑名单|新增");
            bzPayMethodTrackPO.setOperateTime(DateUtil.getNow());
            bzPayMethodTrackPO.setOperateUserName(dto.getOperateUserName());
            bzPayMethodTrackPO.setOperateResult("通过");
            bzPayMethodTrackPO.setChannel("WEB");
            bzPayMethodTrackPO.setRemark("本次支付方式Id:" + dto.getPayMethodId() + "黑名单新增商品" + String.join(",", list));
            payMethodTrackService.save(bzPayMethodTrackPO);
        }

        return SldResponse.success("新增成功");
    }

    /**
     * @param dto
     * @return com.slodon.bbc.core.response.JsonResult
     * @description :删除商品
     */
    @Override
    public JsonResult deleteGoods(PayMethodGoodsDeleteDTO dto) {
        if (dto.getIds().size() < 1) {
            return SldResponse.fail("请选择需要删除的商品");
        }
        List<BzOrderPayBlacklistPO> blacklistPOS = iBzOrderPayBlacklistService.lambdaQuery().in(BzOrderPayBlacklistPO::getId, dto.getIds()).list();
        List<String> collect = blacklistPOS.stream().map(x -> x.getProductId().toString()).collect(Collectors.toList());
        iBzOrderPayBlacklistService.removeByIds(dto.getIds());
        BzPayMethodTrackPO bzPayMethodTrackPO = new BzPayMethodTrackPO();
        bzPayMethodTrackPO.setPayMethodId(dto.getPayMethodId().toString());
        bzPayMethodTrackPO.setFlowName("商品黑名单|删除");
        bzPayMethodTrackPO.setOperateTime(DateUtil.getNow());
        bzPayMethodTrackPO.setOperateUserName(dto.getOperateUserName());
        bzPayMethodTrackPO.setOperateResult("通过");
        bzPayMethodTrackPO.setChannel("WEB");
        bzPayMethodTrackPO.setRemark("本次支付方式Id:" + dto.getPayMethodId() + "黑名单删除商品" + String.join(",", collect));
        payMethodTrackService.save(bzPayMethodTrackPO);
        return SldResponse.success("删除成功");
    }

    @Override
    public JsonResult<String> insertCategory(PayMethodCategoryDTO dto) {
        if (Objects.isNull(dto.getOrderPattern())) {
            throw new MallException("订单模式不能为空");
        }
        BizAssertUtil.notEmpty(dto.getCategoryListDTOS(), "请选择分类");

        List<String> dtoCategoryListDTOS = dto.getCategoryListDTOS();

        List<PayMethodCategoryListDTO> categoryListDTOS = new ArrayList<>();

        for (String categoryListDTO : dtoCategoryListDTOS) {
            String[] split = categoryListDTO.split("_");
            PayMethodCategoryListDTO category = new PayMethodCategoryListDTO();
            category.setCategoryGrade(Integer.valueOf(split[0]));
            category.setCategoryId(Integer.valueOf(split[1]));
            category.setCategoryName(split[2]);
            categoryListDTOS.add(category);
        }

        PayMethodPO payMethodPO = getOne(Wrappers.lambdaQuery(PayMethodPO.class)
                .eq(PayMethodPO::getId, dto.getPayMethodId())
                .last("limit 1"));
        if (payMethodPO == null) {
            return SldResponse.fail("支付方式不存在，请刷新");
        }
        //随心取，授信额度，和用呗使用同一套适用分类
        if (PayMethodEnum.isLoanPay(payMethodPO.getPayMethodCode())) {
            dto.setPayMethodId(CommonConst.LOAN_PAY_ID);
        }

        // 查询所有分类
        List<BzOrderPayCategoryPO> orderPayCategoryPOS = orderPayCategoryService.lambdaQuery()
                .eq(BzOrderPayCategoryPO::getPayId, dto.getPayMethodId())
                .eq(BzOrderPayCategoryPO::getOrderPattern, dto.getOrderPattern())
                .select(BzOrderPayCategoryPO::getCategoryId, BzOrderPayCategoryPO::getId)
                .list();

        //本次删除
        List<Integer> dtoCategory = categoryListDTOS.stream()
                .map(PayMethodCategoryListDTO::getCategoryId).distinct().collect(Collectors.toList());
        List<Long> needDelete = new ArrayList<>();
        List<String> list = new ArrayList<>();
        for (BzOrderPayCategoryPO orderPayCategoryPO : orderPayCategoryPOS) {
            if (!dtoCategory.contains(orderPayCategoryPO.getCategoryId())) {
                needDelete.add(orderPayCategoryPO.getId());
                list.add(orderPayCategoryPO.getCategoryId().toString());
            }
        }
        if (CollectionUtils.isNotEmpty(needDelete)) {
            orderPayCategoryService.removeByIds(needDelete);

            BzPayMethodTrackPO bzPayMethodTrackPO = new BzPayMethodTrackPO();
            bzPayMethodTrackPO.setPayMethodId(dto.getPayMethodId().toString());
            bzPayMethodTrackPO.setFlowName(dto.getOrderPattern() == 1 ? "c端" : "b端" + "适用分类|删除");
            bzPayMethodTrackPO.setOperateTime(DateUtil.getNow());
            bzPayMethodTrackPO.setOperateUserName(dto.getOperateUserName());
            bzPayMethodTrackPO.setOperateResult("通过");
            bzPayMethodTrackPO.setChannel("WEB");
            bzPayMethodTrackPO.setRemark("本次支付方式Id:" + dto.getPayMethodId() + "适用分类删除分类" + String.join(",", list));
            payMethodTrackService.save(bzPayMethodTrackPO);
        }

        //本次新增
        List<Integer> categoryIds = orderPayCategoryPOS.stream()
                .map(BzOrderPayCategoryPO::getCategoryId)
                .distinct().collect(Collectors.toList());
        List<BzOrderPayCategoryPO> saveOrderPayCategoryPos = new ArrayList();
        List<String> saveList = new ArrayList<>();
        for (PayMethodCategoryListDTO categoryListDTO : categoryListDTOS) {
            if (CollectionUtils.isNotEmpty(categoryIds)
                    && categoryIds.contains(categoryListDTO.getCategoryId())) {
                continue;
            }

            BzOrderPayCategoryPO categoryPO = new BzOrderPayCategoryPO();
            categoryPO.setPayId(dto.getPayMethodId());
            categoryPO.setOrderPattern(dto.getOrderPattern());
            categoryPO.setCategoryId(categoryListDTO.getCategoryId());
            categoryPO.setCategoryGrade(categoryListDTO.getCategoryGrade());
            categoryPO.setCategoryName(categoryListDTO.getCategoryName());
            categoryPO.setCreateBy(dto.getOperateUserName());
            categoryPO.setUpdateBy(dto.getOperateUserName());
            saveOrderPayCategoryPos.add(categoryPO);
            saveList.add(categoryPO.getCategoryId().toString());
        }

        if (CollectionUtils.isNotEmpty(saveOrderPayCategoryPos)) {
            orderPayCategoryService.saveBatch(saveOrderPayCategoryPos);

            BzPayMethodTrackPO bzPayMethodTrackPO = new BzPayMethodTrackPO();
            bzPayMethodTrackPO.setPayMethodId(dto.getPayMethodId().toString());
            bzPayMethodTrackPO.setFlowName(dto.getOrderPattern() == 1 ? "c端" : "b端" + "适用分类|新增");
            bzPayMethodTrackPO.setOperateTime(DateUtil.getNow());
            bzPayMethodTrackPO.setOperateUserName(dto.getOperateUserName());
            bzPayMethodTrackPO.setOperateResult("通过");
            bzPayMethodTrackPO.setChannel("WEB");
            bzPayMethodTrackPO.setRemark("本次支付方式Id:" + dto.getPayMethodId() + "适用分类新增分类" + String.join(",", saveList));
            payMethodTrackService.save(bzPayMethodTrackPO);
        }

        return SldResponse.success("保存成功");

    }


    @Override
    public JsonResult<List<String>> queryCategory(String payMethodId, Integer orderPattern) {
        if (Objects.isNull(orderPattern)) {
            orderPattern = OrderPatternEnum.SHOP_STREET.getValue();
        }
        BizAssertUtil.notEmpty(payMethodId, "支付方式id不能为空");
        PayMethodPO payMethodPO = getOne(Wrappers.lambdaQuery(PayMethodPO.class)
                .eq(PayMethodPO::getId, payMethodId)
                .last("limit 1"));
        if (payMethodPO == null) {
            return SldResponse.fail("支付方式不存在，请刷新");
        }
        Long id = payMethodPO.getId();
        //随心取，授信额度，和用呗使用同一套适用分类
        if (payMethodPO.getPayMethodCode().equals(PayMethodEnum.FOLLOW_HEART.getValue()) ||
                payMethodPO.getPayMethodCode().equals(PayMethodEnum.CREDIT_PAY.getValue()) ||
                payMethodPO.getPayMethodCode().equals(PayMethodEnum.ENJOY_PAY.getValue())) {
            id = CommonConst.LOAN_PAY_ID;
        }
        List<BzOrderPayCategoryPO> bzOrderPayCategoryPOS = orderPayCategoryService.lambdaQuery()
                .eq(BzOrderPayCategoryPO::getPayId, id)
                .eq(BzOrderPayCategoryPO::getOrderPattern, orderPattern)
                .list();
        List<String> returnList = new ArrayList<>();

        for (BzOrderPayCategoryPO bzOrderPayCategoryPO : bzOrderPayCategoryPOS) {
            StringBuffer sb = new StringBuffer();
            sb.append(bzOrderPayCategoryPO.getCategoryGrade()).append("_")
                    .append(bzOrderPayCategoryPO.getCategoryId()).append("_")
                    .append(bzOrderPayCategoryPO.getCategoryName());
            returnList.add(sb.toString());
        }

        return SldResponse.success(returnList);
    }


    /**
     * @param queryDTO
     * @return com.slodon.bbc.core.response.JsonResult<com.cfpamf.common.ms.vo.PageVO < com.cfpamf.ms.mallorder.vo.BzOrderPayBlacklistVO>>
     * @description :分页查询商品黑名单列表
     */
    @Override
    public JsonResult<PageVO<BzOrderPayBlacklistVO>> pageGoodsList(PayMethodGoodsReq queryDTO) {
        PageVO<BzOrderPayBlacklistVO> pageVO = new PageVO(queryDTO.getCurrent(), queryDTO.getPageSize());

        Page<BzOrderPayBlacklistPO> page = new Page<>(queryDTO.getCurrent(), queryDTO.getPageSize());

        LambdaQueryWrapper<BzOrderPayBlacklistPO> wrapper = buildBlackQueryWrapper(queryDTO);

        Page<BzOrderPayBlacklistPO> blacklistPOPage = iBzOrderPayBlacklistService.page(page, wrapper);
        pageVO.setRowTotal((int) blacklistPOPage.getTotal());
        pageVO.setPageTotal((int) blacklistPOPage.getPages());

        List<BzOrderPayBlacklistVO> collect = blacklistPOPage.getRecords().stream().map(x -> {
            BzOrderPayBlacklistVO vo = new BzOrderPayBlacklistVO();
            BeanUtils.copyProperties(x, vo);
            return vo;
        }).collect(Collectors.toList());

        pageVO.setRows(collect);
        return SldResponse.success(pageVO);
    }

    /**
     * @param queryDTO
     * @return com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<com.cfpamf.ms.mallorder.po.BzOrderPayBlacklistPO>
     * @description : 构建黑名单查询条件
     */
    private LambdaQueryWrapper<BzOrderPayBlacklistPO> buildBlackQueryWrapper(PayMethodGoodsReq queryDTO) {
        LambdaQueryWrapper<BzOrderPayBlacklistPO> wrapper = Wrappers.lambdaQuery(BzOrderPayBlacklistPO.class);
        if (StringUtils.isNotBlank(queryDTO.getName())) {
            wrapper.like(BzOrderPayBlacklistPO::getGoodsName, queryDTO.getName());
        }
        if (StringUtils.isNotBlank(queryDTO.getStoreName())) {
            wrapper.like(BzOrderPayBlacklistPO::getStoreName, queryDTO.getStoreName());
        }
        if (queryDTO.getPayMethodId() != null) {
            wrapper.eq(BzOrderPayBlacklistPO::getPayId, queryDTO.getPayMethodId());
        }
        if (queryDTO.getGoodsId() != null) {
            wrapper.eq(BzOrderPayBlacklistPO::getGoodsId, queryDTO.getGoodsId());
        }
        if (queryDTO.getProductId() != null) {
            wrapper.eq(BzOrderPayBlacklistPO::getProductId, queryDTO.getProductId());
        }
        if (queryDTO.getStoreId() != null) {
            wrapper.eq(BzOrderPayBlacklistPO::getStoreId, queryDTO.getStoreId());
        }
        if (queryDTO.getCategoryId1() != null) {
            wrapper.eq(BzOrderPayBlacklistPO::getCategoryId1, queryDTO.getCategoryId1());
        }
        if (queryDTO.getCategoryId2() != null) {
            wrapper.eq(BzOrderPayBlacklistPO::getCategoryId2, queryDTO.getCategoryId2());
        }
        if (queryDTO.getCategoryId3() != null) {
            wrapper.eq(BzOrderPayBlacklistPO::getCategoryId3, queryDTO.getCategoryId3());
        }
        if (queryDTO.getSpecValues() != null) {
            wrapper.like(BzOrderPayBlacklistPO::getSpecValues, queryDTO.getSpecValues());
        }
        return wrapper;
    }

    @Override
    public List<? extends Object> goodsList(PayMethodGoodsReq goodsReq) {
        LambdaQueryWrapper<BzOrderPayBlacklistPO> wrapper = buildBlackQueryWrapper(goodsReq);
        List<BzOrderPayBlacklistPO> list = iBzOrderPayBlacklistService.list(wrapper);
        return list;
    }

    @Override
    public Boolean initStore() {

        List<BzOrderPayWhitelistPO> payWhitelistPOS = iBzOrderPayWhitelistService.lambdaQuery().list();

        StoreQueryReq dto = new StoreQueryReq();
        dto.setPageSize(100000);
        dto.setCurrent(1);

        List<NewStoreVo> newStoreVos = storeFeignClient.listAllStore(dto).getList();
        Map<String, NewStoreVo> newStoreVoMap = newStoreVos.stream()
                .collect(Collectors.toMap(x -> x.getStoreId().toString(), item -> item, (v1, v2) -> (v2)));

        List<BzOrderPayWhitelistPO> needUpdate = new ArrayList<>();

        for (BzOrderPayWhitelistPO payWhitelistPO : payWhitelistPOS) {
            NewStoreVo newStoreVo = newStoreVoMap.get(payWhitelistPO.getStoreId());
            if (Objects.isNull(newStoreVo)) {
                continue;
            }
            BzOrderPayWhitelistPO update = new BzOrderPayWhitelistPO();
            update.setId(payWhitelistPO.getId());
            update.setRegisterPhone(newStoreVo.getVendorMobile());
            update.setIdentityName(newStoreVo.getIdentityName());
            update.setRecommendStoreId(newStoreVo.getRecommendBusiness() == null ? "" : newStoreVo.getRecommendBusiness().toString());
            update.setRecommendStoreName(newStoreVo.getRecommendBusinessName());
            needUpdate.add(update);
        }
        return  iBzOrderPayWhitelistService.updateBatchById(needUpdate);
    }

    @Override
    public JsonResult<Boolean> insertCategoryTmp(PayMethodCategoryListDTO dto) {
        BzOrderPayCategoryPO bzOrderPayCategoryPO = new BzOrderPayCategoryPO();
        bzOrderPayCategoryPO.setPayId(CommonConst.LOAN_PAY_ID);
        bzOrderPayCategoryPO.setCategoryId(dto.getCategoryId());
        bzOrderPayCategoryPO.setCategoryGrade(dto.getCategoryGrade());
        bzOrderPayCategoryPO.setCategoryName(dto.getCategoryName());
        return SldResponse.success(orderPayCategoryService.save(bzOrderPayCategoryPO));
    }

    @Override
    public JsonResult<Boolean> syncMerchant(String storeId, String beginDate, String endDate) {
        LambdaQueryWrapper<BzOrderPayWhitelistPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(BzOrderPayWhitelistPO::getPayId, Arrays.asList(9));
        if (StringUtils.isNotEmpty(storeId)) {
            String[] split = storeId.split(",");
            queryWrapper.in(BzOrderPayWhitelistPO::getStoreId, Arrays.asList(split));
        }
        if (StringUtils.isNotEmpty(beginDate) && StringUtils.isNotEmpty(endDate)) {
            queryWrapper.between(BzOrderPayWhitelistPO::getCreateTime, beginDate, endDate);
        }
        List<BzOrderPayWhitelistPO> syncMerchantList = iBzOrderPayWhitelistService.list(queryWrapper);
        if (CollectionUtils.isEmpty(syncMerchantList)) {
            return SldResponse.success(true);
        }
        Result<Boolean> sync = dealSync(syncMerchantList);
        return SldResponse.success(sync.isSuccess());
    }

    @Override
    public boolean syncMerchantIntraday() {
        Date now = DateUtil.getNow();
        String nowDay = DateUtil.format(now);

        List<BzOrderPayWhitelistPO> syncMerchantList = iBzOrderPayWhitelistService.lambdaQuery()
                // .between(BasePO::getCreateTime,nowDay,tomorrowDay)
                .in(BzOrderPayWhitelistPO::getPayId, Arrays.asList(3, 5,9))
                .last("and DATE_FORMAT(create_time,'%Y-%m-%d') = " + "'" + nowDay + "'")
                .list();
        if (CollectionUtils.isEmpty(syncMerchantList)) {
            return true;
        }
        Result<Boolean> sync = dealSync(syncMerchantList);
        return sync.isSuccess();
    }

    @Override
    public boolean addChannelWhiteList(String storeId, Integer channel) {
        if (Objects.isNull(channel)) {
            throw new MallException("增量增加商户白名单时，未指定渠道");
        }
        //云直通微信支付
        Integer yztWxPay = 1;
        //收付通微信支付
        Integer svcWxPay = 2;

        String updateChannel;
        if (yztWxPay.equals(channel)) {
            updateChannel = "wx_white";
        } else if (svcWxPay.equals(channel)) {
            updateChannel = "svc_white";
        } else {
            throw new MallException("增量增加商户白名单时，未识别渠道");
        }

        String oldWhite = orderLocalUtils.getRedisValueByKey(updateChannel);
        if (StringUtils.isEmpty(oldWhite)) {
            orderLocalUtils.setRedisValueByKey(updateChannel, storeId);
        } else {
            List<String> oldWhitelist = Arrays.asList(oldWhite.split(","));
            if (oldWhitelist.contains(storeId)) {
                return true;
            }
            String newWhiteValue = oldWhite + "," + storeId;
            orderLocalUtils.setRedisValueByKey(updateChannel, newWhiteValue);
        }
        return true;
    }

    @Override
    public boolean deleteChannelWhiteList(String storeId, Integer channel) {
        if (Objects.isNull(channel)) {
            throw new MallException("删除商户白名单时，未指定渠道");
        }
        //云直通微信支付
        Integer yztWxPay = 1;
        //收付通微信支付
        Integer svcWxPay = 2;

        String updateChannel;
        if (yztWxPay.equals(channel)) {
            updateChannel = "wx_white";
        } else if (svcWxPay.equals(channel)) {
            updateChannel = "svc_white";
        } else {
            throw new MallException("删除商户白名单时，未识别渠道");
        }
        String oldWhite = orderLocalUtils.getRedisValueByKey(updateChannel);
        if (StringUtils.isEmpty(oldWhite)) {
            return true;
        } else {
            String[] strings = oldWhite.split(",");
            List<String> oldWhiteList = new ArrayList<>(Arrays.asList(strings));
            oldWhiteList.removeIf(x -> x.equals(storeId));
            String newWhiteList = StringUtils.join(oldWhiteList, ",");
            orderLocalUtils.setRedisValueByKey(updateChannel, newWhiteList);
        }

        return false;
    }

    /**
     * 获取店铺下的支付方式
     * @param storeId storeId
     * @return
     */
    @Override
    public List<PayMethodStoreVO> getPayMethodStoreByStoreId(Long storeId) {
        //查询店铺下的支付方式
        List<BzOrderPayWhitelistPO> payWhitelistPoS = iBzOrderPayWhitelistService.getPayMethodByStoreId(storeId);
        if (CollectionUtil.isEmpty(payWhitelistPoS)){
            return Collections.emptyList();
        }
        //根据payId获取支付方式
        List<Long> payIdList = payWhitelistPoS.stream().map(BzOrderPayWhitelistPO::getPayId).collect(Collectors.toList());
        List<PayMethodPO> methodPoList = this.lambdaQuery().in(PayMethodPO::getId, payIdList).list();
        if (CollectionUtil.isEmpty(methodPoList)){
            return Collections.emptyList();
        }
        //构造payWhitelistPoS map集合
        Map<Long, List<BzOrderPayWhitelistPO>> payMethodPOMap = payWhitelistPoS.stream()
                .collect(Collectors.groupingBy(BzOrderPayWhitelistPO::getPayId));

        List<PayMethodStoreVO> payMethodStoreVOS = new ArrayList<>();
        //赋值处理
        methodPoList.forEach(payMethod -> {
            List<BzOrderPayWhitelistPO> bzOrderPayWhitelistPOS = payMethodPOMap.get(payMethod.getId());
            if (CollectionUtil.isNotEmpty(bzOrderPayWhitelistPOS)){
                bzOrderPayWhitelistPOS.forEach(bzOrderPayWhitelistPo -> {
                    PayMethodStoreVO payMethodVO = new PayMethodStoreVO();
                    payMethodVO.setStoreId(bzOrderPayWhitelistPo.getStoreId());
                    payMethodVO.setEnabledFlag(bzOrderPayWhitelistPo.getEnabledFlag());
                    payMethodVO.setPayId(payMethod.getId());
                    payMethodVO.setPayMethod(payMethod.getPayMethodCode());
                    payMethodVO.setPayMethodName(payMethod.getPayMethodName());
                    payMethodStoreVOS.add(payMethodVO);
                });
            }
        });
        return payMethodStoreVOS;
    }

    private Result<Boolean> dealSync(List<BzOrderPayWhitelistPO> syncMerchantList) {
        List<WayMerchantSyncRequest> requests = new ArrayList<>();
        //查询开通银行卡汇款店铺的收款账户信息
        List<Long> storeIdList = syncMerchantList.stream().filter(x -> x.getPayId() == 9).map(x->Long.parseLong(x.getStoreId())).collect(Collectors.toList());
        Map<Long, StoreAccountBookInfo> storeYztAccountMap = new HashMap<>(storeIdList.size());
        if (!CollectionUtils.isEmpty(storeIdList)) {
            List<StoreAccountBookInfo> storeYztAccountList = storeIntegration.batchQueryStoreYztIncomeAccount(storeIdList);
            storeYztAccountMap = storeYztAccountList.stream().collect(Collectors.toMap(StoreAccountBookInfo::getStoreId, Function.identity()));
        }
        for (BzOrderPayWhitelistPO bzOrderPayWhitelistPO : syncMerchantList) {
            WayMerchantSyncRequest wayMerchantSyncRequest = new WayMerchantSyncRequest();
            wayMerchantSyncRequest.setStoreId(bzOrderPayWhitelistPO.getStoreId());
            wayMerchantSyncRequest.setStoreName(bzOrderPayWhitelistPO.getStoreName());
            if (bzOrderPayWhitelistPO.getPayId() == 3) {
                wayMerchantSyncRequest.setPayWay(PayWayEnum.ENJOY_PAY);
            }
            if (bzOrderPayWhitelistPO.getPayId() == 5) {
                wayMerchantSyncRequest.setPayWay(PayWayEnum.FOLLOW_HEART);
            }
            if (bzOrderPayWhitelistPO.getPayId() == 9) {
                wayMerchantSyncRequest.setPayWay(PayWayEnum.BANK_TRANSFER);
                StoreAccountBookInfo storeAccountBookInfo = storeYztAccountMap.get(Long.parseLong(bzOrderPayWhitelistPO.getStoreId()));
                if (Objects.isNull(storeAccountBookInfo)) {
                    log.error("未找到店铺对应的云直通收款账户信息,storeId:{}",bzOrderPayWhitelistPO.getStoreId());
                    continue;
                }
                List<StoreAccountBookInfo.StoreAccountBook> storeAccountBookList = storeAccountBookInfo.getStoreAccountBookList();
                wayMerchantSyncRequest.setSubMchId(storeAccountBookInfo.getSecondaryMerchantId());
                wayMerchantSyncRequest.setAccountBookId(storeAccountBookList.get(0).getBalanceAcctId());
            }
            requests.add(wayMerchantSyncRequest);
        }
        Result<Boolean> sync = mallPaymentFacade.sync(requests);
        return sync;
    }

    @Override
    public Boolean initGoods() {
        List<BzOrderPayBlacklistPO> blacklistPOS = iBzOrderPayBlacklistService.lambdaQuery()
                .isNull(BzOrderPayBlacklistPO::getCategoryId1)
                .list();

        //批量查询商品，获取三级类目等信息
        List<Long> productList = blacklistPOS.stream().map(BzOrderPayBlacklistPO::getProductId).collect(Collectors.toList());
        List<Product> productListByProductIds = productFeignClient.getProductListByProductIds(productList);
        Map<Long, Product> longProductMap = productListByProductIds.stream().collect(Collectors.toMap(Product::getProductId, item -> item, (v1, v2) -> (v2)));

        List<BzOrderPayBlacklistPO> updateList = new ArrayList<>();

        for (BzOrderPayBlacklistPO blacklistPO : blacklistPOS) {
            if (longProductMap.containsKey(blacklistPO.getProductId())) {
                Product y = longProductMap.get(blacklistPO.getProductId());
                blacklistPO.setSpecValueIds(y.getSpecValueIds());
                blacklistPO.setGoodsId(y.getGoodsId());

                List<String> strings = Arrays.asList(y.getCategoryPath().split("->"));

                blacklistPO.setCategoryName1(strings.size() > 0 ? strings.get(0) : "");
                blacklistPO.setCategoryName2(strings.size() > 1 ? strings.get(1) : "");
                blacklistPO.setCategoryName3(strings.size() > 2 ? strings.get(2) : "");

                blacklistPO.setCategoryId1(y.getCategoryId1());
                blacklistPO.setCategoryId2(y.getCategoryId2());
                blacklistPO.setCategoryId3(y.getCategoryId3());

                blacklistPO.setCategoryPath(y.getCategoryPath());

                updateList.add(blacklistPO);
            }
        }
        return iBzOrderPayBlacklistService.updateBatchById(updateList);
    }

    /**
     * @param queryDTO
     * @return com.slodon.bbc.core.response.JsonResult<com.cfpamf.common.ms.vo.PageVO < com.cfpamf.ms.mallorder.vo.BzOrderPayBlacklistVO>>
     * @description :分页查询商家列表V2
     */
    @Override
    public JsonResult<PageVO<BzOrderPayWhitelistVO>> pageMerchantV2List(PayMethodMerchantReq queryDTO) {
        PageVO<BzOrderPayWhitelistVO> pageVO = new PageVO(queryDTO.getCurrent(), queryDTO.getPageSize());

        Page<BzOrderPayWhitelistPO> page = new Page<>(queryDTO.getCurrent(), queryDTO.getPageSize());

        LambdaQueryWrapper<BzOrderPayWhitelistPO> wrapper = Wrappers.lambdaQuery(BzOrderPayWhitelistPO.class);
        if (StringUtils.isNotBlank(queryDTO.getName())) {
            wrapper.like(BzOrderPayWhitelistPO::getStoreName, queryDTO.getName());
        }
        if (StringUtils.isNotBlank(queryDTO.getStoreId())) {
            wrapper.eq(BzOrderPayWhitelistPO::getStoreId, queryDTO.getStoreId());
        }
        if (StringUtils.isNotBlank(queryDTO.getRegisterPhone())) {
            wrapper.eq(BzOrderPayWhitelistPO::getRegisterPhone, queryDTO.getRegisterPhone());
        }
        if (StringUtils.isNotBlank(queryDTO.getIdentityName())) {
            wrapper.like(BzOrderPayWhitelistPO::getIdentityName, queryDTO.getIdentityName());
        }
        if (StringUtils.isNotBlank(queryDTO.getRecommendStoreId())) {
            wrapper.like(BzOrderPayWhitelistPO::getRecommendStoreId, queryDTO.getRecommendStoreId());
        }
        if (StringUtils.isNotBlank(queryDTO.getRecommendStoreName())) {
            wrapper.like(BzOrderPayWhitelistPO::getRecommendStoreName, queryDTO.getRecommendStoreName());
        }
        wrapper.eq(BzOrderPayWhitelistPO::getPayId, queryDTO.getPayMethodId());
        Page<BzOrderPayWhitelistPO> blacklistPOPage = iBzOrderPayWhitelistService.page(page, wrapper);
        pageVO.setRowTotal((int) blacklistPOPage.getTotal());
        pageVO.setPageTotal((int) blacklistPOPage.getPages());

        List<BzOrderPayWhitelistVO> collect = blacklistPOPage.getRecords().stream().map(x -> {
            BzOrderPayWhitelistVO vo = new BzOrderPayWhitelistVO();
            BeanUtils.copyProperties(x, vo);
            return vo;
        }).collect(Collectors.toList());

        pageVO.setRows(collect);
        return SldResponse.success(pageVO);
    }

    /**
     * @param dto
     * @return com.slodon.bbc.core.response.JsonResult
     * @description : 新增商家V2
     */
    @Override
    public JsonResult insertMerchantV2(PayMethodMerchantV2DTO dto) {
        PayMethodPO payMethodPO = getOne(Wrappers.lambdaQuery(PayMethodPO.class)
                .eq(PayMethodPO::getId, dto.getPayMethodId())
                .last("limit 1"));
        if (payMethodPO == null) {
            return SldResponse.fail("支付方式不存在，请刷新");
        }
        List<BzOrderPayWhitelistPO> blackDb = iBzOrderPayWhitelistService.lambdaQuery().eq(BzOrderPayWhitelistPO::getPayId, dto.getPayMethodId()).list();
        List<String> storeDb = blackDb.stream().map(BzOrderPayWhitelistPO::getStoreId).collect(Collectors.toList());

        List<BzOrderPayWhitelistPO> needAdd = new ArrayList<>();
        List<String> list = new ArrayList<>();

        for (PayMethodMerchantV2ListDTO listDTO : dto.getMerchants()) {
            if (!storeDb.contains(listDTO.getStoreId())) {
                BzOrderPayWhitelistPO blacklistPO = new BzOrderPayWhitelistPO();
                blacklistPO.setPayId(dto.getPayMethodId());
                blacklistPO.setIsOwnStore(listDTO.getIsOwnStore());
                blacklistPO.setStoreId(listDTO.getStoreId());
                blacklistPO.setStoreName(listDTO.getStoreName());
                blacklistPO.setIdentityName(listDTO.getIdentityName());
                blacklistPO.setRegisterPhone(listDTO.getRegisterPhone());
                blacklistPO.setRecommendStoreId(listDTO.getRecommendStoreId());
                blacklistPO.setRecommendStoreName(listDTO.getRecommendStoreName());

                needAdd.add(blacklistPO);

                list.add(blacklistPO.getStoreId());
            }
        }
        iBzOrderPayWhitelistService.saveBatch(needAdd);

        if (list.size() > 0) {
            BzPayMethodTrackPO bzPayMethodTrackPO = new BzPayMethodTrackPO();
            bzPayMethodTrackPO.setPayMethodId(dto.getPayMethodId().toString());
            bzPayMethodTrackPO.setFlowName("适用商家|新增");
            bzPayMethodTrackPO.setOperateTime(DateUtil.getNow());
            bzPayMethodTrackPO.setOperateUserName(dto.getOperateUserName());
            bzPayMethodTrackPO.setOperateResult("通过");
            bzPayMethodTrackPO.setChannel("WEB");
            bzPayMethodTrackPO.setRemark("本次支付方式Id:" + dto.getPayMethodId() + "适用商家新增" + String.join(",", list));
            payMethodTrackService.save(bzPayMethodTrackPO);
        }

        return SldResponse.success("新增成功");
    }

    /**
     * @param dto
     * @return com.slodon.bbc.core.response.JsonResult
     * @description :删除商家V2
     */
    @Override
    public JsonResult deleteMerchantV2(PayMethodGoodsDeleteDTO dto) {
        if (dto.getIds().size() < 1) {
            return SldResponse.fail("请选择需要删除的商家");
        }
        List<BzOrderPayWhitelistPO> whitelistPOS = iBzOrderPayWhitelistService.lambdaQuery().in(BzOrderPayWhitelistPO::getId, dto.getIds()).list();
        List<String> collect = whitelistPOS.stream().map(BzOrderPayWhitelistPO::getStoreId).collect(Collectors.toList());
        iBzOrderPayWhitelistService.removeByIds(dto.getIds());
        BzPayMethodTrackPO bzPayMethodTrackPO = new BzPayMethodTrackPO();
        bzPayMethodTrackPO.setPayMethodId(dto.getPayMethodId().toString());
        bzPayMethodTrackPO.setFlowName("适用商家|删除");
        bzPayMethodTrackPO.setOperateTime(DateUtil.getNow());
        bzPayMethodTrackPO.setOperateUserName(dto.getOperateUserName());
        bzPayMethodTrackPO.setOperateResult("通过");
        bzPayMethodTrackPO.setChannel("WEB");
        bzPayMethodTrackPO.setRemark("本次支付方式Id:" + dto.getPayMethodId() + "适用商家删除" + String.join(",", collect));
        payMethodTrackService.save(bzPayMethodTrackPO);
        return SldResponse.success("删除成功");
    }

    /**
     * @param
     * @return com.slodon.bbc.core.response.JsonResult
     * @description : 初始化适用商家V2
     */
    @Deprecated
    @Override
    public JsonResult merchantClean() {
        List<BzOrderPayWhitelistPO> list = new ArrayList<>();

        List<PayMethodPO> methodPOList = iPayMethodService.lambdaQuery().list();
        for (PayMethodPO payMethodPO : methodPOList) {
            List<String> merchantIds = Arrays.asList(payMethodPO.getSupportMerchant().split(","));

            PayMethodStoreQueryDTO dto = new PayMethodStoreQueryDTO();
            dto.setPageSize(100000);
            dto.setCurrent(1);
            dto.setMerchantIds(merchantIds);

            List<PaymethodStoreVO> paymethodStoreVOS = new ArrayList<>();

            try {
                JsonResult<Page<PaymethodStoreVO>> pagePayMethodStoreList = storeFeignClient.pagePayMethodStoreList(dto);
                if (200 == (pagePayMethodStoreList.getState()) && pagePayMethodStoreList.getData() != null) {
                    paymethodStoreVOS = pagePayMethodStoreList.getData().getRecords();
                }
            } catch (Exception e) {
                log.info("查询店铺列表失败:{}", JSONObject.toJSONString(dto));
            }

            for (PaymethodStoreVO paymethodStoreVO : paymethodStoreVOS) {
                BzOrderPayWhitelistPO whitelistPO = new BzOrderPayWhitelistPO();
                whitelistPO.setPayId(payMethodPO.getId());
                whitelistPO.setStoreId(paymethodStoreVO.getStoreId());
                whitelistPO.setStoreName(paymethodStoreVO.getStoreName());
                whitelistPO.setIsOwnStore(paymethodStoreVO.getIsOwnStore());
                list.add(whitelistPO);
            }
        }
        iBzOrderPayWhitelistService.saveBatch(list);
        return SldResponse.success("");
    }

    @Override
    public JsonResult<List<BzPayMethodTrackVO>> listPayMethodTrack(String payMethodId) {
        if (StringUtils.isEmpty(payMethodId)) {
            throw new MallException("支付方式id不能为空");
        }
        List<BzPayMethodTrackPO> payMethodTrackPOS = payMethodTrackService.list(Wrappers.lambdaQuery(BzPayMethodTrackPO.class)
                .eq(BzPayMethodTrackPO::getPayMethodId, payMethodId));
        List<BzPayMethodTrackVO> trackVOS = payMethodTrackPOS.stream().map(x -> {
            BzPayMethodTrackVO vo = new BzPayMethodTrackVO();
            BeanUtils.copyProperties(x, vo);
            return vo;
        }).collect(Collectors.toList());
        return SldResponse.success(trackVOS);
    }

    @Override
    public JsonResult<PageVO<BzPayMethodTrackVO>> listPayMethodTrackV2(String payMethodId, Integer current, Integer pageSize) {
        if (StringUtils.isEmpty(payMethodId)) {
            throw new MallException("支付方式id不能为空");
        }

        PageVO<BzPayMethodTrackVO> pageVO = new PageVO(current, pageSize);

        Page<BzPayMethodTrackPO> page = new Page<>(current, pageSize);

        LambdaQueryWrapper<BzPayMethodTrackPO> wrapper = Wrappers.lambdaQuery(BzPayMethodTrackPO.class);
        wrapper.eq(BzPayMethodTrackPO::getPayMethodId, payMethodId);


        Page<BzPayMethodTrackPO> blacklistPOPage = payMethodTrackService.page(page, wrapper);
        pageVO.setRowTotal((int) blacklistPOPage.getTotal());
        pageVO.setPageTotal((int) blacklistPOPage.getPages());

        List<BzPayMethodTrackVO> trackVOS = blacklistPOPage.getRecords().stream().map(x -> {
            BzPayMethodTrackVO vo = new BzPayMethodTrackVO();
            BeanUtils.copyProperties(x, vo);
            return vo;
        }).collect(Collectors.toList());
        pageVO.setRows(trackVOS);

        return SldResponse.success(pageVO);
    }

    /**** @param request
     * @return com.slodon.bbc.core.response.JsonResult<java.util.List < com.cfpamf.ms.bizconfig.facade.vo.product.ProductVo>>
     * @description : 查询贷款产品列表
     */
    @Override
    public JsonResult<List<ProductVo>> getProductListByQuery(ProductRequest request) {
        CommonResult<List<ProductVo>> productListByQuery = loanProductFacade.getProductListByQuery(request);
        if (productListByQuery == null) {
            return SldResponse.fail("查询贷款产品无返回");
        }
        if (productListByQuery.isSuccess()) {
            return SldResponse.success(productListByQuery.getData());
        } else {
            return SldResponse.fail(productListByQuery.getMessage());
        }
    }

    /**** @param queryRequet
     * @return com.slodon.bbc.core.response.JsonResult<com.cfpamf.cmis.common.base.PageBean < com.cfpamf.ms.bizconfig.facade.vo.product.RepaymentRelationVo>>
     * @description :查询产品信息
     */
    @Override
    public JsonResult<PageBean<RepaymentRelationVo>> queryProductEssential(ProductEssentialQueryRequet queryRequet) {
        Result<PageBean<RepaymentRelationVo>> pageBeanResult = loanProductFacade.queryProductEssential(queryRequet);
        if (pageBeanResult == null) {
            return SldResponse.fail("查询贷款产品无返回");
        }
        if (pageBeanResult.isSuccess()) {
            return SldResponse.success(pageBeanResult.getData());
        } else {
            return SldResponse.fail(pageBeanResult.getMessage());
        }
    }

    /**
     * @param paySn
     * @param channel
     * @param member
     * @param imei
     * @param utdid
     * @param mobiletype
     * @param request
     * @return java.util.List<com.cfpamf.ms.mallorder.vo.PayMethodVO>
     * @description :查询可用支付方式列表
     */
    @Override
    public List<PayMethodVO> getPayMethodVOS(String paySn, OrderCreateChannel channel, Member member, String mobiletype,
                                             String utdid, String imei, HttpServletRequest request) {
        boolean canLoanPay = true;
        boolean canBankTransfer = true;
        OrderPayPO orderPayPO = orderPayModel.getOrderPayByPaySn(paySn);

        List<OrderPO> orderPOS = iOrderService.lambdaQuery().eq(OrderPO::getPaySn, paySn).list();

        List<String> storeIds = new ArrayList<>(orderPOS.size());
        //获取所有的订单货品id集
        List<Long> orderProductIds = new ArrayList<>();
        //获取店铺id集合
        for (OrderPO orderPO : orderPOS) {
            storeIds.add(String.valueOf(orderPO.getStoreId()));
            if (orderPO.getXzCardAmount().compareTo(BigDecimal.ZERO) > 0) {
                canLoanPay = false;
            }
            if (!orderPO.getNewOrder()) {
                canBankTransfer = false;
            }
            //订单货品id获取
            List<OrderProductPO> orderProductPOS = iOrderProductService.lambdaQuery().eq(OrderProductPO::getOrderSn, orderPO.getOrderSn()).list();
            List<Long> productIds = orderProductPOS.stream().map(OrderProductPO::getProductId).collect(Collectors.toList());
            orderProductIds.addAll(productIds);

            //订单中不存在金融标签，查询任意商品行开启地区价格并有金融标签，只是用户没选金融标签，此情况禁止贷款支付
            if (StringUtils.isEmpty(orderPO.getFinanceRuleCode())) {
                //获取所有的商品id
                List<ProductAreaDTO> productAreaDTOS = productIds.stream().map(x -> {
                    ProductAreaDTO productAreaDTO = new ProductAreaDTO();
                    productAreaDTO.setProductId(x);
                    return productAreaDTO;
                }).collect(Collectors.toList());
                //批量查询商品
                List<ProductPriceVO> productPriceVOS = productFeignClient.getProductPriceByProductId(productAreaDTOS);
                for (ProductPriceVO productPriceVO : productPriceVOS) {
                    //商品行开启地区价格并有金融标签
                    if (Objects.nonNull(productPriceVO.getProductExtend())
                            && productPriceVO.getProductExtend().getIsInstalment() == 1
                            && productPriceVO.getProductExtend().getIsAreaPrice() == 1) {
                        canLoanPay = false;
                    }
                }
            }
        }

        //查询可用支付方式列表
        List<PayMethodPO> payMethods = iPayMethodService.lambdaQuery()
                .eq(PayMethodPO::getPayMethodStatus, PayMethodPO.STATUS_ON)
                .orderByAsc(PayMethodPO::getSort)
                .list();
        if (CollectionUtils.isEmpty(payMethods)) {
            throw new MallException("无可用支付方式，请联系管理员");
        }

        OrderPO orderPO = orderPOS.get(0);

        Map<PayMethodEnum, PayMethodVO> loanPayMap = new HashMap<>();

        // 1、单独处理预付定金逻辑
        OrderPresellPO orderPresellPO = dealPreSellMethod(payMethods, orderPO);

        /**
         * 筛选支付方式可用范围
         */
        List<PayMethodVO> vos = new ArrayList<>();
        for (PayMethodPO payMethod : payMethods) {

            if(StringUtils.equals(PayMethodEnum.BANK_TRANSFER.getValue(), payMethod.getPayMethodCode()) && !canBankTransfer){
                continue;
            }

            //适用的下单渠道筛选
            String supportChannels = payMethod.getSupportOrderChannel();
            if (StringUtils.isBlank(supportChannels) || !supportChannels.contains(channel.toString())) {
                continue;
            }
            // 门槛金额筛选
            if (payMethod.getMinAmount().compareTo(orderPayPO.getPayAmount()) > 0) {
                continue;
            }
            //适用商家筛选
            List<BzOrderPayWhitelistPO> whitelistPOS = iBzOrderPayWhitelistService.lambdaQuery()
                    .eq(BzOrderPayWhitelistPO::getPayId, payMethod.getId())
                    .list();

            if (CollectionUtils.isEmpty(whitelistPOS)) {
                continue;
            }
            List<String> stringList = whitelistPOS.stream().map(BzOrderPayWhitelistPO::getStoreId).collect(Collectors.toList());
            if (!fullIntersection(storeIds, stringList)) {
                continue;
            }
            // 支持下单类型筛选
            String supportOrderType = payMethod.getSupportOrderType();
            if (StringUtils.isBlank(supportOrderType)) {
                continue;
            }
            List<String> orderTypes = Arrays.asList(supportOrderType.split(","));
            if (!orderTypes.contains(String.valueOf(orderPO.getOrderType()))) {
                continue;
            }
            //商品黑名单筛选
            List<BzOrderPayBlacklistPO> blacklistPOS = iBzOrderPayBlacklistService.lambdaQuery()
                    .eq(BzOrderPayBlacklistPO::getPayId, payMethod.getId())
                    .list();
            List<Long> blackProductId = blacklistPOS.stream().map(BzOrderPayBlacklistPO::getProductId).collect(Collectors.toList());
            if (fullIntersection2(orderProductIds, blackProductId)) {
                continue;
            }
            //贷款类型筛选
            if (PayMethodEnum.isLoanPay(PayMethodEnum.valueOf(payMethod.getPayMethodCode()))) {
                if (StringUtils.isNotBlank(member.getCustNo())) {
                    PayMethodVO payMethodVO = new PayMethodVO();
                    payMethodVO.setPayMethod(payMethod.getPayMethodCode());
                    payMethodVO.setPayMethodName(payMethod.getPayMethodName());
                    payMethodVO.setSort(payMethod.getSort());
                    payMethodVO.setLoanCode(payMethod.getLoanCode());
                    payMethodVO.setPayIconUrl(FileUrlUtil.getFileUrl(payMethod.getPayIcon(), null));
                    if (canLoanPay) {
                        payMethodVO.setShowStatus(PayWayShowStatusEnum.ENABLE);
                    } else {
                        payMethodVO.setShowStatus(PayWayShowStatusEnum.UNABLE);
                        payMethodVO.setDesc("不支持分期");
                    }
                    loanPayMap.put(PayMethodEnum.valueOf(payMethod.getPayMethodCode()), payMethodVO);
                }
            } else {
                PayMethodVO payMethodVO = new PayMethodVO();
                payMethodVO.setShowStatus(PayWayShowStatusEnum.ENABLE);
                payMethodVO.setPayMethod(payMethod.getPayMethodCode());
                payMethodVO.setPayMethodName(payMethod.getPayMethodName());
                payMethodVO.setSort(payMethod.getSort());
                payMethodVO.setBalance(null);
                payMethodVO.setPayIconUrl(FileUrlUtil.getFileUrl(payMethod.getPayIcon(), null));
                //如果是银行卡汇款支付，则需要去查询客户已添加的付款卡信息
                if (StringUtils.equals(PayMethodEnum.BANK_TRANSFER.getValue(), payMethod.getPayMethodCode())) {

                    BzBankTransferPO entity = bankTransferModel.queryLastByMemberId(orderPayPO.getMemberId());
                    if (null != entity) {
                        payMethodVO.setBankTransferPaymentInfo(new PayMethodVO.BankTransferPaymentInfo(
                                entity.getPaymentAccount(), entity.getPaymentName()));
                    } else {
                        PayerLargePayacctVO payerLargePayacctVO = bankTransferModel.queryLastByPaymentUserNo(orderPO.getUserNo());
                        if (Objects.nonNull(payerLargePayacctVO)) {
                            payMethodVO.setBankTransferPaymentInfo(new PayMethodVO.BankTransferPaymentInfo(
                                    payerLargePayacctVO.getBankAcctNo(), payerLargePayacctVO.getBankAcctName()));
                        }
                    }
                }
                vos.add(payMethodVO);
            }
        }

        /**
         * 查询客户中心 信贷支付状态和余额
         */
        if (StringUtils.isNotBlank(member.getCustNo())) {
            List<CustCreditLimitVo> creditLimitVos = customerIntegration.queryLimitList(member.getCustNo());
            if (CollectionUtils.isNotEmpty(creditLimitVos)) {
                for (CustCreditLimitVo creditLimitVo : creditLimitVos) {
                    if ("E".equals(creditLimitVo.getSts())) {
                        continue;
                    }
                    PayMethodEnum payMethodEnum = CUST_PAY_CODE_MAP.get(creditLimitVo.getCreditType());

                    PayMethodVO payMethodVO = loanPayMap.get(payMethodEnum);
                    if (payMethodVO == null) {
                        continue;
                    }
                    // 用呗 前置决策判断
                    if (payMethodEnum == PayMethodEnum.ENJOY_PAY) {

                        CdmallPreconditionCheckRequest preconditionCheckRequest = new CdmallPreconditionCheckRequest();

                        List<OrderInfoVo> orderInfoList = new ArrayList<>(orderPOS.size());
                        for (OrderPO orderPO1 : orderPOS) {
                            OrderInfoVo orderInfoVo = new OrderInfoVo();
                            orderInfoVo.setOrderId(orderPO1.getOrderSn());
                            orderInfoVo.setRcmdMerchant(String.valueOf(orderPO1.getRecommendStoreId()));
                            orderInfoVo.setOrderAmt(orderPO1.getOrderAmount());
                            orderInfoList.add(orderInfoVo);
                        }
                        preconditionCheckRequest.setOrderInfoList(orderInfoList);

                        OrderExtendPO orderExtendPO = orderExtendService
                                .getOrderExtendByOrderSn(orderPOS.get(0).getOrderSn());

                        preconditionCheckRequest.setBizId(paySn);
                        preconditionCheckRequest.setCustId(member.getCustNo());
                        preconditionCheckRequest.setProductCode(payMethodVO.getLoanCode());
                        preconditionCheckRequest.setBranchCode(orderExtendPO.getBranch());
                        if (Objects.nonNull(orderPresellPO)) {
                            preconditionCheckRequest.setWithdrawAmout(orderPresellPO.getPayAmount());
                        } else {
                            preconditionCheckRequest.setWithdrawAmout(orderPayPO.getPayAmount());
                        }
                        preconditionCheckRequest.setUtdid(utdid);
                        preconditionCheckRequest.setImei(imei);
                        preconditionCheckRequest.setMobiletype(mobiletype);
                        preconditionCheckRequest.setIp(WebUtil.getRealIp(request));
                        preconditionCheckRequest.setProvince(orderExtendPO.getReceiverProvinceCode());
                        preconditionCheckRequest.setCity(orderExtendPO.getReceiverCityCode());
                        preconditionCheckRequest.setAddress(orderExtendPO.getReceiverAddress());
                        preconditionCheckRequest.setExtendInfo(Maps.newHashMap());
                        preconditionCheckRequest.setOrderInfoList(orderInfoList);

                        boolean isAllowed = loanPayIntegration.isPreconditionCheckAllow(preconditionCheckRequest);
                        if (!isAllowed) {
                            continue;
                        }
                    }

                    // 设置余额
                    payMethodVO.setBalance(creditLimitVo.getBalanceAmt());

                    // 设置展示状态
                    if ("S".equals(creditLimitVo.getSts())) {
                        // 去除用呗有效时间校验
                        if (creditLimitVo.getBalanceAmt().compareTo(orderPayPO.getPayAmount()) >= 0) {
                            if (canLoanPay) {
                                payMethodVO.setShowStatus(PayWayShowStatusEnum.ENABLE);
                            } else {
                                payMethodVO.setShowStatus(PayWayShowStatusEnum.UNABLE);
                                payMethodVO.setDesc("不支持分期");
                            }
                        } else {
                            payMethodVO.setShowStatus(PayWayShowStatusEnum.BELOW);
                            payMethodVO.setDesc("额度不足，无法享受分期");
                        }
                    } else {
                        payMethodVO.setShowStatus(PayWayShowStatusEnum.UNABLE);
                        payMethodVO.setDesc("无额度，无法享受分期");
                    }
                    vos.add(payMethodVO);
                }
            }
        }
        return vos.stream().sorted(Comparator.comparing(PayMethodVO::getSort)).collect(Collectors.toList());
    }

    /**
     * @param paySn
     * @param channel
     * @param member
     * @param mobiletype
     * @param utdid
     * @param imei
     * @param request
     * @param wifi
     * @param returnUrl
     * @return java.util.List<com.cfpamf.ms.mallorder.vo.PayMethodVO>
     * @description :查询可用支付方式列表
     */
    @Override
    public List<PayMethodVO3> getPayMethodVOS3(String paySn, OrderCreateChannel channel, Member member, String mobiletype,
                                               String utdid, String imei, HttpServletRequest request, String wifi, String returnUrl,
                                               String chan_nel, String ctversion,String checkAuthVersion) {
    	log.info("【getPayMethodVOS3】paySn:{} member:{}",paySn,JSON.toJSONString(member));
    	boolean canLoanPay = true;
        boolean canBankTransfer = true;
        boolean canAliPay = true;
        boolean useXzCard = false;
        String realIp = WebUtil.getRealIp(request);

        OrderPayPO orderPayPO = orderPayModel.getOrderPayByPaySn(paySn);

        List<OrderPO> orderPOS = iOrderService.lambdaQuery().eq(OrderPO::getPaySn, paySn).list();
        OrderExtendPO orderExtendPO = orderExtendService
                .getOrderExtendByOrderSn(orderPOS.get(0).getOrderSn());

        boolean stationMasterFlag = false;
        String stationMaterCustNo = null;
        UserBaseInfoVo userBaseInfoVo = null;

        List<String> loanPlayerList = orderPOS.stream().map(OrderPO::getLoanPayer).collect(Collectors.toList());

        if(CollectionUtils.isNotEmpty(loanPlayerList)
                && loanPlayerList.contains(LoanPayerEnum.STATION_MASTER.getCode())
                && StringUtils.isNotEmpty(orderExtendPO.getStationMaster())) {
            stationMasterFlag = true;
            //查询客户中心站长信息
            userBaseInfoVo = customerIntegration.userBaseInfo(orderExtendPO.getStationMaster());
            if (Objects.nonNull(userBaseInfoVo)
                    && Objects.nonNull(userBaseInfoVo.getCustInfoVo())
                    && Objects.nonNull(userBaseInfoVo.getCustInfoVo().getCustDetail())) {
                stationMaterCustNo = userBaseInfoVo.getCustInfoVo().getCustDetail().getLoanCustId();
            }

        }

        boolean allowWxPay = orderLocalUtils.allowWxPay(orderPOS);
        boolean unableWxPay = false;

        String unableWxPayReason = "";

        if (allowWxPay) {
            List<String> orderSnList = orderPOS.stream().map(OrderPO::getOrderSn).collect(Collectors.toList());
            List<OrderExtendPO> orderExtendPOS = orderExtendService.lambdaQuery()
                    .in(OrderExtendPO::getOrderSn, orderSnList)
                    .list();
            for (OrderExtendPO orderExtend : orderExtendPOS) {
                JSONObject disableChannelDTOS = orderExtend.getDisableChannelList();
                if (Objects.nonNull(disableChannelDTOS)) {
                    log.info("unableWxPay:{}", disableChannelDTOS);
                    Object wxpay = disableChannelDTOS.get("WXPAY");
                    unableWxPay = wxpay != null;
                    unableWxPayReason = wxpay != null ? wxpay.toString() : "";
                    break;
                }
            }
        }

        List<String> storeIds = new ArrayList<>(orderPOS.size());
        //获取所有的订单货品id集
        List<Long> orderProductIds = new ArrayList<>();
        //获取所有商品信息
        List<ProductPriceVO> productPriceVOList = new ArrayList<>();
        //获取店铺id集合
        for (OrderPO orderPO : orderPOS) {
            storeIds.add(String.valueOf(orderPO.getStoreId()));
            if (orderPO.getXzCardAmount().compareTo(BigDecimal.ZERO) > 0) {
                log.info("【getPayMethodVOS3】paySn:{} 支付方式，xzCardAmount>0", paySn);
                canLoanPay = false;
                useXzCard = true;
            }
            if (!orderPO.getNewOrder()) {
                canBankTransfer = false;
                canAliPay = false;
            }
            /*if (channel.equals(OrderCreateChannel.MINI_PRO) && orderPO.getOrderType().equals(PromotionConst.PROMOTION_TYPE_107)) {
                canBankTransfer = false;
            }*/

            //订单货品id获取
            List<OrderProductPO> orderProductPOS = iOrderProductService.lambdaQuery().eq(OrderProductPO::getOrderSn, orderPO.getOrderSn()).list();
            List<Long> productIds = orderProductPOS.stream().map(OrderProductPO::getProductId).collect(Collectors.toList());
            orderProductIds.addAll(productIds);

            //批量查询商品
            List<ProductAreaDTO> productAreaDTOS = productIds.stream().map(x -> {
                ProductAreaDTO productAreaDTO = new ProductAreaDTO();
                productAreaDTO.setProductId(x);
                return productAreaDTO;
            }).collect(Collectors.toList());
            List<ProductPriceVO> productPriceVOS = productFeignClient.getProductPriceByProductId(productAreaDTOS);
            productPriceVOList.addAll(productPriceVOS);

            //订单中不存在金融标签，查询任意商品行开启地区价格并有金融标签，只是用户没选金融标签，此情况禁止贷款支付
            if (StringUtils.isEmpty(orderPO.getFinanceRuleCode())) {
                for (ProductPriceVO productPriceVO : productPriceVOS) {
                    //商品行开启地区价格并有金融标签
                    if (Objects.nonNull(productPriceVO.getProductExtend())
                            && productPriceVO.getProductExtend().getIsInstalment() == 1
                            && productPriceVO.getProductExtend().getIsAreaPrice() == 1) {
                        log.info("【getPayMethodVOS3】支付方式，商品行开启地区价格并有金融标签。paySn:{}", paySn);
                        canLoanPay = false;
                    }
                }
            }
        }
        log.info("【getPayMethodVOS3】paySn：{}，支付方式，orderProductIds：{}",paySn , orderProductIds);

        //查询可用支付方式列表
        List<PayMethodPO> payMethods = iPayMethodService.lambdaQuery()
                .eq(PayMethodPO::getPayMethodStatus, PayMethodPO.STATUS_ON)
                .orderByAsc(PayMethodPO::getSort)
                .list();
        if (CollectionUtils.isEmpty(payMethods)) {
        	log.info("【getPayMethodVOS3】无可用支付方式：paySn {} payMethods:{}", paySn , payMethods);
            throw new MallException("无可用支付方式，请联系管理员");
        }
        //小程序情况下，客户端判断大于bms配置的版本，只会返回微信支付
        if (StringUtils.isNotEmpty(chan_nel) && OrderCreateChannel.MiniProgram.getValue().equals(chan_nel) && StringUtils.isNotEmpty(ctversion)) {
            log.info("支付接口小程序控制" + paySn);
            payMethods = filterCtvVersion(paySn, ctversion, payMethods);
        }

        OrderPO orderPO = orderPOS.get(0);
        //贷款类支付集合
        Map<PayMethodEnum, PayMethodVO3> loanPayMap = new HashMap<>();
        /**
         * 单独处理预付定金逻辑
         */
        OrderPresellPO orderPresellPO = dealPreSellMethod(payMethods, orderPO);
        log.info("【getPayMethodVOS3】支付方式，处理预付逻辑：paySn {} payMethods:{}", paySn , JSON.toJSONString(payMethods));

        //展示列表
        List<PayMethodVO3> vos = new ArrayList<>();
        //预选贷款类支付集合
        List<String> prePayModeList = new ArrayList<>();

        for (PayMethodPO payMethod : payMethods) {



            if (StringUtils.equals(PayMethodEnum.ALIPAY.getValue(), payMethod.getPayMethodCode()) && !canAliPay) {
                log.info("【getPayMethodVOS3】 支付宝支付筛选过滤，paySn：{}，支付方式:{}", paySn, payMethod.getPayMethodCode());
                continue;
            }
            if (PayMethodEnum.WXPAY.getValue().equals(payMethod.getPayMethodCode()) &&
                    orderPO.getOrderType().equals(OrderTypeEnum.PRE_SELL_DEPOSIT.getValue()) &&
                    orderPO.getPayChannel().equals(PayChannelEnum.SVC_WX.getValue())) {
                this.addGeneralUnAbleVo(vos, payMethod, "抱歉，预付订单暂不支持微信支付，请使用其他支付方式");
                continue;
            }
            if (PayMethodEnum.WXPAY.getValue().equals(payMethod.getPayMethodCode()) && !allowWxPay) {
                this.addGeneralUnAbleVo(vos, payMethod, "抱歉，该商家暂未开通微信支付，请使用其他支付方式");
                continue;
            }
            if (PayMethodEnum.WXPAY.getValue().equals(payMethod.getPayMethodCode()) && unableWxPay) {
                this.addGeneralUnAbleVo(vos, payMethod, unableWxPayReason);
                continue;
            }
            //适用的下单渠道筛选
            String supportChannels = payMethod.getSupportOrderChannel();
            if (StringUtils.isBlank(supportChannels) || !supportChannels.contains(channel.toString())) {
                log.info("【getPayMethodVOS3】适用的下单渠道筛选过滤，paySn:{}，支付方式:{}", paySn, payMethod.getPayMethodCode());
                continue;
            }
            // 门槛金额筛选
            if (payMethod.getMinAmount().compareTo(orderPayPO.getPayAmount()) > 0) {
                log.info("【getPayMethodVOS3】门槛金额筛选过滤.paySn:{}，支付方式:{}", paySn, payMethod.getPayMethodCode());
                addUnAbleVo(vos, payMethod, "未达分期服务起付金额" + payMethod.getMinAmount() + "元");
                continue;
            }
            //适用商家筛选
            List<BzOrderPayWhitelistPO> whitelistPOS = iBzOrderPayWhitelistService.lambdaQuery()
                    .eq(BzOrderPayWhitelistPO::getPayId, payMethod.getId())
                    .list();

            if (CollectionUtils.isEmpty(whitelistPOS)) {
                continue;
            }
            List<String> stringList = whitelistPOS.stream().map(BzOrderPayWhitelistPO::getStoreId).collect(Collectors.toList());
            if (!fullIntersection(storeIds, stringList)) {
                log.info("【getPayMethodVOS3】商家白名单过滤，paySn:{}，支付方式:{}", paySn, payMethod.getPayMethodCode());
                addUnAbleVo(vos, payMethod,"商家不支持分期服务");
                continue;
            }
            // 支持下单类型筛选
            String supportOrderType = payMethod.getSupportOrderType();
            if (StringUtils.isBlank(supportOrderType)) {
                continue;
            }
            List<String> orderTypes = Arrays.asList(supportOrderType.split(","));
            if (!orderTypes.contains(String.valueOf(orderPO.getOrderType()))) {
                log.info("【getPayMethodVOS3】支持下单类型筛选过滤。paySn:{}，支付方式:{}", paySn, payMethod.getPayMethodCode());
                continue;
            }
            //商品黑名单筛选
            List<BzOrderPayBlacklistPO> blacklistPOS = iBzOrderPayBlacklistService.lambdaQuery()
                    .eq(BzOrderPayBlacklistPO::getPayId, payMethod.getId())
                    .list();
            List<Long> blackProductId = blacklistPOS.stream().map(BzOrderPayBlacklistPO::getProductId).collect(Collectors.toList());
            if (fullIntersection2(orderProductIds, blackProductId)) {
                log.info("【getPayMethodVOS3】商品黑名单筛选过滤。paySn:{}，支付方式:{}", paySn, payMethod.getPayMethodCode());
                addUnAbleVo(vos, payMethod,"商品不支持分期服务");
                continue;
            }
            //商品分类筛选
            if (PayMethodEnum.isLoanPay(PayMethodEnum.valueOf(payMethod.getPayMethodCode()))) {
                List<BzOrderPayCategoryPO> orderPayCategoryPOS = orderPayCategoryService.lambdaQuery()
                        .eq(BzOrderPayCategoryPO::getPayId, CommonConst.LOAN_PAY_ID)
                        .eq(BzOrderPayCategoryPO::getOrderPattern, orderPO.getOrderPattern().equals(OrderPatternEnum.PURCHASE_CENTRE.getValue()) ? OrderPatternEnum.PURCHASE_CENTRE.getValue() : 1)
                        .list();
                List<Integer> categoryIds = orderPayCategoryPOS.stream()
                        .map(BzOrderPayCategoryPO::getCategoryId).distinct().collect(Collectors.toList());
                boolean matchCategory = isMatchCategory(paySn, productPriceVOList, categoryIds);
                if (!matchCategory){
                    addUnAbleVo(vos, payMethod,"商品分类不支持分期服务");
                    continue;
                }
            }

            if(StringUtils.equals(PayMethodEnum.BANK_TRANSFER.getValue(), payMethod.getPayMethodCode())){
                log.info("【getPayMethodVOS3】银行卡汇款支付筛选过滤，paySn：{}，支付方式:{}", paySn, payMethod.getPayMethodCode());
                //不能用&&不展示
                if(!canBankTransfer) {
                    continue;
                }
                if(orderPOS.size() > 1) {
                    this.addGeneralUnAbleVo(vos, payMethod, "抱歉，跨店铺订单暂不支持此支付方式");
                    continue;
                }
            }

            //贷款类型筛选
            if (PayMethodEnum.isLoanPay(PayMethodEnum.valueOf(payMethod.getPayMethodCode()))) {
                if(stationMasterFlag) {
                    if(StringUtils.isNotBlank(stationMaterCustNo)) {
                        log.info("【getPayMethodVOS3】stationMaterCustNo客户编号:{}，支付单号:{}", stationMaterCustNo, paySn);
                        PayMethodVO3 payMethodVO = new PayMethodVO3();
                        payMethodVO.setPayMethod(payMethod.getPayMethodCode());
                        payMethodVO.setPayMethodName(payMethod.getPayMethodName());
                        payMethodVO.setSort(payMethod.getSort());
                        payMethodVO.setLoanCode(payMethod.getLoanCode());
                        payMethodVO.setPayIconUrl(FileUrlUtil.getFileUrl(payMethod.getPayIcon(), null));
                        if (canLoanPay) {
                            payMethodVO.setShowStatus(PayWayShowStatusEnum.ENABLE);
                        } else {
                            payMethodVO.setShowStatus(PayWayShowStatusEnum.UNABLE);
                        }
                        loanPayMap.put(PayMethodEnum.valueOf(payMethod.getPayMethodCode()), payMethodVO);
                        prePayModeList.add(convertLoanPayModel(PayMethodEnum.valueOf(payMethod.getPayMethodCode()).getPayWay()));
                        if (PayMethodEnum.valueOf(payMethod.getPayMethodCode()) == PayMethodEnum.ENJOY_PAY) {
                            //预选贷款类，有用呗情况下默认给用呗专享
                            prePayModeList.add("enjoypay_vip");
                        }
                    }
                } else {
                    log.info("【getPayMethodVOS3】客户编号:{}，支付单号:{}", member.getCustNo(), paySn);
                    if (StringUtils.isNotBlank(member.getCustNo())) {
                        PayMethodVO3 payMethodVO = new PayMethodVO3();
                        payMethodVO.setPayMethod(payMethod.getPayMethodCode());
                        payMethodVO.setPayMethodName(payMethod.getPayMethodName());
                        payMethodVO.setSort(payMethod.getSort());
                        payMethodVO.setLoanCode(payMethod.getLoanCode());
                        payMethodVO.setPayIconUrl(FileUrlUtil.getFileUrl(payMethod.getPayIcon(), null));
                        if (canLoanPay) {
                            payMethodVO.setShowStatus(PayWayShowStatusEnum.ENABLE);
                        } else {
                            payMethodVO.setShowStatus(PayWayShowStatusEnum.UNABLE);
                        }
                        loanPayMap.put(PayMethodEnum.valueOf(payMethod.getPayMethodCode()), payMethodVO);
                        prePayModeList.add(convertLoanPayModel(PayMethodEnum.valueOf(payMethod.getPayMethodCode()).getPayWay()));
                        if (PayMethodEnum.valueOf(payMethod.getPayMethodCode()) == PayMethodEnum.ENJOY_PAY) {
                            //预选贷款类，有用呗情况下默认给用呗专享
                            prePayModeList.add("enjoypay_vip");
                        }
                    }
                }
            } else {
                PayMethodVO3 payMethodVO = new PayMethodVO3();
                payMethodVO.setShowStatus(PayWayShowStatusEnum.ENABLE);
                payMethodVO.setPayMethod(payMethod.getPayMethodCode());
                payMethodVO.setPayMethodName(payMethod.getPayMethodName());
                payMethodVO.setSort(payMethod.getSort());
                payMethodVO.setBalance(null);
                payMethodVO.setPayIconUrl(FileUrlUtil.getFileUrl(payMethod.getPayIcon(), null));
                //如果是银行卡汇款支付，则需要去查询客户已添加的付款卡信息
                if (StringUtils.equals(PayMethodEnum.BANK_TRANSFER.getValue(), payMethod.getPayMethodCode())) {
                    BzBankTransferPO entity = bankTransferModel.queryLastByMemberId(orderPayPO.getMemberId());
                    if (null != entity) {
                        payMethodVO.setBankTransferPaymentInfo(new PayMethodVO3.BankTransferPaymentInfo(
                                entity.getPaymentAccount(), entity.getPaymentName()));
                    } else {
                        PayerLargePayacctVO payerLargePayacctVO = bankTransferModel.queryLastByPaymentUserNo(orderPO.getUserNo());
                        if (Objects.nonNull(payerLargePayacctVO)) {
                            payMethodVO.setBankTransferPaymentInfo(new PayMethodVO3.BankTransferPaymentInfo(
                                    payerLargePayacctVO.getBankAcctNo(), payerLargePayacctVO.getBankAcctName()));
                        }
                    }
                }
                vos.add(payMethodVO);
            }
        }
        log.info("【getPayMethodVOS3】支付方式，预处理后的支付方式：{}", JSON.toJSONString(vos));
        if (CollectionUtils.isEmpty(prePayModeList)) {
            log.info("【getPayMethodVOS3】支付方式，预处理后的贷款支付方式为空：{}", paySn);
            return vos;
        }

        if(stationMasterFlag) {
            if(StringUtils.isNotBlank(stationMaterCustNo)) {
                //站长客户编号为空，则不支持贷款类支付
                if (StringUtils.isBlank(member.getCustNo())) {
                    log.info("【getPayMethodVOS3】支付方式，站长客户编号为空，则不支持贷款类支付：paySn：{},:{}", paySn, JSON.toJSONString(member));
                    return vos;
                }
            }
        } else {
            //客户编号为空，则不支持贷款类支付
            if (StringUtils.isBlank(member.getCustNo())) {
                log.info("【getPayMethodVOS3】支付方式，客户编号为空，则不支持贷款类支付：paySn：{},:{}", paySn, JSON.toJSONString(member));
                return vos;
            }
            //查询身份证号,身份证号为空则不允许贷款支付
            userBaseInfoVo = customerIntegration.userBaseInfo(member.getUserNo());

        }
        if (Objects.isNull(userBaseInfoVo) ||
                Objects.isNull(userBaseInfoVo.getCustInfoVo()) ||
                StringUtils.isEmpty(userBaseInfoVo.getCustInfoVo().getIdType()) ||
                StringUtils.isEmpty(userBaseInfoVo.getCustInfoVo().getIdNo())) {
            log.info("【getPayMethodVOS3】支付方式，客户身份校验失败，paySn:{} userBaseInfoVo:{}", paySn,JSON.toJSONString(userBaseInfoVo));
            return vos;
        }



        /**
         * 构建综合额度查询所需订单信息
         */
        List<OrderInfoVo> orderInfoVos = buildOrderInfoVos(orderPOS, orderPO);
        /**
         * 构建综合额度查询所需请求参数
         */
        OverallPreWithdrawRequest withdrawRequest = buildOverallPreWithdrawParam(member, mobiletype, utdid, imei, wifi,
                realIp, orderPayPO, orderPOS, prePayModeList, orderExtendPO, userBaseInfoVo, orderInfoVos, orderPresellPO,returnUrl, checkAuthVersion);


        PayModeListVO payMode;
        try {
            log.info("【getPayMethodVOS3】调用综合额度查询接口,paySn:{} 参数:{}",paySn, JSON.toJSONString(withdrawRequest));;
            payMode = loanPayIntegration.getAvailablePayMode(withdrawRequest);
            log.info("【getPayMethodVOS3】调用综合额度查询接口,paySn:{} 响应结果:{}",paySn, JSON.toJSONString(payMode));
        } catch (Exception e) {
            log.warn("调用综合额度查询接口异常:{}:{}", e.getMessage(), orderPayPO.getPaySn());
            return vos;
        }
        //无可用贷款类支付，则直接返回
        if (Objects.isNull(payMode) || CollectionUtils.isEmpty(payMode.getPayModeList())) {
            log.info("【getPayMethodVOS3】无可用贷款类支付，则直接返回:{}", paySn);
            return vos;
        }
        /**
         * 遍历处理贷款类支付
         */
        dealLoanPayMehtodList(orderPOS, loanPayMap, vos, payMode, canLoanPay, useXzCard);
        return vos;
    }

    private List<PayMethodPO> filterCtvVersion(String paySn, String ctversion, List<PayMethodPO> payMethods) {
        List<DictionaryItemVO> mp_version = bmsIntegration.listDictionaryItemsByTypeCode(CommonConst.MP_VERSION, CommonConst.MALL_SYSTEM_ID);
        DictionaryItemVO dictionaryItemVO = mp_version.stream().filter(x -> CommonConst.XZSH.equals(x.getItemName())).findFirst().orElse(null);
        //bms配置了乡助生活小程序版本号并状态为开启，走校验
        if (Objects.nonNull(dictionaryItemVO) && dictionaryItemVO.getItemStatus() == 1) {
            String bmsVersion = dictionaryItemVO.getItemCode();
            if (StringUtils.isEmpty(bmsVersion)) {
                return payMethods;
            }
            log.info(CommonConst.ctversion + Long.parseLong(ctversion) + CommonConst.bmsVersion + Long.parseLong(bmsVersion));
            if (Long.parseLong(ctversion) > Long.parseLong(bmsVersion)) {
                log.info("版本筛选过滤" + paySn);
                payMethods = payMethods.stream().filter(x -> x.getPayMethodCode().equals(PayMethodEnum.WXPAY.getValue())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(payMethods)) {
                    log.info("【getPayMethodVOS3】无可用支付方式：paySn {} payMethods:{}", paySn, payMethods);
                    throw new MallException("无可用支付方式，请联系管理员");
                }
            }
        }
        return payMethods;
    }

    private boolean isMatchCategory(String paySn, List<ProductPriceVO> productPriceVOList, List<Integer> categoryIds) {
        boolean matchCategory = true;
        for (ProductPriceVO productPriceVO : productPriceVOList) {
            Goods goods = productPriceVO.getGoods();
            List<Integer> goodsCategory = Arrays.asList(goods.getCategoryId1(), goods.getCategoryId2(), goods.getCategoryId3());
            if (!fullIntersection3(categoryIds, goodsCategory)) {
                matchCategory = false;
                log.info("payMethodV3 match category failed,paySn:{},goodsCategory:{}", paySn, goodsCategory);
                break;
            }
        }
        return matchCategory;
    }

    /**
     * 该方法将支付方式后置判断，仅针对金融类的失效场景使用
     * 如果想要用下面addGeneralUnAbleVo通用方法，则需要将金融支付方式判断前置
     * @param vos
     * @param payMethod
     * @param reason
     */
    private void addUnAbleVo(List<PayMethodVO3> vos, PayMethodPO payMethod,String reason) {
        if (PayMethodEnum.isLoanPay(PayMethodEnum.valueOf(payMethod.getPayMethodCode()))) {
            PayMethodVO3 payMethodVO = new PayMethodVO3(payMethod,reason);
            vos.add(payMethodVO);
        }
    }

    /**
     * 通用方法，支付方式需前置判断
     * @param vos
     * @param payMethod
     * @param reason
     */
    private void addGeneralUnAbleVo(List<PayMethodVO3> vos, PayMethodPO payMethod,String reason) {
        PayMethodVO3 payMethodVO = new PayMethodVO3(payMethod,reason);
        vos.add(payMethodVO);
    }


    @Override
    @Transactional
    public JsonResult<String> insertPayMethodStore(PayMethodStoreDTO payMethodStoreDTO) {

        // 查询该店铺已有的支付方式
        Set<Long> alreadyPayIds = iBzOrderPayWhitelistService.lambdaQuery()
                .eq(BzOrderPayWhitelistPO::getStoreId, payMethodStoreDTO.getStoreId())
                .eq(BzOrderPayWhitelistPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                .list().stream().map(BzOrderPayWhitelistPO::getPayId).collect(Collectors.toSet());

        // 保存需要增加的支付方式
        List<BzOrderPayWhitelistPO> needAdd = new ArrayList<>();
        // 保存新增日志记录
        List<BzPayMethodTrackPO> tracks = new ArrayList<>();

        // 根据支付方式编码查询支付方式的id
        for (com.cfpamf.ms.mallorder.enums.PayWayEnum payWayEnum : payMethodStoreDTO.getPayWays()) {
            String payMethodCode = payWayEnum.getValue();
            PayMethodPO payMethod = lambdaQuery().eq(PayMethodPO::getPayMethodCode, payMethodCode)
                    .eq(PayMethodPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                    .one();
            if (payMethod == null) {
                return SldResponse.fail(String.format("支付编码 %s 对应支付方式不存在，请确认", payMethodCode));
            }
            if (!alreadyPayIds.contains(payMethod.getId())) {
                BzOrderPayWhitelistPO blacklistPO = new BzOrderPayWhitelistPO();
                blacklistPO.setPayId(payMethod.getId());
                blacklistPO.setIsOwnStore(payMethodStoreDTO.getIsOwnStore());
                blacklistPO.setStoreId(payMethodStoreDTO.getStoreId());
                blacklistPO.setStoreName(payMethodStoreDTO.getStoreName());
                needAdd.add(blacklistPO);

                BzPayMethodTrackPO bzPayMethodTrackPO = new BzPayMethodTrackPO();
                bzPayMethodTrackPO.setPayMethodId(String.valueOf(payMethod.getId()));
                bzPayMethodTrackPO.setFlowName("适用商家|新增");
                bzPayMethodTrackPO.setOperateTime(DateUtil.getNow());
                bzPayMethodTrackPO.setOperateUserName(payMethodStoreDTO.getOperateUserName());
                bzPayMethodTrackPO.setOperateResult("通过");
                bzPayMethodTrackPO.setChannel("WEB");
                bzPayMethodTrackPO.setRemark(String.format("商家%s新增支付方式id：%d", payMethodStoreDTO.getStoreName(), payMethod.getId()));
                tracks.add(bzPayMethodTrackPO);
            }
        }

        // 批量保存支付方式
        iBzOrderPayWhitelistService.saveBatch(needAdd);
        // 批量保存日志
        payMethodTrackService.saveBatch(tracks);

        return SldResponse.success();
    }

    @Override
    public JsonResult<String> deletePayMethodStore(PayMethodStoreDTO payMethodStoreDTO) {
        // 根据支付方式编码查询支付方式的id
        List<Long> payMethodIds = new ArrayList<>();
        for (com.cfpamf.ms.mallorder.enums.PayWayEnum payWayEnum : payMethodStoreDTO.getPayWays()) {
            String payMethodCode = payWayEnum.getValue();
            PayMethodPO payMethod = lambdaQuery().eq(PayMethodPO::getPayMethodCode, payMethodCode)
                    .eq(PayMethodPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                    .one();
            if (payMethod == null) {
                return SldResponse.fail(String.format("支付编码 %s 对应支付方式不存在，请确认", payMethodCode));
            }
            payMethodIds.add(payMethod.getId());
        }

        List<BzOrderPayWhitelistPO> whitelistPos = iBzOrderPayWhitelistService.lambdaQuery()
                .in(BzOrderPayWhitelistPO::getPayId, payMethodIds)
                .eq(BzOrderPayWhitelistPO::getStoreId, payMethodStoreDTO.getStoreId())
                .eq(BzOrderPayWhitelistPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                .list();
        if (CollectionUtil.isNotEmpty(whitelistPos)) {
            List<Long> whitelistIds = whitelistPos.stream().map(BzOrderPayWhitelistPO::getId)
                    .collect(Collectors.toList());
            iBzOrderPayWhitelistService.removeByIds(whitelistIds);

            // 新增日志
            BzPayMethodTrackPO bzPayMethodTrackPO = new BzPayMethodTrackPO();
            bzPayMethodTrackPO.setPayMethodId(payMethodIds.toString());
            bzPayMethodTrackPO.setFlowName("适用商家|删除");
            bzPayMethodTrackPO.setOperateTime(DateUtil.getNow());
            bzPayMethodTrackPO.setOperateUserName(payMethodStoreDTO.getOperateUserName());
            bzPayMethodTrackPO.setOperateResult("通过");
            bzPayMethodTrackPO.setChannel("WEB");
            bzPayMethodTrackPO.setRemark(String.format("商家%s删除支付方式id：%s", payMethodStoreDTO.getStoreName(),
                    payMethodIds));
            payMethodTrackService.save(bzPayMethodTrackPO);
        }

        return SldResponse.success();
    }

    @Override
    public List<PayMethodVOV2> listPayMethodByCode(List<String> payMethodCodes) {
        BizAssertUtil.notEmpty(payMethodCodes, "支付方式编码不能为空");
        List<PayMethodPO> payMethodPos = super.lambdaQuery()
                .in(PayMethodPO::getPayMethodCode, payMethodCodes)
                .list();

        List<PayMethodVOV2> payMethodVos = new ArrayList<>(payMethodPos.size());
        if (CollectionUtil.isNotEmpty(payMethodPos)) {
            for (PayMethodPO payMethodPo : payMethodPos) {
                PayMethodVOV2 payMethodVo = new PayMethodVOV2();
                BeanUtils.copyProperties(payMethodPo, payMethodVo);
                payMethodVos.add(payMethodVo);
            }
        }

        return payMethodVos;
    }

    /**
     * @return com.cfpamf.mallpayment.facade.request.loan.OverallPreWithdrawRequest
     * @description : 构建综合查询接口所需参数
     * @Param userBaseInfoVo : 支付人客户中心的信息
     */
    private OverallPreWithdrawRequest buildOverallPreWithdrawParam(Member member, String mobiletype, String utdid, String imei,
                                                                   String wifi, String realIp, OrderPayPO orderPayPO, List<OrderPO> orderPOS,
                                                                   List<String> prePayModeList, OrderExtendPO orderExtendPO,
                                                                   UserBaseInfoVo userBaseInfoVo, List<OrderInfoVo> orderInfoVos,
                                                                   OrderPresellPO orderPresellPO, String returnUrl, String checkAuthVersion) {
        Map<String, String> extendMap = new HashMap<>();
        //综合额度查询
        OverallPreWithdrawRequest withdrawRequest = new OverallPreWithdrawRequest();

        List<String> loanPlayerList = orderPOS.stream().map(x -> x.getLoanPayer()).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(loanPlayerList) && loanPlayerList.contains(LoanPayerEnum.STATION_MASTER.getCode())) {
            log.info("使用站长额度支付");
            try {
                if (!StringUtils.isEmpty(orderExtendPO.getStationMaster())) {
                    Member stationMasterMember = OrderBuilder.getStationMasterUser(request);
                    withdrawRequest.setCustId(userBaseInfoVo.getCustInfoVo().getId().toString());
                    withdrawRequest.setLoanCustId(userBaseInfoVo.getCustInfoVo().getCustDetail().getLoanCustId());
                    withdrawRequest.setName(stationMasterMember.getMemberName());
                    withdrawRequest.setMobile(stationMasterMember.getMemberMobile());
                    withdrawRequest.setIdNo(userBaseInfoVo.getCustInfoVo().getIdNo());
                    withdrawRequest.setIdNo(userBaseInfoVo.getCustInfoVo().getIdNo());
                }
            }catch (Exception e) {
                log.warn("buildOverallPreWithdrawParam error");
            }

        } else {
            withdrawRequest.setCustId(userBaseInfoVo.getCustInfoVo().getId().toString());
            withdrawRequest.setLoanCustId(orderExtendPO.getCustomerId());
            withdrawRequest.setName(member.getMemberName());
            withdrawRequest.setMobile(member.getMemberMobile());
            withdrawRequest.setIdNo(userBaseInfoVo.getCustInfoVo().getIdNo());
            withdrawRequest.setIdNo(userBaseInfoVo.getCustInfoVo().getIdNo());
        }

        withdrawRequest.setBranchNo(orderExtendPO.getBranch());
        withdrawRequest.setOrderInfoList(orderInfoVos);
        if (Objects.nonNull(orderPresellPO)) {
            withdrawRequest.setWithdrawAmount(orderPresellPO.getPayAmount());
            withdrawRequest.setOutBizNo(orderPresellPO.getPayNo());
        } else {
            withdrawRequest.setWithdrawAmount(orderPayPO.getPayAmount());
            withdrawRequest.setOutBizNo(orderPayPO.getPaySn());
        }
        withdrawRequest.setProvince(orderExtendPO.getReceiverProvinceCode());
        withdrawRequest.setCity(orderExtendPO.getReceiverCityCode());
        withdrawRequest.setAddress(orderExtendPO.getReceiverAddress());
        withdrawRequest.setPrePayModeList(prePayModeList);
        withdrawRequest.setUtdid(utdid);
        withdrawRequest.setIp(realIp);
        withdrawRequest.setMobileType(mobiletype);
        withdrawRequest.setImei(imei);
        withdrawRequest.setWifi(wifi);
        withdrawRequest.setOrderSource(CommonConst.MALL_ORDER);
        withdrawRequest.setLoanManager(orderExtendPO.getManager());
        extendMap.put("returnUrl", returnUrl);
        if(StringUtils.isNotEmpty(checkAuthVersion)) {
            extendMap.put("checkAuthVersion", checkAuthVersion);
        }
        withdrawRequest.setExtendInfo(extendMap);
        withdrawRequest.setBranchName(orderExtendPO.getBranchName());
        withdrawRequest.setAreaCode(orderExtendPO.getAreaCode());
        withdrawRequest.setAreaName(orderExtendPO.getAreaName());
        withdrawRequest.setMallChannel(orderPOS.get(0).getChannel());
        Integer orderPattern = orderPOS.get(0).getOrderPattern();
        OrderPatternEnum orderPatternEnum = OrderPatternEnum.valueOf(orderPattern);
        withdrawRequest.setOrderPattern(orderPatternEnum == null ? null : orderPatternEnum.getValue().toString());
        withdrawRequest.setOrderPatternDesc(orderPatternEnum == null ? null : orderPatternEnum.getDesc());
        return withdrawRequest;
    }

    /**
     * @param orderPOS
     * @param orderPO
     * @return java.util.List<com.cfpamf.mallpayment.facade.vo.loan.OrderInfoVo>
     * @description : 构建综合查询所需订单信息
     */
    private List<OrderInfoVo> buildOrderInfoVos(List<OrderPO> orderPOS, OrderPO orderPO) {
        List<OrderInfoVo> orderInfoVos = new ArrayList<>();

        for (OrderPO orderDb : orderPOS) {
            //查询订单商品信息
            List<OrderProductPO> orderProductPOS = orderProductService.lambdaQuery()
                    .eq(OrderProductPO::getOrderSn, orderPO.getOrderSn())
                    .list();
            //批量查询商品信息
            Map<Long, Product> longProductMap = orderLocalUtils.getLongProductMap(orderProductPOS);
            for (OrderProductPO orderProductPO : orderProductPOS) {
                //商家信息
                OrderInfoVo orderInfoVo = new OrderInfoVo();
                orderInfoVo.setOrderId(orderDb.getOrderSn());
                orderInfoVo.setRcmdMerchant(orderDb.getRecommendStoreId().toString());
                orderInfoVo.setOrderAmt(orderDb.getOrderAmount());
                orderInfoVo.setMerchantId(orderDb.getStoreId().toString());
                orderInfoVo.setMerchantName(orderDb.getStoreName());
                orderInfoVo.setCommodityName(orderProductPO.getGoodsName());
                orderInfoVo.setIsOwnStore(orderDb.getStoreIsSelf());
                orderInfoVo.setMerchantType(orderDb.getStoreIsSelf().toString());
                //商品信息
                orderInfoVo.setCommodityId(orderProductPO.getGoodsId().toString());
                List<String> strings = Arrays.asList(orderProductPO.getGoodsCategoryPath().split("->"));

                orderInfoVo.setCategoryName1(strings.size() > 0 ? strings.get(0) : "");
                orderInfoVo.setCategoryName2(strings.size() > 1 ? strings.get(1) : "");
                orderInfoVo.setCategoryName3(strings.size() > 2 ? strings.get(2) : "");
                //处理类目id
                if (longProductMap.containsKey(orderProductPO.getProductId())) {
                    Product product = longProductMap.get(orderProductPO.getProductId());
                    orderInfoVo.setCategoryId1(product.getCategoryId1().toString());
                    orderInfoVo.setCategoryId2(product.getCategoryId2().toString());
                    orderInfoVo.setCategoryId3(product.getCategoryId3().toString());
                }
                orderInfoVos.add(orderInfoVo);
            }
        }
        return orderInfoVos;
    }

    /**
     * @param orderPOS   本次支付单下的所有订单
     * @param loanPayMap 预筛选后的贷款类支付
     * @param vos        返回给前端的支付方式集合
     * @param payMode    综合额度查询后的贷款类支付
     * @param canLoanPay
     * @return void
     * @description : 处理贷款类支付
     */
    private void dealLoanPayMehtodList(List<OrderPO> orderPOS, Map<PayMethodEnum, PayMethodVO3> loanPayMap, List<PayMethodVO3> vos, PayModeListVO payMode, boolean canLoanPay, boolean useXzCard) {
        log.info("处理贷款类支付");
        //遍历处理综合额度查询后的贷款类支付
        for (PayModeVO payModeVO : payMode.getPayModeList()) {
            if (Boolean.FALSE.equals(payModeVO.getDisplayable())) {
                continue;
            }
            PayMethodVO3 methodVO = new PayMethodVO3();
            PayMethodVO3 payMethodVO;
            //用呗专享,返回一种虚拟支付方式给前端进行展示
            if ("enjoypay_vip".equals(payModeVO.getPayMode())) {
                payMethodVO = loanPayMap.get(PayMethodEnum.ENJOY_PAY);
                //预选贷款类支付无用呗，则用呗专享也不进行展示
                log.info("预选贷款类支付无用呗1");
            } else {
                PayMethodEnum payMethodEnum = convertPayModelToPayMethod(payModeVO.getPayMode());
                if (Objects.isNull(payMethodEnum)) {
                    continue;
                }
                payMethodVO = loanPayMap.get(payMethodEnum);
                log.info("预选贷款类支付无用呗2");
            }
            if (Objects.isNull(payMethodVO)) {
                continue;
            }
            BeanUtils.copyProperties(payMethodVO, methodVO);
            if ("enjoypay_vip".equals(payModeVO.getPayMode())) {
                methodVO.setIsEnjoyPayVip(CommonEnum.YES.getCode());
            } else{
                methodVO.setIsEnjoyPayVip(CommonEnum.NO.getCode());
            }

            methodVO.setRecommendSort(payModeVO.getRecommendSort() == null ? 0L : payModeVO.getRecommendSort());
            methodVO.setDisplayAmountName(payModeVO.getDisplayAmountName());
            methodVO.setDisplayAmountSummary(payModeVO.getDisplayAmountSummary());
            methodVO.setCustCreditAmountId(payModeVO.getCustCreditAmountId());
            methodVO.setCreditType(payModeVO.getCreditType());
            methodVO.setQuotaType(payModeVO.getQuotaType());
            methodVO.setCreditType(payModeVO.getCreditType());
            methodVO.setProductCode(payModeVO.getProductCode());
            methodVO.setSceneType(payModeVO.getSceneType());

            methodVO.setBalance(payModeVO.getAvailableWithdrawAmount());
            methodVO.setHardRuleCode(payModeVO.getHardRuleCode());
            methodVO.setExtendInfo(payModeVO.getExtendInfo());

            if (orderPOS.size() > 1) {
                methodVO.setShowStatus(PayWayShowStatusEnum.UNABLE);
                methodVO.setShowBappReason("不支持多订单合并支付，请分开下单");
                methodVO.setShowCappReason("不支持多订单合并支付，请分开下单");
            } else if (useXzCard) {
                methodVO.setShowStatus(PayWayShowStatusEnum.UNABLE);
                methodVO.setDesc("您已使用乡助卡，暂不支持分期");
                methodVO.setShowBappReason("您已使用乡助卡，暂不支持分期");
                methodVO.setShowCappReason("您已使用乡助卡，暂不支持分期");
            } else if (canLoanPay) {
                methodVO.setShowStatus(Boolean.TRUE.equals(payModeVO.getPayable()) ? PayWayShowStatusEnum.ENABLE : PayWayShowStatusEnum.UNABLE);
                methodVO.setShowBappReason(payModeVO.getShowBappReason());
                methodVO.setShowCappReason(payModeVO.getShowCappReason());
            } else {
                methodVO.setShowStatus(PayWayShowStatusEnum.UNABLE);
                methodVO.setDesc("您未选择分期服务，暂不支持分期");
                methodVO.setShowBappReason("您未选择分期服务，暂不支持分期");
                methodVO.setShowCappReason("您未选择分期服务，暂不支持分期");
            }

            vos.add(methodVO);
        }
    }

    /**
     * @param payMethods
     * @param orderPO
     * @return java.util.List<com.cfpamf.ms.mallorder.po.PayMethodPO>
     * @description : 支付方式单独处理预付定金逻辑
     */
    private OrderPresellPO dealPreSellMethod(List<PayMethodPO> payMethods, OrderPO orderPO) {
        OrderPresellPO presellPO = null;

        log.info("支付方式，处理预付逻辑,支付方式：{},订单信息:{}", JSON.toJSONString(payMethods), JSON.toJSONString(orderPO));
        // 1、预付订金
        boolean preSaleFlag = orderPO.getOrderType().equals(PromotionConst.PROMOTION_TYPE_107);
        //订金支付标识
        boolean preSaleDepositPaymentFlag = false;
        //尾款支付标识
        boolean preSaleBalancePayment = false;
        if (preSaleFlag) {
            long beginTime = System.currentTimeMillis();
            log.info("getPayMethodVOS PROMOTION_TYPE_107 start...");
            List<OrderPresellPO> orderPreSellPOList = orderPresellMapper.getPreSellOrderDetailByOrderSn(orderPO.getOrderSn());
            orderPreSellPOList.sort(Comparator.comparing(OrderPresellPO::getType));
            for (OrderPresellPO orderPresellPO : orderPreSellPOList) {
                //订金且未支付
                if (orderPresellPO.getType() == 1 && orderPresellPO.getPayStatus() == 1) {
                    preSaleDepositPaymentFlag = true;
                    break;
                }
                if (orderPresellPO.getType() == 2 && orderPresellPO.getPayStatus() == 1) {
                    preSaleBalancePayment = true;
                    presellPO = orderPresellPO;
                    break;
                }
            }

            List<String> payMethodsStrList = new ArrayList<>();
            if (preSaleDepositPaymentFlag) {
                payMethodsStrList = Arrays.asList(depositPayMethods.split(","));

            } else if (preSaleBalancePayment) {
                payMethodsStrList = Arrays.asList(remainPayMethods.split(","));
            }
            List<String> finalPayMethodsStrList = payMethodsStrList;

            payMethods.removeIf(next -> !finalPayMethodsStrList.contains(next.getPayMethodCode()));
            log.info("getPayMethodVOS PROMOTION_TYPE_107 end...耗时：[{}]", System.currentTimeMillis() - beginTime);
        }
        return presellPO;
    }

    /**
     * 判断集合2是否包含集合1所有元素
     *
     * @param s1
     * @param s2
     * @return boolean
     * <AUTHOR>
     * @date 2021/6/26 11:58
     */
    public boolean fullIntersection(List<String> s1, List<String> s2) {
        for (String s : s1) {
            if (!s2.contains(s)) {
                return false;
            }
        }
        return true;
    }

    /**
     * @param s1
     * @param s2
     * @return boolean
     * @description : 判断集合1中任意元素是否在集合2中存在
     */
    public boolean fullIntersection2(List<Long> s1, List<Long> s2) {
        for (Long s : s1) {
            if (s2.contains(s)) {
                return true;
            }
        }
        return false;
    }

    /**
     * @param s1
     * @param s2
     * @return boolean
     * @description : 判断集合1中任意元素是否在集合2中存在
     */
    public boolean fullIntersection3(List<Integer> s1, List<Integer> s2) {
        for (Integer s : s1) {
            if (s2.contains(s)) {
                return true;
            }
        }
        return false;
    }
}
