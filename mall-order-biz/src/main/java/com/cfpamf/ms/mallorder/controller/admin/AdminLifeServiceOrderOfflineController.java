package com.cfpamf.ms.mallorder.controller.admin;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.event.SyncReadListener;
import com.cfpamf.ms.mallorder.dto.LifeServiceOrderOfflineDTO;
import com.cfpamf.ms.mallorder.po.LifeServiceOrderOfflinePO;
import com.cfpamf.ms.mallorder.service.ILifeServiceOrderOfflineService;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/admin/lifeServiceOrderOffline")
@Api(tags = "admin-生活服务线下订单")
public class AdminLifeServiceOrderOfflineController {
    @Autowired
    private ILifeServiceOrderOfflineService lifeServiceOrderOfflineService;

    @ApiOperation("生活服务线下订单导入")
    @PostMapping("/import")
    public JsonResult<Void> importExcel(@RequestParam("file") MultipartFile file) throws Exception {
        SyncReadListener excelListener = new SyncReadListener();
        EasyExcel.read(file.getInputStream(), LifeServiceOrderOfflineDTO.class, excelListener).doReadAll();
        List<LifeServiceOrderOfflineDTO> importList = excelListener.getList().stream()
                .map(obj -> (LifeServiceOrderOfflineDTO) obj)
                .collect(Collectors.toList());
        List<LifeServiceOrderOfflinePO> poList = importList.stream().map(dto -> {
            LifeServiceOrderOfflinePO po = new LifeServiceOrderOfflinePO();
            BeanUtils.copyProperties(dto, po);
            return po;
        }).collect(Collectors.toList());
        lifeServiceOrderOfflineService.batchSave(poList);
        return SldResponse.success();
    }
} 