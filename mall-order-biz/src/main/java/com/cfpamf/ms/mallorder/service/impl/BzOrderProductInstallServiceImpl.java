package com.cfpamf.ms.mallorder.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.po.BzOrderProductInstallPO;
import com.cfpamf.ms.mallorder.mapper.BzOrderProductInstallMapper;
import com.cfpamf.ms.mallorder.req.OrderProductInstallSaveRequest;
import com.cfpamf.ms.mallorder.service.IBzOrderProductInstallService;
import com.cfpamf.framework.autoconfigure.mybatis.BaseRepoServiceImpl;
import com.cfpamf.ms.mallorder.vo.OrderProductInstallLogVO;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 订单商品安装资料表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-14
 */
@Service
public class BzOrderProductInstallServiceImpl extends BaseRepoServiceImpl<BzOrderProductInstallMapper, BzOrderProductInstallPO> implements IBzOrderProductInstallService {

    @Override
    public void save(OrderProductInstallSaveRequest request) {
        BzOrderProductInstallPO bzOrderProductInstallPO = OrderProductInstallSaveRequest.of(request);
        super.save(bzOrderProductInstallPO);
    }

    @Override
    public List<OrderProductInstallLogVO> queryTrackList(Long orderProductId) {
        List<BzOrderProductInstallPO> installSupplierList = this.queryByOrderProductIds(Arrays.asList(orderProductId));
        return installSupplierList
                .stream()
                .map(OrderProductInstallLogVO::of)
                .collect(Collectors.toList());
    }

    @Override
    public List<BzOrderProductInstallPO> queryByOrderProductIds(List<Long> orderProductIds) {
        LambdaQueryWrapper<BzOrderProductInstallPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(BzOrderProductInstallPO::getOrderProductId, orderProductIds);
        queryWrapper.eq(BzOrderProductInstallPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
        queryWrapper.orderBy(true, false, BzOrderProductInstallPO::getStoreId);
        queryWrapper.orderBy(true, false, BzOrderProductInstallPO::getCreateTime);
        return getBaseMapper().selectList(queryWrapper);
    }
}
