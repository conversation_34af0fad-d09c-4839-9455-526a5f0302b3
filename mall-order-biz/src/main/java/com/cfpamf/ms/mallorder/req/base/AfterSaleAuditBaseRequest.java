package com.cfpamf.ms.mallorder.req.base;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 退款审核请求基类
 */
@Data
public class AfterSaleAuditBaseRequest {

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("下单操作渠道：H5-浏览器H5；APP-乡助APP；WE_CHAT-微信浏览器；MINI_PRO-小程序；OMS-运管物资")
    private String channel;

    @ApiModelProperty("退款扣罚金额")
    private BigDecimal refundPunishAmount;

}
