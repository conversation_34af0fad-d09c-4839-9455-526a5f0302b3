package com.cfpamf.ms.mallorder.service.impl;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cfpamf.ms.mallorder.common.enums.TimeRangeEnum;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.EmployeePrivilegeConditionDTO;
import com.cfpamf.ms.mallorder.dto.HouseholdCleanCategoryCollectVO;
import com.cfpamf.ms.mallorder.po.pgrpt.AdsJiaqingWorkDateOrderDetailHfpPO;
import com.cfpamf.ms.mallorder.req.HouseholdCleanSalesReportRequest;
import com.cfpamf.ms.mallorder.service.EmployeePrivilegeService;
import com.cfpamf.ms.mallorder.service.HouseholdCleanWorkbenchService;
import com.cfpamf.ms.mallorder.service.IAdsJiaqingWorkDateOrderDetailHfpService;
import com.cfpamf.ms.mallorder.vo.HouseholdCleanCategoryProportionSalesAmountVO;
import com.cfpamf.ms.mallorder.vo.HouseholdCleanSalesItemVO;
import com.cfpamf.ms.mallorder.vo.HouseholdCleanSalesOrgPerformanceVO;
import com.cfpamf.ms.mallorder.vo.HouseholdCleanSalesReportVO;
import com.cfpamf.ms.mallorder.vo.HouseholdCleanSalesStaffPerformanceVO;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class HouseholdCleanWorkbenchServiceImpl implements HouseholdCleanWorkbenchService {

	@Autowired
	private EmployeePrivilegeService employeePrivilegeService;

	@Autowired
	private IAdsJiaqingWorkDateOrderDetailHfpService adsJiaqingWorkDateOrderDetailHfpService;

	@Override
	public HouseholdCleanSalesReportVO querySalesReport(HouseholdCleanSalesReportRequest request) {
		HouseholdCleanSalesReportVO data = new HouseholdCleanSalesReportVO();
		List<HouseholdCleanSalesItemVO> item = this.querySalesAmountAndQuantity(request);
		if (CollectionUtil.isEmpty(item)) {
			return data;
		}
		item.forEach(i -> {
			data.addRevenueAmt(i.getRevenueAmt());
			data.addValidTradeGoodsNum(i.getValidTradeGoodsNum());
		});
		data.setItem(item);
		return data;
	}

	/**
	 * 查询每日销售金额和数量
	 * 
	 * @param request
	 * @return
	 */
	@Override
	public List<HouseholdCleanSalesItemVO> querySalesAmountAndQuantity(HouseholdCleanSalesReportRequest request) {
		QueryWrapper<AdsJiaqingWorkDateOrderDetailHfpPO> queryWrapper = Wrappers.query();
		//////////////// 权限条件////////////////
		this.privilegeQueryCondition(request, null, queryWrapper);
		// 跟据时间类型解析时间范围
		LocalDateTime[] timeRanges = TimeRangeEnum.getTimeRange(request.getTimeRange());
		queryWrapper.between("pay_time", timeRanges[0], timeRanges[1]);
		if (!TimeRangeEnum.TODAY.equals(request.getTimeRange())) {
			queryWrapper.select("pay_date", "SUM(revenue_amt) as revenue_amt",
					"SUM(valid_trade_goods_num) as valid_trade_goods_num").groupBy("pay_date");
		}
		queryWrapper.orderByAsc("pay_date");
		queryWrapper.eq("enabled_flag", OrderConst.ENABLED_FLAG_Y);
		List<AdsJiaqingWorkDateOrderDetailHfpPO> data = adsJiaqingWorkDateOrderDetailHfpService.list(queryWrapper);
		if (CollectionUtil.isEmpty(data)) {
			return null;
		}
		List<List<HouseholdCleanSalesItemVO>> groupListData = this.parseTimeRangeGroupData(data,
				request.getTimeRange());
		if (CollectionUtil.isEmpty(groupListData)) {
			return null;
		}
		log.info("========groupListData:{}", JSON.toJSON(groupListData));
		List<HouseholdCleanSalesItemVO> targetGroupData = new ArrayList<>();
		for (List<HouseholdCleanSalesItemVO> groupData : groupListData) {
			HouseholdCleanSalesItemVO hcsi = new HouseholdCleanSalesItemVO();
			for (HouseholdCleanSalesItemVO item : groupData) {
				hcsi.setPayDate(item.getPayDate());
				hcsi.setStatisticsTime(item.getStatisticsTime());
				hcsi.setStatisticsTimeStr(TimeRangeEnum.formatDateTime(hcsi.getStatisticsTime(), request.getTimeRange()));
				hcsi.addRevenueAmt(item.getRevenueAmt());
				hcsi.addValidTradeGoodsNum(item.getValidTradeGoodsNum());
			}
			targetGroupData.add(hcsi);
		}
		return targetGroupData;
	}

	/**
	 * 查询销售机构TOP10
	 * 
	 * @param request
	 * @return
	 */
	@Override
	public List<HouseholdCleanSalesOrgPerformanceVO> queryTop10SalesOrgPerformance(
			HouseholdCleanSalesReportRequest request) {
		QueryWrapper<AdsJiaqingWorkDateOrderDetailHfpPO> queryWrapper = Wrappers.query();
		//////////////// 权限条件////////////////
		this.privilegeQueryCondition(request, null, queryWrapper);
		// 跟据时间类型解析时间范围
		LocalDateTime[] timeRange = TimeRangeEnum.getTimeRange(request.getTimeRange());
		queryWrapper.between("pay_time", timeRange[0], timeRange[1]);
		queryWrapper
				.select("area_code", "area_name", "bch_code", "hr_org_id", "hr_org_name", "SUM(revenue_amt) as revenue_amt",
						"SUM(valid_trade_goods_num) as valid_trade_goods_num")
				.groupBy("area_code", "area_name", "bch_code", "hr_org_id", "hr_org_name");
		queryWrapper.last(" LIMIT 10 ");
		queryWrapper.orderByAsc("area_code");
		queryWrapper.eq("enabled_flag", OrderConst.ENABLED_FLAG_Y);
		List<AdsJiaqingWorkDateOrderDetailHfpPO> data = adsJiaqingWorkDateOrderDetailHfpService.list(queryWrapper);
		if (CollectionUtil.isEmpty(data)) {
			return null;
		}
		data.sort(Comparator.comparing(po -> ((AdsJiaqingWorkDateOrderDetailHfpPO) po).getRevenueAmt()).reversed());
		List<HouseholdCleanSalesOrgPerformanceVO> targetList = data.stream().map(source -> {
			HouseholdCleanSalesOrgPerformanceVO vo = new HouseholdCleanSalesOrgPerformanceVO();
			BeanUtils.copyProperties(source, vo);
			return vo;
		}).collect(Collectors.toList());
		return targetList;
	}

	/**
	 * 查询销售人员TOP10
	 * 
	 * @param request
	 * @return
	 */
	@Override
	public List<HouseholdCleanSalesStaffPerformanceVO> queryTop10SalesStaffPerformance(
			HouseholdCleanSalesReportRequest request) {
		QueryWrapper<AdsJiaqingWorkDateOrderDetailHfpPO> queryWrapper = Wrappers.query();
		//////////////// 权限条件////////////////
		this.privilegeQueryCondition(request, null, queryWrapper);
		// 跟据时间类型解析时间范围
		LocalDateTime[] timeRange = TimeRangeEnum.getTimeRange(request.getTimeRange());
		queryWrapper.between("pay_time", timeRange[0], timeRange[1]);
		queryWrapper.select("area_code", "area_name", "bch_code", "hr_org_id", "hr_org_name", "achievement_emp_id",
				"achievement_emp_name", "SUM(revenue_amt) as revenue_amt",
				"SUM(valid_trade_goods_num) as valid_trade_goods_num").groupBy("area_code", "area_name", "bch_code", "hr_org_id", "hr_org_name", "achievement_emp_id", "achievement_emp_name");
		queryWrapper.last(" LIMIT 10 ");
		queryWrapper.orderByAsc("area_code");
		queryWrapper.eq("enabled_flag", OrderConst.ENABLED_FLAG_Y);
		List<AdsJiaqingWorkDateOrderDetailHfpPO> data = adsJiaqingWorkDateOrderDetailHfpService.list(queryWrapper);
		if (CollectionUtil.isEmpty(data)) {
			return null;
		}
		data.sort(Comparator.comparing(po -> ((AdsJiaqingWorkDateOrderDetailHfpPO) po).getRevenueAmt()).reversed());
		List<HouseholdCleanSalesStaffPerformanceVO> targetList = data.stream().map(source -> {
			HouseholdCleanSalesStaffPerformanceVO vo = new HouseholdCleanSalesStaffPerformanceVO();
			BeanUtils.copyProperties(source, vo);
			return vo;
		}).collect(Collectors.toList());
		return targetList;
	}

	/**
	 * 查询货品金额占比
	 * 
	 * @param request
	 * @return
	 */
	@Override
	public List<HouseholdCleanCategoryProportionSalesAmountVO> queryProportionSalesAmountByCategory(
			HouseholdCleanSalesReportRequest request) {
		QueryWrapper<AdsJiaqingWorkDateOrderDetailHfpPO> queryWrapper = Wrappers.query();
		//////////////// 权限条件////////////////
		this.privilegeQueryCondition(request, null, queryWrapper);
		// 跟据时间类型解析时间范围
		LocalDateTime[] timeRange = TimeRangeEnum.getTimeRange(request.getTimeRange());
		queryWrapper.between("pay_time", timeRange[0], timeRange[1]);
		queryWrapper.eq("enabled_flag", OrderConst.ENABLED_FLAG_Y);
		queryWrapper
				.select("erp_material_lvl1_category_id", "erp_material_lvl1_category_name",
						"SUM(revenue_amt) as revenue_amt", "SUM(valid_trade_goods_num) as valid_trade_goods_num")
				.groupBy("erp_material_lvl1_category_id", "erp_material_lvl1_category_name");
		queryWrapper.orderByAsc("erp_material_lvl1_category_id");
		List<AdsJiaqingWorkDateOrderDetailHfpPO> data = adsJiaqingWorkDateOrderDetailHfpService.list(queryWrapper);
		if (CollectionUtil.isEmpty(data)) {
			return null;
		}
		HouseholdCleanCategoryCollectVO traget = new HouseholdCleanCategoryCollectVO();
		for (AdsJiaqingWorkDateOrderDetailHfpPO source : data) {
			traget.setValidTradeGoodsNumTotal(traget.getValidTradeGoodsNumTotal().add(source.getValidTradeGoodsNum()));
			traget.setRevenueAmtTotal(traget.getRevenueAmtTotal().add(source.getRevenueAmt()));
		}
		List<HouseholdCleanCategoryProportionSalesAmountVO> targetList = data.stream().map(source -> {
			HouseholdCleanCategoryProportionSalesAmountVO vo = new HouseholdCleanCategoryProportionSalesAmountVO();
			BeanUtils.copyProperties(source, vo);
			vo.calculateValidTradeGoodsNumProportion(traget.getValidTradeGoodsNumTotal());
			vo.calculateRevenueAmtProportion(traget.getRevenueAmtTotal());
			return vo;
		}).collect(Collectors.toList());
		return targetList;
	}

	/**
	 * 补充权限查询条件
	 * 
	 * @param request
	 * @param queryWrapper
	 */
	private void privilegeQueryCondition(HouseholdCleanSalesReportRequest request,
			EmployeePrivilegeConditionDTO privilegeCondition,
			QueryWrapper<AdsJiaqingWorkDateOrderDetailHfpPO> queryWrapper) {
		if (Objects.isNull(privilegeCondition)) {
			privilegeCondition = employeePrivilegeService.authByBmsDictionary(request);
		}
		final EmployeePrivilegeConditionDTO finalPrivilegeCondition = privilegeCondition;
		privilegeCondition = null;
		//////////////// 权限条件////////////////
		List<String> orgCodeList = finalPrivilegeCondition.getOrgCodeList();
		if (CollectionUtil.isNotEmpty(request.getOrgCodeList())) {
			orgCodeList = request.getOrgCodeList();
			if (CollectionUtil.isNotEmpty(request.getOrgCodeList()) && CollectionUtil.isNotEmpty(finalPrivilegeCondition.getOrgCodeList())) {
				// 用户多选机构取配置权限的交集
				orgCodeList = request.getOrgCodeList().stream()
						.filter(o -> finalPrivilegeCondition.getOrgCodeList().contains(o)).collect(Collectors.toList());
			}
		}
		queryWrapper.in(CollectionUtil.isNotEmpty(orgCodeList), "bch_code", orgCodeList);
		// 根据员工查询
		queryWrapper.in(CollectionUtil.isNotEmpty(finalPrivilegeCondition.getUserList()), "achievement_emp_id",
				finalPrivilegeCondition.getUserList());
		//////////////// 权限条件////////////////
	}

	private List<List<HouseholdCleanSalesItemVO>> parseTimeRangeGroupData(
			final List<AdsJiaqingWorkDateOrderDetailHfpPO> data, TimeRangeEnum timeRange) {
		List<List<HouseholdCleanSalesItemVO>> groupListData = new ArrayList<>();
		if (CollectionUtil.isEmpty(data)) {
			return groupListData;
		}
		// 确保数据按支付时间排序
		if (TimeRangeEnum.TODAY.equals(timeRange)) {
			data.sort(Comparator.comparing(po -> po.getPayTime()));
		} else {
			data.sort(Comparator.comparing(po -> po.getPayDate()));
		}
		List<LocalDateTime[]> timeInterval = TimeRangeEnum.timeIntervalRange(timeRange);
		for (LocalDateTime[] localDateTimes : timeInterval) {
			LocalDateTime startTime = localDateTimes[0];
			LocalDateTime endTime = localDateTimes[1];
			LocalTime localTime = LocalTime.of(0, 0); // 00:00
			List<HouseholdCleanSalesItemVO> groupData = new ArrayList<>();
			boolean flag = false;
			for (int i = 0; i < data.size(); i++) {
				LocalDateTime statisticsTime = TimeRangeEnum.TODAY.equals(timeRange) ? data.get(i).getPayTime()
						: LocalDateTime.of(data.get(i).getPayDate(), localTime);
				// payTime 位于 startTime 和 endTime 之间（包括两者）时执行
				HouseholdCleanSalesItemVO hcsi = new HouseholdCleanSalesItemVO();
				if (!statisticsTime.isBefore(startTime) && !statisticsTime.isAfter(endTime)) {
					AdsJiaqingWorkDateOrderDetailHfpPO ajwdodhp = data.get(i);
					hcsi.setPayDate(endTime.toLocalDate());
					hcsi.setStatisticsTime(endTime);
					hcsi.setRevenueAmt(ajwdodhp.getRevenueAmt());
					hcsi.setValidTradeGoodsNum(ajwdodhp.getValidTradeGoodsNum());
					groupData.add(hcsi);
					flag = true;
				}
			}
			if (flag == false) {//payTime在 startTime 和 endTime 之间不存在，补 0
				HouseholdCleanSalesItemVO hcsi = new HouseholdCleanSalesItemVO();
				hcsi.setPayDate(endTime.toLocalDate());
				hcsi.setStatisticsTime(endTime);
				hcsi.setRevenueAmt(BigDecimal.ZERO);
				hcsi.setValidTradeGoodsNum(BigDecimal.ZERO);
				groupData.add(hcsi);
			}
			groupListData.add(groupData);
		}
		return groupListData;
	}

}
