package com.cfpamf.ms.mallorder.service.impl;


import com.cfpamf.ms.mallorder.common.enums.LoanStatusEnum;
import com.cfpamf.ms.mallorder.common.enums.OrderCreateChannel;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.ProofRecordDTO;
import com.cfpamf.ms.mallorder.model.OrderLogModel;
import com.cfpamf.ms.mallorder.po.ProofDefinitionPO;
import com.cfpamf.ms.mallorder.po.ProofRecordPO;
import com.cfpamf.ms.mallorder.service.IProofDefinitionService;
import com.cfpamf.ms.mallorder.service.IProofRecordService;
import com.cfpamf.ms.mallorder.service.IProofService;
import com.slodon.bbc.core.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class ProofServiceImpl implements IProofService {

    @Resource
    private IProofRecordService proofRecordService;

    @Resource
    private IProofDefinitionService proofDefinitionService;


    @Override
    public void saveProofRecord(ProofRecordDTO proofRecordDTO) {
        ProofDefinitionPO proofDefinitionPO = proofDefinitionService.getProofDefinitionByCode(proofRecordDTO.getProofDefinitionCode());
        AssertUtil.isTrue(proofDefinitionPO == null, "凭证定义编码不能为空");
        ProofRecordPO proofRecordPO = new ProofRecordPO(proofRecordDTO);
        proofRecordService.save(proofRecordPO);
    }

    @Override
    public void saveOrUpdateProofRecord(ProofRecordDTO proofRecordDTO) {
        ProofDefinitionPO proofDefinitionPO = proofDefinitionService.getProofDefinitionByCode(proofRecordDTO.getProofDefinitionCode());
        AssertUtil.isTrue(proofDefinitionPO == null, "凭证定义编码不能为空");

        ProofRecordPO proofRecordPO = proofRecordService.getByKeyValue(proofRecordDTO.getProofKey(), proofRecordDTO.getProofValue(), proofRecordDTO.getProofDefinitionCode());
        if(proofRecordPO == null){
            proofRecordPO = new ProofRecordPO(proofRecordDTO);
            proofRecordService.save(proofRecordPO);
        }
        proofRecordPO.setProofImages(proofRecordDTO.getProofImages());
        proofRecordPO.setUpdateBy(proofRecordDTO.getProofUploadUserName());
        proofRecordPO.setUpdateTime(new Date());
        proofRecordService.saveOrUpdate(proofRecordPO);

    }

    @Override
    public List<String> getProofImages(String proofDefinitionCode, String proofValue) {
        List<String> proofList = new ArrayList<>();
        List<ProofRecordPO> proofRecordPOList = proofRecordService.getProofRecords(proofDefinitionCode, proofValue);
        if (CollectionUtils.isEmpty(proofRecordPOList)) {
            return proofList;
        }
        String proofImages = proofRecordPOList.get(0).getProofImages();
        proofList = Arrays.asList(proofImages.split(","));
        return proofList;
    }
}
