package com.cfpamf.ms.mallorder.validation;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.mallpayment.facade.api.MallPaymentFacade;
import com.cfpamf.mallpayment.facade.request.PaymentCancelRequest;
import com.cfpamf.mallpayment.facade.vo.PaymentOrderCancelVO;
import com.cfpamf.ms.mallorder.common.enums.OrderStatusEnum;
import com.cfpamf.ms.mallorder.common.enums.PayMethodEnum;
import com.cfpamf.ms.mallorder.common.enums.PresellCapitalTypeEnum;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.integration.pay.PayIntegration;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderPresellPO;
import com.cfpamf.ms.mallorder.v2.service.OrderPresellService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 订单取消校验类
 */
@Slf4j
@Component
public class OrderCancelValidation {

    @Resource
    private OrderPresellService orderPresellService;

    @Resource
    private MallPaymentFacade mallPaymentFacade;


    /**
     * 订单取消前置处理，实时查询订单的最新支付状态，如果为处理中的直接关单
     *
     * @param orderPo   订单信息
     */
    public Boolean dealOrderBeforeCancel(OrderPO orderPo) {
        log.info("订单取消前置处理 dealOrderBeforeCancel orderPo: {}", JSONObject.toJSONString(orderPo));
        // 只有待支付的订单才做这个处理
        if (!OrderStatusEnum.WAIT_PAY.getValue().equals(orderPo.getOrderState()) &&
                !OrderStatusEnum.WAIT_PAY_DEPOSIT.getValue().equals(orderPo.getOrderState())
                && !(OrderStatusEnum.DEAL_PAY.getValue().equals(orderPo.getOrderState())
                && orderPo.getPaymentCode().equals(PayMethodEnum.BANK_TRANSFER.getValue()))) {
            return Boolean.TRUE;
        }

        return invokeClosePayment(orderPo);
    }

    /**
     * 调用取消订单接口
     *
     * @param orderPo 订单实体
     * @return 结果
     */
    public Boolean invokeClosePayment(OrderPO orderPo) {
        // payment 对应的支付单号 orderOn, 在预付订单时需要特殊处理
        String payNo = this.getPayNo(orderPo);
        log.info("dealOrderBeforeCancel payNo is {}", payNo);
        PaymentCancelRequest cancelRequest = new PaymentCancelRequest();
        cancelRequest.setMainOrderNo(orderPo.getPaySn());
        cancelRequest.setOrderOn(orderPo.getOrderSn() + "," + payNo);
        cancelRequest.setSystemCode(PayIntegration.SYSTEM_CODE);
        try {
            log.info("cancel order query request is : {}", JSONObject.toJSONString(cancelRequest));
            Result<PaymentOrderCancelVO> result = mallPaymentFacade.paymentClose(cancelRequest);
            if (!result.isSuccess()) {
                return Boolean.FALSE;
            }
            log.info("cancel order query response is : {}", JSONObject.toJSONString(result));
        } catch (Exception e) {
            log.error("mallPaymentFacade.paymentClose error, request is : {}", JSONObject.toJSONString(cancelRequest), e);
        }
        return Boolean.TRUE;
    }


    /**
     * 调用取消订单接口
     *
     * @param orderPo 订单实体
     * @return 结果
     */
    public Boolean invokePresellClosePayment(OrderPO orderPo) {
        // payment 对应的支付单号 orderOn, 在预付订单时需要特殊处理
        String payNo = this.getPayNo(orderPo);
        log.info("dealOrderBeforeCancel payNo is {}", payNo);
        PaymentCancelRequest cancelRequest = new PaymentCancelRequest();
//        cancelRequest.setMainOrderNo(orderPo.getPaySn());
        cancelRequest.setOrderOn(payNo);
        cancelRequest.setSystemCode(PayIntegration.SYSTEM_CODE);
        try {
            log.info("cancel order query request is : {}", JSONObject.toJSONString(cancelRequest));
            Result<PaymentOrderCancelVO> result = mallPaymentFacade.paymentClose(cancelRequest);
            if (!result.isSuccess()) {
                return Boolean.FALSE;
            }
            log.info("cancel order query response is : {}", JSONObject.toJSONString(result));
        } catch (Exception e) {
            log.error("mallPaymentFacade.paymentClose error, request is : {}", JSONObject.toJSONString(cancelRequest), e);
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * 获取支付部分需要校验的支付单号
     *
     * @param orderPo   订单信息
     * @return          支付域对应的支付单号
     */
    private String getPayNo(OrderPO orderPo) {
        if (OrderTypeEnum.PRE_SELL_DEPOSIT.getValue() == orderPo.getOrderType()) {
            LambdaQueryWrapper<OrderPresellPO> orderPresellQuery = Wrappers.lambdaQuery();
            orderPresellQuery.select(OrderPresellPO::getPayNo)
                    .eq(OrderPresellPO::getOrderSn, orderPo.getOrderSn())
                    .eq(OrderPresellPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
            // 待支付定金时取定金payno
            if (OrderStatusEnum.WAIT_PAY_DEPOSIT.getValue().equals(orderPo.getOrderState())) {
                orderPresellQuery.eq(OrderPresellPO::getType, PresellCapitalTypeEnum.DEPOSIT.getValue());
            } else if (OrderStatusEnum.WAIT_PAY.getValue().equals(orderPo.getOrderState())) {
                // 待支付尾款时取尾款payno
                orderPresellQuery.eq(OrderPresellPO::getType, PresellCapitalTypeEnum.BALANCE.getValue());
            }else if (OrderStatusEnum.DEAL_PAY.getValue().equals(orderPo.getOrderState())){
                // 付款中时取尾款payno
                orderPresellQuery.eq(OrderPresellPO::getType, PresellCapitalTypeEnum.BALANCE.getValue());
            }
            OrderPresellPO orderPresellPO = orderPresellService.getOne(orderPresellQuery);
            return orderPresellPO.getPayNo();
        }

        return "";
    }


}
