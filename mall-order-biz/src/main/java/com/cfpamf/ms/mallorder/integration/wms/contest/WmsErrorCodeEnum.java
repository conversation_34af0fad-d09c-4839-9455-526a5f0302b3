package com.cfpamf.ms.mallorder.integration.wms.contest;

public enum WmsErrorCodeEnum {

    API_EXPIRED ("400", "接口已过期, 请使用最新接口"),
    DATA_NOT_FOUND("404", "数据不存在"),
    LONG_ALERT ("444", "停留式错误提示"),
    USER_CHECK_FAILURE("405", "用户信息校验不通过"),
    SYSTEM_EXCEPTION("500", "系统异常"),
    EMPTY_PARAM("520", "参数不能为空"),
    ILLEGAL_PARAM("521", "非法参数值"),
    INVALID_PARAM("522", "参数格式不正确"),
    CHECK_FAILURE("523", "数据校验失败"),
    ORDER_NOT_EXIST("524", "订单不存在"),
    OUTSIDE_EXCEPTION("525", "请求外部服务异常"),
    DATA_LONG("526", "数据过大"),
    AUTH_EXCEPTION("A000001", "数据权限校验失败"),
    RETURN_TYPE_CHANGED("601", "订单不存在"),
    NAME_CANNOT_EMPTY("700", "抱歉，名字不能为空"),
    NAME_TOO_LONG("701", "抱歉，填写的名字太长"),
    WRONG_IDCARD_FORMAT("702", "抱歉，填写的身份证格式不正确"),
    COMPANY_NAME_CANNOT_EMPTY("703", "抱歉，公司名称不能为空"),
    COMPANY_NAME_TOO_LONG("704", "抱歉，填写的公司名称太长"),
    COMPANY_TYPE_FAIL("705", "抱歉，填入的企业类型不正确"),
    SETTLED_TYPE_FAIL("705", "抱歉，填入的入驻类型不正确"),
    UNIFY_SOCIAL_CREDIT_CANNOT_EMPTY("705", "抱歉，统一社会信用代码不能为空"),
    OPERATING_PERIOD_START_DATE_CANNOT_EMPTY("706", "抱歉，经营期限开始日期不能为空"),
    OPERATING_PERIOD_END_DATE_CANNOT_EMPTY("707", "抱歉，经营期限截止日期不能为空"),
    OPERATING_PERIOD_DATE_FAIL("708", "抱歉，经营期限日期格式有误"),
    MERCHANT_QUALIFICATION_ALREADY_EXIST("709", "抱歉，当前商家资质已存在，请勿重复添加"),
    MERCHANT_QUALIFICATION_IMAGE_FAIL("710", "抱歉，上传的资质声明异常，请重新上传"),
    REGISTERED_ADDRESS_CANNOT_EMPTY("711", "抱歉，注册地址不能为空"),
    LEGAL_REPRESENTATIVE_CANNOT_EMPTY("712", "抱歉，法定代表人不能为空"),
    MERCHANT_NOT_EXIST("527","商户不存在！"),
    ;



    private final String code;
    private final String desc;

    WmsErrorCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String code() {
        return this.code;
    }

    public String desc() {
        return this.desc;
    }
    
    /**
     * implements MSEnum
     * <AUTHOR> 09:51
     */
    public String getMsg() {
        return this.desc();
    }

    /**
     * implements MSEnum
     * <AUTHOR> 09:51
     */
    public String getCode() {
        return this.code();
    }

}
