package com.cfpamf.ms.mallorder.integration.cashier.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-13 14:13:14
 */
@Getter
@Setter
public class OrderPhase {

    @ApiModelProperty("批次号")
    private String phaseOrderNo;

    @ApiModelProperty("支付金额（元）")
    private BigDecimal payAmt;

    @ApiModelProperty("同步返回地址")
    private String returnUrl;

    @ApiModelProperty("异步回调地址")
    private String notifyUrl;

    @ApiModelProperty("放款通知url")
    private String loanNotifyUrl;

    @ApiModelProperty("批次顺序")
    private Integer sequence;

    @ApiModelProperty("补差金额")
    private BigDecimal subsidyAmount;

    @ApiModelProperty(value = "截止时间")
    private LocalDateTime orderPhasePeriod;
}
