package com.cfpamf.ms.mallorder.service;

import java.util.List;
import java.util.Set;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.ms.mallorder.po.OrderPromotionDetailPO;

/**
 * <AUTHOR> 2022/4/21.
 */
public interface IOrderPromotionDetailService extends IService<OrderPromotionDetailPO> {

    /**
     * 根据订单编号获取券使用的优惠券编号列表
     *
     * @param orderSn 订单编号
     * @return 优惠券使用列表
     */
    List<String> listCouponNoByOrderSn(String orderSn);

    /**
     * 根据订单号查询店铺活动优惠券
     * 
     * @param orderSns
     * @param isStore
     *            0-平台券 1-店铺券
     * @return
     */
    List<OrderPromotionDetailPO> getOrderPromotionDetailList(Set<String> orderSns, Integer isStore);

    /**
     * 根据订单编号获取订单促销详情
     *
     * @param orderSn 订单编号
     * @return 促销详情
     */
    List<OrderPromotionDetailPO> getOrderPromotionDetailByOrderSn(String orderSn);

    /**
     * 根据订单编号获取订单促销详情
     *
     * @param orderSnList 订单编号列表
     * @return 促销详情
     */
    List<OrderPromotionDetailPO> getOrderPromotionDetailByOrderSnList(List<String> orderSnList);
}
