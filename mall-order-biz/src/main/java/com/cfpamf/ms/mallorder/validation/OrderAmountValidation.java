package com.cfpamf.ms.mallorder.validation;

import com.cfpamf.ms.mallorder.common.config.CommissionIncentiveConfig;
import com.cfpamf.ms.mallorder.enums.OrderCreateChannel;
import com.cfpamf.ms.mallorder.enums.OrderPatternEnum;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.po.OrderPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 *
 * 订单资金类型校验类
 *
 */

@Slf4j
@Component
public class OrderAmountValidation {

    @Autowired
    private CommissionIncentiveConfig commissionIncentiveConfig;

    /**
     * 判断订单是否包含订单佣金项费用
     *
     * @param orderPO   订单信息
     * @return          true/false
     */
    public static boolean isOrderContainOrderCommission(OrderPO orderPO) {
        // OMS订单无佣金服务费
        if (OrderCreateChannel.OMS.getValue().equals(orderPO.getChannel())) {
            return Boolean.FALSE;
        }
        // 预占订单无佣金服务费
        if (OrderTypeEnum.ORDER_TYPE_5.getValue() == orderPO.getOrderType()) {
            return Boolean.FALSE;
        }
        // 线下订单无佣金服务费
        if (OrderTypeEnum.ORDER_TYPE_6.getValue() == orderPO.getOrderType()) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * 判断订单是否包含订单佣金服务费项费用
     *
     * @param orderPO   订单信息
     * @return          true/false
     */
    public static boolean isOrderContainCommissionServiceFee(OrderPO orderPO) {
        // 与订单佣金一致
        return isOrderContainOrderCommission(orderPO);
    }

    /**
     * 判断订单是否需要记录佣金激励费
     *
     * @param orderPO   订单信息
     * @return          true/false
     */
    public boolean needCommissionIncentive(OrderPO orderPO) {
        if (OrderTypeEnum.ORDER_TYPE_6.getValue().equals(orderPO.getOrderType())) {
            return false;
        }
        log.info("佣金激励费白名单店铺信息:{}", commissionIncentiveConfig.getWhitelistsStore());

        if (Objects.isNull(commissionIncentiveConfig) || CollectionUtils.isEmpty(commissionIncentiveConfig.getWhitelistsStore())) {
            return false;
        }

        // 自提订单与物流订单均需要记录录佣金激励费资金项
        if (commissionIncentiveConfig.getWhitelistsStore().contains(String.valueOf(orderPO.getStoreId()))) {
            return true;
        }
        return false;
    }

}
