package com.cfpamf.ms.mallorder.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.mallpayment.facade.vo.CDMallRefundTryResultVO;
import com.cfpamf.ms.mallorder.po.OrderReturnLoanExtendPO;

/**
 * 订单售后贷款类信息扩展表 SERVICE
 */
public interface IOrderReturnLoanExtendService extends IService<OrderReturnLoanExtendPO> {

    /**
     * 保存/更新售后贷款类信息
     *
     * @param orderSn                 订单号
     * @param afsSn                   售后单号
     * @param bizSn                   业务单号（普通售后单为售后单号，组合退款为售后流水单号）
     * @param cdMallRefundTryResultVO 试算结果
     * @return 保存/更新结果
     */
    boolean saveUpdateOrderReturnLoanExtend(String orderSn, String afsSn, String bizSn, CDMallRefundTryResultVO cdMallRefundTryResultVO);


    /**
     * 根据单号查询售后贷款类信息
     *
     * @param orderSn   订单号
     * @param afsSn     售后单号
     * @param bizSn     业务单号（普通售后单为售后单号，组合退款为售后流水单号）
     * @return          售后贷款类信息
     */
    OrderReturnLoanExtendPO getOrderReturnLoanExtendPOBySn(String orderSn, String afsSn, String bizSn);

}

