package com.cfpamf.ms.mallorder.controller.admin;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.event.SyncReadListener;
import com.cfpamf.ms.mallorder.dto.PlanLoanDateDTO;
import com.cfpamf.ms.mallorder.dto.PlanLoanDateImportDTO;
import com.cfpamf.ms.mallorder.req.admin.AdminPlanLoanDateRequest;
import com.cfpamf.ms.mallorder.service.IPlanLoanDateService;
import com.cfpamf.ms.mallorder.vo.ExportingFailedDataToExcelVO;
import com.cfpamf.ms.mallorder.vo.PlanLoanDateVO;
import com.cfpamf.ms.mallsystem.vo.Admin;
import com.slodon.bbc.core.constant.CommonConst;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.PageVO;
import com.slodon.bbc.core.response.PagerInfo;
import com.slodon.bbc.core.response.SldResponse;
import com.slodon.bbc.core.util.StringUtil;
import com.slodon.bbc.core.util.UserUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Api(tags = "admin-计划放款日")
@RestController
@Slf4j
@RequestMapping("admin/planLoanDate")
public class AdminPlanLoanDateController {

    @Autowired
    private IPlanLoanDateService planLoanDateService;


    @ApiOperation("计划放款日更改列表")
    @PostMapping("/list")
    public JsonResult<PageVO<PlanLoanDateVO>> list(@RequestBody AdminPlanLoanDateRequest req) {
        int pageSize = Objects.isNull(req.getPageSize()) || StringUtil.isNullOrZero(Integer.parseInt(req.getPageSize())) ? CommonConst.DEFAULT_PAGE_SIZE : Integer.parseInt(req.getPageSize());
        int pageIndex = Objects.isNull(req.getCurrent()) || StringUtil.isNullOrZero(Integer.parseInt(req.getCurrent())) ? 1 : Integer.parseInt(req.getCurrent());
        PagerInfo pager = new PagerInfo(pageSize, pageIndex);
        List<PlanLoanDateVO> list = planLoanDateService.getPlanLoanDateList(pager, req);
        return SldResponse.success(new PageVO<>(list, pager));
    }

    @ApiOperation("调整放款日")
    @PostMapping("/adjust")
    public JsonResult<ExportingFailedDataToExcelVO> adjust(HttpServletRequest request, @Valid @RequestBody PlanLoanDateDTO planLoanDateDTO) throws Exception {
        Admin admin = UserUtil.getUser(request, Admin.class);
        ExportingFailedDataToExcelVO exportingFailedDataToExcelVO = planLoanDateService.adjustPlanLoanDate(planLoanDateDTO, admin);
        return SldResponse.success(exportingFailedDataToExcelVO);
    }

    @ApiOperation("调整放款日-导入excel")
    @PostMapping("/import")
    public JsonResult<ExportingFailedDataToExcelVO> importExcel(HttpServletRequest request, @RequestParam("file") MultipartFile file) throws Exception {
        Admin admin = UserUtil.getUser(request, Admin.class);
        SyncReadListener excelListener = new SyncReadListener();
        EasyExcel.read(file.getInputStream(), PlanLoanDateImportDTO.class, excelListener).doReadAll();
        List<Object> list = excelListener.getList();

        List<PlanLoanDateImportDTO> loanDateImportDTOList = new ArrayList<>();
        for (Object obj : list) {
            PlanLoanDateImportDTO planLoanDateImportDTO = (PlanLoanDateImportDTO) obj;
            if (Objects.isNull(planLoanDateImportDTO) || StringUtil.isEmpty(planLoanDateImportDTO.getOrderSn())) {
                continue;
            }
            loanDateImportDTOList.add(planLoanDateImportDTO);
        }

        ExportingFailedDataToExcelVO exportingFailedDataToExcelVO = planLoanDateService.excelParseAndSave(loanDateImportDTOList, admin);
        return SldResponse.success(exportingFailedDataToExcelVO);
    }

}
