package com.cfpamf.ms.mallorder.integration.facade;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.bms.facade.constants.BmsConstant;
import com.cfpamf.ms.bms.facade.vo.DingTalkUserBmsVO;
import com.cfpamf.ms.bms.facade.vo.DingTalkUserStepHealthVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "bms-service-biz", url = "${bms.api.url}")
public interface BmsDingTalkFacade {
    @ApiOperation(value = "根据CorpId和CorpSecret获取Access_Token", notes = "/dingtalk/getAccessTokenByCorp")
    @GetMapping(value = "/dingtalk/getAccessTokenByCorp")
    Result<String> getAccessTokenByCorp();

    @ApiOperation(value = "根据AppId和AppSecret获取Access_Token，（供20190730及以后新方式建立微应用使用）", notes = "/dingtalk/getAccessTokenByApp?systemId=1,2,3")
    @GetMapping(value = "/dingtalk/getAccessTokenByApp")
    Result<String> getAccessTokenByApp(@RequestParam(required = false, value = "systemId", defaultValue = BmsConstant.OMS_SYSTEM_ID_STR) Integer systemId);

    @ApiOperation(value = "根据手机号获取钉钉用户详情", notes = "/dingtalk/users/details/mobiles?mobiles=xxx,xxx")
    @GetMapping(value = "/dingtalk/users/details/mobiles")
    Result<List<DingTalkUserBmsVO>> listUserDetailVOByMobiles(@RequestParam("mobiles") List<String> mobiles);

    @ApiOperation(value = "根据钉钉用户Id获取钉钉用户详情", notes = "/dingtalk/users/details/dingTalkUserIdList?dingTalkUserIdList=xxx,xxx")
    @GetMapping(value = "/dingtalk/users/details/dingTalkUserIdList")
    Result<List<DingTalkUserBmsVO>> listUserDetailVOByDingtalkUserIdList(@RequestParam("dingTalkUserIdList") List<String> dingTalkUserIdList);

    @ApiOperation(value = "根据手机号获取钉钉上级用户详情", notes = "/dingtalk/users/leaders/details/mobile?mobile=xxx")
    @GetMapping(value = "/dingtalk/users/leaders/details/mobile")
    Result<List<DingTalkUserBmsVO>> listLeaderUserDetailVOByMobile(@RequestParam("mobile") String mobile);

    @ApiOperation(value = "批量获取指定日期钉钉运动数据", notes = "/dingtalk/health/listDingTalkUserStepHealthVO?statDate=20200601")
    @GetMapping(value = "/dingtalk/health/listDingTalkUserStepHealthVO")
    Result<List<DingTalkUserStepHealthVO>> listDingTalkUserStepHealthVO(@RequestParam("statDate") String statDate);

    @ApiOperation(value = "平台向指定用户发送文本消息", notes = "/dingtalk/users/message/platform/text")
    @PostMapping(value = "/dingtalk/users/message/platform/text")
    void sendPlatformTextMessage(@RequestParam("dingTalkUserIdList") List<String> dingTalkUserIdList, @RequestParam("message") String message);
}
