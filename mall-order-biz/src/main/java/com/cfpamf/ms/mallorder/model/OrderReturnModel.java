package com.cfpamf.ms.mallorder.model;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.dts.biz.api.channel.SkyCraneFeignClient;
import com.cfpamf.dts.biz.constant.ChannelConstants;
import com.cfpamf.dts.biz.response.channel.ChannelAuditResultResponse;
import com.cfpamf.mallpayment.facade.vo.CDMallRefundTryResultVO;
import com.cfpamf.ms.customer.facade.vo.UserInfoVo;
import com.cfpamf.ms.mall.account.enums.AccountTypeEnum;
import com.cfpamf.ms.mall.account.request.AccountQuery;
import com.cfpamf.ms.mall.account.vo.AccountVO;
import com.cfpamf.ms.mall.settlement.api.SettlementBillFeignClient;
import com.cfpamf.ms.mall.settlement.enums.BillAccountTypeEnum;
import com.cfpamf.ms.mallgoods.facade.api.GoodsExtendFeignClient;
import com.cfpamf.ms.mallgoods.facade.api.GoodsFeignClient;
import com.cfpamf.ms.mallgoods.facade.api.ProductFeignClient;
import com.cfpamf.ms.mallgoods.facade.api.ProductStockFeignClient;
import com.cfpamf.ms.mallgoods.facade.enums.EventStockTypeEnum;
import com.cfpamf.ms.mallgoods.facade.vo.Goods;
import com.cfpamf.ms.mallgoods.facade.vo.Product;
import com.cfpamf.ms.mallmember.api.MemberBalanceLogFeignClient;
import com.cfpamf.ms.mallmember.api.MemberFeignClient;
import com.cfpamf.ms.mallmember.api.MemberIntegralLogFeignClient;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallmember.po.MemberBalanceLog;
import com.cfpamf.ms.mallorder.common.calculate.RefundCalculator;
import com.cfpamf.ms.mallorder.common.config.ChannelFeeRateConfig;
import com.cfpamf.ms.mallorder.common.config.RefundBlackListConfig;
import com.cfpamf.ms.mallorder.common.config.WXCombineXZCardOrderConfig;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.constant.OrdersAfsConst;
import com.cfpamf.ms.mallorder.common.enums.*;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.cfpamf.ms.mallorder.common.help.SystemSettingObtainHelper;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.common.util.DateUtil;
import com.cfpamf.ms.mallorder.common.util.OrderLocalUtils;
import com.cfpamf.ms.mallorder.constant.ExchangeOrderConst;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.controller.fegin.facade.DbcServiceFeign;
import com.cfpamf.ms.mallorder.dto.DbcTrialRefundCommissionDTO;
import com.cfpamf.ms.mallorder.dto.ExchangeOrderDetailDTO;
import com.cfpamf.ms.mallorder.dto.OrderAfterDTO;
import com.cfpamf.ms.mallorder.dto.UserDTO;
import com.cfpamf.ms.mallorder.enums.InterestPayerEnum;
import com.cfpamf.ms.mallorder.enums.OrderEventEnum;
import com.cfpamf.ms.mallorder.enums.OrderPerformanceModeEnum;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.integration.cust.CustomerIntegration;
import com.cfpamf.ms.mallorder.integration.erp.ERPIntegration;
import com.cfpamf.ms.mallorder.integration.pay.PayIntegration;
import com.cfpamf.ms.mallorder.integration.promotion.MallCouponPkgIntegration;
import com.cfpamf.ms.mallorder.integration.wms.WmsIntegration;
import com.cfpamf.ms.mallorder.mapper.*;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.request.OrderAfterServiceExample;
import com.cfpamf.ms.mallorder.request.OrderExchangeDetailExample;
import com.cfpamf.ms.mallorder.request.OrderReplacementExample;
import com.cfpamf.ms.mallorder.request.OrderReturnExample;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.service.impl.OrderCreateHelper;
import com.cfpamf.ms.mallorder.service.payment.IPayHandleService;
import com.cfpamf.ms.mallorder.v2.config.CommonConfig;
import com.cfpamf.ms.mallorder.v2.domain.vo.RefundInfoExtraInfoVO;
import com.cfpamf.ms.mallorder.v2.manager.CouponOrderManager;
import com.cfpamf.ms.mallorder.v2.manager.GoodsStockService;
import com.cfpamf.ms.mallorder.v2.manager.PromotionManager;
import com.cfpamf.ms.mallorder.v2.service.OrderRefundRecordService;
import com.cfpamf.ms.mallorder.v2.strategy.LoanRefundStrategy;
import com.cfpamf.ms.mallorder.v2.strategy.context.LoanRefundStrategyContext;
import com.cfpamf.ms.mallorder.validation.OrderReturnAuditValidation;
import com.cfpamf.ms.mallorder.validation.OrderReturnValidation;
import com.cfpamf.ms.mallorder.vo.*;
import com.cfpamf.ms.mallpromotion.api.*;
import com.cfpamf.ms.mallpromotion.constant.PromotionConst;
import com.cfpamf.ms.mallpromotion.request.SeckillOrderExtendExample;
import com.cfpamf.ms.mallpromotion.request.SpellTeamMemberExample;
import com.cfpamf.ms.mallpromotion.vo.*;
import com.cfpamf.ms.mallshop.api.StoreIsolateWhiteListFeignClient;
import com.cfpamf.ms.mallshop.enums.WhiteListEnum;
import com.cfpamf.ms.mallshop.resp.Vendor;
import com.cfpamf.ms.mallsystem.vo.Admin;
import com.slodon.bbc.core.constant.MemberTplConst;
import com.slodon.bbc.core.constant.RedisConst;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.exception.MallException;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.PagerInfo;
import com.slodon.bbc.core.util.AssertUtil;
import com.slodon.bbc.core.util.StringUtil;
import com.slodon.bbc.core.util.TimeUtil;
import com.slodon.bbc.starter.mq.entity.MessageSendProperty;
import com.slodon.bbc.starter.mq.entity.MessageSendVO;
import io.seata.core.context.RootContext;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.MDC;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.slodon.bbc.core.constant.StarterConfigConst.MQ_EXCHANGE_NAME;
import static com.slodon.bbc.core.constant.StarterConfigConst.MQ_QUEUE_NAME_MEMBER_MSG;

@Component
@Slf4j
public class OrderReturnModel {

    @Value("${dayEnd.start}")
    private Integer start;
    @Value("${dayEnd.end}")
    private Integer end;

    @Value("${supplier.code.yzh:103244}")
    private String supplierCodeYzh;

    @Autowired
    private WXCombineXZCardOrderConfig wxCombineXZCardOrderConfig;
    @Resource
    private OrderReturnMapper orderReturnMapper;
    @Resource
    private OrderPromotionDetailMapper orderPromotionDetailMapper;
    @Resource
    private OrderReturnTrackMapper orderReturnTrackMapper;
    @Resource
    private OrderAfterMapper orderAfterMapper;
    @Resource
    private OrderExtendModel orderExtendModel;
    @Resource
    private OrderAfterSaleLogMapper orderAfterSaleLogMapper;
    @Resource
    private OrderReplacementMapper orderReplacementMapper;
    @Resource
    private OrderProductMapper orderProductMapper;
    @Resource
    private OrderMapper orderMapper;
    @Resource
    private OrderModel orderModel;
    @Resource
    private OrderProductModel orderProductModel;
    @Autowired
    private ChannelFeeRateConfig channelFeeRateConfig;
    @Resource
    private MemberFeignClient memberFeignClient;
    @Resource
    private MemberIntegralLogFeignClient memberIntegralLogFeignClient;
    @Resource
    private MemberBalanceLogFeignClient memberBalanceLogFeignClient;
    @Resource
    private GoodsFeignClient goodsFeignClient;
    @Resource
    private GoodsExtendFeignClient goodsExtendFeignClient;
    @Resource
    private SpellTeamMemberFeignClient spellTeamMemberFeignClient;
    @Resource
    private ProductFeignClient productFeignClient;
    @Resource
    private SeckillOrderExtendFeignClient seckillOrderExtendFeignClient;
    @Resource
    private SeckillStageProductFeignClient seckillStageProductFeignClient;
    @Resource
    private SettlementBillFeignClient settlementBillFeignClient;

    @Resource
    private RabbitTemplate rabbitTemplate;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private PayIntegration payIntegration;
    @Autowired
    private OrderAfterServiceModel orderAfterServiceModel;
    @Autowired
    private OrderReturnModel orderReturnModel;
    @Autowired
    private ProductStockFeignClient productStockFeignClient;
    @Resource
    private SpellGoodsFeignClient spellGoodsFeignClient;
    @Resource
    private IOrderReturnService orderReturnService;
    @Autowired
    private IOrderPlacingService orderPlacingService;
    @Autowired
    private IOrderAfterService orderAfterService;
    @Autowired
    private IOrderProductService orderProductService;
    @Resource
    private IOrderService orderService;
    @Resource
    private OrderCreateHelper orderCreateHelper;
    @Resource
    private OrderLogModel orderLogModel;
    @Autowired
    private DbcServiceFeign dbcServiceFeign;

    @Resource
    private SystemSettingObtainHelper systemSettingObtainHelper;
    @Autowired
    private ExclusiveFeignClient exclusiveFeignClient;
    @Autowired
    private IPayHandleService iPayHandleService;
    @Resource
    private IOrderReturnService iOrderReturnService;
    @Autowired
    private IOrderInfoService orderInfoService;

    @Autowired
    private CouponOrderManager couponOrderManager;

    @Autowired
    private GoodsStockService goodsStockService;

    @Autowired
    private PromotionManager promotionManager;

    @Autowired
    private OrderRefundRecordService orderRefundRecordService;

    @Autowired
    private LoanRefundStrategyContext loanRefundStrategyContext;

    @Autowired
    private OrderReturnValidation orderReturnValidation;

    @Autowired
    private OrderReturnAuditValidation orderReturnAuditValidation;

    @Resource
    private SkyCraneFeignClient skyCraneFeignClient;

    @Autowired
    private IBzOldUserPoolService iBzOldUserPoolService;

    @Resource
    private IOrderExchangeService orderExchangeService;

    @Resource
    private IOrderExchangeDetailService orderExchangeDetailService;

    @Autowired
    private IOrderExtendService orderExtendService;

    @Autowired
    private RefundBlackListConfig adminRefundBlackList;

    @Resource
    private IBzOrderProductCombinationService orderProductCombinationService;

    @Resource
    private IOrderReturnLoanExtendService orderReturnLoanExtendService;

    @Resource
    private WmsIntegration wmsIntegration;
    @Resource
    private StoreIsolateWhiteListFeignClient storeIsolateWhiteListFeignClient;
    @Resource
    private MallCouponPkgIntegration mallCouponPkgIntegration;
    @Resource
    private RefundCalculator refundCalculator;

    @Resource
    private CustomerIntegration customerIntegration;

    @Resource
    private ERPIntegration erpIntegration;

    @Autowired
    private IOrderInvoiceService orderInvoiceService;

    /**
     * 新增订单退货表
     *
     * @param orderReturnPO
     * @return
     */
    public Integer saveOrderReturn(OrderReturnPO orderReturnPO) {
        int count = orderReturnMapper.insert(orderReturnPO);
        if (count == 0) {
            throw new MallException("添加订单退货表失败，请重试");
        }
        return count;
    }

    /**
     * 根据returnId删除订单退货表
     *
     * @param returnId returnId
     * @return
     */
    public Integer deleteOrderReturn(Integer returnId) {
        if (StringUtils.isEmpty(returnId)) {
            throw new MallException("请选择要删除的数据");
        }
        int count = orderReturnMapper.deleteByPrimaryKey(returnId);
        if (count == 0) {
            log.error("根据returnId：" + returnId + "删除订单退货表失败");
            throw new MallException("删除订单退货表失败,请重试");
        }
        return count;
    }

    /**
     * 根据returnId更新订单退货表
     *
     * @param orderReturnPO
     * @return
     */
    public Integer updateOrderReturn(OrderReturnPO orderReturnPO) {
        if (StringUtils.isEmpty(orderReturnPO.getReturnId())) {
            throw new MallException("请选择要修改的数据");
        }
        int count = orderReturnMapper.updateByPrimaryKeySelective(orderReturnPO);
        if (count == 0) {
            log.error("根据returnId：" + orderReturnPO.getReturnId() + "更新订单退货表失败");
            throw new MallException("更新订单退货表失败,请重试");
        }
        return count;
    }

    /**
     * 根据returnId获取订单退货表详情
     *
     * @param returnId returnId
     * @return
     */
    public OrderReturnPO getOrderReturnByReturnId(Integer returnId) {
        return orderReturnMapper.getByPrimaryKey(returnId);
    }

    /**
     * 根据条件获取订单退货表列表
     *
     * @param example 查询条件信息
     * @param pager   分页信息
     * @return
     */
    public List<OrderReturnPO> getOrderReturnList(OrderReturnExample example, PagerInfo pager) {
        List<OrderReturnPO> orderReturnPOList;
        if (pager != null) {
            pager.setRowsCount(orderReturnMapper.countByExample(example));
            orderReturnPOList = orderReturnMapper.listPageByExample(example, pager.getStart(), pager.getPageSize());
        } else {
            orderReturnPOList = orderReturnMapper.listByExample(example);
        }
        return orderReturnPOList;
    }

    @Autowired
    private OrderLocalUtils orderLocalUtils;

    /**
     * 根据条件获取订单退货表列表（存在表关联）
     *
     * @param example 查询条件信息
     * @param pager   分页信息
     * @return
     */
    public List<OrderReturnVOV2> getOrderReturnListWithJoin(OrderReturnExample example, PagerInfo pager) {
        List<OrderReturnVOV2> orderReturnPOList;
        if (pager != null) {
            pager.setRowsCount(orderReturnMapper.countByExampleWithJoin(example));
            orderReturnPOList =
                    orderReturnMapper.listPageByExampleWithJoin(example, pager.getStart(), pager.getPageSize());
        } else {
            orderReturnPOList = orderReturnMapper.listByExampleWithJoin(example);
        }
        // 根据用户编码批量从客户中心获取用户信息
        List<String> userNoList = orderReturnPOList.stream().map(OrderReturnVOV2::getUserNo).collect(Collectors.toList());

        Map<String, UserInfoVo> userInfoVoMap = customerIntegration.queryUserInfoByUserNoList(userNoList, false);
        for (OrderReturnVOV2 orderReturnVOV2 : orderReturnPOList) {
            log.info("orderReturnVOV2:{}", JSON.toJSONString(orderReturnVOV2));
            List<OrderProductListVO> orderGiftProductListVOS = orderLocalUtils.getOrderGiftProductListVOS(
                    orderReturnVOV2.getOrderType(), orderReturnVOV2.getOrderSn(),
                    orderReturnVOV2.getGiftGroup(), orderReturnVOV2.getGiftReturnOrderProductId());
            orderReturnVOV2.setOrderGiftProductList(orderGiftProductListVOS);

            if (OrderConst.ORDER_TYPE_13 == orderReturnVOV2.getOrderType()) {
                LambdaQueryWrapper<BzOrderProductCombinationPO> queryOrder = Wrappers.lambdaQuery();
                queryOrder.eq(BzOrderProductCombinationPO::getOrderSn, orderReturnVOV2.getOrderSn())
                        .select(BzOrderProductCombinationPO::getMainProductId);
                BzOrderProductCombinationPO productCombinationPO = orderProductCombinationService.getOne(queryOrder);
                if (ObjectUtil.isNotNull(productCombinationPO) && orderReturnVOV2.getProductId().equals(productCombinationPO.getMainProductId())) {
                    orderReturnVOV2.setIsMain(1);
                }
            }
            // 因为订单保存的会员姓名均为手机号码(网关原因), 根据用户编码转换成用户名称
            UserInfoVo userInfoVo = userInfoVoMap.getOrDefault(orderReturnVOV2.getUserNo(), null);
            if (Objects.nonNull(userInfoVo)) {
                if (Objects.nonNull(userInfoVo.getCustInfoVo())) {
                    orderReturnVOV2.setMemberName(userInfoVo.getCustInfoVo().getCustName());
                } else if (!StringUtils.isEmpty(userInfoVo.getUserName())) {
                    orderReturnVOV2.setMemberName(userInfoVo.getUserName());
                }
            }

            // 只有自提订单，且在白名单内才展示轨迹
            if (OrderPatternEnum.SELF_LIFT.getValue().equals(orderReturnVOV2.getOrderPattern())
                    && storeIsolateWhiteListFeignClient.isStoreOnList(WhiteListEnum.DING_DING_REFUND_WHITE_LIST, orderReturnVOV2.getStoreId())) {
                orderReturnVOV2.setSelfLiftReturnTrack(true);
            }
        }
        return orderReturnPOList;
    }

    /**
     * 根据条件获取订单退货数量
     *
     * @param example 查询条件信息
     * @return
     */
    public Integer getOrderReturnCount(OrderReturnExample example) {
        return orderReturnMapper.countByExample(example);
    }

    /**
     * 根据售后单号获取退货信息
     *
     * @param afsSn
     * @return
     */
    public OrderReturnPO getOrderReturnByAfsSn(String afsSn) {
        OrderReturnExample example = new OrderReturnExample();
        example.setAfsSn(afsSn);
        List<OrderReturnPO> list = orderReturnMapper.listByExample(example);
        BizAssertUtil.notEmpty(list, "查询退货信息为空，请检查退款单号：" + afsSn);
        return list.get(0);
    }

    /**
     * 根据售后单号获取售后服务信息
     *
     * @param afsSn
     * @return
     */
    public OrderAfterPO getOrderAfterServiceByAfsSn(String afsSn) {
        OrderAfterServiceExample example = new OrderAfterServiceExample();
        example.setAfsSn(afsSn);
        List<OrderAfterPO> list = orderAfterMapper.listByExample(example);
        AssertUtil.notEmpty(list, "查询售后服务信息为空，请重试");
        return list.get(0);
    }

    /**
     * 根据售后单号获取换货信息
     *
     * @param afsSn
     * @return
     */
    private OrderReplacementPO getOrderReplacementByAfsSn(String afsSn) {
        OrderReplacementExample example = new OrderReplacementExample();
        example.setAfsSn(afsSn);
        List<OrderReplacementPO> list = orderReplacementMapper.listByExample(example);
        AssertUtil.notEmpty(list, "查询换货信息为空，请重试");
        return list.get(0);
    }

    /**
     * 商家审核退款申请
     */
    @Transactional
    public String afsStoreAudit(Vendor vendor, String afsSn, boolean isPass, String remark, Integer storeAddressId,
                                String channel, Integer distribution, BigDecimal refundPunishAmount, OperationRoleEnum role, String applySource) {
        boolean storeIsPass = isPass;
        String result = OrderConst.RESULT_CODE_SUCCESS;
        UserDTO userDTO = new UserDTO(vendor);
        MDC.put("userDTO", JSONObject.toJSONString(userDTO));

        // 查询售后服务信息
        OrderAfterPO orderAfterServicePODb = this.getOrderAfterServiceByAfsSn(afsSn);
        OrderReturnPO orderReturnPO = orderReturnService.getByAfsSn(afsSn);

        // 权限校验
        orderReturnValidation.storeAuditStateAuditValidate(role, vendor.getStoreId(), orderAfterServicePODb.getStoreId(), distribution);

        // 商家审批时信息校验
        orderReturnAuditValidation.afsStoreAuditValidate(orderReturnPO);
        boolean isYzhFlag = false;
        //判断是否是供应商履约的单，供应商履约的单需要 供应商已受理售后单才能审核
        if (!OrderCreateChannel.CHANNEL_YZH.getValue().equals(channel)) {
            if (OrderPerformanceModeEnum.PERFORMANCE_MODE_SUPPLIER.getValue().equals(orderAfterServicePODb.getPerformanceMode())) {
                OrderProductPO orderProductPO = orderProductService.selectOneByOrderProductId(orderAfterServicePODb.getOrderProductId());
                if (orderProductPO != null && supplierCodeYzh.equals(orderProductPO.getSupplierCode())) {
                    isYzhFlag = true;
                    JsonResult<ChannelAuditResultResponse> channelAuditResult = skyCraneFeignClient.channelAuditStatus(orderAfterServicePODb.getAfsSn());
                    if (channelAuditResult == null || channelAuditResult.getState() != 200 || channelAuditResult.getData() == null) {
                        log.error("调用渠道售后审核接口出错,channelAuditResult = {}", JSONObject.toJSONString(channelAuditResult));
                        throw new BusinessException("调用渠道售后审核接口出错");
                    }
                    ChannelAuditResultResponse channelAuditResultResponse = channelAuditResult.getData();
                    if (ChannelConstants.CHANNEL_AUDIT_STATUS_OF_PASSED.equals(channelAuditResultResponse.getAuditStatus())
                            || ChannelConstants.CHANNEL_AUDIT_STATUS_OF_GOODS_BACK.equals(channelAuditResultResponse.getAuditStatus())) {
                        isPass = true;
                        if (storeIsPass != isPass) {
                            return "拒绝异常，原因：渠道已审核通过，系统无法受理该操作";
                        }
                    } else if (ChannelConstants.CHANNEL_AUDIT_STATUS_OF_REFUSED.equals(channelAuditResultResponse.getAuditStatus())) {
                        isPass = false;
                        remark = channelAuditResultResponse.getRefuseReason();
                        if (storeIsPass != isPass) {
                            return "同意异常，原因：渠道已审核拒绝，系统无法受理该操作";
                        }
                    } else {
                        return "抱歉，渠道正在审核中，请稍后操作";
                    }
                }
            }
        }


        if (orderAfterServicePODb.getAfsType() == OrdersAfsConst.AFS_TYPE_RETURN) {
            //退货退款时店铺地址id必传
            if (isPass && StringUtil.isNullOrZero(storeAddressId)) {
                throw new BusinessException("请选择退货收货地址");
            }
        }

        RefundType refundType = RefundType.value(orderReturnPO.getRefundType());

        if (!RefundType.SQUARE_OFFLINE_REFUND.getValue().equals(orderReturnPO.getRefundType())) {
            // 实时获取退款类型
            refundType = orderAfterServiceModel.getRefundType(orderAfterServicePODb.getOrderSn());
        }


        OrderPO orderPODb = orderService.getByOrderSn(orderReturnPO.getOrderSn());

        if(isPass) {
            //处理erp京东物流拦截
            dealErpJdRefundIntercept(orderAfterServicePODb, orderReturnPO);
        }


        /**
         * 退款类型变更校验
         */
        if (refundType != RefundType.value(orderReturnPO.getRefundType())
                && RefundType.ZERO_YUAN_REFUND != RefundType.value(orderReturnPO.getRefundType())
                && RefundType.OFFLINE_REFUND != RefundType.value(orderReturnPO.getRefundType())) {
            // 由恢复额度变为代还后，处理批次所有退款单
            if (RefundType.value(orderReturnPO.getRefundType()) == RefundType.RESTORE_LIMIT) {
                OrderReturnExample orderReturnExample = new OrderReturnExample();
                orderReturnExample.setOrderSn(orderReturnPO.getOrderSn());
                List<OrderReturnPO> orderReturnPOList = orderReturnModel.getOrderReturnList(orderReturnExample, null);
                for (OrderReturnPO orderReturn : orderReturnPOList) {
                    updateRefundType(orderReturn.getReturnId(), refundType, remark);
                    // 计算扣罚金额，实际退款金额需要减去扣罚金额 并更新
                    OrderReturnPO orderReturnUpdated = orderReturnService.getByAfsSn(orderReturnPO.getAfsSn());
                    if (orderReturnValidation.refundPunishAmountSupport(orderReturnUpdated)) {
                        orderAfterServiceModel.recalculateRefundAmount(orderPODb, orderReturnUpdated);
                    }
                }
            } else {
                updateRefundType(orderReturnPO.getReturnId(), refundType, remark);
            }
            return "当前订单[已起息],退款类型发生变更";
        }
        /**
         * 预售，策略模式，Andy
         */
        LoanRefundStrategy loanRefundStrategy = loanRefundStrategyContext.getStrategy(orderPODb.getOrderType());
        /**
         * 退款类型变更校验，Andy.2022.05-27.组合退款check退款类型，预付尾款是否参与贷款支付
         */
        if (refundType == RefundType.COMBINATION_REFUND) {
            //预付尾款是否参与贷款支付，是否在本次还款
            OrderRefundRecordPO orderRefundRecordPO = orderRefundRecordService.queryLoanRefundByAfsSn(afsSn);
            if (ObjectUtils.isNotEmpty(orderRefundRecordPO)) {
                if (!RefundType.SQUARE_OFFLINE_REFUND.getValue().equals(orderRefundRecordPO.getRefundType())) {
                    orderPODb.setPaymentCode(orderRefundRecordPO.getPaymentCode());
                    refundType = orderAfterServiceModel.getRefundType(orderPODb);//计算尾款退款方式
                    if (refundType != RefundType.value(orderRefundRecordPO.getRefundType())) {
                        orderRefundRecordService.updateRefundTypeById(orderRefundRecordPO.getId(), refundType.getValue());
                        // 计算扣罚金额，实际退款金额需要减去扣罚金额 并更新
                        OrderReturnPO orderReturnUpdated = orderReturnService.getByAfsSn(orderReturnPO.getAfsSn());
                        if (orderReturnValidation.refundPunishAmountSupport(orderReturnUpdated)) {
                            orderAfterServiceModel.recalculateRefundAmount(orderPODb, orderReturnUpdated);
                        }
                        return "当前订单[已起息],组合支付退款类型发生变更";
                    }
                }

            }
        }

        if (isPass) {
            // 先重置退款扣罚金额
            boolean resetRefundPunishAmountResult = orderReturnService.resetRefundPunishAmount(orderReturnPO, refundPunishAmount);
            BizAssertUtil.isTrue(!resetRefundPunishAmountResult,
                    String.format("退款单%s重置退款扣罚金额失败，原扣罚金额：%f，修正的退款金额：%f", orderReturnPO.getAfsSn(),
                            orderReturnPO.getRefundPunishAmount(), refundPunishAmount));
            BizAssertUtil.isTrue(!this.handleRefundCommission(orderPODb, orderAfterServicePODb, orderReturnPO),
                    "退款校验不通过,原因:退单佣金计算失败");
            BizAssertUtil.isTrue(OrdersAfsConst.JD_INTERCEPT_STATE_INTERCEPTING == orderReturnPO.getJdInterceptStatus()
                            || OrdersAfsConst.JD_INTERCEPT_STATE_INTERCEPTED_FAIL == orderReturnPO.getJdInterceptStatus()
                    , "该售后单已对接第三方快递拦截,不可审批通过");
        } else {
            BizAssertUtil.isTrue(OrdersAfsConst.JD_INTERCEPT_STATE_INTERCEPTING == orderReturnPO.getJdInterceptStatus()
                            || OrdersAfsConst.JD_INTERCEPT_STATE_INTERCEPTED_SUCCESS == orderReturnPO.getJdInterceptStatus()
                    , "该售后单已对接第三方快递拦截,不可审批拒绝");
        }

        // 平台同意退款，把实际退款金额、客户承担、其他赔偿重新计算一下
        if ((RefundType.ASSIST_PAYMENT.getValue().equals(orderReturnPO.getRefundType())
                || refundType == RefundType.ASSIST_PAYMENT
                || RefundType.SQUARE_OFFLINE_REFUND.getValue().equals(orderReturnPO.getRefundType())) //Andy.增加组合支付退款，尾款退款方式=代还退款。 重新试算
                && ObjectUtils.isEmpty(orderReturnPO.getRefundEndTime()) && isPass) {
            if (ObjectUtils.isNotEmpty(loanRefundStrategy)) {
                log.info("【商家审核退款申请】发起【tryCalculateChanged】afsSn：{} xid:{} 开启策略模式", orderReturnPO.getAfsSn(), RootContext.getXID());

                String resultS = loanRefundStrategy.tryCaculateChanged(afsSn);
                if (!OrderConst.RESULT_CODE_SUCCESS.equals(resultS)) {
                    return resultS;
                }
                this.remainPlanDiscountAmount(orderReturnPO, orderPODb);

                //校验电子账户余额
                loanRefundStrategy.verifyElectronicAccountBalance(afsSn, orderPODb);

            } else {
                log.info("【商家审核退款申请】发起【tryCalculateChanged】afsSn：{} xid:{} 默认模式", orderReturnPO.getAfsSn(), RootContext.getXID());
                String resultS = this.tryCaculateChanged(afsSn);

                if (!OrderConst.RESULT_CODE_SUCCESS.equals(resultS)) {
                    return resultS;
                }
                this.remainPlanDiscountAmount(orderReturnPO, orderPODb);
                //校验电子账户余额
                iPayHandleService.verifyExecute(orderPODb, orderReturnPO, PayMethodEnum.ENJOY_PAY.getValue());

            }
        }
        if (RefundType.TRANSFER_REFUND.getValue().equals(orderReturnPO.getRefundType())
                && ObjectUtils.isEmpty(orderReturnPO.getRefundEndTime()) && isPass) {
            //校验电子账户余额
            iPayHandleService.verifyExecute(orderPODb, orderReturnPO, PayMethodEnum.BANK_TRANSFER.getValue());
        }
        if (null != wxCombineXZCardOrderConfig.getOrderList() && !wxCombineXZCardOrderConfig.getOrderList().contains(orderAfterServicePODb.getOrderSn())) {
            if (RefundType.WXPAY_REFUND.getValue().equals(orderReturnPO.getRefundType())
                    && ObjectUtils.isEmpty(orderReturnPO.getRefundEndTime()) && isPass) {
                if (ObjectUtils.isNotEmpty(loanRefundStrategy)) {
                    log.info("【商家审核退款申请】发起【verifyElectronicAccountBalance】afsSn：{} xid:{} 开启策略模式", orderReturnPO.getAfsSn(), RootContext.getXID());
                    //校验电子账户余额
                    loanRefundStrategy.verifyElectronicAccountBalance(afsSn, orderPODb);
                } else {
                    //校验电子账户余额
                    iPayHandleService.verifyExecute(orderPODb, orderReturnPO, PayMethodEnum.WXPAY.getValue());
                }
            }
        }

        String operator = vendor.getStore().getStoreName();
        if (!StringUtils.isEmpty(vendor.getVendorMobile())) {
            operator = operator + "-" + vendor.getVendorMobile();
        }

        // 记录退款轨迹
        OrderReturnTrackPO orderReturnTrackPO =
                OrderReturnTrackPO.builder().afsSn(afsSn).operateType(OrderReturnOperateTypeEnum.STORE_AUDIT.getValue())
                        .operator(operator).operateTime(new Date())
                        .operateResult(isPass ? AuditResultEnum.AUDIT_PASS.getValue() : AuditResultEnum.AUDIT_REFUSE.getValue())
                        .operateRemark(remark).channel(channel).build();
        if (Objects.nonNull(role)) {
            orderReturnTrackPO.setOperatorRole(role.getValue());
        }
        orderReturnTrackMapper.insert(orderReturnTrackPO);

        log.info("###商家审核时退款状态：{}", refundType.getValue());
        if (RefundType.RESTORE_LIMIT.getValue().equals(refundType.getValue())) {
            // 如果是恢复额度,自动将订单下所有退款单审批通过,如果最新的退款状态是恢复额度，则退款状态不能发生变化 恢复额度会记录售后日志等
            return this.sellerRestoreLimitOperation(vendor, isPass, remark, storeAddressId, orderAfterServicePODb, applySource, role);
        } else if (RefundType.ASSIST_PAYMENT.getValue().equals(refundType.getValue())) { // 恢复额度--->代还退款
            // 判断退款类型是否发生变化,最新的退款状态和orderReturn状态不同，则发生了改变
            result = this.sellerAssistPaymentOperation(vendor, afsSn, isPass, remark, storeAddressId, refundType, applySource, role);
        }
        // 微信、支付宝 如果退款状态没有发生变化，则走该逻辑--->记录日志信息
        this.sellerRefundOperation(vendor, afsSn, isPass, remark, storeAddressId, orderAfterServicePODb, orderPODb, applySource, role);
        MDC.remove("userDTO");

        return result;
    }

    /**
     * 京东物流 指定店铺及非自提单，仅退款拦截
     *
     * @param orderAfterPO
     * @param orderReturnPO
     */
    private void dealErpJdRefundIntercept(OrderAfterPO orderAfterPO,OrderReturnPO orderReturnPO) {
        if(OrdersAfsConst.AFS_TYPE_REFUND != orderAfterPO.getAfsType()) {
            return;
        }
        switch (orderReturnPO.getJdInterceptStatus()) {
            //未拦截： ERP发货数校验，申请数 > 未发货数 ，风险提示
            case OrdersAfsConst.JD_INTERCEPT_STATE_DEFAULT:
                OrderProductPO orderProductPO = orderProductModel.getOrderProductByOrderProductId(orderAfterPO.getOrderProductId());
                if (orderAfterService.isJdInterceptOrderProduct(orderProductPO)) {
                    boolean checkResult = wmsIntegration.jingDongInterceptCheck(orderAfterPO.getAfsSn(),orderProductPO.getOrderSn(),orderReturnPO.getReturnNum(),orderProductPO.getChannelSkuId(),orderProductPO.getChannelSkuUnit());
                    // BizAssertUtil.isTrue(!checkResult, "商品已推送至【外部渠道】发货，为避免产生货损,需要先拦截快递再同意售后");
                    BizAssertUtil.isTrue(!checkResult, "商品已推送至【外部渠道】发货，暂不支持仅退款操作");
                    orderReturnPO.setJdInterceptStatus(OrdersAfsConst.JD_INTERCEPT_STATE_NO_INTERCEPTED);
                    orderReturnModel.updateJdInterceptStatus(orderAfterPO.getAfsSn(), OrdersAfsConst.JD_INTERCEPT_STATE_NO_INTERCEPTED);
                }
                break;
            /*case OrdersAfsConst.JD_INTERCEPT_STATE_INTERCEPTED_FAIL:
                throw new BusinessException("商品已推送至【外部渠道】发货，快递拦截失败，为避免产生货损，请让用户发起退货退款");
            case OrdersAfsConst.JD_INTERCEPT_STATE_INTERCEPTING:
                throw new BusinessException("商品已推送至【外部渠道】发货，快递正在拦截中为避免产生货损，需要等拦截成功再进行审批");*/
            case OrdersAfsConst.JD_INTERCEPT_STATE_WAIT_INTERCEPTED:
                throw new BusinessException("商品已推送至【外部渠道】发货，为避免产生货损,需要先拦截快递再同意售后");
            default:
                break;
        }
    }

    public void updateRefundType(Integer returnId, RefundType refundType, String remark) {
        OrderReturnPO updateReturn = new OrderReturnPO();
        updateReturn.setReturnId(returnId);
        updateReturn.setRefundType(refundType.getValue());
        updateReturn.setRemark(remark);
        orderReturnService.updateById(updateReturn);
    }

    private String sellerAssistPaymentOperation(Vendor vendor, String afsSn, boolean isPass, String remark,
                                                Integer storeAddressId, RefundType refundType, String applySource, OperationRoleEnum role) {
        OrderAfterPO orderAfterServicePODb = this.getOrderAfterServiceByAfsSn(afsSn);
        StringBuilder refundTypeChangedAfsSn = new StringBuilder();
        // 查询全部处理所有的退款单
        OrderReturnExample example = new OrderReturnExample();
        example.setOrderSn(orderAfterServicePODb.getOrderSn());
        List<OrderReturnPO> orderReturnPOList = orderReturnModel.getOrderReturnList(example, null);
        // 查询所有的orderAfterService
        OrderAfterServiceExample orderAfterServiceExample = new OrderAfterServiceExample();
        orderAfterServiceExample.setOrderSn(orderAfterServicePODb.getOrderSn());
        List<OrderAfterPO> orderAfterServicePOList =
                orderAfterServiceModel.getOrderAfterServiceList(orderAfterServiceExample, null);
        // 获取订单信息
        OrderPO orderPOLatest = orderModel.getOrderByOrderSn(orderAfterServicePODb.getOrderSn());
        // 处理退款类型发生变化
        if (!RefundType.isCombinationRefund(orderReturnPOList.get(0).getRefundType()) //预付，Andy
                && !refundType.getValue().equals(orderReturnPOList.get(0).getRefundType())) {
            // 重新计算退款单金额，更新退款单的金额
            for (OrderAfterPO orderAfterServicePOTemp : orderAfterServicePOList) {
                refundTypeChangedAfsSn.append(",").append(orderAfterServicePOTemp.getAfsSn());
                // OrderAfterDTO orderAfterDTO = JSON.parseObject(orderAfterServiceTemp.getSnapshotInformation(),
                // OrderAfterDTO.class);
                OrderAfterDTO orderAfterDTO = createorderAfterDTO(afsSn);
                List<AfsProductVO> afsProductVOS = orderAfterServiceModel.dealAfsProduct(orderPOLatest, orderAfterDTO);
                // AfsProductVO中 orderProductId --> orderAfterServiceTemp 中 orderProductId --> afsSn
                for (AfsProductVO afsProductVO : afsProductVOS) {
                    if (orderAfterServicePOTemp.getOrderProductId().compareTo(afsProductVO.getOrderProductId()) != 0) {
                        continue;
                    }
                    // 当前afsProductVO的orderProductId == orderAfterServiceTemp的orderProductId --->
                    // orderAfterServiceTemp中afsSn -->根据afsSn 更新orderReturn表中金额信息
                    OrderReturnPO orderReturnPOByAfsSn =
                            orderReturnModel.getOrderReturnByAfsSn(orderAfterServicePOTemp.getAfsSn());
                    // 构造新的OrderReturn对象，只更新需要更新的字段
                    OrderReturnPO orderReturnPOUpdate = new OrderReturnPO();
                    orderReturnPOUpdate.setReturnId(orderReturnPOByAfsSn.getReturnId());
                    orderReturnPOByAfsSn.setReturnType(afsProductVO.getRefundType());
                    // orderReturnByAfsSn.setOtherCompensationAmount(afsProductVO.getOtherCompensationAmount());
                    orderReturnPOByAfsSn.setRemark(afsProductVO.getRemark());
                    orderReturnPOByAfsSn.setReturnMoneyAmount(afsProductVO.getReturnMoneyAmount());
                    // orderReturnByAfsSn.setInterestPayer(afsProductVO.getInterestpayer());
                    orderReturnModel.updateOrderReturn(orderReturnPOUpdate);
                }
                // 退款状态由恢复额度变成代还后，那么商家操作其中一笔，就等于把全部的一起操作，所以变成代还退款后也应该全部操作
                if (ObjectUtils.isNotEmpty(orderAfterServicePOTemp.getStoreAuditTime())) {
                    continue;
                }
                orderAfterServicePODb = this.getOrderAfterServiceByAfsSn(orderAfterServicePOTemp.getAfsSn());
                sellerRefundOperation(vendor, orderAfterServicePOTemp.getAfsSn(), isPass, remark, storeAddressId,
                        orderAfterServicePODb, orderPOLatest, applySource, role);
                // 商家同意退款就重新试算金额，拒绝的话不再重新计算金额
                OrderReturnPO orderReturnPO =
                        orderReturnModel.getOrderReturnByAfsSn(orderAfterServicePOTemp.getAfsSn());
            }
            // 处理完返回
            return refundTypeChangedAfsSn.toString();
        }

        return OrderConst.RESULT_CODE_SUCCESS;
    }

    public String tryCaculateChanged(String afsSn) {
        // 查询orderReturn
        OrderReturnPO orderReturnPO = orderReturnService.getByAfsSn(afsSn);

        // 校验是否有处理中的贷款类退款单
        orderReturnValidation.refundTryCalculatePreCheck(orderReturnPO);

        if (orderReturnPO.getRefundType().equals(RefundType.SQUARE_OFFLINE_REFUND.getValue())) {
            return OrderConst.RESULT_CODE_SUCCESS;
        }

        Result<CDMallRefundTryResultVO> tryCaculateResult = null;

        OrderPO orderPo = orderPlacingService.getByOrderSn(orderReturnPO.getOrderSn());
        if (Objects.isNull(orderPo.getLendingSuccessTime())) {
            throw new MallException("还没有放款成功，请稍后重试！");
        }
        int days = DateUtil.days(DateUtil.format(orderPo.getLendingSuccessTime(), DateUtil.FORMAT_TIME),
                DateUtil.format(orderReturnPO.getApplyTime(), DateUtil.FORMAT_TIME), DateUtil.FORMAT_TIME);

        // 退款试算金额 = 申请退款金额（申请退款商品金额 + 申请退款的运费金额）- 退款扣罚金额
        BigDecimal tryAmount = orderReturnPO.getReturnMoneyAmount().add(orderReturnPO.getReturnExpressAmount())
                .subtract(orderReturnPO.getRefundPunishAmount());

        // 执行试算进行
        tryCaculateResult = orderAfterServiceModel.getTryCaculateResult(tryAmount, afsSn);

        if (this.tryCalculateSettleValidate(tryCaculateResult) && !orderAfterServiceModel.isSquareOfflineRefundWhiteList(orderPo.getStoreId())) {
            return "该借据已结清，请手动终止退款单！";
            //return OrderConst.RESULT_CODE_SUCCESS;
        }

        // 退款额度小于=1元处理（实际退款金额小于1元，请结清处理）
        if ("002005099".equals(tryCaculateResult.getErrorCode())) {
            // tryCaculateResult = orderAfterServiceModel.getTryCaculateResult(BigDecimal.ZERO, afsSn);
            log.info("tryCalculateChanged退款试算，退款额度小于=1元处理:{}，tryCalculateResult：{}", afsSn, tryCaculateResult);
            return "因还款试算金额不足1元，系统无法进行在线退款，可返回调整”退款扣罚金额”。";
        }


        //判断是否已结清，已结清&白名单商家，更改退款类型为线下退款 && 设置 退款扣罚、其他赔偿和客户承担为0
        if (orderAfterServiceModel.isOfflineWhiteListAndSettled(tryCaculateResult, orderPo.getStoreId())) {
            String content = "商家线下退款-因该用户已主动还清贷款,系统无法进行在线退款，只能由商家进行线下退款处理。";
            String isConfirmRefund = MDC.get("isConfirmRefund");
            //非平台端发起的退款
            if (!StringUtils.isEmpty(isConfirmRefund) && "true".equals(isConfirmRefund)
                    && OrderConst.RETURN_BY_3 != orderReturnPO.getReturnBy()) {
                return content;
            }
            OrderReturnPO orderReturnPOLatest = new OrderReturnPO();
            orderReturnPOLatest.setReturnId(orderReturnPO.getReturnId());
            orderReturnPOLatest.setCustomerAssumeAmount(BigDecimal.ZERO);
            orderReturnPOLatest.setOtherCompensationAmount(BigDecimal.ZERO);
            orderReturnPOLatest.setRefundPunishAmount(BigDecimal.ZERO);
            orderReturnPOLatest.setRefundType(RefundType.SQUARE_OFFLINE_REFUND.getValue());

            OrderAfterPO orderAfterPO = orderAfterServiceModel.getAfterServiceByAfsSn(afsSn);

            UserDTO userDTO;
            if (StringUtils.isEmpty(MDC.get("userDTO"))) {
                userDTO = new UserDTO(1L, "system", 1);
            } else {
                userDTO = JSONObject.parseObject(MDC.get("userDTO"), UserDTO.class);
            }
            // 记录退款轨迹
            Integer userRole = userDTO.getUserRole() == 1 ? 3 : 2;


            insertReturnLog(afsSn, userRole, userDTO.getUserRole(), userDTO.getUserId(), userDTO.getUserName(),
                    orderAfterPO.getAfsType(), orderReturnPO.getState().toString(), content, "WEB");

            orderReturnModel.updateOrderReturn(orderReturnPOLatest);
            return content;
        } else {
            CDMallRefundTryResultVO data = tryCaculateResult.getData();
            // 信贷应退总金额
            BigDecimal sumAmout = data.getSumAmount();
            log.info("tryCalculateChanged退款试算afsSn:{},sumAmount:{},tryAmount:{}", afsSn, sumAmout, tryAmount);
            OrderReturnPO orderReturnPOLatest = new OrderReturnPO();
            orderReturnPOLatest.setReturnId(orderReturnPO.getReturnId());
            //实际退款金额不存运费
            orderReturnPOLatest.setActualReturnMoneyAmount(sumAmout.subtract(orderReturnPO.getReturnExpressAmount()));
            orderReturnPOLatest.setCustomerAssumeAmount(BigDecimal.ZERO);
            orderReturnPOLatest.setOtherCompensationAmount(BigDecimal.ZERO);

            orderReturnPOLatest.setInterestPayer(InterestPayerEnum.NULL.getDesc());

            // 更新售后单的贷款类信息
            if (!orderReturnLoanExtendService.saveUpdateOrderReturnLoanExtend(orderReturnPO.getOrderSn(), orderReturnPO.getAfsSn(),
                    orderReturnPO.getOrderSn(), data)) {
                throw new MallException(String.format("更新售后单贷款类信息失败,orderSn:%s,afsSn:%s,CDMallRefundTryResultVO:%s",
                        orderReturnPO.getOrderSn(), orderReturnPO.getAfsSn(), data));
            }

            // 本次试算信贷应退总金额等于上次试算金额信贷应退总金额 OR 本次试算信贷应退总金额等于当前试算金额（未产生利息，初始化actualReturnMoneyAmount=returnExpressAmount）
            if (sumAmout.compareTo(orderReturnPO.getActualReturnMoneyAmount().add(orderReturnPO.getReturnExpressAmount())) == 0) {
                log.info("【tryCalculateChanged】退款处理逻辑afsSn:{},sumAmount:{},actualAmount:{} 信贷应退款金额与电商应退款金额一致。", afsSn, sumAmout, orderReturnPO.getActualReturnMoneyAmount().add(orderReturnPO.getReturnExpressAmount()));
                // 试算发起的退款金额可能变动，不足1元的特殊场景下，得到的试算金额是相同的，所以依旧要重新处理客户承担和其它赔偿
                if (sumAmout.compareTo(tryAmount) > 0) {
                    // 实际还款金额 > 发起退款的金额，多余部分计入其它赔偿，商家/平台承担该部分多余金额
                    orderReturnPOLatest.setInterestPayer(days < 7 ? InterestPayerEnum.PLATFORM.getDesc() : InterestPayerEnum.STORE.getDesc());
                    // 计算其他赔偿
                    orderReturnPOLatest.setOtherCompensationAmount(sumAmout.subtract(tryAmount));
                    log.info("recalculate orderReturnPOLatest is {},", orderReturnPOLatest);
                    orderReturnModel.updateOrderReturn(orderReturnPOLatest);
                } else if (sumAmout.compareTo(tryAmount) < 0) {
                    // 实际还款金额 < 发起退款的金额，少的部分计入客户承担，客户已其它途径偿还
                    orderReturnPOLatest.setCustomerAssumeAmount(tryAmount.subtract(sumAmout));
                    log.info("recalculate orderReturnPOLatest is {},", orderReturnPOLatest);
                    orderReturnModel.updateOrderReturn(orderReturnPOLatest);
                }

                return OrderConst.RESULT_CODE_SUCCESS;
            }
            if (sumAmout.compareTo(tryAmount) > 0) {
                // 实际还款金额 > 发起退款的金额，多余部分计入其它赔偿，商家/平台承担该部分多余金额
                orderReturnPOLatest.setInterestPayer(days < 7 ? InterestPayerEnum.PLATFORM.getDesc() : InterestPayerEnum.STORE.getDesc());
                //计算其他赔偿
                orderReturnPOLatest.setOtherCompensationAmount(sumAmout.subtract(tryAmount));
                // 更新orderReturn表金额字段
                orderReturnModel.updateOrderReturn(orderReturnPOLatest);
                return "代还退款，产生差额" + orderReturnPOLatest.getOtherCompensationAmount() + "，计入【其他赔偿】，该笔金额" + orderReturnPOLatest.getInterestPayer();
            }
            if (sumAmout.compareTo(tryAmount) < 0) {
                //实际还款金额 < 发起退款的金额，少的部分计入客户承担，客户已其它途径偿还
                orderReturnPOLatest.setCustomerAssumeAmount(tryAmount.subtract(sumAmout));
                orderReturnModel.updateOrderReturn(orderReturnPOLatest);
                return "代还退款，产生差额" + orderReturnPOLatest.getCustomerAssumeAmount() + "，计入【客户承担】";
            }
            if (sumAmout.compareTo(tryAmount) == 0) {
                orderReturnModel.updateOrderReturn(orderReturnPOLatest);
                return "代还退款，差额已处理";
            }
            log.info("【tryCalculateChanged】试算afsSn：{} orderSn:{} 情况未匹配异常", afsSn, orderReturnPO.getOrderSn());
            throw new MallException("试算异常，退款单号【" + afsSn + "】请联系技术小哥哥，协助解决。");
        }
    }

    public void insertReturnLog(String afsSn, Integer operateType, Integer role, Long userId
            , String operateName, Integer afsType, String state, String content, String channel) {
        // 记录退款轨迹
        OrderReturnTrackPO orderReturnTrackPO = OrderReturnTrackPO.builder().afsSn(afsSn)
                .operateType(operateType)
                .operatorRole(role)
                .operator(userId + "-" + operateName)
                .operateTime(new Date())
                .operateResult(AuditResultEnum.AUDIT_PASS.getValue())
                .operateRemark(content)
                .channel(channel)
                .build();

        orderReturnTrackMapper.insert(orderReturnTrackPO);

        //记录售后轨迹 OrderAfterSaleLogPO

        // 记录售后日志
        OrderAfterSaleLogPO afterSaleLog = new OrderAfterSaleLogPO();
        afterSaleLog.setLogRole(role);
        afterSaleLog.setLogUserId(userId);
        afterSaleLog.setLogUserName(operateName);
        afterSaleLog.setAfsSn(afsSn);
        afterSaleLog.setAfsType(afsType);
        afterSaleLog.setState(state);
        afterSaleLog.setContent(content);
        afterSaleLog.setCreateTime(new Date());

        orderAfterSaleLogMapper.insert(afterSaleLog);
    }


    /**
     * 是否为借据结清
     *
     * @param tryCalculateResult 借贷试算返回结果
     * @return true / false
     */
    private boolean tryCalculateSettleValidate(Result<CDMallRefundTryResultVO> tryCalculateResult) {
        return !tryCalculateResult.isSuccess() && "0024990099".equals(tryCalculateResult.getErrorCode());
    }

    /**
     * 通过asfSn构造OrderAfterDTO
     */
    public OrderAfterDTO createorderAfterDTO(String afsSn) {
        OrderAfterPO orderAfterServicePODb = this.getOrderAfterServiceByAfsSn(afsSn);
        // 查询全部处理所有的退款单
        OrderReturnExample example = new OrderReturnExample();
        example.setOrderSn(orderAfterServicePODb.getOrderSn());
        List<OrderReturnPO> orderReturnPOList = orderReturnModel.getOrderReturnList(example, null);
        OrderAfterDTO orderAfterDTO = new OrderAfterDTO();
        for (OrderReturnPO orderReturnPO : orderReturnPOList) {
            if (!orderReturnPO.getAfsSn().equals(orderAfterServicePODb.getAfsSn())) {
                continue;
            }
            orderAfterDTO.setOrderSn(orderReturnPO.getOrderSn());
            orderAfterDTO.setAfsType(orderAfterServicePODb.getAfsType());
            orderAfterDTO.setAfsDescription(orderAfterServicePODb.getAfsDescription());
            orderAfterDTO.setGoodsState(orderAfterServicePODb.getGoodsState());
            orderAfterDTO.setApplyReasonContent(orderAfterServicePODb.getApplyReasonContent());
            orderAfterDTO.setApplyImage(orderAfterServicePODb.getApplyImage());
            orderAfterDTO.setFinalReturnAmount(orderReturnPO.getActualReturnMoneyAmount());
            List<OrderAfterDTO.AfterProduct> productList = new ArrayList<>();
            OrderAfterDTO.AfterProduct afterProduct = new OrderAfterDTO.AfterProduct();
            afterProduct.setOrderProductId(orderAfterServicePODb.getOrderProductId());
            afterProduct.setAfsNum(orderAfterServicePODb.getAfsNum());
            afterProduct.setReturnAmount(orderReturnPO.getReturnMoneyAmount());
            productList.add(afterProduct);
            orderAfterDTO.setProductList(productList);
        }
        return orderAfterDTO;
    }

    @GlobalTransactional
    private String sellerRestoreLimitOperation(Vendor vendor, boolean isPass, String remark, Integer storeAddressId,
                                               OrderAfterPO orderAfterServicePODb, String applySource, OperationRoleEnum role) {
        // 查询所有的orderAfterService
        OrderAfterServiceExample orderAfterServiceExample = new OrderAfterServiceExample();
        orderAfterServiceExample.setOrderSn(orderAfterServicePODb.getOrderSn());
        List<OrderAfterPO> orderAfterServicePOList =
                orderAfterServiceModel.getOrderAfterServiceList(orderAfterServiceExample, null);
        // 获取订单信息
        OrderPO orderPO = orderModel.getOrderByOrderSn(orderAfterServicePODb.getOrderSn());

        for (OrderAfterPO orderAfterPO : orderAfterServicePOList) {
            // 查询退款单信息，根据状态判断是否需要通过
            OrderReturnPO orderReturnPO = orderReturnService.getByAfsSn(orderAfterPO.getAfsSn());
            log.info("sellerRestoreLimitOperation orderReturnPO info : {}", JSON.toJSONString(orderReturnPO));
            if (!OrderReturnStatus.REFUND_APPLY.getValue().equals(orderReturnPO.getState()) &&
                    !OrderReturnStatus.RETURN_APPLY.getValue().equals(orderReturnPO.getState())) {
                continue;
            }
            sellerRefundOperation(vendor, orderAfterPO.getAfsSn(), isPass, remark, storeAddressId, orderAfterPO, orderPO,
                    applySource, role);
        }
        return OrderConst.RESULT_CODE_SUCCESS;
    }

    @GlobalTransactional
    private void sellerRefundOperation(Vendor vendor, String afsSn, boolean isPass, String remark, Integer storeAddressId,
                                       OrderAfterPO orderAfterServicePODb, OrderPO orderPODb, String applySource, OperationRoleEnum role) {
        // 记录售后日志
        OrderAfterSaleLogPO afterSaleLog = new OrderAfterSaleLogPO();
        afterSaleLog.setLogRole(OrderConst.LOG_ROLE_VENDOR);
        afterSaleLog.setLogUserId(vendor.getVendorId());
        afterSaleLog.setLogUserName(vendor.getVendorName());
        afterSaleLog.setAfsSn(afsSn);
        afterSaleLog.setCreateTime(new Date());

        // 售后数量
        int afsNun = 0;
        // 售后金额
        BigDecimal afsAmount = BigDecimal.ZERO;

        if (isPass) {
            // 审核通过
            // 更新售后服务信息
            OrderAfterPO updateAfter = new OrderAfterPO();
            updateAfter.setAfsId(orderAfterServicePODb.getAfsId());
            updateAfter.setAfsSn(afsSn);

            // 商家收货地址
            updateAfter.setStoreAfsAddress(storeAddressId == null ? null : storeAddressId + "");
            updateAfter.setStoreAuditTime(new Date());
            updateAfter.setStoreRemark(remark);
            log.info("###更新orderAfterSevice商家操作：{}", JSON.toJSONString(updateAfter));
            int update = orderAfterMapper.updateByAfsSn(updateAfter);
            AssertUtil.isTrue(update == 0, "更新售后服务信息失败");

            // 更新退换货表
            if (orderAfterServicePODb.getAfsType().equals(OrdersAfsConst.AFS_TYPE_RETURN)) {
                // 类型为退货退款，更新退货表
                OrderReturnPO orderReturnPODb = this.getOrderReturnByAfsSn(afsSn);
                // AssertUtil.isTrue(!orderReturnDb.getState().equals(OrdersAfsConst.RETURN_STATE_101), "未到商家审核状态");

                OrderReturnPO updateReturn = new OrderReturnPO();
                updateReturn.setReturnId(orderReturnPODb.getReturnId());
                updateReturn.setState(OrdersAfsConst.RETURN_STATE_201);
                log.info("###更新OrderReturn退货退款操作：{}", JSON.toJSONString(updateReturn));
                update = orderReturnMapper.updateByPrimaryKeySelective(updateReturn);
                AssertUtil.isTrue(update == 0, "更新退货信息失败");

                // 日志信息
                afterSaleLog.setAfsType(OrdersAfsConst.AFS_TYPE_RETURN);
                afterSaleLog.setState(OrdersAfsConst.RETURN_STATE_201 + "");
                afterSaleLog.setContent("商家同意退货退款申请");

                afsAmount = orderReturnPODb.getReturnMoneyAmount().add(orderReturnPODb.getReturnExpressAmount());
                afsNun = orderReturnPODb.getReturnNum();
            } else if (orderAfterServicePODb.getAfsType().equals(OrdersAfsConst.AFS_TYPE_REPLACEMENT)) {
                // 类型为换货，更新换货表
                OrderReplacementPO orderReplacementPODb = this.getOrderReplacementByAfsSn(afsSn);
                BizAssertUtil.isTrue(
                        !orderReplacementPODb.getState().equals(OrdersAfsConst.REPLACEMENT_STATE_MEMBER_APPLY), "未到商家审核状态");

                // 更新换货信息
                OrderReplacementPO updateReplacement = new OrderReplacementPO();
                updateReplacement.setReplacementId(orderReplacementPODb.getReplacementId());
                updateReplacement.setState(OrdersAfsConst.REPLACEMENT_STATE_STORE_AUDIT_PASS);
                update = orderReplacementMapper.updateByPrimaryKeySelective(updateReplacement);
                AssertUtil.isTrue(update == 0, "更新换货信息失败");

                // 日志信息
                afterSaleLog.setAfsType(OrdersAfsConst.AFS_TYPE_REPLACEMENT);
                afterSaleLog.setState(OrdersAfsConst.REPLACEMENT_STATE_STORE_AUDIT_PASS + "");
                afterSaleLog.setContent("商家审核通过,等待买家发货");

                afsNun = orderReplacementPODb.getReplacementNum();
            } else if (orderAfterServicePODb.getAfsType().equals(OrdersAfsConst.AFS_TYPE_REFUND)) {
                // 类型为仅退款，更新退货表
                OrderReturnPO orderReturnPODb = this.getOrderReturnByAfsSn(afsSn);
                BizAssertUtil.isTrue(!orderReturnPODb.getState().equals(OrdersAfsConst.RETURN_STATE_100), "未到商家审核状态");

                OrderReturnPO updateReturn = new OrderReturnPO();
                updateReturn.setReturnId(orderReturnPODb.getReturnId());
                updateReturn.setState(OrdersAfsConst.RETURN_STATE_200);
                update = orderReturnMapper.updateByPrimaryKeySelective(updateReturn);
                AssertUtil.isTrue(update == 0, "更新退货信息失败");

                // 日志信息
                afterSaleLog.setAfsType(OrdersAfsConst.AFS_TYPE_REFUND);
                afterSaleLog.setState(OrdersAfsConst.RETURN_STATE_200 + "");
                afterSaleLog.setContent("商家同意退款申请");

                afsAmount = orderReturnPODb.getReturnMoneyAmount().add(orderReturnPODb.getReturnExpressAmount());
                afsNun = orderReturnPODb.getReturnNum();
            } else {
                throw new MallException("售后类型不正确");
            }
        } else {
            // 审核失败
            BizAssertUtil.isTrue(StringUtils.isEmpty(remark), "请填写拒绝原因");

            /**
             * 预售，策略模式，Andy
             */
            LoanRefundStrategy loanRefundStrategy = loanRefundStrategyContext.getStrategy(orderPODb.getOrderType());
            if (ObjectUtils.isNotEmpty(loanRefundStrategy)) {
                log.info("预售admin审批拒绝，策略模式开启，afsSn:{}", afsSn);
                loanRefundStrategy.refundRefuse(afsSn);
            }

            // 更新售后信息
            Date nowDate = new Date();
            OrderAfterPO updateAfter = new OrderAfterPO();
            updateAfter.setAfsId(orderAfterServicePODb.getAfsId());
            updateAfter.setStoreRemark(remark);
            updateAfter.setStoreAuditTime(nowDate);
            updateAfter.setRefundTerminationTime(nowDate);
            int update = orderAfterMapper.updateByPrimaryKeySelective(updateAfter);
            BizAssertUtil.isTrue(update == 0, "更新售后信息失败");

            // 恢复订单商品行已退数量
            orderProductService.deductReturnNumber(orderAfterServicePODb.getOrderProductId(),
                    orderAfterServicePODb.getAfsNum());
            // 处理订单商品行退款状态
            orderProductService.dealOrderProductReturnStatus(CommonConst.PRODUCT_RETURN_REJECT,
                    orderAfterServicePODb.getOrderProductId());

            // 更新退换货表
            if (orderAfterServicePODb.getAfsType().equals(OrdersAfsConst.AFS_TYPE_RETURN)
                    || orderAfterServicePODb.getAfsType().equals(OrdersAfsConst.AFS_TYPE_REFUND)) {
                // 类型为退货退款/仅退款，更新退货表
                OrderReturnPO orderReturnPODb = this.getOrderReturnByAfsSn(afsSn);

                OrderReturnPO updateReturn = new OrderReturnPO();
                updateReturn.setReturnId(orderReturnPODb.getReturnId());
                updateReturn.setState(OrdersAfsConst.RETURN_STATE_202);
                update = orderReturnMapper.updateByPrimaryKeySelective(updateReturn);
                AssertUtil.isTrue(update == 0, "更新退货信息失败");

                // 审核失败，修改订单锁定状态
                OrderPO orderPO = orderModel.getOrderByOrderSn(orderReturnPODb.getOrderSn());
                OrderPO orderPOUpdate = new OrderPO();
                orderPOUpdate.setOrderSn(orderReturnPODb.getOrderSn());
                orderPOUpdate.setLockState(orderPO.getLockState() - 1);
                update = orderModel.updateOrder(orderPOUpdate);
                AssertUtil.isTrue(update == 0, "修改订单锁定状态失败");

                // 日志信息
                afterSaleLog.setAfsType(OrdersAfsConst.AFS_TYPE_RETURN);
                afterSaleLog.setState(OrdersAfsConst.RETURN_STATE_202 + "");
                afterSaleLog.setContent("退款拒绝，拒绝原因：" + remark);

                afsAmount = orderReturnPODb.getReturnMoneyAmount().add(orderReturnPODb.getReturnExpressAmount());
                afsNun = orderReturnPODb.getReturnNum();
            } else if (orderAfterServicePODb.getAfsType().equals(OrdersAfsConst.AFS_TYPE_REPLACEMENT)) {
                // 类型为换货，更新换货表
                OrderReplacementPO orderReplacementPODb = this.getOrderReplacementByAfsSn(afsSn);

                // 更新换货信息
                OrderReplacementPO updateReplacement = new OrderReplacementPO();
                updateReplacement.setReplacementId(orderReplacementPODb.getReplacementId());
                updateReplacement.setState(OrdersAfsConst.REPLACEMENT_STATE_STORE_AUDIT_FAIL);
                update = orderReplacementMapper.updateByPrimaryKeySelective(updateReplacement);
                AssertUtil.isTrue(update == 0, "更新换货信息失败");

                // 日志信息
                afterSaleLog.setAfsType(OrdersAfsConst.AFS_TYPE_REPLACEMENT);
                afterSaleLog.setState(OrdersAfsConst.REPLACEMENT_STATE_STORE_AUDIT_FAIL + "");
                afterSaleLog.setContent("审核拒绝，拒绝原因：" + remark);

                afsNun = orderReplacementPODb.getReplacementNum();
            } else {
                throw new MallException("售后类型不正确");
            }
        }

        int update = orderAfterSaleLogMapper.insert(afterSaleLog);
        if (update == 0) {
            throw new MallException("记录售后日志信息失败");
        }

        // 发送成功和失败的事件消息
        OrderReturnPO orderReturnPO = this.getOrderReturnByAfsSn(afsSn);
        if (isPass) {
            // 暂时没有商家审批通过的事件消息
            orderCreateHelper.addOrderReturnEvent(orderPODb, orderPODb.getXzCardAmount(), orderReturnPO,
                    OrderEventEnum.STORE_AGREE, new Date(), applySource);
        } else {
            // 发送商家拒绝的消息
            orderCreateHelper.addOrderReturnEvent(orderPODb, orderPODb.getXzCardAmount(), orderReturnPO,
                    OrderEventEnum.STORE_REFUSED, new Date(), applySource);
        }

        // 发送消息通知
        try {
            this.sendMsgAfterSale(orderAfterServicePODb, afsNun, afsAmount);
        } catch (Exception e) {
            log.info("发送消息通知异常,orderAfterServiceDb:{},afsNun:{},afsAmount:{}", JSON.toJSONString(orderAfterServicePODb),
                    afsNun, afsAmount);
            log.info("发送消息通知异常", e);
        }

    }

    /**
     * 商家确认收货
     *
     * @param vendor       商家信息
     * @param afsSn        退款单号
     * @param isReceive    是否收货
     * @param remark       备注
     * @param channel      操作渠道
     * @param distribution 是否是配销
     * @param refundPunish 退款扣罚金额
     * @return
     */
    @Transactional
    public String afsStoreReceive(Vendor vendor, String afsSn, boolean isReceive, String remark, String channel,
                                  Integer distribution, BigDecimal refundPunish, OperationRoleEnum role, String applySource) {
        // 查询售后服务信息
        OrderAfterPO orderAfterServicePODb = this.getOrderAfterServiceByAfsSn(afsSn);

        // 权限校验
        orderReturnValidation.storeAuditStateAuditValidate(role, vendor.getStoreId(), orderAfterServicePODb.getStoreId(), distribution);

        // 商家收货校验
        orderReturnAuditValidation.afsStoreReceiveValidate(orderAfterServicePODb);

        LambdaQueryWrapper<OrderReturnPO> orderReturnaQuery = new LambdaQueryWrapper<>();
        orderReturnaQuery.eq(OrderReturnPO::getAfsSn, afsSn);
        OrderReturnPO orderReturnPO = orderReturnService.getOne(orderReturnaQuery);

        // 实时获取退款类型
        RefundType refundType = RefundType.value(orderReturnPO.getRefundType());
        if (!RefundType.SQUARE_OFFLINE_REFUND.getValue().equals(orderReturnPO.getRefundType())) {
            // 实时获取退款类型
            refundType = orderAfterServiceModel.getRefundType(orderAfterServicePODb.getOrderSn());
        }

        /**
         * 退款类型变更校验
         */
        if (refundType != RefundType.value(orderReturnPO.getRefundType())
                && RefundType.ZERO_YUAN_REFUND != RefundType.value(orderReturnPO.getRefundType())
                && RefundType.OFFLINE_REFUND != RefundType.value(orderReturnPO.getRefundType())) {
            OrderReturnPO updateReturn = new OrderReturnPO();
            updateReturn.setReturnId(orderReturnPO.getReturnId());
            updateReturn.setRefundType(refundType.getValue());
            updateReturn.setRemark(remark);
            orderReturnService.updateById(updateReturn);

            return "当前订单[已起息],退款类型发生变更";
        }

        // 先重置退款扣罚金额
        boolean resetRefundPunishAmountResult = orderReturnService.resetRefundPunishAmount(orderReturnPO, refundPunish);
        BizAssertUtil.isTrue(!resetRefundPunishAmountResult,
                String.format("退款单%s重置退款扣罚金额失败，原扣罚金额：%f，修正的退款金额：%f", orderReturnPO.getAfsSn(),
                        orderReturnPO.getRefundPunishAmount(), refundPunish));

        // 平台同意退款，把实际退款金额、客户承担、其他赔偿重新计算一下
        if (RefundType.ASSIST_PAYMENT.getValue().equals(orderReturnPO.getRefundType())
                && ObjectUtils.isEmpty(orderReturnPO.getRefundEndTime())) {
            String resultS = tryCaculateChanged(afsSn);
            if (!OrderConst.RESULT_CODE_SUCCESS.equals(resultS)) {
                return resultS;
            }
        }

        OrderAfterPO afterServiceByAfsSn = orderAfterServiceModel.getAfterServiceByAfsSn(afsSn);

        // 恢复额度时，该批次下所有的收货单一起审批
        if (RefundType.RESTORE_LIMIT.getValue().equals(refundType.getValue())) {
            // 查出所有买家收货（待商家收货）的退款单 OrderReturnPO
            LambdaQueryWrapper<OrderReturnPO> orderReturnByOrderSnQuery = Wrappers.lambdaQuery();
            orderReturnByOrderSnQuery.select(OrderReturnPO::getAfsSn)
                    .eq(OrderReturnPO::getOrderSn, afterServiceByAfsSn.getOrderSn())
                    .eq(OrderReturnPO::getState, OrderReturnStatus.WAIT_RETURN.getValue())
                    .eq(OrderReturnPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
            List<String> afsSns = orderReturnService.list(orderReturnByOrderSnQuery)
                    .stream().map(OrderReturnPO::getAfsSn).collect(Collectors.toList());

            // 再查出对应的所有的 OrderAfterPO
            LambdaQueryWrapper<OrderAfterPO> orderAfterByAfsSnsQuery = Wrappers.lambdaQuery();
            orderAfterByAfsSnsQuery.in(OrderAfterPO::getAfsSn, afsSns)
                    .eq(OrderAfterPO::getOrderSn, afterServiceByAfsSn.getOrderSn())
                    .eq(OrderAfterPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
            List<OrderAfterPO> orderAfterPos = orderAfterMapper.selectList(orderAfterByAfsSnsQuery);

            for (OrderAfterPO orderAfterServicePOTemp : orderAfterPos) {
                if (ObjectUtils.isNotEmpty(orderAfterServicePOTemp.getStoreReceiveTime())) {
                    continue;
                }
                storeReceive(orderAfterServicePOTemp, isReceive, remark, vendor);
            }
        } else {
            storeReceive(afterServiceByAfsSn, isReceive, remark, vendor);
        }

        // 记录退款轨迹
        OrderReturnTrackPO orderReturnTrackPO = OrderReturnTrackPO.builder().afsSn(afsSn)
                .operateType(OrderReturnOperateTypeEnum.STORE_RECEIVE.getValue())
                .operator(vendor.getStore().getStoreName() + "-" + vendor.getVendorMobile()).operateTime(new Date())
                .operateResult(isReceive ? AuditResultEnum.AUDIT_PASS.getValue() : AuditResultEnum.AUDIT_REFUSE.getValue())
                .operateRemark(remark).channel(channel).build();
        if (Objects.nonNull(role)) {
            orderReturnTrackPO.setOperatorRole(role.getValue());
        }
        orderReturnTrackMapper.insert(orderReturnTrackPO);

        // 发送售后发货MQ通知
        OrderPO orderPO = orderService.getByOrderSn(orderReturnPO.getOrderSn());
        if (isReceive) {
            orderCreateHelper.addOrderReturnEvent(orderPO, orderPO.getXzCardAmount(), orderReturnPO,
                    OrderEventEnum.REFUND_STORE_RECEIVE_GOODS, new Date(), applySource);
        } else {
            orderCreateHelper.addOrderReturnEvent(orderPO, orderPO.getXzCardAmount(), orderReturnPO,
                    OrderEventEnum.REFUND_STORE_REJECT_RECEIVE_GOODS, new Date(), applySource);
        }

        return OrderConst.RESULT_CODE_SUCCESS;
    }

    private void storeReceive(OrderAfterPO ofs, boolean isReceive, String remark, Vendor vendor) {

        int afsNun = 0;// 售后数量
        BigDecimal afsAmount = BigDecimal.ZERO;// 售后金额

        // 更新售后服务信息
        Date nowDate = new Date();
        OrderAfterPO updateAfter = new OrderAfterPO();
        updateAfter.setAfsId(ofs.getAfsId());
        updateAfter.setStoreRemark(remark);
        updateAfter.setStoreReceiveTime(nowDate);
        if (!isReceive) {
            updateAfter.setRefundTerminationTime(nowDate);
        }
        int update = orderAfterMapper.updateByPrimaryKeySelective(updateAfter);
        AssertUtil.isTrue(update == 0, "更新售后服务信息失败");

        // 记录售后日志
        OrderAfterSaleLogPO afterSaleLog = new OrderAfterSaleLogPO();
        afterSaleLog.setLogRole(OrderConst.LOG_ROLE_VENDOR);
        afterSaleLog.setLogUserId(vendor.getVendorId());
        afterSaleLog.setLogUserName(vendor.getVendorName());
        afterSaleLog.setAfsSn(ofs.getAfsSn());
        afterSaleLog.setCreateTime(new Date());

        if (ofs.getAfsType() == OrdersAfsConst.AFS_TYPE_RETURN) {
            // 退货退款
            OrderReturnPO orderReturnPODb = this.getOrderReturnByAfsSn(ofs.getAfsSn());
            AssertUtil.isTrue(!orderReturnPODb.getState().equals(OrdersAfsConst.RETURN_STATE_102), "未到商家收货状态");

            // 更新退货状态
            OrderReturnPO updateReturn = new OrderReturnPO();
            updateReturn.setReturnId(orderReturnPODb.getReturnId());
            if (isReceive) {
                // 商家收货
                updateReturn.setState(OrdersAfsConst.RETURN_STATE_203);
                afterSaleLog.setState(OrdersAfsConst.RETURN_STATE_203 + "");
                afterSaleLog.setContent("商家确认收货");
            } else {
                AssertUtil.isTrue(StringUtils.isEmpty(remark), "请填写拒收原因");

                updateReturn.setState(OrdersAfsConst.RETURN_STATE_202);
                afterSaleLog.setState(OrdersAfsConst.RETURN_STATE_202 + "");
                afterSaleLog.setContent("商家拒收，拒收原因：" + remark);

                // 恢复订单商品行已退数量
                orderProductService.deductReturnNumber(ofs.getOrderProductId(), ofs.getAfsNum());
                // 处理订单商品行退款状态
                orderProductService.dealOrderProductReturnStatus(CommonConst.PRODUCT_RETURN_REJECT, ofs.getOrderProductId());
            }
            afterSaleLog.setAfsType(OrdersAfsConst.AFS_TYPE_RETURN);
            update = orderReturnMapper.updateByPrimaryKeySelective(updateReturn);
            AssertUtil.isTrue(update == 0, "更新退货信息失败");

            afsAmount = orderReturnPODb.getReturnMoneyAmount().add(orderReturnPODb.getReturnExpressAmount());
            afsNun = orderReturnPODb.getReturnNum();

        } else if (ofs.getAfsType() == OrdersAfsConst.AFS_TYPE_REPLACEMENT) {
            // 换货
            OrderReplacementPO orderReplacementPODb = this.getOrderReplacementByAfsSn(ofs.getAfsSn());
            AssertUtil.isTrue(!orderReplacementPODb.getState().equals(OrdersAfsConst.REPLACEMENT_STATE_MEMBER_DELIVERY),
                    "未到商家收货状态");

            // 更新换货状态
            OrderReplacementPO updateReplacement = new OrderReplacementPO();
            updateReplacement.setReplacementId(orderReplacementPODb.getReplacementId());
            if (isReceive) {
                // 商家收货
                updateReplacement.setState(OrdersAfsConst.REPLACEMENT_STATE_STORE_RECEIVE);
                afterSaleLog.setState(OrdersAfsConst.REPLACEMENT_STATE_STORE_RECEIVE + "");
                afterSaleLog.setContent("商家已收货，待发货");
            } else {
                AssertUtil.isTrue(StringUtils.isEmpty(remark), "请填写拒收原因");

                updateReplacement.setState(OrdersAfsConst.REPLACEMENT_STATE_STORE_REJECTION);
                afterSaleLog.setState(OrdersAfsConst.REPLACEMENT_STATE_STORE_REJECTION + "");
                afterSaleLog.setContent("商家拒收，拒收原因：" + remark);
            }
            afterSaleLog.setAfsType(OrdersAfsConst.AFS_TYPE_REPLACEMENT);
            update = orderReplacementMapper.updateByPrimaryKeySelective(updateReplacement);
            AssertUtil.isTrue(update == 0, "更新换货信息失败");

            afsNun = orderReplacementPODb.getReplacementNum();
        } else {
            throw new MallException("售后类型错误");
        }

        update = orderAfterSaleLogMapper.insert(afterSaleLog);
        AssertUtil.isTrue(update == 0, "记录售后日志信息失败");
        // 发送消息通知
        this.sendMsgAfterSale(ofs, afsNun, afsAmount);
    }

    // region ---------------平台确认退款
    // start-----------------------------------------------------------------------------------


    /**
     * 平台确认退款
     *
     * @param admin
     * @param afsSns
     * @param remark
     */
    @GlobalTransactional
    public String adminRefundOperation(Admin admin, String afsSns, String remark, String refuseReason, Boolean isPassed,
                                       String channel, BigDecimal refundPunishAmount) {
        return this.adminRefundOperation(admin, afsSns, remark, refuseReason, isPassed, channel, refundPunishAmount, Boolean.FALSE);
    }

    /**
     * 平台确认退款
     *
     * @param admin
     * @param afsSns
     * @param remark
     */
    @GlobalTransactional
    public String adminRefundOperation(Admin admin, String afsSns, String remark, String refuseReason, Boolean isPassed,
                                       String channel, BigDecimal refundPunishAmount, boolean sftPresellRefund) {
        log.info("发起【adminRefundOperation】afsSns：{} xid:{}", afsSns, RootContext.getXID());

        String[] afsSnArr = afsSns.split(",");

        UserDTO userDTO = new UserDTO(admin);
        MDC.put("userDTO", JSONObject.toJSONString(userDTO));

        for (String afsSn : afsSnArr) {
            OrderAfterPO orderAfterPO = this.getOrderAfterServiceByAfsSn(afsSn);
            BizAssertUtil.notNull(orderAfterPO, "退款订单号：" + afsSn + " 不存在");

            // 退款订单号黑名单校验：名单内的订单不允许退款
            if (CollectionUtils.isNotEmpty(adminRefundBlackList.getOrderList())) {
                String orderSn = orderAfterPO.getOrderSn();
                if (adminRefundBlackList.getOrderList().contains(orderSn)) {
                    throw new BusinessException("退款申请单：" + afsSn + "为限制名单，暂不允许退款，请联系管理员");
                }
            }
        }

        boolean returnFlag;
        int nowTime = Integer.parseInt(DateUtil.getNowHHmm().replaceAll(":", ""));
        //23:00到1:00处于日终，贷款类不准退款
        if (end < start) {
            returnFlag = nowTime >= start || nowTime <= end;
        } else {
            returnFlag = nowTime >= start && nowTime <= end;
        }

        StringBuilder refundTypechangedAfsSn = new StringBuilder();

        for (String afsSn : afsSnArr) {
            // 获取售后服务信息
            OrderAfterPO orderAfterServicePODb = this.getOrderAfterServiceByAfsSn(afsSn);
            // 获取退货信息
            OrderReturnPO orderReturnPODb = orderReturnService.getByAfsSn(afsSn);

            // 平台审批通过信息校验
            orderReturnValidation.adminConfirmRefundValidate(orderReturnPODb, isPassed);

            if (OrderReturnStatus.isFinish(orderReturnPODb.getState())) {
                log.info("售后单{}已经审批完成,售后单状态{},继续遍历售后单", orderReturnPODb.getAfsSn(), orderReturnPODb.getState());
                continue;
            }

            if (OrdersAfsConst.RETURN_TYPE_3 == orderReturnPODb.getReturnType()) {
                //换货退款单校验
                this.exchangeValidate(orderReturnPODb, isPassed);
            }

            if (returnFlag && PayMethodEnum.isLoanPay(PayMethodEnum.getByPaymentCode(orderReturnPODb.getPaymentMethod()))) {
                StringBuilder startTime = new StringBuilder(start.toString());
                StringBuilder endTime = new StringBuilder(end.toString());
                throw new BusinessException("当前时间段" + startTime.insert(startTime.length() - 2, ":") +
                        "-" + endTime.insert(endTime.length() - 2, ":") + "不允许退款！");
            }

            RefundType refundType = RefundType.value(orderReturnPODb.getRefundType());
            //换货退款单、商家线下退款单，不校验refundType是否发生变化
            if (!RefundType.SQUARE_OFFLINE_REFUND.getValue().equals(orderReturnPODb.getRefundType())
                    && OrdersAfsConst.RETURN_TYPE_3 != orderReturnPODb.getReturnType()) {
                // 校验退款状态是否发生变化,实时获取退款类型 退款类型发生变化 ,恢复额度--->代还退款.
                refundType = orderAfterServiceModel.getRefundType(orderReturnPODb.getOrderSn());
            }

            log.info("adminRefundOperation afsSn = {}, refundType = {}", orderReturnPODb.getAfsSn(), refundType);

            /**
             * 预售，策略模式，Andy
             */
            OrderPO orderPODb = orderService.getByOrderSn(orderReturnPODb.getOrderSn());
            // 校验是否存在开票中的发票
            BizAssertUtil.isTrue(!orderInvoiceService.checkPlatformAudit(orderPODb),"订单"+orderPODb.getOrderSn() +"存在开票中的发票，请等待发票开票完成后进行审批");
            log.info("平台审核退款申请时退款状态AfsSn：{} -> orderReturnState-> {} orderType-> {}", orderReturnPODb.getAfsSn(), orderReturnPODb.getState(), orderPODb.getOrderType());
            if (Boolean.FALSE.equals(sftPresellRefund)) {
                BizAssertUtil.isTrue(OrderTypeEnum.isPresell(orderPODb.getOrderType()) && !orderPODb.getNewOrder(), "系统升级中，预付订单暂时不支持非云直通渠道退款，恢复时间待定");
            }
            // 退款类型发生变更，更新处理，对于恢复额度变为代还退款，对应订单下的所有退款单都变为代还退款
            if (refundType != RefundType.value(orderReturnPODb.getRefundType())
                    && RefundType.ZERO_YUAN_REFUND != RefundType.value(orderReturnPODb.getRefundType())
                    && RefundType.OFFLINE_REFUND != RefundType.value(orderReturnPODb.getRefundType())) {
                // 由恢复额度变为代还后，处理批次所有退款单
                if (RefundType.value(orderReturnPODb.getRefundType()) == RefundType.RESTORE_LIMIT) {
                    OrderReturnExample orderReturnExample = new OrderReturnExample();
                    orderReturnExample.setOrderSn(orderReturnPODb.getOrderSn());
                    List<OrderReturnPO> orderReturnPOList = orderReturnModel.getOrderReturnList(orderReturnExample, null);
                    for (OrderReturnPO orderReturn : orderReturnPOList) {
                        updateRefundType(orderReturn.getReturnId(), refundType, remark);
                        // 变为代还退款，需要计算扣罚金额，实际退款金额需要减去扣罚金额 并更新
                        OrderReturnPO orderReturnUpdated = orderReturnService.getByAfsSn(orderReturn.getAfsSn());
                        if (orderReturnValidation.refundPunishAmountSupport(orderReturnUpdated)) {
                            orderAfterServiceModel.recalculateRefundAmount(orderPODb, orderReturnUpdated);
                        }
                    }
                } else {
                    updateRefundType(orderReturnPODb.getReturnId(), refundType, remark);
                }
                return "当前订单[已起息],退款类型发生变更";
            }


            OrderProductPO orderProductPO = orderProductService.selectOneByOrderProductId(orderAfterServicePODb.getOrderProductId());

            //供应商履约订单需要供应商确认退款之后才能确认退款
            //平台审批状态和云中鹤状态不一致时，给予提示
            if (OrderPerformanceModeEnum.PERFORMANCE_MODE_SUPPLIER.getValue().equals(orderAfterServicePODb.getPerformanceMode())) {
                if (orderProductPO != null && supplierCodeYzh.equals(orderProductPO.getSupplierCode())) {
                    Integer isReturnMoney = orderReturnService.getYzhIsRefunded(orderReturnPODb.getOrderSn(), orderReturnPODb.getAfsSn(),
                            orderProductPO.getProductId().toString());

                    BizAssertUtil.notNull(isReturnMoney, "受外部系统限制，系统无法受理");

                    if (ChannelConstants.CHANNEL_WAIT_GOODS.equals(isReturnMoney)) {
                        String desc = isPassed ? "同意异常" : "拒绝异常";
                        return desc + "，原因：渠道订单状态-审核中，系统无法受理";
                    }
                    if (ChannelConstants.FAILURE.equals(isReturnMoney) && isPassed) {
                        return "同意异常，原因：渠道订单状态-已拒绝，系统无法受理";
                    }
                    if (ChannelConstants.SUCCESSFUL.equals(isReturnMoney) && !isPassed) {
                        return "拒绝异常，原因：渠道订单状态-已退款，系统无法受理";
                    }
                }
            }
            LoanRefundStrategy loanRefundStrategy = loanRefundStrategyContext.getStrategy(orderPODb.getOrderType());

            /**
             * 退款类型变更校验，Andy.2022.05-27.组合退款check退款类型，预付尾款是否参与贷款支付
             */
            if (refundType == RefundType.COMBINATION_REFUND) {
                //预付尾款是否参与贷款支付，是否在本次还款
                OrderRefundRecordPO orderRefundRecordPO = orderRefundRecordService.queryLoanRefundByAfsSn(afsSn);
                if (ObjectUtils.isNotEmpty(orderRefundRecordPO)) {
                    if (!RefundType.SQUARE_OFFLINE_REFUND.getValue().equals(orderRefundRecordPO.getRefundType())) {
                        orderPODb.setPaymentCode(orderRefundRecordPO.getPaymentCode());
                        refundType = orderAfterServiceModel.getRefundType(orderPODb);//计算尾款退款方式
                        if (refundType != RefundType.value(orderRefundRecordPO.getRefundType())) {
                            orderRefundRecordService.updateRefundTypeById(orderRefundRecordPO.getId(), refundType.getValue());
                            // 计算扣罚金额，实际退款金额需要减去扣罚金额 并更新
                            OrderReturnPO orderReturnUpdated = orderReturnService.getByAfsSn(orderReturnPODb.getAfsSn());
                            if (orderReturnValidation.refundPunishAmountSupport(orderReturnUpdated)) {
                                orderAfterServiceModel.recalculateRefundAmount(orderPODb, orderReturnUpdated);
                            }
                            return "当前订单[已起息],组合支付退款类型发生变更";
                        }
                    }
                }
            }

            if (isPassed) {
                // 先重置退款扣罚金额
                boolean resetRefundPunishAmountResult = orderReturnService.resetRefundPunishAmount(orderReturnPODb, refundPunishAmount);
                BizAssertUtil.isTrue(!resetRefundPunishAmountResult,
                        String.format("退款单%s重置退款扣罚金额失败，原扣罚金额：%f，修正的退款金额：%f", orderReturnPODb.getAfsSn(),
                                orderReturnPODb.getRefundPunishAmount(), refundPunishAmount));
            } else {
                BizAssertUtil.isTrue(OrdersAfsConst.JD_INTERCEPT_STATE_INTERCEPTING == orderReturnPODb.getJdInterceptStatus()
                                || OrdersAfsConst.JD_INTERCEPT_STATE_INTERCEPTED_SUCCESS == orderReturnPODb.getJdInterceptStatus()
                        , "该售后单已对接第三方快递拦截,不可审批拒绝");
            }

            // 平台同意退款，把实际退款金额、客户承担、其他赔偿重新计算一下。
            //Andy.增加组合支付退款，尾款退款方式=代还退款。 重新试算
            if (isPassed && RefundType.ASSIST_PAYMENT.equals(refundType)
                    && (ObjectUtils.isEmpty(orderReturnPODb.getRefundEndTime())
                    || !OrderReturnStatus.REFUND_SUCCESS.getValue().equals(orderReturnPODb.getState()))) {
                if (ObjectUtils.isNotEmpty(loanRefundStrategy)) {
                    log.info("发起【tryCaculateChanged】afsSn：{} xid:{} 开启策略模式", orderReturnPODb.getAfsSn(), RootContext.getXID());
                    String resultS = loanRefundStrategy.tryCaculateChanged(afsSn);
                    if (!OrderConst.RESULT_CODE_SUCCESS.equals(resultS)) {
                        return resultS;
                    }
                } else {
                    String resultS = this.tryCaculateChanged(afsSn);
                    if (!OrderConst.RESULT_CODE_SUCCESS.equals(resultS)) {
                        return resultS;
                    }
                }
                this.remainPlanDiscountAmount(orderReturnPODb, orderPODb);
            }

            // 退款类型未变更 && 代还-> 直接退款 退款类型未变更 && 恢复额度-> 该订单下所有退款单都要退款 Andy.预售
            if (refundType == RefundType.value(orderReturnPODb.getRefundType())
                    || RefundType.isCombinationRefund(orderReturnPODb.getRefundType())
                    || RefundType.ZERO_YUAN_REFUND == RefundType.value(orderReturnPODb.getRefundType())
                    || RefundType.OFFLINE_REFUND == RefundType.value(orderReturnPODb.getRefundType())
            ) {
                if (RefundType.value(orderReturnPODb.getRefundType()) == RefundType.RESTORE_LIMIT
                        || (RefundType.isCombinationRefund(orderReturnPODb.getRefundType()) && refundType == RefundType.RESTORE_LIMIT)) {
                    OrderAfterServiceExample orderAfterServiceExample = new OrderAfterServiceExample();
                    orderAfterServiceExample.setOrderSn(orderAfterServicePODb.getOrderSn());
                    List<OrderAfterPO> orderAfterServicePOList =
                            orderAfterServiceModel.getOrderAfterServiceList(orderAfterServiceExample, null);
                    for (OrderAfterPO orderAfterServicePO : orderAfterServicePOList) {
                        OrderReturnPO restoreLimitOrderReturnPO = this.getOrderReturnByAfsSn(orderAfterServicePO.getAfsSn());
                        //  非售后中的，不做处理
                        if (!OrderReturnStatus.duringRefund(restoreLimitOrderReturnPO.getState())) {
                            continue;
                        }
                        this.saveRefundRecord(admin, remark, refuseReason, afsSn, orderAfterServicePO, restoreLimitOrderReturnPO, isPassed, channel);
                    }
                } else {
                    this.saveRefundRecord(admin, remark, refuseReason, afsSn, orderAfterServicePODb, orderReturnPODb,
                            isPassed, channel);
                }
                if (isPassed) {
                    this.refundLimit(refundType);//退款日中限制，Andy
                    log.info("发起【doReturnMoney】afsSn：{} xid:{}", orderReturnPODb.getAfsSn(), RootContext.getXID());
                    if (ObjectUtils.isNotEmpty(loanRefundStrategy)) {
                        log.info("发起【doReturnMoney】afsSn：{} xid:{} 开启策略模式", orderReturnPODb.getAfsSn(), RootContext.getXID());
                        loanRefundStrategy.doReturnMoney(orderReturnPODb, orderAfterServicePODb, admin, orderPODb);
                    } else {
                        orderReturnModel.doReturnMoney(orderReturnPODb, orderAfterServicePODb, admin);
                    }
                }
            } else {
                throw new MallException(String.format("平台审核处理异常，请待开发人员排查~，售后单号 %s", afsSn));
            }
        }
        MDC.remove("userDTO");
        return ObjectUtils.isNotEmpty(refundTypechangedAfsSn) ? "当前订单[已起息],退款类型发生变更" : OrderConst.RESULT_CODE_SUCCESS;
    }

    private void exchangeValidate(OrderReturnPO orderReturnPODb, boolean isPassed) {
        //换货退款订单不能终止退款
        BizAssertUtil.isTrue(!isPassed, "换货退款订单不允许终止退款");
        //换货单审批通过需要换货完成
        ExchangeOrderDetailDTO exchangeOrderDetailDTO = orderExchangeDetailService.getOrderExchangeDetailByAfsSn(orderReturnPODb.getAfsSn());
        AssertUtil.isTrue(exchangeOrderDetailDTO == null,
                String.format("该换货退款售后单%s没有关联的换货单到", orderReturnPODb.getAfsSn()));
        BizAssertUtil.isTrue(ExchangeOrderConst.EXCHANGE_ORDER_STATE_FINISH != exchangeOrderDetailDTO.getExchangeOrderState(),
                String.format("当前售后单对应的换货单状态为:%s,不允许审批退款,请先完成换货，再进行退款审批", ExchangeOrderStatusEnum.valueOf(exchangeOrderDetailDTO.getExchangeOrderState()).getDesc()));

    }

    @GlobalTransactional
    public int saveRefundRecord(Admin admin, String remark, String refuseReason, String afsSn,
                                OrderAfterPO orderAfterServicePODb, OrderReturnPO orderReturnPODb,
                                boolean isPassed, String channel) {
        //查询订单信息
        OrderPO orderPODb = orderModel.getOrderByOrderSn(orderAfterServicePODb.getOrderSn());
        if (isPassed) {
            // 微信退款、支付宝退款、银行卡转账退款，恢复额度（新增）；无需判断【三类账户不可用余额+待结算资金】外，其余均需判断
            this.thirdAccountBalanceCheck(orderPODb, orderReturnPODb, null);
        }

        //平台确认退款，更新售后信息表
        OrderAfterPO updateService = new OrderAfterPO();
        updateService.setAfsSn(orderAfterServicePODb.getAfsSn());
        Date now = new Date();
        updateService.setPlatformAuditTime(now);
        updateService.setPlatformRemark(remark);
        if (!isPassed) {
            updateService.setRefundTerminationTime(now);
        }
        log.info("###平台更新OrderAfterService操作：{}", JSON.toJSONString(updateService));
        int update = orderAfterMapper.updateByAfsSn(updateService);
        AssertUtil.isTrue(update == 0, "更新售后服务信息失败");

        //获取退款路径：原路退回or退款到余额
        int returnMoneyType = Integer.parseInt(stringRedisTemplate.opsForValue().get("refund_setting_switch"));

        LambdaUpdateWrapper<OrderReturnPO> updateWrapper = new LambdaUpdateWrapper<>();
        //更新退货表
        updateWrapper.set(OrderReturnPO::getReturnMoneyType, returnMoneyType);
        if (isPassed) {
            BigDecimal channelServiceFee;
            // 实际退款金额
            BigDecimal totalRefund = orderReturnPODb.getActualReturnMoneyAmount().add(orderReturnPODb.getReturnExpressAmount());
            //最后一单采用轧差方式计算退款手续费  订单总手续费-已退手续费; 换货退款退款类型不用轧差
            if (OrdersAfsConst.RETURN_TYPE_3 != orderReturnPODb.getReturnType()
                    && iOrderReturnService.isLastReturn(orderReturnPODb.getAfsSn())) {
                // 查出【平台审批完成】和【退款完成】的退订单
                LambdaQueryWrapper<OrderReturnPO> orderReturnListQuery = new LambdaQueryWrapper<>();
                orderReturnListQuery.eq(OrderReturnPO::getOrderSn, orderReturnPODb.getOrderSn());
                orderReturnListQuery.eq(OrderReturnPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
                orderReturnListQuery.ne(OrderReturnPO::getAfsSn, orderReturnPODb.getAfsSn());
                orderReturnListQuery.in(OrderReturnPO::getState, Arrays.asList(OrderReturnStatus.PLATFORM_AGREE.getValue(),
                        OrderReturnStatus.REFUND_SUCCESS.getValue()));

                List<OrderReturnPO> orderReturnPOS = orderReturnMapper.selectList(orderReturnListQuery);
                BigDecimal alRefundchannelServiceFee = orderReturnPOS.stream().map(OrderReturnPO::getChannelServiceFee).reduce(BigDecimal.ZERO, BigDecimal::add);
                channelServiceFee = orderPODb.getChannelServiceFee().subtract(alRefundchannelServiceFee);
                //    非最后一单退则采用退单金额*手续费率
            } else {
                if (OrderTypeEnum.isPresell(orderPODb.getOrderType())) {//Andy.预付
                    channelServiceFee = BigDecimal.ZERO;
                } else {
                    /**
                     * 计算退单渠道手续费
                     */
                    BillAccountTypeEnum accountEnum;
                    if (null == orderPODb.getNewOrder() || !orderPODb.getNewOrder()) {
                        accountEnum = BillAccountTypeEnum.valueByPay(orderPODb.getPaymentCode());
                    } else {
                        accountEnum = BillAccountTypeEnum.payment2AccountType(orderPODb.getPaymentCode());
                    }
                    String rate = channelFeeRateConfig.getMappedRate().get(accountEnum.getValue() + "-" + orderPODb.getPaymentCode());
                    BigDecimal thousandth = new BigDecimal(rate);
                    channelServiceFee = totalRefund.multiply(thousandth).setScale(2, RoundingMode.HALF_UP);
                }
            }
            // 当实际退款金额是0的时候，渠道手续费也退0
            if (totalRefund.compareTo(BigDecimal.ZERO) == 0) {
                channelServiceFee = BigDecimal.ZERO;
            }
            updateWrapper.set(OrderReturnPO::getChannelServiceFee, channelServiceFee);
            updateWrapper.set(OrderReturnPO::getState, OrdersAfsConst.RETURN_STATE_300);
        } else {
            /**
             * 预售，策略模式，Andy
             */
            LoanRefundStrategy loanRefundStrategy = loanRefundStrategyContext.getStrategy(orderPODb.getOrderType());
            if (ObjectUtils.isNotEmpty(loanRefundStrategy)) {
                log.info("admin预售退款审批拒绝，策略模式开启，afsSn:{}", orderReturnPODb.getAfsSn());
                loanRefundStrategy.refundRefuse(orderReturnPODb.getAfsSn());
            }

            updateWrapper.set(OrderReturnPO::getState, OrdersAfsConst.RETURN_STATE_301);
            updateWrapper.set(OrderReturnPO::getRefuseReason, refuseReason);
            // 恢复订单商品行已退数量
            orderProductService.deductReturnNumber(orderAfterServicePODb.getOrderProductId(),
                    orderAfterServicePODb.getAfsNum());
            // 维护商品行的退款状态
            orderProductService.dealOrderProductReturnStatus(CommonConst.PRODUCT_RETURN_REJECT,
                    orderAfterServicePODb.getOrderProductId());

            // 发送平台拒绝的消息
            orderCreateHelper.addOrderReturnEvent(orderPODb, orderPODb.getXzCardAmount(), orderReturnPODb,
                    OrderEventEnum.PLATFORM_REFUSED, new Date(), null);
        }

        // 更新当前退款单（bz_order_return）
        AssertUtil.isTrue(ObjectUtils.isEmpty(orderReturnPODb.getReturnId()), "returnId不能为空");
        updateWrapper.eq(OrderReturnPO::getReturnId, orderReturnPODb.getReturnId());
        if (RefundType.RESTORE_LIMIT.getValue().equals(orderReturnPODb.getRefundType())) {
            updateWrapper.in(OrderReturnPO::getState, OrderReturnStatus.duringRefundStatus());
        } else {
            updateWrapper.in(OrderReturnPO::getState, Arrays.asList(OrderReturnStatus.STORE_AGREE_REFUND.getValue(),
                    OrderReturnStatus.STORE_RECEIVED.getValue(), OrderReturnStatus.STORE_AGREE_RETURN.getValue()));
        }
        log.info("###平台更新orderReturn操作：updateWrapper:{}", JSON.toJSONString(updateWrapper));

        int updateResult = orderReturnMapper.update(null, updateWrapper);
        log.info(">>>>>>>>>>>>>>>>>>>>>修改退款单的数量为：{}", updateResult);
        AssertUtil.isTrue(updateResult == 0, "更新退货信息失败");

        // 修改订单锁定状态
        boolean lockResult = orderInfoService.dealOrderLock(orderPODb.getOrderSn(), -updateResult);
        AssertUtil.isTrue(!lockResult, "修改订单锁定状态失败");

        // 记录退款轨迹数据（平台审核）
        OrderReturnTrackPO orderReturnTrackPO = OrderReturnTrackPO.builder().afsSn(orderAfterServicePODb.getAfsSn())
                .operateType(OrderReturnOperateTypeEnum.PLATFORM_AUDIT.getValue())
                .operator(admin.getAdminName() + "-" + admin.getPhone()).operateTime(new Date())
                .operateResult(isPassed ? AuditResultEnum.AUDIT_PASS.getValue() : AuditResultEnum.AUDIT_REFUSE.getValue())
                .operateRemark(remark).channel(channel).build();
        orderReturnTrackMapper.insert(orderReturnTrackPO);

        // 处理最后一个售后单的标识
        this.handleAfsLastRefundInfo(orderAfterServicePODb);

        if (orderReturnService.isAllReturnedFinish(orderAfterServicePODb.getOrderSn()) && isPassed) {

            OrderPO order = orderModel.getOrderByOrderSnLambda(orderAfterServicePODb.getOrderSn());
            int orderState = OrderConst.ORDER_STATE_50;
            String content = String.format("平台审核通过，订单全退，订单关闭，触发关闭的退款单号：【%s】", orderAfterServicePODb.getAfsSn());

            if (ExchangeOrderConst.ORDER_EXCHANGE_FLAG_1 == order.getExchangeFlag()) {
                orderState = OrderConst.ORDER_STATE_40;
            }
            boolean updates = orderPlacingService.updateOrderStatus(orderAfterServicePODb.getOrderSn(), orderState);
            AssertUtil.isTrue(!updates, "更新订单状态失败,请重试");
            log.info("saveRefundRecord==={}", orderReturnPODb.getReturnType());

            if (ExchangeOrderConst.ORDER_EXCHANGE_FLAG_1 == order.getExchangeFlag()) {
                orderModel.orderFinishSuccess(order, OrderConst.LOG_ROLE_ADMIN, Long.valueOf(admin.getAdminId()), admin.getAdminName()
                        , "包含换货的订单全部售后完成", OrderCreateChannel.getEnumByValue(channel), Boolean.FALSE);
            } else {
                // 记录订单关闭日志
                orderLogModel.insertOrderLog(OrderConst.LOG_ROLE_ADMIN, Long.valueOf(admin.getAdminId()), admin.getAdminName(), order.getOrderSn(),
                        order.getOrderState(), orderState, order.getLoanPayState(),
                        content,
                        OrderCreateChannel.getEnumByValue(channel));

                // 推送订单关闭消息
                orderCreateHelper.addOrderChangeEvent(order, OrderEventEnum.CLOSE, now);
            }

            // 交易关闭：更新对账明细
            /**
             * 订单状态可能变更场景： 1.非交易成功-交易关闭（全部退款） 2.交易成功-交易关闭（多次部分退货退款） 查询订单完成时间，有完成时间则订单已交易成功，不更新时间， 没有则更新时间为交易关闭时间
             *
             * 需保证交易关闭必须在【交易成功】【放款成功】之前
             */
            OrderPO orderPO = orderMapper.getOrderAmount(orderAfterServicePODb.getOrderSn());
            if (orderPO.getFinishTime() == null) {
                orderPO.setFinishTime(now);
            }
        }

        // 记录售后日志
        OrderAfterSaleLogPO afterSaleLog = new OrderAfterSaleLogPO();
        afterSaleLog.setLogRole(OrderConst.LOG_ROLE_ADMIN);
        afterSaleLog.setLogUserId(Long.parseLong(String.valueOf(admin.getAdminId())));
        afterSaleLog.setLogUserName(admin.getAdminName());
        afterSaleLog.setAfsSn(orderAfterServicePODb.getAfsSn());
        afterSaleLog.setAfsType(orderAfterServicePODb.getAfsType());
        if (isPassed) {
            afterSaleLog.setState(OrdersAfsConst.RETURN_STATE_300 + "");
            afterSaleLog.setContent("平台确认退款");
        } else {
            afterSaleLog.setState(OrdersAfsConst.RETURN_STATE_301 + "");
            afterSaleLog.setContent("平台拒绝退款");
        }
        afterSaleLog.setCreateTime(new Date());
        update = orderAfterSaleLogMapper.insert(afterSaleLog);
        AssertUtil.notNull(update == 0, "记录售后服务操作日志失败");

        if (isPassed) {
            Member memberDb = memberFeignClient.getMemberByMemberId(orderAfterServicePODb.getMemberId());
            AssertUtil.notNull(memberDb, "获取会员信息为空，请重试");
            // 释放积分、库存、优惠劵资源（退款失败次数 > 0，说明已释放过资源，不再释放）
            if (orderReturnPODb.getRefundFailTimes() == 0) {
                this.resourceRelease(orderReturnPODb, orderAfterServicePODb, orderPODb);
            }

            //退款佣金处理
            BizAssertUtil.isTrue(!this.handleRefundCommission(orderPODb, orderAfterServicePODb, orderReturnPODb),
                    "退款校验不通过,原因:退单佣金计算失败");
        }

        return returnMoneyType;
    }

    /**
     * 处理售后单商商品行最后一单和订单最后一单的标识
     *
     * @param orderAfterPO
     * @return
     */
    private boolean handleAfsLastRefundInfo(OrderAfterPO orderAfterPO) {
        LambdaUpdateWrapper<OrderAfterPO> orderAfterUpdateWrapper = Wrappers.lambdaUpdate();
        orderAfterUpdateWrapper.eq(OrderAfterPO::getAfsSn, orderAfterPO.getAfsSn());

        boolean updateMark = false;

        // 全退，且不存在最后一单的标识的退款单，则标记为全退
        if (orderReturnService.isAllReturnedFinish(orderAfterPO.getOrderSn())
                && !isContainValidLastRefundAfs(orderAfterPO.getOrderSn())) {
            orderAfterUpdateWrapper.set(OrderAfterPO::getOrderLastRefund, OrderConst.IS_ALL_RETURN_YES);
            updateMark = true;
        }

        if (orderReturnService.isOrderProductAllReturn(orderAfterPO.getOrderProductId())) {
            orderAfterUpdateWrapper.set(OrderAfterPO::getProductLastRefund, OrderConst.IS_ALL_RETURN_YES);
            updateMark = true;
        }

        if (!updateMark) {
            return false;
        }

        return orderAfterService.update(orderAfterUpdateWrapper);
    }

    /**
     * 是否存在有效的退款单标识为最后一单
     *
     * @param orderSn 订单号
     * @return true/false
     */
    public boolean isContainValidLastRefundAfs(String orderSn) {
        LambdaQueryWrapper<OrderAfterPO> orderAfterLastQueryWrapper = Wrappers.lambdaQuery();
        orderAfterLastQueryWrapper.eq(OrderAfterPO::getOrderSn, orderSn);
        orderAfterLastQueryWrapper.in(OrderAfterPO::getOrderLastRefund, OrderConst.IS_ALL_RETURN_YES);
        List<String> afsSns = orderAfterService.list(orderAfterLastQueryWrapper).stream().map(OrderAfterPO::getAfsSn)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(afsSns)) {
            return false;
        }
        LambdaQueryWrapper<OrderReturnPO> orderReturnLastQueryWrapper = Wrappers.lambdaQuery();
        orderReturnLastQueryWrapper.in(OrderReturnPO::getState,
                Arrays.asList(OrderReturnStatus.PLATFORM_AGREE.getValue(), OrderReturnStatus.REFUND_SUCCESS.getValue()));
        orderReturnLastQueryWrapper.in(OrderReturnPO::getAfsSn, afsSns);
        return orderReturnService.count(orderReturnLastQueryWrapper) > NumberUtils.INTEGER_ZERO;
    }

    /**
     * 计算退款单佣金
     *
     * @param orderAfterPO  退款单信息
     * @param orderReturnPO 退款金额信息
     * @return 处理结果
     */
    private boolean handleRefundCommission(OrderPO orderPO, OrderAfterPO orderAfterPO, OrderReturnPO orderReturnPO) {

        // 校验是否需要计算退佣金
        if (!orderReturnValidation.needCalculateReturnOrderCommission(orderPO)) {
            return Boolean.TRUE;
        }

        // 校验正向佣金是否正确
        if (!orderReturnValidation.checkOrderCommissionAmount(orderPO)) {
            return Boolean.FALSE;
        }

        // 查询最新售后单信息
        OrderAfterPO freshOrderAfterPO = orderAfterService.getByAfsSn(orderAfterPO.getAfsSn());

        // 由分销系统决定退佣金的钱
        // 查询对应商品信息
        OrderProductPO orderProductPO = orderProductService.selectOneByOrderProductId(freshOrderAfterPO.getOrderProductId());
        // 退款单佣金从分销服务同步获取
        DbcTrialRefundCommissionDTO trialRefundCommissionDTO = new DbcTrialRefundCommissionDTO();
        trialRefundCommissionDTO.setStoreId(String.valueOf(freshOrderAfterPO.getStoreId()));
        trialRefundCommissionDTO.setOrderProductDelivery(freshOrderAfterPO.getProductDeliveryState());
        trialRefundCommissionDTO.setRefundOrderId(freshOrderAfterPO.getAfsSn());
        trialRefundCommissionDTO.setOrderId(freshOrderAfterPO.getOrderSn());
        trialRefundCommissionDTO.setCommodityCode(String.valueOf(orderProductPO.getProductId()));
        trialRefundCommissionDTO.setCommodityNum(freshOrderAfterPO.getAfsNum());
        trialRefundCommissionDTO.setOrderPattern(orderPO.getOrderPattern());
        trialRefundCommissionDTO.setBusinessChannel(CommonConst.DBC_MALL_ORDER_BUSINESS_CHANNEL);

        Result<DbcTrialRefundCommissionVO> trialRefundCommissionResult = dbcServiceFeign.trialCalcRefundCommission(trialRefundCommissionDTO);

        if (!trialRefundCommissionResult.isSuccess()) {
            throw new BusinessException(trialRefundCommissionResult.getMessage());
        }

        DbcTrialRefundCommissionVO trialRefundCommissionVO = trialRefundCommissionResult.getData();

        if (Objects.isNull(trialRefundCommissionVO)) {
            throw new MallException("售后信息处理异常，请联系开发人员确认~");
        }
        if (Objects.isNull(trialRefundCommissionVO.getRefundCommission())
                || Objects.isNull(trialRefundCommissionVO.getRefundCommissionServiceFee())) {
            throw new MallException("返回佣金信息异常，请联系开发人员确认~");
        }

        // 更新退款单上佣金、佣金服务费信息
        // 补充退佣金激励费、退佣金激励费服务费
        LambdaUpdateWrapper<OrderReturnPO> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(OrderReturnPO::getAfsSn, orderReturnPO.getAfsSn())
                .eq(OrderReturnPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                .set(OrderReturnPO::getOrderCommission, trialRefundCommissionVO.getRefundCommission())
                .set(OrderReturnPO::getCommissionServiceFee, trialRefundCommissionVO.getRefundCommissionServiceFee())
                .set(OrderReturnPO::getOrderCommissionIncentiveFee, trialRefundCommissionVO.getRefundCommissionIncentiveFee())
                .set(OrderReturnPO::getCommissionIncentiveServiceFee, trialRefundCommissionVO.getRefundCommissionIncentiveServiceFee());

        orderReturnService.update(updateWrapper);

        return Boolean.TRUE;
    }

    @GlobalTransactional
    public void doReturnMoney(OrderReturnPO orderReturnPODb, OrderAfterPO orderAfterServicePODb, Admin admin) {
        log.info("开始 doReturnMoney afsSn->{}", orderReturnPODb.getAfsSn());
        // 查询订单信息
        OrderPO orderPODb = orderModel.getOrderByOrderSn(orderAfterServicePODb.getOrderSn());
        // 查询最新的售后信息
        orderReturnPODb = orderReturnService.getByAfsSn(orderReturnPODb.getAfsSn());

        /**
         * 平台打款逻辑（原路退回）： 查询订单支付方式 1 货到付款，直接修改退货状态 2 余额支付，直接退款到余额，修改状态 3 三方支付： 3.1 未使用余额，直接退回到三方支付原账户 3.2
         * 使用了余额（组合支付），查询所有已经退款的退货单，退款总
         */

        // 实际退款金额 = 商品金额 + 退还运费 + 其它赔偿 - 客户承担 - 退款扣罚
        //  = 实际退款金额 + 退还运费
        BigDecimal returnMoneyAll = orderReturnPODb.getActualReturnMoneyAmount().add(orderReturnPODb.getReturnExpressAmount());

        PayMethodEnum payMethodEnum = PayMethodEnum.getValue(orderPODb.getPaymentCode());
        AssertUtil.notNull(payMethodEnum, "未识别的支付类型");

        if (returnMoneyAll.compareTo(BigDecimal.ZERO) > 0 &&
                (PayMethodEnum.isLoanPay(payMethodEnum) || PayMethodEnum.isThirdPartyPay(payMethodEnum)
                        || (orderPODb.getNewOrder() && PayMethodEnum.BANK_TRANSFER.getValue().equals(orderPODb.getPaymentCode()))
                        || PayMethodEnum.isCombinationPay(orderPODb.getPaymentCode())
                )
        ) {
            if (null != wxCombineXZCardOrderConfig.getOrderList()
                    && !wxCombineXZCardOrderConfig.getOrderList().contains(orderAfterServicePODb.getOrderSn())) {
                log.info("判断是否是云直通类，支持下账");
                // 判断是否是云直通类，支持下账
                if (orderReturnValidation.isSupportBookkeeping(orderPODb, orderReturnPODb)) {
                    log.info("校验电子账户余额、下账");
                    //校验电子账户余额
                    iPayHandleService.verifyExecute(orderPODb, orderReturnPODb, payMethodEnum.getValue());
                    iPayHandleService.syncExecute(orderPODb, orderReturnPODb, payMethodEnum.getValue());
                    updateOrderRefundAmount(orderReturnPODb, orderPODb);
                    return;
                }
            }

            // 代还、恢复额度、现金
            switch (orderReturnPODb.getRefundType()) {
                // 支付宝原路退回
                case 5:
                    // 微信原路退回
                case 6:
                    //1.已退大于等于余额支付-调用三方的原路退回
                    try {
                        iPayHandleService.paymentRefund(
                                orderPODb,
                                orderReturnPODb, null, payMethodEnum.getValue(), null);
                    } catch (MallException e) {
                        throw new BusinessException("原路退回失败，请联系管理员处理，原因：" + e.getSystemMessage());
                    } catch (Exception e) {
                        log.error("调用payment服务，发起退款异常: {}", e.getMessage());
                        throw new MallException("原路退回失败，请联系管理员处理" + e.getMessage());
                    }
                    break;
                // 用呗代还退款
                case 1:
                    // 用呗恢复额度
                case 3:
                    // 平台审核通过后，调用该方法
                    orderAfterServiceModel.refundApply2Payment(orderPODb,
                            orderReturnPODb,
                            orderReturnPODb.getActualReturnMoneyAmount()
                                    .add(orderReturnPODb.getReturnExpressAmount()), admin);
                    break;
                default:
                    throw new MallException("未识别的退款类型:" + orderReturnPODb.getRefundType(),
                            ErrorCodeEnum.U.CHECK_FAILURE.getCode());
            }

            // 修改orderAfterService退款完成时间
            updateOrderRefundAmount(orderReturnPODb, orderPODb);
        }

        if (orderPODb.getPaymentCode().equals(PayMethodEnum.CARD_VOUCHER.getValue())
                || orderPODb.getPaymentCode().equals(PayMethodEnum.BANK_PAY.getValue())
                || (!orderPODb.getNewOrder() && PayMethodEnum.BANK_TRANSFER.getValue().equals(orderPODb.getPaymentCode()))
                || orderPODb.getPaymentCode().equals(PayMethodEnum.CARD.getValue())
                || orderPODb.getPaymentCode().equals(PayMethodEnum.AGREED_PAY.getValue())
                || RefundType.ZERO_YUAN_REFUND == RefundType.value(orderReturnPODb.getRefundType())
                || RefundType.OFFLINE_REFUND == RefundType.value(orderReturnPODb.getRefundType())) {
            log.info("doReturnMoney afsSn = {},refundSuccess orderReturnPODb.getRefundType() = {}",
                    orderReturnPODb.getAfsSn(), orderReturnPODb.getRefundType());
            this.refundSuccess(null, orderAfterServicePODb.getAfsSn());
            //修改订单退款金额
            OrderPO updateOrderPOPO = new OrderPO();
            updateOrderPOPO.setOrderId(orderPODb.getOrderId());
            updateOrderPOPO.setRefundAmount(BigDecimal.ZERO);
            orderPlacingService.updateById(updateOrderPOPO);
        }
    }

    public void updateOrderRefundAmount(OrderReturnPO orderReturnPODb, OrderPO orderPODb) {
        //修改订单退款金额和锁定状态
        OrderPO updateOrderPOPO = new OrderPO();
        updateOrderPOPO.setOrderId(orderPODb.getOrderId());
        updateOrderPOPO.setRefundAmount(orderPODb.getRefundAmount().add(orderReturnPODb.getReturnMoneyAmount()));
        orderPlacingService.updateById(updateOrderPOPO);
    }

    /**
     * 三类账户不可用余额+待结算资金校验
     *
     * @param orderPO       订单信息
     * @param orderReturnPO 退款单信息
     */
    public void thirdAccountBalanceCheck(OrderPO orderPO, OrderReturnPO orderReturnPO, BigDecimal returnAmount) {
        // 平台发起的退款，不需要进行校验
        if (OrderConst.RETURN_BY_3 == orderReturnPO.getReturnBy()) {
            return;
        }
        // 判断退款类型是否符合（除了：微信退款、支付宝退款、银行卡转账、恢复额度（新增）退款无需判断【三类账户不可用余额+待结算资金】外，其余均需判断）
        if (!RefundType.isThirdRefundType(RefundType.value(orderReturnPO.getRefundType()))) {
            return;
        }
        // 后台配置的开关开启，才需要校验
        if (OrderConst.ENABLED_FLAG_N.equals(systemSettingObtainHelper.getThirdAccountCheckEnable())) {
            return;
        }

        // 校验是否是三类账户
        BillAccountTypeEnum accountEnum;
        if (null == orderPO.getNewOrder() || !orderPO.getNewOrder()) {
            accountEnum = BillAccountTypeEnum.valueByPay(orderPO.getPaymentCode());
        } else {
            accountEnum = BillAccountTypeEnum.payment2AccountType(orderPO.getPaymentCode());
        }
        if (BillAccountTypeEnum.LOAN_PAY != accountEnum) {
            return;
        }

        if (!OrderConst.STORE_TYPE_SELF_1.equals(orderPO.getStoreIsSelf())) {
            // 校验：入驻店铺三类账户的不可用余额 >= 退款金额
            AccountQuery accountQuery = new AccountQuery();
            accountQuery.setAccountType(AccountTypeEnum.ENJOY_PAY.getValue());
            accountQuery.setStoreId(orderPO.getStoreId().toString());
            AccountVO accountVO;
            try {
                accountVO = settlementBillFeignClient.accountDetail(accountQuery);
            } catch (Exception e) {
                throw new MallException("校验入驻店铺三类账户的不可用余额异常", ErrorCodeEnum.C.CALL_EXCEPTION.getCode(), e);
            }
            if (!OrderTypeEnum.isPresell(orderPO.getOrderType())) {
                returnAmount = orderReturnPO.getActualReturnMoneyAmount().add(orderReturnPO.getReturnExpressAmount())
                        .add(orderReturnPO.getXzCardAmount()).add(orderReturnPO.getPlatformVoucherAmount());
                log.info("三类账户不可用余额+待结算资金校验，非结算退款金额，returnAmount：{} isPresell:{}", returnAmount, OrderTypeEnum.isPresell(orderPO.getOrderType()));
            }
            log.info("三类账户不可用余额+待结算资金校验，returnAmount：{} isPresell:{}", returnAmount, OrderTypeEnum.isPresell(orderPO.getOrderType()));
            BizAssertUtil.isTrue(accountVO.getIncomeFrozen().compareTo(returnAmount) < 0,
                    "入驻店铺三类账户的不可用余额不足");
        }
    }

    /**
     * 退款成功，资源释放
     *
     * @param orderReturnPO       退款价格信息
     * @param orderAfterServicePO 退款服务信息
     * @param orderPO             订单信息
     */
    public void resourceRelease(OrderReturnPO orderReturnPO, OrderAfterPO orderAfterServicePO, OrderPO orderPO) {

        // 退还优惠券
        this.returnCoupon(orderReturnPO);

        //修改库存
        this.increaseStock(orderAfterServicePO, orderPO);

        //退还赠品库存
        this.returnGiftOrderProduct(orderPO, orderAfterServicePO);

        // 卡券订单退款作废优惠券
        this.couponCentreOrderRefund(orderReturnPO, orderPO);

    }

    /**
     * 卡券订单退款，作废优惠券
     */
    private boolean couponCentreOrderRefund(OrderReturnPO orderReturnPO, OrderPO orderPO) {
        if (!OrderPatternEnum.COUPON_CENTRE.getValue().equals(orderPO.getOrderPattern())) {
            return true;
        }
        // 店铺名单校验
        if (!storeIsolateWhiteListFeignClient.isStoreOnList(WhiteListEnum.CARD_ITEM_REFUND_WHITE_LIST, orderReturnPO.getStoreId())) {
            throw new BusinessException("非卡券订单退款店铺,请确认");
        }
        // 再次校验是否支持退
        CouponPkgRefundResultVo checkResult = mallCouponPkgIntegration.couponCentreOrderRefundable(orderReturnPO.getOrderSn());
        if (!checkResult.getResult()) {
            throw new BusinessException(checkResult.getMessage());
        }

        // 执行退款
        CouponPkgRefundResultVo couponPkgRefundResult = mallCouponPkgIntegration.couponCentreOrderRefund(orderReturnPO.getOrderSn());
        if (!couponPkgRefundResult.getResult()) {
            throw new BusinessException(checkResult.getMessage());
        }
        return true;
    }



    /**
     * 满赠订单赠品回退
     * 商品行 a
     * 赠品行 a1 关联a
     * 商品行 b
     * 商品行 c
     * 赠品行 c1 关联bc联合赠送
     * 其中bc任意商品行发生退款，则赠品行c1全部进行回退
     *
     * @param orderPODb
     * @param afterPO
     */
    private Boolean returnGiftOrderProduct(OrderPO orderPODb, OrderAfterPO afterPO) {
        log.info("满赠订单库存回退:{},:{}", JSON.toJSONString(orderPODb), JSON.toJSONString(afterPO));
        //满赠订单才做处理
        if (OrderTypeEnum.FULL_GIFT.getValue() != orderPODb.getOrderType()) {
            return Boolean.TRUE;
        }
        Long orderProductId = afterPO.getOrderProductId();
        OrderProductPO orderProductPO = orderProductService.lambdaQuery()
                .eq(OrderProductPO::getOrderProductId, orderProductId)
                .eq(OrderProductPO::getIsGift, OrderConst.IS_GIFT_NO)
                .one();
        AssertUtil.notNull(orderProductPO, "处理赠品回退时数据异常");
        //商品行为普通商品行，不存在赠品，则不处理
        if (orderProductPO.getGiftGroup() == OrderConst.IS_GIFT_NO) {
            return Boolean.TRUE;
        }
        //订单号+赠品分组+赠品标识找出本次退款商品行关联的赠品集合
        List<OrderProductPO> giftOrderProductPOS = orderProductService.lambdaQuery()
                .eq(OrderProductPO::getOrderSn, orderPODb.getOrderSn())
                .eq(OrderProductPO::getIsGift, OrderConst.IS_GIFT_YES)
                .eq(OrderProductPO::getGiftGroup, orderProductPO.getGiftGroup())
                .list();
        if (CollectionUtils.isEmpty(giftOrderProductPOS)) {
            return Boolean.TRUE;
        }
        List<String> giftReturnOrderProductId = new ArrayList<>();
        //处理赠品回退
        for (OrderProductPO giftOrderProductPO : giftOrderProductPOS) {
            //判断商品行是否全退（兜底处理），则不处理该赠品行回退
            if (giftOrderProductPO.getReturnNumber().equals(giftOrderProductPO.getProductNum())) {
                continue;
            }
            giftReturnOrderProductId.add(giftOrderProductPO.getOrderProductId().toString());
            // 增加订单商品行已退数量
            orderProductService.addReturnNumber(giftOrderProductPO.getOrderProductId(),
                    giftOrderProductPO.getProductNum());

            // 维护商品行的退款状态
            orderProductService.dealOrderProductReturnStatus(CommonConst.PRODUCT_RETURN_PLATFORM_PASS,
                    giftOrderProductPO.getOrderProductId());

            // 库存回退
            this.increaseGiftProductStock(orderPODb, giftOrderProductPO);
        }
        String giftReturnOrderProductIdUpdate = String.join(",", giftReturnOrderProductId);
        //将赠品回退行记录至退款单上
        orderAfterService.lambdaUpdate()
                .set(OrderAfterPO::getGiftReturnOrderProductId, giftReturnOrderProductIdUpdate)
                .eq(OrderAfterPO::getAfsId, afterPO.getAfsId())
                .update();

        return Boolean.TRUE;
    }

    private void increaseGiftProductStock(OrderPO orderPODb, OrderProductPO giftOrderProductPO) {
        Goods goodsDb = goodsFeignClient.getGoodsByGoodsId(giftOrderProductPO.getGoodsId());
        AssertUtil.notNull(goodsDb, "商品不存在");
        Product productDb = productFeignClient.getProductByProductId(giftOrderProductPO.getProductId());
        AssertUtil.notNull(productDb, "货品不存在");
        // 查订单扩展信息
        OrderExtendPO orderExtendPO = orderExtendService.lambdaQuery()
                .eq(OrderExtendPO::getOrderSn, giftOrderProductPO.getOrderSn())
                .eq(OrderExtendPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                .select(OrderExtendPO::getOrderSn, OrderExtendPO::getBranch, OrderExtendPO::getReceiveBranchCode,
                        OrderExtendPO::getWarehouseCode).one();

        String orderSnTmp = orderPODb.getOrderSn() + "+" + giftOrderProductPO.getOrderProductId();
        // 库存扣减
        Integer isCombination = orderPODb.getOrderType() == OrderTypeEnum.COMBINATION.getValue() ? 1 : 0;
        goodsStockService.goodsStock(orderSnTmp, "0", productDb.getProductId(),
                orderPODb.getAreaCode(), -giftOrderProductPO.getProductNum(), giftOrderProductPO.getFinanceRuleCode(),
                giftOrderProductPO.getBatchNo(), giftOrderProductPO.getPurchaseSubCode(),
                giftOrderProductPO.getChannelSkuId(), orderExtendPO.getBranch(), orderExtendPO.getReceiveBranchCode(),
                orderExtendPO.getWarehouseCode(), EventStockTypeEnum.REFUND_ORDER_IN_STOCK, BizTypeEnum.INCREASE_STOCK,
                OrderConst.LOG_USER_NAME, orderPODb, giftOrderProductPO.getChannelSkuUnit(), OrderConst.IS_GIFT_YES, isCombination);
    }

    /**
     * 优惠券退还：
     * 平台优惠劵：判断订单对应子单状态是否均为“已取消”或者“交易关闭”，是则退还
     * 店铺优惠劵：判断该订单状态是否为“已取消”或者“交易关闭”，是则退还
     *
     * @param orderReturnPO 退款单信息
     */
    private void returnCoupon(OrderReturnPO orderReturnPO) {
        //换货退款类型的售后单，不退还优惠券
        if (OrdersAfsConst.RETURN_TYPE_3 == orderReturnPO.getReturnType()) {
            return;
        }
        OrderPO orderPO = orderModel.getOrderByOrderSnLambda(orderReturnPO.getOrderSn());

        //换货后的订单售后不退还优惠券
        if (ExchangeOrderConst.ORDER_EXCHANGE_FLAG_2 == orderPO.getExchangeFlag()) {
            return;
        }
        // 店铺券处理
        this.handleReturnStoreCoupon(orderPO, orderReturnPO);
        // 平台券处理
        this.handleReturnPlatformCoupon(orderPO, orderReturnPO);
    }

    /**
     * 退店铺券处理
     *
     * @param orderPO 订单信息
     */
    private void handleReturnStoreCoupon(OrderPO orderPO, OrderReturnPO orderReturnPO) {
        // 该单退款完成，则退店铺券
        if (orderPO.getOrderState() == OrderConst.ORDER_STATE_0
                || orderPO.getOrderState() == OrderConst.ORDER_STATE_50) {
            if (CommonConfig.enableTcc()) {
                couponOrderManager.storeCouponReturnBatch(orderPO);
                return;
            }

            // 查询所有的店铺券
            LambdaQueryWrapper<OrderPromotionDetailPO> queryWrapper =
                    Wrappers.lambdaQuery(OrderPromotionDetailPO.class);
            queryWrapper.eq(OrderPromotionDetailPO::getOrderSn, orderPO.getOrderSn())
                    .eq(OrderPromotionDetailPO::getIsStore, OrderConst.IS_STORE_PROMOTION_YES)
                    .eq(OrderPromotionDetailPO::getPromotionType, PromotionConst.PROMOTION_TYPE_402)
                    .eq(OrderPromotionDetailPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
            List<OrderPromotionDetailPO> storePromotionCoupons = orderPromotionDetailMapper.selectList(queryWrapper);
            this.actuallyDoReturnCoupon(new HashSet<>(storePromotionCoupons), orderReturnPO);
        }
    }

    /**
     * 退平台券处理
     *
     * @param orderPO 订单信息
     */
    public void handleReturnPlatformCoupon(OrderPO orderPO, OrderReturnPO orderReturnPO) {
        LambdaQueryWrapper<OrderPO> orderQueryWrapper = Wrappers.lambdaQuery(OrderPO.class);
        orderQueryWrapper.eq(OrderPO::getParentSn, orderPO.getParentSn()).eq(OrderPO::getEnabledFlag,
                OrderConst.ENABLED_FLAG_Y);
        List<OrderPO> orderPOs = orderMapper.selectList(orderQueryWrapper);
        for (OrderPO order : orderPOs) {
            if (order.getOrderState() != OrderConst.ORDER_STATE_0
                    && order.getOrderState() != OrderConst.ORDER_STATE_50) {
                log.warn("退平台券处理，orderSn：{} orderState:{}", orderPO.getOrderSn(), orderPO.getOrderState());
                return;
            }
        }
        if (CommonConfig.enableTcc()) {
            couponOrderManager.platformCouponReturnBatch(orderPOs);
            return;
        }

        // 收集订单号
        Set<String> orderSns = orderPOs.stream().map(OrderPO::getOrderSn).collect(Collectors.toSet());

        // 查询该父订单号下的所有平台券
        LambdaQueryWrapper<OrderPromotionDetailPO> queryWrapper = Wrappers.lambdaQuery(OrderPromotionDetailPO.class);
        queryWrapper.in(OrderPromotionDetailPO::getOrderSn, orderSns)
                .eq(OrderPromotionDetailPO::getIsStore, OrderConst.IS_STORE_PROMOTION_NO)
                .eq(OrderPromotionDetailPO::getPromotionType, PromotionConst.PROMOTION_TYPE_402)
                .eq(OrderPromotionDetailPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
        List<OrderPromotionDetailPO> platformPromotionCoupons = orderPromotionDetailMapper.selectList(queryWrapper);

        // 收集平台券码，去重
        Set<OrderPromotionDetailPO> aloneOrderPromotion =
                new TreeSet<>(Comparator.comparing(OrderPromotionDetailPO::getPromotionId));
        aloneOrderPromotion.addAll(platformPromotionCoupons);

        this.actuallyDoReturnCoupon(aloneOrderPromotion, orderReturnPO);
    }

    private void actuallyDoReturnCoupon(Set<OrderPromotionDetailPO> orderPromotionDetails, OrderReturnPO orderReturnPO) {
        StringBuilder couponCodes = new StringBuilder();
        for (OrderPromotionDetailPO detailPO : orderPromotionDetails) {
            orderModel.doReturnCoupon(detailPO.getPromotionId(), detailPO.getOrderSn());
            couponCodes.append(",").append(detailPO.getPromotionId());
        }
        log.info("actuallyDoReturnCoupon, this orderReturnPo return couponCodes is : {}", couponCodes);
        if (Objects.nonNull(orderReturnPO) && !StringUtils.isEmpty(couponCodes.toString())) {
            // 更新退款单上退的优惠券
            LambdaUpdateWrapper<OrderReturnPO> orderReturnUpdater = Wrappers.lambdaUpdate();
            orderReturnUpdater.set(OrderReturnPO::getReturnVoucherCode, orderReturnPO.getReturnVoucherCode() + couponCodes)
                    .eq(OrderReturnPO::getAfsSn, orderReturnPO.getAfsSn());
            orderReturnService.update(orderReturnUpdater);
        }
    }

    /**
     * 退款成功
     *
     * @param afsSn
     * @return void
     * <AUTHOR>
     * @date 2021/7/14 10:09
     */
    @GlobalTransactional
    public void refundSuccess(RefundInfoExtraInfoVO refundInfoExtraInfoVO, String afsSn) {
        log.info("afsSn:{}，refundInfoExtraInfoVO:{} 退款成功开始处理订单结果记录", afsSn, refundInfoExtraInfoVO);
        OrderReturnPO orderReturnPODb = orderReturnService.getByAfsSn(afsSn);
        AssertUtil.isTrue(orderReturnPODb == null, "退款单不存在:" + afsSn);

        // 重复通知 直接返回
        if (orderReturnPODb.getState() == OrdersAfsConst.RETURN_STATE_400) {
            log.error("refundSuccess退款成功->refundInfoExtraInfoVO:{} afsSn->{}", refundInfoExtraInfoVO, afsSn);
            return;
        }
        log.info("afsSn:{}，refundInfoExtraInfoVO:{} 退款成功开始处理订单结果记录", afsSn, refundInfoExtraInfoVO);
        AssertUtil.isTrue(orderReturnPODb.getState() != OrdersAfsConst.RETURN_STATE_300, "未到平台审核完成状态");
        log.info("afsSn:{}，refundInfoExtraInfoVO:{} 退款成功平台审批结果300开始处理订单结果记录", afsSn, refundInfoExtraInfoVO);
        //Andy.2022.05.30预售增加
        if (ObjectUtils.isNotEmpty(refundInfoExtraInfoVO) && ObjectUtils.isNotEmpty(refundInfoExtraInfoVO.getRefundNo())) {
            log.info("afsSn:{}，refundInfoExtraInfoVO:{} 退款成功预付开始处理订单结果记录", afsSn, refundInfoExtraInfoVO);
            orderRefundRecordService.updateRefundSuccessByRefundNo(refundInfoExtraInfoVO.getRefundNo());
            OrderRefundRecordPO orderRefundRecordPO = orderRefundRecordService.lambdaQuery()
                    .eq(OrderRefundRecordPO::getRefundNo, refundInfoExtraInfoVO.getRefundNo())
                    .last("limit 1")
                    .one();
            //预付情况下，如果是恢复额度，则根据订单号更新贷款类老用户池
            if (orderRefundRecordPO.getRefundType().equals(RefundType.RESTORE_LIMIT.getValue())) {
                iBzOldUserPoolService.restoreFirstLoanOrder(orderRefundRecordPO.getOrderSn());
            }

            if (!orderRefundRecordService.verifyRefundOrderAllFinishByAfsSn(afsSn)) {
                log.warn("afsSn:{}，refundInfoExtraInfoVO:{} 预付未完成整单退款，暂不更新退款单结果。", afsSn, refundInfoExtraInfoVO);
                return;
            }
        }

        log.info("afsSn:{}，refundInfoExtraInfoVO:{} 退款单成功，开始处理订单结果流转400状态", afsSn, refundInfoExtraInfoVO);
        //普通订单情况下，如果是恢复额度，则根据订单号更新贷款类老用户池
        if (orderReturnPODb.getRefundType().equals(RefundType.RESTORE_LIMIT.getValue())) {
            iBzOldUserPoolService.restoreFirstLoanOrder(orderReturnPODb.getOrderSn());
        }

        /**
         * 更新退款单记录
         */
        Date now = new Date();
        LambdaUpdateWrapper<OrderReturnPO> orderReturnUpdate = new LambdaUpdateWrapper<>();
        orderReturnUpdate.set(OrderReturnPO::getState, OrdersAfsConst.RETURN_STATE_400);
        orderReturnUpdate.set(OrderReturnPO::getRefundEndTime, now);
        orderReturnUpdate.set(OrderReturnPO::getCompleteTime, now);
        orderReturnUpdate.eq(OrderReturnPO::getAfsSn, afsSn);
        orderReturnService.update(orderReturnUpdate);
        OrderPO orderSn = orderModel.getOrderByOrderSn(orderReturnPODb.getOrderSn());
        //退款成功，更新退款备注
        orderAfterServiceModel.updateRefundFailReasonByAfsSn(orderReturnPODb.getAfsSn(), "退款成功");

        // 退款成功，发送退款消息
        orderCreateHelper.addOrderReturnEvent(orderSn, orderReturnPODb.getXzCardAmount(), orderReturnPODb,
                OrderEventEnum.REFUND, now, null);
        OrderAfterPO orderAfterPO = orderAfterServiceModel.getOrderAfterByAfsSn(afsSn);
        // 维护商品行的退款状态
        orderProductService.dealOrderProductReturnStatus(CommonConst.PRODUCT_RETURN_PLATFORM_PASS,
                orderAfterPO.getOrderProductId());

        // 记录退款轨迹
        OrderReturnTrackPO orderReturnTrackPO =
                OrderReturnTrackPO.builder().afsSn(afsSn).operateType(OrderReturnOperateTypeEnum.RETURN_SUCCESS.getValue())
                        .operator("admin").operateTime(new Date()).operateResult(AuditResultEnum.AUDIT_PASS.getValue()).build();
        orderReturnTrackMapper.insert(orderReturnTrackPO);

        //记录售后日志
        OrderAfterSaleLogPO afterSaleLog = new OrderAfterSaleLogPO();
        afterSaleLog.setLogRole(OrderConst.LOG_ROLE_ADMIN);
        afterSaleLog.setLogUserId(0L);
        afterSaleLog.setLogUserName("系统");
        afterSaleLog.setAfsSn(afsSn);
        afterSaleLog.setCreateTime(new Date());
        afterSaleLog.setAfsType(orderReturnPODb.getReturnType());
        afterSaleLog.setState(OrdersAfsConst.RETURN_STATE_400 + "");
        afterSaleLog.setContent("退款成功");
        orderAfterSaleLogMapper.insert(afterSaleLog);

        // 冲正处理
        this.reversal(orderReturnPODb.getOrderSn(), afsSn);

    }

    /**
     * 更新退预贴息金额
     *
     * @param orderReturnPO 售后单信息
     * @param orderPO       订单信息
     */
    private void remainPlanDiscountAmount(OrderReturnPO orderReturnPO, OrderPO orderPO) {
        //非最后整单退款,则不退还预贴息金额
        if (!iOrderReturnService.isLastReturn(orderReturnPO.getAfsSn())) {
            return;
        }

        // 计算退还的预贴息金额
        BigDecimal returnPlanDiscountAmt = this.calculateReturnPlanDiscountAmt(orderReturnPO, orderPO);

        LambdaUpdateWrapper<OrderReturnPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(OrderReturnPO::getPlanDiscountAmount, returnPlanDiscountAmt);
        updateWrapper.eq(OrderReturnPO::getReturnId, orderReturnPO.getReturnId());
        AssertUtil.isTrue(!orderReturnService.update(updateWrapper),
                String.format("更新退回预贴息金额失败,afsSn:%s", orderReturnPO.getAfsSn()));

        orderReturnPO.setPlanDiscountAmount(returnPlanDiscountAmt);
    }

    /**
     * 计算可退的预贴息金额
     * <p>
     * 在整单退的逻辑上：
     * 是否退款预贴息是：  退款单申请时间 - 交易完成时间 > 7，成立则不退预贴息
     * 其它赔偿承担方逻辑是：退款单申请时间 - 放款时间时间 > 7，成立则由商家承担其它赔偿
     * 则以上两个条件都成立，则即不会退还剩余的预贴息金额，又需要商家承担其它赔偿。
     * 所以修改为，在以上两个条件都成了时，取min{剩余的预贴息金额, 商家承担其它赔偿}，在退还预贴息金额中返给商家
     *
     * @param orderPO       订单信息
     * @param orderReturnPO 售后单信息
     * @return 可退的预贴息金额
     */
    private BigDecimal calculateReturnPlanDiscountAmt(OrderReturnPO orderReturnPO, OrderPO orderPO) {
        // 计算剩余的预贴息金额
        BigDecimal planDiscountAmount = orderAfterServiceModel.queryTotalDiscountAmount(orderPO);
        AssertUtil.isTrue(BigDecimal.ZERO.compareTo(planDiscountAmount) > 0,
                String.format("查询累计已使用预贴息券额小于0,orderSn:%s", orderPO.getOrderSn()));
        BigDecimal remainAmt = orderPO.getPlanDiscountAmount().subtract(planDiscountAmount);
        AssertUtil.isTrue(BigDecimal.ZERO.compareTo(remainAmt) > 0,
                String.format("存在丢失正向订单贴息金额MQ消息的可能,orderSn:%s", orderPO.getOrderSn()));
        log.info("calculateReturnPlanDiscountAmt,orderSn:{},planDiscountAmount:{}, final result:{}",orderPO.getOrderSn(),planDiscountAmount,remainAmt);
        return remainAmt;

//        // 没有放款，剩余的预贴息全退
//        if (Objects.isNull(orderPO.getLendingSuccessTime())) {
//            return remainAmt;
//        }
//
//        // 原正向订单完成时间不为空,计算可退还的截止日期【放款成功后七天】
//        Date canReturnDate = DateUtil.addDays(orderPO.getLendingSuccessTime(), Calendar.DAY_OF_WEEK);
//
//        if (orderReturnPO.getApplyTime().before(canReturnDate)) {
//            return remainAmt;
//        }
//
//        // 退款申请时间大于可退还的截止日期，则不退还预贴息金额
//        if (InterestPayerEnum.STORE.getDesc().equals(orderReturnPO.getInterestPayer())) {
//            // 其他赔偿承担方为商户时，需要退还预贴息金额，计算退还预贴息金额 = min{其他赔偿金额，（预贴息总金额 - 已使用的金额）}
//            return remainAmt.min(orderReturnPO.getOtherCompensationAmount());
//        } else {
//            return BigDecimal.ZERO;
//        }

    }

    /**
     * 冲正处理
     *
     * @param orderSn 订单号
     * @param afsSn   退款单号
     */
    private void reversal(String orderSn, String afsSn) {
        OrderPO orderPO = orderModel.getOrderByOrderSnLambda(orderSn);
        OrderReturnPO orderReturnPO = this.getOrderReturnByOrderSn(afsSn);
        if (Objects.isNull(orderPO.getFinishTime())) {
            return;
        }
        if (DateUtil.getMonth(orderPO.getFinishTime()) != DateUtil.getMonth(orderReturnPO.getCompleteTime())
                || orderPO.getOrderState() == OrderConst.ORDER_STATE_40) {
            LambdaUpdateWrapper<OrderReturnPO> updateWrapper = Wrappers.lambdaUpdate(OrderReturnPO.class);
            updateWrapper.eq(OrderReturnPO::getAfsSn, afsSn).set(OrderReturnPO::getReversalDate,
                    orderPO.getFinishTime());
            orderReturnMapper.update(null, updateWrapper);
        }
    }

    /**
     * @param afsSn      退款单号
     * @param failReason 失败原因
     */
    @Transactional(rollbackFor = Exception.class)
    public void refundFail(String afsSn, String failReason) {

        // 类型为退货退款，更新退货表
        LambdaQueryWrapper<OrderReturnPO> queryWrapper = Wrappers.lambdaQuery(OrderReturnPO.class);
        queryWrapper.eq(OrderReturnPO::getAfsSn, afsSn);
        OrderReturnPO orderReturnPODb = orderReturnMapper.selectOne(queryWrapper);

        // 失败回调，信息校验
        orderReturnValidation.refundFailValidate(orderReturnPODb);

        RefundType refundType = orderAfterServiceModel.getRefundType(orderReturnPODb.getOrderSn());

        OrderReturnPO updateReturn = new OrderReturnPO();

        // 涉及恢复额度的改动，先使用此方式处理，后续全替换，避免出现新bug
        LambdaUpdateWrapper<OrderReturnPO> updateWrapper = Wrappers.lambdaUpdate(OrderReturnPO.class);
        // 退款失败，直接回退到待平台审核的状态
        if (orderReturnPODb.getReturnType() == OrdersAfsConst.RETURN_TYPE_1 || orderReturnPODb.getReturnType() == OrdersAfsConst.RETURN_TYPE_3) {
            updateReturn.setState(OrdersAfsConst.RETURN_STATE_200);
            updateWrapper.set(OrderReturnPO::getState, OrderReturnStatus.STORE_AGREE_REFUND.getValue());
        } else if (orderReturnPODb.getReturnType() == OrdersAfsConst.RETURN_TYPE_2) {
            updateReturn.setState(OrdersAfsConst.RETURN_STATE_203);
            updateWrapper.set(OrderReturnPO::getState, OrderReturnStatus.STORE_RECEIVED.getValue());
        }
        updateReturn.setRefundEndTime(new Date());
        updateReturn.setRefundFailTimes(orderReturnPODb.getRefundFailTimes() + 1);

        updateWrapper.set(OrderReturnPO::getRefundEndTime, new Date());
        updateWrapper.set(OrderReturnPO::getRefundFailTimes, orderReturnPODb.getRefundFailTimes() + 1);

        if (refundType == RefundType.RESTORE_LIMIT) {
            // 恢复额度，以订单维度更新
            updateWrapper.eq(OrderReturnPO::getOrderSn, orderReturnPODb.getOrderSn()).eq(OrderReturnPO::getState,
                    OrderReturnStatus.PLATFORM_AGREE.getValue());
            orderReturnService.update(updateWrapper);
        } else {
            // 其它以退款单号为维度更新
            updateReturn.setAfsSn(afsSn);
            orderReturnMapper.updateByAfsSn(updateReturn);
        }

        // 记录退款轨迹
        OrderReturnTrackPO orderReturnTrackPO =
                OrderReturnTrackPO.builder().afsSn(afsSn).operateType(OrderReturnOperateTypeEnum.RETURN_FAIL.getValue())
                        .operator("admin").operateTime(new Date()).operateResult(AuditResultEnum.AUDIT_PASS.getValue()).build();
        orderReturnTrackMapper.insert(orderReturnTrackPO);

        //退款失败原因记录
        OrderAfterPO updateAfterService = new OrderAfterPO();
        updateAfterService.setAfsSn(afsSn);
        if (StringUtils.isEmpty(failReason)) {
            log.warn("{},退款失败,失败原因为空", afsSn);
            failReason = "系统异常";
        }
        updateAfterService.setRefundFailReason(failReason);
        orderAfterMapper.updateByAfsSn(updateAfterService);

        // 记录退款失败的售后日志
        this.addAfterSaleLogAfterRefundFail(orderReturnPODb);
    }

    /**
     * 记录退款失败后的售后日志信息
     *
     * @param orderReturnPO 退款信息
     */
    private void addAfterSaleLogAfterRefundFail(OrderReturnPO orderReturnPO) {
        OrderAfterSaleLogPO afterSaleLog = new OrderAfterSaleLogPO();
        afterSaleLog.setLogRole(OrderConst.LOG_ROLE_ADMIN);
        afterSaleLog.setLogUserId(0L);
        afterSaleLog.setLogUserName(CommonConst.ADMIN_NAME);
        afterSaleLog.setAfsSn(orderReturnPO.getAfsSn());
        afterSaleLog.setCreateTime(new Date());
        afterSaleLog.setAfsType(orderReturnPO.getReturnType());
        afterSaleLog.setState(String.valueOf(OrdersAfsConst.RETURN_STATE_401));
        afterSaleLog.setContent(CommonConst.REFUND_FAIL_MSG);
        orderAfterSaleLogMapper.insert(afterSaleLog);
    }

    /**
     * 仅退款未收货-更新商品库存
     *
     * @param orderAfterPO 退款单信息
     * @param orderPO      订单信息
     */
    private void increaseStock(OrderAfterPO orderAfterPO, OrderPO orderPO) {
        Goods goodsDb = goodsFeignClient.getGoodsByGoodsId(orderAfterPO.getGoodsId());
        AssertUtil.notNull(goodsDb, "商品不存在");
        OrderProductPO orderProductPO = orderProductMapper.getByPrimaryKey(orderAfterPO.getOrderProductId());
        AssertUtil.notNull(orderProductPO, "订单货品不存在");
        Product productDb = productFeignClient.getProductByProductId(orderProductPO.getProductId());
        AssertUtil.notNull(productDb, "货品不存在");
        // 查订单扩展信息
        OrderExtendPO orderExtendPO = orderExtendService.lambdaQuery()
                .eq(OrderExtendPO::getOrderSn, orderProductPO.getOrderSn())
                .eq(OrderExtendPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                .select(OrderExtendPO::getOrderSn, OrderExtendPO::getBranch, OrderExtendPO::getReceiveBranchCode,
                        OrderExtendPO::getWarehouseCode).one();

        log.info("increaseStock orderAfterPO getAfsType = {}", orderAfterPO.getAfsType());
        //如果是换货商品，不退还库存
        if (OrdersAfsConst.AFS_TYPE_REPLACEMENT != orderAfterPO.getAfsType()) {
            // 库存扣减
            Integer isCombination = orderPO.getOrderType() == OrderTypeEnum.COMBINATION.getValue() ? 1 : 0;
            goodsStockService.goodsStock(orderPO.getOrderSn(), orderAfterPO.getAfsSn(), productDb.getProductId(),
                    orderPO.getAreaCode(), -orderAfterPO.getAfsNum(), orderProductPO.getFinanceRuleCode(),
                    orderProductPO.getBatchNo(), orderProductPO.getPurchaseSubCode(),
                    orderProductPO.getChannelSkuId(), orderExtendPO.getBranch(), orderExtendPO.getReceiveBranchCode(),
                    orderExtendPO.getWarehouseCode(), EventStockTypeEnum.REFUND_ORDER_IN_STOCK, BizTypeEnum.INCREASE_STOCK,
                    OrderConst.LOG_USER_NAME, orderPO, orderProductPO.getChannelSkuUnit(), null, isCombination);
        }

        if (CommonConfig.enableTcc() && (orderPO.getOrderType().equals(PromotionConst.PROMOTION_TYPE_102)
                || orderPO.getOrderType().equals(PromotionConst.PROMOTION_TYPE_103)
                || orderPO.getOrderType().equals(PromotionConst.PROMOTION_TYPE_104)
                || orderPO.getOrderType().equals(PromotionConst.PROMOTION_TYPE_105)
                || orderPO.getOrderType() == OrderTypeEnum.SPELL_GROUP.getValue()
                || orderPO.getOrderType() == com.slodon.bbc.core.constant.PromotionConst.PROMOTION_TYPE_106)) {
            OrderProductPO orderProduct = new OrderProductPO();
            orderProduct.setMemberId(orderPO.getMemberId());
            orderProduct.setProductId(productDb.getProductId());
            orderProduct.setGoodsId(productDb.getGoodsId());
            orderProduct.setOrderSn(orderPO.getOrderSn());
            orderProduct.setProductNum(orderAfterPO.getAfsNum());
            promotionManager.cancelActivityResource(orderPO.getOrderSn(), orderPO.getOrderType(), orderProduct);
        } else {
            // 秒杀订单
            if (orderPO.getOrderType().equals(PromotionConst.PROMOTION_TYPE_104)) {
                this.seckillGoodsStock(productDb, orderAfterPO, orderProductPO);
            }

            // 拼团活动退库存
            if (orderPO.getOrderType().equals(PromotionConst.PROMOTION_TYPE_102)) {
                this.returnSpellGoodsStock(productDb, orderPO.getOrderSn(), orderProductPO);
            }

            // 专享活动退库存
            if (orderPO.getOrderType().equals(com.slodon.bbc.core.constant.PromotionConst.PROMOTION_TYPE_106)) {
                exclusiveFeignClient.cancelExclusiveOrder(orderPO.getOrderSn());
            }
        }
    }

    /**
     * 换货单售后单扣减库存
     */
    private void reduceExchangeOrderStock(OrderAfterPO orderAfterPO, OrderPO orderPO, OrderExtendPO orderExtendPO) {
        if (OrdersAfsConst.AFS_TYPE_REPLACEMENT != orderAfterPO.getAfsType()) {
            return;
        }
        OrderExchangeDetailExample orderExchangeDetailExample = new OrderExchangeDetailExample();
        orderExchangeDetailExample.setOrderProductId(orderAfterPO.getOrderProductId());
        orderExchangeDetailExample.setAfsSn(orderAfterPO.getAfsSn());
        OrderExchangeDetailPO orderExchangeDetailPO = orderExchangeDetailService.getExchangeOrderDetailByExample(orderExchangeDetailExample);
        if (Objects.nonNull(orderExchangeDetailPO)) {

            OrderProductPO orderProductPO = orderProductModel.getOrderProductById(orderAfterPO.getOrderProductId());
            OrderReturnPO orderReturnPO = this.getOrderReturnByAfsSn(orderAfterPO.getAfsSn());
            Integer isCombination = orderPO.getOrderType().equals(OrderTypeEnum.COMBINATION.getValue()) ? 1 : 0;
            goodsStockService.goodsStock(orderAfterPO.getOrderSn(), orderExchangeDetailPO.getExchangeSn() + "_finish",
                    orderProductPO.getProductId(), orderPO.getAreaCode(), orderReturnPO.getReturnNum(),
                    orderPO.getFinanceRuleCode(), orderProductPO.getBatchNo(), orderProductPO.getPurchaseSubCode(), orderProductPO.getChannelSkuId(),
                    orderExtendPO.getBranch(), orderExtendPO.getReceiveBranchCode(), orderExtendPO.getWarehouseCode(),
                    EventStockTypeEnum.ORDER_EXCHANGE_FINISH_OUT_STOCK, BizTypeEnum.EXCHANGE_ORDER_REDUCE_STOCK,
                    OrderConst.LOG_USER_NAME, orderPO, orderProductPO.getChannelSkuUnit(), null, isCombination);

        }
    }

    /**
     * 秒杀订单退库存
     *
     * @param productDb      商品信息
     * @param orderAfterPO   退款信息
     * @param orderProductPO 订单商品信息
     */
    private void seckillGoodsStock(Product productDb, OrderAfterPO orderAfterPO, OrderProductPO orderProductPO) {
        // 查询秒杀订单扩展信息
        SeckillOrderExtendExample extendExample = new SeckillOrderExtendExample();
        extendExample.setOrderSn(orderProductPO.getOrderSn());
        List<SeckillOrderExtendVO> seckillOrderExtendList =
                seckillOrderExtendFeignClient.getSeckillOrderExtendList(extendExample);
        AssertUtil.notEmpty(seckillOrderExtendList, "秒杀订单扩展信息不存在");
        SeckillOrderExtendVO seckillOrderExtend = seckillOrderExtendList.get(0);
        // 查询秒杀商品信息
        SeckillStageProductVO stageProduct = seckillStageProductFeignClient
                .getSeckillStageProductByStageProductId(seckillOrderExtend.getStageProductstageProductId());
        AssertUtil.notNull(stageProduct, "秒杀商品不存在");
        Date date = new Date();
        // 未结束修改redis中的秒杀库存
        if (!date.after(stageProduct.getEndTime())) {
            String stockKey = RedisConst.REDIS_SECKILL_PRODUCT_STOCK_PREFIX + productDb.getProductId();
            String memberLimitKey = RedisConst.REDIS_SECKILL_MEMBER_BUY_NUM_PREFIX + productDb.getProductId() + "_"
                    + orderProductPO.getMemberId();
            if (stringRedisTemplate.opsForValue().get(stockKey) != null) {
                // 加库存
                stringRedisTemplate.opsForValue().increment(stockKey, orderAfterPO.getAfsNum());
            }
            if (stringRedisTemplate.opsForValue().get(memberLimitKey) != null) {
                stringRedisTemplate.opsForValue().decrement(memberLimitKey, orderAfterPO.getAfsNum());
            }
        }
        // 修改秒杀商品表的已购买人数和购买数量
        PromotionSeckillStageProduct seckillStageProduct = new PromotionSeckillStageProduct();
        seckillStageProduct.setStageProductId(stageProduct.getStageProductId());
        seckillStageProduct.setBuyerCount(-1);
        seckillStageProduct.setBuyQuantity(-orderAfterPO.getAfsNum());
        seckillStageProduct.setSeckillStock(orderAfterPO.getAfsNum());
        seckillStageProductFeignClient.updateSeckillStageProductByOrder(seckillStageProduct);
    }

    /**
     * 退拼团库存
     *
     * @param productDb      商品信息
     * @param orderSn        订单号
     * @param orderProductPO 订单商品信息
     */
    public void returnSpellGoodsStock(Product productDb, String orderSn, OrderProductPO orderProductPO) {
        // 拼团订单，修改redis中的已购买数量
        String stockKey =
                RedisConst.SPELL_PURCHASED_NUM_PREFIX + productDb.getGoodsId() + "_" + orderProductPO.getMemberId();
        if (stringRedisTemplate.opsForValue().get(stockKey) != null) {
            stringRedisTemplate.opsForValue().decrement(stockKey, orderProductPO.getProductNum());
        }
        // 查询拼团订单扩展信息
        SpellTeamMemberExample teamMemberExample = new SpellTeamMemberExample();
        teamMemberExample.setOrderSn(orderSn);
        List<SpellTeamMemberVO> orderExtendList = spellTeamMemberFeignClient.getSpellTeamMemberList(teamMemberExample);
        AssertUtil.notEmpty(orderExtendList, "获取拼团活动信息为空");
        SpellTeamMemberVO orderExtend = orderExtendList.get(0);
        // 查询拼团商品信息
        SpellGoodsVO spellGoods = spellGoodsFeignClient.getSpellGoodsBySpellGoodsId(orderExtend.getSpellGoodsId());
        AssertUtil.notNull(spellGoods, "拼团商品不存在");
        // 修改拼团商品库存
        PromotionSpellGoods spellGoodsNew = new PromotionSpellGoods();
        spellGoodsNew.setSpellGoodsId(spellGoods.getSpellGoodsId());
        spellGoodsNew.setSpellStock(spellGoods.getSpellStock() + orderProductPO.getProductNum());
        spellGoodsFeignClient.updateSpellGoods(spellGoodsNew);
    }

    // endregion ---------------------------------------平台确认退款 end
    // --------------------------------------------------------

    /**
     * 发送售后消息通知
     *
     * @param afterService 售后信息
     * @param afsNum       售后数量
     * @param afsAmount    售后金额
     */
    public void sendMsgAfterSale(OrderAfterPO afterService, Integer afsNum, BigDecimal afsAmount) {
        String msgType = "";
        if (afterService.getAfsType().equals(OrdersAfsConst.AFS_TYPE_RETURN)) {
            msgType = "return_news";
        } else if (afterService.getAfsType().equals(OrdersAfsConst.AFS_TYPE_REPLACEMENT)) {
            msgType = "replacement_news";
        } else {
            msgType = "refund_news";
        }
        // 添加消息通知
        List<MessageSendProperty> messageSendPropertyList = new ArrayList<>();
        messageSendPropertyList.add(new MessageSendProperty("afsSn", afterService.getAfsSn()));
        // 微信消息通知
        List<MessageSendProperty> messageSendPropertyList4Wx = new ArrayList<>();
        String result = String.format("订单号%s售后变化，金额%s", afterService.getOrderSn(), afsAmount);
        messageSendPropertyList4Wx.add(new MessageSendProperty("first", "您的售后单有新的变化。"));
        messageSendPropertyList4Wx.add(new MessageSendProperty("keyword1", afterService.getMemberName()));
        messageSendPropertyList4Wx.add(new MessageSendProperty("keyword2", "售后单变化"));
        messageSendPropertyList4Wx.add(new MessageSendProperty("keyword3", result));
        messageSendPropertyList4Wx.add(new MessageSendProperty("keyword4", TimeUtil.getDateTimeString(new Date())));
        messageSendPropertyList4Wx.add(new MessageSendProperty("remark", "服务农村最后一百米"));

        // 发送消息通知
        String msgLinkInfo = "{\"type\":\"" + msgType + "\",\"afsSn\":\"" + afterService.getAfsSn() + "\"}";
        MessageSendVO messageSendVO = new MessageSendVO(messageSendPropertyList, messageSendPropertyList4Wx, null,
                afterService.getMemberId(), MemberTplConst.AFTER_SALE_REMINDER, msgLinkInfo);
        // 发送到mq
        try {
            rabbitTemplate.convertAndSend(MQ_EXCHANGE_NAME, MQ_QUEUE_NAME_MEMBER_MSG, messageSendVO);
        } catch (Exception e) {
            log.info("发消息异常捕获,{}", JSON.toJSONString(messageSendVO), e);
        }
    }

    /**
     * 发送余额变动消息通知
     *
     * @param memberBalanceLog 余额变动信息
     */
    public void sendMsgAccountChange(MemberBalanceLog memberBalanceLog) {
        // 消息通知
        List<MessageSendProperty> messageSendPropertyList = new ArrayList<>();
        messageSendPropertyList.add(new MessageSendProperty("description", "订单退款"));
        messageSendPropertyList
                .add(new MessageSendProperty("availableBalance", memberBalanceLog.getAfterChangeAmount().toString()));
        messageSendPropertyList
                .add(new MessageSendProperty("frozenBalance", memberBalanceLog.getFreezeAmount().toString()));
        // 微信消息通知
        List<MessageSendProperty> messageSendPropertyList4Wx = new ArrayList<>();
        messageSendPropertyList4Wx.add(new MessageSendProperty("first", "您的账户发生了资金变动。"));
        messageSendPropertyList4Wx.add(new MessageSendProperty("keyword1", "订单退款"));
        messageSendPropertyList4Wx
                .add(new MessageSendProperty("keyword2", memberBalanceLog.getChangeValue().toString()));
        messageSendPropertyList4Wx
                .add(new MessageSendProperty("keyword3", TimeUtil.getDateTimeString(memberBalanceLog.getCreateTime())));
        messageSendPropertyList4Wx
                .add(new MessageSendProperty("keyword4", memberBalanceLog.getAfterChangeAmount().toString()));
        String msgLinkInfo = "{\"type\":\"balance_change\"}";
        MessageSendVO messageSendVO = new MessageSendVO(messageSendPropertyList, messageSendPropertyList4Wx,
                "changeTime", memberBalanceLog.getMemberId(), MemberTplConst.BALANCE_CHANGE_REMINDER, msgLinkInfo);
        // 发送到mq
        try {
            rabbitTemplate.convertAndSend(MQ_EXCHANGE_NAME, MQ_QUEUE_NAME_MEMBER_MSG, messageSendVO);
        } catch (Exception e) {
            log.info("发消息异常捕获,{}", JSON.toJSONString(messageSendVO), e);
        }
    }

    /**
     * 订单下是否有退款中的产品
     *
     * @param orderSn 订单号
     * @return true/false
     */
    public boolean whetherHasReturningProduct(String orderSn) {
        LambdaQueryWrapper<OrderReturnPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderReturnPO::getOrderSn, orderSn)
                .eq(OrderReturnPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                .in(OrderReturnPO::getState, OrderReturnStatus.duringRefundStatus())
                .select(OrderReturnPO::getReturnId);
        return orderReturnMapper.selectCount(queryWrapper) > 0;
    }

    /**
     * 订单下是否有退款中的产品,除了
     *
     * @param orderSn 订单号
     * @return true/false
     */
    public boolean whetherHasReturningProductExceptExchange(String orderSn) {
        LambdaQueryWrapper<OrderReturnPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderReturnPO::getOrderSn, orderSn)
                .eq(OrderReturnPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                .ne(OrderReturnPO::getReturnType, OrdersAfsConst.RETURN_TYPE_3)
                .in(OrderReturnPO::getState, OrderReturnStatus.duringRefundStatus())
                .select(OrderReturnPO::getReturnId);
        return orderReturnMapper.selectCount(queryWrapper) > 0;
    }

    public boolean whetherHasReturningOrder(Long storeId) {
        return returningOrderCount(storeId) > 0;
    }

    public int returningOrderCount(Long storeId) {
        LambdaQueryWrapper<OrderReturnPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderReturnPO::getStoreId, storeId)
                .eq(OrderReturnPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                .in(OrderReturnPO::getState, OrderReturnStatus.duringRefundStatus())
                .select(OrderReturnPO::getReturnId);
        return orderReturnMapper.selectCount(queryWrapper);
    }

    // region 订单退款信息查询 Mybatis Plus 形式

    /**
     * 根据退订单号查询退款信息
     *
     * @param afsSn 退订单号
     * @return 退款信息
     */
    public OrderReturnPO getOrderReturnByOrderSn(String afsSn) {
        LambdaQueryWrapper<OrderReturnPO> queryWrapper = Wrappers.lambdaQuery(OrderReturnPO.class);
        queryWrapper.eq(OrderReturnPO::getAfsSn, afsSn).eq(OrderReturnPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
        return orderReturnMapper.selectOne(queryWrapper);
    }

    /**
     * 信贷类退款方式，日中不能操作
     *
     * @param refundType
     */
    private void refundLimit(RefundType refundType) {
        boolean returnFlag;
        Integer nowTime = Integer.valueOf(DateUtil.getNowHHmm().replaceAll(":", ""));
        //23:00到1:00处于日终，贷款类不准退款
        if (end < start) {
            returnFlag = nowTime >= start || nowTime <= end;
        } else {
            returnFlag = nowTime >= start && nowTime <= end;
        }
        if (returnFlag && RefundType.isLoanPayRefundType(refundType)) {
            StringBuilder startTime = new StringBuilder(start.toString());
            StringBuilder endTime = new StringBuilder(end.toString());
            throw new BusinessException("当前时间段" + startTime.insert(startTime.length() - 2, ":") +
                    "-" + endTime.insert(endTime.length() - 2, ":") + "不允许退款！");
        }
    }

    public AfsOrderCountVO countDuringRefundStatus(Integer memberId) {
        AfsOrderCountVO afsOrderCountVO = new AfsOrderCountVO();
        if (memberId == null) {
            return afsOrderCountVO;
        }
        //查询售后中的数据
        LambdaQueryWrapper<OrderReturnPO> returnQuery = new LambdaQueryWrapper<>();
        returnQuery.eq(OrderReturnPO::getMemberId, memberId)
                .in(OrderReturnPO::getState, OrderReturnStatus.duringRefundStatus())
                .select(OrderReturnPO::getReturnId, OrderReturnPO::getReturnType);
        List<OrderReturnPO> orderReturnPOS = orderReturnService.list(returnQuery);
        Map<Integer, Long> afsOrderMap = orderReturnPOS.stream().collect(Collectors.groupingBy(OrderReturnPO::getReturnType, Collectors.counting()));

        for (Integer key : afsOrderMap.keySet()) {
            if (key == 1) {
                afsOrderCountVO.setRefundNum(afsOrderMap.get(key).intValue());
            } else if (key == 2) {
                afsOrderCountVO.setReturnNum(afsOrderMap.get(key).intValue());
            }
        }

        List<Integer> exchangeOrderStateList = Arrays.asList(ExchangeOrderConst.EXCHANGE_ORDER_STATE_WAIT_AUDIT, ExchangeOrderConst.EXCHANGE_ORDER_STATE_AGREE);
        LambdaQueryWrapper<OrderExchangePO> orderExchangeQueryWrapper = new LambdaQueryWrapper<>();
        orderExchangeQueryWrapper.eq(OrderExchangePO::getMemberId, memberId)
                .in(OrderExchangePO::getExchangeOrderState, exchangeOrderStateList)
                .select(OrderExchangePO::getExchangeOrderId);
        int exchangeOrderCount = orderExchangeService.count(orderExchangeQueryWrapper);

        //查询换货未审批的数据 + 换货已同意且未完成的数据
        afsOrderCountVO.setExchangeNum(exchangeOrderCount);
        return afsOrderCountVO;
    }


    public void afterSaleReturnStock(String afsSn) {
        OrderAfterPO orderAfterPO = this.getOrderAfterServiceByAfsSn(afsSn);
        BizAssertUtil.notNull(orderAfterPO, "退款单" + afsSn + "查询售后信息为空");
        OrderPO orderPODb = orderService.getByOrderSn(orderAfterPO.getOrderSn());
        BizAssertUtil.notNull(orderAfterPO, "查询售后单对应订单" + orderAfterPO.getOrderSn() + "为空");
        this.increaseStock(orderAfterPO, orderPODb);
    }

    public void updateJdInterceptStatus(String afsSn, int jdInterceptStatus) {
        OrderReturnPO orderReturnPO = orderReturnModel.getOrderReturnByAfsSn(afsSn);
        if (JdInterceptStatusEnum.isFinalStatus(orderReturnPO.getJdInterceptStatus())) {
            log.warn("updateJdInterceptStatus 拦截状态已到终态，无需更新");
            return;
        }
        LambdaUpdateWrapper<OrderReturnPO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.set(OrderReturnPO::getJdInterceptStatus, jdInterceptStatus)
                .eq(OrderReturnPO::getAfsSn, afsSn);
        orderReturnService.update(lambdaUpdateWrapper);
    }

}