package com.cfpamf.ms.mallorder.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cfpamf.ms.loan.facade.request.external.mall.CdmallOrderUpdatePlanLoan;
import com.cfpamf.ms.loan.facade.request.external.mall.CdmallOrderUpdatePlanLoanRequest;
import com.cfpamf.ms.loan.facade.vo.external.mall.CdmallOrderUpdatePlanLoanVo;
import com.cfpamf.ms.mall.filecenter.domain.dto.FileDTO;
import com.cfpamf.ms.mallgoods.facade.enums.InterestWayEnum;
import com.cfpamf.ms.mallorder.common.enums.*;
import com.cfpamf.ms.mallorder.common.util.DateUtil;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.PlanLoanDateDTO;
import com.cfpamf.ms.mallorder.dto.PlanLoanDateFailDTO;
import com.cfpamf.ms.mallorder.dto.PlanLoanDateImportDTO;
import com.cfpamf.ms.mallorder.enums.FacadeOrderReturnStatus;
import com.cfpamf.ms.mallorder.integration.loan.LoanPayIntegration;
import com.cfpamf.ms.mallorder.mapper.OrderAfterMapper;
import com.cfpamf.ms.mallorder.mapper.PlanLoanDateMapper;
import com.cfpamf.ms.mallorder.model.OrderLogModel;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.model.OrderProductModel;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.req.admin.AdminPlanLoanDateRequest;
import com.cfpamf.ms.mallorder.service.IOrderExtendFinanceService;
import com.cfpamf.ms.mallorder.service.IPlanLoanDateExportService;
import com.cfpamf.ms.mallorder.service.IPlanLoanDateService;
import com.cfpamf.ms.mallorder.service.ITaskQueueService;
import com.cfpamf.ms.mallorder.v2.service.OrderPresellService;
import com.cfpamf.ms.mallorder.vo.ExportingFailedDataToExcelVO;
import com.cfpamf.ms.mallorder.vo.PlanLoanDateVO;
import com.cfpamf.ms.mallsystem.vo.Admin;
import com.slodon.bbc.core.response.PagerInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PlanLoanDateServiceImpl extends ServiceImpl<PlanLoanDateMapper, PlanLoanDatePO> implements IPlanLoanDateService {

    @Autowired
    private PlanLoanDateMapper planLoanDateMapper;

    @Autowired
    private LoanPayIntegration loanPayIntegration;

    @Autowired
    private IPlanLoanDateService planLoanDateService;

    @Autowired
    private IOrderExtendFinanceService financeService;

    @Autowired
    private ITaskQueueService taskQueueService;

    @Autowired
    private OrderLogModel orderLogModel;

    @Autowired
    private OrderModel orderModel;

    @Autowired
    private IPlanLoanDateExportService planLoanDateExportService;

    @Autowired
    private OrderPresellService orderPresellService;

    @Autowired
    private OrderAfterMapper orderAfterMapper;

    @Autowired
    private OrderProductModel orderProductModel;

    @Autowired
    private IOrderExtendFinanceService orderExtendFinanceService;


    @Override
    public List<PlanLoanDateVO> getPlanLoanDateList(PagerInfo pager, AdminPlanLoanDateRequest req) {

        if (pager != null) {
            pager.setRowsCount(planLoanDateMapper.getPlanLoanDateListCount(req));
            return planLoanDateMapper.getPlanLoanDateListByPage(req, pager.getStart(), pager.getPageSize());
        } else {
            return planLoanDateMapper.getPlanLoanDateListByPage(req, null, null);
        }
    }

    @Override
    public ExportingFailedDataToExcelVO adjustPlanLoanDate(PlanLoanDateDTO planLoanDateDTO, Admin admin) throws Exception {
        ExportingFailedDataToExcelVO exportingFailedDataToExcelVO = new ExportingFailedDataToExcelVO();

        String[] orderSnArr = planLoanDateDTO.getOrderSns().split(",");
        List<String> orderSnOrgList = Arrays.stream(orderSnArr).collect(Collectors.toList());

        List<PlanLoanDateFailDTO> failPlanLoanDateList = new ArrayList<>();


        int pageSize = 1000;
        int pages = 1;
        if (orderSnOrgList.size() > pageSize) {
            pages = (orderSnOrgList.size() % pageSize) == 0 ? (orderSnOrgList.size() / pageSize) : (orderSnOrgList.size() / pageSize + 1);
        }
        String dateStr = DateUtil.getDateString(planLoanDateDTO.getPlanLoanDate(), "yyyy-MM-dd");
        for (int i = 0; i < pages; i++) {
            List<CdmallOrderUpdatePlanLoan> loanRequestList = new ArrayList<>();
            List<String> subList = orderSnOrgList.subList(i * pageSize, Math.min((i + 1) * pageSize, orderSnOrgList.size()));

            for (String orderSn : subList) {
                String validResult = planLoanDateService.validPlanLoanDate(orderSn, planLoanDateDTO.getPlanLoanDate());
                if (!validResult.equalsIgnoreCase(OrderConst.CHECK_SUCCESS)) {
                    PlanLoanDateFailDTO failPlanLoanDateDTO = new PlanLoanDateFailDTO();
                    failPlanLoanDateDTO.setReason(validResult);
                    failPlanLoanDateDTO.setOrderSn(orderSn);
                    failPlanLoanDateDTO.setPlanLoanDate(planLoanDateDTO.getPlanLoanDate());
                    failPlanLoanDateList.add(failPlanLoanDateDTO);
                } else {
                    CdmallOrderUpdatePlanLoan loanRequest = new CdmallOrderUpdatePlanLoan();
                    loanRequest.setOrderBatchId(orderSn);
                    loanRequest.setPlanLoanDate(dateStr);
                    loanRequestList.add(loanRequest);
                }
            }

            List<String> pageOrderSnSet;

            if (CollectionUtils.isEmpty(loanRequestList)) {
                continue;
            }


            List<CdmallOrderUpdatePlanLoanVo> resultList = loanPayIntegration.updateCdmallOrderPlanLoanDate(loanRequestList, admin.getAdminName());
            List<String> loanFailOrderSn = new ArrayList<>();
            if (!CollectionUtils.isEmpty(resultList)) {
                for (CdmallOrderUpdatePlanLoanVo cdmallOrderUpdatePlanLoanVo : resultList) {
                    PlanLoanDateFailDTO failPlanLoanDateDTO = new PlanLoanDateFailDTO();
                    failPlanLoanDateDTO.setReason(cdmallOrderUpdatePlanLoanVo.getFailReason());
                    failPlanLoanDateDTO.setPlanLoanDate(planLoanDateDTO.getPlanLoanDate());
                    failPlanLoanDateDTO.setOrderSn(cdmallOrderUpdatePlanLoanVo.getOrderBatchId());
                    failPlanLoanDateList.add(failPlanLoanDateDTO);
                    loanFailOrderSn.add(cdmallOrderUpdatePlanLoanVo.getOrderBatchId());
                }
                pageOrderSnSet = loanRequestList.stream().filter(x -> !loanFailOrderSn.contains(x.getOrderBatchId())).map(CdmallOrderUpdatePlanLoan::getOrderBatchId).collect(Collectors.toList());

            } else {
                pageOrderSnSet = loanRequestList.stream().map(CdmallOrderUpdatePlanLoan::getOrderBatchId).collect(Collectors.toList());
            }

            if (!CollectionUtils.isEmpty(pageOrderSnSet)) {
                //更新任务表中的数据 && 更新金融规则表里面的数据 && 记录轨迹 && 保存记录;
                planLoanDateService.updatePlanLoanDateForOrderSnList(pageOrderSnSet, planLoanDateDTO.getPlanLoanDate(), planLoanDateDTO.getRemark(), admin);
            }


        }

        if (!CollectionUtils.isEmpty(failPlanLoanDateList)) {
            FileDTO fileDTO = planLoanDateExportService.asyncExportFailureData(Long.valueOf(admin.getAdminId()), admin.getAdminName(), failPlanLoanDateList);
            exportingFailedDataToExcelVO.setFileDTO(fileDTO);
        }

        exportingFailedDataToExcelVO.setFailureCount(failPlanLoanDateList.size());
        exportingFailedDataToExcelVO.setSuccessCount(orderSnOrgList.size() - failPlanLoanDateList.size());


        //将失败记录写入到excel
        return exportingFailedDataToExcelVO;

    }


    private void insertPlanLoanDateTrack(List<String> orderSnList, Date planDate, Integer role, Integer userId, String userName) {
        if (CollectionUtils.isEmpty(orderSnList)) {
            return;
        }
        for (String orderSn : orderSnList) {
            //记录轨迹
            OrderPO orderPO = orderModel.getOrderByOrderSn(orderSn);
            OrderExtendFinancePO orderExtendFinancePO = financeService.getByOrderSn(orderSn);

            if(orderExtendFinancePO == null || orderPO == null) {
                log.warn("insertPlanLoanDateTrack orderExtendFinancePO is null,orderSn = {}", orderSn);
                continue;
            }

            String remark = "";
            if(InterestWayEnum.PLAN_LOAN_DATE.getCode().equals(orderExtendFinancePO.getInterestWay())) {
                remark = "调整放款日，原放款日：" + DateUtil.getDateString(orderExtendFinancePO.getPlanLoanDate(), "yyyy-MM-dd") + "，调整后放款日：" + DateUtil.getDateString(planDate, "yyyy-MM-dd");
            } else if (InterestWayEnum.PLAN_INTEREST_START_DAYS.getCode().equals(orderExtendFinancePO.getInterestWay())) {
                remark = "调整放款日，原放款日：" + DateUtil.getDateString(DateUtil.addDate(orderPO.getPayTime(), orderExtendFinancePO.getPlanInterestStartDays()), "yyyy-MM-dd") + "，调整后放款日：" + DateUtil.getDateString(planDate, "yyyy-MM-dd");
            }

            orderLogModel.insertOrderLog(role, Long.valueOf(userId), userName, orderSn, orderPO.getOrderState(),
                        orderPO.getOrderState(), LoanStatusEnum.valueOf(orderPO.getLoanPayState()).getValue(), "调整放款日", OrderCreateChannel.WEB, remark);

        }

    }

    @Override
    @Transactional
    public void updatePlanLoanDateForOrderSnList(List<String> orderSnList, Date planDate, String remark, Admin admin) {
        if (CollectionUtils.isEmpty(orderSnList)) {
            return;
        }

        List<PlanLoanDatePO> planLoanDatePOList = new ArrayList<>();

        for (String orderSn : orderSnList) {
            //记录轨迹
            OrderPO orderPO = orderModel.getOrderByOrderSn(orderSn);
            OrderExtendFinancePO orderExtendFinancePO = financeService.getByOrderSn(orderSn);

            if(orderExtendFinancePO == null || orderPO == null) {
                log.warn("insertPlanLoanDateTrack orderExtendFinancePO is null,orderSn = {}", orderSn);
                continue;
            }
            String logRemark = "";

            if(InterestWayEnum.PLAN_LOAN_DATE.getCode().equals(orderExtendFinancePO.getInterestWay())) {
                logRemark = "调整放款日，原放款日：" + DateUtil.getDateString(orderExtendFinancePO.getPlanLoanDate(), "yyyy-MM-dd") + "，调整后放款日：" + DateUtil.getDateString(planDate, "yyyy-MM-dd");
            } else if (InterestWayEnum.PLAN_INTEREST_START_DAYS.getCode().equals(orderExtendFinancePO.getInterestWay())) {
                logRemark = "调整放款日，原放款日：" + DateUtil.getDateString(DateUtil.addDate(orderPO.getPayTime(), orderExtendFinancePO.getPlanInterestStartDays()), "yyyy-MM-dd") + "，调整后放款日：" + DateUtil.getDateString(planDate, "yyyy-MM-dd");
            }

            orderLogModel.insertOrderLog(OrderConst.ADMIN_ROLE, Long.valueOf(admin.getAdminId()), admin.getAdminName(), orderSn, orderPO.getOrderState(),
                    orderPO.getOrderState(), LoanStatusEnum.valueOf(orderPO.getLoanPayState()).getValue(), "调整放款日", OrderCreateChannel.WEB, logRemark);


            //保存记录
            PlanLoanDatePO planLoanDatePO = new PlanLoanDatePO();
            planLoanDatePO.setPlanLoanDate(planDate);
            planLoanDatePO.setStoreId(orderPO.getStoreId());
            planLoanDatePO.setStoreName(orderPO.getStoreName());
            planLoanDatePO.setOrderSn(orderSn);
            planLoanDatePO.setCreateBy(admin.getAdminName());
            planLoanDatePO.setOrderAmount(orderPO.getOrderAmount());
            planLoanDatePO.setRemark(remark);
            planLoanDatePO.setUpdateBy(admin.getAdminName());
            Date orgPlanLoanDate = orderExtendFinancePO.getPlanLoanDate();
            if(Objects.isNull(orgPlanLoanDate) && InterestWayEnum.PLAN_INTEREST_START_DAYS.getCode().equals(orderExtendFinancePO.getInterestWay())) {
                try {
                    TaskQueuePO taskQueuePO = taskQueueService.getTaskQueueByBizIdAndBizType(orderSn, TaskQueueBizTypeEnum.N_DAYS_AFTER_PAY.getValue());
                    orgPlanLoanDate = taskQueuePO.getNextExecuteTime();
                } catch (Exception e) {
                    log.warn("getTaskQueueByBizIdAndBizType error,orderSn = {}", orderSn);
                    orgPlanLoanDate = DateUtil.addDate(orderPO.getPayTime(), orderExtendFinancePO.getPlanInterestStartDays());
                }
            }
            planLoanDatePO.setOrgPlanLoanDate(orgPlanLoanDate);
            planLoanDatePOList.add(planLoanDatePO);

            planLoanDateService.saveBatch(planLoanDatePOList);


        }

        //记录轨迹
        //insertPlanLoanDateTrack(orderSnList, planDate, OrderConst.ADMIN_ROLE, admin.getAdminId(), admin.getAdminName());

        //更新任务表中的数据(未执行和执行失败的可以更新)
        LambdaUpdateWrapper<TaskQueuePO> taskQueuePOLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        taskQueuePOLambdaUpdateWrapper.in(TaskQueuePO::getStatus, Arrays.asList(TaskQueueStatusEnum.INIT, TaskQueueStatusEnum.FAIL))
                .set(TaskQueuePO::getUpdateBy, admin.getAdminName())
                .set(TaskQueuePO::getNextExecuteTime, planDate)
                .in(TaskQueuePO::getBizType, Arrays.asList(TaskQueueBizTypeEnum.PLAN_DATE_LOAN.getValue(),TaskQueueBizTypeEnum.N_DAYS_AFTER_PAY.getValue()))
                .in(TaskQueuePO::getBizId, orderSnList);

        taskQueueService.update(taskQueuePOLambdaUpdateWrapper);

        //保存记录
        //planLoanDateService.insertPlanLoanDateList(orderSnList, planDate, remark, admin.getAdminName());

        //更新金融规则表里面的数据 OrderExtendFinancePO
        LambdaUpdateWrapper<OrderExtendFinancePO> orderExtendFinancePOLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        orderExtendFinancePOLambdaUpdateWrapper.set(OrderExtendFinancePO::getPlanLoanDate, planDate)
                .set(OrderExtendFinancePO::getUpdateBy, admin.getAdminName())
                .in(OrderExtendFinancePO::getOrderSn, orderSnList)
                .isNotNull(OrderExtendFinancePO::getPlanLoanDate);

        financeService.update(orderExtendFinancePOLambdaUpdateWrapper);



    }

    @Override
    public void insertPlanLoanDateList(List<String> orderSnList, Date planDate, String remark, String adminName) {
        List<PlanLoanDatePO> planLoanDatePOList = new ArrayList<>();
        for (String orderSn : orderSnList) {
            OrderPO orderPO = orderModel.getOrderByOrderSn(orderSn);
            OrderExtendFinancePO orderExtendFinancePO = financeService.getByOrderSn(orderSn);
            PlanLoanDatePO planLoanDatePO = new PlanLoanDatePO();
            planLoanDatePO.setPlanLoanDate(planDate);
            planLoanDatePO.setStoreId(orderPO.getStoreId());
            planLoanDatePO.setStoreName(orderPO.getStoreName());
            planLoanDatePO.setOrderSn(orderSn);
            planLoanDatePO.setCreateBy(adminName);
            planLoanDatePO.setOrderAmount(orderPO.getOrderAmount());
            planLoanDatePO.setRemark(remark);
            planLoanDatePO.setUpdateBy(adminName);
            Date orgPlanLoanDate = orderExtendFinancePO.getPlanLoanDate();
            if(Objects.isNull(orgPlanLoanDate) && InterestWayEnum.PLAN_INTEREST_START_DAYS.getCode().equals(orderExtendFinancePO.getInterestWay())) {
                orgPlanLoanDate = DateUtil.addDate(orderPO.getPayTime(), orderExtendFinancePO.getPlanInterestStartDays());
            }
            planLoanDatePO.setOrgPlanLoanDate(orgPlanLoanDate);
            planLoanDatePOList.add(planLoanDatePO);
        }
        planLoanDateService.saveBatch(planLoanDatePOList);
    }

    @Override
    public String validPlanLoanDate(String orderSn, Date planDate) {
        if (StringUtils.isEmpty(orderSn)) {
            return "订单号不能为空";
        }
        OrderPO orderPO = orderModel.getOrderByOrderSnLambda(orderSn);
        if (orderPO == null) {
            return "该订单不存在";
        }
        //订单状态校验：待支付尾款、已取消、已关闭 不能修改
        if (OrderStatusEnum.valueOf(orderPO.getOrderState()).getValue().equals(OrderStatusEnum.TRADE_CLOSE.getValue())
                || OrderStatusEnum.valueOf(orderPO.getOrderState()).getValue().equals(OrderStatusEnum.CANCELED.getValue())
                || OrderStatusEnum.valueOf(orderPO.getOrderState()).getValue().equals(OrderStatusEnum.WAIT_PAY.getValue())) {
            return "该订单状态为:" + OrderStatusEnum.valueOf(orderPO.getOrderState()).getDesc() + "，禁止调整";
        }

        //已放款的订单，禁止调整
        if (LoanStatusEnum.isLending(orderPO.getLoanPayState())) {
            return "该订单放款状态为:" + LoanStatusEnum.valueOf(orderPO.getLoanPayState()).getDesc() + "，禁止调整";
        }

        //非贷款类支付（组合支付的尾款），禁止调整
        if (!PayMethodEnum.isLoanPay(orderPO.getPaymentCode())) {
            if (PayMethodEnum.isCombinationPay(orderPO.getPaymentCode())) {
                //尾款支付方式是否为贷款类支付
                if (orderPresellService.checkBalancePayMethodIsLoanPay(orderSn)) {
                    return "该组合订单尾款支付方式为:" + PayMethodEnum.getValue(orderPO.getPaymentCode()) + ",非贷款类支付，禁止调整";
                }
            } else {
                return "该订单支付为" + PayMethodEnum.getValue(orderPO.getPaymentCode()) + ",非贷款类支付，禁止调整";
            }
        }
        List<OrderProductPO> orderProductPOList = orderProductModel.getOrderProductListByOrderSn(orderSn);
        Set<Long> orderProductIdList = orderProductPOList.stream().map(OrderProductPO::getOrderProductId).collect(Collectors.toSet());
        //订单已售后，禁止调整
        int count = orderAfterMapper.getRefundStatusCountByIdAndState(orderProductIdList, FacadeOrderReturnStatus.refuseStateList());

        if (count > 0) {
            return "该订单存在售后,禁止调整";
        }

        OrderExtendFinancePO orderExtendFinancePO = orderExtendFinanceService.getByOrderSn(orderSn);

        //非计划放款日订单不允许调整
        if (Objects.isNull(orderExtendFinancePO) || (!InterestWayEnum.PLAN_LOAN_DATE.getCode().equals(orderExtendFinancePO.getInterestWay())
        && !InterestWayEnum.PLAN_INTEREST_START_DAYS.getCode().equals(orderExtendFinancePO.getInterestWay()))) {
            return "该订单不是计划放款日订单，禁止调整";
        }

        //计划放款日小于当前时间，禁止调整 DateUtil.isEndDateLessBeginDateByDay(new Date(),planDate )
        if (DateUtil.isEndDateLessBeginDate(new Date(), planDate)) {
            return "该订单计划放款日小于当前时间，禁止调整";
        }


        return OrderConst.CHECK_SUCCESS;
    }

    @Override
    public ExportingFailedDataToExcelVO excelParseAndSave(List<PlanLoanDateImportDTO> orderSnOrgList, Admin admin) throws Exception {

        ExportingFailedDataToExcelVO exportingFailedDataToExcelVO = new ExportingFailedDataToExcelVO();

        List<PlanLoanDateFailDTO> failPlanLoanDateList = new ArrayList<>();

        int pageSize = 1000;
        int pages = 1;
        if (orderSnOrgList.size() > pageSize) {
            pages = (orderSnOrgList.size() % pageSize) == 0 ? (orderSnOrgList.size() / pageSize) : (orderSnOrgList.size() / pageSize + 1);
        }
        for (int i = 0; i < pages; i++) {
            List<CdmallOrderUpdatePlanLoan> loanRequestList = new ArrayList<>();
            List<PlanLoanDateImportDTO> subList = orderSnOrgList.subList(i * pageSize, Math.min((i + 1) * pageSize, orderSnOrgList.size()));

            for (PlanLoanDateImportDTO planLoanDateImportDTO : subList) {
                String orderSn = planLoanDateImportDTO.getOrderSn();

                String validResult = planLoanDateService.validPlanLoanDate(orderSn, planLoanDateImportDTO.getPlanLoanDate());
                if (!validResult.equalsIgnoreCase("success")) {
                    PlanLoanDateFailDTO failPlanLoanDateDTO = new PlanLoanDateFailDTO();
                    failPlanLoanDateDTO.setReason(validResult);
                    failPlanLoanDateDTO.setOrderSn(orderSn);
                    failPlanLoanDateDTO.setPlanLoanDate(planLoanDateImportDTO.getPlanLoanDate());
                    failPlanLoanDateList.add(failPlanLoanDateDTO);
                } else {
                    CdmallOrderUpdatePlanLoan loanRequest = new CdmallOrderUpdatePlanLoan();
                    loanRequest.setOrderBatchId(orderSn);
                    loanRequest.setPlanLoanDate(DateUtil.getDateString(planLoanDateImportDTO.getPlanLoanDate(), "yyyy-MM-dd"));
                    loanRequestList.add(loanRequest);
                }
            }


            if (CollectionUtils.isEmpty(loanRequestList)) {
                continue;
            }



            List<CdmallOrderUpdatePlanLoanVo> resultList = loanPayIntegration.updateCdmallOrderPlanLoanDate(loanRequestList, admin.getAdminName());
            List<String> loanFailOrderSn = new ArrayList<>();

            if (!CollectionUtils.isEmpty(resultList)) {
                for (CdmallOrderUpdatePlanLoanVo cdmallOrderUpdatePlanLoanVo : resultList) {
                    PlanLoanDateFailDTO failPlanLoanDateDTO = new PlanLoanDateFailDTO();
                    failPlanLoanDateDTO.setReason(cdmallOrderUpdatePlanLoanVo.getFailReason());
                    failPlanLoanDateDTO.setPlanLoanDate(DateUtil.parse(cdmallOrderUpdatePlanLoanVo.getPlanLoanDate(), "yyyy-MM-dd"));
                    failPlanLoanDateDTO.setOrderSn(cdmallOrderUpdatePlanLoanVo.getOrderBatchId());
                    failPlanLoanDateList.add(failPlanLoanDateDTO);
                    loanFailOrderSn.add(cdmallOrderUpdatePlanLoanVo.getOrderBatchId());

                }

                loanRequestList = loanRequestList.stream().filter(x -> !loanFailOrderSn.contains(x.getOrderBatchId())).collect(Collectors.toList());
            }
            if (!CollectionUtils.isEmpty(loanRequestList)) {
                //按日期分组保存
                Map<String, Set<CdmallOrderUpdatePlanLoan>> groupMap = loanRequestList.stream().collect(Collectors.groupingBy(CdmallOrderUpdatePlanLoan::getPlanLoanDate, Collectors.toSet()));
                groupMap.forEach((key, list) -> {
                    //更新任务表中的数据 && 更新金融规则表里面的数据 && 记录轨迹;
                    List<String> pageOrderSnSet = list.stream().map(CdmallOrderUpdatePlanLoan::getOrderBatchId).collect(Collectors.toList());
                    planLoanDateService.updatePlanLoanDateForOrderSnList(pageOrderSnSet, DateUtil.parse(key, DateUtil.FORMAT_DATE), null, admin);
                });

            }


        }

        if (!CollectionUtils.isEmpty(failPlanLoanDateList)) {
            FileDTO fileDTO = planLoanDateExportService.asyncExportFailureData(Long.valueOf(admin.getAdminId()), admin.getAdminName(), failPlanLoanDateList);
            exportingFailedDataToExcelVO.setFileDTO(fileDTO);
        }
        exportingFailedDataToExcelVO.setFailureCount(failPlanLoanDateList.size());
        exportingFailedDataToExcelVO.setSuccessCount(orderSnOrgList.size() - failPlanLoanDateList.size());

        return exportingFailedDataToExcelVO;
    }


}
