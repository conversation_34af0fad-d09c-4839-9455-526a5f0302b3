package com.cfpamf.ms.mallorder.controller.fegin.facade.workflow.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.util.Map;

@Getter
@Setter
public class TaskRejectRequestDto extends ProcessModifyRequestDTO {
    @ApiModelProperty("系统编号")
    private Integer systemId = 38;
    @ApiModelProperty(value = "流程实例ID", required = true)
    @NotBlank(message = "流程实例ID不能为空")
    private String procInstId;
    @ApiModelProperty("审批任务时提交的批注")
    @NotBlank(message = "驳回任务评论不能为空")
    private String comment;
    @ApiModelProperty("完成任务时传入的流程变量如：下个节点处理人（key为固定值”PROCESSOR_JOB_NO“，value为工号，多个用‘,’号隔开）")
    private Map<String, Object> vars;
}
