package com.cfpamf.ms.mallorder.controller.fegin.facade;


import com.cfpamf.ms.mallorder.model.CartModel;
import com.cfpamf.ms.mallorder.po.CartPO;
import com.cfpamf.ms.mallorder.request.CartExample;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Slf4j
public class CartFeign {

    @Resource
    private CartModel cartModel;

    /**
     * 根据cartId获取商城购物车详情
     *
     * @param cartId cartId
     * @return
     */
    @GetMapping("/v1/feign/business/cart/get")
    public CartPO getCartByCartId(@RequestParam("cartId") Integer cartId) {
        return cartModel.getCartByCartId(cartId);
    }

    /**
     * 获取条件获取商城购物车列表
     *
     * @param example 查询条件信息
     * @return
     */
    @PostMapping("/v1/feign/business/cart/getList")
    public List<CartPO> getCartList(@RequestBody CartExample example) {
        return cartModel.getCartList(example, example.getPager());
    }

    /**
     * 新增商城购物车
     *
     * @param cartPO
     * @return
     */
    @PostMapping( "/v1/feign/business/cart/addCart")
    public Integer saveCart(@RequestBody CartPO cartPO) {
        return cartModel.saveCart(cartPO);
    }

    /**
     * 登录时添加离线购物车信息
     *
     * @param cartPOList
     * @param memberId
     * @return
     */
    @PostMapping("/v1/feign/business/cart/addCartByMemberId")
    public void addCartByMemberId(List<CartPO> cartPOList, Integer memberId, String areaCode) {
        cartModel.addCartByMemberId(cartPOList, memberId, areaCode);
    }

    /**
     * 根据cartId更新商城购物车
     *
     * @param cartPO
     * @return
     */
    @PostMapping("/v1/feign/business/cart/updateCart")
    public Integer updateCart(@RequestBody CartPO cartPO) {
        return cartModel.updateCart(cartPO);
    }

    /**
     * 根据cartId删除商城购物车
     *
     * @param cartId cartId
     * @return
     */
    @PostMapping("/v1/feign/business/cart/deleteCart")
    public Integer deleteCart(@RequestParam("cartId") Integer cartId) {
        return cartModel.deleteCart(cartId);
    }

}