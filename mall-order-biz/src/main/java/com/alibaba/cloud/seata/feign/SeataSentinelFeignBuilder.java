package com.alibaba.cloud.seata.feign;

import com.alibaba.csp.sentinel.feign.adapter.SentinelFeign;
import feign.Feign;
import feign.Retryer;
import org.springframework.beans.factory.BeanFactory;

/**
 * Sentinel限流seata配置
 * <AUTHOR>
 */
final class SeataSentinelFeignBuilder {

    private SeataSentinelFeignBuilder() {
    }

    static Feign.Builder builder(BeanFactory beanFactory) {
        return SentinelFeign.builder().retryer(Retryer.NEVER_RETRY)
                .client(new SeataFeignClient(beanFactory));
    }

}