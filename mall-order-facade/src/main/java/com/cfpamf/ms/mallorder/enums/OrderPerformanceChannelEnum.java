package com.cfpamf.ms.mallorder.enums;


import com.alibaba.fastjson.annotation.JSONType;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 履约渠道
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
@JSONType(serializeEnumAsJavaBean = true)
public enum OrderPerformanceChannelEnum implements IEnum<Integer> {

    PERFORMANCE_CHANNEL_MALL(1, "标准电商"),
    PERFORMANCE_CHANNEL_YZH(2, "云中鹤"),
    PERFORMANCE_CHANNEL_JD(3, "京东物流"),
    PERFORMANCE_CHANNEL_ERP(4,"ERP");

    Integer value;
    String desc;

    public static OrderPerformanceChannelEnum valueOf(int value) {
        for (OrderPerformanceChannelEnum ps : OrderPerformanceChannelEnum.values()) {
            if (value == ps.value) {
                return ps;
            }
        }
        return PERFORMANCE_CHANNEL_MALL;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }
}
