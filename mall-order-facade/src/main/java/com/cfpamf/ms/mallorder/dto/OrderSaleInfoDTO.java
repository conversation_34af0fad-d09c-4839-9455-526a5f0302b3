package com.cfpamf.ms.mallorder.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 管理平台-首页数据报表
 *
 * <AUTHOR>
 * @Date 2022-08-23
 */
@Data
public class OrderSaleInfoDTO {
    @ApiModelProperty("店铺名称")
    private String storeName;

    @ApiModelProperty("商品名称")
    private String goodsName;

    @ApiModelProperty("汇总数量")
    private Integer totalNum;

    @ApiModelProperty("汇总金额")
    private BigDecimal totalAmount;
}
