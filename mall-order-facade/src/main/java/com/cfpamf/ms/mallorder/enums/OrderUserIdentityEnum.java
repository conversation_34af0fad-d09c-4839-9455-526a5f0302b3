package com.cfpamf.ms.mallorder.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * 下单用户身份
 */
@AllArgsConstructor
@Getter
public enum OrderUserIdentityEnum {

	CUSTOMER(1, "客户"),
	EMPLOYEE(2, "员工")	;

	Integer value;
	String desc;

	public static OrderUserIdentityEnum valueOf(int value) {
		for (OrderUserIdentityEnum e : OrderUserIdentityEnum.values()) {
			if (value == e.value) {
				return e;
			}
		}
		return null;
	}

}
