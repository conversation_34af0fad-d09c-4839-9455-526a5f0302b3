package com.cfpamf.ms.mallorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 可用支付方式查询接口
 *
 * <AUTHOR>
 * @date 2021/6/25 11:10
 * @return
 */
@Data
public class PayWayReq implements Serializable {
    private static final long serialVersionUID = -2964136902144210150L;

    @ApiModelProperty(value = "客户编号 调试用", hidden = true)
    private String custNo;

    @ApiModelProperty(value = "下单渠道：H5-浏览器H5，APP-乡助APP，WE_CHAT-微信浏览器，MINI_PRO-小程序")
    private String channel;

    @ApiModelProperty(value = "实付金额")
    private BigDecimal totalAmount;
    @ApiModelProperty(value = "店铺信息")
    private List<PayStoreInfo> storeGroupList;

    @Data
    public static class PayStoreInfo implements Serializable {
        @ApiModelProperty(value = "店铺id", required = true)
        private Long storeId;
        @ApiModelProperty(value = "商品列表", required = true)
        private PayOrderProduct productList;
    }

    @Data
    public static class PayOrderProduct {
        @ApiModelProperty(value = "商品ID", required = true)
        private Long goodsId;
        @ApiModelProperty(value = "商品ID", required = true)
        private Integer productId;
    }
}
