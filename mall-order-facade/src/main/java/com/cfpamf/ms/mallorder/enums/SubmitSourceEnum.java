package com.cfpamf.ms.mallorder.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum SubmitSourceEnum {

    IMMEDIATELY(1,"立刻购买"),

    ORDER_CONFIRM(2,""),

    ORDER_COMMIT(3,"提交订单");

    public final Integer code;

    public final String desc;

    public static SubmitSourceEnum getSourceEnumByCode(Integer code){
        if (Objects.isNull(code)){
            return null;
        }
        for (SubmitSourceEnum item : SubmitSourceEnum.values()){
            if (item.getCode().equals(code)){
                return item;
            }
        }
        return null;
    }
}
