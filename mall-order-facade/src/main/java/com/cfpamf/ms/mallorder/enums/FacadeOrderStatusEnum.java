package com.cfpamf.ms.mallorder.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单状态定义
 *
 * <AUTHOR>
 * @date 2021/6/19 17:16
 * @return
 */
@AllArgsConstructor
@Getter
public enum FacadeOrderStatusEnum {
    UN_KNOW(-1, "未知"),
    CANCELED(0, "交易关闭"),
    WAIT_PAY_DEPOSIT(5, "待支付订金"),
    WAIT_PAY(10, "待付款"),
    DEAL_PAY(15, "付款中"),
    WAIT_DELIVER(20, "待发货"),
    PART_DELIVERED(25, "部分发货"),
    WAIT_RECEIPT(30, "待签收"),
    TRADE_SUCCESS(40, "交易成功"),
    TRADE_CLOSE(50, "已关闭");

    Integer value;
    String desc;

    public static boolean isClosed(int value){
        return CANCELED.getValue() == value || TRADE_CLOSE.getValue() == value;
    }

    public static FacadeOrderStatusEnum valueOf(int value) {
        for (FacadeOrderStatusEnum ps : FacadeOrderStatusEnum.values()) {
            if (value == ps.value) {
                return ps;
            }
        }
        return UN_KNOW;
    }

    public static boolean isPaid(int state) {
        return state == WAIT_DELIVER.getValue() || state == PART_DELIVERED.getValue() || state == WAIT_RECEIPT.getValue() || state == TRADE_SUCCESS.getValue();
    }

    public static boolean isAllowedReturn(int state) {
        return state == WAIT_DELIVER.getValue() || state == PART_DELIVERED.getValue() || state == WAIT_RECEIPT.getValue() || state == TRADE_SUCCESS.getValue();
    }

    public boolean isTrue(int state){
        return this.getValue() == state;
    }
}
