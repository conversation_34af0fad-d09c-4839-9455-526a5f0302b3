package com.cfpamf.ms.mallorder.enums;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public enum OrderPerformanceTypeEnum {

    DEFAULT(0, "无"),
    DISTRIBUTION(1, "分销关系类型"),
    EMPLOYEE_BUY(2, "员工自下单类型"),
    COMMODITY_SHARING(3, "分享推荐类型"),
    REGISTER_RECOMMEND(4, "注册推荐类型"),
    SETTLED_RECOMMEND(5, "店铺引荐类型"),
    STORE_LOCATION(6, "店铺所在地类型"),
    AGRIC_MANAGE(7,"农服管护关系"),
    LOAN_MANAGE(8,"信贷管护关系");



    OrderPerformanceTypeEnum(Integer code, String desc) {
        this.code = code ;
        this.desc   = desc  ;
    }

    private final Integer code;

    private final String desc;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static OrderPerformanceTypeEnum parseEnum(Integer code){
        return Arrays.stream(OrderPerformanceTypeEnum.values()).filter(x -> x.getCode().equals(code)).findFirst().orElse(null);
    }

    public static List<Integer> collectCode() {
        return Arrays.stream(OrderPerformanceTypeEnum.values()).map(OrderPerformanceTypeEnum::getCode).collect(Collectors.toList());
    }

    public static String parseDesc(Integer code){
        OrderPerformanceTypeEnum stateEnum = parseEnum(code);
        if (!Objects.isNull(stateEnum)) {
            return stateEnum.getDesc();
        }
        return null;
    }

}
