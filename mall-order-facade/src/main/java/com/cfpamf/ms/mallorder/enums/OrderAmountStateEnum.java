package com.cfpamf.ms.mallorder.enums;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public enum OrderAmountStateEnum {

    INIT("init", "初始化"),
    EFFECT("effect", "已生效");
//    ABOLISH("abolish","已废除");

    OrderAmountStateEnum(String name, String memo) {
        this.name = name;
        this.memo = memo;
    }

    private final String name;

    private final String memo;

    public String getName() {
        return name;
    }

    public String getMemo() {
        return memo;
    }

    public static OrderAmountStateEnum parseEnum(String name){
        return Arrays.stream(OrderAmountStateEnum.values()).filter(x -> x.getName().equals(name)).findFirst().orElse(null);
    }

    public static List<String> collectName() {
        return Arrays.stream(OrderAmountStateEnum.values()).map(OrderAmountStateEnum::getName).collect(Collectors.toList());
    }

    public static String parseMemo(String name){
        OrderAmountStateEnum stateEnum = parseEnum(name);
        if (!Objects.isNull(stateEnum)) {
            return stateEnum.getMemo();
        }
        return null;
    }

    public static boolean isInit(String name){
        OrderAmountStateEnum stateEnum = parseEnum(name);
        if(null == stateEnum){
            return false;
        }
        return INIT == stateEnum;
    }

    public static boolean isEffect(String name){
        OrderAmountStateEnum stateEnum = parseEnum(name);
        if(null == stateEnum){
            return false;
        }
        return EFFECT == stateEnum;
    }

}
