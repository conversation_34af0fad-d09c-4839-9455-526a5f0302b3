package com.cfpamf.ms.mallorder.api.feign;

import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.po.Order;
import com.cfpamf.ms.mallorder.request.OrderExample;
import com.cfpamf.ms.mallorder.request.OrderOfflineRequest;
import com.cfpamf.ms.mallorder.vo.OrderOfflineInfoVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = OrderConst.MALL_ORDER_APPLICATION_NAME, contextId = "orderOffline")
public interface OrderOfflineFeignClient {

    String PREFIX = "/v1/feign/business/orderOffline";
    String GET_RECEIPT_INFO = "/getReceiptInfoList";

    /**
     * 更新条件获取线下补录收款信息
     *
     * @param request 查询条件信息
     * @return
     */
    @PostMapping(GET_RECEIPT_INFO)
    List<OrderOfflineInfoVO> getOrderList(@RequestBody OrderOfflineRequest request);
}
