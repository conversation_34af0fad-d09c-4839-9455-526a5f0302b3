package com.cfpamf.ms.mallorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class OrderExchangeDetailExample implements Serializable {

    private static final long serialVersionUID = -2207127810142458399L;

    @ApiModelProperty("换货申请单主键ID")
    private Integer exchangeDetailId;

    @ApiModelProperty("换货单号")
    private String exchangeSn;

    @ApiModelProperty("原订单号")
    private String orderSn;

    @ApiModelProperty("订单货品明细ID")
    private Long orderProductId;

    @ApiModelProperty("订单货品明细ID")
    private List<Long> orderProductIdIn;

    @ApiModelProperty("原商品名称")
    private String productName;

    @ApiModelProperty("原商品数量")
    private Integer productNum;

    @ApiModelProperty("售后单号")
    private String afsSn;

    @ApiModelProperty("换货后新的订单号")
    private String exchangeOrderSn;

    @ApiModelProperty("换货后商品明细")
    private Long exchangeOrderProductId;

    @ApiModelProperty("换后商品名称")
    private String exchangeProductName;

    @ApiModelProperty("换后商品数量")
    private Integer exchangeProductNum;

    @ApiModelProperty("换货应退金额")
    private BigDecimal refundMoney;

    @ApiModelProperty("换货实际退款金额")
    private BigDecimal actualRefundMoney;

    @ApiModelProperty("退还的乡助卡优惠金额")
    private BigDecimal xzCardAmount;

    @ApiModelProperty("平台优惠券金额")
    private BigDecimal platformVoucherAmount;

    @ApiModelProperty("平台活动金额")
    private BigDecimal platformActivityAmount;

    @ApiModelProperty("店铺活动优惠金额")
    private BigDecimal storeActivityAmount;

    @ApiModelProperty("店铺优惠券优惠金额")
    private BigDecimal storeVoucherAmount;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "是否可用:1-是 0-否")
    private Integer enabledFlag;
}
