package com.cfpamf.ms.mallorder.enums;

import com.alibaba.fastjson.annotation.JSONType;
import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * 支付方式展示状态
 */
@AllArgsConstructor
@Getter
@JSONType(serializeEnumAsJavaBean = true)
public enum PayWayShowStatusEnum {
    ENABLE(1, "可用"),
    BELOW(2, "余额不足"),
    UNABLE(3, "不可用");

    Integer value;
    String desc;

    public static PayWayShowStatusEnum valueOf(int value) {
        for (PayWayShowStatusEnum ps : PayWayShowStatusEnum.values()) {
            if (value == ps.value) {
                return ps;
            }
        }
        return null;
    }
}
