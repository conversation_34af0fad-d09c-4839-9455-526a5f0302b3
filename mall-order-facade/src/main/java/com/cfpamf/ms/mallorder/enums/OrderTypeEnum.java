package com.cfpamf.ms.mallorder.enums;

import com.slodon.bbc.core.exception.BusinessException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 订单类型枚举
 *
 * @version V1.0
 * @author: maoliang
 * @date: 2020/09/08
 */
@AllArgsConstructor
@Getter
public enum OrderTypeEnum {

    NORMAL(1, "普通订单"),
    ORDER_TYPE_5(5, "预占订单"),
    ORDER_TYPE_6(6, "线下订单"),
    ORDER_TYPE_7(7, "配销订单"),
    // 未使用 ORDER_TYPE_8(8, "卡券订单"),

    FULL_GIFT(11, "满赠订单"),
    GROUP_BUYING_GIFT(12, "赠品订单"),
    COMBINATION(13, "组合订单"),
    REBATE_GIFT(14, "返利赠品订单"),

    SPELL_GROUP(102, "拼团订单"),
    /**
     * 生产未使用
     */
    PRE_SELL(103, "预售订单"),
    SECONDS_KILL(104, "秒杀订单"),
    /**
     * 生产未使用
     */
    LADDER_GROUP(105, "阶梯团订单"),
    NEW_PEOPLE(106, "专享订单"),
    PRE_SELL_DEPOSIT(107, "预付订金订单"),
    ;

    Integer value;
    String desc;

    /**
     * 获取枚举值
     * @param value
     * @return
     */
    public static OrderTypeEnum getValue(int value) {
        for (OrderTypeEnum ot : OrderTypeEnum.values()) {
            if (value == ot.getValue()) {
                return ot;
            }
        }
        return null;
    }
    /**
     * 获取枚举值
     * @param value
     * @return
     */
    public static OrderTypeEnum valueOf (Integer value) {
        for (OrderTypeEnum e : OrderTypeEnum.values ()) {
            if (Objects.equals(value, e.value)) {
                return e;
            }
        }
        return null;
    }
    /**
     * 获取枚举值
     * @param desc
     * @return
     */
    public static OrderTypeEnum getByDesc (String desc) {
        for (OrderTypeEnum e : OrderTypeEnum.values ()) {
            if (Objects.equals(desc, e.getDesc())) {
                return e;
            }
        }
        throw new BusinessException("订单类型不存在");
    }
    /**
     * 是否预售订单 
     * @param value
     * @return
     */
    public static boolean isPresell(Integer value){
         return Objects.equals(PRE_SELL_DEPOSIT.value, value);
     }
    
    /**
     * 是否线下补录订单 
     * @param value
     * @return
     */
    public static boolean isOfflineAll(OrderTypeEnum value){
         return ORDER_TYPE_5==value || ORDER_TYPE_6==value ;
    }

    /**
     * 是否线下补录订单
     * @param value
     * @return
     */
    public static boolean isOfflineAll(Integer value){
        return isOfflineAll(valueOf(value));
    }

    /**
     * 是否赠品订单
     * @param orderTypeEnum
     * @return
     */
    private static boolean isGiftAll(OrderTypeEnum orderTypeEnum) {
        return GROUP_BUYING_GIFT == orderTypeEnum || REBATE_GIFT == orderTypeEnum ;
    }

    /**
     * 是否赠品订单
     * @param value
     * @return
     */
    public static boolean isGiftAll(Integer value){
        return isGiftAll(valueOf(value));
    }

    /**
     * 是否组合商品下单
     * @param value
     * @return
     */
    public static boolean isCombination(OrderTypeEnum value){
        return COMBINATION == value;
    }
    
    /**
     * 是否线下补录订单 
     * @param value
     * @return
     */
    public static boolean isOffline(OrderTypeEnum value){
         return ORDER_TYPE_6==value ;
    }

    /**
     * 是否预占补录订单
     * @param value
     * @return
     */
    public static boolean isPreOccupiedOrder(Integer value){
        return Objects.equals(ORDER_TYPE_5.getValue(), value);
    }

    /**
     * 是否线下补录订单 
     * @param value
     * @return
     */
    public static boolean isOffline(Integer value){
         return isOffline(valueOf(value));
     }

    /**
     * 是否活动类型
     * @param orderType
     * @return
     */
     public static boolean isPromotionType(OrderTypeEnum orderType){
         if(null == orderType){
             return false;
         }
         List<OrderTypeEnum> promotionType = Arrays.asList(
                 SPELL_GROUP,
                 LADDER_GROUP,
                 PRE_SELL,
                 SECONDS_KILL,
                 NEW_PEOPLE,
                 PRE_SELL_DEPOSIT);
         return promotionType.contains(orderType);
     }

    public static boolean isPromotionType(Integer orderType){
        OrderTypeEnum orderTypeEnum = OrderTypeEnum.valueOf(orderType);
        if (Objects.isNull(orderTypeEnum)){
            return false;
        }

        return isPromotionType(orderTypeEnum);
    }
}
