package com.cfpamf.ms.mallorder.request;

import com.slodon.bbc.core.response.PagerInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 订单example
 */
@Data
public class OrderExample implements Serializable {

    private static final long serialVersionUID = 1413935572667961795L;
    /**
     * 用于编辑时的重复判断
     */
    private Integer orderIdNotEquals;

    /**
     * 用户ID
     */
    private String userNo;

    /**
     * 用户编号(手机号)
     */
    private String userMobile;

    /**
     * 用于批量操作
     */
    private String orderIdIn;

    /**
     * 订单id
     */
    private Integer orderId;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 订单号,用于模糊查询
     */
    private String orderSnLike;

    /**
     * 用于批量操作
     */
    private String orderSnIn;

    /**
     * 支付单号
     */
    private String paySn;

    /**
     * 支付单号,用于模糊查询
     */
    private String paySnLike;

    /**
     * 父订单号，无需拆单时，父订单号=订单号
     */
    private String parentSn;

    /**
     * 父订单号，无需拆单时，父订单号=订单号,用于模糊查询
     */
    private String parentSnLike;

    /**
     * 商家ID
     */
    private Long storeId;

    /**
     * 商家名称
     */
    private String storeName;

    /**
     * 引荐商户名称
     */
    private String recommendStoreName;

    /**
     * 引荐商户ID
     */
    private String recommendStoreId;

    /**
     * 商家名称,用于模糊查询
     */
    private String storeNameLike;

    /**
     * 买家name
     */
    private String memberName;

    /**
     * 买家name,用于模糊查询
     */
    private String memberNameLike;

    /**
     * 买家ID
     */
    private Integer memberId;

    /**
     * 客户编码
     */
    private String customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 大于等于开始时间
     */
    private Date payTimeAfter;

    /**
     * 小于等于结束时间
     */
    private Date payTimeBefore;

    /**
     * 大于等于开始时间
     */
    private Date payUpdateTimeAfter;

    /**
     * 小于等于结束时间
     */
    private Date payUpdateTimeBefore;

    /**
     * 大于等于开始时间
     */
    private Date createTimeAfter;

    /**
     * 小于等于结束时间
     */
    private Date createTimeBefore;

    /**
     * 大于等于开始时间
     */
    private Date finishTimeAfter;

    /**
     * 小于等于结束时间
     */
    private Date finishTimeBefore;

    /**
     * 订单状态：0-已取消；5-未支付订金；10-未付款订单；15-付款中; 20-已付款；30-已发货；40-已完成;50-已关闭
     */
    private Integer orderState;

    /**
     * orderStateIn，用于批量操作
     */
    private String orderStateIn;

    /**
     * orderStateIn，用于批量操作
     */
    private List<Integer> orderStateList;

    /**
     * orderStateNotIn，用于批量操作
     */
    private String orderStateNotIn;

    /**
     * orderStateNotEquals，用于批量操作
     */
    private Integer orderStateNotEquals;

    /**
     * 支付方式名称，参考OrderPaymentConst类
     */
    private String paymentName;

    /**
     * 支付方式名称，参考OrderPaymentConst类,用于模糊查询
     */
    private String paymentNameLike;

    /**
     * 支付方式code, 参考OrderPaymentConst类
     */
    private String paymentCode;

    /**
     * 商品金额，等于订单中所有的商品的单价乘以数量之和
     */
    private BigDecimal goodsAmount;

    /**
     * 物流费用
     */
    private BigDecimal expressFee;

    /**
     * 活动优惠总金额 （= 店铺优惠券 + 平台优惠券 + 活动优惠【店铺活动 + 平台活动】 + 积分抵扣金额）
     */
    private BigDecimal activityDiscountAmount;

    /**
     * 活动优惠明细，json存储（对应List<PromotionInfo>）
     */
    private String activityDiscountDetail;

    /**
     * 订单总金额(用户需要支付的金额)，等于商品总金额＋运费-活动优惠金额总额activity_discount_amount
     */
    private BigDecimal orderAmount;

    /**
     * 余额账户支付总金额
     */
    private BigDecimal balanceAmount;

    /**
     * 三方支付金额
     */
    private BigDecimal payAmount;

    /**
     * 乡助卡金额
     */
    private BigDecimal xzCardAmount;

    /**
     * 乡助卡运费优惠金额
     */
    private BigDecimal xzCardExpressFeeAmount;

    /**
     * 组合支付方式
     */
    private String composePayName;

    /**
     * 放款成功
     */
    private Date lendingSuccessTime;

    /**
     * 退款的金额，订单没有退款则为0
     */
    private BigDecimal refundAmount;

    /**
     * 积分抵扣金额
     */
    private BigDecimal integralCashAmount;

    /**
     * 订单使用的积分数量
     */
    private Integer integral;

    /**
     * 收货人
     */
    private String receiverName;

    /**
     * 收货人,用于模糊查询
     */
    private String receiverNameLike;

    /**
     * 省市区组合
     */
    private String receiverAreaInfo;

    /**
     * 收货人详细地址
     */
    private String receiverAddress;

    /**
     * 收货人手机号
     */
    private String receiverMobile;

    /**
     * 延长多少天收货
     */
    private Integer delayDays;

    /**
     * 物流公司ID
     */
    private Integer expressId;

    /**
     * 物流公司
     */
    private String expressName;

    /**
     * 物流公司,用于模糊查询
     */
    private String expressNameLike;

    /**
     * 快递单号
     */
    private String expressNumber;

    /**
     * 快递单号,用于模糊查询
     */
    private String expressNumberLike;

    /**
     * 是否评价:1.未评价,2.部分评价,3.全部评价
     */
    private Integer evaluateState;

    /**
     * evaluateStateIn，用于批量操作
     */
    private String evaluateStateIn;

    /**
     * evaluateStateNotIn，用于批量操作
     */
    private String evaluateStateNotIn;

    /**
     * evaluateStateNotEquals，用于批量操作
     */
    private Integer evaluateStateNotEquals;

    /**
     * 订单类型：1-普通订单；其他直接存活动类型（具体类型查看ActivityConst）
     */
    private Integer orderType;
    /**
     * 订单类型：1-普通订单；其他直接存活动类型（具体类型查看ActivityConst）
     */
    private String orderTypeIn;

    /**
     * 订单类型：1-普通订单；其他直接存活动类型（具体类型查看ActivityConst）
     */
    private String orderTypeNotIn;

    /**
     * 锁定状态：0-是正常, 大于0是锁定状态，用户申请退款或退货时锁定状态加1，处理完毕减1。锁定后不能操作订单
     */
    private Integer lockState;

    /**
     * lockStateIn，用于批量操作
     */
    private String lockStateIn;

    /**
     * lockStateNotIn，用于批量操作
     */
    private String lockStateNotIn;

    /**
     * lockStateNotEquals，用于批量操作
     */
    private Integer lockStateNotEquals;

    /**
     * 删除状态：0-未删除；1-放入回收站；2-彻底删除
     */
    private Integer deleteState;

    /**
     * deleteStateIn，用于批量操作
     */
    private String deleteStateIn;

    /**
     * deleteStateNotIn，用于批量操作
     */
    private String deleteStateNotIn;

    /**
     * deleteStateNotEquals，用于批量操作
     */
    private Integer deleteStateNotEquals;

    /**
     * 取消原因
     */
    private String refuseReason;

    /**
     * 取消原因,用于模糊查询
     */
    private String refuseReasonLike;

    /**
     * 取消备注
     */
    private String refuseRemark;

//    /**
//     * 是否已生成电子面单 1 - 已生成 2 - 未生成
//     */
//    private Integer isGenerateFacesheet;

    /**
     * 是否结算：0-未结算；1-已结算
     */
    private Integer isSettlement;

    /**
     * 退款状态：1-退款中
     */
    private Integer orderReturnState;

    /**
     * 排序条件，条件之间用逗号隔开，如果不传则按照orderId倒序排列
     */
    private String orderBy;

    /**
     * 分组条件
     */
    private String groupBy;

    /**
     * 分页信息
     */
    private PagerInfo pager;

    /**
     * 商品名称，用于模糊查询
     */
    private String goodsNameLike;

    /**
     * 发货时间-截止
     */
    private String deliverTimeEnd;


    @ApiModelProperty(value = "金融规则编号")
    private String financeRuleCode;

    @ApiModelProperty("金融规则标签")
    private String ruleTag;

    /**
     * 支付放款状态
     */
    private Integer loanPayState;

    /**
     * 下单操作渠道：H5-浏览器H5；APP-乡助APP；WE_CHAT-微信浏览器；MINI_PRO-小程序；OMS-运管物资
     */
    private String channel;

    /**
     * 机构
     */
    private String branchNameLike;

    /**
     * 区域
     */
    private String areaNameLike;

    /**
     * 订单模式：1-C端店铺街，2-B端采购中心
     */
    private Integer orderPattern;

    /**
     * orderPatternNotIn，用于批量操作
     */
    private String orderPatternNotIn;

    /**
     * newOrder，是否新订单标识
     */
    private Boolean newOrder;

    @ApiModelProperty("履约模式，0-内部履约模式，1-供应商履约模式")
    private Integer performanceMode;

    @ApiModelProperty("履约服务：0-常规，1-供应商履约，2-安装服务，3-自提，4-农机厂商")
    private String performanceService;

    @ApiModelProperty("履约服务：0-常规，1-供应商履约，2-安装服务，3-自提，4-农机厂商")
    private String performanceServiceNotIn;

    @ApiModelProperty("客户经理")
    private String manager;

    @ApiModelProperty("客户经理名称")
    private String managerNameLike;

    @ApiModelProperty("客户经理名集合")
    private List<String> managerNameList;

    @ApiModelProperty("分支编码")
    private List<String> branchCodeIn;

    @ApiModelProperty("供应商编码")
    private String supplierCode;

    @ApiModelProperty("采购订单-入驻类型，见PurchaseOrderSettledTypeEnum")
    private String settledType;

    @ApiModelProperty("采购订单-企业名称")
    private String companyNameLike;

    @ApiModelProperty("采购订单-店铺所属分支编码")
    private List<String> storeBranchCodeIn;

    @ApiModelProperty("售后截止时间")
    private Date afterSalesDeadline;

    @ApiModelProperty("售后截止时间在该时间之前")
    private Date afterSalesDeadlineBefore;

    @ApiModelProperty("售后截止时间在该时间之后")
    private Date afterSalesDeadlineAfter;

    @ApiModelProperty("客户确认状态code：0-无需确认，1-草稿，2-待确认，3-已确认 4-拒绝 见枚举CustomerConfirmStatusEnum")
    private Integer customerConfirmStatus;

    @ApiModelProperty("换货标识：0=普通订单；1=被换货的订单 2=换货后新生成的订单")
    private Integer exchangeFlag;

    @ApiModelProperty("换货标识：0=普通订单；1=被换货的订单 2=换货后新生成的订单")
    private Integer exchangeFlagNotEquals;

    @ApiModelProperty("订单换货列表标识，1-标识是从订单换货列表过来的")
    private Integer orderExchangeFlag;

    @ApiModelProperty("商品三级分类ID")
    private List<Integer> goodsCategoryIdIn;

    @ApiModelProperty("订单号集合")
    private List<String> orderSnList;

    @ApiModelProperty("站长userNo")
    private String stationMaster;

    @ApiModelProperty("站长姓名(电话)")
    private String stationMasterNameLike;

    @ApiModelProperty(value = "收款标签，默认未收款1，0-默认值（普通订单），1-待收款，2-部分收款，3-已收款")
    private Integer paymentTag;

    @ApiModelProperty(value = "线下补录订单开票标签 0-未开票，1-已开票")
    private Integer invoiceStatus;

    @ApiModelProperty("线下补录订单收款截止日期")
    private Date overdueTimeAfter;

    @ApiModelProperty("线下补录订单收款截止日期")
    private Date overdueTimeBefore;

    @ApiModelProperty(value = "线下补录订单是否逾期 默认 0 - 未逾期， 1 - 已逾期")
    private Integer overdueFlag;

    @ApiModelProperty("线下补录订单买方供应商编码")
    private String buyerSupplierCode;

    @ApiModelProperty(value = "自提点id")
    private Long pointId;

    @ApiModelProperty(value = "签收确认书上传状态,0-未上传,1-已上传")
    private Integer receiveMaterialStatus;

    @ApiModelProperty(value = "业绩归属分支编号")
    private String performanceBranchCode;

    @ApiModelProperty(value = "业绩归属分支编号列表")
    private List<String> performanceBranchCodeList;

    @ApiModelProperty(value = "业绩归属人姓名")
    private String performanceBelongerName;

    @ApiModelProperty(value = "业绩归属人姓名列表")
    private List<String> performanceBelongerNameList;

    @ApiModelProperty(value = "业绩归属人工号")
    private String performanceBelongerEmployeeNo;

    @ApiModelProperty(value = "业绩归属人工号列表")
    private List<String> performanceBelongerEmployeeNoList;
}