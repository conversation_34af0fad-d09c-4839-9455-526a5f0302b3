package com.cfpamf.ms.mallorder.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum CustomerConfirmStatusEnum {

	NO_NEED_CONFIRM(0, "无需确认"),
	DRAFT(1, "草稿"),
	UNCONFIRMED(2, "待确认"),
	CONFIRM(3, "已确认"),
	REFUSE(4, "拒绝"),
	CLOSED(5, "已关闭");

	Integer value;
	String desc;


	public static boolean isDraft(int value) {
		return value == DRAFT.value;
	}

	public static boolean isUnconfirmed(int value) {
		return value == UNCONFIRMED.value;
	}
	
	public static boolean isConfirm(int value) {
		return value == CONFIRM.value;
	}
	
	public static boolean isNoNeedConfirm(int value) {
		return value == NO_NEED_CONFIRM.value;
	}

	public static boolean isDraftOrUnconfirmed(int value) {
		return value == UNCONFIRMED.value || value == DRAFT.value;
	}


}
