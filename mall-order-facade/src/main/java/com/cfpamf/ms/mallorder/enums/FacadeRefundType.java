package com.cfpamf.ms.mallorder.enums;

import com.alibaba.fastjson.annotation.JSONType;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 退款类型枚举
 *
 * @version V1.0
 * @author: maoliang
 * @date: 2020/7/17
 */
@AllArgsConstructor
@Getter
@JSONType(serializeEnumAsJavaBean = true)
public enum FacadeRefundType {

    OTHER(0, "其他"),
    ACCOUNT_REGULATION(2, "调账退款"),
    ASSIST_PAYMENT(1, "代还退款"),
    RESTORE_LIMIT(3, "恢复额度"),
    WITHDRAW_DEPOSIT(4, "提现退款"),
    APLIPAY_REFUND(5, "支付宝退款"),
    WXPAY_REFUND(6, "微信退款"),
    AGREED_REFUND(7, "协议退款"),
    OFFLINE_REFUND(8, "线下退款"),
    CARD_VOUCHER_REFUND(9, "卡券退款"),
    BANK_PAY_REFUND(10, "银行卡退款"),
    CARD_REFUND(11, "乡助卡退款"),
    TRANSFER_REFUND(12,"银行卡汇款退款"),
    COMBINATION_REFUND(13, "组合退款"),
    ZERO_YUAN_REFUND(14, "0元退款"),
    SQUARE_OFFLINE_REFUND(15, "商家线下退款");

    Integer value;
    String desc;

    public Integer getValue() {
        return value;
    }

    public static FacadeRefundType value(int value) {
        for (FacadeRefundType ps : FacadeRefundType.values()) {
            if (value == ps.value) {
                return ps;
            }
        }
        return null;
    }
}
