package com.cfpamf.ms.mallorder.request.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> 2021/11/24.
 */
@Data
public class ErpOrderDeliveryRequest {

    @NotBlank(message = "订单号 不能为空")
    @ApiModelProperty(value = "订单号")
    private String orderSn;

    @ApiModelProperty(value = "公司ID")
    @NotNull(message = "公司ID不能为空")
    private Long storeId;

    @NotEmpty(message = "订单商品ID集合 不能为空")
    @ApiModelProperty(value = "订单商品ID集合")
    private List<Long> orderProductIds;

    @NotNull(message = "发货类型 不能为空")
    @ApiModelProperty(value = "发货类型：0-物流发货，1-无需物流")
    private Integer deliverType;

    @ApiModelProperty(value = "物流公司ID(物流发货时必填)")
    private Integer expressId;

    @ApiModelProperty(value = "物流公司名称")
    private String expressName;

    @ApiModelProperty(value = "物流公司编号")
    private String expressCompanyCode;

    @ApiModelProperty(value = "物流单号(物流发货时必填)")
    private String expressNumber;

    @ApiModelProperty(value = "联系人(无需物流时必填)")
    private String deliverName;

    @ApiModelProperty(value = "联系人电话(无需物流时必填)")
    private String deliverMobile;

}
