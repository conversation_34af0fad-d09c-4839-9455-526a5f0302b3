package com.cfpamf.ms.mallorder.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@AllArgsConstructor
@Getter
public enum ChannelEnum {

    CHANNEL_YZH ("YZH", "云中鹤"),
    CHANNEL_ERP ("ERP", "ERP"),
    CHANNEL_WMS ("WMS", "WMS仓储服务"),
    CHANNEL_HSQ ("HSQ", "好食期"),
    HOME_SERVICE ("HOME_SERVICE", "到家服务"),
    MFR_DEALER ("MFR_DEALER", "农机厂商"),
    BAPP("BAPP", "BAPP"),
    XXAPP("XXAPP","乡信APP"),
    XX_MINI_PRO("XX_MINI_PRO","乡信小程序");

    String value;
    String desc;


    public static ChannelEnum getValue (String value) {
        for (ChannelEnum ps : ChannelEnum.values ()) {
            if (Objects.equals (value, ps.value)) {
                return ps;
            }
        }
        return null;
    }

    public static String getDescByValue(String value) {
        for(ChannelEnum channel : ChannelEnum.values()) {
            if (channel.value.equals(value)) {
                return channel.desc;
            }
        }
        return null;
    }
}
