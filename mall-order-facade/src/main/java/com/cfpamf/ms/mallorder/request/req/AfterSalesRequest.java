package com.cfpamf.ms.mallorder.request.req;

import com.slodon.bbc.core.response.PagerInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 乡信协助下单售后查询入参
 */
@Data
@ApiModel(value = "售后申请查询入参", description = "售后申请查询入参")
public class AfterSalesRequest implements Serializable {

    private static final long serialVersionUID = 318275912136L;

    @ApiModelProperty(value = "会员id集合")
    @NotEmpty(message = "会员id集合不能为空")
    private List<Integer> memberIdList;

    @ApiModelProperty(value = "退款方式：1-仅退款 2-退货退款")
    private Integer returnType;

    @ApiModelProperty("分页参数")
    private PagerInfo pager = new PagerInfo(10, 1);
}
