package com.cfpamf.ms.mallorder.request.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 订单快照记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-28
 */
@Data
public class OrderSnapshotExample implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("ID")
    private Integer snapshotId;

    @ApiModelProperty(value = "业务单号")
    private String bizSn;

    @ApiModelProperty(value = "子业务单号")
    private String subBizSn;

    @ApiModelProperty(value = "业务类型")
    private String bizType;

}
