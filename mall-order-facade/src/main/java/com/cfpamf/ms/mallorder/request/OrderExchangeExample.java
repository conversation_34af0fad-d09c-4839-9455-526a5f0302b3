package com.cfpamf.ms.mallorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class OrderExchangeExample implements Serializable {

    private static final long serialVersionUID = -2207127810142458399L;

    @ApiModelProperty("换货申请单主键ID")
    private Integer exchangeOrderId;

    @ApiModelProperty("换货单号")
    private String exchangeSn;

    @ApiModelProperty("买家ID")
    private Integer memberId;

    @ApiModelProperty("商家ID")
    private Long storeId;

    @ApiModelProperty("换货原因")
    private String exchangeReason;

    @ApiModelProperty("是否需要买家确认:1-是 0-否")
    private Integer buyerConfirmFlag;

    @ApiModelProperty("换货订单状态：0-【待审批】、1-【撤销换货】、2-【拒绝换货】、3-【同意换货】、4-【换货完成】、5-【换货取消】 6-【换货关闭】")
    private Integer exchangeOrderState;

    @ApiModelProperty("申请人ID")
    private Long applicantId;

    @ApiModelProperty("'申请人名称'")
    private String applicantName;

    @ApiModelProperty("申请人角色(1-系统管理员，2-商户，3-会员）")
    private Integer applicantRole;

    @ApiModelProperty("审批人ID")
    private Long approverId;

    @ApiModelProperty("审批人名称")
    private String approverName;

    @ApiModelProperty("审批人角色(1-系统管理员，2-商户，3-会员）")
    private Integer approverRole;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "是否可用:1-是 0-否")
    private Integer enabledFlag;
}
