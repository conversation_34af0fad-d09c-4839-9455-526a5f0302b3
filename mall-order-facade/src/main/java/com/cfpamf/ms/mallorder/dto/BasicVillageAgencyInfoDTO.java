package com.cfpamf.ms.mallorder.dto;

import com.cfpamf.framework.autoconfigure.common.exception.MSException;
import com.cfpamf.ms.mallorder.enums.UserIdentityTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Objects;

@Data
@ApiModel(value="BasicVillageAgencyInfoDTO对象", description="基本用户信息DTO")
public class BasicVillageAgencyInfoDTO extends BasicUserInfoDTO implements Serializable {

    private static final long serialVersionUID = 522092116211665751L;
    @ApiModelProperty(value = "实名村代/站长编号")
    private String agentNo;
    @ApiModelProperty(value = "实名村代/站长姓名")
    private String agentName;
    @ApiModelProperty(value = "实名村代/站长唯一标识")
    private String agentIdentity;
    @ApiModelProperty(value = "实名村代/站长联系方式")
    private String agentPhoneNum;
    @ApiModelProperty(value = "上级员工信息")
    private BasicEmployeeInfoDTO superiorEmployeeDTO;

    @Override
    public UserIdentityTypeEnum getUserIdentityType() {
        return UserIdentityTypeEnum.NORMAL_VILLAGE_AGENCY;
    }

    public BasicVillageAgencyInfoDTO(BasicUserInfoDTO basicUserInfoDTO) {
        this.userCode = basicUserInfoDTO.getUserCode();
        this.userName = basicUserInfoDTO.getUserName();
        this.userIdentity = basicUserInfoDTO.getUserIdentity();
        this.userPhoneNum = basicUserInfoDTO.getUserPhoneNum();
    }

    @Override
    public String getBelongerEmployeeNo() {
        if (Objects.nonNull(superiorEmployeeDTO)) {
            return superiorEmployeeDTO.getEmployeeNo();
        }
        throw new MSException("9901002","获取上级员工信息为空");
    }

    @Override
    public String getBelongerName() {
        if (Objects.nonNull(superiorEmployeeDTO)) {
            return superiorEmployeeDTO.getEmployeeName();
        }
        throw new MSException("9901002","获取上级员工信息为空");
    }
}
