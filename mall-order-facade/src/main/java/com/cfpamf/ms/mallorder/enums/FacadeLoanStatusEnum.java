package com.cfpamf.ms.mallorder.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum FacadeLoanStatusEnum {

    DEFAULT(0, "未知"),
    WAIT_APPLY(10, "待申请"),
    DEAL_APPLY(20, "申请中"),
    APPLY_SUCCESS(30, "申请成功"),
    APPLY_FAIL(40, "申请失败"),
    WAIT_LENDING(50, "待起息"),
    DEAL_LENDING(60, "起息中"),
    LENDING_SUCCESS(70, "起息成功"),
    LENDING_FAIL(80, "起息失败");

    public Integer getValue(){
        return this.value;
    }

    public String getDesc() {
        return this.desc;
    }

    Integer value;
    String desc;

    public static FacadeLoanStatusEnum valueOf(int value) {
        for (FacadeLoanStatusEnum ps : FacadeLoanStatusEnum.values()) {
            if (value == ps.value) {
                return ps;
            }
        }
        return null;
    }

    public boolean isTrue(int value){
        return this.getValue() == value;
    }
}
