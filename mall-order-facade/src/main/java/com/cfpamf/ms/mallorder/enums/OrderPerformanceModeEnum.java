package com.cfpamf.ms.mallorder.enums;


import com.alibaba.fastjson.annotation.JSONType;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 订单履约模式定义
 *
 * <AUTHOR>
 * @date 2022/07/05 17:16
 * @return
 */
@AllArgsConstructor
@Getter
@JSONType(serializeEnumAsJavaBean = true)
public enum OrderPerformanceModeEnum implements IEnum<Integer> {

    PERFORMANCE_MODE_COMMON(0, "常规"),
    PERFORMANCE_MODE_SUPPLIER(1, "供应商"),
    PERFORMANCE_MODE_HOME_SERVICE(2, "到家服务"),
    PERFORMANCE_MODE_SELF(3, "自提"),
    PERFORMANCE_MODE_MFR_DEALER(4, "农机厂商"),
    PERFORMANCE_MODE_FUNDS_BORROW(5, "现款现货"),
    PERFORMANCE_MODE_FULL_GIFT(6, "活动赠品"),
    ;

    Integer value;
    String desc;

    public static OrderPerformanceModeEnum valueOf(int value) {
        for (OrderPerformanceModeEnum ps : OrderPerformanceModeEnum.values()) {
            if (value == ps.value) {
                return ps;
            }
        }
        return PERFORMANCE_MODE_COMMON;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }

    public static List<OrderPerformanceModeEnum> parseValues(String valueStr) {
        if (valueStr.length() < 3) {
            throw new IllegalArgumentException("待解析的订单履约模式参数长度小于3");
        }
        Set<String> performanceModeSet = Arrays.stream(valueStr.substring(1, valueStr.length() - 1).split(","))
                .map(String::trim).collect(Collectors.toSet());

        List<OrderPerformanceModeEnum> result = new ArrayList<>();

        for (OrderPerformanceModeEnum itemEnum : OrderPerformanceModeEnum.values()) {
            if (performanceModeSet.contains(itemEnum.getValue().toString())) {
                result.add(itemEnum);
            }
        }
        return result;
    }

    public static boolean isContain(String valueStr, OrderPerformanceModeEnum performanceMode) {
        Set<String> performanceModeSet = Arrays.stream(valueStr.substring(1, valueStr.length() - 1).split(","))
                .map(String::trim).collect(Collectors.toSet());
        return performanceModeSet.contains(performanceMode.getValue().toString());
    }
}
