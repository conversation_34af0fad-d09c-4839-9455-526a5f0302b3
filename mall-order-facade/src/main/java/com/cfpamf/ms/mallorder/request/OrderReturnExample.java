package com.cfpamf.ms.mallorder.request;

import com.slodon.bbc.core.response.PagerInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 订单退货表example
 */
@Data
public class OrderReturnExample implements Serializable {
    private static final long serialVersionUID = -2418615448608772333L;
    /**
     * 用于编辑时的重复判断
     */
    private Integer returnIdNotEquals;

    /**
     * 用于批量操作
     */
    private String returnIdIn;

    /**
     * 退货id
     */
    private Integer returnId;

    /**
     * 售后服务单号
     */
    private String afsSn;

    /**
     * 售后服务单号,用于模糊查询
     */
    private String afsSnLike;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 订单号,用于模糊查询
     */
    private String orderSnLike;

    /**
     * 店铺id
     */
    private Long storeId;

    /**
     * 店铺id
     */
    private String storeIdIn;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 店铺名称,用于模糊查询
     */
    private String storeNameLike;

    /**
     * 用户ID
     */
    private Integer memberId;

    /**
     * 用户ID
     */
    private String memberIdIn;
    /**
     * 用户名称
     */
    private String memberName;

    /**
     * 用户名称,用于模糊查询
     */
    private String memberNameLike;

    /**
     * 退款方式：0-原路退回，1-退账户余额
     */
    private Integer returnMoneyType;

    /**
     * 退款类型：1==仅退款 2=退货退款
     */
    private Integer returnType;

    /**
     * 退款类型：1==仅退款 2=退货退款 3=换货退款
     */
    private String returnTypeIn;

    /**
     * 退货数量
     */
    private Integer returnNum;

    /**
     * 退款金额
     */
    private BigDecimal returnMoneyAmount;

    /**
     * 退还积分
     */
    private Integer returnIntegralAmount;

    /**
     * 扣除积分数量（购物赠送的积分）
     */
    private Integer deductIntegralAmount;

    /**
     * 退还运费的金额（用于待发货订单仅退款时处理）
     */
    private BigDecimal returnExpressAmount;

    /**
     * 退还优惠券编码（最后一单退还优惠券）
     */
    private String returnVoucherCode;

    /**
     * 平台对应类别的佣金比例，0-1数字，
     */
    private BigDecimal commissionRate;

    /**
     * 佣金金额
     */
    private BigDecimal commissionAmount;

    // 平台服务费
    private BigDecimal serviceFee;

    // 代运营服务费
    private BigDecimal thirdpartnarFee;

    // 订单佣金
    private BigDecimal orderCommission;

    // 业务佣金
    private BigDecimal businessCommission;

    /**
     * 退货退款状态：100-买家申请仅退款；101-买家申请退货退款；102-买家退货给商家；200-商家同意退款申请；201-商家同意退货退款申请；202-商家拒绝退款申请(退款关闭/拒收关闭)；203-商家确认收货；300-平台确认退款(已完成)
     */
    private Integer state;

    /**
     * stateIn，用于批量操作
     */
    private String stateIn;

    /**
     * stateNotIn，用于批量操作
     */
    private String stateNotIn;

    /**
     * stateNotEquals，用于批量操作
     */
    private Integer stateNotEquals;

    /**
     * 大于等于开始时间
     */
    private Date applyTimeAfter;

    /**
     * 小于等于结束时间
     */
    private Date applyTimeBefore;

    /**
     * 大于等于开始时间
     */
    private Date completeTimeAfter;

    /**
     * 小于等于结束时间
     */
    private Date completeTimeBefore;

    /**
     * 拒绝原因
     */
    private String refuseReason;

    /**
     * 拒绝原因,用于模糊查询
     */
    private String refuseReasonLike;

    /**
     * 排序条件，条件之间用逗号隔开，如果不传则按照returnId倒序排列
     */
    private String orderBy;

    /**
     * 分组条件
     */
    private String groupBy;

    /**
     * 分页信息
     */
    private PagerInfo pager;

    /**
     * 订单货品id
     */
    private Long orderProductId;

    /**
     * 发货时间
     */
    private String deliverTimeEnd;

    /**
     * 实际退款金额
     */
    private BigDecimal actualReturnMoneyAmount;

    /**
     * 客户承担金额
     */
    private BigDecimal customerAssumeAmount;

    /**
     * 退款类型
     */
    private Integer refundType;

    /**
     * 退款开始时间
     */
    private Date refundStartTime;

    /**
     * 退款结束时间
     */
    private Date refundEndTime;

    @ApiModelProperty(value = "平台审核开始时间")
    protected Date platformAuditStartTime;

    @ApiModelProperty(value = "平台审核结束时间")
    protected Date platformAuditEndTime;


    /**
     * 其他赔偿
     */
    private BigDecimal otherCompensationAmount;

    /**
     * 利息承担方
     */
    private String interestpayer;

    /**
     * 备注
     */
    private String remark;


    /*************************订单字段*************************/

    /**
     * 订单渠道
     */
    private String channel;

    /**
     * 客户编号
     */
    private String customerId;

    /**
     * 客户姓名
     */
    private String customerName;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 引荐商户ID
     */
    private String recommendStoreId;

    /**
     * 机构
     */
    private String branchNameLike;

    /**
     * 区域
     */
    private String areaNameLike;
    /**
     * 订单类型
     */
    private Integer orderType;

    /**
     * 订单模式：1-C端店铺街，2-B端采购中心
     */
    private Integer orderPattern;

    /**
     * 发起人
     */
    private String contactName;

    /**
     * 退款发起方:0-系统 1-客户 2-商家 3-平台
     */
    private Integer returnBy;

    /**
     * 用户手机号码
     */
    private String userMobile;

    @ApiModelProperty("换货单号")
    private String exchangeSn;

    @ApiModelProperty("换货订单状态：0-【待审批】、1-【撤销换货】、2-【拒绝换货】、3-【同意换货】、4-【换货完成】、5-【换货取消】 6-【换货关闭】")
    private Integer exchangeOrderState;

    /**
     * 发起退款时，商品的发货状态: 0-待发货；1-已发货
     */
    private Integer productDeliveryState;

    /**
     * 客户经理
     */
    private String manager;

    private String managerNameLike;

    @ApiModelProperty("客户经理名集合")
    private List<String> managerNameList;


    /**
     * 分支编码
     */
    private List<String> branchCodeIn;

    @ApiModelProperty("京东物流拦截状态：0-默认值 1-待拦截（需拦截）2-无需拦截 3-拦截中 4-拦截成功 5-拦截失败")
    private Integer jdInterceptStatus;

}