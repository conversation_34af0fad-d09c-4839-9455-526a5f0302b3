package com.cfpamf.ms.mallorder.constant;

/**
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/5/25 11:26
 */
public class DomainUrlConstant {
    /**
     * 主站的URL
     */
    public static final String SLD_SOCKET_URL = "http://127.0.0.1:8011/";

    /**
     * 主站的URL
     */
    public static final String SLD_API_URL = "https://jbbcadmindev.slodon.cn";

    /**
     * 静态资源的URL
     */
    public static final String SLD_STATIC_RESOURCES = "https://jbbcadmindev.slodon.cn";

    /**
     * 移动端前端地址
     */
    public static final String SLD_H5_URL = "https://jbbcdev.slodon.cn";


    /**
     * 移动端H5地址
     */
    public static final String FRONT_H5_URL = "https://h5.cdfinance.com.cn/";

    /**
     * pc前端地址
     */
    public static final String SLD_PC_URL = "https://jbbcdevpc.slodon.cn";

    /**
     * admin前端地址
     */
    public static final String SLD_ADMIN_URL = "https://jbbcadmindev.slodon.cn";

    /**
     * seller前端地址
     */
    public static final String SLD_SELLER_URL = "https://jbbcsellerdev.slodon.cn";

    /**
     * 图片资源的URL
     */
    public static final String SLD_IMAGE_RESOURCES = "https://jbbcimgdev.slodon.cn";

    /**
     * 七牛云图片资源的URL
     */
    public static final String QINIUYUN_IMAGE_RESOURCES = "https://jbbcimgdev.slodon.cn";

    /**
     * 腾讯云图片资源的URL
     */
    public static final String TENCENTCLOUD_IMAGE_RESOURCES = "https://jbbcimgdev.slodon.cn";

    /**
     * 阿里云图片资源的URL
     */
    public static final String ALIYUN_IMAGE_RESOURCES = "http://bucket33333333.oss-cn-beijing.aliyuncs.com";

    /**
     * Elastic Search URL
     */
    public static final String SLD_ES_URL = "************";

    /**
     * Elastic Search PORT
     */
    public static final Integer SLD_ES_PORT = 9200;

    /**
     * es索引名称
     */
    public static final String ES_INDEX_NAME = "slodon";

    /**
     * 积分商城es索引名称
     */
    public static final String INTEGRAL_ES_INDEX_NAME = "slodon_integral";

    /**
     * rabbitMq host
     */
    public static final String SLD_MQ_HOST = "rabbitmq.tsg.cfpamf.com";

    /**
     * rabbitMq 端口号
     */
    public static final Integer SLD_MQ_PORT = 5672;

    /**
     * 用户名
     */
    public static final String SLD_USER_NAME = "admin";

    /**
     * 密码
     */
    public static final String SLD_PASS_WORD = "donttelldev";

    /**
     * rabbitMq 名称前缀（交换机、队列名称）
     */
    public static final String SLD_MQ_NAME_PREFIX = "slodon";

    /**
     * redis host
     */
    public static final String SLD_REDIS_HOST = "r-2zef4d5b5cdab4e4.redis.rds.aliyuncs.com";

    /**
     * redis 端口
     */
    public static final Integer SLD_REDIS_PORT = 6379;

    /**
     * redis 密码
     */
    public static final String SLD_REDIS_PASSWORD = "Db123456";

    /**
     * redis 默认DB
     */
    public static final Integer SLD_REDIS_DATABASE = 99;

    /**
     * 上传图片类型，1-公共上传，2-七牛云上传，3-腾讯云上传，4-阿里云上传
     */
    public static final Integer UPLOAD_IMAGE_TYPE = 4;

    /**
     * 直播类型，1-腾讯云，2-阿里云
     */
    public static final Integer LIVE_TYPE = 2;
}

