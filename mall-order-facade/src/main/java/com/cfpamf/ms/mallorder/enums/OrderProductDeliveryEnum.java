package com.cfpamf.ms.mallorder.enums;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONType;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单商品发货状态枚举
 */
@AllArgsConstructor
@Getter
@JSONType(serializeEnumAsJavaBean = true)
public enum OrderProductDeliveryEnum implements IEnum<Integer> {

    WAIT_DELIVERY(0, "待发货"),
    DELIVERED(1, "已发货"),
    OUTBOUND(2,"出库中");

    Integer value;
    String desc;

    public static OrderProductDeliveryEnum valueOf(int value) {
        for (OrderProductDeliveryEnum ps : OrderProductDeliveryEnum.values()) {
            if (value == ps.value) {
                return ps;
            }
        }
        return null;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }

    @JsonValue
    public JSONObject toJsonString () {
        JSONObject json = new JSONObject ();
        json.put ("value", this.value);
        json.put ("desc", this.desc);
        return json;
    }
}
