package com.cfpamf.ms.mallorder.constant;

/**
 * 订单常量
 **/
public class OrderConst {


    public final static String RESULT_CODE_SUCCESS = "SUCCESS";

    public final static Integer RESULT_STATE_SUCCESS = 200;

    public final static Integer ENABLED_FLAG_Y = 1;
    public final static Integer ENABLED_FLAG_N = 0;

    public final static Integer ADMIN_ROLE = 1;
    public final static Long USER_ID_SYSTEM = 1L;
    public final static Long OPT_USER_ID = 1L;
    public final static String USER_NAME_SYSTEM = "系统";
    public final static String LOG_USER_NAME = "system";
    public final static Long LOG_USER_ID = 0L;
    public final static Integer LOG_ROLE = 1;

    public static final int STAND_MALL_BUSINESS_ChANNEL = 5;

    public static final String WAIT_PAY_ORDER_EXPIRE_REASON = "支付超时系统自动取消";
    public static final String BANK_TRANSFER_EXPIRE_REASON = "银行卡汇款转账期限已超时";

    /**
     * redisLock
     */
    public final static String LOCK_KEY = "MALL_ORDER_REDIS_LOCK";

    /**
     * 批量处理暂定 一次1000条一次
     */
    public static final int MAX_BATCH_SIZE = 1000;

    public static final String MALL_ORDER_APPLICATION_NAME = "mall-order";

    /**
     * 乡助模式店铺ID
     */
    public static final Long XZ_STORE_ID = 283303L;

    /**
     * mq连接工厂名称
     */
    public final static String MQ_FACTORY_NAME_SINGLE_PASS_ERR = DomainUrlConstant.SLD_MQ_NAME_PREFIX + "_factory_single_pass_err";//连接工厂，单一消费者，发生异常丢弃消息


    /**
     * 1-发货
     * 2-取消
     * 3-更新金额
     * 4-确认收款
     * 5-确认
     */
    public static final int DELIVER = 1;
    public static final int CANCEL = 2;
    public static final int UPDATE_AMOUNT = 3;
    public static final int SUBMIT_PAY = 4;
    public static final int CONFIRM = 5;

    /**
     * order_pay表   api_pay_state  支付状态： 0、未支付，1、已支付，2、部分支付(Andy新增)
     */
    public final static String API_PAY_STATE_0 = "0";
    public final static String API_PAY_STATE_1 = "1";
    public final static String API_PAY_STATE_2 = "2";

    /**
     * 普通订单状态  order_state  订单状态：
     * 0-已取消；
     * 5-待支付定金；
     * 10-未付款订单；
     * 15-支付中
     * 20-已付款；
     * 25-部分发货
     * 30-已发货；
     * 40-已完成;
     * 50-已关闭
     * 60-放款成功
     * 70-放款失败
     */
    public final static int ORDER_STATE_0 = 0;
    public final static int ORDER_STATE_5 = 5;
    public final static int ORDER_STATE_10 = 10;
    public final static int ORDER_STATE_15 = 15;
    public final static int ORDER_STATE_20 = 20;
    public final static int ORDER_STATE_25 = 25;
    public final static int ORDER_STATE_30 = 30;
    public final static int ORDER_STATE_40 = 40;
    public final static int ORDER_STATE_50 = 50;
    public final static int ORDER_STATE_60 = 60;
    public final static int ORDER_STATE_70 = 70;


    /**
     * 预售订单状态：101-待付定金；102-待付尾款；103-已付全款
     */
    public final static int ORDER_SUB_STATE_101 = 101;
    public final static int ORDER_SUB_STATE_102 = 102;
    public final static int ORDER_SUB_STATE_103 = 103;

    /**
     * 是否全款订单：1-全款订单，0-定金预售订单
     */
    public final static int IS_ALL_PAY_1 = 1;
    public final static int IS_ALL_PAY_0 = 0;

    /**
     * 订单支付表  pay_type  支付类型：1-全款支付；2-定金支付；3-尾款支付
     */
    public final static int PAY_TYPE_1 = 1;
    public final static int PAY_TYPE_2 = 2;
    public final static int PAY_TYPE_3 = 3;

    /**
     * 订单类型 order_type：
     * ###### 已废弃，使用OrderTypeEnum ######
     * 0-货到付款订单；
     * 1-普通订单；
     * 5-全款预售订单；
     * 6-阶梯团订单等等
     * 7-配销订单
     * 11-满赠订单
     * 13-组合订单
     */
    @Deprecated
    public final static int ORDER_TYPE_0 = 0;
    public final static int ORDER_TYPE_1 = 1;
    public final static int ORDER_TYPE_6 = 6;
    public final static int ORDER_TYPE_7 = 7;
    public final static int ORDER_TYPE_11 = 11;
    public final static int ORDER_TYPE_13 = 13;

    /**
     * 订单表  invoice_STATE  发票状态：0、未开发票；
     */
    public final static int INVOICE_STATE_0 = 0;
    public final static int INVOICE_STATE_1 = 1;

    /**
     * 线下补录订单扩展表  overdue_flag  逾期标识：0-未逾期，1-已逾期；
     */
    public final static int OVERDUE_FLAG_0 = 0;
    public final static int OVERDUE_FLAG_1 = 1;

    /**
     * 付款状态：0、买家未付款, 1、买家已付款
     */
    public final static int PAYMENT_STATE_0 = 0;
    public final static int PAYMENT_STATE_1 = 1;

    /**
     * 订单评价状态：1.未评价,2.部分评价,3.全部评价;
     */
    public static final int EVALUATE_STATE_1 = 1;
    public static final int EVALUATE_STATE_2 = 2;
    public static final int EVALUATE_STATE_3 = 3;

    /**
     * 是否总订单：0-否；1-是；
     */
    public final static int IS_PARENT_0 = 0;
    public final static int IS_PARENT_1 = 1;

    /**
     * 是否显示：0-否；1-是；
     */
    public final static int IS_SHOW_0 = 0;
    public final static int IS_SHOW_1 = 1;

    /**
     * 退款发起者：0-系统 1-客户 2-商家 3-平台 4-客户经理 5-站长 6-返利中心
     *
     * 最新数据见枚举 ReturnByEnum
     */
    public final static int RETURN_BY_0 = 0;
    public final static int RETURN_BY_1 = 1;
    public final static int RETURN_BY_2 = 2;
    public final static int RETURN_BY_3 = 3;
    public final static int RETURN_BY_4 = 4;
    public final static int RETURN_BY_5 = 5;
    /**
     * 返利中心
     */
    public final static int RETURN_BY_6 = 6;

    /**
     * 订单删除状态： 0-未删除；1-已删除
     */
    public final static int DELETE_STATE_0 = 0;
    public final static int DELETE_STATE_1 = 1;

    /**
     * 操作人角色(1-系统管理员，2-商户，3-会员，4-客户经理，5-站长，6-事业部运营）
     */
    public final static int LOG_ROLE_ADMIN = 1;
    public final static int LOG_ROLE_VENDOR = 2;
    public final static int LOG_ROLE_MEMBER = 3;
    public final static int LOG_ROLE_CUSTOMER_MANAGER = 4;
    public final static int LOG_ROLE_STATIONMASTER = 5;
    public final static int LOG_ROLE_OPERATIONAL = 6;

    /**
     * 贷款确认方式:READ-完成订单阅读确认(客户端);FACE_DETECTION-完成订单人脸识别（站长端）
     */
    public final static String LOAN_CONFIRM_METHOD_READ = "READ";
    public final static String LOAN_CONFIRM_METHOD_FACE_DETECTION = "FACE_DETECTION";

    /**
     * 订单状态：0-已取消；10-未付款订单；20-已付款；30-已发货；40-已完成;50-已关闭
     */
    public final static int ORDER_STATE_LOG_0 = 0;
    public final static int ORDER_STATE_LOG_1 = 10;
    public final static int ORDER_STATE_LOG_2 = 20;
    public final static int ORDER_STATE_LOG_3 = 30;
    public final static int ORDER_STATE_LOG_4 = 40;
    public final static int ORDER_STATE_LOG_5 = 50;

    /**
     * 是否评价:0-未评价，1-已评价
     */
    public final static int IS_COMMENT_0 = 0;
    public final static int IS_COMMENT_1 = 1;

    /**
     * 订单货品参与的活动是否为店铺活动，1==是；0==否
     */
    public final static int IS_STORE_PROMOTION_YES = 1;
    public final static int IS_STORE_PROMOTION_NO = 0;

    /**
     * 订单货品是否赠品，1==是；0==否
     */
    public final static int IS_GIFT_YES = 1;
    public final static int IS_GIFT_NO = 0;

    /**
     * 是否结算：0-未结算；1-已结算
     */
    public final static int IS_SETTLEMENT_YES = 1;
    public final static int IS_SETTLEMENT_NO = 0;

    /**
     * 发货类型：0-物流发货，1-无需物流
     */
    public final static int DELIVER_TYPE_0 = 0;
    public final static int DELIVER_TYPE_1 = 1;

    /**
     * 发货类型：0-待发货，1-已发货 2-出库中 3-部分发货
     */
    public final static int DELIVER_STATE_0 = 0;
    public final static int DELIVER_STATE_1 = 1;
    public final static int DELIVER_STATE_2 = 2;
    public final static int DELIVER_STATE_3 = 3;

    /**
     * 是否可以发货：0-否，1-是
     */
    public final static int IS_DELIVER_0 = 0;
    public final static int IS_DELIVER_1 = 1;

    /**
     * is_generate_face_sheet 是否已生成电子面单 1 - 已生成 2 - 未生成
     */
    public final static int IS_GENERATE_FACE__SHEET_1 = 1;
    public final static int IS_GENERATE_FACE__SHEET_2 = 2;

    /**
     * 提交订单入mq队列标识前缀，标识存入redis中，key=前缀+支付单号
     */
    public final static String ORDER_SUBMIT_MQ_REDIS_PREFIX = "order_submit_";

    /**
     * 订单处理进度，1-排队处理中，2-提交订单失败，3-提交成功
     */
    public final static int ORDER_SUBMIT_DEAL_STATE_1 = 1;
    public final static int ORDER_SUBMIT_DEAL_STATE_2 = 2;
    public final static int ORDER_SUBMIT_DEAL_STATE_3 = 3;

    /**
     * 检验结果
     */
    public static final String CHECK_SUCCESS = "SUCCESS";
    public static final String CHECK_FAILURE = "FAILURE";

    /**
     * 是否需要正在拼团中：1-是；2-否
     */
    public static final Integer IS_SPELLING_1 = 1;
    public static final Integer IS_SPELLING_2 = 2;

    /**
     * 是否是分销：0-否，1-是
     */
    public static final Integer IS_DISTRIBUTION_NO = 0;
    public static final Integer IS_DISTRIBUTION_YES = 1;

    /**
     * 是否是订单已全退：0-否，1-是
     */
    public static final Integer IS_ALL_RETURN_NO = 0;
    public static final Integer IS_ALL_RETURN_YES = 1;

    /**
     * 商家类型：1-自营店铺，2-入驻店铺
     */
    public static final Integer STORE_TYPE_SELF_1 = 1;

    /**
     * 是否推送金蝶
     */
    public static final Integer SEND_JINDIE_YES_1 = 1;
    public static final Integer SEND_JINDIE_NO_2 = 2;

    /**
     * 商品分销配销类型:1->普通分销，2->业务分销，4->配销商品
     */
    public static final Integer DISTRIBUTION_TYPE_1 = 1;
    public static final Integer DISTRIBUTION_TYPE_2 = 2;
    public static final Integer DISTRIBUTION_TYPE_4 = 4;


    /**
     * 运管物资办公车辆编码
     */
    public static final String BGCL = "BGCL";
    /**
     * 币种代码
     */
    public static final String CNY_CURRENCY_CODE = "CNY";
    /**
     * 售后服务端类型：1-仅退款
     */
    public static final int AFS_TYPE_RETURN_1 = 1;
    /**
     * 售后服务端类型：2-退货退款
     */
    public static final int AFS_TYPE_RETURN_2 = 2;
    /**
     * 绑定状态 1.已生效
     */
    public static final int BIND_STATE_CODE_TSX = 1;
    /**
     * 绑定状态 2.已调整
     */
    public static final int BIND_STATE_CODE_YTZ = 2;
    /**
     * 员工记息差方式-> 1: 固定日期
     */
    public static int INTEREST_START_TYPE_FIXED_DATE = 1;
    /**
     * 员工记息差方式-> 2: 确认后N天
     */
    public static int INTEREST_START_TYPE_AFTER_CONFIRM = 2;

    public static String FUNDS_BORROW_STORE_DELIVERY_EXPIRE_ALERT = "funds_borrow_store_delivery_expire_alert";
    public static String FUNDS_BORROW_STORE_DELIVERY_EXPIRE_WECHAT = "funds_borrow_store_delivery_expire_wechat";
    public static String FUNDS_BORROW_STORE_CLOSE_ALERT = "funds_borrow_store_close_alert";
    public static String FUNDS_BORROW_STORE_CLOSE_WECHAT = "funds_borrow_store_close_wechat";
    public static String  NEWMALL_QUEUE_SELLER_ADMIN_MSG = "newmall_queue_seller_admin_msg";

    public static final String FUNDS_BORROW_GOODS_DELIVERY_CODE = "100101001001";

    public static final String FUNDS_BORROW_GOODS_RECEIPT_CODE = "100101001002";

    /**
     * 提交订单入mq队列标识前缀，标识存入redis中，key=前缀+支付单号
     */
    public final static String JS_PK_DYNAMIC_PERFORMANCE_TIME = "js_pk_dynamic_performance_time";

    /**订单签收，短信模板业务类型**/
    public static final String ORDER_RECEIVE_BIZ_TYPE = "mallOrderReceiveCode";

    /**订单发货，短信模板业务类型**/
    public static final String ORDER_DELIVERY_BIZ_TYPE = "mallOrderDeliveryReceiveCode";

    /**
     * 客户经理名下所有客户近30天内产生金融支付交易≥5笔-模板消息编码
     * 客户经理名下所有客户近90天内在同一家店铺产生金融支付交易≥5笔
     */
    public static final String RISK_WARNING = "risk-warning";
    /**
     * 客户经理名下所有客户近30天内在同一家店铺产生金融支付交易≥3笔-模板消息编码
     */
    public static final String RISK_WARNING3 = "risk-warening2";

    /**
     * 同一个客户在同一个店铺90天内大于等于2笔（客户ID）
     *
     */
    public static final String RISK_WARNING_SAME_STORE = "risk-warning-same-store";

    /**
     * 缓存订单确认页标识前缀，标识存入redis中，key=前缀+分享码
     */
    public final static String CACHE_CONFIRM_REDIS_PREFIX = "cache_confirm_";
    /**
     *退款拒绝提醒
     */
    public static final String ORDER_RETURN_APPLY = "ORDER_RETURN_APPLY";
    /**
     *退款同意提醒
     */
    public static final String ORDER_RETURN_APPLY2 = "ORDER-RETURN_APPLY2";

    /**
     * 自提订单库发货时存不足提醒
     */
    public static final String SELF_LIFT_ORDER_DELIVERY_DINGDING_REMIND = "SelfLiftOrderDeliveryDingdingRemind";

    public static final int ORDER_OFFLINE_AGRIC_FEE_CODE = 10000001;

    /**
     * MQ消息：0-正向；1-逆向
     */
    public static final Integer MQ_MESSAGE_TYPE_0 = 0;
    public static final Integer MQ_MESSAGE_TYPE_1 = 1;

    /**
     * 订单同步标记
     */
    public static final Integer NOT_SYNC = 0;
    public static final Integer SYNC_SUCCESS = 1;
    public static final Integer SYNC_FAIL = 2;
    public static final Integer SYNC_PASS = 3;
    /**
     * 商品是否开启地区价格
     */
    public static final Integer IS_AREA_PRICE = 1;

    /**
     * 店铺与金蝶组织配置关系字典type
     */
    public static final String STORE_KINGDEE_ORG_DICTIONARY_TYPE = "STORE_KINGDEE_RGANIZATION_CONFIG";
}
