package com.cfpamf.ms.mallorder.enums;

import com.alibaba.fastjson.annotation.JSONType;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
@JSONType(serializeEnumAsJavaBean = true)
public enum OrderGroupBuyingEnum {

    NON(0, "不参与"),
    WAITING(1, "未拼单"),
    FINISHED(2, "已拼单");

    Integer value;
    String desc;

    public static OrderGroupBuyingEnum valueOf(int value) {
        for (OrderGroupBuyingEnum ps : OrderGroupBuyingEnum.values()) {
            if (value == ps.value) {
                return ps;
            }
        }
        return null;
    }

   public static Boolean isParticipateGroupBuying(Integer value){
       OrderGroupBuyingEnum orderGroupBuyingEnum = valueOf(value);
       if (Objects.isNull(orderGroupBuyingEnum)){
           return false;
       }

       return !(NON == orderGroupBuyingEnum);
   }

    public static Boolean isFinished(Integer value){
        OrderGroupBuyingEnum orderGroupBuyingEnum = valueOf(value);
        if (Objects.isNull(orderGroupBuyingEnum)){
            return false;
        }

        return FINISHED == orderGroupBuyingEnum;
    }
}
