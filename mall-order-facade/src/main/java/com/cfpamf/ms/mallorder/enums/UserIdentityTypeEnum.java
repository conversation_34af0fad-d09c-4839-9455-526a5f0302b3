package com.cfpamf.ms.mallorder.enums;

import com.cfpamf.framework.autoconfigure.common.exception.MSException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/*
佣金规则配置枚举
 */
@AllArgsConstructor
@Getter
public enum UserIdentityTypeEnum {

    COMPANY_STAFF(1, "内部员工"),
    CUSTOMER_MANAGER(2, "客户经理"),
    NORMAL_VILLAGE_AGENCY(5,"普通型村代"),
    NORMAL_USER(999,"普通用户");


    private Integer code;
    private String desc;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }


    public static UserIdentityTypeEnum parseEnum(Integer code){
        return Arrays.stream(UserIdentityTypeEnum.values()).filter(x -> x.getCode().equals(code)).findFirst()
                .orElseThrow(()->new MSException("*********",String.format("用户身份类型code转换成enum失败,code:%s",code)));
    }

    public static List<Integer> collectCode() {
        return Arrays.stream(UserIdentityTypeEnum.values()).map(UserIdentityTypeEnum::getCode).collect(Collectors.toList());
    }

    public static String parseDesc(Integer code){
        return Arrays.stream(UserIdentityTypeEnum.values()).filter(x -> x.getCode().equals(code)).findFirst()
                .orElseThrow(()->new MSException("*********",String.format("用户身份类型code转换成desc失败,code:%s",code)))
                .getDesc();
    }
}
