package com.cfpamf.ms.mallorder.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;


@AllArgsConstructor
@Getter
public enum CouponSendStateEnum {

    VALUE_1(1, "待发放"),
    VALUE_2(2, "发放成功"),
    VALUE_3(3, "发放失败"),
    ;

    int value;
    String desc;

    public static CouponSendStateEnum valueOf(int value) {
        for (CouponSendStateEnum e : CouponSendStateEnum.values()) {
            if (value == e.getValue()) {
                return e;
            }
        }
        return null;
    }

    public static List<Integer> valueList() {
        List<Integer> valueArray = new ArrayList<>();
        for (CouponSendStateEnum e : CouponSendStateEnum.values()) {
            valueArray.add(e.getValue());
        }
        return valueArray;
    }

    public static Boolean contains(Integer value) {
        if (value == null){
            return Boolean.FALSE;
        }
        return CouponSendStateEnum.valueList().contains(value);
    }
}
