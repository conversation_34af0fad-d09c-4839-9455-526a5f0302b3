package com.cfpamf.ms.mallorder.enums;

import java.util.Objects;

public enum BeverageWorkReportEnum {
    TODAY("TODAY", "今日"),
    PAST_7_DAYS("PAST_7_DAYS", "近7日"),
    PAST_30_DAYS("PAST_30_DAYS", "近30天"),
    PAST_6_MONTHS("PAST_6_MONTHS", "近6个月"),
    THIS_YEAR("THIS_YEAR", "今年"),
    THIS_MONTHS("THIS_MONTHS","当月"),
    PAST_1_YEAR("PAST_1_YEAR", "近一年");

    private final String value;
    private final String desc;

    BeverageWorkReportEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }


    public static BeverageWorkReportEnum getValue (String value) {
        for (BeverageWorkReportEnum ps : BeverageWorkReportEnum.values ()) {
            if (Objects.equals (value, ps.value)) {
                return ps;
            }
        }
        return null;
    }

    public String getValue() {
        return this.value;
    }
}
