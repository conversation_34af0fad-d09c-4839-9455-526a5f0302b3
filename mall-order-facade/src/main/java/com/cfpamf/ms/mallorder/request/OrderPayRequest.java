package com.cfpamf.ms.mallorder.request;

import com.cfpamf.ms.mallorder.enums.OrderCreateChannel;
import com.cfpamf.ms.mallorder.enums.PayWayEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
@Data
public class OrderPayRequest  {

    @NotNull(message = "orderSn 不能为空")
    @ApiModelProperty(value = "orderSn 需要支付订单号列表")
    private List<String> orderSn;

    @NotNull(message = "支付方式 不能为空")
    @ApiModelProperty(value = "支付方式")
    private PayWayEnum payWay;

    @ApiModelProperty(value = "贷款用途")
    private String loanPurpose;

    @ApiModelProperty(value = "还款日")
    private String repaymentDay;

    @ApiModelProperty(value = "还款方式")
    private String repaymentMode;

    @ApiModelProperty(value = "贷款期限")
    private Integer loanPeriod;

    @NotNull(message = "channel 不能为空")
    @ApiModelProperty(value = "付款渠道 CAPP BAPP")
    OrderCreateChannel channel;

    @NotEmpty(message = "payer 不能为空")
    @ApiModelProperty(value = "付款人客户ID")
    private String payer;

    @NotEmpty(message = "payerName 不能为空")
    @ApiModelProperty(value = "付款人姓名")
    private String payerName;

    @ApiModelProperty(value = "模板ID 用于预览合同")
    private String templeId;

    @ApiModelProperty(value = "客户号 C0664XXXX")
    private String loanCustId;
}
