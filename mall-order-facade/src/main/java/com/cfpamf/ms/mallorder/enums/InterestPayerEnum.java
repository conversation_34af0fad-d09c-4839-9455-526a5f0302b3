package com.cfpamf.ms.mallorder.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 代还退款-利息承担方枚举
 */
@AllArgsConstructor
@Getter
public enum InterestPayerEnum {

    PLATFORM ("PLATFORM", "未满7天，平台运营承担"),

    STORE ("STORE", "已满7天，商家承担"),
    
    NULL ("NULL", "无");

    String value;
    String desc;

    public static InterestPayerEnum getValue (String value) {
        for (InterestPayerEnum e : InterestPayerEnum.values ()) {
            if (Objects.equals (value, e.value)) {
                return e;
            }
        }
        return null;
    }

}
