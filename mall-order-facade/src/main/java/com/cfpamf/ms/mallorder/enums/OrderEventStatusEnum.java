package com.cfpamf.ms.mallorder.enums;

/**
 * 订单变更状态定义 对应 com.cfpamf.ms.mallorder.common.enums.OrderTypeEnum
 *
 * <AUTHOR>
 * @date 2021/09/14 17:16
 * @return
 */
public enum OrderEventStatusEnum {

    CANCELED(0, "交易关闭"),
    WAIT_PAY(10, "待付款"),
    DEAL_PAY(15, "付款中"),
    WAIT_DELIVER(20, "待发货"),
    WAIT_RECEIPT(30, "待签收"),
    TRADE_SUCCESS(40, "交易成功"),
    TRADE_CLOSE(50, "已关闭");

    OrderEventStatusEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    Integer value;
    String desc;

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
