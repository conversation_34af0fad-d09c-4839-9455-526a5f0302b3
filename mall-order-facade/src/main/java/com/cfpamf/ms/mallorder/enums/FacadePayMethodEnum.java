package com.cfpamf.ms.mallorder.enums;

import com.alibaba.fastjson.annotation.JSONType;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * 支付方式
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/2 14:07
 */
@AllArgsConstructor
@Getter
@JSONType(serializeEnumAsJavaBean = true)
public enum FacadePayMethodEnum {

    WXPAY("WXPAY", "微信支付", PayWayEnum.WX_PAY_V3),
    ALIPAY("ALIPAY", "支付宝", PayWayEnum.ALI_PAY),
    ENJOY_PAY("ENJOY_PAY", "用呗支付", PayWayEnum.ENJOY_PAY),
    CREDIT_PAY("CREDIT_PAY", "授信额度", PayWayEnum.CREDIT_PAY),
    FOLLOW_HEART("FOLLOW_HEART", "随心取", PayWayEnum.FOLLOW_HEART),
    BALANCE("BALANCE", "虚拟账余额", PayWayEnum.BALANCE),
    CARD_VOUCHER("CARD_VOUCHER", "卡券支付",null),
    CARD("CARD", "乡助卡支付",null),
    MALL_BALANCE("MALL_BALANCE", "电商余额", null),
    AGREED_PAY("AGREED_PAY", "协议支付", null),
    BANK_PAY("BANK_PAY", "银行卡转账", null),
    BANK_TRANSFER("BANK_TRANSFER", "银行卡汇款", null),
    COMBINATION_PAY("COMBINATION_PAY", "组合支付", null);

    public String getValue(){
        return this.value;
    }

    String value;
    String desc;
    PayWayEnum payWay;

    public static FacadePayMethodEnum getValue(String value) {
        for (FacadePayMethodEnum ps : FacadePayMethodEnum.values()) {
            if (value.equals(ps.value)) {
                return ps;
            }
        }
        return null;
    }

    public static boolean isLoanPay(FacadePayMethodEnum payWay) {
        return payWay == ENJOY_PAY || payWay == CREDIT_PAY || payWay == FOLLOW_HEART;
    }

    public static boolean isThirdPartyPay(FacadePayMethodEnum payWay) {
        return payWay == ALIPAY || payWay == WXPAY || payWay == ENJOY_PAY;
    }

    /**
     * 获取贷款类支付编码
     *
     * @return      贷款类支付编码集合
     */
    public static List<String> loanPayMethodCode() {
        return Arrays.asList(ENJOY_PAY.getValue(), CREDIT_PAY.getValue(), FOLLOW_HEART.getValue());
    }

    /**
     * 获取非贷款类支付编码
     *
     * @return      非贷款类支付编码集合
     */
    public static List<String> nonLoanPayMethodCode() {
        return Arrays.asList(WXPAY.getValue(), ALIPAY.getValue(), BALANCE.getValue(), CARD_VOUCHER.getValue(),
                CARD.getValue(), MALL_BALANCE.getValue(), AGREED_PAY.getValue(), BANK_PAY.getValue(),
                BANK_TRANSFER.getValue(), COMBINATION_PAY.getValue());
    }

}
