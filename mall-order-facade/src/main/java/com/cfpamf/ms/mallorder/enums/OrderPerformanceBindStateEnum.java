package com.cfpamf.ms.mallorder.enums;

import com.cfpamf.framework.autoconfigure.common.exception.MSException;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public enum OrderPerformanceBindStateEnum {

    INIT(0, "初始化"),
    EFFECT(1, "已生效"),
    ADJUST(2,"已调整");

    OrderPerformanceBindStateEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private final Integer code;

    private final String desc;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static OrderPerformanceBindStateEnum parseEnum(Integer code){
        return Arrays.stream(OrderPerformanceBindStateEnum.values()).filter(x -> x.getCode().equals(code)).findFirst()
                .orElseThrow(()->new MSException("044999996",String.format("订单业绩归属绑定状态code转换成enum失败,code:%s",code)));
    }

    public static List<Integer> collectCode() {
        return Arrays.stream(OrderPerformanceBindStateEnum.values()).map(OrderPerformanceBindStateEnum::getCode).collect(Collectors.toList());
    }

    public static String parseMemo(Integer code){
        return parseEnum(code).getDesc();
    }

    public static boolean isInit(Integer code){
        OrderPerformanceBindStateEnum stateEnum = parseEnum(code);
        return INIT == stateEnum;
    }

    public static boolean isEffect(Integer code){
        OrderPerformanceBindStateEnum stateEnum = parseEnum(code);
        return EFFECT == stateEnum;
    }

    public static boolean isAdjust(Integer code){
        OrderPerformanceBindStateEnum stateEnum = parseEnum(code);
        return ADJUST == stateEnum;
    }

    public static boolean isAvailable(Integer code){
        OrderPerformanceBindStateEnum stateEnum = parseEnum(code);
        return EFFECT == stateEnum || ADJUST == stateEnum;
    }

    public static boolean canConvertible(Integer oldCode, Integer newCode){
        OrderPerformanceBindStateEnum oldStateEnum = parseEnum(oldCode);
        OrderPerformanceBindStateEnum newStateEnum = parseEnum(newCode);
        if (INIT == oldStateEnum && EFFECT == newStateEnum) {
            return true;
        }
        if (EFFECT == oldStateEnum && ADJUST == newStateEnum) {
            return true;
        }
        if (ADJUST == oldStateEnum && ADJUST == newStateEnum) {
            return true;
        }
        return false;
    }

}
