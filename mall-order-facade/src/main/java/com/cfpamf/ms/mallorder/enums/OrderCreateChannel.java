package com.cfpamf.ms.mallorder.enums;

import com.alibaba.fastjson.annotation.JSONType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang.StringUtils;

/**
 * 下单操作渠道定义
 *
 * <AUTHOR>
 * @date 2020-03-04
 */
@AllArgsConstructor
@Getter
@JSONType(serializeEnumAsJavaBean = true)
public enum OrderCreateChannel implements IEnum<String> {
    H5("H5", "浏览器H5"),
    APP("APP", "乡助APP"),
    BAPP("BAPP", "BAPP"),
    WE_CHAT("WE_CHAT","微信浏览器"),
    MINI_PRO("MINI_PRO","小程序"),
    MiniProgram("MiniProgram","小程序版本号控制专用"),
    WEB("WEB","网页"),
    OMS("OMS","运管物资"),
    CHANNEL_YZH ("YZH", "云中鹤"),
    HOME_SERVICE ("HOME_SERVICE", "到家服务"),
    SELLER_WEB ("SELLER_WEB", "商家网页"),
    CHANNEL_WMS ("WMS", "WMS仓储服务"),
    MFR_DEALER ("MFR_DEALER", "农机厂商"),
    XXAPP("XXAPP","乡信APP"),
    XX_MINI_PRO("XX_MINI_PRO","乡信小程序"),
    REBATE_GIFT("REBATE_GIFT","返利赠品")
    ;


    @Override
    public String getValue(){
        return this.value;
    }

    private String value;
    private String desc;

    public boolean valueEquals(String art) {
        if (StringUtils.isBlank(art)) {
            return false;
        }
        return value.equalsIgnoreCase(art.trim());
    }

    public static String getDescByValue(String value) {
        for(OrderCreateChannel channel : OrderCreateChannel.values()){
            if (channel.value.equals(value)){
                return channel.desc;
            }
        }
        return null;
    }
}
