package com.cfpamf.ms.mallorder.constant;

/**
 * 供应商履约-订单常量
 * @Author: zml
 * @CreateTime: 2022/7/13 14:57
 */
public class OrderPerformanceConstant {

    /**
     * 供应商履约-订单操作类型
     * 1-下单失败
     * 2-取消订单
     */
    public static final int ORDER_EXTERNAL_PERFORMANCE_OPERATE_TYPE_1 = 1;
    public static final int ORDER_EXTERNAL_PERFORMANCE_OPERATE_TYPE_2 = 2;

    /***
     * ERP库存类型
     * 库存类型 1-销售库存 2-实物库存
     */
    public static final int ERP_STOCK_TYPE_2 = 2;

    public static final String ERP_NORM_MALL_BIZ_SOURCE = "norm_mall";

    /**
     * 渠道订单存在售后的编码
     */
    public static final int CHANNEL_ORDER_REFUND_CODE = 1000001;


}
