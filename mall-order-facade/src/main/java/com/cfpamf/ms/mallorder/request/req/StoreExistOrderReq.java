package com.cfpamf.ms.mallorder.request.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> 2021/11/24.
 */
@Data
public class StoreExistOrderReq {


    @ApiModelProperty(value = "店铺ID集合")
    private List<Long> storeIdList;



    @ApiModelProperty(value = "计算开始时间")
    @NotBlank(message = "计算开始时间不可为空")
    private String startTime;

}
