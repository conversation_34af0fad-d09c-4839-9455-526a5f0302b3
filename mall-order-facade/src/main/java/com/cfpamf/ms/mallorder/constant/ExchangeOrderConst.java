package com.cfpamf.ms.mallorder.constant;

public class ExchangeOrderConst {
    /**
     * 订单换货标识-> 1: 被换的订单
     */
    public static int ORDER_EXCHANGE_FLAG_0 = 0;

    /**
     * 订单换货标识-> 1: 被换的订单
     */
    public static int ORDER_EXCHANGE_FLAG_1 = 1;

    /**
     * 订单换货标识-> 2: 换货后新生成的订单
     */
    public static int ORDER_EXCHANGE_FLAG_2 = 2;

    /**
     * 订单换货,微信推送模板
     */
    public static String ORDER_EXCHANGE_REMINDER_TEMPLATE = "order_exchange_reminder";

    /**
     * 换货订单状态：待审批
     */
    public static final int EXCHANGE_ORDER_STATE_WAIT_AUDIT = 0;

    /**
     * 换货订单状态：撤销换货
     */
    public static final int EXCHANGE_ORDER_STATE_CANCEL = 1;

    /**
     * 换货订单状态：拒绝换货
     */
    public static final int EXCHANGE_ORDER_STATE_DISAGREE = 2;

    /**
     * 换货订单状态：同意换货
     */
    public static final int EXCHANGE_ORDER_STATE_AGREE = 3;

    /**
     * 换货订单状态：换货完成
     */
    public static final int EXCHANGE_ORDER_STATE_FINISH = 4;

    /**
     * 换货订单状态：换货自动取消
     */
    public static final int EXCHANGE_ORDER_STATE_AUTO_CLOSE = 5;

    /**
     * 换货订单状态：换货关闭
     */
    public static final int EXCHANGE_ORDER_STATE_CLOSE = 6;


    /**
     * 换货订单状态：同意换货--待发货
     */
    public static final int EXCHANGE_ORDER_STATE_AGREE_WAIT_DELIVERY = 3;

    /**
     * 换货订单状态：同意换货--待签收
     */
    public static final int EXCHANGE_ORDER_STATE_AGREE_WAIT_RECEIVE = 7;

    /**
     * 换货单是否需要用户确定：1-需要用户确定
     */
    public static final int BUYER_CONFIRM_FLAG_1 = 1;

    /**
     * 换货单是否需要用户确定：0-不需要用户确定
     */
    public static final int BUYER_CONFIRM_FLAG_0 = 0;


}
