package com.cfpamf.ms.mallorder.request.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> 2022/4/12.
 */
@Data
public class PayMethodMerchantReq {

    @ApiModelProperty("支付方式id")
    @NotNull(message = "支付方式id不能为空")
    Long payMethodId;

    @ApiModelProperty("名称")
    String name;

    @ApiModelProperty("店铺id")
    String storeId;

    @ApiModelProperty("当前页面")
    private Integer current = 1;

    @ApiModelProperty("页面大小")
    private Integer pageSize = 10;

}
