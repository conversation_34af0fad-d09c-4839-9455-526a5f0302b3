package com.cfpamf.ms.mallorder.enums;


import com.alibaba.fastjson.annotation.JSONType;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 支付渠道定义
 *
 * <AUTHOR>
 * @date 2021/6/24 11:33
 * @return
 */
@AllArgsConstructor
@Getter
@JSONType(serializeEnumAsJavaBean = true)
public enum PayChannelEnum implements IEnum<Integer> {

    YZT_WX(1, "云直通微信"),
    SVC_WX(2, "收付通微信"),
    JS_WX(3, "江苏银行微信");

    Integer value;
    String desc;

    public static PayChannelEnum valueOf(int value) {
        for (PayChannelEnum ps : PayChannelEnum.values()) {
            if (value == ps.value) {
                return ps;
            }
        }
        return null;
    }
}
