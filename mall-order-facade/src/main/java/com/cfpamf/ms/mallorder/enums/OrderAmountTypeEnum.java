package com.cfpamf.ms.mallorder.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Getter
public enum OrderAmountTypeEnum {

    ORDER_COMMISSION("order_commission", "佣金类型"),
    COMMISSION_SERVICE_FEE("commission_service_fee", "佣金服务费"),
    ORDER_COMMISSION_INCENTIVE_FEE ("order_commission_incentive_fee", "订单佣金激励费"),
    COMMISSION_INCENTIVE_SERVICE_FEE("commission_incentive_service_fee", "佣金激励费服务费"),
    PLAN_DISCOUNT_AMOUNT("plan_discount_amount", "预贴息类型"),
    SERVICE_FEE("service_fee", "平台服务费");

    OrderAmountTypeEnum(String name, String memo) {
        this.name = name;
        this.memo = memo;
    }

    private final String name;

    private final String memo;

    public String getName() {
        return name;
    }

    public String getMemo() {
        return memo;
    }

    public static OrderAmountTypeEnum parseEnum(String name){
        return Arrays.stream(OrderAmountTypeEnum.values()).filter(x -> x.getName().equals(name)).findFirst().orElse(null);
    }

    public static List<String> collectName() {
        return Arrays.stream(OrderAmountTypeEnum.values()).map(OrderAmountTypeEnum::getName).collect(Collectors.toList());
    }

    public static String parseMemo(String name){
        OrderAmountTypeEnum stateEnum = parseEnum(name);
        if (!Objects.isNull(stateEnum)) {
            return stateEnum.getMemo();
        }
        return null;
    }
}
