package com.cfpamf.ms.mallorder.enums;

import com.cfpamf.framework.autoconfigure.common.exception.MSException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum ManagerBusinessModelEnum {

    AGRIC_MANAGER(1,"农服管护模式"),
    LIQUOR_MANAGER(2,"酒水管护模式"),
    LOAN_MANAGER(3,"信贷管护模式");

    private final Integer code;

    private final String desc;

    public static ManagerBusinessModelEnum parseEnum(Integer code){
        return Arrays.stream(ManagerBusinessModelEnum.values()).filter(x -> x.getCode().equals(code)).findFirst()
                .orElseThrow(()->new MSException("044999996",String.format("业务管护模式code转换成enum失败,code:%s",code)));
    }

    public static List<Integer> collectCode() {
        return Arrays.stream(ManagerBusinessModelEnum.values()).map(ManagerBusinessModelEnum::getCode).collect(Collectors.toList());
    }

    public static String parseDesc(Integer code){
        return Arrays.stream(ManagerBusinessModelEnum.values()).filter(x -> x.getCode().equals(code)).findFirst()
                .orElseThrow(()->new MSException("044999996",String.format("业务管护模式code转换成desc失败,code:%s",code)))
                .getDesc();
    }
}
