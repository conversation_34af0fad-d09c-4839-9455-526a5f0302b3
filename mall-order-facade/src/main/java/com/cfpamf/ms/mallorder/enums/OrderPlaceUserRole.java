package com.cfpamf.ms.mallorder.enums;

import com.cfpamf.ms.mallorder.constant.OrderConst;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR> 下单用户角色
 */
@AllArgsConstructor
@Getter
public enum OrderPlaceUserRole {

	SELF(1, "本人", OrderConst.LOG_ROLE_MEMBER),
	CUSTOMER_MANAGER(2, "客户经理", OrderConst.LOG_ROLE_CUSTOMER_MANAGER),
	STATION_MASTER(3, "站长", OrderConst.LOG_ROLE_STATIONMASTER),
	OPERATIONAL_MASTER(4, "事业部运营", OrderConst.LOG_ROLE_OPERATIONAL);

	Integer value;
	String desc;
	int operationRoleCode;

	public static OrderPlaceUserRole valueOf(int value) {
		for (OrderPlaceUserRole opur : OrderPlaceUserRole.values()) {
			if (value == opur.value) {
				return opur;
			}
		}
		return null;
	}

	public static boolean isSelfOrderPlace(int value) {
		return SELF.value == value;
	}
	
	
	public static boolean isSelfOrderPlace(OrderPlaceUserRole orderPlaceUserRoleCode) {
		return isSelfOrderPlace(orderPlaceUserRoleCode.value);
	}

	public static boolean isCustomerManagerOrderPlace(Integer value) {
		return Objects.equals(CUSTOMER_MANAGER.value, value);
	}

	public static boolean isStationMasterOrderPlace(Integer value) {
		return Objects.equals(STATION_MASTER.value, value);
	}

	public static boolean isCustomerManagerOrderPlace(OrderPlaceUserRole orderPlaceUserRoleCode) {
		return isCustomerManagerOrderPlace(orderPlaceUserRoleCode.value);
	}

	public static boolean isStationMasterOrderPlace(OrderPlaceUserRole orderPlaceUserRoleCode) {
		return isStationMasterOrderPlace(orderPlaceUserRoleCode.value);
	}

	/**
	 * 是否为代客下单，代客下单角色增加需要同步扩展
	 * @param value
	 * @return
	 */
	public static boolean isValetOrder(Integer value) {
		return CUSTOMER_MANAGER.value == value || STATION_MASTER.value == value;
	}

	public static boolean isStationMasterOrder(Integer value) {
		return STATION_MASTER.value == value;
	}

}
