package com.cfpamf.ms.mallorder.enums;

/**
 * <AUTHOR>
 * 结算方式枚举
 */
public enum SettleChannelEnum {

    UN_KNOW(0, "未知"),
    STANDARD(1, "正常结算"),
    AGREEMENT(2, "往来结算");

    Integer code;
    String desc;

    SettleChannelEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
