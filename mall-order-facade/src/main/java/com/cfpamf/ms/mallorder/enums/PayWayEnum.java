package com.cfpamf.ms.mallorder.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@AllArgsConstructor
@Getter
public enum PayWayEnum {

    ENJOY_PAY ("ENJOY_PAY", "用呗"),

    CREDIT_PAY ("CREDIT_PAY", "授信额度"),

    FOLLOW_HEART ("FOLLOW_HEART", "随心取"),

    BALANCE ("BALANCE", "余额"),

    ALI_PAY ("ALIPAY", "支付宝"),

    WX_PAY ("WXPAY", "微信支付"),

    WX_PAY_V3 ("WX_PAY_V3", "微信支付V3"),

    CARD_VOUCHER ("CARD_VOUCHER", "卡券支付"),

    BANK_TRANSFER("BANK_TRANSFER", "银行卡汇款")

    ;

    String value;
    String desc;

    public static PayWayEnum getValue (String value) {
        for (PayWayEnum ps : PayWayEnum.values ()) {
            if (Objects.equals (value, ps.value)) {
                return ps;
            }
        }
        return null;
    }

    public static String convertLoanPayModel(PayWayEnum payWayEnum) {
        if (payWayEnum == ENJOY_PAY) {
            return "enjoypay";
        } else if (payWayEnum == CREDIT_PAY) {
            return "credit";
        } else if (payWayEnum == FOLLOW_HEART) {
            return "followheart";
        }
        return "";
    }

}
