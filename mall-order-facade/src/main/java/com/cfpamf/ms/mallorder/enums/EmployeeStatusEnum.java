package com.cfpamf.ms.mallorder.enums;

import com.cfpamf.framework.autoconfigure.common.exception.MSException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/*
佣金规则配置枚举
 */
@AllArgsConstructor
@Getter
public enum EmployeeStatusEnum {
    WAIT_HIRED(1,"待入职员工"),
    PROBATIONARY(2, "试用员工"),
    REGULAR(3, "正式员工"),
    CALL_OUT(4,"调出员工"),
    CALL_IN(5,"待调入"),
    RETIRED(6,"退休员工"),
    DIMISSION(8,"离职员工"),
    INFORMAL(12,"非正式员工");

    private Integer code;
    private String desc;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }


    public static EmployeeStatusEnum parseEnum(Integer code){
        return Arrays.stream(EmployeeStatusEnum.values()).filter(x -> x.getCode().equals(code)).findFirst()
                .orElseThrow(()->new MSException("044999996",String.format("员工分销用户状态code转换成enum失败,code:%s",code)));
    }

    public static List<Integer> collectCode() {
        return Arrays.stream(EmployeeStatusEnum.values()).map(EmployeeStatusEnum::getCode).collect(Collectors.toList());
    }

    public static String parseDesc(Integer code){
        return Arrays.stream(EmployeeStatusEnum.values()).filter(x -> x.getCode().equals(code)).findFirst()
                .orElseThrow(()->new MSException("044999996",String.format("员工分销用户状态code转换成desc失败,code:%s",code)))
                .getDesc();
    }

    /**
     * 是否是在职状态
     * @param code
     * @return
     */
    public static boolean isReferralEmployee(Integer code){
        return PROBATIONARY.getCode().equals(code) || REGULAR.getCode().equals(code);
    }
}
