package com.cfpamf.ms.mallorder.enums.checkresult;

import com.alibaba.fastjson.annotation.JSONType;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 检查商品资料结果
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
@JSONType(serializeEnumAsJavaBean = true)
public enum BzOrderTradeProofCheckResultEnum {

    NORMAL("000000", "正常商品"),
    SUSPECTED_SECOND_HAND("100001", "疑似二手商品"),
    SUSPECTED_ORDER_REPEAT("100002", "疑似在乡助多次下单"),
    ;

    String value;
    String desc;

    public static BzOrderTradeProofCheckResultEnum getForValue(String value) {
        for (BzOrderTradeProofCheckResultEnum ps : BzOrderTradeProofCheckResultEnum.values()) {
            if (value.equals(ps.value)) {
                return ps;
            }
        }
        return null;
    }

}
