package com.cfpamf.ms.mallorder.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
public class OrderAmountUpdateDTO implements java.io.Serializable {

	private static final long serialVersionUID = 3277543621835250016L;
	
	private String orderSn;

	private Integer amountType;

	private BigDecimal amount;

	// 附属资金：例如 佣金类型的附属资金为佣金服务费

	/**
	 * 附属资金项类型
	 */
	private Integer subAmountType;

	/**
	 * 附属资金项资金金额
	 */
	private BigDecimal subAmount;

	public OrderAmountUpdateDTO() {
	}

	public OrderAmountUpdateDTO(String orderSn, Integer amountType, BigDecimal amount) {
		this.orderSn = orderSn;
		this.amountType = amountType;
		this.amount = amount;
	}

	public OrderAmountUpdateDTO(String orderSn, Integer amountType, BigDecimal amount, Integer subAmountType,
								BigDecimal subAmount) {
		this.orderSn = orderSn;
		this.amountType = amountType;
		this.amount = amount;
		this.subAmountType = subAmountType;
		this.subAmount = subAmount;
	}


}
