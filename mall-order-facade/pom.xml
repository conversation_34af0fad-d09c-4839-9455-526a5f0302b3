<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.cfpamf.mall</groupId>
        <artifactId>mall-order</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <name>mall-order-facade</name>
    <artifactId>mall-order-facade</artifactId>
    <version>20250421-SNAPSHOT</version>

    <dependencies>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cfpamf.mall</groupId>
            <artifactId>mall-core</artifactId>
            <version>1.0.13-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>gexin-rp-sdk-http</artifactId>
                    <groupId>com.gexin.platform</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.cfpamf.common</groupId>
            <artifactId>ms-common</artifactId>
            <version>2.0.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.75_noneautotype</version>
        </dependency>
        <dependency>
            <groupId>com.cfpamf.ms</groupId>
            <artifactId>mall-file-center-facade</artifactId>
            <version>1.0.8-SNAPSHOT</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>mall-order-facade</finalName>
        <!-- Source attach plugin -->
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.2.1</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>

